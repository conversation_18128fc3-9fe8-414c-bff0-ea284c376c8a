# 路径相似度比较测试
# 比较PathSimilarityOptimizer与原有share_distance函数的功能

import numpy as np
import time
import random
from path_similarity_optimizer import PathSimilarityOptimizer
from gls_evol_enhanced import share_distance, calculate_path_similarity, normalize_path

# 测试函数：比较两种方法对路径变体的识别能力
def test_path_variants_recognition():
    print("\n测试路径变体识别能力...")
    
    # 创建基准路径
    base_path = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    
    # 创建各种变体路径
    # 1. 完全相同的路径
    same_path = base_path.copy()
    
    # 2. 不同起点的路径（循环移位）
    rotated_path = base_path[3:] + base_path[:3]  # 从索引3开始的循环移位
    
    # 3. 反转的路径
    reversed_path = base_path.copy()
    reversed_path.reverse()
    
    # 4. 部分扰动的路径（80%相似）
    perturbed_path = base_path.copy()
    # 随机交换两个位置
    idx1, idx2 = random.sample(range(len(perturbed_path)), 2)
    perturbed_path[idx1], perturbed_path[idx2] = perturbed_path[idx2], perturbed_path[idx1]
    
    # 5. 完全不同的路径
    different_path = list(range(10, 20))
    
    # 创建路径相似度优化器
    optimizer = PathSimilarityOptimizer(similarity_threshold=0.7)
    
    # 添加基准路径
    optimizer.add_path(base_path)
    
    # 测试各种变体
    paths = [
        ("完全相同", same_path),
        ("不同起点", rotated_path),
        ("反转路径", reversed_path),
        ("部分扰动", perturbed_path),
        ("完全不同", different_path)
    ]
    
    print("\n基准路径:", base_path)
    print("\n使用PathSimilarityOptimizer测试:")
    for name, path in paths:
        is_similar, similar_id, similarity = optimizer.check_similarity(path)
        print(f"{name}路径: {path}")
        print(f"  - 是否相似: {is_similar}, 相似度: {similarity:.4f}")
    
    print("\n使用原有share_distance函数测试:")
    for name, path in paths:
        shared_edges = share_distance(base_path, path)
        similarity = shared_edges / len(base_path) if len(base_path) > 0 else 0
        is_similar = similarity >= 0.7
        print(f"{name}路径: {path}")
        print(f"  - 共享边数: {shared_edges}, 相似度: {similarity:.4f}, 是否相似: {is_similar}")

# 测试函数：比较两种方法的性能
def test_performance():
    print("\n测试性能比较...")
    
    # 创建大量随机路径
    num_paths = 100
    path_length = 50
    paths = []
    
    for _ in range(num_paths):
        path = list(range(path_length))
        random.shuffle(path)
        paths.append(path)
    
    # 测试PathSimilarityOptimizer性能
    optimizer = PathSimilarityOptimizer()
    
    # 添加所有路径
    start_time = time.time()
    for path in paths:
        optimizer.add_path(path)
    add_time = time.time() - start_time
    
    # 测试相似度检查性能
    start_time = time.time()
    for i in range(10):
        test_path = paths[random.randint(0, num_paths-1)]
        optimizer.check_similarity(test_path)
    check_time = time.time() - start_time
    
    print(f"PathSimilarityOptimizer性能:")
    print(f"  - 添加{num_paths}条路径耗时: {add_time:.6f}秒")
    print(f"  - 检查相似度耗时: {check_time:.6f}秒")
    
    # 测试原有share_distance函数性能
    start_time = time.time()
    for i in range(10):
        test_path = paths[random.randint(0, num_paths-1)]
        for path in paths:
            share_distance(test_path, path)
    original_time = time.time() - start_time
    
    print(f"原有share_distance函数性能:")
    print(f"  - 检查相似度耗时: {original_time:.6f}秒")
    print(f"  - 性能提升比例: {original_time/check_time:.2f}倍")

# 测试函数：比较两种方法在TSPLIB实例上的表现
def test_tsplib_instances():
    print("\n模拟TSPLIB实例测试...")
    
    # 创建一个模拟的TSPLIB实例路径
    tsp_path = list(range(50))
    random.shuffle(tsp_path)
    
    # 创建一些变体
    variants = []
    
    # 1. 高质量变体 - 与原路径相似度高但成本略高
    high_quality = tsp_path.copy()
    # 交换两个相邻位置
    idx = random.randint(0, len(high_quality)-2)
    high_quality[idx], high_quality[idx+1] = high_quality[idx+1], high_quality[idx]
    variants.append(("高质量变体", high_quality, 105))  # 成本比最优解高5%
    
    # 2. 多样性变体 - 与原路径相似度低
    diverse = tsp_path.copy()
    # 随机交换多个位置
    for _ in range(10):
        idx1, idx2 = random.sample(range(len(diverse)), 2)
        diverse[idx1], diverse[idx2] = diverse[idx2], diverse[idx1]
    variants.append(("多样性变体", diverse, 110))  # 成本比最优解高10%
    
    # 3. 低质量变体 - 与原路径相似度高但成本很高
    low_quality = tsp_path.copy()
    # 交换一个位置
    idx1, idx2 = random.sample(range(len(low_quality)), 2)
    low_quality[idx1], low_quality[idx2] = low_quality[idx2], low_quality[idx1]
    variants.append(("低质量变体", low_quality, 130))  # 成本比最优解高30%
    
    # 创建路径相似度优化器
    optimizer = PathSimilarityOptimizer(similarity_threshold=0.7)
    optimizer.add_path(tsp_path)
    
    print(f"\n基准路径长度: {len(tsp_path)}")
    print(f"基准路径成本: 100 (假设值)")
    
    print("\n使用PathSimilarityOptimizer测试:")
    for name, path, cost in variants:
        similarity = optimizer.calculate_similarity(tsp_path, path)
        is_similar = similarity >= 0.7
        print(f"{name}: 成本={cost}, 相似度={similarity:.4f}, 是否相似={is_similar}")
    
    print("\n使用原有calculate_path_similarity函数测试:")
    for name, path, cost in variants:
        similarity = calculate_path_similarity(tsp_path, path)
        is_similar = similarity >= 0.7
        print(f"{name}: 成本={cost}, 相似度={similarity:.4f}, 是否相似={is_similar}")

# 测试函数：测试normalize_path与PathSimilarityOptimizer的标准化能力
def test_path_normalization():
    print("\n测试路径标准化能力...")
    
    # 创建基准路径
    base_path = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    
    # 创建各种变体路径
    variants = [
        ("原始路径", base_path),
        ("循环移位", base_path[3:] + base_path[:3]),
        ("反转路径", base_path[::-1]),
        ("反转+移位", (base_path[::-1])[2:] + (base_path[::-1])[:2])
    ]
    
    print("\n使用normalize_path函数测试:")
    normalized_paths = []
    for name, path in variants:
        norm_path = normalize_path(path)
        normalized_paths.append(norm_path)
        print(f"{name}: {path}")
        print(f"标准化后: {norm_path}")
    
    # 检查所有标准化路径是否相同
    all_same = all(np.array_equal(normalized_paths[0], path) for path in normalized_paths[1:])
    print(f"\n所有标准化路径是否相同: {all_same}")
    
    print("\n使用PathSimilarityOptimizer测试:")
    optimizer = PathSimilarityOptimizer(similarity_threshold=0.7)
    optimizer.add_path(base_path)
    
    for name, path in variants:
        is_similar, similar_id, similarity = optimizer.check_similarity(path)
        print(f"{name}: {path}")
        print(f"  - 是否相似: {is_similar}, 相似度: {similarity:.4f}")

# 测试函数：测试在guided_local_search中集成PathSimilarityOptimizer
def test_integration_simulation():
    print("\n模拟在guided_local_search中集成PathSimilarityOptimizer...")
    
    # 模拟res_populations
    res_populations = []
    for i in range(5):
        path = list(range(20))
        random.shuffle(path)
        res_populations.append({"tour": path, "cur_cost": 100 + i * 5})
    
    # 创建测试路径
    test_path = list(range(20))
    random.shuffle(test_path)
    test_cost = 102  # 假设成本
    
    # 创建路径相似度优化器
    optimizer = PathSimilarityOptimizer(similarity_threshold=0.7)
    
    # 添加所有精英解到优化器
    for res in res_populations:
        optimizer.add_path(res["tour"])
    
    # 模拟原有判断逻辑
    print("\n原有判断逻辑:")
    best_res_cost = min(res_populations, key=lambda x: x["cur_cost"])["cur_cost"]
    in_res_tours_flag, index_res = False, -1
    
    for i, res in enumerate(res_populations):
        shared_edges = share_distance(test_path, res["tour"])
        if shared_edges == len(test_path):
            in_res_tours_flag = True
            index_res = i
            break
    
    if test_cost <= best_res_cost and not in_res_tours_flag:
        print(f"路径被添加到res_populations (成本={test_cost}, 最佳成本={best_res_cost})")
    else:
        print(f"路径未被添加到res_populations (成本={test_cost}, 最佳成本={best_res_cost}, 已存在={in_res_tours_flag})")
    
    # 模拟使用PathSimilarityOptimizer的判断逻辑
    print("\n使用PathSimilarityOptimizer的判断逻辑:")
    is_similar, similar_id, similarity = optimizer.check_similarity(test_path)
    
    # 设置相似度阈值和成本比例参数
    similarity_threshold = 0.7
    cost_ratio = 0.05
    
    # 检查是否满足成本条件
    cost_condition = test_cost <= best_res_cost * (1 + cost_ratio)
    
    # 检查是否满足相似度条件
    similarity_condition = not is_similar
    
    if (cost_condition and similarity_condition) or (test_cost < best_res_cost):
        print(f"路径被添加到res_populations (成本={test_cost}, 最佳成本={best_res_cost})")
        print(f"成本条件: {cost_condition}, 相似度条件: {similarity_condition}")
        if is_similar:
            print(f"与路径{similar_id}相似，相似度={similarity:.4f}")
    else:
        print(f"路径未被添加到res_populations")
        print(f"成本条件: {cost_condition}, 相似度条件: {similarity_condition}")
        if is_similar:
            print(f"与路径{similar_id}相似，相似度={similarity:.4f}")

# 主函数
if __name__ == "__main__":
    print("路径相似度比较测试开始...\n")
    
    # 运行测试函数
    test_path_variants_recognition()
    test_performance()
    test_tsplib_instances()
    test_path_normalization()
    test_integration_simulation()
    
    print("\n测试完成!")