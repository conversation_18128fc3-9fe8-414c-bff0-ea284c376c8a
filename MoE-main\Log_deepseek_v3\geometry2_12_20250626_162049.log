2025-06-26 16:20:49,010 - __main__ - INFO - geometry2_12 开始进化第 1 代
2025-06-26 16:20:49,010 - __main__ - INFO - 开始分析阶段
2025-06-26 16:20:49,010 - StatsExpert - INFO - 开始统计分析
2025-06-26 16:20:49,014 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 1344.0, 'max': 2144.0, 'mean': 1778.0, 'std': 299.3653286538039}, 'diversity': 0.7944444444444444, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 16:20:49,015 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 1344.0, 'max': 2144.0, 'mean': 1778.0, 'std': 299.3653286538039}, 'diversity_level': 0.7944444444444444, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[264, 213], [210, 244], [156, 213], [156, 151], [210, 120], [264, 151], [291, 322], [129, 322], [48, 182], [129, 42], [291, 42], [372, 182]], 'distance_matrix': array([[  0.,  62., 108., 125., 108.,  62., 112., 174., 218., 218., 173.,
        112.],
       [ 62.,   0.,  62., 108., 124., 108., 112., 112., 173., 218., 218.,
        173.],
       [108.,  62.,   0.,  62., 108., 125., 174., 112., 112., 173., 218.,
        218.],
       [125., 108.,  62.,   0.,  62., 108., 218., 173., 112., 112., 174.,
        218.],
       [108., 124., 108.,  62.,   0.,  62., 218., 218., 173., 112., 112.,
        173.],
       [ 62., 108., 125., 108.,  62.,   0., 173., 218., 218., 174., 112.,
        112.],
       [112., 112., 174., 218., 218., 173.,   0., 162., 280., 323., 280.,
        162.],
       [174., 112., 112., 173., 218., 218., 162.,   0., 162., 280., 323.,
        280.],
       [218., 173., 112., 112., 173., 218., 280., 162.,   0., 162., 280.,
        324.],
       [218., 218., 173., 112., 112., 174., 323., 280., 162.,   0., 162.,
        280.],
       [173., 218., 218., 174., 112., 112., 280., 323., 280., 162.,   0.,
        162.],
       [112., 173., 218., 218., 173., 112., 162., 280., 324., 280., 162.,
          0.]])}
2025-06-26 16:20:49,016 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 16:20:49,016 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 16:20:49,016 - PathExpert - INFO - 开始路径结构分析
2025-06-26 16:20:49,017 - PathExpert - INFO - 路径结构分析完成
2025-06-26 16:20:49,017 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (2, 3), 'frequency': 0.5, 'avg_cost': 62.0}, {'edge': (5, 10), 'frequency': 0.5, 'avg_cost': 112.0}], 'common_subpaths': [], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(0, 1)', 'frequency': 0.4}, {'edge': '(4, 5)', 'frequency': 0.4}, {'edge': '(2, 3)', 'frequency': 0.5}, {'edge': '(6, 7)', 'frequency': 0.4}, {'edge': '(6, 11)', 'frequency': 0.5}, {'edge': '(9, 10)', 'frequency': 0.5}, {'edge': '(0, 11)', 'frequency': 0.4}, {'edge': '(5, 10)', 'frequency': 0.5}], 'low_frequency_edges': [{'edge': '(0, 5)', 'frequency': 0.2}, {'edge': '(3, 4)', 'frequency': 0.3}, {'edge': '(2, 7)', 'frequency': 0.3}, {'edge': '(10, 11)', 'frequency': 0.2}, {'edge': '(8, 9)', 'frequency': 0.3}, {'edge': '(1, 8)', 'frequency': 0.2}, {'edge': '(1, 2)', 'frequency': 0.2}, {'edge': '(7, 8)', 'frequency': 0.3}, {'edge': '(1, 7)', 'frequency': 0.2}, {'edge': '(2, 8)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.2}, {'edge': '(1, 6)', 'frequency': 0.3}, {'edge': '(1, 5)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.3}, {'edge': '(4, 10)', 'frequency': 0.2}, {'edge': '(0, 9)', 'frequency': 0.3}, {'edge': '(0, 2)', 'frequency': 0.2}, {'edge': '(3, 6)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.2}, {'edge': '(2, 5)', 'frequency': 0.2}, {'edge': '(0, 4)', 'frequency': 0.2}, {'edge': '(8, 11)', 'frequency': 0.2}, {'edge': '(3, 9)', 'frequency': 0.2}, {'edge': '(2, 9)', 'frequency': 0.2}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(7, 10)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.2}, {'edge': '(3, 8)', 'frequency': 0.3}, {'edge': '(4, 7)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [8, 11, 7], 'cost': 604.0, 'size': 3}, {'region': [7, 10, 6], 'cost': 603.0, 'size': 3}, {'region': [11, 9, 7], 'cost': 560.0, 'size': 3}]}
2025-06-26 16:20:49,017 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 16:20:49,017 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 16:20:49,017 - EliteExpert - INFO - 开始精英解分析
2025-06-26 16:20:49,017 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 16:20:49,017 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 16:20:49,017 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 16:20:49,017 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 16:20:49,022 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 16:20:49,022 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 1344.0, mean 1778.0, max 2144.0, std 299.3653286538039
- diversity: 0.7944444444444444
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [164.0, 169.0, 170.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (2, 3), 'frequency': 0.5, 'avg_cost': 62.0}, {'edge': (5, 10), 'frequency': 0.5, 'avg_cost': 112.0}]
- common_subpaths_sample: []
- difficult_regions_sample: [{'region': [8, 11, 7], 'cost': 604.0, 'size': 3}, {'region': [7, 10, 6], 'cost': 603.0, 'size': 3}, {'region': [11, 9, 7], 'cost': 560.0, 'size': 3}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [48.0, 42.0, 372.0, 322.0]
- centroid/spread: [210.0, 182.0] | rms 122.586
- density_grid(3x3): [[1, 1, 1], [1, 2, 3], [1, 1, 1]]
- edge_len_stats(min,q1,med,q3,max): [62.0, 112.0, 167.5, 218.0, 324.0]
- long_edge_ratio: 0.136
- direction_hist(8 sectors): [2, 1, 2, 1, 2, 2, 0, 1]
- nn_median_dist: 87.28

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 16:20:49,023 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:21:15,712 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate (wide edge length range [62.0, 324.0], Q3-Q1 spread: 106.0)",
    "modality": "Potential multi-modality (direction_hist shows non-uniform sector distribution)",
    "deceptiveness": "Low (long_edge_ratio only 13.6%, most edges in moderate length range)"
  },
  "population_state": {
    "coverage": "Good (diversity 0.79, density_grid shows multiple occupied cells)",
    "convergence": "Early-stage (0.0 convergence, recent_best_costs still fluctuating)",
    "spread": "Centered around [210.0, 182.0] with RMS spread 122.586"
  },
  "difficult_regions": [
    {"region": [8, 11, 7], "reason": "High cost (604.0) + low density_grid coverage"},
    {"region": [7, 10, 6], "reason": "High cost (603.0) + directional sparsity in sector 6"},
    {"region": [11, 9, 7], "reason": "High cost (560.0) + no elite common edges"}
  ],
  "opportunity_regions": [
    {"edge": "(2, 3)", "reason": "High-quality (freq 50%, avg_cost 62.0) + short length"},
    {"cell": "density_grid[1][2]", "reason": "Highest density (3 nodes) near centroid"}
  ],
  "evolution_phase": "Exploration (no elites, high diversity, no convergence)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Edge recombination (exploit high-quality edges like (2,3))",
    "2. Local search around density_grid[1][2] nodes",
    "3. Penalize long edges (>300) in selection",
    "4. Path relinking for difficult regions"
  ]
}
```
2025-06-26 16:21:15,712 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 16:21:15,712 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate (wide edge length range [62.0, 324.0], Q3-Q1 spread: 106.0)', 'modality': 'Potential multi-modality (direction_hist shows non-uniform sector distribution)', 'deceptiveness': 'Low (long_edge_ratio only 13.6%, most edges in moderate length range)'}, 'population_state': {'coverage': 'Good (diversity 0.79, density_grid shows multiple occupied cells)', 'convergence': 'Early-stage (0.0 convergence, recent_best_costs still fluctuating)', 'spread': 'Centered around [210.0, 182.0] with RMS spread 122.586'}, 'difficult_regions': [{'region': [8, 11, 7], 'reason': 'High cost (604.0) + low density_grid coverage'}, {'region': [7, 10, 6], 'reason': 'High cost (603.0) + directional sparsity in sector 6'}, {'region': [11, 9, 7], 'reason': 'High cost (560.0) + no elite common edges'}], 'opportunity_regions': [{'edge': '(2, 3)', 'reason': 'High-quality (freq 50%, avg_cost 62.0) + short length'}, {'cell': 'density_grid[1][2]', 'reason': 'Highest density (3 nodes) near centroid'}], 'evolution_phase': 'Exploration (no elites, high diversity, no convergence)', 'evolution_direction': ['Operator suggestions:', '1. Edge recombination (exploit high-quality edges like (2,3))', '2. Local search around density_grid[1][2] nodes', '3. Penalize long edges (>300) in selection', '4. Path relinking for difficult regions']}
2025-06-26 16:21:15,712 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 16:21:15,712 - __main__ - INFO - 分析阶段完成
2025-06-26 16:21:15,712 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate (wide edge length range [62.0, 324.0], Q3-Q1 spread: 106.0)', 'modality': 'Potential multi-modality (direction_hist shows non-uniform sector distribution)', 'deceptiveness': 'Low (long_edge_ratio only 13.6%, most edges in moderate length range)'}, 'population_state': {'coverage': 'Good (diversity 0.79, density_grid shows multiple occupied cells)', 'convergence': 'Early-stage (0.0 convergence, recent_best_costs still fluctuating)', 'spread': 'Centered around [210.0, 182.0] with RMS spread 122.586'}, 'difficult_regions': [{'region': [8, 11, 7], 'reason': 'High cost (604.0) + low density_grid coverage'}, {'region': [7, 10, 6], 'reason': 'High cost (603.0) + directional sparsity in sector 6'}, {'region': [11, 9, 7], 'reason': 'High cost (560.0) + no elite common edges'}], 'opportunity_regions': [{'edge': '(2, 3)', 'reason': 'High-quality (freq 50%, avg_cost 62.0) + short length'}, {'cell': 'density_grid[1][2]', 'reason': 'Highest density (3 nodes) near centroid'}], 'evolution_phase': 'Exploration (no elites, high diversity, no convergence)', 'evolution_direction': ['Operator suggestions:', '1. Edge recombination (exploit high-quality edges like (2,3))', '2. Local search around density_grid[1][2] nodes', '3. Penalize long edges (>300) in selection', '4. Path relinking for difficult regions']}
2025-06-26 16:21:15,712 - __main__ - INFO - 开始策略分配阶段
2025-06-26 16:21:15,712 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 16:21:15,712 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 1344.0
  • mean_cost: 1778.0
- Search-space: ruggedness Moderate (wide edge length range [62.0, 324.0], Q3-Q1 spread: 106.0), modality Potential multi-modality (direction_hist shows non-uniform sector distribution), deceptiveness Low (long_edge_ratio only 13.6%, most edges in moderate length range)
- Evolution phase: Exploration (no elites, high diversity, no convergence)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [8, 11, 7, {'region': [7, 10, 6...
- Opportunity regions (# 2): {'edge': '(2, 3)', ', {'cell': 'density_gr
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 16:21:15,712 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 16:21:15,712 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:21:26,340 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 16:21:26,340 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 16:21:26,340 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 16:21:26,345 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 16:21:26,345 - __main__ - INFO - 策略分配阶段完成
2025-06-26 16:21:26,345 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 16:21:26,346 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 16:21:26,346 - __main__ - INFO - 开始进化阶段
2025-06-26 16:21:26,346 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 16:21:26,347 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:21:26,348 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 1405.0 (rank 20.0%)
- Population diversity: 0.45454545454545453
- Evolution phase: Exploration (no elites, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'edge': '(2, 3, {'cell': 'densi
- Difficult regions to avoid (sample): {'region': [8, , {'region': [7, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:21:26,348 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:21:26,348 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:21:34,784 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 3, 5, 7, 9, 10, 11, 8, 6, 4, 1],
  "targeted_regions": "edge (2,3) and adjacent sparse cells",
  "strategy_comment": "Prioritize low-density edges while avoiding high-cost regions"
}
```
2025-06-26 16:21:34,784 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:21:34,784 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2108.0, 路径: [0, 2, 3, 5, 7, 9, 10, 11, 8, 6, 4, 1]
2025-06-26 16:21:34,784 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 3, 5, 7, 9, 10, 11, 8, 6, 4, 1], 'cur_cost': 2108.0}
2025-06-26 16:21:34,784 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 16:21:34,784 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:21:34,784 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 1344.0 (rank 0.0%)
- Population diversity: 0.4727272727272727
- Evolution phase: Exploration (no elites, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'edge': '(2, 3, {'cell': 'densi
- Difficult regions to avoid (sample): {'region': [8, , {'region': [7, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:21:34,784 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:21:34,784 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:21:42,254 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 3, 5, 7, 9, 11, 10, 8, 6, 4, 1],
  "targeted_regions": "edge (2,3) and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions while balancing exploration"
}
```
2025-06-26 16:21:42,261 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:21:42,261 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2182.0, 路径: [0, 2, 3, 5, 7, 9, 11, 10, 8, 6, 4, 1]
2025-06-26 16:21:42,261 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 2, 3, 5, 7, 9, 11, 10, 8, 6, 4, 1], 'cur_cost': 2182.0}
2025-06-26 16:21:42,262 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 16:21:42,262 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:21:42,263 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 1344.0 (rank 0.0%)
- Population diversity: 0.4818181818181818
- Evolution phase: Exploration (no elites, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'edge': '(2, 3, {'cell': 'densi
- Difficult regions to avoid (sample): {'region': [8, , {'region': [7, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:21:42,263 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:21:42,263 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:21:50,432 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 3, 1, 5, 4, 6, 9, 10, 7, 8, 11],
  "targeted_regions": "edge (2,3) and adjacent sparse cells",
  "strategy_comment": "Prioritize low-density edges with moderate cost"
}
```
2025-06-26 16:21:50,432 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:21:50,432 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2072.0, 路径: [0, 2, 3, 1, 5, 4, 6, 9, 10, 7, 8, 11]
2025-06-26 16:21:50,432 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 3, 1, 5, 4, 6, 9, 10, 7, 8, 11], 'cur_cost': 2072.0}
2025-06-26 16:21:50,432 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 16:21:50,432 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:21:50,434 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 1982.0 (rank 30.0%)
- Population diversity: 0.4636363636363636
- Evolution phase: Exploration (no elites, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'edge': '(2, 3, {'cell': 'densi
- Difficult regions to avoid (sample): {'region': [8, , {'region': [7, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:21:50,434 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:21:50,434 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:21:58,304 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11],
  "targeted_regions": "edge (2,3) and adjacent sparse cells",
  "strategy_comment": "Prioritize low-density edges while avoiding high-cost regions"
}
```
2025-06-26 16:21:58,310 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:21:58,310 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2125.0, 路径: [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11]
2025-06-26 16:21:58,310 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}
2025-06-26 16:21:58,310 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 16:21:58,310 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:21:58,310 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 1954.0 (rank 20.0%)
- Population diversity: 0.43636363636363634
- Evolution phase: Exploration (no elites, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'edge': '(2, 3, {'cell': 'densi
- Difficult regions to avoid (sample): {'region': [8, , {'region': [7, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:21:58,310 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:21:58,310 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:22:05,905 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11],
  "targeted_regions": "edge (2,3) and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions while balancing diversity"
}
```
2025-06-26 16:22:05,905 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:22:05,908 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2125.0, 路径: [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11]
2025-06-26 16:22:05,908 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}
2025-06-26 16:22:05,908 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 16:22:05,908 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:22:05,908 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2144.0 (rank 80.0%)
- Population diversity: 0.4090909090909091
- Evolution phase: Exploration (no elites, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'edge': '(2, 3, {'cell': 'densi
- Difficult regions to avoid (sample): {'region': [8, , {'region': [7, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:22:05,908 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:22:05,908 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:22:13,428 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 3, 5, 1, 4, 6, 9, 10, 7, 8, 11],
  "targeted_regions": "edge (2,3) and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions while avoiding dense clusters"
}
```
2025-06-26 16:22:13,428 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:22:13,430 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2134.0, 路径: [0, 2, 3, 5, 1, 4, 6, 9, 10, 7, 8, 11]
2025-06-26 16:22:13,430 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 2, 3, 5, 1, 4, 6, 9, 10, 7, 8, 11], 'cur_cost': 2134.0}
2025-06-26 16:22:13,430 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 16:22:13,430 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:22:13,430 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 1997.0 (rank 20.0%)
- Population diversity: 0.35454545454545455
- Evolution phase: Exploration (no elites, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'edge': '(2, 3, {'cell': 'densi
- Difficult regions to avoid (sample): {'region': [8, , {'region': [7, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:22:13,430 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:22:13,430 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:22:20,943 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11],
  "targeted_regions": "edge (2,3) and adjacent sparse cells",
  "strategy_comment": "Prioritize low-density edges while avoiding high-cost regions"
}
```
2025-06-26 16:22:20,943 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:22:20,943 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2125.0, 路径: [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11]
2025-06-26 16:22:20,943 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}
2025-06-26 16:22:20,943 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 16:22:20,943 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:22:20,943 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:22:20,943 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 2371.0
2025-06-26 16:22:21,454 - ExploitationExpert - INFO - res_population_num: 69
2025-06-26 16:22:21,454 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:22:21,454 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64)]
2025-06-26 16:22:21,466 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:22:21,466 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 3, 5, 7, 9, 10, 11, 8, 6, 4, 1], 'cur_cost': 2108.0}, {'tour': [0, 2, 3, 5, 7, 9, 11, 10, 8, 6, 4, 1], 'cur_cost': 2182.0}, {'tour': [0, 2, 3, 1, 5, 4, 6, 9, 10, 7, 8, 11], 'cur_cost': 2072.0}, {'tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}, {'tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}, {'tour': [0, 2, 3, 5, 1, 4, 6, 9, 10, 7, 8, 11], 'cur_cost': 2134.0}, {'tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}, {'tour': array([ 1,  9, 11,  2,  5,  4,  7,  0,  3,  6, 10,  8]), 'cur_cost': 2371.0}, {'tour': [6, 1, 11, 9, 7, 4, 10, 5, 3, 8, 2, 0], 'cur_cost': 1839.0}, {'tour': [11, 0, 1, 5, 10, 9, 2, 3, 8, 7, 4, 6], 'cur_cost': 1663.0}]
2025-06-26 16:22:21,468 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:22:21,468 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-06-26 16:22:21,468 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 16:22:21,468 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 16:22:21,468 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:22:21,468 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:22:21,469 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 2114.0
2025-06-26 16:22:21,971 - ExploitationExpert - INFO - res_population_num: 87
2025-06-26 16:22:21,972 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:22:21,972 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64)]
2025-06-26 16:22:21,985 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:22:21,985 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 3, 5, 7, 9, 10, 11, 8, 6, 4, 1], 'cur_cost': 2108.0}, {'tour': [0, 2, 3, 5, 7, 9, 11, 10, 8, 6, 4, 1], 'cur_cost': 2182.0}, {'tour': [0, 2, 3, 1, 5, 4, 6, 9, 10, 7, 8, 11], 'cur_cost': 2072.0}, {'tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}, {'tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}, {'tour': [0, 2, 3, 5, 1, 4, 6, 9, 10, 7, 8, 11], 'cur_cost': 2134.0}, {'tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}, {'tour': array([ 1,  9, 11,  2,  5,  4,  7,  0,  3,  6, 10,  8]), 'cur_cost': 2371.0}, {'tour': array([ 5,  1, 10,  8,  4,  6, 11,  9,  3,  0,  2,  7]), 'cur_cost': 2114.0}, {'tour': [11, 0, 1, 5, 10, 9, 2, 3, 8, 7, 4, 6], 'cur_cost': 1663.0}]
2025-06-26 16:22:21,987 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:22:21,987 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 154, 'cache_hit_rate': 0.0, 'cache_size': 154}}
2025-06-26 16:22:21,988 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 16:22:21,988 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 16:22:21,988 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:22:21,988 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:22:21,989 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1964.0
2025-06-26 16:22:22,493 - ExploitationExpert - INFO - res_population_num: 97
2025-06-26 16:22:22,493 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:22:22,493 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64)]
2025-06-26 16:22:22,510 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:22:22,511 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 3, 5, 7, 9, 10, 11, 8, 6, 4, 1], 'cur_cost': 2108.0}, {'tour': [0, 2, 3, 5, 7, 9, 11, 10, 8, 6, 4, 1], 'cur_cost': 2182.0}, {'tour': [0, 2, 3, 1, 5, 4, 6, 9, 10, 7, 8, 11], 'cur_cost': 2072.0}, {'tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}, {'tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}, {'tour': [0, 2, 3, 5, 1, 4, 6, 9, 10, 7, 8, 11], 'cur_cost': 2134.0}, {'tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}, {'tour': array([ 1,  9, 11,  2,  5,  4,  7,  0,  3,  6, 10,  8]), 'cur_cost': 2371.0}, {'tour': array([ 5,  1, 10,  8,  4,  6, 11,  9,  3,  0,  2,  7]), 'cur_cost': 2114.0}, {'tour': array([ 5,  0,  7,  1,  4, 10,  9,  2,  8, 11,  3,  6]), 'cur_cost': 1964.0}]
2025-06-26 16:22:22,511 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:22:22,511 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 156, 'cache_hit_rate': 0.0, 'cache_size': 156}}
2025-06-26 16:22:22,512 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 16:22:22,513 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 3, 5, 7, 9, 10, 11, 8, 6, 4, 1], 'cur_cost': 2108.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 3, 5, 7, 9, 11, 10, 8, 6, 4, 1], 'cur_cost': 2182.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 3, 1, 5, 4, 6, 9, 10, 7, 8, 11], 'cur_cost': 2072.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 3, 5, 1, 4, 6, 9, 10, 7, 8, 11], 'cur_cost': 2134.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 3, 5, 7, 9, 10, 8, 6, 4, 1, 11], 'cur_cost': 2125.0}}]
2025-06-26 16:22:22,513 - __main__ - INFO - 进化阶段完成
2025-06-26 16:22:22,513 - __main__ - INFO - 开始评估阶段
2025-06-26 16:22:22,513 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 16:22:22,514 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 16:22:22,514 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 1344.0 → 1964.0 (Δ -620.0)
- Mean cost: 1778.0 → 2132.0
- Diversity: 0.455 → 0.355 (Δ -0.1)

#### Elite metrics
- Elite count: 0 → 97
- Best elite cost: 1344.0 → 1344 (Δ 0.0)

#### Strategy performance
- Explore: 7 inds, success 14.3% , avg Δ -385.86
- Exploit: 3 inds, success 0.0% , avg Δ -279.67

#### Other indicators
- No-change individuals: 0
- Regression individuals: 9
- Historical trends: Last 3 iterations: costs [164.0, 169.0, 170.0], diversity [0.7222222222222222, 0.7155555555555556, 0.7288888888888888]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 16:22:22,515 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:22:36,316 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "contribution_to_elite": "moderate",
      "success_rate": 14.3,
      "average_delta": -385.86
    },
    "exploit": {
      "contribution_to_elite": "low",
      "success_rate": 0.0,
      "average_delta": -279.67
    }
  },
  "balance_state": "explore_heavy_but_effective",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "elite_preservation": "increase"
  }
}
```
2025-06-26 16:22:36,320 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 16:22:36,320 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "contribution_to_elite": "moderate",
      "success_rate": 14.3,
      "average_delta": -385.86
    },
    "exploit": {
      "contribution_to_elite": "low",
      "success_rate": 0.0,
      "average_delta": -279.67
    }
  },
  "balance_state": "explore_heavy_but_effective",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "elite_preservation": "increase"
  }
}
```
2025-06-26 16:22:36,321 - __main__ - INFO - 评估阶段完成
2025-06-26 16:22:36,321 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "contribution_to_elite": "moderate",
      "success_rate": 14.3,
      "average_delta": -385.86
    },
    "exploit": {
      "contribution_to_elite": "low",
      "success_rate": 0.0,
      "average_delta": -279.67
    }
  },
  "balance_state": "explore_heavy_but_effective",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "elite_preservation": "increase"
  }
}
```
2025-06-26 16:22:36,321 - __main__ - INFO - 当前最佳适应度: 1964.0
2025-06-26 16:22:36,323 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\geometry2_12_route_0.pkl
2025-06-26 16:22:36,323 - __main__ - INFO - geometry2_12 开始进化第 2 代
2025-06-26 16:22:36,323 - __main__ - INFO - 开始分析阶段
2025-06-26 16:22:36,324 - StatsExpert - INFO - 开始统计分析
2025-06-26 16:22:36,326 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 1964.0, 'max': 2371.0, 'mean': 2132.0, 'std': 96.17484078489551}, 'diversity': 0.5962962962962963, 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 16:22:36,327 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 1964.0, 'max': 2371.0, 'mean': 2132.0, 'std': 96.17484078489551}, 'diversity_level': 0.5962962962962963, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'coordinates': [[264, 213], [210, 244], [156, 213], [156, 151], [210, 120], [264, 151], [291, 322], [129, 322], [48, 182], [129, 42], [291, 42], [372, 182]], 'distance_matrix': array([[  0.,  62., 108., 125., 108.,  62., 112., 174., 218., 218., 173.,
        112.],
       [ 62.,   0.,  62., 108., 124., 108., 112., 112., 173., 218., 218.,
        173.],
       [108.,  62.,   0.,  62., 108., 125., 174., 112., 112., 173., 218.,
        218.],
       [125., 108.,  62.,   0.,  62., 108., 218., 173., 112., 112., 174.,
        218.],
       [108., 124., 108.,  62.,   0.,  62., 218., 218., 173., 112., 112.,
        173.],
       [ 62., 108., 125., 108.,  62.,   0., 173., 218., 218., 174., 112.,
        112.],
       [112., 112., 174., 218., 218., 173.,   0., 162., 280., 323., 280.,
        162.],
       [174., 112., 112., 173., 218., 218., 162.,   0., 162., 280., 323.,
        280.],
       [218., 173., 112., 112., 173., 218., 280., 162.,   0., 162., 280.,
        324.],
       [218., 218., 173., 112., 112., 174., 323., 280., 162.,   0., 162.,
        280.],
       [173., 218., 218., 174., 112., 112., 280., 323., 280., 162.,   0.,
        162.],
       [112., 173., 218., 218., 173., 112., 162., 280., 324., 280., 162.,
          0.]])}
2025-06-26 16:22:36,328 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 16:22:36,328 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 16:22:36,328 - PathExpert - INFO - 开始路径结构分析
2025-06-26 16:22:36,329 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 16:22:36,329 - PathExpert - INFO - 路径结构分析完成
2025-06-26 16:22:36,329 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 16:22:36,329 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 16:22:36,329 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 16:22:36,329 - EliteExpert - INFO - 开始精英解分析
2025-06-26 16:22:36,359 - EliteExpert - INFO - 精英解分析完成
2025-06-26 16:22:36,359 - __main__ - INFO - 精英专家分析报告: {'elite_count': 97, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 620.0, 'avg_gap': 788.0}, 'structure_gap': {'unique_elite_edges': 32, 'unique_pop_edges': 37, 'common_edges': 16}}, 'elite_diversity': {'diversity_score': 0.49955254868270327}}
2025-06-26 16:22:36,359 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 16:22:36,360 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 16:22:36,361 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 16:22:36,362 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 1964.0, mean 2132.0, max 2371.0, std 96.17484078489551
- diversity: 0.5962962962962963
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [169.0, 170.0, 1964.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [48.0, 42.0, 372.0, 322.0]
- centroid/spread: [210.0, 182.0] | rms 122.586
- density_grid(3x3): [[1, 1, 1], [1, 2, 3], [1, 1, 1]]
- edge_len_stats(min,q1,med,q3,max): [62.0, 112.0, 167.5, 218.0, 324.0]
- long_edge_ratio: 0.136
- direction_hist(8 sectors): [2, 1, 2, 1, 2, 2, 0, 1]
- nn_median_dist: 87.28

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 16:22:36,363 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:22:53,200 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "potentially multimodal",
    "deceptiveness": "low",
    "edge_length_variability": "high",
    "long_edge_ratio": "low"
  },
  "population_state": {
    "coverage": "moderate",
    "convergence": "none",
    "diversity": "high",
    "spatial_distribution": "uneven",
    "density_grid_hotspots": "center-right"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "early_exploration",
  "evolution_direction": {
    "operators": [
      "edge_recombination",
      "local_search_around_high_density_cells",
      "long_edge_repair_heuristics"
    ],
    "focus_areas": [
      "exploit_high_density_regions",
      "improve_edge_length_balance"
    ]
  }
}
```
2025-06-26 16:22:53,201 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 16:22:53,201 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high', 'long_edge_ratio': 'low'}, 'population_state': {'coverage': 'moderate', 'convergence': 'none', 'diversity': 'high', 'spatial_distribution': 'uneven', 'density_grid_hotspots': 'center-right'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'early_exploration', 'evolution_direction': {'operators': ['edge_recombination', 'local_search_around_high_density_cells', 'long_edge_repair_heuristics'], 'focus_areas': ['exploit_high_density_regions', 'improve_edge_length_balance']}}
2025-06-26 16:22:53,201 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 16:22:53,201 - __main__ - INFO - 分析阶段完成
2025-06-26 16:22:53,201 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high', 'long_edge_ratio': 'low'}, 'population_state': {'coverage': 'moderate', 'convergence': 'none', 'diversity': 'high', 'spatial_distribution': 'uneven', 'density_grid_hotspots': 'center-right'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'early_exploration', 'evolution_direction': {'operators': ['edge_recombination', 'local_search_around_high_density_cells', 'long_edge_repair_heuristics'], 'focus_areas': ['exploit_high_density_regions', 'improve_edge_length_balance']}}
2025-06-26 16:22:53,203 - __main__ - INFO - 开始策略分配阶段
2025-06-26 16:22:53,203 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 16:22:53,203 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 1964.0
  • mean_cost: 2132.0
- Search-space: ruggedness moderate, modality potentially multimodal, deceptiveness low
- Evolution phase: early_exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "contribution_to_elite": "m...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 16:22:53,203 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 16:22:53,203 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:23:04,768 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration; top 30% individuals exploit to refine solutions."
}
```
2025-06-26 16:23:04,768 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 16:23:04,768 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 16:23:04,770 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration; top 30% individuals exploit to refine solutions."
}
```
2025-06-26 16:23:04,770 - __main__ - INFO - 策略分配阶段完成
2025-06-26 16:23:04,770 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 16:23:04,770 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration; top 30% individuals exploit to refine solutions."
}
```
2025-06-26 16:23:04,771 - __main__ - INFO - 开始进化阶段
2025-06-26 16:23:04,771 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 16:23:04,771 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:23:04,772 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2108.0 (rank 20.0%)
- Population diversity: 0.35454545454545455
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:23:04,772 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:23:04,772 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:23:14,420 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 5, 8, 2, 11, 1, 9, 4, 6],
  "targeted_regions": "Unexplored sparse cells",
  "strategy_comment": "Prioritize least visited nodes while maintaining feasible cost"
}
```
2025-06-26 16:23:14,421 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:23:14,421 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2114.0, 路径: [0, 3, 7, 10, 5, 8, 2, 11, 1, 9, 4, 6]
2025-06-26 16:23:14,421 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 9, 4, 6], 'cur_cost': 2114.0}
2025-06-26 16:23:14,421 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 16:23:14,422 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:23:14,422 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2182.0 (rank 80.0%)
- Population diversity: 0.39090909090909093
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:23:14,422 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:23:14,422 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:23:24,345 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4],
  "targeted_regions": "Unexplored sparse cells",
  "strategy_comment": "Prioritize least visited nodes while maintaining TSP validity"
}
```
2025-06-26 16:23:24,345 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:23:24,345 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2109.0, 路径: [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4]
2025-06-26 16:23:24,345 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4], 'cur_cost': 2109.0}
2025-06-26 16:23:24,350 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 16:23:24,350 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:23:24,351 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2072.0 (rank 10.0%)
- Population diversity: 0.39090909090909093
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:23:24,351 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:23:24,352 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:23:32,257 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9],
  "targeted_regions": "Unexplored sparse cells",
  "strategy_comment": "Balanced exploration focusing on least visited nodes"
}
```
2025-06-26 16:23:32,259 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:23:32,259 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2562.0, 路径: [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9]
2025-06-26 16:23:32,261 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}
2025-06-26 16:23:32,261 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 16:23:32,261 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:23:32,261 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2125.0 (rank 40.0%)
- Population diversity: 0.39090909090909093
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:23:32,261 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:23:32,261 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:23:39,854 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9],
  "targeted_regions": "Unexplored sparse cells",
  "strategy_comment": "Prioritize least visited nodes while balancing cost"
}
```
2025-06-26 16:23:39,854 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:23:39,854 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2562.0, 路径: [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9]
2025-06-26 16:23:39,854 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}
2025-06-26 16:23:39,854 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 16:23:39,857 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:23:39,857 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2125.0 (rank 40.0%)
- Population diversity: 0.39090909090909093
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:23:39,857 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:23:39,857 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:23:47,948 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9],
  "targeted_regions": "Unexplored sparse cells",
  "strategy_comment": "Prioritize least visited nodes while maintaining reasonable cost"
}
```
2025-06-26 16:23:47,948 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:23:47,948 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2562.0, 路径: [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9]
2025-06-26 16:23:47,948 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}
2025-06-26 16:23:47,948 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 16:23:47,948 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:23:47,948 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2134.0 (rank 50.0%)
- Population diversity: 0.39090909090909093
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:23:47,948 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:23:47,948 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:23:55,804 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4],
  "targeted_regions": "Unexplored sparse cells",
  "strategy_comment": "Prioritize unvisited nodes to boost diversity"
}
```
2025-06-26 16:23:55,806 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:23:55,806 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2109.0, 路径: [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4]
2025-06-26 16:23:55,806 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4], 'cur_cost': 2109.0}
2025-06-26 16:23:55,806 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 16:23:55,806 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:23:55,808 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2125.0 (rank 50.0%)
- Population diversity: 0.38181818181818183
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:23:55,808 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:23:55,808 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:24:03,504 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9],
  "targeted_regions": "Unexplored sparse cells",
  "strategy_comment": "Balanced exploration with moderate cost"
}
```
2025-06-26 16:24:03,504 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:24:03,504 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2562.0, 路径: [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9]
2025-06-26 16:24:03,504 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}
2025-06-26 16:24:03,504 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 16:24:03,512 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:24:03,512 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:24:03,512 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1992.0
2025-06-26 16:24:04,015 - ExploitationExpert - INFO - res_population_num: 100
2025-06-26 16:24:04,016 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:24:04,016 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64)]
2025-06-26 16:24:04,034 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:24:04,034 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 9, 4, 6], 'cur_cost': 2114.0}, {'tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4], 'cur_cost': 2109.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4], 'cur_cost': 2109.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': array([ 3,  8,  1,  7, 10,  5, 11,  2,  6,  4,  0,  9]), 'cur_cost': 1992.0}, {'tour': array([ 5,  1, 10,  8,  4,  6, 11,  9,  3,  0,  2,  7]), 'cur_cost': 2114.0}, {'tour': array([ 5,  0,  7,  1,  4, 10,  9,  2,  8, 11,  3,  6]), 'cur_cost': 1964.0}]
2025-06-26 16:24:04,034 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:24:04,034 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 159, 'cache_hit_rate': 0.0, 'cache_size': 159}}
2025-06-26 16:24:04,034 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 16:24:04,034 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 16:24:04,034 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:24:04,034 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:24:04,037 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 2221.0
2025-06-26 16:24:04,538 - ExploitationExpert - INFO - res_population_num: 105
2025-06-26 16:24:04,538 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:24:04,538 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64)]
2025-06-26 16:24:04,555 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:24:04,555 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 9, 4, 6], 'cur_cost': 2114.0}, {'tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4], 'cur_cost': 2109.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4], 'cur_cost': 2109.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': array([ 3,  8,  1,  7, 10,  5, 11,  2,  6,  4,  0,  9]), 'cur_cost': 1992.0}, {'tour': array([11,  8,  1, 10,  9,  7,  6,  4,  0,  3,  5,  2]), 'cur_cost': 2221.0}, {'tour': array([ 5,  0,  7,  1,  4, 10,  9,  2,  8, 11,  3,  6]), 'cur_cost': 1964.0}]
2025-06-26 16:24:04,558 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:24:04,558 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 163, 'cache_hit_rate': 0.0, 'cache_size': 163}}
2025-06-26 16:24:04,558 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 16:24:04,558 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 16:24:04,559 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:24:04,559 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:24:04,559 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1937.0
2025-06-26 16:24:05,062 - ExploitationExpert - INFO - res_population_num: 108
2025-06-26 16:24:05,062 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:24:05,062 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64)]
2025-06-26 16:24:05,082 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:24:05,082 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 9, 4, 6], 'cur_cost': 2114.0}, {'tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4], 'cur_cost': 2109.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4], 'cur_cost': 2109.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': array([ 3,  8,  1,  7, 10,  5, 11,  2,  6,  4,  0,  9]), 'cur_cost': 1992.0}, {'tour': array([11,  8,  1, 10,  9,  7,  6,  4,  0,  3,  5,  2]), 'cur_cost': 2221.0}, {'tour': array([ 4,  3,  7,  6,  8,  0, 10,  9,  2,  5, 11,  1]), 'cur_cost': 1937.0}]
2025-06-26 16:24:05,083 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:24:05,083 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 24, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 24, 'cache_hits': 0, 'similarity_calculations': 168, 'cache_hit_rate': 0.0, 'cache_size': 168}}
2025-06-26 16:24:05,083 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 16:24:05,083 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 9, 4, 6], 'cur_cost': 2114.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4], 'cur_cost': 2109.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 5, 8, 2, 11, 1, 6, 9, 4], 'cur_cost': 2109.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}}]
2025-06-26 16:24:05,084 - __main__ - INFO - 进化阶段完成
2025-06-26 16:24:05,084 - __main__ - INFO - 开始评估阶段
2025-06-26 16:24:05,084 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 16:24:05,084 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 16:24:05,084 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 1964.0 → 1937.0 (Δ 27.0)
- Mean cost: 2132.0 → 2273.0
- Diversity: 0.355 → 0.309 (Δ -0.0455)

#### Elite metrics
- Elite count: 97 → 108
- Best elite cost: 1344 → 1344 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 28.6% , avg Δ -244.14
- Exploit: 3 inds, success 66.7% , avg Δ 99.67

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [169.0, 170.0, 1964.0], diversity [0.7155555555555556, 0.7288888888888888, 0.5962962962962963]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 16:24:05,086 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:24:22,761 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -244.14,
      "contribution_to_elite": "moderate (negative impact on mean cost but potential for breakthroughs)"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 99.67,
      "contribution_to_elite": "high (consistent positive improvements)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "over-exploration (low success rate with explore, high regression count)",
    "diversity_trend": "declining (0.355 → 0.309)"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_actions": [
      "increase exploit attempts for local refinement",
      "introduce mild mutation to regression individuals",
      "monitor diversity for 2 more iterations before further adjustments"
    ]
  }
}
```
2025-06-26 16:24:22,770 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 16:24:22,770 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -244.14,
      "contribution_to_elite": "moderate (negative impact on mean cost but potential for breakthroughs)"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 99.67,
      "contribution_to_elite": "high (consistent positive improvements)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "over-exploration (low success rate with explore, high regression count)",
    "diversity_trend": "declining (0.355 → 0.309)"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_actions": [
      "increase exploit attempts for local refinement",
      "introduce mild mutation to regression individuals",
      "monitor diversity for 2 more iterations before further adjustments"
    ]
  }
}
```
2025-06-26 16:24:22,770 - __main__ - INFO - 评估阶段完成
2025-06-26 16:24:22,770 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -244.14,
      "contribution_to_elite": "moderate (negative impact on mean cost but potential for breakthroughs)"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 99.67,
      "contribution_to_elite": "high (consistent positive improvements)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "over-exploration (low success rate with explore, high regression count)",
    "diversity_trend": "declining (0.355 → 0.309)"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_actions": [
      "increase exploit attempts for local refinement",
      "introduce mild mutation to regression individuals",
      "monitor diversity for 2 more iterations before further adjustments"
    ]
  }
}
```
2025-06-26 16:24:22,770 - __main__ - INFO - 当前最佳适应度: 1937.0
2025-06-26 16:24:22,770 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\geometry2_12_route_1.pkl
2025-06-26 16:24:22,770 - __main__ - INFO - geometry2_12 开始进化第 3 代
2025-06-26 16:24:22,770 - __main__ - INFO - 开始分析阶段
2025-06-26 16:24:22,774 - StatsExpert - INFO - 开始统计分析
2025-06-26 16:24:22,776 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 1937.0, 'max': 2562.0, 'mean': 2273.0, 'std': 246.51937043567185}, 'diversity': 0.5314814814814816, 'clusters': {'clusters': 5, 'cluster_sizes': [3, 4, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 16:24:22,778 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 1937.0, 'max': 2562.0, 'mean': 2273.0, 'std': 246.51937043567185}, 'diversity_level': 0.5314814814814816, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [3, 4, 1, 1, 1]}, 'coordinates': [[264, 213], [210, 244], [156, 213], [156, 151], [210, 120], [264, 151], [291, 322], [129, 322], [48, 182], [129, 42], [291, 42], [372, 182]], 'distance_matrix': array([[  0.,  62., 108., 125., 108.,  62., 112., 174., 218., 218., 173.,
        112.],
       [ 62.,   0.,  62., 108., 124., 108., 112., 112., 173., 218., 218.,
        173.],
       [108.,  62.,   0.,  62., 108., 125., 174., 112., 112., 173., 218.,
        218.],
       [125., 108.,  62.,   0.,  62., 108., 218., 173., 112., 112., 174.,
        218.],
       [108., 124., 108.,  62.,   0.,  62., 218., 218., 173., 112., 112.,
        173.],
       [ 62., 108., 125., 108.,  62.,   0., 173., 218., 218., 174., 112.,
        112.],
       [112., 112., 174., 218., 218., 173.,   0., 162., 280., 323., 280.,
        162.],
       [174., 112., 112., 173., 218., 218., 162.,   0., 162., 280., 323.,
        280.],
       [218., 173., 112., 112., 173., 218., 280., 162.,   0., 162., 280.,
        324.],
       [218., 218., 173., 112., 112., 174., 323., 280., 162.,   0., 162.,
        280.],
       [173., 218., 218., 174., 112., 112., 280., 323., 280., 162.,   0.,
        162.],
       [112., 173., 218., 218., 173., 112., 162., 280., 324., 280., 162.,
          0.]])}
2025-06-26 16:24:22,779 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 16:24:22,779 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 16:24:22,780 - PathExpert - INFO - 开始路径结构分析
2025-06-26 16:24:22,780 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 16:24:22,780 - PathExpert - INFO - 路径结构分析完成
2025-06-26 16:24:22,780 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 16:24:22,780 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 16:24:22,780 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 16:24:22,780 - EliteExpert - INFO - 开始精英解分析
2025-06-26 16:24:22,814 - EliteExpert - INFO - 精英解分析完成
2025-06-26 16:24:22,814 - __main__ - INFO - 精英专家分析报告: {'elite_count': 108, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 593.0, 'avg_gap': 929.0}, 'structure_gap': {'unique_elite_edges': 36, 'unique_pop_edges': 29, 'common_edges': 12}}, 'elite_diversity': {'diversity_score': 0.4980673820237683}}
2025-06-26 16:24:22,814 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 16:24:22,814 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 16:24:22,814 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 16:24:22,814 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 1937.0, mean 2273.0, max 2562.0, std 246.51937043567185
- diversity: 0.5314814814814816
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [170.0, 1964.0, 1937.0] (Δ 13.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [48.0, 42.0, 372.0, 322.0]
- centroid/spread: [210.0, 182.0] | rms 122.586
- density_grid(3x3): [[1, 1, 1], [1, 2, 3], [1, 1, 1]]
- edge_len_stats(min,q1,med,q3,max): [62.0, 112.0, 167.5, 218.0, 324.0]
- long_edge_ratio: 0.136
- direction_hist(8 sectors): [2, 1, 2, 1, 2, 2, 0, 1]
- nn_median_dist: 87.28

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 16:24:22,819 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:24:44,271 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate (wide edge length range 62-324, std dev ~246.5)",
    "modality": "Potentially multimodal (direction histogram shows varied edge orientations)",
    "deceptiveness": "Possible mild deceptiveness (long_edge_ratio 0.136 suggests some misleading long edges)"
  },
  "population_state": {
    "coverage": "Partial (diversity 0.53, density_grid shows uneven distribution with one dense cell)",
    "convergence": "Early stage (0.0 convergence, recent best costs still improving)"
  },
  "difficult_regions": [
    {"type": "long-edge corridor", "evidence": "max_edge_len 324, long_edge_ratio >0.1"},
    {"type": "low-density zone", "evidence": "density_grid shows 4 cells with only 1 node"}
  ],
  "opportunity_regions": [
    {"type": "high-density cell", "evidence": "center grid cell contains 3 nodes"},
    {"type": "short-edge cluster", "evidence": "min_edge_len 62, q1 edge_len 112"}
  ],
  "evolution_phase": "Early exploration (iter 2/5, no elites yet)",
  "evolution_direction": [
    "Operator suggestions: Edge-exchange near dense cells, long-edge removal",
    "Focus area: Exploit center grid cell clusters while maintaining diversity"
  ]
}
```
2025-06-26 16:24:44,271 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 16:24:44,271 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate (wide edge length range 62-324, std dev ~246.5)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio 0.136 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Partial (diversity 0.53, density_grid shows uneven distribution with one dense cell)', 'convergence': 'Early stage (0.0 convergence, recent best costs still improving)'}, 'difficult_regions': [{'type': 'long-edge corridor', 'evidence': 'max_edge_len 324, long_edge_ratio >0.1'}, {'type': 'low-density zone', 'evidence': 'density_grid shows 4 cells with only 1 node'}], 'opportunity_regions': [{'type': 'high-density cell', 'evidence': 'center grid cell contains 3 nodes'}, {'type': 'short-edge cluster', 'evidence': 'min_edge_len 62, q1 edge_len 112'}], 'evolution_phase': 'Early exploration (iter 2/5, no elites yet)', 'evolution_direction': ['Operator suggestions: Edge-exchange near dense cells, long-edge removal', 'Focus area: Exploit center grid cell clusters while maintaining diversity']}
2025-06-26 16:24:44,271 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 16:24:44,271 - __main__ - INFO - 分析阶段完成
2025-06-26 16:24:44,271 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate (wide edge length range 62-324, std dev ~246.5)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio 0.136 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Partial (diversity 0.53, density_grid shows uneven distribution with one dense cell)', 'convergence': 'Early stage (0.0 convergence, recent best costs still improving)'}, 'difficult_regions': [{'type': 'long-edge corridor', 'evidence': 'max_edge_len 324, long_edge_ratio >0.1'}, {'type': 'low-density zone', 'evidence': 'density_grid shows 4 cells with only 1 node'}], 'opportunity_regions': [{'type': 'high-density cell', 'evidence': 'center grid cell contains 3 nodes'}, {'type': 'short-edge cluster', 'evidence': 'min_edge_len 62, q1 edge_len 112'}], 'evolution_phase': 'Early exploration (iter 2/5, no elites yet)', 'evolution_direction': ['Operator suggestions: Edge-exchange near dense cells, long-edge removal', 'Focus area: Exploit center grid cell clusters while maintaining diversity']}
2025-06-26 16:24:44,271 - __main__ - INFO - 开始策略分配阶段
2025-06-26 16:24:44,271 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 16:24:44,271 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 1937.0
  • mean_cost: 2273.0
- Search-space: ruggedness Moderate (wide edge length range 62-324, std dev ~246.5), modality Potentially multimodal (direction histogram shows varied edge orientations), deceptiveness Possible mild deceptiveness (long_edge_ratio 0.136 suggests some misleading long edges)
- Evolution phase: Early exploration (iter 2/5, no elites yet)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'long-edge , {'type': 'low-densit
- Opportunity regions (# 2): {'type': 'high-densi, {'type': 'short-edge
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 16:24:44,271 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 16:24:44,271 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:24:54,678 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 16:24:54,681 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 16:24:54,681 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 16:24:54,681 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 16:24:54,683 - __main__ - INFO - 策略分配阶段完成
2025-06-26 16:24:54,683 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 16:24:54,683 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 16:24:54,683 - __main__ - INFO - 开始进化阶段
2025-06-26 16:24:54,683 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 16:24:54,683 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:24:54,683 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2114.0 (rank 40.0%)
- Population diversity: 0.3090909090909091
- Evolution phase: Early exploration (iter 2/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:24:54,683 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:24:54,683 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:25:02,782 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11],
  "targeted_regions": "high-, short-opportunity regions & adjacent low-density",
  "strategy_comment": "Balanced exploration of sparse cells near opportunities"
}
```
2025-06-26 16:25:02,782 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:25:02,785 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2069.0, 路径: [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11]
2025-06-26 16:25:02,785 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11], 'cur_cost': 2069.0}
2025-06-26 16:25:02,785 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 16:25:02,785 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:25:02,785 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2109.0 (rank 30.0%)
- Population diversity: 0.3181818181818182
- Evolution phase: Early exploration (iter 2/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:25:02,785 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:25:02,785 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:25:10,757 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "high-opportunity, low-density adjacent",
  "strategy_comment": "Alternate high/low nodes to explore sparse regions"
}
```
2025-06-26 16:25:10,759 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:25:10,759 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:25:10,759 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:25:10,759 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 16:25:10,759 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:25:10,759 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2562.0 (rank 60.0%)
- Population diversity: 0.37272727272727274
- Evolution phase: Early exploration (iter 2/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:25:10,760 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:25:10,760 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:25:18,839 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "high-density, short-distance",
  "strategy_comment": "Alternate high/low nodes to balance exploration"
}
```
2025-06-26 16:25:18,839 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:25:18,839 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:25:18,839 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:25:18,839 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 16:25:18,839 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:25:18,839 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2562.0 (rank 70.0%)
- Population diversity: 0.37272727272727274
- Evolution phase: Early exploration (iter 2/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:25:18,839 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:25:18,839 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:25:26,934 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11],
  "targeted_regions": "high-d, short",
  "strategy_comment": "Balanced exploration of high-density and short regions"
}
```
2025-06-26 16:25:26,934 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:25:26,934 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1981.0, 路径: [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11]
2025-06-26 16:25:26,934 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11], 'cur_cost': 1981.0}
2025-06-26 16:25:26,937 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 16:25:26,937 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:25:26,937 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2562.0 (rank 80.0%)
- Population diversity: 0.38181818181818183
- Evolution phase: Early exploration (iter 2/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:25:26,937 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:25:26,938 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:25:35,564 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9],
  "targeted_regions": "high-, short",
  "strategy_comment": "Balanced exploration of high- and short regions, avoiding long- and low-d"
}
```
2025-06-26 16:25:35,564 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:25:35,566 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2562.0, 路径: [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9]
2025-06-26 16:25:35,566 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}
2025-06-26 16:25:35,566 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 16:25:35,566 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:25:35,566 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2109.0 (rank 40.0%)
- Population diversity: 0.38181818181818183
- Evolution phase: Early exploration (iter 2/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:25:35,566 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:25:35,566 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:25:43,242 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "high-, short",
  "strategy_comment": "Alternate high/low nodes to explore sparse regions"
}
```
2025-06-26 16:25:43,245 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:25:43,245 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:25:43,245 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:25:43,245 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 16:25:43,245 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:25:43,245 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2562.0 (rank 80.0%)
- Population diversity: 0.38181818181818183
- Evolution phase: Early exploration (iter 2/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:25:43,247 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:25:43,247 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:25:51,016 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11],
  "targeted_regions": "high-opportunity, low-density adjacent",
  "strategy_comment": "Balance exploration of sparse cells near opportunities"
}
```
2025-06-26 16:25:51,016 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:25:51,016 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1981.0, 路径: [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11]
2025-06-26 16:25:51,016 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11], 'cur_cost': 1981.0}
2025-06-26 16:25:51,016 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 16:25:51,016 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:25:51,016 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:25:51,016 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1961.0
2025-06-26 16:25:51,517 - ExploitationExpert - INFO - res_population_num: 109
2025-06-26 16:25:51,518 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:25:51,518 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64)]
2025-06-26 16:25:51,536 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:25:51,536 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11], 'cur_cost': 2069.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11], 'cur_cost': 1981.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11], 'cur_cost': 1981.0}, {'tour': array([ 9,  8,  0,  5,  3,  2, 10,  6,  7,  1,  4, 11]), 'cur_cost': 1961.0}, {'tour': array([11,  8,  1, 10,  9,  7,  6,  4,  0,  3,  5,  2]), 'cur_cost': 2221.0}, {'tour': array([ 4,  3,  7,  6,  8,  0, 10,  9,  2,  5, 11,  1]), 'cur_cost': 1937.0}]
2025-06-26 16:25:51,538 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:25:51,538 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 25, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 25, 'cache_hits': 0, 'similarity_calculations': 174, 'cache_hit_rate': 0.0, 'cache_size': 174}}
2025-06-26 16:25:51,538 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 16:25:51,539 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 16:25:51,539 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:25:51,539 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:25:51,539 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 1917.0
2025-06-26 16:25:52,044 - ExploitationExpert - INFO - res_population_num: 109
2025-06-26 16:25:52,045 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:25:52,045 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64)]
2025-06-26 16:25:52,063 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:25:52,064 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11], 'cur_cost': 2069.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11], 'cur_cost': 1981.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11], 'cur_cost': 1981.0}, {'tour': array([ 9,  8,  0,  5,  3,  2, 10,  6,  7,  1,  4, 11]), 'cur_cost': 1961.0}, {'tour': array([ 5,  7,  1,  6,  8,  3,  0, 10,  4,  2,  9, 11]), 'cur_cost': 1917.0}, {'tour': array([ 4,  3,  7,  6,  8,  0, 10,  9,  2,  5, 11,  1]), 'cur_cost': 1937.0}]
2025-06-26 16:25:52,065 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 16:25:52,065 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 26, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 26, 'cache_hits': 0, 'similarity_calculations': 181, 'cache_hit_rate': 0.0, 'cache_size': 181}}
2025-06-26 16:25:52,065 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 16:25:52,065 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 16:25:52,065 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:25:52,066 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:25:52,066 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 2161.0
2025-06-26 16:25:52,569 - ExploitationExpert - INFO - res_population_num: 109
2025-06-26 16:25:52,569 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:25:52,571 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64)]
2025-06-26 16:25:52,589 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:25:52,589 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11], 'cur_cost': 2069.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11], 'cur_cost': 1981.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11], 'cur_cost': 1981.0}, {'tour': array([ 9,  8,  0,  5,  3,  2, 10,  6,  7,  1,  4, 11]), 'cur_cost': 1961.0}, {'tour': array([ 5,  7,  1,  6,  8,  3,  0, 10,  4,  2,  9, 11]), 'cur_cost': 1917.0}, {'tour': array([ 8,  5,  9,  0,  2,  7, 11,  3,  6,  1, 10,  4]), 'cur_cost': 2161.0}]
2025-06-26 16:25:52,591 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:25:52,591 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 27, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 27, 'cache_hits': 0, 'similarity_calculations': 189, 'cache_hit_rate': 0.0, 'cache_size': 189}}
2025-06-26 16:25:52,592 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 16:25:52,592 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11], 'cur_cost': 2069.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11], 'cur_cost': 1981.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 5, 8, 1, 4, 9, 2, 6, 11], 'cur_cost': 1981.0}}]
2025-06-26 16:25:52,592 - __main__ - INFO - 进化阶段完成
2025-06-26 16:25:52,592 - __main__ - INFO - 开始评估阶段
2025-06-26 16:25:52,593 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 16:25:52,593 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 16:25:52,593 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 1937.0 → 1917.0 (Δ 20.0)
- Mean cost: 2273.0 → 2158.6
- Diversity: 0.309 → 0.427 (Δ 0.1182)

#### Elite metrics
- Elite count: 108 → 109
- Best elite cost: 1344 → 1344 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 57.1% , avg Δ 147.57
- Exploit: 3 inds, success 66.7% , avg Δ 37.0

#### Other indicators
- No-change individuals: 1
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [170.0, 1964.0, 1937.0], diversity [0.7288888888888888, 0.5962962962962963, 0.5314814814814816]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 16:25:52,594 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:26:08,309 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_delta": 147.57,
      "contribution": "high impact, high variability"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_delta": 37.0,
      "contribution": "consistent but smaller improvements"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore-heavy but effective; slight exploit underutilization"
  },
  "recommendations": {
    "explore_ratio": 0.65,
    "diversity_weight": 0.75,
    "additional_actions": "increase exploit attempts on top elites, enforce diversity preservation"
  }
}
```
2025-06-26 16:26:08,310 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 16:26:08,310 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_delta": 147.57,
      "contribution": "high impact, high variability"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_delta": 37.0,
      "contribution": "consistent but smaller improvements"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore-heavy but effective; slight exploit underutilization"
  },
  "recommendations": {
    "explore_ratio": 0.65,
    "diversity_weight": 0.75,
    "additional_actions": "increase exploit attempts on top elites, enforce diversity preservation"
  }
}
```
2025-06-26 16:26:08,310 - __main__ - INFO - 评估阶段完成
2025-06-26 16:26:08,310 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_delta": 147.57,
      "contribution": "high impact, high variability"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_delta": 37.0,
      "contribution": "consistent but smaller improvements"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore-heavy but effective; slight exploit underutilization"
  },
  "recommendations": {
    "explore_ratio": 0.65,
    "diversity_weight": 0.75,
    "additional_actions": "increase exploit attempts on top elites, enforce diversity preservation"
  }
}
```
2025-06-26 16:26:08,312 - __main__ - INFO - 当前最佳适应度: 1917.0
2025-06-26 16:26:08,312 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\geometry2_12_route_2.pkl
2025-06-26 16:26:08,312 - __main__ - INFO - geometry2_12 开始进化第 4 代
2025-06-26 16:26:08,312 - __main__ - INFO - 开始分析阶段
2025-06-26 16:26:08,312 - StatsExpert - INFO - 开始统计分析
2025-06-26 16:26:08,317 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 1917.0, 'max': 2562.0, 'mean': 2158.6, 'std': 201.86391455631687}, 'diversity': 0.7425925925925927, 'clusters': {'clusters': 7, 'cluster_sizes': [1, 3, 2, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 16:26:08,317 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 1917.0, 'max': 2562.0, 'mean': 2158.6, 'std': 201.86391455631687}, 'diversity_level': 0.7425925925925927, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [1, 3, 2, 1, 1, 1, 1]}, 'coordinates': [[264, 213], [210, 244], [156, 213], [156, 151], [210, 120], [264, 151], [291, 322], [129, 322], [48, 182], [129, 42], [291, 42], [372, 182]], 'distance_matrix': array([[  0.,  62., 108., 125., 108.,  62., 112., 174., 218., 218., 173.,
        112.],
       [ 62.,   0.,  62., 108., 124., 108., 112., 112., 173., 218., 218.,
        173.],
       [108.,  62.,   0.,  62., 108., 125., 174., 112., 112., 173., 218.,
        218.],
       [125., 108.,  62.,   0.,  62., 108., 218., 173., 112., 112., 174.,
        218.],
       [108., 124., 108.,  62.,   0.,  62., 218., 218., 173., 112., 112.,
        173.],
       [ 62., 108., 125., 108.,  62.,   0., 173., 218., 218., 174., 112.,
        112.],
       [112., 112., 174., 218., 218., 173.,   0., 162., 280., 323., 280.,
        162.],
       [174., 112., 112., 173., 218., 218., 162.,   0., 162., 280., 323.,
        280.],
       [218., 173., 112., 112., 173., 218., 280., 162.,   0., 162., 280.,
        324.],
       [218., 218., 173., 112., 112., 174., 323., 280., 162.,   0., 162.,
        280.],
       [173., 218., 218., 174., 112., 112., 280., 323., 280., 162.,   0.,
        162.],
       [112., 173., 218., 218., 173., 112., 162., 280., 324., 280., 162.,
          0.]])}
2025-06-26 16:26:08,318 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 16:26:08,318 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 16:26:08,318 - PathExpert - INFO - 开始路径结构分析
2025-06-26 16:26:08,319 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 16:26:08,319 - PathExpert - INFO - 路径结构分析完成
2025-06-26 16:26:08,320 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 16:26:08,320 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 16:26:08,320 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 16:26:08,320 - EliteExpert - INFO - 开始精英解分析
2025-06-26 16:26:08,356 - EliteExpert - INFO - 精英解分析完成
2025-06-26 16:26:08,356 - __main__ - INFO - 精英专家分析报告: {'elite_count': 109, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 573.0, 'avg_gap': 814.5999999999999}, 'structure_gap': {'unique_elite_edges': 32, 'unique_pop_edges': 44, 'common_edges': 16}}, 'elite_diversity': {'diversity_score': 0.4970551591346698}}
2025-06-26 16:26:08,356 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 16:26:08,356 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 16:26:08,356 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 16:26:08,356 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 1917.0, mean 2158.6, max 2562.0, std 201.86391455631687
- diversity: 0.7425925925925927
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [1964.0, 1937.0, 1917.0] (Δ 23.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [48.0, 42.0, 372.0, 322.0]
- centroid/spread: [210.0, 182.0] | rms 122.586
- density_grid(3x3): [[1, 1, 1], [1, 2, 3], [1, 1, 1]]
- edge_len_stats(min,q1,med,q3,max): [62.0, 112.0, 167.5, 218.0, 324.0]
- long_edge_ratio: 0.136
- direction_hist(8 sectors): [2, 1, 2, 1, 2, 2, 0, 1]
- nn_median_dist: 87.28

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 16:26:08,356 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:26:30,030 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate (wide edge length range: 62-324, Q3-Q1 spread: 106)",
    "modality": "Potentially multimodal (direction histogram shows varied edge orientations)",
    "deceptiveness": "Low (no extreme long edges, long_edge_ratio only 13.6%)"
  },
  "population_state": {
    "coverage": "Good (diversity 0.74, density_grid shows exploration in all regions)",
    "convergence": "Early-stage (0.0 convergence, recent best costs still improving)"
  },
  "difficult_regions": [
    {"type": "Low-density corridor", "location": "Top-left grid cell (density=1)"},
    {"type": "Long-edge region", "location": "Edges >300 units in length"}
  ],
  "opportunity_regions": [
    {"type": "High-density cluster", "location": "Center-right grid cell (density=3)"},
    {"type": "Short-edge region", "location": "Edges near median length (167.5 units)"}
  ],
  "evolution_phase": "Exploration-dominant",
  "evolution_direction": [
    "Prioritize 2-opt around high-density clusters",
    "Add path-relinking between elite solutions when they emerge",
    "Maintain mutation rate to explore low-density regions",
    "Monitor direction histogram for emerging patterns"
  ]
}
```
2025-06-26 16:26:30,030 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 16:26:30,030 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate (wide edge length range: 62-324, Q3-Q1 spread: 106)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations)', 'deceptiveness': 'Low (no extreme long edges, long_edge_ratio only 13.6%)'}, 'population_state': {'coverage': 'Good (diversity 0.74, density_grid shows exploration in all regions)', 'convergence': 'Early-stage (0.0 convergence, recent best costs still improving)'}, 'difficult_regions': [{'type': 'Low-density corridor', 'location': 'Top-left grid cell (density=1)'}, {'type': 'Long-edge region', 'location': 'Edges >300 units in length'}], 'opportunity_regions': [{'type': 'High-density cluster', 'location': 'Center-right grid cell (density=3)'}, {'type': 'Short-edge region', 'location': 'Edges near median length (167.5 units)'}], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Prioritize 2-opt around high-density clusters', 'Add path-relinking between elite solutions when they emerge', 'Maintain mutation rate to explore low-density regions', 'Monitor direction histogram for emerging patterns']}
2025-06-26 16:26:30,030 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 16:26:30,030 - __main__ - INFO - 分析阶段完成
2025-06-26 16:26:30,030 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate (wide edge length range: 62-324, Q3-Q1 spread: 106)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations)', 'deceptiveness': 'Low (no extreme long edges, long_edge_ratio only 13.6%)'}, 'population_state': {'coverage': 'Good (diversity 0.74, density_grid shows exploration in all regions)', 'convergence': 'Early-stage (0.0 convergence, recent best costs still improving)'}, 'difficult_regions': [{'type': 'Low-density corridor', 'location': 'Top-left grid cell (density=1)'}, {'type': 'Long-edge region', 'location': 'Edges >300 units in length'}], 'opportunity_regions': [{'type': 'High-density cluster', 'location': 'Center-right grid cell (density=3)'}, {'type': 'Short-edge region', 'location': 'Edges near median length (167.5 units)'}], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Prioritize 2-opt around high-density clusters', 'Add path-relinking between elite solutions when they emerge', 'Maintain mutation rate to explore low-density regions', 'Monitor direction histogram for emerging patterns']}
2025-06-26 16:26:30,030 - __main__ - INFO - 开始策略分配阶段
2025-06-26 16:26:30,030 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 16:26:30,030 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 1917.0
  • mean_cost: 2158.6
- Search-space: ruggedness Moderate (wide edge length range: 62-324, Q3-Q1 spread: 106), modality Potentially multimodal (direction histogram shows varied edge orientations), deceptiveness Low (no extreme long edges, long_edge_ratio only 13.6%)
- Evolution phase: Exploration-dominant
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'Low-densit, {'type': 'Long-edge 
- Opportunity regions (# 2): {'type': 'High-densi, {'type': 'Short-edge
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 16:26:30,030 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 16:26:30,030 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:26:41,751 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 16:26:41,751 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 16:26:41,753 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 16:26:41,753 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 16:26:41,753 - __main__ - INFO - 策略分配阶段完成
2025-06-26 16:26:41,753 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 16:26:41,753 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 16:26:41,753 - __main__ - INFO - 开始进化阶段
2025-06-26 16:26:41,753 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 16:26:41,755 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:26:41,755 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2069.0 (rank 40.0%)
- Population diversity: 0.42727272727272725
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Low-d, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:26:41,755 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:26:41,755 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:26:51,513 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Alternate between high-opportunity and sparse regions to balance exploration"
}
```
2025-06-26 16:26:51,513 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:26:51,513 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:26:51,517 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:26:51,518 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 16:26:51,518 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:26:51,518 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2318.0 (rank 50.0%)
- Population diversity: 0.4090909090909091
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Low-d, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:26:51,518 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:26:51,518 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:27:00,083 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Balance exploration with cost by targeting high-opportunity regions"
}
```
2025-06-26 16:27:00,083 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:27:00,083 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2069.0, 路径: [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11]
2025-06-26 16:27:00,084 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11], 'cur_cost': 2069.0}
2025-06-26 16:27:00,084 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 16:27:00,084 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:27:00,084 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2318.0 (rank 60.0%)
- Population diversity: 0.42727272727272725
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Low-d, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:27:00,084 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:27:00,084 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:27:07,909 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9],
  "targeted_regions": "High-opportunity and adjacent sparse cells",
  "strategy_comment": "Balanced exploration of high-opportunity and sparse regions"
}
```
2025-06-26 16:27:07,909 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:27:07,917 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2562.0, 路径: [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9]
2025-06-26 16:27:07,917 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}
2025-06-26 16:27:07,917 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 16:27:07,917 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:27:07,917 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 1981.0 (rank 20.0%)
- Population diversity: 0.42727272727272725
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Low-d, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:27:07,917 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:27:07,917 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:27:15,829 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Alternate high and low nodes to explore sparse regions"
}
```
2025-06-26 16:27:15,837 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:27:15,837 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:27:15,837 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:27:15,837 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 16:27:15,837 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:27:15,837 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2562.0 (rank 80.0%)
- Population diversity: 0.42727272727272725
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Low-d, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:27:15,837 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:27:15,837 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:27:23,407 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Alternate between high-opportunity and sparse regions to balance exploration"
}
```
2025-06-26 16:27:23,410 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:27:23,410 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:27:23,410 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:27:23,410 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 16:27:23,412 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:27:23,412 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2318.0 (rank 50.0%)
- Population diversity: 0.42727272727272725
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Low-d, {'type': 'Long-
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:27:23,412 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:27:23,412 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:27:31,185 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Alternate even-odd nodes to balance exploration"
}
```
2025-06-26 16:27:31,185 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:27:31,185 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:27:31,185 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:27:31,185 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 16:27:31,185 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:27:31,193 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:27:31,193 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 1841.0
2025-06-26 16:27:31,695 - ExploitationExpert - INFO - res_population_num: 109
2025-06-26 16:27:31,696 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:27:31,696 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64)]
2025-06-26 16:27:31,716 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:27:31,716 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11], 'cur_cost': 2069.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': array([ 2,  5,  0, 11,  3,  4, 10,  1,  8,  9,  6,  7]), 'cur_cost': 1841.0}, {'tour': array([ 9,  8,  0,  5,  3,  2, 10,  6,  7,  1,  4, 11]), 'cur_cost': 1961.0}, {'tour': array([ 5,  7,  1,  6,  8,  3,  0, 10,  4,  2,  9, 11]), 'cur_cost': 1917.0}, {'tour': array([ 8,  5,  9,  0,  2,  7, 11,  3,  6,  1, 10,  4]), 'cur_cost': 2161.0}]
2025-06-26 16:27:31,716 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:27:31,718 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 28, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 28, 'cache_hits': 0, 'similarity_calculations': 198, 'cache_hit_rate': 0.0, 'cache_size': 198}}
2025-06-26 16:27:31,718 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 16:27:31,718 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 16:27:31,718 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:27:31,719 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:27:31,719 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 2320.0
2025-06-26 16:27:32,221 - ExploitationExpert - INFO - res_population_num: 110
2025-06-26 16:27:32,222 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:27:32,222 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64)]
2025-06-26 16:27:32,242 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:27:32,242 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11], 'cur_cost': 2069.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': array([ 2,  5,  0, 11,  3,  4, 10,  1,  8,  9,  6,  7]), 'cur_cost': 1841.0}, {'tour': array([11,  3,  1,  5,  6, 10,  4,  7,  9,  2,  0,  8]), 'cur_cost': 2320.0}, {'tour': array([ 5,  7,  1,  6,  8,  3,  0, 10,  4,  2,  9, 11]), 'cur_cost': 1917.0}, {'tour': array([ 8,  5,  9,  0,  2,  7, 11,  3,  6,  1, 10,  4]), 'cur_cost': 2161.0}]
2025-06-26 16:27:32,242 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:27:32,243 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 29, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 29, 'cache_hits': 0, 'similarity_calculations': 208, 'cache_hit_rate': 0.0, 'cache_size': 208}}
2025-06-26 16:27:32,243 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 16:27:32,244 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 16:27:32,244 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:27:32,244 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:27:32,245 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 2238.0
2025-06-26 16:27:32,746 - ExploitationExpert - INFO - res_population_num: 110
2025-06-26 16:27:32,748 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:27:32,748 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64)]
2025-06-26 16:27:32,766 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:27:32,766 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11], 'cur_cost': 2069.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': array([ 2,  5,  0, 11,  3,  4, 10,  1,  8,  9,  6,  7]), 'cur_cost': 1841.0}, {'tour': array([11,  3,  1,  5,  6, 10,  4,  7,  9,  2,  0,  8]), 'cur_cost': 2320.0}, {'tour': array([ 1,  3, 11,  8,  5,  4,  9,  6,  0,  2, 10,  7]), 'cur_cost': 2238.0}, {'tour': array([ 8,  5,  9,  0,  2,  7, 11,  3,  6,  1, 10,  4]), 'cur_cost': 2161.0}]
2025-06-26 16:27:32,767 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:27:32,767 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 30, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 30, 'cache_hits': 0, 'similarity_calculations': 219, 'cache_hit_rate': 0.0, 'cache_size': 219}}
2025-06-26 16:27:32,768 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 16:27:32,768 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 16:27:32,768 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:27:32,768 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:27:32,769 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 1757.0
2025-06-26 16:27:33,271 - ExploitationExpert - INFO - res_population_num: 110
2025-06-26 16:27:33,271 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:27:33,272 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64)]
2025-06-26 16:27:33,290 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:27:33,290 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11], 'cur_cost': 2069.0}, {'tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': array([ 2,  5,  0, 11,  3,  4, 10,  1,  8,  9,  6,  7]), 'cur_cost': 1841.0}, {'tour': array([11,  3,  1,  5,  6, 10,  4,  7,  9,  2,  0,  8]), 'cur_cost': 2320.0}, {'tour': array([ 1,  3, 11,  8,  5,  4,  9,  6,  0,  2, 10,  7]), 'cur_cost': 2238.0}, {'tour': array([ 2,  3,  7,  4, 10,  0,  1,  8,  9,  5, 11,  6]), 'cur_cost': 1757.0}]
2025-06-26 16:27:33,292 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:27:33,293 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 31, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 31, 'cache_hits': 0, 'similarity_calculations': 231, 'cache_hit_rate': 0.0, 'cache_size': 231}}
2025-06-26 16:27:33,293 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 16:27:33,293 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 5, 8, 2, 9, 1, 6, 4, 11], 'cur_cost': 2069.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 10, 2, 5, 8, 11, 1, 4, 6, 9], 'cur_cost': 2562.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}]
2025-06-26 16:27:33,293 - __main__ - INFO - 进化阶段完成
2025-06-26 16:27:33,294 - __main__ - INFO - 开始评估阶段
2025-06-26 16:27:33,294 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 16:27:33,294 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 16:27:33,295 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 1917.0 → 1757.0 (Δ 160.0)
- Mean cost: 2158.6 → 2205.9
- Diversity: 0.427 → 0.445 (Δ 0.0182)

#### Elite metrics
- Elite count: 109 → 110
- Best elite cost: 1344 → 1344 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 33.3% , avg Δ -56.17
- Exploit: 4 inds, success 50.0% , avg Δ -34.0

#### Other indicators
- No-change individuals: 1
- Regression individuals: 5
- Historical trends: Last 3 iterations: costs [1964.0, 1937.0, 1917.0], diversity [0.5962962962962963, 0.5314814814814816, 0.7425925925925927]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 16:27:33,295 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:27:46,324 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_improvement": -56.17,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_improvement": -34.0,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "slightly_unbalanced_towards_exploit",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators to enhance exploration effectiveness."
  }
}
```
2025-06-26 16:27:46,326 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 16:27:46,326 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_improvement": -56.17,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_improvement": -34.0,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "slightly_unbalanced_towards_exploit",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators to enhance exploration effectiveness."
  }
}
```
2025-06-26 16:27:46,326 - __main__ - INFO - 评估阶段完成
2025-06-26 16:27:46,326 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_improvement": -56.17,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_improvement": -34.0,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "slightly_unbalanced_towards_exploit",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators to enhance exploration effectiveness."
  }
}
```
2025-06-26 16:27:46,326 - __main__ - INFO - 当前最佳适应度: 1757.0
2025-06-26 16:27:46,326 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\geometry2_12_route_3.pkl
2025-06-26 16:27:46,326 - __main__ - INFO - geometry2_12 开始进化第 5 代
2025-06-26 16:27:46,326 - __main__ - INFO - 开始分析阶段
2025-06-26 16:27:46,326 - StatsExpert - INFO - 开始统计分析
2025-06-26 16:27:46,331 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 1757.0, 'max': 2562.0, 'mean': 2205.9, 'std': 233.35100171201321}, 'diversity': 0.7333333333333332, 'clusters': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 16:27:46,333 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 1757.0, 'max': 2562.0, 'mean': 2205.9, 'std': 233.35100171201321}, 'diversity_level': 0.7333333333333332, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[264, 213], [210, 244], [156, 213], [156, 151], [210, 120], [264, 151], [291, 322], [129, 322], [48, 182], [129, 42], [291, 42], [372, 182]], 'distance_matrix': array([[  0.,  62., 108., 125., 108.,  62., 112., 174., 218., 218., 173.,
        112.],
       [ 62.,   0.,  62., 108., 124., 108., 112., 112., 173., 218., 218.,
        173.],
       [108.,  62.,   0.,  62., 108., 125., 174., 112., 112., 173., 218.,
        218.],
       [125., 108.,  62.,   0.,  62., 108., 218., 173., 112., 112., 174.,
        218.],
       [108., 124., 108.,  62.,   0.,  62., 218., 218., 173., 112., 112.,
        173.],
       [ 62., 108., 125., 108.,  62.,   0., 173., 218., 218., 174., 112.,
        112.],
       [112., 112., 174., 218., 218., 173.,   0., 162., 280., 323., 280.,
        162.],
       [174., 112., 112., 173., 218., 218., 162.,   0., 162., 280., 323.,
        280.],
       [218., 173., 112., 112., 173., 218., 280., 162.,   0., 162., 280.,
        324.],
       [218., 218., 173., 112., 112., 174., 323., 280., 162.,   0., 162.,
        280.],
       [173., 218., 218., 174., 112., 112., 280., 323., 280., 162.,   0.,
        162.],
       [112., 173., 218., 218., 173., 112., 162., 280., 324., 280., 162.,
          0.]])}
2025-06-26 16:27:46,333 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 16:27:46,333 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 16:27:46,333 - PathExpert - INFO - 开始路径结构分析
2025-06-26 16:27:46,334 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 16:27:46,334 - PathExpert - INFO - 路径结构分析完成
2025-06-26 16:27:46,334 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 16:27:46,335 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 16:27:46,335 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 16:27:46,335 - EliteExpert - INFO - 开始精英解分析
2025-06-26 16:27:46,366 - EliteExpert - INFO - 精英解分析完成
2025-06-26 16:27:46,366 - __main__ - INFO - 精英专家分析报告: {'elite_count': 110, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 413.0, 'avg_gap': 861.9000000000001}, 'structure_gap': {'unique_elite_edges': 28, 'unique_pop_edges': 46, 'common_edges': 20}}, 'elite_diversity': {'diversity_score': 0.4964136780650542}}
2025-06-26 16:27:46,366 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 16:27:46,366 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 16:27:46,374 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 16:27:46,374 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 1757.0, mean 2205.9, max 2562.0, std 233.35100171201321
- diversity: 0.7333333333333332
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [1937.0, 1917.0, 1757.0] (Δ 90.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [48.0, 42.0, 372.0, 322.0]
- centroid/spread: [210.0, 182.0] | rms 122.586
- density_grid(3x3): [[1, 1, 1], [1, 2, 3], [1, 1, 1]]
- edge_len_stats(min,q1,med,q3,max): [62.0, 112.0, 167.5, 218.0, 324.0]
- long_edge_ratio: 0.136
- direction_hist(8 sectors): [2, 1, 2, 1, 2, 2, 0, 1]
- nn_median_dist: 87.28

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 16:27:46,374 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:28:09,066 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate (edge length stats show wide range from 62.0 to 324.0, with Q1-Q3 spread of 112.0-218.0)",
    "modality": "Potentially multimodal (direction histogram shows non-uniform distribution across 8 sectors)",
    "deceptiveness": "Low to moderate (long_edge_ratio of 0.136 suggests some deceptive long connections, but not predominant)"
  },
  "population_state": {
    "coverage": "Fair (diversity 0.733, density_grid shows uneven distribution with one dense cell [2,3])",
    "convergence": "Early stage (convergence 0.0, recent best costs show improvement Δ90.00/iteration)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges >300 units)",
    "Low-density grid cells (particularly corners with density=1)"
  ],
  "opportunity_regions": [
    "High-density cell (center-right grid cell with density=3)",
    "Regions near centroid (210.0,182.0) where multiple paths intersect"
  ],
  "evolution_phase": "Exploration-dominant (high diversity, no elite convergence)",
  "evolution_direction": [
    "Intensify exploitation in high-density cells via 2-opt local search",
    "Maintain exploration in low-density regions using edge-recombination crossover",
    "Penalize long edges in selection to address difficult regions",
    "Consider spatial clustering of nodes to identify promising subpaths"
  ]
}
```
2025-06-26 16:28:09,074 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 16:28:09,074 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate (edge length stats show wide range from 62.0 to 324.0, with Q1-Q3 spread of 112.0-218.0)', 'modality': 'Potentially multimodal (direction histogram shows non-uniform distribution across 8 sectors)', 'deceptiveness': 'Low to moderate (long_edge_ratio of 0.136 suggests some deceptive long connections, but not predominant)'}, 'population_state': {'coverage': 'Fair (diversity 0.733, density_grid shows uneven distribution with one dense cell [2,3])', 'convergence': 'Early stage (convergence 0.0, recent best costs show improvement Δ90.00/iteration)'}, 'difficult_regions': ['Long-edge corridors (edges >300 units)', 'Low-density grid cells (particularly corners with density=1)'], 'opportunity_regions': ['High-density cell (center-right grid cell with density=3)', 'Regions near centroid (210.0,182.0) where multiple paths intersect'], 'evolution_phase': 'Exploration-dominant (high diversity, no elite convergence)', 'evolution_direction': ['Intensify exploitation in high-density cells via 2-opt local search', 'Maintain exploration in low-density regions using edge-recombination crossover', 'Penalize long edges in selection to address difficult regions', 'Consider spatial clustering of nodes to identify promising subpaths']}
2025-06-26 16:28:09,074 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 16:28:09,074 - __main__ - INFO - 分析阶段完成
2025-06-26 16:28:09,074 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate (edge length stats show wide range from 62.0 to 324.0, with Q1-Q3 spread of 112.0-218.0)', 'modality': 'Potentially multimodal (direction histogram shows non-uniform distribution across 8 sectors)', 'deceptiveness': 'Low to moderate (long_edge_ratio of 0.136 suggests some deceptive long connections, but not predominant)'}, 'population_state': {'coverage': 'Fair (diversity 0.733, density_grid shows uneven distribution with one dense cell [2,3])', 'convergence': 'Early stage (convergence 0.0, recent best costs show improvement Δ90.00/iteration)'}, 'difficult_regions': ['Long-edge corridors (edges >300 units)', 'Low-density grid cells (particularly corners with density=1)'], 'opportunity_regions': ['High-density cell (center-right grid cell with density=3)', 'Regions near centroid (210.0,182.0) where multiple paths intersect'], 'evolution_phase': 'Exploration-dominant (high diversity, no elite convergence)', 'evolution_direction': ['Intensify exploitation in high-density cells via 2-opt local search', 'Maintain exploration in low-density regions using edge-recombination crossover', 'Penalize long edges in selection to address difficult regions', 'Consider spatial clustering of nodes to identify promising subpaths']}
2025-06-26 16:28:09,074 - __main__ - INFO - 开始策略分配阶段
2025-06-26 16:28:09,074 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 16:28:09,074 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 1757.0
  • mean_cost: 2205.9
- Search-space: ruggedness Moderate (edge length stats show wide range from 62.0 to 324.0, with Q1-Q3 spread of 112.0-218.0), modality Potentially multimodal (direction histogram shows non-uniform distribution across 8 sectors), deceptiveness Low to moderate (long_edge_ratio of 0.136 suggests some deceptive long connections, but not predominant)
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Low-density grid cel
- Opportunity regions (# 2): High-density cell (c, Regions near centroi
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 16:28:09,074 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 16:28:09,074 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:28:19,908 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and landscape focus suggestion 'balance' favor exploration. Top 60% explore to discover new regions, bottom 40% exploit existing opportunities."
}
```
2025-06-26 16:28:19,908 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 16:28:19,908 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 16:28:19,908 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and landscape focus suggestion 'balance' favor exploration. Top 60% explore to discover new regions, bottom 40% exploit existing opportunities."
}
```
2025-06-26 16:28:19,908 - __main__ - INFO - 策略分配阶段完成
2025-06-26 16:28:19,908 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 16:28:19,908 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and landscape focus suggestion 'balance' favor exploration. Top 60% explore to discover new regions, bottom 40% exploit existing opportunities."
}
```
2025-06-26 16:28:19,908 - __main__ - INFO - 开始进化阶段
2025-06-26 16:28:19,908 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 16:28:19,908 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:28:19,908 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2318.0 (rank 40.0%)
- Population diversity: 0.44545454545454544
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:28:19,916 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:28:19,916 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:28:27,763 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Alternating high/low nodes to explore density edges"
}
```
2025-06-26 16:28:27,763 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:28:27,763 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:28:27,763 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:28:27,763 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 16:28:27,763 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:28:27,763 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2069.0 (rank 20.0%)
- Population diversity: 0.44545454545454544
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:28:27,763 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:28:27,763 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:28:35,996 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Alternating high/low nodes to explore sparse regions while balancing cost"
}
```
2025-06-26 16:28:35,996 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:28:35,996 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:28:35,996 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:28:35,996 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 16:28:35,996 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:28:35,996 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2562.0 (rank 90.0%)
- Population diversity: 0.4
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:28:35,996 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:28:35,996 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:28:44,120 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Alternate high/low density nodes to balance exploration"
}
```
2025-06-26 16:28:44,120 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:28:44,120 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:28:44,120 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:28:44,120 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 16:28:44,120 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:28:44,120 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2318.0 (rank 30.0%)
- Population diversity: 0.37272727272727274
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:28:44,120 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:28:44,120 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:28:51,822 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Alternate high/low density nodes to balance exploration"
}
```
2025-06-26 16:28:51,822 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:28:51,823 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:28:51,823 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:28:51,823 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 16:28:51,823 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:28:51,823 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2318.0 (rank 30.0%)
- Population diversity: 0.37272727272727274
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:28:51,823 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:28:51,823 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:28:59,701 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Alternate high/low density nodes to balance exploration"
}
```
2025-06-26 16:28:59,701 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:28:59,701 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:28:59,702 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:28:59,702 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 16:28:59,703 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 16:28:59,703 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 2318.0 (rank 30.0%)
- Population diversity: 0.37272727272727274
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 11]  
• Path length == 12 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 16:28:59,703 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 16:28:59,703 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:29:07,550 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Alternate high/low density nodes to balance exploration"
}
```
2025-06-26 16:29:07,552 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 16:29:07,552 - ExplorationExpert - INFO - 探索路径生成完成，成本: 2318.0, 路径: [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11]
2025-06-26 16:29:07,552 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}
2025-06-26 16:29:07,553 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 16:29:07,553 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:29:07,553 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:29:07,553 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 2182.0
2025-06-26 16:29:08,057 - ExploitationExpert - INFO - res_population_num: 110
2025-06-26 16:29:08,057 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:29:08,057 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64)]
2025-06-26 16:29:08,076 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:29:08,076 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': array([10,  2,  8,  0,  7,  1,  9,  5,  6,  4, 11,  3]), 'cur_cost': 2182.0}, {'tour': array([11,  3,  1,  5,  6, 10,  4,  7,  9,  2,  0,  8]), 'cur_cost': 2320.0}, {'tour': array([ 1,  3, 11,  8,  5,  4,  9,  6,  0,  2, 10,  7]), 'cur_cost': 2238.0}, {'tour': array([ 2,  3,  7,  4, 10,  0,  1,  8,  9,  5, 11,  6]), 'cur_cost': 1757.0}]
2025-06-26 16:29:08,076 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:29:08,076 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 32, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 32, 'cache_hits': 0, 'similarity_calculations': 244, 'cache_hit_rate': 0.0, 'cache_size': 244}}
2025-06-26 16:29:08,076 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 16:29:08,076 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 16:29:08,076 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:29:08,079 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:29:08,079 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 1921.0
2025-06-26 16:29:08,582 - ExploitationExpert - INFO - res_population_num: 110
2025-06-26 16:29:08,582 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:29:08,583 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64)]
2025-06-26 16:29:08,604 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:29:08,604 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': array([10,  2,  8,  0,  7,  1,  9,  5,  6,  4, 11,  3]), 'cur_cost': 2182.0}, {'tour': array([ 6,  5,  4, 11, 10,  1,  7,  3,  0,  2,  9,  8]), 'cur_cost': 1921.0}, {'tour': array([ 1,  3, 11,  8,  5,  4,  9,  6,  0,  2, 10,  7]), 'cur_cost': 2238.0}, {'tour': array([ 2,  3,  7,  4, 10,  0,  1,  8,  9,  5, 11,  6]), 'cur_cost': 1757.0}]
2025-06-26 16:29:08,605 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 16:29:08,605 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 33, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 33, 'cache_hits': 0, 'similarity_calculations': 258, 'cache_hit_rate': 0.0, 'cache_size': 258}}
2025-06-26 16:29:08,606 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 16:29:08,606 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 16:29:08,606 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:29:08,606 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:29:08,606 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 2086.0
2025-06-26 16:29:09,107 - ExploitationExpert - INFO - res_population_num: 110
2025-06-26 16:29:09,108 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:29:09,108 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64)]
2025-06-26 16:29:09,127 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:29:09,127 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': array([10,  2,  8,  0,  7,  1,  9,  5,  6,  4, 11,  3]), 'cur_cost': 2182.0}, {'tour': array([ 6,  5,  4, 11, 10,  1,  7,  3,  0,  2,  9,  8]), 'cur_cost': 1921.0}, {'tour': array([ 7,  2, 10,  6,  5,  0,  9,  8,  1,  4, 11,  3]), 'cur_cost': 2086.0}, {'tour': array([ 2,  3,  7,  4, 10,  0,  1,  8,  9,  5, 11,  6]), 'cur_cost': 1757.0}]
2025-06-26 16:29:09,128 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:29:09,128 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 34, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 34, 'cache_hits': 0, 'similarity_calculations': 273, 'cache_hit_rate': 0.0, 'cache_size': 273}}
2025-06-26 16:29:09,129 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 16:29:09,129 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 16:29:09,129 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 16:29:09,129 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 16:29:09,130 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 2054.0
2025-06-26 16:29:09,633 - ExploitationExpert - INFO - res_population_num: 110
2025-06-26 16:29:09,633 - ExploitationExpert - INFO - res_population_costs: [1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344, 1344]
2025-06-26 16:29:09,633 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  2,  3,  8,  7,  6, 11, 10,  9,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10, 11,  6,  7,  8,  9,  3,  2,  1], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  9, 10,  4,  5], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  7,  2,  1,  6], dtype=int64), array([ 0,  5, 10,  9,  4,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  8,  2,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5, 10,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  8,  3,  2,  1,  7,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5], dtype=int64), array([ 0,  5,  4, 10,  9,  3,  8,  7,  2,  1,  6, 11], dtype=int64), array([ 0,  5, 10,  4,  9,  3,  2,  8,  7,  1,  6, 11], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5,  4,  3,  9, 10, 11,  6,  7,  8,  2,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  9,  3,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  8,  9, 10, 11,  6,  7,  2,  1], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  6,  7,  8,  9, 10, 11], dtype=int64), array([ 0,  1,  7,  6, 11, 10,  9,  8,  2,  3,  4,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  2,  8,  7,  1], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0,  1,  7,  8,  2,  3,  9, 10,  4,  5, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9, 10,  4,  5, 11], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  2,  7,  8,  3,  9,  4, 10,  5], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  3,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  2,  7,  8,  3,  9,  4,  5, 10, 11,  6], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  6, 11,  5,  4, 10,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  1,  2,  3,  4,  5, 10,  9,  8,  7,  6, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  8,  2,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0,  6,  7,  8,  9, 10, 11,  5,  4,  3,  2,  1], dtype=int64), array([ 0,  5, 10, 11,  6,  7,  8,  9,  4,  3,  2,  1], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  7,  2,  1], dtype=int64), array([ 0,  5, 10,  4,  3,  9,  8,  2,  7,  1,  6, 11], dtype=int64), array([ 0,  5,  4,  3,  2,  1,  7,  8,  9, 10, 11,  6], dtype=int64), array([ 0,  6, 11, 10,  5,  4,  9,  8,  3,  2,  7,  1], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  9,  3,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  9,  3,  4, 10, 11,  5], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  9,  3,  4, 10,  5], dtype=int64), array([ 0, 11,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  5, 11, 10,  4,  9,  8,  3,  2,  7,  1,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  1,  2,  3,  4, 10,  9,  8,  7,  6, 11,  5], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  9,  3,  8,  7,  2,  1], dtype=int64), array([ 0,  1,  6,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  7,  6,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11,  6], dtype=int64), array([ 0, 11,  5,  4, 10,  9,  3,  2,  8,  7,  6,  1], dtype=int64), array([ 0, 11,  6,  7,  1,  2,  8,  3,  4,  9, 10,  5], dtype=int64), array([ 0, 11,  5, 10,  9,  4,  3,  8,  2,  1,  7,  6], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  4,  9, 10,  5, 11], dtype=int64), array([ 0,  6, 11,  5, 10,  4,  3,  9,  8,  2,  7,  1], dtype=int64), array([ 0,  5, 11, 10,  9,  4,  3,  2,  8,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  8,  2,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9, 10,  4,  5, 11], dtype=int64), array([ 0,  6,  7,  1,  2,  8,  3,  9,  4,  5, 10, 11], dtype=int64), array([ 0,  6,  1,  2,  7,  8,  3,  9,  4, 10, 11,  5], dtype=int64), array([ 0,  1,  7,  2,  3,  8,  9,  4, 10,  5, 11,  6], dtype=int64), array([ 0,  5,  4,  3,  2,  7,  8,  9, 10, 11,  6,  1], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10, 11,  5], dtype=int64), array([ 0, 11, 10,  5,  4,  3,  9,  8,  2,  7,  1,  6], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  9,  4, 10,  5, 11], dtype=int64), array([ 0,  6,  1,  7,  2,  8,  3,  4,  9, 10,  5, 11], dtype=int64)]
2025-06-26 16:29:09,651 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 16:29:09,651 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}, {'tour': array([10,  2,  8,  0,  7,  1,  9,  5,  6,  4, 11,  3]), 'cur_cost': 2182.0}, {'tour': array([ 6,  5,  4, 11, 10,  1,  7,  3,  0,  2,  9,  8]), 'cur_cost': 1921.0}, {'tour': array([ 7,  2, 10,  6,  5,  0,  9,  8,  1,  4, 11,  3]), 'cur_cost': 2086.0}, {'tour': array([ 2, 11,  3,  6,  8,  0,  9, 10,  5,  4,  1,  7]), 'cur_cost': 2054.0}]
2025-06-26 16:29:09,652 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 16:29:09,652 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 35, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 35, 'cache_hits': 0, 'similarity_calculations': 289, 'cache_hit_rate': 0.0, 'cache_size': 289}}
2025-06-26 16:29:09,654 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 16:29:09,654 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11], 'cur_cost': 2318.0}}]
2025-06-26 16:29:09,654 - __main__ - INFO - 进化阶段完成
2025-06-26 16:29:09,654 - __main__ - INFO - 开始评估阶段
2025-06-26 16:29:09,654 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 16:29:09,655 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 16:29:09,655 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 1757.0 → 1921.0 (Δ -164.0)
- Mean cost: 2205.9 → 2215.1
- Diversity: 0.445 → 0.336 (Δ -0.1091)

#### Elite metrics
- Elite count: 110 → 110
- Best elite cost: 1344 → 1344 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ -0.83
- Exploit: 4 inds, success 50.0% , avg Δ -21.75

#### Other indicators
- No-change individuals: 4
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [1937.0, 1917.0, 1757.0], diversity [0.5314814814814816, 0.7425925925925927, 0.7333333333333332]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 16:29:09,656 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 16:29:27,232 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "late-stage stagnation",
  "strategy_effectiveness": {
    "exploit": {
      "success_rate": 50.0,
      "avg_improvement": -21.75,
      "contribution": "primary"
    },
    "explore": {
      "success_rate": 16.7,
      "avg_improvement": -0.83,
      "contribution": "secondary"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "exploit is more effective but underutilized",
    "diversity_warning": "critical (0.336, -24.5% from last iteration)"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.85,
    "mutation_rate": "increase by 20%",
    "elite_preservation": "maintain current 110",
    "additional_actions": [
      "introduce 2-3 new random individuals per iteration",
      "implement small-cost perturbation for no-change individuals"
    ]
  }
}
```
2025-06-26 16:29:27,233 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 16:29:27,235 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "late-stage stagnation",
  "strategy_effectiveness": {
    "exploit": {
      "success_rate": 50.0,
      "avg_improvement": -21.75,
      "contribution": "primary"
    },
    "explore": {
      "success_rate": 16.7,
      "avg_improvement": -0.83,
      "contribution": "secondary"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "exploit is more effective but underutilized",
    "diversity_warning": "critical (0.336, -24.5% from last iteration)"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.85,
    "mutation_rate": "increase by 20%",
    "elite_preservation": "maintain current 110",
    "additional_actions": [
      "introduce 2-3 new random individuals per iteration",
      "implement small-cost perturbation for no-change individuals"
    ]
  }
}
```
2025-06-26 16:29:27,235 - __main__ - INFO - 评估阶段完成
2025-06-26 16:29:27,235 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "late-stage stagnation",
  "strategy_effectiveness": {
    "exploit": {
      "success_rate": 50.0,
      "avg_improvement": -21.75,
      "contribution": "primary"
    },
    "explore": {
      "success_rate": 16.7,
      "avg_improvement": -0.83,
      "contribution": "secondary"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "exploit is more effective but underutilized",
    "diversity_warning": "critical (0.336, -24.5% from last iteration)"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.85,
    "mutation_rate": "increase by 20%",
    "elite_preservation": "maintain current 110",
    "additional_actions": [
      "introduce 2-3 new random individuals per iteration",
      "implement small-cost perturbation for no-change individuals"
    ]
  }
}
```
2025-06-26 16:29:27,235 - __main__ - INFO - 当前最佳适应度: 1921.0
2025-06-26 16:29:27,235 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\geometry2_12_route_4.pkl
2025-06-26 16:29:27,247 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\geometry2_12_solution.json
2025-06-26 16:29:27,248 - __main__ - INFO - 实例 geometry2_12 处理完成
