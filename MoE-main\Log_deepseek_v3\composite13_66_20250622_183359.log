2025-06-22 18:33:59,230 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-22 18:33:59,230 - __main__ - INFO - 开始分析阶段
2025-06-22 18:33:59,230 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:33:59,251 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9989.0, 'max': 112680.0, 'mean': 75836.2, 'std': 43503.23567919977}, 'diversity': 0.9111111111111111, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:33:59,251 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9989.0, 'max': 112680.0, 'mean': 75836.2, 'std': 43503.23567919977}, 'diversity_level': 0.9111111111111111, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:33:59,252 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:33:59,252 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:33:59,252 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:33:59,257 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:33:59,257 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (53, 61), 'frequency': 0.5, 'avg_cost': 30.0}, {'edge': (28, 35), 'frequency': 0.5, 'avg_cost': 22.0}], 'common_subpaths': [{'subpath': (5, 4, 8), 'frequency': 0.3}, {'subpath': (4, 8, 2), 'frequency': 0.3}, {'subpath': (8, 2, 6), 'frequency': 0.3}, {'subpath': (2, 6, 10), 'frequency': 0.3}, {'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}, {'subpath': (53, 62, 59), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}, {'subpath': (59, 56, 58), 'frequency': 0.3}, {'subpath': (56, 58, 60), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(2, 6)', 'frequency': 0.4}, {'edge': '(53, 61)', 'frequency': 0.5}, {'edge': '(57, 64)', 'frequency': 0.4}, {'edge': '(14, 15)', 'frequency': 0.4}, {'edge': '(17, 18)', 'frequency': 0.4}, {'edge': '(20, 21)', 'frequency': 0.4}, {'edge': '(25, 26)', 'frequency': 0.4}, {'edge': '(26, 36)', 'frequency': 0.4}, {'edge': '(28, 35)', 'frequency': 0.5}, {'edge': '(30, 34)', 'frequency': 0.4}, {'edge': '(47, 49)', 'frequency': 0.4}, {'edge': '(46, 47)', 'frequency': 0.4}, {'edge': '(50, 51)', 'frequency': 0.4}, {'edge': '(41, 50)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(0, 1)', 'frequency': 0.2}, {'edge': '(1, 7)', 'frequency': 0.3}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(3, 9)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(5, 11)', 'frequency': 0.3}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(6, 10)', 'frequency': 0.3}, {'edge': '(10, 55)', 'frequency': 0.2}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(14, 63)', 'frequency': 0.2}, {'edge': '(15, 22)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(13, 20)', 'frequency': 0.2}, {'edge': '(19, 21)', 'frequency': 0.2}, {'edge': '(19, 27)', 'frequency': 0.3}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(32, 40)', 'frequency': 0.2}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(46, 48)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(39, 43)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(41, 42)', 'frequency': 0.2}, {'edge': '(0, 42)', 'frequency': 0.2}, {'edge': '(13, 21)', 'frequency': 0.2}, {'edge': '(11, 50)', 'frequency': 0.2}, {'edge': '(12, 31)', 'frequency': 0.2}, {'edge': '(37, 43)', 'frequency': 0.2}, {'edge': '(45, 62)', 'frequency': 0.3}, {'edge': '(22, 52)', 'frequency': 0.2}, {'edge': '(20, 49)', 'frequency': 0.2}, {'edge': '(15, 16)', 'frequency': 0.3}, {'edge': '(18, 19)', 'frequency': 0.2}, {'edge': '(2, 59)', 'frequency': 0.2}, {'edge': '(21, 24)', 'frequency': 0.2}, {'edge': '(40, 54)', 'frequency': 0.2}, {'edge': '(32, 38)', 'frequency': 0.2}, {'edge': '(57, 60)', 'frequency': 0.2}, {'edge': '(16, 65)', 'frequency': 0.2}, {'edge': '(13, 62)', 'frequency': 0.2}, {'edge': '(28, 33)', 'frequency': 0.2}, {'edge': '(27, 44)', 'frequency': 0.2}, {'edge': '(0, 37)', 'frequency': 0.2}, {'edge': '(4, 30)', 'frequency': 0.2}, {'edge': '(30, 60)', 'frequency': 0.2}, {'edge': '(10, 11)', 'frequency': 0.2}, {'edge': '(11, 12)', 'frequency': 0.2}, {'edge': '(8, 17)', 'frequency': 0.3}, {'edge': '(32, 47)', 'frequency': 0.2}, {'edge': '(38, 56)', 'frequency': 0.2}, {'edge': '(16, 48)', 'frequency': 0.2}, {'edge': '(41, 58)', 'frequency': 0.2}, {'edge': '(31, 52)', 'frequency': 0.2}, {'edge': '(1, 25)', 'frequency': 0.2}, {'edge': '(12, 34)', 'frequency': 0.2}, {'edge': '(9, 19)', 'frequency': 0.2}, {'edge': '(39, 64)', 'frequency': 0.2}, {'edge': '(26, 47)', 'frequency': 0.2}, {'edge': '(18, 26)', 'frequency': 0.2}, {'edge': '(21, 22)', 'frequency': 0.2}, {'edge': '(7, 23)', 'frequency': 0.2}, {'edge': '(43, 58)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [40, 29, 62, 45, 57, 46, 3], 'cost': 16056.0, 'size': 7}, {'region': [43, 6, 44, 56, 45, 62], 'cost': 13300.0, 'size': 6}, {'region': [4, 43, 56, 38, 5, 48], 'cost': 13012.0, 'size': 6}, {'region': [33, 61, 39, 64, 25], 'cost': 11497.0, 'size': 5}, {'region': [61, 42, 57, 43, 1], 'cost': 11098.0, 'size': 5}]}
2025-06-22 18:33:59,258 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:33:59,258 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:33:59,258 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:33:59,258 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-22 18:33:59,258 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-22 18:33:59,258 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:33:59,259 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:33:59,259 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:33:59,259 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=9989.0, max=112680.0, mean=75836.2, std=43503.23567919977
- Diversity: 0.9111111111111111, Convergence: 0.0
- Structure: 2 high quality edges, 10 common subpaths
- Elite: 0 solutions with 0 fixed nodes

## Evolution Context:
- Iteration: 0/5
- Historical trend: Not available
- Improvement rate: Not available
- Stagnation periods: None detected

## Problem Characteristics:
- TSP instance: Unknown with 0 nodes
- Search space topology: Likely rugged with many local optima
- Edge distribution: Few common high-quality edges
- Known difficult regions: 5 identified

## Key Relationships:
- Elite solutions share 0% of edges
- Population clustering: No clustering information
- Exploration vs exploitation balance: High exploration (>70%)
- Local optima density: Moderate

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality, deceptiveness)
2. Population state (diversity, convergence, clustering)
3. Difficult regions and specific challenges (edge crossings, isolated nodes)
4. Opportunity regions with potential for improvement
5. Current evolution phase (exploration, exploitation, refinement)
6. Recommended evolution direction with specific operator suggestions

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics including ruggedness, modality, and deceptiveness
- population_state: Current state assessment including diversity, convergence, and clustering
- difficult_regions: Identified challenges with specific node sequences or edges to avoid
- opportunity_regions: Promising areas with specific node sequences or edges to include
- evolution_phase: Current phase identification (exploration/exploitation/refinement)
- evolution_direction: Recommended strategy with specific operator suggestions

2025-06-22 18:34:06,093 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Highly Rugged. High standard deviation of costs (43503.23567919977) suggests significant variation in solution quality, indicating a rugged landscape. Few high-quality edges and a lack of convergence further support this.",
    "modality": "Multimodal.  The presence of 'few common high-quality edges' and the lack of elite solutions, alongside the ruggedness, strongly suggests the presence of multiple local optima.",
    "deceptiveness": "Potentially Deceptive. The rugged landscape and the absence of convergence, even in the initial iteration, hint at the possibility of deceptive regions where initial explorations might lead towards poorer solutions."
  },
  "population_state": {
    "diversity": "High. Diversity of 0.9111111111111111 is very high, which is expected given the early iteration and lack of convergence.",
    "convergence": "None. Convergence is at 0.0, confirming the population is far from converging towards a single good solution. This is consistent with the initial iteration and the high diversity.",
    "clustering": "No clustering information available. This limits the assessment of population structure, but the high diversity mitigates the immediate need to consider clustering."
  },
  "difficult_regions": {
    "challenges": "Edge Crossings and Local Optima are the primary challenges. The 'few common high-quality edges' suggest that the population is struggling to converge on beneficial edge combinations. The 'known difficult regions (5 identified)' also contribute to the complexity.",
    "specific_concerns": [
      "Avoiding known difficult regions.",
      "Identifying edges that consistently lead to poor performance.",
      "Avoiding early convergence in suboptimal edge combinations."
    ]
  },
  "opportunity_regions": {
    "potential": "Exploration of various edge combinations and potentially building upon the '2 high quality edges'.",
    "specific_suggestions": [
      "Carefully explore and evaluate the '2 high quality edges' and their surrounding neighborhoods.",
      "Experiment with adding/removing edges from the current population to diversify and search across different regions.",
       "Identify promising regions based on edge similarities among the population."
    ]
  },
  "evolution_phase": "Exploration.  The high diversity, lack of convergence, and the emphasis on edge exploration clearly indicate that the evolution is firmly in the exploration phase. This is expected at iteration 0/5.",
  "evolution_direction": {
    "strategy": "Aggressive Exploration with Local Refinement. Given the high diversity and the need to overcome potentially deceptive regions, a strategy that emphasizes exploration while introducing elements of local refinement is recommended.",
    "operator_suggestions": [
      {
        "operator": "Edge Exchange (or 2-opt, 3-opt, etc.)",
        "purpose": "Local search to improve the quality of existing solutions, even during exploration. Start with lower-order opt-operators and increase complexity after reaching a degree of local convergence. This will help to refine solutions and provide a more nuanced picture of the search space.",
        "parameters": "Apply with varying levels of aggressiveness. Start with more frequent applications to allow the algorithm to escape poor local optima. "
      },
      {
        "operator": "Edge Insertion/Deletion",
        "purpose": "To explore new edge combinations and escape suboptimal solutions. This operator allows the population to diversify and try different edges/paths.",
        "parameters": "Randomly insert and delete edges. Consider implementing a scoring method to determine the best edge to keep during the exploration phase."
      },
      {
        "operator": "Heuristic Crossover",
        "purpose": "If possible, and depending on the TSP instance, use crossover operators that prioritize the preservation of high-quality edges and subpaths.",
        "parameters": "Use crossover operators that specifically favor high-quality or promising edges or subpaths identified from the population."
      }
    ]
  }
}
```
2025-06-22 18:34:06,094 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:34:06,094 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Highly Rugged. High standard deviation of costs (43503.23567919977) suggests significant variation in solution quality, indicating a rugged landscape. Few high-quality edges and a lack of convergence further support this.', 'modality': "Multimodal.  The presence of 'few common high-quality edges' and the lack of elite solutions, alongside the ruggedness, strongly suggests the presence of multiple local optima.", 'deceptiveness': 'Potentially Deceptive. The rugged landscape and the absence of convergence, even in the initial iteration, hint at the possibility of deceptive regions where initial explorations might lead towards poorer solutions.'}, 'population_state': {'diversity': 'High. Diversity of 0.9111111111111111 is very high, which is expected given the early iteration and lack of convergence.', 'convergence': 'None. Convergence is at 0.0, confirming the population is far from converging towards a single good solution. This is consistent with the initial iteration and the high diversity.', 'clustering': 'No clustering information available. This limits the assessment of population structure, but the high diversity mitigates the immediate need to consider clustering.'}, 'difficult_regions': {'challenges': "Edge Crossings and Local Optima are the primary challenges. The 'few common high-quality edges' suggest that the population is struggling to converge on beneficial edge combinations. The 'known difficult regions (5 identified)' also contribute to the complexity.", 'specific_concerns': ['Avoiding known difficult regions.', 'Identifying edges that consistently lead to poor performance.', 'Avoiding early convergence in suboptimal edge combinations.']}, 'opportunity_regions': {'potential': "Exploration of various edge combinations and potentially building upon the '2 high quality edges'.", 'specific_suggestions': ["Carefully explore and evaluate the '2 high quality edges' and their surrounding neighborhoods.", 'Experiment with adding/removing edges from the current population to diversify and search across different regions.', 'Identify promising regions based on edge similarities among the population.']}, 'evolution_phase': 'Exploration.  The high diversity, lack of convergence, and the emphasis on edge exploration clearly indicate that the evolution is firmly in the exploration phase. This is expected at iteration 0/5.', 'evolution_direction': {'strategy': 'Aggressive Exploration with Local Refinement. Given the high diversity and the need to overcome potentially deceptive regions, a strategy that emphasizes exploration while introducing elements of local refinement is recommended.', 'operator_suggestions': [{'operator': 'Edge Exchange (or 2-opt, 3-opt, etc.)', 'purpose': 'Local search to improve the quality of existing solutions, even during exploration. Start with lower-order opt-operators and increase complexity after reaching a degree of local convergence. This will help to refine solutions and provide a more nuanced picture of the search space.', 'parameters': 'Apply with varying levels of aggressiveness. Start with more frequent applications to allow the algorithm to escape poor local optima. '}, {'operator': 'Edge Insertion/Deletion', 'purpose': 'To explore new edge combinations and escape suboptimal solutions. This operator allows the population to diversify and try different edges/paths.', 'parameters': 'Randomly insert and delete edges. Consider implementing a scoring method to determine the best edge to keep during the exploration phase.'}, {'operator': 'Heuristic Crossover', 'purpose': 'If possible, and depending on the TSP instance, use crossover operators that prioritize the preservation of high-quality edges and subpaths.', 'parameters': 'Use crossover operators that specifically favor high-quality or promising edges or subpaths identified from the population.'}]}}
2025-06-22 18:34:06,094 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:34:06,094 - __main__ - INFO - 分析阶段完成
2025-06-22 18:34:06,094 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Highly Rugged. High standard deviation of costs (43503.23567919977) suggests significant variation in solution quality, indicating a rugged landscape. Few high-quality edges and a lack of convergence further support this.', 'modality': "Multimodal.  The presence of 'few common high-quality edges' and the lack of elite solutions, alongside the ruggedness, strongly suggests the presence of multiple local optima.", 'deceptiveness': 'Potentially Deceptive. The rugged landscape and the absence of convergence, even in the initial iteration, hint at the possibility of deceptive regions where initial explorations might lead towards poorer solutions.'}, 'population_state': {'diversity': 'High. Diversity of 0.9111111111111111 is very high, which is expected given the early iteration and lack of convergence.', 'convergence': 'None. Convergence is at 0.0, confirming the population is far from converging towards a single good solution. This is consistent with the initial iteration and the high diversity.', 'clustering': 'No clustering information available. This limits the assessment of population structure, but the high diversity mitigates the immediate need to consider clustering.'}, 'difficult_regions': {'challenges': "Edge Crossings and Local Optima are the primary challenges. The 'few common high-quality edges' suggest that the population is struggling to converge on beneficial edge combinations. The 'known difficult regions (5 identified)' also contribute to the complexity.", 'specific_concerns': ['Avoiding known difficult regions.', 'Identifying edges that consistently lead to poor performance.', 'Avoiding early convergence in suboptimal edge combinations.']}, 'opportunity_regions': {'potential': "Exploration of various edge combinations and potentially building upon the '2 high quality edges'.", 'specific_suggestions': ["Carefully explore and evaluate the '2 high quality edges' and their surrounding neighborhoods.", 'Experiment with adding/removing edges from the current population to diversify and search across different regions.', 'Identify promising regions based on edge similarities among the population.']}, 'evolution_phase': 'Exploration.  The high diversity, lack of convergence, and the emphasis on edge exploration clearly indicate that the evolution is firmly in the exploration phase. This is expected at iteration 0/5.', 'evolution_direction': {'strategy': 'Aggressive Exploration with Local Refinement. Given the high diversity and the need to overcome potentially deceptive regions, a strategy that emphasizes exploration while introducing elements of local refinement is recommended.', 'operator_suggestions': [{'operator': 'Edge Exchange (or 2-opt, 3-opt, etc.)', 'purpose': 'Local search to improve the quality of existing solutions, even during exploration. Start with lower-order opt-operators and increase complexity after reaching a degree of local convergence. This will help to refine solutions and provide a more nuanced picture of the search space.', 'parameters': 'Apply with varying levels of aggressiveness. Start with more frequent applications to allow the algorithm to escape poor local optima. '}, {'operator': 'Edge Insertion/Deletion', 'purpose': 'To explore new edge combinations and escape suboptimal solutions. This operator allows the population to diversify and try different edges/paths.', 'parameters': 'Randomly insert and delete edges. Consider implementing a scoring method to determine the best edge to keep during the exploration phase.'}, {'operator': 'Heuristic Crossover', 'purpose': 'If possible, and depending on the TSP instance, use crossover operators that prioritize the preservation of high-quality edges and subpaths.', 'parameters': 'Use crossover operators that specifically favor high-quality or promising edges or subpaths identified from the population.'}]}}
2025-06-22 18:34:06,094 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:34:06,094 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:34:06,094 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 0
- Population: 10 individuals, diversity=High. Diversity of 0.9111111111111111 is very high, which is expected given the early iteration and lack of convergence.
- Best cost: 9989.0, Mean cost: 75836.2
- Search space: Ruggedness: Highly Rugged. High standard deviation of costs (43503.23567919977) suggests significant variation in solution quality, indicating a rugged landscape. Few high-quality edges and a lack of convergence further support this., Modality: Multimodal.  The presence of 'few common high-quality edges' and the lack of elite solutions, alongside the ruggedness, strongly suggests the presence of multiple local optima.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: No previous feedback

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:34:06,094 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:34:09,840 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.7,
      "step_size": 0.1,
      "local_search_iterations": 0,
      "selection_pressure": "low"
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size": 0.01,
      "local_search_iterations": 5,
      "selection_pressure": "moderate"
    }
  }
}
```

2025-06-22 18:34:09,840 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-06-22 18:34:09,840 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-06-22 18:34:09,840 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.7,
      "step_size": 0.1,
      "local_search_iterations": 0,
      "selection_pressure": "low"
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size": 0.01,
      "local_search_iterations": 5,
      "selection_pressure": "moderate"
    }
  }
}
```

2025-06-22 18:34:09,840 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:34:09,840 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-06-22 18:34:09,840 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.7,
      "step_size": 0.1,
      "local_search_iterations": 0,
      "selection_pressure": "low"
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size": 0.01,
      "local_search_iterations": 5,
      "selection_pressure": "moderate"
    }
  }
}
```

2025-06-22 18:34:09,840 - __main__ - INFO - 开始进化阶段
2025-06-22 18:34:09,840 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:34:09,840 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:34:09,840 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 1, 7] and ending with [50, 41, 42]
- Cost: 10267.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7338461538461538
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:34:09,840 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:34:12,964 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 2, 9, 16, 23, 30, 37, 44, 51, 58, 61, 54, 47, 40, 33, 26, 19, 12, 5, 1, 8, 15, 22, 29, 36, 43, 50, 57, 60, 63, 56, 49, 42, 35, 28, 21, 14, 7, 4, 11, 18, 25, 32, 39, 46, 53, 64, 62, 55, 48, 41, 34, 27, 20, 13, 6, 3, 10, 17, 24, 31, 38, 45, 52, 59, 65, 42],
  "modification_strategy": "This strategy prioritizes a more diverse path by alternating between exploring regions and then attempting to jump to other regions. The path is broken up into multiple regions. I tried to create a different order of nodes, making sure that all nodes are still covered. It also focuses on exploring the 'opportunity' and 'difficult' regions identified in the search context, mixing exploration in those regions.",
  "targeted_regions": ["Opportunity Regions", "Difficult Regions"]
}
```
2025-06-22 18:34:12,964 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:34:12,964 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54606.0, 路径: [0, 2, 9, 16, 23, 30, 37, 44, 51, 58, 61, 54, 47, 40, 33, 26, 19, 12, 5, 1, 8, 15, 22, 29, 36, 43, 50, 57, 60, 63, 56, 49, 42, 35, 28, 21, 14, 7, 4, 11, 18, 25, 32, 39, 46, 53, 64, 62, 55, 48, 41, 34, 27, 20, 13, 6, 3, 10, 17, 24, 31, 38, 45, 52, 59, 65]
2025-06-22 18:34:12,964 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 9, 16, 23, 30, 37, 44, 51, 58, 61, 54, 47, 40, 33, 26, 19, 12, 5, 1, 8, 15, 22, 29, 36, 43, 50, 57, 60, 63, 56, 49, 42, 35, 28, 21, 14, 7, 4, 11, 18, 25, 32, 39, 46, 53, 64, 62, 55, 48, 41, 34, 27, 20, 13, 6, 3, 10, 17, 24, 31, 38, 45, 52, 59, 65], 'cur_cost': 54606.0}
2025-06-22 18:34:12,964 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 18:34:12,964 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:34:12,964 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 1, 7] and ending with [50, 41, 42]
- Cost: 10267.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8076923076923077
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:34:12,964 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:34:16,967 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 2, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 50, 41, 42
  ],
  "modification_strategy": "Modified a block-wise approach, swapping the middle section of nodes. Initial path starts with an exploration of even numbers before transitioning to odd and targeting the areas near the beginning and ending of the path to explore unexplored regions by alternating between even and odd numbers. This allows for a more diverse exploration while trying to maintain a similar cost. This method attempts to keep a structure while making significant changes. The last three numbers are taken from the last of the original path, and the nodes [50, 41, 42] are simply appended to maintain the final end point of the path.",
  "targeted_regions": "Explored areas that were not prioritized in the original path. Specifically, the areas with odd indices from 1 to 65 and trying to make a good mix between beginning and end portions of the original path. Also targeted regions identified as 'unexplored regions' by switching them into the middle and beginning of the path to improve exploration. By ensuring that the [50, 41, 42] values are there, the algorithm ensures an end point similar to that of the old path."
}
```
2025-06-22 18:34:16,967 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:34:16,967 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19772.0, 路径: [0, 2, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 4]
2025-06-22 18:34:16,967 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 2, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 4], 'cur_cost': 19772.0}
2025-06-22 18:34:16,967 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 18:34:16,967 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:34:16,971 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [12, 22, 23] and ending with [43, 48, 42]
- Cost: 9989.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8569230769230769
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:34:16,971 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:34:20,569 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    12, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21
  ],
  "modification_strategy": "The strategy prioritizes exploration of unexplored regions by starting with the known starting segment ([12, 22, 23]) and extending it to include a larger section of nodes with increasing indices. Subsequently, it proceeds to incorporate the remaining nodes in a more 'scattered' fashion, including indices with a more irregular pattern, and ending with the known end node. This approach aims for a diverse path by exploring the entire space while still connecting nodes in a reasonably efficient manner, reducing the chance of extreme detours early in the path.",
  "targeted_regions": "The strategy implicitly targets all regions by ensuring a complete traversal of all nodes. While the initial sequence of nodes explores the known and adjacent regions, the latter portions ensures that all regions are integrated effectively. "
}
```
2025-06-22 18:34:20,569 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:34:20,569 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11493.0, 路径: [12, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21]
2025-06-22 18:34:20,569 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21], 'cur_cost': 11493.0}
2025-06-22 18:34:20,569 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 18:34:20,569 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:34:20,572 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [39, 5, 11] and ending with [57, 60, 36]
- Cost: 105033.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8692307692307693
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:34:20,572 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:34:23,543 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    39, 5, 11, 2, 18, 25, 32, 40, 47, 54, 61, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 3, 10, 17, 24, 31, 38, 45, 52, 59, 4, 19, 26, 33, 41, 48, 55, 62, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 6, 13, 20, 27, 34, 44, 51, 58, 65, 9, 16, 23, 30, 37, 46, 53, 60
  ],
  "modification_strategy": "This path prioritizes exploring the unexplored regions and avoids difficult regions. It uses a more chaotic and distributed approach to shuffle the nodes. It tries to scatter the nodes compared to the original path, aiming for increased diversity by breaking the existing path into chunks and merging them differently. The distribution seeks to explore the opportunity regions that were previously unexplored while considering the risk tolerance.",
  "targeted_regions": "Unexplored regions, avoiding difficult regions."
}
```
2025-06-22 18:34:23,543 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:34:23,543 - ExplorationExpert - INFO - 探索路径生成完成，成本: 65186.0, 路径: [39, 5, 11, 2, 18, 25, 32, 40, 47, 54, 61, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 3, 10, 17, 24, 31, 38, 45, 52, 59, 4, 19, 26, 33, 41, 48, 55, 62, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 6, 13, 20, 27, 34, 44, 51, 58, 65, 9, 16, 23, 30, 37, 46, 53, 60, 12]
2025-06-22 18:34:23,543 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [39, 5, 11, 2, 18, 25, 32, 40, 47, 54, 61, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 3, 10, 17, 24, 31, 38, 45, 52, 59, 4, 19, 26, 33, 41, 48, 55, 62, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 6, 13, 20, 27, 34, 44, 51, 58, 65, 9, 16, 23, 30, 37, 46, 53, 60, 12], 'cur_cost': 65186.0}
2025-06-22 18:34:23,543 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 18:34:23,543 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:34:23,548 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [2, 64, 48] and ending with [24, 54, 29]
- Cost: 107216.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8261538461538461
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:34:23,548 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:34:27,039 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    2, 64, 48, 15, 32, 55, 10, 27, 41, 6, 39, 58, 1, 34, 49, 5, 22, 30, 44, 18, 60, 9, 43, 13, 62, 20, 37, 52, 4, 25, 50, 11, 16, 3, 28, 65, 31, 56, 19, 36, 59, 7, 23, 40, 63, 17, 33, 54, 29, 0, 42, 53, 14, 24, 35, 8, 45, 51, 12, 61, 26, 46, 38, 57, 21, 47
  ],
  "modification_strategy": "Re-arranged the path based on a completely new sequence.  Focus on different parts of the initial path, and shuffle them to create a path that utilizes different starting locations. Avoidance of consecutive node placements as seen in original, while utilizing the knowledge of regions to guide path. Emphasis on diverse routes throughout the path.",
  "targeted_regions": "Primarily aiming to hit different regions and opportunity regions in a more spread out fashion to improve diversity while maintaining quality.  Specifically, prioritizing exploration in areas that appeared less frequented in the original path."
}
```
2025-06-22 18:34:27,039 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:34:27,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 125723.0, 路径: [2, 64, 48, 15, 32, 55, 10, 27, 41, 6, 39, 58, 1, 34, 49, 5, 22, 30, 44, 18, 60, 9, 43, 13, 62, 20, 37, 52, 4, 25, 50, 11, 16, 3, 28, 65, 31, 56, 19, 36, 59, 7, 23, 40, 63, 17, 33, 54, 29, 0, 42, 53, 14, 24, 35, 8, 45, 51, 12, 61, 26, 46, 38, 57, 21, 47]
2025-06-22 18:34:27,039 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 64, 48, 15, 32, 55, 10, 27, 41, 6, 39, 58, 1, 34, 49, 5, 22, 30, 44, 18, 60, 9, 43, 13, 62, 20, 37, 52, 4, 25, 50, 11, 16, 3, 28, 65, 31, 56, 19, 36, 59, 7, 23, 40, 63, 17, 33, 54, 29, 0, 42, 53, 14, 24, 35, 8, 45, 51, 12, 61, 26, 46, 38, 57, 21, 47], 'cur_cost': 125723.0}
2025-06-22 18:34:27,039 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 18:34:27,039 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:34:27,039 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:34:27,039 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 114802.0
2025-06-22 18:34:29,853 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 18:34:29,859 - ExploitationExpert - INFO - res_population_costs: [101310]
2025-06-22 18:34:29,859 - ExploitationExpert - INFO - res_populations: [array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64)]
2025-06-22 18:34:29,859 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:34:29,859 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 9, 16, 23, 30, 37, 44, 51, 58, 61, 54, 47, 40, 33, 26, 19, 12, 5, 1, 8, 15, 22, 29, 36, 43, 50, 57, 60, 63, 56, 49, 42, 35, 28, 21, 14, 7, 4, 11, 18, 25, 32, 39, 46, 53, 64, 62, 55, 48, 41, 34, 27, 20, 13, 6, 3, 10, 17, 24, 31, 38, 45, 52, 59, 65], 'cur_cost': 54606.0}, {'tour': [0, 2, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 4], 'cur_cost': 19772.0}, {'tour': [12, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21], 'cur_cost': 11493.0}, {'tour': [39, 5, 11, 2, 18, 25, 32, 40, 47, 54, 61, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 3, 10, 17, 24, 31, 38, 45, 52, 59, 4, 19, 26, 33, 41, 48, 55, 62, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 6, 13, 20, 27, 34, 44, 51, 58, 65, 9, 16, 23, 30, 37, 46, 53, 60, 12], 'cur_cost': 65186.0}, {'tour': [2, 64, 48, 15, 32, 55, 10, 27, 41, 6, 39, 58, 1, 34, 49, 5, 22, 30, 44, 18, 60, 9, 43, 13, 62, 20, 37, 52, 4, 25, 50, 11, 16, 3, 28, 65, 31, 56, 19, 36, 59, 7, 23, 40, 63, 17, 33, 54, 29, 0, 42, 53, 14, 24, 35, 8, 45, 51, 12, 61, 26, 46, 38, 57, 21, 47], 'cur_cost': 125723.0}, {'tour': array([49, 30, 65, 23, 32, 54,  3, 17,  8, 37, 33, 35, 42, 18, 55, 63, 26,
        2, 51, 59, 52, 24, 64, 14, 57, 16, 36,  7,  1, 28,  0, 60, 39, 29,
       43, 40,  6, 22, 31, 56, 19,  9, 50, 41, 58, 15, 10, 44, 13, 47, 53,
       20, 34, 45, 27, 61, 48, 38, 25, 21, 11, 46,  4, 62,  5, 12]), 'cur_cost': 114802.0}, {'tour': [38, 40, 45, 43, 37, 42, 62, 9, 19, 64, 39, 57, 52, 11, 58, 55, 35, 28, 33, 23, 49, 50, 10, 48, 59, 4, 54, 31, 25, 0, 6, 20, 2, 3, 65, 16, 44, 32, 24, 27, 34, 17, 36, 46, 61, 12, 51, 60, 30, 53, 8, 63, 56, 5, 1, 15, 47, 26, 18, 14, 29, 41, 22, 21, 13, 7], 'cur_cost': 86967.0}, {'tour': [18, 49, 20, 21, 22, 59, 37, 34, 15, 31, 40, 29, 62, 45, 57, 46, 3, 58, 52, 43, 33, 61, 39, 64, 25, 1, 14, 24, 51, 35, 56, 8, 60, 0, 50, 41, 63, 54, 36, 10, 11, 12, 32, 47, 26, 17, 13, 28, 53, 44, 42, 55, 5, 65, 7, 23, 2, 16, 38, 27, 48, 19, 9, 6, 30, 4], 'cur_cost': 109640.0}, {'tour': [61, 57, 37, 0, 31, 12, 14, 11, 50, 48, 9, 60, 25, 28, 10, 63, 36, 65, 35, 6, 2, 54, 7, 47, 41, 40, 53, 56, 38, 55, 39, 4, 32, 45, 8, 17, 24, 46, 42, 49, 27, 44, 3, 19, 18, 1, 13, 62, 21, 52, 22, 5, 34, 30, 16, 15, 64, 20, 33, 59, 29, 26, 51, 23, 58, 43], 'cur_cost': 107474.0}, {'tour': [57, 65, 31, 52, 0, 18, 26, 36, 4, 44, 38, 10, 46, 51, 16, 48, 14, 27, 13, 35, 63, 17, 21, 3, 6, 1, 40, 54, 56, 62, 45, 9, 39, 50, 15, 33, 29, 53, 61, 24, 23, 7, 42, 59, 2, 19, 20, 41, 58, 43, 8, 11, 25, 22, 47, 49, 32, 30, 55, 34, 12, 28, 64, 5, 37, 60], 'cur_cost': 98829.0}]
2025-06-22 18:34:29,861 - ExploitationExpert - INFO - 局部搜索耗时: 2.82秒
2025-06-22 18:34:29,861 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-22 18:34:29,861 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 18:34:29,861 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 18:34:29,861 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:34:29,861 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:34:29,863 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 105090.0
2025-06-22 18:34:30,906 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 18:34:30,906 - ExploitationExpert - INFO - res_population_costs: [101310, 9570]
2025-06-22 18:34:30,906 - ExploitationExpert - INFO - res_populations: [array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 18:34:30,906 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:34:30,906 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 9, 16, 23, 30, 37, 44, 51, 58, 61, 54, 47, 40, 33, 26, 19, 12, 5, 1, 8, 15, 22, 29, 36, 43, 50, 57, 60, 63, 56, 49, 42, 35, 28, 21, 14, 7, 4, 11, 18, 25, 32, 39, 46, 53, 64, 62, 55, 48, 41, 34, 27, 20, 13, 6, 3, 10, 17, 24, 31, 38, 45, 52, 59, 65], 'cur_cost': 54606.0}, {'tour': [0, 2, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 4], 'cur_cost': 19772.0}, {'tour': [12, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21], 'cur_cost': 11493.0}, {'tour': [39, 5, 11, 2, 18, 25, 32, 40, 47, 54, 61, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 3, 10, 17, 24, 31, 38, 45, 52, 59, 4, 19, 26, 33, 41, 48, 55, 62, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 6, 13, 20, 27, 34, 44, 51, 58, 65, 9, 16, 23, 30, 37, 46, 53, 60, 12], 'cur_cost': 65186.0}, {'tour': [2, 64, 48, 15, 32, 55, 10, 27, 41, 6, 39, 58, 1, 34, 49, 5, 22, 30, 44, 18, 60, 9, 43, 13, 62, 20, 37, 52, 4, 25, 50, 11, 16, 3, 28, 65, 31, 56, 19, 36, 59, 7, 23, 40, 63, 17, 33, 54, 29, 0, 42, 53, 14, 24, 35, 8, 45, 51, 12, 61, 26, 46, 38, 57, 21, 47], 'cur_cost': 125723.0}, {'tour': array([49, 30, 65, 23, 32, 54,  3, 17,  8, 37, 33, 35, 42, 18, 55, 63, 26,
        2, 51, 59, 52, 24, 64, 14, 57, 16, 36,  7,  1, 28,  0, 60, 39, 29,
       43, 40,  6, 22, 31, 56, 19,  9, 50, 41, 58, 15, 10, 44, 13, 47, 53,
       20, 34, 45, 27, 61, 48, 38, 25, 21, 11, 46,  4, 62,  5, 12]), 'cur_cost': 114802.0}, {'tour': array([17, 52, 19, 32, 27, 14, 59, 24,  2,  1, 35, 11, 48,  7, 40, 22, 42,
       38,  3, 21,  6,  9, 37, 30, 61, 65, 58, 23, 15, 10, 12, 29, 20, 51,
       63, 39, 36, 64, 16, 49, 25, 43, 50,  8, 47, 34, 26, 60, 62, 55,  5,
       13, 44, 54,  0, 45, 18,  4, 46, 33, 57, 28, 31, 41, 53, 56]), 'cur_cost': 105090.0}, {'tour': [18, 49, 20, 21, 22, 59, 37, 34, 15, 31, 40, 29, 62, 45, 57, 46, 3, 58, 52, 43, 33, 61, 39, 64, 25, 1, 14, 24, 51, 35, 56, 8, 60, 0, 50, 41, 63, 54, 36, 10, 11, 12, 32, 47, 26, 17, 13, 28, 53, 44, 42, 55, 5, 65, 7, 23, 2, 16, 38, 27, 48, 19, 9, 6, 30, 4], 'cur_cost': 109640.0}, {'tour': [61, 57, 37, 0, 31, 12, 14, 11, 50, 48, 9, 60, 25, 28, 10, 63, 36, 65, 35, 6, 2, 54, 7, 47, 41, 40, 53, 56, 38, 55, 39, 4, 32, 45, 8, 17, 24, 46, 42, 49, 27, 44, 3, 19, 18, 1, 13, 62, 21, 52, 22, 5, 34, 30, 16, 15, 64, 20, 33, 59, 29, 26, 51, 23, 58, 43], 'cur_cost': 107474.0}, {'tour': [57, 65, 31, 52, 0, 18, 26, 36, 4, 44, 38, 10, 46, 51, 16, 48, 14, 27, 13, 35, 63, 17, 21, 3, 6, 1, 40, 54, 56, 62, 45, 9, 39, 50, 15, 33, 29, 53, 61, 24, 23, 7, 42, 59, 2, 19, 20, 41, 58, 43, 8, 11, 25, 22, 47, 49, 32, 30, 55, 34, 12, 28, 64, 5, 37, 60], 'cur_cost': 98829.0}]
2025-06-22 18:34:30,906 - ExploitationExpert - INFO - 局部搜索耗时: 1.04秒
2025-06-22 18:34:30,906 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-22 18:34:30,906 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 18:34:30,911 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-22 18:34:30,911 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:34:30,911 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [18, 49, 20] and ending with [6, 30, 4]
- Cost: 109640.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.84
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:34:30,912 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:34:34,498 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    18, 49, 55, 1, 4, 59, 3, 21, 40, 25, 17, 61, 54, 34, 24, 60, 5, 22, 39, 42, 33, 11, 63, 15, 46, 50, 2, 13, 27, 32, 37, 36, 43, 14, 65, 28, 9, 35, 20, 47, 31, 48, 16, 29, 19, 52, 51, 53, 23, 0, 62, 45, 30, 57, 44, 10, 58, 12, 41, 26, 38, 8, 64, 7, 6
  ],
  "modification_strategy": "This strategy prioritizes exploration of unexplored regions and difficult regions identified in the search context. The path was significantly altered by focusing on different clusters of nodes, and incorporating nodes in the previously unvisited regions early in the path. Specifically, a substantial portion of the path was rearranged to include nodes from regions suggested in the search context while trying to keep the diversity and risk parameters in mind.",
  "targeted_regions": "Unexplored regions identified in the search context; potentially regions with nodes clustering together near nodes 55, 39, and nodes on the other end of the path around 6 and 7. Difficult regions (implicitly), as guided by the search context, are also considered by modifying the regions of the path"
}
```
2025-06-22 18:34:34,498 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:34:34,498 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103192.0, 路径: [18, 49, 55, 1, 4, 59, 3, 21, 40, 25, 17, 61, 54, 34, 24, 60, 5, 22, 39, 42, 33, 11, 63, 15, 46, 50, 2, 13, 27, 32, 37, 36, 43, 14, 65, 28, 9, 35, 20, 47, 31, 48, 16, 29, 19, 52, 51, 53, 23, 0, 62, 45, 30, 57, 44, 10, 58, 12, 41, 26, 38, 8, 64, 7, 6, 56]
2025-06-22 18:34:34,498 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [18, 49, 55, 1, 4, 59, 3, 21, 40, 25, 17, 61, 54, 34, 24, 60, 5, 22, 39, 42, 33, 11, 63, 15, 46, 50, 2, 13, 27, 32, 37, 36, 43, 14, 65, 28, 9, 35, 20, 47, 31, 48, 16, 29, 19, 52, 51, 53, 23, 0, 62, 45, 30, 57, 44, 10, 58, 12, 41, 26, 38, 8, 64, 7, 6, 56], 'cur_cost': 103192.0}
2025-06-22 18:34:34,498 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 18:34:34,498 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:34:34,498 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:34:34,498 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 98278.0
2025-06-22 18:34:34,998 - ExploitationExpert - INFO - res_population_num: 9
2025-06-22 18:34:34,999 - ExploitationExpert - INFO - res_population_costs: [101310, 9570, 9547, 9543, 9527, 9527, 9526, 9521, 9521]
2025-06-22 18:34:34,999 - ExploitationExpert - INFO - res_populations: [array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:34:35,003 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:34:35,003 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 9, 16, 23, 30, 37, 44, 51, 58, 61, 54, 47, 40, 33, 26, 19, 12, 5, 1, 8, 15, 22, 29, 36, 43, 50, 57, 60, 63, 56, 49, 42, 35, 28, 21, 14, 7, 4, 11, 18, 25, 32, 39, 46, 53, 64, 62, 55, 48, 41, 34, 27, 20, 13, 6, 3, 10, 17, 24, 31, 38, 45, 52, 59, 65], 'cur_cost': 54606.0}, {'tour': [0, 2, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 4], 'cur_cost': 19772.0}, {'tour': [12, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21], 'cur_cost': 11493.0}, {'tour': [39, 5, 11, 2, 18, 25, 32, 40, 47, 54, 61, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 3, 10, 17, 24, 31, 38, 45, 52, 59, 4, 19, 26, 33, 41, 48, 55, 62, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 6, 13, 20, 27, 34, 44, 51, 58, 65, 9, 16, 23, 30, 37, 46, 53, 60, 12], 'cur_cost': 65186.0}, {'tour': [2, 64, 48, 15, 32, 55, 10, 27, 41, 6, 39, 58, 1, 34, 49, 5, 22, 30, 44, 18, 60, 9, 43, 13, 62, 20, 37, 52, 4, 25, 50, 11, 16, 3, 28, 65, 31, 56, 19, 36, 59, 7, 23, 40, 63, 17, 33, 54, 29, 0, 42, 53, 14, 24, 35, 8, 45, 51, 12, 61, 26, 46, 38, 57, 21, 47], 'cur_cost': 125723.0}, {'tour': array([49, 30, 65, 23, 32, 54,  3, 17,  8, 37, 33, 35, 42, 18, 55, 63, 26,
        2, 51, 59, 52, 24, 64, 14, 57, 16, 36,  7,  1, 28,  0, 60, 39, 29,
       43, 40,  6, 22, 31, 56, 19,  9, 50, 41, 58, 15, 10, 44, 13, 47, 53,
       20, 34, 45, 27, 61, 48, 38, 25, 21, 11, 46,  4, 62,  5, 12]), 'cur_cost': 114802.0}, {'tour': array([17, 52, 19, 32, 27, 14, 59, 24,  2,  1, 35, 11, 48,  7, 40, 22, 42,
       38,  3, 21,  6,  9, 37, 30, 61, 65, 58, 23, 15, 10, 12, 29, 20, 51,
       63, 39, 36, 64, 16, 49, 25, 43, 50,  8, 47, 34, 26, 60, 62, 55,  5,
       13, 44, 54,  0, 45, 18,  4, 46, 33, 57, 28, 31, 41, 53, 56]), 'cur_cost': 105090.0}, {'tour': [18, 49, 55, 1, 4, 59, 3, 21, 40, 25, 17, 61, 54, 34, 24, 60, 5, 22, 39, 42, 33, 11, 63, 15, 46, 50, 2, 13, 27, 32, 37, 36, 43, 14, 65, 28, 9, 35, 20, 47, 31, 48, 16, 29, 19, 52, 51, 53, 23, 0, 62, 45, 30, 57, 44, 10, 58, 12, 41, 26, 38, 8, 64, 7, 6, 56], 'cur_cost': 103192.0}, {'tour': array([ 8, 16, 46, 57, 55, 23, 33, 42, 43, 24, 11, 18, 37, 25, 29, 59, 21,
       62, 13, 47, 12, 30, 27, 32, 22, 64, 44, 56, 51,  5,  3,  4, 50, 60,
       36, 26, 52,  6,  1, 65,  0, 28, 54, 40, 58, 49, 31, 19,  2, 48, 41,
       35, 34, 10,  7,  9, 15, 45, 14, 39, 17, 63, 53, 61, 38, 20]), 'cur_cost': 98278.0}, {'tour': [57, 65, 31, 52, 0, 18, 26, 36, 4, 44, 38, 10, 46, 51, 16, 48, 14, 27, 13, 35, 63, 17, 21, 3, 6, 1, 40, 54, 56, 62, 45, 9, 39, 50, 15, 33, 29, 53, 61, 24, 23, 7, 42, 59, 2, 19, 20, 41, 58, 43, 8, 11, 25, 22, 47, 49, 32, 30, 55, 34, 12, 28, 64, 5, 37, 60], 'cur_cost': 98829.0}]
2025-06-22 18:34:35,004 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:34:35,004 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-22 18:34:35,004 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 18:34:35,005 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 18:34:35,005 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:34:35,005 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:34:35,005 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 116586.0
2025-06-22 18:34:35,508 - ExploitationExpert - INFO - res_population_num: 13
2025-06-22 18:34:35,508 - ExploitationExpert - INFO - res_population_costs: [101310, 9570, 9547, 9543, 9527, 9527, 9526, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:34:35,508 - ExploitationExpert - INFO - res_populations: [array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:34:35,512 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:34:35,512 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 9, 16, 23, 30, 37, 44, 51, 58, 61, 54, 47, 40, 33, 26, 19, 12, 5, 1, 8, 15, 22, 29, 36, 43, 50, 57, 60, 63, 56, 49, 42, 35, 28, 21, 14, 7, 4, 11, 18, 25, 32, 39, 46, 53, 64, 62, 55, 48, 41, 34, 27, 20, 13, 6, 3, 10, 17, 24, 31, 38, 45, 52, 59, 65], 'cur_cost': 54606.0}, {'tour': [0, 2, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 4], 'cur_cost': 19772.0}, {'tour': [12, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21], 'cur_cost': 11493.0}, {'tour': [39, 5, 11, 2, 18, 25, 32, 40, 47, 54, 61, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 3, 10, 17, 24, 31, 38, 45, 52, 59, 4, 19, 26, 33, 41, 48, 55, 62, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 6, 13, 20, 27, 34, 44, 51, 58, 65, 9, 16, 23, 30, 37, 46, 53, 60, 12], 'cur_cost': 65186.0}, {'tour': [2, 64, 48, 15, 32, 55, 10, 27, 41, 6, 39, 58, 1, 34, 49, 5, 22, 30, 44, 18, 60, 9, 43, 13, 62, 20, 37, 52, 4, 25, 50, 11, 16, 3, 28, 65, 31, 56, 19, 36, 59, 7, 23, 40, 63, 17, 33, 54, 29, 0, 42, 53, 14, 24, 35, 8, 45, 51, 12, 61, 26, 46, 38, 57, 21, 47], 'cur_cost': 125723.0}, {'tour': array([49, 30, 65, 23, 32, 54,  3, 17,  8, 37, 33, 35, 42, 18, 55, 63, 26,
        2, 51, 59, 52, 24, 64, 14, 57, 16, 36,  7,  1, 28,  0, 60, 39, 29,
       43, 40,  6, 22, 31, 56, 19,  9, 50, 41, 58, 15, 10, 44, 13, 47, 53,
       20, 34, 45, 27, 61, 48, 38, 25, 21, 11, 46,  4, 62,  5, 12]), 'cur_cost': 114802.0}, {'tour': array([17, 52, 19, 32, 27, 14, 59, 24,  2,  1, 35, 11, 48,  7, 40, 22, 42,
       38,  3, 21,  6,  9, 37, 30, 61, 65, 58, 23, 15, 10, 12, 29, 20, 51,
       63, 39, 36, 64, 16, 49, 25, 43, 50,  8, 47, 34, 26, 60, 62, 55,  5,
       13, 44, 54,  0, 45, 18,  4, 46, 33, 57, 28, 31, 41, 53, 56]), 'cur_cost': 105090.0}, {'tour': [18, 49, 55, 1, 4, 59, 3, 21, 40, 25, 17, 61, 54, 34, 24, 60, 5, 22, 39, 42, 33, 11, 63, 15, 46, 50, 2, 13, 27, 32, 37, 36, 43, 14, 65, 28, 9, 35, 20, 47, 31, 48, 16, 29, 19, 52, 51, 53, 23, 0, 62, 45, 30, 57, 44, 10, 58, 12, 41, 26, 38, 8, 64, 7, 6, 56], 'cur_cost': 103192.0}, {'tour': array([ 8, 16, 46, 57, 55, 23, 33, 42, 43, 24, 11, 18, 37, 25, 29, 59, 21,
       62, 13, 47, 12, 30, 27, 32, 22, 64, 44, 56, 51,  5,  3,  4, 50, 60,
       36, 26, 52,  6,  1, 65,  0, 28, 54, 40, 58, 49, 31, 19,  2, 48, 41,
       35, 34, 10,  7,  9, 15, 45, 14, 39, 17, 63, 53, 61, 38, 20]), 'cur_cost': 98278.0}, {'tour': array([30, 33,  7, 52,  4, 28,  0, 43, 49, 16, 64, 15, 12, 46, 25, 63,  2,
       60,  1, 39, 54, 18,  6, 51,  3, 26, 65, 38, 32, 22, 34,  8, 55, 14,
       40, 19, 62, 11, 23, 59, 37, 48, 45, 31, 17, 57, 41, 10, 21, 44, 24,
       35, 61, 20, 53,  5, 50, 36, 42, 47, 58, 29, 13,  9, 27, 56]), 'cur_cost': 116586.0}]
2025-06-22 18:34:35,515 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:34:35,515 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-22 18:34:35,515 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 18:34:35,516 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 9, 16, 23, 30, 37, 44, 51, 58, 61, 54, 47, 40, 33, 26, 19, 12, 5, 1, 8, 15, 22, 29, 36, 43, 50, 57, 60, 63, 56, 49, 42, 35, 28, 21, 14, 7, 4, 11, 18, 25, 32, 39, 46, 53, 64, 62, 55, 48, 41, 34, 27, 20, 13, 6, 3, 10, 17, 24, 31, 38, 45, 52, 59, 65], 'cur_cost': 54606.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 4], 'cur_cost': 19772.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [12, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21], 'cur_cost': 11493.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [39, 5, 11, 2, 18, 25, 32, 40, 47, 54, 61, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 3, 10, 17, 24, 31, 38, 45, 52, 59, 4, 19, 26, 33, 41, 48, 55, 62, 1, 8, 15, 22, 29, 36, 43, 50, 57, 64, 6, 13, 20, 27, 34, 44, 51, 58, 65, 9, 16, 23, 30, 37, 46, 53, 60, 12], 'cur_cost': 65186.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 64, 48, 15, 32, 55, 10, 27, 41, 6, 39, 58, 1, 34, 49, 5, 22, 30, 44, 18, 60, 9, 43, 13, 62, 20, 37, 52, 4, 25, 50, 11, 16, 3, 28, 65, 31, 56, 19, 36, 59, 7, 23, 40, 63, 17, 33, 54, 29, 0, 42, 53, 14, 24, 35, 8, 45, 51, 12, 61, 26, 46, 38, 57, 21, 47], 'cur_cost': 125723.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [18, 49, 55, 1, 4, 59, 3, 21, 40, 25, 17, 61, 54, 34, 24, 60, 5, 22, 39, 42, 33, 11, 63, 15, 46, 50, 2, 13, 27, 32, 37, 36, 43, 14, 65, 28, 9, 35, 20, 47, 31, 48, 16, 29, 19, 52, 51, 53, 23, 0, 62, 45, 30, 57, 44, 10, 58, 12, 41, 26, 38, 8, 64, 7, 6, 56], 'cur_cost': 103192.0}}]
2025-06-22 18:34:35,516 - __main__ - INFO - 进化阶段完成
2025-06-22 18:34:35,516 - __main__ - INFO - 开始评估阶段
2025-06-22 18:34:35,516 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:34:35,517 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:34:35,518 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 0/5
- Population changes:
  * Cost: min 9989.0->11493.0, mean 75836.2->81472.8
  * Diversity: 0.7338461538461538->0.8476923076923077
  * Elite solutions: 3->1

## Strategy Performance:
- Explore: 6 individuals, success rate 33.3%, avg improvement -4593.33
- Exploit: 4 individuals, success rate 25.0%, avg improvement -7201.5
- Overall improvement: -1504.0

## Historical Trends:
No historical data available

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:34:41,606 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Exploration/Stagnation",
    "features": "The population is experiencing an increase in the cost (both min and mean), while diversity has improved. Elite solutions decreased from 3 to 1, indicating that the best solutions are being driven down and are likely to be converging on a local optima or exploring a low-performing region of the search space."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "33.3%",
      "avg_improvement": "-4593.33",
      "evaluation": "Explore has a low success rate and is leading to a net cost increase. Despite the negative average improvement, this is not unexpected in the exploration phase where we try more individuals."
    },
    "exploit": {
      "success_rate": "25.0%",
      "avg_improvement": "-7201.5",
      "evaluation": "Exploit has an even lower success rate and is also showing a net cost increase, signifying that current exploitation attempts are counterproductive. Solutions are not improving via exploitation."
    },
    "overall": {
      "improvement": "-1504.0",
      "evaluation": "The overall improvement is negative, indicating a net loss of performance. Both exploration and exploitation strategies are failing to produce better solutions."
    }
  },
  "balance_state": {
    "assessment": "The balance is skewed towards ineffective strategies. The population is likely trapped or converging towards non-optimal regions of the search space. Both Exploration and Exploitation are failing, implying there are flaws in the strategies used or the individuals sampled.",
    "adjustment_needs": "The primary need is to reformulate the search by reviewing the exploration and exploitation parameters. A more focused exploration phase, followed by a period of exploitation with a new set of parameters is recommended."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Review and adjust the exploration strategy.",
      "details": "Increase the exploration rate by exploring the problem from different perspectives, such as: sampling individuals from a broader range of the search space, or modifying the initial parameters in a way that promotes solution diversity. Consider different mutation operators or parameter tuning for exploration to find potentially better individuals."
    },
    {
      "priority": "High",
      "action": "Re-evaluate exploitation strategy parameters.",
      "details": "Adjust parameters for exploitation or investigate a different exploitation method. Perhaps use more targeted mutation/crossover that specifically targets the regions around successful solutions. If possible, introduce adaptive parameters, such as adaptive mutation rates for the exploitative phase."
    },
    {
      "priority": "Medium",
      "action": "Check individual assessment parameters",
      "details": "If the solution assessment is flawed, then the evolution will not converge to better solutions. Check the assessment method to see if a good parameter selection or approach has been neglected."
    },
    {
      "priority": "Medium",
      "action": "Increase Elite Count",
      "details": "Since the elite count has decreased, you can increase the elite count or consider a strategy to prevent losing elite individuals. This will preserve promising solutions in the next iteration, while allowing for a more thorough exploration."
    }
  ]
}
```
2025-06-22 18:34:41,637 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:34:41,637 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Exploration/Stagnation",
    "features": "The population is experiencing an increase in the cost (both min and mean), while diversity has improved. Elite solutions decreased from 3 to 1, indicating that the best solutions are being driven down and are likely to be converging on a local optima or exploring a low-performing region of the search space."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "33.3%",
      "avg_improvement": "-4593.33",
      "evaluation": "Explore has a low success rate and is leading to a net cost increase. Despite the negative average improvement, this is not unexpected in the exploration phase where we try more individuals."
    },
    "exploit": {
      "success_rate": "25.0%",
      "avg_improvement": "-7201.5",
      "evaluation": "Exploit has an even lower success rate and is also showing a net cost increase, signifying that current exploitation attempts are counterproductive. Solutions are not improving via exploitation."
    },
    "overall": {
      "improvement": "-1504.0",
      "evaluation": "The overall improvement is negative, indicating a net loss of performance. Both exploration and exploitation strategies are failing to produce better solutions."
    }
  },
  "balance_state": {
    "assessment": "The balance is skewed towards ineffective strategies. The population is likely trapped or converging towards non-optimal regions of the search space. Both Exploration and Exploitation are failing, implying there are flaws in the strategies used or the individuals sampled.",
    "adjustment_needs": "The primary need is to reformulate the search by reviewing the exploration and exploitation parameters. A more focused exploration phase, followed by a period of exploitation with a new set of parameters is recommended."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Review and adjust the exploration strategy.",
      "details": "Increase the exploration rate by exploring the problem from different perspectives, such as: sampling individuals from a broader range of the search space, or modifying the initial parameters in a way that promotes solution diversity. Consider different mutation operators or parameter tuning for exploration to find potentially better individuals."
    },
    {
      "priority": "High",
      "action": "Re-evaluate exploitation strategy parameters.",
      "details": "Adjust parameters for exploitation or investigate a different exploitation method. Perhaps use more targeted mutation/crossover that specifically targets the regions around successful solutions. If possible, introduce adaptive parameters, such as adaptive mutation rates for the exploitative phase."
    },
    {
      "priority": "Medium",
      "action": "Check individual assessment parameters",
      "details": "If the solution assessment is flawed, then the evolution will not converge to better solutions. Check the assessment method to see if a good parameter selection or approach has been neglected."
    },
    {
      "priority": "Medium",
      "action": "Increase Elite Count",
      "details": "Since the elite count has decreased, you can increase the elite count or consider a strategy to prevent losing elite individuals. This will preserve promising solutions in the next iteration, while allowing for a more thorough exploration."
    }
  ]
}
```
2025-06-22 18:34:41,637 - __main__ - INFO - 评估阶段完成
2025-06-22 18:34:41,637 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Exploration/Stagnation",
    "features": "The population is experiencing an increase in the cost (both min and mean), while diversity has improved. Elite solutions decreased from 3 to 1, indicating that the best solutions are being driven down and are likely to be converging on a local optima or exploring a low-performing region of the search space."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "33.3%",
      "avg_improvement": "-4593.33",
      "evaluation": "Explore has a low success rate and is leading to a net cost increase. Despite the negative average improvement, this is not unexpected in the exploration phase where we try more individuals."
    },
    "exploit": {
      "success_rate": "25.0%",
      "avg_improvement": "-7201.5",
      "evaluation": "Exploit has an even lower success rate and is also showing a net cost increase, signifying that current exploitation attempts are counterproductive. Solutions are not improving via exploitation."
    },
    "overall": {
      "improvement": "-1504.0",
      "evaluation": "The overall improvement is negative, indicating a net loss of performance. Both exploration and exploitation strategies are failing to produce better solutions."
    }
  },
  "balance_state": {
    "assessment": "The balance is skewed towards ineffective strategies. The population is likely trapped or converging towards non-optimal regions of the search space. Both Exploration and Exploitation are failing, implying there are flaws in the strategies used or the individuals sampled.",
    "adjustment_needs": "The primary need is to reformulate the search by reviewing the exploration and exploitation parameters. A more focused exploration phase, followed by a period of exploitation with a new set of parameters is recommended."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Review and adjust the exploration strategy.",
      "details": "Increase the exploration rate by exploring the problem from different perspectives, such as: sampling individuals from a broader range of the search space, or modifying the initial parameters in a way that promotes solution diversity. Consider different mutation operators or parameter tuning for exploration to find potentially better individuals."
    },
    {
      "priority": "High",
      "action": "Re-evaluate exploitation strategy parameters.",
      "details": "Adjust parameters for exploitation or investigate a different exploitation method. Perhaps use more targeted mutation/crossover that specifically targets the regions around successful solutions. If possible, introduce adaptive parameters, such as adaptive mutation rates for the exploitative phase."
    },
    {
      "priority": "Medium",
      "action": "Check individual assessment parameters",
      "details": "If the solution assessment is flawed, then the evolution will not converge to better solutions. Check the assessment method to see if a good parameter selection or approach has been neglected."
    },
    {
      "priority": "Medium",
      "action": "Increase Elite Count",
      "details": "Since the elite count has decreased, you can increase the elite count or consider a strategy to prevent losing elite individuals. This will preserve promising solutions in the next iteration, while allowing for a more thorough exploration."
    }
  ]
}
```
2025-06-22 18:34:41,637 - __main__ - INFO - 当前最佳适应度: 11493.0
2025-06-22 18:34:41,639 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-22 18:34:41,639 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-22 18:34:41,639 - __main__ - INFO - 开始分析阶段
2025-06-22 18:34:41,639 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:34:41,657 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11493.0, 'max': 125723.0, 'mean': 81472.8, 'std': 39121.61684235456}, 'diversity': 0.9626262626262625, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:34:41,658 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11493.0, 'max': 125723.0, 'mean': 81472.8, 'std': 39121.61684235456}, 'diversity_level': 0.9626262626262625, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:34:41,658 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:34:41,658 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:34:41,658 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:34:41,661 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 18:34:41,661 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:34:41,661 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 18:34:41,661 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:34:41,662 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:34:41,662 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:34:41,663 - EliteExpert - INFO - 精英解分析完成
2025-06-22 18:34:41,663 - __main__ - INFO - 精英专家分析报告: {'elite_count': 13, 'elite_common_features': {'common_edges': {'(63, 52)': 0.7692307692307693, '(52, 65)': 0.7692307692307693, '(53, 64)': 0.7692307692307693, '(64, 57)': 0.8461538461538461, '(57, 54)': 0.8461538461538461, '(54, 60)': 0.7692307692307693, '(39, 44)': 0.7692307692307693, '(50, 42)': 0.7692307692307693, '(46, 47)': 0.7692307692307693, '(30, 28)': 0.7692307692307693, '(32, 29)': 0.7692307692307693, '(4, 6)': 0.7692307692307693, '(6, 2)': 0.7692307692307693, '(2, 8)': 0.7692307692307693, '(11, 9)': 0.7692307692307693, '(3, 7)': 0.7692307692307693, '(7, 1)': 0.7692307692307693, '(1, 0)': 0.7692307692307693}, 'common_edge_ratio': 0.2727272727272727}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1972.0, 'avg_gap': 64882.338461538464}, 'structure_gap': {'unique_elite_edges': 199, 'unique_pop_edges': 548, 'common_edges': 58}}, 'elite_diversity': {'diversity_score': 0.34304584304584307}}
2025-06-22 18:34:41,663 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:34:41,667 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:34:41,667 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:34:41,667 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=11493.0, max=125723.0, mean=81472.8, std=39121.61684235456
- Diversity: 0.9626262626262625, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Evolution Context:
- Iteration: 1/5
- Historical trend: Recent costs: [11493.0]
- Improvement rate: Not available
- Stagnation periods: None detected

## Problem Characteristics:
- TSP instance: Unknown with 0 nodes
- Search space topology: Likely rugged with many local optima
- Edge distribution: Unknown
- Known difficult regions: 0 identified

## Key Relationships:
- Elite solutions share 0% of edges
- Population clustering: No clustering information
- Exploration vs exploitation balance: High exploration (>70%)
- Local optima density: Low

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality, deceptiveness)
2. Population state (diversity, convergence, clustering)
3. Difficult regions and specific challenges (edge crossings, isolated nodes)
4. Opportunity regions with potential for improvement
5. Current evolution phase (exploration, exploitation, refinement)
6. Recommended evolution direction with specific operator suggestions

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics including ruggedness, modality, and deceptiveness
- population_state: Current state assessment including diversity, convergence, and clustering
- difficult_regions: Identified challenges with specific node sequences or edges to avoid
- opportunity_regions: Promising areas with specific node sequences or edges to include
- evolution_phase: Current phase identification (exploration/exploitation/refinement)
- evolution_direction: Recommended strategy with specific operator suggestions

2025-06-22 18:34:46,061 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely very rugged due to the high standard deviation of costs (39121.6) and the fact that this is iteration 1. The initial low cost suggests a very lucky, potentially unrepresentative, solution has been found.",
    "modality": "Potentially multi-modal, but insufficient data to confirm. The vast cost range suggests different regions of the search space may have vastly different qualities, indicative of modality.",
    "deceptiveness": "Unknown, but likely present. The low number of nodes in the TSP instance prevents assessing. The ruggedness suggests that misleading local optima may exist.",
    "edge_distribution_information": "Unknown due to no edges or known nodes, and the TSP instance being defined as unknown."
  },
  "population_state": {
    "diversity": "High (0.963), indicating the population is exploring a wide range of solutions. This is expected at the beginning of evolution.",
    "convergence": "Very low (0.0), which is expected given the early stage of evolution and the high diversity.",
    "clustering": "No clustering information is available. This will become important as the population evolves."
  },
  "difficult_regions": "Due to the extremely limited data, it is impossible to identify difficult regions. No node sequences, edges or specific characteristics can be identified.",
  "opportunity_regions": "Due to the extremely limited data, it is impossible to identify promising areas. No node sequences or edges to include can be recommended.",
  "evolution_phase": "Primarily exploration. The high diversity and lack of convergence indicate the algorithm is still exploring the search space. This phase is important to avoid premature convergence.",
  "evolution_direction": {
    "strategy": "Continue exploration to diversify the population and avoid premature convergence, especially since this is iteration 1.",
    "operator_suggestions": [
      "Apply mutation operators with a high probability (e.g., swap, insert, 2-opt, or 3-opt) to ensure population exploration.",
      "Use a selection mechanism that favors diversity over convergence. Consider roulette wheel selection or tournament selection with a low tournament size to reduce the chance of a single high-quality solution dominating the population."
    ]
  }
}
```

2025-06-22 18:34:46,061 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:34:46,061 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Likely very rugged due to the high standard deviation of costs (39121.6) and the fact that this is iteration 1. The initial low cost suggests a very lucky, potentially unrepresentative, solution has been found.', 'modality': 'Potentially multi-modal, but insufficient data to confirm. The vast cost range suggests different regions of the search space may have vastly different qualities, indicative of modality.', 'deceptiveness': 'Unknown, but likely present. The low number of nodes in the TSP instance prevents assessing. The ruggedness suggests that misleading local optima may exist.', 'edge_distribution_information': 'Unknown due to no edges or known nodes, and the TSP instance being defined as unknown.'}, 'population_state': {'diversity': 'High (0.963), indicating the population is exploring a wide range of solutions. This is expected at the beginning of evolution.', 'convergence': 'Very low (0.0), which is expected given the early stage of evolution and the high diversity.', 'clustering': 'No clustering information is available. This will become important as the population evolves.'}, 'difficult_regions': 'Due to the extremely limited data, it is impossible to identify difficult regions. No node sequences, edges or specific characteristics can be identified.', 'opportunity_regions': 'Due to the extremely limited data, it is impossible to identify promising areas. No node sequences or edges to include can be recommended.', 'evolution_phase': 'Primarily exploration. The high diversity and lack of convergence indicate the algorithm is still exploring the search space. This phase is important to avoid premature convergence.', 'evolution_direction': {'strategy': 'Continue exploration to diversify the population and avoid premature convergence, especially since this is iteration 1.', 'operator_suggestions': ['Apply mutation operators with a high probability (e.g., swap, insert, 2-opt, or 3-opt) to ensure population exploration.', 'Use a selection mechanism that favors diversity over convergence. Consider roulette wheel selection or tournament selection with a low tournament size to reduce the chance of a single high-quality solution dominating the population.']}}
2025-06-22 18:34:46,061 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:34:46,061 - __main__ - INFO - 分析阶段完成
2025-06-22 18:34:46,061 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Likely very rugged due to the high standard deviation of costs (39121.6) and the fact that this is iteration 1. The initial low cost suggests a very lucky, potentially unrepresentative, solution has been found.', 'modality': 'Potentially multi-modal, but insufficient data to confirm. The vast cost range suggests different regions of the search space may have vastly different qualities, indicative of modality.', 'deceptiveness': 'Unknown, but likely present. The low number of nodes in the TSP instance prevents assessing. The ruggedness suggests that misleading local optima may exist.', 'edge_distribution_information': 'Unknown due to no edges or known nodes, and the TSP instance being defined as unknown.'}, 'population_state': {'diversity': 'High (0.963), indicating the population is exploring a wide range of solutions. This is expected at the beginning of evolution.', 'convergence': 'Very low (0.0), which is expected given the early stage of evolution and the high diversity.', 'clustering': 'No clustering information is available. This will become important as the population evolves.'}, 'difficult_regions': 'Due to the extremely limited data, it is impossible to identify difficult regions. No node sequences, edges or specific characteristics can be identified.', 'opportunity_regions': 'Due to the extremely limited data, it is impossible to identify promising areas. No node sequences or edges to include can be recommended.', 'evolution_phase': 'Primarily exploration. The high diversity and lack of convergence indicate the algorithm is still exploring the search space. This phase is important to avoid premature convergence.', 'evolution_direction': {'strategy': 'Continue exploration to diversify the population and avoid premature convergence, especially since this is iteration 1.', 'operator_suggestions': ['Apply mutation operators with a high probability (e.g., swap, insert, 2-opt, or 3-opt) to ensure population exploration.', 'Use a selection mechanism that favors diversity over convergence. Consider roulette wheel selection or tournament selection with a low tournament size to reduce the chance of a single high-quality solution dominating the population.']}}
2025-06-22 18:34:46,061 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:34:46,061 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:34:46,061 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 1
- Population: 10 individuals, diversity=High (0.963), indicating the population is exploring a wide range of solutions. This is expected at the beginning of evolution.
- Best cost: 11493.0, Mean cost: 81472.8
- Search space: Ruggedness: Likely very rugged due to the high standard deviation of costs (39121.6) and the fact that this is iteration 1. The initial low cost suggests a very lucky, potentially unrepresentative, solution has been found., Modality: Potentially multi-modal, but insufficient data to confirm. The vast cost range suggests different regions of the search space may have vastly different qualities, indicative of modality.
- Difficult regions: 154 regions
- Opportunity regions: 138 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early Exploration/Stagnation",
    "features": "The population is experiencing an increase in the cost (both min and mean), while diversity has improved....

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:34:46,061 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:34:48,490 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size_factor": 0.2,
      "local_search_iterations": 5,
      "crossover_probability": 0.8
    },
    "exploit": {
      "mutation_rate": 0.05,
      "step_size_factor": 0.05,
      "local_search_iterations": 10,
      "crossover_probability": 0.9
    }
  }
}
```

2025-06-22 18:34:48,490 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-22 18:34:48,490 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-22 18:34:48,491 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size_factor": 0.2,
      "local_search_iterations": 5,
      "crossover_probability": 0.8
    },
    "exploit": {
      "mutation_rate": 0.05,
      "step_size_factor": 0.05,
      "local_search_iterations": 10,
      "crossover_probability": 0.9
    }
  }
}
```

2025-06-22 18:34:48,491 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:34:48,491 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-22 18:34:48,491 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size_factor": 0.2,
      "local_search_iterations": 5,
      "crossover_probability": 0.8
    },
    "exploit": {
      "mutation_rate": 0.05,
      "step_size_factor": 0.05,
      "local_search_iterations": 10,
      "crossover_probability": 0.9
    }
  }
}
```

2025-06-22 18:34:48,491 - __main__ - INFO - 开始进化阶段
2025-06-22 18:34:48,491 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:34:48,491 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:34:48,491 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 2, 9] and ending with [52, 59, 65]
- Cost: 54606.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8476923076923077
- Unexplored regions: 138 opportunity regions
- Opportunity regions: 138 regions
- Difficult regions to avoid: 154 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:34:48,491 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:34:52,325 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 9
  ],
  "modification_strategy": "This strategy prioritizes diversity by starting with a more linear traversal of the initial nodes (0-8) and then exploring the rest of the graph in a new, potentially less efficient but more diverse order. The focus is on exploring the unexplored and difficult regions identified by the search context. I tried to create a path with a more diverse starting sequence and a different node order to cover more unexplored areas. The inclusion of node 9 at the end aims to create a slightly different path ending. ",
  "targeted_regions": "The strategy targets the opportunity regions by attempting to distribute the exploration across them rather than focusing on a specific cluster. The order of nodes after the first few allows exploration in various difficult regions and the unexplored ones."
}
```
2025-06-22 18:34:52,325 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:34:52,328 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11458.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 9]
2025-06-22 18:34:52,328 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 9], 'cur_cost': 11458.0}
2025-06-22 18:34:52,328 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 18:34:52,328 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:34:52,328 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 2, 6] and ending with [63, 65, 4]
- Cost: 19772.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8276923076923077
- Unexplored regions: 138 opportunity regions
- Opportunity regions: 138 regions
- Difficult regions to avoid: 154 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:34:52,328 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:34:56,911 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 3, 7, 11, 13, 17, 21, 23, 27, 31, 33, 37, 41, 43, 47, 49, 51, 55, 57, 59, 61, 62, 64, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 5, 9, 15, 19, 25, 29, 35, 39, 45, 53, 63, 65, 61, 4
  ],
  "modification_strategy": "This strategy emphasizes diversity by creating a path that zigzags through the node space. It starts by picking a selection of nodes with increasing indices up to the middle and then reversing and zigzagging. Further, nodes were strategically inserted to create more diversity. The end and beginning nodes of the current path have been maintained.",
  "targeted_regions": "The primary targets for exploration were the unexplored opportunity regions, especially those with higher diversity scores as indicated in the search context. Also included nodes from difficult regions to avoid."
}
```
2025-06-22 18:34:56,911 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:34:56,911 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25723.0, 路径: [0, 1, 3, 7, 11, 13, 17, 21, 23, 27, 31, 33, 37, 41, 43, 47, 49, 51, 55, 57, 59, 61, 62, 64, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 5, 9, 15, 19, 25, 29, 35, 39, 45, 53, 63, 65]
2025-06-22 18:34:56,911 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 3, 7, 11, 13, 17, 21, 23, 27, 31, 33, 37, 41, 43, 47, 49, 51, 55, 57, 59, 61, 62, 64, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 5, 9, 15, 19, 25, 29, 35, 39, 45, 53, 63, 65], 'cur_cost': 25723.0}
2025-06-22 18:34:56,911 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 18:34:56,911 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:34:56,914 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [12, 22, 23] and ending with [19, 20, 21]
- Cost: 11493.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8261538461538461
- Unexplored regions: 138 opportunity regions
- Opportunity regions: 138 regions
- Difficult regions to avoid: 154 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:34:56,914 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:35:00,038 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11
  ],
  "modification_strategy": "Reversed the initial and final segments of the current path, and then explored a contiguous region of unexplored nodes, aiming for a different overall path structure and improved diversity. A new segment starting from 24 has been created to ensure new promising regions are considered. The remaining nodes were connected using a simple sequential ordering.",
  "targeted_regions": "Regions associated with the nodes from 24 to 65, focusing on those regions identified as unexplored opportunities."
}
```
2025-06-22 18:35:00,038 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:35:00,038 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11461.0, 路径: [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-06-22 18:35:00,038 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}
2025-06-22 18:35:00,038 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 18:35:00,038 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:35:00,038 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [39, 5, 11] and ending with [53, 60, 12]
- Cost: 65186.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8276923076923077
- Unexplored regions: 138 opportunity regions
- Opportunity regions: 138 regions
- Difficult regions to avoid: 154 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:35:00,038 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:35:05,297 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    39, 5, 11, 22, 44, 1, 58, 17, 28, 40, 4, 61, 32, 13, 49, 60, 53, 2, 46, 31, 56, 9, 20, 65, 34, 52, 37, 16, 45, 18, 25, 12, 3, 64, 55, 50, 29, 59, 47, 23, 30, 10, 54, 62, 38, 42, 24, 41, 7, 15, 6, 36, 19, 8, 35, 26, 63, 57, 43, 27, 51, 0, 33, 14, 48, 21
  ],
  "modification_strategy": "Implemented a path generation strategy that prioritizes exploration of unexplored regions while attempting to maintain some connection to the starting and ending points. The strategy focuses on maximizing diversity by incorporating nodes in a random order, and incorporating nodes from potentially difficult regions to avoid becoming stuck in a sub-optimal loop. The path then attempts to connect a starting node and an ending node.",
  "targeted_regions": "Focused on regions indicated as 'unexplored' (138 regions), and 'difficult to avoid' (154 regions), in order to promote diversity in the new path and break out of local optima. The goal was to deviate from the original path and explore different sections of the overall problem space, while attempting to connect the starting and ending nodes. This was done without making the path look like the original path's sequence. "
}
```
2025-06-22 18:35:05,297 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:35:05,297 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111223.0, 路径: [39, 5, 11, 22, 44, 1, 58, 17, 28, 40, 4, 61, 32, 13, 49, 60, 53, 2, 46, 31, 56, 9, 20, 65, 34, 52, 37, 16, 45, 18, 25, 12, 3, 64, 55, 50, 29, 59, 47, 23, 30, 10, 54, 62, 38, 42, 24, 41, 7, 15, 6, 36, 19, 8, 35, 26, 63, 57, 43, 27, 51, 0, 33, 14, 48, 21]
2025-06-22 18:35:05,297 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [39, 5, 11, 22, 44, 1, 58, 17, 28, 40, 4, 61, 32, 13, 49, 60, 53, 2, 46, 31, 56, 9, 20, 65, 34, 52, 37, 16, 45, 18, 25, 12, 3, 64, 55, 50, 29, 59, 47, 23, 30, 10, 54, 62, 38, 42, 24, 41, 7, 15, 6, 36, 19, 8, 35, 26, 63, 57, 43, 27, 51, 0, 33, 14, 48, 21], 'cur_cost': 111223.0}
2025-06-22 18:35:05,297 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 18:35:05,297 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:35:05,297 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [2, 64, 48] and ending with [57, 21, 47]
- Cost: 125723.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8307692307692308
- Unexplored regions: 138 opportunity regions
- Opportunity regions: 138 regions
- Difficult regions to avoid: 154 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:35:05,300 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:35:12,213 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    2, 18, 4, 12, 56, 23, 34, 58, 45, 3, 54, 37, 41, 14, 0, 38, 63, 28, 49, 15, 29, 1, 5, 61, 25, 59, 24, 36, 64, 53, 60, 52, 10, 30, 9, 42, 17, 50, 62, 27, 65, 46, 33, 20, 26, 19, 11, 31, 13, 40, 51, 32, 7, 39, 48, 35, 6, 8, 16, 43, 44, 22, 21, 47, 57
  ],
  "modification_strategy": "Implemented a hybrid approach.  Initially, the first few nodes are altered significantly to move away from the starting nodes of the old path. Then, the algorithm attempts to distribute the remaining nodes to the unexplored regions by swapping nodes or adding nodes to the new path if not already included. The diversity weight is utilized to encourage the exploration of more diverse path segments. This results in a more varied and potentially higher cost, but more diverse solution.",
  "targeted_regions": "Focused on exploring the 138 unexplored regions as indicated in the search context.  Also implicitly targeted regions likely to be avoided in the original path, to improve diversity. Finally, the beginning and ending nodes were modified to diversify the beginning and ending segments of the path."
}
```
2025-06-22 18:35:12,216 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:35:12,216 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107830.0, 路径: [2, 18, 4, 12, 56, 23, 34, 58, 45, 3, 54, 37, 41, 14, 0, 38, 63, 28, 49, 15, 29, 1, 5, 61, 25, 59, 24, 36, 64, 53, 60, 52, 10, 30, 9, 42, 17, 50, 62, 27, 65, 46, 33, 20, 26, 19, 11, 31, 13, 40, 51, 32, 7, 39, 48, 35, 6, 8, 16, 43, 44, 22, 21, 47, 57, 55]
2025-06-22 18:35:12,217 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 18, 4, 12, 56, 23, 34, 58, 45, 3, 54, 37, 41, 14, 0, 38, 63, 28, 49, 15, 29, 1, 5, 61, 25, 59, 24, 36, 64, 53, 60, 52, 10, 30, 9, 42, 17, 50, 62, 27, 65, 46, 33, 20, 26, 19, 11, 31, 13, 40, 51, 32, 7, 39, 48, 35, 6, 8, 16, 43, 44, 22, 21, 47, 57, 55], 'cur_cost': 107830.0}
2025-06-22 18:35:12,217 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-22 18:35:12,217 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:35:12,218 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [49, 30, 65] and ending with [62, 5, 12]
- Cost: 114802.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8276923076923077
- Unexplored regions: 138 opportunity regions
- Opportunity regions: 138 regions
- Difficult regions to avoid: 154 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:35:12,218 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:35:15,611 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    49, 30, 65, 10, 2, 4, 60, 42, 58, 16, 3, 22, 38, 44, 56, 13, 61, 1, 64, 15, 50, 27, 34, 5, 54, 33, 14, 52, 20, 37, 6, 53, 25, 46, 9, 35, 40, 55, 23, 18, 48, 39, 57, 36, 29, 43, 17, 24, 62, 19, 26, 28, 8, 47, 32, 12, 0, 63, 45, 51, 11, 31, 7, 21, 41
  ],
  "modification_strategy": "Utilized a diversification strategy focusing on swapping segments and random exploration. Started with the given path's start and end nodes. Then, prioritized swapping larger segments of the original path to avoid similar node adjacency. Additionally, I focused on unexplored regions identified by the context, placing them strategically in the path.",
  "targeted_regions": "Focus on exploring the opportunity regions identified in the context. Specifically, I aimed to visit the more unexplored regions at various points along the path to increase path diversity. Additionally, difficult regions were carefully considered, avoiding close proximity when planning route segments to these regions.",
}
```
2025-06-22 18:35:15,614 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-22 18:35:15,614 - IdeaExtractor - ERROR - 提取探索路径时出错: Expecting property name enclosed in double quotes: line 7 column 1 (char 1003)
2025-06-22 18:35:15,614 - ExplorationExpert - ERROR - 提取探索路径时出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 18:35:15,614 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 18:35:15,614 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:35:15,614 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [17, 52, 19] and ending with [41, 53, 56]
- Cost: 105090.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8276923076923077
- Unexplored regions: 138 opportunity regions
- Opportunity regions: 138 regions
- Difficult regions to avoid: 154 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:35:15,614 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:35:19,170 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    17, 43, 23, 27, 33, 11, 59, 45, 4, 1, 61, 38, 2, 35, 19, 52, 56, 60, 50, 8, 20, 15, 63, 34, 24, 40, 16, 62, 10, 5, 6, 13, 57, 3, 26, 28, 47, 36, 21, 49, 0, 53, 51, 9, 39, 31, 18, 37, 46, 12, 29, 64, 54, 25, 32, 48, 65, 41, 14, 7, 58, 30, 44, 22, 55, 42
  ],
  "modification_strategy": "This strategy aims to diversify the path by prioritizing exploration of unexplored regions and incorporating nodes that are far from the current path. The algorithm starts with a node close to the start of the original path (17) and then uses a mix of random node selection and targeted selection of nodes, considering distance and opportunity. A degree of randomness has been integrated to escape local optima and increase path diversity.",
  "targeted_regions": "The strategy targets exploration of regions with high opportunity scores (unexplored and promising areas) that were not well-represented in the original path and avoids regions deemed difficult to traverse."
}
```
2025-06-22 18:35:19,170 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:35:19,170 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106314.0, 路径: [17, 43, 23, 27, 33, 11, 59, 45, 4, 1, 61, 38, 2, 35, 19, 52, 56, 60, 50, 8, 20, 15, 63, 34, 24, 40, 16, 62, 10, 5, 6, 13, 57, 3, 26, 28, 47, 36, 21, 49, 0, 53, 51, 9, 39, 31, 18, 37, 46, 12, 29, 64, 54, 25, 32, 48, 65, 41, 14, 7, 58, 30, 44, 22, 55, 42]
2025-06-22 18:35:19,170 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [17, 43, 23, 27, 33, 11, 59, 45, 4, 1, 61, 38, 2, 35, 19, 52, 56, 60, 50, 8, 20, 15, 63, 34, 24, 40, 16, 62, 10, 5, 6, 13, 57, 3, 26, 28, 47, 36, 21, 49, 0, 53, 51, 9, 39, 31, 18, 37, 46, 12, 29, 64, 54, 25, 32, 48, 65, 41, 14, 7, 58, 30, 44, 22, 55, 42], 'cur_cost': 106314.0}
2025-06-22 18:35:19,170 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-22 18:35:19,170 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:35:19,170 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [18, 49, 55] and ending with [7, 6, 56]
- Cost: 103192.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8276923076923077
- Unexplored regions: 138 opportunity regions
- Opportunity regions: 138 regions
- Difficult regions to avoid: 154 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:35:19,170 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:35:22,634 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    18, 49, 55, 23, 1, 38, 46, 60, 4, 12, 3, 63, 39, 41, 2, 28, 10, 16, 65, 5, 14, 52, 36, 34, 50, 25, 58, 51, 20, 33, 44, 24, 59, 62, 30, 45, 9, 11, 35, 40, 54, 6, 56, 0, 8, 13, 22, 29, 37, 61, 19, 21, 42, 48, 27, 32, 53, 15, 43, 17, 26, 31, 57, 64, 47, 7
  ],
  "modification_strategy": "Leveraged a combination of diversification and local search. The starting and ending points are maintained as in the original path. Several mid-path segments were modified by selecting nodes based on their potential to lead into unexplored regions and avoiding difficult regions. The modifications involved creating new clusters of nodes based on spatial proximity and minimizing overlaps with the existing path.",
  "targeted_regions": "Focus was on unexplored regions within the search context by prioritizing nodes in regions that have not been visited in the previous path. Also focused on avoiding high-risk regions by skipping those node indices."
}
```
2025-06-22 18:35:22,634 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:35:22,634 - ExplorationExpert - INFO - 探索路径生成完成，成本: 104310.0, 路径: [18, 49, 55, 23, 1, 38, 46, 60, 4, 12, 3, 63, 39, 41, 2, 28, 10, 16, 65, 5, 14, 52, 36, 34, 50, 25, 58, 51, 20, 33, 44, 24, 59, 62, 30, 45, 9, 11, 35, 40, 54, 6, 56, 0, 8, 13, 22, 29, 37, 61, 19, 21, 42, 48, 27, 32, 53, 15, 43, 17, 26, 31, 57, 64, 47, 7]
2025-06-22 18:35:22,634 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [18, 49, 55, 23, 1, 38, 46, 60, 4, 12, 3, 63, 39, 41, 2, 28, 10, 16, 65, 5, 14, 52, 36, 34, 50, 25, 58, 51, 20, 33, 44, 24, 59, 62, 30, 45, 9, 11, 35, 40, 54, 6, 56, 0, 8, 13, 22, 29, 37, 61, 19, 21, 42, 48, 27, 32, 53, 15, 43, 17, 26, 31, 57, 64, 47, 7], 'cur_cost': 104310.0}
2025-06-22 18:35:22,634 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 18:35:22,634 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:35:22,634 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:35:22,634 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 102711.0
2025-06-22 18:35:23,135 - ExploitationExpert - INFO - res_population_num: 16
2025-06-22 18:35:23,139 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521, 9521]
2025-06-22 18:35:23,140 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:35:23,144 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:35:23,144 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 9], 'cur_cost': 11458.0}, {'tour': [0, 1, 3, 7, 11, 13, 17, 21, 23, 27, 31, 33, 37, 41, 43, 47, 49, 51, 55, 57, 59, 61, 62, 64, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 5, 9, 15, 19, 25, 29, 35, 39, 45, 53, 63, 65], 'cur_cost': 25723.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': [39, 5, 11, 22, 44, 1, 58, 17, 28, 40, 4, 61, 32, 13, 49, 60, 53, 2, 46, 31, 56, 9, 20, 65, 34, 52, 37, 16, 45, 18, 25, 12, 3, 64, 55, 50, 29, 59, 47, 23, 30, 10, 54, 62, 38, 42, 24, 41, 7, 15, 6, 36, 19, 8, 35, 26, 63, 57, 43, 27, 51, 0, 33, 14, 48, 21], 'cur_cost': 111223.0}, {'tour': [2, 18, 4, 12, 56, 23, 34, 58, 45, 3, 54, 37, 41, 14, 0, 38, 63, 28, 49, 15, 29, 1, 5, 61, 25, 59, 24, 36, 64, 53, 60, 52, 10, 30, 9, 42, 17, 50, 62, 27, 65, 46, 33, 20, 26, 19, 11, 31, 13, 40, 51, 32, 7, 39, 48, 35, 6, 8, 16, 43, 44, 22, 21, 47, 57, 55], 'cur_cost': 107830.0}, {'tour': array([49, 30, 65, 23, 32, 54,  3, 17,  8, 37, 33, 35, 42, 18, 55, 63, 26,
        2, 51, 59, 52, 24, 64, 14, 57, 16, 36,  7,  1, 28,  0, 60, 39, 29,
       43, 40,  6, 22, 31, 56, 19,  9, 50, 41, 58, 15, 10, 44, 13, 47, 53,
       20, 34, 45, 27, 61, 48, 38, 25, 21, 11, 46,  4, 62,  5, 12]), 'cur_cost': 114802.0}, {'tour': [17, 43, 23, 27, 33, 11, 59, 45, 4, 1, 61, 38, 2, 35, 19, 52, 56, 60, 50, 8, 20, 15, 63, 34, 24, 40, 16, 62, 10, 5, 6, 13, 57, 3, 26, 28, 47, 36, 21, 49, 0, 53, 51, 9, 39, 31, 18, 37, 46, 12, 29, 64, 54, 25, 32, 48, 65, 41, 14, 7, 58, 30, 44, 22, 55, 42], 'cur_cost': 106314.0}, {'tour': [18, 49, 55, 23, 1, 38, 46, 60, 4, 12, 3, 63, 39, 41, 2, 28, 10, 16, 65, 5, 14, 52, 36, 34, 50, 25, 58, 51, 20, 33, 44, 24, 59, 62, 30, 45, 9, 11, 35, 40, 54, 6, 56, 0, 8, 13, 22, 29, 37, 61, 19, 21, 42, 48, 27, 32, 53, 15, 43, 17, 26, 31, 57, 64, 47, 7], 'cur_cost': 104310.0}, {'tour': array([43, 47, 38, 29, 15,  6, 54,  2, 23, 13, 21, 11, 19, 46, 48, 17, 65,
       26, 33, 37, 57, 12, 63, 34, 51, 39, 30, 59, 36,  0, 31, 22,  8, 45,
       49,  1,  5, 24, 35,  4, 28, 44,  7, 41, 32, 61,  9, 14, 25, 50, 64,
       16, 52, 62, 55, 42, 60, 53, 40, 18, 10, 27, 56, 58, 20,  3]), 'cur_cost': 102711.0}, {'tour': array([30, 33,  7, 52,  4, 28,  0, 43, 49, 16, 64, 15, 12, 46, 25, 63,  2,
       60,  1, 39, 54, 18,  6, 51,  3, 26, 65, 38, 32, 22, 34,  8, 55, 14,
       40, 19, 62, 11, 23, 59, 37, 48, 45, 31, 17, 57, 41, 10, 21, 44, 24,
       35, 61, 20, 53,  5, 50, 36, 42, 47, 58, 29, 13,  9, 27, 56]), 'cur_cost': 116586.0}]
2025-06-22 18:35:23,145 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:35:23,145 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-22 18:35:23,145 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 18:35:23,145 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 18:35:23,147 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:35:23,147 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:35:23,147 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 95143.0
2025-06-22 18:35:23,649 - ExploitationExpert - INFO - res_population_num: 20
2025-06-22 18:35:23,649 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:35:23,649 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 18:35:23,657 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:35:23,657 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 9], 'cur_cost': 11458.0}, {'tour': [0, 1, 3, 7, 11, 13, 17, 21, 23, 27, 31, 33, 37, 41, 43, 47, 49, 51, 55, 57, 59, 61, 62, 64, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 5, 9, 15, 19, 25, 29, 35, 39, 45, 53, 63, 65], 'cur_cost': 25723.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': [39, 5, 11, 22, 44, 1, 58, 17, 28, 40, 4, 61, 32, 13, 49, 60, 53, 2, 46, 31, 56, 9, 20, 65, 34, 52, 37, 16, 45, 18, 25, 12, 3, 64, 55, 50, 29, 59, 47, 23, 30, 10, 54, 62, 38, 42, 24, 41, 7, 15, 6, 36, 19, 8, 35, 26, 63, 57, 43, 27, 51, 0, 33, 14, 48, 21], 'cur_cost': 111223.0}, {'tour': [2, 18, 4, 12, 56, 23, 34, 58, 45, 3, 54, 37, 41, 14, 0, 38, 63, 28, 49, 15, 29, 1, 5, 61, 25, 59, 24, 36, 64, 53, 60, 52, 10, 30, 9, 42, 17, 50, 62, 27, 65, 46, 33, 20, 26, 19, 11, 31, 13, 40, 51, 32, 7, 39, 48, 35, 6, 8, 16, 43, 44, 22, 21, 47, 57, 55], 'cur_cost': 107830.0}, {'tour': array([49, 30, 65, 23, 32, 54,  3, 17,  8, 37, 33, 35, 42, 18, 55, 63, 26,
        2, 51, 59, 52, 24, 64, 14, 57, 16, 36,  7,  1, 28,  0, 60, 39, 29,
       43, 40,  6, 22, 31, 56, 19,  9, 50, 41, 58, 15, 10, 44, 13, 47, 53,
       20, 34, 45, 27, 61, 48, 38, 25, 21, 11, 46,  4, 62,  5, 12]), 'cur_cost': 114802.0}, {'tour': [17, 43, 23, 27, 33, 11, 59, 45, 4, 1, 61, 38, 2, 35, 19, 52, 56, 60, 50, 8, 20, 15, 63, 34, 24, 40, 16, 62, 10, 5, 6, 13, 57, 3, 26, 28, 47, 36, 21, 49, 0, 53, 51, 9, 39, 31, 18, 37, 46, 12, 29, 64, 54, 25, 32, 48, 65, 41, 14, 7, 58, 30, 44, 22, 55, 42], 'cur_cost': 106314.0}, {'tour': [18, 49, 55, 23, 1, 38, 46, 60, 4, 12, 3, 63, 39, 41, 2, 28, 10, 16, 65, 5, 14, 52, 36, 34, 50, 25, 58, 51, 20, 33, 44, 24, 59, 62, 30, 45, 9, 11, 35, 40, 54, 6, 56, 0, 8, 13, 22, 29, 37, 61, 19, 21, 42, 48, 27, 32, 53, 15, 43, 17, 26, 31, 57, 64, 47, 7], 'cur_cost': 104310.0}, {'tour': array([43, 47, 38, 29, 15,  6, 54,  2, 23, 13, 21, 11, 19, 46, 48, 17, 65,
       26, 33, 37, 57, 12, 63, 34, 51, 39, 30, 59, 36,  0, 31, 22,  8, 45,
       49,  1,  5, 24, 35,  4, 28, 44,  7, 41, 32, 61,  9, 14, 25, 50, 64,
       16, 52, 62, 55, 42, 60, 53, 40, 18, 10, 27, 56, 58, 20,  3]), 'cur_cost': 102711.0}, {'tour': array([18, 10, 63, 49, 27,  9, 43, 65, 62,  8, 38, 25, 19,  1, 30, 16, 17,
       23, 34, 26, 35, 47, 52, 44,  6, 37, 28, 36,  0, 40, 45, 58, 56, 61,
       55, 59, 41, 21,  3, 29, 24, 14, 32, 15,  5, 57, 64, 48, 54, 20,  7,
       50, 22, 31, 11,  2, 39, 53,  4, 51, 33, 42, 46, 13, 12, 60]), 'cur_cost': 95143.0}]
2025-06-22 18:35:23,658 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 18:35:23,658 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-22 18:35:23,659 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 18:35:23,659 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 9], 'cur_cost': 11458.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 7, 11, 13, 17, 21, 23, 27, 31, 33, 37, 41, 43, 47, 49, 51, 55, 57, 59, 61, 62, 64, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 5, 9, 15, 19, 25, 29, 35, 39, 45, 53, 63, 65], 'cur_cost': 25723.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [39, 5, 11, 22, 44, 1, 58, 17, 28, 40, 4, 61, 32, 13, 49, 60, 53, 2, 46, 31, 56, 9, 20, 65, 34, 52, 37, 16, 45, 18, 25, 12, 3, 64, 55, 50, 29, 59, 47, 23, 30, 10, 54, 62, 38, 42, 24, 41, 7, 15, 6, 36, 19, 8, 35, 26, 63, 57, 43, 27, 51, 0, 33, 14, 48, 21], 'cur_cost': 111223.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 18, 4, 12, 56, 23, 34, 58, 45, 3, 54, 37, 41, 14, 0, 38, 63, 28, 49, 15, 29, 1, 5, 61, 25, 59, 24, 36, 64, 53, 60, 52, 10, 30, 9, 42, 17, 50, 62, 27, 65, 46, 33, 20, 26, 19, 11, 31, 13, 40, 51, 32, 7, 39, 48, 35, 6, 8, 16, 43, 44, 22, 21, 47, 57, 55], 'cur_cost': 107830.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [17, 43, 23, 27, 33, 11, 59, 45, 4, 1, 61, 38, 2, 35, 19, 52, 56, 60, 50, 8, 20, 15, 63, 34, 24, 40, 16, 62, 10, 5, 6, 13, 57, 3, 26, 28, 47, 36, 21, 49, 0, 53, 51, 9, 39, 31, 18, 37, 46, 12, 29, 64, 54, 25, 32, 48, 65, 41, 14, 7, 58, 30, 44, 22, 55, 42], 'cur_cost': 106314.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [18, 49, 55, 23, 1, 38, 46, 60, 4, 12, 3, 63, 39, 41, 2, 28, 10, 16, 65, 5, 14, 52, 36, 34, 50, 25, 58, 51, 20, 33, 44, 24, 59, 62, 30, 45, 9, 11, 35, 40, 54, 6, 56, 0, 8, 13, 22, 29, 37, 61, 19, 21, 42, 48, 27, 32, 53, 15, 43, 17, 26, 31, 57, 64, 47, 7], 'cur_cost': 104310.0}}]
2025-06-22 18:35:23,659 - __main__ - INFO - 进化阶段完成
2025-06-22 18:35:23,659 - __main__ - INFO - 开始评估阶段
2025-06-22 18:35:23,659 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:35:23,660 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:35:23,661 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 1/5
- Population changes:
  * Cost: min 11493.0->11458.0, mean 81472.8->79097.5
  * Diversity: 0.8476923076923077->0.8215384615384616
  * Elite solutions: 1->2

## Strategy Performance:
- Explore: 8 individuals, success rate 37.5%, avg improvement 842.88
- Exploit: 2 individuals, success rate 50.0%, avg improvement 8505.0
- Overall improvement: 35.0

## Historical Trends:
Last 1 iterations: costs [11493.0], diversity [0.9626262626262625]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:35:28,428 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early, but showing signs of improvement",
    "features": [
      "Iteration 1 of 5, indicating the initial phase.",
      "Cost is decreasing (good).",
      "Diversity is decreasing (potentially concerning - see below).",
      "Elite solutions are increasing (positive).",
      "Overall improvement is 35.0 (moderate)."
    ]
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "37.5%",
      "avg_improvement": "842.88",
      "evaluation": "Explore is showing some success, but the success rate is not high. The average improvement is relatively small, suggesting incremental improvements, likely due to the early stage or the size of the explore set."
    },
    "exploit": {
      "success_rate": "50.0%",
      "avg_improvement": "8505.0",
      "evaluation": "Exploit is highly effective in terms of average improvement. Its higher success rate also supports the initial good candidates. But the number of exploitations is small and can be further improved. "
    },
    "overall": "Overall improvement is moderate, indicating a good initial starting point."
  },
  "balance_state": {
    "assessment": "The balance leans toward exploration due to a higher number of individuals and a lower exploit count. The exploit strategy has significantly higher improvements, highlighting an potential imbalance towards exploration and indicating good initial candidates.",
    "adjustment_needs": "Slightly shift the balance toward exploitation to leverage the successful strategies identified. Consider expanding the exploit count, depending on the diversity trend."
  },
  "recommendations": [
    {
      "action": "Increase Exploitation.",
      "details": "Increase the number of individuals using the exploit strategy by 1 or 2 individuals, while cautiously decreasing the exploration count to keep a diverse population. Ensure the elite solutions can be used."
    },
    {
      "action": "Monitor Diversity Closely.",
      "details": "The decreasing diversity is concerning.  Implement techniques to encourage diversity like mutation or the diversity of candidates used for exploiting, in the next iteration, the effects of this must be carefully monitored."
    },
    {
      "action": "Analyze Elite Solutions.",
      "details": "Inspect the 2 elite solutions to understand what makes them successful. This can inform future explorations and exploitations. Specifically, it's essential to identify patterns or features that can be integrated into a better exploration or exploitation method."
    },
      {
        "action": "Evaluate the impact on average cost",
        "details": "Check the evolution in this iteration, observe the average cost and analyse if the elite solutions really impact the average cost and if the number of exploitations is working, analyze both exploration and exploitation."
      }
  ]
}
```
2025-06-22 18:35:28,458 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:35:28,458 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early, but showing signs of improvement",
    "features": [
      "Iteration 1 of 5, indicating the initial phase.",
      "Cost is decreasing (good).",
      "Diversity is decreasing (potentially concerning - see below).",
      "Elite solutions are increasing (positive).",
      "Overall improvement is 35.0 (moderate)."
    ]
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "37.5%",
      "avg_improvement": "842.88",
      "evaluation": "Explore is showing some success, but the success rate is not high. The average improvement is relatively small, suggesting incremental improvements, likely due to the early stage or the size of the explore set."
    },
    "exploit": {
      "success_rate": "50.0%",
      "avg_improvement": "8505.0",
      "evaluation": "Exploit is highly effective in terms of average improvement. Its higher success rate also supports the initial good candidates. But the number of exploitations is small and can be further improved. "
    },
    "overall": "Overall improvement is moderate, indicating a good initial starting point."
  },
  "balance_state": {
    "assessment": "The balance leans toward exploration due to a higher number of individuals and a lower exploit count. The exploit strategy has significantly higher improvements, highlighting an potential imbalance towards exploration and indicating good initial candidates.",
    "adjustment_needs": "Slightly shift the balance toward exploitation to leverage the successful strategies identified. Consider expanding the exploit count, depending on the diversity trend."
  },
  "recommendations": [
    {
      "action": "Increase Exploitation.",
      "details": "Increase the number of individuals using the exploit strategy by 1 or 2 individuals, while cautiously decreasing the exploration count to keep a diverse population. Ensure the elite solutions can be used."
    },
    {
      "action": "Monitor Diversity Closely.",
      "details": "The decreasing diversity is concerning.  Implement techniques to encourage diversity like mutation or the diversity of candidates used for exploiting, in the next iteration, the effects of this must be carefully monitored."
    },
    {
      "action": "Analyze Elite Solutions.",
      "details": "Inspect the 2 elite solutions to understand what makes them successful. This can inform future explorations and exploitations. Specifically, it's essential to identify patterns or features that can be integrated into a better exploration or exploitation method."
    },
      {
        "action": "Evaluate the impact on average cost",
        "details": "Check the evolution in this iteration, observe the average cost and analyse if the elite solutions really impact the average cost and if the number of exploitations is working, analyze both exploration and exploitation."
      }
  ]
}
```
2025-06-22 18:35:28,458 - __main__ - INFO - 评估阶段完成
2025-06-22 18:35:28,458 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early, but showing signs of improvement",
    "features": [
      "Iteration 1 of 5, indicating the initial phase.",
      "Cost is decreasing (good).",
      "Diversity is decreasing (potentially concerning - see below).",
      "Elite solutions are increasing (positive).",
      "Overall improvement is 35.0 (moderate)."
    ]
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "37.5%",
      "avg_improvement": "842.88",
      "evaluation": "Explore is showing some success, but the success rate is not high. The average improvement is relatively small, suggesting incremental improvements, likely due to the early stage or the size of the explore set."
    },
    "exploit": {
      "success_rate": "50.0%",
      "avg_improvement": "8505.0",
      "evaluation": "Exploit is highly effective in terms of average improvement. Its higher success rate also supports the initial good candidates. But the number of exploitations is small and can be further improved. "
    },
    "overall": "Overall improvement is moderate, indicating a good initial starting point."
  },
  "balance_state": {
    "assessment": "The balance leans toward exploration due to a higher number of individuals and a lower exploit count. The exploit strategy has significantly higher improvements, highlighting an potential imbalance towards exploration and indicating good initial candidates.",
    "adjustment_needs": "Slightly shift the balance toward exploitation to leverage the successful strategies identified. Consider expanding the exploit count, depending on the diversity trend."
  },
  "recommendations": [
    {
      "action": "Increase Exploitation.",
      "details": "Increase the number of individuals using the exploit strategy by 1 or 2 individuals, while cautiously decreasing the exploration count to keep a diverse population. Ensure the elite solutions can be used."
    },
    {
      "action": "Monitor Diversity Closely.",
      "details": "The decreasing diversity is concerning.  Implement techniques to encourage diversity like mutation or the diversity of candidates used for exploiting, in the next iteration, the effects of this must be carefully monitored."
    },
    {
      "action": "Analyze Elite Solutions.",
      "details": "Inspect the 2 elite solutions to understand what makes them successful. This can inform future explorations and exploitations. Specifically, it's essential to identify patterns or features that can be integrated into a better exploration or exploitation method."
    },
      {
        "action": "Evaluate the impact on average cost",
        "details": "Check the evolution in this iteration, observe the average cost and analyse if the elite solutions really impact the average cost and if the number of exploitations is working, analyze both exploration and exploitation."
      }
  ]
}
```
2025-06-22 18:35:28,458 - __main__ - INFO - 当前最佳适应度: 11458.0
2025-06-22 18:35:28,464 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_1.pkl
2025-06-22 18:35:28,464 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-06-22 18:35:28,464 - __main__ - INFO - 开始分析阶段
2025-06-22 18:35:28,465 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:35:28,481 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11458.0, 'max': 114802.0, 'mean': 79097.5, 'std': 41620.324903224864}, 'diversity': 0.9562289562289561, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:35:28,481 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11458.0, 'max': 114802.0, 'mean': 79097.5, 'std': 41620.324903224864}, 'diversity_level': 0.9562289562289561, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:35:28,482 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:35:28,482 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:35:28,482 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:35:28,484 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 18:35:28,485 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:35:28,485 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 18:35:28,485 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:35:28,485 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:35:28,486 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:35:28,491 - EliteExpert - INFO - 精英解分析完成
2025-06-22 18:35:28,491 - __main__ - INFO - 精英专家分析报告: {'elite_count': 20, 'elite_common_features': {'common_edges': {'(51, 38)': 0.75}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1937.0, 'avg_gap': 64981.35}, 'structure_gap': {'unique_elite_edges': 211, 'unique_pop_edges': 517, 'common_edges': 61}}, 'elite_diversity': {'diversity_score': 0.2950558213716109}}
2025-06-22 18:35:28,491 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:35:28,493 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:35:28,493 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:35:28,493 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=11458.0, max=114802.0, mean=79097.5, std=41620.324903224864
- Diversity: 0.9562289562289561, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Evolution Context:
- Iteration: 2/5
- Historical trend: Recent costs: [11493.0, 11458.0]
- Improvement rate: 35.00 per iteration
- Stagnation periods: None detected

## Problem Characteristics:
- TSP instance: Unknown with 0 nodes
- Search space topology: Likely rugged with many local optima
- Edge distribution: Unknown
- Known difficult regions: 0 identified

## Key Relationships:
- Elite solutions share 0% of edges
- Population clustering: No clustering information
- Exploration vs exploitation balance: High exploration (>70%)
- Local optima density: Low

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality, deceptiveness)
2. Population state (diversity, convergence, clustering)
3. Difficult regions and specific challenges (edge crossings, isolated nodes)
4. Opportunity regions with potential for improvement
5. Current evolution phase (exploration, exploitation, refinement)
6. Recommended evolution direction with specific operator suggestions

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics including ruggedness, modality, and deceptiveness
- population_state: Current state assessment including diversity, convergence, and clustering
- difficult_regions: Identified challenges with specific node sequences or edges to avoid
- opportunity_regions: Promising areas with specific node sequences or edges to include
- evolution_phase: Current phase identification (exploration/exploitation/refinement)
- evolution_direction: Recommended strategy with specific operator suggestions

2025-06-22 18:35:33,739 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely high, given the wide cost range and stagnation of solutions. The high standard deviation of costs (41620.32) also suggests a volatile landscape.",
    "modality": "Likely multimodal due to the high cost variability. The lack of convergence also suggests multiple potential optima.",
    "deceptiveness": "Potentially deceptive. The significant cost difference between the best and worst solutions (11458.0 vs 114802.0) hints at the existence of traps or local optima which may mislead the search."
  },
  "population_state": {
    "diversity": "High (0.956). The population is well-spread across the search space.",
    "convergence": "Very low (0.0). The solutions are not clustering together.",
    "clustering": "No clustering information available, but the lack of convergence and zero elite solutions hint that similar solutions have not yet been discovered."
  },
  "difficult_regions": {
    "identified_challenges": "None explicitly identified due to the lack of information about the TSP instance and edge structure. However, the high costs of most solutions suggest the population is currently avoiding good regions, and potentially exploring more difficult ones."
  },
  "opportunity_regions": {
    "promising_areas": "It's impossible to determine specific node sequences or edges to include without knowing the TSP instance details. However, since costs have recently improved, the best solution (cost = 11458.0) should be a good starting point for local exploitation and creating variations with low cost. More exploitation may yield convergence.",
    "exploration_recommendation": "Explore the neighborhood around the lowest cost solution to improve the average cost of solutions, avoiding high-cost regions discovered in the first two iterations."
  },
  "evolution_phase": "Early exploration phase with a transition to exploitation. The recent improvement in costs suggests a beneficial exploration approach. High diversity is not harmful at this stage. The high exploration bias should shift now towards exploiting a promising area of the search space.",
  "evolution_direction": {
    "strategy": "Balanced Exploitation and Refinement",
    "operator_suggestions": [
      {
        "operator": "2-opt",
        "rationale": "Apply 2-opt to the best solutions from the last two iterations to refine solution quality and explore the local neighborhood effectively."
      },
      {
        "operator": "Swap",
        "rationale": "Swap random nodes in the best and/or promising solutions found to generate new, potentially better solutions, while promoting diversity. Use moderate mutation rate."
      },
	  {
		"operator": "Crossover",
		"rationale": "Apply crossover (e.g., partially mapped crossover (PMX)) between solutions with high fitness to create new solutions by combining existing good traits. This will facilitate refinement and exploit the potential of found areas, encouraging some level of exploitation."
	  }
    ]
  }
}
```
2025-06-22 18:35:33,739 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:35:33,739 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Likely high, given the wide cost range and stagnation of solutions. The high standard deviation of costs (41620.32) also suggests a volatile landscape.', 'modality': 'Likely multimodal due to the high cost variability. The lack of convergence also suggests multiple potential optima.', 'deceptiveness': 'Potentially deceptive. The significant cost difference between the best and worst solutions (11458.0 vs 114802.0) hints at the existence of traps or local optima which may mislead the search.'}, 'population_state': {'diversity': 'High (0.956). The population is well-spread across the search space.', 'convergence': 'Very low (0.0). The solutions are not clustering together.', 'clustering': 'No clustering information available, but the lack of convergence and zero elite solutions hint that similar solutions have not yet been discovered.'}, 'difficult_regions': {'identified_challenges': 'None explicitly identified due to the lack of information about the TSP instance and edge structure. However, the high costs of most solutions suggest the population is currently avoiding good regions, and potentially exploring more difficult ones.'}, 'opportunity_regions': {'promising_areas': "It's impossible to determine specific node sequences or edges to include without knowing the TSP instance details. However, since costs have recently improved, the best solution (cost = 11458.0) should be a good starting point for local exploitation and creating variations with low cost. More exploitation may yield convergence.", 'exploration_recommendation': 'Explore the neighborhood around the lowest cost solution to improve the average cost of solutions, avoiding high-cost regions discovered in the first two iterations.'}, 'evolution_phase': 'Early exploration phase with a transition to exploitation. The recent improvement in costs suggests a beneficial exploration approach. High diversity is not harmful at this stage. The high exploration bias should shift now towards exploiting a promising area of the search space.', 'evolution_direction': {'strategy': 'Balanced Exploitation and Refinement', 'operator_suggestions': [{'operator': '2-opt', 'rationale': 'Apply 2-opt to the best solutions from the last two iterations to refine solution quality and explore the local neighborhood effectively.'}, {'operator': 'Swap', 'rationale': 'Swap random nodes in the best and/or promising solutions found to generate new, potentially better solutions, while promoting diversity. Use moderate mutation rate.'}, {'operator': 'Crossover', 'rationale': 'Apply crossover (e.g., partially mapped crossover (PMX)) between solutions with high fitness to create new solutions by combining existing good traits. This will facilitate refinement and exploit the potential of found areas, encouraging some level of exploitation.'}]}}
2025-06-22 18:35:33,739 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:35:33,739 - __main__ - INFO - 分析阶段完成
2025-06-22 18:35:33,739 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Likely high, given the wide cost range and stagnation of solutions. The high standard deviation of costs (41620.32) also suggests a volatile landscape.', 'modality': 'Likely multimodal due to the high cost variability. The lack of convergence also suggests multiple potential optima.', 'deceptiveness': 'Potentially deceptive. The significant cost difference between the best and worst solutions (11458.0 vs 114802.0) hints at the existence of traps or local optima which may mislead the search.'}, 'population_state': {'diversity': 'High (0.956). The population is well-spread across the search space.', 'convergence': 'Very low (0.0). The solutions are not clustering together.', 'clustering': 'No clustering information available, but the lack of convergence and zero elite solutions hint that similar solutions have not yet been discovered.'}, 'difficult_regions': {'identified_challenges': 'None explicitly identified due to the lack of information about the TSP instance and edge structure. However, the high costs of most solutions suggest the population is currently avoiding good regions, and potentially exploring more difficult ones.'}, 'opportunity_regions': {'promising_areas': "It's impossible to determine specific node sequences or edges to include without knowing the TSP instance details. However, since costs have recently improved, the best solution (cost = 11458.0) should be a good starting point for local exploitation and creating variations with low cost. More exploitation may yield convergence.", 'exploration_recommendation': 'Explore the neighborhood around the lowest cost solution to improve the average cost of solutions, avoiding high-cost regions discovered in the first two iterations.'}, 'evolution_phase': 'Early exploration phase with a transition to exploitation. The recent improvement in costs suggests a beneficial exploration approach. High diversity is not harmful at this stage. The high exploration bias should shift now towards exploiting a promising area of the search space.', 'evolution_direction': {'strategy': 'Balanced Exploitation and Refinement', 'operator_suggestions': [{'operator': '2-opt', 'rationale': 'Apply 2-opt to the best solutions from the last two iterations to refine solution quality and explore the local neighborhood effectively.'}, {'operator': 'Swap', 'rationale': 'Swap random nodes in the best and/or promising solutions found to generate new, potentially better solutions, while promoting diversity. Use moderate mutation rate.'}, {'operator': 'Crossover', 'rationale': 'Apply crossover (e.g., partially mapped crossover (PMX)) between solutions with high fitness to create new solutions by combining existing good traits. This will facilitate refinement and exploit the potential of found areas, encouraging some level of exploitation.'}]}}
2025-06-22 18:35:33,739 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:35:33,739 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:35:33,739 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 2
- Population: 10 individuals, diversity=High (0.956). The population is well-spread across the search space.
- Best cost: 11458.0, Mean cost: 79097.5
- Search space: Ruggedness: Likely high, given the wide cost range and stagnation of solutions. The high standard deviation of costs (41620.32) also suggests a volatile landscape., Modality: Likely multimodal due to the high cost variability. The lack of convergence also suggests multiple potential optima.
- Difficult regions: 1 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early, but showing signs of improvement",
    "features": [
      "Iteration 1 of 5, indicating the initial phase.",
      "Cost is decreasing (good).",
...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:35:33,739 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:35:36,485 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "strategy": "Random Walk with Adaptive Step Size",
      "step_size_init": 0.1,
      "step_size_min": 0.001,
      "step_size_max": 1.0,
      "step_size_adapt_factor": 0.9
    },
    "exploit": {
      "strategy": "Local Search with Perturbation",
      "perturbation_strength": 0.05,
      "max_iterations": 100
    }
  }
}
```

2025-06-22 18:35:36,485 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 18:35:36,485 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 18:35:36,485 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "strategy": "Random Walk with Adaptive Step Size",
      "step_size_init": 0.1,
      "step_size_min": 0.001,
      "step_size_max": 1.0,
      "step_size_adapt_factor": 0.9
    },
    "exploit": {
      "strategy": "Local Search with Perturbation",
      "perturbation_strength": 0.05,
      "max_iterations": 100
    }
  }
}
```

2025-06-22 18:35:36,485 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:35:36,485 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 18:35:36,485 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "strategy": "Random Walk with Adaptive Step Size",
      "step_size_init": 0.1,
      "step_size_min": 0.001,
      "step_size_max": 1.0,
      "step_size_adapt_factor": 0.9
    },
    "exploit": {
      "strategy": "Local Search with Perturbation",
      "perturbation_strength": 0.05,
      "max_iterations": 100
    }
  }
}
```

2025-06-22 18:35:36,485 - __main__ - INFO - 开始进化阶段
2025-06-22 18:35:36,485 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:35:36,485 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:35:36,485 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 1, 2] and ending with [64, 65, 9]
- Cost: 11458.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8215384615384616
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:35:36,485 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:35:40,759 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2
  ],
  "modification_strategy": "This strategy prioritizes exploring unexplored regions by creating a spiral-like traversal across the nodes. Starting from a base set of nodes (0, 1, 2, 3, 6, 9, 12, 15 etc.) and then reversing direction and hitting the nodes that create a kind of 'spiral' exploration pattern. It aims to diversify the path significantly from the existing one while covering all nodes exactly once. The strategy is also designed to quickly cover opportunity regions. It explores nodes in a seemingly 'orderly' fashion to allow for potentially faster optimization in a following stage. The inclusion of the last node as a reverse path to the previous node and then returning allows for good coverage as it starts at one end and goes to the other side, exploring it in one direction and coming back in the opposite.",
  "targeted_regions": "The path targets all regions but prioritizes the opportunity regions by spreading nodes more evenly across them. The spiral pattern aims to reduce the risk of getting stuck in the difficult regions by allowing the search to 'bounce' over them. The path also attempts to maximize the travel through the opportunity regions, therefore, there is good coverage of the opportunity regions."
}
```
2025-06-22 18:35:40,759 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:35:40,759 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25977.0, 路径: [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2]
2025-06-22 18:35:40,759 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 25977.0}
2025-06-22 18:35:40,762 - __main__ - INFO - 为个体 1 生成利用路径
2025-06-22 18:35:40,762 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:35:40,762 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:35:40,762 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 108539.0
2025-06-22 18:35:42,389 - ExploitationExpert - INFO - res_population_num: 21
2025-06-22 18:35:42,389 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521]
2025-06-22 18:35:42,389 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:35:42,399 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:35:42,399 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 25977.0}, {'tour': array([41, 58, 37, 50, 57, 38, 54, 24, 47, 46, 61, 51, 20, 44,  3, 22,  8,
       23, 59,  2, 26, 19,  9,  0,  4, 21, 34, 31, 65,  7, 27, 12,  6, 25,
       62, 16, 40, 55, 60, 36, 30, 18, 15, 43, 13,  5, 53, 35, 11, 52, 10,
       63, 48, 56, 49, 28, 39, 42, 64, 32, 14, 17, 29, 33,  1, 45]), 'cur_cost': 108539.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': [39, 5, 11, 22, 44, 1, 58, 17, 28, 40, 4, 61, 32, 13, 49, 60, 53, 2, 46, 31, 56, 9, 20, 65, 34, 52, 37, 16, 45, 18, 25, 12, 3, 64, 55, 50, 29, 59, 47, 23, 30, 10, 54, 62, 38, 42, 24, 41, 7, 15, 6, 36, 19, 8, 35, 26, 63, 57, 43, 27, 51, 0, 33, 14, 48, 21], 'cur_cost': 111223.0}, {'tour': [2, 18, 4, 12, 56, 23, 34, 58, 45, 3, 54, 37, 41, 14, 0, 38, 63, 28, 49, 15, 29, 1, 5, 61, 25, 59, 24, 36, 64, 53, 60, 52, 10, 30, 9, 42, 17, 50, 62, 27, 65, 46, 33, 20, 26, 19, 11, 31, 13, 40, 51, 32, 7, 39, 48, 35, 6, 8, 16, 43, 44, 22, 21, 47, 57, 55], 'cur_cost': 107830.0}, {'tour': array([49, 30, 65, 23, 32, 54,  3, 17,  8, 37, 33, 35, 42, 18, 55, 63, 26,
        2, 51, 59, 52, 24, 64, 14, 57, 16, 36,  7,  1, 28,  0, 60, 39, 29,
       43, 40,  6, 22, 31, 56, 19,  9, 50, 41, 58, 15, 10, 44, 13, 47, 53,
       20, 34, 45, 27, 61, 48, 38, 25, 21, 11, 46,  4, 62,  5, 12]), 'cur_cost': 114802.0}, {'tour': [17, 43, 23, 27, 33, 11, 59, 45, 4, 1, 61, 38, 2, 35, 19, 52, 56, 60, 50, 8, 20, 15, 63, 34, 24, 40, 16, 62, 10, 5, 6, 13, 57, 3, 26, 28, 47, 36, 21, 49, 0, 53, 51, 9, 39, 31, 18, 37, 46, 12, 29, 64, 54, 25, 32, 48, 65, 41, 14, 7, 58, 30, 44, 22, 55, 42], 'cur_cost': 106314.0}, {'tour': [18, 49, 55, 23, 1, 38, 46, 60, 4, 12, 3, 63, 39, 41, 2, 28, 10, 16, 65, 5, 14, 52, 36, 34, 50, 25, 58, 51, 20, 33, 44, 24, 59, 62, 30, 45, 9, 11, 35, 40, 54, 6, 56, 0, 8, 13, 22, 29, 37, 61, 19, 21, 42, 48, 27, 32, 53, 15, 43, 17, 26, 31, 57, 64, 47, 7], 'cur_cost': 104310.0}, {'tour': array([43, 47, 38, 29, 15,  6, 54,  2, 23, 13, 21, 11, 19, 46, 48, 17, 65,
       26, 33, 37, 57, 12, 63, 34, 51, 39, 30, 59, 36,  0, 31, 22,  8, 45,
       49,  1,  5, 24, 35,  4, 28, 44,  7, 41, 32, 61,  9, 14, 25, 50, 64,
       16, 52, 62, 55, 42, 60, 53, 40, 18, 10, 27, 56, 58, 20,  3]), 'cur_cost': 102711.0}, {'tour': array([18, 10, 63, 49, 27,  9, 43, 65, 62,  8, 38, 25, 19,  1, 30, 16, 17,
       23, 34, 26, 35, 47, 52, 44,  6, 37, 28, 36,  0, 40, 45, 58, 56, 61,
       55, 59, 41, 21,  3, 29, 24, 14, 32, 15,  5, 57, 64, 48, 54, 20,  7,
       50, 22, 31, 11,  2, 39, 53,  4, 51, 33, 42, 46, 13, 12, 60]), 'cur_cost': 95143.0}]
2025-06-22 18:35:42,402 - ExploitationExpert - INFO - 局部搜索耗时: 1.64秒
2025-06-22 18:35:42,402 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-22 18:35:42,402 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-06-22 18:35:42,402 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 18:35:42,403 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:35:42,404 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [12, 23, 22] and ending with [9, 10, 11]
- Cost: 11461.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:35:42,404 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:35:45,992 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Implemented a modified path by reordering and including nodes. Targeted exploration by including a different distribution, especially focusing on the unexplored regions indicated in search context, while preserving path validity and length. Maintained initial and end nodes.",
  "targeted_regions": "Primarily focused on exploring the unexplored regions by including a different order of node visits within the broader problem space.  Aiming to find alternative, diverse routes through the 'unexplored regions' specifically, and different overall order."
}
```
2025-06-22 18:35:45,992 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:35:45,992 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13176.0, 路径: [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 18:35:45,992 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13176.0}
2025-06-22 18:35:45,995 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-22 18:35:45,995 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:35:45,995 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:35:45,996 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 98576.0
2025-06-22 18:35:46,496 - ExploitationExpert - INFO - res_population_num: 23
2025-06-22 18:35:46,497 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521, 9521]
2025-06-22 18:35:46,497 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:35:46,508 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:35:46,508 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 25977.0}, {'tour': array([41, 58, 37, 50, 57, 38, 54, 24, 47, 46, 61, 51, 20, 44,  3, 22,  8,
       23, 59,  2, 26, 19,  9,  0,  4, 21, 34, 31, 65,  7, 27, 12,  6, 25,
       62, 16, 40, 55, 60, 36, 30, 18, 15, 43, 13,  5, 53, 35, 11, 52, 10,
       63, 48, 56, 49, 28, 39, 42, 64, 32, 14, 17, 29, 33,  1, 45]), 'cur_cost': 108539.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13176.0}, {'tour': array([42, 22, 24, 10, 31, 25, 45, 16, 11,  0, 62, 30, 44, 14, 55, 19, 48,
        4, 41, 43, 12,  1,  3, 34, 60, 40, 18, 29, 50, 27, 35, 23, 51, 47,
        6,  2, 15,  5, 39, 37, 46, 52, 57, 59, 17, 36, 53, 28, 65,  8,  9,
       21,  7, 13, 32, 33, 49, 64, 63, 54, 61, 26, 56, 58, 38, 20]), 'cur_cost': 98576.0}, {'tour': [2, 18, 4, 12, 56, 23, 34, 58, 45, 3, 54, 37, 41, 14, 0, 38, 63, 28, 49, 15, 29, 1, 5, 61, 25, 59, 24, 36, 64, 53, 60, 52, 10, 30, 9, 42, 17, 50, 62, 27, 65, 46, 33, 20, 26, 19, 11, 31, 13, 40, 51, 32, 7, 39, 48, 35, 6, 8, 16, 43, 44, 22, 21, 47, 57, 55], 'cur_cost': 107830.0}, {'tour': array([49, 30, 65, 23, 32, 54,  3, 17,  8, 37, 33, 35, 42, 18, 55, 63, 26,
        2, 51, 59, 52, 24, 64, 14, 57, 16, 36,  7,  1, 28,  0, 60, 39, 29,
       43, 40,  6, 22, 31, 56, 19,  9, 50, 41, 58, 15, 10, 44, 13, 47, 53,
       20, 34, 45, 27, 61, 48, 38, 25, 21, 11, 46,  4, 62,  5, 12]), 'cur_cost': 114802.0}, {'tour': [17, 43, 23, 27, 33, 11, 59, 45, 4, 1, 61, 38, 2, 35, 19, 52, 56, 60, 50, 8, 20, 15, 63, 34, 24, 40, 16, 62, 10, 5, 6, 13, 57, 3, 26, 28, 47, 36, 21, 49, 0, 53, 51, 9, 39, 31, 18, 37, 46, 12, 29, 64, 54, 25, 32, 48, 65, 41, 14, 7, 58, 30, 44, 22, 55, 42], 'cur_cost': 106314.0}, {'tour': [18, 49, 55, 23, 1, 38, 46, 60, 4, 12, 3, 63, 39, 41, 2, 28, 10, 16, 65, 5, 14, 52, 36, 34, 50, 25, 58, 51, 20, 33, 44, 24, 59, 62, 30, 45, 9, 11, 35, 40, 54, 6, 56, 0, 8, 13, 22, 29, 37, 61, 19, 21, 42, 48, 27, 32, 53, 15, 43, 17, 26, 31, 57, 64, 47, 7], 'cur_cost': 104310.0}, {'tour': array([43, 47, 38, 29, 15,  6, 54,  2, 23, 13, 21, 11, 19, 46, 48, 17, 65,
       26, 33, 37, 57, 12, 63, 34, 51, 39, 30, 59, 36,  0, 31, 22,  8, 45,
       49,  1,  5, 24, 35,  4, 28, 44,  7, 41, 32, 61,  9, 14, 25, 50, 64,
       16, 52, 62, 55, 42, 60, 53, 40, 18, 10, 27, 56, 58, 20,  3]), 'cur_cost': 102711.0}, {'tour': array([18, 10, 63, 49, 27,  9, 43, 65, 62,  8, 38, 25, 19,  1, 30, 16, 17,
       23, 34, 26, 35, 47, 52, 44,  6, 37, 28, 36,  0, 40, 45, 58, 56, 61,
       55, 59, 41, 21,  3, 29, 24, 14, 32, 15,  5, 57, 64, 48, 54, 20,  7,
       50, 22, 31, 11,  2, 39, 53,  4, 51, 33, 42, 46, 13, 12, 60]), 'cur_cost': 95143.0}]
2025-06-22 18:35:46,510 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:35:46,510 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-06-22 18:35:46,511 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-22 18:35:46,511 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 18:35:46,511 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:35:46,511 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [2, 18, 4] and ending with [47, 57, 55]
- Cost: 107830.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9015384615384615
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:35:46,511 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:35:51,083 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    2, 18, 4, 15, 22, 31, 45, 52, 60, 65, 58, 50, 43, 36, 29, 20, 8, 0, 1, 3, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 17, 19, 21, 23, 24, 25, 26, 27, 28, 30, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 44, 46, 47, 48, 49, 51, 53, 54, 55, 56, 57, 59, 61, 62, 63, 64
  ],
  "modification_strategy": "This strategy prioritized exploring the unexplored and difficult regions by strategically reordering the initial path.  The beginning and end points are maintained to retain the initial framing, but a reshuffling of key middle segments and a diversification of the order. This strategy leverages randomness to explore new possibilities while trying to maintain a degree of quality by retaining the start and end, along with some other well placed points in the old path.",
  "targeted_regions": "Unexplored regions and difficult regions. The strategy aims to deviate significantly from the initial path's trajectory in several key areas and to include the difficult regions to improve the overall path"
}
```
2025-06-22 18:35:51,083 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:35:51,083 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28409.0, 路径: [2, 18, 4, 15, 22, 31, 45, 52, 60, 65, 58, 50, 43, 36, 29, 20, 8, 0, 1, 3, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 17, 19, 21, 23, 24, 25, 26, 27, 28, 30, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 44, 46, 47, 48, 49, 51, 53, 54, 55, 56, 57, 59, 61, 62, 63, 64]
2025-06-22 18:35:51,083 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [2, 18, 4, 15, 22, 31, 45, 52, 60, 65, 58, 50, 43, 36, 29, 20, 8, 0, 1, 3, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 17, 19, 21, 23, 24, 25, 26, 27, 28, 30, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 44, 46, 47, 48, 49, 51, 53, 54, 55, 56, 57, 59, 61, 62, 63, 64], 'cur_cost': 28409.0}
2025-06-22 18:35:51,083 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 18:35:51,083 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:35:51,083 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:35:51,083 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 119362.0
2025-06-22 18:35:51,589 - ExploitationExpert - INFO - res_population_num: 23
2025-06-22 18:35:51,590 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521, 9521]
2025-06-22 18:35:51,590 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:35:51,600 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:35:51,600 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 25977.0}, {'tour': array([41, 58, 37, 50, 57, 38, 54, 24, 47, 46, 61, 51, 20, 44,  3, 22,  8,
       23, 59,  2, 26, 19,  9,  0,  4, 21, 34, 31, 65,  7, 27, 12,  6, 25,
       62, 16, 40, 55, 60, 36, 30, 18, 15, 43, 13,  5, 53, 35, 11, 52, 10,
       63, 48, 56, 49, 28, 39, 42, 64, 32, 14, 17, 29, 33,  1, 45]), 'cur_cost': 108539.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13176.0}, {'tour': array([42, 22, 24, 10, 31, 25, 45, 16, 11,  0, 62, 30, 44, 14, 55, 19, 48,
        4, 41, 43, 12,  1,  3, 34, 60, 40, 18, 29, 50, 27, 35, 23, 51, 47,
        6,  2, 15,  5, 39, 37, 46, 52, 57, 59, 17, 36, 53, 28, 65,  8,  9,
       21,  7, 13, 32, 33, 49, 64, 63, 54, 61, 26, 56, 58, 38, 20]), 'cur_cost': 98576.0}, {'tour': [2, 18, 4, 15, 22, 31, 45, 52, 60, 65, 58, 50, 43, 36, 29, 20, 8, 0, 1, 3, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 17, 19, 21, 23, 24, 25, 26, 27, 28, 30, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 44, 46, 47, 48, 49, 51, 53, 54, 55, 56, 57, 59, 61, 62, 63, 64], 'cur_cost': 28409.0}, {'tour': array([22, 45, 50, 55, 32, 15, 60, 29, 58, 30, 40, 57, 44, 33, 19, 62, 20,
       51,  1, 26, 42,  8, 43, 64, 25, 39, 13, 23,  0, 24,  2, 17, 48, 53,
       10, 37, 34, 41, 59,  9, 28, 52, 65, 35, 36, 61,  4,  5, 46, 27, 11,
       21,  3,  6, 18, 49, 63, 38, 54,  7, 47, 31, 12, 14, 56, 16]), 'cur_cost': 119362.0}, {'tour': [17, 43, 23, 27, 33, 11, 59, 45, 4, 1, 61, 38, 2, 35, 19, 52, 56, 60, 50, 8, 20, 15, 63, 34, 24, 40, 16, 62, 10, 5, 6, 13, 57, 3, 26, 28, 47, 36, 21, 49, 0, 53, 51, 9, 39, 31, 18, 37, 46, 12, 29, 64, 54, 25, 32, 48, 65, 41, 14, 7, 58, 30, 44, 22, 55, 42], 'cur_cost': 106314.0}, {'tour': [18, 49, 55, 23, 1, 38, 46, 60, 4, 12, 3, 63, 39, 41, 2, 28, 10, 16, 65, 5, 14, 52, 36, 34, 50, 25, 58, 51, 20, 33, 44, 24, 59, 62, 30, 45, 9, 11, 35, 40, 54, 6, 56, 0, 8, 13, 22, 29, 37, 61, 19, 21, 42, 48, 27, 32, 53, 15, 43, 17, 26, 31, 57, 64, 47, 7], 'cur_cost': 104310.0}, {'tour': array([43, 47, 38, 29, 15,  6, 54,  2, 23, 13, 21, 11, 19, 46, 48, 17, 65,
       26, 33, 37, 57, 12, 63, 34, 51, 39, 30, 59, 36,  0, 31, 22,  8, 45,
       49,  1,  5, 24, 35,  4, 28, 44,  7, 41, 32, 61,  9, 14, 25, 50, 64,
       16, 52, 62, 55, 42, 60, 53, 40, 18, 10, 27, 56, 58, 20,  3]), 'cur_cost': 102711.0}, {'tour': array([18, 10, 63, 49, 27,  9, 43, 65, 62,  8, 38, 25, 19,  1, 30, 16, 17,
       23, 34, 26, 35, 47, 52, 44,  6, 37, 28, 36,  0, 40, 45, 58, 56, 61,
       55, 59, 41, 21,  3, 29, 24, 14, 32, 15,  5, 57, 64, 48, 54, 20,  7,
       50, 22, 31, 11,  2, 39, 53,  4, 51, 33, 42, 46, 13, 12, 60]), 'cur_cost': 95143.0}]
2025-06-22 18:35:51,603 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:35:51,603 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-06-22 18:35:51,603 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 18:35:51,603 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 18:35:51,603 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:35:51,605 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [17, 43, 23] and ending with [22, 55, 42]
- Cost: 106314.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8584615384615385
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:35:51,605 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:35:55,349 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    17, 43, 23, 27, 33, 1, 60, 5, 10, 38, 48, 15, 52, 28, 4, 58, 63, 36, 12, 25, 50, 65, 3, 31, 45, 2, 56, 49, 54, 41, 8, 20, 61, 32, 13, 47, 18, 53, 30, 64, 9, 35, 19, 40, 24, 62, 39, 57, 7, 11, 37, 44, 21, 59, 14, 29, 46, 16, 6, 51, 26, 0, 34, 42, 22, 55
  ],
  "modification_strategy": "This strategy prioritizes a complete reshuffling of the path, specifically aiming to explore the unexplored and opportunity regions identified in the search context. The path begins with a similar starting point and then takes a significantly different route. The later portion of the path is heavily modified to hit the latter opportunity region while still ensuring a good spread across the entire node space.",
  "targeted_regions": "Unexplored and Opportunity regions, as well as a general even distribution across the entire set of nodes to maximize diversity, focusing on a new distribution of the nodes in the path."
}
```
2025-06-22 18:35:55,349 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:35:55,349 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116931.0, 路径: [17, 43, 23, 27, 33, 1, 60, 5, 10, 38, 48, 15, 52, 28, 4, 58, 63, 36, 12, 25, 50, 65, 3, 31, 45, 2, 56, 49, 54, 41, 8, 20, 61, 32, 13, 47, 18, 53, 30, 64, 9, 35, 19, 40, 24, 62, 39, 57, 7, 11, 37, 44, 21, 59, 14, 29, 46, 16, 6, 51, 26, 0, 34, 42, 22, 55]
2025-06-22 18:35:55,349 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [17, 43, 23, 27, 33, 1, 60, 5, 10, 38, 48, 15, 52, 28, 4, 58, 63, 36, 12, 25, 50, 65, 3, 31, 45, 2, 56, 49, 54, 41, 8, 20, 61, 32, 13, 47, 18, 53, 30, 64, 9, 35, 19, 40, 24, 62, 39, 57, 7, 11, 37, 44, 21, 59, 14, 29, 46, 16, 6, 51, 26, 0, 34, 42, 22, 55], 'cur_cost': 116931.0}
2025-06-22 18:35:55,349 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 18:35:55,349 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:35:55,349 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:35:55,354 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 94674.0
2025-06-22 18:35:55,856 - ExploitationExpert - INFO - res_population_num: 24
2025-06-22 18:35:55,856 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521, 9521, 9521]
2025-06-22 18:35:55,856 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:35:55,866 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:35:55,867 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 25977.0}, {'tour': array([41, 58, 37, 50, 57, 38, 54, 24, 47, 46, 61, 51, 20, 44,  3, 22,  8,
       23, 59,  2, 26, 19,  9,  0,  4, 21, 34, 31, 65,  7, 27, 12,  6, 25,
       62, 16, 40, 55, 60, 36, 30, 18, 15, 43, 13,  5, 53, 35, 11, 52, 10,
       63, 48, 56, 49, 28, 39, 42, 64, 32, 14, 17, 29, 33,  1, 45]), 'cur_cost': 108539.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13176.0}, {'tour': array([42, 22, 24, 10, 31, 25, 45, 16, 11,  0, 62, 30, 44, 14, 55, 19, 48,
        4, 41, 43, 12,  1,  3, 34, 60, 40, 18, 29, 50, 27, 35, 23, 51, 47,
        6,  2, 15,  5, 39, 37, 46, 52, 57, 59, 17, 36, 53, 28, 65,  8,  9,
       21,  7, 13, 32, 33, 49, 64, 63, 54, 61, 26, 56, 58, 38, 20]), 'cur_cost': 98576.0}, {'tour': [2, 18, 4, 15, 22, 31, 45, 52, 60, 65, 58, 50, 43, 36, 29, 20, 8, 0, 1, 3, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 17, 19, 21, 23, 24, 25, 26, 27, 28, 30, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 44, 46, 47, 48, 49, 51, 53, 54, 55, 56, 57, 59, 61, 62, 63, 64], 'cur_cost': 28409.0}, {'tour': array([22, 45, 50, 55, 32, 15, 60, 29, 58, 30, 40, 57, 44, 33, 19, 62, 20,
       51,  1, 26, 42,  8, 43, 64, 25, 39, 13, 23,  0, 24,  2, 17, 48, 53,
       10, 37, 34, 41, 59,  9, 28, 52, 65, 35, 36, 61,  4,  5, 46, 27, 11,
       21,  3,  6, 18, 49, 63, 38, 54,  7, 47, 31, 12, 14, 56, 16]), 'cur_cost': 119362.0}, {'tour': [17, 43, 23, 27, 33, 1, 60, 5, 10, 38, 48, 15, 52, 28, 4, 58, 63, 36, 12, 25, 50, 65, 3, 31, 45, 2, 56, 49, 54, 41, 8, 20, 61, 32, 13, 47, 18, 53, 30, 64, 9, 35, 19, 40, 24, 62, 39, 57, 7, 11, 37, 44, 21, 59, 14, 29, 46, 16, 6, 51, 26, 0, 34, 42, 22, 55], 'cur_cost': 116931.0}, {'tour': array([ 4, 46,  8, 10, 32, 52, 58, 36, 15, 54, 59, 60, 20, 16, 28, 44, 21,
       41, 49, 48, 26, 45, 19, 25, 51, 11, 17, 12, 53, 57, 63, 50, 35,  2,
       37, 43, 47, 56, 55, 22, 13, 34, 30, 23,  7, 65, 31, 38, 39, 33, 61,
       64,  6, 27,  9, 42, 18, 62, 14, 40,  1,  3, 24,  0, 29,  5]), 'cur_cost': 94674.0}, {'tour': array([43, 47, 38, 29, 15,  6, 54,  2, 23, 13, 21, 11, 19, 46, 48, 17, 65,
       26, 33, 37, 57, 12, 63, 34, 51, 39, 30, 59, 36,  0, 31, 22,  8, 45,
       49,  1,  5, 24, 35,  4, 28, 44,  7, 41, 32, 61,  9, 14, 25, 50, 64,
       16, 52, 62, 55, 42, 60, 53, 40, 18, 10, 27, 56, 58, 20,  3]), 'cur_cost': 102711.0}, {'tour': array([18, 10, 63, 49, 27,  9, 43, 65, 62,  8, 38, 25, 19,  1, 30, 16, 17,
       23, 34, 26, 35, 47, 52, 44,  6, 37, 28, 36,  0, 40, 45, 58, 56, 61,
       55, 59, 41, 21,  3, 29, 24, 14, 32, 15,  5, 57, 64, 48, 54, 20,  7,
       50, 22, 31, 11,  2, 39, 53,  4, 51, 33, 42, 46, 13, 12, 60]), 'cur_cost': 95143.0}]
2025-06-22 18:35:55,870 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:35:55,870 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-06-22 18:35:55,870 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 18:35:55,870 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 18:35:55,870 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:35:55,872 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [43, 47, 38] and ending with [58, 20, 3]
- Cost: 102711.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8569230769230769
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:35:55,872 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:35:59,547 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    43, 47, 38, 60, 2, 50, 15, 55, 44, 31, 18, 40, 5, 23, 65, 10, 35, 1, 63, 13, 39, 21, 54, 26, 48, 34, 17, 64, 36, 9, 29, 46, 59, 7, 4, 56, 25, 32, 62, 11, 57, 27, 33, 16, 61, 42, 8, 53, 37, 19, 51, 30, 45, 14, 28, 22, 49, 20, 3, 52, 0, 12, 24, 41, 6, 58
  ],
  "modification_strategy": "I aimed to diversify the path significantly by focusing on unexplored regions and avoiding difficult ones. I started with the existing start and end nodes, and then used a more scattered approach for intermediate nodes, prioritizing nodes known to lead towards unexplored or opportunity regions. The goal was to create a path that differs significantly from the existing one.",
  "targeted_regions": "Opportunity regions and unexplored regions were the primary focus. Difficult regions were taken into account to avoid increasing the cost unnecessarily."
}
```
2025-06-22 18:35:59,547 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:35:59,547 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111265.0, 路径: [43, 47, 38, 60, 2, 50, 15, 55, 44, 31, 18, 40, 5, 23, 65, 10, 35, 1, 63, 13, 39, 21, 54, 26, 48, 34, 17, 64, 36, 9, 29, 46, 59, 7, 4, 56, 25, 32, 62, 11, 57, 27, 33, 16, 61, 42, 8, 53, 37, 19, 51, 30, 45, 14, 28, 22, 49, 20, 3, 52, 0, 12, 24, 41, 6, 58]
2025-06-22 18:35:59,547 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [43, 47, 38, 60, 2, 50, 15, 55, 44, 31, 18, 40, 5, 23, 65, 10, 35, 1, 63, 13, 39, 21, 54, 26, 48, 34, 17, 64, 36, 9, 29, 46, 59, 7, 4, 56, 25, 32, 62, 11, 57, 27, 33, 16, 61, 42, 8, 53, 37, 19, 51, 30, 45, 14, 28, 22, 49, 20, 3, 52, 0, 12, 24, 41, 6, 58], 'cur_cost': 111265.0}
2025-06-22 18:35:59,547 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 18:35:59,547 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:35:59,547 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:35:59,547 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 112426.0
2025-06-22 18:36:00,053 - ExploitationExpert - INFO - res_population_num: 25
2025-06-22 18:36:00,053 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:36:00,053 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:36:00,063 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:36:00,065 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 25977.0}, {'tour': array([41, 58, 37, 50, 57, 38, 54, 24, 47, 46, 61, 51, 20, 44,  3, 22,  8,
       23, 59,  2, 26, 19,  9,  0,  4, 21, 34, 31, 65,  7, 27, 12,  6, 25,
       62, 16, 40, 55, 60, 36, 30, 18, 15, 43, 13,  5, 53, 35, 11, 52, 10,
       63, 48, 56, 49, 28, 39, 42, 64, 32, 14, 17, 29, 33,  1, 45]), 'cur_cost': 108539.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13176.0}, {'tour': array([42, 22, 24, 10, 31, 25, 45, 16, 11,  0, 62, 30, 44, 14, 55, 19, 48,
        4, 41, 43, 12,  1,  3, 34, 60, 40, 18, 29, 50, 27, 35, 23, 51, 47,
        6,  2, 15,  5, 39, 37, 46, 52, 57, 59, 17, 36, 53, 28, 65,  8,  9,
       21,  7, 13, 32, 33, 49, 64, 63, 54, 61, 26, 56, 58, 38, 20]), 'cur_cost': 98576.0}, {'tour': [2, 18, 4, 15, 22, 31, 45, 52, 60, 65, 58, 50, 43, 36, 29, 20, 8, 0, 1, 3, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 17, 19, 21, 23, 24, 25, 26, 27, 28, 30, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 44, 46, 47, 48, 49, 51, 53, 54, 55, 56, 57, 59, 61, 62, 63, 64], 'cur_cost': 28409.0}, {'tour': array([22, 45, 50, 55, 32, 15, 60, 29, 58, 30, 40, 57, 44, 33, 19, 62, 20,
       51,  1, 26, 42,  8, 43, 64, 25, 39, 13, 23,  0, 24,  2, 17, 48, 53,
       10, 37, 34, 41, 59,  9, 28, 52, 65, 35, 36, 61,  4,  5, 46, 27, 11,
       21,  3,  6, 18, 49, 63, 38, 54,  7, 47, 31, 12, 14, 56, 16]), 'cur_cost': 119362.0}, {'tour': [17, 43, 23, 27, 33, 1, 60, 5, 10, 38, 48, 15, 52, 28, 4, 58, 63, 36, 12, 25, 50, 65, 3, 31, 45, 2, 56, 49, 54, 41, 8, 20, 61, 32, 13, 47, 18, 53, 30, 64, 9, 35, 19, 40, 24, 62, 39, 57, 7, 11, 37, 44, 21, 59, 14, 29, 46, 16, 6, 51, 26, 0, 34, 42, 22, 55], 'cur_cost': 116931.0}, {'tour': array([ 4, 46,  8, 10, 32, 52, 58, 36, 15, 54, 59, 60, 20, 16, 28, 44, 21,
       41, 49, 48, 26, 45, 19, 25, 51, 11, 17, 12, 53, 57, 63, 50, 35,  2,
       37, 43, 47, 56, 55, 22, 13, 34, 30, 23,  7, 65, 31, 38, 39, 33, 61,
       64,  6, 27,  9, 42, 18, 62, 14, 40,  1,  3, 24,  0, 29,  5]), 'cur_cost': 94674.0}, {'tour': [43, 47, 38, 60, 2, 50, 15, 55, 44, 31, 18, 40, 5, 23, 65, 10, 35, 1, 63, 13, 39, 21, 54, 26, 48, 34, 17, 64, 36, 9, 29, 46, 59, 7, 4, 56, 25, 32, 62, 11, 57, 27, 33, 16, 61, 42, 8, 53, 37, 19, 51, 30, 45, 14, 28, 22, 49, 20, 3, 52, 0, 12, 24, 41, 6, 58], 'cur_cost': 111265.0}, {'tour': array([62, 19, 12, 63, 26, 45, 36, 56,  2, 15, 57, 23, 27,  7, 24, 53, 43,
        0, 47, 22, 14, 10, 33,  8, 61, 65, 42, 46, 18, 34, 28, 20, 29, 49,
       17,  1, 25,  6, 52,  5, 16, 50, 21, 58, 54, 30,  3, 48, 40, 64, 38,
       31, 51, 13, 35, 32,  4, 59, 39,  9, 55, 44, 60, 11, 41, 37]), 'cur_cost': 112426.0}]
2025-06-22 18:36:00,067 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:36:00,067 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-06-22 18:36:00,068 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 18:36:00,068 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 64, 61, 58, 55, 52, 49, 46, 43, 40, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 25977.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13176.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [2, 18, 4, 15, 22, 31, 45, 52, 60, 65, 58, 50, 43, 36, 29, 20, 8, 0, 1, 3, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 17, 19, 21, 23, 24, 25, 26, 27, 28, 30, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 44, 46, 47, 48, 49, 51, 53, 54, 55, 56, 57, 59, 61, 62, 63, 64], 'cur_cost': 28409.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [17, 43, 23, 27, 33, 1, 60, 5, 10, 38, 48, 15, 52, 28, 4, 58, 63, 36, 12, 25, 50, 65, 3, 31, 45, 2, 56, 49, 54, 41, 8, 20, 61, 32, 13, 47, 18, 53, 30, 64, 9, 35, 19, 40, 24, 62, 39, 57, 7, 11, 37, 44, 21, 59, 14, 29, 46, 16, 6, 51, 26, 0, 34, 42, 22, 55], 'cur_cost': 116931.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [43, 47, 38, 60, 2, 50, 15, 55, 44, 31, 18, 40, 5, 23, 65, 10, 35, 1, 63, 13, 39, 21, 54, 26, 48, 34, 17, 64, 36, 9, 29, 46, 59, 7, 4, 56, 25, 32, 62, 11, 57, 27, 33, 16, 61, 42, 8, 53, 37, 19, 51, 30, 45, 14, 28, 22, 49, 20, 3, 52, 0, 12, 24, 41, 6, 58], 'cur_cost': 111265.0}}]
2025-06-22 18:36:00,068 - __main__ - INFO - 进化阶段完成
2025-06-22 18:36:00,068 - __main__ - INFO - 开始评估阶段
2025-06-22 18:36:00,068 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:36:00,070 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:36:00,070 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 2/5
- Population changes:
  * Cost: min 11458.0->13176.0, mean 79097.5->82933.5
  * Diversity: 0.8215384615384616->0.8723076923076923
  * Elite solutions: 2->1

## Strategy Performance:
- Explore: 5 individuals, success rate 20.0%, avg improvement 8803.2
- Exploit: 5 individuals, success rate 40.0%, avg improvement -16475.2
- Overall improvement: -1718.0

## Historical Trends:
Last 2 iterations: costs [11493.0, 11458.0], diversity [0.9626262626262625, 0.9562289562289561]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:36:06,712 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration, Potential for Improvement",
    "features": "Cost is trending upwards, indicating potential degradation. Diversity is increasing slightly, which is positive. Number of elite solutions has decreased, which is concerning.  Overall improvement is negative, suggesting the current strategy is not yielding positive results.",
    "indicators": {
      "cost_trend": "Increasing (detrimental)",
      "diversity_trend": "Slightly Increasing (favorable)",
      "elite_count_trend": "Decreasing (detrimental)",
      "overall_improvement": "Negative (detrimental)"
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Low (20%)",
      "avg_improvement": "Positive (8803.2)",
      "evaluation": "Explore is yielding some positive improvements, but with a very low success rate. This suggests that while the individuals explored have potential, the selection/mutation is not optimized or exploring too broadly.",
      "action": "Refine explore strategy, possibly by adjusting mutation rates or focusing on more promising regions"
    },
    "exploit": {
      "success_rate": "Moderate (40%)",
      "avg_improvement": "Negative (-16475.2)",
      "evaluation": "Exploit is yielding negative average improvements despite a moderate success rate. This indicates the exploitation is pulling towards worse solutions or that the exploited solutions' success is masking its negative impact on the population.",
      "action": "Review exploit strategy and potentially adjust how similar the exploited solutions are. Consider reducing the number of exploitation iterations to decrease the negative impact."
    },
    "overall": {
      "performance": "Negative, strategies are not effectively improving solutions.",
      "summary": "Both exploration and exploitation have significant issues.  Exploitation is degrading solutions, and Exploration is not yielding a high enough success rate to counter it."
    }
  },
  "balance_state": {
    "assessment": "The current balance is skewed towards ineffective exploitation and weak exploration.  The increase in cost and decrease in elite solutions highlight this imbalance.",
    "adjustment_needs": "Focus should be on increasing the success rate of exploration and refining the exploitation to not regress to poorer solutions. Potentially needs a stronger focus on exploring new spaces.",
    "priority": "Increase exploration effectiveness and then reign in exploitation to avoid dragging the population down"
  },
  "recommendations": [
    {
      "action": "Increase exploration efforts.",
      "details": "Increase the number of individuals explored in the next iteration. Explore more diverse areas of the solution space, perhaps through increased mutation rate for exploration."
    },
    {
      "action": "Refine exploit strategy to avoid regression",
      "details": "Thoroughly examine the exploited solutions. Identify common characteristics that lead to reduced performance.  Reduce the number of exploited individuals in the following iteration."
    },
    {
      "action": "Monitor Diversity & Elite Solutions closely",
      "details": "Pay close attention to the diversity metric. Make sure the exploration strategy is actually providing more diversity or it might be stagnating.  Monitor the number of elite solutions, as this is trending down and this should be investigated."
    },
    {
        "action": "Check mutation rate.",
        "details": "Evaluate how drastic the mutations are to determine whether the solutions are being altered too much or not enough"
    }
  ]
}
```
2025-06-22 18:36:06,743 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:36:06,743 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration, Potential for Improvement",
    "features": "Cost is trending upwards, indicating potential degradation. Diversity is increasing slightly, which is positive. Number of elite solutions has decreased, which is concerning.  Overall improvement is negative, suggesting the current strategy is not yielding positive results.",
    "indicators": {
      "cost_trend": "Increasing (detrimental)",
      "diversity_trend": "Slightly Increasing (favorable)",
      "elite_count_trend": "Decreasing (detrimental)",
      "overall_improvement": "Negative (detrimental)"
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Low (20%)",
      "avg_improvement": "Positive (8803.2)",
      "evaluation": "Explore is yielding some positive improvements, but with a very low success rate. This suggests that while the individuals explored have potential, the selection/mutation is not optimized or exploring too broadly.",
      "action": "Refine explore strategy, possibly by adjusting mutation rates or focusing on more promising regions"
    },
    "exploit": {
      "success_rate": "Moderate (40%)",
      "avg_improvement": "Negative (-16475.2)",
      "evaluation": "Exploit is yielding negative average improvements despite a moderate success rate. This indicates the exploitation is pulling towards worse solutions or that the exploited solutions' success is masking its negative impact on the population.",
      "action": "Review exploit strategy and potentially adjust how similar the exploited solutions are. Consider reducing the number of exploitation iterations to decrease the negative impact."
    },
    "overall": {
      "performance": "Negative, strategies are not effectively improving solutions.",
      "summary": "Both exploration and exploitation have significant issues.  Exploitation is degrading solutions, and Exploration is not yielding a high enough success rate to counter it."
    }
  },
  "balance_state": {
    "assessment": "The current balance is skewed towards ineffective exploitation and weak exploration.  The increase in cost and decrease in elite solutions highlight this imbalance.",
    "adjustment_needs": "Focus should be on increasing the success rate of exploration and refining the exploitation to not regress to poorer solutions. Potentially needs a stronger focus on exploring new spaces.",
    "priority": "Increase exploration effectiveness and then reign in exploitation to avoid dragging the population down"
  },
  "recommendations": [
    {
      "action": "Increase exploration efforts.",
      "details": "Increase the number of individuals explored in the next iteration. Explore more diverse areas of the solution space, perhaps through increased mutation rate for exploration."
    },
    {
      "action": "Refine exploit strategy to avoid regression",
      "details": "Thoroughly examine the exploited solutions. Identify common characteristics that lead to reduced performance.  Reduce the number of exploited individuals in the following iteration."
    },
    {
      "action": "Monitor Diversity & Elite Solutions closely",
      "details": "Pay close attention to the diversity metric. Make sure the exploration strategy is actually providing more diversity or it might be stagnating.  Monitor the number of elite solutions, as this is trending down and this should be investigated."
    },
    {
        "action": "Check mutation rate.",
        "details": "Evaluate how drastic the mutations are to determine whether the solutions are being altered too much or not enough"
    }
  ]
}
```
2025-06-22 18:36:06,744 - __main__ - INFO - 评估阶段完成
2025-06-22 18:36:06,744 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration, Potential for Improvement",
    "features": "Cost is trending upwards, indicating potential degradation. Diversity is increasing slightly, which is positive. Number of elite solutions has decreased, which is concerning.  Overall improvement is negative, suggesting the current strategy is not yielding positive results.",
    "indicators": {
      "cost_trend": "Increasing (detrimental)",
      "diversity_trend": "Slightly Increasing (favorable)",
      "elite_count_trend": "Decreasing (detrimental)",
      "overall_improvement": "Negative (detrimental)"
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Low (20%)",
      "avg_improvement": "Positive (8803.2)",
      "evaluation": "Explore is yielding some positive improvements, but with a very low success rate. This suggests that while the individuals explored have potential, the selection/mutation is not optimized or exploring too broadly.",
      "action": "Refine explore strategy, possibly by adjusting mutation rates or focusing on more promising regions"
    },
    "exploit": {
      "success_rate": "Moderate (40%)",
      "avg_improvement": "Negative (-16475.2)",
      "evaluation": "Exploit is yielding negative average improvements despite a moderate success rate. This indicates the exploitation is pulling towards worse solutions or that the exploited solutions' success is masking its negative impact on the population.",
      "action": "Review exploit strategy and potentially adjust how similar the exploited solutions are. Consider reducing the number of exploitation iterations to decrease the negative impact."
    },
    "overall": {
      "performance": "Negative, strategies are not effectively improving solutions.",
      "summary": "Both exploration and exploitation have significant issues.  Exploitation is degrading solutions, and Exploration is not yielding a high enough success rate to counter it."
    }
  },
  "balance_state": {
    "assessment": "The current balance is skewed towards ineffective exploitation and weak exploration.  The increase in cost and decrease in elite solutions highlight this imbalance.",
    "adjustment_needs": "Focus should be on increasing the success rate of exploration and refining the exploitation to not regress to poorer solutions. Potentially needs a stronger focus on exploring new spaces.",
    "priority": "Increase exploration effectiveness and then reign in exploitation to avoid dragging the population down"
  },
  "recommendations": [
    {
      "action": "Increase exploration efforts.",
      "details": "Increase the number of individuals explored in the next iteration. Explore more diverse areas of the solution space, perhaps through increased mutation rate for exploration."
    },
    {
      "action": "Refine exploit strategy to avoid regression",
      "details": "Thoroughly examine the exploited solutions. Identify common characteristics that lead to reduced performance.  Reduce the number of exploited individuals in the following iteration."
    },
    {
      "action": "Monitor Diversity & Elite Solutions closely",
      "details": "Pay close attention to the diversity metric. Make sure the exploration strategy is actually providing more diversity or it might be stagnating.  Monitor the number of elite solutions, as this is trending down and this should be investigated."
    },
    {
        "action": "Check mutation rate.",
        "details": "Evaluate how drastic the mutations are to determine whether the solutions are being altered too much or not enough"
    }
  ]
}
```
2025-06-22 18:36:06,745 - __main__ - INFO - 当前最佳适应度: 13176.0
2025-06-22 18:36:06,746 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_2.pkl
2025-06-22 18:36:06,746 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-06-22 18:36:06,747 - __main__ - INFO - 开始分析阶段
2025-06-22 18:36:06,747 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:36:06,764 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 13176.0, 'max': 119362.0, 'mean': 82933.5, 'std': 40344.25441435249}, 'diversity': 0.9683501683501683, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:36:06,765 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 13176.0, 'max': 119362.0, 'mean': 82933.5, 'std': 40344.25441435249}, 'diversity_level': 0.9683501683501683, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:36:06,765 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:36:06,765 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:36:06,765 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:36:06,767 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 18:36:06,767 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:36:06,767 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 18:36:06,768 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:36:06,768 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:36:06,768 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:36:06,778 - EliteExpert - INFO - 精英解分析完成
2025-06-22 18:36:06,779 - __main__ - INFO - 精英专家分析报告: {'elite_count': 25, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 3655.0, 'avg_gap': 69736.38}, 'structure_gap': {'unique_elite_edges': 217, 'unique_pop_edges': 546, 'common_edges': 57}}, 'elite_diversity': {'diversity_score': 0.28035353535353535}}
2025-06-22 18:36:06,779 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:36:06,779 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:36:06,779 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:36:06,779 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=13176.0, max=119362.0, mean=82933.5, std=40344.25441435249
- Diversity: 0.9683501683501683, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Evolution Context:
- Iteration: 3/5
- Historical trend: Recent costs: [11493.0, 11458.0, 13176.0]
- Improvement rate: 17.50 per iteration
- Stagnation periods: None detected

## Problem Characteristics:
- TSP instance: Unknown with 0 nodes
- Search space topology: Likely rugged with many local optima
- Edge distribution: Unknown
- Known difficult regions: 0 identified

## Key Relationships:
- Elite solutions share 0% of edges
- Population clustering: No clustering information
- Exploration vs exploitation balance: High exploration (>70%)
- Local optima density: Low

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality, deceptiveness)
2. Population state (diversity, convergence, clustering)
3. Difficult regions and specific challenges (edge crossings, isolated nodes)
4. Opportunity regions with potential for improvement
5. Current evolution phase (exploration, exploitation, refinement)
6. Recommended evolution direction with specific operator suggestions

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics including ruggedness, modality, and deceptiveness
- population_state: Current state assessment including diversity, convergence, and clustering
- difficult_regions: Identified challenges with specific node sequences or edges to avoid
- opportunity_regions: Promising areas with specific node sequences or edges to include
- evolution_phase: Current phase identification (exploration/exploitation/refinement)
- evolution_direction: Recommended strategy with specific operator suggestions

2025-06-22 18:36:12,662 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely high, given the cost statistics (high standard deviation and range) and lack of convergence. The absence of any structure (high quality edges, common subpaths) also points to a less smooth landscape.",
    "modality": "Potentially multimodal, given the ruggedness and the fact that the population contains solutions with varying costs. The presence of local optima is probable.",
    "deceptiveness": "Unknown, but the rapid improvement in the recent costs suggests the search is finding better solutions, indicating possible areas with potentially deceptive features or trap states, though this isn't definitively confirmed without further information."
  },
  "population_state": {
    "diversity": "High (0.968), indicating a good spread of solutions and exploration of the search space. This is consistent with the early stages of the evolution.",
    "convergence": "Zero (0.0), further supporting the early stage of the evolution. The lack of convergence is expected at this point and doesn't indicate a problem in this context.",
    "clustering": "No clustering information available. However, with the high diversity and lack of elite solutions, strong clustering is unlikely. Further investigation would be needed with more complete data."
  },
  "difficult_regions": {
    "identified_challenges": "0 identified, since the structure analysis shows no high quality edges or common subpaths. Inferred: The unknown TSP instance and the lack of identified common edges suggests the initial iterations are unlikely to have discovered difficult edges or node sequences."
  },
  "opportunity_regions": {
    "promising_areas": "The dramatic cost reductions in the recent iterations are encouraging.  The region of best observed cost (around 11458) is the current area to focus exploration, so the search should aim to maintain exploration in this region. Identifying commonalities, however minimal, among improved solutions could indicate an opportunity region. No specific node sequences or edges can be identified, due to the nature of the unknown TSP instance and the lack of common structure or node data, but continue to observe for these, for further iterations.",
    "further_exploration": "Continue to maintain high diversity through mutation and crossover operators to find solutions with even lower costs."
  },
  "evolution_phase": {
    "phase": "Exploration",
    "justification": "The high diversity, lack of convergence, and early iteration count strongly suggest that the evolution is primarily in an exploration phase.  The improvement rate also suggests a profitable phase in the early iterations, and the aim is to avoid premature convergence."
  },
  "evolution_direction": {
    "strategy": "Maintain high exploration while trying to exploit the promising region of recent cost reductions. This allows for continued discovery of better solutions.  Increase diversity, and investigate commonalities of new solutions.",
    "operator_suggestions": [
      "Apply a high mutation rate to maintain diversity and avoid premature convergence, such as random swaps or node insertion/removal.",
      "Use a crossover operator to combine promising solutions and create new ones. Maintain diversity by using a variety of crossover types, ensuring that a high number of parents are selected, or increasing the crossover probability.",
      "Consider a form of elitism to keep the best performing solutions.",
      "Avoid a high degree of selection pressure that could lead to premature convergence; consider roulette wheel selection, tournament selection, or similar. Keep the current selection scheme."
    ]
  }
}
```
2025-06-22 18:36:12,663 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:36:12,663 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Likely high, given the cost statistics (high standard deviation and range) and lack of convergence. The absence of any structure (high quality edges, common subpaths) also points to a less smooth landscape.', 'modality': 'Potentially multimodal, given the ruggedness and the fact that the population contains solutions with varying costs. The presence of local optima is probable.', 'deceptiveness': "Unknown, but the rapid improvement in the recent costs suggests the search is finding better solutions, indicating possible areas with potentially deceptive features or trap states, though this isn't definitively confirmed without further information."}, 'population_state': {'diversity': 'High (0.968), indicating a good spread of solutions and exploration of the search space. This is consistent with the early stages of the evolution.', 'convergence': "Zero (0.0), further supporting the early stage of the evolution. The lack of convergence is expected at this point and doesn't indicate a problem in this context.", 'clustering': 'No clustering information available. However, with the high diversity and lack of elite solutions, strong clustering is unlikely. Further investigation would be needed with more complete data.'}, 'difficult_regions': {'identified_challenges': '0 identified, since the structure analysis shows no high quality edges or common subpaths. Inferred: The unknown TSP instance and the lack of identified common edges suggests the initial iterations are unlikely to have discovered difficult edges or node sequences.'}, 'opportunity_regions': {'promising_areas': 'The dramatic cost reductions in the recent iterations are encouraging.  The region of best observed cost (around 11458) is the current area to focus exploration, so the search should aim to maintain exploration in this region. Identifying commonalities, however minimal, among improved solutions could indicate an opportunity region. No specific node sequences or edges can be identified, due to the nature of the unknown TSP instance and the lack of common structure or node data, but continue to observe for these, for further iterations.', 'further_exploration': 'Continue to maintain high diversity through mutation and crossover operators to find solutions with even lower costs.'}, 'evolution_phase': {'phase': 'Exploration', 'justification': 'The high diversity, lack of convergence, and early iteration count strongly suggest that the evolution is primarily in an exploration phase.  The improvement rate also suggests a profitable phase in the early iterations, and the aim is to avoid premature convergence.'}, 'evolution_direction': {'strategy': 'Maintain high exploration while trying to exploit the promising region of recent cost reductions. This allows for continued discovery of better solutions.  Increase diversity, and investigate commonalities of new solutions.', 'operator_suggestions': ['Apply a high mutation rate to maintain diversity and avoid premature convergence, such as random swaps or node insertion/removal.', 'Use a crossover operator to combine promising solutions and create new ones. Maintain diversity by using a variety of crossover types, ensuring that a high number of parents are selected, or increasing the crossover probability.', 'Consider a form of elitism to keep the best performing solutions.', 'Avoid a high degree of selection pressure that could lead to premature convergence; consider roulette wheel selection, tournament selection, or similar. Keep the current selection scheme.']}}
2025-06-22 18:36:12,664 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:36:12,664 - __main__ - INFO - 分析阶段完成
2025-06-22 18:36:12,664 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Likely high, given the cost statistics (high standard deviation and range) and lack of convergence. The absence of any structure (high quality edges, common subpaths) also points to a less smooth landscape.', 'modality': 'Potentially multimodal, given the ruggedness and the fact that the population contains solutions with varying costs. The presence of local optima is probable.', 'deceptiveness': "Unknown, but the rapid improvement in the recent costs suggests the search is finding better solutions, indicating possible areas with potentially deceptive features or trap states, though this isn't definitively confirmed without further information."}, 'population_state': {'diversity': 'High (0.968), indicating a good spread of solutions and exploration of the search space. This is consistent with the early stages of the evolution.', 'convergence': "Zero (0.0), further supporting the early stage of the evolution. The lack of convergence is expected at this point and doesn't indicate a problem in this context.", 'clustering': 'No clustering information available. However, with the high diversity and lack of elite solutions, strong clustering is unlikely. Further investigation would be needed with more complete data.'}, 'difficult_regions': {'identified_challenges': '0 identified, since the structure analysis shows no high quality edges or common subpaths. Inferred: The unknown TSP instance and the lack of identified common edges suggests the initial iterations are unlikely to have discovered difficult edges or node sequences.'}, 'opportunity_regions': {'promising_areas': 'The dramatic cost reductions in the recent iterations are encouraging.  The region of best observed cost (around 11458) is the current area to focus exploration, so the search should aim to maintain exploration in this region. Identifying commonalities, however minimal, among improved solutions could indicate an opportunity region. No specific node sequences or edges can be identified, due to the nature of the unknown TSP instance and the lack of common structure or node data, but continue to observe for these, for further iterations.', 'further_exploration': 'Continue to maintain high diversity through mutation and crossover operators to find solutions with even lower costs.'}, 'evolution_phase': {'phase': 'Exploration', 'justification': 'The high diversity, lack of convergence, and early iteration count strongly suggest that the evolution is primarily in an exploration phase.  The improvement rate also suggests a profitable phase in the early iterations, and the aim is to avoid premature convergence.'}, 'evolution_direction': {'strategy': 'Maintain high exploration while trying to exploit the promising region of recent cost reductions. This allows for continued discovery of better solutions.  Increase diversity, and investigate commonalities of new solutions.', 'operator_suggestions': ['Apply a high mutation rate to maintain diversity and avoid premature convergence, such as random swaps or node insertion/removal.', 'Use a crossover operator to combine promising solutions and create new ones. Maintain diversity by using a variety of crossover types, ensuring that a high number of parents are selected, or increasing the crossover probability.', 'Consider a form of elitism to keep the best performing solutions.', 'Avoid a high degree of selection pressure that could lead to premature convergence; consider roulette wheel selection, tournament selection, or similar. Keep the current selection scheme.']}}
2025-06-22 18:36:12,664 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:36:12,664 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:36:12,664 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 3
- Population: 10 individuals, diversity=High (0.968), indicating a good spread of solutions and exploration of the search space. This is consistent with the early stages of the evolution.
- Best cost: 13176.0, Mean cost: 82933.5
- Search space: Ruggedness: Likely high, given the cost statistics (high standard deviation and range) and lack of convergence. The absence of any structure (high quality edges, common subpaths) also points to a less smooth landscape., Modality: Potentially multimodal, given the ruggedness and the fact that the population contains solutions with varying costs. The presence of local optima is probable.
- Difficult regions: 1 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early Iteration, Potential for Improvement",
    "features": "Cost is trending upwards, indicating potential degradation. Diversity is increasing slightl...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:36:12,664 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:36:15,079 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "crossover_type": "uniform",
      "perturbation_strength": 0.2,
      "local_search_depth": 1
    },
    "exploit": {
      "mutation_rate": 0.05,
      "crossover_type": "single_point",
      "perturbation_strength": 0.05,
      "local_search_depth": 3
    }
  }
}
```

2025-06-22 18:36:15,079 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-06-22 18:36:15,079 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-06-22 18:36:15,079 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "crossover_type": "uniform",
      "perturbation_strength": 0.2,
      "local_search_depth": 1
    },
    "exploit": {
      "mutation_rate": 0.05,
      "crossover_type": "single_point",
      "perturbation_strength": 0.05,
      "local_search_depth": 3
    }
  }
}
```

2025-06-22 18:36:15,079 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:36:15,079 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit']
2025-06-22 18:36:15,079 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "crossover_type": "uniform",
      "perturbation_strength": 0.2,
      "local_search_depth": 1
    },
    "exploit": {
      "mutation_rate": 0.05,
      "crossover_type": "single_point",
      "perturbation_strength": 0.05,
      "local_search_depth": 3
    }
  }
}
```

2025-06-22 18:36:15,079 - __main__ - INFO - 开始进化阶段
2025-06-22 18:36:15,079 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:36:15,079 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:36:15,079 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 3, 6] and ending with [8, 5, 2]
- Cost: 25977.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8723076923076923
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:36:15,079 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:36:18,442 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 58, 57, 52, 47, 42, 37, 32, 27, 22, 17, 12, 7, 2, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53
  ],
  "modification_strategy": "This strategy prioritizes exploring the unexplored regions and promoting diversity. It starts by creating a path that quickly covers the entire range of nodes, focusing on a systematic traversal of the nodes. Then, it attempts to weave in the start nodes more strategically, alternating between forward and backward traversals across the main path to increase diversity while still maintaining overall coverage. Finally, it attempts to incorporate other regions to improve the overall coverage and diversity and avoid difficult areas to avoid.",
  "targeted_regions": "Focused on broadly covering all nodes and strategically incorporating regions indicated as 'opportunity regions' and avoiding the 'difficult regions' as mentioned in the context (implicitly represented by path structure)."
}
```
2025-06-22 18:36:18,442 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:36:18,442 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40401.0, 路径: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 58, 57, 52, 47, 42, 37, 32, 27, 22, 17, 12, 7, 2, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53]
2025-06-22 18:36:18,442 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 58, 57, 52, 47, 42, 37, 32, 27, 22, 17, 12, 7, 2, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53], 'cur_cost': 40401.0}
2025-06-22 18:36:18,442 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 18:36:18,442 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:36:18,442 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [41, 58, 37] and ending with [33, 1, 45]
- Cost: 108539.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.86
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:36:18,442 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:36:22,354 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    41, 58, 37, 12, 4, 22, 55, 17, 6, 31, 63, 2, 28, 48, 3, 59, 19, 52, 25, 61, 44, 14, 36, 0, 20, 43, 26, 5, 33, 65, 10, 50, 39, 15, 56, 34, 9, 30, 64, 16, 51, 23, 49, 29, 18, 24, 46, 62, 38, 53, 42, 57, 45, 8, 1, 11, 35, 47, 27, 60, 32, 7, 13, 54, 21, 40
  ],
  "modification_strategy": "Utilized a 'regional exploration with diversification' approach. Started with a few initial nodes from the provided path. Then, it prioritized visiting nodes that are far away from already visited nodes to increase diversity, while ensuring even exploration throughout the whole graph, based on identified opportunity regions and trying to avoid difficult ones. Used a random permutation of the non-visited nodes.",
  "targeted_regions": "Opportunity regions (unspecified in prompt) and also aimed to explore the entire search space evenly, avoiding difficult regions (unspecified in prompt)."
}
```
2025-06-22 18:36:22,354 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:36:22,354 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126833.0, 路径: [41, 58, 37, 12, 4, 22, 55, 17, 6, 31, 63, 2, 28, 48, 3, 59, 19, 52, 25, 61, 44, 14, 36, 0, 20, 43, 26, 5, 33, 65, 10, 50, 39, 15, 56, 34, 9, 30, 64, 16, 51, 23, 49, 29, 18, 24, 46, 62, 38, 53, 42, 57, 45, 8, 1, 11, 35, 47, 27, 60, 32, 7, 13, 54, 21, 40]
2025-06-22 18:36:22,354 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [41, 58, 37, 12, 4, 22, 55, 17, 6, 31, 63, 2, 28, 48, 3, 59, 19, 52, 25, 61, 44, 14, 36, 0, 20, 43, 26, 5, 33, 65, 10, 50, 39, 15, 56, 34, 9, 30, 64, 16, 51, 23, 49, 29, 18, 24, 46, 62, 38, 53, 42, 57, 45, 8, 1, 11, 35, 47, 27, 60, 32, 7, 13, 54, 21, 40], 'cur_cost': 126833.0}
2025-06-22 18:36:22,354 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 18:36:22,354 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:36:22,354 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [12, 23, 22] and ending with [63, 64, 65]
- Cost: 13176.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.86
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:36:22,354 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:36:25,579 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11
  ],
  "modification_strategy": "This strategy prioritizes a significant departure from the original path by rearranging a large contiguous segment and integrating nodes from unexplored regions (0-11). The path starts with the same initial nodes but deviates early to explore a separate section before traversing the nodes at the end. It aims to balance the exploration of unexplored areas and exploit existing path knowledge for improvement by connecting to the known good final part of the initial path.",
  "targeted_regions": "The primary target is to explore unexplored region 0-11 by linking it with the final segment."
}
```
2025-06-22 18:36:25,579 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:36:25,579 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11461.0, 路径: [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-06-22 18:36:25,581 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}
2025-06-22 18:36:25,581 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 18:36:25,581 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:36:25,581 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [42, 22, 24] and ending with [58, 38, 20]
- Cost: 98576.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.86
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:36:25,581 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:36:28,707 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    42, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43,
    44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63,
    64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
    20, 21, 23, 24
  ],
  "modification_strategy": "This strategy prioritizes exploring unexplored regions and maximizing diversity by significantly changing the order of traversal. It starts with the initial portion of the original path and then strategically reorganizes the remaining nodes. The strategy also explores a greater variety of nodes, potentially passing through the opportunity regions by exploring nodes that are likely to be in these regions.",
  "targeted_regions": "The strategy aims to explore the opportunity regions by making sure the path traverses new segments of the graph that are yet to be explored by the current path. The initial and terminal nodes are preserved to satisfy the initial constraints."
}
```
2025-06-22 18:36:28,707 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:36:28,708 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16122.0, 路径: [42, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24]
2025-06-22 18:36:28,708 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [42, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24], 'cur_cost': 16122.0}
2025-06-22 18:36:28,708 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-22 18:36:28,708 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:36:28,708 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:36:28,709 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 99764.0
2025-06-22 18:36:29,212 - ExploitationExpert - INFO - res_population_num: 30
2025-06-22 18:36:29,212 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:36:29,212 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-22 18:36:29,225 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:36:29,225 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 58, 57, 52, 47, 42, 37, 32, 27, 22, 17, 12, 7, 2, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53], 'cur_cost': 40401.0}, {'tour': [41, 58, 37, 12, 4, 22, 55, 17, 6, 31, 63, 2, 28, 48, 3, 59, 19, 52, 25, 61, 44, 14, 36, 0, 20, 43, 26, 5, 33, 65, 10, 50, 39, 15, 56, 34, 9, 30, 64, 16, 51, 23, 49, 29, 18, 24, 46, 62, 38, 53, 42, 57, 45, 8, 1, 11, 35, 47, 27, 60, 32, 7, 13, 54, 21, 40], 'cur_cost': 126833.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': [42, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24], 'cur_cost': 16122.0}, {'tour': array([34, 27, 46, 15, 50, 47, 64, 18, 32, 19, 63, 24, 49, 13, 54,  9,  3,
       55, 58, 37, 53, 41, 40,  8, 61, 44, 22, 23,  6, 52, 26, 42, 12, 39,
       31, 29, 28, 10, 57,  1,  2, 30, 51, 35, 43, 21, 59, 65,  4, 14, 33,
       36, 56, 48, 20, 38,  5, 16, 60,  7, 62,  0, 25, 11, 45, 17]), 'cur_cost': 99764.0}, {'tour': array([22, 45, 50, 55, 32, 15, 60, 29, 58, 30, 40, 57, 44, 33, 19, 62, 20,
       51,  1, 26, 42,  8, 43, 64, 25, 39, 13, 23,  0, 24,  2, 17, 48, 53,
       10, 37, 34, 41, 59,  9, 28, 52, 65, 35, 36, 61,  4,  5, 46, 27, 11,
       21,  3,  6, 18, 49, 63, 38, 54,  7, 47, 31, 12, 14, 56, 16]), 'cur_cost': 119362.0}, {'tour': [17, 43, 23, 27, 33, 1, 60, 5, 10, 38, 48, 15, 52, 28, 4, 58, 63, 36, 12, 25, 50, 65, 3, 31, 45, 2, 56, 49, 54, 41, 8, 20, 61, 32, 13, 47, 18, 53, 30, 64, 9, 35, 19, 40, 24, 62, 39, 57, 7, 11, 37, 44, 21, 59, 14, 29, 46, 16, 6, 51, 26, 0, 34, 42, 22, 55], 'cur_cost': 116931.0}, {'tour': array([ 4, 46,  8, 10, 32, 52, 58, 36, 15, 54, 59, 60, 20, 16, 28, 44, 21,
       41, 49, 48, 26, 45, 19, 25, 51, 11, 17, 12, 53, 57, 63, 50, 35,  2,
       37, 43, 47, 56, 55, 22, 13, 34, 30, 23,  7, 65, 31, 38, 39, 33, 61,
       64,  6, 27,  9, 42, 18, 62, 14, 40,  1,  3, 24,  0, 29,  5]), 'cur_cost': 94674.0}, {'tour': [43, 47, 38, 60, 2, 50, 15, 55, 44, 31, 18, 40, 5, 23, 65, 10, 35, 1, 63, 13, 39, 21, 54, 26, 48, 34, 17, 64, 36, 9, 29, 46, 59, 7, 4, 56, 25, 32, 62, 11, 57, 27, 33, 16, 61, 42, 8, 53, 37, 19, 51, 30, 45, 14, 28, 22, 49, 20, 3, 52, 0, 12, 24, 41, 6, 58], 'cur_cost': 111265.0}, {'tour': array([62, 19, 12, 63, 26, 45, 36, 56,  2, 15, 57, 23, 27,  7, 24, 53, 43,
        0, 47, 22, 14, 10, 33,  8, 61, 65, 42, 46, 18, 34, 28, 20, 29, 49,
       17,  1, 25,  6, 52,  5, 16, 50, 21, 58, 54, 30,  3, 48, 40, 64, 38,
       31, 51, 13, 35, 32,  4, 59, 39,  9, 55, 44, 60, 11, 41, 37]), 'cur_cost': 112426.0}]
2025-06-22 18:36:29,225 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:36:29,225 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-06-22 18:36:29,225 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-22 18:36:29,229 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-22 18:36:29,229 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:36:29,230 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [22, 45, 50] and ending with [14, 56, 16]
- Cost: 119362.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8184615384615385
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:36:29,230 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:36:32,550 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    22, 45, 50, 2, 64, 33, 41, 1, 28, 54, 65, 38, 4, 27, 40, 57, 11, 10, 60, 39, 35, 3, 48, 30, 19, 13, 51, 55, 24, 62, 17, 46, 9, 43, 15, 8, 26, 6, 36, 42, 23, 21, 49, 32, 31, 59, 12, 61, 52, 5, 29, 37, 53, 20, 47, 63, 18, 14, 56, 16, 0, 44, 7, 25, 58, 34
  ],
  "modification_strategy": "I focused on a path that strongly diverges from the original. I prioritized exploring the 'Unexplored regions' identified in the search context by including nodes from those regions early and frequently in the path.  I also used a random reordering of nodes while respecting start and end points to add diversity. The inclusion of nodes clustered in regions known to be difficult to avoid was handled via randomness",
  "targeted_regions": "Unexplored regions and regions identified as containing difficult-to-avoid areas. Attempted to balance exploration with preservation of the path characteristics."
}
```
2025-06-22 18:36:32,550 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:36:32,552 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118913.0, 路径: [22, 45, 50, 2, 64, 33, 41, 1, 28, 54, 65, 38, 4, 27, 40, 57, 11, 10, 60, 39, 35, 3, 48, 30, 19, 13, 51, 55, 24, 62, 17, 46, 9, 43, 15, 8, 26, 6, 36, 42, 23, 21, 49, 32, 31, 59, 12, 61, 52, 5, 29, 37, 53, 20, 47, 63, 18, 14, 56, 16, 0, 44, 7, 25, 58, 34]
2025-06-22 18:36:32,552 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [22, 45, 50, 2, 64, 33, 41, 1, 28, 54, 65, 38, 4, 27, 40, 57, 11, 10, 60, 39, 35, 3, 48, 30, 19, 13, 51, 55, 24, 62, 17, 46, 9, 43, 15, 8, 26, 6, 36, 42, 23, 21, 49, 32, 31, 59, 12, 61, 52, 5, 29, 37, 53, 20, 47, 63, 18, 14, 56, 16, 0, 44, 7, 25, 58, 34], 'cur_cost': 118913.0}
2025-06-22 18:36:32,552 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 18:36:32,554 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:36:32,554 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:36:32,554 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 97165.0
2025-06-22 18:36:33,056 - ExploitationExpert - INFO - res_population_num: 31
2025-06-22 18:36:33,056 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:36:33,057 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:36:33,071 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:36:33,071 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 58, 57, 52, 47, 42, 37, 32, 27, 22, 17, 12, 7, 2, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53], 'cur_cost': 40401.0}, {'tour': [41, 58, 37, 12, 4, 22, 55, 17, 6, 31, 63, 2, 28, 48, 3, 59, 19, 52, 25, 61, 44, 14, 36, 0, 20, 43, 26, 5, 33, 65, 10, 50, 39, 15, 56, 34, 9, 30, 64, 16, 51, 23, 49, 29, 18, 24, 46, 62, 38, 53, 42, 57, 45, 8, 1, 11, 35, 47, 27, 60, 32, 7, 13, 54, 21, 40], 'cur_cost': 126833.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': [42, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24], 'cur_cost': 16122.0}, {'tour': array([34, 27, 46, 15, 50, 47, 64, 18, 32, 19, 63, 24, 49, 13, 54,  9,  3,
       55, 58, 37, 53, 41, 40,  8, 61, 44, 22, 23,  6, 52, 26, 42, 12, 39,
       31, 29, 28, 10, 57,  1,  2, 30, 51, 35, 43, 21, 59, 65,  4, 14, 33,
       36, 56, 48, 20, 38,  5, 16, 60,  7, 62,  0, 25, 11, 45, 17]), 'cur_cost': 99764.0}, {'tour': [22, 45, 50, 2, 64, 33, 41, 1, 28, 54, 65, 38, 4, 27, 40, 57, 11, 10, 60, 39, 35, 3, 48, 30, 19, 13, 51, 55, 24, 62, 17, 46, 9, 43, 15, 8, 26, 6, 36, 42, 23, 21, 49, 32, 31, 59, 12, 61, 52, 5, 29, 37, 53, 20, 47, 63, 18, 14, 56, 16, 0, 44, 7, 25, 58, 34], 'cur_cost': 118913.0}, {'tour': array([48, 45, 43,  2, 21, 32, 64, 42, 62, 63, 58, 31, 10, 65, 25, 49,  0,
        8,  3, 53, 17, 59, 57, 14, 20, 35, 24,  7, 15, 61, 40, 56, 46, 55,
       33, 23, 22, 13, 37, 30, 39, 11, 34, 29, 60, 18, 44, 41,  6,  1, 28,
        4,  9, 51, 50, 19, 38, 26, 54,  5, 16, 12, 52, 36, 27, 47]), 'cur_cost': 97165.0}, {'tour': array([ 4, 46,  8, 10, 32, 52, 58, 36, 15, 54, 59, 60, 20, 16, 28, 44, 21,
       41, 49, 48, 26, 45, 19, 25, 51, 11, 17, 12, 53, 57, 63, 50, 35,  2,
       37, 43, 47, 56, 55, 22, 13, 34, 30, 23,  7, 65, 31, 38, 39, 33, 61,
       64,  6, 27,  9, 42, 18, 62, 14, 40,  1,  3, 24,  0, 29,  5]), 'cur_cost': 94674.0}, {'tour': [43, 47, 38, 60, 2, 50, 15, 55, 44, 31, 18, 40, 5, 23, 65, 10, 35, 1, 63, 13, 39, 21, 54, 26, 48, 34, 17, 64, 36, 9, 29, 46, 59, 7, 4, 56, 25, 32, 62, 11, 57, 27, 33, 16, 61, 42, 8, 53, 37, 19, 51, 30, 45, 14, 28, 22, 49, 20, 3, 52, 0, 12, 24, 41, 6, 58], 'cur_cost': 111265.0}, {'tour': array([62, 19, 12, 63, 26, 45, 36, 56,  2, 15, 57, 23, 27,  7, 24, 53, 43,
        0, 47, 22, 14, 10, 33,  8, 61, 65, 42, 46, 18, 34, 28, 20, 29, 49,
       17,  1, 25,  6, 52,  5, 16, 50, 21, 58, 54, 30,  3, 48, 40, 64, 38,
       31, 51, 13, 35, 32,  4, 59, 39,  9, 55, 44, 60, 11, 41, 37]), 'cur_cost': 112426.0}]
2025-06-22 18:36:33,073 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:36:33,073 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-06-22 18:36:33,073 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 18:36:33,074 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 18:36:33,074 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:36:33,074 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:36:33,074 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 99365.0
2025-06-22 18:36:33,577 - ExploitationExpert - INFO - res_population_num: 31
2025-06-22 18:36:33,577 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:36:33,577 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:36:33,591 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:36:33,591 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 58, 57, 52, 47, 42, 37, 32, 27, 22, 17, 12, 7, 2, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53], 'cur_cost': 40401.0}, {'tour': [41, 58, 37, 12, 4, 22, 55, 17, 6, 31, 63, 2, 28, 48, 3, 59, 19, 52, 25, 61, 44, 14, 36, 0, 20, 43, 26, 5, 33, 65, 10, 50, 39, 15, 56, 34, 9, 30, 64, 16, 51, 23, 49, 29, 18, 24, 46, 62, 38, 53, 42, 57, 45, 8, 1, 11, 35, 47, 27, 60, 32, 7, 13, 54, 21, 40], 'cur_cost': 126833.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': [42, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24], 'cur_cost': 16122.0}, {'tour': array([34, 27, 46, 15, 50, 47, 64, 18, 32, 19, 63, 24, 49, 13, 54,  9,  3,
       55, 58, 37, 53, 41, 40,  8, 61, 44, 22, 23,  6, 52, 26, 42, 12, 39,
       31, 29, 28, 10, 57,  1,  2, 30, 51, 35, 43, 21, 59, 65,  4, 14, 33,
       36, 56, 48, 20, 38,  5, 16, 60,  7, 62,  0, 25, 11, 45, 17]), 'cur_cost': 99764.0}, {'tour': [22, 45, 50, 2, 64, 33, 41, 1, 28, 54, 65, 38, 4, 27, 40, 57, 11, 10, 60, 39, 35, 3, 48, 30, 19, 13, 51, 55, 24, 62, 17, 46, 9, 43, 15, 8, 26, 6, 36, 42, 23, 21, 49, 32, 31, 59, 12, 61, 52, 5, 29, 37, 53, 20, 47, 63, 18, 14, 56, 16, 0, 44, 7, 25, 58, 34], 'cur_cost': 118913.0}, {'tour': array([48, 45, 43,  2, 21, 32, 64, 42, 62, 63, 58, 31, 10, 65, 25, 49,  0,
        8,  3, 53, 17, 59, 57, 14, 20, 35, 24,  7, 15, 61, 40, 56, 46, 55,
       33, 23, 22, 13, 37, 30, 39, 11, 34, 29, 60, 18, 44, 41,  6,  1, 28,
        4,  9, 51, 50, 19, 38, 26, 54,  5, 16, 12, 52, 36, 27, 47]), 'cur_cost': 97165.0}, {'tour': array([64, 50,  7, 14, 43, 39, 55, 42, 37, 59, 35, 62, 51, 58, 18, 48, 23,
       20, 34, 57, 56, 12, 38, 19,  6, 65,  9, 63,  1, 31, 28, 60, 61, 54,
       44, 25, 26, 47, 27, 29, 22, 52, 46, 33, 30,  2, 32,  3, 21, 15,  0,
       16, 17, 49, 45, 13, 40, 36, 41, 53,  4,  8, 10,  5, 24, 11]), 'cur_cost': 99365.0}, {'tour': [43, 47, 38, 60, 2, 50, 15, 55, 44, 31, 18, 40, 5, 23, 65, 10, 35, 1, 63, 13, 39, 21, 54, 26, 48, 34, 17, 64, 36, 9, 29, 46, 59, 7, 4, 56, 25, 32, 62, 11, 57, 27, 33, 16, 61, 42, 8, 53, 37, 19, 51, 30, 45, 14, 28, 22, 49, 20, 3, 52, 0, 12, 24, 41, 6, 58], 'cur_cost': 111265.0}, {'tour': array([62, 19, 12, 63, 26, 45, 36, 56,  2, 15, 57, 23, 27,  7, 24, 53, 43,
        0, 47, 22, 14, 10, 33,  8, 61, 65, 42, 46, 18, 34, 28, 20, 29, 49,
       17,  1, 25,  6, 52,  5, 16, 50, 21, 58, 54, 30,  3, 48, 40, 64, 38,
       31, 51, 13, 35, 32,  4, 59, 39,  9, 55, 44, 60, 11, 41, 37]), 'cur_cost': 112426.0}]
2025-06-22 18:36:33,593 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:36:33,594 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-06-22 18:36:33,594 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 18:36:33,594 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 18:36:33,594 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:36:33,595 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [43, 47, 38] and ending with [41, 6, 58]
- Cost: 111265.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8123076923076923
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:36:33,596 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:36:37,004 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    43, 47, 38, 5, 61, 54, 23, 11, 32, 1, 4, 13, 2, 16, 39, 25, 10, 34, 50, 65, 18, 52, 31, 15, 41, 6, 58, 20, 48, 28, 60, 17, 29, 40, 63, 0, 57, 37, 33, 9, 45, 46, 26, 24, 19, 64, 27, 51, 12, 55, 59, 7, 36, 56, 3, 22, 14, 30, 62, 35, 42, 49, 21, 53, 8, 44
  ],
  "modification_strategy": "This strategy focuses on generating a path significantly different from the original while attempting to incorporate more diverse nodes.  It aims to move away from the initial segments in the original path and use a random node selection approach while maintaining a close check on visiting all nodes exactly once.",
  "targeted_regions": "The strategy aims at visiting nodes in different regions, specifically those highlighted as unexplored or opportunity regions based on the search context."
}
```
2025-06-22 18:36:37,004 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:36:37,004 - ExplorationExpert - INFO - 探索路径生成完成，成本: 104776.0, 路径: [43, 47, 38, 5, 61, 54, 23, 11, 32, 1, 4, 13, 2, 16, 39, 25, 10, 34, 50, 65, 18, 52, 31, 15, 41, 6, 58, 20, 48, 28, 60, 17, 29, 40, 63, 0, 57, 37, 33, 9, 45, 46, 26, 24, 19, 64, 27, 51, 12, 55, 59, 7, 36, 56, 3, 22, 14, 30, 62, 35, 42, 49, 21, 53, 8, 44]
2025-06-22 18:36:37,004 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [43, 47, 38, 5, 61, 54, 23, 11, 32, 1, 4, 13, 2, 16, 39, 25, 10, 34, 50, 65, 18, 52, 31, 15, 41, 6, 58, 20, 48, 28, 60, 17, 29, 40, 63, 0, 57, 37, 33, 9, 45, 46, 26, 24, 19, 64, 27, 51, 12, 55, 59, 7, 36, 56, 3, 22, 14, 30, 62, 35, 42, 49, 21, 53, 8, 44], 'cur_cost': 104776.0}
2025-06-22 18:36:37,004 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 18:36:37,004 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:36:37,004 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:36:37,004 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 111717.0
2025-06-22 18:36:37,505 - ExploitationExpert - INFO - res_population_num: 31
2025-06-22 18:36:37,505 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-22 18:36:37,506 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:36:37,518 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:36:37,518 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 58, 57, 52, 47, 42, 37, 32, 27, 22, 17, 12, 7, 2, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53], 'cur_cost': 40401.0}, {'tour': [41, 58, 37, 12, 4, 22, 55, 17, 6, 31, 63, 2, 28, 48, 3, 59, 19, 52, 25, 61, 44, 14, 36, 0, 20, 43, 26, 5, 33, 65, 10, 50, 39, 15, 56, 34, 9, 30, 64, 16, 51, 23, 49, 29, 18, 24, 46, 62, 38, 53, 42, 57, 45, 8, 1, 11, 35, 47, 27, 60, 32, 7, 13, 54, 21, 40], 'cur_cost': 126833.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': [42, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24], 'cur_cost': 16122.0}, {'tour': array([34, 27, 46, 15, 50, 47, 64, 18, 32, 19, 63, 24, 49, 13, 54,  9,  3,
       55, 58, 37, 53, 41, 40,  8, 61, 44, 22, 23,  6, 52, 26, 42, 12, 39,
       31, 29, 28, 10, 57,  1,  2, 30, 51, 35, 43, 21, 59, 65,  4, 14, 33,
       36, 56, 48, 20, 38,  5, 16, 60,  7, 62,  0, 25, 11, 45, 17]), 'cur_cost': 99764.0}, {'tour': [22, 45, 50, 2, 64, 33, 41, 1, 28, 54, 65, 38, 4, 27, 40, 57, 11, 10, 60, 39, 35, 3, 48, 30, 19, 13, 51, 55, 24, 62, 17, 46, 9, 43, 15, 8, 26, 6, 36, 42, 23, 21, 49, 32, 31, 59, 12, 61, 52, 5, 29, 37, 53, 20, 47, 63, 18, 14, 56, 16, 0, 44, 7, 25, 58, 34], 'cur_cost': 118913.0}, {'tour': array([48, 45, 43,  2, 21, 32, 64, 42, 62, 63, 58, 31, 10, 65, 25, 49,  0,
        8,  3, 53, 17, 59, 57, 14, 20, 35, 24,  7, 15, 61, 40, 56, 46, 55,
       33, 23, 22, 13, 37, 30, 39, 11, 34, 29, 60, 18, 44, 41,  6,  1, 28,
        4,  9, 51, 50, 19, 38, 26, 54,  5, 16, 12, 52, 36, 27, 47]), 'cur_cost': 97165.0}, {'tour': array([64, 50,  7, 14, 43, 39, 55, 42, 37, 59, 35, 62, 51, 58, 18, 48, 23,
       20, 34, 57, 56, 12, 38, 19,  6, 65,  9, 63,  1, 31, 28, 60, 61, 54,
       44, 25, 26, 47, 27, 29, 22, 52, 46, 33, 30,  2, 32,  3, 21, 15,  0,
       16, 17, 49, 45, 13, 40, 36, 41, 53,  4,  8, 10,  5, 24, 11]), 'cur_cost': 99365.0}, {'tour': [43, 47, 38, 5, 61, 54, 23, 11, 32, 1, 4, 13, 2, 16, 39, 25, 10, 34, 50, 65, 18, 52, 31, 15, 41, 6, 58, 20, 48, 28, 60, 17, 29, 40, 63, 0, 57, 37, 33, 9, 45, 46, 26, 24, 19, 64, 27, 51, 12, 55, 59, 7, 36, 56, 3, 22, 14, 30, 62, 35, 42, 49, 21, 53, 8, 44], 'cur_cost': 104776.0}, {'tour': array([20, 61, 32, 43, 48, 23, 29, 56, 40, 28, 64, 27, 22, 63, 49, 38, 19,
       25, 46, 65, 18, 57,  4, 60, 53,  1, 45, 58, 33,  3,  5, 54, 11, 26,
       51, 59, 13, 12, 39,  6, 35, 44, 17, 34, 24, 41, 62,  7,  2, 36, 16,
        0, 30, 21, 10, 52, 47, 37, 55, 15,  8, 50, 42, 14,  9, 31]), 'cur_cost': 111717.0}]
2025-06-22 18:36:37,521 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:36:37,522 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-06-22 18:36:37,522 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 18:36:37,522 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 58, 57, 52, 47, 42, 37, 32, 27, 22, 17, 12, 7, 2, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53], 'cur_cost': 40401.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [41, 58, 37, 12, 4, 22, 55, 17, 6, 31, 63, 2, 28, 48, 3, 59, 19, 52, 25, 61, 44, 14, 36, 0, 20, 43, 26, 5, 33, 65, 10, 50, 39, 15, 56, 34, 9, 30, 64, 16, 51, 23, 49, 29, 18, 24, 46, 62, 38, 53, 42, 57, 45, 8, 1, 11, 35, 47, 27, 60, 32, 7, 13, 54, 21, 40], 'cur_cost': 126833.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [42, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24], 'cur_cost': 16122.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [22, 45, 50, 2, 64, 33, 41, 1, 28, 54, 65, 38, 4, 27, 40, 57, 11, 10, 60, 39, 35, 3, 48, 30, 19, 13, 51, 55, 24, 62, 17, 46, 9, 43, 15, 8, 26, 6, 36, 42, 23, 21, 49, 32, 31, 59, 12, 61, 52, 5, 29, 37, 53, 20, 47, 63, 18, 14, 56, 16, 0, 44, 7, 25, 58, 34], 'cur_cost': 118913.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [43, 47, 38, 5, 61, 54, 23, 11, 32, 1, 4, 13, 2, 16, 39, 25, 10, 34, 50, 65, 18, 52, 31, 15, 41, 6, 58, 20, 48, 28, 60, 17, 29, 40, 63, 0, 57, 37, 33, 9, 45, 46, 26, 24, 19, 64, 27, 51, 12, 55, 59, 7, 36, 56, 3, 22, 14, 30, 62, 35, 42, 49, 21, 53, 8, 44], 'cur_cost': 104776.0}}]
2025-06-22 18:36:37,523 - __main__ - INFO - 进化阶段完成
2025-06-22 18:36:37,523 - __main__ - INFO - 开始评估阶段
2025-06-22 18:36:37,523 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:36:37,524 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:36:37,524 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 3/5
- Population changes:
  * Cost: min 13176.0->11461.0, mean 82933.5->82651.7
  * Diversity: 0.8723076923076923->0.8015384615384615
  * Elite solutions: 1->1

## Strategy Performance:
- Explore: 6 individuals, success rate 66.7%, avg improvement 9731.5
- Exploit: 4 individuals, success rate 50.0%, avg improvement -13892.75
- Overall improvement: 1715.0

## Historical Trends:
Last 3 iterations: costs [11493.0, 11458.0, 13176.0], diversity [0.9626262626262625, 0.9562289562289561, 0.9683501683501683]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:36:42,812 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Late-Stage Refinement/Local Search",
    "features": [
      "Cost is approaching a local minimum (as indicated by the smaller cost fluctuations in historical data).",
      "Diversity is decreasing, suggesting convergence.",
      "The elite solution has not changed, implying the current best solution hasn't been improved upon in the last iteration.",
      "Overall improvement is small, also pointing to refinement stage."
    ]
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Good",
      "details": "Explore strategy shows a good success rate (66.7%) and significant average improvement (9731.5). This suggests that there are still regions of the search space with potential for improvement."
    },
    "exploit": {
      "performance": "Poor",
      "details": "Exploit strategy's success rate is only 50.0% and demonstrates a *negative* average improvement (-13892.75). This indicates the exploitation is either getting stuck in a local minima or generating sub-optimal solutions around existing good solutions."
    },
    "overall": {
      "summary": "Overall improvement is positive but small (1715.0), indicating that the gains are becoming more difficult to achieve."
    }
  },
  "balance_state": {
    "assessment": "The balance is shifted *towards* exploration is *not* optimal in late-stage refinement. The exploitation is clearly underperforming. The exploration is helping, but it is not enough to find better solution",
    "adjustment_needs": "The strategy needs a major adjustment by giving more weight to exploration or adjust the exploitation strategy"
  },
  "recommendations": [
    {
      "action": "Increase Exploration Intensity",
      "details": "Increase the number of individuals used in the exploration phase in the next iteration. This will likely allow more diversity in solutions and find a better solution.",
      "rationale": "Since exploitation is not as useful, it is safe to give more weight to exploration to find a better solution in this refine-stage."
    },
    {
      "action": "Review the Exploitation Strategy",
      "details": "Analyze the methods used in the exploitation phase. Consider the cause of the poor performance and try to improve the exploitation strategy to move towards the best solution.",
      "rationale": "If exploitation can be made successful, then the algorithm can move to the best solution in less time and effort."
    },
    {
      "action": "Monitor Diversity closely",
      "details": "Since diversity is dropping, closely monitor the diversity metrics to prevent premature convergence. Consider adjusting mutation rates or introducing novel search strategies if the diversity drops below a critical threshold. Make sure the changes will not make the cost increase.",
      "rationale": "Maintaining diversity is crucial to prevent getting stuck in local optima and explore a wider search space in this stage"
    }
  ]
}
```
2025-06-22 18:36:42,840 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:36:42,840 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Late-Stage Refinement/Local Search",
    "features": [
      "Cost is approaching a local minimum (as indicated by the smaller cost fluctuations in historical data).",
      "Diversity is decreasing, suggesting convergence.",
      "The elite solution has not changed, implying the current best solution hasn't been improved upon in the last iteration.",
      "Overall improvement is small, also pointing to refinement stage."
    ]
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Good",
      "details": "Explore strategy shows a good success rate (66.7%) and significant average improvement (9731.5). This suggests that there are still regions of the search space with potential for improvement."
    },
    "exploit": {
      "performance": "Poor",
      "details": "Exploit strategy's success rate is only 50.0% and demonstrates a *negative* average improvement (-13892.75). This indicates the exploitation is either getting stuck in a local minima or generating sub-optimal solutions around existing good solutions."
    },
    "overall": {
      "summary": "Overall improvement is positive but small (1715.0), indicating that the gains are becoming more difficult to achieve."
    }
  },
  "balance_state": {
    "assessment": "The balance is shifted *towards* exploration is *not* optimal in late-stage refinement. The exploitation is clearly underperforming. The exploration is helping, but it is not enough to find better solution",
    "adjustment_needs": "The strategy needs a major adjustment by giving more weight to exploration or adjust the exploitation strategy"
  },
  "recommendations": [
    {
      "action": "Increase Exploration Intensity",
      "details": "Increase the number of individuals used in the exploration phase in the next iteration. This will likely allow more diversity in solutions and find a better solution.",
      "rationale": "Since exploitation is not as useful, it is safe to give more weight to exploration to find a better solution in this refine-stage."
    },
    {
      "action": "Review the Exploitation Strategy",
      "details": "Analyze the methods used in the exploitation phase. Consider the cause of the poor performance and try to improve the exploitation strategy to move towards the best solution.",
      "rationale": "If exploitation can be made successful, then the algorithm can move to the best solution in less time and effort."
    },
    {
      "action": "Monitor Diversity closely",
      "details": "Since diversity is dropping, closely monitor the diversity metrics to prevent premature convergence. Consider adjusting mutation rates or introducing novel search strategies if the diversity drops below a critical threshold. Make sure the changes will not make the cost increase.",
      "rationale": "Maintaining diversity is crucial to prevent getting stuck in local optima and explore a wider search space in this stage"
    }
  ]
}
```
2025-06-22 18:36:42,842 - __main__ - INFO - 评估阶段完成
2025-06-22 18:36:42,842 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Late-Stage Refinement/Local Search",
    "features": [
      "Cost is approaching a local minimum (as indicated by the smaller cost fluctuations in historical data).",
      "Diversity is decreasing, suggesting convergence.",
      "The elite solution has not changed, implying the current best solution hasn't been improved upon in the last iteration.",
      "Overall improvement is small, also pointing to refinement stage."
    ]
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Good",
      "details": "Explore strategy shows a good success rate (66.7%) and significant average improvement (9731.5). This suggests that there are still regions of the search space with potential for improvement."
    },
    "exploit": {
      "performance": "Poor",
      "details": "Exploit strategy's success rate is only 50.0% and demonstrates a *negative* average improvement (-13892.75). This indicates the exploitation is either getting stuck in a local minima or generating sub-optimal solutions around existing good solutions."
    },
    "overall": {
      "summary": "Overall improvement is positive but small (1715.0), indicating that the gains are becoming more difficult to achieve."
    }
  },
  "balance_state": {
    "assessment": "The balance is shifted *towards* exploration is *not* optimal in late-stage refinement. The exploitation is clearly underperforming. The exploration is helping, but it is not enough to find better solution",
    "adjustment_needs": "The strategy needs a major adjustment by giving more weight to exploration or adjust the exploitation strategy"
  },
  "recommendations": [
    {
      "action": "Increase Exploration Intensity",
      "details": "Increase the number of individuals used in the exploration phase in the next iteration. This will likely allow more diversity in solutions and find a better solution.",
      "rationale": "Since exploitation is not as useful, it is safe to give more weight to exploration to find a better solution in this refine-stage."
    },
    {
      "action": "Review the Exploitation Strategy",
      "details": "Analyze the methods used in the exploitation phase. Consider the cause of the poor performance and try to improve the exploitation strategy to move towards the best solution.",
      "rationale": "If exploitation can be made successful, then the algorithm can move to the best solution in less time and effort."
    },
    {
      "action": "Monitor Diversity closely",
      "details": "Since diversity is dropping, closely monitor the diversity metrics to prevent premature convergence. Consider adjusting mutation rates or introducing novel search strategies if the diversity drops below a critical threshold. Make sure the changes will not make the cost increase.",
      "rationale": "Maintaining diversity is crucial to prevent getting stuck in local optima and explore a wider search space in this stage"
    }
  ]
}
```
2025-06-22 18:36:42,842 - __main__ - INFO - 当前最佳适应度: 11461.0
2025-06-22 18:36:42,842 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_3.pkl
2025-06-22 18:36:42,842 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-06-22 18:36:42,842 - __main__ - INFO - 开始分析阶段
2025-06-22 18:36:42,842 - StatsExpert - INFO - 开始统计分析
2025-06-22 18:36:42,862 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11461.0, 'max': 126833.0, 'mean': 82651.7, 'std': 40820.809578081615}, 'diversity': 0.9454545454545454, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 1, 2, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 18:36:42,863 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11461.0, 'max': 126833.0, 'mean': 82651.7, 'std': 40820.809578081615}, 'diversity_level': 0.9454545454545454, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 1, 2, 1, 1, 1, 1, 1, 1]}}
2025-06-22 18:36:42,863 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 18:36:42,863 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 18:36:42,863 - PathExpert - INFO - 开始路径结构分析
2025-06-22 18:36:42,865 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 18:36:42,866 - PathExpert - INFO - 路径结构分析完成
2025-06-22 18:36:42,866 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 18:36:42,867 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 18:36:42,867 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 18:36:42,867 - EliteExpert - INFO - 开始精英解分析
2025-06-22 18:36:42,878 - EliteExpert - INFO - 精英解分析完成
2025-06-22 18:36:42,878 - __main__ - INFO - 精英专家分析报告: {'elite_count': 31, 'elite_common_features': {'common_edges': {'(51, 38)': 0.7096774193548387}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1940.0, 'avg_gap': 70166.0870967742}, 'structure_gap': {'unique_elite_edges': 222, 'unique_pop_edges': 517, 'common_edges': 53}}, 'elite_diversity': {'diversity_score': 0.2613880742913002}}
2025-06-22 18:36:42,878 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 18:36:42,878 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 18:36:42,878 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 18:36:42,883 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=11461.0, max=126833.0, mean=82651.7, std=40820.809578081615
- Diversity: 0.9454545454545454, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Evolution Context:
- Iteration: 4/5
- Historical trend: Recent costs: [11458.0, 13176.0, 11461.0]
- Improvement rate: 857.50 per iteration
- Stagnation periods: None detected

## Problem Characteristics:
- TSP instance: Unknown with 0 nodes
- Search space topology: Likely rugged with many local optima
- Edge distribution: Unknown
- Known difficult regions: 0 identified

## Key Relationships:
- Elite solutions share 0% of edges
- Population clustering: No clustering information
- Exploration vs exploitation balance: High exploration (>70%)
- Local optima density: Low

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality, deceptiveness)
2. Population state (diversity, convergence, clustering)
3. Difficult regions and specific challenges (edge crossings, isolated nodes)
4. Opportunity regions with potential for improvement
5. Current evolution phase (exploration, exploitation, refinement)
6. Recommended evolution direction with specific operator suggestions

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics including ruggedness, modality, and deceptiveness
- population_state: Current state assessment including diversity, convergence, and clustering
- difficult_regions: Identified challenges with specific node sequences or edges to avoid
- opportunity_regions: Promising areas with specific node sequences or edges to include
- evolution_phase: Current phase identification (exploration/exploitation/refinement)
- evolution_direction: Recommended strategy with specific operator suggestions

2025-06-22 18:36:48,459 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely highly rugged due to the large cost range (min to max is a significant factor) and high standard deviation in cost.  This suggests many local optima.",
    "modality": "Likely multimodal, given the ruggedness.  The presence of multiple cost plateaus or localized good solutions is probable.",
    "deceptiveness": "Potentially deceptive.  The lack of convergence and the presence of good solutions that don't share edges could mean that misleading gradients guide the search away from the global optimum."
  },
  "population_state": {
    "diversity": "High (0.945). This is expected with the low convergence.  This confirms that the population is exploring diverse areas of the search space.",
    "convergence": "Extremely low (0.0). This indicates the population is not converging towards a common solution or set of edges. This is typical for a highly exploratory phase or an instance that is difficult to solve.",
    "clustering": "No clustering information available, but the high diversity and low convergence suggest very limited clustering."
  },
  "difficult_regions": {
    "challenges": "No specific difficult regions are identified. The absence of known difficult regions or elite solutions sharing edges suggests a broader exploration phase. The large cost range suggests several potential suboptimal 'zones'." ,
    "node_sequences_to_avoid": "Unable to identify specific node sequences to avoid at this stage given the limited data and high diversity.  It would likely be beneficial to identify edge crossings in a next stage using visualization, as the TSP instance is not known."
  },
  "opportunity_regions": {
    "promising_areas": "Unclear at this stage. The high diversity doesn't point to specific promising areas.  More data points are needed. However, the presence of some 'good' solutions (e.g., the recent costs including values close to the minimum) hints at promising areas that have not yet been exploited.",
    "node_sequences_to_include": "Impossible to specify yet, based on the current analysis. The high diversity, zero shared edges and lack of converged solution prevents targeted improvements."
  },
  "evolution_phase": "Predominantly Exploration with a small degree of Refinement",
  "evolution_direction": {
    "strategy": "Continue with a largely exploratory strategy, but subtly introduce exploitation.",
    "operator_suggestions": [
      "**Exploration:** Use operators like 2-opt, or 3-opt with a bias toward diversification.  Consider a mutation operator that allows for adding/removing random nodes from the routes at a low rate, as a way to expand the search area.",
      "**Refinement:** Slowly increase the rate of crossover, especially to keep the population stable and prevent random drifting of solutions.",
       "**Adaptive Operators:** Implement an adaptive mechanism (e.g., Reinforcement learning) for tuning exploration/exploitation rates dynamically, to learn the proper balance as the algorithm continues to evolve."
    ]
  }
}
```
2025-06-22 18:36:48,459 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 18:36:48,459 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Likely highly rugged due to the large cost range (min to max is a significant factor) and high standard deviation in cost.  This suggests many local optima.', 'modality': 'Likely multimodal, given the ruggedness.  The presence of multiple cost plateaus or localized good solutions is probable.', 'deceptiveness': "Potentially deceptive.  The lack of convergence and the presence of good solutions that don't share edges could mean that misleading gradients guide the search away from the global optimum."}, 'population_state': {'diversity': 'High (0.945). This is expected with the low convergence.  This confirms that the population is exploring diverse areas of the search space.', 'convergence': 'Extremely low (0.0). This indicates the population is not converging towards a common solution or set of edges. This is typical for a highly exploratory phase or an instance that is difficult to solve.', 'clustering': 'No clustering information available, but the high diversity and low convergence suggest very limited clustering.'}, 'difficult_regions': {'challenges': "No specific difficult regions are identified. The absence of known difficult regions or elite solutions sharing edges suggests a broader exploration phase. The large cost range suggests several potential suboptimal 'zones'.", 'node_sequences_to_avoid': 'Unable to identify specific node sequences to avoid at this stage given the limited data and high diversity.  It would likely be beneficial to identify edge crossings in a next stage using visualization, as the TSP instance is not known.'}, 'opportunity_regions': {'promising_areas': "Unclear at this stage. The high diversity doesn't point to specific promising areas.  More data points are needed. However, the presence of some 'good' solutions (e.g., the recent costs including values close to the minimum) hints at promising areas that have not yet been exploited.", 'node_sequences_to_include': 'Impossible to specify yet, based on the current analysis. The high diversity, zero shared edges and lack of converged solution prevents targeted improvements.'}, 'evolution_phase': 'Predominantly Exploration with a small degree of Refinement', 'evolution_direction': {'strategy': 'Continue with a largely exploratory strategy, but subtly introduce exploitation.', 'operator_suggestions': ['**Exploration:** Use operators like 2-opt, or 3-opt with a bias toward diversification.  Consider a mutation operator that allows for adding/removing random nodes from the routes at a low rate, as a way to expand the search area.', '**Refinement:** Slowly increase the rate of crossover, especially to keep the population stable and prevent random drifting of solutions.', '**Adaptive Operators:** Implement an adaptive mechanism (e.g., Reinforcement learning) for tuning exploration/exploitation rates dynamically, to learn the proper balance as the algorithm continues to evolve.']}}
2025-06-22 18:36:48,459 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 18:36:48,459 - __main__ - INFO - 分析阶段完成
2025-06-22 18:36:48,459 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Likely highly rugged due to the large cost range (min to max is a significant factor) and high standard deviation in cost.  This suggests many local optima.', 'modality': 'Likely multimodal, given the ruggedness.  The presence of multiple cost plateaus or localized good solutions is probable.', 'deceptiveness': "Potentially deceptive.  The lack of convergence and the presence of good solutions that don't share edges could mean that misleading gradients guide the search away from the global optimum."}, 'population_state': {'diversity': 'High (0.945). This is expected with the low convergence.  This confirms that the population is exploring diverse areas of the search space.', 'convergence': 'Extremely low (0.0). This indicates the population is not converging towards a common solution or set of edges. This is typical for a highly exploratory phase or an instance that is difficult to solve.', 'clustering': 'No clustering information available, but the high diversity and low convergence suggest very limited clustering.'}, 'difficult_regions': {'challenges': "No specific difficult regions are identified. The absence of known difficult regions or elite solutions sharing edges suggests a broader exploration phase. The large cost range suggests several potential suboptimal 'zones'.", 'node_sequences_to_avoid': 'Unable to identify specific node sequences to avoid at this stage given the limited data and high diversity.  It would likely be beneficial to identify edge crossings in a next stage using visualization, as the TSP instance is not known.'}, 'opportunity_regions': {'promising_areas': "Unclear at this stage. The high diversity doesn't point to specific promising areas.  More data points are needed. However, the presence of some 'good' solutions (e.g., the recent costs including values close to the minimum) hints at promising areas that have not yet been exploited.", 'node_sequences_to_include': 'Impossible to specify yet, based on the current analysis. The high diversity, zero shared edges and lack of converged solution prevents targeted improvements.'}, 'evolution_phase': 'Predominantly Exploration with a small degree of Refinement', 'evolution_direction': {'strategy': 'Continue with a largely exploratory strategy, but subtly introduce exploitation.', 'operator_suggestions': ['**Exploration:** Use operators like 2-opt, or 3-opt with a bias toward diversification.  Consider a mutation operator that allows for adding/removing random nodes from the routes at a low rate, as a way to expand the search area.', '**Refinement:** Slowly increase the rate of crossover, especially to keep the population stable and prevent random drifting of solutions.', '**Adaptive Operators:** Implement an adaptive mechanism (e.g., Reinforcement learning) for tuning exploration/exploitation rates dynamically, to learn the proper balance as the algorithm continues to evolve.']}}
2025-06-22 18:36:48,462 - __main__ - INFO - 开始策略分配阶段
2025-06-22 18:36:48,462 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 18:36:48,462 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 4
- Population: 10 individuals, diversity=High (0.945). This is expected with the low convergence.  This confirms that the population is exploring diverse areas of the search space.
- Best cost: 11461.0, Mean cost: 82651.7
- Search space: Ruggedness: Likely highly rugged due to the large cost range (min to max is a significant factor) and high standard deviation in cost.  This suggests many local optima., Modality: Likely multimodal, given the ruggedness.  The presence of multiple cost plateaus or localized good solutions is probable.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Late-Stage Refinement/Local Search",
    "features": [
      "Cost is approaching a local minimum (as indicated by the smaller cost fluctuations in histo...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 18:36:48,462 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 18:36:51,114 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.4,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_strength": 0.2,
      "crossover_probability": 0.8,
      "search_radius_reduction": 0.8,
      "step_size_adaptation": "adaptive"
    },
    "exploit": {
      "mutation_strength": 0.05,
      "crossover_probability": 0.2,
      "search_radius_reduction": 0.95,
      "step_size_adaptation": "none"
    }
  }
}
```

2025-06-22 18:36:51,114 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 18:36:51,114 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 18:36:51,114 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.4,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_strength": 0.2,
      "crossover_probability": 0.8,
      "search_radius_reduction": 0.8,
      "step_size_adaptation": "adaptive"
    },
    "exploit": {
      "mutation_strength": 0.05,
      "crossover_probability": 0.2,
      "search_radius_reduction": 0.95,
      "step_size_adaptation": "none"
    }
  }
}
```

2025-06-22 18:36:51,114 - __main__ - INFO - 策略分配阶段完成
2025-06-22 18:36:51,114 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 18:36:51,114 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.4,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_strength": 0.2,
      "crossover_probability": 0.8,
      "search_radius_reduction": 0.8,
      "step_size_adaptation": "adaptive"
    },
    "exploit": {
      "mutation_strength": 0.05,
      "crossover_probability": 0.2,
      "search_radius_reduction": 0.95,
      "step_size_adaptation": "none"
    }
  }
}
```

2025-06-22 18:36:51,114 - __main__ - INFO - 开始进化阶段
2025-06-22 18:36:51,114 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 18:36:51,114 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:36:51,114 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 5, 10] and ending with [43, 48, 53]
- Cost: 40401.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8015384615384615
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:36:51,114 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:36:55,284 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Completely reset the path to a naive linear progression from 0 to 65. This ensures maximal diversity and is guaranteed to be valid. The cost will be very high, but the goal is to explore new node orderings.",
  "targeted_regions": "All regions are equally targeted as every node is visited. While the cost may be extremely poor, the emphasis is on a path completely different from the original. The opportunity regions and difficult regions will be touched upon."
}
```
2025-06-22 18:36:55,284 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:36:55,284 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 18:36:55,284 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-22 18:36:55,284 - __main__ - INFO - 为个体 1 生成利用路径
2025-06-22 18:36:55,284 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:36:55,284 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:36:55,284 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 102935.0
2025-06-22 18:36:55,786 - ExploitationExpert - INFO - res_population_num: 32
2025-06-22 18:36:55,786 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521]
2025-06-22 18:36:55,786 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:36:55,799 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:36:55,799 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([45, 26, 50, 30, 54, 62, 11, 25, 28, 38, 58, 22, 60,  8, 47, 64,  1,
       37, 35, 24,  7, 21, 16,  4, 20, 36, 43, 49, 53, 23,  2, 61, 55, 41,
        6, 57, 40, 39,  5, 17, 14, 13, 27, 63, 18, 56, 42, 10,  9, 52, 31,
       32, 65, 59, 29, 19, 48,  0, 46, 12, 33, 15, 44, 51, 34,  3]), 'cur_cost': 102935.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': [42, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24], 'cur_cost': 16122.0}, {'tour': array([34, 27, 46, 15, 50, 47, 64, 18, 32, 19, 63, 24, 49, 13, 54,  9,  3,
       55, 58, 37, 53, 41, 40,  8, 61, 44, 22, 23,  6, 52, 26, 42, 12, 39,
       31, 29, 28, 10, 57,  1,  2, 30, 51, 35, 43, 21, 59, 65,  4, 14, 33,
       36, 56, 48, 20, 38,  5, 16, 60,  7, 62,  0, 25, 11, 45, 17]), 'cur_cost': 99764.0}, {'tour': [22, 45, 50, 2, 64, 33, 41, 1, 28, 54, 65, 38, 4, 27, 40, 57, 11, 10, 60, 39, 35, 3, 48, 30, 19, 13, 51, 55, 24, 62, 17, 46, 9, 43, 15, 8, 26, 6, 36, 42, 23, 21, 49, 32, 31, 59, 12, 61, 52, 5, 29, 37, 53, 20, 47, 63, 18, 14, 56, 16, 0, 44, 7, 25, 58, 34], 'cur_cost': 118913.0}, {'tour': array([48, 45, 43,  2, 21, 32, 64, 42, 62, 63, 58, 31, 10, 65, 25, 49,  0,
        8,  3, 53, 17, 59, 57, 14, 20, 35, 24,  7, 15, 61, 40, 56, 46, 55,
       33, 23, 22, 13, 37, 30, 39, 11, 34, 29, 60, 18, 44, 41,  6,  1, 28,
        4,  9, 51, 50, 19, 38, 26, 54,  5, 16, 12, 52, 36, 27, 47]), 'cur_cost': 97165.0}, {'tour': array([64, 50,  7, 14, 43, 39, 55, 42, 37, 59, 35, 62, 51, 58, 18, 48, 23,
       20, 34, 57, 56, 12, 38, 19,  6, 65,  9, 63,  1, 31, 28, 60, 61, 54,
       44, 25, 26, 47, 27, 29, 22, 52, 46, 33, 30,  2, 32,  3, 21, 15,  0,
       16, 17, 49, 45, 13, 40, 36, 41, 53,  4,  8, 10,  5, 24, 11]), 'cur_cost': 99365.0}, {'tour': [43, 47, 38, 5, 61, 54, 23, 11, 32, 1, 4, 13, 2, 16, 39, 25, 10, 34, 50, 65, 18, 52, 31, 15, 41, 6, 58, 20, 48, 28, 60, 17, 29, 40, 63, 0, 57, 37, 33, 9, 45, 46, 26, 24, 19, 64, 27, 51, 12, 55, 59, 7, 36, 56, 3, 22, 14, 30, 62, 35, 42, 49, 21, 53, 8, 44], 'cur_cost': 104776.0}, {'tour': array([20, 61, 32, 43, 48, 23, 29, 56, 40, 28, 64, 27, 22, 63, 49, 38, 19,
       25, 46, 65, 18, 57,  4, 60, 53,  1, 45, 58, 33,  3,  5, 54, 11, 26,
       51, 59, 13, 12, 39,  6, 35, 44, 17, 34, 24, 41, 62,  7,  2, 36, 16,
        0, 30, 21, 10, 52, 47, 37, 55, 15,  8, 50, 42, 14,  9, 31]), 'cur_cost': 111717.0}]
2025-06-22 18:36:55,803 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:36:55,803 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-06-22 18:36:55,803 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-06-22 18:36:55,803 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 18:36:55,803 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:36:55,804 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [12, 23, 22] and ending with [9, 10, 11]
- Cost: 11461.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7261538461538461
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:36:55,804 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:36:59,293 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11
  ],
  "modification_strategy": "This path prioritizes exploring the unexplored and opportunity regions identified in the search context. It deviates significantly from the original path by creating larger clusters and utilizing a sweeping strategy through the node space to ensure diversity. The early nodes are used to navigate towards unexplored regions (24-39), followed by a broader exploration. The last part returns to end node (9, 10, 11) from the initial clusters(12, 23, 22).",
  "targeted_regions": "Unexplored regions (nodes roughly around 24-39), and general traversal for node 0-8"
}
```
2025-06-22 18:36:59,293 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:36:59,293 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11461.0, 路径: [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-06-22 18:36:59,293 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}
2025-06-22 18:36:59,293 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-22 18:36:59,293 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:36:59,293 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:36:59,293 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 96336.0
2025-06-22 18:36:59,795 - ExploitationExpert - INFO - res_population_num: 33
2025-06-22 18:36:59,795 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521]
2025-06-22 18:36:59,795 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:36:59,810 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:36:59,810 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([45, 26, 50, 30, 54, 62, 11, 25, 28, 38, 58, 22, 60,  8, 47, 64,  1,
       37, 35, 24,  7, 21, 16,  4, 20, 36, 43, 49, 53, 23,  2, 61, 55, 41,
        6, 57, 40, 39,  5, 17, 14, 13, 27, 63, 18, 56, 42, 10,  9, 52, 31,
       32, 65, 59, 29, 19, 48,  0, 46, 12, 33, 15, 44, 51, 34,  3]), 'cur_cost': 102935.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': array([47, 39, 43, 37,  2, 61, 15, 28, 24, 16,  3, 48, 64, 46, 14,  0, 40,
       22, 57, 21, 31, 17, 12, 56, 33, 23, 52, 59,  4, 38, 58, 27,  8, 25,
       49, 18, 42,  9, 34, 30, 26,  1,  7, 13, 41, 53, 19, 60, 11,  6, 10,
        5, 36, 50, 20, 44, 54, 63, 55, 62, 35, 65, 32, 29, 45, 51]), 'cur_cost': 96336.0}, {'tour': array([34, 27, 46, 15, 50, 47, 64, 18, 32, 19, 63, 24, 49, 13, 54,  9,  3,
       55, 58, 37, 53, 41, 40,  8, 61, 44, 22, 23,  6, 52, 26, 42, 12, 39,
       31, 29, 28, 10, 57,  1,  2, 30, 51, 35, 43, 21, 59, 65,  4, 14, 33,
       36, 56, 48, 20, 38,  5, 16, 60,  7, 62,  0, 25, 11, 45, 17]), 'cur_cost': 99764.0}, {'tour': [22, 45, 50, 2, 64, 33, 41, 1, 28, 54, 65, 38, 4, 27, 40, 57, 11, 10, 60, 39, 35, 3, 48, 30, 19, 13, 51, 55, 24, 62, 17, 46, 9, 43, 15, 8, 26, 6, 36, 42, 23, 21, 49, 32, 31, 59, 12, 61, 52, 5, 29, 37, 53, 20, 47, 63, 18, 14, 56, 16, 0, 44, 7, 25, 58, 34], 'cur_cost': 118913.0}, {'tour': array([48, 45, 43,  2, 21, 32, 64, 42, 62, 63, 58, 31, 10, 65, 25, 49,  0,
        8,  3, 53, 17, 59, 57, 14, 20, 35, 24,  7, 15, 61, 40, 56, 46, 55,
       33, 23, 22, 13, 37, 30, 39, 11, 34, 29, 60, 18, 44, 41,  6,  1, 28,
        4,  9, 51, 50, 19, 38, 26, 54,  5, 16, 12, 52, 36, 27, 47]), 'cur_cost': 97165.0}, {'tour': array([64, 50,  7, 14, 43, 39, 55, 42, 37, 59, 35, 62, 51, 58, 18, 48, 23,
       20, 34, 57, 56, 12, 38, 19,  6, 65,  9, 63,  1, 31, 28, 60, 61, 54,
       44, 25, 26, 47, 27, 29, 22, 52, 46, 33, 30,  2, 32,  3, 21, 15,  0,
       16, 17, 49, 45, 13, 40, 36, 41, 53,  4,  8, 10,  5, 24, 11]), 'cur_cost': 99365.0}, {'tour': [43, 47, 38, 5, 61, 54, 23, 11, 32, 1, 4, 13, 2, 16, 39, 25, 10, 34, 50, 65, 18, 52, 31, 15, 41, 6, 58, 20, 48, 28, 60, 17, 29, 40, 63, 0, 57, 37, 33, 9, 45, 46, 26, 24, 19, 64, 27, 51, 12, 55, 59, 7, 36, 56, 3, 22, 14, 30, 62, 35, 42, 49, 21, 53, 8, 44], 'cur_cost': 104776.0}, {'tour': array([20, 61, 32, 43, 48, 23, 29, 56, 40, 28, 64, 27, 22, 63, 49, 38, 19,
       25, 46, 65, 18, 57,  4, 60, 53,  1, 45, 58, 33,  3,  5, 54, 11, 26,
       51, 59, 13, 12, 39,  6, 35, 44, 17, 34, 24, 41, 62,  7,  2, 36, 16,
        0, 30, 21, 10, 52, 47, 37, 55, 15,  8, 50, 42, 14,  9, 31]), 'cur_cost': 111717.0}]
2025-06-22 18:36:59,814 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:36:59,814 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-06-22 18:36:59,814 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-22 18:36:59,814 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 18:36:59,814 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:36:59,815 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [34, 27, 46] and ending with [11, 45, 17]
- Cost: 99764.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:36:59,815 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:37:03,601 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    34, 27, 46, 52, 14, 3, 10, 49, 60, 22, 1, 36, 44, 57, 64, 5, 16, 28, 58, 25, 40, 19, 8, 55, 37, 62, 4, 12, 30, 23, 41, 51, 20, 32, 65, 6, 48, 13, 35, 54, 9, 2, 39, 50, 24, 42, 11, 45, 17, 59, 61, 0, 53, 38, 47, 15, 63, 21, 29, 31, 7, 33, 56, 43, 26, 18
  ],
  "modification_strategy": "Utilized a swap-based approach focusing on the first few nodes, and then distributed the remaining nodes to maximize exploration of potentially promising regions, while avoiding difficult regions. Implemented a random element with node reordering to generate a diverse path while prioritizing the provided starting and ending nodes.",
  "targeted_regions": "Focus on exploring opportunity regions identified in the search context. Aimed to weave through these areas while maintaining a balance between diversity and path quality. Avoided the identified difficult regions by strategically reordering nodes."
}
```
2025-06-22 18:37:03,601 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:37:03,601 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107388.0, 路径: [34, 27, 46, 52, 14, 3, 10, 49, 60, 22, 1, 36, 44, 57, 64, 5, 16, 28, 58, 25, 40, 19, 8, 55, 37, 62, 4, 12, 30, 23, 41, 51, 20, 32, 65, 6, 48, 13, 35, 54, 9, 2, 39, 50, 24, 42, 11, 45, 17, 59, 61, 0, 53, 38, 47, 15, 63, 21, 29, 31, 7, 33, 56, 43, 26, 18]
2025-06-22 18:37:03,601 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [34, 27, 46, 52, 14, 3, 10, 49, 60, 22, 1, 36, 44, 57, 64, 5, 16, 28, 58, 25, 40, 19, 8, 55, 37, 62, 4, 12, 30, 23, 41, 51, 20, 32, 65, 6, 48, 13, 35, 54, 9, 2, 39, 50, 24, 42, 11, 45, 17, 59, 61, 0, 53, 38, 47, 15, 63, 21, 29, 31, 7, 33, 56, 43, 26, 18], 'cur_cost': 107388.0}
2025-06-22 18:37:03,601 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 18:37:03,601 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:37:03,601 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:37:03,601 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 110238.0
2025-06-22 18:37:04,106 - ExploitationExpert - INFO - res_population_num: 33
2025-06-22 18:37:04,106 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521]
2025-06-22 18:37:04,106 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:37:04,123 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:37:04,123 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([45, 26, 50, 30, 54, 62, 11, 25, 28, 38, 58, 22, 60,  8, 47, 64,  1,
       37, 35, 24,  7, 21, 16,  4, 20, 36, 43, 49, 53, 23,  2, 61, 55, 41,
        6, 57, 40, 39,  5, 17, 14, 13, 27, 63, 18, 56, 42, 10,  9, 52, 31,
       32, 65, 59, 29, 19, 48,  0, 46, 12, 33, 15, 44, 51, 34,  3]), 'cur_cost': 102935.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': array([47, 39, 43, 37,  2, 61, 15, 28, 24, 16,  3, 48, 64, 46, 14,  0, 40,
       22, 57, 21, 31, 17, 12, 56, 33, 23, 52, 59,  4, 38, 58, 27,  8, 25,
       49, 18, 42,  9, 34, 30, 26,  1,  7, 13, 41, 53, 19, 60, 11,  6, 10,
        5, 36, 50, 20, 44, 54, 63, 55, 62, 35, 65, 32, 29, 45, 51]), 'cur_cost': 96336.0}, {'tour': [34, 27, 46, 52, 14, 3, 10, 49, 60, 22, 1, 36, 44, 57, 64, 5, 16, 28, 58, 25, 40, 19, 8, 55, 37, 62, 4, 12, 30, 23, 41, 51, 20, 32, 65, 6, 48, 13, 35, 54, 9, 2, 39, 50, 24, 42, 11, 45, 17, 59, 61, 0, 53, 38, 47, 15, 63, 21, 29, 31, 7, 33, 56, 43, 26, 18], 'cur_cost': 107388.0}, {'tour': array([17, 32, 65, 61,  1,  8, 11,  0,  6, 34,  7, 25, 39, 64, 16, 22, 55,
       36, 62, 19, 30,  4, 63, 23, 58, 57, 45, 40, 54, 44, 53, 18, 56, 37,
       42, 52, 51, 15, 41,  3, 10, 43, 59, 47, 12, 24, 20, 13, 60, 21, 46,
       14, 31, 26,  5, 27, 38, 29,  2, 33, 28, 50, 35,  9, 49, 48]), 'cur_cost': 110238.0}, {'tour': array([48, 45, 43,  2, 21, 32, 64, 42, 62, 63, 58, 31, 10, 65, 25, 49,  0,
        8,  3, 53, 17, 59, 57, 14, 20, 35, 24,  7, 15, 61, 40, 56, 46, 55,
       33, 23, 22, 13, 37, 30, 39, 11, 34, 29, 60, 18, 44, 41,  6,  1, 28,
        4,  9, 51, 50, 19, 38, 26, 54,  5, 16, 12, 52, 36, 27, 47]), 'cur_cost': 97165.0}, {'tour': array([64, 50,  7, 14, 43, 39, 55, 42, 37, 59, 35, 62, 51, 58, 18, 48, 23,
       20, 34, 57, 56, 12, 38, 19,  6, 65,  9, 63,  1, 31, 28, 60, 61, 54,
       44, 25, 26, 47, 27, 29, 22, 52, 46, 33, 30,  2, 32,  3, 21, 15,  0,
       16, 17, 49, 45, 13, 40, 36, 41, 53,  4,  8, 10,  5, 24, 11]), 'cur_cost': 99365.0}, {'tour': [43, 47, 38, 5, 61, 54, 23, 11, 32, 1, 4, 13, 2, 16, 39, 25, 10, 34, 50, 65, 18, 52, 31, 15, 41, 6, 58, 20, 48, 28, 60, 17, 29, 40, 63, 0, 57, 37, 33, 9, 45, 46, 26, 24, 19, 64, 27, 51, 12, 55, 59, 7, 36, 56, 3, 22, 14, 30, 62, 35, 42, 49, 21, 53, 8, 44], 'cur_cost': 104776.0}, {'tour': array([20, 61, 32, 43, 48, 23, 29, 56, 40, 28, 64, 27, 22, 63, 49, 38, 19,
       25, 46, 65, 18, 57,  4, 60, 53,  1, 45, 58, 33,  3,  5, 54, 11, 26,
       51, 59, 13, 12, 39,  6, 35, 44, 17, 34, 24, 41, 62,  7,  2, 36, 16,
        0, 30, 21, 10, 52, 47, 37, 55, 15,  8, 50, 42, 14,  9, 31]), 'cur_cost': 111717.0}]
2025-06-22 18:37:04,125 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:37:04,125 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-06-22 18:37:04,127 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 18:37:04,127 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 18:37:04,127 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:37:04,128 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [48, 45, 43] and ending with [36, 27, 47]
- Cost: 97165.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8092307692307692
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:37:04,128 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:37:23,046 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    48, 45, 43, 41, 40, 39, 38, 37, 36, 27, 47, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 6, 5, 4, 3, 2, 1, 0, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 42, 44, 46, 49
  ],
  "modification_strategy": "Heuristic path construction focusing on exploring unexplored regions by strategically changing the initial part and adding a reverse sweep through the end regions.",
  "targeted_regions": "Regions at the beginning and end of the original path, and potentially unexplored regions suggested by the search context."
}
```
2025-06-22 18:37:23,046 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:37:23,046 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15483.0, 路径: [48, 45, 43, 41, 40, 39, 38, 37, 36, 27, 47, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 6, 5, 4, 3, 2, 1, 0, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 42, 44, 46, 49]
2025-06-22 18:37:23,046 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [48, 45, 43, 41, 40, 39, 38, 37, 36, 27, 47, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 6, 5, 4, 3, 2, 1, 0, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 42, 44, 46, 49], 'cur_cost': 15483.0}
2025-06-22 18:37:23,046 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 18:37:23,048 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:37:23,048 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:37:23,048 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 109369.0
2025-06-22 18:37:23,550 - ExploitationExpert - INFO - res_population_num: 33
2025-06-22 18:37:23,550 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521]
2025-06-22 18:37:23,551 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:37:23,565 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:37:23,565 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([45, 26, 50, 30, 54, 62, 11, 25, 28, 38, 58, 22, 60,  8, 47, 64,  1,
       37, 35, 24,  7, 21, 16,  4, 20, 36, 43, 49, 53, 23,  2, 61, 55, 41,
        6, 57, 40, 39,  5, 17, 14, 13, 27, 63, 18, 56, 42, 10,  9, 52, 31,
       32, 65, 59, 29, 19, 48,  0, 46, 12, 33, 15, 44, 51, 34,  3]), 'cur_cost': 102935.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': array([47, 39, 43, 37,  2, 61, 15, 28, 24, 16,  3, 48, 64, 46, 14,  0, 40,
       22, 57, 21, 31, 17, 12, 56, 33, 23, 52, 59,  4, 38, 58, 27,  8, 25,
       49, 18, 42,  9, 34, 30, 26,  1,  7, 13, 41, 53, 19, 60, 11,  6, 10,
        5, 36, 50, 20, 44, 54, 63, 55, 62, 35, 65, 32, 29, 45, 51]), 'cur_cost': 96336.0}, {'tour': [34, 27, 46, 52, 14, 3, 10, 49, 60, 22, 1, 36, 44, 57, 64, 5, 16, 28, 58, 25, 40, 19, 8, 55, 37, 62, 4, 12, 30, 23, 41, 51, 20, 32, 65, 6, 48, 13, 35, 54, 9, 2, 39, 50, 24, 42, 11, 45, 17, 59, 61, 0, 53, 38, 47, 15, 63, 21, 29, 31, 7, 33, 56, 43, 26, 18], 'cur_cost': 107388.0}, {'tour': array([17, 32, 65, 61,  1,  8, 11,  0,  6, 34,  7, 25, 39, 64, 16, 22, 55,
       36, 62, 19, 30,  4, 63, 23, 58, 57, 45, 40, 54, 44, 53, 18, 56, 37,
       42, 52, 51, 15, 41,  3, 10, 43, 59, 47, 12, 24, 20, 13, 60, 21, 46,
       14, 31, 26,  5, 27, 38, 29,  2, 33, 28, 50, 35,  9, 49, 48]), 'cur_cost': 110238.0}, {'tour': [48, 45, 43, 41, 40, 39, 38, 37, 36, 27, 47, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 6, 5, 4, 3, 2, 1, 0, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 42, 44, 46, 49], 'cur_cost': 15483.0}, {'tour': array([31, 17, 46,  4, 41, 36,  3, 51, 65, 10,  5, 16, 63, 38, 29, 48, 49,
       43, 30, 14, 39,  0,  7, 44, 53, 15, 21, 24, 23, 54, 56, 25, 47, 64,
       33, 37, 40, 32, 60, 26, 19, 11, 52,  8, 50, 28, 61, 18,  1, 62, 34,
       42,  2, 22,  6, 58, 57, 12, 13, 55,  9, 20, 59, 45, 35, 27]), 'cur_cost': 109369.0}, {'tour': [43, 47, 38, 5, 61, 54, 23, 11, 32, 1, 4, 13, 2, 16, 39, 25, 10, 34, 50, 65, 18, 52, 31, 15, 41, 6, 58, 20, 48, 28, 60, 17, 29, 40, 63, 0, 57, 37, 33, 9, 45, 46, 26, 24, 19, 64, 27, 51, 12, 55, 59, 7, 36, 56, 3, 22, 14, 30, 62, 35, 42, 49, 21, 53, 8, 44], 'cur_cost': 104776.0}, {'tour': array([20, 61, 32, 43, 48, 23, 29, 56, 40, 28, 64, 27, 22, 63, 49, 38, 19,
       25, 46, 65, 18, 57,  4, 60, 53,  1, 45, 58, 33,  3,  5, 54, 11, 26,
       51, 59, 13, 12, 39,  6, 35, 44, 17, 34, 24, 41, 62,  7,  2, 36, 16,
        0, 30, 21, 10, 52, 47, 37, 55, 15,  8, 50, 42, 14,  9, 31]), 'cur_cost': 111717.0}]
2025-06-22 18:37:23,569 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:37:23,569 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-06-22 18:37:23,569 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 18:37:23,569 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 18:37:23,570 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 18:37:23,570 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [43, 47, 38] and ending with [53, 8, 44]
- Cost: 104776.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7492307692307693
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 18:37:23,570 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 18:37:27,965 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    43, 12, 57, 2, 18, 34, 6, 51, 28, 40, 64, 1, 30, 15, 59, 3, 35, 45, 50, 62, 21, 8, 44, 24, 11, 61, 27, 48, 32, 5, 14, 42, 54, 19, 29, 56, 9, 20, 39, 17, 46, 38, 26, 16, 65, 47, 33, 4, 53, 55, 10, 58, 31, 22, 0, 60, 25, 41, 37, 52, 13, 36, 23, 49, 63, 7
  ],
  "modification_strategy": "This strategy prioritizes diversity by constructing a path that significantly deviates from the provided one. It attempts to explore unexplored regions indicated in the search context. The path generation logic involves a more scattered node selection, aiming for a more random-looking traversal, while ensuring that the constraints of the TSP problem are respected.",
  "targeted_regions": "The path attempts to target the two opportunity regions and avoids the two difficult regions by creating a less structured path to explore previously unexplored areas. The high diversity weight encourages exploration."
}
```
2025-06-22 18:37:27,965 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 18:37:27,965 - ExplorationExpert - INFO - 探索路径生成完成，成本: 114920.0, 路径: [43, 12, 57, 2, 18, 34, 6, 51, 28, 40, 64, 1, 30, 15, 59, 3, 35, 45, 50, 62, 21, 8, 44, 24, 11, 61, 27, 48, 32, 5, 14, 42, 54, 19, 29, 56, 9, 20, 39, 17, 46, 38, 26, 16, 65, 47, 33, 4, 53, 55, 10, 58, 31, 22, 0, 60, 25, 41, 37, 52, 13, 36, 23, 49, 63, 7]
2025-06-22 18:37:27,965 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [43, 12, 57, 2, 18, 34, 6, 51, 28, 40, 64, 1, 30, 15, 59, 3, 35, 45, 50, 62, 21, 8, 44, 24, 11, 61, 27, 48, 32, 5, 14, 42, 54, 19, 29, 56, 9, 20, 39, 17, 46, 38, 26, 16, 65, 47, 33, 4, 53, 55, 10, 58, 31, 22, 0, 60, 25, 41, 37, 52, 13, 36, 23, 49, 63, 7], 'cur_cost': 114920.0}
2025-06-22 18:37:27,965 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 18:37:27,965 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 18:37:27,965 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 18:37:27,965 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 108252.0
2025-06-22 18:37:28,466 - ExploitationExpert - INFO - res_population_num: 33
2025-06-22 18:37:28,468 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9527, 9527, 9543, 9547, 9570, 101310, 9521, 9521]
2025-06-22 18:37:28,468 - ExploitationExpert - INFO - res_populations: [array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 16, 18, 23, 13, 21, 20, 14, 15, 22, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 31, 33, 35, 34, 30, 28, 32, 29, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 14, 15, 22, 12, 17, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 29, 11, 28, 49,  1, 25, 13, 31, 52, 10, 20, 45,  7,  2, 36, 41,
       58, 16, 48,  5, 38, 43,  4, 53, 54, 59,  6, 15, 46, 33, 26, 50, 51,
       27, 23, 57, 64, 37,  3, 24, 21, 60, 39, 42, 19, 65, 44, 35, 47, 32,
       22,  8, 17, 18, 63, 61, 40,  9, 14, 55, 62, 56, 30, 34, 12],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-22 18:37:28,482 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 18:37:28,482 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([45, 26, 50, 30, 54, 62, 11, 25, 28, 38, 58, 22, 60,  8, 47, 64,  1,
       37, 35, 24,  7, 21, 16,  4, 20, 36, 43, 49, 53, 23,  2, 61, 55, 41,
        6, 57, 40, 39,  5, 17, 14, 13, 27, 63, 18, 56, 42, 10,  9, 52, 31,
       32, 65, 59, 29, 19, 48,  0, 46, 12, 33, 15, 44, 51, 34,  3]), 'cur_cost': 102935.0}, {'tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}, {'tour': array([47, 39, 43, 37,  2, 61, 15, 28, 24, 16,  3, 48, 64, 46, 14,  0, 40,
       22, 57, 21, 31, 17, 12, 56, 33, 23, 52, 59,  4, 38, 58, 27,  8, 25,
       49, 18, 42,  9, 34, 30, 26,  1,  7, 13, 41, 53, 19, 60, 11,  6, 10,
        5, 36, 50, 20, 44, 54, 63, 55, 62, 35, 65, 32, 29, 45, 51]), 'cur_cost': 96336.0}, {'tour': [34, 27, 46, 52, 14, 3, 10, 49, 60, 22, 1, 36, 44, 57, 64, 5, 16, 28, 58, 25, 40, 19, 8, 55, 37, 62, 4, 12, 30, 23, 41, 51, 20, 32, 65, 6, 48, 13, 35, 54, 9, 2, 39, 50, 24, 42, 11, 45, 17, 59, 61, 0, 53, 38, 47, 15, 63, 21, 29, 31, 7, 33, 56, 43, 26, 18], 'cur_cost': 107388.0}, {'tour': array([17, 32, 65, 61,  1,  8, 11,  0,  6, 34,  7, 25, 39, 64, 16, 22, 55,
       36, 62, 19, 30,  4, 63, 23, 58, 57, 45, 40, 54, 44, 53, 18, 56, 37,
       42, 52, 51, 15, 41,  3, 10, 43, 59, 47, 12, 24, 20, 13, 60, 21, 46,
       14, 31, 26,  5, 27, 38, 29,  2, 33, 28, 50, 35,  9, 49, 48]), 'cur_cost': 110238.0}, {'tour': [48, 45, 43, 41, 40, 39, 38, 37, 36, 27, 47, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 6, 5, 4, 3, 2, 1, 0, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 42, 44, 46, 49], 'cur_cost': 15483.0}, {'tour': array([31, 17, 46,  4, 41, 36,  3, 51, 65, 10,  5, 16, 63, 38, 29, 48, 49,
       43, 30, 14, 39,  0,  7, 44, 53, 15, 21, 24, 23, 54, 56, 25, 47, 64,
       33, 37, 40, 32, 60, 26, 19, 11, 52,  8, 50, 28, 61, 18,  1, 62, 34,
       42,  2, 22,  6, 58, 57, 12, 13, 55,  9, 20, 59, 45, 35, 27]), 'cur_cost': 109369.0}, {'tour': [43, 12, 57, 2, 18, 34, 6, 51, 28, 40, 64, 1, 30, 15, 59, 3, 35, 45, 50, 62, 21, 8, 44, 24, 11, 61, 27, 48, 32, 5, 14, 42, 54, 19, 29, 56, 9, 20, 39, 17, 46, 38, 26, 16, 65, 47, 33, 4, 53, 55, 10, 58, 31, 22, 0, 60, 25, 41, 37, 52, 13, 36, 23, 49, 63, 7], 'cur_cost': 114920.0}, {'tour': array([56, 27, 58, 57, 43, 12, 54, 13,  0, 62, 19,  8, 22,  2, 24, 46,  9,
       31, 47, 61, 49, 40, 30, 11, 14, 17, 48, 23, 25, 53, 35,  1, 41,  4,
       33,  6, 16, 65, 44, 55, 52, 51, 39, 34, 29, 38, 36, 63, 15, 28, 37,
       50, 45, 18, 10, 60, 20,  7,  3, 59, 64, 32, 26, 42, 21,  5]), 'cur_cost': 108252.0}]
2025-06-22 18:37:28,485 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 18:37:28,486 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-06-22 18:37:28,486 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 18:37:28,486 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [12, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 11461.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [34, 27, 46, 52, 14, 3, 10, 49, 60, 22, 1, 36, 44, 57, 64, 5, 16, 28, 58, 25, 40, 19, 8, 55, 37, 62, 4, 12, 30, 23, 41, 51, 20, 32, 65, 6, 48, 13, 35, 54, 9, 2, 39, 50, 24, 42, 11, 45, 17, 59, 61, 0, 53, 38, 47, 15, 63, 21, 29, 31, 7, 33, 56, 43, 26, 18], 'cur_cost': 107388.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [48, 45, 43, 41, 40, 39, 38, 37, 36, 27, 47, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 6, 5, 4, 3, 2, 1, 0, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 42, 44, 46, 49], 'cur_cost': 15483.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [43, 12, 57, 2, 18, 34, 6, 51, 28, 40, 64, 1, 30, 15, 59, 3, 35, 45, 50, 62, 21, 8, 44, 24, 11, 61, 27, 48, 32, 5, 14, 42, 54, 19, 29, 56, 9, 20, 39, 17, 46, 38, 26, 16, 65, 47, 33, 4, 53, 55, 10, 58, 31, 22, 0, 60, 25, 41, 37, 52, 13, 36, 23, 49, 63, 7], 'cur_cost': 114920.0}}]
2025-06-22 18:37:28,486 - __main__ - INFO - 进化阶段完成
2025-06-22 18:37:28,486 - __main__ - INFO - 开始评估阶段
2025-06-22 18:37:28,486 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 18:37:28,487 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 18:37:28,488 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 4/5
- Population changes:
  * Cost: min 11461.0->11461.0, mean 82651.7->78786.1
  * Diversity: 0.8015384615384615->0.74
  * Elite solutions: 1->2

## Strategy Performance:
- Explore: 5 individuals, success rate 40.0%, avg improvement 18567.2
- Exploit: 5 individuals, success rate 60.0%, avg improvement -10836.0
- Overall improvement: 0.0

## Historical Trends:
Last 3 iterations: costs [11458.0, 13176.0, 11461.0], diversity [0.9562289562289561, 0.9683501683501683, 0.9454545454545454]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 18:37:33,711 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Refinement/Near Convergence",
    "features": "The cost is showing minimal change, plateauing at the minimum. Diversity is decreasing, and the population is possibly converging to a local optimum. The emergence of a second elite solution hints at potential stability, but also less room for improvement."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Moderate (40%)",
      "avg_improvement": "Significant positive improvement (18567.2).",
      "evaluation": "Exploration is still finding beneficial solutions, suggesting there are still areas of the search space that are promising."
    },
    "exploit": {
      "success_rate": "Good (60%)",
      "avg_improvement": "Negative improvement (-10836.0).",
      "evaluation": "Exploitation is failing, on average, to improve on existing solutions. This points towards the algorithm being near a local optimum, or the exploitation phase not focusing on promising areas. Potentially the exploitation is too aggressive and over-optimizing on current solutions at the expense of broader improvements."
    },
    "overall": {
      "improvement": "Zero overall improvement. This means a balancing problem is occurring, where benefits from explore are lost through exploit."
    }
  },
  "balance_state": {
    "assessment": "The balance leans towards over-exploitation at this point. The exploration is having success and finding new solutions. However, the exploitation phase isn't consistently improving, suggesting it might be too focused on areas that have been thoroughly explored.",
    "adjustment_needs": "Increase exploration and reduce over-reliance on exploitation, given the stagnation in cost. Consider increasing the exploration rate and decreasing the exploitation rate."
  },
  "recommendations": {
    "next_iteration": [
      "**Increase the number of exploration individuals:** Increase the number of individuals using the explore strategy to perhaps 7 or 8, while reducing the exploit individuals.",
      "**Refine the exploitation strategy:** Adjust the exploitation strategy to be less aggressive or potentially introduce diversity to the exploitation individuals. Consider if exploitation should be focused on the solutions found by exploration.",
      "**Monitor Diversity Closely:** Keep a close eye on diversity. If it continues to decrease, it's a sign of convergence and may be necessary to introduce a more disruptive exploration strategy. Perhaps explore mutations that result in wider search space movements.",
      "**Consider Elite Solution Selection:** Examine the two elite solutions. Do they offer fundamentally different approaches? If they are similar, it confirms convergence to a local area, and more aggressive exploration is needed.",
      "**Analyze Exploration's Successes:** Investigate the characteristics of solutions found by exploration. Are there any patterns? Can you use this information to guide exploitation, such as focusing exploitation around the most promising areas found in exploration?",
      "**Iterate and Observe:** Continuously observe and refine the balance between exploration and exploitation based on the evolution's progress. Evaluate the impacts of changes in the next iteration."
    ]
  }
}
```
2025-06-22 18:37:33,739 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 18:37:33,741 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Refinement/Near Convergence",
    "features": "The cost is showing minimal change, plateauing at the minimum. Diversity is decreasing, and the population is possibly converging to a local optimum. The emergence of a second elite solution hints at potential stability, but also less room for improvement."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Moderate (40%)",
      "avg_improvement": "Significant positive improvement (18567.2).",
      "evaluation": "Exploration is still finding beneficial solutions, suggesting there are still areas of the search space that are promising."
    },
    "exploit": {
      "success_rate": "Good (60%)",
      "avg_improvement": "Negative improvement (-10836.0).",
      "evaluation": "Exploitation is failing, on average, to improve on existing solutions. This points towards the algorithm being near a local optimum, or the exploitation phase not focusing on promising areas. Potentially the exploitation is too aggressive and over-optimizing on current solutions at the expense of broader improvements."
    },
    "overall": {
      "improvement": "Zero overall improvement. This means a balancing problem is occurring, where benefits from explore are lost through exploit."
    }
  },
  "balance_state": {
    "assessment": "The balance leans towards over-exploitation at this point. The exploration is having success and finding new solutions. However, the exploitation phase isn't consistently improving, suggesting it might be too focused on areas that have been thoroughly explored.",
    "adjustment_needs": "Increase exploration and reduce over-reliance on exploitation, given the stagnation in cost. Consider increasing the exploration rate and decreasing the exploitation rate."
  },
  "recommendations": {
    "next_iteration": [
      "**Increase the number of exploration individuals:** Increase the number of individuals using the explore strategy to perhaps 7 or 8, while reducing the exploit individuals.",
      "**Refine the exploitation strategy:** Adjust the exploitation strategy to be less aggressive or potentially introduce diversity to the exploitation individuals. Consider if exploitation should be focused on the solutions found by exploration.",
      "**Monitor Diversity Closely:** Keep a close eye on diversity. If it continues to decrease, it's a sign of convergence and may be necessary to introduce a more disruptive exploration strategy. Perhaps explore mutations that result in wider search space movements.",
      "**Consider Elite Solution Selection:** Examine the two elite solutions. Do they offer fundamentally different approaches? If they are similar, it confirms convergence to a local area, and more aggressive exploration is needed.",
      "**Analyze Exploration's Successes:** Investigate the characteristics of solutions found by exploration. Are there any patterns? Can you use this information to guide exploitation, such as focusing exploitation around the most promising areas found in exploration?",
      "**Iterate and Observe:** Continuously observe and refine the balance between exploration and exploitation based on the evolution's progress. Evaluate the impacts of changes in the next iteration."
    ]
  }
}
```
2025-06-22 18:37:33,741 - __main__ - INFO - 评估阶段完成
2025-06-22 18:37:33,742 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Refinement/Near Convergence",
    "features": "The cost is showing minimal change, plateauing at the minimum. Diversity is decreasing, and the population is possibly converging to a local optimum. The emergence of a second elite solution hints at potential stability, but also less room for improvement."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Moderate (40%)",
      "avg_improvement": "Significant positive improvement (18567.2).",
      "evaluation": "Exploration is still finding beneficial solutions, suggesting there are still areas of the search space that are promising."
    },
    "exploit": {
      "success_rate": "Good (60%)",
      "avg_improvement": "Negative improvement (-10836.0).",
      "evaluation": "Exploitation is failing, on average, to improve on existing solutions. This points towards the algorithm being near a local optimum, or the exploitation phase not focusing on promising areas. Potentially the exploitation is too aggressive and over-optimizing on current solutions at the expense of broader improvements."
    },
    "overall": {
      "improvement": "Zero overall improvement. This means a balancing problem is occurring, where benefits from explore are lost through exploit."
    }
  },
  "balance_state": {
    "assessment": "The balance leans towards over-exploitation at this point. The exploration is having success and finding new solutions. However, the exploitation phase isn't consistently improving, suggesting it might be too focused on areas that have been thoroughly explored.",
    "adjustment_needs": "Increase exploration and reduce over-reliance on exploitation, given the stagnation in cost. Consider increasing the exploration rate and decreasing the exploitation rate."
  },
  "recommendations": {
    "next_iteration": [
      "**Increase the number of exploration individuals:** Increase the number of individuals using the explore strategy to perhaps 7 or 8, while reducing the exploit individuals.",
      "**Refine the exploitation strategy:** Adjust the exploitation strategy to be less aggressive or potentially introduce diversity to the exploitation individuals. Consider if exploitation should be focused on the solutions found by exploration.",
      "**Monitor Diversity Closely:** Keep a close eye on diversity. If it continues to decrease, it's a sign of convergence and may be necessary to introduce a more disruptive exploration strategy. Perhaps explore mutations that result in wider search space movements.",
      "**Consider Elite Solution Selection:** Examine the two elite solutions. Do they offer fundamentally different approaches? If they are similar, it confirms convergence to a local area, and more aggressive exploration is needed.",
      "**Analyze Exploration's Successes:** Investigate the characteristics of solutions found by exploration. Are there any patterns? Can you use this information to guide exploitation, such as focusing exploitation around the most promising areas found in exploration?",
      "**Iterate and Observe:** Continuously observe and refine the balance between exploration and exploitation based on the evolution's progress. Evaluate the impacts of changes in the next iteration."
    ]
  }
}
```
2025-06-22 18:37:33,742 - __main__ - INFO - 当前最佳适应度: 11461.0
2025-06-22 18:37:33,744 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_4.pkl
2025-06-22 18:37:33,762 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_solution.json
2025-06-22 18:37:33,762 - __main__ - INFO - 实例 composite13_66 处理完成
