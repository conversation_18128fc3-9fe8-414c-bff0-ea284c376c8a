2025-07-04 10:16:07,863 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-04 10:16:07,863 - __main__ - INFO - 开始分析阶段
2025-07-04 10:16:07,863 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:16:07,886 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9971.0, 'max': 113611.0, 'mean': 76528.3, 'std': 43730.057143914186}, 'diversity': 0.9252525252525252, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:16:07,887 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9971.0, 'max': 113611.0, 'mean': 76528.3, 'std': 43730.057143914186}, 'diversity_level': 0.9252525252525252, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-04 10:16:07,896 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-04 10:16:07,896 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-04 10:16:07,897 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:16:07,903 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:16:07,903 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (26, 36, 35), 'frequency': 0.3}, {'subpath': (36, 35, 28), 'frequency': 0.3}, {'subpath': (35, 28, 30), 'frequency': 0.3}, {'subpath': (28, 30, 34), 'frequency': 0.3}, {'subpath': (30, 34, 33), 'frequency': 0.3}, {'subpath': (34, 33, 31), 'frequency': 0.3}, {'subpath': (33, 31, 24), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(40, 49)', 'frequency': 0.4}, {'edge': '(30, 34)', 'frequency': 0.4}, {'edge': '(24, 29)', 'frequency': 0.4}, {'edge': '(3, 32)', 'frequency': 0.4}, {'edge': '(0, 10)', 'frequency': 0.4}, {'edge': '(55, 61)', 'frequency': 0.4}, {'edge': '(56, 58)', 'frequency': 0.4}, {'edge': '(58, 60)', 'frequency': 0.4}, {'edge': '(54, 57)', 'frequency': 0.4}, {'edge': '(41, 50)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(46, 48)', 'frequency': 0.3}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(13, 20)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(12, 18)', 'frequency': 0.3}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.2}, {'edge': '(14, 15)', 'frequency': 0.2}, {'edge': '(14, 17)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 7)', 'frequency': 0.3}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(5, 9)', 'frequency': 0.2}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(0, 55)', 'frequency': 0.2}, {'edge': '(53, 61)', 'frequency': 0.2}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.2}, {'edge': '(54, 65)', 'frequency': 0.2}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(19, 21)', 'frequency': 0.2}, {'edge': '(16, 19)', 'frequency': 0.2}, {'edge': '(22, 23)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.2}, {'edge': '(38, 45)', 'frequency': 0.2}, {'edge': '(50, 51)', 'frequency': 0.2}, {'edge': '(41, 46)', 'frequency': 0.2}, {'edge': '(46, 47)', 'frequency': 0.2}, {'edge': '(54, 64)', 'frequency': 0.2}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(6, 9)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.3}, {'edge': '(10, 15)', 'frequency': 0.2}, {'edge': '(22, 30)', 'frequency': 0.2}, {'edge': '(43, 49)', 'frequency': 0.2}, {'edge': '(2, 24)', 'frequency': 0.2}, {'edge': '(8, 37)', 'frequency': 0.2}, {'edge': '(21, 59)', 'frequency': 0.2}, {'edge': '(14, 18)', 'frequency': 0.2}, {'edge': '(31, 42)', 'frequency': 0.2}, {'edge': '(58, 64)', 'frequency': 0.2}, {'edge': '(51, 56)', 'frequency': 0.2}, {'edge': '(16, 50)', 'frequency': 0.2}, {'edge': '(28, 45)', 'frequency': 0.2}, {'edge': '(6, 62)', 'frequency': 0.2}, {'edge': '(2, 62)', 'frequency': 0.2}, {'edge': '(27, 29)', 'frequency': 0.2}, {'edge': '(12, 36)', 'frequency': 0.2}, {'edge': '(24, 40)', 'frequency': 0.3}, {'edge': '(7, 14)', 'frequency': 0.2}, {'edge': '(14, 63)', 'frequency': 0.2}, {'edge': '(13, 54)', 'frequency': 0.2}, {'edge': '(53, 57)', 'frequency': 0.2}, {'edge': '(9, 41)', 'frequency': 0.2}, {'edge': '(20, 27)', 'frequency': 0.2}, {'edge': '(26, 60)', 'frequency': 0.2}, {'edge': '(2, 60)', 'frequency': 0.2}, {'edge': '(38, 43)', 'frequency': 0.2}, {'edge': '(3, 13)', 'frequency': 0.2}, {'edge': '(25, 57)', 'frequency': 0.2}, {'edge': '(8, 39)', 'frequency': 0.2}, {'edge': '(19, 35)', 'frequency': 0.2}, {'edge': '(52, 55)', 'frequency': 0.2}, {'edge': '(24, 28)', 'frequency': 0.2}, {'edge': '(29, 55)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [54, 32, 57, 39, 53, 46, 2], 'cost': 16958.0, 'size': 7}, {'region': [42, 62, 37, 60, 38, 31], 'cost': 13683.0, 'size': 6}, {'region': [25, 52, 43, 58, 32, 46], 'cost': 13663.0, 'size': 6}, {'region': [55, 34, 65, 51, 3], 'cost': 11297.0, 'size': 5}, {'region': [50, 0, 48, 55, 25], 'cost': 10787.0, 'size': 5}]}
2025-07-04 10:16:07,903 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-04 10:16:07,903 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-04 10:16:07,905 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:16:07,905 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-04 10:16:07,905 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-07-04 10:16:07,905 - __main__ - INFO - 更新共享数据: elite_features
2025-07-04 10:16:07,905 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:16:08,374 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:16:08,374 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9971.0, mean 76528.3, max 113611.0, std 43730.057143914186
- diversity: 0.9252525252525252
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: [{'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [54, 32, 57, 39, 53, 46, 2], 'cost': 16958.0, 'size': 7}, {'region': [42, 62, 37, 60, 38, 31], 'cost': 13683.0, 'size': 6}, {'region': [25, 52, 43, 58, 32, 46], 'cost': 13663.0, 'size': 6}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state: (Object. MUST contain a numerical 'diversity_score' key alongside any textual descriptions)
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "balance"}
}
```

2025-07-04 10:16:08,374 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:16:12,662 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.6,
    "modality": "multi",
    "deceptiveness": "potentially high"
  },
  "population_state": {
    "diversity_score": 0.925,
    "description": "High diversity, indicating exploration is still ongoing. Convergence is 0, suggesting the algorithm hasn't settled on a good solution yet."
  },
  "difficult_regions": [
    {
      "region_indices": [43, 58, 32, 46, 25, 52],
      "reason": "Identified as difficult based on structural signal - difficult_regions_sample"
    },
    {
      "region_indices": [54, 32, 57, 39, 53, 46, 2],
      "reason": "Identified as difficult based on structural signal - difficult_regions_sample"
    },
    {
      "region_indices": [42, 62, 37, 60, 38, 31],
      "reason": "Identified as difficult based on structural signal - difficult_regions_sample"
    },
    {
      "region_indices": [],
      "reason": "Long-edge corridors could exist.  Further investigation is needed."
    }
  ],
  "opportunity_regions": [
    {
      "region_indices": [],
      "reason": "The density grid [14, 0, 0], [0, 12, 12], [14, 0, 14] shows the potential for opportunities.  Focus should be on edges and routes within the more densely clustered areas, though no regions were identified."
    }
  ],
  "evolution_phase": "Exploration",
  "evolution_direction": {
    "recommended_focus": "Exploration and Exploitation. Balance between improving current solutions, while continuing to explore diverse solutions. Given the diversity, it's important to now focus on exploiting promising regions and leveraging subpath reuse."
  }
}
```
2025-07-04 10:16:12,662 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:16:12,662 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 0.6, 'modality': 'multi', 'deceptiveness': 'potentially high'}, 'population_state': {'diversity_score': 0.925, 'description': "High diversity, indicating exploration is still ongoing. Convergence is 0, suggesting the algorithm hasn't settled on a good solution yet."}, 'difficult_regions': [{'region_indices': [43, 58, 32, 46, 25, 52], 'reason': 'Identified as difficult based on structural signal - difficult_regions_sample'}, {'region_indices': [54, 32, 57, 39, 53, 46, 2], 'reason': 'Identified as difficult based on structural signal - difficult_regions_sample'}, {'region_indices': [42, 62, 37, 60, 38, 31], 'reason': 'Identified as difficult based on structural signal - difficult_regions_sample'}, {'region_indices': [], 'reason': 'Long-edge corridors could exist.  Further investigation is needed.'}], 'opportunity_regions': [{'region_indices': [], 'reason': 'The density grid [14, 0, 0], [0, 12, 12], [14, 0, 14] shows the potential for opportunities.  Focus should be on edges and routes within the more densely clustered areas, though no regions were identified.'}], 'evolution_phase': 'Exploration', 'evolution_direction': {'recommended_focus': "Exploration and Exploitation. Balance between improving current solutions, while continuing to explore diverse solutions. Given the diversity, it's important to now focus on exploiting promising regions and leveraging subpath reuse."}}
2025-07-04 10:16:12,662 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-04 10:16:12,662 - __main__ - INFO - 分析阶段完成
2025-07-04 10:16:12,662 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.6, 'modality': 'multi', 'deceptiveness': 'potentially high'}, 'population_state': {'diversity_score': 0.925, 'description': "High diversity, indicating exploration is still ongoing. Convergence is 0, suggesting the algorithm hasn't settled on a good solution yet."}, 'difficult_regions': [{'region_indices': [43, 58, 32, 46, 25, 52], 'reason': 'Identified as difficult based on structural signal - difficult_regions_sample'}, {'region_indices': [54, 32, 57, 39, 53, 46, 2], 'reason': 'Identified as difficult based on structural signal - difficult_regions_sample'}, {'region_indices': [42, 62, 37, 60, 38, 31], 'reason': 'Identified as difficult based on structural signal - difficult_regions_sample'}, {'region_indices': [], 'reason': 'Long-edge corridors could exist.  Further investigation is needed.'}], 'opportunity_regions': [{'region_indices': [], 'reason': 'The density grid [14, 0, 0], [0, 12, 12], [14, 0, 14] shows the potential for opportunities.  Focus should be on edges and routes within the more densely clustered areas, though no regions were identified.'}], 'evolution_phase': 'Exploration', 'evolution_direction': {'recommended_focus': "Exploration and Exploitation. Balance between improving current solutions, while continuing to explore diverse solutions. Given the diversity, it's important to now focus on exploiting promising regions and leveraging subpath reuse."}}
2025-07-04 10:16:12,662 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:16:12,662 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:16:12,662 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.925
  • best_cost: 9971.0
  • mean_cost: 76528.3
- Search-space: ruggedness 0.6, modality multi, deceptiveness potentially high
- Evolution phase: Exploration
- Landscape focus suggestion: Exploration and Exploitation. Balance between improving current solutions, while continuing to explore diverse solutions. Given the diversity, it's important to now focus on exploiting promising regions and leveraging subpath reuse.
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 4): 4 items, first 2: [{'region_indices': [43, 58, 32, 46, 25, 52], 'reason': 'Identified as difficult based on structural signal - difficult_regions_sample'}, {'region_indices': [54, 32, 57, 39, 53, 46, 2], 'reason': 'Identified as difficult based on structural signal - difficult_regions_sample'}]
- Opportunity regions (# 1): [{'region_indices': [], 'reason': 'The density grid [14, 0, 0], [0, 12, 12], [14, 0, 14] shows the potential for opportunities.  Focus should be on edges and routes within the more densely clustered areas, though no regions were identified.'}]
- Last-iteration feedback: N/A

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:16:12,662 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:16:12,662 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:16:14,948 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.5,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity suggests a balance. Focus on both exploiting promising regions and exploring new areas. Split population 50/50."
}
```
2025-07-04 10:16:14,949 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-04 10:16:14,949 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-04 10:16:14,949 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.5,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity suggests a balance. Focus on both exploiting promising regions and exploring new areas. Split population 50/50."
}
```
2025-07-04 10:16:14,949 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:16:14,949 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-04 10:16:14,949 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.5,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity suggests a balance. Focus on both exploiting promising regions and exploring new areas. Split population 50/50."
}
```
2025-07-04 10:16:14,949 - __main__ - INFO - 开始进化阶段
2025-07-04 10:16:14,949 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:16:14,949 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:16:14,949 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9971.0 (rank 0.0%)
- Population diversity: 0.7615384615384615
- Evolution phase: Exploration
- Landscape recommends focus: Exploration and Exploitation. Balance between improving current solutions, while continuing to explore diverse solutions. Given the diversity, it's important to now focus on exploiting promising regions and leveraging subpath reuse.
- Opportunity regions (sample): {'region_indice
- Difficult regions to avoid (sample): {'region_indice, {'region_indice...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:16:14,949 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:16:14,949 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:16:17,836 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2],
  "targeted_regions": "traverse full boundary and uses initial nodes",
  "strategy_comment": "Exploration with a complete boundary traversal for diversity and potential subpath reuse."
}
```
2025-07-04 10:16:17,836 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:16:17,836 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11494.0, 路径: [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2]
2025-07-04 10:16:17,836 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}
2025-07-04 10:16:17,836 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:16:17,839 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:16:17,839 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9984.0 (rank 0.0%)
- Population diversity: 0.8107692307692308
- Evolution phase: Exploration
- Landscape recommends focus: Exploration and Exploitation. Balance between improving current solutions, while continuing to explore diverse solutions. Given the diversity, it's important to now focus on exploiting promising regions and leveraging subpath reuse.
- Opportunity regions (sample): {'region_indice
- Difficult regions to avoid (sample): {'region_indice, {'region_indice...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:16:17,839 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:16:17,839 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:16:20,671 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited",
  "strategy_comment": "Simple path covering all nodes in a linear order for exploration."
}
```
2025-07-04 10:16:20,671 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:16:20,671 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:16:20,671 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:16:20,674 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:16:20,674 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:16:20,674 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10079.0 (rank 0.0%)
- Population diversity: 0.7892307692307692
- Evolution phase: Exploration
- Landscape recommends focus: Exploration and Exploitation. Balance between improving current solutions, while continuing to explore diverse solutions. Given the diversity, it's important to now focus on exploiting promising regions and leveraging subpath reuse.
- Opportunity regions (sample): {'region_indice
- Difficult regions to avoid (sample): {'region_indice, {'region_indice...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:16:20,674 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:16:20,674 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:16:23,672 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes, leveraging adjacency.",
  "strategy_comment": "Basic path construction, hits all nodes to provide a baseline for diversity and exploration of solution space."
}
```
2025-07-04 10:16:23,672 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:16:23,672 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 10:16:23,672 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 10:16:23,672 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-04 10:16:23,672 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:16:23,698 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:16:23,699 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 119648.0
2025-07-04 10:16:26,064 - ExploitationExpert - INFO - res_population_num: 1
2025-07-04 10:16:26,064 - ExploitationExpert - INFO - res_population_costs: [94768]
2025-07-04 10:16:26,064 - ExploitationExpert - INFO - res_populations: [array([ 0, 48, 55, 25, 28,  3, 11, 20, 19, 27, 29, 44, 38, 65, 62, 61, 15,
       60, 12,  7, 34, 30, 22,  6,  1, 46, 49, 43, 41,  4, 45, 36, 57, 54,
        2, 24, 32,  5, 33, 13, 17, 40, 23, 47,  8, 37, 53, 63, 35, 26, 21,
       59, 18, 14, 39,  9, 31, 42, 58, 64, 10, 52, 51, 56, 16, 50],
      dtype=int64)]
2025-07-04 10:16:26,074 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:16:26,074 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([50, 16,  5, 25, 64, 38,  6, 40, 43, 59, 14, 13, 15, 36, 65, 32, 17,
       34, 58,  8, 29, 57,  3, 33, 62, 30, 51, 53,  2, 46, 56, 61, 60, 22,
       42, 20, 52, 41, 24, 48, 45, 44,  4, 39, 11, 54, 12,  1, 31, 10, 49,
       27,  7, 47,  9, 18, 23,  0, 19, 35, 37, 55, 26, 63, 28, 21]), 'cur_cost': 119648.0}, {'tour': [17, 11, 28, 45, 23, 22, 52, 64, 58, 60, 44, 56, 21, 18, 8, 61, 55, 34, 65, 51, 3, 32, 42, 31, 6, 62, 2, 49, 27, 29, 47, 30, 43, 50, 41, 46, 57, 33, 4, 0, 12, 36, 39, 40, 24, 37, 38, 25, 1, 35, 59, 15, 16, 19, 7, 14, 63, 13, 54, 26, 48, 10, 9, 20, 5, 53], 'cur_cost': 100723.0}, {'tour': [24, 57, 53, 6, 4, 14, 18, 41, 9, 30, 48, 11, 62, 58, 44, 5, 51, 55, 56, 61, 49, 40, 1, 27, 20, 35, 17, 26, 60, 2, 42, 22, 32, 19, 0, 36, 7, 64, 29, 38, 43, 10, 65, 47, 63, 54, 16, 39, 12, 34, 28, 13, 3, 50, 31, 21, 59, 46, 15, 37, 8, 23, 33, 25, 45, 52], 'cur_cost': 105099.0}, {'tour': [3, 56, 45, 28, 17, 51, 49, 37, 5, 14, 36, 12, 58, 10, 54, 8, 62, 6, 39, 46, 30, 44, 27, 15, 55, 38, 43, 16, 64, 31, 65, 23, 18, 50, 35, 53, 52, 48, 33, 19, 9, 41, 63, 59, 32, 7, 22, 34, 21, 11, 42, 4, 20, 61, 47, 1, 0, 25, 57, 40, 24, 29, 26, 60, 2, 13], 'cur_cost': 108817.0}, {'tour': [3, 26, 43, 11, 31, 41, 65, 42, 49, 28, 15, 30, 22, 48, 18, 12, 53, 44, 13, 54, 0, 10, 1, 5, 38, 33, 61, 14, 63, 57, 25, 34, 40, 59, 60, 64, 45, 47, 24, 2, 27, 20, 36, 16, 23, 4, 21, 51, 7, 62, 17, 37, 9, 6, 50, 39, 8, 56, 58, 46, 29, 35, 19, 55, 52, 32], 'cur_cost': 98234.0}, {'tour': [0, 49, 26, 37, 33, 56, 62, 2, 11, 15, 4, 51, 63, 28, 24, 40, 65, 19, 35, 23, 38, 9, 53, 57, 18, 36, 34, 1, 7, 14, 20, 22, 61, 45, 21, 47, 5, 42, 44, 17, 16, 41, 50, 30, 60, 39, 8, 10, 13, 64, 54, 29, 55, 12, 59, 31, 25, 52, 43, 58, 32, 46, 48, 3, 27, 6], 'cur_cost': 102453.0}, {'tour': [30, 65, 8, 36, 28, 24, 49, 43, 19, 25, 40, 48, 17, 6, 44, 7, 41, 64, 56, 51, 23, 58, 20, 0, 1, 18, 61, 34, 42, 62, 37, 60, 38, 31, 22, 5, 3, 59, 26, 11, 45, 33, 35, 13, 27, 29, 55, 52, 12, 9, 16, 50, 10, 15, 47, 4, 54, 32, 57, 39, 53, 46, 2, 14, 21, 63], 'cur_cost': 113611.0}]
2025-07-04 10:16:26,074 - ExploitationExpert - INFO - 局部搜索耗时: 2.38秒
2025-07-04 10:16:26,075 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-04 10:16:26,075 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-04 10:16:26,075 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-04 10:16:26,075 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:16:26,075 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:16:26,076 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 119190.0
2025-07-04 10:16:26,960 - ExploitationExpert - INFO - res_population_num: 2
2025-07-04 10:16:26,964 - ExploitationExpert - INFO - res_population_costs: [94768, 9575]
2025-07-04 10:16:26,964 - ExploitationExpert - INFO - res_populations: [array([ 0, 48, 55, 25, 28,  3, 11, 20, 19, 27, 29, 44, 38, 65, 62, 61, 15,
       60, 12,  7, 34, 30, 22,  6,  1, 46, 49, 43, 41,  4, 45, 36, 57, 54,
        2, 24, 32,  5, 33, 13, 17, 40, 23, 47,  8, 37, 53, 63, 35, 26, 21,
       59, 18, 14, 39,  9, 31, 42, 58, 64, 10, 52, 51, 56, 16, 50],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       47, 49, 40, 43, 48, 46, 45, 44, 41, 38, 51, 50, 42, 34, 30, 28, 35,
       25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 18, 16, 19, 21, 20, 13, 23,
       22, 12, 17, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:16:26,964 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:16:26,964 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([50, 16,  5, 25, 64, 38,  6, 40, 43, 59, 14, 13, 15, 36, 65, 32, 17,
       34, 58,  8, 29, 57,  3, 33, 62, 30, 51, 53,  2, 46, 56, 61, 60, 22,
       42, 20, 52, 41, 24, 48, 45, 44,  4, 39, 11, 54, 12,  1, 31, 10, 49,
       27,  7, 47,  9, 18, 23,  0, 19, 35, 37, 55, 26, 63, 28, 21]), 'cur_cost': 119648.0}, {'tour': array([54, 12, 51, 14, 59, 43, 49, 11, 16, 21, 64, 19, 65, 13, 31, 20,  4,
        7, 33, 24, 44, 55, 39, 36, 48,  3, 61,  2, 18, 42, 40, 56, 27, 62,
        0, 28, 53, 29, 32, 50, 52, 15, 38, 30, 60, 45, 25, 57, 22, 10, 17,
       41, 46,  8, 37,  6, 23,  9, 63, 34,  1, 26, 58,  5, 35, 47]), 'cur_cost': 119190.0}, {'tour': [24, 57, 53, 6, 4, 14, 18, 41, 9, 30, 48, 11, 62, 58, 44, 5, 51, 55, 56, 61, 49, 40, 1, 27, 20, 35, 17, 26, 60, 2, 42, 22, 32, 19, 0, 36, 7, 64, 29, 38, 43, 10, 65, 47, 63, 54, 16, 39, 12, 34, 28, 13, 3, 50, 31, 21, 59, 46, 15, 37, 8, 23, 33, 25, 45, 52], 'cur_cost': 105099.0}, {'tour': [3, 56, 45, 28, 17, 51, 49, 37, 5, 14, 36, 12, 58, 10, 54, 8, 62, 6, 39, 46, 30, 44, 27, 15, 55, 38, 43, 16, 64, 31, 65, 23, 18, 50, 35, 53, 52, 48, 33, 19, 9, 41, 63, 59, 32, 7, 22, 34, 21, 11, 42, 4, 20, 61, 47, 1, 0, 25, 57, 40, 24, 29, 26, 60, 2, 13], 'cur_cost': 108817.0}, {'tour': [3, 26, 43, 11, 31, 41, 65, 42, 49, 28, 15, 30, 22, 48, 18, 12, 53, 44, 13, 54, 0, 10, 1, 5, 38, 33, 61, 14, 63, 57, 25, 34, 40, 59, 60, 64, 45, 47, 24, 2, 27, 20, 36, 16, 23, 4, 21, 51, 7, 62, 17, 37, 9, 6, 50, 39, 8, 56, 58, 46, 29, 35, 19, 55, 52, 32], 'cur_cost': 98234.0}, {'tour': [0, 49, 26, 37, 33, 56, 62, 2, 11, 15, 4, 51, 63, 28, 24, 40, 65, 19, 35, 23, 38, 9, 53, 57, 18, 36, 34, 1, 7, 14, 20, 22, 61, 45, 21, 47, 5, 42, 44, 17, 16, 41, 50, 30, 60, 39, 8, 10, 13, 64, 54, 29, 55, 12, 59, 31, 25, 52, 43, 58, 32, 46, 48, 3, 27, 6], 'cur_cost': 102453.0}, {'tour': [30, 65, 8, 36, 28, 24, 49, 43, 19, 25, 40, 48, 17, 6, 44, 7, 41, 64, 56, 51, 23, 58, 20, 0, 1, 18, 61, 34, 42, 62, 37, 60, 38, 31, 22, 5, 3, 59, 26, 11, 45, 33, 35, 13, 27, 29, 55, 52, 12, 9, 16, 50, 10, 15, 47, 4, 54, 32, 57, 39, 53, 46, 2, 14, 21, 63], 'cur_cost': 113611.0}]
2025-07-04 10:16:26,967 - ExploitationExpert - INFO - 局部搜索耗时: 0.89秒
2025-07-04 10:16:26,967 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-04 10:16:26,967 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-07-04 10:16:26,967 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-04 10:16:26,968 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:16:26,968 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:16:26,969 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 96224.0
2025-07-04 10:16:27,470 - ExploitationExpert - INFO - res_population_num: 9
2025-07-04 10:16:27,470 - ExploitationExpert - INFO - res_population_costs: [94768, 9575, 9557, 9534, 9534, 9527, 9522, 9521, 9521]
2025-07-04 10:16:27,470 - ExploitationExpert - INFO - res_populations: [array([ 0, 48, 55, 25, 28,  3, 11, 20, 19, 27, 29, 44, 38, 65, 62, 61, 15,
       60, 12,  7, 34, 30, 22,  6,  1, 46, 49, 43, 41,  4, 45, 36, 57, 54,
        2, 24, 32,  5, 33, 13, 17, 40, 23, 47,  8, 37, 53, 63, 35, 26, 21,
       59, 18, 14, 39,  9, 31, 42, 58, 64, 10, 52, 51, 56, 16, 50],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       47, 49, 40, 43, 48, 46, 45, 44, 41, 38, 51, 50, 42, 34, 30, 28, 35,
       25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 18, 16, 19, 21, 20, 13, 23,
       22, 12, 17, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 13, 23, 16, 18, 17, 12, 22, 15, 14, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:16:27,476 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:16:27,476 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([50, 16,  5, 25, 64, 38,  6, 40, 43, 59, 14, 13, 15, 36, 65, 32, 17,
       34, 58,  8, 29, 57,  3, 33, 62, 30, 51, 53,  2, 46, 56, 61, 60, 22,
       42, 20, 52, 41, 24, 48, 45, 44,  4, 39, 11, 54, 12,  1, 31, 10, 49,
       27,  7, 47,  9, 18, 23,  0, 19, 35, 37, 55, 26, 63, 28, 21]), 'cur_cost': 119648.0}, {'tour': array([54, 12, 51, 14, 59, 43, 49, 11, 16, 21, 64, 19, 65, 13, 31, 20,  4,
        7, 33, 24, 44, 55, 39, 36, 48,  3, 61,  2, 18, 42, 40, 56, 27, 62,
        0, 28, 53, 29, 32, 50, 52, 15, 38, 30, 60, 45, 25, 57, 22, 10, 17,
       41, 46,  8, 37,  6, 23,  9, 63, 34,  1, 26, 58,  5, 35, 47]), 'cur_cost': 119190.0}, {'tour': array([15, 22, 25, 39, 40, 57,  9, 52, 29, 49, 60, 18, 13, 43, 46,  1,  8,
       21, 54, 56, 38, 58, 65,  2,  6, 61, 45,  0, 37, 42, 11, 53, 30, 19,
       33,  3, 20, 55, 59,  5, 62, 44, 14, 17, 24, 36, 27, 23, 41, 64, 48,
        4, 35, 34, 16, 47, 63,  7, 10, 26, 12, 50, 31, 32, 51, 28]), 'cur_cost': 96224.0}, {'tour': [3, 56, 45, 28, 17, 51, 49, 37, 5, 14, 36, 12, 58, 10, 54, 8, 62, 6, 39, 46, 30, 44, 27, 15, 55, 38, 43, 16, 64, 31, 65, 23, 18, 50, 35, 53, 52, 48, 33, 19, 9, 41, 63, 59, 32, 7, 22, 34, 21, 11, 42, 4, 20, 61, 47, 1, 0, 25, 57, 40, 24, 29, 26, 60, 2, 13], 'cur_cost': 108817.0}, {'tour': [3, 26, 43, 11, 31, 41, 65, 42, 49, 28, 15, 30, 22, 48, 18, 12, 53, 44, 13, 54, 0, 10, 1, 5, 38, 33, 61, 14, 63, 57, 25, 34, 40, 59, 60, 64, 45, 47, 24, 2, 27, 20, 36, 16, 23, 4, 21, 51, 7, 62, 17, 37, 9, 6, 50, 39, 8, 56, 58, 46, 29, 35, 19, 55, 52, 32], 'cur_cost': 98234.0}, {'tour': [0, 49, 26, 37, 33, 56, 62, 2, 11, 15, 4, 51, 63, 28, 24, 40, 65, 19, 35, 23, 38, 9, 53, 57, 18, 36, 34, 1, 7, 14, 20, 22, 61, 45, 21, 47, 5, 42, 44, 17, 16, 41, 50, 30, 60, 39, 8, 10, 13, 64, 54, 29, 55, 12, 59, 31, 25, 52, 43, 58, 32, 46, 48, 3, 27, 6], 'cur_cost': 102453.0}, {'tour': [30, 65, 8, 36, 28, 24, 49, 43, 19, 25, 40, 48, 17, 6, 44, 7, 41, 64, 56, 51, 23, 58, 20, 0, 1, 18, 61, 34, 42, 62, 37, 60, 38, 31, 22, 5, 3, 59, 26, 11, 45, 33, 35, 13, 27, 29, 55, 52, 12, 9, 16, 50, 10, 15, 47, 4, 54, 32, 57, 39, 53, 46, 2, 14, 21, 63], 'cur_cost': 113611.0}]
2025-07-04 10:16:27,477 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:16:27,477 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-04 10:16:27,477 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-04 10:16:27,477 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 10:16:27,477 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:16:27,480 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 108817.0 (rank 60.0%)
- Population diversity: 0.7415384615384616
- Evolution phase: Exploration
- Landscape recommends focus: Exploration and Exploitation. Balance between improving current solutions, while continuing to explore diverse solutions. Given the diversity, it's important to now focus on exploiting promising regions and leveraging subpath reuse.
- Opportunity regions (sample): {'region_indice
- Difficult regions to avoid (sample): {'region_indice, {'region_indice...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:16:27,480 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:16:27,480 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:16:31,768 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 15, 32, 48, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59],
  "targeted_regions": "Opportunity region indices are likely covered through this path. Exploiting subpath reuse.",
  "strategy_comment": "Explores opportunities first, then builds a diverse path, leveraging subpath structures to improve exploration."
}
```
2025-07-04 10:16:31,768 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:16:31,768 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19819.0, 路径: [0, 15, 32, 48, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59]
2025-07-04 10:16:31,768 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 15, 32, 48, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 19819.0}
2025-07-04 10:16:31,768 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 10:16:31,768 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:16:31,768 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:16:31,768 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 102549.0
2025-07-04 10:16:32,277 - ExploitationExpert - INFO - res_population_num: 17
2025-07-04 10:16:32,277 - ExploitationExpert - INFO - res_population_costs: [94768, 9575, 9557, 9534, 9534, 9527, 9522, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 10:16:32,277 - ExploitationExpert - INFO - res_populations: [array([ 0, 48, 55, 25, 28,  3, 11, 20, 19, 27, 29, 44, 38, 65, 62, 61, 15,
       60, 12,  7, 34, 30, 22,  6,  1, 46, 49, 43, 41,  4, 45, 36, 57, 54,
        2, 24, 32,  5, 33, 13, 17, 40, 23, 47,  8, 37, 53, 63, 35, 26, 21,
       59, 18, 14, 39,  9, 31, 42, 58, 64, 10, 52, 51, 56, 16, 50],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       47, 49, 40, 43, 48, 46, 45, 44, 41, 38, 51, 50, 42, 34, 30, 28, 35,
       25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 18, 16, 19, 21, 20, 13, 23,
       22, 12, 17, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 13, 23, 16, 18, 17, 12, 22, 15, 14, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:16:32,286 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:16:32,287 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([50, 16,  5, 25, 64, 38,  6, 40, 43, 59, 14, 13, 15, 36, 65, 32, 17,
       34, 58,  8, 29, 57,  3, 33, 62, 30, 51, 53,  2, 46, 56, 61, 60, 22,
       42, 20, 52, 41, 24, 48, 45, 44,  4, 39, 11, 54, 12,  1, 31, 10, 49,
       27,  7, 47,  9, 18, 23,  0, 19, 35, 37, 55, 26, 63, 28, 21]), 'cur_cost': 119648.0}, {'tour': array([54, 12, 51, 14, 59, 43, 49, 11, 16, 21, 64, 19, 65, 13, 31, 20,  4,
        7, 33, 24, 44, 55, 39, 36, 48,  3, 61,  2, 18, 42, 40, 56, 27, 62,
        0, 28, 53, 29, 32, 50, 52, 15, 38, 30, 60, 45, 25, 57, 22, 10, 17,
       41, 46,  8, 37,  6, 23,  9, 63, 34,  1, 26, 58,  5, 35, 47]), 'cur_cost': 119190.0}, {'tour': array([15, 22, 25, 39, 40, 57,  9, 52, 29, 49, 60, 18, 13, 43, 46,  1,  8,
       21, 54, 56, 38, 58, 65,  2,  6, 61, 45,  0, 37, 42, 11, 53, 30, 19,
       33,  3, 20, 55, 59,  5, 62, 44, 14, 17, 24, 36, 27, 23, 41, 64, 48,
        4, 35, 34, 16, 47, 63,  7, 10, 26, 12, 50, 31, 32, 51, 28]), 'cur_cost': 96224.0}, {'tour': [0, 15, 32, 48, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 19819.0}, {'tour': array([48, 35, 45, 44, 21, 51, 43, 11, 55,  4, 16, 41,  1, 42, 31, 47, 33,
       34, 37, 53, 46, 19,  9, 50, 17,  7, 63, 40, 20, 14,  3,  2, 56, 15,
       62, 13, 28, 29,  5, 32, 22, 58, 36, 26, 64, 49, 38, 27, 30, 12, 60,
       23, 61,  8, 25, 65, 18, 59, 57, 10, 52,  0,  6, 39, 54, 24]), 'cur_cost': 102549.0}, {'tour': [0, 49, 26, 37, 33, 56, 62, 2, 11, 15, 4, 51, 63, 28, 24, 40, 65, 19, 35, 23, 38, 9, 53, 57, 18, 36, 34, 1, 7, 14, 20, 22, 61, 45, 21, 47, 5, 42, 44, 17, 16, 41, 50, 30, 60, 39, 8, 10, 13, 64, 54, 29, 55, 12, 59, 31, 25, 52, 43, 58, 32, 46, 48, 3, 27, 6], 'cur_cost': 102453.0}, {'tour': [30, 65, 8, 36, 28, 24, 49, 43, 19, 25, 40, 48, 17, 6, 44, 7, 41, 64, 56, 51, 23, 58, 20, 0, 1, 18, 61, 34, 42, 62, 37, 60, 38, 31, 22, 5, 3, 59, 26, 11, 45, 33, 35, 13, 27, 29, 55, 52, 12, 9, 16, 50, 10, 15, 47, 4, 54, 32, 57, 39, 53, 46, 2, 14, 21, 63], 'cur_cost': 113611.0}]
2025-07-04 10:16:32,289 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 10:16:32,289 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-04 10:16:32,289 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 10:16:32,289 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-04 10:16:32,289 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:16:32,290 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 102453.0 (rank 50.0%)
- Population diversity: 0.6615384615384615
- Evolution phase: Exploration
- Landscape recommends focus: Exploration and Exploitation. Balance between improving current solutions, while continuing to explore diverse solutions. Given the diversity, it's important to now focus on exploiting promising regions and leveraging subpath reuse.
- Opportunity regions (sample): {'region_indice
- Difficult regions to avoid (sample): {'region_indice, {'region_indice...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:16:32,290 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:16:32,291 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:16:35,431 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 15, 30, 45, 60, 61, 62, 63, 64, 65, 50, 35, 20, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1, 2, 3, 4, 16, 17, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59],
  "targeted_regions": "Nodes near opportunity regions 0, 15, 30, 45, 60 and adjacent cells.",
  "strategy_comment": "Initial nodes target opportunity regions. Connects to remaining nodes, maximizing diversity through spread and utilizing adjacent sparse cells."
}
```
2025-07-04 10:16:35,431 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:16:35,431 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28577.0, 路径: [0, 15, 30, 45, 60, 61, 62, 63, 64, 65, 50, 35, 20, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1, 2, 3, 4, 16, 17, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59]
2025-07-04 10:16:35,431 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 15, 30, 45, 60, 61, 62, 63, 64, 65, 50, 35, 20, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1, 2, 3, 4, 16, 17, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 28577.0}
2025-07-04 10:16:35,431 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 10:16:35,431 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:16:35,431 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:16:35,431 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110963.0
2025-07-04 10:16:35,935 - ExploitationExpert - INFO - res_population_num: 18
2025-07-04 10:16:35,935 - ExploitationExpert - INFO - res_population_costs: [94768, 9575, 9557, 9534, 9534, 9527, 9522, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 10:16:35,937 - ExploitationExpert - INFO - res_populations: [array([ 0, 48, 55, 25, 28,  3, 11, 20, 19, 27, 29, 44, 38, 65, 62, 61, 15,
       60, 12,  7, 34, 30, 22,  6,  1, 46, 49, 43, 41,  4, 45, 36, 57, 54,
        2, 24, 32,  5, 33, 13, 17, 40, 23, 47,  8, 37, 53, 63, 35, 26, 21,
       59, 18, 14, 39,  9, 31, 42, 58, 64, 10, 52, 51, 56, 16, 50],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       47, 49, 40, 43, 48, 46, 45, 44, 41, 38, 51, 50, 42, 34, 30, 28, 35,
       25, 26, 36, 37, 31, 33, 32, 29, 24, 27, 18, 16, 19, 21, 20, 13, 23,
       22, 12, 17, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 23, 22, 12, 17, 15, 14, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 13, 23, 16, 18, 17, 12, 22, 15, 14, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:16:35,944 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:16:35,944 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([50, 16,  5, 25, 64, 38,  6, 40, 43, 59, 14, 13, 15, 36, 65, 32, 17,
       34, 58,  8, 29, 57,  3, 33, 62, 30, 51, 53,  2, 46, 56, 61, 60, 22,
       42, 20, 52, 41, 24, 48, 45, 44,  4, 39, 11, 54, 12,  1, 31, 10, 49,
       27,  7, 47,  9, 18, 23,  0, 19, 35, 37, 55, 26, 63, 28, 21]), 'cur_cost': 119648.0}, {'tour': array([54, 12, 51, 14, 59, 43, 49, 11, 16, 21, 64, 19, 65, 13, 31, 20,  4,
        7, 33, 24, 44, 55, 39, 36, 48,  3, 61,  2, 18, 42, 40, 56, 27, 62,
        0, 28, 53, 29, 32, 50, 52, 15, 38, 30, 60, 45, 25, 57, 22, 10, 17,
       41, 46,  8, 37,  6, 23,  9, 63, 34,  1, 26, 58,  5, 35, 47]), 'cur_cost': 119190.0}, {'tour': array([15, 22, 25, 39, 40, 57,  9, 52, 29, 49, 60, 18, 13, 43, 46,  1,  8,
       21, 54, 56, 38, 58, 65,  2,  6, 61, 45,  0, 37, 42, 11, 53, 30, 19,
       33,  3, 20, 55, 59,  5, 62, 44, 14, 17, 24, 36, 27, 23, 41, 64, 48,
        4, 35, 34, 16, 47, 63,  7, 10, 26, 12, 50, 31, 32, 51, 28]), 'cur_cost': 96224.0}, {'tour': [0, 15, 32, 48, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 19819.0}, {'tour': array([48, 35, 45, 44, 21, 51, 43, 11, 55,  4, 16, 41,  1, 42, 31, 47, 33,
       34, 37, 53, 46, 19,  9, 50, 17,  7, 63, 40, 20, 14,  3,  2, 56, 15,
       62, 13, 28, 29,  5, 32, 22, 58, 36, 26, 64, 49, 38, 27, 30, 12, 60,
       23, 61,  8, 25, 65, 18, 59, 57, 10, 52,  0,  6, 39, 54, 24]), 'cur_cost': 102549.0}, {'tour': [0, 15, 30, 45, 60, 61, 62, 63, 64, 65, 50, 35, 20, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1, 2, 3, 4, 16, 17, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 28577.0}, {'tour': array([37, 18, 58,  2, 49, 35, 15,  7, 38, 56, 32, 52, 40, 43,  3,  4, 48,
       22, 23,  8, 30, 10,  1, 20, 51, 25, 42, 57, 31, 11, 27, 12, 33, 13,
       34, 63, 53, 64, 50,  0, 44, 24, 47, 16, 54, 55, 60, 19, 61,  6, 46,
       26,  9, 45, 17, 14, 29, 36, 41,  5, 65, 62, 21, 28, 59, 39]), 'cur_cost': 110963.0}]
2025-07-04 10:16:35,947 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 10:16:35,947 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-04 10:16:35,947 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 10:16:35,948 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 32, 48, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 19819.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 30, 45, 60, 61, 62, 63, 64, 65, 50, 35, 20, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1, 2, 3, 4, 16, 17, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 28577.0}}]
2025-07-04 10:16:35,948 - __main__ - INFO - 进化阶段完成
2025-07-04 10:16:35,949 - __main__ - INFO - 开始评估阶段
2025-07-04 10:16:35,949 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
