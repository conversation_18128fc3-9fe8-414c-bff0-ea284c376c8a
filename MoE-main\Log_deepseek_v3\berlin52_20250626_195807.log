2025-06-26 19:58:07,794 - __main__ - INFO - berlin52 开始进化第 1 代
2025-06-26 19:58:07,794 - __main__ - INFO - 开始分析阶段
2025-06-26 19:58:07,794 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:58:07,808 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9251.0, 'max': 30889.0, 'mean': 23170.7, 'std': 8935.738235311059}, 'diversity': 0.9205128205128206, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:58:07,809 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9251.0, 'max': 30889.0, 'mean': 23170.7, 'std': 8935.738235311059}, 'diversity_level': 0.9205128205128206, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[565, 575], [25, 185], [345, 750], [945, 685], [845, 655], [880, 660], [25, 230], [525, 1000], [580, 1175], [650, 1130], [1605, 620], [1220, 580], [1465, 200], [1530, 5], [845, 680], [725, 370], [145, 665], [415, 635], [510, 875], [560, 365], [300, 465], [520, 585], [480, 415], [835, 625], [975, 580], [1215, 245], [1320, 315], [1250, 400], [660, 180], [410, 250], [420, 555], [575, 665], [1150, 1160], [700, 580], [685, 595], [685, 610], [770, 610], [795, 645], [720, 635], [760, 650], [475, 960], [95, 260], [875, 920], [700, 500], [555, 815], [830, 485], [1170, 65], [830, 610], [605, 625], [595, 360], [1340, 725], [1740, 245]], 'distance_matrix': array([[   0.,  666.,  281., ...,  217.,  789., 1220.],
       [ 666.,    0.,  649., ...,  596., 1422., 1716.],
       [ 281.,  649.,    0., ...,  463.,  995., 1484.],
       ...,
       [ 217.,  596.,  463., ...,    0.,  830., 1151.],
       [ 789., 1422.,  995., ...,  830.,    0.,  625.],
       [1220., 1716., 1484., ..., 1151.,  625.,    0.]])}
2025-06-26 19:58:07,819 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:58:07,819 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:58:07,819 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:58:07,823 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:58:07,824 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (49, 19, 22), 'frequency': 0.3}, {'subpath': (30, 17, 21), 'frequency': 0.3}, {'subpath': (17, 21, 0), 'frequency': 0.3}, {'subpath': (21, 0, 48), 'frequency': 0.3}, {'subpath': (0, 48, 31), 'frequency': 0.3}, {'subpath': (35, 34, 33), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(19, 49)', 'frequency': 0.4}, {'edge': '(17, 30)', 'frequency': 0.4}, {'edge': '(4, 14)', 'frequency': 0.4}, {'edge': '(16, 20)', 'frequency': 0.4}, {'edge': '(2, 16)', 'frequency': 0.4}, {'edge': '(11, 50)', 'frequency': 0.4}, {'edge': '(26, 27)', 'frequency': 0.4}, {'edge': '(1, 6)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(19, 22)', 'frequency': 0.3}, {'edge': '(22, 30)', 'frequency': 0.3}, {'edge': '(17, 21)', 'frequency': 0.3}, {'edge': '(0, 21)', 'frequency': 0.3}, {'edge': '(0, 48)', 'frequency': 0.3}, {'edge': '(31, 48)', 'frequency': 0.3}, {'edge': '(31, 35)', 'frequency': 0.2}, {'edge': '(34, 35)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(33, 38)', 'frequency': 0.2}, {'edge': '(38, 39)', 'frequency': 0.3}, {'edge': '(37, 39)', 'frequency': 0.3}, {'edge': '(36, 37)', 'frequency': 0.3}, {'edge': '(36, 47)', 'frequency': 0.2}, {'edge': '(23, 47)', 'frequency': 0.3}, {'edge': '(4, 23)', 'frequency': 0.2}, {'edge': '(5, 14)', 'frequency': 0.2}, {'edge': '(3, 5)', 'frequency': 0.3}, {'edge': '(3, 24)', 'frequency': 0.3}, {'edge': '(24, 45)', 'frequency': 0.2}, {'edge': '(43, 45)', 'frequency': 0.3}, {'edge': '(15, 43)', 'frequency': 0.2}, {'edge': '(28, 29)', 'frequency': 0.3}, {'edge': '(20, 29)', 'frequency': 0.2}, {'edge': '(18, 44)', 'frequency': 0.3}, {'edge': '(7, 40)', 'frequency': 0.3}, {'edge': '(7, 9)', 'frequency': 0.2}, {'edge': '(8, 9)', 'frequency': 0.3}, {'edge': '(8, 42)', 'frequency': 0.2}, {'edge': '(32, 42)', 'frequency': 0.3}, {'edge': '(11, 27)', 'frequency': 0.2}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(25, 46)', 'frequency': 0.3}, {'edge': '(12, 46)', 'frequency': 0.2}, {'edge': '(12, 13)', 'frequency': 0.2}, {'edge': '(13, 51)', 'frequency': 0.2}, {'edge': '(10, 51)', 'frequency': 0.3}, {'edge': '(6, 41)', 'frequency': 0.3}, {'edge': '(20, 30)', 'frequency': 0.2}, {'edge': '(15, 49)', 'frequency': 0.3}, {'edge': '(22, 29)', 'frequency': 0.2}, {'edge': '(28, 46)', 'frequency': 0.2}, {'edge': '(10, 50)', 'frequency': 0.2}, {'edge': '(7, 8)', 'frequency': 0.2}, {'edge': '(18, 40)', 'frequency': 0.2}, {'edge': '(6, 32)', 'frequency': 0.3}, {'edge': '(4, 5)', 'frequency': 0.2}, {'edge': '(36, 39)', 'frequency': 0.2}, {'edge': '(2, 32)', 'frequency': 0.2}, {'edge': '(12, 43)', 'frequency': 0.2}, {'edge': '(50, 51)', 'frequency': 0.2}, {'edge': '(44, 49)', 'frequency': 0.2}, {'edge': '(21, 40)', 'frequency': 0.2}, {'edge': '(9, 21)', 'frequency': 0.2}, {'edge': '(33, 47)', 'frequency': 0.2}, {'edge': '(33, 39)', 'frequency': 0.2}, {'edge': '(23, 39)', 'frequency': 0.2}, {'edge': '(5, 15)', 'frequency': 0.2}, {'edge': '(24, 31)', 'frequency': 0.2}, {'edge': '(0, 42)', 'frequency': 0.3}, {'edge': '(6, 12)', 'frequency': 0.2}, {'edge': '(21, 29)', 'frequency': 0.2}, {'edge': '(3, 33)', 'frequency': 0.2}, {'edge': '(13, 33)', 'frequency': 0.2}, {'edge': '(10, 27)', 'frequency': 0.2}, {'edge': '(2, 28)', 'frequency': 0.2}, {'edge': '(17, 50)', 'frequency': 0.2}, {'edge': '(44, 45)', 'frequency': 0.2}, {'edge': '(15, 25)', 'frequency': 0.2}, {'edge': '(18, 29)', 'frequency': 0.2}, {'edge': '(26, 38)', 'frequency': 0.2}, {'edge': '(23, 24)', 'frequency': 0.2}, {'edge': '(24, 46)', 'frequency': 0.2}, {'edge': '(10, 25)', 'frequency': 0.2}, {'edge': '(5, 43)', 'frequency': 0.2}, {'edge': '(37, 50)', 'frequency': 0.2}, {'edge': '(27, 51)', 'frequency': 0.2}, {'edge': '(4, 13)', 'frequency': 0.2}, {'edge': '(9, 37)', 'frequency': 0.2}, {'edge': '(5, 8)', 'frequency': 0.2}, {'edge': '(36, 42)', 'frequency': 0.2}, {'edge': '(1, 48)', 'frequency': 0.2}, {'edge': '(22, 51)', 'frequency': 0.2}, {'edge': '(13, 34)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [27, 40, 6, 12, 2], 'cost': 4502.0, 'size': 5}, {'region': [51, 22, 8, 6, 44], 'cost': 3923.0, 'size': 5}, {'region': [42, 16, 13, 34], 'cost': 3338.0, 'size': 4}, {'region': [14, 10, 17, 51], 'cost': 3333.0, 'size': 4}, {'region': [38, 6, 12, 49], 'cost': 3129.0, 'size': 4}]}
2025-06-26 19:58:07,824 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:58:07,825 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:58:07,825 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:58:07,825 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 19:58:07,825 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 19:58:07,825 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:58:07,825 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:58:08,616 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:58:08,616 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9251.0, mean 23170.7, max 30889.0, std 8935.738235311059
- diversity: 0.9205128205128206
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: [{'subpath': (49, 19, 22), 'frequency': 0.3}, {'subpath': (30, 17, 21), 'frequency': 0.3}, {'subpath': (17, 21, 0), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [27, 40, 6, 12, 2], 'cost': 4502.0, 'size': 5}, {'region': [51, 22, 8, 6, 44], 'cost': 3923.0, 'size': 5}, {'region': [42, 16, 13, 34], 'cost': 3338.0, 'size': 4}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [25.0, 5.0, 1740.0, 1175.0]
- centroid/spread: [758.462, 564.904] | rms 467.889
- density_grid(3x3): [[6, 2, 6], [9, 17, 4], [5, 3, 0]]
- edge_len_stats(min,q1,med,q3,max): [15.0, 308.0, 525.0, 789.75, 1716.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 6, 7, 5, 10, 6, 5, 6]
- nn_median_dist: 78.079

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:58:08,616 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:58:38,790 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 15.0 to 1716.0 with median 525.0 and Q3 at 789.75)",
    "modality": "Potentially multimodal (high diversity 0.92, no convergence yet, and presence of common subpaths)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio 0.25 indicates some very long edges that may mislead search)"
  },
  "population_state": {
    "coverage": "Good initial coverage (high diversity 0.92, density_grid shows spread across most cells)",
    "convergence": "No convergence yet (convergence 0.0, no stagnation detected)",
    "clustering": "No explicit clustering information, but density_grid shows higher concentration in center cell (17 nodes)"
  },
  "difficult_regions": [
    {"region": [27, 40, 6, 12, 2], "reason": "High cost (4502.0) for size 5, potentially rugged area"},
    {"region": [51, 22, 8, 6, 44], "reason": "High cost (3923.0) for size 5, combined with long-edge ratio"},
    {"region": [42, 16, 13, 34], "reason": "High cost (3338.0) for size 4, located in sparse grid cell (bottom-left)"}
  ],
  "opportunity_regions": [
    {"region": "Center grid cell (17 nodes)", "reason": "High density suggests promising cluster"},
    {"region": "Subpath (49,19,22)", "reason": "Frequently occurring (30%) in population"},
    {"region": "Subpath (30,17,21)", "reason": "Frequently occurring (30%) in population"}
  ],
  "evolution_phase": "Early exploration (iteration 0/5, high diversity, no convergence)",
  "evolution_direction": [
    "Focus exploitation on high-density center region and frequent subpaths",
    "Apply local optimization to difficult regions to smooth rugged areas",
    "Maintain diversity through mutation operators that target low-density grid cells",
    "Consider edge recombination that preserves common subpaths while exploring new combinations"
  ]
}
```
2025-06-26 19:58:38,790 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:58:38,790 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 15.0 to 1716.0 with median 525.0 and Q3 at 789.75)', 'modality': 'Potentially multimodal (high diversity 0.92, no convergence yet, and presence of common subpaths)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio 0.25 indicates some very long edges that may mislead search)'}, 'population_state': {'coverage': 'Good initial coverage (high diversity 0.92, density_grid shows spread across most cells)', 'convergence': 'No convergence yet (convergence 0.0, no stagnation detected)', 'clustering': 'No explicit clustering information, but density_grid shows higher concentration in center cell (17 nodes)'}, 'difficult_regions': [{'region': [27, 40, 6, 12, 2], 'reason': 'High cost (4502.0) for size 5, potentially rugged area'}, {'region': [51, 22, 8, 6, 44], 'reason': 'High cost (3923.0) for size 5, combined with long-edge ratio'}, {'region': [42, 16, 13, 34], 'reason': 'High cost (3338.0) for size 4, located in sparse grid cell (bottom-left)'}], 'opportunity_regions': [{'region': 'Center grid cell (17 nodes)', 'reason': 'High density suggests promising cluster'}, {'region': 'Subpath (49,19,22)', 'reason': 'Frequently occurring (30%) in population'}, {'region': 'Subpath (30,17,21)', 'reason': 'Frequently occurring (30%) in population'}], 'evolution_phase': 'Early exploration (iteration 0/5, high diversity, no convergence)', 'evolution_direction': ['Focus exploitation on high-density center region and frequent subpaths', 'Apply local optimization to difficult regions to smooth rugged areas', 'Maintain diversity through mutation operators that target low-density grid cells', 'Consider edge recombination that preserves common subpaths while exploring new combinations']}
2025-06-26 19:58:38,790 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:58:38,790 - __main__ - INFO - 分析阶段完成
2025-06-26 19:58:38,790 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 15.0 to 1716.0 with median 525.0 and Q3 at 789.75)', 'modality': 'Potentially multimodal (high diversity 0.92, no convergence yet, and presence of common subpaths)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio 0.25 indicates some very long edges that may mislead search)'}, 'population_state': {'coverage': 'Good initial coverage (high diversity 0.92, density_grid shows spread across most cells)', 'convergence': 'No convergence yet (convergence 0.0, no stagnation detected)', 'clustering': 'No explicit clustering information, but density_grid shows higher concentration in center cell (17 nodes)'}, 'difficult_regions': [{'region': [27, 40, 6, 12, 2], 'reason': 'High cost (4502.0) for size 5, potentially rugged area'}, {'region': [51, 22, 8, 6, 44], 'reason': 'High cost (3923.0) for size 5, combined with long-edge ratio'}, {'region': [42, 16, 13, 34], 'reason': 'High cost (3338.0) for size 4, located in sparse grid cell (bottom-left)'}], 'opportunity_regions': [{'region': 'Center grid cell (17 nodes)', 'reason': 'High density suggests promising cluster'}, {'region': 'Subpath (49,19,22)', 'reason': 'Frequently occurring (30%) in population'}, {'region': 'Subpath (30,17,21)', 'reason': 'Frequently occurring (30%) in population'}], 'evolution_phase': 'Early exploration (iteration 0/5, high diversity, no convergence)', 'evolution_direction': ['Focus exploitation on high-density center region and frequent subpaths', 'Apply local optimization to difficult regions to smooth rugged areas', 'Maintain diversity through mutation operators that target low-density grid cells', 'Consider edge recombination that preserves common subpaths while exploring new combinations']}
2025-06-26 19:58:38,793 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:58:38,793 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:58:38,793 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 9251.0
  • mean_cost: 23170.7
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 15.0 to 1716.0 with median 525.0 and Q3 at 789.75), modality Potentially multimodal (high diversity 0.92, no convergence yet, and presence of common subpaths), deceptiveness Possible deceptive regions (long_edge_ratio 0.25 indicates some very long edges that may mislead search)
- Evolution phase: Early exploration (iteration 0/5, high diversity, no convergence)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [27, 40, , {'region': [51, 22, ...
- Opportunity regions (# 3): {'region': 'Center g, {'region': 'Subpath ...
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:58:38,793 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:58:38,793 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:58:50,236 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and no convergence favors exploration. Top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 19:58:50,240 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 19:58:50,240 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 19:58:50,240 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and no convergence favors exploration. Top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 19:58:50,240 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:58:50,240 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 19:58:50,240 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and no convergence favors exploration. Top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 19:58:50,240 - __main__ - INFO - 开始进化阶段
2025-06-26 19:58:50,240 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:58:50,240 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:58:50,240 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9251.0 (rank 0.0%)
- Population diversity: 0.7372549019607844
- Evolution phase: Early exploration (iteration 0/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'Cen, {'region': 'Sub...
- Difficult regions to avoid (sample): {'region': [27,, {'region': [51,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:58:50,240 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:58:50,240 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:59:03,336 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 21, 30, 12, 42, 15, 33, 45, 8, 26, 39, 17, 48, 5, 36, 23, 50, 10, 31, 19, 44, 7, 28, 41, 14, 34, 47, 3, 25, 38, 16, 49, 6, 22, 51, 9, 29, 40, 13, 35, 46, 2, 24, 37, 18, 43, 11, 32, 20, 4, 27, 1],
  "targeted_regions": "Cen, Sub, adjacent sparse cells",
  "strategy_comment": "Balanced exploration of opportunity regions with cost-aware diversity"
}
```
2025-06-26 19:59:03,336 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:59:03,336 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30853.0, 路径: [0, 21, 30, 12, 42, 15, 33, 45, 8, 26, 39, 17, 48, 5, 36, 23, 50, 10, 31, 19, 44, 7, 28, 41, 14, 34, 47, 3, 25, 38, 16, 49, 6, 22, 51, 9, 29, 40, 13, 35, 46, 2, 24, 37, 18, 43, 11, 32, 20, 4, 27, 1]
2025-06-26 19:59:03,336 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 21, 30, 12, 42, 15, 33, 45, 8, 26, 39, 17, 48, 5, 36, 23, 50, 10, 31, 19, 44, 7, 28, 41, 14, 34, 47, 3, 25, 38, 16, 49, 6, 22, 51, 9, 29, 40, 13, 35, 46, 2, 24, 37, 18, 43, 11, 32, 20, 4, 27, 1], 'cur_cost': 30853.0}
2025-06-26 19:59:03,336 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:59:03,336 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:59:03,338 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10200.0 (rank 10.0%)
- Population diversity: 0.7980392156862746
- Evolution phase: Early exploration (iteration 0/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'Cen, {'region': 'Sub...
- Difficult regions to avoid (sample): {'region': [27,, {'region': [51,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:59:03,338 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:59:03,338 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:59:16,219 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27],
  "targeted_regions": "Cen, Sub",
  "strategy_comment": "Shift difficult node 27 to end, prioritize opportunity regions"
}
```
2025-06-26 19:59:16,219 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:59:16,219 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22140.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27]
2025-06-26 19:59:16,219 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}
2025-06-26 19:59:16,219 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:59:16,219 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:59:16,228 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9553.0 (rank 0.0%)
- Population diversity: 0.8313725490196079
- Evolution phase: Early exploration (iteration 0/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'Cen, {'region': 'Sub...
- Difficult regions to avoid (sample): {'region': [27,, {'region': [51,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:59:16,228 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:59:16,228 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:59:28,986 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51],
  "targeted_regions": "Central and adjacent sparse cells",
  "strategy_comment": "Sequential sweep through central regions, avoiding known difficult areas"
}
```
2025-06-26 19:59:28,986 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:59:28,986 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22205.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51]
2025-06-26 19:59:28,986 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], 'cur_cost': 22205.0}
2025-06-26 19:59:28,986 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:59:28,986 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:59:28,986 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 28711.0 (rank 50.0%)
- Population diversity: 0.7764705882352941
- Evolution phase: Early exploration (iteration 0/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'Cen, {'region': 'Sub...
- Difficult regions to avoid (sample): {'region': [27,, {'region': [51,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:59:28,986 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:59:28,986 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:59:41,872 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27],
  "targeted_regions": "Cen, Sub",
  "strategy_comment": "Shift difficult node 27 to end, explore adjacent sparse cells"
}
```
2025-06-26 19:59:41,877 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:59:41,878 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22140.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27]
2025-06-26 19:59:41,878 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}
2025-06-26 19:59:41,880 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:59:41,880 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:59:41,880 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30889.0 (rank 90.0%)
- Population diversity: 0.7058823529411765
- Evolution phase: Early exploration (iteration 0/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'Cen, {'region': 'Sub...
- Difficult regions to avoid (sample): {'region': [27,, {'region': [51,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:59:41,880 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:59:41,880 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:59:55,456 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 21, 32, 15, 45, 8, 37, 12, 49, 3, 26, 18, 41, 5, 30, 23, 47, 10, 34, 7, 28, 19, 42, 14, 36, 1, 24, 39, 9, 50, 4, 31, 16, 43, 6, 29, 22, 46, 11, 35, 2, 25, 38, 13, 48, 20, 33, 17, 44, 27, 40, 51],
  "targeted_regions": "Cen, Sub, adjacent sparse cells",
  "strategy_comment": "Balanced exploration of opportunity regions while avoiding high-cost areas"
}
```
2025-06-26 19:59:55,456 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:59:55,456 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31786.0, 路径: [0, 21, 32, 15, 45, 8, 37, 12, 49, 3, 26, 18, 41, 5, 30, 23, 47, 10, 34, 7, 28, 19, 42, 14, 36, 1, 24, 39, 9, 50, 4, 31, 16, 43, 6, 29, 22, 46, 11, 35, 2, 25, 38, 13, 48, 20, 33, 17, 44, 27, 40, 51]
2025-06-26 19:59:55,456 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 21, 32, 15, 45, 8, 37, 12, 49, 3, 26, 18, 41, 5, 30, 23, 47, 10, 34, 7, 28, 19, 42, 14, 36, 1, 24, 39, 9, 50, 4, 31, 16, 43, 6, 29, 22, 46, 11, 35, 2, 25, 38, 13, 48, 20, 33, 17, 44, 27, 40, 51], 'cur_cost': 31786.0}
2025-06-26 19:59:55,463 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:59:55,463 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:59:55,463 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 25881.0 (rank 30.0%)
- Population diversity: 0.707843137254902
- Evolution phase: Early exploration (iteration 0/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'Cen, {'region': 'Sub...
- Difficult regions to avoid (sample): {'region': [27,, {'region': [51,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:59:55,464 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:59:55,464 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:00:08,158 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27],
  "targeted_regions": "Cen, Sub",
  "strategy_comment": "Shift difficult node 27 to end, explore adjacent sparse cells"
}
```
2025-06-26 20:00:08,158 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:00:08,158 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22140.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27]
2025-06-26 20:00:08,158 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}
2025-06-26 20:00:08,158 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:00:08,158 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:00:08,158 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 28670.0 (rank 50.0%)
- Population diversity: 0.6294117647058823
- Evolution phase: Early exploration (iteration 0/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'Cen, {'region': 'Sub...
- Difficult regions to avoid (sample): {'region': [27,, {'region': [51,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:00:08,158 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:00:08,158 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:00:21,375 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27],
  "targeted_regions": "Cen, Sub",
  "strategy_comment": "Shift difficult node 27 to end, prioritize opportunity regions"
}
```
2025-06-26 20:00:21,375 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:00:21,375 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22140.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27]
2025-06-26 20:00:21,375 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}
2025-06-26 20:00:21,375 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 20:00:21,375 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:00:21,375 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30183.0 (rank 70.0%)
- Population diversity: 0.5490196078431373
- Evolution phase: Early exploration (iteration 0/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'Cen, {'region': 'Sub...
- Difficult regions to avoid (sample): {'region': [27,, {'region': [51,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:00:21,375 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:00:21,375 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:00:34,616 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27],
  "targeted_regions": "Cen, Sub",
  "strategy_comment": "Shift difficult node 27 to end, prioritize central regions"
}
```
2025-06-26 20:00:34,616 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:00:34,616 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22140.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27]
2025-06-26 20:00:34,616 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}
2025-06-26 20:00:34,616 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:00:34,616 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:00:34,616 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:00:34,632 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 31094.0
2025-06-26 20:00:37,511 - ExploitationExpert - INFO - res_population_num: 1
2025-06-26 20:00:37,512 - ExploitationExpert - INFO - res_population_costs: [26623]
2025-06-26 20:00:37,512 - ExploitationExpert - INFO - res_populations: [array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:00:37,512 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:00:37,513 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 30, 12, 42, 15, 33, 45, 8, 26, 39, 17, 48, 5, 36, 23, 50, 10, 31, 19, 44, 7, 28, 41, 14, 34, 47, 3, 25, 38, 16, 49, 6, 22, 51, 9, 29, 40, 13, 35, 46, 2, 24, 37, 18, 43, 11, 32, 20, 4, 27, 1], 'cur_cost': 30853.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], 'cur_cost': 22205.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}, {'tour': [0, 21, 32, 15, 45, 8, 37, 12, 49, 3, 26, 18, 41, 5, 30, 23, 47, 10, 34, 7, 28, 19, 42, 14, 36, 1, 24, 39, 9, 50, 4, 31, 16, 43, 6, 29, 22, 46, 11, 35, 2, 25, 38, 13, 48, 20, 33, 17, 44, 27, 40, 51], 'cur_cost': 31786.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}, {'tour': array([21, 38,  9, 20,  4, 45, 11,  1, 19, 46, 28, 34,  7, 24, 49, 12,  5,
        6, 31,  8, 18, 47, 26, 35,  3, 37, 33, 27, 30,  0, 40, 25, 36, 41,
       23, 15, 10, 29, 22, 51, 14, 48, 50, 44, 17, 13,  2, 16, 39, 32, 42,
       43]), 'cur_cost': 31094.0}, {'tour': [35, 5, 43, 26, 3, 20, 50, 37, 25, 10, 9, 45, 31, 24, 23, 11, 7, 28, 46, 19, 33, 30, 1, 17, 47, 4, 14, 2, 49, 48, 34, 13, 27, 51, 22, 8, 6, 44, 39, 38, 12, 41, 21, 40, 16, 15, 32, 42, 0, 36, 29, 18], 'cur_cost': 28269.0}]
2025-06-26 20:00:37,513 - ExploitationExpert - INFO - 局部搜索耗时: 2.90秒
2025-06-26 20:00:37,513 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-26 20:00:37,513 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:00:37,513 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:00:37,513 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:00:37,513 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:00:37,515 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 28774.0
2025-06-26 20:00:38,548 - ExploitationExpert - INFO - res_population_num: 2
2025-06-26 20:00:38,548 - ExploitationExpert - INFO - res_population_costs: [26623, 8089]
2025-06-26 20:00:38,548 - ExploitationExpert - INFO - res_populations: [array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64)]
2025-06-26 20:00:38,548 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:00:38,548 - ExploitationExpert - INFO - populations: [{'tour': [0, 21, 30, 12, 42, 15, 33, 45, 8, 26, 39, 17, 48, 5, 36, 23, 50, 10, 31, 19, 44, 7, 28, 41, 14, 34, 47, 3, 25, 38, 16, 49, 6, 22, 51, 9, 29, 40, 13, 35, 46, 2, 24, 37, 18, 43, 11, 32, 20, 4, 27, 1], 'cur_cost': 30853.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], 'cur_cost': 22205.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}, {'tour': [0, 21, 32, 15, 45, 8, 37, 12, 49, 3, 26, 18, 41, 5, 30, 23, 47, 10, 34, 7, 28, 19, 42, 14, 36, 1, 24, 39, 9, 50, 4, 31, 16, 43, 6, 29, 22, 46, 11, 35, 2, 25, 38, 13, 48, 20, 33, 17, 44, 27, 40, 51], 'cur_cost': 31786.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}, {'tour': array([21, 38,  9, 20,  4, 45, 11,  1, 19, 46, 28, 34,  7, 24, 49, 12,  5,
        6, 31,  8, 18, 47, 26, 35,  3, 37, 33, 27, 30,  0, 40, 25, 36, 41,
       23, 15, 10, 29, 22, 51, 14, 48, 50, 44, 17, 13,  2, 16, 39, 32, 42,
       43]), 'cur_cost': 31094.0}, {'tour': array([40, 36,  6, 34, 33,  0, 18, 19,  9, 30, 35,  3, 51,  5, 48, 20, 45,
       17, 39, 10, 13,  1, 46, 25, 24, 29, 23, 42, 11, 41, 31, 32, 50, 14,
       44, 28, 38,  8, 16,  2, 26, 49, 43, 27,  4,  7, 37, 47, 15, 22, 12,
       21]), 'cur_cost': 28774.0}]
2025-06-26 20:00:38,548 - ExploitationExpert - INFO - 局部搜索耗时: 1.03秒
2025-06-26 20:00:38,548 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-26 20:00:38,548 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:00:38,548 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 30, 12, 42, 15, 33, 45, 8, 26, 39, 17, 48, 5, 36, 23, 50, 10, 31, 19, 44, 7, 28, 41, 14, 34, 47, 3, 25, 38, 16, 49, 6, 22, 51, 9, 29, 40, 13, 35, 46, 2, 24, 37, 18, 43, 11, 32, 20, 4, 27, 1], 'cur_cost': 30853.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], 'cur_cost': 22205.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 21, 32, 15, 45, 8, 37, 12, 49, 3, 26, 18, 41, 5, 30, 23, 47, 10, 34, 7, 28, 19, 42, 14, 36, 1, 24, 39, 9, 50, 4, 31, 16, 43, 6, 29, 22, 46, 11, 35, 2, 25, 38, 13, 48, 20, 33, 17, 44, 27, 40, 51], 'cur_cost': 31786.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 27], 'cur_cost': 22140.0}}]
2025-06-26 20:00:38,548 - __main__ - INFO - 进化阶段完成
2025-06-26 20:00:38,548 - __main__ - INFO - 开始评估阶段
2025-06-26 20:00:38,548 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:00:38,548 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:00:38,548 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9251.0 → 22140.0 (Δ -12889.0)
- Mean cost: 23170.7 → 25541.2
- Diversity: 0.737 → 0.461 (Δ -0.2765)

#### Elite metrics
- Elite count: 0 → 2
- Best elite cost: 9251.0 → 8089 (Δ 1162.0)

#### Strategy performance
- Explore: 8 inds, success 50.0% , avg Δ -2775.75
- Exploit: 2 inds, success 0.0% , avg Δ -749.5

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:00:38,554 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:00:50,773 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_delta": -2775.75,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_delta": -749.5,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "unbalanced",
    "diversity": "decreasing",
    "elite_improvement": "moderate"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "exploit_attempts": 1
  }
}
```
2025-06-26 20:00:50,793 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:00:50,793 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_delta": -2775.75,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_delta": -749.5,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "unbalanced",
    "diversity": "decreasing",
    "elite_improvement": "moderate"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "exploit_attempts": 1
  }
}
```
2025-06-26 20:00:50,794 - __main__ - INFO - 评估阶段完成
2025-06-26 20:00:50,794 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_delta": -2775.75,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_delta": -749.5,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "unbalanced",
    "diversity": "decreasing",
    "elite_improvement": "moderate"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "exploit_attempts": 1
  }
}
```
2025-06-26 20:00:50,794 - __main__ - INFO - 当前最佳适应度: 22140.0
2025-06-26 20:00:50,798 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\berlin52_route_0.pkl
2025-06-26 20:00:50,798 - __main__ - INFO - berlin52 开始进化第 2 代
2025-06-26 20:00:50,799 - __main__ - INFO - 开始分析阶段
2025-06-26 20:00:50,799 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:00:50,809 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 22140.0, 'max': 31786.0, 'mean': 25541.2, 'std': 4212.699462340033}, 'diversity': 0.6478632478632478, 'clusters': {'clusters': 5, 'cluster_sizes': [1, 6, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:00:50,810 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 22140.0, 'max': 31786.0, 'mean': 25541.2, 'std': 4212.699462340033}, 'diversity_level': 0.6478632478632478, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [1, 6, 1, 1, 1]}, 'coordinates': [[565, 575], [25, 185], [345, 750], [945, 685], [845, 655], [880, 660], [25, 230], [525, 1000], [580, 1175], [650, 1130], [1605, 620], [1220, 580], [1465, 200], [1530, 5], [845, 680], [725, 370], [145, 665], [415, 635], [510, 875], [560, 365], [300, 465], [520, 585], [480, 415], [835, 625], [975, 580], [1215, 245], [1320, 315], [1250, 400], [660, 180], [410, 250], [420, 555], [575, 665], [1150, 1160], [700, 580], [685, 595], [685, 610], [770, 610], [795, 645], [720, 635], [760, 650], [475, 960], [95, 260], [875, 920], [700, 500], [555, 815], [830, 485], [1170, 65], [830, 610], [605, 625], [595, 360], [1340, 725], [1740, 245]], 'distance_matrix': array([[   0.,  666.,  281., ...,  217.,  789., 1220.],
       [ 666.,    0.,  649., ...,  596., 1422., 1716.],
       [ 281.,  649.,    0., ...,  463.,  995., 1484.],
       ...,
       [ 217.,  596.,  463., ...,    0.,  830., 1151.],
       [ 789., 1422.,  995., ...,  830.,    0.,  625.],
       [1220., 1716., 1484., ..., 1151.,  625.,    0.]])}
2025-06-26 20:00:50,810 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:00:50,811 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:00:50,811 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:00:50,812 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:00:50,813 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:00:50,813 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:00:50,813 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:00:50,813 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:00:50,813 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:00:50,814 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:00:50,814 - __main__ - INFO - 精英专家分析报告: {'elite_count': 2, 'elite_common_features': {'common_edges': {'(5, 4)': 1.0}, 'common_edge_ratio': 0.019230769230769232}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 14051.0, 'avg_gap': 8185.200000000001}, 'structure_gap': {'unique_elite_edges': 85, 'unique_pop_edges': 230, 'common_edges': 18}}, 'elite_diversity': {'diversity_score': 0.9615384615384616}}
2025-06-26 20:00:50,815 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:00:50,815 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:00:50,816 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:00:50,817 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 22140.0, mean 25541.2, max 31786.0, std 4212.699462340033
- diversity: 0.6478632478632478
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [22140.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [25.0, 5.0, 1740.0, 1175.0]
- centroid/spread: [758.462, 564.904] | rms 467.889
- density_grid(3x3): [[6, 2, 6], [9, 17, 4], [5, 3, 0]]
- edge_len_stats(min,q1,med,q3,max): [15.0, 308.0, 525.0, 789.75, 1716.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 6, 7, 5, 10, 6, 5, 6]
- nn_median_dist: 78.079

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:00:50,817 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:01:12,648 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high ruggedness indicated by wide edge length distribution (15.0 to 1716.0) and significant standard deviation in costs (4212.7).",
    "modality": "Potential multimodality suggested by high diversity (0.648) and lack of convergence (0.0).",
    "deceptiveness": "Possible deceptiveness due to long-edge ratio (0.25) and uneven density distribution (density_grid)."
  },
  "population_state": {
    "coverage": "Good spatial coverage with some clustering in central regions (density_grid shows high concentration in center cell).",
    "convergence": "No convergence detected (0.0), population remains exploratory.",
    "diversity": "High diversity (0.648) suggests effective exploration."
  },
  "difficult_regions": [
    "Low-density cells (especially bottom-right corner - density_grid[2][2] = 0)",
    "Regions with long edges (top 25% of edge lengths > 789.75)"
  ],
  "opportunity_regions": [
    "High-density central region (density_grid[1][1] = 17 nodes)",
    "Areas with median edge lengths (525.0) and moderate directionality"
  ],
  "evolution_phase": "Early exploration phase (iteration 1/5)",
  "evolution_direction": [
    "Prioritize exploitation in high-density central regions using edge recombination",
    "Target exploration in low-density areas with mutation operators favoring long edges",
    "Maintain diversity through tournament selection with moderate pressure",
    "Consider path-relinking between elite solutions once they emerge"
  ]
}
```
2025-06-26 20:01:12,648 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:01:12,648 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high ruggedness indicated by wide edge length distribution (15.0 to 1716.0) and significant standard deviation in costs (4212.7).', 'modality': 'Potential multimodality suggested by high diversity (0.648) and lack of convergence (0.0).', 'deceptiveness': 'Possible deceptiveness due to long-edge ratio (0.25) and uneven density distribution (density_grid).'}, 'population_state': {'coverage': 'Good spatial coverage with some clustering in central regions (density_grid shows high concentration in center cell).', 'convergence': 'No convergence detected (0.0), population remains exploratory.', 'diversity': 'High diversity (0.648) suggests effective exploration.'}, 'difficult_regions': ['Low-density cells (especially bottom-right corner - density_grid[2][2] = 0)', 'Regions with long edges (top 25% of edge lengths > 789.75)'], 'opportunity_regions': ['High-density central region (density_grid[1][1] = 17 nodes)', 'Areas with median edge lengths (525.0) and moderate directionality'], 'evolution_phase': 'Early exploration phase (iteration 1/5)', 'evolution_direction': ['Prioritize exploitation in high-density central regions using edge recombination', 'Target exploration in low-density areas with mutation operators favoring long edges', 'Maintain diversity through tournament selection with moderate pressure', 'Consider path-relinking between elite solutions once they emerge']}
2025-06-26 20:01:12,649 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:01:12,649 - __main__ - INFO - 分析阶段完成
2025-06-26 20:01:12,649 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high ruggedness indicated by wide edge length distribution (15.0 to 1716.0) and significant standard deviation in costs (4212.7).', 'modality': 'Potential multimodality suggested by high diversity (0.648) and lack of convergence (0.0).', 'deceptiveness': 'Possible deceptiveness due to long-edge ratio (0.25) and uneven density distribution (density_grid).'}, 'population_state': {'coverage': 'Good spatial coverage with some clustering in central regions (density_grid shows high concentration in center cell).', 'convergence': 'No convergence detected (0.0), population remains exploratory.', 'diversity': 'High diversity (0.648) suggests effective exploration.'}, 'difficult_regions': ['Low-density cells (especially bottom-right corner - density_grid[2][2] = 0)', 'Regions with long edges (top 25% of edge lengths > 789.75)'], 'opportunity_regions': ['High-density central region (density_grid[1][1] = 17 nodes)', 'Areas with median edge lengths (525.0) and moderate directionality'], 'evolution_phase': 'Early exploration phase (iteration 1/5)', 'evolution_direction': ['Prioritize exploitation in high-density central regions using edge recombination', 'Target exploration in low-density areas with mutation operators favoring long edges', 'Maintain diversity through tournament selection with moderate pressure', 'Consider path-relinking between elite solutions once they emerge']}
2025-06-26 20:01:12,649 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:01:12,649 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:01:12,649 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 22140.0
  • mean_cost: 25541.2
- Search-space: ruggedness Moderate to high ruggedness indicated by wide edge length distribution (15.0 to 1716.0) and significant standard deviation in costs (4212.7)., modality Potential multimodality suggested by high diversity (0.648) and lack of convergence (0.0)., deceptiveness Possible deceptiveness due to long-edge ratio (0.25) and uneven density distribution (density_grid).
- Evolution phase: Early exploration phase (iteration 1/5)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Low-density cells (e, Regions with long ed
- Opportunity regions (# 2): High-density central, Areas with median ed
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
     ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:01:12,649 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:01:12,649 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:01:23,281 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with low diversity and moderate ruggedness favors exploration (70%). Top 30% individuals exploit high-density regions."
}
```
2025-06-26 20:01:23,281 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:01:23,281 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:01:23,281 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with low diversity and moderate ruggedness favors exploration (70%). Top 30% individuals exploit high-density regions."
}
```
2025-06-26 20:01:23,281 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:01:23,281 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:01:23,281 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with low diversity and moderate ruggedness favors exploration (70%). Top 30% individuals exploit high-density regions."
}
```
2025-06-26 20:01:23,281 - __main__ - INFO - 开始进化阶段
2025-06-26 20:01:23,296 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:01:23,296 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:01:23,296 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30853.0 (rank 70.0%)
- Population diversity: 0.46078431372549017
- Evolution phase: Early exploration phase (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Areas with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with lo
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:01:23,296 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:01:23,296 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:01:36,657 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "High-density ce, adjacent sparse cells",
  "strategy_comment": "Leverage modular jumps to explore sparse regions"
}
```
2025-06-26 20:01:36,657 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:01:36,657 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:01:36,657 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:01:36,657 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:01:36,657 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:01:36,657 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 22140.0 (rank 0.0%)
- Population diversity: 0.4666666666666667
- Evolution phase: Early exploration phase (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Areas with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with lo
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:01:36,657 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:01:36,657 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:01:49,801 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "High-density and adjacent low-density cells",
  "strategy_comment": "Balanced exploration of opportunity regions with structured jumps"
}
```
2025-06-26 20:01:49,801 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:01:49,801 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:01:49,801 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:01:49,801 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:01:49,801 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:01:49,801 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 22205.0 (rank 40.0%)
- Population diversity: 0.4666666666666667
- Evolution phase: Early exploration phase (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Areas with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with lo
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:01:49,801 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:01:49,801 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:02:02,968 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 42, 7, 19, 31, 43, 8, 20, 32, 44, 9, 21, 33, 45, 10, 22, 34, 46, 11, 23, 35, 47],
  "targeted_regions": "High-density and adjacent low-density cells",
  "strategy_comment": "Balanced exploration of high-density and adjacent sparse cells"
}
```
2025-06-26 20:02:02,981 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:02:02,981 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28344.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 42, 7, 19, 31, 43, 8, 20, 32, 44, 9, 21, 33, 45, 10, 22, 34, 46, 11, 23, 35, 47]
2025-06-26 20:02:02,981 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 42, 7, 19, 31, 43, 8, 20, 32, 44, 9, 21, 33, 45, 10, 22, 34, 46, 11, 23, 35, 47], 'cur_cost': 28344.0}
2025-06-26 20:02:02,981 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:02:02,981 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:02:02,981 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 22140.0 (rank 0.0%)
- Population diversity: 0.48627450980392156
- Evolution phase: Early exploration phase (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Areas with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with lo
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:02:02,981 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:02:02,981 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:02:16,385 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 25, 38, 7, 20, 33, 46, 5, 18, 31, 44, 3, 16, 29, 42, 9, 22, 35, 48, 1, 14, 27, 40, 8, 21, 34, 47, 6, 19, 32, 45, 4, 17, 30, 43, 2, 15, 28, 41, 10, 23, 36, 49, 11, 24, 37, 50, 13, 26, 39, 51],
  "targeted_regions": "High-density and adjacent low-density cells",
  "strategy_comment": "Balanced exploration of opportunity regions with sparse cells"
}
```
2025-06-26 20:02:16,385 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:02:16,385 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27914.0, 路径: [0, 12, 25, 38, 7, 20, 33, 46, 5, 18, 31, 44, 3, 16, 29, 42, 9, 22, 35, 48, 1, 14, 27, 40, 8, 21, 34, 47, 6, 19, 32, 45, 4, 17, 30, 43, 2, 15, 28, 41, 10, 23, 36, 49, 11, 24, 37, 50, 13, 26, 39, 51]
2025-06-26 20:02:16,385 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 25, 38, 7, 20, 33, 46, 5, 18, 31, 44, 3, 16, 29, 42, 9, 22, 35, 48, 1, 14, 27, 40, 8, 21, 34, 47, 6, 19, 32, 45, 4, 17, 30, 43, 2, 15, 28, 41, 10, 23, 36, 49, 11, 24, 37, 50, 13, 26, 39, 51], 'cur_cost': 27914.0}
2025-06-26 20:02:16,385 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:02:16,385 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:02:16,387 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 31786.0 (rank 90.0%)
- Population diversity: 0.5725490196078431
- Evolution phase: Early exploration phase (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Areas with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with lo
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:02:16,387 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:02:16,387 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:02:28,656 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "High-density and adjacent low-density cells",
  "strategy_comment": "Leverage modular jumps to balance exploration"
}
```
2025-06-26 20:02:28,656 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:02:28,656 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:02:28,656 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:02:28,656 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:02:28,656 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:02:28,656 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 22140.0 (rank 0.0%)
- Population diversity: 0.49607843137254903
- Evolution phase: Early exploration phase (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Areas with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with lo
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:02:28,656 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:02:28,656 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:02:42,302 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 25, 38, 51, 11, 24, 37, 50, 10, 23, 36, 49, 9, 22, 35, 48, 8, 21, 34, 47, 7, 20, 33, 46, 6, 19, 32, 45, 5, 18, 31, 44, 4, 17, 30, 43, 3, 16, 29, 42, 2, 15, 28, 41, 1, 14, 27, 40, 13, 26, 39, 52],
  "targeted_regions": "High-density and adjacent low-density cells",
  "strategy_comment": "Leverage opportunity regions while exploring adjacent sparse cells"
}
```
2025-06-26 20:02:42,317 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:02:42,317 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26500.0, 路径: [0, 12, 25, 38, 51, 11, 24, 37, 50, 10, 23, 36, 49, 9, 22, 35, 48, 8, 21, 34, 47, 7, 20, 33, 46, 6, 19, 32, 45, 5, 18, 31, 44, 4, 17, 30, 43, 3, 16, 29, 42, 2, 15, 28, 41, 1, 14, 27, 40, 13, 26, 39]
2025-06-26 20:02:42,317 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 25, 38, 51, 11, 24, 37, 50, 10, 23, 36, 49, 9, 22, 35, 48, 8, 21, 34, 47, 7, 20, 33, 46, 6, 19, 32, 45, 5, 18, 31, 44, 4, 17, 30, 43, 3, 16, 29, 42, 2, 15, 28, 41, 1, 14, 27, 40, 13, 26, 39], 'cur_cost': 26500.0}
2025-06-26 20:02:42,317 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:02:42,318 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:02:42,318 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 22140.0 (rank 0.0%)
- Population diversity: 0.5215686274509804
- Evolution phase: Early exploration phase (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Areas with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with lo
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:02:42,318 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:02:42,318 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:02:55,619 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "High-density and adjacent low-density cells",
  "strategy_comment": "Leverage modular jumps to explore sparse regions"
}
```
2025-06-26 20:02:55,619 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:02:55,624 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:02:55,624 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:02:55,624 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:02:55,624 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:02:55,624 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:02:55,624 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 30795.0
2025-06-26 20:02:56,126 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:02:56,126 - ExploitationExpert - INFO - res_population_costs: [8089, 26623, 7542]
2025-06-26 20:02:56,126 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64), array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-06-26 20:02:56,127 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:02:56,127 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 42, 7, 19, 31, 43, 8, 20, 32, 44, 9, 21, 33, 45, 10, 22, 34, 46, 11, 23, 35, 47], 'cur_cost': 28344.0}, {'tour': [0, 12, 25, 38, 7, 20, 33, 46, 5, 18, 31, 44, 3, 16, 29, 42, 9, 22, 35, 48, 1, 14, 27, 40, 8, 21, 34, 47, 6, 19, 32, 45, 4, 17, 30, 43, 2, 15, 28, 41, 10, 23, 36, 49, 11, 24, 37, 50, 13, 26, 39, 51], 'cur_cost': 27914.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 25, 38, 51, 11, 24, 37, 50, 10, 23, 36, 49, 9, 22, 35, 48, 8, 21, 34, 47, 7, 20, 33, 46, 6, 19, 32, 45, 5, 18, 31, 44, 4, 17, 30, 43, 3, 16, 29, 42, 2, 15, 28, 41, 1, 14, 27, 40, 13, 26, 39], 'cur_cost': 26500.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': array([44,  3, 20, 11, 45, 31, 49,  1, 37,  9, 46, 35,  2, 25, 47,  5, 43,
        7, 24, 26, 28, 51, 17,  4, 12, 14, 30,  6,  0, 38, 39, 33, 32, 23,
       42, 36, 29, 22, 16, 21,  8, 13, 40, 19, 15, 34, 27, 10, 18, 41, 48,
       50]), 'cur_cost': 30795.0}, {'tour': array([21, 38,  9, 20,  4, 45, 11,  1, 19, 46, 28, 34,  7, 24, 49, 12,  5,
        6, 31,  8, 18, 47, 26, 35,  3, 37, 33, 27, 30,  0, 40, 25, 36, 41,
       23, 15, 10, 29, 22, 51, 14, 48, 50, 44, 17, 13,  2, 16, 39, 32, 42,
       43]), 'cur_cost': 31094.0}, {'tour': array([40, 36,  6, 34, 33,  0, 18, 19,  9, 30, 35,  3, 51,  5, 48, 20, 45,
       17, 39, 10, 13,  1, 46, 25, 24, 29, 23, 42, 11, 41, 31, 32, 50, 14,
       44, 28, 38,  8, 16,  2, 26, 49, 43, 27,  4,  7, 37, 47, 15, 22, 12,
       21]), 'cur_cost': 28774.0}]
2025-06-26 20:02:56,129 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-26 20:02:56,129 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-26 20:02:56,129 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:02:56,129 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:02:56,129 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:02:56,129 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:02:56,129 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 30402.0
2025-06-26 20:02:56,633 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:02:56,633 - ExploitationExpert - INFO - res_population_costs: [8089, 26623, 7542]
2025-06-26 20:02:56,634 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64), array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-06-26 20:02:56,635 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:02:56,635 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 42, 7, 19, 31, 43, 8, 20, 32, 44, 9, 21, 33, 45, 10, 22, 34, 46, 11, 23, 35, 47], 'cur_cost': 28344.0}, {'tour': [0, 12, 25, 38, 7, 20, 33, 46, 5, 18, 31, 44, 3, 16, 29, 42, 9, 22, 35, 48, 1, 14, 27, 40, 8, 21, 34, 47, 6, 19, 32, 45, 4, 17, 30, 43, 2, 15, 28, 41, 10, 23, 36, 49, 11, 24, 37, 50, 13, 26, 39, 51], 'cur_cost': 27914.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 25, 38, 51, 11, 24, 37, 50, 10, 23, 36, 49, 9, 22, 35, 48, 8, 21, 34, 47, 7, 20, 33, 46, 6, 19, 32, 45, 5, 18, 31, 44, 4, 17, 30, 43, 3, 16, 29, 42, 2, 15, 28, 41, 1, 14, 27, 40, 13, 26, 39], 'cur_cost': 26500.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': array([44,  3, 20, 11, 45, 31, 49,  1, 37,  9, 46, 35,  2, 25, 47,  5, 43,
        7, 24, 26, 28, 51, 17,  4, 12, 14, 30,  6,  0, 38, 39, 33, 32, 23,
       42, 36, 29, 22, 16, 21,  8, 13, 40, 19, 15, 34, 27, 10, 18, 41, 48,
       50]), 'cur_cost': 30795.0}, {'tour': array([47, 50, 10, 48, 28, 26,  0, 35, 32, 12, 41, 49, 19, 27,  8,  3, 29,
       42, 51, 39, 33, 16, 15, 23, 18, 45, 24, 22,  7, 11, 40, 30, 21, 17,
       36,  9,  6, 46, 44, 31, 25, 13, 34,  5,  4, 43, 37,  2, 38,  1, 14,
       20]), 'cur_cost': 30402.0}, {'tour': array([40, 36,  6, 34, 33,  0, 18, 19,  9, 30, 35,  3, 51,  5, 48, 20, 45,
       17, 39, 10, 13,  1, 46, 25, 24, 29, 23, 42, 11, 41, 31, 32, 50, 14,
       44, 28, 38,  8, 16,  2, 26, 49, 43, 27,  4,  7, 37, 47, 15, 22, 12,
       21]), 'cur_cost': 28774.0}]
2025-06-26 20:02:56,636 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:02:56,637 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-26 20:02:56,637 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:02:56,637 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:02:56,637 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:02:56,637 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:02:56,637 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 28832.0
2025-06-26 20:02:57,139 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:02:57,139 - ExploitationExpert - INFO - res_population_costs: [8089, 26623, 7542]
2025-06-26 20:02:57,139 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64), array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64)]
2025-06-26 20:02:57,141 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:02:57,141 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 42, 7, 19, 31, 43, 8, 20, 32, 44, 9, 21, 33, 45, 10, 22, 34, 46, 11, 23, 35, 47], 'cur_cost': 28344.0}, {'tour': [0, 12, 25, 38, 7, 20, 33, 46, 5, 18, 31, 44, 3, 16, 29, 42, 9, 22, 35, 48, 1, 14, 27, 40, 8, 21, 34, 47, 6, 19, 32, 45, 4, 17, 30, 43, 2, 15, 28, 41, 10, 23, 36, 49, 11, 24, 37, 50, 13, 26, 39, 51], 'cur_cost': 27914.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 25, 38, 51, 11, 24, 37, 50, 10, 23, 36, 49, 9, 22, 35, 48, 8, 21, 34, 47, 7, 20, 33, 46, 6, 19, 32, 45, 5, 18, 31, 44, 4, 17, 30, 43, 3, 16, 29, 42, 2, 15, 28, 41, 1, 14, 27, 40, 13, 26, 39], 'cur_cost': 26500.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': array([44,  3, 20, 11, 45, 31, 49,  1, 37,  9, 46, 35,  2, 25, 47,  5, 43,
        7, 24, 26, 28, 51, 17,  4, 12, 14, 30,  6,  0, 38, 39, 33, 32, 23,
       42, 36, 29, 22, 16, 21,  8, 13, 40, 19, 15, 34, 27, 10, 18, 41, 48,
       50]), 'cur_cost': 30795.0}, {'tour': array([47, 50, 10, 48, 28, 26,  0, 35, 32, 12, 41, 49, 19, 27,  8,  3, 29,
       42, 51, 39, 33, 16, 15, 23, 18, 45, 24, 22,  7, 11, 40, 30, 21, 17,
       36,  9,  6, 46, 44, 31, 25, 13, 34,  5,  4, 43, 37,  2, 38,  1, 14,
       20]), 'cur_cost': 30402.0}, {'tour': array([26, 38, 50, 46, 45, 24, 14, 27,  2, 35, 33,  1, 48, 28, 39,  7, 49,
       15, 21, 32, 42,  8,  6, 41, 40, 11,  4, 51, 20, 18, 36, 16, 31, 29,
       47, 22, 19,  5, 43, 13,  3, 44,  0, 37, 23,  9, 30, 25, 34, 10, 17,
       12]), 'cur_cost': 28832.0}]
2025-06-26 20:02:57,142 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-26 20:02:57,142 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-26 20:02:57,142 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:02:57,142 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 5, 17, 29, 41, 6, 18, 30, 42, 7, 19, 31, 43, 8, 20, 32, 44, 9, 21, 33, 45, 10, 22, 34, 46, 11, 23, 35, 47], 'cur_cost': 28344.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 25, 38, 7, 20, 33, 46, 5, 18, 31, 44, 3, 16, 29, 42, 9, 22, 35, 48, 1, 14, 27, 40, 8, 21, 34, 47, 6, 19, 32, 45, 4, 17, 30, 43, 2, 15, 28, 41, 10, 23, 36, 49, 11, 24, 37, 50, 13, 26, 39, 51], 'cur_cost': 27914.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 25, 38, 51, 11, 24, 37, 50, 10, 23, 36, 49, 9, 22, 35, 48, 8, 21, 34, 47, 7, 20, 33, 46, 6, 19, 32, 45, 5, 18, 31, 44, 4, 17, 30, 43, 3, 16, 29, 42, 2, 15, 28, 41, 1, 14, 27, 40, 13, 26, 39], 'cur_cost': 26500.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}]
2025-06-26 20:02:57,144 - __main__ - INFO - 进化阶段完成
2025-06-26 20:02:57,144 - __main__ - INFO - 开始评估阶段
2025-06-26 20:02:57,144 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:02:57,144 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:02:57,144 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 22140.0 → 26500.0 (Δ -4360.0)
- Mean cost: 25541.2 → 29426.3
- Diversity: 0.461 → 0.492 (Δ 0.0314)

#### Elite metrics
- Elite count: 2 → 3
- Best elite cost: 8089 → 7542 (Δ 547)

#### Strategy performance
- Explore: 7 inds, success 28.6% , avg Δ -4404.29
- Exploit: 3 inds, success 33.3% , avg Δ -2673.67

#### Other indicators
- No-change individuals: 0
- Regression individuals: 7
- Historical trends: Last 1 iterations: costs [22140.0], diversity [0.6478632478632478]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:02:57,145 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:03:10,575 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "avg_improvement": -4404.29,
      "elite_contribution": "primary"
    },
    "exploit": {
      "success_rate": 33.3,
      "avg_improvement": -2673.67,
      "elite_contribution": "secondary"
    }
  },
  "balance_state": {
    "current_ratio": "70/30",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": false
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.6,
    "elite_preservation": 3,
    "mutation_intensity": "moderate"
  }
}
```
2025-06-26 20:03:10,597 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:03:10,597 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "avg_improvement": -4404.29,
      "elite_contribution": "primary"
    },
    "exploit": {
      "success_rate": 33.3,
      "avg_improvement": -2673.67,
      "elite_contribution": "secondary"
    }
  },
  "balance_state": {
    "current_ratio": "70/30",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": false
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.6,
    "elite_preservation": 3,
    "mutation_intensity": "moderate"
  }
}
```
2025-06-26 20:03:10,597 - __main__ - INFO - 评估阶段完成
2025-06-26 20:03:10,597 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "avg_improvement": -4404.29,
      "elite_contribution": "primary"
    },
    "exploit": {
      "success_rate": 33.3,
      "avg_improvement": -2673.67,
      "elite_contribution": "secondary"
    }
  },
  "balance_state": {
    "current_ratio": "70/30",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": false
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.6,
    "elite_preservation": 3,
    "mutation_intensity": "moderate"
  }
}
```
2025-06-26 20:03:10,597 - __main__ - INFO - 当前最佳适应度: 26500.0
2025-06-26 20:03:10,597 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\berlin52_route_1.pkl
2025-06-26 20:03:10,597 - __main__ - INFO - berlin52 开始进化第 3 代
2025-06-26 20:03:10,597 - __main__ - INFO - 开始分析阶段
2025-06-26 20:03:10,597 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:03:10,614 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 26500.0, 'max': 30795.0, 'mean': 29426.3, 'std': 1369.4857465486818}, 'diversity': 0.7572649572649574, 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:03:10,615 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 26500.0, 'max': 30795.0, 'mean': 29426.3, 'std': 1369.4857465486818}, 'diversity_level': 0.7572649572649574, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'coordinates': [[565, 575], [25, 185], [345, 750], [945, 685], [845, 655], [880, 660], [25, 230], [525, 1000], [580, 1175], [650, 1130], [1605, 620], [1220, 580], [1465, 200], [1530, 5], [845, 680], [725, 370], [145, 665], [415, 635], [510, 875], [560, 365], [300, 465], [520, 585], [480, 415], [835, 625], [975, 580], [1215, 245], [1320, 315], [1250, 400], [660, 180], [410, 250], [420, 555], [575, 665], [1150, 1160], [700, 580], [685, 595], [685, 610], [770, 610], [795, 645], [720, 635], [760, 650], [475, 960], [95, 260], [875, 920], [700, 500], [555, 815], [830, 485], [1170, 65], [830, 610], [605, 625], [595, 360], [1340, 725], [1740, 245]], 'distance_matrix': array([[   0.,  666.,  281., ...,  217.,  789., 1220.],
       [ 666.,    0.,  649., ...,  596., 1422., 1716.],
       [ 281.,  649.,    0., ...,  463.,  995., 1484.],
       ...,
       [ 217.,  596.,  463., ...,    0.,  830., 1151.],
       [ 789., 1422.,  995., ...,  830.,    0.,  625.],
       [1220., 1716., 1484., ..., 1151.,  625.,    0.]])}
2025-06-26 20:03:10,615 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:03:10,615 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:03:10,615 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:03:10,617 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:03:10,617 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:03:10,617 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:03:10,617 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:03:10,617 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:03:10,617 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:03:10,618 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:03:10,618 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 18958.0, 'avg_gap': 15341.633333333333}, 'structure_gap': {'unique_elite_edges': 127, 'unique_pop_edges': 253, 'common_edges': 19}}, 'elite_diversity': {'diversity_score': 0.75}}
2025-06-26 20:03:10,618 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:03:10,618 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:03:10,619 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:03:10,620 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 26500.0, mean 29426.3, max 30795.0, std 1369.4857465486818
- diversity: 0.7572649572649574
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [22140.0, 26500.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [25.0, 5.0, 1740.0, 1175.0]
- centroid/spread: [758.462, 564.904] | rms 467.889
- density_grid(3x3): [[6, 2, 6], [9, 17, 4], [5, 3, 0]]
- edge_len_stats(min,q1,med,q3,max): [15.0, 308.0, 525.0, 789.75, 1716.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 6, 7, 5, 10, 6, 5, 6]
- nn_median_dist: 78.079

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:03:10,621 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:03:33,505 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 15.0 to 1716.0 with Q1-Q3 spread of 481.75)",
    "modality": "Potentially multimodal (direction histogram shows relatively balanced distribution across sectors)",
    "deceptiveness": "Possible mild deceptiveness (long_edge_ratio of 0.25 suggests some misleading long connections)"
  },
  "population_state": {
    "coverage": "Fair (diversity 0.76 with uneven density_grid distribution)",
    "convergence": "Early-stage (0.0 convergence, no stagnation detected)",
    "spread": "Clustered in central regions (high density_grid values around centroid)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Edges in max length quartile (789.75-1716.0)",
    "low_density_areas": "Bottom-right grid cell (0 density) and top-middle (2 density)"
  },
  "opportunity_regions": {
    "high_density_nodes": "Central grid cell (17 density) and adjacent left cell (9 density)",
    "promising_edges": "Medium-length edges (308-789.75) in direction sectors with higher counts"
  },
  "evolution_phase": "Exploration-dominant (high diversity, no elites, recent best cost improvement)",
  "evolution_direction": [
    "Prioritize 2-opt operators for long-edge reduction",
    "Focus crossover on medium-length edges in high-density regions",
    "Introduce directed mutation toward under-explored grid cells",
    "Maintain diversity through niching in spatial clusters"
  ]
}
```
2025-06-26 20:03:33,516 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:03:33,516 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 15.0 to 1716.0 with Q1-Q3 spread of 481.75)', 'modality': 'Potentially multimodal (direction histogram shows relatively balanced distribution across sectors)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio of 0.25 suggests some misleading long connections)'}, 'population_state': {'coverage': 'Fair (diversity 0.76 with uneven density_grid distribution)', 'convergence': 'Early-stage (0.0 convergence, no stagnation detected)', 'spread': 'Clustered in central regions (high density_grid values around centroid)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max length quartile (789.75-1716.0)', 'low_density_areas': 'Bottom-right grid cell (0 density) and top-middle (2 density)'}, 'opportunity_regions': {'high_density_nodes': 'Central grid cell (17 density) and adjacent left cell (9 density)', 'promising_edges': 'Medium-length edges (308-789.75) in direction sectors with higher counts'}, 'evolution_phase': 'Exploration-dominant (high diversity, no elites, recent best cost improvement)', 'evolution_direction': ['Prioritize 2-opt operators for long-edge reduction', 'Focus crossover on medium-length edges in high-density regions', 'Introduce directed mutation toward under-explored grid cells', 'Maintain diversity through niching in spatial clusters']}
2025-06-26 20:03:33,516 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:03:33,516 - __main__ - INFO - 分析阶段完成
2025-06-26 20:03:33,516 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 15.0 to 1716.0 with Q1-Q3 spread of 481.75)', 'modality': 'Potentially multimodal (direction histogram shows relatively balanced distribution across sectors)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio of 0.25 suggests some misleading long connections)'}, 'population_state': {'coverage': 'Fair (diversity 0.76 with uneven density_grid distribution)', 'convergence': 'Early-stage (0.0 convergence, no stagnation detected)', 'spread': 'Clustered in central regions (high density_grid values around centroid)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max length quartile (789.75-1716.0)', 'low_density_areas': 'Bottom-right grid cell (0 density) and top-middle (2 density)'}, 'opportunity_regions': {'high_density_nodes': 'Central grid cell (17 density) and adjacent left cell (9 density)', 'promising_edges': 'Medium-length edges (308-789.75) in direction sectors with higher counts'}, 'evolution_phase': 'Exploration-dominant (high diversity, no elites, recent best cost improvement)', 'evolution_direction': ['Prioritize 2-opt operators for long-edge reduction', 'Focus crossover on medium-length edges in high-density regions', 'Introduce directed mutation toward under-explored grid cells', 'Maintain diversity through niching in spatial clusters']}
2025-06-26 20:03:33,516 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:03:33,516 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:03:33,516 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 26500.0
  • mean_cost: 29426.3
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 15.0 to 1716.0 with Q1-Q3 spread of 481.75), modality Potentially multimodal (direction histogram shows relatively balanced distribution across sectors), deceptiveness Possible mild deceptiveness (long_edge_ratio of 0.25 suggests some misleading long connections)
- Evolution phase: Exploration-dominant (high diversity, no elites, recent best cost improvement)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_areas
- Opportunity regions (# 2): high_density_nodes, promising_edges
- Last-iteration feedback: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:03:33,516 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:03:33,516 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:03:42,771 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and landscape focus suggestion 'balance' favor exploration. Top 30% individuals exploit promising regions while others explore."
}
```
2025-06-26 20:03:42,772 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:03:42,772 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:03:42,772 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and landscape focus suggestion 'balance' favor exploration. Top 30% individuals exploit promising regions while others explore."
}
```
2025-06-26 20:03:42,773 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:03:42,773 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:03:42,773 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and landscape focus suggestion 'balance' favor exploration. Top 30% individuals exploit promising regions while others explore."
}
```
2025-06-26 20:03:42,773 - __main__ - INFO - 开始进化阶段
2025-06-26 20:03:42,773 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:03:42,773 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:03:42,773 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30369.0 (rank 40.0%)
- Population diversity: 0.492156862745098
- Evolution phase: Exploration-dominant (high diversity, no elites, recent best cost improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_no, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:03:42,773 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:03:42,773 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:03:55,162 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "high_density_no, promising_edges",
  "strategy_comment": "Leverage modular jumps to explore sparse cells while avoiding long edges"
}
```
2025-06-26 20:03:55,162 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:03:55,162 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:03:55,162 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:03:55,165 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:03:55,165 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:03:55,165 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30369.0 (rank 40.0%)
- Population diversity: 0.492156862745098
- Evolution phase: Exploration-dominant (high diversity, no elites, recent best cost improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_no, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:03:55,165 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:03:55,165 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:04:08,246 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "high_density_no, promising_edges",
  "strategy_comment": "Leverage modular jumps to explore sparse cells while avoiding long edges"
}
```
2025-06-26 20:04:08,246 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:04:08,246 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:04:08,246 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:04:08,246 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:04:08,246 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:04:08,260 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 28344.0 (rank 20.0%)
- Population diversity: 0.492156862745098
- Evolution phase: Exploration-dominant (high diversity, no elites, recent best cost improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_no, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:04:08,269 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:04:08,269 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:04:21,835 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "high_density_no, promising_edges",
  "strategy_comment": "Leverage modular jumps to explore sparse cells near opportunities"
}
```
2025-06-26 20:04:21,835 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:04:21,835 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:04:21,835 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:04:21,835 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:04:21,838 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:04:21,838 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 27914.0 (rank 10.0%)
- Population diversity: 0.4764705882352941
- Evolution phase: Exploration-dominant (high diversity, no elites, recent best cost improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_no, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:04:21,838 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:04:21,838 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:04:35,195 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 25, 38, 7, 19, 31, 44, 3, 16, 29, 42, 8, 21, 34, 47, 11, 24, 37, 50, 6, 18, 30, 43, 2, 15, 28, 41, 9, 22, 35, 48, 13, 26, 39, 51, 5, 17, 32, 45, 10, 23, 36, 49, 4, 20, 33, 46, 14, 27, 40, 1],
  "targeted_regions": "high_density_no, promising_edges",
  "strategy_comment": "Balanced exploration of sparse cells near opportunities, avoiding long edges"
}
```
2025-06-26 20:04:35,195 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:04:35,195 - ExplorationExpert - INFO - 探索路径生成完成，成本: 29238.0, 路径: [0, 12, 25, 38, 7, 19, 31, 44, 3, 16, 29, 42, 8, 21, 34, 47, 11, 24, 37, 50, 6, 18, 30, 43, 2, 15, 28, 41, 9, 22, 35, 48, 13, 26, 39, 51, 5, 17, 32, 45, 10, 23, 36, 49, 4, 20, 33, 46, 14, 27, 40, 1]
2025-06-26 20:04:35,195 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 16, 29, 42, 8, 21, 34, 47, 11, 24, 37, 50, 6, 18, 30, 43, 2, 15, 28, 41, 9, 22, 35, 48, 13, 26, 39, 51, 5, 17, 32, 45, 10, 23, 36, 49, 4, 20, 33, 46, 14, 27, 40, 1], 'cur_cost': 29238.0}
2025-06-26 20:04:35,195 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:04:35,195 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:04:35,195 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30369.0 (rank 30.0%)
- Population diversity: 0.4823529411764706
- Evolution phase: Exploration-dominant (high diversity, no elites, recent best cost improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_no, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:04:35,195 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:04:35,195 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:04:48,365 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "high_density_no, promising_edges",
  "strategy_comment": "Leverage modular jumps to explore sparse cells"
}
```
2025-06-26 20:04:48,365 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:04:48,365 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:04:48,365 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:04:48,365 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:04:48,365 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:04:48,365 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26500.0 (rank 0.0%)
- Population diversity: 0.4823529411764706
- Evolution phase: Exploration-dominant (high diversity, no elites, recent best cost improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_no, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:04:48,365 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:04:48,365 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:05:02,463 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "high_density_no, promising_edges",
  "strategy_comment": "Leverage modular jumps to explore sparse cells while avoiding long edges"
}
```
2025-06-26 20:05:02,463 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:05:02,463 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:05:02,464 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:05:02,464 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:05:02,464 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:05:02,464 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30369.0 (rank 20.0%)
- Population diversity: 0.45098039215686275
- Evolution phase: Exploration-dominant (high diversity, no elites, recent best cost improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_no, promising_edges
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:05:02,464 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:05:02,464 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:05:15,559 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "high_density_no, promising_edges",
  "strategy_comment": "Leverage modular jumps to explore sparse cells while avoiding long edges"
}
```
2025-06-26 20:05:15,559 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:05:15,559 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:05:15,559 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:05:15,559 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:05:15,559 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:05:15,559 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:05:15,559 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 29618.0
2025-06-26 20:05:16,066 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:05:16,066 - ExploitationExpert - INFO - res_population_costs: [7542, 8089, 26623]
2025-06-26 20:05:16,067 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:05:16,067 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:05:16,067 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 16, 29, 42, 8, 21, 34, 47, 11, 24, 37, 50, 6, 18, 30, 43, 2, 15, 28, 41, 9, 22, 35, 48, 13, 26, 39, 51, 5, 17, 32, 45, 10, 23, 36, 49, 4, 20, 33, 46, 14, 27, 40, 1], 'cur_cost': 29238.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': array([12, 51, 26, 21, 20,  1, 13, 23, 33,  8, 35, 27, 44,  6, 30,  3, 49,
        7, 17, 37, 32, 22, 29, 24, 15, 19, 31, 41,  4, 40, 28, 39, 45, 14,
       36, 43,  9, 11,  0, 16,  5, 47, 10, 38, 46, 42, 25,  2, 48, 34, 50,
       18]), 'cur_cost': 29618.0}, {'tour': array([47, 50, 10, 48, 28, 26,  0, 35, 32, 12, 41, 49, 19, 27,  8,  3, 29,
       42, 51, 39, 33, 16, 15, 23, 18, 45, 24, 22,  7, 11, 40, 30, 21, 17,
       36,  9,  6, 46, 44, 31, 25, 13, 34,  5,  4, 43, 37,  2, 38,  1, 14,
       20]), 'cur_cost': 30402.0}, {'tour': array([26, 38, 50, 46, 45, 24, 14, 27,  2, 35, 33,  1, 48, 28, 39,  7, 49,
       15, 21, 32, 42,  8,  6, 41, 40, 11,  4, 51, 20, 18, 36, 16, 31, 29,
       47, 22, 19,  5, 43, 13,  3, 44,  0, 37, 23,  9, 30, 25, 34, 10, 17,
       12]), 'cur_cost': 28832.0}]
2025-06-26 20:05:16,069 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:05:16,069 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-26 20:05:16,069 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:05:16,070 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:05:16,070 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:05:16,070 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:05:16,070 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 29720.0
2025-06-26 20:05:16,574 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:05:16,574 - ExploitationExpert - INFO - res_population_costs: [7542, 8089, 26623]
2025-06-26 20:05:16,574 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:05:16,577 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:05:16,577 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 16, 29, 42, 8, 21, 34, 47, 11, 24, 37, 50, 6, 18, 30, 43, 2, 15, 28, 41, 9, 22, 35, 48, 13, 26, 39, 51, 5, 17, 32, 45, 10, 23, 36, 49, 4, 20, 33, 46, 14, 27, 40, 1], 'cur_cost': 29238.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': array([12, 51, 26, 21, 20,  1, 13, 23, 33,  8, 35, 27, 44,  6, 30,  3, 49,
        7, 17, 37, 32, 22, 29, 24, 15, 19, 31, 41,  4, 40, 28, 39, 45, 14,
       36, 43,  9, 11,  0, 16,  5, 47, 10, 38, 46, 42, 25,  2, 48, 34, 50,
       18]), 'cur_cost': 29618.0}, {'tour': array([12, 39, 31, 42, 32, 36, 19, 34, 43, 41, 47, 46, 11, 29, 22,  3, 28,
       15, 35,  7, 17, 21,  8, 26, 30, 24,  1, 25, 33, 20, 16, 51, 13, 23,
       10,  4, 14,  2,  5,  9, 27, 38, 48,  6, 18,  0, 37, 49, 45, 40, 50,
       44]), 'cur_cost': 29720.0}, {'tour': array([26, 38, 50, 46, 45, 24, 14, 27,  2, 35, 33,  1, 48, 28, 39,  7, 49,
       15, 21, 32, 42,  8,  6, 41, 40, 11,  4, 51, 20, 18, 36, 16, 31, 29,
       47, 22, 19,  5, 43, 13,  3, 44,  0, 37, 23,  9, 30, 25, 34, 10, 17,
       12]), 'cur_cost': 28832.0}]
2025-06-26 20:05:16,578 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:05:16,578 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-26 20:05:16,578 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:05:16,578 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:05:16,579 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:05:16,579 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:05:16,579 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 31240.0
2025-06-26 20:05:17,081 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:05:17,081 - ExploitationExpert - INFO - res_population_costs: [7542, 8089, 26623]
2025-06-26 20:05:17,081 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:05:17,083 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:05:17,083 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 16, 29, 42, 8, 21, 34, 47, 11, 24, 37, 50, 6, 18, 30, 43, 2, 15, 28, 41, 9, 22, 35, 48, 13, 26, 39, 51, 5, 17, 32, 45, 10, 23, 36, 49, 4, 20, 33, 46, 14, 27, 40, 1], 'cur_cost': 29238.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': array([12, 51, 26, 21, 20,  1, 13, 23, 33,  8, 35, 27, 44,  6, 30,  3, 49,
        7, 17, 37, 32, 22, 29, 24, 15, 19, 31, 41,  4, 40, 28, 39, 45, 14,
       36, 43,  9, 11,  0, 16,  5, 47, 10, 38, 46, 42, 25,  2, 48, 34, 50,
       18]), 'cur_cost': 29618.0}, {'tour': array([12, 39, 31, 42, 32, 36, 19, 34, 43, 41, 47, 46, 11, 29, 22,  3, 28,
       15, 35,  7, 17, 21,  8, 26, 30, 24,  1, 25, 33, 20, 16, 51, 13, 23,
       10,  4, 14,  2,  5,  9, 27, 38, 48,  6, 18,  0, 37, 49, 45, 40, 50,
       44]), 'cur_cost': 29720.0}, {'tour': array([12, 43, 16, 32, 15, 40, 14, 47, 20, 34, 10, 21, 38, 23, 31, 49, 48,
       39, 13, 27, 44, 36, 45, 50,  1, 29, 25, 17,  3,  9,  8,  5,  0,  2,
       26,  7, 22, 51, 37, 18, 11, 46, 30, 19, 33,  4, 42,  6, 28, 24, 41,
       35]), 'cur_cost': 31240.0}]
2025-06-26 20:05:17,084 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-26 20:05:17,085 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-06-26 20:05:17,085 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:05:17,085 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 16, 29, 42, 8, 21, 34, 47, 11, 24, 37, 50, 6, 18, 30, 43, 2, 15, 28, 41, 9, 22, 35, 48, 13, 26, 39, 51, 5, 17, 32, 45, 10, 23, 36, 49, 4, 20, 33, 46, 14, 27, 40, 1], 'cur_cost': 29238.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}]
2025-06-26 20:05:17,085 - __main__ - INFO - 进化阶段完成
2025-06-26 20:05:17,085 - __main__ - INFO - 开始评估阶段
2025-06-26 20:05:17,085 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:05:17,087 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:05:17,087 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 26500.0 → 29238.0 (Δ -2738.0)
- Mean cost: 29426.3 → 30203.0
- Diversity: 0.492 → 0.455 (Δ -0.0373)

#### Elite metrics
- Elite count: 3 → 3
- Best elite cost: 7542 → 7542 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 0.0% , avg Δ -1031.14
- Exploit: 3 inds, success 66.7% , avg Δ -183.0

#### Other indicators
- No-change individuals: 4
- Regression individuals: 4
- Historical trends: Last 2 iterations: costs [22140.0, 26500.0], diversity [0.6478632478632478, 0.7572649572649574]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:05:17,087 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:05:30,907 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "exploit": {
      "success_rate": 66.7,
      "avg_improvement": -183.0,
      "contribution": "primary elite improvement driver"
    },
    "explore": {
      "success_rate": 0.0,
      "avg_improvement": -1031.14,
      "contribution": "negative impact on population metrics"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore ratio too high given poor performance",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_suggestions": "Consider introducing new exploration operators or restarting worst-performing individuals"
  }
}
```
2025-06-26 20:05:30,924 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:05:30,925 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "exploit": {
      "success_rate": 66.7,
      "avg_improvement": -183.0,
      "contribution": "primary elite improvement driver"
    },
    "explore": {
      "success_rate": 0.0,
      "avg_improvement": -1031.14,
      "contribution": "negative impact on population metrics"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore ratio too high given poor performance",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_suggestions": "Consider introducing new exploration operators or restarting worst-performing individuals"
  }
}
```
2025-06-26 20:05:30,925 - __main__ - INFO - 评估阶段完成
2025-06-26 20:05:30,925 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "exploit": {
      "success_rate": 66.7,
      "avg_improvement": -183.0,
      "contribution": "primary elite improvement driver"
    },
    "explore": {
      "success_rate": 0.0,
      "avg_improvement": -1031.14,
      "contribution": "negative impact on population metrics"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore ratio too high given poor performance",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_suggestions": "Consider introducing new exploration operators or restarting worst-performing individuals"
  }
}
```
2025-06-26 20:05:30,925 - __main__ - INFO - 当前最佳适应度: 29238.0
2025-06-26 20:05:30,927 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\berlin52_route_2.pkl
2025-06-26 20:05:30,927 - __main__ - INFO - berlin52 开始进化第 4 代
2025-06-26 20:05:30,927 - __main__ - INFO - 开始分析阶段
2025-06-26 20:05:30,927 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:05:30,938 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 29238.0, 'max': 31240.0, 'mean': 30203.0, 'std': 524.1606623927439}, 'diversity': 0.6260683760683761, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:05:30,939 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 29238.0, 'max': 31240.0, 'mean': 30203.0, 'std': 524.1606623927439}, 'diversity_level': 0.6260683760683761, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'coordinates': [[565, 575], [25, 185], [345, 750], [945, 685], [845, 655], [880, 660], [25, 230], [525, 1000], [580, 1175], [650, 1130], [1605, 620], [1220, 580], [1465, 200], [1530, 5], [845, 680], [725, 370], [145, 665], [415, 635], [510, 875], [560, 365], [300, 465], [520, 585], [480, 415], [835, 625], [975, 580], [1215, 245], [1320, 315], [1250, 400], [660, 180], [410, 250], [420, 555], [575, 665], [1150, 1160], [700, 580], [685, 595], [685, 610], [770, 610], [795, 645], [720, 635], [760, 650], [475, 960], [95, 260], [875, 920], [700, 500], [555, 815], [830, 485], [1170, 65], [830, 610], [605, 625], [595, 360], [1340, 725], [1740, 245]], 'distance_matrix': array([[   0.,  666.,  281., ...,  217.,  789., 1220.],
       [ 666.,    0.,  649., ...,  596., 1422., 1716.],
       [ 281.,  649.,    0., ...,  463.,  995., 1484.],
       ...,
       [ 217.,  596.,  463., ...,    0.,  830., 1151.],
       [ 789., 1422.,  995., ...,  830.,    0.,  625.],
       [1220., 1716., 1484., ..., 1151.,  625.,    0.]])}
2025-06-26 20:05:30,939 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:05:30,939 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:05:30,939 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:05:30,940 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:05:30,940 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:05:30,940 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:05:30,940 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:05:30,940 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:05:30,940 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:05:30,943 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:05:30,943 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 21696.0, 'avg_gap': 16118.333333333334}, 'structure_gap': {'unique_elite_edges': 134, 'unique_pop_edges': 233, 'common_edges': 12}}, 'elite_diversity': {'diversity_score': 0.75}}
2025-06-26 20:05:30,943 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:05:30,943 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:05:30,946 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:05:30,947 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 29238.0, mean 30203.0, max 31240.0, std 524.1606623927439
- diversity: 0.6260683760683761
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [22140.0, 26500.0, 29238.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [25.0, 5.0, 1740.0, 1175.0]
- centroid/spread: [758.462, 564.904] | rms 467.889
- density_grid(3x3): [[6, 2, 6], [9, 17, 4], [5, 3, 0]]
- edge_len_stats(min,q1,med,q3,max): [15.0, 308.0, 525.0, 789.75, 1716.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 6, 7, 5, 10, 6, 5, 6]
- nn_median_dist: 78.079

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:05:30,947 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:05:57,541 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 15.0 to 1716.0, with a long_edge_ratio of 0.25 indicating significant variability)",
    "modality": "Potentially multimodal (direction_hist shows relatively even distribution across sectors, no single dominant direction)",
    "deceptiveness": "Possible deceptive regions (high std in cost_stats and no clear clustering, but insufficient structural signals to confirm)"
  },
  "population_state": {
    "coverage": "Moderate (diversity 0.626, density_grid shows uneven distribution with some high-density cells like center cell [1,1] with 17 nodes)",
    "convergence": "Early stage (convergence 0.0, recent_best_costs show improvement but no stagnation)"
  },
  "difficult_regions": [
    "Low-density cells (e.g., [2,2] with 0 nodes) or long-edge corridors (edges > q3 789.75)",
    "Regions with sparse node distribution (e.g., edges in direction sectors 4 or 7)"
  ],
  "opportunity_regions": [
    "High-density cell [1,1] (17 nodes) and adjacent cells [0,0], [0,2], [2,0] (5-6 nodes each)",
    "Edges with median lengths (~525.0) or directions with higher counts (sectors 4, 5)"
  ],
  "evolution_phase": "Exploration (early iteration, no elites, high diversity, improving but non-converged costs)",
  "evolution_direction": [
    "Prioritize crossover operators that exploit high-density regions (e.g., edge recombination in cell [1,1])",
    "Use mutation to explore low-density/long-edge regions (e.g., 2-opt on edges > q3 789.75)",
    "Maintain diversity via spatial-aware selection (e.g., favoring solutions covering sparse grid cells)"
  ]
}
```
2025-06-26 20:05:57,546 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:05:57,546 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 15.0 to 1716.0, with a long_edge_ratio of 0.25 indicating significant variability)', 'modality': 'Potentially multimodal (direction_hist shows relatively even distribution across sectors, no single dominant direction)', 'deceptiveness': 'Possible deceptive regions (high std in cost_stats and no clear clustering, but insufficient structural signals to confirm)'}, 'population_state': {'coverage': 'Moderate (diversity 0.626, density_grid shows uneven distribution with some high-density cells like center cell [1,1] with 17 nodes)', 'convergence': 'Early stage (convergence 0.0, recent_best_costs show improvement but no stagnation)'}, 'difficult_regions': ['Low-density cells (e.g., [2,2] with 0 nodes) or long-edge corridors (edges > q3 789.75)', 'Regions with sparse node distribution (e.g., edges in direction sectors 4 or 7)'], 'opportunity_regions': ['High-density cell [1,1] (17 nodes) and adjacent cells [0,0], [0,2], [2,0] (5-6 nodes each)', 'Edges with median lengths (~525.0) or directions with higher counts (sectors 4, 5)'], 'evolution_phase': 'Exploration (early iteration, no elites, high diversity, improving but non-converged costs)', 'evolution_direction': ['Prioritize crossover operators that exploit high-density regions (e.g., edge recombination in cell [1,1])', 'Use mutation to explore low-density/long-edge regions (e.g., 2-opt on edges > q3 789.75)', 'Maintain diversity via spatial-aware selection (e.g., favoring solutions covering sparse grid cells)']}
2025-06-26 20:05:57,547 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:05:57,547 - __main__ - INFO - 分析阶段完成
2025-06-26 20:05:57,547 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 15.0 to 1716.0, with a long_edge_ratio of 0.25 indicating significant variability)', 'modality': 'Potentially multimodal (direction_hist shows relatively even distribution across sectors, no single dominant direction)', 'deceptiveness': 'Possible deceptive regions (high std in cost_stats and no clear clustering, but insufficient structural signals to confirm)'}, 'population_state': {'coverage': 'Moderate (diversity 0.626, density_grid shows uneven distribution with some high-density cells like center cell [1,1] with 17 nodes)', 'convergence': 'Early stage (convergence 0.0, recent_best_costs show improvement but no stagnation)'}, 'difficult_regions': ['Low-density cells (e.g., [2,2] with 0 nodes) or long-edge corridors (edges > q3 789.75)', 'Regions with sparse node distribution (e.g., edges in direction sectors 4 or 7)'], 'opportunity_regions': ['High-density cell [1,1] (17 nodes) and adjacent cells [0,0], [0,2], [2,0] (5-6 nodes each)', 'Edges with median lengths (~525.0) or directions with higher counts (sectors 4, 5)'], 'evolution_phase': 'Exploration (early iteration, no elites, high diversity, improving but non-converged costs)', 'evolution_direction': ['Prioritize crossover operators that exploit high-density regions (e.g., edge recombination in cell [1,1])', 'Use mutation to explore low-density/long-edge regions (e.g., 2-opt on edges > q3 789.75)', 'Maintain diversity via spatial-aware selection (e.g., favoring solutions covering sparse grid cells)']}
2025-06-26 20:05:57,547 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:05:57,547 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:05:57,547 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 29238.0
  • mean_cost: 30203.0
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 15.0 to 1716.0, with a long_edge_ratio of 0.25 indicating significant variability), modality Potentially multimodal (direction_hist shows relatively even distribution across sectors, no single dominant direction), deceptiveness Possible deceptive regions (high std in cost_stats and no clear clustering, but insufficient structural signals to confirm)
- Evolution phase: Exploration (early iteration, no elites, high diversity, improving but non-converged costs)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Low-density cells (e, Regions with sparse 
- Opportunity regions (# 2): High-density cell [1, Edges with median le
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "exploit": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:05:57,547 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:05:57,547 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:06:08,069 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early iteration with low diversity and moderate ruggedness favors exploration. Top 60% explore to diversify, bottom 40% exploit current bests."
}
```
2025-06-26 20:06:08,071 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:06:08,071 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:06:08,071 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early iteration with low diversity and moderate ruggedness favors exploration. Top 60% explore to diversify, bottom 40% exploit current bests."
}
```
2025-06-26 20:06:08,071 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:06:08,071 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:06:08,072 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early iteration with low diversity and moderate ruggedness favors exploration. Top 60% explore to diversify, bottom 40% exploit current bests."
}
```
2025-06-26 20:06:08,072 - __main__ - INFO - 开始进化阶段
2025-06-26 20:06:08,072 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:06:08,072 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:06:08,073 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30369.0 (rank 30.0%)
- Population diversity: 0.4549019607843137
- Evolution phase: Exploration (early iteration, no elites, high diversity, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Edges with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with sp
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:06:08,073 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:06:08,073 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:06:21,521 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "High-density and adjacent sparse cells",
  "strategy_comment": "Leverage modular jumps to explore sparse regions while maintaining structure"
}
```
2025-06-26 20:06:21,524 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:06:21,524 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:06:21,524 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:06:21,524 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:06:21,524 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:06:21,524 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30369.0 (rank 30.0%)
- Population diversity: 0.4549019607843137
- Evolution phase: Exploration (early iteration, no elites, high diversity, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Edges with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with sp
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:06:21,527 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:06:21,527 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:06:36,352 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 25, 38, 7, 20, 33, 46, 15, 28, 41, 4, 17, 30, 43, 8, 21, 34, 47, 16, 29, 42, 5, 18, 31, 44, 9, 22, 35, 48, 19, 32, 45, 6, 23, 36, 49, 10, 27, 40, 3, 14, 37, 50, 13, 26, 39, 2, 11, 24, 51, 1],
  "targeted_regions": "High-density center, adjacent sparse edges",
  "strategy_comment": "Spiral pattern balancing opportunity regions and diversity"
}
```
2025-06-26 20:06:36,359 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:06:36,359 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27336.0, 路径: [0, 12, 25, 38, 7, 20, 33, 46, 15, 28, 41, 4, 17, 30, 43, 8, 21, 34, 47, 16, 29, 42, 5, 18, 31, 44, 9, 22, 35, 48, 19, 32, 45, 6, 23, 36, 49, 10, 27, 40, 3, 14, 37, 50, 13, 26, 39, 2, 11, 24, 51, 1]
2025-06-26 20:06:36,359 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 25, 38, 7, 20, 33, 46, 15, 28, 41, 4, 17, 30, 43, 8, 21, 34, 47, 16, 29, 42, 5, 18, 31, 44, 9, 22, 35, 48, 19, 32, 45, 6, 23, 36, 49, 10, 27, 40, 3, 14, 37, 50, 13, 26, 39, 2, 11, 24, 51, 1], 'cur_cost': 27336.0}
2025-06-26 20:06:36,359 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:06:36,359 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:06:36,359 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30369.0 (rank 40.0%)
- Population diversity: 0.49607843137254903
- Evolution phase: Exploration (early iteration, no elites, high diversity, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Edges with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with sp
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:06:36,359 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:06:36,359 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:06:49,847 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51],
  "targeted_regions": "High-density ce, adjacent sparse cells",
  "strategy_comment": "Balanced exploration with clustered jumps near opportunity regions"
}
```
2025-06-26 20:06:49,847 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:06:49,847 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27379.0, 路径: [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51]
2025-06-26 20:06:49,847 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}
2025-06-26 20:06:49,847 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:06:49,847 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:06:49,847 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 29238.0 (rank 20.0%)
- Population diversity: 0.515686274509804
- Evolution phase: Exploration (early iteration, no elites, high diversity, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Edges with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with sp
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:06:49,847 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:06:49,847 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:07:03,809 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 10, 23, 36, 49, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 11, 24, 37, 50, 1, 13, 26, 39, 6, 18, 32, 45, 20, 33, 46, 51],
  "targeted_regions": "High-density and adjacent sparse cells",
  "strategy_comment": "Balanced exploration of dense/sparse regions with skip-step sequencing"
}
```
2025-06-26 20:07:03,809 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:07:03,809 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27834.0, 路径: [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 10, 23, 36, 49, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 11, 24, 37, 50, 1, 13, 26, 39, 6, 18, 32, 45, 20, 33, 46, 51]
2025-06-26 20:07:03,809 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 10, 23, 36, 49, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 11, 24, 37, 50, 1, 13, 26, 39, 6, 18, 32, 45, 20, 33, 46, 51], 'cur_cost': 27834.0}
2025-06-26 20:07:03,809 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:07:03,809 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:07:03,809 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30369.0 (rank 50.0%)
- Population diversity: 0.4980392156862745
- Evolution phase: Exploration (early iteration, no elites, high diversity, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Edges with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with sp
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:07:03,809 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:07:03,815 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:07:17,179 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51],
  "targeted_regions": "High-density cells, adjacent sparse areas",
  "strategy_comment": "Balanced exploration of dense/sparse regions with radial jumps"
}
```
2025-06-26 20:07:17,187 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:07:17,187 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27379.0, 路径: [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51]
2025-06-26 20:07:17,187 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}
2025-06-26 20:07:17,187 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:07:17,187 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:07:17,187 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30369.0 (rank 60.0%)
- Population diversity: 0.4980392156862745
- Evolution phase: Exploration (early iteration, no elites, high diversity, improving but non-converged costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Edges with medi
- Difficult regions to avoid (sample): Low-density cel, Regions with sp
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:07:17,187 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:07:17,187 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:07:31,031 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "High-density and adjacent sparse cells",
  "strategy_comment": "Alternating high-density and sparse regions to balance exploration"
}
```
2025-06-26 20:07:31,047 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:07:31,047 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:07:31,047 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:07:31,047 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 20:07:31,047 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:07:31,047 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:07:31,047 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 30898.0
2025-06-26 20:07:31,549 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:07:31,549 - ExploitationExpert - INFO - res_population_costs: [7542, 8089, 26623]
2025-06-26 20:07:31,549 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:07:31,551 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:07:31,551 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 25, 38, 7, 20, 33, 46, 15, 28, 41, 4, 17, 30, 43, 8, 21, 34, 47, 16, 29, 42, 5, 18, 31, 44, 9, 22, 35, 48, 19, 32, 45, 6, 23, 36, 49, 10, 27, 40, 3, 14, 37, 50, 13, 26, 39, 2, 11, 24, 51, 1], 'cur_cost': 27336.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 10, 23, 36, 49, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 11, 24, 37, 50, 1, 13, 26, 39, 6, 18, 32, 45, 20, 33, 46, 51], 'cur_cost': 27834.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': array([22, 17, 32, 46,  3, 13, 45,  0, 44, 23, 15, 47, 25, 36, 10, 50, 21,
        7, 38, 20, 41,  4, 51,  1, 14, 39,  8,  5,  2, 29, 11, 18, 49, 30,
       28,  9, 40, 26, 48, 43, 34, 35, 12, 37, 24, 19, 16, 33, 31, 27,  6,
       42]), 'cur_cost': 30898.0}, {'tour': array([12, 51, 26, 21, 20,  1, 13, 23, 33,  8, 35, 27, 44,  6, 30,  3, 49,
        7, 17, 37, 32, 22, 29, 24, 15, 19, 31, 41,  4, 40, 28, 39, 45, 14,
       36, 43,  9, 11,  0, 16,  5, 47, 10, 38, 46, 42, 25,  2, 48, 34, 50,
       18]), 'cur_cost': 29618.0}, {'tour': array([12, 39, 31, 42, 32, 36, 19, 34, 43, 41, 47, 46, 11, 29, 22,  3, 28,
       15, 35,  7, 17, 21,  8, 26, 30, 24,  1, 25, 33, 20, 16, 51, 13, 23,
       10,  4, 14,  2,  5,  9, 27, 38, 48,  6, 18,  0, 37, 49, 45, 40, 50,
       44]), 'cur_cost': 29720.0}, {'tour': array([12, 43, 16, 32, 15, 40, 14, 47, 20, 34, 10, 21, 38, 23, 31, 49, 48,
       39, 13, 27, 44, 36, 45, 50,  1, 29, 25, 17,  3,  9,  8,  5,  0,  2,
       26,  7, 22, 51, 37, 18, 11, 46, 30, 19, 33,  4, 42,  6, 28, 24, 41,
       35]), 'cur_cost': 31240.0}]
2025-06-26 20:07:31,553 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:07:31,553 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-06-26 20:07:31,553 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 20:07:31,553 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:07:31,553 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:07:31,553 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:07:31,553 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 31911.0
2025-06-26 20:07:32,054 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:07:32,055 - ExploitationExpert - INFO - res_population_costs: [7542, 8089, 26623]
2025-06-26 20:07:32,055 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:07:32,056 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:07:32,056 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 25, 38, 7, 20, 33, 46, 15, 28, 41, 4, 17, 30, 43, 8, 21, 34, 47, 16, 29, 42, 5, 18, 31, 44, 9, 22, 35, 48, 19, 32, 45, 6, 23, 36, 49, 10, 27, 40, 3, 14, 37, 50, 13, 26, 39, 2, 11, 24, 51, 1], 'cur_cost': 27336.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 10, 23, 36, 49, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 11, 24, 37, 50, 1, 13, 26, 39, 6, 18, 32, 45, 20, 33, 46, 51], 'cur_cost': 27834.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': array([22, 17, 32, 46,  3, 13, 45,  0, 44, 23, 15, 47, 25, 36, 10, 50, 21,
        7, 38, 20, 41,  4, 51,  1, 14, 39,  8,  5,  2, 29, 11, 18, 49, 30,
       28,  9, 40, 26, 48, 43, 34, 35, 12, 37, 24, 19, 16, 33, 31, 27,  6,
       42]), 'cur_cost': 30898.0}, {'tour': array([ 8, 51, 29, 14, 48, 15, 49,  6, 42,  5, 41, 11, 37, 40, 23, 46, 35,
       21, 12, 19, 25, 20, 39, 22, 28, 50, 10,  4, 38,  1, 18, 43, 45, 31,
        7, 27, 36,  0,  2, 47, 16, 32, 24, 34, 17, 26, 33,  9, 13,  3, 44,
       30]), 'cur_cost': 31911.0}, {'tour': array([12, 39, 31, 42, 32, 36, 19, 34, 43, 41, 47, 46, 11, 29, 22,  3, 28,
       15, 35,  7, 17, 21,  8, 26, 30, 24,  1, 25, 33, 20, 16, 51, 13, 23,
       10,  4, 14,  2,  5,  9, 27, 38, 48,  6, 18,  0, 37, 49, 45, 40, 50,
       44]), 'cur_cost': 29720.0}, {'tour': array([12, 43, 16, 32, 15, 40, 14, 47, 20, 34, 10, 21, 38, 23, 31, 49, 48,
       39, 13, 27, 44, 36, 45, 50,  1, 29, 25, 17,  3,  9,  8,  5,  0,  2,
       26,  7, 22, 51, 37, 18, 11, 46, 30, 19, 33,  4, 42,  6, 28, 24, 41,
       35]), 'cur_cost': 31240.0}]
2025-06-26 20:07:32,057 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-26 20:07:32,057 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-06-26 20:07:32,057 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:07:32,058 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:07:32,058 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:07:32,058 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:07:32,058 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 28220.0
2025-06-26 20:07:32,561 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:07:32,561 - ExploitationExpert - INFO - res_population_costs: [7542, 8089, 26623]
2025-06-26 20:07:32,561 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:07:32,562 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:07:32,562 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 25, 38, 7, 20, 33, 46, 15, 28, 41, 4, 17, 30, 43, 8, 21, 34, 47, 16, 29, 42, 5, 18, 31, 44, 9, 22, 35, 48, 19, 32, 45, 6, 23, 36, 49, 10, 27, 40, 3, 14, 37, 50, 13, 26, 39, 2, 11, 24, 51, 1], 'cur_cost': 27336.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 10, 23, 36, 49, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 11, 24, 37, 50, 1, 13, 26, 39, 6, 18, 32, 45, 20, 33, 46, 51], 'cur_cost': 27834.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': array([22, 17, 32, 46,  3, 13, 45,  0, 44, 23, 15, 47, 25, 36, 10, 50, 21,
        7, 38, 20, 41,  4, 51,  1, 14, 39,  8,  5,  2, 29, 11, 18, 49, 30,
       28,  9, 40, 26, 48, 43, 34, 35, 12, 37, 24, 19, 16, 33, 31, 27,  6,
       42]), 'cur_cost': 30898.0}, {'tour': array([ 8, 51, 29, 14, 48, 15, 49,  6, 42,  5, 41, 11, 37, 40, 23, 46, 35,
       21, 12, 19, 25, 20, 39, 22, 28, 50, 10,  4, 38,  1, 18, 43, 45, 31,
        7, 27, 36,  0,  2, 47, 16, 32, 24, 34, 17, 26, 33,  9, 13,  3, 44,
       30]), 'cur_cost': 31911.0}, {'tour': array([ 6, 48, 30,  2, 35,  8, 38, 31,  0, 50, 29, 49, 42, 47,  1, 33, 51,
       26, 45, 17, 20, 36, 14, 13, 21, 28, 12,  5, 46, 10, 39, 16, 34, 37,
       18, 15, 27,  7, 19,  4, 23,  9, 40, 44,  3, 43, 41, 22, 24, 32, 25,
       11]), 'cur_cost': 28220.0}, {'tour': array([12, 43, 16, 32, 15, 40, 14, 47, 20, 34, 10, 21, 38, 23, 31, 49, 48,
       39, 13, 27, 44, 36, 45, 50,  1, 29, 25, 17,  3,  9,  8,  5,  0,  2,
       26,  7, 22, 51, 37, 18, 11, 46, 30, 19, 33,  4, 42,  6, 28, 24, 41,
       35]), 'cur_cost': 31240.0}]
2025-06-26 20:07:32,563 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-26 20:07:32,563 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-06-26 20:07:32,563 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:07:32,563 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:07:32,564 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:07:32,564 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:07:32,565 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 31949.0
2025-06-26 20:07:33,067 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:07:33,067 - ExploitationExpert - INFO - res_population_costs: [7542, 8089, 26623]
2025-06-26 20:07:33,067 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:07:33,067 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:07:33,067 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 25, 38, 7, 20, 33, 46, 15, 28, 41, 4, 17, 30, 43, 8, 21, 34, 47, 16, 29, 42, 5, 18, 31, 44, 9, 22, 35, 48, 19, 32, 45, 6, 23, 36, 49, 10, 27, 40, 3, 14, 37, 50, 13, 26, 39, 2, 11, 24, 51, 1], 'cur_cost': 27336.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 10, 23, 36, 49, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 11, 24, 37, 50, 1, 13, 26, 39, 6, 18, 32, 45, 20, 33, 46, 51], 'cur_cost': 27834.0}, {'tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}, {'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': array([22, 17, 32, 46,  3, 13, 45,  0, 44, 23, 15, 47, 25, 36, 10, 50, 21,
        7, 38, 20, 41,  4, 51,  1, 14, 39,  8,  5,  2, 29, 11, 18, 49, 30,
       28,  9, 40, 26, 48, 43, 34, 35, 12, 37, 24, 19, 16, 33, 31, 27,  6,
       42]), 'cur_cost': 30898.0}, {'tour': array([ 8, 51, 29, 14, 48, 15, 49,  6, 42,  5, 41, 11, 37, 40, 23, 46, 35,
       21, 12, 19, 25, 20, 39, 22, 28, 50, 10,  4, 38,  1, 18, 43, 45, 31,
        7, 27, 36,  0,  2, 47, 16, 32, 24, 34, 17, 26, 33,  9, 13,  3, 44,
       30]), 'cur_cost': 31911.0}, {'tour': array([ 6, 48, 30,  2, 35,  8, 38, 31,  0, 50, 29, 49, 42, 47,  1, 33, 51,
       26, 45, 17, 20, 36, 14, 13, 21, 28, 12,  5, 46, 10, 39, 16, 34, 37,
       18, 15, 27,  7, 19,  4, 23,  9, 40, 44,  3, 43, 41, 22, 24, 32, 25,
       11]), 'cur_cost': 28220.0}, {'tour': array([38, 21,  0, 22,  7, 50, 30, 36, 48, 32, 14,  9, 29,  8, 26, 44,  6,
       37, 35, 19,  5, 10, 11, 49, 17, 40, 13, 18, 12, 45, 33, 27, 23, 43,
       31, 15, 20,  4,  3,  1, 42, 34, 47, 24, 41, 39, 46, 28, 25, 16,  2,
       51]), 'cur_cost': 31949.0}]
2025-06-26 20:07:33,071 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:07:33,071 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-06-26 20:07:33,071 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:07:33,072 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 25, 38, 7, 20, 33, 46, 15, 28, 41, 4, 17, 30, 43, 8, 21, 34, 47, 16, 29, 42, 5, 18, 31, 44, 9, 22, 35, 48, 19, 32, 45, 6, 23, 36, 49, 10, 27, 40, 3, 14, 37, 50, 13, 26, 39, 2, 11, 24, 51, 1], 'cur_cost': 27336.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 10, 23, 36, 49, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 11, 24, 37, 50, 1, 13, 26, 39, 6, 18, 32, 45, 20, 33, 46, 51], 'cur_cost': 27834.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 25, 38, 7, 19, 31, 44, 3, 15, 28, 41, 9, 22, 35, 48, 5, 17, 30, 43, 2, 14, 27, 40, 8, 21, 34, 47, 4, 16, 29, 42, 10, 23, 36, 49, 6, 18, 32, 45, 1, 13, 26, 39, 11, 24, 37, 50, 20, 33, 46, 51], 'cur_cost': 27379.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}]
2025-06-26 20:07:33,072 - __main__ - INFO - 进化阶段完成
2025-06-26 20:07:33,072 - __main__ - INFO - 开始评估阶段
2025-06-26 20:07:33,072 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:07:33,073 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:07:33,073 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 29238.0 → 27336.0 (Δ 1902.0)
- Mean cost: 30203.0 → 29364.4
- Diversity: 0.455 → 0.590 (Δ 0.1353)

#### Elite metrics
- Elite count: 3 → 3
- Best elite cost: 7542 → 7542 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 66.7% , avg Δ 1736.17
- Exploit: 4 inds, success 25.0% , avg Δ -507.75

#### Other indicators
- No-change individuals: 2
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [22140.0, 26500.0, 29238.0], diversity [0.6478632478632478, 0.7572649572649574, 0.6260683760683761]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:07:33,073 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:07:46,096 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_improvement": 1736.17,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -507.75,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy (effective), exploit-ineffective",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators to enhance exploit effectiveness while maintaining current explore success."
  }
}
```
2025-06-26 20:07:46,114 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:07:46,114 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_improvement": 1736.17,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -507.75,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy (effective), exploit-ineffective",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators to enhance exploit effectiveness while maintaining current explore success."
  }
}
```
2025-06-26 20:07:46,115 - __main__ - INFO - 评估阶段完成
2025-06-26 20:07:46,115 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_improvement": 1736.17,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -507.75,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy (effective), exploit-ineffective",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators to enhance exploit effectiveness while maintaining current explore success."
  }
}
```
2025-06-26 20:07:46,115 - __main__ - INFO - 当前最佳适应度: 27336.0
2025-06-26 20:07:46,117 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\berlin52_route_3.pkl
2025-06-26 20:07:46,117 - __main__ - INFO - berlin52 开始进化第 5 代
2025-06-26 20:07:46,117 - __main__ - INFO - 开始分析阶段
2025-06-26 20:07:46,118 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:07:46,129 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 27336.0, 'max': 31949.0, 'mean': 29364.4, 'std': 1821.6890074872824}, 'diversity': 0.8307692307692308, 'clusters': {'clusters': 7, 'cluster_sizes': [2, 1, 3, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:07:46,130 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 27336.0, 'max': 31949.0, 'mean': 29364.4, 'std': 1821.6890074872824}, 'diversity_level': 0.8307692307692308, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [2, 1, 3, 1, 1, 1, 1]}, 'coordinates': [[565, 575], [25, 185], [345, 750], [945, 685], [845, 655], [880, 660], [25, 230], [525, 1000], [580, 1175], [650, 1130], [1605, 620], [1220, 580], [1465, 200], [1530, 5], [845, 680], [725, 370], [145, 665], [415, 635], [510, 875], [560, 365], [300, 465], [520, 585], [480, 415], [835, 625], [975, 580], [1215, 245], [1320, 315], [1250, 400], [660, 180], [410, 250], [420, 555], [575, 665], [1150, 1160], [700, 580], [685, 595], [685, 610], [770, 610], [795, 645], [720, 635], [760, 650], [475, 960], [95, 260], [875, 920], [700, 500], [555, 815], [830, 485], [1170, 65], [830, 610], [605, 625], [595, 360], [1340, 725], [1740, 245]], 'distance_matrix': array([[   0.,  666.,  281., ...,  217.,  789., 1220.],
       [ 666.,    0.,  649., ...,  596., 1422., 1716.],
       [ 281.,  649.,    0., ...,  463.,  995., 1484.],
       ...,
       [ 217.,  596.,  463., ...,    0.,  830., 1151.],
       [ 789., 1422.,  995., ...,  830.,    0.,  625.],
       [1220., 1716., 1484., ..., 1151.,  625.,    0.]])}
2025-06-26 20:07:46,130 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:07:46,130 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:07:46,130 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:07:46,130 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:07:46,130 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:07:46,130 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:07:46,130 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:07:46,130 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:07:46,130 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:07:46,130 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:07:46,130 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 19794.0, 'avg_gap': 15279.733333333335}, 'structure_gap': {'unique_elite_edges': 132, 'unique_pop_edges': 302, 'common_edges': 14}}, 'elite_diversity': {'diversity_score': 0.75}}
2025-06-26 20:07:46,135 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:07:46,135 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:07:46,136 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:07:46,136 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 27336.0, mean 29364.4, max 31949.0, std 1821.6890074872824
- diversity: 0.8307692307692308
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [26500.0, 29238.0, 27336.0] (Δ 951.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [25.0, 5.0, 1740.0, 1175.0]
- centroid/spread: [758.462, 564.904] | rms 467.889
- density_grid(3x3): [[6, 2, 6], [9, 17, 4], [5, 3, 0]]
- edge_len_stats(min,q1,med,q3,max): [15.0, 308.0, 525.0, 789.75, 1716.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 6, 7, 5, 10, 6, 5, 6]
- nn_median_dist: 78.079

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:07:46,137 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:08:15,773 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 15.0 to 1716.0, with a long_edge_ratio of 0.25 indicating significant variability)",
    "modality": "Potentially multimodal (direction_hist shows relatively balanced distribution across sectors, no single dominant direction)",
    "deceptiveness": "Possible deceptive regions (high std in cost_stats and no clear clustering, but no direct evidence of deceptive local optima)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (diversity 0.83, density_grid shows multiple populated cells)",
    "convergence": "Early stage (convergence 0.0, recent_best_costs show improvement Δ 951.00 per iteration)",
    "elite_influence": "Minimal (elite_count 0, no common edges or features detected)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Edges in max quartile (789.75-1716.0 length)",
    "low_density_areas": "Bottom-right cell (0 nodes in density_grid[2][2]) and top-middle cell (2 nodes in density_grid[0][1])"
  },
  "opportunity_regions": {
    "high_density_clusters": "Center cell (17 nodes in density_grid[1][1]) and top-left cell (6 nodes in density_grid[0][0])",
    "short_edge_zones": "Edges in min quartile (15.0-308.0 length)"
  },
  "evolution_phase": "Exploration (high diversity, no convergence, improving costs)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Focused exploitation in high-density cells (e.g., 2-opt or local search around density_grid[1][1])",
    "2. Adaptive mutation for long-edge corridors (e.g., edge-repair operators for edges >789.75 length)",
    "3. Maintain exploration in low-density cells (e.g., controlled randomization in density_grid[2][2])",
    "4. Direction-aware crossover (leverage direction_hist balance by preserving sector diversity)"
  ]
}
```
2025-06-26 20:08:15,773 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:08:15,773 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 15.0 to 1716.0, with a long_edge_ratio of 0.25 indicating significant variability)', 'modality': 'Potentially multimodal (direction_hist shows relatively balanced distribution across sectors, no single dominant direction)', 'deceptiveness': 'Possible deceptive regions (high std in cost_stats and no clear clustering, but no direct evidence of deceptive local optima)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.83, density_grid shows multiple populated cells)', 'convergence': 'Early stage (convergence 0.0, recent_best_costs show improvement Δ 951.00 per iteration)', 'elite_influence': 'Minimal (elite_count 0, no common edges or features detected)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max quartile (789.75-1716.0 length)', 'low_density_areas': 'Bottom-right cell (0 nodes in density_grid[2][2]) and top-middle cell (2 nodes in density_grid[0][1])'}, 'opportunity_regions': {'high_density_clusters': 'Center cell (17 nodes in density_grid[1][1]) and top-left cell (6 nodes in density_grid[0][0])', 'short_edge_zones': 'Edges in min quartile (15.0-308.0 length)'}, 'evolution_phase': 'Exploration (high diversity, no convergence, improving costs)', 'evolution_direction': ['Operator suggestions:', '1. Focused exploitation in high-density cells (e.g., 2-opt or local search around density_grid[1][1])', '2. Adaptive mutation for long-edge corridors (e.g., edge-repair operators for edges >789.75 length)', '3. Maintain exploration in low-density cells (e.g., controlled randomization in density_grid[2][2])', '4. Direction-aware crossover (leverage direction_hist balance by preserving sector diversity)']}
2025-06-26 20:08:15,773 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:08:15,773 - __main__ - INFO - 分析阶段完成
2025-06-26 20:08:15,773 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 15.0 to 1716.0, with a long_edge_ratio of 0.25 indicating significant variability)', 'modality': 'Potentially multimodal (direction_hist shows relatively balanced distribution across sectors, no single dominant direction)', 'deceptiveness': 'Possible deceptive regions (high std in cost_stats and no clear clustering, but no direct evidence of deceptive local optima)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.83, density_grid shows multiple populated cells)', 'convergence': 'Early stage (convergence 0.0, recent_best_costs show improvement Δ 951.00 per iteration)', 'elite_influence': 'Minimal (elite_count 0, no common edges or features detected)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max quartile (789.75-1716.0 length)', 'low_density_areas': 'Bottom-right cell (0 nodes in density_grid[2][2]) and top-middle cell (2 nodes in density_grid[0][1])'}, 'opportunity_regions': {'high_density_clusters': 'Center cell (17 nodes in density_grid[1][1]) and top-left cell (6 nodes in density_grid[0][0])', 'short_edge_zones': 'Edges in min quartile (15.0-308.0 length)'}, 'evolution_phase': 'Exploration (high diversity, no convergence, improving costs)', 'evolution_direction': ['Operator suggestions:', '1. Focused exploitation in high-density cells (e.g., 2-opt or local search around density_grid[1][1])', '2. Adaptive mutation for long-edge corridors (e.g., edge-repair operators for edges >789.75 length)', '3. Maintain exploration in low-density cells (e.g., controlled randomization in density_grid[2][2])', '4. Direction-aware crossover (leverage direction_hist balance by preserving sector diversity)']}
2025-06-26 20:08:15,773 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:08:15,773 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:08:15,773 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 27336.0
  • mean_cost: 29364.4
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 15.0 to 1716.0, with a long_edge_ratio of 0.25 indicating significant variability), modality Potentially multimodal (direction_hist shows relatively balanced distribution across sectors, no single dominant direction), deceptiveness Possible deceptive regions (high std in cost_stats and no clear clustering, but no direct evidence of deceptive local optima)
- Evolution phase: Exploration (high diversity, no convergence, improving costs)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_areas
- Opportunity regions (# 2): high_density_cluster, short_edge_zones
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:08:15,773 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:08:15,773 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:08:26,524 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 60% explore to cover opportunity regions, bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 20:08:26,524 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:08:26,524 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:08:26,524 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 60% explore to cover opportunity regions, bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 20:08:26,527 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:08:26,527 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:08:26,527 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 60% explore to cover opportunity regions, bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 20:08:26,527 - __main__ - INFO - 开始进化阶段
2025-06-26 20:08:26,528 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:08:26,528 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:08:26,528 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30369.0 (rank 50.0%)
- Population diversity: 0.5901960784313726
- Evolution phase: Exploration (high diversity, no convergence, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:08:26,528 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:08:26,529 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:08:39,923 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Leverage clustered nodes with short edges, spiral outward"
}
```
2025-06-26 20:08:39,923 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:08:39,923 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30369.0, 路径: [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51]
2025-06-26 20:08:39,923 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}
2025-06-26 20:08:39,923 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:08:39,923 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:08:39,923 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 27336.0 (rank 0.0%)
- Population diversity: 0.5901960784313726
- Evolution phase: Exploration (high diversity, no convergence, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:08:39,923 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:08:39,926 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:08:53,029 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Leverage clusters, alternate dense/sparse traversal"
}
```
2025-06-26 20:08:53,029 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:08:53,029 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30768.0, 路径: [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46]
2025-06-26 20:08:53,029 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}
2025-06-26 20:08:53,029 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:08:53,029 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:08:53,029 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 27379.0 (rank 0.0%)
- Population diversity: 0.6431372549019608
- Evolution phase: Exploration (high diversity, no convergence, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:08:53,029 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:08:53,029 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:09:06,297 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Prioritize dense clusters and short edges, avoid sparse areas"
}
```
2025-06-26 20:09:06,297 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:09:06,297 - ExplorationExpert - INFO - 探索路径生成完成，成本: 23824.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51]
2025-06-26 20:09:06,297 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], 'cur_cost': 23824.0}
2025-06-26 20:09:06,297 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:09:06,297 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:09:06,297 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 27834.0 (rank 20.0%)
- Population diversity: 0.7215686274509804
- Evolution phase: Exploration (high diversity, no convergence, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:09:06,297 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:09:06,297 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:09:19,359 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Leverage clustered nodes, alternate directions to explore"
}
```
2025-06-26 20:09:19,359 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:09:19,375 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30632.0, 路径: [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51]
2025-06-26 20:09:19,375 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}
2025-06-26 20:09:19,375 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:09:19,375 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:09:19,375 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 27379.0 (rank 10.0%)
- Population diversity: 0.711764705882353
- Evolution phase: Exploration (high diversity, no convergence, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:09:19,375 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:09:19,375 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:09:32,696 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Cluster jumps in dense zones, spiral through sparse edges"
}
```
2025-06-26 20:09:32,696 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:09:32,696 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30632.0, 路径: [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51]
2025-06-26 20:09:32,696 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}
2025-06-26 20:09:32,701 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:09:32,701 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:09:32,701 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30369.0 (rank 20.0%)
- Population diversity: 0.6392156862745098
- Evolution phase: Exploration (high diversity, no convergence, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 51]  
• Path length == 52 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:09:32,701 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:09:32,701 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:09:45,708 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Leverage high-density clusters with spiral pattern"
}
```
2025-06-26 20:09:45,708 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:09:45,717 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30768.0, 路径: [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46]
2025-06-26 20:09:45,717 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}
2025-06-26 20:09:45,717 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 20:09:45,717 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:09:45,717 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:09:45,717 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 31183.0
2025-06-26 20:09:46,219 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:09:46,219 - ExploitationExpert - INFO - res_population_costs: [7542, 8089, 26623]
2025-06-26 20:09:46,219 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:09:46,220 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:09:46,221 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], 'cur_cost': 23824.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}, {'tour': array([28, 18, 35, 49, 41, 44, 20, 30,  5,  6, 42, 10,  1,  8, 50, 38, 11,
       16, 24, 25, 29, 34, 22, 36, 14, 31, 48, 39, 12, 33,  9, 46, 45, 17,
        3, 37, 27, 43, 51, 32,  7,  0, 21,  2, 26, 19, 40,  4, 47, 15, 13,
       23]), 'cur_cost': 31183.0}, {'tour': array([ 8, 51, 29, 14, 48, 15, 49,  6, 42,  5, 41, 11, 37, 40, 23, 46, 35,
       21, 12, 19, 25, 20, 39, 22, 28, 50, 10,  4, 38,  1, 18, 43, 45, 31,
        7, 27, 36,  0,  2, 47, 16, 32, 24, 34, 17, 26, 33,  9, 13,  3, 44,
       30]), 'cur_cost': 31911.0}, {'tour': array([ 6, 48, 30,  2, 35,  8, 38, 31,  0, 50, 29, 49, 42, 47,  1, 33, 51,
       26, 45, 17, 20, 36, 14, 13, 21, 28, 12,  5, 46, 10, 39, 16, 34, 37,
       18, 15, 27,  7, 19,  4, 23,  9, 40, 44,  3, 43, 41, 22, 24, 32, 25,
       11]), 'cur_cost': 28220.0}, {'tour': array([38, 21,  0, 22,  7, 50, 30, 36, 48, 32, 14,  9, 29,  8, 26, 44,  6,
       37, 35, 19,  5, 10, 11, 49, 17, 40, 13, 18, 12, 45, 33, 27, 23, 43,
       31, 15, 20,  4,  3,  1, 42, 34, 47, 24, 41, 39, 46, 28, 25, 16,  2,
       51]), 'cur_cost': 31949.0}]
2025-06-26 20:09:46,223 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:09:46,223 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-06-26 20:09:46,223 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 20:09:46,223 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:09:46,223 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:09:46,224 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:09:46,224 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 33431.0
2025-06-26 20:09:46,726 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:09:46,726 - ExploitationExpert - INFO - res_population_costs: [7542, 8089, 26623]
2025-06-26 20:09:46,727 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:09:46,729 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:09:46,729 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], 'cur_cost': 23824.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}, {'tour': array([28, 18, 35, 49, 41, 44, 20, 30,  5,  6, 42, 10,  1,  8, 50, 38, 11,
       16, 24, 25, 29, 34, 22, 36, 14, 31, 48, 39, 12, 33,  9, 46, 45, 17,
        3, 37, 27, 43, 51, 32,  7,  0, 21,  2, 26, 19, 40,  4, 47, 15, 13,
       23]), 'cur_cost': 31183.0}, {'tour': array([50, 23, 39, 25, 33, 17, 19, 40, 38,  5,  9,  1, 32,  6,  3, 16, 15,
       10, 29, 11,  0, 20, 28,  8, 47, 35, 12, 30, 45, 36, 18, 46, 43, 44,
       13, 37, 26,  7, 42,  2, 41, 21, 34, 27, 24, 14, 31, 22,  4, 49, 51,
       48]), 'cur_cost': 33431.0}, {'tour': array([ 6, 48, 30,  2, 35,  8, 38, 31,  0, 50, 29, 49, 42, 47,  1, 33, 51,
       26, 45, 17, 20, 36, 14, 13, 21, 28, 12,  5, 46, 10, 39, 16, 34, 37,
       18, 15, 27,  7, 19,  4, 23,  9, 40, 44,  3, 43, 41, 22, 24, 32, 25,
       11]), 'cur_cost': 28220.0}, {'tour': array([38, 21,  0, 22,  7, 50, 30, 36, 48, 32, 14,  9, 29,  8, 26, 44,  6,
       37, 35, 19,  5, 10, 11, 49, 17, 40, 13, 18, 12, 45, 33, 27, 23, 43,
       31, 15, 20,  4,  3,  1, 42, 34, 47, 24, 41, 39, 46, 28, 25, 16,  2,
       51]), 'cur_cost': 31949.0}]
2025-06-26 20:09:46,730 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:09:46,730 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-06-26 20:09:46,730 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:09:46,731 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:09:46,731 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:09:46,731 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:09:46,731 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 32686.0
2025-06-26 20:09:47,233 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:09:47,233 - ExploitationExpert - INFO - res_population_costs: [7542, 8089, 26623]
2025-06-26 20:09:47,233 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:09:47,235 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:09:47,235 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], 'cur_cost': 23824.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}, {'tour': array([28, 18, 35, 49, 41, 44, 20, 30,  5,  6, 42, 10,  1,  8, 50, 38, 11,
       16, 24, 25, 29, 34, 22, 36, 14, 31, 48, 39, 12, 33,  9, 46, 45, 17,
        3, 37, 27, 43, 51, 32,  7,  0, 21,  2, 26, 19, 40,  4, 47, 15, 13,
       23]), 'cur_cost': 31183.0}, {'tour': array([50, 23, 39, 25, 33, 17, 19, 40, 38,  5,  9,  1, 32,  6,  3, 16, 15,
       10, 29, 11,  0, 20, 28,  8, 47, 35, 12, 30, 45, 36, 18, 46, 43, 44,
       13, 37, 26,  7, 42,  2, 41, 21, 34, 27, 24, 14, 31, 22,  4, 49, 51,
       48]), 'cur_cost': 33431.0}, {'tour': array([ 6, 35, 51, 17, 45, 24, 16, 34, 44, 23, 31, 42,  5, 15, 10,  0, 14,
       22,  8, 33, 29, 11, 36, 19, 26,  1,  2, 40, 38, 25, 32, 13, 43, 30,
        3, 20, 21, 49,  7, 39, 47, 12, 28, 18, 50, 41, 46,  9,  4, 37, 27,
       48]), 'cur_cost': 32686.0}, {'tour': array([38, 21,  0, 22,  7, 50, 30, 36, 48, 32, 14,  9, 29,  8, 26, 44,  6,
       37, 35, 19,  5, 10, 11, 49, 17, 40, 13, 18, 12, 45, 33, 27, 23, 43,
       31, 15, 20,  4,  3,  1, 42, 34, 47, 24, 41, 39, 46, 28, 25, 16,  2,
       51]), 'cur_cost': 31949.0}]
2025-06-26 20:09:47,236 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:09:47,236 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-06-26 20:09:47,237 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:09:47,237 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:09:47,237 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:09:47,237 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:09:47,237 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 29239.0
2025-06-26 20:09:47,740 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:09:47,741 - ExploitationExpert - INFO - res_population_costs: [7542, 8089, 26623]
2025-06-26 20:09:47,741 - ExploitationExpert - INFO - res_populations: [array([ 0, 21, 30, 17,  2, 16, 20, 41,  6,  1, 29, 22, 19, 49, 28, 15, 45,
       43, 33, 34, 35, 38, 39, 36, 37, 47, 23,  4, 14,  5,  3, 24, 11, 27,
       26, 25, 46, 12, 13, 51, 10, 50, 32, 42,  9,  8,  7, 40, 18, 44, 31,
       48], dtype=int64), array([ 0, 21, 17, 30, 20, 29, 41,  1,  6, 16,  2, 40,  7,  8,  9, 18, 44,
       31, 48, 35, 34, 33, 38, 39, 36, 37, 14, 42, 32, 50, 10, 51, 13, 12,
       46, 25, 26, 27, 11, 24,  3,  5,  4, 23, 47, 45, 43, 15, 28, 49, 19,
       22], dtype=int64), array([ 0, 43, 37,  7, 47, 33, 18, 11, 34, 13, 51, 12, 35, 46, 29,  3, 14,
       38, 26, 10, 27, 24, 40, 32, 25, 17, 19, 48,  1, 36, 42, 16, 22, 44,
       45,  2, 28, 30, 49, 20,  8, 39, 41, 31, 23, 15,  9, 50,  5,  4, 21,
        6], dtype=int64)]
2025-06-26 20:09:47,742 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:09:47,742 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], 'cur_cost': 23824.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}, {'tour': array([28, 18, 35, 49, 41, 44, 20, 30,  5,  6, 42, 10,  1,  8, 50, 38, 11,
       16, 24, 25, 29, 34, 22, 36, 14, 31, 48, 39, 12, 33,  9, 46, 45, 17,
        3, 37, 27, 43, 51, 32,  7,  0, 21,  2, 26, 19, 40,  4, 47, 15, 13,
       23]), 'cur_cost': 31183.0}, {'tour': array([50, 23, 39, 25, 33, 17, 19, 40, 38,  5,  9,  1, 32,  6,  3, 16, 15,
       10, 29, 11,  0, 20, 28,  8, 47, 35, 12, 30, 45, 36, 18, 46, 43, 44,
       13, 37, 26,  7, 42,  2, 41, 21, 34, 27, 24, 14, 31, 22,  4, 49, 51,
       48]), 'cur_cost': 33431.0}, {'tour': array([ 6, 35, 51, 17, 45, 24, 16, 34, 44, 23, 31, 42,  5, 15, 10,  0, 14,
       22,  8, 33, 29, 11, 36, 19, 26,  1,  2, 40, 38, 25, 32, 13, 43, 30,
        3, 20, 21, 49,  7, 39, 47, 12, 28, 18, 50, 41, 46,  9,  4, 37, 27,
       48]), 'cur_cost': 32686.0}, {'tour': array([37, 23, 46, 33, 51, 10, 15, 41, 13, 49, 48,  7,  3,  0, 29, 19, 14,
       28, 36, 40, 47, 20, 44,  9, 16, 30, 39, 31, 26,  8,  5, 17, 45, 24,
       43, 42, 34, 32, 35,  2, 11, 25,  4, 27, 50, 22,  6, 18, 21, 12,  1,
       38]), 'cur_cost': 29239.0}]
2025-06-26 20:09:47,742 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-26 20:09:47,742 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-06-26 20:09:47,742 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:09:47,742 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 11, 23, 35, 47, 10, 22, 34, 46, 9, 21, 33, 45, 8, 20, 32, 44, 7, 19, 31, 43, 6, 18, 30, 42, 5, 17, 29, 41, 4, 16, 28, 40, 3, 15, 27, 39, 2, 14, 26, 38, 1, 13, 25, 37, 49, 50, 51], 'cur_cost': 30369.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51], 'cur_cost': 23824.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51], 'cur_cost': 30632.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 51, 50, 49, 48, 47, 46], 'cur_cost': 30768.0}}]
2025-06-26 20:09:47,742 - __main__ - INFO - 进化阶段完成
2025-06-26 20:09:47,742 - __main__ - INFO - 开始评估阶段
2025-06-26 20:09:47,742 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:09:47,742 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:09:47,742 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 27336.0 → 23824.0 (Δ 3512.0)
- Mean cost: 29364.4 → 30353.2
- Diversity: 0.590 → 0.616 (Δ 0.0255)

#### Elite metrics
- Elite count: 3 → 3
- Best elite cost: 7542 → 7542 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ -1054.5
- Exploit: 4 inds, success 25.0% , avg Δ -890.25

#### Other indicators
- No-change individuals: 1
- Regression individuals: 7
- Historical trends: Last 3 iterations: costs [26500.0, 29238.0, 27336.0], diversity [0.7572649572649574, 0.6260683760683761, 0.8307692307692308]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:09:47,746 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:10:02,056 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -1054.5,
      "contribution": "moderate improvement potential"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -890.25,
      "contribution": "limited but slightly better success rate"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (explore:exploit)",
    "assessment": "explore slightly underperforming, exploit marginally better but both need enhancement"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider introducing new mutation operators to boost explore success rate"
  }
}
```
2025-06-26 20:10:02,074 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:10:02,075 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -1054.5,
      "contribution": "moderate improvement potential"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -890.25,
      "contribution": "limited but slightly better success rate"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (explore:exploit)",
    "assessment": "explore slightly underperforming, exploit marginally better but both need enhancement"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider introducing new mutation operators to boost explore success rate"
  }
}
```
2025-06-26 20:10:02,075 - __main__ - INFO - 评估阶段完成
2025-06-26 20:10:02,075 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -1054.5,
      "contribution": "moderate improvement potential"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -890.25,
      "contribution": "limited but slightly better success rate"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (explore:exploit)",
    "assessment": "explore slightly underperforming, exploit marginally better but both need enhancement"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider introducing new mutation operators to boost explore success rate"
  }
}
```
2025-06-26 20:10:02,075 - __main__ - INFO - 当前最佳适应度: 23824.0
2025-06-26 20:10:02,075 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\berlin52_route_4.pkl
2025-06-26 20:10:02,081 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\berlin52_solution.json
2025-06-26 20:10:02,081 - __main__ - INFO - 实例 berlin52 处理完成
