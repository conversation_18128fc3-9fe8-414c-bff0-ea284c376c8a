2025-06-08 18:53:27,786 - __main__ - INFO - composite12_60 开始进化第 1 代
2025-06-08 18:53:27,786 - __main__ - INFO - 开始分析阶段
2025-06-08 18:53:27,786 - StatsExpert - INFO - 开始统计分析
2025-06-08 18:53:27,804 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9998.0, 'max': 105388.0, 'mean': 72249.3, 'std': 40893.86164218293}, 'diversity': 0.9296296296296297, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-08 18:53:27,805 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9998.0, 'max': 105388.0, 'mean': 72249.3, 'std': 40893.86164218293}, 'diversity_level': 0.9296296296296297, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-08 18:53:27,814 - PathExpert - INFO - 开始路径结构分析
2025-06-08 18:53:27,820 - PathExpert - INFO - 路径结构分析完成
2025-06-08 18:53:27,820 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (4, 6, 5), 'frequency': 0.3}, {'subpath': (28, 27, 29), 'frequency': 0.3}, {'subpath': (27, 29, 33), 'frequency': 0.3}, {'subpath': (29, 33, 24), 'frequency': 0.3}, {'subpath': (33, 24, 30), 'frequency': 0.3}, {'subpath': (24, 30, 34), 'frequency': 0.3}, {'subpath': (45, 38, 42), 'frequency': 0.3}, {'subpath': (38, 42, 47), 'frequency': 0.3}, {'subpath': (37, 40, 46), 'frequency': 0.3}, {'subpath': (40, 46, 39), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(30, 34)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(52, 57)', 'frequency': 0.2}, {'edge': '(57, 55)', 'frequency': 0.2}, {'edge': '(55, 56)', 'frequency': 0.2}, {'edge': '(56, 49)', 'frequency': 0.2}, {'edge': '(49, 58)', 'frequency': 0.2}, {'edge': '(58, 54)', 'frequency': 0.2}, {'edge': '(54, 53)', 'frequency': 0.2}, {'edge': '(53, 48)', 'frequency': 0.2}, {'edge': '(59, 51)', 'frequency': 0.3}, {'edge': '(19, 15)', 'frequency': 0.2}, {'edge': '(15, 17)', 'frequency': 0.3}, {'edge': '(17, 22)', 'frequency': 0.2}, {'edge': '(22, 20)', 'frequency': 0.2}, {'edge': '(20, 16)', 'frequency': 0.2}, {'edge': '(16, 23)', 'frequency': 0.2}, {'edge': '(23, 12)', 'frequency': 0.2}, {'edge': '(12, 13)', 'frequency': 0.2}, {'edge': '(3, 2)', 'frequency': 0.2}, {'edge': '(2, 7)', 'frequency': 0.3}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(11, 10)', 'frequency': 0.2}, {'edge': '(10, 1)', 'frequency': 0.2}, {'edge': '(1, 0)', 'frequency': 0.2}, {'edge': '(0, 4)', 'frequency': 0.2}, {'edge': '(4, 6)', 'frequency': 0.3}, {'edge': '(6, 5)', 'frequency': 0.3}, {'edge': '(8, 26)', 'frequency': 0.2}, {'edge': '(26, 32)', 'frequency': 0.2}, {'edge': '(32, 31)', 'frequency': 0.2}, {'edge': '(31, 25)', 'frequency': 0.3}, {'edge': '(25, 35)', 'frequency': 0.2}, {'edge': '(35, 28)', 'frequency': 0.2}, {'edge': '(28, 27)', 'frequency': 0.3}, {'edge': '(27, 29)', 'frequency': 0.3}, {'edge': '(29, 33)', 'frequency': 0.3}, {'edge': '(33, 24)', 'frequency': 0.3}, {'edge': '(24, 30)', 'frequency': 0.3}, {'edge': '(34, 36)', 'frequency': 0.2}, {'edge': '(36, 45)', 'frequency': 0.2}, {'edge': '(45, 38)', 'frequency': 0.3}, {'edge': '(38, 42)', 'frequency': 0.3}, {'edge': '(42, 47)', 'frequency': 0.3}, {'edge': '(47, 37)', 'frequency': 0.3}, {'edge': '(37, 40)', 'frequency': 0.3}, {'edge': '(40, 46)', 'frequency': 0.3}, {'edge': '(46, 39)', 'frequency': 0.3}, {'edge': '(39, 41)', 'frequency': 0.3}, {'edge': '(41, 43)', 'frequency': 0.3}, {'edge': '(43, 44)', 'frequency': 0.2}, {'edge': '(44, 50)', 'frequency': 0.2}, {'edge': '(51, 52)', 'frequency': 0.2}, {'edge': '(12, 23)', 'frequency': 0.2}, {'edge': '(16, 20)', 'frequency': 0.2}, {'edge': '(14, 21)', 'frequency': 0.2}, {'edge': '(21, 18)', 'frequency': 0.2}, {'edge': '(34, 44)', 'frequency': 0.2}, {'edge': '(49, 48)', 'frequency': 0.2}, {'edge': '(5, 8)', 'frequency': 0.2}, {'edge': '(6, 32)', 'frequency': 0.2}, {'edge': '(22, 3)', 'frequency': 0.3}, {'edge': '(7, 56)', 'frequency': 0.2}, {'edge': '(17, 33)', 'frequency': 0.2}, {'edge': '(21, 38)', 'frequency': 0.2}, {'edge': '(34, 18)', 'frequency': 0.2}, {'edge': '(27, 4)', 'frequency': 0.2}, {'edge': '(23, 14)', 'frequency': 0.2}, {'edge': '(14, 2)', 'frequency': 0.2}, {'edge': '(26, 1)', 'frequency': 0.2}, {'edge': '(9, 27)', 'frequency': 0.2}, {'edge': '(29, 24)', 'frequency': 0.2}, {'edge': '(54, 8)', 'frequency': 0.2}, {'edge': '(10, 25)', 'frequency': 0.2}, {'edge': '(59, 42)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [23, 26, 53, 37, 17, 35, 47, 57], 'cost': 17817.0, 'size': 8}, {'region': [44, 58, 35, 46, 54, 27, 14, 41], 'cost': 17603.0, 'size': 8}, {'region': [28, 41, 27, 57, 45, 33, 43, 58], 'cost': 15560.0, 'size': 8}, {'region': [27, 48, 45, 32, 58, 38, 31], 'cost': 13838.0, 'size': 7}, {'region': [22, 35, 53, 26, 37, 18], 'cost': 13046.0, 'size': 6}]}
2025-06-08 18:53:27,821 - EliteExpert - INFO - 开始精英解分析
2025-06-08 18:53:27,821 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-08 18:53:27,821 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-08 18:53:27,821 - LandscapeExpert - INFO - 开始景观分析
2025-06-08 18:53:27,822 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-08 18:53:27,822 - LandscapeExpert - INFO - 发送给LLM的提示词: 
# Landscape Analysis Task

## Role Definition
You are an expert in evolutionary algorithms and combinatorial optimization, specializing in search space analysis. You excel at analyzing population statistics, path structures, and elite solutions to provide insights into the search landscape.

## Current Population Statistics
- Population Size: 10
- Cost Statistics: Min=9998.0, Max=105388.0, Mean=72249.3, Std=40893.86164218293
- Diversity Level: 0.9296296296296297
- Convergence Level: 0.0
- Clustering Information: {"clusters": 9, "cluster_sizes": [2, 1, 1, 1, 1, 1, 1, 1, 1]}

## Path Structure Analysis
- High Quality Edges: []
- Common Subpaths: [{"subpath": [4, 6, 5], "frequency": 0.3}, {"subpath": [28, 27, 29], "frequency": 0.3}, {"subpath": [27, 29, 33], "frequency": 0.3}, {"subpath": [29, 33, 24], "frequency": 0.3}, {"subpath": [33, 24, 30], "frequency": 0.3}, {"subpath": [24, 30, 34], "frequency": 0.3}, {"subpath": [45, 38, 42], "frequency": 0.3}, {"subpath": [38, 42, 47], "frequency": 0.3}, {"subpath": [37, 40, 46], "frequency": 0.3}, {"subpath": [40, 46, 39], "frequency": 0.3}]
- Edge Frequency Distribution: {"high_frequency_edges": [], "medium_frequency_edges": [{"edge": "(30, 34)", "frequency": 0.4}], "low_frequency_edges": [{"edge": "(52, 57)", "frequency": 0.2}, {"edge": "(57, 55)", "frequency": 0.2}, {"edge": "(55, 56)", "frequency": 0.2}, {"edge": "(56, 49)", "frequency": 0.2}, {"edge": "(49, 58)", "frequency": 0.2}, {"edge": "(58, 54)", "frequency": 0.2}, {"edge": "(54, 53)", "frequency": 0.2}, {"edge": "(53, 48)", "frequency": 0.2}, {"edge": "(59, 51)", "frequency": 0.3}, {"edge": "(19, 15)", "frequency": 0.2}, {"edge": "(15, 17)", "frequency": 0.3}, {"edge": "(17, 22)", "frequency": 0.2}, {"edge": "(22, 20)", "frequency": 0.2}, {"edge": "(20, 16)", "frequency": 0.2}, {"edge": "(16, 23)", "frequency": 0.2}, {"edge": "(23, 12)", "frequency": 0.2}, {"edge": "(12, 13)", "frequency": 0.2}, {"edge": "(3, 2)", "frequency": 0.2}, {"edge": "(2, 7)", "frequency": 0.3}, {"edge": "(7, 11)", "frequency": 0.2}, {"edge": "(11, 10)", "frequency": 0.2}, {"edge": "(10, 1)", "frequency": 0.2}, {"edge": "(1, 0)", "frequency": 0.2}, {"edge": "(0, 4)", "frequency": 0.2}, {"edge": "(4, 6)", "frequency": 0.3}, {"edge": "(6, 5)", "frequency": 0.3}, {"edge": "(8, 26)", "frequency": 0.2}, {"edge": "(26, 32)", "frequency": 0.2}, {"edge": "(32, 31)", "frequency": 0.2}, {"edge": "(31, 25)", "frequency": 0.3}, {"edge": "(25, 35)", "frequency": 0.2}, {"edge": "(35, 28)", "frequency": 0.2}, {"edge": "(28, 27)", "frequency": 0.3}, {"edge": "(27, 29)", "frequency": 0.3}, {"edge": "(29, 33)", "frequency": 0.3}, {"edge": "(33, 24)", "frequency": 0.3}, {"edge": "(24, 30)", "frequency": 0.3}, {"edge": "(34, 36)", "frequency": 0.2}, {"edge": "(36, 45)", "frequency": 0.2}, {"edge": "(45, 38)", "frequency": 0.3}, {"edge": "(38, 42)", "frequency": 0.3}, {"edge": "(42, 47)", "frequency": 0.3}, {"edge": "(47, 37)", "frequency": 0.3}, {"edge": "(37, 40)", "frequency": 0.3}, {"edge": "(40, 46)", "frequency": 0.3}, {"edge": "(46, 39)", "frequency": 0.3}, {"edge": "(39, 41)", "frequency": 0.3}, {"edge": "(41, 43)", "frequency": 0.3}, {"edge": "(43, 44)", "frequency": 0.2}, {"edge": "(44, 50)", "frequency": 0.2}, {"edge": "(51, 52)", "frequency": 0.2}, {"edge": "(12, 23)", "frequency": 0.2}, {"edge": "(16, 20)", "frequency": 0.2}, {"edge": "(14, 21)", "frequency": 0.2}, {"edge": "(21, 18)", "frequency": 0.2}, {"edge": "(34, 44)", "frequency": 0.2}, {"edge": "(49, 48)", "frequency": 0.2}, {"edge": "(5, 8)", "frequency": 0.2}, {"edge": "(6, 32)", "frequency": 0.2}, {"edge": "(22, 3)", "frequency": 0.3}, {"edge": "(7, 56)", "frequency": 0.2}, {"edge": "(17, 33)", "frequency": 0.2}, {"edge": "(21, 38)", "frequency": 0.2}, {"edge": "(34, 18)", "frequency": 0.2}, {"edge": "(27, 4)", "frequency": 0.2}, {"edge": "(23, 14)", "frequency": 0.2}, {"edge": "(14, 2)", "frequency": 0.2}, {"edge": "(26, 1)", "frequency": 0.2}, {"edge": "(9, 27)", "frequency": 0.2}, {"edge": "(29, 24)", "frequency": 0.2}, {"edge": "(54, 8)", "frequency": 0.2}, {"edge": "(10, 25)", "frequency": 0.2}, {"edge": "(59, 42)", "frequency": 0.2}]}
- Low Quality Regions: [{"region": [23, 26, 53, 37, 17, 35, 47, 57], "cost": 17817.0, "size": 8}, {"region": [44, 58, 35, 46, 54, 27, 14, 41], "cost": 17603.0, "size": 8}, {"region": [28, 41, 27, 57, 45, 33, 43, 58], "cost": 15560.0, "size": 8}, {"region": [27, 48, 45, 32, 58, 38, 31], "cost": 13838.0, "size": 7}, {"region": [22, 35, 53, 26, 37, 18], "cost": 13046.0, "size": 6}]

## Elite Solution Analysis
- Number of Elite Solutions: 0
- Common Features: {}
- Fixed Nodes: []
- Population Gap: {}
- Elite Diversity: {}

## Historical Trends
No historical trend data

# Analysis Request
Based on the above information, please provide a comprehensive landscape analysis of the search space and current population state. Your analysis should include:

1. Overall search space characteristics
2. Current population state assessment
3. Identification of difficult regions and search challenges
4. Identification of opportunity regions with potential for improvement
5. Recommended evolution direction and strategy balance

2025-06-08 18:53:36,373 - LandscapeExpert - INFO - LLM返回的分析结果: Okay, here's a comprehensive landscape analysis based on the provided data, focusing on evolutionary algorithm behavior and search space characteristics.

**1. Overall Search Space Characteristics**

*   **Rough Landscape:** The significant spread in cost statistics (Min=9998.0, Max=105388.0, Mean=72249.3, Std=40893.86) suggests a rugged or multimodal search space. The large standard deviation indicates substantial variability in solution quality, implying the existence of many local optima or plateaus.
*   **Presence of Building Blocks (Potentially):** The "Common Subpaths" data, even with a frequency of only 0.3, indicates potential building blocks. These frequent subpaths (e.g., \[4, 6, 5], \[28, 27, 29]) could be valuable partial solutions. Exploiting these could accelerate convergence.
*   **No Clear Convergence:** The "Convergence Level" of 0.0 suggests that the population has not yet converged.
*   **High Diversity:** The "Diversity Level" of 0.93 implies the population is highly diverse, which is expected in the early stages of search and in a multimodal landscape. This can be a good starting point.
*   **Cluster Structure:** The clustering information (9 clusters, sizes ranging from 1 to 2) reinforces the idea of a diverse population with some degree of structure. This suggests the algorithm may be sampling different regions of the search space but needs to improve exploitation.

**2. Current Population State Assessment**

*   **Early Stage Exploration:** The current state appears to be in an exploration phase. The high diversity, lack of convergence, and the cost distribution support this.
*   **Lack of Elite Solutions:** The absence of "Elite Solutions" makes it difficult to judge the search's success. The absence of elite solutions may mean that the best solutions found are not much better than the average ones.
*   **Potential for Premature Convergence:** Since there is no convergence, premature convergence does not look like a danger currently. However, with increasing iterations it may become an issue.
*   **Path Structure Information:** Edge and subpath frequencies provide insights into the structures in the search. They suggest that some paths are more likely to be included in a solution than others.
*   **Low-Quality Regions:** The identified "Low Quality Regions" highlight areas of the search space that consistently lead to poor solutions. These regions might contain traps or suboptimal configurations.

**3. Identification of Difficult Regions and Search Challenges**

*   **Local Optima:** The rugged landscape and cost distribution suggest the presence of numerous local optima. The algorithm might be getting trapped in these, preventing it from finding the global optimum.
*   **Exploration vs. Exploitation Imbalance:** At this point, it looks like exploration is the primary mode of search. However, a balance is needed, as the algorithm might struggle to locate high-quality solutions without enough focused exploitation.
*   **Difficulty in Identifying High-Quality Regions:** Without elite solutions, the algorithm might be having trouble distinguishing promising regions from less promising ones.
*   **Region Identification Challenges:** The "Low Quality Regions" may indicate areas where the algorithm has a hard time finding good solutions. The algorithm might be getting stuck in these regions because of poor crossover or mutation strategies.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Exploit Common Subpaths:** The "Common Subpaths" represent potential building blocks. By promoting the formation of those paths in the population, the algorithm might identify regions with better solutions.
*   **Focus on Medium/High-Frequency Edges:** Investigate edges such as (30, 34) that appear more frequently. These edges could be indicators of successful sub-structures. Further investigation is needed to understand why these edges are important.
*   **Refine Crossover and Mutation Strategies:** Tune the crossover and mutation operators to explore and exploit.

**5. Recommended Evolution Direction and Strategy Balance**

*   **Gradual Shift Towards Exploitation:** While exploration is important, the strategy should begin to shift toward more exploitation as the search progresses. This can be achieved by a few things:
    *   **Increasing Selection Pressure:** If a tournament selection is used, increase the tournament size. If roulette wheel selection is used, scale the fitness values to make the differences more prominent. This can help make better solutions be favored.
    *   **Refining the Mutation Strategy:** Focus mutations on the important features of the solutions. The more common paths or edges could be locked in during mutation. This would cause the algorithm to exploit the important features of the population.
*   **Building Blocks Incorporation:** Use the "Common Subpaths" information to guide search.
    *   **Crossover Operator:** Design the crossover operator to favor recombination of individuals with those subpaths. This could involve biasing crossover towards specific points.
    *   **Repair Mechanism:** Ensure that the subpaths are not disrupted by mutations or crossovers.
*   **Focus on Avoiding Low-Quality Regions:** Try to use the low quality regions to prevent the algorithm from converging to them.
    *   **Penalty:** Introduce a penalty if the algorithm visits these regions.

In summary, the algorithm appears to be in an early exploration phase in a rugged, multimodal search space. The focus should shift towards finding better solutions. The next steps are to exploit the building blocks and common edges, as well as tuning the crossover and mutation operators to improve the solution quality. It should also seek to shift towards more exploitation as the iterations increase.

2025-06-08 18:53:36,375 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-08 18:53:36,375 - __main__ - INFO - 景观专家分析报告: Okay, here's a comprehensive landscape analysis based on the provided data, focusing on evolutionary algorithm behavior and search space characteristics.

**1. Overall Search Space Characteristics**

*   **Rough Landscape:** The significant spread in cost statistics (Min=9998.0, Max=105388.0, Mean=72249.3, Std=40893.86) suggests a rugged or multimodal search space. The large standard deviation indicates substantial variability in solution quality, implying the existence of many local optima or plateaus.
*   **Presence of Building Blocks (Potentially):** The "Common Subpaths" data, even with a frequency of only 0.3, indicates potential building blocks. These frequent subpaths (e.g., \[4, 6, 5], \[28, 27, 29]) could be valuable partial solutions. Exploiting these could accelerate convergence.
*   **No Clear Convergence:** The "Convergence Level" of 0.0 suggests that the population has not yet converged.
*   **High Diversity:** The "Diversity Level" of 0.93 implies the population is highly diverse, which is expected in the early stages of search and in a multimodal landscape. This can be a good starting point.
*   **Cluster Structure:** The clustering information (9 clusters, sizes ranging from 1 to 2) reinforces the idea of a diverse population with some degree of structure. This suggests the algorithm may be sampling different regions of the search space but needs to improve exploitation.

**2. Current Population State Assessment**

*   **Early Stage Exploration:** The current state appears to be in an exploration phase. The high diversity, lack of convergence, and the cost distribution support this.
*   **Lack of Elite Solutions:** The absence of "Elite Solutions" makes it difficult to judge the search's success. The absence of elite solutions may mean that the best solutions found are not much better than the average ones.
*   **Potential for Premature Convergence:** Since there is no convergence, premature convergence does not look like a danger currently. However, with increasing iterations it may become an issue.
*   **Path Structure Information:** Edge and subpath frequencies provide insights into the structures in the search. They suggest that some paths are more likely to be included in a solution than others.
*   **Low-Quality Regions:** The identified "Low Quality Regions" highlight areas of the search space that consistently lead to poor solutions. These regions might contain traps or suboptimal configurations.

**3. Identification of Difficult Regions and Search Challenges**

*   **Local Optima:** The rugged landscape and cost distribution suggest the presence of numerous local optima. The algorithm might be getting trapped in these, preventing it from finding the global optimum.
*   **Exploration vs. Exploitation Imbalance:** At this point, it looks like exploration is the primary mode of search. However, a balance is needed, as the algorithm might struggle to locate high-quality solutions without enough focused exploitation.
*   **Difficulty in Identifying High-Quality Regions:** Without elite solutions, the algorithm might be having trouble distinguishing promising regions from less promising ones.
*   **Region Identification Challenges:** The "Low Quality Regions" may indicate areas where the algorithm has a hard time finding good solutions. The algorithm might be getting stuck in these regions because of poor crossover or mutation strategies.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Exploit Common Subpaths:** The "Common Subpaths" represent potential building blocks. By promoting the formation of those paths in the population, the algorithm might identify regions with better solutions.
*   **Focus on Medium/High-Frequency Edges:** Investigate edges such as (30, 34) that appear more frequently. These edges could be indicators of successful sub-structures. Further investigation is needed to understand why these edges are important.
*   **Refine Crossover and Mutation Strategies:** Tune the crossover and mutation operators to explore and exploit.

**5. Recommended Evolution Direction and Strategy Balance**

*   **Gradual Shift Towards Exploitation:** While exploration is important, the strategy should begin to shift toward more exploitation as the search progresses. This can be achieved by a few things:
    *   **Increasing Selection Pressure:** If a tournament selection is used, increase the tournament size. If roulette wheel selection is used, scale the fitness values to make the differences more prominent. This can help make better solutions be favored.
    *   **Refining the Mutation Strategy:** Focus mutations on the important features of the solutions. The more common paths or edges could be locked in during mutation. This would cause the algorithm to exploit the important features of the population.
*   **Building Blocks Incorporation:** Use the "Common Subpaths" information to guide search.
    *   **Crossover Operator:** Design the crossover operator to favor recombination of individuals with those subpaths. This could involve biasing crossover towards specific points.
    *   **Repair Mechanism:** Ensure that the subpaths are not disrupted by mutations or crossovers.
*   **Focus on Avoiding Low-Quality Regions:** Try to use the low quality regions to prevent the algorithm from converging to them.
    *   **Penalty:** Introduce a penalty if the algorithm visits these regions.

In summary, the algorithm appears to be in an early exploration phase in a rugged, multimodal search space. The focus should shift towards finding better solutions. The next steps are to exploit the building blocks and common edges, as well as tuning the crossover and mutation operators to improve the solution quality. It should also seek to shift towards more exploitation as the iterations increase.

2025-06-08 18:53:36,375 - __main__ - INFO - 分析阶段完成
2025-06-08 18:53:36,375 - __main__ - INFO - 景观分析完整报告: Okay, here's a comprehensive landscape analysis based on the provided data, focusing on evolutionary algorithm behavior and search space characteristics.

**1. Overall Search Space Characteristics**

*   **Rough Landscape:** The significant spread in cost statistics (Min=9998.0, Max=105388.0, Mean=72249.3, Std=40893.86) suggests a rugged or multimodal search space. The large standard deviation indicates substantial variability in solution quality, implying the existence of many local optima or plateaus.
*   **Presence of Building Blocks (Potentially):** The "Common Subpaths" data, even with a frequency of only 0.3, indicates potential building blocks. These frequent subpaths (e.g., \[4, 6, 5], \[28, 27, 29]) could be valuable partial solutions. Exploiting these could accelerate convergence.
*   **No Clear Convergence:** The "Convergence Level" of 0.0 suggests that the population has not yet converged.
*   **High Diversity:** The "Diversity Level" of 0.93 implies the population is highly diverse, which is expected in the early stages of search and in a multimodal landscape. This can be a good starting point.
*   **Cluster Structure:** The clustering information (9 clusters, sizes ranging from 1 to 2) reinforces the idea of a diverse population with some degree of structure. This suggests the algorithm may be sampling different regions of the search space but needs to improve exploitation.

**2. Current Population State Assessment**

*   **Early Stage Exploration:** The current state appears to be in an exploration phase. The high diversity, lack of convergence, and the cost distribution support this.
*   **Lack of Elite Solutions:** The absence of "Elite Solutions" makes it difficult to judge the search's success. The absence of elite solutions may mean that the best solutions found are not much better than the average ones.
*   **Potential for Premature Convergence:** Since there is no convergence, premature convergence does not look like a danger currently. However, with increasing iterations it may become an issue.
*   **Path Structure Information:** Edge and subpath frequencies provide insights into the structures in the search. They suggest that some paths are more likely to be included in a solution than others.
*   **Low-Quality Regions:** The identified "Low Quality Regions" highlight areas of the search space that consistently lead to poor solutions. These regions might contain traps or suboptimal configurations.

**3. Identification of Difficult Regions and Search Challenges**

*   **Local Optima:** The rugged landscape and cost distribution suggest the presence of numerous local optima. The algorithm might be getting trapped in these, preventing it from finding the global optimum.
*   **Exploration vs. Exploitation Imbalance:** At this point, it looks like exploration is the primary mode of search. However, a balance is needed, as the algorithm might struggle to locate high-quality solutions without enough focused exploitation.
*   **Difficulty in Identifying High-Quality Regions:** Without elite solutions, the algorithm might be having trouble distinguishing promising regions from less promising ones.
*   **Region Identification Challenges:** The "Low Quality Regions" may indicate areas where the algorithm has a hard time finding good solutions. The algorithm might be getting stuck in these regions because of poor crossover or mutation strategies.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Exploit Common Subpaths:** The "Common Subpaths" represent potential building blocks. By promoting the formation of those paths in the population, the algorithm might identify regions with better solutions.
*   **Focus on Medium/High-Frequency Edges:** Investigate edges such as (30, 34) that appear more frequently. These edges could be indicators of successful sub-structures. Further investigation is needed to understand why these edges are important.
*   **Refine Crossover and Mutation Strategies:** Tune the crossover and mutation operators to explore and exploit.

**5. Recommended Evolution Direction and Strategy Balance**

*   **Gradual Shift Towards Exploitation:** While exploration is important, the strategy should begin to shift toward more exploitation as the search progresses. This can be achieved by a few things:
    *   **Increasing Selection Pressure:** If a tournament selection is used, increase the tournament size. If roulette wheel selection is used, scale the fitness values to make the differences more prominent. This can help make better solutions be favored.
    *   **Refining the Mutation Strategy:** Focus mutations on the important features of the solutions. The more common paths or edges could be locked in during mutation. This would cause the algorithm to exploit the important features of the population.
*   **Building Blocks Incorporation:** Use the "Common Subpaths" information to guide search.
    *   **Crossover Operator:** Design the crossover operator to favor recombination of individuals with those subpaths. This could involve biasing crossover towards specific points.
    *   **Repair Mechanism:** Ensure that the subpaths are not disrupted by mutations or crossovers.
*   **Focus on Avoiding Low-Quality Regions:** Try to use the low quality regions to prevent the algorithm from converging to them.
    *   **Penalty:** Introduce a penalty if the algorithm visits these regions.

In summary, the algorithm appears to be in an early exploration phase in a rugged, multimodal search space. The focus should shift towards finding better solutions. The next steps are to exploit the building blocks and common edges, as well as tuning the crossover and mutation operators to improve the solution quality. It should also seek to shift towards more exploitation as the iterations increase.

2025-06-08 18:53:36,375 - __main__ - INFO - 开始策略分配阶段
2025-06-08 18:53:36,375 - StrategyExpert - INFO - 开始策略分配分析
2025-06-08 18:53:36,375 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
# Strategy Selection Task

## Role Definition
You are an expert in evolutionary algorithms and combinatorial optimization. You excel at analyzing search space characteristics and population states to select the optimal balance between exploration and exploitation, and to assign appropriate strategies to different individuals.

## Current Iteration: 0

## Population Information
[{"id": 0, "cost": 10013.0, "diversity_contribution": null}, {"id": 1, "cost": 9998.0, "diversity_contribution": null}, {"id": 2, "cost": 10302.0, "diversity_contribution": null}, {"id": 3, "cost": 105388.0, "diversity_contribution": null}, {"id": 4, "cost": 102962.0, "diversity_contribution": null}, {"id": 5, "cost": 97510.0, "diversity_contribution": null}, {"id": 6, "cost": 92358.0, "diversity_contribution": null}, {"id": 7, "cost": 91846.0, "diversity_contribution": null}, {"id": 8, "cost": 103204.0, "diversity_contribution": null}, {"id": 9, "cost": 98912.0, "diversity_contribution": null}]

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Population State: Population status extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Recommended Evolution Direction: Suggestions on evolutionary direction extracted from landscape analysis

## Previous Feedback (if available)
None

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. The above data comes from the landscape analysis expert's analysis results and the previous generation's assessment feedback. We need you to determine the optimal exploration/exploitation balance for the current iteration and assign appropriate strategies to each individual.

# Strategy Selection Request
Please determine the optimal strategy allocation plan for the current iteration. Please follow these steps in your thinking:

## Step 1: Analyze Current Search State
- Evaluate search space characteristics (ruggedness, modality, deceptiveness, etc.)
- Analyze the current state of the population (exploration phase, diversity, convergence, etc.)
- Consider feedback and effects from the previous generation (if available)
- Identify the main challenges and opportunities currently faced

## Step 2: Determine Global Exploration Ratio
- Based on search space characteristics, determine the base exploration ratio
- Adjust the exploration ratio based on population state
- Consider the impact of evolutionary stage on exploration needs
- Analyze the effects of the previous generation's strategy to further adjust the ratio
- Determine the final global exploration ratio (between 0.0 and 1.0)

## Step 3: Build Strategy Profiles for Individuals
- Analyze the characteristics of each individual (quality, diversity contribution, historical performance, etc.)
- Evaluate the degree to which each individual is suitable for exploration or exploitation
- Consider the relationship between individuals and difficult/opportunity regions
- Build strategy tendency profiles for each individual

## Step 4: Assign Individual Strategies
- Based on the global exploration ratio, determine the number of individuals to assign exploration strategies
- Prioritize assigning exploration strategies to individuals most suitable for exploration
- Assign exploitation strategies to the remaining individuals
- Ensure strategy allocation complies with the global exploration ratio
- Record the rationale for each allocation decision

## Step 5: Determine Strategy Parameters
- Based on search space characteristics and population state, determine exploration strategy parameters
  - Diversity weight: controls the diversity level of generated solutions
  - Knowledge usage weight: controls the degree of historical knowledge utilization
  - Risk tolerance: controls the possibility of accepting suboptimal solutions
- Based on search space characteristics and population state, determine exploitation strategy parameters
  - Local search depth: controls the depth of local search
  - Quality edge usage: controls the degree of utilizing known high-quality edges
  - Elite influence: controls the influence of elite solutions on new solution generation

Please return your analysis results in JSON format with the following sections:
```json
{
  "global_explore_ratio": float,
  "justification": string,
  "individual_assignments": {
    "individual_id": "explore|exploit",
    ...
  },
  "assignment_rationale": {
    "individual_id": string,
    ...
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": float,
      "knowledge_usage_weight": float,
      "risk_tolerance": float
    },
    "exploit": {
      "local_search_depth": int,
      "quality_edge_usage": float,
      "elite_influence": float
    }
  }
}
```

Additionally, please provide a brief summary explaining your strategy selection logic and its expected effects.

2025-06-08 18:53:36,375 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-08 18:53:43,085 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.3,
  "justification": "The initial population has some individuals with relatively good cost values (around 10000) but also a significant portion of individuals with much higher costs (above 90000 and 100000). This suggests the search space might have some promising areas that are easily accessible (the good individuals) but also difficult regions where many individuals are getting trapped. Since this is iteration 0, we don't have historical data or landscape analysis information, therefore a moderate exploration ratio is used to discover different regions and to avoid premature convergence. The goal is to sample more of the search space to assess landscape features and identify potential opportunities.",
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "Exploiting. This individual has a relatively low cost among the population and can be refined.",
    "1": "Exploiting. This individual has a relatively low cost among the population and can be refined.",
    "2": "Exploiting. This individual has a relatively low cost among the population and can be refined.",
    "3": "Exploring. This individual is very bad (high cost) and exploration is needed to diversify and escape potential local optima. The exploration might reveal better regions than what is represented by this individual.",
    "4": "Exploring. This individual is very bad (high cost) and exploration is needed to diversify and escape potential local optima. The exploration might reveal better regions than what is represented by this individual.",
    "5": "Exploring. This individual is very bad (high cost) and exploration is needed to diversify and escape potential local optima. The exploration might reveal better regions than what is represented by this individual.",
    "6": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region.",
    "7": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region.",
    "8": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region.",
    "9": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.7,
      "knowledge_usage_weight": 0.2,
      "risk_tolerance": 0.4
    },
    "exploit": {
      "local_search_depth": 3,
      "quality_edge_usage": 0.6,
      "elite_influence": 0.3
    }
  }
}
```

**Summary of Strategy Selection Logic and Expected Effects:**

The strategy aims to balance exploration and exploitation given the limited initial information. A 30% exploration ratio is used to allow for discovery of new promising regions, especially since the initial population quality varies. The strategy emphasizes exploration for the worst performing individuals and exploitation for those with relatively good performance. This mixed approach is designed to:

*   **Discover new regions:** High diversity weight in exploration will encourage generation of diverse solutions to avoid premature convergence.
*   **Refine promising regions:** Exploitation attempts to refine and improve solutions found in better regions.
*   **Avoid getting trapped:** Moderate risk tolerance in exploration allows for escaping local optima.
*   **Maintain good solutions:** Exploitation uses knowledge of good solutions and local search to preserve and enhance promising regions.

The expected effect is a balanced search, potentially leading to quick discovery of better regions while avoiding premature convergence. The strategy also allows the algorithm to explore and assess the landscape, allowing for future informed decisions as more data becomes available.

2025-06-08 18:53:43,087 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-08 18:53:43,087 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.3,
  "justification": "The initial population has some individuals with relatively good cost values (around 10000) but also a significant portion of individuals with much higher costs (above 90000 and 100000). This suggests the search space might have some promising areas that are easily accessible (the good individuals) but also difficult regions where many individuals are getting trapped. Since this is iteration 0, we don't have historical data or landscape analysis information, therefore a moderate exploration ratio is used to discover different regions and to avoid premature convergence. The goal is to sample more of the search space to assess landscape features and identify potential opportunities.",
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "Exploiting. This individual has a relatively low cost among the population and can be refined.",
    "1": "Exploiting. This individual has a relatively low cost among the population and can be refined.",
    "2": "Exploiting. This individual has a relatively low cost among the population and can be refined.",
    "3": "Exploring. This individual is very bad (high cost) and exploration is needed to diversify and escape potential local optima. The exploration might reveal better regions than what is represented by this individual.",
    "4": "Exploring. This individual is very bad (high cost) and exploration is needed to diversify and escape potential local optima. The exploration might reveal better regions than what is represented by this individual.",
    "5": "Exploring. This individual is very bad (high cost) and exploration is needed to diversify and escape potential local optima. The exploration might reveal better regions than what is represented by this individual.",
    "6": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region.",
    "7": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region.",
    "8": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region.",
    "9": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.7,
      "knowledge_usage_weight": 0.2,
      "risk_tolerance": 0.4
    },
    "exploit": {
      "local_search_depth": 3,
      "quality_edge_usage": 0.6,
      "elite_influence": 0.3
    }
  }
}
```

**Summary of Strategy Selection Logic and Expected Effects:**

The strategy aims to balance exploration and exploitation given the limited initial information. A 30% exploration ratio is used to allow for discovery of new promising regions, especially since the initial population quality varies. The strategy emphasizes exploration for the worst performing individuals and exploitation for those with relatively good performance. This mixed approach is designed to:

*   **Discover new regions:** High diversity weight in exploration will encourage generation of diverse solutions to avoid premature convergence.
*   **Refine promising regions:** Exploitation attempts to refine and improve solutions found in better regions.
*   **Avoid getting trapped:** Moderate risk tolerance in exploration allows for escaping local optima.
*   **Maintain good solutions:** Exploitation uses knowledge of good solutions and local search to preserve and enhance promising regions.

The expected effect is a balanced search, potentially leading to quick discovery of better regions while avoiding premature convergence. The strategy also allows the algorithm to explore and assess the landscape, allowing for future informed decisions as more data becomes available.

2025-06-08 18:53:43,087 - __main__ - INFO - 策略分配阶段完成
2025-06-08 18:53:43,087 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-08 18:53:43,087 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "justification": "The initial population has some individuals with relatively good cost values (around 10000) but also a significant portion of individuals with much higher costs (above 90000 and 100000). This suggests the search space might have some promising areas that are easily accessible (the good individuals) but also difficult regions where many individuals are getting trapped. Since this is iteration 0, we don't have historical data or landscape analysis information, therefore a moderate exploration ratio is used to discover different regions and to avoid premature convergence. The goal is to sample more of the search space to assess landscape features and identify potential opportunities.",
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "Exploiting. This individual has a relatively low cost among the population and can be refined.",
    "1": "Exploiting. This individual has a relatively low cost among the population and can be refined.",
    "2": "Exploiting. This individual has a relatively low cost among the population and can be refined.",
    "3": "Exploring. This individual is very bad (high cost) and exploration is needed to diversify and escape potential local optima. The exploration might reveal better regions than what is represented by this individual.",
    "4": "Exploring. This individual is very bad (high cost) and exploration is needed to diversify and escape potential local optima. The exploration might reveal better regions than what is represented by this individual.",
    "5": "Exploring. This individual is very bad (high cost) and exploration is needed to diversify and escape potential local optima. The exploration might reveal better regions than what is represented by this individual.",
    "6": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region.",
    "7": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region.",
    "8": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region.",
    "9": "Exploiting. This individual is very bad, but we can try exploitation as a complement to exploration. If exploration fails, we still give a chance of improvement in this region."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.7,
      "knowledge_usage_weight": 0.2,
      "risk_tolerance": 0.4
    },
    "exploit": {
      "local_search_depth": 3,
      "quality_edge_usage": 0.6,
      "elite_influence": 0.3
    }
  }
}
```

**Summary of Strategy Selection Logic and Expected Effects:**

The strategy aims to balance exploration and exploitation given the limited initial information. A 30% exploration ratio is used to allow for discovery of new promising regions, especially since the initial population quality varies. The strategy emphasizes exploration for the worst performing individuals and exploitation for those with relatively good performance. This mixed approach is designed to:

*   **Discover new regions:** High diversity weight in exploration will encourage generation of diverse solutions to avoid premature convergence.
*   **Refine promising regions:** Exploitation attempts to refine and improve solutions found in better regions.
*   **Avoid getting trapped:** Moderate risk tolerance in exploration allows for escaping local optima.
*   **Maintain good solutions:** Exploitation uses knowledge of good solutions and local search to preserve and enhance promising regions.

The expected effect is a balanced search, potentially leading to quick discovery of better regions while avoiding premature convergence. The strategy also allows the algorithm to explore and assess the landscape, allowing for future informed decisions as more data becomes available.

2025-06-08 18:53:43,088 - __main__ - INFO - 开始进化阶段
2025-06-08 18:53:43,088 - __main__ - INFO - 为个体 0 生成利用路径
2025-06-08 18:53:43,088 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:53:43,088 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:53:43,088 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 86151.0
2025-06-08 18:53:45,885 - ExploitationExpert - INFO - res_population_num: 1
2025-06-08 18:53:45,885 - ExploitationExpert - INFO - res_population_costs: [9912]
2025-06-08 18:53:45,885 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 13, 12, 23, 16, 20, 22, 17, 15,
       19, 14, 21, 18, 59, 48, 53, 54, 58, 49, 56, 55, 57, 52, 50, 51, 44,
       43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-08 18:53:45,885 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:53:45,885 - ExploitationExpert - INFO - populations: [{'tour': array([53, 20, 29, 16, 57,  1, 44, 59, 17, 54, 24, 27,  5, 48, 37, 40, 55,
       11, 10, 22, 51, 46, 56, 36, 41, 39, 49,  9,  4, 34, 25, 14, 47, 43,
       42, 32, 35, 21, 31,  8, 50, 45, 28, 52,  7, 30, 38, 12, 58, 18, 13,
       15, 23, 33,  6, 26,  0, 19,  3,  2]), 'cur_cost': 86151.0}, {'tour': [44, 37, 40, 46, 39, 41, 43, 45, 38, 42, 47, 36, 4, 6, 5, 0, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34], 'cur_cost': 9998.0}, {'tour': [14, 21, 18, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 0, 4, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10302.0}, {'tour': [19, 12, 11, 44, 58, 35, 46, 54, 27, 14, 41, 2, 15, 4, 30, 26, 6, 32, 39, 22, 3, 0, 45, 28, 55, 20, 36, 16, 1, 24, 7, 56, 57, 59, 47, 37, 48, 25, 8, 50, 17, 33, 49, 51, 21, 38, 13, 40, 43, 52, 42, 34, 18, 53, 9, 5, 10, 29, 23, 31], 'cur_cost': 105388.0}, {'tour': [50, 41, 12, 39, 56, 59, 17, 2, 10, 34, 26, 27, 4, 20, 11, 53, 24, 47, 33, 42, 16, 8, 52, 43, 31, 40, 0, 13, 1, 51, 28, 30, 49, 48, 38, 57, 19, 58, 7, 35, 54, 5, 46, 32, 3, 29, 55, 23, 14, 6, 18, 22, 45, 9, 37, 21, 25, 15, 36, 44], 'cur_cost': 102962.0}, {'tour': [41, 21, 20, 18, 46, 16, 31, 58, 55, 47, 12, 23, 24, 50, 51, 59, 35, 39, 11, 38, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27], 'cur_cost': 97510.0}, {'tour': [49, 28, 59, 55, 22, 19, 30, 37, 13, 57, 43, 36, 23, 14, 2, 47, 17, 33, 3, 39, 34, 42, 44, 46, 10, 51, 16, 29, 24, 26, 1, 11, 56, 12, 4, 35, 41, 7, 53, 20, 9, 27, 48, 45, 32, 58, 38, 31, 25, 21, 52, 6, 15, 54, 8, 40, 5, 50, 0, 18], 'cur_cost': 92358.0}, {'tour': [40, 16, 11, 15, 45, 5, 6, 41, 46, 14, 42, 48, 37, 47, 38, 7, 56, 13, 19, 50, 33, 10, 25, 34, 32, 59, 27, 4, 36, 20, 1, 29, 31, 54, 8, 12, 9, 35, 2, 18, 57, 26, 24, 55, 44, 21, 53, 23, 17, 49, 0, 51, 52, 22, 28, 39, 3, 58, 30, 43], 'cur_cost': 91846.0}, {'tour': [2, 24, 16, 20, 7, 12, 17, 29, 6, 32, 5, 56, 22, 35, 53, 26, 37, 18, 55, 38, 34, 40, 36, 8, 9, 50, 19, 39, 1, 48, 14, 0, 46, 21, 31, 49, 54, 59, 42, 3, 10, 25, 44, 13, 30, 15, 52, 47, 51, 11, 4, 28, 41, 27, 57, 45, 33, 43, 58, 23], 'cur_cost': 103204.0}, {'tour': [49, 43, 28, 29, 24, 54, 44, 51, 13, 48, 46, 31, 12, 21, 38, 15, 39, 25, 7, 45, 41, 40, 14, 58, 59, 42, 1, 6, 10, 33, 8, 19, 52, 55, 5, 20, 56, 11, 32, 34, 18, 2, 23, 26, 53, 37, 17, 35, 47, 57, 9, 36, 0, 16, 30, 50, 22, 3, 4, 27], 'cur_cost': 98912.0}]
2025-06-08 18:53:45,885 - ExploitationExpert - INFO - 局部搜索耗时: 2.80秒
2025-06-08 18:53:45,885 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-08 18:53:45,885 - __main__ - WARNING - 个体 0 的利用路径生成失败，保留原个体
2025-06-08 18:53:45,890 - __main__ - INFO - 为个体 1 生成利用路径
2025-06-08 18:53:45,890 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:53:45,890 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:53:45,890 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 112741.0
2025-06-08 18:53:46,861 - ExploitationExpert - INFO - res_population_num: 2
2025-06-08 18:53:46,861 - ExploitationExpert - INFO - res_population_costs: [9912, 9626]
2025-06-08 18:53:46,861 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 13, 12, 23, 16, 20, 22, 17, 15,
       19, 14, 21, 18, 59, 48, 53, 54, 58, 49, 56, 55, 57, 52, 50, 51, 44,
       43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 59, 51, 50, 58, 49, 56, 55, 57,
       52, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-06-08 18:53:46,861 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:53:46,861 - ExploitationExpert - INFO - populations: [{'tour': array([53, 20, 29, 16, 57,  1, 44, 59, 17, 54, 24, 27,  5, 48, 37, 40, 55,
       11, 10, 22, 51, 46, 56, 36, 41, 39, 49,  9,  4, 34, 25, 14, 47, 43,
       42, 32, 35, 21, 31,  8, 50, 45, 28, 52,  7, 30, 38, 12, 58, 18, 13,
       15, 23, 33,  6, 26,  0, 19,  3,  2]), 'cur_cost': 86151.0}, {'tour': array([ 8, 42, 27, 39, 28,  4, 18, 43, 57, 25, 26, 46, 33, 22, 32, 37, 14,
       51, 23, 29, 35, 56, 52, 12, 10, 59, 11, 40,  3, 13, 34, 50, 38, 54,
        6, 21, 24, 17, 47,  7, 15, 16,  5, 20, 41, 19,  0, 55, 31, 53,  2,
       58,  1, 49, 36, 48,  9, 44, 45, 30]), 'cur_cost': 112741.0}, {'tour': [14, 21, 18, 19, 15, 17, 22, 20, 16, 23, 12, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 0, 4, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10302.0}, {'tour': [19, 12, 11, 44, 58, 35, 46, 54, 27, 14, 41, 2, 15, 4, 30, 26, 6, 32, 39, 22, 3, 0, 45, 28, 55, 20, 36, 16, 1, 24, 7, 56, 57, 59, 47, 37, 48, 25, 8, 50, 17, 33, 49, 51, 21, 38, 13, 40, 43, 52, 42, 34, 18, 53, 9, 5, 10, 29, 23, 31], 'cur_cost': 105388.0}, {'tour': [50, 41, 12, 39, 56, 59, 17, 2, 10, 34, 26, 27, 4, 20, 11, 53, 24, 47, 33, 42, 16, 8, 52, 43, 31, 40, 0, 13, 1, 51, 28, 30, 49, 48, 38, 57, 19, 58, 7, 35, 54, 5, 46, 32, 3, 29, 55, 23, 14, 6, 18, 22, 45, 9, 37, 21, 25, 15, 36, 44], 'cur_cost': 102962.0}, {'tour': [41, 21, 20, 18, 46, 16, 31, 58, 55, 47, 12, 23, 24, 50, 51, 59, 35, 39, 11, 38, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27], 'cur_cost': 97510.0}, {'tour': [49, 28, 59, 55, 22, 19, 30, 37, 13, 57, 43, 36, 23, 14, 2, 47, 17, 33, 3, 39, 34, 42, 44, 46, 10, 51, 16, 29, 24, 26, 1, 11, 56, 12, 4, 35, 41, 7, 53, 20, 9, 27, 48, 45, 32, 58, 38, 31, 25, 21, 52, 6, 15, 54, 8, 40, 5, 50, 0, 18], 'cur_cost': 92358.0}, {'tour': [40, 16, 11, 15, 45, 5, 6, 41, 46, 14, 42, 48, 37, 47, 38, 7, 56, 13, 19, 50, 33, 10, 25, 34, 32, 59, 27, 4, 36, 20, 1, 29, 31, 54, 8, 12, 9, 35, 2, 18, 57, 26, 24, 55, 44, 21, 53, 23, 17, 49, 0, 51, 52, 22, 28, 39, 3, 58, 30, 43], 'cur_cost': 91846.0}, {'tour': [2, 24, 16, 20, 7, 12, 17, 29, 6, 32, 5, 56, 22, 35, 53, 26, 37, 18, 55, 38, 34, 40, 36, 8, 9, 50, 19, 39, 1, 48, 14, 0, 46, 21, 31, 49, 54, 59, 42, 3, 10, 25, 44, 13, 30, 15, 52, 47, 51, 11, 4, 28, 41, 27, 57, 45, 33, 43, 58, 23], 'cur_cost': 103204.0}, {'tour': [49, 43, 28, 29, 24, 54, 44, 51, 13, 48, 46, 31, 12, 21, 38, 15, 39, 25, 7, 45, 41, 40, 14, 58, 59, 42, 1, 6, 10, 33, 8, 19, 52, 55, 5, 20, 56, 11, 32, 34, 18, 2, 23, 26, 53, 37, 17, 35, 47, 57, 9, 36, 0, 16, 30, 50, 22, 3, 4, 27], 'cur_cost': 98912.0}]
2025-06-08 18:53:46,873 - ExploitationExpert - INFO - 局部搜索耗时: 0.98秒
2025-06-08 18:53:46,873 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-08 18:53:46,873 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-06-08 18:53:46,873 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-08 18:53:46,873 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:53:46,873 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:53:46,873 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 97217.0
2025-06-08 18:53:47,039 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: critical_edge
2025-06-08 18:53:47,182 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:53:47,188 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:53:47,203 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,206 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,209 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:53:47,211 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:53:47,216 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:53:47,221 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:53:47,231 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,231 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,233 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,238 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: pattern_based
2025-06-08 18:53:47,240 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:53:47,240 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,246 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:53:47,247 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,249 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,253 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,254 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,258 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,264 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,273 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,277 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,278 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: segment_preservation
2025-06-08 18:53:47,286 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:53:47,288 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,292 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:53:47,294 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,296 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,297 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:53:47,299 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:53:47,299 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:53:47,303 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,305 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,307 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,308 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,309 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,310 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,310 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,311 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:53:47,313 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,313 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,313 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,314 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,315 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,316 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,318 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:53:47,319 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,320 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,321 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,322 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:53:47,323 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,328 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,329 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,329 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:53:47,334 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,335 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,336 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:53:47,338 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,338 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,339 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,341 - root - INFO - 拓扑感知扰动用时: 0.0014秒，使用策略: pattern_based
2025-06-08 18:53:47,341 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,344 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:53:47,349 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,349 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,350 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:53:47,351 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:53:47,351 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:53:47,354 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:53:49,043 - ExploitationExpert - INFO - res_population_num: 21
2025-06-08 18:53:49,043 - ExploitationExpert - INFO - res_population_costs: [9912, 9626, 9626, 9626, 9626, 9621, 9621, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-08 18:53:49,043 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 13, 12, 23, 16, 20, 22, 17, 15,
       19, 14, 21, 18, 59, 48, 53, 54, 58, 49, 56, 55, 57, 52, 50, 51, 44,
       43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 59, 51, 50, 58, 49, 56, 55, 57,
       52, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-06-08 18:53:49,053 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:53:49,053 - ExploitationExpert - INFO - populations: [{'tour': array([53, 20, 29, 16, 57,  1, 44, 59, 17, 54, 24, 27,  5, 48, 37, 40, 55,
       11, 10, 22, 51, 46, 56, 36, 41, 39, 49,  9,  4, 34, 25, 14, 47, 43,
       42, 32, 35, 21, 31,  8, 50, 45, 28, 52,  7, 30, 38, 12, 58, 18, 13,
       15, 23, 33,  6, 26,  0, 19,  3,  2]), 'cur_cost': 86151.0}, {'tour': array([ 8, 42, 27, 39, 28,  4, 18, 43, 57, 25, 26, 46, 33, 22, 32, 37, 14,
       51, 23, 29, 35, 56, 52, 12, 10, 59, 11, 40,  3, 13, 34, 50, 38, 54,
        6, 21, 24, 17, 47,  7, 15, 16,  5, 20, 41, 19,  0, 55, 31, 53,  2,
       58,  1, 49, 36, 48,  9, 44, 45, 30]), 'cur_cost': 112741.0}, {'tour': array([53, 54,  5, 43, 42, 49,  3, 30, 40,  2,  0, 34, 52, 23, 21, 57, 28,
       27, 41, 33, 17, 18, 46, 35, 55, 32, 26, 11, 13,  9, 47, 22, 31, 29,
       12, 37, 24,  1, 50, 58, 39, 48, 14,  7, 44,  6,  4, 20, 19, 51, 10,
       25, 59, 56, 36, 16, 45, 15, 38,  8]), 'cur_cost': 97217.0}, {'tour': [19, 12, 11, 44, 58, 35, 46, 54, 27, 14, 41, 2, 15, 4, 30, 26, 6, 32, 39, 22, 3, 0, 45, 28, 55, 20, 36, 16, 1, 24, 7, 56, 57, 59, 47, 37, 48, 25, 8, 50, 17, 33, 49, 51, 21, 38, 13, 40, 43, 52, 42, 34, 18, 53, 9, 5, 10, 29, 23, 31], 'cur_cost': 105388.0}, {'tour': [50, 41, 12, 39, 56, 59, 17, 2, 10, 34, 26, 27, 4, 20, 11, 53, 24, 47, 33, 42, 16, 8, 52, 43, 31, 40, 0, 13, 1, 51, 28, 30, 49, 48, 38, 57, 19, 58, 7, 35, 54, 5, 46, 32, 3, 29, 55, 23, 14, 6, 18, 22, 45, 9, 37, 21, 25, 15, 36, 44], 'cur_cost': 102962.0}, {'tour': [41, 21, 20, 18, 46, 16, 31, 58, 55, 47, 12, 23, 24, 50, 51, 59, 35, 39, 11, 38, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27], 'cur_cost': 97510.0}, {'tour': [49, 28, 59, 55, 22, 19, 30, 37, 13, 57, 43, 36, 23, 14, 2, 47, 17, 33, 3, 39, 34, 42, 44, 46, 10, 51, 16, 29, 24, 26, 1, 11, 56, 12, 4, 35, 41, 7, 53, 20, 9, 27, 48, 45, 32, 58, 38, 31, 25, 21, 52, 6, 15, 54, 8, 40, 5, 50, 0, 18], 'cur_cost': 92358.0}, {'tour': [40, 16, 11, 15, 45, 5, 6, 41, 46, 14, 42, 48, 37, 47, 38, 7, 56, 13, 19, 50, 33, 10, 25, 34, 32, 59, 27, 4, 36, 20, 1, 29, 31, 54, 8, 12, 9, 35, 2, 18, 57, 26, 24, 55, 44, 21, 53, 23, 17, 49, 0, 51, 52, 22, 28, 39, 3, 58, 30, 43], 'cur_cost': 91846.0}, {'tour': [2, 24, 16, 20, 7, 12, 17, 29, 6, 32, 5, 56, 22, 35, 53, 26, 37, 18, 55, 38, 34, 40, 36, 8, 9, 50, 19, 39, 1, 48, 14, 0, 46, 21, 31, 49, 54, 59, 42, 3, 10, 25, 44, 13, 30, 15, 52, 47, 51, 11, 4, 28, 41, 27, 57, 45, 33, 43, 58, 23], 'cur_cost': 103204.0}, {'tour': [49, 43, 28, 29, 24, 54, 44, 51, 13, 48, 46, 31, 12, 21, 38, 15, 39, 25, 7, 45, 41, 40, 14, 58, 59, 42, 1, 6, 10, 33, 8, 19, 52, 55, 5, 20, 56, 11, 32, 34, 18, 2, 23, 26, 53, 37, 17, 35, 47, 57, 9, 36, 0, 16, 30, 50, 22, 3, 4, 27], 'cur_cost': 98912.0}]
2025-06-08 18:53:49,055 - ExploitationExpert - INFO - 局部搜索耗时: 2.18秒
2025-06-08 18:53:49,055 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-08 18:53:49,056 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-06-08 18:53:49,056 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-08 18:53:49,058 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 18:53:49,079 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[19, 12, 11, 44, 58, 35, 46, 54, 27, 14, 41, 2, 15, 4, 30, 26, 6, 32, 39, 22, 3, 0, 45, 28, 55, 20, 36, 16, 1, 24, 7, 56, 57, 59, 47, 37, 48, 25, 8, 50, 17, 33, 49, 51, 21, 38, 13, 40, 43, 52, 42, 34, 18, 53, 9, 5, 10, 29, 23, 31]

## Path Cost: 105388.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9696296296296296

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 18:53:49,081 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 18:53:54,864 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19],
  "modification_strategy": "Large-scale restructuring with regional refinement.",
  "targeted_regions": ["High-diversity regions", "Potentially high-reward regions based on reverse order exploration"],
  "avoided_regions": ["Regions exhibiting high path costs based on the landscape analysis (Implicit, as no specific regions are provided)."],
  "knowledge_application": "Reverse the path partially to explore alternative sequence",
  "diversity_contribution": "High. This strategy aims for significant structural changes, promoting exploration of diverse solution areas.",
  "expected_benefits": "Increased population diversity, potential discovery of new solution structures, and improved exploration of the search space. This should provide a significant different path while not necessarily guaranteeing a lower path cost at this stage."
}
```

**Explanation:**

**Step 1: Analyze Current Path**

The current path appears to be a full tour of the TSP problem, likely generated by a previous iteration of the evolutionary algorithm. The path is relatively long (60 nodes) with a path cost of 105388.0. Its specific structure and order is not critical to understand, but the goal is to create a significantly different path, rather than simply modifying this one.

**Step 2: Determine Exploration Strategy**

Given the parameters:
*   **Diversity Weight: 0.7:**  High emphasis on diversity. Therefore, a strategy that promotes substantial changes is needed.
*   **Knowledge Usage Weight: 0.3:** Moderate use of historical knowledge. This allows for a slight influence from prior observations.
*   **Risk Tolerance: 0.6:** Moderate risk tolerance.

The chosen strategy is **Large-scale restructuring with regional refinement.** This strategy will significantly alter the path while attempting to explore new areas, leveraging the diversity focus. Regional refinement ensures potentially good sections are kept as it is believed to give the best results.

**Step 3: Select Target Regions**

*   Targeted exploration regions are based on the landscape analysis with high potential regions.

**Step 4: Apply Historical Knowledge**

Since no specific knowledge was provided, a simple but effective strategy: Reverse order the path partly. This technique can explore vastly different combinations without requiring explicit historical data.

**Step 5: Generate New Path**

The new path is generated by applying large restructuring with the reversed path portions. This creates a path that is markedly different from the original and should increase exploration potential.

2025-06-08 18:53:54,864 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 18:53:54,867 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 18:53:54,867 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 18:53:54,867 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103309.0, 路径: [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19]
2025-06-08 18:53:54,867 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_path': [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19], 'modification_strategy': 'Large-scale restructuring with regional refinement.', 'targeted_regions': ['High-diversity regions', 'Potentially high-reward regions based on reverse order exploration'], 'avoided_regions': ['Regions exhibiting high path costs based on the landscape analysis (Implicit, as no specific regions are provided).'], 'knowledge_application': 'Reverse the path partially to explore alternative sequence', 'diversity_contribution': 'High. This strategy aims for significant structural changes, promoting exploration of diverse solution areas.', 'expected_benefits': 'Increased population diversity, potential discovery of new solution structures, and improved exploration of the search space. This should provide a significant different path while not necessarily guaranteeing a lower path cost at this stage.', 'new_tour': [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19], 'cur_cost': 103309.0}
2025-06-08 18:53:54,867 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-08 18:53:54,867 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 18:53:54,878 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[50, 41, 12, 39, 56, 59, 17, 2, 10, 34, 26, 27, 4, 20, 11, 53, 24, 47, 33, 42, 16, 8, 52, 43, 31, 40, 0, 13, 1, 51, 28, 30, 49, 48, 38, 57, 19, 58, 7, 35, 54, 5, 46, 32, 3, 29, 55, 23, 14, 6, 18, 22, 45, 9, 37, 21, 25, 15, 36, 44]

## Path Cost: 102962.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9781481481481483

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 18:53:54,879 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 18:54:00,230 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50],
  "modification_strategy": "Large-scale restructuring with regional recombination.",
  "targeted_regions": ["Region 1 (nodes 0-10)", "Region 2 (nodes 10-20)", "Region 3 (nodes 20-30)"],
  "avoided_regions": [],
  "knowledge_application": "No historical knowledge to apply in this turn, so a more diverse approach is used.",
  "diversity_contribution": "High. The new path is significantly different from the current path, increasing diversity significantly.",
  "expected_benefits": "Increased population diversity, potential discovery of new high-quality solutions by exploring previously unvisited regions of the search space."
}
```

**Explanation:**

The current path, while complete, needs significant diversification. Given the high diversity weight (0.7), and the absence of retrieved knowledge, a significant restructuring is warranted. The risk tolerance of 0.6 suggests a moderately aggressive approach, so we'll go for it!

*   **Step 1: Analyze Current Path:** The current path is a complete tour. We do not have any information about the landscape, so we make an educated guess, and assume regions can be identified by node index ranges.
*   **Step 2: Determine Exploration Strategy:**  The strategy is a large-scale restructuring with regional recombination (or partial shuffling). Since we lack landscape features and historical knowledge, we lean towards random exploration combined with some localized optimization through partial shuffling.
*   **Step 3: Select Target Regions:** Since we don't know the landscape features, we target different node regions by swapping the order and creating different orders of nodes. This assumes that nodes within a defined range of indexes create a region.
*   **Step 4: Apply Historical Knowledge:** No knowledge is available to guide this exploration.
*   **Step 5: Generate New Path:** The `new_path` is generated by reordering the node indexes to create significant diversity and explore the target regions in a new order. It randomly picks start points and proceeds to change the route.
*   The strategy ensures that the new path explores different regions compared to the original.

2025-06-08 18:54:00,230 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 18:54:00,230 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 18:54:00,230 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 18:54:00,230 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102894.0, 路径: [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50]
2025-06-08 18:54:00,230 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_path': [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50], 'modification_strategy': 'Large-scale restructuring with regional recombination.', 'targeted_regions': ['Region 1 (nodes 0-10)', 'Region 2 (nodes 10-20)', 'Region 3 (nodes 20-30)'], 'avoided_regions': [], 'knowledge_application': 'No historical knowledge to apply in this turn, so a more diverse approach is used.', 'diversity_contribution': 'High. The new path is significantly different from the current path, increasing diversity significantly.', 'expected_benefits': 'Increased population diversity, potential discovery of new high-quality solutions by exploring previously unvisited regions of the search space.', 'new_tour': [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50], 'cur_cost': 102894.0}
2025-06-08 18:54:00,230 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-08 18:54:00,230 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 18:54:00,238 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[41, 21, 20, 18, 46, 16, 31, 58, 55, 47, 12, 23, 24, 50, 51, 59, 35, 39, 11, 38, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27]

## Path Cost: 97510.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9811048336472066

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 18:54:00,238 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 18:54:06,402 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38],
  "modification_strategy": "Large-scale restructuring with regional refinement. A partial reverse is applied, followed by a local search within a promising sub-region.",
  "targeted_regions": ["Regions not visited frequently by the population", "Potentially high-reward clusters, identified by landscape analysis (unspecified)"],
  "avoided_regions": ["Difficult regions identified by landscape analysis (unspecified)"],
  "knowledge_application": "None. No knowledge retrieved.",
  "diversity_contribution": "High. The large-scale restructuring and regional refinement will significantly alter the path structure, promoting exploration of unexplored regions.",
  "expected_benefits": "Improved path quality by escaping local optima and discovering new solution structures. Increased population diversity, leading to faster convergence towards the global optimum."
}
```

**Explanation of Exploration Strategy and Expected Effects:**

Given the high diversity weight (0.7) and a moderate risk tolerance (0.6), the primary focus is on generating a path that differs significantly from the current one, while still maintaining some level of path quality. The absence of retrieved knowledge necessitates a more general exploration strategy.

1.  **Step 1: Analyze Current Path:** The current path appears to have a degree of smoothness, likely due to the evolutionary algorithm's optimization efforts. There's a danger of getting trapped in local optima. The path's overall structure needs to be significantly altered to explore new areas.

2.  **Step 2: Determine Exploration Strategy:** The chosen strategy combines elements of both exploration and exploitation. Large-scale restructuring ensures sufficient diversity, while regional refinement improves potential path quality.

3.  **Step 3: Select Target Regions:** The "targeted regions" are not specified, but they would typically involve regions that are underexplored by the current population or identified as promising by landscape analysis (if landscape information was available). Difficult regions should be avoided. The specifics of the regions would depend on the landscape analysis' output (e.g., clustered nodes, high-density areas, specific structures identified during the problem analysis phase) that are not supplied.

4.  **Step 4: Apply Historical Knowledge:** No historical knowledge is available, so this step is skipped.

5.  **Step 5: Generate New Path:**

    *   **Large-Scale Restructuring (Partial Reverse):** A large portion of the path is reversed to introduce significant changes in the route structure. This disrupts established order and encourages exploration of new areas. The exact segment reversed is chosen strategically, taking into account the potential impact on the overall path.
    *   **Regional Refinement (Local Search):** A small local optimization method is performed on a section of the path to improve quality after restructuring. This helps to retain some level of efficiency.

The expected benefits include a jump out of potentially local optimal solutions, creating more diversity within the population, therefore accelerating convergence toward the true optimum.

2025-06-08 18:54:06,404 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 18:54:06,405 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 18:54:06,405 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 18:54:06,405 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99133.0, 路径: [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38]
2025-06-08 18:54:06,405 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_path': [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38], 'modification_strategy': 'Large-scale restructuring with regional refinement. A partial reverse is applied, followed by a local search within a promising sub-region.', 'targeted_regions': ['Regions not visited frequently by the population', 'Potentially high-reward clusters, identified by landscape analysis (unspecified)'], 'avoided_regions': ['Difficult regions identified by landscape analysis (unspecified)'], 'knowledge_application': 'None. No knowledge retrieved.', 'diversity_contribution': 'High. The large-scale restructuring and regional refinement will significantly alter the path structure, promoting exploration of unexplored regions.', 'expected_benefits': 'Improved path quality by escaping local optima and discovering new solution structures. Increased population diversity, leading to faster convergence towards the global optimum.', 'new_tour': [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38], 'cur_cost': 99133.0}
2025-06-08 18:54:06,406 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-08 18:54:06,406 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:54:06,406 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:54:06,406 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 94321.0
2025-06-08 18:54:06,429 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:06,434 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:54:06,451 - root - INFO - 拓扑感知扰动用时: 0.0017秒，使用策略: pattern_based
2025-06-08 18:54:06,453 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:06,456 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:06,460 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:06,503 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,517 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,524 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:06,528 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 18:54:06,546 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:06,550 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,568 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:06,570 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,572 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:06,587 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,593 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,595 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,597 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:06,617 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 18:54:06,630 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,642 - root - INFO - 拓扑感知扰动用时: 0.0025秒，使用策略: pattern_based
2025-06-08 18:54:06,647 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: cluster_based
2025-06-08 18:54:06,650 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,661 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:06,666 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,669 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:06,672 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,677 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:54:06,693 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: segment_preservation
2025-06-08 18:54:06,694 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:06,696 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,700 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,703 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:06,706 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:06,708 - root - INFO - 拓扑感知扰动用时: 0.0014秒，使用策略: pattern_based
2025-06-08 18:54:06,711 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,713 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:54:06,715 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: pattern_based
2025-06-08 18:54:06,720 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:54:06,724 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: segment_preservation
2025-06-08 18:54:06,726 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:06,727 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:06,734 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,739 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,740 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,741 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,745 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,748 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 18:54:06,750 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:06,754 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:54:06,754 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,757 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 18:54:06,760 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: critical_edge
2025-06-08 18:54:06,762 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,766 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: segment_preservation
2025-06-08 18:54:06,767 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,770 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,774 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:06,775 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,779 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,781 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:06,782 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,784 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,788 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,789 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,790 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,791 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,792 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,794 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,797 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: pattern_based
2025-06-08 18:54:06,798 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:06,805 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:54:06,808 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,813 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,815 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,817 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:06,821 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,825 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: critical_edge
2025-06-08 18:54:06,827 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,829 - root - INFO - 拓扑感知扰动用时: 0.0007秒，使用策略: pattern_based
2025-06-08 18:54:06,836 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:06,841 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,849 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:06,858 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:54:06,860 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:06,866 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,868 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,873 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,876 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:06,883 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,885 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:06,889 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,892 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,943 - ExploitationExpert - INFO - res_population_num: 32
2025-06-08 18:54:06,943 - ExploitationExpert - INFO - res_population_costs: [9912, 9626, 9626, 9626, 9626, 9621, 9621, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-08 18:54:06,944 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 13, 12, 23, 16, 20, 22, 17, 15,
       19, 14, 21, 18, 59, 48, 53, 54, 58, 49, 56, 55, 57, 52, 50, 51, 44,
       43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 59, 51, 50, 58, 49, 56, 55, 57,
       52, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-06-08 18:54:06,961 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:54:06,963 - ExploitationExpert - INFO - populations: [{'tour': array([53, 20, 29, 16, 57,  1, 44, 59, 17, 54, 24, 27,  5, 48, 37, 40, 55,
       11, 10, 22, 51, 46, 56, 36, 41, 39, 49,  9,  4, 34, 25, 14, 47, 43,
       42, 32, 35, 21, 31,  8, 50, 45, 28, 52,  7, 30, 38, 12, 58, 18, 13,
       15, 23, 33,  6, 26,  0, 19,  3,  2]), 'cur_cost': 86151.0}, {'tour': array([ 8, 42, 27, 39, 28,  4, 18, 43, 57, 25, 26, 46, 33, 22, 32, 37, 14,
       51, 23, 29, 35, 56, 52, 12, 10, 59, 11, 40,  3, 13, 34, 50, 38, 54,
        6, 21, 24, 17, 47,  7, 15, 16,  5, 20, 41, 19,  0, 55, 31, 53,  2,
       58,  1, 49, 36, 48,  9, 44, 45, 30]), 'cur_cost': 112741.0}, {'tour': array([53, 54,  5, 43, 42, 49,  3, 30, 40,  2,  0, 34, 52, 23, 21, 57, 28,
       27, 41, 33, 17, 18, 46, 35, 55, 32, 26, 11, 13,  9, 47, 22, 31, 29,
       12, 37, 24,  1, 50, 58, 39, 48, 14,  7, 44,  6,  4, 20, 19, 51, 10,
       25, 59, 56, 36, 16, 45, 15, 38,  8]), 'cur_cost': 97217.0}, {'tour': [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19], 'cur_cost': 103309.0}, {'tour': [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50], 'cur_cost': 102894.0}, {'tour': [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38], 'cur_cost': 99133.0}, {'tour': array([12, 45, 19,  0,  3,  8, 32,  5, 35, 57,  9, 58,  2, 44, 30, 33, 42,
       10,  7, 17, 11, 29, 53,  6, 55, 56, 24, 27, 48, 50, 26, 59, 34, 39,
       51, 22, 14, 49, 37, 40, 28,  4,  1, 52, 47, 20, 25, 15, 38, 43, 41,
       18, 36, 46, 13, 54, 23, 31, 21, 16]), 'cur_cost': 94321.0}, {'tour': [40, 16, 11, 15, 45, 5, 6, 41, 46, 14, 42, 48, 37, 47, 38, 7, 56, 13, 19, 50, 33, 10, 25, 34, 32, 59, 27, 4, 36, 20, 1, 29, 31, 54, 8, 12, 9, 35, 2, 18, 57, 26, 24, 55, 44, 21, 53, 23, 17, 49, 0, 51, 52, 22, 28, 39, 3, 58, 30, 43], 'cur_cost': 91846.0}, {'tour': [2, 24, 16, 20, 7, 12, 17, 29, 6, 32, 5, 56, 22, 35, 53, 26, 37, 18, 55, 38, 34, 40, 36, 8, 9, 50, 19, 39, 1, 48, 14, 0, 46, 21, 31, 49, 54, 59, 42, 3, 10, 25, 44, 13, 30, 15, 52, 47, 51, 11, 4, 28, 41, 27, 57, 45, 33, 43, 58, 23], 'cur_cost': 103204.0}, {'tour': [49, 43, 28, 29, 24, 54, 44, 51, 13, 48, 46, 31, 12, 21, 38, 15, 39, 25, 7, 45, 41, 40, 14, 58, 59, 42, 1, 6, 10, 33, 8, 19, 52, 55, 5, 20, 56, 11, 32, 34, 18, 2, 23, 26, 53, 37, 17, 35, 47, 57, 9, 36, 0, 16, 30, 50, 22, 3, 4, 27], 'cur_cost': 98912.0}]
2025-06-08 18:54:06,966 - ExploitationExpert - INFO - 局部搜索耗时: 0.56秒
2025-06-08 18:54:06,966 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-08 18:54:06,967 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-08 18:54:06,967 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-08 18:54:06,967 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:54:06,967 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:54:06,967 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 91621.0
2025-06-08 18:54:06,996 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:06,998 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,002 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,011 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,015 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,033 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,057 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,078 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,092 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,111 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,150 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,154 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,163 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,170 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,174 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,176 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,179 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,191 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:54:07,192 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,193 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:07,217 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:07,221 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,226 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,237 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,246 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,256 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,263 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,271 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,276 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,280 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,297 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,304 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,306 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,309 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,311 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,312 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,316 - root - INFO - 拓扑感知扰动用时: 0.0018秒，使用策略: segment_preservation
2025-06-08 18:54:07,317 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,320 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,322 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,326 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,329 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,341 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,345 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,346 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,349 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,351 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:54:07,352 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,360 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,361 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,372 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,374 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,376 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,378 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,381 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,389 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:07,391 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,394 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,399 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 18:54:07,402 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,404 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,408 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,411 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,413 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:07,415 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,418 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,420 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,421 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,424 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:07,426 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:54:07,429 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 18:54:07,429 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,433 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:54:07,434 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,436 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,440 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,443 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 18:54:07,444 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,447 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: cluster_based
2025-06-08 18:54:07,448 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,451 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,452 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,454 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,458 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 18:54:07,462 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:07,466 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,467 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,471 - ExploitationExpert - INFO - res_population_num: 38
2025-06-08 18:54:07,471 - ExploitationExpert - INFO - res_population_costs: [9912, 9626, 9626, 9626, 9626, 9621, 9621, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-08 18:54:07,471 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 13, 12, 23, 16, 20, 22, 17, 15,
       19, 14, 21, 18, 59, 48, 53, 54, 58, 49, 56, 55, 57, 52, 50, 51, 44,
       43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 59, 51, 50, 58, 49, 56, 55, 57,
       52, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-06-08 18:54:07,488 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:54:07,489 - ExploitationExpert - INFO - populations: [{'tour': array([53, 20, 29, 16, 57,  1, 44, 59, 17, 54, 24, 27,  5, 48, 37, 40, 55,
       11, 10, 22, 51, 46, 56, 36, 41, 39, 49,  9,  4, 34, 25, 14, 47, 43,
       42, 32, 35, 21, 31,  8, 50, 45, 28, 52,  7, 30, 38, 12, 58, 18, 13,
       15, 23, 33,  6, 26,  0, 19,  3,  2]), 'cur_cost': 86151.0}, {'tour': array([ 8, 42, 27, 39, 28,  4, 18, 43, 57, 25, 26, 46, 33, 22, 32, 37, 14,
       51, 23, 29, 35, 56, 52, 12, 10, 59, 11, 40,  3, 13, 34, 50, 38, 54,
        6, 21, 24, 17, 47,  7, 15, 16,  5, 20, 41, 19,  0, 55, 31, 53,  2,
       58,  1, 49, 36, 48,  9, 44, 45, 30]), 'cur_cost': 112741.0}, {'tour': array([53, 54,  5, 43, 42, 49,  3, 30, 40,  2,  0, 34, 52, 23, 21, 57, 28,
       27, 41, 33, 17, 18, 46, 35, 55, 32, 26, 11, 13,  9, 47, 22, 31, 29,
       12, 37, 24,  1, 50, 58, 39, 48, 14,  7, 44,  6,  4, 20, 19, 51, 10,
       25, 59, 56, 36, 16, 45, 15, 38,  8]), 'cur_cost': 97217.0}, {'tour': [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19], 'cur_cost': 103309.0}, {'tour': [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50], 'cur_cost': 102894.0}, {'tour': [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38], 'cur_cost': 99133.0}, {'tour': array([12, 45, 19,  0,  3,  8, 32,  5, 35, 57,  9, 58,  2, 44, 30, 33, 42,
       10,  7, 17, 11, 29, 53,  6, 55, 56, 24, 27, 48, 50, 26, 59, 34, 39,
       51, 22, 14, 49, 37, 40, 28,  4,  1, 52, 47, 20, 25, 15, 38, 43, 41,
       18, 36, 46, 13, 54, 23, 31, 21, 16]), 'cur_cost': 94321.0}, {'tour': array([21, 13, 56, 47, 39,  6, 58, 19, 22,  2,  4,  9,  5, 29, 17, 18, 23,
       45, 38, 36, 48, 59, 16,  8, 10, 28, 30,  7, 57,  0, 40, 12, 25,  3,
       44, 15, 51, 41, 24, 54, 32, 53,  1, 14, 55, 27, 37, 11, 46, 33, 52,
       26, 49, 34, 43, 20, 31, 35, 42, 50]), 'cur_cost': 91621.0}, {'tour': [2, 24, 16, 20, 7, 12, 17, 29, 6, 32, 5, 56, 22, 35, 53, 26, 37, 18, 55, 38, 34, 40, 36, 8, 9, 50, 19, 39, 1, 48, 14, 0, 46, 21, 31, 49, 54, 59, 42, 3, 10, 25, 44, 13, 30, 15, 52, 47, 51, 11, 4, 28, 41, 27, 57, 45, 33, 43, 58, 23], 'cur_cost': 103204.0}, {'tour': [49, 43, 28, 29, 24, 54, 44, 51, 13, 48, 46, 31, 12, 21, 38, 15, 39, 25, 7, 45, 41, 40, 14, 58, 59, 42, 1, 6, 10, 33, 8, 19, 52, 55, 5, 20, 56, 11, 32, 34, 18, 2, 23, 26, 53, 37, 17, 35, 47, 57, 9, 36, 0, 16, 30, 50, 22, 3, 4, 27], 'cur_cost': 98912.0}]
2025-06-08 18:54:07,491 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-08 18:54:07,491 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-08 18:54:07,491 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-08 18:54:07,492 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-08 18:54:07,492 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:54:07,493 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:54:07,493 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 97729.0
2025-06-08 18:54:07,499 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,502 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 18:54:07,507 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,519 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,528 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,532 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,562 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,575 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: pattern_based
2025-06-08 18:54:07,584 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,586 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,626 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:07,640 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,653 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,658 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,670 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,687 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,735 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,739 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,741 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,746 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,779 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,794 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,798 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,802 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,807 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:07,821 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,823 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,830 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,833 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,835 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,837 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,837 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,844 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,846 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,848 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:54:07,849 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,850 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,852 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,853 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 18:54:07,855 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,859 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:54:07,860 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,871 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,873 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,875 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,878 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,882 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:07,885 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,890 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:07,895 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,896 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,897 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,899 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:07,907 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,910 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 18:54:07,911 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,914 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,917 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,918 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,919 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,922 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,923 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:07,924 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,929 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,931 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,936 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,944 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,945 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,947 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:07,952 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:07,956 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:07,958 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:07,959 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,962 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,967 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 18:54:07,970 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 18:54:07,975 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:07,978 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,982 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,985 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,989 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:07,993 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 18:54:07,994 - ExploitationExpert - INFO - res_population_num: 40
2025-06-08 18:54:07,995 - ExploitationExpert - INFO - res_population_costs: [9912, 9626, 9626, 9626, 9626, 9621, 9621, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-08 18:54:07,995 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 13, 12, 23, 16, 20, 22, 17, 15,
       19, 14, 21, 18, 59, 48, 53, 54, 58, 49, 56, 55, 57, 52, 50, 51, 44,
       43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 59, 51, 50, 58, 49, 56, 55, 57,
       52, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64)]
2025-06-08 18:54:08,010 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:54:08,010 - ExploitationExpert - INFO - populations: [{'tour': array([53, 20, 29, 16, 57,  1, 44, 59, 17, 54, 24, 27,  5, 48, 37, 40, 55,
       11, 10, 22, 51, 46, 56, 36, 41, 39, 49,  9,  4, 34, 25, 14, 47, 43,
       42, 32, 35, 21, 31,  8, 50, 45, 28, 52,  7, 30, 38, 12, 58, 18, 13,
       15, 23, 33,  6, 26,  0, 19,  3,  2]), 'cur_cost': 86151.0}, {'tour': array([ 8, 42, 27, 39, 28,  4, 18, 43, 57, 25, 26, 46, 33, 22, 32, 37, 14,
       51, 23, 29, 35, 56, 52, 12, 10, 59, 11, 40,  3, 13, 34, 50, 38, 54,
        6, 21, 24, 17, 47,  7, 15, 16,  5, 20, 41, 19,  0, 55, 31, 53,  2,
       58,  1, 49, 36, 48,  9, 44, 45, 30]), 'cur_cost': 112741.0}, {'tour': array([53, 54,  5, 43, 42, 49,  3, 30, 40,  2,  0, 34, 52, 23, 21, 57, 28,
       27, 41, 33, 17, 18, 46, 35, 55, 32, 26, 11, 13,  9, 47, 22, 31, 29,
       12, 37, 24,  1, 50, 58, 39, 48, 14,  7, 44,  6,  4, 20, 19, 51, 10,
       25, 59, 56, 36, 16, 45, 15, 38,  8]), 'cur_cost': 97217.0}, {'tour': [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19], 'cur_cost': 103309.0}, {'tour': [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50], 'cur_cost': 102894.0}, {'tour': [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38], 'cur_cost': 99133.0}, {'tour': array([12, 45, 19,  0,  3,  8, 32,  5, 35, 57,  9, 58,  2, 44, 30, 33, 42,
       10,  7, 17, 11, 29, 53,  6, 55, 56, 24, 27, 48, 50, 26, 59, 34, 39,
       51, 22, 14, 49, 37, 40, 28,  4,  1, 52, 47, 20, 25, 15, 38, 43, 41,
       18, 36, 46, 13, 54, 23, 31, 21, 16]), 'cur_cost': 94321.0}, {'tour': array([21, 13, 56, 47, 39,  6, 58, 19, 22,  2,  4,  9,  5, 29, 17, 18, 23,
       45, 38, 36, 48, 59, 16,  8, 10, 28, 30,  7, 57,  0, 40, 12, 25,  3,
       44, 15, 51, 41, 24, 54, 32, 53,  1, 14, 55, 27, 37, 11, 46, 33, 52,
       26, 49, 34, 43, 20, 31, 35, 42, 50]), 'cur_cost': 91621.0}, {'tour': array([23, 46, 22, 28, 25, 33, 56, 53, 12, 40, 10, 44,  8, 50, 21, 20, 18,
       51, 43,  6,  1, 11, 35, 57,  2,  4, 29, 54, 19, 59, 31,  5, 13, 27,
        7, 42, 26, 36, 16, 48, 58,  0, 52, 47, 41, 14, 37,  9, 32, 39, 55,
       34, 30,  3, 24, 15, 45, 17, 38, 49]), 'cur_cost': 97729.0}, {'tour': [49, 43, 28, 29, 24, 54, 44, 51, 13, 48, 46, 31, 12, 21, 38, 15, 39, 25, 7, 45, 41, 40, 14, 58, 59, 42, 1, 6, 10, 33, 8, 19, 52, 55, 5, 20, 56, 11, 32, 34, 18, 2, 23, 26, 53, 37, 17, 35, 47, 57, 9, 36, 0, 16, 30, 50, 22, 3, 4, 27], 'cur_cost': 98912.0}]
2025-06-08 18:54:08,016 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-08 18:54:08,016 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-08 18:54:08,017 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-08 18:54:08,017 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-08 18:54:08,017 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:54:08,017 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:54:08,017 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110749.0
2025-06-08 18:54:08,022 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:08,027 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: segment_preservation
2025-06-08 18:54:08,030 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,085 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,099 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,114 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,133 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,140 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:08,154 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 18:54:08,186 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:08,191 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:08,213 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:54:08,235 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: pattern_based
2025-06-08 18:54:08,256 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,259 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:54:08,270 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,278 - root - INFO - 拓扑感知扰动用时: 0.0025秒，使用策略: pattern_based
2025-06-08 18:54:08,305 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,311 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:08,315 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:08,330 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,348 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:08,362 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:54:08,378 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:54:08,388 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,440 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,478 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:08,480 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:08,489 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:08,491 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:08,492 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,503 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,505 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,507 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,514 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:08,515 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:08,518 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:08,520 - ExploitationExpert - INFO - res_population_num: 41
2025-06-08 18:54:08,520 - ExploitationExpert - INFO - res_population_costs: [9912, 9626, 9626, 9626, 9626, 9621, 9621, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-08 18:54:08,520 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 13, 12, 23, 16, 20, 22, 17, 15,
       19, 14, 21, 18, 59, 48, 53, 54, 58, 49, 56, 55, 57, 52, 50, 51, 44,
       43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 59, 51, 50, 58, 49, 56, 55, 57,
       52, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64)]
2025-06-08 18:54:08,541 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:54:08,543 - ExploitationExpert - INFO - populations: [{'tour': array([53, 20, 29, 16, 57,  1, 44, 59, 17, 54, 24, 27,  5, 48, 37, 40, 55,
       11, 10, 22, 51, 46, 56, 36, 41, 39, 49,  9,  4, 34, 25, 14, 47, 43,
       42, 32, 35, 21, 31,  8, 50, 45, 28, 52,  7, 30, 38, 12, 58, 18, 13,
       15, 23, 33,  6, 26,  0, 19,  3,  2]), 'cur_cost': 86151.0}, {'tour': array([ 8, 42, 27, 39, 28,  4, 18, 43, 57, 25, 26, 46, 33, 22, 32, 37, 14,
       51, 23, 29, 35, 56, 52, 12, 10, 59, 11, 40,  3, 13, 34, 50, 38, 54,
        6, 21, 24, 17, 47,  7, 15, 16,  5, 20, 41, 19,  0, 55, 31, 53,  2,
       58,  1, 49, 36, 48,  9, 44, 45, 30]), 'cur_cost': 112741.0}, {'tour': array([53, 54,  5, 43, 42, 49,  3, 30, 40,  2,  0, 34, 52, 23, 21, 57, 28,
       27, 41, 33, 17, 18, 46, 35, 55, 32, 26, 11, 13,  9, 47, 22, 31, 29,
       12, 37, 24,  1, 50, 58, 39, 48, 14,  7, 44,  6,  4, 20, 19, 51, 10,
       25, 59, 56, 36, 16, 45, 15, 38,  8]), 'cur_cost': 97217.0}, {'tour': [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19], 'cur_cost': 103309.0}, {'tour': [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50], 'cur_cost': 102894.0}, {'tour': [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38], 'cur_cost': 99133.0}, {'tour': array([12, 45, 19,  0,  3,  8, 32,  5, 35, 57,  9, 58,  2, 44, 30, 33, 42,
       10,  7, 17, 11, 29, 53,  6, 55, 56, 24, 27, 48, 50, 26, 59, 34, 39,
       51, 22, 14, 49, 37, 40, 28,  4,  1, 52, 47, 20, 25, 15, 38, 43, 41,
       18, 36, 46, 13, 54, 23, 31, 21, 16]), 'cur_cost': 94321.0}, {'tour': array([21, 13, 56, 47, 39,  6, 58, 19, 22,  2,  4,  9,  5, 29, 17, 18, 23,
       45, 38, 36, 48, 59, 16,  8, 10, 28, 30,  7, 57,  0, 40, 12, 25,  3,
       44, 15, 51, 41, 24, 54, 32, 53,  1, 14, 55, 27, 37, 11, 46, 33, 52,
       26, 49, 34, 43, 20, 31, 35, 42, 50]), 'cur_cost': 91621.0}, {'tour': array([23, 46, 22, 28, 25, 33, 56, 53, 12, 40, 10, 44,  8, 50, 21, 20, 18,
       51, 43,  6,  1, 11, 35, 57,  2,  4, 29, 54, 19, 59, 31,  5, 13, 27,
        7, 42, 26, 36, 16, 48, 58,  0, 52, 47, 41, 14, 37,  9, 32, 39, 55,
       34, 30,  3, 24, 15, 45, 17, 38, 49]), 'cur_cost': 97729.0}, {'tour': array([12, 11,  9, 28,  5, 36, 13, 39, 38, 49,  1, 10, 52, 17, 46, 57, 34,
       58,  3, 43, 37, 27, 54, 35, 26, 16, 45, 15,  4,  6, 32,  7, 21, 25,
       14, 31,  0, 33, 55, 50, 24, 22, 29, 48, 53, 23, 41, 18, 59, 51,  2,
       44, 20, 42,  8, 30, 40, 19, 56, 47]), 'cur_cost': 110749.0}]
2025-06-08 18:54:08,546 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-08 18:54:08,546 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-08 18:54:08,546 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-08 18:54:08,547 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 3, 'strategy': 'explore', 'path_data': {'new_path': [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19], 'modification_strategy': 'Large-scale restructuring with regional refinement.', 'targeted_regions': ['High-diversity regions', 'Potentially high-reward regions based on reverse order exploration'], 'avoided_regions': ['Regions exhibiting high path costs based on the landscape analysis (Implicit, as no specific regions are provided).'], 'knowledge_application': 'Reverse the path partially to explore alternative sequence', 'diversity_contribution': 'High. This strategy aims for significant structural changes, promoting exploration of diverse solution areas.', 'expected_benefits': 'Increased population diversity, potential discovery of new solution structures, and improved exploration of the search space. This should provide a significant different path while not necessarily guaranteeing a lower path cost at this stage.', 'new_tour': [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19], 'cur_cost': 103309.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_path': [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50], 'modification_strategy': 'Large-scale restructuring with regional recombination.', 'targeted_regions': ['Region 1 (nodes 0-10)', 'Region 2 (nodes 10-20)', 'Region 3 (nodes 20-30)'], 'avoided_regions': [], 'knowledge_application': 'No historical knowledge to apply in this turn, so a more diverse approach is used.', 'diversity_contribution': 'High. The new path is significantly different from the current path, increasing diversity significantly.', 'expected_benefits': 'Increased population diversity, potential discovery of new high-quality solutions by exploring previously unvisited regions of the search space.', 'new_tour': [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50], 'cur_cost': 102894.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_path': [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38], 'modification_strategy': 'Large-scale restructuring with regional refinement. A partial reverse is applied, followed by a local search within a promising sub-region.', 'targeted_regions': ['Regions not visited frequently by the population', 'Potentially high-reward clusters, identified by landscape analysis (unspecified)'], 'avoided_regions': ['Difficult regions identified by landscape analysis (unspecified)'], 'knowledge_application': 'None. No knowledge retrieved.', 'diversity_contribution': 'High. The large-scale restructuring and regional refinement will significantly alter the path structure, promoting exploration of unexplored regions.', 'expected_benefits': 'Improved path quality by escaping local optima and discovering new solution structures. Increased population diversity, leading to faster convergence towards the global optimum.', 'new_tour': [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38], 'cur_cost': 99133.0}}]
2025-06-08 18:54:08,547 - __main__ - INFO - 进化阶段完成
2025-06-08 18:54:08,548 - __main__ - INFO - 开始评估阶段
2025-06-08 18:54:08,548 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-08 18:54:08,586 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
# Evolution Assessment Task

## Role Definition
You are an expert in evolutionary algorithms and performance assessment. You excel at analyzing the evolutionary state of algorithms, evaluating strategy effectiveness, optimizing the balance between exploration and exploitation, and providing specific improvement recommendations.

## Iteration Information
- Current Iteration: 0
- Total Iterations: 5

## Population Changes
- Old Population Statistics: {"min_cost": 9998.0, "max_cost": 105388.0, "mean_cost": 72249.3, "diversity": 0.9296296296296297}
- New Population Statistics: {"min_cost": 86151.0, "max_cost": 112741.0, "mean_cost": 99586.5, "diversity": 0.9781418706842436}
- Elite Solution Changes: {"old_best_cost": 9998.0, "new_best_cost": 9614, "improvement": 384.0, "old_elite_count": 3, "new_elite_count": 41, "old_elite_diversity": 0.3222222222222223, "new_elite_diversity": 0.0444444444444444, "new_elite_solutions_count": 0}

## Strategy Allocation
- Global Exploration Ratio: 0.3
- Individual Strategy Assignments: {"0": "exploit", "1": "exploit", "2": "exploit", "3": "explore", "4": "explore", "5": "explore", "6": "exploit", "7": "exploit", "8": "exploit", "9": "exploit"}
- Strategy Parameters: {"explore": {"diversity_weight": 0.7, "knowledge_usage_weight": 0.3, "risk_tolerance": 0.6}, "exploit": {"local_search_depth": 2, "quality_edge_usage": 0.8, "elite_influence": 0.7}}

## Strategy Results
- Exploration Strategy Results: {"success_count": 2, "improvement_sum": 524.0}
- Exploitation Strategy Results: {"success_count": 2, "improvement_sum": -273896.0}

## Historical Trends
无历史趋势数据

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. The above data shows the population changes, strategy allocation, and strategy results in the current iteration. We need you to assess the evolutionary effects of the current iteration, analyze the evolutionary stage, evaluate strategy effectiveness, and provide improvement recommendations for the next iteration.

# Assessment Request
Please conduct a comprehensive assessment of the evolutionary effects in the current iteration. Please follow these steps in your thinking:

## Step 1: Evolution Stage Identification and Analysis
- Analyze the current population's diversity level, convergence degree, and elite solution stability
- Evaluate the improvement rate and effectiveness of various strategies
- Determine the current evolutionary stage of the algorithm (early exploration, middle transition, late fine-tuning)
- Assess the confidence level of the stage determination
- Detect if approaching a stage transition point and estimate the transition time
- Provide recommendations for preparing for stage transitions

## Step 2: Strategy Effectiveness Evaluation
- Analyze the effectiveness of exploration strategies:
  - Calculate success rate and average improvement magnitude
  - Evaluate contribution to diversity
  - Evaluate new region discovery capability
  - Evaluate ability to escape local optima
  - Analyze individual-level effectiveness
- Analyze the effectiveness of exploitation strategies:
  - Calculate success rate and average improvement magnitude
  - Evaluate contribution to convergence
  - Evaluate elite solution improvement capability
  - Evaluate local fine-tuning capability
  - Analyze individual-level effectiveness
- Evaluate overall strategy effectiveness:
  - Analyze strategy synergy effects
  - Evaluate balance effectiveness
  - Calculate iteration efficiency

## Step 3: Exploration vs. Exploitation Balance Analysis
- Calculate the current exploration/exploitation ratio
- Based on the evolutionary stage and strategy effectiveness, predict the optimal ratio
- Calculate the gap between the current ratio and the optimal ratio
- Evaluate the current balance state
- Determine if adjustment is needed, and the direction and magnitude of adjustment
- Analyze the trend of balance state changes

## Step 4: Improvement Recommendation Generation
- Recommend exploration ratio for the next iteration
- Propose strategy parameter adjustment recommendations
- Identify areas that need special attention
- Provide strategy recommendations for individuals

## Step 5: Long-term Trend Analysis
- Analyze convergence trends
- Analyze diversity trends
- Analyze efficiency trends
- Identify potential problems
- Discover optimization opportunities

Please return your assessment results in JSON format with the following sections:
```json
{
  "evolution_stage": {
    "stage": string,
    "confidence": float,
    "stage_features": {
      "diversity_level": float,
      "convergence_level": float,
      "elite_stability": float,
      "improvement_rate": float,
      "exploration_effectiveness": float,
      "exploitation_effectiveness": float
    },
    "transition_indicators": {
      "approaching_next_stage": bool,
      "estimated_iterations_to_transition": int,
      "recommended_preparation": string
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": float,
      "avg_improvement": float,
      "diversity_contribution": float,
      "new_region_discovery": float,
      "escape_local_optima": float,
      "individual_effectiveness": {}
    },
    "exploit": {
      "success_rate": float,
      "avg_improvement": float,
      "convergence_contribution": float,
      "elite_improvement": float,
      "local_refinement": float,
      "individual_effectiveness": {}
    },
    "overall": {
      "strategy_synergy": float,
      "balance_effectiveness": float,
      "iteration_efficiency": float
    }
  },
  "balance_state": {
    "current_ratio": float,
    "optimal_ratio": float,
    "ratio_gap": float,
    "balance_score": float,
    "adjustment_needed": bool,
    "adjustment_magnitude": float,
    "adjustment_direction": string,
    "balance_trend": {
      "direction": string,
      "rate": float,
      "prediction": string
    }
  },
  "recommendations": {
    "explore_ratio": float,
    "strategy_parameters": {},
    "focus_areas": [string, ...],
    "individual_recommendations": {}
  },
  "long_term_analysis": {
    "convergence_trend": string,
    "diversity_trend": string,
    "efficiency_trend": string,
    "potential_issues": [string, ...],
    "optimization_opportunities": [string, ...]
  }
}
```

Additionally, please provide a brief summary explaining your main findings and their implications for the next iteration's strategy.

2025-06-08 18:54:08,586 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-08 18:54:18,029 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "middle transition",
    "confidence": 0.8,
    "stage_features": {
      "diversity_level": 0.978,
      "convergence_level": 0.001,
      "elite_stability": 0.000,
      "improvement_rate": 0.001,
      "exploration_effectiveness": 0.5,
      "exploitation_effectiveness": -10.0
    },
    "transition_indicators": {
      "approaching_next_stage": true,
      "estimated_iterations_to_transition": 1,
      "recommended_preparation": "Reduce global exploration, focus on local search refinement near promising regions. Prepare for potential convergence."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.6,
      "avg_improvement": 262.0,
      "diversity_contribution": 0.048,
      "new_region_discovery": 0.4,
      "escape_local_optima": 0.6,
      "individual_effectiveness": {
        "3": "Successfully explored, positive improvement.",
        "4": "Successfully explored, positive improvement.",
        "5": "Successfully explored, positive improvement."
      }
    },
    "exploit": {
      "success_rate": 0.2,
      "avg_improvement": -136948.0,
      "convergence_contribution": -10.0,
      "elite_improvement": 0.0,
      "local_refinement": 0.0,
      "individual_effectiveness": {
        "0": "Unsuccessful exploit, significant degradation.",
        "1": "Unsuccessful exploit, significant degradation.",
        "2": "Unsuccessful exploit, significant degradation.",
        "6": "Unsuccessful exploit, significant degradation.",
        "7": "Unsuccessful exploit, significant degradation.",
        "8": "Unsuccessful exploit, significant degradation.",
        "9": "Unsuccessful exploit, significant degradation."
      }
    },
    "overall": {
      "strategy_synergy": 0.2,
      "balance_effectiveness": 0.1,
      "iteration_efficiency": -0.2
    }
  },
  "balance_state": {
    "current_ratio": 0.3,
    "optimal_ratio": 0.1,
    "ratio_gap": 0.2,
    "balance_score": 0.3,
    "adjustment_needed": true,
    "adjustment_magnitude": 0.1,
    "adjustment_direction": "decrease",
    "balance_trend": {
      "direction": "unstable",
      "rate": -0.1,
      "prediction": "The balance is currently poor, leaning toward excessive exploration, resulting in limited overall improvement. Continue the trend towards decreasing exploration and increasing exploitation."
    }
  },
  "recommendations": {
    "explore_ratio": 0.2,
    "strategy_parameters": {
      "explore": {
        "diversity_weight": 0.7,
        "knowledge_usage_weight": 0.3,
        "risk_tolerance": 0.6
      },
      "exploit": {
        "local_search_depth": 3,
        "quality_edge_usage": 0.9,
        "elite_influence": 0.8
      }
    },
    "focus_areas": [
      "Improve exploitation strategy performance. Unsuccessful exploitation is greatly hindering progress.",
      "Evaluate the elite solutions and assess whether the elite solutions are truly elite.",
      "Monitor the elite's diversity to avoid over-specialization."
    ],
    "individual_recommendations": {
      "0": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "1": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "2": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "3": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
       "4": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
       "5": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
      "6": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "7": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "8": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "9": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation."
    }
  },
  "long_term_analysis": {
    "convergence_trend": "Negative. Current exploitation is significantly degrading solutions.",
    "diversity_trend": "High, but decreasing among elite solutions.",
    "efficiency_trend": "Negative. Driven by poor exploitation performance.",
    "potential_issues": [
      "Over-reliance on exploration may be hindering convergence. Exploitation strategies are underperforming significantly.",
      "Elite solutions need careful monitoring; the change in elite count, combined with degradation from exploitation, suggests either the elite is poorly selected or that the problem is not well defined. ",
      "The exploration-exploitation balance is currently skewed towards exploration."
    ],
    "optimization_opportunities": [
      "Refine exploitation strategies to improve convergence.",
      "Thoroughly check if the elite solutions are actually better than others",
      "Adjust exploration/exploitation balance to favor exploitation."
    ]
  }
}
```

**Summary:**

The evolutionary algorithm is currently in a middle transition phase, characterized by high diversity but poor convergence and a marked decline in solution quality. Exploitation strategies are failing dramatically, significantly degrading the population's fitness. While exploration shows some success in finding improved solutions, it's not enough to offset the harm from the exploitation side. The elite solutions are showing significant instability, suggesting potentially inappropriate elite selection or a fundamental issue with the problem definition within the given search landscape. For the next iteration, the algorithm should prioritize strengthening its exploitation strategy, carefully review the chosen elite solutions, and reduce the global exploration ratio to promote faster convergence.  The focus should be on using a more aggressive exploitation strategy and a more refined elite solution process.

2025-06-08 18:54:18,030 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-08 18:54:18,030 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "middle transition",
    "confidence": 0.8,
    "stage_features": {
      "diversity_level": 0.978,
      "convergence_level": 0.001,
      "elite_stability": 0.000,
      "improvement_rate": 0.001,
      "exploration_effectiveness": 0.5,
      "exploitation_effectiveness": -10.0
    },
    "transition_indicators": {
      "approaching_next_stage": true,
      "estimated_iterations_to_transition": 1,
      "recommended_preparation": "Reduce global exploration, focus on local search refinement near promising regions. Prepare for potential convergence."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.6,
      "avg_improvement": 262.0,
      "diversity_contribution": 0.048,
      "new_region_discovery": 0.4,
      "escape_local_optima": 0.6,
      "individual_effectiveness": {
        "3": "Successfully explored, positive improvement.",
        "4": "Successfully explored, positive improvement.",
        "5": "Successfully explored, positive improvement."
      }
    },
    "exploit": {
      "success_rate": 0.2,
      "avg_improvement": -136948.0,
      "convergence_contribution": -10.0,
      "elite_improvement": 0.0,
      "local_refinement": 0.0,
      "individual_effectiveness": {
        "0": "Unsuccessful exploit, significant degradation.",
        "1": "Unsuccessful exploit, significant degradation.",
        "2": "Unsuccessful exploit, significant degradation.",
        "6": "Unsuccessful exploit, significant degradation.",
        "7": "Unsuccessful exploit, significant degradation.",
        "8": "Unsuccessful exploit, significant degradation.",
        "9": "Unsuccessful exploit, significant degradation."
      }
    },
    "overall": {
      "strategy_synergy": 0.2,
      "balance_effectiveness": 0.1,
      "iteration_efficiency": -0.2
    }
  },
  "balance_state": {
    "current_ratio": 0.3,
    "optimal_ratio": 0.1,
    "ratio_gap": 0.2,
    "balance_score": 0.3,
    "adjustment_needed": true,
    "adjustment_magnitude": 0.1,
    "adjustment_direction": "decrease",
    "balance_trend": {
      "direction": "unstable",
      "rate": -0.1,
      "prediction": "The balance is currently poor, leaning toward excessive exploration, resulting in limited overall improvement. Continue the trend towards decreasing exploration and increasing exploitation."
    }
  },
  "recommendations": {
    "explore_ratio": 0.2,
    "strategy_parameters": {
      "explore": {
        "diversity_weight": 0.7,
        "knowledge_usage_weight": 0.3,
        "risk_tolerance": 0.6
      },
      "exploit": {
        "local_search_depth": 3,
        "quality_edge_usage": 0.9,
        "elite_influence": 0.8
      }
    },
    "focus_areas": [
      "Improve exploitation strategy performance. Unsuccessful exploitation is greatly hindering progress.",
      "Evaluate the elite solutions and assess whether the elite solutions are truly elite.",
      "Monitor the elite's diversity to avoid over-specialization."
    ],
    "individual_recommendations": {
      "0": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "1": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "2": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "3": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
       "4": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
       "5": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
      "6": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "7": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "8": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "9": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation."
    }
  },
  "long_term_analysis": {
    "convergence_trend": "Negative. Current exploitation is significantly degrading solutions.",
    "diversity_trend": "High, but decreasing among elite solutions.",
    "efficiency_trend": "Negative. Driven by poor exploitation performance.",
    "potential_issues": [
      "Over-reliance on exploration may be hindering convergence. Exploitation strategies are underperforming significantly.",
      "Elite solutions need careful monitoring; the change in elite count, combined with degradation from exploitation, suggests either the elite is poorly selected or that the problem is not well defined. ",
      "The exploration-exploitation balance is currently skewed towards exploration."
    ],
    "optimization_opportunities": [
      "Refine exploitation strategies to improve convergence.",
      "Thoroughly check if the elite solutions are actually better than others",
      "Adjust exploration/exploitation balance to favor exploitation."
    ]
  }
}
```

**Summary:**

The evolutionary algorithm is currently in a middle transition phase, characterized by high diversity but poor convergence and a marked decline in solution quality. Exploitation strategies are failing dramatically, significantly degrading the population's fitness. While exploration shows some success in finding improved solutions, it's not enough to offset the harm from the exploitation side. The elite solutions are showing significant instability, suggesting potentially inappropriate elite selection or a fundamental issue with the problem definition within the given search landscape. For the next iteration, the algorithm should prioritize strengthening its exploitation strategy, carefully review the chosen elite solutions, and reduce the global exploration ratio to promote faster convergence.  The focus should be on using a more aggressive exploitation strategy and a more refined elite solution process.

2025-06-08 18:54:18,031 - __main__ - INFO - 评估阶段完成
2025-06-08 18:54:18,031 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "middle transition",
    "confidence": 0.8,
    "stage_features": {
      "diversity_level": 0.978,
      "convergence_level": 0.001,
      "elite_stability": 0.000,
      "improvement_rate": 0.001,
      "exploration_effectiveness": 0.5,
      "exploitation_effectiveness": -10.0
    },
    "transition_indicators": {
      "approaching_next_stage": true,
      "estimated_iterations_to_transition": 1,
      "recommended_preparation": "Reduce global exploration, focus on local search refinement near promising regions. Prepare for potential convergence."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.6,
      "avg_improvement": 262.0,
      "diversity_contribution": 0.048,
      "new_region_discovery": 0.4,
      "escape_local_optima": 0.6,
      "individual_effectiveness": {
        "3": "Successfully explored, positive improvement.",
        "4": "Successfully explored, positive improvement.",
        "5": "Successfully explored, positive improvement."
      }
    },
    "exploit": {
      "success_rate": 0.2,
      "avg_improvement": -136948.0,
      "convergence_contribution": -10.0,
      "elite_improvement": 0.0,
      "local_refinement": 0.0,
      "individual_effectiveness": {
        "0": "Unsuccessful exploit, significant degradation.",
        "1": "Unsuccessful exploit, significant degradation.",
        "2": "Unsuccessful exploit, significant degradation.",
        "6": "Unsuccessful exploit, significant degradation.",
        "7": "Unsuccessful exploit, significant degradation.",
        "8": "Unsuccessful exploit, significant degradation.",
        "9": "Unsuccessful exploit, significant degradation."
      }
    },
    "overall": {
      "strategy_synergy": 0.2,
      "balance_effectiveness": 0.1,
      "iteration_efficiency": -0.2
    }
  },
  "balance_state": {
    "current_ratio": 0.3,
    "optimal_ratio": 0.1,
    "ratio_gap": 0.2,
    "balance_score": 0.3,
    "adjustment_needed": true,
    "adjustment_magnitude": 0.1,
    "adjustment_direction": "decrease",
    "balance_trend": {
      "direction": "unstable",
      "rate": -0.1,
      "prediction": "The balance is currently poor, leaning toward excessive exploration, resulting in limited overall improvement. Continue the trend towards decreasing exploration and increasing exploitation."
    }
  },
  "recommendations": {
    "explore_ratio": 0.2,
    "strategy_parameters": {
      "explore": {
        "diversity_weight": 0.7,
        "knowledge_usage_weight": 0.3,
        "risk_tolerance": 0.6
      },
      "exploit": {
        "local_search_depth": 3,
        "quality_edge_usage": 0.9,
        "elite_influence": 0.8
      }
    },
    "focus_areas": [
      "Improve exploitation strategy performance. Unsuccessful exploitation is greatly hindering progress.",
      "Evaluate the elite solutions and assess whether the elite solutions are truly elite.",
      "Monitor the elite's diversity to avoid over-specialization."
    ],
    "individual_recommendations": {
      "0": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "1": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "2": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "3": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
       "4": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
       "5": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
      "6": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "7": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "8": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "9": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation."
    }
  },
  "long_term_analysis": {
    "convergence_trend": "Negative. Current exploitation is significantly degrading solutions.",
    "diversity_trend": "High, but decreasing among elite solutions.",
    "efficiency_trend": "Negative. Driven by poor exploitation performance.",
    "potential_issues": [
      "Over-reliance on exploration may be hindering convergence. Exploitation strategies are underperforming significantly.",
      "Elite solutions need careful monitoring; the change in elite count, combined with degradation from exploitation, suggests either the elite is poorly selected or that the problem is not well defined. ",
      "The exploration-exploitation balance is currently skewed towards exploration."
    ],
    "optimization_opportunities": [
      "Refine exploitation strategies to improve convergence.",
      "Thoroughly check if the elite solutions are actually better than others",
      "Adjust exploration/exploitation balance to favor exploitation."
    ]
  }
}
```

**Summary:**

The evolutionary algorithm is currently in a middle transition phase, characterized by high diversity but poor convergence and a marked decline in solution quality. Exploitation strategies are failing dramatically, significantly degrading the population's fitness. While exploration shows some success in finding improved solutions, it's not enough to offset the harm from the exploitation side. The elite solutions are showing significant instability, suggesting potentially inappropriate elite selection or a fundamental issue with the problem definition within the given search landscape. For the next iteration, the algorithm should prioritize strengthening its exploitation strategy, carefully review the chosen elite solutions, and reduce the global exploration ratio to promote faster convergence.  The focus should be on using a more aggressive exploitation strategy and a more refined elite solution process.

2025-06-08 18:54:18,031 - __main__ - INFO - 当前最佳适应度: 86151.0
2025-06-08 18:54:18,031 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0509_0bug-fa-feedback\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite12_60_route_0.pkl
2025-06-08 18:54:18,031 - __main__ - INFO - composite12_60 开始进化第 2 代
2025-06-08 18:54:18,031 - __main__ - INFO - 开始分析阶段
2025-06-08 18:54:18,031 - StatsExpert - INFO - 开始统计分析
2025-06-08 18:54:18,044 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 86151.0, 'max': 112741.0, 'mean': 99586.5, 'std': 7767.861214645895}, 'diversity': 0.9781418706842436, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-08 18:54:18,045 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 86151.0, 'max': 112741.0, 'mean': 99586.5, 'std': 7767.861214645895}, 'diversity_level': 0.9781418706842436, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-08 18:54:18,046 - PathExpert - INFO - 开始路径结构分析
2025-06-08 18:54:18,053 - PathExpert - INFO - 路径结构分析完成
2025-06-08 18:54:18,053 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [], 'low_frequency_edges': [{'edge': '(17, 54)', 'frequency': 0.2}, {'edge': '(24, 27)', 'frequency': 0.2}, {'edge': '(48, 37)', 'frequency': 0.2}, {'edge': '(37, 40)', 'frequency': 0.2}, {'edge': '(56, 36)', 'frequency': 0.2}, {'edge': '(25, 14)', 'frequency': 0.3}, {'edge': '(43, 42)', 'frequency': 0.3}, {'edge': '(8, 50)', 'frequency': 0.2}, {'edge': '(6, 26)', 'frequency': 0.2}, {'edge': '(28, 4)', 'frequency': 0.2}, {'edge': '(46, 33)', 'frequency': 0.2}, {'edge': '(19, 0)', 'frequency': 0.2}, {'edge': '(36, 48)', 'frequency': 0.2}, {'edge': '(30, 40)', 'frequency': 0.2}, {'edge': '(33, 17)', 'frequency': 0.2}, {'edge': '(17, 18)', 'frequency': 0.2}, {'edge': '(46, 35)', 'frequency': 0.2}, {'edge': '(24, 1)', 'frequency': 0.2}, {'edge': '(59, 56)', 'frequency': 0.2}, {'edge': '(36, 16)', 'frequency': 0.2}, {'edge': '(16, 45)', 'frequency': 0.2}, {'edge': '(45, 15)', 'frequency': 0.2}, {'edge': '(15, 38)', 'frequency': 0.2}, {'edge': '(49, 33)', 'frequency': 0.2}, {'edge': '(0, 3)', 'frequency': 0.2}, {'edge': '(41, 14)', 'frequency': 0.2}, {'edge': '(27, 54)', 'frequency': 0.2}, {'edge': '(21, 25)', 'frequency': 0.2}, {'edge': '(54, 35)', 'frequency': 0.2}, {'edge': '(58, 19)', 'frequency': 0.2}, {'edge': '(0, 40)', 'frequency': 0.3}, {'edge': '(59, 51)', 'frequency': 0.2}, {'edge': '(50, 24)', 'frequency': 0.2}, {'edge': '(10, 28)', 'frequency': 0.2}, {'edge': '(45, 17)', 'frequency': 0.2}, {'edge': '(35, 57)', 'frequency': 0.2}, {'edge': '(2, 44)', 'frequency': 0.2}, {'edge': '(52, 47)', 'frequency': 0.2}, {'edge': '(41, 18)', 'frequency': 0.2}, {'edge': '(56, 47)', 'frequency': 0.2}, {'edge': '(2, 4)', 'frequency': 0.2}, {'edge': '(50, 21)', 'frequency': 0.2}, {'edge': '(38, 49)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [36, 16, 45, 15, 38], 'cost': 12791.0, 'size': 5}, {'region': [24, 15, 45, 17, 38], 'cost': 12438.0, 'size': 5}, {'region': [47, 20, 25, 15, 38], 'cost': 11941.0, 'size': 5}, {'region': [58, 31, 16, 46, 18], 'cost': 11524.0, 'size': 5}, {'region': [50, 24, 22, 29, 48], 'cost': 10754.0, 'size': 5}]}
2025-06-08 18:54:18,053 - EliteExpert - INFO - 开始精英解分析
2025-06-08 18:54:18,075 - EliteExpert - INFO - 精英解分析完成
2025-06-08 18:54:18,078 - __main__ - INFO - 精英专家分析报告: {'elite_count': 41, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 76537.0, 'avg_gap': 89963.71951219512}, 'structure_gap': {'unique_elite_edges': 152, 'unique_pop_edges': 515, 'common_edges': 37}}, 'elite_diversity': {'diversity_score': 0.1746747967479675}}
2025-06-08 18:54:18,078 - LandscapeExpert - INFO - 开始景观分析
2025-06-08 18:54:18,078 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-08 18:54:18,078 - LandscapeExpert - INFO - 发送给LLM的提示词: 
# Landscape Analysis Task

## Role Definition
You are an expert in evolutionary algorithms and combinatorial optimization, specializing in search space analysis. You excel at analyzing population statistics, path structures, and elite solutions to provide insights into the search landscape.

## Current Population Statistics
- Population Size: 10
- Cost Statistics: Min=86151.0, Max=112741.0, Mean=99586.5, Std=7767.861214645895
- Diversity Level: 0.9781418706842436
- Convergence Level: 0.0
- Clustering Information: {"clusters": 10, "cluster_sizes": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}

## Path Structure Analysis
- High Quality Edges: []
- Common Subpaths: []
- Edge Frequency Distribution: {"high_frequency_edges": [], "medium_frequency_edges": [], "low_frequency_edges": [{"edge": "(17, 54)", "frequency": 0.2}, {"edge": "(24, 27)", "frequency": 0.2}, {"edge": "(48, 37)", "frequency": 0.2}, {"edge": "(37, 40)", "frequency": 0.2}, {"edge": "(56, 36)", "frequency": 0.2}, {"edge": "(25, 14)", "frequency": 0.3}, {"edge": "(43, 42)", "frequency": 0.3}, {"edge": "(8, 50)", "frequency": 0.2}, {"edge": "(6, 26)", "frequency": 0.2}, {"edge": "(28, 4)", "frequency": 0.2}, {"edge": "(46, 33)", "frequency": 0.2}, {"edge": "(19, 0)", "frequency": 0.2}, {"edge": "(36, 48)", "frequency": 0.2}, {"edge": "(30, 40)", "frequency": 0.2}, {"edge": "(33, 17)", "frequency": 0.2}, {"edge": "(17, 18)", "frequency": 0.2}, {"edge": "(46, 35)", "frequency": 0.2}, {"edge": "(24, 1)", "frequency": 0.2}, {"edge": "(59, 56)", "frequency": 0.2}, {"edge": "(36, 16)", "frequency": 0.2}, {"edge": "(16, 45)", "frequency": 0.2}, {"edge": "(45, 15)", "frequency": 0.2}, {"edge": "(15, 38)", "frequency": 0.2}, {"edge": "(49, 33)", "frequency": 0.2}, {"edge": "(0, 3)", "frequency": 0.2}, {"edge": "(41, 14)", "frequency": 0.2}, {"edge": "(27, 54)", "frequency": 0.2}, {"edge": "(21, 25)", "frequency": 0.2}, {"edge": "(54, 35)", "frequency": 0.2}, {"edge": "(58, 19)", "frequency": 0.2}, {"edge": "(0, 40)", "frequency": 0.3}, {"edge": "(59, 51)", "frequency": 0.2}, {"edge": "(50, 24)", "frequency": 0.2}, {"edge": "(10, 28)", "frequency": 0.2}, {"edge": "(45, 17)", "frequency": 0.2}, {"edge": "(35, 57)", "frequency": 0.2}, {"edge": "(2, 44)", "frequency": 0.2}, {"edge": "(52, 47)", "frequency": 0.2}, {"edge": "(41, 18)", "frequency": 0.2}, {"edge": "(56, 47)", "frequency": 0.2}, {"edge": "(2, 4)", "frequency": 0.2}, {"edge": "(50, 21)", "frequency": 0.2}, {"edge": "(38, 49)", "frequency": 0.2}]}
- Low Quality Regions: [{"region": [36, 16, 45, 15, 38], "cost": 12791.0, "size": 5}, {"region": [24, 15, 45, 17, 38], "cost": 12438.0, "size": 5}, {"region": [47, 20, 25, 15, 38], "cost": 11941.0, "size": 5}, {"region": [58, 31, 16, 46, 18], "cost": 11524.0, "size": 5}, {"region": [50, 24, 22, 29, 48], "cost": 10754.0, "size": 5}]

## Elite Solution Analysis
- Number of Elite Solutions: 41
- Common Features: {"common_edges": {}, "common_edge_ratio": 0.0}
- Fixed Nodes: [{"node": 0, "position": 0}]
- Population Gap: {"cost_gap": {"min_gap": 76537.0, "avg_gap": 89963.71951219512}, "structure_gap": {"unique_elite_edges": 152, "unique_pop_edges": 515, "common_edges": 37}}
- Elite Diversity: {"diversity_score": 0.1746747967479675}

## Historical Trends
Historical trend data

# Analysis Request
Based on the above information, please provide a comprehensive landscape analysis of the search space and current population state. Your analysis should include:

1. Overall search space characteristics
2. Current population state assessment
3. Identification of difficult regions and search challenges
4. Identification of opportunity regions with potential for improvement
5. Recommended evolution direction and strategy balance

2025-06-08 18:54:27,957 - LandscapeExpert - INFO - LLM返回的分析结果: Okay, here's a comprehensive landscape analysis based on the provided information, aiming to give insights into the search space characteristics, population state, challenges, opportunities, and recommendations.

**1. Overall Search Space Characteristics**

*   **Ruggedness:** The landscape appears to be quite rugged. The cost statistics show a relatively large range (Min=86151.0, Max=112741.0) and a high standard deviation (Std=7767.86). This indicates significant variability in solution quality, suggesting many local optima and potentially deceptive regions. The lack of convergence (Convergence Level: 0.0) further supports this.
*   **Multi-modality:** The clustering information ("clusters": 10, "cluster\_sizes": \[1, 1, 1, 1, 1, 1, 1, 1, 1, 1]) reveals that the population is highly diverse, and that each solution is currently an isolated cluster. This strongly indicates a multi-modal landscape with several distinct regions of potentially good solutions.
*   **Presence of Suboptimal Regions:** The "Low Quality Regions" detected suggest that the search is getting trapped or struggling to navigate through certain areas of the search space. These regions represent potential pitfalls, and the algorithm is spending time evaluating solutions in these, which may lead to stagnation.
*   **Edge Density:** The high number of low-frequency edges suggests a complex relationship between cities (nodes). The edge frequency data shows that edges between node pairs appear across a variety of paths, suggesting a complex relationship between the cities. The lack of high-frequency edges is also noteworthy.
*   **Elite Solutions Diversity:** The high number of elite solutions (41) with a low diversity score(0.1746747967479675) suggests that, the solutions are getting closer in terms of quality, but are still a bit different in terms of structure. This suggests that the high number of elites could be very similar, and the fact that the diversity is also low confirms it. This could mean that the search algorithm is very good at finding the better areas in the landscape.

**2. Current Population State Assessment**

*   **High Diversity, No Convergence:** The population maintains high diversity (0.9781418706842436), yet shows no convergence (0.0). This means that solutions are not converging toward a specific region of the search space, which could be due to the ruggedness of the landscape or the algorithm's exploration strategy. The lack of common edges between elite solutions and a very high average cost gap between the elite and current population supports this further.
*   **Stagnation:** The lack of high-quality edges in the path structure analysis and the identification of low-quality regions suggest that the population may be stagnating in less promising areas of the search space.
*   **Elite Solution Distribution:** There is a high number of elite solutions, but they are also very close to each other.
*   **Fixed Nodes:** The fixed node at position 0 can be a constraint, as if the optimal solution lies elsewhere then the population will not converge.

**3. Identification of Difficult Regions and Search Challenges**

*   **Local Optima:** The rugged landscape and high diversity suggest that the algorithm is likely getting trapped in local optima. The high number of low-frequency edges also suggests that the algorithm is getting lost in the search space.
*   **Deceptive Regions:** The algorithm might be exploring "deceptive" regions, where initial moves lead to improvement, but ultimately trap the search in suboptimal areas.
*   **Exploration-Exploitation Imbalance:** The high diversity and lack of convergence suggest a possible imbalance between exploration and exploitation. The algorithm might be spending too much time exploring and not enough time focusing on promising areas.
*   **Edge Importance:** The algorithm is facing a hard time identifying the best edges, and this is because all of the edges appear across a variety of paths.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Elite Solution Refinement:** With many elite solutions, there is an opportunity to further refine these by exploiting them, leading to better quality solutions.
*   **Focus on "Low Quality Regions":** Analyzing the features of the low-quality regions could reveal patterns that might be avoided or utilized to escape local optima.
*   **Focus on Important Edges:** Although no edges are high frequency, the edges that do occur at a higher frequency are the ones that may be important and help the algorithm progress.

**5. Recommended Evolution Direction and Strategy Balance**

*   **Enhance Exploitation:** The current population state demands more exploitation. Increase the selection pressure to favor better solutions. This could involve adjusting the selection mechanism (e.g., increasing the proportion of elite solutions selected) or introducing more elitism.
*   **Focused Exploration:** Even with increased exploitation, avoid excessive exploitation that can quickly lead to convergence on a local optimum.
*   **Edge Analysis Refinement:** Analyze the edges present in the elite solutions for common patterns or structures. The common edges should be tracked and, if found, can be applied to further refine the selection criteria.
*   **Adaptive Mutation/Crossover Rates:** The mutation and crossover rates should be adjusted. Given the high diversity in the population, it's worth considering using an adaptive mutation rate based on the fitness of the solution.
*   **Diversification Techniques:** If stagnation continues, consider introducing diversity-enhancing mechanisms:
    *   **Re-initialization:** Periodically re-initialize some portion of the population to introduce new genetic material.
    *   **Species Preservation:** Use a niching strategy to maintain diversity within the population, preventing any single solution from dominating the population and allowing solutions to find and preserve the areas that are better than the rest.
*   **Historical Trends:** Analyzing the historical trends is essential. If there is convergence over time, or some of the areas are trending better, the algorithm can lean more into exploiting those areas of the search space.
*   **Constraint Handling:** Re-evaluate the necessity of fixing the node at a specific location.

In summary, the algorithm faces a complex landscape with potential for good solutions in different areas of the search space. The high diversity and low convergence imply that there needs to be a shift from exploration to exploitation and to find the commonalities of those solutions in the high and low-quality areas.

2025-06-08 18:54:27,957 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-08 18:54:27,957 - __main__ - INFO - 景观专家分析报告: Okay, here's a comprehensive landscape analysis based on the provided information, aiming to give insights into the search space characteristics, population state, challenges, opportunities, and recommendations.

**1. Overall Search Space Characteristics**

*   **Ruggedness:** The landscape appears to be quite rugged. The cost statistics show a relatively large range (Min=86151.0, Max=112741.0) and a high standard deviation (Std=7767.86). This indicates significant variability in solution quality, suggesting many local optima and potentially deceptive regions. The lack of convergence (Convergence Level: 0.0) further supports this.
*   **Multi-modality:** The clustering information ("clusters": 10, "cluster\_sizes": \[1, 1, 1, 1, 1, 1, 1, 1, 1, 1]) reveals that the population is highly diverse, and that each solution is currently an isolated cluster. This strongly indicates a multi-modal landscape with several distinct regions of potentially good solutions.
*   **Presence of Suboptimal Regions:** The "Low Quality Regions" detected suggest that the search is getting trapped or struggling to navigate through certain areas of the search space. These regions represent potential pitfalls, and the algorithm is spending time evaluating solutions in these, which may lead to stagnation.
*   **Edge Density:** The high number of low-frequency edges suggests a complex relationship between cities (nodes). The edge frequency data shows that edges between node pairs appear across a variety of paths, suggesting a complex relationship between the cities. The lack of high-frequency edges is also noteworthy.
*   **Elite Solutions Diversity:** The high number of elite solutions (41) with a low diversity score(0.1746747967479675) suggests that, the solutions are getting closer in terms of quality, but are still a bit different in terms of structure. This suggests that the high number of elites could be very similar, and the fact that the diversity is also low confirms it. This could mean that the search algorithm is very good at finding the better areas in the landscape.

**2. Current Population State Assessment**

*   **High Diversity, No Convergence:** The population maintains high diversity (0.9781418706842436), yet shows no convergence (0.0). This means that solutions are not converging toward a specific region of the search space, which could be due to the ruggedness of the landscape or the algorithm's exploration strategy. The lack of common edges between elite solutions and a very high average cost gap between the elite and current population supports this further.
*   **Stagnation:** The lack of high-quality edges in the path structure analysis and the identification of low-quality regions suggest that the population may be stagnating in less promising areas of the search space.
*   **Elite Solution Distribution:** There is a high number of elite solutions, but they are also very close to each other.
*   **Fixed Nodes:** The fixed node at position 0 can be a constraint, as if the optimal solution lies elsewhere then the population will not converge.

**3. Identification of Difficult Regions and Search Challenges**

*   **Local Optima:** The rugged landscape and high diversity suggest that the algorithm is likely getting trapped in local optima. The high number of low-frequency edges also suggests that the algorithm is getting lost in the search space.
*   **Deceptive Regions:** The algorithm might be exploring "deceptive" regions, where initial moves lead to improvement, but ultimately trap the search in suboptimal areas.
*   **Exploration-Exploitation Imbalance:** The high diversity and lack of convergence suggest a possible imbalance between exploration and exploitation. The algorithm might be spending too much time exploring and not enough time focusing on promising areas.
*   **Edge Importance:** The algorithm is facing a hard time identifying the best edges, and this is because all of the edges appear across a variety of paths.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Elite Solution Refinement:** With many elite solutions, there is an opportunity to further refine these by exploiting them, leading to better quality solutions.
*   **Focus on "Low Quality Regions":** Analyzing the features of the low-quality regions could reveal patterns that might be avoided or utilized to escape local optima.
*   **Focus on Important Edges:** Although no edges are high frequency, the edges that do occur at a higher frequency are the ones that may be important and help the algorithm progress.

**5. Recommended Evolution Direction and Strategy Balance**

*   **Enhance Exploitation:** The current population state demands more exploitation. Increase the selection pressure to favor better solutions. This could involve adjusting the selection mechanism (e.g., increasing the proportion of elite solutions selected) or introducing more elitism.
*   **Focused Exploration:** Even with increased exploitation, avoid excessive exploitation that can quickly lead to convergence on a local optimum.
*   **Edge Analysis Refinement:** Analyze the edges present in the elite solutions for common patterns or structures. The common edges should be tracked and, if found, can be applied to further refine the selection criteria.
*   **Adaptive Mutation/Crossover Rates:** The mutation and crossover rates should be adjusted. Given the high diversity in the population, it's worth considering using an adaptive mutation rate based on the fitness of the solution.
*   **Diversification Techniques:** If stagnation continues, consider introducing diversity-enhancing mechanisms:
    *   **Re-initialization:** Periodically re-initialize some portion of the population to introduce new genetic material.
    *   **Species Preservation:** Use a niching strategy to maintain diversity within the population, preventing any single solution from dominating the population and allowing solutions to find and preserve the areas that are better than the rest.
*   **Historical Trends:** Analyzing the historical trends is essential. If there is convergence over time, or some of the areas are trending better, the algorithm can lean more into exploiting those areas of the search space.
*   **Constraint Handling:** Re-evaluate the necessity of fixing the node at a specific location.

In summary, the algorithm faces a complex landscape with potential for good solutions in different areas of the search space. The high diversity and low convergence imply that there needs to be a shift from exploration to exploitation and to find the commonalities of those solutions in the high and low-quality areas.

2025-06-08 18:54:27,957 - __main__ - INFO - 分析阶段完成
2025-06-08 18:54:27,957 - __main__ - INFO - 景观分析完整报告: Okay, here's a comprehensive landscape analysis based on the provided information, aiming to give insights into the search space characteristics, population state, challenges, opportunities, and recommendations.

**1. Overall Search Space Characteristics**

*   **Ruggedness:** The landscape appears to be quite rugged. The cost statistics show a relatively large range (Min=86151.0, Max=112741.0) and a high standard deviation (Std=7767.86). This indicates significant variability in solution quality, suggesting many local optima and potentially deceptive regions. The lack of convergence (Convergence Level: 0.0) further supports this.
*   **Multi-modality:** The clustering information ("clusters": 10, "cluster\_sizes": \[1, 1, 1, 1, 1, 1, 1, 1, 1, 1]) reveals that the population is highly diverse, and that each solution is currently an isolated cluster. This strongly indicates a multi-modal landscape with several distinct regions of potentially good solutions.
*   **Presence of Suboptimal Regions:** The "Low Quality Regions" detected suggest that the search is getting trapped or struggling to navigate through certain areas of the search space. These regions represent potential pitfalls, and the algorithm is spending time evaluating solutions in these, which may lead to stagnation.
*   **Edge Density:** The high number of low-frequency edges suggests a complex relationship between cities (nodes). The edge frequency data shows that edges between node pairs appear across a variety of paths, suggesting a complex relationship between the cities. The lack of high-frequency edges is also noteworthy.
*   **Elite Solutions Diversity:** The high number of elite solutions (41) with a low diversity score(0.1746747967479675) suggests that, the solutions are getting closer in terms of quality, but are still a bit different in terms of structure. This suggests that the high number of elites could be very similar, and the fact that the diversity is also low confirms it. This could mean that the search algorithm is very good at finding the better areas in the landscape.

**2. Current Population State Assessment**

*   **High Diversity, No Convergence:** The population maintains high diversity (0.9781418706842436), yet shows no convergence (0.0). This means that solutions are not converging toward a specific region of the search space, which could be due to the ruggedness of the landscape or the algorithm's exploration strategy. The lack of common edges between elite solutions and a very high average cost gap between the elite and current population supports this further.
*   **Stagnation:** The lack of high-quality edges in the path structure analysis and the identification of low-quality regions suggest that the population may be stagnating in less promising areas of the search space.
*   **Elite Solution Distribution:** There is a high number of elite solutions, but they are also very close to each other.
*   **Fixed Nodes:** The fixed node at position 0 can be a constraint, as if the optimal solution lies elsewhere then the population will not converge.

**3. Identification of Difficult Regions and Search Challenges**

*   **Local Optima:** The rugged landscape and high diversity suggest that the algorithm is likely getting trapped in local optima. The high number of low-frequency edges also suggests that the algorithm is getting lost in the search space.
*   **Deceptive Regions:** The algorithm might be exploring "deceptive" regions, where initial moves lead to improvement, but ultimately trap the search in suboptimal areas.
*   **Exploration-Exploitation Imbalance:** The high diversity and lack of convergence suggest a possible imbalance between exploration and exploitation. The algorithm might be spending too much time exploring and not enough time focusing on promising areas.
*   **Edge Importance:** The algorithm is facing a hard time identifying the best edges, and this is because all of the edges appear across a variety of paths.

**4. Identification of Opportunity Regions with Potential for Improvement**

*   **Elite Solution Refinement:** With many elite solutions, there is an opportunity to further refine these by exploiting them, leading to better quality solutions.
*   **Focus on "Low Quality Regions":** Analyzing the features of the low-quality regions could reveal patterns that might be avoided or utilized to escape local optima.
*   **Focus on Important Edges:** Although no edges are high frequency, the edges that do occur at a higher frequency are the ones that may be important and help the algorithm progress.

**5. Recommended Evolution Direction and Strategy Balance**

*   **Enhance Exploitation:** The current population state demands more exploitation. Increase the selection pressure to favor better solutions. This could involve adjusting the selection mechanism (e.g., increasing the proportion of elite solutions selected) or introducing more elitism.
*   **Focused Exploration:** Even with increased exploitation, avoid excessive exploitation that can quickly lead to convergence on a local optimum.
*   **Edge Analysis Refinement:** Analyze the edges present in the elite solutions for common patterns or structures. The common edges should be tracked and, if found, can be applied to further refine the selection criteria.
*   **Adaptive Mutation/Crossover Rates:** The mutation and crossover rates should be adjusted. Given the high diversity in the population, it's worth considering using an adaptive mutation rate based on the fitness of the solution.
*   **Diversification Techniques:** If stagnation continues, consider introducing diversity-enhancing mechanisms:
    *   **Re-initialization:** Periodically re-initialize some portion of the population to introduce new genetic material.
    *   **Species Preservation:** Use a niching strategy to maintain diversity within the population, preventing any single solution from dominating the population and allowing solutions to find and preserve the areas that are better than the rest.
*   **Historical Trends:** Analyzing the historical trends is essential. If there is convergence over time, or some of the areas are trending better, the algorithm can lean more into exploiting those areas of the search space.
*   **Constraint Handling:** Re-evaluate the necessity of fixing the node at a specific location.

In summary, the algorithm faces a complex landscape with potential for good solutions in different areas of the search space. The high diversity and low convergence imply that there needs to be a shift from exploration to exploitation and to find the commonalities of those solutions in the high and low-quality areas.

2025-06-08 18:54:27,957 - __main__ - INFO - 开始策略分配阶段
2025-06-08 18:54:27,957 - StrategyExpert - INFO - 开始策略分配分析
2025-06-08 18:54:27,957 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
# Strategy Selection Task

## Role Definition
You are an expert in evolutionary algorithms and combinatorial optimization. You excel at analyzing search space characteristics and population states to select the optimal balance between exploration and exploitation, and to assign appropriate strategies to different individuals.

## Current Iteration: 1

## Population Information
[{"id": 0, "cost": 86151.0, "diversity_contribution": null}, {"id": 1, "cost": 112741.0, "diversity_contribution": null}, {"id": 2, "cost": 97217.0, "diversity_contribution": null}, {"id": 3, "cost": 103309.0, "diversity_contribution": null}, {"id": 4, "cost": 102894.0, "diversity_contribution": null}, {"id": 5, "cost": 99133.0, "diversity_contribution": null}, {"id": 6, "cost": 94321.0, "diversity_contribution": null}, {"id": 7, "cost": 91621.0, "diversity_contribution": null}, {"id": 8, "cost": 97729.0, "diversity_contribution": null}, {"id": 9, "cost": 110749.0, "diversity_contribution": null}]

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Population State: Population status extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Recommended Evolution Direction: Suggestions on evolutionary direction extracted from landscape analysis

## Previous Feedback (if available)
```json
{
  "evolution_stage": {
    "stage": "middle transition",
    "confidence": 0.8,
    "stage_features": {
      "diversity_level": 0.978,
      "convergence_level": 0.001,
      "elite_stability": 0.000,
      "improvement_rate": 0.001,
      "exploration_effectiveness": 0.5,
      "exploitation_effectiveness": -10.0
    },
    "transition_indicators": {
      "approaching_next_stage": true,
      "estimated_iterations_to_transition": 1,
      "recommended_preparation": "Reduce global exploration, focus on local search refinement near promising regions. Prepare for potential convergence."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.6,
      "avg_improvement": 262.0,
      "diversity_contribution": 0.048,
      "new_region_discovery": 0.4,
      "escape_local_optima": 0.6,
      "individual_effectiveness": {
        "3": "Successfully explored, positive improvement.",
        "4": "Successfully explored, positive improvement.",
        "5": "Successfully explored, positive improvement."
      }
    },
    "exploit": {
      "success_rate": 0.2,
      "avg_improvement": -136948.0,
      "convergence_contribution": -10.0,
      "elite_improvement": 0.0,
      "local_refinement": 0.0,
      "individual_effectiveness": {
        "0": "Unsuccessful exploit, significant degradation.",
        "1": "Unsuccessful exploit, significant degradation.",
        "2": "Unsuccessful exploit, significant degradation.",
        "6": "Unsuccessful exploit, significant degradation.",
        "7": "Unsuccessful exploit, significant degradation.",
        "8": "Unsuccessful exploit, significant degradation.",
        "9": "Unsuccessful exploit, significant degradation."
      }
    },
    "overall": {
      "strategy_synergy": 0.2,
      "balance_effectiveness": 0.1,
      "iteration_efficiency": -0.2
    }
  },
  "balance_state": {
    "current_ratio": 0.3,
    "optimal_ratio": 0.1,
    "ratio_gap": 0.2,
    "balance_score": 0.3,
    "adjustment_needed": true,
    "adjustment_magnitude": 0.1,
    "adjustment_direction": "decrease",
    "balance_trend": {
      "direction": "unstable",
      "rate": -0.1,
      "prediction": "The balance is currently poor, leaning toward excessive exploration, resulting in limited overall improvement. Continue the trend towards decreasing exploration and increasing exploitation."
    }
  },
  "recommendations": {
    "explore_ratio": 0.2,
    "strategy_parameters": {
      "explore": {
        "diversity_weight": 0.7,
        "knowledge_usage_weight": 0.3,
        "risk_tolerance": 0.6
      },
      "exploit": {
        "local_search_depth": 3,
        "quality_edge_usage": 0.9,
        "elite_influence": 0.8
      }
    },
    "focus_areas": [
      "Improve exploitation strategy performance. Unsuccessful exploitation is greatly hindering progress.",
      "Evaluate the elite solutions and assess whether the elite solutions are truly elite.",
      "Monitor the elite's diversity to avoid over-specialization."
    ],
    "individual_recommendations": {
      "0": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "1": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "2": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "3": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
       "4": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
       "5": "Continue exploration, consider increasing exploration parameter values slightly (risk tolerance, diversity weight).",
      "6": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "7": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "8": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation.",
      "9": "Re-evaluate the individual's potential; consider a more exploratory approach.  Avoid this exploitation approach which lead to degradation."
    }
  },
  "long_term_analysis": {
    "convergence_trend": "Negative. Current exploitation is significantly degrading solutions.",
    "diversity_trend": "High, but decreasing among elite solutions.",
    "efficiency_trend": "Negative. Driven by poor exploitation performance.",
    "potential_issues": [
      "Over-reliance on exploration may be hindering convergence. Exploitation strategies are underperforming significantly.",
      "Elite solutions need careful monitoring; the change in elite count, combined with degradation from exploitation, suggests either the elite is poorly selected or that the problem is not well defined. ",
      "The exploration-exploitation balance is currently skewed towards exploration."
    ],
    "optimization_opportunities": [
      "Refine exploitation strategies to improve convergence.",
      "Thoroughly check if the elite solutions are actually better than others",
      "Adjust exploration/exploitation balance to favor exploitation."
    ]
  }
}
```

**Summary:**

The evolutionary algorithm is currently in a middle transition phase, characterized by high diversity but poor convergence and a marked decline in solution quality. Exploitation strategies are failing dramatically, significantly degrading the population's fitness. While exploration shows some success in finding improved solutions, it's not enough to offset the harm from the exploitation side. The elite solutions are showing significant instability, suggesting potentially inappropriate elite selection or a fundamental issue with the problem definition within the given search landscape. For the next iteration, the algorithm should prioritize strengthening its exploitation strategy, carefully review the chosen elite solutions, and reduce the global exploration ratio to promote faster convergence.  The focus should be on using a more aggressive exploitation strategy and a more refined elite solution process.


## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. The above data comes from the landscape analysis expert's analysis results and the previous generation's assessment feedback. We need you to determine the optimal exploration/exploitation balance for the current iteration and assign appropriate strategies to each individual.

# Strategy Selection Request
Please determine the optimal strategy allocation plan for the current iteration. Please follow these steps in your thinking:

## Step 1: Analyze Current Search State
- Evaluate search space characteristics (ruggedness, modality, deceptiveness, etc.)
- Analyze the current state of the population (exploration phase, diversity, convergence, etc.)
- Consider feedback and effects from the previous generation (if available)
- Identify the main challenges and opportunities currently faced

## Step 2: Determine Global Exploration Ratio
- Based on search space characteristics, determine the base exploration ratio
- Adjust the exploration ratio based on population state
- Consider the impact of evolutionary stage on exploration needs
- Analyze the effects of the previous generation's strategy to further adjust the ratio
- Determine the final global exploration ratio (between 0.0 and 1.0)

## Step 3: Build Strategy Profiles for Individuals
- Analyze the characteristics of each individual (quality, diversity contribution, historical performance, etc.)
- Evaluate the degree to which each individual is suitable for exploration or exploitation
- Consider the relationship between individuals and difficult/opportunity regions
- Build strategy tendency profiles for each individual

## Step 4: Assign Individual Strategies
- Based on the global exploration ratio, determine the number of individuals to assign exploration strategies
- Prioritize assigning exploration strategies to individuals most suitable for exploration
- Assign exploitation strategies to the remaining individuals
- Ensure strategy allocation complies with the global exploration ratio
- Record the rationale for each allocation decision

## Step 5: Determine Strategy Parameters
- Based on search space characteristics and population state, determine exploration strategy parameters
  - Diversity weight: controls the diversity level of generated solutions
  - Knowledge usage weight: controls the degree of historical knowledge utilization
  - Risk tolerance: controls the possibility of accepting suboptimal solutions
- Based on search space characteristics and population state, determine exploitation strategy parameters
  - Local search depth: controls the depth of local search
  - Quality edge usage: controls the degree of utilizing known high-quality edges
  - Elite influence: controls the influence of elite solutions on new solution generation

Please return your analysis results in JSON format with the following sections:
```json
{
  "global_explore_ratio": float,
  "justification": string,
  "individual_assignments": {
    "individual_id": "explore|exploit",
    ...
  },
  "assignment_rationale": {
    "individual_id": string,
    ...
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": float,
      "knowledge_usage_weight": float,
      "risk_tolerance": float
    },
    "exploit": {
      "local_search_depth": int,
      "quality_edge_usage": float,
      "elite_influence": float
    }
  }
}
```

Additionally, please provide a brief summary explaining your strategy selection logic and its expected effects.

2025-06-08 18:54:27,957 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-08 18:54:34,052 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.1,
  "justification": "The previous generation's feedback indicates severe issues with exploitation, leading to degradation. The algorithm is in a middle transition phase, and the elite solutions are underperforming. The current exploration ratio is too high, and the algorithm is unstable. Therefore, we must drastically reduce the global exploration ratio, and focus on exploitation to increase the convergence and prevent further degradation. The low ratio will let us capitalize on any regions found by exploration and refine in a focused way. We also need to verify whether the elite is accurate.",
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "1": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "2": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "3": "This individual was successfully exploring in the previous generation. Keeping it as an exploratory node will hopefully discover new opportunity regions.",
    "4": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "5": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "6": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "7": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "8": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "9": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.7,
      "knowledge_usage_weight": 0.3,
      "risk_tolerance": 0.8
    },
    "exploit": {
      "local_search_depth": 4,
      "quality_edge_usage": 0.95,
      "elite_influence": 0.9
    }
  }
}
```

**Summary:**

The chosen strategy prioritizes a sharp reduction in the global exploration ratio (down to 10%) to shift the focus toward exploitation and convergence. Given the feedback from the previous iteration, the exploitation strategies need to be significantly enhanced to compensate for the losses incurred during the previous iteration.  Individual assignments reflect this emphasis, assigning the vast majority of the population to exploitation.  Only a single individual is selected for exploration. The increased risk tolerance for exploration (0.8) aims to try and uncover more promising areas, while the more intensive exploitation parameters (local search depth, quality edge usage and elite influence) intend to quickly refine any potential improvements. This strategy aims to correct the imbalance and negative performance observed in the previous generation, preventing further degradation and promoting faster convergence towards optimal solutions. The high elite influence will help direct the exploited individuals towards more promising regions.

2025-06-08 18:54:34,052 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-08 18:54:34,052 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.1,
  "justification": "The previous generation's feedback indicates severe issues with exploitation, leading to degradation. The algorithm is in a middle transition phase, and the elite solutions are underperforming. The current exploration ratio is too high, and the algorithm is unstable. Therefore, we must drastically reduce the global exploration ratio, and focus on exploitation to increase the convergence and prevent further degradation. The low ratio will let us capitalize on any regions found by exploration and refine in a focused way. We also need to verify whether the elite is accurate.",
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "1": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "2": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "3": "This individual was successfully exploring in the previous generation. Keeping it as an exploratory node will hopefully discover new opportunity regions.",
    "4": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "5": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "6": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "7": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "8": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "9": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.7,
      "knowledge_usage_weight": 0.3,
      "risk_tolerance": 0.8
    },
    "exploit": {
      "local_search_depth": 4,
      "quality_edge_usage": 0.95,
      "elite_influence": 0.9
    }
  }
}
```

**Summary:**

The chosen strategy prioritizes a sharp reduction in the global exploration ratio (down to 10%) to shift the focus toward exploitation and convergence. Given the feedback from the previous iteration, the exploitation strategies need to be significantly enhanced to compensate for the losses incurred during the previous iteration.  Individual assignments reflect this emphasis, assigning the vast majority of the population to exploitation.  Only a single individual is selected for exploration. The increased risk tolerance for exploration (0.8) aims to try and uncover more promising areas, while the more intensive exploitation parameters (local search depth, quality edge usage and elite influence) intend to quickly refine any potential improvements. This strategy aims to correct the imbalance and negative performance observed in the previous generation, preventing further degradation and promoting faster convergence towards optimal solutions. The high elite influence will help direct the exploited individuals towards more promising regions.

2025-06-08 18:54:34,052 - __main__ - INFO - 策略分配阶段完成
2025-06-08 18:54:34,052 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-08 18:54:34,052 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.1,
  "justification": "The previous generation's feedback indicates severe issues with exploitation, leading to degradation. The algorithm is in a middle transition phase, and the elite solutions are underperforming. The current exploration ratio is too high, and the algorithm is unstable. Therefore, we must drastically reduce the global exploration ratio, and focus on exploitation to increase the convergence and prevent further degradation. The low ratio will let us capitalize on any regions found by exploration and refine in a focused way. We also need to verify whether the elite is accurate.",
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "assignment_rationale": {
    "0": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "1": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "2": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "3": "This individual was successfully exploring in the previous generation. Keeping it as an exploratory node will hopefully discover new opportunity regions.",
    "4": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "5": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "6": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "7": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "8": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous.",
    "9": "This individual experienced significant degradation and requires immediate refinement through exploitation. The previous attempt at exploitation failed, so the approach should be more rigorous."
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": 0.7,
      "knowledge_usage_weight": 0.3,
      "risk_tolerance": 0.8
    },
    "exploit": {
      "local_search_depth": 4,
      "quality_edge_usage": 0.95,
      "elite_influence": 0.9
    }
  }
}
```

**Summary:**

The chosen strategy prioritizes a sharp reduction in the global exploration ratio (down to 10%) to shift the focus toward exploitation and convergence. Given the feedback from the previous iteration, the exploitation strategies need to be significantly enhanced to compensate for the losses incurred during the previous iteration.  Individual assignments reflect this emphasis, assigning the vast majority of the population to exploitation.  Only a single individual is selected for exploration. The increased risk tolerance for exploration (0.8) aims to try and uncover more promising areas, while the more intensive exploitation parameters (local search depth, quality edge usage and elite influence) intend to quickly refine any potential improvements. This strategy aims to correct the imbalance and negative performance observed in the previous generation, preventing further degradation and promoting faster convergence towards optimal solutions. The high elite influence will help direct the exploited individuals towards more promising regions.

2025-06-08 18:54:34,052 - __main__ - INFO - 开始进化阶段
2025-06-08 18:54:34,056 - __main__ - INFO - 为个体 0 生成利用路径
2025-06-08 18:54:34,056 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:54:34,056 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:54:34,056 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 79524.0
2025-06-08 18:54:34,070 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,097 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 18:54:34,104 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: critical_edge
2025-06-08 18:54:34,119 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,121 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,138 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,154 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,156 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,159 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,164 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:34,173 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,177 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,182 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,212 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:34,238 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,250 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:34,276 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:54:34,282 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:34,301 - root - INFO - 拓扑感知扰动用时: 0.0007秒，使用策略: pattern_based
2025-06-08 18:54:34,340 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:34,344 - root - INFO - 拓扑感知扰动用时: 0.0024秒，使用策略: pattern_based
2025-06-08 18:54:34,348 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,374 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:34,384 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:34,400 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,414 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,419 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:34,423 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,427 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:54:34,431 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:34,432 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:34,433 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:34,434 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,436 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,439 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,440 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,443 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 18:54:34,448 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,454 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:34,455 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:34,459 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,461 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: critical_edge
2025-06-08 18:54:34,463 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,464 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,469 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,472 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,474 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:34,477 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 18:54:34,478 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,482 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:34,485 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:34,489 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:54:34,492 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,493 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,494 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,498 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,500 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,502 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: segment_preservation
2025-06-08 18:54:34,503 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,506 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 18:54:34,510 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:34,514 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,518 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: segment_preservation
2025-06-08 18:54:34,519 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,521 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,523 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:54:34,526 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,527 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,530 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,537 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,541 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:34,542 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:34,545 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,547 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,551 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,552 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,559 - ExploitationExpert - INFO - res_population_num: 41
2025-06-08 18:54:34,559 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9621, 9621, 9626, 9626, 9626, 9626, 9912]
2025-06-08 18:54:34,559 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 59, 51, 50, 58, 49, 56, 55, 57,
       52, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 13, 12, 23, 16, 20, 22, 17, 15,
       19, 14, 21, 18, 59, 48, 53, 54, 58, 49, 56, 55, 57, 52, 50, 51, 44,
       43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-08 18:54:34,577 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:54:34,577 - ExploitationExpert - INFO - populations: [{'tour': array([39,  0, 25, 31, 27, 51,  9, 19, 33, 56, 45, 13, 49, 34, 44, 47, 14,
       55, 18, 11,  7, 28, 29, 53, 54,  8, 16, 48, 57, 15,  5, 30,  4, 43,
       38, 46, 40, 42, 32, 35, 59,  2, 10,  6, 17, 20, 52, 24, 50, 26, 12,
       21, 58, 23,  1, 41,  3, 22, 36, 37]), 'cur_cost': 79524.0}, {'tour': array([ 8, 42, 27, 39, 28,  4, 18, 43, 57, 25, 26, 46, 33, 22, 32, 37, 14,
       51, 23, 29, 35, 56, 52, 12, 10, 59, 11, 40,  3, 13, 34, 50, 38, 54,
        6, 21, 24, 17, 47,  7, 15, 16,  5, 20, 41, 19,  0, 55, 31, 53,  2,
       58,  1, 49, 36, 48,  9, 44, 45, 30]), 'cur_cost': 112741.0}, {'tour': array([53, 54,  5, 43, 42, 49,  3, 30, 40,  2,  0, 34, 52, 23, 21, 57, 28,
       27, 41, 33, 17, 18, 46, 35, 55, 32, 26, 11, 13,  9, 47, 22, 31, 29,
       12, 37, 24,  1, 50, 58, 39, 48, 14,  7, 44,  6,  4, 20, 19, 51, 10,
       25, 59, 56, 36, 16, 45, 15, 38,  8]), 'cur_cost': 97217.0}, {'tour': [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19], 'cur_cost': 103309.0}, {'tour': [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50], 'cur_cost': 102894.0}, {'tour': [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38], 'cur_cost': 99133.0}, {'tour': array([12, 45, 19,  0,  3,  8, 32,  5, 35, 57,  9, 58,  2, 44, 30, 33, 42,
       10,  7, 17, 11, 29, 53,  6, 55, 56, 24, 27, 48, 50, 26, 59, 34, 39,
       51, 22, 14, 49, 37, 40, 28,  4,  1, 52, 47, 20, 25, 15, 38, 43, 41,
       18, 36, 46, 13, 54, 23, 31, 21, 16]), 'cur_cost': 94321.0}, {'tour': array([21, 13, 56, 47, 39,  6, 58, 19, 22,  2,  4,  9,  5, 29, 17, 18, 23,
       45, 38, 36, 48, 59, 16,  8, 10, 28, 30,  7, 57,  0, 40, 12, 25,  3,
       44, 15, 51, 41, 24, 54, 32, 53,  1, 14, 55, 27, 37, 11, 46, 33, 52,
       26, 49, 34, 43, 20, 31, 35, 42, 50]), 'cur_cost': 91621.0}, {'tour': array([23, 46, 22, 28, 25, 33, 56, 53, 12, 40, 10, 44,  8, 50, 21, 20, 18,
       51, 43,  6,  1, 11, 35, 57,  2,  4, 29, 54, 19, 59, 31,  5, 13, 27,
        7, 42, 26, 36, 16, 48, 58,  0, 52, 47, 41, 14, 37,  9, 32, 39, 55,
       34, 30,  3, 24, 15, 45, 17, 38, 49]), 'cur_cost': 97729.0}, {'tour': array([12, 11,  9, 28,  5, 36, 13, 39, 38, 49,  1, 10, 52, 17, 46, 57, 34,
       58,  3, 43, 37, 27, 54, 35, 26, 16, 45, 15,  4,  6, 32,  7, 21, 25,
       14, 31,  0, 33, 55, 50, 24, 22, 29, 48, 53, 23, 41, 18, 59, 51,  2,
       44, 20, 42,  8, 30, 40, 19, 56, 47]), 'cur_cost': 110749.0}]
2025-06-08 18:54:34,579 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-08 18:54:34,579 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-06-08 18:54:34,581 - __main__ - WARNING - 个体 0 的利用路径生成失败，保留原个体
2025-06-08 18:54:34,581 - __main__ - INFO - 为个体 1 生成利用路径
2025-06-08 18:54:34,581 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:54:34,581 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:54:34,581 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 96946.0
2025-06-08 18:54:34,586 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,601 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 18:54:34,603 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,605 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:54:34,618 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,624 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,628 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,634 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:34,642 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,648 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,663 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:34,673 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,680 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,684 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:34,686 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:34,691 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:34,715 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:54:34,725 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,739 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,756 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,763 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,793 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:34,797 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,797 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,822 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,840 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,841 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,846 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,864 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:34,871 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,893 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,896 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,896 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:34,903 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:54:34,904 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,910 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,912 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,914 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:34,918 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,921 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,926 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,928 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,932 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,936 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,938 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,941 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:34,943 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,947 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,951 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:34,955 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,963 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,965 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,968 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,971 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,973 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,975 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,976 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,980 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,981 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:34,982 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:34,985 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 18:54:34,985 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:34,989 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:34,999 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: segment_preservation
2025-06-08 18:54:35,000 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:35,003 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:35,005 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,008 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:35,016 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:35,018 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,020 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:54:35,026 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:54:35,031 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,035 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:54:35,036 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,044 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:35,045 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,045 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,053 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:54:35,057 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,059 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,064 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,066 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:35,068 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: segment_preservation
2025-06-08 18:54:35,069 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,071 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,074 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,077 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,080 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:54:35,084 - ExploitationExpert - INFO - res_population_num: 41
2025-06-08 18:54:35,085 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9621, 9621, 9626, 9626, 9626, 9626, 9912]
2025-06-08 18:54:35,085 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 59, 51, 50, 58, 49, 56, 55, 57,
       52, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 13, 12, 23, 16, 20, 22, 17, 15,
       19, 14, 21, 18, 59, 48, 53, 54, 58, 49, 56, 55, 57, 52, 50, 51, 44,
       43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-08 18:54:35,103 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:54:35,103 - ExploitationExpert - INFO - populations: [{'tour': array([39,  0, 25, 31, 27, 51,  9, 19, 33, 56, 45, 13, 49, 34, 44, 47, 14,
       55, 18, 11,  7, 28, 29, 53, 54,  8, 16, 48, 57, 15,  5, 30,  4, 43,
       38, 46, 40, 42, 32, 35, 59,  2, 10,  6, 17, 20, 52, 24, 50, 26, 12,
       21, 58, 23,  1, 41,  3, 22, 36, 37]), 'cur_cost': 79524.0}, {'tour': array([24, 13, 30,  2, 23, 28, 10, 19, 36, 39,  1, 45, 41,  3, 49,  0, 58,
       38, 22, 32, 29, 42,  8,  9, 21, 59, 43,  4,  6, 17, 46,  7, 34, 12,
       50, 48, 51, 31, 57, 15, 55, 35, 54, 20, 52, 44, 33, 56, 53, 16, 47,
       37, 14, 27, 25, 26,  5, 11, 40, 18]), 'cur_cost': 96946.0}, {'tour': array([53, 54,  5, 43, 42, 49,  3, 30, 40,  2,  0, 34, 52, 23, 21, 57, 28,
       27, 41, 33, 17, 18, 46, 35, 55, 32, 26, 11, 13,  9, 47, 22, 31, 29,
       12, 37, 24,  1, 50, 58, 39, 48, 14,  7, 44,  6,  4, 20, 19, 51, 10,
       25, 59, 56, 36, 16, 45, 15, 38,  8]), 'cur_cost': 97217.0}, {'tour': [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19], 'cur_cost': 103309.0}, {'tour': [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50], 'cur_cost': 102894.0}, {'tour': [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38], 'cur_cost': 99133.0}, {'tour': array([12, 45, 19,  0,  3,  8, 32,  5, 35, 57,  9, 58,  2, 44, 30, 33, 42,
       10,  7, 17, 11, 29, 53,  6, 55, 56, 24, 27, 48, 50, 26, 59, 34, 39,
       51, 22, 14, 49, 37, 40, 28,  4,  1, 52, 47, 20, 25, 15, 38, 43, 41,
       18, 36, 46, 13, 54, 23, 31, 21, 16]), 'cur_cost': 94321.0}, {'tour': array([21, 13, 56, 47, 39,  6, 58, 19, 22,  2,  4,  9,  5, 29, 17, 18, 23,
       45, 38, 36, 48, 59, 16,  8, 10, 28, 30,  7, 57,  0, 40, 12, 25,  3,
       44, 15, 51, 41, 24, 54, 32, 53,  1, 14, 55, 27, 37, 11, 46, 33, 52,
       26, 49, 34, 43, 20, 31, 35, 42, 50]), 'cur_cost': 91621.0}, {'tour': array([23, 46, 22, 28, 25, 33, 56, 53, 12, 40, 10, 44,  8, 50, 21, 20, 18,
       51, 43,  6,  1, 11, 35, 57,  2,  4, 29, 54, 19, 59, 31,  5, 13, 27,
        7, 42, 26, 36, 16, 48, 58,  0, 52, 47, 41, 14, 37,  9, 32, 39, 55,
       34, 30,  3, 24, 15, 45, 17, 38, 49]), 'cur_cost': 97729.0}, {'tour': array([12, 11,  9, 28,  5, 36, 13, 39, 38, 49,  1, 10, 52, 17, 46, 57, 34,
       58,  3, 43, 37, 27, 54, 35, 26, 16, 45, 15,  4,  6, 32,  7, 21, 25,
       14, 31,  0, 33, 55, 50, 24, 22, 29, 48, 53, 23, 41, 18, 59, 51,  2,
       44, 20, 42,  8, 30, 40, 19, 56, 47]), 'cur_cost': 110749.0}]
2025-06-08 18:54:35,107 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-08 18:54:35,107 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-06-08 18:54:35,107 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-06-08 18:54:35,107 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-08 18:54:35,107 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:54:35,108 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:54:35,109 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 100499.0
2025-06-08 18:54:35,132 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:35,149 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:35,153 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:35,188 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,192 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:54:35,201 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,208 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:35,212 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,230 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,238 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,241 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,272 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,310 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:35,330 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,349 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:35,408 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:54:35,439 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,441 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,446 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,462 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,467 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,499 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,516 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:35,518 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:54:35,521 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: critical_edge
2025-06-08 18:54:35,523 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,531 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:35,537 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:54:35,539 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:54:35,544 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:54:35,606 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:54:35,631 - ExploitationExpert - INFO - res_population_num: 41
2025-06-08 18:54:35,631 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9621, 9621, 9626, 9626, 9626, 9626, 9912]
2025-06-08 18:54:35,631 - ExploitationExpert - INFO - res_populations: [array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,
       26,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  9,  3,  2,  7, 11,  6,  5,  4], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 59, 51, 50, 58, 49, 56, 55, 57,
       52, 53, 48, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36,
       45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48, 53, 55, 57,
       52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14,
       18,  3,  2,  7, 11,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 13, 12, 23, 16, 20, 22, 17, 15,
       19, 14, 21, 18, 59, 48, 53, 54, 58, 49, 56, 55, 57, 52, 50, 51, 44,
       43, 41, 39, 46, 40, 37, 47, 42, 38, 45, 36, 34, 30, 24, 33, 29, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-08 18:54:35,654 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:54:35,654 - ExploitationExpert - INFO - populations: [{'tour': array([39,  0, 25, 31, 27, 51,  9, 19, 33, 56, 45, 13, 49, 34, 44, 47, 14,
       55, 18, 11,  7, 28, 29, 53, 54,  8, 16, 48, 57, 15,  5, 30,  4, 43,
       38, 46, 40, 42, 32, 35, 59,  2, 10,  6, 17, 20, 52, 24, 50, 26, 12,
       21, 58, 23,  1, 41,  3, 22, 36, 37]), 'cur_cost': 79524.0}, {'tour': array([24, 13, 30,  2, 23, 28, 10, 19, 36, 39,  1, 45, 41,  3, 49,  0, 58,
       38, 22, 32, 29, 42,  8,  9, 21, 59, 43,  4,  6, 17, 46,  7, 34, 12,
       50, 48, 51, 31, 57, 15, 55, 35, 54, 20, 52, 44, 33, 56, 53, 16, 47,
       37, 14, 27, 25, 26,  5, 11, 40, 18]), 'cur_cost': 96946.0}, {'tour': array([18, 46, 15,  6, 19,  4, 13, 35,  2, 49, 29,  0, 27, 38, 33,  9, 26,
       47, 58, 57, 42, 43,  5,  3, 59, 10, 56, 12, 28, 14, 41, 22, 21, 11,
        8, 20, 37, 39, 50, 40, 44, 30, 31, 52, 48,  7, 51, 55, 24, 16, 34,
       25, 53, 54, 36, 32, 23, 17, 45,  1]), 'cur_cost': 100499.0}, {'tour': [31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19], 'cur_cost': 103309.0}, {'tour': [36, 15, 6, 18, 22, 45, 9, 37, 21, 25, 14, 23, 55, 29, 3, 32, 46, 5, 54, 35, 7, 58, 19, 57, 38, 48, 49, 30, 28, 51, 1, 13, 0, 40, 31, 43, 52, 8, 16, 42, 33, 47, 24, 53, 11, 20, 4, 27, 26, 34, 10, 2, 17, 59, 56, 39, 12, 41, 50], 'cur_cost': 102894.0}, {'tour': [41, 21, 39, 35, 59, 51, 50, 24, 23, 12, 47, 55, 58, 31, 16, 46, 18, 20, 5, 8, 6, 0, 40, 32, 14, 2, 48, 43, 42, 25, 53, 22, 3, 57, 36, 49, 33, 37, 10, 28, 15, 13, 29, 30, 34, 44, 52, 45, 17, 54, 4, 7, 19, 26, 1, 56, 9, 27, 11, 38], 'cur_cost': 99133.0}, {'tour': array([12, 45, 19,  0,  3,  8, 32,  5, 35, 57,  9, 58,  2, 44, 30, 33, 42,
       10,  7, 17, 11, 29, 53,  6, 55, 56, 24, 27, 48, 50, 26, 59, 34, 39,
       51, 22, 14, 49, 37, 40, 28,  4,  1, 52, 47, 20, 25, 15, 38, 43, 41,
       18, 36, 46, 13, 54, 23, 31, 21, 16]), 'cur_cost': 94321.0}, {'tour': array([21, 13, 56, 47, 39,  6, 58, 19, 22,  2,  4,  9,  5, 29, 17, 18, 23,
       45, 38, 36, 48, 59, 16,  8, 10, 28, 30,  7, 57,  0, 40, 12, 25,  3,
       44, 15, 51, 41, 24, 54, 32, 53,  1, 14, 55, 27, 37, 11, 46, 33, 52,
       26, 49, 34, 43, 20, 31, 35, 42, 50]), 'cur_cost': 91621.0}, {'tour': array([23, 46, 22, 28, 25, 33, 56, 53, 12, 40, 10, 44,  8, 50, 21, 20, 18,
       51, 43,  6,  1, 11, 35, 57,  2,  4, 29, 54, 19, 59, 31,  5, 13, 27,
        7, 42, 26, 36, 16, 48, 58,  0, 52, 47, 41, 14, 37,  9, 32, 39, 55,
       34, 30,  3, 24, 15, 45, 17, 38, 49]), 'cur_cost': 97729.0}, {'tour': array([12, 11,  9, 28,  5, 36, 13, 39, 38, 49,  1, 10, 52, 17, 46, 57, 34,
       58,  3, 43, 37, 27, 54, 35, 26, 16, 45, 15,  4,  6, 32,  7, 21, 25,
       14, 31,  0, 33, 55, 50, 24, 22, 29, 48, 53, 23, 41, 18, 59, 51,  2,
       44, 20, 42,  8, 30, 40, 19, 56, 47]), 'cur_cost': 110749.0}]
2025-06-08 18:54:35,657 - ExploitationExpert - INFO - 局部搜索耗时: 0.55秒
2025-06-08 18:54:35,657 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-06-08 18:54:35,659 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-06-08 18:54:35,659 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-08 18:54:35,659 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 18:54:35,672 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 44, 11, 12, 19]

## Path Cost: 103309.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9774011299435028

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 18:54:35,674 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 18:54:41,481 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [44, 12, 19, 31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 11],
  "modification_strategy": "Large-scale restructuring with focus on diversifying the beginning and end of the path.",
  "targeted_regions": ["Regions influenced by nodes 11, 12, 19, and 44 (as indicated by the starting and ending positions.)"],
  "avoided_regions": [],
  "knowledge_application": "No knowledge was used as there was no historical knowledge available.",
  "diversity_contribution": "High, due to the significant changes to the path's structure and the altered ordering of initial and final nodes. This contributes to a higher diversity within the population.",
  "expected_benefits": "Increased population diversity, potentially leading to the discovery of new and improved solution areas. Shifting the start and end nodes explores different regions of the search space. This will provide a more robust search and greater chance of improving overall solution quality."
}
```

**Explanation:**

The strategy employed is a large-scale restructuring of the existing path to enhance diversity and explore unexplored regions. Given the high diversity weight (0.7), a significant degree of modification is warranted. With no retrieved knowledge available to draw on, the focus is on pure exploration. The risk tolerance (0.6) allows for a moderate degree of aggressive exploration.

**Step-by-step breakdown:**

1.  **Analyze Current Path:** The current path shows a generally consistent ordering. The strategy will keep much of the core structure but focuses on making large changes to the start and end portion to drive exploration.

2.  **Determine Exploration Strategy:** A large-scale restructuring is selected. This involves altering the order of significant sections of the path, particularly focusing on the beginning and end. This is due to the lack of any prior knowledge; this aims to ensure the new path is very different.

3.  **Select Target Regions:** The primary exploration target is the region influenced by nodes at the beginning and end of the tour. By reordering these nodes, different traversal patterns are explored, potentially finding better starting or ending points that lead to improved solutions.

4.  **Apply Historical Knowledge:** No historical knowledge is available, so this step is skipped.

5.  **Generate New Path:** The initial nodes (44, 12, 19) were moved to the start, and the final node (11) was moved towards the end. The intermediate portion was mostly left untouched. This approach ensures a high degree of path modification and directs exploration towards specific nodes. The strategy avoids focusing on specific 'difficult regions' as none are highlighted.

2025-06-08 18:54:41,481 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 18:54:41,481 - IdeaExtractor - INFO - 成功从JSON中提取路径
2025-06-08 18:54:41,481 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 18:54:41,481 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103631.0, 路径: [44, 12, 19, 31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 11]
2025-06-08 18:54:41,481 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_path': [44, 12, 19, 31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 11], 'modification_strategy': 'Large-scale restructuring with focus on diversifying the beginning and end of the path.', 'targeted_regions': ['Regions influenced by nodes 11, 12, 19, and 44 (as indicated by the starting and ending positions.)'], 'avoided_regions': [], 'knowledge_application': 'No knowledge was used as there was no historical knowledge available.', 'diversity_contribution': "High, due to the significant changes to the path's structure and the altered ordering of initial and final nodes. This contributes to a higher diversity within the population.", 'expected_benefits': 'Increased population diversity, potentially leading to the discovery of new and improved solution areas. Shifting the start and end nodes explores different regions of the search space. This will provide a more robust search and greater chance of improving overall solution quality.', 'new_tour': [44, 12, 19, 31, 23, 10, 5, 9, 53, 18, 34, 42, 52, 43, 40, 13, 38, 21, 51, 49, 33, 17, 50, 8, 25, 48, 37, 47, 59, 57, 56, 7, 24, 1, 16, 36, 20, 55, 28, 45, 0, 3, 22, 39, 32, 6, 26, 30, 4, 15, 2, 41, 14, 27, 54, 46, 35, 58, 11], 'cur_cost': 103631.0}
2025-06-08 18:54:41,481 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-08 18:54:41,481 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:54:41,481 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:54:41,481 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 104420.0
