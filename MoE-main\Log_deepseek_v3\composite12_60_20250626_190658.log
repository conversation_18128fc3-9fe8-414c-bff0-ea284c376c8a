2025-06-26 19:06:58,814 - __main__ - INFO - composite12_60 开始进化第 1 代
2025-06-26 19:06:58,815 - __main__ - INFO - 开始分析阶段
2025-06-26 19:06:58,815 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:06:58,830 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9930.0, 'max': 107186.0, 'mean': 71402.7, 'std': 40418.766252942456}, 'diversity': 0.9085185185185185, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:06:58,831 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9930.0, 'max': 107186.0, 'mean': 71402.7, 'std': 40418.766252942456}, 'diversity_level': 0.9085185185185185, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[1380, 1590], [1386, 1610], [1446, 1664], [1473, 1651], [1403, 1573], [1429, 1576], [1414, 1596], [1421, 1664], [1385, 1662], [1475, 1616], [1405, 1625], [1439, 1639], [3242, 2428], [3193, 2490], [3212, 2461], [3238, 2498], [3270, 2452], [3236, 2513], [3196, 2449], [3220, 2481], [3262, 2465], [3198, 2468], [3267, 2481], [3264, 2442], [426, 2488], [478, 2496], [499, 2467], [445, 2489], [451, 2481], [441, 2477], [461, 2463], [511, 2487], [499, 2478], [431, 2471], [461, 2456], [476, 2486], [832, 400], [885, 348], [838, 364], [921, 379], [900, 346], [918, 411], [855, 361], [866, 388], [878, 327], [841, 380], [902, 374], [871, 361], [2763, 1531], [2817, 1507], [2789, 1453], [2739, 1464], [2773, 1490], [2783, 1514], [2808, 1535], [2797, 1510], [2807, 1517], [2793, 1495], [2828, 1490], [2736, 1497]], 'distance_matrix': array([[   0.,   21.,   99., ..., 1416., 1451., 1359.],
       [  21.,    0.,   81., ..., 1412., 1447., 1355.],
       [  99.,   81.,    0., ..., 1358., 1393., 1301.],
       ...,
       [1416., 1412., 1358., ...,    0.,   35.,   57.],
       [1451., 1447., 1393., ...,   35.,    0.,   92.],
       [1359., 1355., 1301., ...,   57.,   92.,    0.]])}
2025-06-26 19:06:58,840 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:06:58,840 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:06:58,840 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:06:58,846 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:06:58,846 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (40, 46), 'frequency': 0.5, 'avg_cost': 28.0}], 'common_subpaths': [{'subpath': (28, 27, 29), 'frequency': 0.3}, {'subpath': (27, 29, 33), 'frequency': 0.3}, {'subpath': (29, 33, 24), 'frequency': 0.3}, {'subpath': (32, 26, 31), 'frequency': 0.3}, {'subpath': (8, 7, 2), 'frequency': 0.3}, {'subpath': (7, 2, 11), 'frequency': 0.3}, {'subpath': (2, 11, 3), 'frequency': 0.3}, {'subpath': (11, 3, 9), 'frequency': 0.3}, {'subpath': (3, 9, 5), 'frequency': 0.3}, {'subpath': (9, 5, 6), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(30, 34)', 'frequency': 0.4}, {'edge': '(26, 31)', 'frequency': 0.4}, {'edge': '(2, 11)', 'frequency': 0.4}, {'edge': '(10, 41)', 'frequency': 0.4}, {'edge': '(40, 46)', 'frequency': 0.5}, {'edge': '(51, 59)', 'frequency': 0.4}, {'edge': '(55, 57)', 'frequency': 0.4}, {'edge': '(55, 56)', 'frequency': 0.4}, {'edge': '(53, 54)', 'frequency': 0.4}, {'edge': '(12, 23)', 'frequency': 0.4}, {'edge': '(15, 22)', 'frequency': 0.4}, {'edge': '(13, 18)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(28, 34)', 'frequency': 0.2}, {'edge': '(27, 28)', 'frequency': 0.3}, {'edge': '(27, 29)', 'frequency': 0.3}, {'edge': '(29, 33)', 'frequency': 0.3}, {'edge': '(24, 33)', 'frequency': 0.3}, {'edge': '(25, 35)', 'frequency': 0.3}, {'edge': '(25, 32)', 'frequency': 0.2}, {'edge': '(26, 32)', 'frequency': 0.3}, {'edge': '(8, 31)', 'frequency': 0.3}, {'edge': '(7, 8)', 'frequency': 0.3}, {'edge': '(2, 7)', 'frequency': 0.3}, {'edge': '(3, 11)', 'frequency': 0.3}, {'edge': '(3, 9)', 'frequency': 0.3}, {'edge': '(5, 9)', 'frequency': 0.3}, {'edge': '(5, 6)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.3}, {'edge': '(0, 4)', 'frequency': 0.3}, {'edge': '(0, 1)', 'frequency': 0.3}, {'edge': '(1, 10)', 'frequency': 0.3}, {'edge': '(39, 41)', 'frequency': 0.3}, {'edge': '(39, 46)', 'frequency': 0.3}, {'edge': '(37, 40)', 'frequency': 0.3}, {'edge': '(37, 47)', 'frequency': 0.3}, {'edge': '(42, 47)', 'frequency': 0.3}, {'edge': '(38, 42)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(36, 45)', 'frequency': 0.3}, {'edge': '(36, 43)', 'frequency': 0.3}, {'edge': '(43, 44)', 'frequency': 0.3}, {'edge': '(44, 51)', 'frequency': 0.3}, {'edge': '(52, 59)', 'frequency': 0.3}, {'edge': '(52, 57)', 'frequency': 0.3}, {'edge': '(49, 56)', 'frequency': 0.3}, {'edge': '(49, 58)', 'frequency': 0.3}, {'edge': '(54, 58)', 'frequency': 0.3}, {'edge': '(48, 53)', 'frequency': 0.3}, {'edge': '(48, 50)', 'frequency': 0.3}, {'edge': '(12, 50)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(16, 20)', 'frequency': 0.3}, {'edge': '(20, 22)', 'frequency': 0.3}, {'edge': '(15, 17)', 'frequency': 0.3}, {'edge': '(17, 19)', 'frequency': 0.3}, {'edge': '(14, 19)', 'frequency': 0.3}, {'edge': '(14, 21)', 'frequency': 0.3}, {'edge': '(18, 21)', 'frequency': 0.3}, {'edge': '(43, 57)', 'frequency': 0.2}, {'edge': '(1, 43)', 'frequency': 0.2}, {'edge': '(20, 53)', 'frequency': 0.2}, {'edge': '(27, 59)', 'frequency': 0.2}, {'edge': '(11, 47)', 'frequency': 0.2}, {'edge': '(47, 58)', 'frequency': 0.2}, {'edge': '(30, 48)', 'frequency': 0.2}, {'edge': '(5, 23)', 'frequency': 0.3}, {'edge': '(5, 39)', 'frequency': 0.2}, {'edge': '(34, 50)', 'frequency': 0.2}, {'edge': '(34, 37)', 'frequency': 0.2}, {'edge': '(37, 45)', 'frequency': 0.2}, {'edge': '(0, 45)', 'frequency': 0.2}, {'edge': '(20, 29)', 'frequency': 0.2}, {'edge': '(35, 55)', 'frequency': 0.2}, {'edge': '(24, 41)', 'frequency': 0.2}, {'edge': '(25, 57)', 'frequency': 0.2}, {'edge': '(36, 42)', 'frequency': 0.2}, {'edge': '(16, 50)', 'frequency': 0.2}, {'edge': '(5, 21)', 'frequency': 0.2}, {'edge': '(4, 45)', 'frequency': 0.2}, {'edge': '(25, 39)', 'frequency': 0.2}, {'edge': '(2, 24)', 'frequency': 0.2}, {'edge': '(17, 24)', 'frequency': 0.2}, {'edge': '(6, 55)', 'frequency': 0.2}, {'edge': '(31, 47)', 'frequency': 0.2}, {'edge': '(52, 56)', 'frequency': 0.2}, {'edge': '(8, 34)', 'frequency': 0.2}, {'edge': '(35, 47)', 'frequency': 0.2}, {'edge': '(47, 52)', 'frequency': 0.2}, {'edge': '(32, 40)', 'frequency': 0.2}, {'edge': '(23, 37)', 'frequency': 0.2}, {'edge': '(17, 21)', 'frequency': 0.2}, {'edge': '(14, 16)', 'frequency': 0.2}, {'edge': '(19, 53)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [20, 29, 19, 42, 55, 40], 'cost': 13258.0, 'size': 6}, {'region': [15, 43, 13, 46, 21], 'cost': 12537.0, 'size': 5}, {'region': [26, 15, 29, 21, 40], 'cost': 11421.0, 'size': 5}, {'region': [36, 54, 32, 14, 29], 'cost': 10257.0, 'size': 5}, {'region': [38, 59, 27, 54, 42], 'cost': 9534.0, 'size': 5}]}
2025-06-26 19:06:58,847 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:06:58,847 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:06:58,847 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:06:58,847 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 19:06:58,848 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 19:06:58,848 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:06:58,848 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:06:58,850 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:06:58,850 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9930.0, mean 71402.7, max 107186.0, std 40418.766252942456
- diversity: 0.9085185185185185
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [26245.0, 33711.0, 26245.0] (Δ 3733.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (40, 46), 'frequency': 0.5, 'avg_cost': 28.0}]
- common_subpaths_sample: [{'subpath': (28, 27, 29), 'frequency': 0.3}, {'subpath': (27, 29, 33), 'frequency': 0.3}, {'subpath': (29, 33, 24), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [20, 29, 19, 42, 55, 40], 'cost': 13258.0, 'size': 6}, {'region': [15, 43, 13, 46, 21], 'cost': 12537.0, 'size': 5}, {'region': [26, 15, 29, 21, 40], 'cost': 11421.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [426.0, 327.0, 3270.0, 2513.0]
- centroid/spread: [1756.217, 1687.917] | rms 1327.809
- density_grid(3x3): [[12, 0, 0], [0, 12, 12], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [7.0, 1094.25, 1427.5, 2273.75, 3222.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 7, 6, 9, 12, 8, 4, 7]
- nn_median_dist: 17.242

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:06:58,851 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:07:24,337 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 7.0 to 3222.0, std dev ~40418.7)",
    "modality": "Potentially multimodal (high diversity 0.91, no clustering info yet)",
    "deceptiveness": "Possible (long_edge_ratio 0.25 suggests some deceptive long edges)"
  },
  "population_state": {
    "coverage": "Fragmented (density_grid shows clusters with 12 nodes and empty cells)",
    "convergence": "Early stage (0.0 convergence, recent best costs fluctuating)",
    "diversity": "High (0.91)"
  },
  "difficult_regions": [
    {"region": [20, 29, 19, 42, 55, 40], "reason": "High cost (13258) + spatial spread"},
    {"region": [15, 43, 13, 46, 21], "reason": "High cost (12537) + overlaps with low-density grid cells"},
    {"region": [26, 15, 29, 21, 40], "reason": "High cost (11421) + directional bias in sector histogram"}
  ],
  "opportunity_regions": [
    {"potential_nodes": [28, 27, 29], "reason": "Frequent subpath (0.3 freq) in high-density grid cell"},
    {"potential_edges": [(40, 46)], "reason": "High-quality edge (0.5 freq, avg_cost 28)"}
  ],
  "evolution_phase": "Exploration (high diversity, no elites, early iteration)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Edge recombination focusing on high-frequency subpaths (e.g. 28-27-29)",
    "2. Local search intensification around node 40 (anchor of high-quality edge)",
    "3. Adaptive mutation rates targeting sparse grid cells",
    "4. Direction-aware crossover (sector histogram shows bias)"
  ]
}
```
2025-06-26 19:07:24,337 - LandscapeExpert - WARNING - 解析景观分析结果失败: Extracted JSON data is invalid
2025-06-26 19:07:24,337 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:07:24,337 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': '```json\n{\n  "search_space_features": {\n    "ruggedness": "Moderate to high (wide range of edge lengths from 7.0 to 3222.0, std dev ~40418.7)",\n    "modality": "Potentially multimodal (high diversity 0.91, no clustering info yet)",\n    "deceptiveness": "Possible (long_edge_ratio 0.25 suggests some deceptive long edges)"\n  },\n  "population_state": {\n    "coverage": "Fragmented (density_grid shows clusters with 12 nodes and empty cells)",\n    "convergence": "Early stage (0.0 convergence, recent best costs fluctuating)",\n    "diversity": "High (0.91)"\n  },\n  "difficult_regions": [\n    {"region": [20, 29, 19, 42, 55, 40], "reason": "High cost (13258) + spatial spread"},\n    {"region": [15, 43, 13, 46, 21], "reason": "High cost (12537) + overlaps with low-density grid cells"},\n    {"region": [26, 15, 29, 21, 40], "reason": "High cost (11421) + directional bias in sector histogram"}\n  ],\n  "opportunity_regions": [\n    {"potential_nodes": [28, 27, 29], "reason": "Frequent subpath (0.3 freq) in high-density grid cell"},\n    {"potential_edges": [(40, 46)], "reason": "High-quality edge (0.5 freq, avg_cost 28)"}\n  ],\n  "evolution_phase": "Exploration (high diversity, no elites, early iteration)",\n  "evolution_direction": [\n    "Operator suggestions:",\n    "1. Edge recombination focusing on high-frequency subpaths (e.g. 28-27-29)",\n    "2. Local search intensification around node 40 (anchor of high-quality edge)",\n    "3. Adaptive mutation rates targeting sparse grid cells",\n    "4. Direction-aware crossover (sector histogram shows bias)"\n  ]\n}\n```'}
2025-06-26 19:07:24,337 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:07:24,337 - __main__ - INFO - 分析阶段完成
2025-06-26 19:07:24,337 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'unknown', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.5, 'convergence': 0.5, 'clustering': 'unknown'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'unknown', 'evolution_direction': {'recommended_focus': 'balance', 'operators': []}, 'raw_text': '```json\n{\n  "search_space_features": {\n    "ruggedness": "Moderate to high (wide range of edge lengths from 7.0 to 3222.0, std dev ~40418.7)",\n    "modality": "Potentially multimodal (high diversity 0.91, no clustering info yet)",\n    "deceptiveness": "Possible (long_edge_ratio 0.25 suggests some deceptive long edges)"\n  },\n  "population_state": {\n    "coverage": "Fragmented (density_grid shows clusters with 12 nodes and empty cells)",\n    "convergence": "Early stage (0.0 convergence, recent best costs fluctuating)",\n    "diversity": "High (0.91)"\n  },\n  "difficult_regions": [\n    {"region": [20, 29, 19, 42, 55, 40], "reason": "High cost (13258) + spatial spread"},\n    {"region": [15, 43, 13, 46, 21], "reason": "High cost (12537) + overlaps with low-density grid cells"},\n    {"region": [26, 15, 29, 21, 40], "reason": "High cost (11421) + directional bias in sector histogram"}\n  ],\n  "opportunity_regions": [\n    {"potential_nodes": [28, 27, 29], "reason": "Frequent subpath (0.3 freq) in high-density grid cell"},\n    {"potential_edges": [(40, 46)], "reason": "High-quality edge (0.5 freq, avg_cost 28)"}\n  ],\n  "evolution_phase": "Exploration (high diversity, no elites, early iteration)",\n  "evolution_direction": [\n    "Operator suggestions:",\n    "1. Edge recombination focusing on high-frequency subpaths (e.g. 28-27-29)",\n    "2. Local search intensification around node 40 (anchor of high-quality edge)",\n    "3. Adaptive mutation rates targeting sparse grid cells",\n    "4. Direction-aware crossover (sector histogram shows bias)"\n  ]\n}\n```'}
2025-06-26 19:07:24,340 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:07:24,340 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:07:24,340 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.5
  • best_cost: 9930.0
  • mean_cost: 71402.7
- Search-space: ruggedness 0.5, modality unknown, deceptiveness unknown
- Evolution phase: unknown
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:07:24,340 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:07:24,340 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:07:35,167 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Moderate diversity and balanced focus suggest prioritizing exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 19:07:35,175 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:07:35,176 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:07:35,176 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Moderate diversity and balanced focus suggest prioritizing exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 19:07:35,176 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:07:35,176 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:07:35,176 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Moderate diversity and balanced focus suggest prioritizing exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 19:07:35,176 - __main__ - INFO - 开始进化阶段
2025-06-26 19:07:35,176 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:07:35,176 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:07:35,176 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9930.0 (rank 0.0%)
- Population diversity: 0.7271186440677966
- Evolution phase: unknown
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:07:35,178 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:07:35,178 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:07:48,328 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59],
  "targeted_regions": "None specified",
  "strategy_comment": "Initial exploration with sequential path"
}
```
2025-06-26 19:07:48,328 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:07:48,328 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13233.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59]
2025-06-26 19:07:48,328 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}
2025-06-26 19:07:48,328 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:07:48,328 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:07:48,328 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9978.0 (rank 10.0%)
- Population diversity: 0.7864406779661017
- Evolution phase: unknown
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:07:48,328 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:07:48,328 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:08:01,765 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "Unexplored sparse cells via modular spacing",
  "strategy_comment": "Modular node spacing to explore sparse regions"
}
```
2025-06-26 19:08:01,765 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:08:01,765 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:08:01,765 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:08:01,765 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:08:01,765 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:08:01,765 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9940.0 (rank 0.0%)
- Population diversity: 0.8559322033898306
- Evolution phase: unknown
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:08:01,765 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:08:01,765 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:08:14,926 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59],
  "targeted_regions": "No specific regions targeted",
  "strategy_comment": "Initial exploration with minimal cost and maximum diversity"
}
```
2025-06-26 19:08:14,926 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:08:14,926 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13233.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59]
2025-06-26 19:08:14,926 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}
2025-06-26 19:08:14,926 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:08:14,926 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:08:14,926 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98531.0 (rank 70.0%)
- Population diversity: 0.7864406779661017
- Evolution phase: unknown
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:08:14,926 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:08:14,934 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:08:28,521 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59],
  "targeted_regions": "Unexplored sparse cells",
  "strategy_comment": "Sequential exploration to maximize coverage"
}
```
2025-06-26 19:08:28,521 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:08:28,521 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13233.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59]
2025-06-26 19:08:28,521 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}
2025-06-26 19:08:28,521 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:08:28,521 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:08:28,524 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 96712.0 (rank 50.0%)
- Population diversity: 0.7152542372881356
- Evolution phase: unknown
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:08:28,524 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:08:28,524 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:08:41,115 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59],
  "targeted_regions": "Unexplored sparse cells",
  "strategy_comment": "Reverse default path to explore new regions"
}
```
2025-06-26 19:08:41,115 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:08:41,115 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13233.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59]
2025-06-26 19:08:41,115 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}
2025-06-26 19:08:41,115 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:08:41,115 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:08:41,115 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 107186.0 (rank 80.0%)
- Population diversity: 0.6322033898305085
- Evolution phase: unknown
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:08:41,115 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:08:41,115 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:08:54,748 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 59, 55, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 56, 51, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 17, 23, 29, 35, 41, 47, 53, 58, 52, 46, 40, 34, 28, 22, 16, 57],
  "targeted_regions": "Unexplored sparse cells",
  "strategy_comment": "Spiral pattern to cover sparse regions"
}
```
2025-06-26 19:08:54,748 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:08:54,748 - ExplorationExpert - INFO - 探索路径生成完成，成本: 57001.0, 路径: [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 59, 55, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 56, 51, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 17, 23, 29, 35, 41, 47, 53, 58, 52, 46, 40, 34, 28, 22, 16, 57]
2025-06-26 19:08:54,748 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 59, 55, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 56, 51, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 17, 23, 29, 35, 41, 47, 53, 58, 52, 46, 40, 34, 28, 22, 16, 57], 'cur_cost': 57001.0}
2025-06-26 19:08:54,748 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 19:08:54,748 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:08:54,748 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:08:54,748 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 100477.0
2025-06-26 19:08:55,249 - ExploitationExpert - INFO - res_population_num: 26
2025-06-26 19:08:55,249 - ExploitationExpert - INFO - res_population_costs: [9654, 9642, 9620, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-26 19:08:55,249 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-06-26 19:08:55,261 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:08:55,261 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 59, 55, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 56, 51, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 17, 23, 29, 35, 41, 47, 53, 58, 52, 46, 40, 34, 28, 22, 16, 57], 'cur_cost': 57001.0}, {'tour': array([54,  3, 32, 49, 18, 30,  7,  1,  6, 31,  0, 52, 57, 36, 10, 45, 14,
       53, 47, 29, 42, 41, 43, 28, 44, 38, 23,  2, 17,  9, 12, 40, 25, 37,
       55, 46, 24, 21, 35, 59, 27, 13, 48, 15, 39, 34, 26,  4, 56, 11, 33,
       20, 58,  5, 22,  8, 16, 50, 19, 51]), 'cur_cost': 100477.0}, {'tour': [8, 53, 54, 55, 35, 49, 13, 34, 45, 27, 44, 19, 6, 41, 24, 15, 1, 36, 18, 3, 2, 42, 43, 57, 9, 32, 29, 59, 33, 31, 47, 22, 25, 39, 5, 16, 11, 4, 10, 40, 26, 14, 48, 20, 0, 28, 46, 58, 30, 56, 52, 7, 37, 50, 38, 51, 21, 17, 23, 12], 'cur_cost': 97756.0}, {'tour': [0, 9, 55, 6, 18, 32, 36, 3, 49, 47, 58, 59, 42, 57, 25, 38, 20, 14, 16, 12, 39, 19, 53, 22, 54, 10, 48, 31, 11, 13, 7, 43, 1, 27, 26, 15, 29, 21, 40, 45, 46, 44, 35, 2, 24, 17, 51, 37, 23, 5, 4, 8, 33, 30, 34, 52, 28, 50, 41, 56], 'cur_cost': 95745.0}, {'tour': [56, 55, 25, 29, 58, 43, 52, 47, 35, 45, 50, 33, 39, 15, 22, 3, 28, 26, 42, 36, 8, 34, 5, 20, 27, 51, 12, 30, 53, 19, 59, 10, 11, 54, 57, 0, 7, 23, 49, 31, 9, 4, 14, 16, 44, 37, 13, 17, 21, 2, 46, 40, 32, 1, 6, 48, 24, 18, 41, 38], 'cur_cost': 90209.0}]
2025-06-26 19:08:55,262 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:08:55,262 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 299, 'skip_rate': 0.04013377926421405, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 287, 'cache_hits': 186, 'similarity_calculations': 4223, 'cache_hit_rate': 0.044044518115084065, 'cache_size': 4037}}
2025-06-26 19:08:55,262 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 19:08:55,263 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:08:55,263 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:08:55,263 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:08:55,263 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 99536.0
2025-06-26 19:08:55,766 - ExploitationExpert - INFO - res_population_num: 35
2025-06-26 19:08:55,766 - ExploitationExpert - INFO - res_population_costs: [9654, 9642, 9620, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-26 19:08:55,766 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64)]
2025-06-26 19:08:55,785 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:08:55,785 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 59, 55, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 56, 51, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 17, 23, 29, 35, 41, 47, 53, 58, 52, 46, 40, 34, 28, 22, 16, 57], 'cur_cost': 57001.0}, {'tour': array([54,  3, 32, 49, 18, 30,  7,  1,  6, 31,  0, 52, 57, 36, 10, 45, 14,
       53, 47, 29, 42, 41, 43, 28, 44, 38, 23,  2, 17,  9, 12, 40, 25, 37,
       55, 46, 24, 21, 35, 59, 27, 13, 48, 15, 39, 34, 26,  4, 56, 11, 33,
       20, 58,  5, 22,  8, 16, 50, 19, 51]), 'cur_cost': 100477.0}, {'tour': array([11, 25,  0, 26, 18, 15,  1, 36, 34, 14, 55, 30, 12, 56, 53, 40, 54,
       39, 57, 49, 23, 46, 44, 32,  4, 52, 42, 33,  8,  3, 37,  7, 45, 28,
       27, 50, 38, 22, 43, 24, 58, 17, 29, 13, 59,  5, 47, 31, 41,  2, 19,
       21,  9, 48, 20,  6, 35, 10, 51, 16]), 'cur_cost': 99536.0}, {'tour': [0, 9, 55, 6, 18, 32, 36, 3, 49, 47, 58, 59, 42, 57, 25, 38, 20, 14, 16, 12, 39, 19, 53, 22, 54, 10, 48, 31, 11, 13, 7, 43, 1, 27, 26, 15, 29, 21, 40, 45, 46, 44, 35, 2, 24, 17, 51, 37, 23, 5, 4, 8, 33, 30, 34, 52, 28, 50, 41, 56], 'cur_cost': 95745.0}, {'tour': [56, 55, 25, 29, 58, 43, 52, 47, 35, 45, 50, 33, 39, 15, 22, 3, 28, 26, 42, 36, 8, 34, 5, 20, 27, 51, 12, 30, 53, 19, 59, 10, 11, 54, 57, 0, 7, 23, 49, 31, 9, 4, 14, 16, 44, 37, 13, 17, 21, 2, 46, 40, 32, 1, 6, 48, 24, 18, 41, 38], 'cur_cost': 90209.0}]
2025-06-26 19:08:55,786 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:08:55,787 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 300, 'skip_rate': 0.04, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 288, 'cache_hits': 186, 'similarity_calculations': 4224, 'cache_hit_rate': 0.04403409090909091, 'cache_size': 4038}}
2025-06-26 19:08:55,788 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:08:55,788 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:08:55,788 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:08:55,788 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:08:55,789 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 93842.0
2025-06-26 19:08:56,292 - ExploitationExpert - INFO - res_population_num: 36
2025-06-26 19:08:56,292 - ExploitationExpert - INFO - res_population_costs: [9654, 9642, 9620, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-26 19:08:56,293 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-06-26 19:08:56,307 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:08:56,308 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 59, 55, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 56, 51, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 17, 23, 29, 35, 41, 47, 53, 58, 52, 46, 40, 34, 28, 22, 16, 57], 'cur_cost': 57001.0}, {'tour': array([54,  3, 32, 49, 18, 30,  7,  1,  6, 31,  0, 52, 57, 36, 10, 45, 14,
       53, 47, 29, 42, 41, 43, 28, 44, 38, 23,  2, 17,  9, 12, 40, 25, 37,
       55, 46, 24, 21, 35, 59, 27, 13, 48, 15, 39, 34, 26,  4, 56, 11, 33,
       20, 58,  5, 22,  8, 16, 50, 19, 51]), 'cur_cost': 100477.0}, {'tour': array([11, 25,  0, 26, 18, 15,  1, 36, 34, 14, 55, 30, 12, 56, 53, 40, 54,
       39, 57, 49, 23, 46, 44, 32,  4, 52, 42, 33,  8,  3, 37,  7, 45, 28,
       27, 50, 38, 22, 43, 24, 58, 17, 29, 13, 59,  5, 47, 31, 41,  2, 19,
       21,  9, 48, 20,  6, 35, 10, 51, 16]), 'cur_cost': 99536.0}, {'tour': array([31, 20, 37,  0, 54, 46, 45, 59,  6, 34,  9, 16, 23, 19, 21, 49, 41,
       28, 52, 33, 55, 15,  5, 10, 26, 42, 51,  7, 22, 36, 30, 38, 48, 50,
       53, 18, 57, 24, 47, 12,  8, 25, 43, 40, 27,  3, 56, 17, 44, 39, 29,
       32,  2,  1, 35, 13, 11, 14, 58,  4]), 'cur_cost': 93842.0}, {'tour': [56, 55, 25, 29, 58, 43, 52, 47, 35, 45, 50, 33, 39, 15, 22, 3, 28, 26, 42, 36, 8, 34, 5, 20, 27, 51, 12, 30, 53, 19, 59, 10, 11, 54, 57, 0, 7, 23, 49, 31, 9, 4, 14, 16, 44, 37, 13, 17, 21, 2, 46, 40, 32, 1, 6, 48, 24, 18, 41, 38], 'cur_cost': 90209.0}]
2025-06-26 19:08:56,309 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:08:56,311 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 301, 'skip_rate': 0.03986710963455149, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 289, 'cache_hits': 186, 'similarity_calculations': 4226, 'cache_hit_rate': 0.04401325130146711, 'cache_size': 4040}}
2025-06-26 19:08:56,311 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:08:56,311 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:08:56,311 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:08:56,311 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:08:56,312 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 97351.0
2025-06-26 19:08:56,813 - ExploitationExpert - INFO - res_population_num: 37
2025-06-26 19:08:56,813 - ExploitationExpert - INFO - res_population_costs: [9654, 9642, 9620, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-26 19:08:56,815 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64)]
2025-06-26 19:08:56,830 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:08:56,830 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 59, 55, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 56, 51, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 17, 23, 29, 35, 41, 47, 53, 58, 52, 46, 40, 34, 28, 22, 16, 57], 'cur_cost': 57001.0}, {'tour': array([54,  3, 32, 49, 18, 30,  7,  1,  6, 31,  0, 52, 57, 36, 10, 45, 14,
       53, 47, 29, 42, 41, 43, 28, 44, 38, 23,  2, 17,  9, 12, 40, 25, 37,
       55, 46, 24, 21, 35, 59, 27, 13, 48, 15, 39, 34, 26,  4, 56, 11, 33,
       20, 58,  5, 22,  8, 16, 50, 19, 51]), 'cur_cost': 100477.0}, {'tour': array([11, 25,  0, 26, 18, 15,  1, 36, 34, 14, 55, 30, 12, 56, 53, 40, 54,
       39, 57, 49, 23, 46, 44, 32,  4, 52, 42, 33,  8,  3, 37,  7, 45, 28,
       27, 50, 38, 22, 43, 24, 58, 17, 29, 13, 59,  5, 47, 31, 41,  2, 19,
       21,  9, 48, 20,  6, 35, 10, 51, 16]), 'cur_cost': 99536.0}, {'tour': array([31, 20, 37,  0, 54, 46, 45, 59,  6, 34,  9, 16, 23, 19, 21, 49, 41,
       28, 52, 33, 55, 15,  5, 10, 26, 42, 51,  7, 22, 36, 30, 38, 48, 50,
       53, 18, 57, 24, 47, 12,  8, 25, 43, 40, 27,  3, 56, 17, 44, 39, 29,
       32,  2,  1, 35, 13, 11, 14, 58,  4]), 'cur_cost': 93842.0}, {'tour': array([24,  1, 23, 22, 55, 52, 28,  8, 54,  7,  2, 29, 59, 18, 34,  4, 39,
        0, 25, 53, 36, 15,  6,  3, 58, 16, 42, 32, 19, 56, 49, 51, 57, 26,
       38,  5, 43, 37, 44, 13, 17, 33, 41, 46, 30, 10, 31, 11,  9, 50, 20,
       45, 12, 27, 40, 21, 47, 48, 35, 14]), 'cur_cost': 97351.0}]
2025-06-26 19:08:56,834 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:08:56,834 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 302, 'skip_rate': 0.039735099337748346, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 290, 'cache_hits': 186, 'similarity_calculations': 4229, 'cache_hit_rate': 0.04398202884842752, 'cache_size': 4043}}
2025-06-26 19:08:56,834 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:08:56,835 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 59, 55, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 56, 51, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 17, 23, 29, 35, 41, 47, 53, 58, 52, 46, 40, 34, 28, 22, 16, 57], 'cur_cost': 57001.0}}]
2025-06-26 19:08:56,835 - __main__ - INFO - 进化阶段完成
2025-06-26 19:08:56,835 - __main__ - INFO - 开始评估阶段
2025-06-26 19:08:56,835 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:08:56,836 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:08:56,836 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9930.0 → 13233.0 (Δ -3303.0)
- Mean cost: 71402.7 → 62723.3
- Diversity: 0.727 → 0.663 (Δ -0.0644)

#### Elite metrics
- Elite count: 0 → 37
- Best elite cost: 9930.0 → 9614 (Δ 316.0)

#### Strategy performance
- Explore: 6 inds, success 50.0% , avg Δ 16041.67
- Exploit: 4 inds, success 25.0% , avg Δ -2364.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [26245.0, 33711.0, 26245.0], diversity [0.8873822975517892, 0.9491525423728815, 0.9532956685499059]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:08:56,837 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:09:09,574 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_delta": 16041.67,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -2364.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore_effective_but_diversity_decreasing",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_ratio": 0.4,
    "additional_suggestions": "Consider increasing the population size to enhance diversity and explore more potential solutions."
  }
}
```
2025-06-26 19:09:09,600 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:09:09,600 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_delta": 16041.67,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -2364.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore_effective_but_diversity_decreasing",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_ratio": 0.4,
    "additional_suggestions": "Consider increasing the population size to enhance diversity and explore more potential solutions."
  }
}
```
2025-06-26 19:09:09,600 - __main__ - INFO - 评估阶段完成
2025-06-26 19:09:09,600 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_delta": 16041.67,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -2364.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore_effective_but_diversity_decreasing",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_ratio": 0.4,
    "additional_suggestions": "Consider increasing the population size to enhance diversity and explore more potential solutions."
  }
}
```
2025-06-26 19:09:09,600 - __main__ - INFO - 当前最佳适应度: 13233.0
2025-06-26 19:09:09,600 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite12_60_route_0.pkl
2025-06-26 19:09:09,600 - __main__ - INFO - composite12_60 开始进化第 2 代
2025-06-26 19:09:09,600 - __main__ - INFO - 开始分析阶段
2025-06-26 19:09:09,600 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:09:09,618 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 13233.0, 'max': 126094.0, 'mean': 62723.3, 'std': 43351.721250833856}, 'diversity': 0.8503703703703703, 'clusters': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:09:09,618 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 13233.0, 'max': 126094.0, 'mean': 62723.3, 'std': 43351.721250833856}, 'diversity_level': 0.8503703703703703, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[1380, 1590], [1386, 1610], [1446, 1664], [1473, 1651], [1403, 1573], [1429, 1576], [1414, 1596], [1421, 1664], [1385, 1662], [1475, 1616], [1405, 1625], [1439, 1639], [3242, 2428], [3193, 2490], [3212, 2461], [3238, 2498], [3270, 2452], [3236, 2513], [3196, 2449], [3220, 2481], [3262, 2465], [3198, 2468], [3267, 2481], [3264, 2442], [426, 2488], [478, 2496], [499, 2467], [445, 2489], [451, 2481], [441, 2477], [461, 2463], [511, 2487], [499, 2478], [431, 2471], [461, 2456], [476, 2486], [832, 400], [885, 348], [838, 364], [921, 379], [900, 346], [918, 411], [855, 361], [866, 388], [878, 327], [841, 380], [902, 374], [871, 361], [2763, 1531], [2817, 1507], [2789, 1453], [2739, 1464], [2773, 1490], [2783, 1514], [2808, 1535], [2797, 1510], [2807, 1517], [2793, 1495], [2828, 1490], [2736, 1497]], 'distance_matrix': array([[   0.,   21.,   99., ..., 1416., 1451., 1359.],
       [  21.,    0.,   81., ..., 1412., 1447., 1355.],
       [  99.,   81.,    0., ..., 1358., 1393., 1301.],
       ...,
       [1416., 1412., 1358., ...,    0.,   35.,   57.],
       [1451., 1447., 1393., ...,   35.,    0.,   92.],
       [1359., 1355., 1301., ...,   57.,   92.,    0.]])}
2025-06-26 19:09:09,621 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:09:09,621 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:09:09,621 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:09:09,623 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 19:09:09,624 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:09:09,624 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 19:09:09,624 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:09:09,624 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:09:09,624 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:09:09,644 - EliteExpert - INFO - 精英解分析完成
2025-06-26 19:09:09,644 - __main__ - INFO - 精英专家分析报告: {'elite_count': 37, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 3619.0, 'avg_gap': 53107.3}, 'structure_gap': {'unique_elite_edges': 150, 'unique_pop_edges': 381, 'common_edges': 30}}, 'elite_diversity': {'diversity_score': 0.15925925925925932}}
2025-06-26 19:09:09,645 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:09:09,645 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:09:09,647 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:09:09,647 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 13233.0, mean 62723.3, max 126094.0, std 43351.721250833856
- diversity: 0.8503703703703703
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [33711.0, 26245.0, 13233.0] (Δ 10239.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [426.0, 327.0, 3270.0, 2513.0]
- centroid/spread: [1756.217, 1687.917] | rms 1327.809
- density_grid(3x3): [[12, 0, 0], [0, 12, 12], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [7.0, 1094.25, 1427.5, 2273.75, 3222.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 7, 6, 9, 12, 8, 4, 7]
- nn_median_dist: 17.242

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:09:09,648 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:09:31,885 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 7.0 to 3222.0, Q3 at 2273.75 suggests significant variability)",
    "modality": "Potentially multimodal (direction histogram shows varied edge orientations, no single dominant direction)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 indicates some very long edges that may mislead local search)"
  },
  "population_state": {
    "coverage": "Good initial diversity (0.85) but uneven spatial distribution (density_grid shows clusters with 12 nodes and empty cells)",
    "convergence": "Early stage (0.0 convergence, recent best costs show improvement but no stagnation)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges > 2273.75 length, particularly in sparse grid cells)",
    "Low-density areas (empty cells in density_grid, possibly under-explored regions)"
  ],
  "opportunity_regions": [
    "High-density cells (grid cells with 12 nodes, likely containing promising node clusters)",
    "Regions near centroid (1756.217, 1687.917) with median edge lengths ~1427.5"
  ],
  "evolution_phase": "Exploration (high diversity, no convergence, improving but unstable best costs)",
  "evolution_direction": [
    "Prioritize exploitation in high-density cells (intensify search around nodes in populated grid cells)",
    "Target long-edge reduction (use 2-opt or edge-recombination operators on longest edges)",
    "Maintain diversity (use restart mechanisms or niching in low-density grid cells)",
    "Monitor direction histogram (balance edge orientations to avoid directional bias)"
  ]
}
```
2025-06-26 19:09:31,885 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:09:31,885 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 7.0 to 3222.0, Q3 at 2273.75 suggests significant variability)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations, no single dominant direction)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 indicates some very long edges that may mislead local search)'}, 'population_state': {'coverage': 'Good initial diversity (0.85) but uneven spatial distribution (density_grid shows clusters with 12 nodes and empty cells)', 'convergence': 'Early stage (0.0 convergence, recent best costs show improvement but no stagnation)'}, 'difficult_regions': ['Long-edge corridors (edges > 2273.75 length, particularly in sparse grid cells)', 'Low-density areas (empty cells in density_grid, possibly under-explored regions)'], 'opportunity_regions': ['High-density cells (grid cells with 12 nodes, likely containing promising node clusters)', 'Regions near centroid (1756.217, 1687.917) with median edge lengths ~1427.5'], 'evolution_phase': 'Exploration (high diversity, no convergence, improving but unstable best costs)', 'evolution_direction': ['Prioritize exploitation in high-density cells (intensify search around nodes in populated grid cells)', 'Target long-edge reduction (use 2-opt or edge-recombination operators on longest edges)', 'Maintain diversity (use restart mechanisms or niching in low-density grid cells)', 'Monitor direction histogram (balance edge orientations to avoid directional bias)']}
2025-06-26 19:09:31,885 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:09:31,885 - __main__ - INFO - 分析阶段完成
2025-06-26 19:09:31,885 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 7.0 to 3222.0, Q3 at 2273.75 suggests significant variability)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations, no single dominant direction)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 indicates some very long edges that may mislead local search)'}, 'population_state': {'coverage': 'Good initial diversity (0.85) but uneven spatial distribution (density_grid shows clusters with 12 nodes and empty cells)', 'convergence': 'Early stage (0.0 convergence, recent best costs show improvement but no stagnation)'}, 'difficult_regions': ['Long-edge corridors (edges > 2273.75 length, particularly in sparse grid cells)', 'Low-density areas (empty cells in density_grid, possibly under-explored regions)'], 'opportunity_regions': ['High-density cells (grid cells with 12 nodes, likely containing promising node clusters)', 'Regions near centroid (1756.217, 1687.917) with median edge lengths ~1427.5'], 'evolution_phase': 'Exploration (high diversity, no convergence, improving but unstable best costs)', 'evolution_direction': ['Prioritize exploitation in high-density cells (intensify search around nodes in populated grid cells)', 'Target long-edge reduction (use 2-opt or edge-recombination operators on longest edges)', 'Maintain diversity (use restart mechanisms or niching in low-density grid cells)', 'Monitor direction histogram (balance edge orientations to avoid directional bias)']}
2025-06-26 19:09:31,885 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:09:31,885 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:09:31,885 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 13233.0
  • mean_cost: 62723.3
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 7.0 to 3222.0, Q3 at 2273.75 suggests significant variability), modality Potentially multimodal (direction histogram shows varied edge orientations, no single dominant direction), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 indicates some very long edges that may mislead local search)
- Evolution phase: Exploration (high diversity, no convergence, improving but unstable best costs)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Low-density areas (e
- Opportunity regions (# 2): High-density cells (, Regions near centroi
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
     ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:09:31,885 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:09:31,890 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:09:41,371 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and moderate ruggedness favors exploration (70%). Top 60% explore to diversify; bottom 40% exploit promising regions."
}
```
2025-06-26 19:09:41,371 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:09:41,371 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:09:41,371 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and moderate ruggedness favors exploration (70%). Top 60% explore to diversify; bottom 40% exploit promising regions."
}
```
2025-06-26 19:09:41,371 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:09:41,371 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:09:41,371 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and moderate ruggedness favors exploration (70%). Top 60% explore to diversify; bottom 40% exploit promising regions."
}
```
2025-06-26 19:09:41,371 - __main__ - INFO - 开始进化阶段
2025-06-26 19:09:41,371 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:09:41,371 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:09:41,371 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13233.0 (rank 0.0%)
- Population diversity: 0.6627118644067796
- Evolution phase: Exploration (high diversity, no convergence, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:09:41,371 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:09:41,371 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:09:54,852 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 19:09:54,852 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:09:54,856 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:09:54,856 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:09:54,856 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:09:54,856 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:09:54,857 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 80.0%)
- Population diversity: 0.6627118644067796
- Evolution phase: Exploration (high diversity, no convergence, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:09:54,857 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:09:54,857 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:10:08,019 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Structured sweep through opportunity regions, alternating clusters"
}
```
2025-06-26 19:10:08,019 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:10:08,019 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:10:08,019 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:10:08,019 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:10:08,019 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:10:08,019 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13233.0 (rank 0.0%)
- Population diversity: 0.6627118644067796
- Evolution phase: Exploration (high diversity, no convergence, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:10:08,019 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:10:08,019 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:10:21,430 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Structured spiral through opportunity regions"
}
```
2025-06-26 19:10:21,430 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:10:21,430 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:10:21,430 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:10:21,430 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:10:21,430 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:10:21,430 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13233.0 (rank 0.0%)
- Population diversity: 0.6627118644067796
- Evolution phase: Exploration (high diversity, no convergence, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:10:21,430 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:10:21,430 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:10:33,648 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce and adjacent low-density cells",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 19:10:33,649 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:10:33,649 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:10:33,649 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:10:33,649 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:10:33,649 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:10:33,651 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13233.0 (rank 0.0%)
- Population diversity: 0.6627118644067796
- Evolution phase: Exploration (high diversity, no convergence, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:10:33,651 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:10:33,651 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:10:46,863 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 19:10:46,863 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:10:46,863 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:10:46,863 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:10:46,863 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:10:46,863 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:10:46,863 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 57001.0 (rank 0.0%)
- Population diversity: 0.5728813559322034
- Evolution phase: Exploration (high diversity, no convergence, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Regions near ce
- Difficult regions to avoid (sample): Long-edge corri, Low-density are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:10:46,863 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:10:46,863 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:11:00,356 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 19:11:00,356 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:11:00,356 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:11:00,359 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:11:00,359 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 19:11:00,359 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:11:00,359 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:11:00,360 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 95472.0
2025-06-26 19:11:00,862 - ExploitationExpert - INFO - res_population_num: 37
2025-06-26 19:11:00,862 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:11:00,862 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:11:00,879 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:11:00,879 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': array([47, 38, 37, 10, 29, 12,  5, 51, 19, 53, 46, 59, 41,  2, 21, 30, 52,
       43, 27,  4,  7, 54, 34, 22, 18, 16, 17, 14, 28, 58, 24, 31, 39,  0,
       33,  9, 20,  6, 40, 13, 44, 25, 55, 26,  8, 15, 48, 49, 32, 11, 50,
       56, 45, 35, 57, 23, 42,  1,  3, 36]), 'cur_cost': 95472.0}, {'tour': array([11, 25,  0, 26, 18, 15,  1, 36, 34, 14, 55, 30, 12, 56, 53, 40, 54,
       39, 57, 49, 23, 46, 44, 32,  4, 52, 42, 33,  8,  3, 37,  7, 45, 28,
       27, 50, 38, 22, 43, 24, 58, 17, 29, 13, 59,  5, 47, 31, 41,  2, 19,
       21,  9, 48, 20,  6, 35, 10, 51, 16]), 'cur_cost': 99536.0}, {'tour': array([31, 20, 37,  0, 54, 46, 45, 59,  6, 34,  9, 16, 23, 19, 21, 49, 41,
       28, 52, 33, 55, 15,  5, 10, 26, 42, 51,  7, 22, 36, 30, 38, 48, 50,
       53, 18, 57, 24, 47, 12,  8, 25, 43, 40, 27,  3, 56, 17, 44, 39, 29,
       32,  2,  1, 35, 13, 11, 14, 58,  4]), 'cur_cost': 93842.0}, {'tour': array([24,  1, 23, 22, 55, 52, 28,  8, 54,  7,  2, 29, 59, 18, 34,  4, 39,
        0, 25, 53, 36, 15,  6,  3, 58, 16, 42, 32, 19, 56, 49, 51, 57, 26,
       38,  5, 43, 37, 44, 13, 17, 33, 41, 46, 30, 10, 31, 11,  9, 50, 20,
       45, 12, 27, 40, 21, 47, 48, 35, 14]), 'cur_cost': 97351.0}]
2025-06-26 19:11:00,881 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:11:00,881 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 303, 'skip_rate': 0.039603960396039604, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 291, 'cache_hits': 186, 'similarity_calculations': 4233, 'cache_hit_rate': 0.04394046775336641, 'cache_size': 4047}}
2025-06-26 19:11:00,881 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 19:11:00,881 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:11:00,881 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:11:00,883 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:11:00,883 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 100094.0
2025-06-26 19:11:01,386 - ExploitationExpert - INFO - res_population_num: 37
2025-06-26 19:11:01,386 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:11:01,386 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:11:01,403 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:11:01,404 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': array([47, 38, 37, 10, 29, 12,  5, 51, 19, 53, 46, 59, 41,  2, 21, 30, 52,
       43, 27,  4,  7, 54, 34, 22, 18, 16, 17, 14, 28, 58, 24, 31, 39,  0,
       33,  9, 20,  6, 40, 13, 44, 25, 55, 26,  8, 15, 48, 49, 32, 11, 50,
       56, 45, 35, 57, 23, 42,  1,  3, 36]), 'cur_cost': 95472.0}, {'tour': array([53,  3, 17, 27, 32, 29, 52, 36, 39, 51, 55,  4,  0, 16, 30, 22, 11,
       37, 58,  2, 31, 56, 50, 24, 48,  1, 20,  7, 38,  6, 49, 54, 23, 34,
        5, 43,  8, 33, 40,  9, 59, 25, 46, 26, 18, 47, 15, 10, 28, 41, 42,
       35, 14, 13, 57, 45, 12, 21, 19, 44]), 'cur_cost': 100094.0}, {'tour': array([31, 20, 37,  0, 54, 46, 45, 59,  6, 34,  9, 16, 23, 19, 21, 49, 41,
       28, 52, 33, 55, 15,  5, 10, 26, 42, 51,  7, 22, 36, 30, 38, 48, 50,
       53, 18, 57, 24, 47, 12,  8, 25, 43, 40, 27,  3, 56, 17, 44, 39, 29,
       32,  2,  1, 35, 13, 11, 14, 58,  4]), 'cur_cost': 93842.0}, {'tour': array([24,  1, 23, 22, 55, 52, 28,  8, 54,  7,  2, 29, 59, 18, 34,  4, 39,
        0, 25, 53, 36, 15,  6,  3, 58, 16, 42, 32, 19, 56, 49, 51, 57, 26,
       38,  5, 43, 37, 44, 13, 17, 33, 41, 46, 30, 10, 31, 11,  9, 50, 20,
       45, 12, 27, 40, 21, 47, 48, 35, 14]), 'cur_cost': 97351.0}]
2025-06-26 19:11:01,408 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:11:01,409 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 304, 'skip_rate': 0.039473684210526314, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 292, 'cache_hits': 186, 'similarity_calculations': 4238, 'cache_hit_rate': 0.0438886267107126, 'cache_size': 4052}}
2025-06-26 19:11:01,409 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:11:01,409 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:11:01,409 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:11:01,409 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:11:01,411 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 91692.0
2025-06-26 19:11:01,912 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:11:01,912 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654, 9614, 9614]
2025-06-26 19:11:01,912 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64)]
2025-06-26 19:11:01,929 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:11:01,929 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': array([47, 38, 37, 10, 29, 12,  5, 51, 19, 53, 46, 59, 41,  2, 21, 30, 52,
       43, 27,  4,  7, 54, 34, 22, 18, 16, 17, 14, 28, 58, 24, 31, 39,  0,
       33,  9, 20,  6, 40, 13, 44, 25, 55, 26,  8, 15, 48, 49, 32, 11, 50,
       56, 45, 35, 57, 23, 42,  1,  3, 36]), 'cur_cost': 95472.0}, {'tour': array([53,  3, 17, 27, 32, 29, 52, 36, 39, 51, 55,  4,  0, 16, 30, 22, 11,
       37, 58,  2, 31, 56, 50, 24, 48,  1, 20,  7, 38,  6, 49, 54, 23, 34,
        5, 43,  8, 33, 40,  9, 59, 25, 46, 26, 18, 47, 15, 10, 28, 41, 42,
       35, 14, 13, 57, 45, 12, 21, 19, 44]), 'cur_cost': 100094.0}, {'tour': array([25, 39, 35, 56, 28, 24, 15,  7,  9,  1,  0, 12,  5, 37, 11, 45, 18,
       32, 34,  8, 55, 49,  2, 23, 21, 50, 10, 46, 16, 58, 48,  4, 53, 47,
       59, 36, 14, 52, 17, 13, 51, 30, 22, 41, 20, 40,  6, 29, 33, 26, 27,
       42, 38, 57, 44, 43, 31, 54, 19,  3]), 'cur_cost': 91692.0}, {'tour': array([24,  1, 23, 22, 55, 52, 28,  8, 54,  7,  2, 29, 59, 18, 34,  4, 39,
        0, 25, 53, 36, 15,  6,  3, 58, 16, 42, 32, 19, 56, 49, 51, 57, 26,
       38,  5, 43, 37, 44, 13, 17, 33, 41, 46, 30, 10, 31, 11,  9, 50, 20,
       45, 12, 27, 40, 21, 47, 48, 35, 14]), 'cur_cost': 97351.0}]
2025-06-26 19:11:01,930 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:11:01,932 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 305, 'skip_rate': 0.03934426229508197, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 293, 'cache_hits': 186, 'similarity_calculations': 4244, 'cache_hit_rate': 0.04382657869934024, 'cache_size': 4058}}
2025-06-26 19:11:01,932 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:11:01,932 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:11:01,932 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:11:01,932 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:11:01,933 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 93349.0
2025-06-26 19:11:02,435 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:11:02,435 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654, 9614, 9614]
2025-06-26 19:11:02,435 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64)]
2025-06-26 19:11:02,450 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:11:02,453 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': array([47, 38, 37, 10, 29, 12,  5, 51, 19, 53, 46, 59, 41,  2, 21, 30, 52,
       43, 27,  4,  7, 54, 34, 22, 18, 16, 17, 14, 28, 58, 24, 31, 39,  0,
       33,  9, 20,  6, 40, 13, 44, 25, 55, 26,  8, 15, 48, 49, 32, 11, 50,
       56, 45, 35, 57, 23, 42,  1,  3, 36]), 'cur_cost': 95472.0}, {'tour': array([53,  3, 17, 27, 32, 29, 52, 36, 39, 51, 55,  4,  0, 16, 30, 22, 11,
       37, 58,  2, 31, 56, 50, 24, 48,  1, 20,  7, 38,  6, 49, 54, 23, 34,
        5, 43,  8, 33, 40,  9, 59, 25, 46, 26, 18, 47, 15, 10, 28, 41, 42,
       35, 14, 13, 57, 45, 12, 21, 19, 44]), 'cur_cost': 100094.0}, {'tour': array([25, 39, 35, 56, 28, 24, 15,  7,  9,  1,  0, 12,  5, 37, 11, 45, 18,
       32, 34,  8, 55, 49,  2, 23, 21, 50, 10, 46, 16, 58, 48,  4, 53, 47,
       59, 36, 14, 52, 17, 13, 51, 30, 22, 41, 20, 40,  6, 29, 33, 26, 27,
       42, 38, 57, 44, 43, 31, 54, 19,  3]), 'cur_cost': 91692.0}, {'tour': array([46, 49, 57, 43, 44, 40,  4, 28, 27, 11, 20, 38, 21, 55, 41, 52, 51,
       32, 50,  0,  6, 29, 45, 24, 36,  7, 34,  3, 23, 19, 54,  2, 10, 16,
       48,  9, 59, 13, 25, 17,  8, 37,  5, 31,  1, 42, 35, 33, 53, 26, 14,
       30, 39, 18, 47, 56, 58, 22, 15, 12]), 'cur_cost': 93349.0}]
2025-06-26 19:11:02,455 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:11:02,455 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 306, 'skip_rate': 0.0392156862745098, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 294, 'cache_hits': 186, 'similarity_calculations': 4251, 'cache_hit_rate': 0.043754410726887794, 'cache_size': 4065}}
2025-06-26 19:11:02,456 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:11:02,456 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}]
2025-06-26 19:11:02,456 - __main__ - INFO - 进化阶段完成
2025-06-26 19:11:02,456 - __main__ - INFO - 开始评估阶段
2025-06-26 19:11:02,456 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:11:02,457 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:11:02,458 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 13233.0 → 91692.0 (Δ -78459.0)
- Mean cost: 62723.3 → 113717.1
- Diversity: 0.663 → 0.456 (Δ -0.2068)

#### Elite metrics
- Elite count: 37 → 39
- Best elite cost: 9614 → 9614 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 0.0% , avg Δ -86756.17
- Exploit: 4 inds, success 75.0% , avg Δ 2649.75

#### Other indicators
- No-change individuals: 1
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [33711.0, 26245.0, 13233.0], diversity [0.9491525423728815, 0.9532956685499059, 0.8503703703703703]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:11:02,458 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:11:12,437 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "late-stage stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success, large negative Δ)",
    "exploit": "effective (75% success, positive Δ)"
  },
  "balance_state": "over-exploitation (explore success=0%, diversity dropped 31%)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "increase by 20-30%",
    "elite_preservation": "reduce from 39 to 25-30"
  }
}
```
2025-06-26 19:11:12,461 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:11:12,461 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "late-stage stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success, large negative Δ)",
    "exploit": "effective (75% success, positive Δ)"
  },
  "balance_state": "over-exploitation (explore success=0%, diversity dropped 31%)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "increase by 20-30%",
    "elite_preservation": "reduce from 39 to 25-30"
  }
}
```
2025-06-26 19:11:12,462 - __main__ - INFO - 评估阶段完成
2025-06-26 19:11:12,462 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "late-stage stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success, large negative Δ)",
    "exploit": "effective (75% success, positive Δ)"
  },
  "balance_state": "over-exploitation (explore success=0%, diversity dropped 31%)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "increase by 20-30%",
    "elite_preservation": "reduce from 39 to 25-30"
  }
}
```
2025-06-26 19:11:12,462 - __main__ - INFO - 当前最佳适应度: 91692.0
2025-06-26 19:11:12,463 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite12_60_route_1.pkl
2025-06-26 19:11:12,463 - __main__ - INFO - composite12_60 开始进化第 3 代
2025-06-26 19:11:12,463 - __main__ - INFO - 开始分析阶段
2025-06-26 19:11:12,463 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:11:12,478 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 91692.0, 'max': 126094.0, 'mean': 113717.1, 'std': 15289.090806519529}, 'diversity': 0.6329629629629631, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:11:12,479 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 91692.0, 'max': 126094.0, 'mean': 113717.1, 'std': 15289.090806519529}, 'diversity_level': 0.6329629629629631, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'coordinates': [[1380, 1590], [1386, 1610], [1446, 1664], [1473, 1651], [1403, 1573], [1429, 1576], [1414, 1596], [1421, 1664], [1385, 1662], [1475, 1616], [1405, 1625], [1439, 1639], [3242, 2428], [3193, 2490], [3212, 2461], [3238, 2498], [3270, 2452], [3236, 2513], [3196, 2449], [3220, 2481], [3262, 2465], [3198, 2468], [3267, 2481], [3264, 2442], [426, 2488], [478, 2496], [499, 2467], [445, 2489], [451, 2481], [441, 2477], [461, 2463], [511, 2487], [499, 2478], [431, 2471], [461, 2456], [476, 2486], [832, 400], [885, 348], [838, 364], [921, 379], [900, 346], [918, 411], [855, 361], [866, 388], [878, 327], [841, 380], [902, 374], [871, 361], [2763, 1531], [2817, 1507], [2789, 1453], [2739, 1464], [2773, 1490], [2783, 1514], [2808, 1535], [2797, 1510], [2807, 1517], [2793, 1495], [2828, 1490], [2736, 1497]], 'distance_matrix': array([[   0.,   21.,   99., ..., 1416., 1451., 1359.],
       [  21.,    0.,   81., ..., 1412., 1447., 1355.],
       [  99.,   81.,    0., ..., 1358., 1393., 1301.],
       ...,
       [1416., 1412., 1358., ...,    0.,   35.,   57.],
       [1451., 1447., 1393., ...,   35.,    0.,   92.],
       [1359., 1355., 1301., ...,   57.,   92.,    0.]])}
2025-06-26 19:11:12,480 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:11:12,480 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:11:12,480 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:11:12,482 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 19:11:12,482 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:11:12,482 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 19:11:12,482 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:11:12,483 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:11:12,483 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:11:12,504 - EliteExpert - INFO - 精英解分析完成
2025-06-26 19:11:12,504 - __main__ - INFO - 精英专家分析报告: {'elite_count': 39, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 82078.0, 'avg_gap': 104101.20256410257}, 'structure_gap': {'unique_elite_edges': 169, 'unique_pop_edges': 276, 'common_edges': 11}}, 'elite_diversity': {'diversity_score': 0.1571300044984255}}
2025-06-26 19:11:12,504 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:11:12,504 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:11:12,506 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:11:12,506 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 91692.0, mean 113717.1, max 126094.0, std 15289.090806519529
- diversity: 0.6329629629629631
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [26245.0, 13233.0, 91692.0] (Δ 6506.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [426.0, 327.0, 3270.0, 2513.0]
- centroid/spread: [1756.217, 1687.917] | rms 1327.809
- density_grid(3x3): [[12, 0, 0], [0, 12, 12], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [7.0, 1094.25, 1427.5, 2273.75, 3222.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 7, 6, 9, 12, 8, 4, 7]
- nn_median_dist: 17.242

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:11:12,506 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:11:33,876 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 7.0 to 3222.0, with significant quartile spreads)",
    "modality": "Potentially multimodal (direction histogram shows varied edge orientations, no single dominant direction)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 suggests some paths may appear optimal but lead to traps)"
  },
  "population_state": {
    "coverage": "Patchy (density_grid shows uneven distribution with some empty cells and clustered nodes)",
    "convergence": "Early stage (diversity 0.63, convergence 0.0, recent best costs show improvement but no stagnation)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges in max quartile 2273.75-3222.0)",
    "Low-density grid cells (empty cells in density_grid)"
  ],
  "opportunity_regions": [
    "High-density cells (grid cells with 12 nodes, especially near centroid)",
    "Short-to-median edges (edges in q1-q3 range 1094.25-2273.75)"
  ],
  "evolution_phase": "Exploration-dominant (high diversity, no clustering, improving but not yet converging)",
  "evolution_direction": [
    "Prioritize edge recombination operators to exploit high-density regions",
    "Apply local optimization (2-opt) on long-edge corridors",
    "Introduce directed mutation toward low-density cells to improve coverage",
    "Maintain diversity via niching or island models given multimodal signals"
  ]
}
```
2025-06-26 19:11:33,876 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:11:33,876 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 7.0 to 3222.0, with significant quartile spreads)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations, no single dominant direction)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some paths may appear optimal but lead to traps)'}, 'population_state': {'coverage': 'Patchy (density_grid shows uneven distribution with some empty cells and clustered nodes)', 'convergence': 'Early stage (diversity 0.63, convergence 0.0, recent best costs show improvement but no stagnation)'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile 2273.75-3222.0)', 'Low-density grid cells (empty cells in density_grid)'], 'opportunity_regions': ['High-density cells (grid cells with 12 nodes, especially near centroid)', 'Short-to-median edges (edges in q1-q3 range 1094.25-2273.75)'], 'evolution_phase': 'Exploration-dominant (high diversity, no clustering, improving but not yet converging)', 'evolution_direction': ['Prioritize edge recombination operators to exploit high-density regions', 'Apply local optimization (2-opt) on long-edge corridors', 'Introduce directed mutation toward low-density cells to improve coverage', 'Maintain diversity via niching or island models given multimodal signals']}
2025-06-26 19:11:33,876 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:11:33,876 - __main__ - INFO - 分析阶段完成
2025-06-26 19:11:33,876 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 7.0 to 3222.0, with significant quartile spreads)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations, no single dominant direction)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some paths may appear optimal but lead to traps)'}, 'population_state': {'coverage': 'Patchy (density_grid shows uneven distribution with some empty cells and clustered nodes)', 'convergence': 'Early stage (diversity 0.63, convergence 0.0, recent best costs show improvement but no stagnation)'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile 2273.75-3222.0)', 'Low-density grid cells (empty cells in density_grid)'], 'opportunity_regions': ['High-density cells (grid cells with 12 nodes, especially near centroid)', 'Short-to-median edges (edges in q1-q3 range 1094.25-2273.75)'], 'evolution_phase': 'Exploration-dominant (high diversity, no clustering, improving but not yet converging)', 'evolution_direction': ['Prioritize edge recombination operators to exploit high-density regions', 'Apply local optimization (2-opt) on long-edge corridors', 'Introduce directed mutation toward low-density cells to improve coverage', 'Maintain diversity via niching or island models given multimodal signals']}
2025-06-26 19:11:33,876 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:11:33,876 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:11:33,876 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 91692.0
  • mean_cost: 113717.1
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 7.0 to 3222.0, with significant quartile spreads), modality Potentially multimodal (direction histogram shows varied edge orientations, no single dominant direction), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 suggests some paths may appear optimal but lead to traps)
- Evolution phase: Exploration-dominant (high diversity, no clustering, improving but not yet converging)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Low-density grid cel
- Opportunity regions (# 2): High-density cells (, Short-to-median edge
- Last-iteration feedback: ```json
{
  "evolution_stage": "late-stage stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% su...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:11:33,876 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:11:33,876 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:11:43,958 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration-dominant phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 19:11:43,958 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:11:43,958 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:11:43,958 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration-dominant phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 19:11:43,958 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:11:43,958 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:11:43,958 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration-dominant phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 19:11:43,958 - __main__ - INFO - 开始进化阶段
2025-06-26 19:11:43,958 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:11:43,958 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:11:43,958 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 40.0%)
- Population diversity: 0.4559322033898305
- Evolution phase: Exploration-dominant (high diversity, no clustering, improving but not yet converging)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-to-median
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:11:43,958 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:11:43,958 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:11:57,445 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 23, 34, 45, 56, 7, 18, 29, 39, 50, 1, 13, 24, 35, 46, 57, 8, 19, 30, 40, 51, 2, 14, 25, 36, 47, 58, 9, 20, 31, 41, 52, 3, 15, 26, 37, 48, 59, 10, 21, 32, 42, 53, 4, 16, 27, 38, 49, 0, 11, 22, 33, 44, 55, 6, 17, 28, 43, 54, 5],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of dense/sparse regions with diagonal jumps"
}
```
2025-06-26 19:11:57,445 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:11:57,445 - ExplorationExpert - INFO - 探索路径生成完成，成本: 115811.0, 路径: [12, 23, 34, 45, 56, 7, 18, 29, 39, 50, 1, 13, 24, 35, 46, 57, 8, 19, 30, 40, 51, 2, 14, 25, 36, 47, 58, 9, 20, 31, 41, 52, 3, 15, 26, 37, 48, 59, 10, 21, 32, 42, 53, 4, 16, 27, 38, 49, 0, 11, 22, 33, 44, 55, 6, 17, 28, 43, 54, 5]
2025-06-26 19:11:57,445 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [12, 23, 34, 45, 56, 7, 18, 29, 39, 50, 1, 13, 24, 35, 46, 57, 8, 19, 30, 40, 51, 2, 14, 25, 36, 47, 58, 9, 20, 31, 41, 52, 3, 15, 26, 37, 48, 59, 10, 21, 32, 42, 53, 4, 16, 27, 38, 49, 0, 11, 22, 33, 44, 55, 6, 17, 28, 43, 54, 5], 'cur_cost': 115811.0}
2025-06-26 19:11:57,445 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:11:57,445 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:11:57,445 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 50.0%)
- Population diversity: 0.5389830508474577
- Evolution phase: Exploration-dominant (high diversity, no clustering, improving but not yet converging)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-to-median
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:11:57,445 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:11:57,445 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:12:11,085 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 19:12:11,093 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:12:11,093 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:12:11,093 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:12:11,093 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:12:11,093 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:12:11,093 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 50.0%)
- Population diversity: 0.5389830508474577
- Evolution phase: Exploration-dominant (high diversity, no clustering, improving but not yet converging)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-to-median
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:12:11,093 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:12:11,093 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:12:24,780 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 7, 45, 23, 56, 8, 19, 37, 52, 14, 41, 3, 29, 48, 25, 59, 16, 33, 5, 47, 21, 38, 10, 54, 27, 42, 1, 36, 50, 15, 44, 20, 58, 9, 31, 53, 18, 39, 6, 49, 24, 57, 11, 35, 22, 46, 4, 30, 55, 17, 40, 2, 28, 51, 13, 43, 26, 0, 32],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of dense/sparse regions with shuffled connectivity"
}
```
2025-06-26 19:12:24,780 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:12:24,780 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118599.0, 路径: [12, 34, 7, 45, 23, 56, 8, 19, 37, 52, 14, 41, 3, 29, 48, 25, 59, 16, 33, 5, 47, 21, 38, 10, 54, 27, 42, 1, 36, 50, 15, 44, 20, 58, 9, 31, 53, 18, 39, 6, 49, 24, 57, 11, 35, 22, 46, 4, 30, 55, 17, 40, 2, 28, 51, 13, 43, 26, 0, 32]
2025-06-26 19:12:24,780 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 52, 14, 41, 3, 29, 48, 25, 59, 16, 33, 5, 47, 21, 38, 10, 54, 27, 42, 1, 36, 50, 15, 44, 20, 58, 9, 31, 53, 18, 39, 6, 49, 24, 57, 11, 35, 22, 46, 4, 30, 55, 17, 40, 2, 28, 51, 13, 43, 26, 0, 32], 'cur_cost': 118599.0}
2025-06-26 19:12:24,780 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:12:24,780 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:12:24,780 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 60.0%)
- Population diversity: 0.6203389830508474
- Evolution phase: Exploration-dominant (high diversity, no clustering, improving but not yet converging)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-to-median
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:12:24,780 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:12:24,780 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:12:38,452 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 48, 15, 29, 41, 5, 52, 18, 33, 9, 44, 26, 58, 13, 36, 1, 47, 21, 54, 10, 39, 24, 50, 6, 30, 42, 16, 55, 3, 27, 49, 14, 38, 22, 57, 11, 35, 20, 53, 4, 28, 43, 17, 59, 0, 25, 46, 31, 51, 32, 40],
  "targeted_regions": "High-density ce, adjacent low-density",
  "strategy_comment": "Prioritize opportunity regions while balancing exploration"
}
```
2025-06-26 19:12:38,452 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:12:38,452 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113042.0, 路径: [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 48, 15, 29, 41, 5, 52, 18, 33, 9, 44, 26, 58, 13, 36, 1, 47, 21, 54, 10, 39, 24, 50, 6, 30, 42, 16, 55, 3, 27, 49, 14, 38, 22, 57, 11, 35, 20, 53, 4, 28, 43, 17, 59, 0, 25, 46, 31, 51, 32, 40]
2025-06-26 19:12:38,452 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 48, 15, 29, 41, 5, 52, 18, 33, 9, 44, 26, 58, 13, 36, 1, 47, 21, 54, 10, 39, 24, 50, 6, 30, 42, 16, 55, 3, 27, 49, 14, 38, 22, 57, 11, 35, 20, 53, 4, 28, 43, 17, 59, 0, 25, 46, 31, 51, 32, 40], 'cur_cost': 113042.0}
2025-06-26 19:12:38,452 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:12:38,455 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:12:38,455 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 70.0%)
- Population diversity: 0.6796610169491526
- Evolution phase: Exploration-dominant (high diversity, no clustering, improving but not yet converging)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-to-median
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:12:38,455 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:12:38,456 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:12:52,388 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 19:12:52,388 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:12:52,388 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:12:52,388 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:12:52,388 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:12:52,388 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:12:52,388 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 70.0%)
- Population diversity: 0.6796610169491526
- Evolution phase: Exploration-dominant (high diversity, no clustering, improving but not yet converging)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-to-median
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:12:52,388 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:12:52,388 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:13:05,983 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 19:13:05,983 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:13:05,985 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:13:05,985 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:13:05,985 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 19:13:05,985 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:13:05,985 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 95472.0 (rank 20.0%)
- Population diversity: 0.6796610169491526
- Evolution phase: Exploration-dominant (high diversity, no clustering, improving but not yet converging)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-to-median
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:13:05,986 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:13:05,986 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:13:20,362 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 19:13:20,367 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:13:20,367 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:13:20,367 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:13:20,367 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:13:20,367 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:13:20,367 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:13:20,367 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 83548.0
2025-06-26 19:13:20,870 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:13:20,871 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:13:20,871 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:13:20,887 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:13:20,887 - ExploitationExpert - INFO - populations: [{'tour': [12, 23, 34, 45, 56, 7, 18, 29, 39, 50, 1, 13, 24, 35, 46, 57, 8, 19, 30, 40, 51, 2, 14, 25, 36, 47, 58, 9, 20, 31, 41, 52, 3, 15, 26, 37, 48, 59, 10, 21, 32, 42, 53, 4, 16, 27, 38, 49, 0, 11, 22, 33, 44, 55, 6, 17, 28, 43, 54, 5], 'cur_cost': 115811.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 52, 14, 41, 3, 29, 48, 25, 59, 16, 33, 5, 47, 21, 38, 10, 54, 27, 42, 1, 36, 50, 15, 44, 20, 58, 9, 31, 53, 18, 39, 6, 49, 24, 57, 11, 35, 22, 46, 4, 30, 55, 17, 40, 2, 28, 51, 13, 43, 26, 0, 32], 'cur_cost': 118599.0}, {'tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 48, 15, 29, 41, 5, 52, 18, 33, 9, 44, 26, 58, 13, 36, 1, 47, 21, 54, 10, 39, 24, 50, 6, 30, 42, 16, 55, 3, 27, 49, 14, 38, 22, 57, 11, 35, 20, 53, 4, 28, 43, 17, 59, 0, 25, 46, 31, 51, 32, 40], 'cur_cost': 113042.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': array([49, 15, 57, 32, 38, 59, 21, 18, 53,  9, 14, 58, 26, 44,  4, 48,  5,
        1, 10, 31, 42, 47, 19, 34, 11,  6, 39, 46, 40, 50, 33, 28, 27, 41,
       37, 23, 12, 54,  7, 25, 24, 51,  2,  0, 56, 16, 45, 30, 55, 52, 43,
       29, 36, 22, 13,  8,  3, 20, 17, 35]), 'cur_cost': 83548.0}, {'tour': array([25, 39, 35, 56, 28, 24, 15,  7,  9,  1,  0, 12,  5, 37, 11, 45, 18,
       32, 34,  8, 55, 49,  2, 23, 21, 50, 10, 46, 16, 58, 48,  4, 53, 47,
       59, 36, 14, 52, 17, 13, 51, 30, 22, 41, 20, 40,  6, 29, 33, 26, 27,
       42, 38, 57, 44, 43, 31, 54, 19,  3]), 'cur_cost': 91692.0}, {'tour': array([46, 49, 57, 43, 44, 40,  4, 28, 27, 11, 20, 38, 21, 55, 41, 52, 51,
       32, 50,  0,  6, 29, 45, 24, 36,  7, 34,  3, 23, 19, 54,  2, 10, 16,
       48,  9, 59, 13, 25, 17,  8, 37,  5, 31,  1, 42, 35, 33, 53, 26, 14,
       30, 39, 18, 47, 56, 58, 22, 15, 12]), 'cur_cost': 93349.0}]
2025-06-26 19:13:20,890 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:13:20,890 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 307, 'skip_rate': 0.03908794788273615, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 295, 'cache_hits': 186, 'similarity_calculations': 4259, 'cache_hit_rate': 0.04367222352664945, 'cache_size': 4073}}
2025-06-26 19:13:20,890 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:13:20,890 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:13:20,890 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:13:20,890 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:13:20,891 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 89399.0
2025-06-26 19:13:21,394 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:13:21,394 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:13:21,395 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:13:21,413 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:13:21,413 - ExploitationExpert - INFO - populations: [{'tour': [12, 23, 34, 45, 56, 7, 18, 29, 39, 50, 1, 13, 24, 35, 46, 57, 8, 19, 30, 40, 51, 2, 14, 25, 36, 47, 58, 9, 20, 31, 41, 52, 3, 15, 26, 37, 48, 59, 10, 21, 32, 42, 53, 4, 16, 27, 38, 49, 0, 11, 22, 33, 44, 55, 6, 17, 28, 43, 54, 5], 'cur_cost': 115811.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 52, 14, 41, 3, 29, 48, 25, 59, 16, 33, 5, 47, 21, 38, 10, 54, 27, 42, 1, 36, 50, 15, 44, 20, 58, 9, 31, 53, 18, 39, 6, 49, 24, 57, 11, 35, 22, 46, 4, 30, 55, 17, 40, 2, 28, 51, 13, 43, 26, 0, 32], 'cur_cost': 118599.0}, {'tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 48, 15, 29, 41, 5, 52, 18, 33, 9, 44, 26, 58, 13, 36, 1, 47, 21, 54, 10, 39, 24, 50, 6, 30, 42, 16, 55, 3, 27, 49, 14, 38, 22, 57, 11, 35, 20, 53, 4, 28, 43, 17, 59, 0, 25, 46, 31, 51, 32, 40], 'cur_cost': 113042.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': array([49, 15, 57, 32, 38, 59, 21, 18, 53,  9, 14, 58, 26, 44,  4, 48,  5,
        1, 10, 31, 42, 47, 19, 34, 11,  6, 39, 46, 40, 50, 33, 28, 27, 41,
       37, 23, 12, 54,  7, 25, 24, 51,  2,  0, 56, 16, 45, 30, 55, 52, 43,
       29, 36, 22, 13,  8,  3, 20, 17, 35]), 'cur_cost': 83548.0}, {'tour': array([10, 22, 45, 11,  3, 40, 50, 37,  0, 35, 12,  4, 39,  8, 18, 38, 25,
       59,  5, 20, 48, 16, 32, 27, 30, 31, 26, 47, 43,  6, 49, 51, 36, 15,
       44, 33, 34,  2, 46, 23, 29, 41, 57, 21, 17, 52, 58, 28, 24, 54,  1,
       42, 55, 53,  7,  9, 14, 19, 56, 13]), 'cur_cost': 89399.0}, {'tour': array([46, 49, 57, 43, 44, 40,  4, 28, 27, 11, 20, 38, 21, 55, 41, 52, 51,
       32, 50,  0,  6, 29, 45, 24, 36,  7, 34,  3, 23, 19, 54,  2, 10, 16,
       48,  9, 59, 13, 25, 17,  8, 37,  5, 31,  1, 42, 35, 33, 53, 26, 14,
       30, 39, 18, 47, 56, 58, 22, 15, 12]), 'cur_cost': 93349.0}]
2025-06-26 19:13:21,414 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:13:21,415 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 308, 'skip_rate': 0.03896103896103896, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 296, 'cache_hits': 186, 'similarity_calculations': 4268, 'cache_hit_rate': 0.04358013120899719, 'cache_size': 4082}}
2025-06-26 19:13:21,415 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:13:21,416 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:13:21,416 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:13:21,416 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:13:21,416 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 103051.0
2025-06-26 19:13:21,920 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:13:21,920 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:13:21,921 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:13:21,937 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:13:21,937 - ExploitationExpert - INFO - populations: [{'tour': [12, 23, 34, 45, 56, 7, 18, 29, 39, 50, 1, 13, 24, 35, 46, 57, 8, 19, 30, 40, 51, 2, 14, 25, 36, 47, 58, 9, 20, 31, 41, 52, 3, 15, 26, 37, 48, 59, 10, 21, 32, 42, 53, 4, 16, 27, 38, 49, 0, 11, 22, 33, 44, 55, 6, 17, 28, 43, 54, 5], 'cur_cost': 115811.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 52, 14, 41, 3, 29, 48, 25, 59, 16, 33, 5, 47, 21, 38, 10, 54, 27, 42, 1, 36, 50, 15, 44, 20, 58, 9, 31, 53, 18, 39, 6, 49, 24, 57, 11, 35, 22, 46, 4, 30, 55, 17, 40, 2, 28, 51, 13, 43, 26, 0, 32], 'cur_cost': 118599.0}, {'tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 48, 15, 29, 41, 5, 52, 18, 33, 9, 44, 26, 58, 13, 36, 1, 47, 21, 54, 10, 39, 24, 50, 6, 30, 42, 16, 55, 3, 27, 49, 14, 38, 22, 57, 11, 35, 20, 53, 4, 28, 43, 17, 59, 0, 25, 46, 31, 51, 32, 40], 'cur_cost': 113042.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': array([49, 15, 57, 32, 38, 59, 21, 18, 53,  9, 14, 58, 26, 44,  4, 48,  5,
        1, 10, 31, 42, 47, 19, 34, 11,  6, 39, 46, 40, 50, 33, 28, 27, 41,
       37, 23, 12, 54,  7, 25, 24, 51,  2,  0, 56, 16, 45, 30, 55, 52, 43,
       29, 36, 22, 13,  8,  3, 20, 17, 35]), 'cur_cost': 83548.0}, {'tour': array([10, 22, 45, 11,  3, 40, 50, 37,  0, 35, 12,  4, 39,  8, 18, 38, 25,
       59,  5, 20, 48, 16, 32, 27, 30, 31, 26, 47, 43,  6, 49, 51, 36, 15,
       44, 33, 34,  2, 46, 23, 29, 41, 57, 21, 17, 52, 58, 28, 24, 54,  1,
       42, 55, 53,  7,  9, 14, 19, 56, 13]), 'cur_cost': 89399.0}, {'tour': array([15, 44,  2, 37, 47, 53, 17,  7, 16, 27, 23, 35,  3, 39,  0,  4, 30,
       21, 49, 33, 36, 46, 42, 54, 41, 59,  8, 57, 29,  6, 28, 45,  1, 14,
       58, 12, 43, 13, 34, 48, 55,  9, 25, 19, 26, 10, 22, 50, 52,  5, 20,
       56, 11, 40, 24, 51, 18, 38, 31, 32]), 'cur_cost': 103051.0}]
2025-06-26 19:13:21,940 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:13:21,940 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 309, 'skip_rate': 0.038834951456310676, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 297, 'cache_hits': 186, 'similarity_calculations': 4278, 'cache_hit_rate': 0.043478260869565216, 'cache_size': 4092}}
2025-06-26 19:13:21,940 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:13:21,941 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [12, 23, 34, 45, 56, 7, 18, 29, 39, 50, 1, 13, 24, 35, 46, 57, 8, 19, 30, 40, 51, 2, 14, 25, 36, 47, 58, 9, 20, 31, 41, 52, 3, 15, 26, 37, 48, 59, 10, 21, 32, 42, 53, 4, 16, 27, 38, 49, 0, 11, 22, 33, 44, 55, 6, 17, 28, 43, 54, 5], 'cur_cost': 115811.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 52, 14, 41, 3, 29, 48, 25, 59, 16, 33, 5, 47, 21, 38, 10, 54, 27, 42, 1, 36, 50, 15, 44, 20, 58, 9, 31, 53, 18, 39, 6, 49, 24, 57, 11, 35, 22, 46, 4, 30, 55, 17, 40, 2, 28, 51, 13, 43, 26, 0, 32], 'cur_cost': 118599.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 48, 15, 29, 41, 5, 52, 18, 33, 9, 44, 26, 58, 13, 36, 1, 47, 21, 54, 10, 39, 24, 50, 6, 30, 42, 16, 55, 3, 27, 49, 14, 38, 22, 57, 11, 35, 20, 53, 4, 28, 43, 17, 59, 0, 25, 46, 31, 51, 32, 40], 'cur_cost': 113042.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}]
2025-06-26 19:13:21,941 - __main__ - INFO - 进化阶段完成
2025-06-26 19:13:21,941 - __main__ - INFO - 开始评估阶段
2025-06-26 19:13:21,942 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:13:21,942 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:13:21,943 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 91692.0 → 83548.0 (Δ 8144.0)
- Mean cost: 113717.1 → 112782.6
- Diversity: 0.456 → 0.607 (Δ 0.1508)

#### Elite metrics
- Elite count: 39 → 39
- Best elite cost: 9614 → 9614 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 42.9% , avg Δ 29.71
- Exploit: 3 inds, success 66.7% , avg Δ 3045.67

#### Other indicators
- No-change individuals: 3
- Regression individuals: 2
- Historical trends: Last 3 iterations: costs [26245.0, 13233.0, 91692.0], diversity [0.9532956685499059, 0.8503703703703703, 0.6329629629629631]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:13:21,943 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:13:32,868 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 42.9,
      "average_delta": 29.71,
      "contribution": "low"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_delta": 3045.67,
      "contribution": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the mutation rate slightly to enhance exploration effectiveness."
  }
}
```
2025-06-26 19:13:32,896 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:13:32,896 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 42.9,
      "average_delta": 29.71,
      "contribution": "low"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_delta": 3045.67,
      "contribution": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the mutation rate slightly to enhance exploration effectiveness."
  }
}
```
2025-06-26 19:13:32,896 - __main__ - INFO - 评估阶段完成
2025-06-26 19:13:32,896 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 42.9,
      "average_delta": 29.71,
      "contribution": "low"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_delta": 3045.67,
      "contribution": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the mutation rate slightly to enhance exploration effectiveness."
  }
}
```
2025-06-26 19:13:32,896 - __main__ - INFO - 当前最佳适应度: 83548.0
2025-06-26 19:13:32,896 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite12_60_route_2.pkl
2025-06-26 19:13:32,896 - __main__ - INFO - composite12_60 开始进化第 4 代
2025-06-26 19:13:32,896 - __main__ - INFO - 开始分析阶段
2025-06-26 19:13:32,896 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:13:32,911 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 83548.0, 'max': 126094.0, 'mean': 112782.6, 'std': 14993.335547502431}, 'diversity': 0.8274074074074075, 'clusters': {'clusters': 7, 'cluster_sizes': [1, 4, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:13:32,911 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 83548.0, 'max': 126094.0, 'mean': 112782.6, 'std': 14993.335547502431}, 'diversity_level': 0.8274074074074075, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [1, 4, 1, 1, 1, 1, 1]}, 'coordinates': [[1380, 1590], [1386, 1610], [1446, 1664], [1473, 1651], [1403, 1573], [1429, 1576], [1414, 1596], [1421, 1664], [1385, 1662], [1475, 1616], [1405, 1625], [1439, 1639], [3242, 2428], [3193, 2490], [3212, 2461], [3238, 2498], [3270, 2452], [3236, 2513], [3196, 2449], [3220, 2481], [3262, 2465], [3198, 2468], [3267, 2481], [3264, 2442], [426, 2488], [478, 2496], [499, 2467], [445, 2489], [451, 2481], [441, 2477], [461, 2463], [511, 2487], [499, 2478], [431, 2471], [461, 2456], [476, 2486], [832, 400], [885, 348], [838, 364], [921, 379], [900, 346], [918, 411], [855, 361], [866, 388], [878, 327], [841, 380], [902, 374], [871, 361], [2763, 1531], [2817, 1507], [2789, 1453], [2739, 1464], [2773, 1490], [2783, 1514], [2808, 1535], [2797, 1510], [2807, 1517], [2793, 1495], [2828, 1490], [2736, 1497]], 'distance_matrix': array([[   0.,   21.,   99., ..., 1416., 1451., 1359.],
       [  21.,    0.,   81., ..., 1412., 1447., 1355.],
       [  99.,   81.,    0., ..., 1358., 1393., 1301.],
       ...,
       [1416., 1412., 1358., ...,    0.,   35.,   57.],
       [1451., 1447., 1393., ...,   35.,    0.,   92.],
       [1359., 1355., 1301., ...,   57.,   92.,    0.]])}
2025-06-26 19:13:32,916 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:13:32,916 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:13:32,916 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:13:32,918 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 19:13:32,919 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:13:32,919 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 19:13:32,919 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:13:32,920 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:13:32,920 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:13:32,940 - EliteExpert - INFO - 精英解分析完成
2025-06-26 19:13:32,941 - __main__ - INFO - 精英专家分析报告: {'elite_count': 39, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 73934.0, 'avg_gap': 103166.70256410257}, 'structure_gap': {'unique_elite_edges': 160, 'unique_pop_edges': 357, 'common_edges': 20}}, 'elite_diversity': {'diversity_score': 0.1571300044984255}}
2025-06-26 19:13:32,941 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:13:32,941 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:13:32,943 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:13:32,943 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 83548.0, mean 112782.6, max 126094.0, std 14993.335547502431
- diversity: 0.8274074074074075
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [13233.0, 91692.0, 83548.0] (Δ 4072.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [426.0, 327.0, 3270.0, 2513.0]
- centroid/spread: [1756.217, 1687.917] | rms 1327.809
- density_grid(3x3): [[12, 0, 0], [0, 12, 12], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [7.0, 1094.25, 1427.5, 2273.75, 3222.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 7, 6, 9, 12, 8, 4, 7]
- nn_median_dist: 17.242

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:13:32,944 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:13:54,575 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide edge length range from 7.0 to 3222.0 with Q3 at 2273.75)",
    "modality": "Potentially multimodal (direction histogram shows varied edge directions, no clear dominance in any sector)",
    "deceptiveness": "Possible deceptive regions (25% long edges may create misleading fitness gradients)"
  },
  "population_state": {
    "coverage": "Good spatial diversity (diversity=0.83, density_grid shows multiple occupied cells)",
    "convergence": "Early exploration phase (0.0 convergence, recent best costs still improving by ~4072 per iteration)",
    "elite_influence": "Minimal (0 elites, only one fixed node in population)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges >2273.75 length, particularly in max quadrant)",
    "Low-density grid cells (top-center and center-right in 3x3 grid)"
  ],
  "opportunity_regions": [
    "High-density cells (bottom-left and bottom-right grid clusters)",
    "Short-edge clusters (edges <1094.25 length, especially near centroid at [1756,1688])"
  ],
  "evolution_phase": "Early exploration (iter 3/5)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Edge recombination focusing on short-edge clusters",
    "2. Directed mutation towards low-density grid cells",
    "3. Local search intensification around centroid region",
    "4. Path consolidation for long-edge corridors (consider 2-opt or node insertion)"
  ]
}
```
2025-06-26 19:13:54,578 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:13:54,578 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide edge length range from 7.0 to 3222.0 with Q3 at 2273.75)', 'modality': 'Potentially multimodal (direction histogram shows varied edge directions, no clear dominance in any sector)', 'deceptiveness': 'Possible deceptive regions (25% long edges may create misleading fitness gradients)'}, 'population_state': {'coverage': 'Good spatial diversity (diversity=0.83, density_grid shows multiple occupied cells)', 'convergence': 'Early exploration phase (0.0 convergence, recent best costs still improving by ~4072 per iteration)', 'elite_influence': 'Minimal (0 elites, only one fixed node in population)'}, 'difficult_regions': ['Long-edge corridors (edges >2273.75 length, particularly in max quadrant)', 'Low-density grid cells (top-center and center-right in 3x3 grid)'], 'opportunity_regions': ['High-density cells (bottom-left and bottom-right grid clusters)', 'Short-edge clusters (edges <1094.25 length, especially near centroid at [1756,1688])'], 'evolution_phase': 'Early exploration (iter 3/5)', 'evolution_direction': ['Operator suggestions:', '1. Edge recombination focusing on short-edge clusters', '2. Directed mutation towards low-density grid cells', '3. Local search intensification around centroid region', '4. Path consolidation for long-edge corridors (consider 2-opt or node insertion)']}
2025-06-26 19:13:54,579 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:13:54,579 - __main__ - INFO - 分析阶段完成
2025-06-26 19:13:54,579 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide edge length range from 7.0 to 3222.0 with Q3 at 2273.75)', 'modality': 'Potentially multimodal (direction histogram shows varied edge directions, no clear dominance in any sector)', 'deceptiveness': 'Possible deceptive regions (25% long edges may create misleading fitness gradients)'}, 'population_state': {'coverage': 'Good spatial diversity (diversity=0.83, density_grid shows multiple occupied cells)', 'convergence': 'Early exploration phase (0.0 convergence, recent best costs still improving by ~4072 per iteration)', 'elite_influence': 'Minimal (0 elites, only one fixed node in population)'}, 'difficult_regions': ['Long-edge corridors (edges >2273.75 length, particularly in max quadrant)', 'Low-density grid cells (top-center and center-right in 3x3 grid)'], 'opportunity_regions': ['High-density cells (bottom-left and bottom-right grid clusters)', 'Short-edge clusters (edges <1094.25 length, especially near centroid at [1756,1688])'], 'evolution_phase': 'Early exploration (iter 3/5)', 'evolution_direction': ['Operator suggestions:', '1. Edge recombination focusing on short-edge clusters', '2. Directed mutation towards low-density grid cells', '3. Local search intensification around centroid region', '4. Path consolidation for long-edge corridors (consider 2-opt or node insertion)']}
2025-06-26 19:13:54,579 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:13:54,579 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:13:54,579 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 83548.0
  • mean_cost: 112782.6
- Search-space: ruggedness Moderate to high (wide edge length range from 7.0 to 3222.0 with Q3 at 2273.75), modality Potentially multimodal (direction histogram shows varied edge directions, no clear dominance in any sector), deceptiveness Possible deceptive regions (25% long edges may create misleading fitness gradients)
- Evolution phase: Early exploration (iter 3/5)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Low-density grid cel
- Opportunity regions (# 2): High-density cells (, Short-edge clusters 
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 42.9,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:13:54,579 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:13:54,579 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:14:04,131 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 30% individuals exploit high-density/short-edge regions."
}
```
2025-06-26 19:14:04,131 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:14:04,131 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:14:04,131 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 30% individuals exploit high-density/short-edge regions."
}
```
2025-06-26 19:14:04,131 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:14:04,131 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:14:04,138 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 30% individuals exploit high-density/short-edge regions."
}
```
2025-06-26 19:14:04,138 - __main__ - INFO - 开始进化阶段
2025-06-26 19:14:04,138 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:14:04,139 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:14:04,139 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 115811.0 (rank 40.0%)
- Population diversity: 0.6067796610169491
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:14:04,139 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:14:04,139 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:14:17,493 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Cluster-first, reverse sweep for diversity"
}
```
2025-06-26 19:14:17,493 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:14:17,493 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13253.0, 路径: [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
2025-06-26 19:14:17,493 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}
2025-06-26 19:14:17,493 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:14:17,493 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:14:17,493 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 60.0%)
- Population diversity: 0.6186440677966102
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:14:17,493 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:14:17,493 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:14:30,442 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Cluster traversal with reverse sweep for diversity"
}
```
2025-06-26 19:14:30,442 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:14:30,442 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13253.0, 路径: [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
2025-06-26 19:14:30,442 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}
2025-06-26 19:14:30,442 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:14:30,442 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:14:30,444 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 118599.0 (rank 60.0%)
- Population diversity: 0.6186440677966102
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:14:30,444 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:14:30,444 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:14:44,057 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Prioritize high-density clusters first, then sparse areas"
}
```
2025-06-26 19:14:44,057 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:14:44,057 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13253.0, 路径: [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
2025-06-26 19:14:44,057 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}
2025-06-26 19:14:44,057 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:14:44,057 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:14:44,057 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 113042.0 (rank 60.0%)
- Population diversity: 0.5559322033898305
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:14:44,057 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:14:44,057 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:14:57,327 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Prioritize clustering in high-density areas with short edges"
}
```
2025-06-26 19:14:57,327 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:14:57,327 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13233.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59]
2025-06-26 19:14:57,327 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}
2025-06-26 19:14:57,327 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:14:57,327 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:14:57,327 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 70.0%)
- Population diversity: 0.4694915254237288
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:14:57,327 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:14:57,327 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:15:11,656 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Leverage modular patterns to explore adjacent low-density cells"
}
```
2025-06-26 19:15:11,662 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:15:11,662 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:15:11,662 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:15:11,663 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:15:11,663 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:15:11,663 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 70.0%)
- Population diversity: 0.4694915254237288
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:15:11,664 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:15:11,664 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:15:24,587 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Cluster dense regions first, then sparse edges"
}
```
2025-06-26 19:15:24,587 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:15:24,592 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13253.0, 路径: [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
2025-06-26 19:15:24,592 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}
2025-06-26 19:15:24,592 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 19:15:24,592 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:15:24,592 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 80.0%)
- Population diversity: 0.4694915254237288
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:15:24,592 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:15:24,592 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:15:38,387 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Prioritize high-density clusters first, then sparse areas"
}
```
2025-06-26 19:15:38,387 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:15:38,387 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13253.0, 路径: [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
2025-06-26 19:15:38,387 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}
2025-06-26 19:15:38,387 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:15:38,387 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:15:38,393 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:15:38,393 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 89724.0
2025-06-26 19:15:38,896 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:15:38,896 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:15:38,896 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:15:38,912 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:15:38,912 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': array([14, 32, 56, 47,  4, 48, 46, 54,  6, 38, 45, 16, 11, 22, 18, 26, 25,
       42,  0, 10, 28, 52, 53, 21, 39, 37, 29, 31, 20, 41, 36, 43, 40, 33,
        8, 19, 15, 27,  3, 13, 23, 35, 17, 24, 49, 59, 55,  9,  1, 50, 51,
       12, 30, 44, 34, 58,  7, 57,  5,  2]), 'cur_cost': 89724.0}, {'tour': array([10, 22, 45, 11,  3, 40, 50, 37,  0, 35, 12,  4, 39,  8, 18, 38, 25,
       59,  5, 20, 48, 16, 32, 27, 30, 31, 26, 47, 43,  6, 49, 51, 36, 15,
       44, 33, 34,  2, 46, 23, 29, 41, 57, 21, 17, 52, 58, 28, 24, 54,  1,
       42, 55, 53,  7,  9, 14, 19, 56, 13]), 'cur_cost': 89399.0}, {'tour': array([15, 44,  2, 37, 47, 53, 17,  7, 16, 27, 23, 35,  3, 39,  0,  4, 30,
       21, 49, 33, 36, 46, 42, 54, 41, 59,  8, 57, 29,  6, 28, 45,  1, 14,
       58, 12, 43, 13, 34, 48, 55,  9, 25, 19, 26, 10, 22, 50, 52,  5, 20,
       56, 11, 40, 24, 51, 18, 38, 31, 32]), 'cur_cost': 103051.0}]
2025-06-26 19:15:38,914 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:15:38,914 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 310, 'skip_rate': 0.03870967741935484, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 298, 'cache_hits': 186, 'similarity_calculations': 4289, 'cache_hit_rate': 0.04336675215667988, 'cache_size': 4103}}
2025-06-26 19:15:38,916 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:15:38,916 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:15:38,916 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:15:38,916 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:15:38,918 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 101560.0
2025-06-26 19:15:39,422 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:15:39,423 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:15:39,423 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:15:39,440 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:15:39,440 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': array([14, 32, 56, 47,  4, 48, 46, 54,  6, 38, 45, 16, 11, 22, 18, 26, 25,
       42,  0, 10, 28, 52, 53, 21, 39, 37, 29, 31, 20, 41, 36, 43, 40, 33,
        8, 19, 15, 27,  3, 13, 23, 35, 17, 24, 49, 59, 55,  9,  1, 50, 51,
       12, 30, 44, 34, 58,  7, 57,  5,  2]), 'cur_cost': 89724.0}, {'tour': array([47, 38,  5, 37,  0, 48, 31, 11, 33, 20, 29, 45, 25, 16, 42, 34, 21,
       56, 10, 39,  8, 19, 18,  4, 49, 58, 14, 44, 53, 17, 36, 41, 55, 59,
       13,  2, 24,  7, 57,  3, 15, 52, 46, 12, 27, 30, 50,  1, 54, 40, 22,
       35,  9, 32, 43,  6, 51, 23, 26, 28]), 'cur_cost': 101560.0}, {'tour': array([15, 44,  2, 37, 47, 53, 17,  7, 16, 27, 23, 35,  3, 39,  0,  4, 30,
       21, 49, 33, 36, 46, 42, 54, 41, 59,  8, 57, 29,  6, 28, 45,  1, 14,
       58, 12, 43, 13, 34, 48, 55,  9, 25, 19, 26, 10, 22, 50, 52,  5, 20,
       56, 11, 40, 24, 51, 18, 38, 31, 32]), 'cur_cost': 103051.0}]
2025-06-26 19:15:39,442 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:15:39,442 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 311, 'skip_rate': 0.03858520900321544, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 299, 'cache_hits': 186, 'similarity_calculations': 4301, 'cache_hit_rate': 0.043245756800744016, 'cache_size': 4115}}
2025-06-26 19:15:39,443 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:15:39,443 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:15:39,443 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:15:39,444 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:15:39,444 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 91199.0
2025-06-26 19:15:39,945 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:15:39,945 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:15:39,946 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:15:39,961 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:15:39,961 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': array([14, 32, 56, 47,  4, 48, 46, 54,  6, 38, 45, 16, 11, 22, 18, 26, 25,
       42,  0, 10, 28, 52, 53, 21, 39, 37, 29, 31, 20, 41, 36, 43, 40, 33,
        8, 19, 15, 27,  3, 13, 23, 35, 17, 24, 49, 59, 55,  9,  1, 50, 51,
       12, 30, 44, 34, 58,  7, 57,  5,  2]), 'cur_cost': 89724.0}, {'tour': array([47, 38,  5, 37,  0, 48, 31, 11, 33, 20, 29, 45, 25, 16, 42, 34, 21,
       56, 10, 39,  8, 19, 18,  4, 49, 58, 14, 44, 53, 17, 36, 41, 55, 59,
       13,  2, 24,  7, 57,  3, 15, 52, 46, 12, 27, 30, 50,  1, 54, 40, 22,
       35,  9, 32, 43,  6, 51, 23, 26, 28]), 'cur_cost': 101560.0}, {'tour': array([43, 32, 23,  7, 58, 57, 17, 56, 25,  4, 53, 54, 11, 37, 40, 14, 28,
       24,  1, 30, 38, 51, 13, 50, 59,  6, 39, 44, 35, 49,  5, 10, 34, 26,
       22, 19, 45, 52, 27, 21, 42,  2, 12, 29, 18,  3, 47, 33, 48,  8,  0,
       31, 20, 16, 15, 46, 41, 36,  9, 55]), 'cur_cost': 91199.0}]
2025-06-26 19:15:39,964 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:15:39,964 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 312, 'skip_rate': 0.038461538461538464, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 300, 'cache_hits': 186, 'similarity_calculations': 4314, 'cache_hit_rate': 0.043115438108484005, 'cache_size': 4128}}
2025-06-26 19:15:39,965 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:15:39,965 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 13233.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}}]
2025-06-26 19:15:39,965 - __main__ - INFO - 进化阶段完成
2025-06-26 19:15:39,966 - __main__ - INFO - 开始评估阶段
2025-06-26 19:15:39,966 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:15:39,968 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:15:39,968 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 83548.0 → 13233.0 (Δ 70315.0)
- Mean cost: 112782.6 → 48807.5
- Diversity: 0.607 → 0.468 (Δ -0.139)

#### Elite metrics
- Elite count: 39 → 39
- Best elite cost: 9614 → 9614 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 85.7% , avg Δ 92319.43
- Exploit: 3 inds, success 33.3% , avg Δ -2161.67

#### Other indicators
- No-change individuals: 1
- Regression individuals: 2
- Historical trends: Last 3 iterations: costs [13233.0, 91692.0, 83548.0], diversity [0.8503703703703703, 0.6329629629629631, 0.8274074074074075]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:15:39,968 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:15:54,128 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 85.7,
      "average_improvement": 92319.43,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_improvement": -2161.67,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore is effective but exploit is underperforming",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider refining exploit operators or increasing their application rate slightly to improve local search effectiveness"
  }
}
```
2025-06-26 19:15:54,152 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:15:54,152 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 85.7,
      "average_improvement": 92319.43,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_improvement": -2161.67,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore is effective but exploit is underperforming",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider refining exploit operators or increasing their application rate slightly to improve local search effectiveness"
  }
}
```
2025-06-26 19:15:54,152 - __main__ - INFO - 评估阶段完成
2025-06-26 19:15:54,152 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 85.7,
      "average_improvement": 92319.43,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_improvement": -2161.67,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore is effective but exploit is underperforming",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider refining exploit operators or increasing their application rate slightly to improve local search effectiveness"
  }
}
```
2025-06-26 19:15:54,152 - __main__ - INFO - 当前最佳适应度: 13233.0
2025-06-26 19:15:54,155 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite12_60_route_3.pkl
2025-06-26 19:15:54,155 - __main__ - INFO - composite12_60 开始进化第 5 代
2025-06-26 19:15:54,156 - __main__ - INFO - 开始分析阶段
2025-06-26 19:15:54,156 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:15:54,168 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 13233.0, 'max': 126094.0, 'mean': 48807.5, 'std': 44512.22976272926}, 'diversity': 0.6466666666666667, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:15:54,168 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 13233.0, 'max': 126094.0, 'mean': 48807.5, 'std': 44512.22976272926}, 'diversity_level': 0.6466666666666667, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'coordinates': [[1380, 1590], [1386, 1610], [1446, 1664], [1473, 1651], [1403, 1573], [1429, 1576], [1414, 1596], [1421, 1664], [1385, 1662], [1475, 1616], [1405, 1625], [1439, 1639], [3242, 2428], [3193, 2490], [3212, 2461], [3238, 2498], [3270, 2452], [3236, 2513], [3196, 2449], [3220, 2481], [3262, 2465], [3198, 2468], [3267, 2481], [3264, 2442], [426, 2488], [478, 2496], [499, 2467], [445, 2489], [451, 2481], [441, 2477], [461, 2463], [511, 2487], [499, 2478], [431, 2471], [461, 2456], [476, 2486], [832, 400], [885, 348], [838, 364], [921, 379], [900, 346], [918, 411], [855, 361], [866, 388], [878, 327], [841, 380], [902, 374], [871, 361], [2763, 1531], [2817, 1507], [2789, 1453], [2739, 1464], [2773, 1490], [2783, 1514], [2808, 1535], [2797, 1510], [2807, 1517], [2793, 1495], [2828, 1490], [2736, 1497]], 'distance_matrix': array([[   0.,   21.,   99., ..., 1416., 1451., 1359.],
       [  21.,    0.,   81., ..., 1412., 1447., 1355.],
       [  99.,   81.,    0., ..., 1358., 1393., 1301.],
       ...,
       [1416., 1412., 1358., ...,    0.,   35.,   57.],
       [1451., 1447., 1393., ...,   35.,    0.,   92.],
       [1359., 1355., 1301., ...,   57.,   92.,    0.]])}
2025-06-26 19:15:54,168 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:15:54,171 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:15:54,171 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:15:54,172 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 19:15:54,172 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:15:54,175 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 19:15:54,176 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:15:54,176 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:15:54,176 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:15:54,195 - EliteExpert - INFO - 精英解分析完成
2025-06-26 19:15:54,195 - __main__ - INFO - 精英专家分析报告: {'elite_count': 39, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 3619.0, 'avg_gap': 39191.60256410256}, 'structure_gap': {'unique_elite_edges': 152, 'unique_pop_edges': 272, 'common_edges': 28}}, 'elite_diversity': {'diversity_score': 0.1571300044984255}}
2025-06-26 19:15:54,195 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:15:54,195 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:15:54,199 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:15:54,199 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 13233.0, mean 48807.5, max 126094.0, std 44512.22976272926
- diversity: 0.6466666666666667
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [91692.0, 83548.0, 13233.0] (Δ 39229.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [426.0, 327.0, 3270.0, 2513.0]
- centroid/spread: [1756.217, 1687.917] | rms 1327.809
- density_grid(3x3): [[12, 0, 0], [0, 12, 12], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [7.0, 1094.25, 1427.5, 2273.75, 3222.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 7, 6, 9, 12, 8, 4, 7]
- nn_median_dist: 17.242

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:15:54,201 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:16:15,474 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 7.0 to 3222.0, with significant quartile spreads)",
    "modality": "Potentially multimodal (diverse edge lengths and directions, with no clear single peak in direction histogram)",
    "deceptiveness": "Possible deceptive regions (presence of long edges (25% ratio) that may mislead local search)"
  },
  "population_state": {
    "coverage": "Moderate (diversity 0.647, but density_grid shows uneven distribution with some empty cells)",
    "convergence": "None detected (convergence 0.0, recent best costs show improvement Δ 39229.50 per iteration)"
  },
  "difficult_regions": [
    "Low-density cells (grid positions [0,1], [0,2], [1,0], [1,1], [2,1])",
    "Long-edge corridors (edges in top quartile length ≥2273.75)"
  ],
  "opportunity_regions": [
    "High-density cells (grid positions [0,0], [1,2], [2,0], [2,2] with 12 nodes each)",
    "Short-edge clusters (edges in bottom quartile length ≤1094.25)"
  ],
  "evolution_phase": "Mid-phase exploration (population shows improving diversity but no elite convergence)",
  "evolution_direction": [
    "Enhance exploitation in high-density cells with path consolidation operators",
    "Target long-edge regions with mutation operators that break/reconnect subpaths",
    "Maintain diversity through spatial niching in low-density cells"
  ]
}
```
2025-06-26 19:16:15,474 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:16:15,474 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 7.0 to 3222.0, with significant quartile spreads)', 'modality': 'Potentially multimodal (diverse edge lengths and directions, with no clear single peak in direction histogram)', 'deceptiveness': 'Possible deceptive regions (presence of long edges (25% ratio) that may mislead local search)'}, 'population_state': {'coverage': 'Moderate (diversity 0.647, but density_grid shows uneven distribution with some empty cells)', 'convergence': 'None detected (convergence 0.0, recent best costs show improvement Δ 39229.50 per iteration)'}, 'difficult_regions': ['Low-density cells (grid positions [0,1], [0,2], [1,0], [1,1], [2,1])', 'Long-edge corridors (edges in top quartile length ≥2273.75)'], 'opportunity_regions': ['High-density cells (grid positions [0,0], [1,2], [2,0], [2,2] with 12 nodes each)', 'Short-edge clusters (edges in bottom quartile length ≤1094.25)'], 'evolution_phase': 'Mid-phase exploration (population shows improving diversity but no elite convergence)', 'evolution_direction': ['Enhance exploitation in high-density cells with path consolidation operators', 'Target long-edge regions with mutation operators that break/reconnect subpaths', 'Maintain diversity through spatial niching in low-density cells']}
2025-06-26 19:16:15,474 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:16:15,474 - __main__ - INFO - 分析阶段完成
2025-06-26 19:16:15,474 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 7.0 to 3222.0, with significant quartile spreads)', 'modality': 'Potentially multimodal (diverse edge lengths and directions, with no clear single peak in direction histogram)', 'deceptiveness': 'Possible deceptive regions (presence of long edges (25% ratio) that may mislead local search)'}, 'population_state': {'coverage': 'Moderate (diversity 0.647, but density_grid shows uneven distribution with some empty cells)', 'convergence': 'None detected (convergence 0.0, recent best costs show improvement Δ 39229.50 per iteration)'}, 'difficult_regions': ['Low-density cells (grid positions [0,1], [0,2], [1,0], [1,1], [2,1])', 'Long-edge corridors (edges in top quartile length ≥2273.75)'], 'opportunity_regions': ['High-density cells (grid positions [0,0], [1,2], [2,0], [2,2] with 12 nodes each)', 'Short-edge clusters (edges in bottom quartile length ≤1094.25)'], 'evolution_phase': 'Mid-phase exploration (population shows improving diversity but no elite convergence)', 'evolution_direction': ['Enhance exploitation in high-density cells with path consolidation operators', 'Target long-edge regions with mutation operators that break/reconnect subpaths', 'Maintain diversity through spatial niching in low-density cells']}
2025-06-26 19:16:15,474 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:16:15,474 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:16:15,474 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 13233.0
  • mean_cost: 48807.5
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 7.0 to 3222.0, with significant quartile spreads), modality Potentially multimodal (diverse edge lengths and directions, with no clear single peak in direction histogram), deceptiveness Possible deceptive regions (presence of long edges (25% ratio) that may mislead local search)
- Evolution phase: Mid-phase exploration (population shows improving diversity but no elite convergence)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Low-density cells (g, Long-edge corridors 
- Opportunity regions (# 2): High-density cells (, Short-edge clusters 
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:16:15,482 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:16:15,482 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:16:24,888 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and mid-phase exploration suggest prioritizing exploration (70%). Top 50% individuals exploit, others explore to balance search."
}
```
2025-06-26 19:16:24,888 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:16:24,888 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:16:24,888 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and mid-phase exploration suggest prioritizing exploration (70%). Top 50% individuals exploit, others explore to balance search."
}
```
2025-06-26 19:16:24,888 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:16:24,888 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:16:24,888 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and mid-phase exploration suggest prioritizing exploration (70%). Top 50% individuals exploit, others explore to balance search."
}
```
2025-06-26 19:16:24,890 - __main__ - INFO - 开始进化阶段
2025-06-26 19:16:24,890 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:16:24,890 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:16:24,890 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13253.0 (rank 10.0%)
- Population diversity: 0.46779661016949153
- Evolution phase: Mid-phase exploration (population shows improving diversity but no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:16:24,890 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:16:24,890 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:16:38,408 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Modular traversal balancing opportunity regions and diversity"
}
```
2025-06-26 19:16:38,408 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:16:38,408 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:16:38,408 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:16:38,408 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:16:38,408 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:16:38,408 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13253.0 (rank 10.0%)
- Population diversity: 0.46779661016949153
- Evolution phase: Mid-phase exploration (population shows improving diversity but no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:16:38,408 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:16:38,408 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:16:53,599 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Alternate high/low density regions to balance exploration"
}
```
2025-06-26 19:16:53,599 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:16:53,599 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21405.0, 路径: [12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59]
2025-06-26 19:16:53,599 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59], 'cur_cost': 21405.0}
2025-06-26 19:16:53,599 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:16:53,599 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:16:53,601 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13253.0 (rank 10.0%)
- Population diversity: 0.5610169491525424
- Evolution phase: Mid-phase exploration (population shows improving diversity but no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:16:53,601 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:16:53,601 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:17:07,194 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Leverage modular grouping to explore adjacent low-density cells"
}
```
2025-06-26 19:17:07,194 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:17:07,194 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:17:07,194 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:17:07,194 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:17:07,194 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:17:07,194 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13233.0 (rank 0.0%)
- Population diversity: 0.5610169491525424
- Evolution phase: Mid-phase exploration (population shows improving diversity but no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:17:07,194 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:17:07,194 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:17:20,147 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Leverage grid-like traversal for density balance"
}
```
2025-06-26 19:17:20,147 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:17:20,147 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:17:20,147 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:17:20,147 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:17:20,147 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:17:20,147 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 126094.0 (rank 60.0%)
- Population diversity: 0.5576271186440678
- Evolution phase: Mid-phase exploration (population shows improving diversity but no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:17:20,147 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:17:20,147 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:17:33,792 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of opportunity regions with structured diversity"
}
```
2025-06-26 19:17:33,792 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:17:33,792 - ExplorationExpert - INFO - 探索路径生成完成，成本: 126094.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:17:33,797 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}
2025-06-26 19:17:33,797 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-26 19:17:33,797 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:17:33,797 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:17:33,799 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 101238.0
2025-06-26 19:17:34,301 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:17:34,301 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:17:34,301 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:17:34,318 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:17:34,318 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59], 'cur_cost': 21405.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': array([13,  2, 38, 28, 17, 53, 46, 37, 41, 40, 52, 19,  7, 56, 26, 18, 21,
       39,  4, 59, 31, 15, 25, 12, 54,  9,  0, 35, 57, 43, 55,  3, 32, 14,
       30, 50, 10, 34, 44, 36, 27, 29,  1, 23, 45, 49, 16, 48,  8,  5, 33,
       22, 47, 11, 42, 58, 20, 51, 24,  6]), 'cur_cost': 101238.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 13253.0}, {'tour': array([14, 32, 56, 47,  4, 48, 46, 54,  6, 38, 45, 16, 11, 22, 18, 26, 25,
       42,  0, 10, 28, 52, 53, 21, 39, 37, 29, 31, 20, 41, 36, 43, 40, 33,
        8, 19, 15, 27,  3, 13, 23, 35, 17, 24, 49, 59, 55,  9,  1, 50, 51,
       12, 30, 44, 34, 58,  7, 57,  5,  2]), 'cur_cost': 89724.0}, {'tour': array([47, 38,  5, 37,  0, 48, 31, 11, 33, 20, 29, 45, 25, 16, 42, 34, 21,
       56, 10, 39,  8, 19, 18,  4, 49, 58, 14, 44, 53, 17, 36, 41, 55, 59,
       13,  2, 24,  7, 57,  3, 15, 52, 46, 12, 27, 30, 50,  1, 54, 40, 22,
       35,  9, 32, 43,  6, 51, 23, 26, 28]), 'cur_cost': 101560.0}, {'tour': array([43, 32, 23,  7, 58, 57, 17, 56, 25,  4, 53, 54, 11, 37, 40, 14, 28,
       24,  1, 30, 38, 51, 13, 50, 59,  6, 39, 44, 35, 49,  5, 10, 34, 26,
       22, 19, 45, 52, 27, 21, 42,  2, 12, 29, 18,  3, 47, 33, 48,  8,  0,
       31, 20, 16, 15, 46, 41, 36,  9, 55]), 'cur_cost': 91199.0}]
2025-06-26 19:17:34,321 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:17:34,321 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 313, 'skip_rate': 0.038338658146964855, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 301, 'cache_hits': 186, 'similarity_calculations': 4328, 'cache_hit_rate': 0.042975970425138635, 'cache_size': 4142}}
2025-06-26 19:17:34,322 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-26 19:17:34,322 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 19:17:34,322 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:17:34,322 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:17:34,323 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 87549.0
2025-06-26 19:17:34,324 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：300,跳过局部搜索
2025-06-26 19:17:34,324 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 19:17:34,324 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([55, 41, 19,  5, 43, 40, 30, 42, 11, 14, 27, 59, 49, 48, 22,  2,  0,
       26,  8, 44, 37, 54, 25, 45, 15, 58,  3,  7, 47, 38, 56, 28,  9, 29,
       33, 35, 52, 21, 51, 10, 31, 36, 17, 20, 24, 46, 12, 16, 53, 13, 18,
       34,  6,  1, 23,  4, 39, 32, 57, 50]), 'cur_cost': 87549.0}
2025-06-26 19:17:34,324 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:17:34,324 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:17:34,324 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:17:34,326 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 110229.0
2025-06-26 19:17:34,830 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:17:34,830 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:17:34,831 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:17:34,850 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:17:34,850 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59], 'cur_cost': 21405.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': array([13,  2, 38, 28, 17, 53, 46, 37, 41, 40, 52, 19,  7, 56, 26, 18, 21,
       39,  4, 59, 31, 15, 25, 12, 54,  9,  0, 35, 57, 43, 55,  3, 32, 14,
       30, 50, 10, 34, 44, 36, 27, 29,  1, 23, 45, 49, 16, 48,  8,  5, 33,
       22, 47, 11, 42, 58, 20, 51, 24,  6]), 'cur_cost': 101238.0}, {'tour': array([55, 41, 19,  5, 43, 40, 30, 42, 11, 14, 27, 59, 49, 48, 22,  2,  0,
       26,  8, 44, 37, 54, 25, 45, 15, 58,  3,  7, 47, 38, 56, 28,  9, 29,
       33, 35, 52, 21, 51, 10, 31, 36, 17, 20, 24, 46, 12, 16, 53, 13, 18,
       34,  6,  1, 23,  4, 39, 32, 57, 50]), 'cur_cost': 87549.0}, {'tour': array([36, 23,  0, 41, 24, 55, 10, 52,  1,  6, 35, 46, 31, 30, 44, 28, 21,
       38, 56, 51, 45, 59,  2,  5, 37, 20, 42, 14,  4, 53,  9,  3, 17, 43,
       33, 29, 22, 39, 18, 49, 27, 11, 47, 48, 16, 58, 12, 54, 25, 19, 40,
       26, 32, 57, 15, 34,  7, 50,  8, 13]), 'cur_cost': 110229.0}, {'tour': array([47, 38,  5, 37,  0, 48, 31, 11, 33, 20, 29, 45, 25, 16, 42, 34, 21,
       56, 10, 39,  8, 19, 18,  4, 49, 58, 14, 44, 53, 17, 36, 41, 55, 59,
       13,  2, 24,  7, 57,  3, 15, 52, 46, 12, 27, 30, 50,  1, 54, 40, 22,
       35,  9, 32, 43,  6, 51, 23, 26, 28]), 'cur_cost': 101560.0}, {'tour': array([43, 32, 23,  7, 58, 57, 17, 56, 25,  4, 53, 54, 11, 37, 40, 14, 28,
       24,  1, 30, 38, 51, 13, 50, 59,  6, 39, 44, 35, 49,  5, 10, 34, 26,
       22, 19, 45, 52, 27, 21, 42,  2, 12, 29, 18,  3, 47, 33, 48,  8,  0,
       31, 20, 16, 15, 46, 41, 36,  9, 55]), 'cur_cost': 91199.0}]
2025-06-26 19:17:34,854 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 19:17:34,854 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 315, 'skip_rate': 0.04126984126984127, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 302, 'cache_hits': 200, 'similarity_calculations': 4358, 'cache_hit_rate': 0.04589261128958238, 'cache_size': 4158}}
2025-06-26 19:17:34,854 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:17:34,854 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:17:34,856 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:17:34,856 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:17:34,856 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 105441.0
2025-06-26 19:17:35,361 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:17:35,361 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:17:35,361 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:17:35,377 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:17:35,377 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59], 'cur_cost': 21405.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': array([13,  2, 38, 28, 17, 53, 46, 37, 41, 40, 52, 19,  7, 56, 26, 18, 21,
       39,  4, 59, 31, 15, 25, 12, 54,  9,  0, 35, 57, 43, 55,  3, 32, 14,
       30, 50, 10, 34, 44, 36, 27, 29,  1, 23, 45, 49, 16, 48,  8,  5, 33,
       22, 47, 11, 42, 58, 20, 51, 24,  6]), 'cur_cost': 101238.0}, {'tour': array([55, 41, 19,  5, 43, 40, 30, 42, 11, 14, 27, 59, 49, 48, 22,  2,  0,
       26,  8, 44, 37, 54, 25, 45, 15, 58,  3,  7, 47, 38, 56, 28,  9, 29,
       33, 35, 52, 21, 51, 10, 31, 36, 17, 20, 24, 46, 12, 16, 53, 13, 18,
       34,  6,  1, 23,  4, 39, 32, 57, 50]), 'cur_cost': 87549.0}, {'tour': array([36, 23,  0, 41, 24, 55, 10, 52,  1,  6, 35, 46, 31, 30, 44, 28, 21,
       38, 56, 51, 45, 59,  2,  5, 37, 20, 42, 14,  4, 53,  9,  3, 17, 43,
       33, 29, 22, 39, 18, 49, 27, 11, 47, 48, 16, 58, 12, 54, 25, 19, 40,
       26, 32, 57, 15, 34,  7, 50,  8, 13]), 'cur_cost': 110229.0}, {'tour': array([52, 39, 42, 57, 47, 26,  1, 23, 43, 30, 32, 48, 25, 54, 35, 13,  8,
       41, 14, 36, 12, 11, 19,  0, 58, 33, 31, 28, 29, 40, 18, 22, 46, 17,
        3,  9, 51,  6, 24, 55, 21, 50, 34, 44, 38, 59, 16,  2, 53, 15, 37,
        4, 49, 10, 56, 20,  7,  5, 27, 45]), 'cur_cost': 105441.0}, {'tour': array([43, 32, 23,  7, 58, 57, 17, 56, 25,  4, 53, 54, 11, 37, 40, 14, 28,
       24,  1, 30, 38, 51, 13, 50, 59,  6, 39, 44, 35, 49,  5, 10, 34, 26,
       22, 19, 45, 52, 27, 21, 42,  2, 12, 29, 18,  3, 47, 33, 48,  8,  0,
       31, 20, 16, 15, 46, 41, 36,  9, 55]), 'cur_cost': 91199.0}]
2025-06-26 19:17:35,380 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:17:35,380 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 316, 'skip_rate': 0.04113924050632911, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 303, 'cache_hits': 200, 'similarity_calculations': 4374, 'cache_hit_rate': 0.04572473708276177, 'cache_size': 4174}}
2025-06-26 19:17:35,382 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:17:35,382 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:17:35,382 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:17:35,382 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:17:35,382 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 89293.0
2025-06-26 19:17:35,886 - ExploitationExpert - INFO - res_population_num: 39
2025-06-26 19:17:35,886 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9620, 9642, 9654]
2025-06-26 19:17:35,887 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 13, 21, 14, 18, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9, 11,  7,  2,  3, 18, 14, 21, 13, 19, 17, 15, 22,
       20, 16, 23, 12, 54, 53, 48, 59, 52, 57, 55, 56, 49, 58, 50, 51, 41,
       39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 31, 32, 26,  8, 10,  1], dtype=int64)]
2025-06-26 19:17:35,904 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:17:35,904 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59], 'cur_cost': 21405.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}, {'tour': array([13,  2, 38, 28, 17, 53, 46, 37, 41, 40, 52, 19,  7, 56, 26, 18, 21,
       39,  4, 59, 31, 15, 25, 12, 54,  9,  0, 35, 57, 43, 55,  3, 32, 14,
       30, 50, 10, 34, 44, 36, 27, 29,  1, 23, 45, 49, 16, 48,  8,  5, 33,
       22, 47, 11, 42, 58, 20, 51, 24,  6]), 'cur_cost': 101238.0}, {'tour': array([55, 41, 19,  5, 43, 40, 30, 42, 11, 14, 27, 59, 49, 48, 22,  2,  0,
       26,  8, 44, 37, 54, 25, 45, 15, 58,  3,  7, 47, 38, 56, 28,  9, 29,
       33, 35, 52, 21, 51, 10, 31, 36, 17, 20, 24, 46, 12, 16, 53, 13, 18,
       34,  6,  1, 23,  4, 39, 32, 57, 50]), 'cur_cost': 87549.0}, {'tour': array([36, 23,  0, 41, 24, 55, 10, 52,  1,  6, 35, 46, 31, 30, 44, 28, 21,
       38, 56, 51, 45, 59,  2,  5, 37, 20, 42, 14,  4, 53,  9,  3, 17, 43,
       33, 29, 22, 39, 18, 49, 27, 11, 47, 48, 16, 58, 12, 54, 25, 19, 40,
       26, 32, 57, 15, 34,  7, 50,  8, 13]), 'cur_cost': 110229.0}, {'tour': array([52, 39, 42, 57, 47, 26,  1, 23, 43, 30, 32, 48, 25, 54, 35, 13,  8,
       41, 14, 36, 12, 11, 19,  0, 58, 33, 31, 28, 29, 40, 18, 22, 46, 17,
        3,  9, 51,  6, 24, 55, 21, 50, 34, 44, 38, 59, 16,  2, 53, 15, 37,
        4, 49, 10, 56, 20,  7,  5, 27, 45]), 'cur_cost': 105441.0}, {'tour': array([ 1, 59, 46, 47, 31, 26, 39, 17, 52, 50, 55, 58, 14, 32,  5, 28, 27,
       29, 54, 38, 35,  2, 24,  3, 57,  6, 36, 15, 25, 49, 16,  7, 19, 23,
       56, 30, 43, 37, 20, 10, 22, 33, 44, 41, 42, 34, 51, 45, 12, 40,  4,
       48, 11,  0, 53,  9, 13, 21, 18,  8]), 'cur_cost': 89293.0}]
2025-06-26 19:17:35,906 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:17:35,906 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 317, 'skip_rate': 0.04100946372239748, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 304, 'cache_hits': 200, 'similarity_calculations': 4391, 'cache_hit_rate': 0.04554771122751082, 'cache_size': 4191}}
2025-06-26 19:17:35,906 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:17:35,908 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 0, 2, 4, 6, 8, 10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59], 'cur_cost': 21405.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 126094.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([55, 41, 19,  5, 43, 40, 30, 42, 11, 14, 27, 59, 49, 48, 22,  2,  0,
       26,  8, 44, 37, 54, 25, 45, 15, 58,  3,  7, 47, 38, 56, 28,  9, 29,
       33, 35, 52, 21, 51, 10, 31, 36, 17, 20, 24, 46, 12, 16, 53, 13, 18,
       34,  6,  1, 23,  4, 39, 32, 57, 50]), 'cur_cost': 87549.0}}]
2025-06-26 19:17:35,908 - __main__ - INFO - 进化阶段完成
2025-06-26 19:17:35,908 - __main__ - INFO - 开始评估阶段
2025-06-26 19:17:35,908 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:17:35,908 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:17:35,908 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 13233.0 → 21405.0 (Δ -8172.0)
- Mean cost: 48807.5 → 101953.1
- Diversity: 0.468 → 0.627 (Δ 0.1593)

#### Elite metrics
- Elite count: 39 → 39
- Best elite cost: 9614 → 9614 (Δ 0)

#### Strategy performance
- Explore: 5 inds, success 0.0% , avg Δ -69339.0
- Exploit: 5 inds, success 20.0% , avg Δ -36952.2

#### Other indicators
- No-change individuals: 1
- Regression individuals: 8
- Historical trends: Last 3 iterations: costs [91692.0, 83548.0, 13233.0], diversity [0.6329629629629631, 0.8274074074074075, 0.6466666666666667]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:17:35,911 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:17:47,710 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0.0% success, negative avg delta)",
    "exploit": "moderately effective (20.0% success, negative avg delta)"
  },
  "balance_state": "exploration insufficient (low success rate, high negative impact), exploitation needs refinement",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "mutation_intensity": "increase by 20%",
    "elite_preservation": "maintain current count (39)",
    "additional_notes": "Consider introducing new genetic operators if stagnation persists next iteration"
  }
}
```
2025-06-26 19:17:47,735 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:17:47,735 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0.0% success, negative avg delta)",
    "exploit": "moderately effective (20.0% success, negative avg delta)"
  },
  "balance_state": "exploration insufficient (low success rate, high negative impact), exploitation needs refinement",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "mutation_intensity": "increase by 20%",
    "elite_preservation": "maintain current count (39)",
    "additional_notes": "Consider introducing new genetic operators if stagnation persists next iteration"
  }
}
```
2025-06-26 19:17:47,735 - __main__ - INFO - 评估阶段完成
2025-06-26 19:17:47,735 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0.0% success, negative avg delta)",
    "exploit": "moderately effective (20.0% success, negative avg delta)"
  },
  "balance_state": "exploration insufficient (low success rate, high negative impact), exploitation needs refinement",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "mutation_intensity": "increase by 20%",
    "elite_preservation": "maintain current count (39)",
    "additional_notes": "Consider introducing new genetic operators if stagnation persists next iteration"
  }
}
```
2025-06-26 19:17:47,735 - __main__ - INFO - 当前最佳适应度: 21405.0
2025-06-26 19:17:47,735 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite12_60_route_4.pkl
2025-06-26 19:17:47,752 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite12_60_solution.json
2025-06-26 19:17:47,752 - __main__ - INFO - 实例 composite12_60 处理完成
