from loadinstance import load_all_instances
import pickle as pkl
from initpop import INIT
import random
import json
import re
import numpy as np
import logging
import time
import os
import sys
import copy
from datetime import datetime
import gls_run
from path_similarity_optimizer import PathSimilarityOptimizer
import gls_evol_enhanced
# 导入API接口
from api_general import InterfaceAPI

# 导入工具类
from idea_extractor import IdeaExtractor
from gls_evol_enhanced import route2tour, tour_cost
import utils

# 导入配置文件
from config import API_CONFIG, ALGORITHM_CONFIG, get_paths
import argparse

# 导入日志配置模块
from log_config import setup_logging, get_instance_log_file, get_logger

# 导入专家提示词模块
from experts_prompt import *

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 定义项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))

# 获取路径配置
paths = get_paths(project_root)
GLOBAL_INPUT_PATH = paths["input_path"]
GLOBAL_OUTPUT_PATH = paths["output_path"]

# 创建主模块的日志记录器
main_logger = get_logger(__name__)


class ExpertBase:
    """专家基类，所有专家模块继承自此类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def analyze(self, *args, **kwargs):
        """专家分析方法，由子类实现"""
        raise NotImplementedError
    
    def generate_report(self, analysis_result):
        """生成分析报告，由子类实现"""
        raise NotImplementedError


class StatsExpert(ExpertBase):
    """统计分析专家，负责种群的统计学特征分析"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, populations, distance_matrix=None):
        """分析种群的统计学特征"""
        self.logger.info("开始统计分析")
        
        # 计算成本统计
        costs = [p["cur_cost"] for p in populations]
        cost_stats = {
            "min": min(costs),
            "max": max(costs),
            "mean": sum(costs) / len(costs),
            "std": np.std(costs)
        }
        
        # 计算种群多样性
        diversity = utils.calculate_population_diversity(populations)
        
        # 聚类分析
        from stats_analyzer import PopulationAnalyzer
        clusters = PopulationAnalyzer.analyze_solution_clusters(populations)
        
        # 收敛度分析
        convergence = PopulationAnalyzer.common_edge_analysis(populations)
        
        analysis_result = {
            "population_size": len(populations),
            "cost_stats": cost_stats,
            "diversity": diversity,
            "clusters": clusters,
            "convergence": convergence
        }
        
        self.logger.info(f"统计分析完成: {analysis_result}")
        return analysis_result
    
    def generate_report(self, analysis_result, coordinates=None, distance_matrix=None):
        """生成统计分析报告
        新增 coordinates 和 distance_matrix 字段，供后续空间统计计算使用。"""
        report = {
            "population_size": analysis_result.get("population_size", 0),
            "cost_stats": analysis_result.get("cost_stats", {}),
            "diversity_level": analysis_result.get("diversity", 0),
            "convergence_level": analysis_result.get("convergence", 0),
            "clustering_info": analysis_result.get("clusters", {}),
            # 新增空间相关字段
            "coordinates": coordinates,
            "distance_matrix": distance_matrix
        }
        return report


class PathExpert(ExpertBase):
    """路径结构专家，分析路径结构特征"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, populations, distance_matrix):
        """分析路径结构特征"""
        self.logger.info("开始路径结构分析")
        
        # 验证输入数据
        if not populations:
            self.logger.warning("种群为空，无法进行路径结构分析")
            return {
                "high_quality_edges": [],
                "common_subpaths": [],
                "edge_frequency": {},
                "low_quality_regions": []
            }
        
        if distance_matrix is None or not hasattr(distance_matrix, 'shape'):
            self.logger.warning("距离矩阵无效，无法进行路径结构分析")
            return {
                "high_quality_edges": [],
                "common_subpaths": [],
                "edge_frequency": {},
                "low_quality_regions": []
            }
        
        # 验证tour中的索引是否有效
        matrix_size = distance_matrix.shape[0]
        valid_populations = []
        
        for individual in populations:
            tour = individual.get('tour', [])
            # 处理NumPy数组
            try:
                if isinstance(tour, np.ndarray):
                    if tour.size == 0:
                        continue
                else:
                    if not tour:
                        continue
            except ImportError:
                # 如果没有NumPy，使用标准检查
                if not tour:
                    continue
                
            # 检查tour中的索引是否有效
            invalid_indices = [idx for idx in tour if idx >= matrix_size]
            if invalid_indices:
                self.logger.warning(f"发现无效的城市索引: {invalid_indices}, 距离矩阵大小: {matrix_size}，跳过此路径")
                # 创建一个有效的tour副本，将无效索引替换为有效值
                valid_tour = [idx if idx < matrix_size else idx % matrix_size for idx in tour]
                individual_copy = individual.copy()
                individual_copy['tour'] = valid_tour
                valid_populations.append(individual_copy)
            else:
                valid_populations.append(individual)
        
        if not valid_populations:
            self.logger.warning("没有有效的路径可供分析")
            return {
                "high_quality_edges": [],
                "common_subpaths": [],
                "edge_frequency": {},
                "low_quality_regions": []
            }
        
        # 导入路径结构分析器
        from path_structure_analyzer import PathStructureAnalyzer
        
        try:
            # 分析高质量边
            high_quality_edges = PathStructureAnalyzer.identify_high_quality_edges(valid_populations, distance_matrix)
            
            # 分析公共子路径
            common_subpaths = PathStructureAnalyzer.identify_common_subpaths(valid_populations)
            
            # 边频率分析
            edge_frequency = PathStructureAnalyzer.analyze_edge_frequency(valid_populations)
            
            # 低质量区域识别
            low_quality_regions = PathStructureAnalyzer.identify_low_quality_regions(valid_populations, distance_matrix)
            
            analysis_result = {
                "high_quality_edges": high_quality_edges,
                "common_subpaths": common_subpaths,
                "edge_frequency": edge_frequency,
                "low_quality_regions": low_quality_regions
            }
        except Exception as e:
            self.logger.error(f"路径结构分析出错: {str(e)}")
            analysis_result = {
                "high_quality_edges": [],
                "common_subpaths": [],
                "edge_frequency": {},
                "low_quality_regions": []
            }
        
        self.logger.info("路径结构分析完成")
        return analysis_result
    
    def generate_report(self, analysis_result):
        """生成路径结构分析报告"""
        report = {
            "high_quality_edges": analysis_result.get("high_quality_edges", []),
            "common_subpaths": analysis_result.get("common_subpaths", []),
            "edge_frequency": analysis_result.get("edge_frequency", {}),
            "low_quality_regions": analysis_result.get("low_quality_regions", [])
        }
        return report


class EliteExpert(ExpertBase):
    """精英解分析专家，分析已发现的精英解"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, elite_solutions, populations, distance_matrix):
        """分析精英解特征"""
        self.logger.info("开始精英解分析")
        
        # 如果没有精英解，返回空结果
        # 处理NumPy数组
        try:
            if isinstance(elite_solutions, np.ndarray):
                # 对NumPy数组使用size属性检查是否为空
                if elite_solutions.size == 0:
                    self.logger.warning("没有精英解可供分析")
                    return {}
            else:
                # 标准列表处理
                if not elite_solutions:
                    self.logger.warning("没有精英解可供分析")
                    return {}
        except ImportError:
            # 如果没有NumPy，使用标准处理
            if not elite_solutions:
                self.logger.warning("没有精英解可供分析")
                return {}
        
        # 从stats_analyzer导入精英解分析方法
        from stats_analyzer import PopulationAnalyzer
        
        # 精英解共有特征分析
        common_features = PopulationAnalyzer.analyze_common_features(elite_solutions)
        
        # 精英解与当前种群的差异分析
        population_gap = PopulationAnalyzer.analyze_population_gap(elite_solutions, populations)
        
        # 固定位置节点识别
        fixed_nodes = PopulationAnalyzer.identify_fixed_nodes(elite_solutions)
        
        # 精英解多样性分析
        elite_diversity = PopulationAnalyzer.analyze_elite_diversity(elite_solutions)
        
        analysis_result = {
            "elite_count": len(elite_solutions),
            "common_features": common_features,
            "population_gap": population_gap,
            "fixed_nodes": fixed_nodes,
            "elite_diversity": elite_diversity
        }
        
        self.logger.info("精英解分析完成")
        return analysis_result
    
    def generate_report(self, analysis_result):
        """生成精英解分析报告"""
        report = {
            "elite_count": analysis_result.get("elite_count", 0),
            "elite_common_features": analysis_result.get("common_features", {}),
            "fixed_nodes": analysis_result.get("fixed_nodes", []),
            "population_gap": analysis_result.get("population_gap", {}),
            "elite_diversity": analysis_result.get("elite_diversity", {})
        }
        return report


class LandscapeExpert(ExpertBase):
    """景观分析专家，整合其他专家的分析结果"""
    
    def __init__(self, interface_llm):
        super().__init__()
        self.interface_llm = interface_llm
        # Use the optimized landscape prompt template from experts_prompt.py
        from experts_prompt import LANDSCAPE_PROMPT
        self.prompt_template = LANDSCAPE_PROMPT
    
    # 添加一个辅助函数用于转换numpy类型为Python原生类型
    @staticmethod
    def convert_numpy_types(obj):
        """将numpy类型转换为Python原生类型，以便JSON序列化
        
        Args:
            obj: 需要转换的对象
        
        Returns:
            转换后的对象
        """
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: LandscapeExpert.convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list) or isinstance(obj, tuple):
            return [LandscapeExpert.convert_numpy_types(i) for i in obj]
        else:
            return obj
    
    def analyze(self, stats_report, path_report, elite_report, iteration=0, total_iterations=10, history_data=None):
        """整合分析结果，生成综合景观分析"""
        self.logger.info("开始景观分析")
        
        # Import the optimized function from experts_prompt.py
        from experts_prompt import generate_landscape_expert_prompt
        
        # Generate the optimized prompt
        prompt = generate_landscape_expert_prompt(
            stats_report=stats_report, 
            path_report=path_report, 
            elite_report=elite_report,
            iteration=iteration,
            total_iterations=total_iterations,
            history_data=history_data
        )
        
        # 调用LLM获取分析结果
        self.logger.info("调用LLM进行景观分析")
        self.logger.info(f"发送给LLM的提示词: {prompt}")
        landscape_analysis_text = self.interface_llm.get_response(prompt)
        self.logger.info(f"LLM返回的分析结果: {landscape_analysis_text}")
        
        # 解析LLM返回的文本为JSON对象
        from experts_prompt import parse_expert_response
        landscape_analysis = parse_expert_response(landscape_analysis_text)
        
        # 如果解析失败，创建一个基本的结构化数据
        if "error" in landscape_analysis:
            self.logger.warning(f"解析景观分析结果失败: {landscape_analysis['error']}")
            # 创建一个基本结构，包含原始文本
            landscape_analysis = {
                "search_space_features": {"ruggedness": 0.5, "modality": "unknown", "deceptiveness": "unknown"},
                "population_state": {"diversity": 0.5, "convergence": 0.5, "clustering": "unknown"},
                "difficult_regions": [],
                "opportunity_regions": [],
                "evolution_phase": "unknown",
                "evolution_direction": {"recommended_focus": "balance", "operators": []},
                "raw_text": landscape_analysis_text  # 保留原始文本以备后用
            }
        
        self.logger.info("=====景观分析完成====")
        return landscape_analysis
    
    def generate_report(self, analysis_result):
        """直接返回LLM生成的分析结果"""
        return analysis_result


class StrategyExpert(ExpertBase):
    """增强的策略选择专家，集成自适应学习和多层次决策"""

    def __init__(self, interface_llm):
        super().__init__()
        self.interface_llm = interface_llm
        # Use the optimized strategy prompt template from experts_prompt.py
        from experts_prompt import STRATEGY_PROMPT
        self.prompt_template = STRATEGY_PROMPT

        # 集成自适应策略专家
        try:
            from adaptive_strategy_expert import AdaptiveStrategyExpert
            self.adaptive_expert = AdaptiveStrategyExpert()
            self.use_adaptive_mode = True
        except ImportError:
            self.adaptive_expert = None
            self.use_adaptive_mode = False

        # 策略执行历史记录
        self.strategy_execution_history = []

        # 集成自适应策略专家
        from adaptive_strategy_expert import AdaptiveStrategyExpert
        self.adaptive_expert = AdaptiveStrategyExpert()

        # 策略执行历史记录
        self.strategy_execution_history = []
        self.use_adaptive_mode = True  # 是否使用自适应模式
    
    def analyze(self, landscape_report, populations, iteration, strategy_feedback=None):
        """基于景观分析，为每个个体分配策略"""
        self.logger.info("开始策略分配分析")

        # 如果启用自适应模式且自适应专家可用，优先使用自适应分析
        if self.use_adaptive_mode and self.adaptive_expert:
            try:
                strategy_selection, strategy_response = self.adaptive_expert.analyze(
                    landscape_report, populations, iteration, strategy_feedback
                )

                # 记录策略执行历史
                self.strategy_execution_history.append({
                    'iteration': iteration,
                    'strategy_selection': strategy_selection,
                    'landscape_context': landscape_report,
                    'feedback': strategy_feedback
                })

                self.logger.info(f"自适应策略分配完成: {strategy_selection}")
                return strategy_selection, strategy_response

            except Exception as e:
                self.logger.warning(f"自适应策略分析失败，回退到传统方法: {e}")
                self.use_adaptive_mode = False

        # 传统的LLM基础策略分析
        return self._traditional_strategy_analysis(landscape_report, populations, iteration, strategy_feedback)

    def _traditional_strategy_analysis(self, landscape_report, populations, iteration, strategy_feedback):
        """传统的基于LLM的策略分析"""
        # Import the optimized function from experts_prompt.py
        from experts_prompt import generate_strategy_expert_prompt

        # Generate the optimized prompt
        prompt = generate_strategy_expert_prompt(
            landscape_report=landscape_report,
            population=populations,
            previous_feedback=strategy_feedback,
            iteration=iteration
        )

        # 调用LLM获取策略分配结果
        self.logger.info(f"发送给LLM的策略分配提示词: {prompt}")
        self.logger.info("调用LLM进行策略分配")
        strategy_response = self.interface_llm.get_response(prompt)
        self.logger.info(f"LLM返回的策略分配结果: {strategy_response}")

        # 解析策略分配结果
        try:
            # Import the response parsing function
            from experts_prompt import parse_expert_response

            # Parse the response
            strategy_data = parse_expert_response(strategy_response)

            if "error" in strategy_data:
                self.logger.warning(f"解析策略分配结果时出错: {strategy_data['error']}")
                # 使用默认策略
                strategy_selection = ["explore" if i % 2 == 0 else "exploit" for i in range(len(populations))]
            else:
                individual_assignments = strategy_data.get("individual_assignments", {})

                # 转换为策略列表
                strategy_selection = []
                for i in range(len(populations)):
                    strategy = individual_assignments.get(str(i), "explore")
                    strategy_selection.append(strategy)
        except Exception as e:
            self.logger.error(f"解析策略分配结果时出错: {str(e)}")
            # 使用默认策略
            strategy_selection = ["explore" if i % 2 == 0 else "exploit" for i in range(len(populations))]

        self.logger.info(f"传统策略分配完成: {strategy_selection}")
        return strategy_selection, strategy_response

    def update_strategy_feedback(self, strategy_results):
        """更新策略反馈信息"""
        if self.adaptive_expert:
            self.adaptive_expert._update_strategy_performance(strategy_results)

    def get_strategy_performance_stats(self):
        """获取策略性能统计"""
        if self.adaptive_expert:
            return self.adaptive_expert.get_performance_stats()
        return {}
    
    def generate_report(self, analysis_result):
        """生成策略分配报告"""
        strategy_selection, strategy_response = analysis_result
        return {
            "strategy_selection": strategy_selection,
            "strategy_response": strategy_response
        }


class ExplorationExpert(ExpertBase):
    """增强的探索路径生成专家，集成混合生成策略"""

    def __init__(self, interface_llm):
        super().__init__()
        self.interface_llm = interface_llm
        # Use the optimized exploration prompt template from experts_prompt.py
        from experts_prompt import EXPLORATION_PROMPT
        self.prompt_template = EXPLORATION_PROMPT
        self.knowledge_base = None  # 暂不实现知识库

        # 集成混合探索专家
        try:
            from hybrid_exploration_expert import HybridExplorationExpert
            self.hybrid_expert = HybridExplorationExpert(interface_llm, heuristic_ratio=0.7)
            self.use_hybrid_mode = True
        except ImportError:
            self.hybrid_expert = None
            self.use_hybrid_mode = False

        # 路径生成统计
        self.generation_stats = {
            'heuristic_count': 0,
            'llm_count': 0,
            'success_count': 0,
            'failure_count': 0
        }
        
    def calculate_path_cost(self, tour, distance_matrix):
        """计算路径的总成本
        
        参数:
            tour (list): 旅行路径
            distance_matrix (numpy.ndarray): 距离矩阵
            
        返回:
            float: 路径总成本
        """
        self.logger.info("计算路径成本")
        return tour_cost(distance_matrix, tour)
    
    def generate_path(self, individual, landscape_report, populations, distance_matrix, individual_index, evo_populations=None, res_populations=None):
        """生成探索路径，并更新进化种群和精英解种群

        参数:
            individual: 当前个体
            landscape_report: 景观分析报告
            populations: 当前种群
            distance_matrix: 距离矩阵
            individual_index: 个体在种群中的索引
            evo_populations: 进化种群，用于存储生成的路径
            res_populations: 精英解种群，用于存储高质量解
        """
        self.logger.info("开始生成探索路径")

        # 初始化种群列表（如果未提供）
        if populations is None:
            populations = []
        if res_populations is None:
            res_populations = []

        # 如果启用混合模式，优先使用混合探索专家
        if self.use_hybrid_mode and self.hybrid_expert:
            try:
                result = self.hybrid_expert.generate_path(
                    individual, landscape_report, populations, distance_matrix,
                    individual_index=individual_index,
                    evo_populations=evo_populations,
                    res_populations=res_populations
                )

                if result and "new_tour" in result:
                    # 更新统计信息
                    method = result.get("generation_method", "unknown")
                    if method == "heuristic":
                        self.generation_stats['heuristic_count'] += 1
                    elif method == "llm_guided":
                        self.generation_stats['llm_count'] += 1

                    self.generation_stats['success_count'] += 1

                    # 更新种群
                    if individual_index < len(populations):
                        populations[individual_index]["tour"] = result["new_tour"]
                        populations[individual_index]["cur_cost"] = result["cur_cost"]

                    self.logger.info(f"混合探索成功，方法: {method}, 成本: {result['cur_cost']}")
                    return result
                else:
                    self.logger.warning("混合探索返回无效结果，回退到传统方法")

            except Exception as e:
                self.logger.warning(f"混合探索失败，回退到传统方法: {e}")
                self.generation_stats['failure_count'] += 1

        # 传统的LLM基础探索方法
        return self._traditional_exploration(individual, landscape_report, populations, distance_matrix, individual_index, evo_populations, res_populations)

    def _traditional_exploration(self, individual, landscape_report, populations, distance_matrix, individual_index, evo_populations, res_populations):
        """传统的基于LLM的探索方法"""
        
        # Import the optimized function from experts_prompt.py
        from experts_prompt import generate_exploration_expert_prompt
        
        # Prepare strategy parameters
        strategy_params = {
            "diversity_weight": 0.7,  # 高多样性权重
            "risk_tolerance": 0.6,    # 中等风险容忍度
        }
        
        # Generate the optimized prompt
        prompt = generate_exploration_expert_prompt(
            individual=individual,
            population=populations,
            landscape_report=landscape_report,
            strategy_params=strategy_params,
            distance_matrix=distance_matrix  # 传递距离矩阵参数
        )
        
        # 调用LLM生成探索路径
        self.logger.info(f"发送给LLM的探索路径生成提示词: {prompt}")
        self.logger.info("调用LLM生成探索路径")
        exploration_response = self.interface_llm.get_response(prompt)
        self.logger.info(f"LLM生成的探索路径: {exploration_response}")
        
        # Import the response parsing function
        from experts_prompt import parse_expert_response
        
        # 提取生成的路径
        idea_extractor = IdeaExtractor()
        try:
            # First try to parse as JSON
            parsed_response = parse_expert_response(exploration_response)
            
            if "error" not in parsed_response and "new_path" in parsed_response:
                # Successfully parsed JSON with new_path
                new_tour = parsed_response["new_path"]
                exploration_path = {"new_tour": new_tour}
            else:
                # Fall back to the original extraction method
                exploration_path = idea_extractor.extract_exploration_path(
                    response=exploration_response,
                    original_tour=individual["tour"]
                )
            
            # 如果成功提取到路径，计算成本并更新种群
            if exploration_path and "new_tour" in exploration_path:
                # 验证和修复路径
                new_tour = exploration_path["new_tour"]
                
                # 获取距离矩阵大小，确定有效的节点索引范围
                max_node_index = distance_matrix.shape[0] - 1 if distance_matrix is not None and hasattr(distance_matrix, 'shape') else 65  # 默认最大索引为65
                
                # 检查并修复无效的节点索引
                valid_tour = []
                for node in new_tour:
                    if isinstance(node, (int, float)) and 0 <= node <= max_node_index:
                        valid_tour.append(int(node))
                    else:
                        # 如果节点索引无效，用一个有效的随机索引替换
                        import random
                        valid_tour.append(random.randint(0, max_node_index))
                
                # 确保路径长度正确
                expected_length = len(individual["tour"]) if individual and "tour" in individual else max_node_index + 1
                
                if len(valid_tour) < expected_length:
                    # 如果路径太短，添加缺失的节点
                    missing_nodes = [i for i in range(max_node_index + 1) if i not in valid_tour]
                    import random
                    random.shuffle(missing_nodes)
                    valid_tour.extend(missing_nodes[:expected_length - len(valid_tour)])
                
                if len(valid_tour) > expected_length:
                    # 如果路径太长，截断
                    valid_tour = valid_tour[:expected_length]
                
                # 确保没有重复节点
                if len(set(valid_tour)) < len(valid_tour):
                    # 如果有重复节点，创建一个无重复的路径
                    unique_nodes = []
                    for node in valid_tour:
                        if node not in unique_nodes:
                            unique_nodes.append(node)
                    
                    # 添加缺失的节点
                    missing_nodes = [i for i in range(max_node_index + 1) if i not in unique_nodes]
                    import random
                    random.shuffle(missing_nodes)
                    
                    # 确保路径长度正确
                    while len(unique_nodes) < expected_length and missing_nodes:
                        unique_nodes.append(missing_nodes.pop())
                    
                    valid_tour = unique_nodes[:expected_length]
                
                # 更新探索路径
                exploration_path["new_tour"] = valid_tour
                
                # 计算新路径的成本
                exploration_path["cur_cost"] = self.calculate_path_cost(exploration_path["new_tour"], distance_matrix)
                # 创建进化个体的副本
                evo_individual = copy.deepcopy(individual)
                evo_individual["tour"] = exploration_path["new_tour"]
                evo_individual["cur_cost"] = exploration_path["cur_cost"]
                
                # 更新种群中对应位置的路径
                populations[individual_index]["tour"] = evo_individual["tour"]
                populations[individual_index]["cur_cost"] = evo_individual["cur_cost"]
                self.logger.info(f"探索路径生成完成，成本: {exploration_path['cur_cost']}, 路径: {exploration_path['new_tour']}")
                return exploration_path
            else:
                self.logger.warning("未能提取有效的探索路径")
                return {}
        except Exception as e:
            self.logger.error(f"提取探索路径时出错: {str(e)}")
            return {}
    
    def _update_populations(self, evo_individual, populations, res_populations):
        """更新进化种群和精英解种群
        
        参数:
            evo_individual: 当前进化个体
            populations: 进化种群
            res_populations: 精英解种群
        """
        # 使用gls_evol_enhanced中的函数
        from gls_evol_enhanced import share_distance_o2a, is_tsplib_instance, calculate_path_similarity
        
        tour = evo_individual["tour"]
        cur_cost = evo_individual["cur_cost"]
        
        # 提取种群中的路径
        tours = [indival["tour"] for indival in populations]
        in_tours_flag, index = share_distance_o2a(tour, tours)
        
        # 提取精英解种群中的路径
        res_tours = [res_indival["tour"] for res_indival in res_populations]
        in_res_tours_flag, index_res = share_distance_o2a(tour, res_tours)
        
        # 获取当前最佳成本
        if res_populations:
            best_res_cost = min(res_populations, key=lambda x: x["cur_cost"])["cur_cost"]
        else:
            best_res_cost = float('inf')
        
        # 检查是否为TSPLIB实例
        is_tsplib = False
        if "func_name" in evo_individual and is_tsplib_instance(evo_individual["func_name"]):
            is_tsplib = True
        
        # 设置相似度阈值和成本比例参数
        similarity_threshold = 0.7  # 相似度阈值，可根据需要调整
        cost_ratio = 0.05  # 成本比例，即x值，可根据需要调整
        
        # 更新种群
        if not in_tours_flag:  # 如果路径不在进化种群中
            if is_tsplib:  # 对TSPLIB实例使用新的加入条件
                # 检查是否满足成本条件：当前成本小于等于(1+x)*最佳成本
                cost_condition = cur_cost <= best_res_cost * (1 + cost_ratio) if res_populations else True
                
                # 检查是否满足相似度条件：与所有精英解的相似度都小于等于y
                similarity_condition = True
                for res_tour in res_tours:
                    similarity = calculate_path_similarity(tour, res_tour)
                    if similarity > similarity_threshold:
                        similarity_condition = False
                        break
                
                # 同时满足成本条件和相似度条件时加入精英解
                if cost_condition and similarity_condition and not in_res_tours_flag:
                    res_populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到精英解种群(TSPLIB实例)，成本: {cur_cost}，成本比: {cur_cost/best_res_cost if best_res_cost > 0 else 'N/A'}")
                else:
                    # 否则添加到进化种群
                    populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到进化种群，成本: {cur_cost}")
            else:  # 非TSPLIB实例使用原有逻辑
                # 如果成本低于精英解且不在精英解中，添加到精英解
                if (cur_cost <= best_res_cost) and not in_res_tours_flag:
                    res_populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到精英解种群，成本: {cur_cost}")
                else:
                    # 否则添加到进化种群
                    populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到进化种群，成本: {cur_cost}")
        else:  # 如果路径已在进化种群中
            if is_tsplib:  # 对TSPLIB实例使用新的加入条件
                if not in_res_tours_flag:
                    # 检查是否满足成本条件
                    cost_condition = cur_cost <= best_res_cost * (1 + cost_ratio) if res_populations else True
                    
                    # 检查是否满足相似度条件
                    similarity_condition = True
                    for res_tour in res_tours:
                        similarity = calculate_path_similarity(tour, res_tour)
                        if similarity > similarity_threshold:
                            similarity_condition = False
                            break
                    
                    # 同时满足成本条件和相似度条件时加入精英解
                    if cost_condition and similarity_condition:
                        res_populations.append(copy.deepcopy(populations[index]))
                        self.logger.info(f"将进化种群中的路径移动到精英解种群(TSPLIB实例)，成本: {cur_cost}，成本比: {cur_cost/best_res_cost if best_res_cost > 0 else 'N/A'}")
                # 如果已在精英解中，从进化种群中移除
                elif in_res_tours_flag:
                    populations.pop(index)
                    self.logger.info("从进化种群中移除已存在于精英解的路径")
            else:  # 非TSPLIB实例使用原有逻辑
                # 检查是否应该移动到精英解
                if (cur_cost <= best_res_cost) and not in_res_tours_flag:
                    res_populations.append(copy.deepcopy(populations[index]))
                    self.logger.info(f"将进化种群中的路径移动到精英解种群，成本: {cur_cost}")
                # 如果已在精英解中，从进化种群中移除
                elif in_res_tours_flag:
                    populations.pop(index)
                    self.logger.info("从进化种群中移除已存在于精英解的路径")
        
        self.logger.info(f"当前进化种群大小: {len(populations)}, 精英解种群大小: {len(res_populations)}")

    def get_generation_stats(self):
        """获取路径生成统计信息"""
        total_attempts = self.generation_stats['success_count'] + self.generation_stats['failure_count']
        success_rate = self.generation_stats['success_count'] / total_attempts if total_attempts > 0 else 0

        return {
            'total_attempts': total_attempts,
            'success_rate': success_rate,
            'heuristic_ratio': self.generation_stats['heuristic_count'] / total_attempts if total_attempts > 0 else 0,
            'llm_ratio': self.generation_stats['llm_count'] / total_attempts if total_attempts > 0 else 0,
            'hybrid_mode_enabled': self.use_hybrid_mode,
            'detailed_stats': self.generation_stats.copy()
        }


class ExploitationExpert(ExpertBase):
    """增强的开发专家，集成自适应局部搜索和记忆增强搜索"""

    def __init__(self, interface_llm, similarity_threshold=1.0):
        """初始化增强的开发专家

        参数:
            interface_llm: LLM接口
            similarity_threshold: 相似度阈值，当路径相似度超过此阈值时，跳过局部搜索
        """
        super().__init__()
        self.interface_llm = interface_llm  # 保留接口但不使用

        # 使用优化的路径相似度优化器替代简单的路径存储列表
        self.path_optimizer = PathSimilarityOptimizer(similarity_threshold=similarity_threshold)

        # 集成自适应开发专家
        try:
            from adaptive_exploitation_expert import AdaptiveExploitationExpert
            self.adaptive_expert = AdaptiveExploitationExpert(similarity_threshold)
            self.use_adaptive_mode = True
        except ImportError:
            self.adaptive_expert = None
            self.use_adaptive_mode = False

        # 记录性能统计信息
        self.skipped_searches = 0
        self.total_searches = 0
        self.search_time_saved = 0  # 估计节省的搜索时间（秒）
    
    def check_path_similarity(self, path):
        """检查路径与已存储路径的相似度
        
        参数:
            path: 待检查的路径
            
        返回:
            bool: 如果与任何已存储路径的相似度超过阈值，返回True；否则返回False
        """
        # 使用路径优化器检查相似度
        is_similar, similar_id, similarity = self.path_optimizer.check_similarity(path)
        
        if is_similar:
            self.logger.info(f"发现相似路径，相似度: {similarity:.4f}，相似id：{similar_id},跳过局部搜索")
            self.skipped_searches += 1
            # 估计节省的搜索时间（假设每次搜索平均需要5秒）
            self.search_time_saved += 5
        
        self.total_searches += 1
        
        return is_similar
    
    def calculate_path_similarity(self, path1, path2):
        """计算两条路径之间的相似度
        
        参数:
            path1, path2: 两个路径
        
        返回:
            float: 相似度，范围[0,1]，值越大表示越相似
        """
        # 使用路径优化器计算相似度
        return self.path_optimizer.calculate_similarity(path1, path2)

    def get_performance_stats(self):
        """获取性能统计信息
        
        返回:
            dict: 性能统计信息
        """
        # 获取路径优化器的统计信息
        optimizer_stats = self.path_optimizer.get_statistics()
        
        # 计算跳过率
        skip_rate = self.skipped_searches / max(1, self.total_searches)
        
        # 合并统计信息
        stats = {
            "skipped_searches": self.skipped_searches,
            "total_searches": self.total_searches,
            "skip_rate": skip_rate,
            "estimated_time_saved": self.search_time_saved,
            "path_optimizer": optimizer_stats
        }
        return stats
    
    def generate_path(self, individual, landscape_report, populations, distance_matrix, individual_index, res_populations=None):
        """生成利用路径，使用自适应局部搜索和传统算法

        参数:
            individual: 当前个体
            landscape_report: 景观分析报告
            populations: 当前种群
            distance_matrix: 距离矩阵
            individual_index: 个体在种群中的索引
            res_populations: 精英解种群，用于存储高质量解
        """
        self.logger.info("开始生成利用路径")

        # 初始化种群列表（如果未提供）
        if populations is None:
            populations = []
        if res_populations is None:
            res_populations = []

        # 如果启用自适应模式，优先使用自适应开发专家
        if self.use_adaptive_mode and self.adaptive_expert:
            try:
                result = self.adaptive_expert.generate_path(
                    individual, landscape_report, populations, distance_matrix,
                    individual_index=individual_index,
                    res_populations=res_populations
                )

                if result and "new_tour" in result:
                    # 更新种群
                    if individual_index < len(populations):
                        populations[individual_index]["tour"] = result["new_tour"]
                        populations[individual_index]["cur_cost"] = result["cur_cost"]

                    self.logger.info(f"自适应开发成功，改进: {result.get('improvement', 0):.2f}")
                    return result
                else:
                    self.logger.warning("自适应开发返回无效结果，回退到传统方法")

            except Exception as e:
                self.logger.warning(f"自适应开发失败，回退到传统方法: {e}")

        # 传统的局部搜索方法
        return self._traditional_exploitation(individual, populations, distance_matrix, individual_index, res_populations)

    def _traditional_exploitation(self, individual, populations, distance_matrix, individual_index, res_populations):
        """传统的基于局部搜索的开发方法"""
        self.logger.info("使用传统局部搜索和扰动算法")

        # 找出精英解
        elite_solutions = sorted(populations, key=lambda x: x["cur_cost"])[:3]
        
        # 使用局部搜索加扰动策略
        try:
            # 导入优化版的局部搜索和拓扑感知扰动模块
            from gls_evol_enhanced import route2tour, tour_cost
            from optimized_topology_aware_perturbation import topology_aware_perturbation
            from time_tracker import time_tracker
            
            self.logger.info("使用优化版局部搜索加拓扑感知扰动策略优化路径")
            
            # 提取精英解的路径列表，用于拓扑感知扰动
            known_solutions = [e["tour"] for e in elite_solutions]
            # 添加已有的精英解到known_solutions
            if res_populations:
                res_tours = [res_indival["tour"] for res_indival in res_populations]
                known_solutions.extend(res_tours)
                # 去重
                known_solutions = list({tuple(sol.tolist()) if isinstance(sol, np.ndarray) else (tuple(sol) if isinstance(sol, list) else sol) for sol in known_solutions})
                known_solutions = [np.array(sol) if isinstance(sol, tuple) else (list(sol) if isinstance(sol, tuple) else sol) for sol in known_solutions]
            
            # 创建临时进化个体和种群，用于局部搜索过程中更新
            evo_individual = copy.deepcopy(individual)
            
            # 确保输入的tour是正确的格式
            input_tour = individual["tour"]
            if isinstance(input_tour, list):
                input_tour = np.array(input_tour)
            
            # 记录开始时间
            start_time = time.time()

            from greedy_path_generator import generate_path
            new_tour, new_cost = generate_path(distance_matrix)
            
            # 更新种群中对应位置的路径
            populations[individual_index]["tour"] = new_tour
            populations[individual_index]["cur_cost"] = new_cost
            
            self.logger.info(f"已用贪心路径替换种群中索引 {individual_index} 处的路径，新成本: {new_cost}")
            
            # 检查路径相似度，如果与已搜索路径相似度高，则跳过局部搜索并生成一条新的贪心路径替换
            if self.check_path_similarity(input_tour):
                self.logger.info("路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换")
               
                return {"new_tour": new_tour, "cur_cost": new_cost}
            
            # 将当前路径添加到路径优化器中
            self.path_optimizer.add_path(input_tour)
            
            # 执行优化版局部搜索，并传递种群参数
            init_cost = tour_cost(distance_matrix, individual["tour"])
            init_route = np.array(gls_evol_enhanced.tour2route(input_tour))
           
            gls_run.solve_instance( distance_matrix,  #第i个实例距离矩阵  #   #第i个实例的每个节点的坐标
                                    0.5,   #时间限制
                                    1000000,      #最大搜索次数
                                    5,#扰动时每次修改次数
                                    init_route,
                                    evo_individual,populations,res_populations)
            
            
            costs= [ind["cur_cost"] for ind in res_populations]
            tours= [ind["tour"] for ind in res_populations]
            self.logger.info("res_population_num: %s", len(res_populations))
            self.logger.info("res_population_costs: %s", costs)
            self.logger.info("res_populations: %s", tours)
            self.logger.info("populations_num: %s", len(populations))
            self.logger.info("populations: %s", populations)
            
            # 记录结束时间
            end_time = time.time()
            search_time = end_time - start_time
            
            # 更新统计信息
            self.logger.info(f"局部搜索耗时: {search_time:.2f}秒")
            staus= self.get_performance_stats()
            self.logger.info(f"路径优化器性能统计: {staus}")
           
                
        except Exception as e:
            self.logger.error(f"生成利用路径时出错: {str(e)}")
            # 发生错误时，也生成一条新的贪心路径替换
            try:
                from greedy_path_generator import generate_path
                new_tour, new_cost = generate_path(distance_matrix)
                
                # 更新种群中对应位置的路径
                populations[individual_index]["tour"] = new_tour
                populations[individual_index]["cur_cost"] = new_cost
                
                self.logger.info(f"发生错误后已用贪心路径替换种群中索引 {individual_index} 处的路径，新成本: {new_cost}")
                return {"new_tour": new_tour, "cur_cost": new_cost}
            except Exception as e2:
                self.logger.error(f"生成贪心路径时出错: {str(e2)}")
                return {"new_tour": individual["tour"], "cur_cost": individual["cur_cost"]}
    


class EvolutionAssessmentExpert(ExpertBase):
    """进化评估专家，评估策略效果，提供反馈"""
    
    def __init__(self, interface_llm):
        super().__init__()
        self.interface_llm = interface_llm
        # Use the optimized assessment prompt template from experts_prompt.py
        from experts_prompt import ASSESSMENT_PROMPT
        self.prompt_template = ASSESSMENT_PROMPT
        self.feedback_history = []

        # 集成多目标评估专家
        try:
            from multi_objective_assessment_expert import MultiObjectiveAssessmentExpert
            self.multi_objective_expert = MultiObjectiveAssessmentExpert()
            self.use_multi_objective_mode = True
        except ImportError:
            self.multi_objective_expert = None
            self.use_multi_objective_mode = False
    
    # 添加一个辅助函数用于转换numpy类型为Python原生类型
    @staticmethod
    def convert_numpy_types(obj):
        """将numpy类型转换为Python原生类型，以便JSON序列化
        
        Args:
            obj: 需要转换的对象
        
        Returns:
            转换后的对象
        """
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: EvolutionAssessmentExpert.convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list) or isinstance(obj, tuple):
            return [EvolutionAssessmentExpert.convert_numpy_types(i) for i in obj]
        else:
            return obj
    
    def evaluate(self, old_stats_report, new_stats_report, strategies, iteration, total_iterations, old_res_populations=None, new_res_populations=None):
        """评估进化结果并生成报告"""
        self.logger.info(f"--- Iteration {iteration} Assessment ---")

        # 如果启用多目标模式，优先使用多目标评估专家
        if self.use_multi_objective_mode and self.multi_objective_expert:
            try:
                # 提取种群数据
                old_population = self._extract_population_from_report(old_stats_report)
                new_population = self._extract_population_from_report(new_stats_report)

                # 构造策略结果（简化版本）
                strategy_results = self._construct_strategy_results(strategies, old_population, new_population)

                # 调用多目标评估专家
                multi_objective_report = self.multi_objective_expert.evaluate(
                    old_population, new_population, strategies, strategy_results,
                    iteration, total_iterations, old_res_populations, new_res_populations
                )

                # 记录多目标评估结果
                self.feedback_history.append({
                    'iteration': iteration,
                    'assessment_type': 'multi_objective',
                    'report': multi_objective_report,
                    'timestamp': iteration
                })

                self.logger.info(f"多目标评估完成，总体评分: {multi_objective_report.get('overall_score', 0):.3f}")
                return multi_objective_report

            except Exception as e:
                self.logger.warning(f"多目标评估失败，回退到传统方法: {e}")

        # 传统的LLM基础评估方法
        return self._traditional_evaluation(old_stats_report, new_stats_report, strategies, iteration, total_iterations, old_res_populations, new_res_populations)

    def _traditional_evaluation(self, old_stats_report, new_stats_report, strategies, iteration, total_iterations, old_res_populations, new_res_populations):
        """传统的基于LLM的评估方法"""
        # 使用新的统计报告生成提示词
        prompt = generate_assessment_expert_prompt(
            old_stats_report, new_stats_report, strategies,
            strategy_results=[], # strategy_results is not available here, pass empty
            iteration=iteration, total_iterations=total_iterations,
            old_res_populations=old_res_populations, new_res_populations=new_res_populations
        )

        # 调用LLM生成评估报告
        self.logger.info("调用LLM生成传统评估报告")
        self.logger.info(f"生成的提示词: {prompt}")
        assessment_response = self.interface_llm.get_response(prompt)
        self.logger.info("LLM生成的评估报告: %s", assessment_response)
        
        # 保存反馈历史 - 添加精英解统计信息
        
        # --- 兼容: old_stats_report / new_stats_report 可能是 dict（stats_report）或 list（population） ---
        def _extract_costs(entity):
            if isinstance(entity, list):
                return [p.get("cur_cost", 0) for p in entity if isinstance(p, dict)]
            elif isinstance(entity, dict):
                cs = entity.get("cost_stats", {})
                return [cs.get("min", 0), cs.get("max", 0), cs.get("mean", 0)]
            else:
                return [0]

        def _calc_diversity(entity):
            if isinstance(entity, list):
                return utils.calculate_population_diversity(entity)
            return entity.get("diversity_level", 0) if isinstance(entity, dict) else 0

        old_costs = _extract_costs(old_stats_report)
        new_costs = _extract_costs(new_stats_report)

        old_population_stats = {
            "min_cost": min(old_costs) if old_costs else 0,
            "max_cost": max(old_costs) if old_costs else 0,
            "mean_cost": sum(old_costs) / len(old_costs) if old_costs else 0,
            "diversity": _calc_diversity(old_stats_report)
        }

        new_population_stats = {
            "min_cost": min(new_costs) if new_costs else 0,
            "max_cost": max(new_costs) if new_costs else 0,
            "mean_cost": sum(new_costs) / len(new_costs) if new_costs else 0,
            "diversity": _calc_diversity(new_stats_report)
        }
        
        # 计算精英解数量变化
        old_elite_count = len(old_res_populations) if old_res_populations is not None else 0
        new_elite_count = len(new_res_populations) if new_res_populations is not None else 0
        
        self.feedback_history.append({
            "iteration": iteration,
            "old_stats": old_population_stats,
            "new_stats": new_population_stats,
            "strategy_distribution": {
                "explore": strategies.count("explore"),
                "exploit": strategies.count("exploit")
            },
            "elite_stats": {
                "old_elite_count": old_elite_count,
                "new_elite_count": new_elite_count,
                "best_cost_improvement": old_population_stats["min_cost"] - new_population_stats["min_cost"]
            }
        })
        
        # 限制历史记录长度
        if len(self.feedback_history) > 5:
            self.feedback_history.pop(0)

        self.logger.info("传统进化评估完成")
        return assessment_response

    def _extract_population_from_report(self, stats_report):
        """从统计报告中提取种群数据"""
        if isinstance(stats_report, list):
            return stats_report
        elif isinstance(stats_report, dict) and "population" in stats_report:
            return stats_report["population"]
        else:
            return []

    def _construct_strategy_results(self, strategies, old_population, new_population):
        """构造策略结果（简化版本）"""
        strategy_results = {}

        if isinstance(strategies, list) and old_population and new_population:
            # 统计策略使用情况和效果
            strategy_counts = {}
            strategy_improvements = {}

            for i, strategy in enumerate(strategies):
                if strategy not in strategy_counts:
                    strategy_counts[strategy] = 0
                    strategy_improvements[strategy] = []

                strategy_counts[strategy] += 1

                # 计算改进（如果有对应的新旧个体）
                if i < len(old_population) and i < len(new_population):
                    old_cost = old_population[i].get("cur_cost", float('inf'))
                    new_cost = new_population[i].get("cur_cost", float('inf'))
                    improvement = old_cost - new_cost
                    strategy_improvements[strategy].append(improvement)

            # 构造结果
            for strategy in strategy_counts:
                improvements = strategy_improvements[strategy]
                success_count = sum(1 for imp in improvements if imp > 0)

                strategy_results[strategy] = {
                    'usage_count': strategy_counts[strategy],
                    'success_rate': success_count / len(improvements) if improvements else 0,
                    'avg_improvement': np.mean(improvements) if improvements else 0,
                    'total_improvement': sum(improvements) if improvements else 0
                }

        return strategy_results

    def get_evaluation_statistics(self):
        """获取评估统计信息"""
        if self.multi_objective_expert:
            return self.multi_objective_expert.get_evaluation_statistics()
        else:
            return {
                'evaluation_count': len(self.feedback_history),
                'evaluation_mode': 'traditional_llm',
                'multi_objective_available': False
            }


class ExpertCollaborationManager:
    """专家协作管理器，管理专家间的交互
    根据优化建议，优化专家与LLM的交互方式：
    1. 保留景观分析、策略选择和评估专家与LLM的交互
    2. 统计分析、路径结构和精英解专家使用算法实现，不与LLM交互
    3. 探索部分继续使用LLM生成多样化路径
    4. 利用部分完全使用局部搜索和扰动代码，不使用LLM
    """

    def __init__(self, interface_llm, config=None):
        self.config = config or {}
        self.interface_llm = interface_llm
        self.experts = {}
        self._initialize_experts()
        self.logger = logging.getLogger(__name__)

        # 尝试集成改进的协作管理器
        self.improved_manager = None
        self.use_improved_mode = False
        try:
            from improved_collaboration_manager import ImprovedExpertCollaborationManager
            self.improved_manager = ImprovedExpertCollaborationManager(interface_llm, config)
            self.use_improved_mode = True
            self.logger.info("已启用改进的专家协作模式")
        except ImportError:
            self.logger.info("使用传统专家协作模式")

        # 添加专家间数据共享存储
        self.shared_data = {
            "high_quality_edges": [],
            "difficult_regions": [],
            "opportunity_regions": [],
            "elite_features": {},
            "population_diversity": 0.0,
            "convergence_level": 0.0
        }
    
    def _initialize_experts(self):
        """初始化所有专家模块"""
        # 这些专家使用算法实现，不与LLM交互
        self.experts['stats'] = StatsExpert()
        self.experts['path'] = PathExpert()
        self.experts['elite'] = EliteExpert()
        
        # 这些专家需要与LLM交互
        self.experts['landscape'] = LandscapeExpert(self.interface_llm)
        self.experts['strategy'] = StrategyExpert(self.interface_llm)
        self.experts['exploration'] = ExplorationExpert(self.interface_llm)
        self.experts['assessment'] = EvolutionAssessmentExpert(self.interface_llm)
        
        # 利用专家使用局部搜索和扰动算法，不使用LLM
        self.experts['exploitation'] = ExploitationExpert(self.interface_llm)
    
    def update_shared_data(self, key, value):
        """更新专家间共享数据"""
        if key in self.shared_data:
            self.shared_data[key] = value
            self.logger.info(f"更新共享数据: {key}")
    
    def run_analysis_phase(self, populations, res_populations, distance_matrix, iteration, total_iterations=10, coordinates=None):
        """运行分析阶段，包括统计、路径和精英分析"""
        self.logger.info(f"--- Running Analysis Phase (Iteration {iteration}) ---")

        # 如果启用改进模式，使用改进的协作决策
        if self.use_improved_mode and self.improved_manager:
            try:
                current_state = {
                    'populations': populations,
                    'res_populations': res_populations,
                    'distance_matrix': distance_matrix,
                    'coordinates': coordinates
                }

                collaborative_result = self.improved_manager.collaborative_decision_making(
                    current_state, iteration, total_iterations
                )

                # 提取景观分析结果
                landscape_analysis = collaborative_result.get('landscape_analysis')
                if landscape_analysis:
                    landscape_report = landscape_analysis.get('report')
                    self.update_shared_data('landscape_report', landscape_report)

                    # 生成简化的统计报告用于兼容性
                    stats_analysis = self.experts["stats"].analyze(populations)
                    stats_report = self.experts["stats"].generate_report(stats_analysis, coordinates, distance_matrix)

                    self.logger.info("使用改进协作模式完成分析阶段")
                    return landscape_report, stats_report
                else:
                    self.logger.warning("改进协作模式未返回景观分析，回退到传统模式")

            except Exception as e:
                self.logger.warning(f"改进协作模式失败，回退到传统模式: {e}")

        # 传统分析模式
        return self._traditional_analysis_phase(populations, res_populations, distance_matrix, iteration, total_iterations, coordinates)

    def _traditional_analysis_phase(self, populations, res_populations, distance_matrix, iteration, total_iterations, coordinates):
        """传统分析阶段"""
        # 引入空间统计计算函数
        from experts_prompt import compute_spatial_stats

        # 运行本地分析专家
        stats_analysis = self.experts["stats"].analyze(populations)
        stats_report = self.experts["stats"].generate_report(stats_analysis, coordinates, distance_matrix)

        path_analysis = self.experts["path"].analyze(populations, distance_matrix)
        path_report = self.experts["path"].generate_report(path_analysis)

        elite_solutions = sorted(res_populations, key=lambda p: p['cur_cost']) if res_populations else []
        elite_report = self.experts["elite"].analyze(elite_solutions, populations, distance_matrix)

        # 运行景观分析专家
        landscape_report = self.experts["landscape"].analyze(
            stats_report, path_report, elite_report, iteration, total_iterations,
            history_data={'spatial_stats': compute_spatial_stats(coordinates, distance_matrix)}
        )

        self.update_shared_data('landscape_report', landscape_report)
        return landscape_report, stats_report
    
    def run_strategy_phase(self, landscape_report, populations, iteration, strategy_feedback=None):
        """运行策略分配阶段"""
        self.logger.info("开始策略分配阶段")
        
        # 使用优化后的策略专家
        strategy_result = self.experts['strategy'].analyze(
            landscape_report=landscape_report,
            populations=populations,
            iteration=iteration,
            strategy_feedback=strategy_feedback
        )
        
        # 记录策略分配报告
        strategy_selection, strategy_response = strategy_result
        self.logger.info(f"策略分配报告: {strategy_selection}")
        self.logger.info(f"策略分配完整报告: {strategy_response}")
        
        self.logger.info("策略分配阶段完成")
        return strategy_result
    
    def run_evolution_phase(self, populations, strategies, landscape_report, distance_matrix, res_populations=None):
        """运行进化阶段，生成新路径"""
        self.logger.info("开始进化阶段")
        
        new_populations = []
        evolution_reports = []
        
        for i, individual in enumerate(populations):
            # 将共享数据传递给专家
            if strategies[i] == 'explore':
                self.logger.info(f"为个体 {i} 生成探索路径")
                new_path_data = self.experts['exploration'].generate_path(
                    individual=individual,
                    landscape_report=landscape_report,  # 包含了共享数据
                    populations=populations,
                    distance_matrix=distance_matrix,
                    individual_index=i,
                    evo_populations=None,
                    res_populations=res_populations
                )
                
                # 记录探索路径生成报告
                if new_path_data:
                    evolution_reports.append({"individual": i, "strategy": "explore", "path_data": new_path_data})
                    self.logger.info(f"个体 {i} 探索路径生成报告: {new_path_data}")
                
                if new_path_data and 'new_tour' in new_path_data:
                    # 创建新个体
                    new_tour = new_path_data['new_tour']
                    new_cost = tour_cost(distance_matrix, new_tour)
                    
                    new_individual = {
                        "tour": utils.convert_to_list(new_tour),
                        "cur_cost": new_cost
                    }
                else:
                    # 如果生成失败，保留原个体
                    new_individual = copy.deepcopy(individual)
            else:  # exploit
                self.logger.info(f"为个体 {i} 生成利用路径")
                
                # 传递共享数据给利用专家
                exploitation_landscape_report = {
                    "high_quality_edges": self.shared_data["high_quality_edges"],
                    "fixed_nodes": self.shared_data.get("elite_features", {}).get("fixed_nodes", []),
                    "low_quality_regions": self.shared_data["difficult_regions"]
                }
                
                new_path_data = self.experts['exploitation'].generate_path(
                    individual=individual,
                    landscape_report=exploitation_landscape_report,  # 使用共享数据
                    populations=populations,
                    distance_matrix=distance_matrix,
                    individual_index=i,
                    res_populations=res_populations  # 传递精英解集合
                )
                
                # 记录利用路径生成报告
                if new_path_data:
                    evolution_reports.append({"individual": i, "strategy": "exploit", "path_data": new_path_data})
                    self.logger.info(f"个体 {i} 利用路径生成报告: {new_path_data}")
                
                if new_path_data and 'new_tour' in new_path_data:
                    # 创建新个体
                    new_tour = new_path_data['new_tour']
                    new_cost = tour_cost(distance_matrix, new_tour)
                    
                    new_individual = {
                        "tour": utils.convert_to_list(new_tour),
                        "cur_cost": new_cost
                    }
                else:
                    # 如果生成失败，保留原个体
                    self.logger.warning(f"个体 {i} 的利用路径生成失败，保留原个体")
                    new_individual = copy.deepcopy(individual)
            
            new_populations.append(new_individual)
        
        # 记录整体进化报告
        self.logger.info(f"进化阶段报告汇总: {evolution_reports}")
        self.logger.info("进化阶段完成")
        return new_populations
    
    def run_assessment_phase(self, old_stats_report, new_stats_report, strategies, iteration, total_iterations, old_res_populations=None, new_res_populations=None):
        """运行评估阶段"""
        self.logger.info(f"--- Running Assessment Phase (Iteration {iteration}) ---")
        
        assessment_report = self.experts["assessment"].evaluate(
            old_stats_report, new_stats_report, strategies, iteration, total_iterations,
            old_res_populations=old_res_populations, new_res_populations=new_res_populations
        )
        
        # 更新共享数据
        self.update_shared_data('assessment_report', assessment_report)
        return assessment_report

    def get_collaboration_statistics(self):
        """获取专家协作统计信息"""
        if self.use_improved_mode and self.improved_manager:
            return self.improved_manager.get_collaboration_statistics()
        else:
            # 传统模式的简单统计
            expert_stats = {}
            for expert_name, expert in self.experts.items():
                if hasattr(expert, 'get_generation_stats'):
                    expert_stats[expert_name] = expert.get_generation_stats()
                elif hasattr(expert, 'get_performance_stats'):
                    expert_stats[expert_name] = expert.get_performance_stats()
                elif hasattr(expert, 'get_evaluation_statistics'):
                    expert_stats[expert_name] = expert.get_evaluation_statistics()

            return {
                'collaboration_mode': 'traditional',
                'expert_statistics': expert_stats,
                'shared_data_keys': list(self.shared_data.keys()),
                'improved_mode_available': self.improved_manager is not None
            }


def main():
    """多专家系统主函数"""
    # 导入并调用JIT预热模块，确保在处理实际数据前完成JIT编译
    from jit_warmup import warmup_jit_functions
    warmup_jit_functions()
    
    parser = argparse.ArgumentParser(description="多专家协作进化算法框架")
    parser.add_argument('--func_begin', type=int, required=False, default=24, help='起始索引，默认为0')
    parser.add_argument('--func_end', type=int, required=False, default=24,help='结束索引，默认为0')
    parser.add_argument('--iter_num', type=int, help='迭代次数', default=5)
    parser.add_argument('--pop_size', type=int, help='种群规模', default=10)
    args = parser.parse_args()

    # 定义实例列表并处理空值
    func_name = [
        "simple1_9", "simple2_10", "simple3_10", "simple4_11", "simple5_12", "simple6_12",
        "geometry1_10", "geometry2_12", "geometry3_10", "geometry4_10", "geometry5_10", "geometry6_15",
        "composite1_28", "composite2_34", "composite3_22", "composite4_33", "composite5_35", "composite6_39", 
        "composite7_42", "composite8_45", "composite9_48", "composite10_55", "composite11_59", "composite12_60", "composite13_66",
        "eil51","berlin52","st70","pr76","kroA100","lin105"
    ]
    func_name = [name for name in func_name if name]

    # 设置func_end默认值
    if args.func_end == 0 or args.func_end >= len(func_name):
        args.func_end = args.func_begin

    # 验证参数有效性
    if args.func_begin < 0 or args.func_end >= len(func_name) or args.func_begin > args.func_end:
        print(f"错误：参数范围无效。有效范围: 0-{len(func_name)-1}，当前值: {args.func_begin}-{args.func_end}")
        sys.exit(1)
    
    # 设置日志系统
    # 修改日志保存路径到MoE-main/Log文件夹
    log_dir = os.path.join(os.path.dirname(current_dir), "Log")
    os.makedirs(log_dir, exist_ok=True)
    setup_logging(log_dir=log_dir, log_file="moe_app.log")
    
    # 打印路径信息以便调试
    main_logger.info(f"项目根目录: {project_root}")
    main_logger.info(f"输入路径: {GLOBAL_INPUT_PATH}")
    main_logger.info(f"输出路径: {GLOBAL_OUTPUT_PATH}")
    
    # 检查输入目录是否存在
    if not os.path.exists(GLOBAL_INPUT_PATH):
        print(f"错误: 输入目录不存在: {GLOBAL_INPUT_PATH}")
        return
    
    # 确保目录存在
    os.makedirs(GLOBAL_INPUT_PATH, exist_ok=True)
    os.makedirs(GLOBAL_OUTPUT_PATH, exist_ok=True)
    
    # 从配置文件获取算法参数
    pop_size = args.pop_size if args.pop_size else ALGORITHM_CONFIG.get("pop_size", 20)
    evo_num = args.iter_num if args.iter_num else ALGORITHM_CONFIG.get("evo_num", 10)
    
    # 读取实例内容
    try:
        load_all_instances(func_name, GLOBAL_INPUT_PATH, args.func_begin, args.func_end, GLOBAL_OUTPUT_PATH)
        
        instance_file = os.path.join(GLOBAL_OUTPUT_PATH, "mmtsp_instances.pkl")
        
        if not os.path.exists(instance_file):
            print(f"警告: 实例文件不存在: {instance_file}")
            instances = {"func_name": [], "coordinate": [], "distance_matrix": [], "opt_cost": [], "opt_tour": []}
        else:
            with open(instance_file, "rb") as f: 
                instances = pkl.load(f)
                
        if len(instances["func_name"]) == 0:
            print("警告: 没有成功加载任何实例")
            return
            
    except Exception as e:
        print(f"错误: 加载或处理实例时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return
    
    # 准备实例列表
    instances_selected = []
    for i in range(len(instances["func_name"])):
        instance_selected = {
            "func_name": instances["func_name"][i],
            "coordinate": instances["coordinate"][i],
            "distance_matrix": instances["distance_matrix"][i],
        }
        instances_selected.append(instance_selected)
    
    # 从配置文件获取API配置
    xfyun_config = API_CONFIG["xfyun"]
    api_endpoint = xfyun_config["endpoint"]
    api_key = xfyun_config["api_key"]
    model_LLM = xfyun_config["model"]
    
    # API接口设置
    debug_mode = False
    interface_llm = InterfaceAPI(api_type="gemini", debug_mode=debug_mode)
    # interface_llm = InterfaceAPI(api_type="deepseek")
    
    # 初始化专家协作管理器
    collaboration_manager = ExpertCollaborationManager(interface_llm)
    
    # 主循环，对每个实例进行处理
    for iter_idx in range(len(instances_selected)):
        instance = instances_selected[iter_idx]
        main_logger.info(f"开始处理实例: {instance['func_name']}")
        
        # 初始化种群
        # 使用INIT类的静态方法初始化种群
        populations = INIT.mixed_init(instance['distance_matrix'], pop_size)
        # 计算种群成本
        populations = INIT.calculate_population_costs(populations, instance['distance_matrix'])
        cur_best_cost = min(populations, key=lambda x: x["cur_cost"])["cur_cost"]
        print(f"当前最佳适应度：{cur_best_cost}")
        main_logger.info(f"初始化种群完成，当前最佳适应度: {cur_best_cost}")
        
        # 为当前实例创建专用的日志文件处理器
        # 确保使用MoE-main/Log文件夹保存实例日志
        instance_log_dir = os.path.join(os.path.dirname(current_dir), "Log")
        os.makedirs(instance_log_dir, exist_ok=True)
        instance_log_file = get_instance_log_file(instance['func_name'], instance_log_dir)
        file_handler = logging.FileHandler(instance_log_file, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        root_logger = logging.getLogger('')
        root_logger.addHandler(file_handler)
        
        # 存储各代进化个体和精英解
        evo_populations = []
        res_populations = []  # 存储精英解
        old_res_populations = None  # 初始化上一代精英解为None
        strategy_feedback = None
        
        # 每个实例进化evo_num代
        for evo_iter in range(evo_num):
            main_logger.info(f"{instance['func_name']} 开始进化第 {evo_iter+1} 代")
            print(f"iter: {evo_iter}")
            
            # 1. 分析阶段
            landscape_report, old_stats_report = collaboration_manager.run_analysis_phase(
                populations=populations,
                res_populations=res_populations,
                coordinates=instance["coordinate"],
                distance_matrix=instance["distance_matrix"],
                iteration=evo_iter,
                total_iterations=evo_num
            )
            # 记录完整的景观分析报告
            main_logger.info(f"景观分析完整报告: {landscape_report}")
            
            # 2. 策略分配阶段
            strategy_result = collaboration_manager.run_strategy_phase(
                landscape_report=landscape_report,
                populations=populations,
                iteration=evo_iter,
                strategy_feedback=strategy_feedback
            )
            strategy_selection, strategy_response = strategy_result
            # 记录完整的策略分配报告
            main_logger.info(f"策略分配: {strategy_selection}")
            main_logger.info(f"策略分配完整报告: {strategy_response}")
            
            # 保存当前种群副本用于后续评估
            old_populations = copy.deepcopy(populations)
            
            # 3. 进化阶段
            new_populations = collaboration_manager.run_evolution_phase(
                populations=populations,
                strategies=strategy_selection,
                landscape_report=landscape_report,
                distance_matrix=instance["distance_matrix"],
                res_populations=res_populations  # 传递精英解集合
            )
            
            # 更新种群
            populations = new_populations
            res_populations.sort(key=lambda x: x["cur_cost"])
            
            # 4. 评估阶段
            # 为新种群计算统计数据
            stats_expert = collaboration_manager.experts["stats"]
            new_stats_analysis = stats_expert.analyze(populations)
            new_stats_report = stats_expert.generate_report(new_stats_analysis, instance["coordinate"], instance["distance_matrix"])

            assessment_report = collaboration_manager.run_assessment_phase(
                old_stats_report, new_stats_report, strategy_selection, evo_iter, evo_num,
                old_res_populations=res_populations, new_res_populations=res_populations
            )
            
            # 更新下一次迭代的输入
            strategy_feedback = assessment_report
            populations = new_populations
            res_populations = res_populations
            
            main_logger.info(f"--- Finished Evolution Iteration {evo_iter+1} ---")
        
        # 保存最终结果
        # 首先按照JSON格式保存，保持原有功能
        final_result = {
            "instance_name": instance["func_name"],
            "best_cost": min(res_populations, key=lambda x: x["cur_cost"])["cur_cost"] if res_populations else float('inf'),
            "best_tour": min(res_populations, key=lambda x: x["cur_cost"])["tour"] if res_populations else [],
            "elite_solutions": [{
                "cost": sol["cur_cost"],
                "tour": sol["tour"]
            } for sol in res_populations[:3]]
        }
        
        # 修改解集保存路径到MoE-main/solution文件夹
        result_dir = os.path.join(os.path.dirname(current_dir), "solution")
        os.makedirs(result_dir, exist_ok=True)
        result_path = os.path.join(result_dir, f"{instance['func_name']}_solution.json")
        
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(final_result, f, ensure_ascii=False, indent=2, cls=utils.NumpyEncoder)
        
        solution_dir = os.path.join(os.path.dirname(current_dir), "solution")
        os.makedirs(solution_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        solution_path = os.path.join(solution_dir, f"{instance['func_name']}_{timestamp}.solution")
        
        with open(solution_path, 'w', encoding='utf-8') as f:
            # 对精英解按成本排序
            sorted_solutions = sorted(res_populations, key=lambda x: x["cur_cost"])
            
            if sorted_solutions:
                # 获取最优成本
                best_cost = int(round(sorted_solutions[0]['cur_cost']))
                
                # 写入每个解
                for sol in sorted_solutions:
                    # 构建行：成本 + 空格 + 节点序列
                    # 确保成本值为整数
                    cost = int(sol["cur_cost"]) if isinstance(sol["cur_cost"], (int, float)) else int(round(sol["cur_cost"]))
                    
                    # 只保存成本最优的路径
                    if cost > best_cost:
                        break  # 因为已排序，后续成本都会更大
                    
                    # 确保路径以0开始
                    tour = sol["tour"]
                    # if tour[0] != 0:
                    #     tour = [0] + [node for node in tour if node != 0]
                    
                    # 使用gls_evol_enhanced中的normalize_path函数，确保它返回有效的路径
                    normalized_tour = gls_evol_enhanced.normalize_path(tour)
                    if normalized_tour is None:
                        # 如果normalize_path返回None，则使用原始tour
                        normalized_tour = tour
                        
                    # 转换为字符串
                    tour_str = ' '.join(str(node) for node in normalized_tour)
                    line = f"{cost} {tour_str}\n"
                    f.write(line)
        
        main_logger.info(f"最终结果已保存到: {result_path}")
        main_logger.info(f"实例 {instance['func_name']} 处理完成")
        
        # 移除实例专用的日志处理器
        root_logger.removeHandler(file_handler)
        file_handler.close()


if __name__ == "__main__":
    main()