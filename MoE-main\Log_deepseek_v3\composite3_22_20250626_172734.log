2025-06-26 17:27:34,440 - __main__ - INFO - composite3_22 开始进化第 1 代
2025-06-26 17:27:34,441 - __main__ - INFO - 开始分析阶段
2025-06-26 17:27:34,441 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:27:34,441 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9700.0, 'max': 41044.0, 'mean': 27987.7, 'std': 12334.317703464589}, 'diversity': 0.8626262626262625, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:27:34,448 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9700.0, 'max': 41044.0, 'mean': 27987.7, 'std': 12334.317703464589}, 'diversity_level': 0.8626262626262625, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[360, 2564], [380, 2529], [340, 2529], [360, 2604], [2427, 580], [2455, 552], [2399, 552], [2399, 608], [2455, 608], [2491, 3776], [2491, 3817], [2530, 3789], [2515, 3743], [2467, 3743], [2452, 3789], [1620, 2430], [1660, 2430], [1640, 2465], [1600, 2465], [1580, 2430], [1600, 2395], [1640, 2395]], 'distance_matrix': array([[   0.,   40.,   40.,   40., 2865., 2905., 2865., 2826., 2866.,
        2452., 2472., 2492., 2456., 2414., 2424., 1267., 1307., 1284.,
        1244., 1227., 1251., 1291.],
       [  40.,    0.,   40.,   78., 2826., 2866., 2826., 2787., 2828.,
        2452., 2473., 2492., 2456., 2414., 2425., 1244., 1284., 1262.,
        1222., 1204., 1227., 1267.],
       [  40.,   40.,    0.,   78., 2856., 2895., 2854., 2816., 2857.,
        2486., 2507., 2527., 2491., 2449., 2459., 1284., 1324., 1302.,
        1262., 1244., 1267., 1307.],
       [  40.,   78.,   78.,    0., 2893., 2933., 2893., 2853., 2894.,
        2432., 2452., 2472., 2437., 2395., 2404., 1272., 1312., 1288.,
        1248., 1232., 1257., 1297.],
       [2865., 2826., 2856., 2893.,    0.,   40.,   40.,   40.,   40.,
        3197., 3238., 3211., 3164., 3163., 3209., 2018., 2003., 2043.,
        2058., 2035., 1995., 1978.],
       [2905., 2866., 2895., 2933.,   40.,    0.,   56.,   79.,   56.,
        3224., 3265., 3238., 3192., 3191., 3237., 2055., 2039., 2079.,
        2095., 2072., 2032., 2015.],
       [2865., 2826., 2854., 2893.,   40.,   56.,    0.,   56.,   79.,
        3225., 3266., 3240., 3193., 3192., 3237., 2033., 2018., 2058.,
        2073., 2049., 2009., 1993.],
       [2826., 2787., 2816., 2853.,   40.,   79.,   56.,    0.,   56.,
        3169., 3210., 3184., 3137., 3136., 3181., 1982., 1966., 2006.,
        2022., 1998., 1957., 1942.],
       [2866., 2828., 2857., 2894.,   40.,   56.,   79.,   56.,    0.,
        3168., 3209., 3182., 3136., 3135., 3181., 2004., 1988., 2028.,
        2044., 2021., 1981., 1964.],
       [2452., 2452., 2486., 2432., 3197., 3224., 3225., 3169., 3168.,
           0.,   41.,   41.,   41.,   41.,   41., 1603., 1582., 1563.,
        1585., 1625., 1643., 1622.],
       [2472., 2473., 2507., 2452., 3238., 3265., 3266., 3210., 3209.,
          41.,    0.,   48.,   78.,   78.,   48., 1638., 1617., 1598.,
        1619., 1659., 1678., 1657.],
       [2492., 2492., 2527., 2472., 3211., 3238., 3240., 3184., 3182.,
          41.,   48.,    0.,   48.,   78.,   78., 1636., 1614., 1595.,
        1618., 1658., 1676., 1654.],
       [2456., 2456., 2491., 2437., 3164., 3192., 3193., 3137., 3136.,
          41.,   78.,   48.,    0.,   48.,   78., 1589., 1567., 1549.,
        1572., 1612., 1629., 1607.],
       [2414., 2414., 2449., 2395., 3163., 3191., 3192., 3136., 3135.,
          41.,   78.,   78.,   48.,    0.,   48., 1562., 1541., 1522.,
        1544., 1585., 1603., 1581.],
       [2424., 2425., 2459., 2404., 3209., 3237., 3237., 3181., 3181.,
          41.,   48.,   78.,   78.,   48.,    0., 1593., 1573., 1553.,
        1574., 1615., 1634., 1613.],
       [1267., 1244., 1284., 1272., 2018., 2055., 2033., 1982., 2004.,
        1603., 1638., 1636., 1589., 1562., 1593.,    0.,   40.,   40.,
          40.,   40.,   40.,   40.],
       [1307., 1284., 1324., 1312., 2003., 2039., 2018., 1966., 1988.,
        1582., 1617., 1614., 1567., 1541., 1573.,   40.,    0.,   40.,
          69.,   80.,   69.,   40.],
       [1284., 1262., 1302., 1288., 2043., 2079., 2058., 2006., 2028.,
        1563., 1598., 1595., 1549., 1522., 1553.,   40.,   40.,    0.,
          40.,   69.,   81.,   70.],
       [1244., 1222., 1262., 1248., 2058., 2095., 2073., 2022., 2044.,
        1585., 1619., 1618., 1572., 1544., 1574.,   40.,   69.,   40.,
           0.,   40.,   70.,   81.],
       [1227., 1204., 1244., 1232., 2035., 2072., 2049., 1998., 2021.,
        1625., 1659., 1658., 1612., 1585., 1615.,   40.,   80.,   69.,
          40.,    0.,   40.,   69.],
       [1251., 1227., 1267., 1257., 1995., 2032., 2009., 1957., 1981.,
        1643., 1678., 1676., 1629., 1603., 1634.,   40.,   69.,   81.,
          70.,   40.,    0.,   40.],
       [1291., 1267., 1307., 1297., 1978., 2015., 1993., 1942., 1964.,
        1622., 1657., 1654., 1607., 1581., 1613.,   40.,   40.,   70.,
          81.,   69.,   40.,    0.]])}
2025-06-26 17:27:34,457 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:27:34,457 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:27:34,457 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:27:34,459 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:27:34,460 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (4, 7), 'frequency': 0.5, 'avg_cost': 40.0}], 'common_subpaths': [{'subpath': (2, 3, 13), 'frequency': 0.3}, {'subpath': (13, 9, 10), 'frequency': 0.3}, {'subpath': (9, 10, 11), 'frequency': 0.3}, {'subpath': (10, 11, 12), 'frequency': 0.3}, {'subpath': (11, 12, 14), 'frequency': 0.3}, {'subpath': (12, 14, 7), 'frequency': 0.3}, {'subpath': (14, 7, 4), 'frequency': 0.3}, {'subpath': (7, 4, 5), 'frequency': 0.3}, {'subpath': (4, 5, 6), 'frequency': 0.3}, {'subpath': (5, 6, 8), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(20, 21)', 'frequency': 0.4}, {'edge': '(0, 1)', 'frequency': 0.4}, {'edge': '(2, 3)', 'frequency': 0.4}, {'edge': '(3, 13)', 'frequency': 0.5}, {'edge': '(9, 10)', 'frequency': 0.4}, {'edge': '(11, 12)', 'frequency': 0.4}, {'edge': '(12, 14)', 'frequency': 0.4}, {'edge': '(4, 7)', 'frequency': 0.5}, {'edge': '(6, 8)', 'frequency': 0.4}, {'edge': '(18, 19)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(15, 16)', 'frequency': 0.3}, {'edge': '(16, 17)', 'frequency': 0.3}, {'edge': '(19, 20)', 'frequency': 0.3}, {'edge': '(1, 21)', 'frequency': 0.3}, {'edge': '(0, 2)', 'frequency': 0.3}, {'edge': '(9, 13)', 'frequency': 0.3}, {'edge': '(10, 11)', 'frequency': 0.3}, {'edge': '(7, 14)', 'frequency': 0.3}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(5, 6)', 'frequency': 0.3}, {'edge': '(15, 17)', 'frequency': 0.2}, {'edge': '(17, 18)', 'frequency': 0.3}, {'edge': '(3, 19)', 'frequency': 0.2}, {'edge': '(13, 21)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(8, 21)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(4, 12)', 'frequency': 0.3}, {'edge': '(12, 15)', 'frequency': 0.2}, {'edge': '(15, 20)', 'frequency': 0.2}, {'edge': '(3, 5)', 'frequency': 0.3}, {'edge': '(1, 17)', 'frequency': 0.2}, {'edge': '(2, 16)', 'frequency': 0.3}, {'edge': '(14, 18)', 'frequency': 0.2}, {'edge': '(7, 15)', 'frequency': 0.2}, {'edge': '(11, 18)', 'frequency': 0.2}, {'edge': '(11, 20)', 'frequency': 0.2}, {'edge': '(0, 3)', 'frequency': 0.2}, {'edge': '(0, 9)', 'frequency': 0.2}, {'edge': '(8, 15)', 'frequency': 0.2}, {'edge': '(13, 14)', 'frequency': 0.2}, {'edge': '(6, 12)', 'frequency': 0.2}, {'edge': '(10, 16)', 'frequency': 0.2}, {'edge': '(0, 5)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [13, 3, 5, 11], 'cost': 8566.0, 'size': 4}, {'region': [14, 0, 10, 2], 'cost': 7403.0, 'size': 4}, {'region': [8, 11, 6], 'cost': 6422.0, 'size': 3}, {'region': [12, 4, 10], 'cost': 6402.0, 'size': 3}, {'region': [14, 4, 12], 'cost': 6373.0, 'size': 3}]}
2025-06-26 17:27:34,460 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:27:34,460 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:27:34,461 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:27:34,461 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 17:27:34,461 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 17:27:34,461 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:27:34,461 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:27:34,463 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:27:34,463 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9700.0, mean 27987.7, max 41044.0, std 12334.317703464589
- diversity: 0.8626262626262625
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [10385.0, 10399.0, 9940.0] (Δ 229.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (4, 7), 'frequency': 0.5, 'avg_cost': 40.0}]
- common_subpaths_sample: [{'subpath': (2, 3, 13), 'frequency': 0.3}, {'subpath': (13, 9, 10), 'frequency': 0.3}, {'subpath': (9, 10, 11), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [13, 3, 5, 11], 'cost': 8566.0, 'size': 4}, {'region': [14, 0, 10, 2], 'cost': 7403.0, 'size': 4}, {'region': [8, 11, 6], 'cost': 6422.0, 'size': 3}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [340.0, 552.0, 2530.0, 3817.0]
- centroid/spread: [1811.864, 2399.682] | rms 1371.908
- density_grid(3x3): [[0, 0, 5], [4, 7, 0], [0, 0, 6]]
- edge_len_stats(min,q1,med,q3,max): [40.0, 1244.0, 1643.0, 2472.0, 3266.0]
- long_edge_ratio: 0.247
- direction_hist(8 sectors): [4, 3, 2, 3, 3, 2, 4, 0]
- nn_median_dist: 40.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:27:34,464 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:28:04,600 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide edge length range [40.0, 3266.0] with Q1-Q3 spread of 1228 units)",
    "modality": "Potentially multimodal (direction histogram shows multiple peaks, long_edge_ratio of 0.247 suggests mixed connectivity)",
    "deceptiveness": "Possible deceptive regions (high-cost difficult regions exist despite some short edges with nn_median_dist=40.0)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (diversity=0.86) but uneven density (density_grid shows sparse NW/SE corners vs concentrated center)",
    "convergence": "Early exploration phase (convergence=0.0, recent best costs still improving by ~229.5/iteration)"
  },
  "difficult_regions": [
    {"region": [13, 3, 5, 11], "reason": "High cost (8566.0) despite small size, overlaps with frequent subpath (2,3,13)"},
    {"region": [8, 11, 6], "reason": "High cost (6422.0) with sparse connections in NW density_grid cell"},
    {"region": [14, 0, 10, 2], "reason": "High cost (7403.0) in low-density SE quadrant"}
  ],
  "opportunity_regions": [
    {"region": [4, 7], "reason": "High-quality edge (freq=0.5, avg_cost=40.0) in dense central grid cell"},
    {"region": [9, 10, 11], "reason": "Frequent subpath (freq=0.3) with medium edge lengths (Q1=1244.0)"}
  ],
  "evolution_phase": "Early exploration (iter 0/5)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Edge recombination focusing on high-quality edges (4,7) and common subpaths",
    "2. Local search intensification in dense central grid cells",
    "3. Adaptive mutation favoring long-edge replacement in difficult regions",
    "4. Diversity preservation for nodes in sparse grid cells (e.g., node 14)"
  ]
}
```
2025-06-26 17:28:04,600 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:28:04,608 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide edge length range [40.0, 3266.0] with Q1-Q3 spread of 1228 units)', 'modality': 'Potentially multimodal (direction histogram shows multiple peaks, long_edge_ratio of 0.247 suggests mixed connectivity)', 'deceptiveness': 'Possible deceptive regions (high-cost difficult regions exist despite some short edges with nn_median_dist=40.0)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity=0.86) but uneven density (density_grid shows sparse NW/SE corners vs concentrated center)', 'convergence': 'Early exploration phase (convergence=0.0, recent best costs still improving by ~229.5/iteration)'}, 'difficult_regions': [{'region': [13, 3, 5, 11], 'reason': 'High cost (8566.0) despite small size, overlaps with frequent subpath (2,3,13)'}, {'region': [8, 11, 6], 'reason': 'High cost (6422.0) with sparse connections in NW density_grid cell'}, {'region': [14, 0, 10, 2], 'reason': 'High cost (7403.0) in low-density SE quadrant'}], 'opportunity_regions': [{'region': [4, 7], 'reason': 'High-quality edge (freq=0.5, avg_cost=40.0) in dense central grid cell'}, {'region': [9, 10, 11], 'reason': 'Frequent subpath (freq=0.3) with medium edge lengths (Q1=1244.0)'}], 'evolution_phase': 'Early exploration (iter 0/5)', 'evolution_direction': ['Operator suggestions:', '1. Edge recombination focusing on high-quality edges (4,7) and common subpaths', '2. Local search intensification in dense central grid cells', '3. Adaptive mutation favoring long-edge replacement in difficult regions', '4. Diversity preservation for nodes in sparse grid cells (e.g., node 14)']}
2025-06-26 17:28:04,608 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:28:04,608 - __main__ - INFO - 分析阶段完成
2025-06-26 17:28:04,609 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide edge length range [40.0, 3266.0] with Q1-Q3 spread of 1228 units)', 'modality': 'Potentially multimodal (direction histogram shows multiple peaks, long_edge_ratio of 0.247 suggests mixed connectivity)', 'deceptiveness': 'Possible deceptive regions (high-cost difficult regions exist despite some short edges with nn_median_dist=40.0)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity=0.86) but uneven density (density_grid shows sparse NW/SE corners vs concentrated center)', 'convergence': 'Early exploration phase (convergence=0.0, recent best costs still improving by ~229.5/iteration)'}, 'difficult_regions': [{'region': [13, 3, 5, 11], 'reason': 'High cost (8566.0) despite small size, overlaps with frequent subpath (2,3,13)'}, {'region': [8, 11, 6], 'reason': 'High cost (6422.0) with sparse connections in NW density_grid cell'}, {'region': [14, 0, 10, 2], 'reason': 'High cost (7403.0) in low-density SE quadrant'}], 'opportunity_regions': [{'region': [4, 7], 'reason': 'High-quality edge (freq=0.5, avg_cost=40.0) in dense central grid cell'}, {'region': [9, 10, 11], 'reason': 'Frequent subpath (freq=0.3) with medium edge lengths (Q1=1244.0)'}], 'evolution_phase': 'Early exploration (iter 0/5)', 'evolution_direction': ['Operator suggestions:', '1. Edge recombination focusing on high-quality edges (4,7) and common subpaths', '2. Local search intensification in dense central grid cells', '3. Adaptive mutation favoring long-edge replacement in difficult regions', '4. Diversity preservation for nodes in sparse grid cells (e.g., node 14)']}
2025-06-26 17:28:04,609 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:28:04,609 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:28:04,609 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 9700.0
  • mean_cost: 27987.7
- Search-space: ruggedness Moderate to high (wide edge length range [40.0, 3266.0] with Q1-Q3 spread of 1228 units), modality Potentially multimodal (direction histogram shows multiple peaks, long_edge_ratio of 0.247 suggests mixed connectivity), deceptiveness Possible deceptive regions (high-cost difficult regions exist despite some short edges with nn_median_dist=40.0)
- Evolution phase: Early exploration (iter 0/5)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [13, 3, 5, {'region': [8, 11, 6...
- Opportunity regions (# 2): {'region': [4, 7], ', {'region': [9, 10, 1
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:28:04,609 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:28:04,609 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:28:15,758 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit opportunity regions."
}
```
2025-06-26 17:28:15,758 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:28:15,758 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:28:15,758 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit opportunity regions."
}
```
2025-06-26 17:28:15,758 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:28:15,758 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:28:15,758 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit opportunity regions."
}
```
2025-06-26 17:28:15,758 - __main__ - INFO - 开始进化阶段
2025-06-26 17:28:15,758 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:28:15,758 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:28:15,758 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9785.0 (rank 20.0%)
- Population diversity: 0.6142857142857143
- Evolution phase: Early exploration (iter 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [4, , {'region': [9, 
- Difficult regions to avoid (sample): {'region': [13,, {'region': [8, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:28:15,758 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:28:15,758 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:28:25,355 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 7, 14, 17, 3, 10, 19, 6, 12, 16, 21, 8, 13],
  "targeted_regions": "4,9, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid dense clusters, balance exploration"
}
```
2025-06-26 17:28:25,363 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:28:25,363 - ExplorationExpert - INFO - 探索路径生成完成，成本: 45390.0, 路径: [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 7, 14, 17, 3, 10, 19, 6, 12, 16, 21, 8, 13]
2025-06-26 17:28:25,364 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 7, 14, 17, 3, 10, 19, 6, 12, 16, 21, 8, 13], 'cur_cost': 45390.0}
2025-06-26 17:28:25,364 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:28:25,364 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:28:25,364 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9700.0 (rank 0.0%)
- Population diversity: 0.6523809523809524
- Evolution phase: Early exploration (iter 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [4, , {'region': [9, 
- Difficult regions to avoid (sample): {'region': [13,, {'region': [8, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:28:25,364 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:28:25,364 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:28:35,049 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 8, 13],
  "targeted_regions": "Focus on regions 4,9, adjacent sparse cells",
  "strategy_comment": "Balance exploration of opportunity regions while avoiding difficult nodes 8,13"
}
```
2025-06-26 17:28:35,049 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:28:35,049 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15933.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 8, 13]
2025-06-26 17:28:35,049 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 8, 13], 'cur_cost': 15933.0}
2025-06-26 17:28:35,049 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:28:35,049 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:28:35,053 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9750.0 (rank 0.0%)
- Population diversity: 0.6619047619047619
- Evolution phase: Early exploration (iter 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [4, , {'region': [9, 
- Difficult regions to avoid (sample): {'region': [13,, {'region': [8, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:28:35,053 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:28:35,053 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:28:45,379 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8],
  "targeted_regions": "Regions 4, 9, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid 8/13, balance diversity"
}
```
2025-06-26 17:28:45,385 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:28:45,385 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16002.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8]
2025-06-26 17:28:45,385 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}
2025-06-26 17:28:45,385 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:28:45,385 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:28:45,385 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 37460.0 (rank 60.0%)
- Population diversity: 0.638095238095238
- Evolution phase: Early exploration (iter 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [4, , {'region': [9, 
- Difficult regions to avoid (sample): {'region': [13,, {'region': [8, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:28:45,385 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:28:45,389 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:28:55,183 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8],
  "targeted_regions": "Regions 4, 9, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid 8/13, explore adjacent sparse cells"
}
```
2025-06-26 17:28:55,189 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:28:55,189 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16002.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8]
2025-06-26 17:28:55,189 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}
2025-06-26 17:28:55,189 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:28:55,189 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:28:55,190 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 35669.0 (rank 50.0%)
- Population diversity: 0.5904761904761905
- Evolution phase: Early exploration (iter 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [4, , {'region': [9, 
- Difficult regions to avoid (sample): {'region': [13,, {'region': [8, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:28:55,190 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:28:55,191 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:29:04,514 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 6, 14, 19, 3, 10, 16, 21, 7, 12, 17, 8, 13],
  "targeted_regions": "Regions 4, 9, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid dense clusters, balance exploration"
}
```
2025-06-26 17:29:04,514 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:29:04,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 45332.0, 路径: [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 6, 14, 19, 3, 10, 16, 21, 7, 12, 17, 8, 13]
2025-06-26 17:29:04,514 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 6, 14, 19, 3, 10, 16, 21, 7, 12, 17, 8, 13], 'cur_cost': 45332.0}
2025-06-26 17:29:04,514 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:29:04,519 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:29:04,519 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 28235.0 (rank 30.0%)
- Population diversity: 0.5619047619047619
- Evolution phase: Early exploration (iter 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [4, , {'region': [9, 
- Difficult regions to avoid (sample): {'region': [13,, {'region': [8, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:29:04,519 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:29:04,519 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:29:14,414 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 9, 2, 5, 7, 11, 15, 18, 20, 19, 17, 16, 14, 12, 10, 6, 3, 1, 8, 13, 21],
  "targeted_regions": "4,9 and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions while avoiding 8,13 for diversity"
}
```
2025-06-26 17:29:14,414 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:29:14,416 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33402.0, 路径: [0, 4, 9, 2, 5, 7, 11, 15, 18, 20, 19, 17, 16, 14, 12, 10, 6, 3, 1, 8, 13, 21]
2025-06-26 17:29:14,416 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 4, 9, 2, 5, 7, 11, 15, 18, 20, 19, 17, 16, 14, 12, 10, 6, 3, 1, 8, 13, 21], 'cur_cost': 33402.0}
2025-06-26 17:29:14,416 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 17:29:14,416 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:29:14,416 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41044.0 (rank 70.0%)
- Population diversity: 0.5476190476190477
- Evolution phase: Early exploration (iter 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [4, , {'region': [9, 
- Difficult regions to avoid (sample): {'region': [13,, {'region': [8, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:29:14,416 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:29:14,416 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:29:24,359 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8],
  "targeted_regions": "Regions 4, 9, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid 8/13, balance exploration"
}
```
2025-06-26 17:29:24,359 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:29:24,366 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16002.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8]
2025-06-26 17:29:24,366 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}
2025-06-26 17:29:24,366 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:29:24,366 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:29:24,366 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:29:24,366 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 42946.0
2025-06-26 17:29:24,869 - ExploitationExpert - INFO - res_population_num: 53
2025-06-26 17:29:24,869 - ExploitationExpert - INFO - res_population_costs: [9470, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455]
2025-06-26 17:29:24,870 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64)]
2025-06-26 17:29:24,881 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:29:24,881 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 7, 14, 17, 3, 10, 19, 6, 12, 16, 21, 8, 13], 'cur_cost': 45390.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 8, 13], 'cur_cost': 15933.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}, {'tour': [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 6, 14, 19, 3, 10, 16, 21, 7, 12, 17, 8, 13], 'cur_cost': 45332.0}, {'tour': [0, 4, 9, 2, 5, 7, 11, 15, 18, 20, 19, 17, 16, 14, 12, 10, 6, 3, 1, 8, 13, 21], 'cur_cost': 33402.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}, {'tour': array([ 9,  0,  6,  1, 13,  7, 14, 15, 21, 10, 16,  3,  5, 18, 19,  8, 17,
        4,  2, 12, 20, 11]), 'cur_cost': 42946.0}, {'tour': [15, 14, 13, 21, 4, 11, 12, 6, 3, 16, 10, 20, 5, 0, 9, 17, 18, 19, 7, 1, 2, 8], 'cur_cost': 37870.0}, {'tour': [18, 14, 21, 1, 16, 10, 13, 3, 19, 0, 5, 9, 15, 2, 8, 6, 12, 17, 11, 20, 7, 4], 'cur_cost': 36307.0}]
2025-06-26 17:29:24,883 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:29:24,883 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 146, 'skip_rate': 0.06164383561643835, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 137, 'cache_hits': 172, 'similarity_calculations': 3014, 'cache_hit_rate': 0.05706702057067021, 'cache_size': 2842}}
2025-06-26 17:29:24,883 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:29:24,883 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:29:24,883 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:29:24,885 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:29:24,885 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 37095.0
2025-06-26 17:29:25,387 - ExploitationExpert - INFO - res_population_num: 65
2025-06-26 17:29:25,387 - ExploitationExpert - INFO - res_population_costs: [9470, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455]
2025-06-26 17:29:25,388 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64)]
2025-06-26 17:29:25,405 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:29:25,405 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 7, 14, 17, 3, 10, 19, 6, 12, 16, 21, 8, 13], 'cur_cost': 45390.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 8, 13], 'cur_cost': 15933.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}, {'tour': [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 6, 14, 19, 3, 10, 16, 21, 7, 12, 17, 8, 13], 'cur_cost': 45332.0}, {'tour': [0, 4, 9, 2, 5, 7, 11, 15, 18, 20, 19, 17, 16, 14, 12, 10, 6, 3, 1, 8, 13, 21], 'cur_cost': 33402.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}, {'tour': array([ 9,  0,  6,  1, 13,  7, 14, 15, 21, 10, 16,  3,  5, 18, 19,  8, 17,
        4,  2, 12, 20, 11]), 'cur_cost': 42946.0}, {'tour': array([ 3,  1,  9,  2, 17, 10, 12,  8, 14, 16,  0,  4,  7, 15, 20, 11, 18,
       19,  6, 21,  5, 13]), 'cur_cost': 37095.0}, {'tour': [18, 14, 21, 1, 16, 10, 13, 3, 19, 0, 5, 9, 15, 2, 8, 6, 12, 17, 11, 20, 7, 4], 'cur_cost': 36307.0}]
2025-06-26 17:29:25,406 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:29:25,406 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 147, 'skip_rate': 0.061224489795918366, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 138, 'cache_hits': 172, 'similarity_calculations': 3015, 'cache_hit_rate': 0.05704809286898839, 'cache_size': 2843}}
2025-06-26 17:29:25,406 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:29:25,407 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:29:25,407 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:29:25,407 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:29:25,408 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 42315.0
2025-06-26 17:29:25,910 - ExploitationExpert - INFO - res_population_num: 68
2025-06-26 17:29:25,910 - ExploitationExpert - INFO - res_population_costs: [9470, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455]
2025-06-26 17:29:25,910 - ExploitationExpert - INFO - res_populations: [array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64)]
2025-06-26 17:29:25,927 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:29:25,928 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 7, 14, 17, 3, 10, 19, 6, 12, 16, 21, 8, 13], 'cur_cost': 45390.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 8, 13], 'cur_cost': 15933.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}, {'tour': [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 6, 14, 19, 3, 10, 16, 21, 7, 12, 17, 8, 13], 'cur_cost': 45332.0}, {'tour': [0, 4, 9, 2, 5, 7, 11, 15, 18, 20, 19, 17, 16, 14, 12, 10, 6, 3, 1, 8, 13, 21], 'cur_cost': 33402.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}, {'tour': array([ 9,  0,  6,  1, 13,  7, 14, 15, 21, 10, 16,  3,  5, 18, 19,  8, 17,
        4,  2, 12, 20, 11]), 'cur_cost': 42946.0}, {'tour': array([ 3,  1,  9,  2, 17, 10, 12,  8, 14, 16,  0,  4,  7, 15, 20, 11, 18,
       19,  6, 21,  5, 13]), 'cur_cost': 37095.0}, {'tour': array([15,  1,  9,  8, 10,  4,  6, 18,  7, 14, 16,  3,  5, 21, 19,  0, 12,
       17, 11,  2, 20, 13]), 'cur_cost': 42315.0}]
2025-06-26 17:29:25,930 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:29:25,930 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 148, 'skip_rate': 0.060810810810810814, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 139, 'cache_hits': 172, 'similarity_calculations': 3017, 'cache_hit_rate': 0.057010275107722906, 'cache_size': 2845}}
2025-06-26 17:29:25,931 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:29:25,931 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 7, 14, 17, 3, 10, 19, 6, 12, 16, 21, 8, 13], 'cur_cost': 45390.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 8, 13], 'cur_cost': 15933.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 9, 2, 15, 18, 5, 11, 20, 1, 6, 14, 19, 3, 10, 16, 21, 7, 12, 17, 8, 13], 'cur_cost': 45332.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 9, 2, 5, 7, 11, 15, 18, 20, 19, 17, 16, 14, 12, 10, 6, 3, 1, 8, 13, 21], 'cur_cost': 33402.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 13, 8], 'cur_cost': 16002.0}}]
2025-06-26 17:29:25,931 - __main__ - INFO - 进化阶段完成
2025-06-26 17:29:25,931 - __main__ - INFO - 开始评估阶段
2025-06-26 17:29:25,931 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:29:25,932 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:29:25,932 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9700.0 → 15933.0 (Δ -6233.0)
- Mean cost: 27987.7 → 31041.9
- Diversity: 0.614 → 0.486 (Δ -0.1286)

#### Elite metrics
- Elite count: 0 → 68
- Best elite cost: 9700.0 → 9455 (Δ 245.0)

#### Strategy performance
- Explore: 7 inds, success 28.6% , avg Δ -2345.71
- Exploit: 3 inds, success 33.3% , avg Δ -4707.33

#### Other indicators
- No-change individuals: 0
- Regression individuals: 7
- Historical trends: Last 3 iterations: costs [10385.0, 10399.0, 9940.0], diversity [0.8686274509803921, 0.9124183006535949, 0.9124183006535946]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:29:25,933 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:29:38,583 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "avg_improvement": -2345.71,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 33.3,
      "avg_improvement": -4707.33,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing mutation rate slightly to boost diversity"
  }
}
```
2025-06-26 17:29:38,583 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:29:38,583 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "avg_improvement": -2345.71,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 33.3,
      "avg_improvement": -4707.33,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing mutation rate slightly to boost diversity"
  }
}
```
2025-06-26 17:29:38,589 - __main__ - INFO - 评估阶段完成
2025-06-26 17:29:38,589 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "avg_improvement": -2345.71,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 33.3,
      "avg_improvement": -4707.33,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing mutation rate slightly to boost diversity"
  }
}
```
2025-06-26 17:29:38,589 - __main__ - INFO - 当前最佳适应度: 15933.0
2025-06-26 17:29:38,592 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite3_22_route_0.pkl
2025-06-26 17:29:38,592 - __main__ - INFO - composite3_22 开始进化第 2 代
2025-06-26 17:29:38,593 - __main__ - INFO - 开始分析阶段
2025-06-26 17:29:38,593 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:29:38,596 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 15933.0, 'max': 45390.0, 'mean': 31041.9, 'std': 12759.114541769739}, 'diversity': 0.7757575757575756, 'clusters': {'clusters': 7, 'cluster_sizes': [1, 4, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:29:38,599 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 15933.0, 'max': 45390.0, 'mean': 31041.9, 'std': 12759.114541769739}, 'diversity_level': 0.7757575757575756, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [1, 4, 1, 1, 1, 1, 1]}, 'coordinates': [[360, 2564], [380, 2529], [340, 2529], [360, 2604], [2427, 580], [2455, 552], [2399, 552], [2399, 608], [2455, 608], [2491, 3776], [2491, 3817], [2530, 3789], [2515, 3743], [2467, 3743], [2452, 3789], [1620, 2430], [1660, 2430], [1640, 2465], [1600, 2465], [1580, 2430], [1600, 2395], [1640, 2395]], 'distance_matrix': array([[   0.,   40.,   40.,   40., 2865., 2905., 2865., 2826., 2866.,
        2452., 2472., 2492., 2456., 2414., 2424., 1267., 1307., 1284.,
        1244., 1227., 1251., 1291.],
       [  40.,    0.,   40.,   78., 2826., 2866., 2826., 2787., 2828.,
        2452., 2473., 2492., 2456., 2414., 2425., 1244., 1284., 1262.,
        1222., 1204., 1227., 1267.],
       [  40.,   40.,    0.,   78., 2856., 2895., 2854., 2816., 2857.,
        2486., 2507., 2527., 2491., 2449., 2459., 1284., 1324., 1302.,
        1262., 1244., 1267., 1307.],
       [  40.,   78.,   78.,    0., 2893., 2933., 2893., 2853., 2894.,
        2432., 2452., 2472., 2437., 2395., 2404., 1272., 1312., 1288.,
        1248., 1232., 1257., 1297.],
       [2865., 2826., 2856., 2893.,    0.,   40.,   40.,   40.,   40.,
        3197., 3238., 3211., 3164., 3163., 3209., 2018., 2003., 2043.,
        2058., 2035., 1995., 1978.],
       [2905., 2866., 2895., 2933.,   40.,    0.,   56.,   79.,   56.,
        3224., 3265., 3238., 3192., 3191., 3237., 2055., 2039., 2079.,
        2095., 2072., 2032., 2015.],
       [2865., 2826., 2854., 2893.,   40.,   56.,    0.,   56.,   79.,
        3225., 3266., 3240., 3193., 3192., 3237., 2033., 2018., 2058.,
        2073., 2049., 2009., 1993.],
       [2826., 2787., 2816., 2853.,   40.,   79.,   56.,    0.,   56.,
        3169., 3210., 3184., 3137., 3136., 3181., 1982., 1966., 2006.,
        2022., 1998., 1957., 1942.],
       [2866., 2828., 2857., 2894.,   40.,   56.,   79.,   56.,    0.,
        3168., 3209., 3182., 3136., 3135., 3181., 2004., 1988., 2028.,
        2044., 2021., 1981., 1964.],
       [2452., 2452., 2486., 2432., 3197., 3224., 3225., 3169., 3168.,
           0.,   41.,   41.,   41.,   41.,   41., 1603., 1582., 1563.,
        1585., 1625., 1643., 1622.],
       [2472., 2473., 2507., 2452., 3238., 3265., 3266., 3210., 3209.,
          41.,    0.,   48.,   78.,   78.,   48., 1638., 1617., 1598.,
        1619., 1659., 1678., 1657.],
       [2492., 2492., 2527., 2472., 3211., 3238., 3240., 3184., 3182.,
          41.,   48.,    0.,   48.,   78.,   78., 1636., 1614., 1595.,
        1618., 1658., 1676., 1654.],
       [2456., 2456., 2491., 2437., 3164., 3192., 3193., 3137., 3136.,
          41.,   78.,   48.,    0.,   48.,   78., 1589., 1567., 1549.,
        1572., 1612., 1629., 1607.],
       [2414., 2414., 2449., 2395., 3163., 3191., 3192., 3136., 3135.,
          41.,   78.,   78.,   48.,    0.,   48., 1562., 1541., 1522.,
        1544., 1585., 1603., 1581.],
       [2424., 2425., 2459., 2404., 3209., 3237., 3237., 3181., 3181.,
          41.,   48.,   78.,   78.,   48.,    0., 1593., 1573., 1553.,
        1574., 1615., 1634., 1613.],
       [1267., 1244., 1284., 1272., 2018., 2055., 2033., 1982., 2004.,
        1603., 1638., 1636., 1589., 1562., 1593.,    0.,   40.,   40.,
          40.,   40.,   40.,   40.],
       [1307., 1284., 1324., 1312., 2003., 2039., 2018., 1966., 1988.,
        1582., 1617., 1614., 1567., 1541., 1573.,   40.,    0.,   40.,
          69.,   80.,   69.,   40.],
       [1284., 1262., 1302., 1288., 2043., 2079., 2058., 2006., 2028.,
        1563., 1598., 1595., 1549., 1522., 1553.,   40.,   40.,    0.,
          40.,   69.,   81.,   70.],
       [1244., 1222., 1262., 1248., 2058., 2095., 2073., 2022., 2044.,
        1585., 1619., 1618., 1572., 1544., 1574.,   40.,   69.,   40.,
           0.,   40.,   70.,   81.],
       [1227., 1204., 1244., 1232., 2035., 2072., 2049., 1998., 2021.,
        1625., 1659., 1658., 1612., 1585., 1615.,   40.,   80.,   69.,
          40.,    0.,   40.,   69.],
       [1251., 1227., 1267., 1257., 1995., 2032., 2009., 1957., 1981.,
        1643., 1678., 1676., 1629., 1603., 1634.,   40.,   69.,   81.,
          70.,   40.,    0.,   40.],
       [1291., 1267., 1307., 1297., 1978., 2015., 1993., 1942., 1964.,
        1622., 1657., 1654., 1607., 1581., 1613.,   40.,   40.,   70.,
          81.,   69.,   40.,    0.]])}
2025-06-26 17:29:38,600 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:29:38,600 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:29:38,600 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:29:38,600 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:29:38,600 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:29:38,600 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:29:38,600 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:29:38,600 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:29:38,600 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:29:38,624 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:29:38,624 - __main__ - INFO - 精英专家分析报告: {'elite_count': 68, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 6478.0, 'avg_gap': 21586.679411764708}, 'structure_gap': {'unique_elite_edges': 50, 'unique_pop_edges': 92, 'common_edges': 28}}, 'elite_diversity': {'diversity_score': 0.3562335381913959}}
2025-06-26 17:29:38,624 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:29:38,624 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:29:38,624 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:29:38,624 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 15933.0, mean 31041.9, max 45390.0, std 12759.114541769739
- diversity: 0.7757575757575756
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [10399.0, 9940.0, 15933.0] (Δ 229.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [340.0, 552.0, 2530.0, 3817.0]
- centroid/spread: [1811.864, 2399.682] | rms 1371.908
- density_grid(3x3): [[0, 0, 5], [4, 7, 0], [0, 0, 6]]
- edge_len_stats(min,q1,med,q3,max): [40.0, 1244.0, 1643.0, 2472.0, 3266.0]
- long_edge_ratio: 0.247
- direction_hist(8 sectors): [4, 3, 2, 3, 3, 2, 4, 0]
- nn_median_dist: 40.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:29:38,624 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:30:01,317 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 40 to 3266, with significant standard deviation in costs)",
    "modality": "Potentially multimodal (varied direction histogram and edge length distribution)",
    "deceptiveness": "Possible mild deceptiveness (long_edge_ratio of 0.247 suggests some misleading long edges)"
  },
  "population_state": {
    "coverage": "Moderate (diversity 0.776, but density_grid shows uneven distribution with some empty cells)",
    "convergence": "Early stage (convergence 0.0, recent best costs still improving)",
    "exploration_status": "Exploring but with clustering in certain regions (density_grid shows concentrations in 3 cells)"
  },
  "difficult_regions": [
    "Low-density cells (top-left and bottom-middle in density_grid)",
    "Long-edge corridors (edges in q3-max range: 2472-3266)"
  ],
  "opportunity_regions": [
    "High-density cells (center-right in density_grid with 5-7 nodes)",
    "Short-edge clusters (edges in min-q1 range: 40-1244)"
  ],
  "evolution_phase": "Early exploration phase",
  "evolution_direction": [
    "Prioritize exploitation in high-density cells with local search operators",
    "Targeted exploration in low-density regions with mutation operators that encourage spreading",
    "Edge recombination operators to capitalize on emerging short-edge patterns",
    "Consider adaptive operators that balance exploration/exploitation based on density_grid feedback"
  ]
}
```
2025-06-26 17:30:01,317 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:30:01,317 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 40 to 3266, with significant standard deviation in costs)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio of 0.247 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Moderate (diversity 0.776, but density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs still improving)', 'exploration_status': 'Exploring but with clustering in certain regions (density_grid shows concentrations in 3 cells)'}, 'difficult_regions': ['Low-density cells (top-left and bottom-middle in density_grid)', 'Long-edge corridors (edges in q3-max range: 2472-3266)'], 'opportunity_regions': ['High-density cells (center-right in density_grid with 5-7 nodes)', 'Short-edge clusters (edges in min-q1 range: 40-1244)'], 'evolution_phase': 'Early exploration phase', 'evolution_direction': ['Prioritize exploitation in high-density cells with local search operators', 'Targeted exploration in low-density regions with mutation operators that encourage spreading', 'Edge recombination operators to capitalize on emerging short-edge patterns', 'Consider adaptive operators that balance exploration/exploitation based on density_grid feedback']}
2025-06-26 17:30:01,317 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:30:01,317 - __main__ - INFO - 分析阶段完成
2025-06-26 17:30:01,317 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 40 to 3266, with significant standard deviation in costs)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio of 0.247 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Moderate (diversity 0.776, but density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs still improving)', 'exploration_status': 'Exploring but with clustering in certain regions (density_grid shows concentrations in 3 cells)'}, 'difficult_regions': ['Low-density cells (top-left and bottom-middle in density_grid)', 'Long-edge corridors (edges in q3-max range: 2472-3266)'], 'opportunity_regions': ['High-density cells (center-right in density_grid with 5-7 nodes)', 'Short-edge clusters (edges in min-q1 range: 40-1244)'], 'evolution_phase': 'Early exploration phase', 'evolution_direction': ['Prioritize exploitation in high-density cells with local search operators', 'Targeted exploration in low-density regions with mutation operators that encourage spreading', 'Edge recombination operators to capitalize on emerging short-edge patterns', 'Consider adaptive operators that balance exploration/exploitation based on density_grid feedback']}
2025-06-26 17:30:01,317 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:30:01,317 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:30:01,323 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 15933.0
  • mean_cost: 31041.9
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 40 to 3266, with significant standard deviation in costs), modality Potentially multimodal (varied direction histogram and edge length distribution), deceptiveness Possible mild deceptiveness (long_edge_ratio of 0.247 suggests some misleading long edges)
- Evolution phase: Early exploration phase
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Low-density cells (t, Long-edge corridors 
- Opportunity regions (# 2): High-density cells (, Short-edge clusters 
- Last-iteration feedback: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:30:01,323 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:30:01,323 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:30:11,688 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 30% exploit to refine promising solutions."
}
```
2025-06-26 17:30:11,688 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:30:11,688 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:30:11,688 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 30% exploit to refine promising solutions."
}
```
2025-06-26 17:30:11,688 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:30:11,688 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:30:11,688 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 30% exploit to refine promising solutions."
}
```
2025-06-26 17:30:11,688 - __main__ - INFO - 开始进化阶段
2025-06-26 17:30:11,693 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:30:11,693 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:30:11,693 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 45390.0 (rank 90.0%)
- Population diversity: 0.4857142857142857
- Evolution phase: Early exploration phase
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:30:11,693 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:30:11,693 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:30:21,302 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 3, 15, 19, 7, 14, 2, 10, 18, 1, 9, 13, 6, 16, 4, 20, 11, 17, 21],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Prioritize opportunity regions while exploring adjacent sparse cells"
}
```
2025-06-26 17:30:21,302 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:30:21,308 - ExplorationExpert - INFO - 探索路径生成完成，成本: 42758.0, 路径: [0, 5, 12, 8, 3, 15, 19, 7, 14, 2, 10, 18, 1, 9, 13, 6, 16, 4, 20, 11, 17, 21]
2025-06-26 17:30:21,308 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 3, 15, 19, 7, 14, 2, 10, 18, 1, 9, 13, 6, 16, 4, 20, 11, 17, 21], 'cur_cost': 42758.0}
2025-06-26 17:30:21,308 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:30:21,308 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:30:21,308 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15933.0 (rank 0.0%)
- Population diversity: 0.5333333333333333
- Evolution phase: Early exploration phase
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:30:21,310 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:30:21,310 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:30:31,050 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 10, 18, 6, 13, 1, 9, 16, 4, 20, 11, 17, 21],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Prioritize opportunity regions while exploring nearby sparse cells"
}
```
2025-06-26 17:30:31,050 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:30:31,058 - ExplorationExpert - INFO - 探索路径生成完成，成本: 45848.0, 路径: [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 10, 18, 6, 13, 1, 9, 16, 4, 20, 11, 17, 21]
2025-06-26 17:30:31,058 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 10, 18, 6, 13, 1, 9, 16, 4, 20, 11, 17, 21], 'cur_cost': 45848.0}
2025-06-26 17:30:31,059 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:30:31,059 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:30:31,059 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 16002.0 (rank 0.0%)
- Population diversity: 0.5380952380952381
- Evolution phase: Early exploration phase
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:30:31,059 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:30:31,059 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:30:41,182 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 3, 15, 19, 7, 14, 10, 2, 17, 9, 1, 13, 18, 6, 20, 4, 11, 16, 21],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Prioritize opportunity regions while exploring adjacent sparse cells"
}
```
2025-06-26 17:30:41,182 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:30:41,182 - ExplorationExpert - INFO - 探索路径生成完成，成本: 42681.0, 路径: [0, 5, 12, 8, 3, 15, 19, 7, 14, 10, 2, 17, 9, 1, 13, 18, 6, 20, 4, 11, 16, 21]
2025-06-26 17:30:41,182 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 3, 15, 19, 7, 14, 10, 2, 17, 9, 1, 13, 18, 6, 20, 4, 11, 16, 21], 'cur_cost': 42681.0}
2025-06-26 17:30:41,182 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:30:41,182 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:30:41,182 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 16002.0 (rank 0.0%)
- Population diversity: 0.5666666666666667
- Evolution phase: Early exploration phase
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:30:41,182 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:30:41,182 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:30:50,980 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 3, 19, 15, 11, 7, 2, 18, 14, 10, 6, 1, 17, 13, 9, 4, 20, 16, 21],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Cluster hops in dense regions, sparse transitions, avoid long edges"
}
```
2025-06-26 17:30:50,983 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:30:50,983 - ExplorationExpert - INFO - 探索路径生成完成，成本: 39428.0, 路径: [0, 5, 12, 8, 3, 19, 15, 11, 7, 2, 18, 14, 10, 6, 1, 17, 13, 9, 4, 20, 16, 21]
2025-06-26 17:30:50,983 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 3, 19, 15, 11, 7, 2, 18, 14, 10, 6, 1, 17, 13, 9, 4, 20, 16, 21], 'cur_cost': 39428.0}
2025-06-26 17:30:50,985 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:30:50,985 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:30:50,985 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 45332.0 (rank 80.0%)
- Population diversity: 0.5952380952380952
- Evolution phase: Early exploration phase
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:30:50,985 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:30:50,986 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:31:01,487 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Alternating dense/sparse regions with short edges"
}
```
2025-06-26 17:31:01,487 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:31:01,487 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16252.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 17:31:01,487 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}
2025-06-26 17:31:01,487 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:31:01,487 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:31:01,491 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 33402.0 (rank 20.0%)
- Population diversity: 0.5904761904761905
- Evolution phase: Early exploration phase
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:31:01,491 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:31:01,491 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:31:11,736 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 16, 8, 19, 4, 11, 17, 6, 20, 10, 13, 1],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Prioritize dense clusters with short edges, explore nearby sparse cells"
}
```
2025-06-26 17:31:11,739 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:31:11,739 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41521.0, 路径: [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 16, 8, 19, 4, 11, 17, 6, 20, 10, 13, 1]
2025-06-26 17:31:11,739 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 16, 8, 19, 4, 11, 17, 6, 20, 10, 13, 1], 'cur_cost': 41521.0}
2025-06-26 17:31:11,739 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 17:31:11,740 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:31:11,740 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 16002.0 (rank 0.0%)
- Population diversity: 0.6190476190476191
- Evolution phase: Early exploration phase
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Low-density cel, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:31:11,740 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:31:11,740 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:31:21,400 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 3, 19, 15, 10, 7, 14, 18, 2, 9, 13, 17, 1, 6, 16, 4, 11, 20, 21],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Prioritize dense clusters with short edges, explore nearby sparse cells"
}
```
2025-06-26 17:31:21,400 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:31:21,400 - ExplorationExpert - INFO - 探索路径生成完成，成本: 42640.0, 路径: [0, 5, 12, 8, 3, 19, 15, 10, 7, 14, 18, 2, 9, 13, 17, 1, 6, 16, 4, 11, 20, 21]
2025-06-26 17:31:21,402 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 3, 19, 15, 10, 7, 14, 18, 2, 9, 13, 17, 1, 6, 16, 4, 11, 20, 21], 'cur_cost': 42640.0}
2025-06-26 17:31:21,402 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:31:21,402 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:31:21,402 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:31:21,402 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 37268.0
2025-06-26 17:31:21,903 - ExploitationExpert - INFO - res_population_num: 72
2025-06-26 17:31:21,903 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470, 9455, 9455, 9455, 9455]
2025-06-26 17:31:21,904 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64)]
2025-06-26 17:31:21,921 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:31:21,921 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 8, 3, 15, 19, 7, 14, 2, 10, 18, 1, 9, 13, 6, 16, 4, 20, 11, 17, 21], 'cur_cost': 42758.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 10, 18, 6, 13, 1, 9, 16, 4, 20, 11, 17, 21], 'cur_cost': 45848.0}, {'tour': [0, 5, 12, 8, 3, 15, 19, 7, 14, 10, 2, 17, 9, 1, 13, 18, 6, 20, 4, 11, 16, 21], 'cur_cost': 42681.0}, {'tour': [0, 5, 12, 8, 3, 19, 15, 11, 7, 2, 18, 14, 10, 6, 1, 17, 13, 9, 4, 20, 16, 21], 'cur_cost': 39428.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 16, 8, 19, 4, 11, 17, 6, 20, 10, 13, 1], 'cur_cost': 41521.0}, {'tour': [0, 5, 12, 8, 3, 19, 15, 10, 7, 14, 18, 2, 9, 13, 17, 1, 6, 16, 4, 11, 20, 21], 'cur_cost': 42640.0}, {'tour': array([17,  5, 21, 20,  4, 15,  7,  1, 19,  6,  0, 12,  2, 10, 13, 16, 18,
        8,  3, 11, 14,  9]), 'cur_cost': 37268.0}, {'tour': array([ 3,  1,  9,  2, 17, 10, 12,  8, 14, 16,  0,  4,  7, 15, 20, 11, 18,
       19,  6, 21,  5, 13]), 'cur_cost': 37095.0}, {'tour': array([15,  1,  9,  8, 10,  4,  6, 18,  7, 14, 16,  3,  5, 21, 19,  0, 12,
       17, 11,  2, 20, 13]), 'cur_cost': 42315.0}]
2025-06-26 17:31:21,922 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:31:21,922 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 149, 'skip_rate': 0.06040268456375839, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 140, 'cache_hits': 172, 'similarity_calculations': 3020, 'cache_hit_rate': 0.05695364238410596, 'cache_size': 2848}}
2025-06-26 17:31:21,922 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:31:21,922 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:31:21,923 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:31:21,923 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:31:21,923 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 43163.0
2025-06-26 17:31:22,426 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:31:22,426 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470, 9455, 9455, 9455, 9455, 9455]
2025-06-26 17:31:22,427 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64)]
2025-06-26 17:31:22,443 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:31:22,443 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 8, 3, 15, 19, 7, 14, 2, 10, 18, 1, 9, 13, 6, 16, 4, 20, 11, 17, 21], 'cur_cost': 42758.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 10, 18, 6, 13, 1, 9, 16, 4, 20, 11, 17, 21], 'cur_cost': 45848.0}, {'tour': [0, 5, 12, 8, 3, 15, 19, 7, 14, 10, 2, 17, 9, 1, 13, 18, 6, 20, 4, 11, 16, 21], 'cur_cost': 42681.0}, {'tour': [0, 5, 12, 8, 3, 19, 15, 11, 7, 2, 18, 14, 10, 6, 1, 17, 13, 9, 4, 20, 16, 21], 'cur_cost': 39428.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 16, 8, 19, 4, 11, 17, 6, 20, 10, 13, 1], 'cur_cost': 41521.0}, {'tour': [0, 5, 12, 8, 3, 19, 15, 10, 7, 14, 18, 2, 9, 13, 17, 1, 6, 16, 4, 11, 20, 21], 'cur_cost': 42640.0}, {'tour': array([17,  5, 21, 20,  4, 15,  7,  1, 19,  6,  0, 12,  2, 10, 13, 16, 18,
        8,  3, 11, 14,  9]), 'cur_cost': 37268.0}, {'tour': array([ 8,  2, 11, 17,  0, 13, 20,  6, 14, 18, 21,  9,  3, 15,  7, 19,  5,
        1, 16,  4, 12, 10]), 'cur_cost': 43163.0}, {'tour': array([15,  1,  9,  8, 10,  4,  6, 18,  7, 14, 16,  3,  5, 21, 19,  0, 12,
       17, 11,  2, 20, 13]), 'cur_cost': 42315.0}]
2025-06-26 17:31:22,445 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:31:22,445 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 150, 'skip_rate': 0.06, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 141, 'cache_hits': 172, 'similarity_calculations': 3024, 'cache_hit_rate': 0.056878306878306875, 'cache_size': 2852}}
2025-06-26 17:31:22,445 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:31:22,445 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:31:22,445 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:31:22,446 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:31:22,446 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 36283.0
2025-06-26 17:31:22,949 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:31:22,949 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470, 9455, 9455, 9455, 9455, 9455]
2025-06-26 17:31:22,949 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64)]
2025-06-26 17:31:22,966 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:31:22,966 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 8, 3, 15, 19, 7, 14, 2, 10, 18, 1, 9, 13, 6, 16, 4, 20, 11, 17, 21], 'cur_cost': 42758.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 10, 18, 6, 13, 1, 9, 16, 4, 20, 11, 17, 21], 'cur_cost': 45848.0}, {'tour': [0, 5, 12, 8, 3, 15, 19, 7, 14, 10, 2, 17, 9, 1, 13, 18, 6, 20, 4, 11, 16, 21], 'cur_cost': 42681.0}, {'tour': [0, 5, 12, 8, 3, 19, 15, 11, 7, 2, 18, 14, 10, 6, 1, 17, 13, 9, 4, 20, 16, 21], 'cur_cost': 39428.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 16, 8, 19, 4, 11, 17, 6, 20, 10, 13, 1], 'cur_cost': 41521.0}, {'tour': [0, 5, 12, 8, 3, 19, 15, 10, 7, 14, 18, 2, 9, 13, 17, 1, 6, 16, 4, 11, 20, 21], 'cur_cost': 42640.0}, {'tour': array([17,  5, 21, 20,  4, 15,  7,  1, 19,  6,  0, 12,  2, 10, 13, 16, 18,
        8,  3, 11, 14,  9]), 'cur_cost': 37268.0}, {'tour': array([ 8,  2, 11, 17,  0, 13, 20,  6, 14, 18, 21,  9,  3, 15,  7, 19,  5,
        1, 16,  4, 12, 10]), 'cur_cost': 43163.0}, {'tour': array([ 1,  4,  6, 16, 14, 21, 10,  3, 18,  0, 15,  9, 20,  8, 17, 11, 12,
       19,  7,  5,  2, 13]), 'cur_cost': 36283.0}]
2025-06-26 17:31:22,967 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:31:22,967 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 151, 'skip_rate': 0.059602649006622516, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 142, 'cache_hits': 172, 'similarity_calculations': 3029, 'cache_hit_rate': 0.056784417299438755, 'cache_size': 2857}}
2025-06-26 17:31:22,968 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:31:22,968 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 3, 15, 19, 7, 14, 2, 10, 18, 1, 9, 13, 6, 16, 4, 20, 11, 17, 21], 'cur_cost': 42758.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 10, 18, 6, 13, 1, 9, 16, 4, 20, 11, 17, 21], 'cur_cost': 45848.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 3, 15, 19, 7, 14, 10, 2, 17, 9, 1, 13, 18, 6, 20, 4, 11, 16, 21], 'cur_cost': 42681.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 3, 19, 15, 11, 7, 2, 18, 14, 10, 6, 1, 17, 13, 9, 4, 20, 16, 21], 'cur_cost': 39428.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 16, 8, 19, 4, 11, 17, 6, 20, 10, 13, 1], 'cur_cost': 41521.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 3, 19, 15, 10, 7, 14, 18, 2, 9, 13, 17, 1, 6, 16, 4, 11, 20, 21], 'cur_cost': 42640.0}}]
2025-06-26 17:31:22,968 - __main__ - INFO - 进化阶段完成
2025-06-26 17:31:22,968 - __main__ - INFO - 开始评估阶段
2025-06-26 17:31:22,969 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:31:22,969 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:31:22,969 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 15933.0 → 16252.0 (Δ -319.0)
- Mean cost: 31041.9 → 38784.2
- Diversity: 0.486 → 0.576 (Δ 0.0905)

#### Elite metrics
- Elite count: 68 → 73
- Best elite cost: 9455 → 9455 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 28.6% , avg Δ -11866.43
- Exploit: 3 inds, success 66.7% , avg Δ 1880.67

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [10399.0, 9940.0, 15933.0], diversity [0.9124183006535949, 0.9124183006535946, 0.7757575757575756]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:31:22,969 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:31:39,969 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -11866.43,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 1880.67,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore strategy is ineffective (negative improvement), exploit is stable but not driving significant elite improvement",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_suggestions": "consider refining exploration parameters to avoid detrimental solutions, and temporarily increase elite preservation to stabilize quality"
  }
}
```
2025-06-26 17:31:39,977 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:31:39,977 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -11866.43,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 1880.67,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore strategy is ineffective (negative improvement), exploit is stable but not driving significant elite improvement",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_suggestions": "consider refining exploration parameters to avoid detrimental solutions, and temporarily increase elite preservation to stabilize quality"
  }
}
```
2025-06-26 17:31:39,977 - __main__ - INFO - 评估阶段完成
2025-06-26 17:31:39,977 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -11866.43,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 1880.67,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "explore strategy is ineffective (negative improvement), exploit is stable but not driving significant elite improvement",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_suggestions": "consider refining exploration parameters to avoid detrimental solutions, and temporarily increase elite preservation to stabilize quality"
  }
}
```
2025-06-26 17:31:39,977 - __main__ - INFO - 当前最佳适应度: 16252.0
2025-06-26 17:31:39,980 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite3_22_route_1.pkl
2025-06-26 17:31:39,980 - __main__ - INFO - composite3_22 开始进化第 3 代
2025-06-26 17:31:39,980 - __main__ - INFO - 开始分析阶段
2025-06-26 17:31:39,982 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:31:39,984 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 16252.0, 'max': 45848.0, 'mean': 38784.2, 'std': 7994.210052281588}, 'diversity': 0.8151515151515151, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:31:39,987 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 16252.0, 'max': 45848.0, 'mean': 38784.2, 'std': 7994.210052281588}, 'diversity_level': 0.8151515151515151, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[360, 2564], [380, 2529], [340, 2529], [360, 2604], [2427, 580], [2455, 552], [2399, 552], [2399, 608], [2455, 608], [2491, 3776], [2491, 3817], [2530, 3789], [2515, 3743], [2467, 3743], [2452, 3789], [1620, 2430], [1660, 2430], [1640, 2465], [1600, 2465], [1580, 2430], [1600, 2395], [1640, 2395]], 'distance_matrix': array([[   0.,   40.,   40.,   40., 2865., 2905., 2865., 2826., 2866.,
        2452., 2472., 2492., 2456., 2414., 2424., 1267., 1307., 1284.,
        1244., 1227., 1251., 1291.],
       [  40.,    0.,   40.,   78., 2826., 2866., 2826., 2787., 2828.,
        2452., 2473., 2492., 2456., 2414., 2425., 1244., 1284., 1262.,
        1222., 1204., 1227., 1267.],
       [  40.,   40.,    0.,   78., 2856., 2895., 2854., 2816., 2857.,
        2486., 2507., 2527., 2491., 2449., 2459., 1284., 1324., 1302.,
        1262., 1244., 1267., 1307.],
       [  40.,   78.,   78.,    0., 2893., 2933., 2893., 2853., 2894.,
        2432., 2452., 2472., 2437., 2395., 2404., 1272., 1312., 1288.,
        1248., 1232., 1257., 1297.],
       [2865., 2826., 2856., 2893.,    0.,   40.,   40.,   40.,   40.,
        3197., 3238., 3211., 3164., 3163., 3209., 2018., 2003., 2043.,
        2058., 2035., 1995., 1978.],
       [2905., 2866., 2895., 2933.,   40.,    0.,   56.,   79.,   56.,
        3224., 3265., 3238., 3192., 3191., 3237., 2055., 2039., 2079.,
        2095., 2072., 2032., 2015.],
       [2865., 2826., 2854., 2893.,   40.,   56.,    0.,   56.,   79.,
        3225., 3266., 3240., 3193., 3192., 3237., 2033., 2018., 2058.,
        2073., 2049., 2009., 1993.],
       [2826., 2787., 2816., 2853.,   40.,   79.,   56.,    0.,   56.,
        3169., 3210., 3184., 3137., 3136., 3181., 1982., 1966., 2006.,
        2022., 1998., 1957., 1942.],
       [2866., 2828., 2857., 2894.,   40.,   56.,   79.,   56.,    0.,
        3168., 3209., 3182., 3136., 3135., 3181., 2004., 1988., 2028.,
        2044., 2021., 1981., 1964.],
       [2452., 2452., 2486., 2432., 3197., 3224., 3225., 3169., 3168.,
           0.,   41.,   41.,   41.,   41.,   41., 1603., 1582., 1563.,
        1585., 1625., 1643., 1622.],
       [2472., 2473., 2507., 2452., 3238., 3265., 3266., 3210., 3209.,
          41.,    0.,   48.,   78.,   78.,   48., 1638., 1617., 1598.,
        1619., 1659., 1678., 1657.],
       [2492., 2492., 2527., 2472., 3211., 3238., 3240., 3184., 3182.,
          41.,   48.,    0.,   48.,   78.,   78., 1636., 1614., 1595.,
        1618., 1658., 1676., 1654.],
       [2456., 2456., 2491., 2437., 3164., 3192., 3193., 3137., 3136.,
          41.,   78.,   48.,    0.,   48.,   78., 1589., 1567., 1549.,
        1572., 1612., 1629., 1607.],
       [2414., 2414., 2449., 2395., 3163., 3191., 3192., 3136., 3135.,
          41.,   78.,   78.,   48.,    0.,   48., 1562., 1541., 1522.,
        1544., 1585., 1603., 1581.],
       [2424., 2425., 2459., 2404., 3209., 3237., 3237., 3181., 3181.,
          41.,   48.,   78.,   78.,   48.,    0., 1593., 1573., 1553.,
        1574., 1615., 1634., 1613.],
       [1267., 1244., 1284., 1272., 2018., 2055., 2033., 1982., 2004.,
        1603., 1638., 1636., 1589., 1562., 1593.,    0.,   40.,   40.,
          40.,   40.,   40.,   40.],
       [1307., 1284., 1324., 1312., 2003., 2039., 2018., 1966., 1988.,
        1582., 1617., 1614., 1567., 1541., 1573.,   40.,    0.,   40.,
          69.,   80.,   69.,   40.],
       [1284., 1262., 1302., 1288., 2043., 2079., 2058., 2006., 2028.,
        1563., 1598., 1595., 1549., 1522., 1553.,   40.,   40.,    0.,
          40.,   69.,   81.,   70.],
       [1244., 1222., 1262., 1248., 2058., 2095., 2073., 2022., 2044.,
        1585., 1619., 1618., 1572., 1544., 1574.,   40.,   69.,   40.,
           0.,   40.,   70.,   81.],
       [1227., 1204., 1244., 1232., 2035., 2072., 2049., 1998., 2021.,
        1625., 1659., 1658., 1612., 1585., 1615.,   40.,   80.,   69.,
          40.,    0.,   40.,   69.],
       [1251., 1227., 1267., 1257., 1995., 2032., 2009., 1957., 1981.,
        1643., 1678., 1676., 1629., 1603., 1634.,   40.,   69.,   81.,
          70.,   40.,    0.,   40.],
       [1291., 1267., 1307., 1297., 1978., 2015., 1993., 1942., 1964.,
        1622., 1657., 1654., 1607., 1581., 1613.,   40.,   40.,   70.,
          81.,   69.,   40.,    0.]])}
2025-06-26 17:31:39,988 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:31:39,988 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:31:39,988 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:31:39,990 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:31:39,990 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:31:39,990 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:31:39,990 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:31:39,990 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:31:39,990 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:31:40,019 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:31:40,019 - __main__ - INFO - 精英专家分析报告: {'elite_count': 73, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 6797.0, 'avg_gap': 29328.99452054794}, 'structure_gap': {'unique_elite_edges': 58, 'unique_pop_edges': 128, 'common_edges': 20}}, 'elite_diversity': {'diversity_score': 0.3567870485678706}}
2025-06-26 17:31:40,019 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:31:40,020 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:31:40,021 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:31:40,021 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 16252.0, mean 38784.2, max 45848.0, std 7994.210052281588
- diversity: 0.8151515151515151
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [9940.0, 15933.0, 16252.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [340.0, 552.0, 2530.0, 3817.0]
- centroid/spread: [1811.864, 2399.682] | rms 1371.908
- density_grid(3x3): [[0, 0, 5], [4, 7, 0], [0, 0, 6]]
- edge_len_stats(min,q1,med,q3,max): [40.0, 1244.0, 1643.0, 2472.0, 3266.0]
- long_edge_ratio: 0.247
- direction_hist(8 sectors): [4, 3, 2, 3, 3, 2, 4, 0]
- nn_median_dist: 40.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:31:40,021 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:32:06,437 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "potentially multimodal",
    "deceptiveness": "low to moderate",
    "edge_length_variability": "high (range 40.0 to 3266.0)",
    "long_edge_prevalence": "moderate (24.7% long edges)"
  },
  "population_state": {
    "diversity": "high (0.815)",
    "coverage": "uneven (density_grid shows clusters in 3 cells, empty in others)",
    "convergence": "none detected (0.0)",
    "elite_influence": "minimal (no elites present)"
  },
  "difficult_regions": {
    "spatial_indicators": [
      "low-density cells (top-left and bottom-middle in density_grid)",
      "long-edge corridors (q3 edge length 2472.0 suggests challenging connections)"
    ],
    "structural_indicators": "none explicitly identified"
  },
  "opportunity_regions": {
    "spatial_indicators": [
      "high-density cells (center-right in density_grid with 7 nodes)",
      "tight clusters (nn_median_dist 40.0 suggests exploitable proximity)"
    ],
    "structural_indicators": "none explicitly identified"
  },
  "evolution_phase": "early exploration (iteration 2/5)",
  "evolution_direction": {
    "suggested_operators": [
      "spatially-aware crossover (prioritize edges within high-density cells)",
      "directed mutation (reduce long edges in difficult regions)",
      "diversity maintenance (preserve coverage of low-density cells)",
      "local search intensification around nn_median_dist clusters"
    ],
    "focus_areas": [
      "exploit high-density opportunity regions",
      "improve connectivity in difficult regions",
      "balance exploration/exploitation given current diversity"
    ]
  }
}
```
2025-06-26 17:32:06,437 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:32:06,438 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low to moderate', 'edge_length_variability': 'high (range 40.0 to 3266.0)', 'long_edge_prevalence': 'moderate (24.7% long edges)'}, 'population_state': {'diversity': 'high (0.815)', 'coverage': 'uneven (density_grid shows clusters in 3 cells, empty in others)', 'convergence': 'none detected (0.0)', 'elite_influence': 'minimal (no elites present)'}, 'difficult_regions': {'spatial_indicators': ['low-density cells (top-left and bottom-middle in density_grid)', 'long-edge corridors (q3 edge length 2472.0 suggests challenging connections)'], 'structural_indicators': 'none explicitly identified'}, 'opportunity_regions': {'spatial_indicators': ['high-density cells (center-right in density_grid with 7 nodes)', 'tight clusters (nn_median_dist 40.0 suggests exploitable proximity)'], 'structural_indicators': 'none explicitly identified'}, 'evolution_phase': 'early exploration (iteration 2/5)', 'evolution_direction': {'suggested_operators': ['spatially-aware crossover (prioritize edges within high-density cells)', 'directed mutation (reduce long edges in difficult regions)', 'diversity maintenance (preserve coverage of low-density cells)', 'local search intensification around nn_median_dist clusters'], 'focus_areas': ['exploit high-density opportunity regions', 'improve connectivity in difficult regions', 'balance exploration/exploitation given current diversity']}}
2025-06-26 17:32:06,438 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:32:06,438 - __main__ - INFO - 分析阶段完成
2025-06-26 17:32:06,438 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low to moderate', 'edge_length_variability': 'high (range 40.0 to 3266.0)', 'long_edge_prevalence': 'moderate (24.7% long edges)'}, 'population_state': {'diversity': 'high (0.815)', 'coverage': 'uneven (density_grid shows clusters in 3 cells, empty in others)', 'convergence': 'none detected (0.0)', 'elite_influence': 'minimal (no elites present)'}, 'difficult_regions': {'spatial_indicators': ['low-density cells (top-left and bottom-middle in density_grid)', 'long-edge corridors (q3 edge length 2472.0 suggests challenging connections)'], 'structural_indicators': 'none explicitly identified'}, 'opportunity_regions': {'spatial_indicators': ['high-density cells (center-right in density_grid with 7 nodes)', 'tight clusters (nn_median_dist 40.0 suggests exploitable proximity)'], 'structural_indicators': 'none explicitly identified'}, 'evolution_phase': 'early exploration (iteration 2/5)', 'evolution_direction': {'suggested_operators': ['spatially-aware crossover (prioritize edges within high-density cells)', 'directed mutation (reduce long edges in difficult regions)', 'diversity maintenance (preserve coverage of low-density cells)', 'local search intensification around nn_median_dist clusters'], 'focus_areas': ['exploit high-density opportunity regions', 'improve connectivity in difficult regions', 'balance exploration/exploitation given current diversity']}}
2025-06-26 17:32:06,438 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:32:06,438 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:32:06,438 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 16252.0
  • mean_cost: 38784.2
- Search-space: ruggedness moderate, modality potentially multimodal, deceptiveness low to moderate
- Evolution phase: early exploration (iteration 2/5)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): spatial_indicators, structural_indicator
- Opportunity regions (# 2): spatial_indicators, structural_indicator
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:32:06,438 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:32:06,438 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:32:17,266 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify; bottom 40% exploit current best."
}
```
2025-06-26 17:32:17,270 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:32:17,270 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:32:17,270 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify; bottom 40% exploit current best."
}
```
2025-06-26 17:32:17,270 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:32:17,270 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:32:17,270 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify; bottom 40% exploit current best."
}
```
2025-06-26 17:32:17,270 - __main__ - INFO - 开始进化阶段
2025-06-26 17:32:17,272 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:32:17,272 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:32:17,272 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 42758.0 (rank 70.0%)
- Population diversity: 0.5761904761904761
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial_indicat, structural_indi
- Difficult regions to avoid (sample): spatial_indicat, structural_indi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:32:17,272 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:32:17,272 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:32:26,488 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 15, 3, 19, 11, 2, 17, 9, 14, 6, 20, 1, 13, 7, 18, 4, 16, 10, 21],
  "targeted_regions": "spatial_indicat, structural_indi",
  "strategy_comment": "Balanced exploration of sparse cells near opportunity regions"
}
```
2025-06-26 17:32:26,490 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:32:26,490 - ExplorationExpert - INFO - 探索路径生成完成，成本: 43503.0, 路径: [0, 5, 12, 8, 15, 3, 19, 11, 2, 17, 9, 14, 6, 20, 1, 13, 7, 18, 4, 16, 10, 21]
2025-06-26 17:32:26,490 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 15, 3, 19, 11, 2, 17, 9, 14, 6, 20, 1, 13, 7, 18, 4, 16, 10, 21], 'cur_cost': 43503.0}
2025-06-26 17:32:26,490 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:32:26,490 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:32:26,490 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 45848.0 (rank 90.0%)
- Population diversity: 0.6
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial_indicat, structural_indi
- Difficult regions to avoid (sample): spatial_indicat, structural_indi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:32:26,490 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:32:26,490 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:32:36,553 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "spatial_indicat, structural_indi",
  "strategy_comment": "Alternating high-low nodes to explore sparse regions"
}
```
2025-06-26 17:32:36,561 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:32:36,562 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16252.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 17:32:36,562 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}
2025-06-26 17:32:36,562 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:32:36,562 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:32:36,562 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 42681.0 (rank 70.0%)
- Population diversity: 0.580952380952381
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial_indicat, structural_indi
- Difficult regions to avoid (sample): spatial_indicat, structural_indi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:32:36,562 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:32:36,562 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:32:45,450 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 17, 8, 20, 1, 13, 6, 19, 4, 16, 10, 11],
  "targeted_regions": "spatial_indicat, structural_indi",
  "strategy_comment": "Balanced exploration of sparse cells near opportunity regions"
}
```
2025-06-26 17:32:45,457 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:32:45,458 - ExplorationExpert - INFO - 探索路径生成完成，成本: 43458.0, 路径: [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 17, 8, 20, 1, 13, 6, 19, 4, 16, 10, 11]
2025-06-26 17:32:45,458 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 17, 8, 20, 1, 13, 6, 19, 4, 16, 10, 11], 'cur_cost': 43458.0}
2025-06-26 17:32:45,458 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:32:45,458 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:32:45,458 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 39428.0 (rank 40.0%)
- Population diversity: 0.5714285714285714
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial_indicat, structural_indi
- Difficult regions to avoid (sample): spatial_indicat, structural_indi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:32:45,458 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:32:45,458 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:32:55,089 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 3, 19, 15, 21, 10, 7, 14, 18, 2, 16, 9, 4, 13, 20, 6, 1, 17, 11],
  "targeted_regions": "spatial_indicat, structural_indi",
  "strategy_comment": "Balanced exploration of opportunity regions with sparse adjacency"
}
```
2025-06-26 17:32:55,089 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:32:55,089 - ExplorationExpert - INFO - 探索路径生成完成，成本: 45376.0, 路径: [0, 5, 12, 8, 3, 19, 15, 21, 10, 7, 14, 18, 2, 16, 9, 4, 13, 20, 6, 1, 17, 11]
2025-06-26 17:32:55,089 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 3, 19, 15, 21, 10, 7, 14, 18, 2, 16, 9, 4, 13, 20, 6, 1, 17, 11], 'cur_cost': 45376.0}
2025-06-26 17:32:55,092 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:32:55,092 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:32:55,092 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 16252.0 (rank 0.0%)
- Population diversity: 0.5523809523809524
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial_indicat, structural_indi
- Difficult regions to avoid (sample): spatial_indicat, structural_indi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:32:55,092 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:32:55,092 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:33:04,267 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17],
  "targeted_regions": "spatial_indicat, structural_indi",
  "strategy_comment": "Balanced exploration of sparse cells near opportunity regions"
}
```
2025-06-26 17:33:04,267 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:33:04,268 - ExplorationExpert - INFO - 探索路径生成完成，成本: 42481.0, 路径: [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17]
2025-06-26 17:33:04,268 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}
2025-06-26 17:33:04,269 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:33:04,270 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:33:04,270 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41521.0 (rank 30.0%)
- Population diversity: 0.6238095238095238
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial_indicat, structural_indi
- Difficult regions to avoid (sample): spatial_indicat, structural_indi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:33:04,270 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:33:04,270 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:33:14,166 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17],
  "targeted_regions": "spatial_indicat, structural_indi",
  "strategy_comment": "Balanced exploration of sparse cells near opportunity regions"
}
```
2025-06-26 17:33:14,167 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:33:14,167 - ExplorationExpert - INFO - 探索路径生成完成，成本: 42481.0, 路径: [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17]
2025-06-26 17:33:14,167 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}
2025-06-26 17:33:14,169 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 17:33:14,169 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:33:14,169 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:33:14,169 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 40240.0
2025-06-26 17:33:14,670 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:33:14,670 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:33:14,671 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:33:14,687 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:33:14,687 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 8, 15, 3, 19, 11, 2, 17, 9, 14, 6, 20, 1, 13, 7, 18, 4, 16, 10, 21], 'cur_cost': 43503.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 17, 8, 20, 1, 13, 6, 19, 4, 16, 10, 11], 'cur_cost': 43458.0}, {'tour': [0, 5, 12, 8, 3, 19, 15, 21, 10, 7, 14, 18, 2, 16, 9, 4, 13, 20, 6, 1, 17, 11], 'cur_cost': 45376.0}, {'tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}, {'tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}, {'tour': array([10,  0, 21,  7, 14,  2, 11,  1,  3, 15, 16,  6, 19, 13, 18, 12,  4,
        9, 20,  8,  5, 17]), 'cur_cost': 40240.0}, {'tour': array([17,  5, 21, 20,  4, 15,  7,  1, 19,  6,  0, 12,  2, 10, 13, 16, 18,
        8,  3, 11, 14,  9]), 'cur_cost': 37268.0}, {'tour': array([ 8,  2, 11, 17,  0, 13, 20,  6, 14, 18, 21,  9,  3, 15,  7, 19,  5,
        1, 16,  4, 12, 10]), 'cur_cost': 43163.0}, {'tour': array([ 1,  4,  6, 16, 14, 21, 10,  3, 18,  0, 15,  9, 20,  8, 17, 11, 12,
       19,  7,  5,  2, 13]), 'cur_cost': 36283.0}]
2025-06-26 17:33:14,689 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:33:14,689 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 152, 'skip_rate': 0.05921052631578947, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 143, 'cache_hits': 172, 'similarity_calculations': 3035, 'cache_hit_rate': 0.056672158154859965, 'cache_size': 2863}}
2025-06-26 17:33:14,689 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 17:33:14,689 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:33:14,690 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:33:14,690 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:33:14,690 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 43208.0
2025-06-26 17:33:15,193 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:33:15,193 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:33:15,194 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:33:15,210 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:33:15,211 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 8, 15, 3, 19, 11, 2, 17, 9, 14, 6, 20, 1, 13, 7, 18, 4, 16, 10, 21], 'cur_cost': 43503.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 17, 8, 20, 1, 13, 6, 19, 4, 16, 10, 11], 'cur_cost': 43458.0}, {'tour': [0, 5, 12, 8, 3, 19, 15, 21, 10, 7, 14, 18, 2, 16, 9, 4, 13, 20, 6, 1, 17, 11], 'cur_cost': 45376.0}, {'tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}, {'tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}, {'tour': array([10,  0, 21,  7, 14,  2, 11,  1,  3, 15, 16,  6, 19, 13, 18, 12,  4,
        9, 20,  8,  5, 17]), 'cur_cost': 40240.0}, {'tour': array([ 3, 17, 19,  5, 16,  4, 14, 21,  2,  8, 15, 13,  7, 11,  9,  1, 10,
       18,  6,  0, 12, 20]), 'cur_cost': 43208.0}, {'tour': array([ 8,  2, 11, 17,  0, 13, 20,  6, 14, 18, 21,  9,  3, 15,  7, 19,  5,
        1, 16,  4, 12, 10]), 'cur_cost': 43163.0}, {'tour': array([ 1,  4,  6, 16, 14, 21, 10,  3, 18,  0, 15,  9, 20,  8, 17, 11, 12,
       19,  7,  5,  2, 13]), 'cur_cost': 36283.0}]
2025-06-26 17:33:15,212 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:33:15,212 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 153, 'skip_rate': 0.058823529411764705, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 144, 'cache_hits': 172, 'similarity_calculations': 3042, 'cache_hit_rate': 0.05654174884944116, 'cache_size': 2870}}
2025-06-26 17:33:15,212 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:33:15,214 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:33:15,214 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:33:15,214 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:33:15,214 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 36302.0
2025-06-26 17:33:15,716 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:33:15,717 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:33:15,717 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:33:15,733 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:33:15,733 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 8, 15, 3, 19, 11, 2, 17, 9, 14, 6, 20, 1, 13, 7, 18, 4, 16, 10, 21], 'cur_cost': 43503.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 17, 8, 20, 1, 13, 6, 19, 4, 16, 10, 11], 'cur_cost': 43458.0}, {'tour': [0, 5, 12, 8, 3, 19, 15, 21, 10, 7, 14, 18, 2, 16, 9, 4, 13, 20, 6, 1, 17, 11], 'cur_cost': 45376.0}, {'tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}, {'tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}, {'tour': array([10,  0, 21,  7, 14,  2, 11,  1,  3, 15, 16,  6, 19, 13, 18, 12,  4,
        9, 20,  8,  5, 17]), 'cur_cost': 40240.0}, {'tour': array([ 3, 17, 19,  5, 16,  4, 14, 21,  2,  8, 15, 13,  7, 11,  9,  1, 10,
       18,  6,  0, 12, 20]), 'cur_cost': 43208.0}, {'tour': array([ 4, 18, 20,  0, 10, 16, 13,  9, 12,  7,  2,  6, 19, 15, 14,  1, 21,
        8, 17,  3, 11,  5]), 'cur_cost': 36302.0}, {'tour': array([ 1,  4,  6, 16, 14, 21, 10,  3, 18,  0, 15,  9, 20,  8, 17, 11, 12,
       19,  7,  5,  2, 13]), 'cur_cost': 36283.0}]
2025-06-26 17:33:15,735 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:33:15,735 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 154, 'skip_rate': 0.05844155844155844, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 145, 'cache_hits': 172, 'similarity_calculations': 3050, 'cache_hit_rate': 0.05639344262295082, 'cache_size': 2878}}
2025-06-26 17:33:15,736 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:33:15,736 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:33:15,736 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:33:15,736 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:33:15,737 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 31678.0
2025-06-26 17:33:16,238 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:33:16,238 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:33:16,239 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:33:16,256 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:33:16,256 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 8, 15, 3, 19, 11, 2, 17, 9, 14, 6, 20, 1, 13, 7, 18, 4, 16, 10, 21], 'cur_cost': 43503.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 17, 8, 20, 1, 13, 6, 19, 4, 16, 10, 11], 'cur_cost': 43458.0}, {'tour': [0, 5, 12, 8, 3, 19, 15, 21, 10, 7, 14, 18, 2, 16, 9, 4, 13, 20, 6, 1, 17, 11], 'cur_cost': 45376.0}, {'tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}, {'tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}, {'tour': array([10,  0, 21,  7, 14,  2, 11,  1,  3, 15, 16,  6, 19, 13, 18, 12,  4,
        9, 20,  8,  5, 17]), 'cur_cost': 40240.0}, {'tour': array([ 3, 17, 19,  5, 16,  4, 14, 21,  2,  8, 15, 13,  7, 11,  9,  1, 10,
       18,  6,  0, 12, 20]), 'cur_cost': 43208.0}, {'tour': array([ 4, 18, 20,  0, 10, 16, 13,  9, 12,  7,  2,  6, 19, 15, 14,  1, 21,
        8, 17,  3, 11,  5]), 'cur_cost': 36302.0}, {'tour': array([ 5,  8, 19, 11,  7, 14, 13,  3, 17, 16,  4, 18,  2,  0, 21, 20,  1,
       15,  6,  9, 12, 10]), 'cur_cost': 31678.0}]
2025-06-26 17:33:16,258 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:33:16,258 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 155, 'skip_rate': 0.05806451612903226, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 146, 'cache_hits': 172, 'similarity_calculations': 3059, 'cache_hit_rate': 0.05622752533507682, 'cache_size': 2887}}
2025-06-26 17:33:16,259 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:33:16,259 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 15, 3, 19, 11, 2, 17, 9, 14, 6, 20, 1, 13, 7, 18, 4, 16, 10, 21], 'cur_cost': 43503.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 3, 15, 9, 21, 7, 14, 2, 17, 8, 20, 1, 13, 6, 19, 4, 16, 10, 11], 'cur_cost': 43458.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 3, 19, 15, 21, 10, 7, 14, 18, 2, 16, 9, 4, 13, 20, 6, 1, 17, 11], 'cur_cost': 45376.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 3, 8, 13, 18, 1, 6, 11, 16, 21, 4, 9, 14, 19, 2, 7, 12, 17], 'cur_cost': 42481.0}}]
2025-06-26 17:33:16,259 - __main__ - INFO - 进化阶段完成
2025-06-26 17:33:16,259 - __main__ - INFO - 开始评估阶段
2025-06-26 17:33:16,259 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:33:16,261 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:33:16,261 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 16252.0 → 16252.0 (Δ 0.0)
- Mean cost: 38784.2 → 38497.9
- Diversity: 0.576 → 0.624 (Δ 0.0476)

#### Elite metrics
- Elite count: 73 → 73
- Best elite cost: 9455 → 9455 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ -843.83
- Exploit: 4 inds, success 75.0% , avg Δ 1981.5

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [9940.0, 15933.0, 16252.0], diversity [0.9124183006535946, 0.7757575757575756, 0.8151515151515151]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:33:16,261 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:33:30,782 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -843.83,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 1981.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (explore:exploit)",
    "assessment": "exploit is more effective but explore is needed for diversity",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider increasing the number of individuals generated by explore to maintain diversity while still benefiting from exploit's high success rate"
  }
}
```
2025-06-26 17:33:30,783 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:33:30,783 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -843.83,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 1981.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (explore:exploit)",
    "assessment": "exploit is more effective but explore is needed for diversity",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider increasing the number of individuals generated by explore to maintain diversity while still benefiting from exploit's high success rate"
  }
}
```
2025-06-26 17:33:30,783 - __main__ - INFO - 评估阶段完成
2025-06-26 17:33:30,783 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -843.83,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 1981.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (explore:exploit)",
    "assessment": "exploit is more effective but explore is needed for diversity",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "consider increasing the number of individuals generated by explore to maintain diversity while still benefiting from exploit's high success rate"
  }
}
```
2025-06-26 17:33:30,789 - __main__ - INFO - 当前最佳适应度: 16252.0
2025-06-26 17:33:30,791 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite3_22_route_2.pkl
2025-06-26 17:33:30,792 - __main__ - INFO - composite3_22 开始进化第 4 代
2025-06-26 17:33:30,792 - __main__ - INFO - 开始分析阶段
2025-06-26 17:33:30,792 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:33:30,795 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 16252.0, 'max': 45376.0, 'mean': 38497.9, 'std': 8367.34774525357}, 'diversity': 0.8868686868686867, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 1, 1, 1, 2, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:33:30,799 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 16252.0, 'max': 45376.0, 'mean': 38497.9, 'std': 8367.34774525357}, 'diversity_level': 0.8868686868686867, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 1, 1, 1, 2, 1, 1, 1, 1]}, 'coordinates': [[360, 2564], [380, 2529], [340, 2529], [360, 2604], [2427, 580], [2455, 552], [2399, 552], [2399, 608], [2455, 608], [2491, 3776], [2491, 3817], [2530, 3789], [2515, 3743], [2467, 3743], [2452, 3789], [1620, 2430], [1660, 2430], [1640, 2465], [1600, 2465], [1580, 2430], [1600, 2395], [1640, 2395]], 'distance_matrix': array([[   0.,   40.,   40.,   40., 2865., 2905., 2865., 2826., 2866.,
        2452., 2472., 2492., 2456., 2414., 2424., 1267., 1307., 1284.,
        1244., 1227., 1251., 1291.],
       [  40.,    0.,   40.,   78., 2826., 2866., 2826., 2787., 2828.,
        2452., 2473., 2492., 2456., 2414., 2425., 1244., 1284., 1262.,
        1222., 1204., 1227., 1267.],
       [  40.,   40.,    0.,   78., 2856., 2895., 2854., 2816., 2857.,
        2486., 2507., 2527., 2491., 2449., 2459., 1284., 1324., 1302.,
        1262., 1244., 1267., 1307.],
       [  40.,   78.,   78.,    0., 2893., 2933., 2893., 2853., 2894.,
        2432., 2452., 2472., 2437., 2395., 2404., 1272., 1312., 1288.,
        1248., 1232., 1257., 1297.],
       [2865., 2826., 2856., 2893.,    0.,   40.,   40.,   40.,   40.,
        3197., 3238., 3211., 3164., 3163., 3209., 2018., 2003., 2043.,
        2058., 2035., 1995., 1978.],
       [2905., 2866., 2895., 2933.,   40.,    0.,   56.,   79.,   56.,
        3224., 3265., 3238., 3192., 3191., 3237., 2055., 2039., 2079.,
        2095., 2072., 2032., 2015.],
       [2865., 2826., 2854., 2893.,   40.,   56.,    0.,   56.,   79.,
        3225., 3266., 3240., 3193., 3192., 3237., 2033., 2018., 2058.,
        2073., 2049., 2009., 1993.],
       [2826., 2787., 2816., 2853.,   40.,   79.,   56.,    0.,   56.,
        3169., 3210., 3184., 3137., 3136., 3181., 1982., 1966., 2006.,
        2022., 1998., 1957., 1942.],
       [2866., 2828., 2857., 2894.,   40.,   56.,   79.,   56.,    0.,
        3168., 3209., 3182., 3136., 3135., 3181., 2004., 1988., 2028.,
        2044., 2021., 1981., 1964.],
       [2452., 2452., 2486., 2432., 3197., 3224., 3225., 3169., 3168.,
           0.,   41.,   41.,   41.,   41.,   41., 1603., 1582., 1563.,
        1585., 1625., 1643., 1622.],
       [2472., 2473., 2507., 2452., 3238., 3265., 3266., 3210., 3209.,
          41.,    0.,   48.,   78.,   78.,   48., 1638., 1617., 1598.,
        1619., 1659., 1678., 1657.],
       [2492., 2492., 2527., 2472., 3211., 3238., 3240., 3184., 3182.,
          41.,   48.,    0.,   48.,   78.,   78., 1636., 1614., 1595.,
        1618., 1658., 1676., 1654.],
       [2456., 2456., 2491., 2437., 3164., 3192., 3193., 3137., 3136.,
          41.,   78.,   48.,    0.,   48.,   78., 1589., 1567., 1549.,
        1572., 1612., 1629., 1607.],
       [2414., 2414., 2449., 2395., 3163., 3191., 3192., 3136., 3135.,
          41.,   78.,   78.,   48.,    0.,   48., 1562., 1541., 1522.,
        1544., 1585., 1603., 1581.],
       [2424., 2425., 2459., 2404., 3209., 3237., 3237., 3181., 3181.,
          41.,   48.,   78.,   78.,   48.,    0., 1593., 1573., 1553.,
        1574., 1615., 1634., 1613.],
       [1267., 1244., 1284., 1272., 2018., 2055., 2033., 1982., 2004.,
        1603., 1638., 1636., 1589., 1562., 1593.,    0.,   40.,   40.,
          40.,   40.,   40.,   40.],
       [1307., 1284., 1324., 1312., 2003., 2039., 2018., 1966., 1988.,
        1582., 1617., 1614., 1567., 1541., 1573.,   40.,    0.,   40.,
          69.,   80.,   69.,   40.],
       [1284., 1262., 1302., 1288., 2043., 2079., 2058., 2006., 2028.,
        1563., 1598., 1595., 1549., 1522., 1553.,   40.,   40.,    0.,
          40.,   69.,   81.,   70.],
       [1244., 1222., 1262., 1248., 2058., 2095., 2073., 2022., 2044.,
        1585., 1619., 1618., 1572., 1544., 1574.,   40.,   69.,   40.,
           0.,   40.,   70.,   81.],
       [1227., 1204., 1244., 1232., 2035., 2072., 2049., 1998., 2021.,
        1625., 1659., 1658., 1612., 1585., 1615.,   40.,   80.,   69.,
          40.,    0.,   40.,   69.],
       [1251., 1227., 1267., 1257., 1995., 2032., 2009., 1957., 1981.,
        1643., 1678., 1676., 1629., 1603., 1634.,   40.,   69.,   81.,
          70.,   40.,    0.,   40.],
       [1291., 1267., 1307., 1297., 1978., 2015., 1993., 1942., 1964.,
        1622., 1657., 1654., 1607., 1581., 1613.,   40.,   40.,   70.,
          81.,   69.,   40.,    0.]])}
2025-06-26 17:33:30,800 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:33:30,800 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:33:30,800 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:33:30,801 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:33:30,801 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:33:30,801 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:33:30,802 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:33:30,802 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:33:30,802 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:33:30,830 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:33:30,831 - __main__ - INFO - 精英专家分析报告: {'elite_count': 73, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 6797.0, 'avg_gap': 29042.694520547946}, 'structure_gap': {'unique_elite_edges': 53, 'unique_pop_edges': 135, 'common_edges': 25}}, 'elite_diversity': {'diversity_score': 0.3567870485678706}}
2025-06-26 17:33:30,831 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:33:30,831 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:33:30,834 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:33:30,834 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 16252.0, mean 38497.9, max 45376.0, std 8367.34774525357
- diversity: 0.8868686868686867
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [15933.0, 16252.0, 16252.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [340.0, 552.0, 2530.0, 3817.0]
- centroid/spread: [1811.864, 2399.682] | rms 1371.908
- density_grid(3x3): [[0, 0, 5], [4, 7, 0], [0, 0, 6]]
- edge_len_stats(min,q1,med,q3,max): [40.0, 1244.0, 1643.0, 2472.0, 3266.0]
- long_edge_ratio: 0.247
- direction_hist(8 sectors): [4, 3, 2, 3, 3, 2, 4, 0]
- nn_median_dist: 40.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:33:30,834 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:33:55,651 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 40.0 to 3266.0, with significant std deviation)",
    "modality": "Potentially multimodal (diverse edge lengths and directions, no clear clustering)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.247 suggests some long, potentially suboptimal edges)"
  },
  "population_state": {
    "coverage": "Good (diversity 0.887) but uneven (density_grid shows clusters in top-right and bottom-left cells)",
    "convergence": "None detected (convergence 0.0, recent best costs stable)",
    "elite_influence": "Minimal (no elites, only one fixed node)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges >2472.0, especially in direction sectors with low counts)",
    "Low-density grid cells (top-left and center-right in 3x3 grid)"
  ],
  "opportunity_regions": [
    "High-density cells (5 nodes in top-right cell, 6 in bottom-left cell)",
    "Short-edge clusters (edges <1244.0, especially around median nn_dist of 40.0)"
  ],
  "evolution_phase": "Early exploration (iteration 3/5, high diversity, no convergence)",
  "evolution_direction": [
    "Prioritize exploitation in high-density cells (e.g., intensify 2-opt near centroid [1811.864, 2399.682])",
    "Address difficult regions with long-edge mutation operators",
    "Maintain diversity via restart if density clusters become too pronounced"
  ]
}
```
2025-06-26 17:33:55,651 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:33:55,651 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 40.0 to 3266.0, with significant std deviation)', 'modality': 'Potentially multimodal (diverse edge lengths and directions, no clear clustering)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.247 suggests some long, potentially suboptimal edges)'}, 'population_state': {'coverage': 'Good (diversity 0.887) but uneven (density_grid shows clusters in top-right and bottom-left cells)', 'convergence': 'None detected (convergence 0.0, recent best costs stable)', 'elite_influence': 'Minimal (no elites, only one fixed node)'}, 'difficult_regions': ['Long-edge corridors (edges >2472.0, especially in direction sectors with low counts)', 'Low-density grid cells (top-left and center-right in 3x3 grid)'], 'opportunity_regions': ['High-density cells (5 nodes in top-right cell, 6 in bottom-left cell)', 'Short-edge clusters (edges <1244.0, especially around median nn_dist of 40.0)'], 'evolution_phase': 'Early exploration (iteration 3/5, high diversity, no convergence)', 'evolution_direction': ['Prioritize exploitation in high-density cells (e.g., intensify 2-opt near centroid [1811.864, 2399.682])', 'Address difficult regions with long-edge mutation operators', 'Maintain diversity via restart if density clusters become too pronounced']}
2025-06-26 17:33:55,652 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:33:55,652 - __main__ - INFO - 分析阶段完成
2025-06-26 17:33:55,652 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 40.0 to 3266.0, with significant std deviation)', 'modality': 'Potentially multimodal (diverse edge lengths and directions, no clear clustering)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.247 suggests some long, potentially suboptimal edges)'}, 'population_state': {'coverage': 'Good (diversity 0.887) but uneven (density_grid shows clusters in top-right and bottom-left cells)', 'convergence': 'None detected (convergence 0.0, recent best costs stable)', 'elite_influence': 'Minimal (no elites, only one fixed node)'}, 'difficult_regions': ['Long-edge corridors (edges >2472.0, especially in direction sectors with low counts)', 'Low-density grid cells (top-left and center-right in 3x3 grid)'], 'opportunity_regions': ['High-density cells (5 nodes in top-right cell, 6 in bottom-left cell)', 'Short-edge clusters (edges <1244.0, especially around median nn_dist of 40.0)'], 'evolution_phase': 'Early exploration (iteration 3/5, high diversity, no convergence)', 'evolution_direction': ['Prioritize exploitation in high-density cells (e.g., intensify 2-opt near centroid [1811.864, 2399.682])', 'Address difficult regions with long-edge mutation operators', 'Maintain diversity via restart if density clusters become too pronounced']}
2025-06-26 17:33:55,653 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:33:55,653 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:33:55,653 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 16252.0
  • mean_cost: 38497.9
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 40.0 to 3266.0, with significant std deviation), modality Potentially multimodal (diverse edge lengths and directions, no clear clustering), deceptiveness Possible deceptive regions (long_edge_ratio of 0.247 suggests some long, potentially suboptimal edges)
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Low-density grid cel
- Opportunity regions (# 2): High-density cells (, Short-edge clusters 
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:33:55,653 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:33:55,653 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:34:06,676 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and rugged landscape favors exploration (70%). Top 60% explore to diversify; bottom 40% exploit promising regions."
}
```
2025-06-26 17:34:06,678 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:34:06,678 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:34:06,679 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and rugged landscape favors exploration (70%). Top 60% explore to diversify; bottom 40% exploit promising regions."
}
```
2025-06-26 17:34:06,679 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:34:06,679 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:34:06,679 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity and rugged landscape favors exploration (70%). Top 60% explore to diversify; bottom 40% exploit promising regions."
}
```
2025-06-26 17:34:06,679 - __main__ - INFO - 开始进化阶段
2025-06-26 17:34:06,679 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:34:06,679 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:34:06,679 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 43503.0 (rank 80.0%)
- Population diversity: 0.6238095238095238
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:34:06,679 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:34:06,679 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:34:16,860 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 1, 6, 11, 16, 21, 2, 7, 12, 17, 3, 8, 13, 18, 4, 9, 14, 19],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Leverage high-density clusters with short edges, skip long corridors"
}
```
2025-06-26 17:34:16,860 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:34:16,860 - ExplorationExpert - INFO - 探索路径生成完成，成本: 42603.0, 路径: [0, 5, 10, 15, 20, 1, 6, 11, 16, 21, 2, 7, 12, 17, 3, 8, 13, 18, 4, 9, 14, 19]
2025-06-26 17:34:16,860 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 1, 6, 11, 16, 21, 2, 7, 12, 17, 3, 8, 13, 18, 4, 9, 14, 19], 'cur_cost': 42603.0}
2025-06-26 17:34:16,860 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:34:16,865 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:34:16,866 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 16252.0 (rank 0.0%)
- Population diversity: 0.6095238095238096
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:34:16,866 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:34:16,866 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:34:26,505 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 18, 9, 13, 4, 17, 10, 20, 1, 16, 6, 11, 21],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Prioritize dense clusters with short edges, explore nearby sparse cells"
}
```
2025-06-26 17:34:26,506 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:34:26,506 - ExplorationExpert - INFO - 探索路径生成完成，成本: 43463.0, 路径: [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 18, 9, 13, 4, 17, 10, 20, 1, 16, 6, 11, 21]
2025-06-26 17:34:26,506 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 18, 9, 13, 4, 17, 10, 20, 1, 16, 6, 11, 21], 'cur_cost': 43463.0}
2025-06-26 17:34:26,506 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:34:26,507 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:34:26,507 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 43458.0 (rank 70.0%)
- Population diversity: 0.5714285714285714
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:34:26,507 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:34:26,508 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:34:37,316 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Prioritize dense clusters with short edges, explore nearby sparse cells"
}
```
2025-06-26 17:34:37,321 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:34:37,321 - ExplorationExpert - INFO - 探索路径生成完成，成本: 46001.0, 路径: [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21]
2025-06-26 17:34:37,321 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}
2025-06-26 17:34:37,321 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:34:37,321 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:34:37,323 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 45376.0 (rank 80.0%)
- Population diversity: 0.5761904761904761
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:34:37,323 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:34:37,323 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:34:46,663 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Alternating dense/sparse traversal with reverse sweep"
}
```
2025-06-26 17:34:46,663 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:34:46,663 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16252.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 17:34:46,663 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}
2025-06-26 17:34:46,663 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:34:46,663 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:34:46,663 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 42481.0 (rank 40.0%)
- Population diversity: 0.6
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:34:46,665 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:34:46,665 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:34:56,891 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Leverage dense clusters with short edges, explore adjacent sparse cells"
}
```
2025-06-26 17:34:56,892 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:34:56,892 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16252.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 17:34:56,892 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}
2025-06-26 17:34:56,894 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:34:56,894 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:34:56,894 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 42481.0 (rank 50.0%)
- Population diversity: 0.6
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:34:56,895 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:34:56,895 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:35:05,908 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "High-density ce, Short-edge clus, adjacent low-density",
  "strategy_comment": "Alternating high/low density traversal with reverse sweep"
}
```
2025-06-26 17:35:05,908 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:35:05,908 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16252.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 17:35:05,908 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}
2025-06-26 17:35:05,910 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 17:35:05,910 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:35:05,910 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:35:05,910 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 40967.0
2025-06-26 17:35:06,412 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:35:06,412 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:35:06,412 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:35:06,430 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:35:06,430 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 1, 6, 11, 16, 21, 2, 7, 12, 17, 3, 8, 13, 18, 4, 9, 14, 19], 'cur_cost': 42603.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 18, 9, 13, 4, 17, 10, 20, 1, 16, 6, 11, 21], 'cur_cost': 43463.0}, {'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': array([ 5, 20,  3, 18, 13,  6, 17,  4,  9, 19, 12, 15, 11,  7,  0, 21, 16,
       14, 10,  8,  1,  2]), 'cur_cost': 40967.0}, {'tour': array([ 3, 17, 19,  5, 16,  4, 14, 21,  2,  8, 15, 13,  7, 11,  9,  1, 10,
       18,  6,  0, 12, 20]), 'cur_cost': 43208.0}, {'tour': array([ 4, 18, 20,  0, 10, 16, 13,  9, 12,  7,  2,  6, 19, 15, 14,  1, 21,
        8, 17,  3, 11,  5]), 'cur_cost': 36302.0}, {'tour': array([ 5,  8, 19, 11,  7, 14, 13,  3, 17, 16,  4, 18,  2,  0, 21, 20,  1,
       15,  6,  9, 12, 10]), 'cur_cost': 31678.0}]
2025-06-26 17:35:06,431 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:35:06,431 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 156, 'skip_rate': 0.057692307692307696, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 147, 'cache_hits': 172, 'similarity_calculations': 3069, 'cache_hit_rate': 0.05604431410883024, 'cache_size': 2897}}
2025-06-26 17:35:06,432 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 17:35:06,432 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:35:06,432 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:35:06,432 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:35:06,433 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 30502.0
2025-06-26 17:35:06,935 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:35:06,935 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:35:06,936 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:35:06,953 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:35:06,953 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 1, 6, 11, 16, 21, 2, 7, 12, 17, 3, 8, 13, 18, 4, 9, 14, 19], 'cur_cost': 42603.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 18, 9, 13, 4, 17, 10, 20, 1, 16, 6, 11, 21], 'cur_cost': 43463.0}, {'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': array([ 5, 20,  3, 18, 13,  6, 17,  4,  9, 19, 12, 15, 11,  7,  0, 21, 16,
       14, 10,  8,  1,  2]), 'cur_cost': 40967.0}, {'tour': array([20, 10, 12, 16,  2,  5,  4, 18, 13, 14, 17,  0, 15, 11,  9,  3,  6,
       21,  1,  7,  8, 19]), 'cur_cost': 30502.0}, {'tour': array([ 4, 18, 20,  0, 10, 16, 13,  9, 12,  7,  2,  6, 19, 15, 14,  1, 21,
        8, 17,  3, 11,  5]), 'cur_cost': 36302.0}, {'tour': array([ 5,  8, 19, 11,  7, 14, 13,  3, 17, 16,  4, 18,  2,  0, 21, 20,  1,
       15,  6,  9, 12, 10]), 'cur_cost': 31678.0}]
2025-06-26 17:35:06,954 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:35:06,955 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 157, 'skip_rate': 0.05732484076433121, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 148, 'cache_hits': 172, 'similarity_calculations': 3080, 'cache_hit_rate': 0.05584415584415584, 'cache_size': 2908}}
2025-06-26 17:35:06,955 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:35:06,955 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:35:06,955 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:35:06,956 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:35:06,956 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 42289.0
2025-06-26 17:35:07,457 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:35:07,457 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:35:07,457 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:35:07,478 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:35:07,478 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 1, 6, 11, 16, 21, 2, 7, 12, 17, 3, 8, 13, 18, 4, 9, 14, 19], 'cur_cost': 42603.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 18, 9, 13, 4, 17, 10, 20, 1, 16, 6, 11, 21], 'cur_cost': 43463.0}, {'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': array([ 5, 20,  3, 18, 13,  6, 17,  4,  9, 19, 12, 15, 11,  7,  0, 21, 16,
       14, 10,  8,  1,  2]), 'cur_cost': 40967.0}, {'tour': array([20, 10, 12, 16,  2,  5,  4, 18, 13, 14, 17,  0, 15, 11,  9,  3,  6,
       21,  1,  7,  8, 19]), 'cur_cost': 30502.0}, {'tour': array([17,  6, 21, 11,  0, 14, 18, 12,  5, 16, 13,  1,  8,  2, 10,  4,  7,
       19,  9, 20, 15,  3]), 'cur_cost': 42289.0}, {'tour': array([ 5,  8, 19, 11,  7, 14, 13,  3, 17, 16,  4, 18,  2,  0, 21, 20,  1,
       15,  6,  9, 12, 10]), 'cur_cost': 31678.0}]
2025-06-26 17:35:07,480 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:35:07,480 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 158, 'skip_rate': 0.056962025316455694, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 149, 'cache_hits': 172, 'similarity_calculations': 3092, 'cache_hit_rate': 0.055627425614489, 'cache_size': 2920}}
2025-06-26 17:35:07,481 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:35:07,481 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:35:07,481 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:35:07,482 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:35:07,482 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 38670.0
2025-06-26 17:35:07,986 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:35:07,987 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:35:07,987 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:35:08,004 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:35:08,004 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 1, 6, 11, 16, 21, 2, 7, 12, 17, 3, 8, 13, 18, 4, 9, 14, 19], 'cur_cost': 42603.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 18, 9, 13, 4, 17, 10, 20, 1, 16, 6, 11, 21], 'cur_cost': 43463.0}, {'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': array([ 5, 20,  3, 18, 13,  6, 17,  4,  9, 19, 12, 15, 11,  7,  0, 21, 16,
       14, 10,  8,  1,  2]), 'cur_cost': 40967.0}, {'tour': array([20, 10, 12, 16,  2,  5,  4, 18, 13, 14, 17,  0, 15, 11,  9,  3,  6,
       21,  1,  7,  8, 19]), 'cur_cost': 30502.0}, {'tour': array([17,  6, 21, 11,  0, 14, 18, 12,  5, 16, 13,  1,  8,  2, 10,  4,  7,
       19,  9, 20, 15,  3]), 'cur_cost': 42289.0}, {'tour': array([ 1, 13,  8,  0, 18,  7, 10, 11,  2,  6,  3,  9, 15, 20, 14,  5,  4,
       21, 17, 16, 12, 19]), 'cur_cost': 38670.0}]
2025-06-26 17:35:08,005 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:35:08,005 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 159, 'skip_rate': 0.05660377358490566, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 150, 'cache_hits': 172, 'similarity_calculations': 3105, 'cache_hit_rate': 0.05539452495974235, 'cache_size': 2933}}
2025-06-26 17:35:08,005 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:35:08,007 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 1, 6, 11, 16, 21, 2, 7, 12, 17, 3, 8, 13, 18, 4, 9, 14, 19], 'cur_cost': 42603.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 15, 3, 19, 7, 14, 2, 18, 9, 13, 4, 17, 10, 20, 1, 16, 6, 11, 21], 'cur_cost': 43463.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}}]
2025-06-26 17:35:08,007 - __main__ - INFO - 进化阶段完成
2025-06-26 17:35:08,007 - __main__ - INFO - 开始评估阶段
2025-06-26 17:35:08,007 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:35:08,008 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:35:08,008 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 16252.0 → 16252.0 (Δ 0.0)
- Mean cost: 38497.9 → 33325.1
- Diversity: 0.624 → 0.586 (Δ -0.0381)

#### Elite metrics
- Elite count: 73 → 73
- Best elite cost: 9455 → 9455 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 66.7% , avg Δ 8788.0
- Exploit: 4 inds, success 25.0% , avg Δ -250.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 5
- Historical trends: Last 3 iterations: costs [15933.0, 16252.0, 16252.0], diversity [0.7757575757575756, 0.8151515151515151, 0.8868686868686867]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:35:08,008 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:35:21,275 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": 8788.0,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -250.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy but effective, exploit underperforming",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators to boost exploit success rate"
  }
}
```
2025-06-26 17:35:21,278 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:35:21,278 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": 8788.0,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -250.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy but effective, exploit underperforming",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators to boost exploit success rate"
  }
}
```
2025-06-26 17:35:21,278 - __main__ - INFO - 评估阶段完成
2025-06-26 17:35:21,278 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": 8788.0,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -250.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy but effective, exploit underperforming",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators to boost exploit success rate"
  }
}
```
2025-06-26 17:35:21,281 - __main__ - INFO - 当前最佳适应度: 16252.0
2025-06-26 17:35:21,283 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite3_22_route_3.pkl
2025-06-26 17:35:21,283 - __main__ - INFO - composite3_22 开始进化第 5 代
2025-06-26 17:35:21,283 - __main__ - INFO - 开始分析阶段
2025-06-26 17:35:21,283 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:35:21,287 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 16252.0, 'max': 46001.0, 'mean': 33325.1, 'std': 11829.180465695837}, 'diversity': 0.8636363636363634, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 1, 3, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:35:21,291 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 16252.0, 'max': 46001.0, 'mean': 33325.1, 'std': 11829.180465695837}, 'diversity_level': 0.8636363636363634, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [1, 1, 1, 3, 1, 1, 1, 1]}, 'coordinates': [[360, 2564], [380, 2529], [340, 2529], [360, 2604], [2427, 580], [2455, 552], [2399, 552], [2399, 608], [2455, 608], [2491, 3776], [2491, 3817], [2530, 3789], [2515, 3743], [2467, 3743], [2452, 3789], [1620, 2430], [1660, 2430], [1640, 2465], [1600, 2465], [1580, 2430], [1600, 2395], [1640, 2395]], 'distance_matrix': array([[   0.,   40.,   40.,   40., 2865., 2905., 2865., 2826., 2866.,
        2452., 2472., 2492., 2456., 2414., 2424., 1267., 1307., 1284.,
        1244., 1227., 1251., 1291.],
       [  40.,    0.,   40.,   78., 2826., 2866., 2826., 2787., 2828.,
        2452., 2473., 2492., 2456., 2414., 2425., 1244., 1284., 1262.,
        1222., 1204., 1227., 1267.],
       [  40.,   40.,    0.,   78., 2856., 2895., 2854., 2816., 2857.,
        2486., 2507., 2527., 2491., 2449., 2459., 1284., 1324., 1302.,
        1262., 1244., 1267., 1307.],
       [  40.,   78.,   78.,    0., 2893., 2933., 2893., 2853., 2894.,
        2432., 2452., 2472., 2437., 2395., 2404., 1272., 1312., 1288.,
        1248., 1232., 1257., 1297.],
       [2865., 2826., 2856., 2893.,    0.,   40.,   40.,   40.,   40.,
        3197., 3238., 3211., 3164., 3163., 3209., 2018., 2003., 2043.,
        2058., 2035., 1995., 1978.],
       [2905., 2866., 2895., 2933.,   40.,    0.,   56.,   79.,   56.,
        3224., 3265., 3238., 3192., 3191., 3237., 2055., 2039., 2079.,
        2095., 2072., 2032., 2015.],
       [2865., 2826., 2854., 2893.,   40.,   56.,    0.,   56.,   79.,
        3225., 3266., 3240., 3193., 3192., 3237., 2033., 2018., 2058.,
        2073., 2049., 2009., 1993.],
       [2826., 2787., 2816., 2853.,   40.,   79.,   56.,    0.,   56.,
        3169., 3210., 3184., 3137., 3136., 3181., 1982., 1966., 2006.,
        2022., 1998., 1957., 1942.],
       [2866., 2828., 2857., 2894.,   40.,   56.,   79.,   56.,    0.,
        3168., 3209., 3182., 3136., 3135., 3181., 2004., 1988., 2028.,
        2044., 2021., 1981., 1964.],
       [2452., 2452., 2486., 2432., 3197., 3224., 3225., 3169., 3168.,
           0.,   41.,   41.,   41.,   41.,   41., 1603., 1582., 1563.,
        1585., 1625., 1643., 1622.],
       [2472., 2473., 2507., 2452., 3238., 3265., 3266., 3210., 3209.,
          41.,    0.,   48.,   78.,   78.,   48., 1638., 1617., 1598.,
        1619., 1659., 1678., 1657.],
       [2492., 2492., 2527., 2472., 3211., 3238., 3240., 3184., 3182.,
          41.,   48.,    0.,   48.,   78.,   78., 1636., 1614., 1595.,
        1618., 1658., 1676., 1654.],
       [2456., 2456., 2491., 2437., 3164., 3192., 3193., 3137., 3136.,
          41.,   78.,   48.,    0.,   48.,   78., 1589., 1567., 1549.,
        1572., 1612., 1629., 1607.],
       [2414., 2414., 2449., 2395., 3163., 3191., 3192., 3136., 3135.,
          41.,   78.,   78.,   48.,    0.,   48., 1562., 1541., 1522.,
        1544., 1585., 1603., 1581.],
       [2424., 2425., 2459., 2404., 3209., 3237., 3237., 3181., 3181.,
          41.,   48.,   78.,   78.,   48.,    0., 1593., 1573., 1553.,
        1574., 1615., 1634., 1613.],
       [1267., 1244., 1284., 1272., 2018., 2055., 2033., 1982., 2004.,
        1603., 1638., 1636., 1589., 1562., 1593.,    0.,   40.,   40.,
          40.,   40.,   40.,   40.],
       [1307., 1284., 1324., 1312., 2003., 2039., 2018., 1966., 1988.,
        1582., 1617., 1614., 1567., 1541., 1573.,   40.,    0.,   40.,
          69.,   80.,   69.,   40.],
       [1284., 1262., 1302., 1288., 2043., 2079., 2058., 2006., 2028.,
        1563., 1598., 1595., 1549., 1522., 1553.,   40.,   40.,    0.,
          40.,   69.,   81.,   70.],
       [1244., 1222., 1262., 1248., 2058., 2095., 2073., 2022., 2044.,
        1585., 1619., 1618., 1572., 1544., 1574.,   40.,   69.,   40.,
           0.,   40.,   70.,   81.],
       [1227., 1204., 1244., 1232., 2035., 2072., 2049., 1998., 2021.,
        1625., 1659., 1658., 1612., 1585., 1615.,   40.,   80.,   69.,
          40.,    0.,   40.,   69.],
       [1251., 1227., 1267., 1257., 1995., 2032., 2009., 1957., 1981.,
        1643., 1678., 1676., 1629., 1603., 1634.,   40.,   69.,   81.,
          70.,   40.,    0.,   40.],
       [1291., 1267., 1307., 1297., 1978., 2015., 1993., 1942., 1964.,
        1622., 1657., 1654., 1607., 1581., 1613.,   40.,   40.,   70.,
          81.,   69.,   40.,    0.]])}
2025-06-26 17:35:21,291 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:35:21,292 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:35:21,292 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:35:21,293 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:35:21,293 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:35:21,293 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:35:21,293 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:35:21,293 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:35:21,293 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:35:21,319 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:35:21,319 - __main__ - INFO - 精英专家分析报告: {'elite_count': 73, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 6797.0, 'avg_gap': 23869.894520547943}, 'structure_gap': {'unique_elite_edges': 53, 'unique_pop_edges': 128, 'common_edges': 25}}, 'elite_diversity': {'diversity_score': 0.3567870485678706}}
2025-06-26 17:35:21,319 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:35:21,319 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:35:21,319 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:35:21,319 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 16252.0, mean 33325.1, max 46001.0, std 11829.180465695837
- diversity: 0.8636363636363634
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [16252.0, 16252.0, 16252.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [340.0, 552.0, 2530.0, 3817.0]
- centroid/spread: [1811.864, 2399.682] | rms 1371.908
- density_grid(3x3): [[0, 0, 5], [4, 7, 0], [0, 0, 6]]
- edge_len_stats(min,q1,med,q3,max): [40.0, 1244.0, 1643.0, 2472.0, 3266.0]
- long_edge_ratio: 0.247
- direction_hist(8 sectors): [4, 3, 2, 3, 3, 2, 4, 0]
- nn_median_dist: 40.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:35:21,319 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:35:46,716 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "potentially multimodal",
    "deceptiveness": "moderate",
    "edge_length_variability": "high",
    "long_edge_ratio": "significant (24.7%)"
  },
  "population_state": {
    "coverage": "uneven",
    "convergence": "none detected",
    "diversity": "high (0.864)",
    "spatial_distribution": "clustered in specific regions (see density_grid)",
    "stagnation": "recent stagnation detected in best costs"
  },
  "difficult_regions": {
    "potential_areas": [
      "low-density cells in grid (especially top-left and bottom-middle)",
      "regions with long edges (q3-max edge lengths: 2472-3266)"
    ],
    "structural_evidence": "lack of high-quality edges or common subpaths in samples"
  },
  "opportunity_regions": {
    "potential_areas": [
      "high-density cells (bottom-left and top-right in density_grid)",
      "regions with median edge lengths (1244-1643)",
      "direction sectors with higher edge counts (sectors 0, 6)"
    ],
    "structural_evidence": "none identified in current samples"
  },
  "evolution_phase": "mid-phase (iteration 4/5)",
  "evolution_direction": {
    "suggested_operators": [
      "targeted local search in high-density opportunity regions",
      "edge recombination focusing on median-length edges",
      "diversity maintenance in low-density regions",
      "long-edge elimination or optimization"
    ],
    "adaptive_suggestions": [
      "increase exploitation in high-density clusters",
      "maintain exploration in sparse regions",
      "consider stagnation-breaking operators"
    ]
  }
}
```
2025-06-26 17:35:46,716 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:35:46,716 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'moderate', 'edge_length_variability': 'high', 'long_edge_ratio': 'significant (24.7%)'}, 'population_state': {'coverage': 'uneven', 'convergence': 'none detected', 'diversity': 'high (0.864)', 'spatial_distribution': 'clustered in specific regions (see density_grid)', 'stagnation': 'recent stagnation detected in best costs'}, 'difficult_regions': {'potential_areas': ['low-density cells in grid (especially top-left and bottom-middle)', 'regions with long edges (q3-max edge lengths: 2472-3266)'], 'structural_evidence': 'lack of high-quality edges or common subpaths in samples'}, 'opportunity_regions': {'potential_areas': ['high-density cells (bottom-left and top-right in density_grid)', 'regions with median edge lengths (1244-1643)', 'direction sectors with higher edge counts (sectors 0, 6)'], 'structural_evidence': 'none identified in current samples'}, 'evolution_phase': 'mid-phase (iteration 4/5)', 'evolution_direction': {'suggested_operators': ['targeted local search in high-density opportunity regions', 'edge recombination focusing on median-length edges', 'diversity maintenance in low-density regions', 'long-edge elimination or optimization'], 'adaptive_suggestions': ['increase exploitation in high-density clusters', 'maintain exploration in sparse regions', 'consider stagnation-breaking operators']}}
2025-06-26 17:35:46,716 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:35:46,716 - __main__ - INFO - 分析阶段完成
2025-06-26 17:35:46,724 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'moderate', 'edge_length_variability': 'high', 'long_edge_ratio': 'significant (24.7%)'}, 'population_state': {'coverage': 'uneven', 'convergence': 'none detected', 'diversity': 'high (0.864)', 'spatial_distribution': 'clustered in specific regions (see density_grid)', 'stagnation': 'recent stagnation detected in best costs'}, 'difficult_regions': {'potential_areas': ['low-density cells in grid (especially top-left and bottom-middle)', 'regions with long edges (q3-max edge lengths: 2472-3266)'], 'structural_evidence': 'lack of high-quality edges or common subpaths in samples'}, 'opportunity_regions': {'potential_areas': ['high-density cells (bottom-left and top-right in density_grid)', 'regions with median edge lengths (1244-1643)', 'direction sectors with higher edge counts (sectors 0, 6)'], 'structural_evidence': 'none identified in current samples'}, 'evolution_phase': 'mid-phase (iteration 4/5)', 'evolution_direction': {'suggested_operators': ['targeted local search in high-density opportunity regions', 'edge recombination focusing on median-length edges', 'diversity maintenance in low-density regions', 'long-edge elimination or optimization'], 'adaptive_suggestions': ['increase exploitation in high-density clusters', 'maintain exploration in sparse regions', 'consider stagnation-breaking operators']}}
2025-06-26 17:35:46,724 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:35:46,724 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:35:46,724 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 16252.0
  • mean_cost: 33325.1
- Search-space: ruggedness moderate, modality potentially multimodal, deceptiveness moderate
- Evolution phase: mid-phase (iteration 4/5)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): potential_areas, structural_evidence
- Opportunity regions (# 2): potential_areas, structural_evidence
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:35:46,724 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:35:46,724 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:35:57,160 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest exploration. Top 50% explore to diversify, bottom 50% exploit to refine."
}
```
2025-06-26 17:35:57,160 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:35:57,164 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:35:57,164 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest exploration. Top 50% explore to diversify, bottom 50% exploit to refine."
}
```
2025-06-26 17:35:57,174 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:35:57,174 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:35:57,174 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest exploration. Top 50% explore to diversify, bottom 50% exploit to refine."
}
```
2025-06-26 17:35:57,175 - __main__ - INFO - 开始进化阶段
2025-06-26 17:35:57,175 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:35:57,175 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:35:57,175 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 42603.0 (rank 70.0%)
- Population diversity: 0.5857142857142857
- Evolution phase: mid-phase (iteration 4/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_areas, structural_evid
- Difficult regions to avoid (sample): potential_areas, structural_evid
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:35:57,175 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:35:57,175 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:36:05,860 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21],
  "targeted_regions": "potential_areas, adjacent sparse cells",
  "strategy_comment": "Balanced exploration of opportunity regions with sparse adjacency"
}
```
2025-06-26 17:36:05,860 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:36:05,860 - ExplorationExpert - INFO - 探索路径生成完成，成本: 46001.0, 路径: [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21]
2025-06-26 17:36:05,860 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}
2025-06-26 17:36:05,860 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:36:05,860 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:36:05,860 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 43463.0 (rank 70.0%)
- Population diversity: 0.5285714285714286
- Evolution phase: mid-phase (iteration 4/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_areas, structural_evid
- Difficult regions to avoid (sample): potential_areas, structural_evid
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:36:05,860 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:36:05,860 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:36:15,316 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21],
  "targeted_regions": "potential_areas, adjacent sparse cells",
  "strategy_comment": "Balanced exploration of opportunity regions with sparse adjacency"
}
```
2025-06-26 17:36:15,316 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:36:15,319 - ExplorationExpert - INFO - 探索路径生成完成，成本: 46001.0, 路径: [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21]
2025-06-26 17:36:15,319 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}
2025-06-26 17:36:15,319 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:36:15,319 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:36:15,319 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 46001.0 (rank 70.0%)
- Population diversity: 0.4666666666666667
- Evolution phase: mid-phase (iteration 4/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_areas, structural_evid
- Difficult regions to avoid (sample): potential_areas, structural_evid
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:36:15,320 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:36:15,320 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:36:24,338 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "potential_areas, adjacent sparse cells",
  "strategy_comment": "Alternating high-low node jumps to explore sparse regions while balancing cost"
}
```
2025-06-26 17:36:24,338 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:36:24,338 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16252.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 17:36:24,346 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}
2025-06-26 17:36:24,346 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:36:24,346 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:36:24,346 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 16252.0 (rank 0.0%)
- Population diversity: 0.4666666666666667
- Evolution phase: mid-phase (iteration 4/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_areas, structural_evid
- Difficult regions to avoid (sample): potential_areas, structural_evid
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:36:24,346 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:36:24,346 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:36:33,979 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 15, 3, 19, 11, 7, 14, 2, 17, 9, 20, 6, 13, 1, 18, 10, 16, 4, 21],
  "targeted_regions": "potential_areas, adjacent sparse cells",
  "strategy_comment": "Balanced exploration of opportunity regions with sparse adjacency"
}
```
2025-06-26 17:36:33,979 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:36:33,979 - ExplorationExpert - INFO - 探索路径生成完成，成本: 46076.0, 路径: [0, 5, 12, 8, 15, 3, 19, 11, 7, 14, 2, 17, 9, 20, 6, 13, 1, 18, 10, 16, 4, 21]
2025-06-26 17:36:33,979 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 15, 3, 19, 11, 7, 14, 2, 17, 9, 20, 6, 13, 1, 18, 10, 16, 4, 21], 'cur_cost': 46076.0}
2025-06-26 17:36:33,979 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:36:33,979 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:36:33,979 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 16252.0 (rank 0.0%)
- Population diversity: 0.5285714285714286
- Evolution phase: mid-phase (iteration 4/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_areas, structural_evid
- Difficult regions to avoid (sample): potential_areas, structural_evid
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 21]  
• Path length == 22 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:36:33,979 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:36:33,979 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:36:43,626 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "potential_areas, sparse_cells",
  "strategy_comment": "Alternating high-low node traversal to explore sparse regions"
}
```
2025-06-26 17:36:43,626 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:36:43,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16252.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 17:36:43,626 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}
2025-06-26 17:36:43,626 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-26 17:36:43,626 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:36:43,626 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:36:43,626 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 38392.0
2025-06-26 17:36:44,126 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:36:44,126 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:36:44,128 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:36:44,144 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:36:44,145 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 11, 7, 14, 2, 17, 9, 20, 6, 13, 1, 18, 10, 16, 4, 21], 'cur_cost': 46076.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': array([10, 16,  9,  4, 17,  8, 18,  7,  0, 12,  2,  3, 19,  5, 15, 21,  6,
       13, 14,  1, 20, 11]), 'cur_cost': 38392.0}, {'tour': array([ 5, 20,  3, 18, 13,  6, 17,  4,  9, 19, 12, 15, 11,  7,  0, 21, 16,
       14, 10,  8,  1,  2]), 'cur_cost': 40967.0}, {'tour': array([20, 10, 12, 16,  2,  5,  4, 18, 13, 14, 17,  0, 15, 11,  9,  3,  6,
       21,  1,  7,  8, 19]), 'cur_cost': 30502.0}, {'tour': array([17,  6, 21, 11,  0, 14, 18, 12,  5, 16, 13,  1,  8,  2, 10,  4,  7,
       19,  9, 20, 15,  3]), 'cur_cost': 42289.0}, {'tour': array([ 1, 13,  8,  0, 18,  7, 10, 11,  2,  6,  3,  9, 15, 20, 14,  5,  4,
       21, 17, 16, 12, 19]), 'cur_cost': 38670.0}]
2025-06-26 17:36:44,146 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:36:44,146 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 160, 'skip_rate': 0.05625, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 151, 'cache_hits': 172, 'similarity_calculations': 3119, 'cache_hit_rate': 0.055145880089772364, 'cache_size': 2947}}
2025-06-26 17:36:44,146 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-26 17:36:44,147 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 17:36:44,147 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:36:44,147 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:36:44,148 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 45804.0
2025-06-26 17:36:44,651 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:36:44,651 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:36:44,651 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:36:44,668 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:36:44,668 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 11, 7, 14, 2, 17, 9, 20, 6, 13, 1, 18, 10, 16, 4, 21], 'cur_cost': 46076.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': array([10, 16,  9,  4, 17,  8, 18,  7,  0, 12,  2,  3, 19,  5, 15, 21,  6,
       13, 14,  1, 20, 11]), 'cur_cost': 38392.0}, {'tour': array([19,  4, 14, 18,  7, 13, 17,  3, 15,  0, 11, 21,  9,  8,  1, 10, 16,
        5, 12,  2,  6, 20]), 'cur_cost': 45804.0}, {'tour': array([20, 10, 12, 16,  2,  5,  4, 18, 13, 14, 17,  0, 15, 11,  9,  3,  6,
       21,  1,  7,  8, 19]), 'cur_cost': 30502.0}, {'tour': array([17,  6, 21, 11,  0, 14, 18, 12,  5, 16, 13,  1,  8,  2, 10,  4,  7,
       19,  9, 20, 15,  3]), 'cur_cost': 42289.0}, {'tour': array([ 1, 13,  8,  0, 18,  7, 10, 11,  2,  6,  3,  9, 15, 20, 14,  5,  4,
       21, 17, 16, 12, 19]), 'cur_cost': 38670.0}]
2025-06-26 17:36:44,670 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:36:44,670 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 161, 'skip_rate': 0.055900621118012424, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 152, 'cache_hits': 172, 'similarity_calculations': 3134, 'cache_hit_rate': 0.05488194001276324, 'cache_size': 2962}}
2025-06-26 17:36:44,671 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 17:36:44,671 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:36:44,671 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:36:44,672 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:36:44,672 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 27027.0
2025-06-26 17:36:45,175 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:36:45,176 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:36:45,176 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:36:45,192 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:36:45,192 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 11, 7, 14, 2, 17, 9, 20, 6, 13, 1, 18, 10, 16, 4, 21], 'cur_cost': 46076.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': array([10, 16,  9,  4, 17,  8, 18,  7,  0, 12,  2,  3, 19,  5, 15, 21,  6,
       13, 14,  1, 20, 11]), 'cur_cost': 38392.0}, {'tour': array([19,  4, 14, 18,  7, 13, 17,  3, 15,  0, 11, 21,  9,  8,  1, 10, 16,
        5, 12,  2,  6, 20]), 'cur_cost': 45804.0}, {'tour': array([ 8,  6,  0,  1, 14, 19, 21, 17, 10, 12,  2,  7,  5,  3, 20, 11, 15,
        9, 13, 18, 16,  4]), 'cur_cost': 27027.0}, {'tour': array([17,  6, 21, 11,  0, 14, 18, 12,  5, 16, 13,  1,  8,  2, 10,  4,  7,
       19,  9, 20, 15,  3]), 'cur_cost': 42289.0}, {'tour': array([ 1, 13,  8,  0, 18,  7, 10, 11,  2,  6,  3,  9, 15, 20, 14,  5,  4,
       21, 17, 16, 12, 19]), 'cur_cost': 38670.0}]
2025-06-26 17:36:45,194 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:36:45,194 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 162, 'skip_rate': 0.05555555555555555, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 153, 'cache_hits': 172, 'similarity_calculations': 3150, 'cache_hit_rate': 0.054603174603174605, 'cache_size': 2978}}
2025-06-26 17:36:45,194 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:36:45,194 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:36:45,196 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:36:45,196 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:36:45,196 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 25309.0
2025-06-26 17:36:45,698 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:36:45,699 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:36:45,699 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:36:45,715 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:36:45,716 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 11, 7, 14, 2, 17, 9, 20, 6, 13, 1, 18, 10, 16, 4, 21], 'cur_cost': 46076.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': array([10, 16,  9,  4, 17,  8, 18,  7,  0, 12,  2,  3, 19,  5, 15, 21,  6,
       13, 14,  1, 20, 11]), 'cur_cost': 38392.0}, {'tour': array([19,  4, 14, 18,  7, 13, 17,  3, 15,  0, 11, 21,  9,  8,  1, 10, 16,
        5, 12,  2,  6, 20]), 'cur_cost': 45804.0}, {'tour': array([ 8,  6,  0,  1, 14, 19, 21, 17, 10, 12,  2,  7,  5,  3, 20, 11, 15,
        9, 13, 18, 16,  4]), 'cur_cost': 27027.0}, {'tour': array([20, 21,  0,  2, 12, 13,  7, 14,  9, 19, 16,  8,  5, 11, 10, 17, 18,
        6,  4,  3,  1, 15]), 'cur_cost': 25309.0}, {'tour': array([ 1, 13,  8,  0, 18,  7, 10, 11,  2,  6,  3,  9, 15, 20, 14,  5,  4,
       21, 17, 16, 12, 19]), 'cur_cost': 38670.0}]
2025-06-26 17:36:45,718 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:36:45,718 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 163, 'skip_rate': 0.05521472392638037, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 154, 'cache_hits': 172, 'similarity_calculations': 3167, 'cache_hit_rate': 0.05431007262393432, 'cache_size': 2995}}
2025-06-26 17:36:45,718 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:36:45,718 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:36:45,719 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:36:45,719 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:36:45,719 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 32271.0
2025-06-26 17:36:46,220 - ExploitationExpert - INFO - res_population_num: 73
2025-06-26 17:36:46,220 - ExploitationExpert - INFO - res_population_costs: [9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9455, 9470]
2025-06-26 17:36:46,220 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10,  9, 11, 12,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14, 10, 11,  9, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13,  9, 14, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  4,  5,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  5,  6,  4,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11, 10,  9, 14,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 18, 19, 20, 15, 16, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 18, 19, 20, 15, 16, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12,  9, 11, 10, 14, 13, 17, 18, 15,
       16, 21, 20, 19,  3], dtype=int64), array([ 0,  3, 14, 10,  9, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 20, 19, 18, 15, 16, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
       11,  9, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14,  9, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  6,  5,  4,  8, 12,  9,
       11, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1, 19, 20, 15, 18, 17, 16, 21,  7,  4,  6,  5,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10,  9, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 19, 18, 15, 20, 21, 16, 17, 13, 14,  9, 10, 11, 12,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  6,  5,  4,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  4,  6,  5,  8, 12, 11,
       10, 14,  9, 13,  3], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  5,  6,  4,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11, 12,  9, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        4,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 19, 20, 21, 16, 15, 18, 17, 13, 14, 10,  9, 11, 12,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 12, 11,  9, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
       10,  9, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12, 11, 10, 14,  9, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  4,  5,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14, 10, 11,  9, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  4,
        5,  6,  7,  1,  2], dtype=int64), array([ 0,  3, 13, 14, 10, 11,  9, 12,  8,  4,  5,  6,  7, 21, 20, 15, 16,
       17, 18, 19,  1,  2], dtype=int64), array([ 0,  3, 13,  9, 14, 10, 11, 12,  8,  5,  4,  6,  7, 21, 16, 17, 18,
       15, 20, 19,  1,  2], dtype=int64), array([ 0,  3, 14,  9, 10, 11, 12, 13, 17, 16, 15, 18, 19, 20, 21,  8,  5,
        6,  4,  7,  1,  2], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 20, 19, 18, 15, 16, 17, 13,  9,
       12, 11, 10, 14,  3], dtype=int64), array([ 0,  2,  1,  7,  6,  4,  5,  8, 12,  9, 11, 10, 14, 13, 17, 16, 21,
       20, 15, 18, 19,  3], dtype=int64), array([ 0,  2,  1, 19, 18, 17, 16, 15, 20, 21,  7,  6,  5,  4,  8, 12, 11,
        9, 10, 14, 13,  3], dtype=int64), array([ 0,  2,  1,  7,  4,  6,  5,  8, 21, 16, 15, 20, 19, 18, 17, 13, 12,
        9, 11, 10, 14,  3], dtype=int64), array([ 0,  3, 13, 14, 10,  9, 11, 12,  8,  5,  6,  4,  7, 20, 15, 21, 16,
       17, 18, 19,  1,  2], dtype=int64)]
2025-06-26 17:36:46,238 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:36:46,245 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': [0, 5, 12, 8, 15, 3, 19, 11, 7, 14, 2, 17, 9, 20, 6, 13, 1, 18, 10, 16, 4, 21], 'cur_cost': 46076.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}, {'tour': array([10, 16,  9,  4, 17,  8, 18,  7,  0, 12,  2,  3, 19,  5, 15, 21,  6,
       13, 14,  1, 20, 11]), 'cur_cost': 38392.0}, {'tour': array([19,  4, 14, 18,  7, 13, 17,  3, 15,  0, 11, 21,  9,  8,  1, 10, 16,
        5, 12,  2,  6, 20]), 'cur_cost': 45804.0}, {'tour': array([ 8,  6,  0,  1, 14, 19, 21, 17, 10, 12,  2,  7,  5,  3, 20, 11, 15,
        9, 13, 18, 16,  4]), 'cur_cost': 27027.0}, {'tour': array([20, 21,  0,  2, 12, 13,  7, 14,  9, 19, 16,  8,  5, 11, 10, 17, 18,
        6,  4,  3,  1, 15]), 'cur_cost': 25309.0}, {'tour': array([ 6,  5,  1,  9,  0,  7,  4,  8, 17,  3, 16, 19, 15, 14, 12, 21, 13,
       20, 11, 18,  2, 10]), 'cur_cost': 32271.0}]
2025-06-26 17:36:46,247 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 17:36:46,247 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 164, 'skip_rate': 0.054878048780487805, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 155, 'cache_hits': 172, 'similarity_calculations': 3185, 'cache_hit_rate': 0.05400313971742543, 'cache_size': 3013}}
2025-06-26 17:36:46,248 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:36:46,248 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 9, 3, 15, 7, 20, 11, 2, 14, 6, 19, 10, 4, 17, 8, 1, 13, 16, 21], 'cur_cost': 46001.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 15, 3, 19, 11, 7, 14, 2, 17, 9, 20, 6, 13, 1, 18, 10, 16, 4, 21], 'cur_cost': 46076.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16252.0}}]
2025-06-26 17:36:46,248 - __main__ - INFO - 进化阶段完成
2025-06-26 17:36:46,249 - __main__ - INFO - 开始评估阶段
2025-06-26 17:36:46,249 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:36:46,249 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:36:46,250 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 16252.0 → 16252.0 (Δ 0.0)
- Mean cost: 33325.1 → 33938.5
- Diversity: 0.586 → 0.610 (Δ 0.0238)

#### Elite metrics
- Elite count: 73 → 73
- Best elite cost: 9455 → 9455 (Δ 0)

#### Strategy performance
- Explore: 5 inds, success 20.0% , avg Δ -1202.2
- Exploit: 5 inds, success 60.0% , avg Δ -24.6

#### Other indicators
- No-change individuals: 1
- Regression individuals: 5
- Historical trends: Last 3 iterations: costs [16252.0, 16252.0, 16252.0], diversity [0.8151515151515151, 0.8868686868686867, 0.8636363636363634]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:36:46,250 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:36:59,933 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 20.0,
      "average_delta": -1202.2,
      "contribution": "moderate"
    },
    "exploit": {
      "success_rate": 60.0,
      "average_delta": -24.6,
      "contribution": "low"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "adequate",
    "diversity_trend": "increasing",
    "stagnation": "yes"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators or increasing mutation rates to break stagnation."
  }
}
```
2025-06-26 17:36:59,934 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:36:59,934 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 20.0,
      "average_delta": -1202.2,
      "contribution": "moderate"
    },
    "exploit": {
      "success_rate": 60.0,
      "average_delta": -24.6,
      "contribution": "low"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "adequate",
    "diversity_trend": "increasing",
    "stagnation": "yes"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators or increasing mutation rates to break stagnation."
  }
}
```
2025-06-26 17:36:59,934 - __main__ - INFO - 评估阶段完成
2025-06-26 17:36:59,934 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 20.0,
      "average_delta": -1202.2,
      "contribution": "moderate"
    },
    "exploit": {
      "success_rate": 60.0,
      "average_delta": -24.6,
      "contribution": "low"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "adequate",
    "diversity_trend": "increasing",
    "stagnation": "yes"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators or increasing mutation rates to break stagnation."
  }
}
```
2025-06-26 17:36:59,934 - __main__ - INFO - 当前最佳适应度: 16252.0
2025-06-26 17:36:59,940 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite3_22_route_4.pkl
2025-06-26 17:36:59,951 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite3_22_solution.json
2025-06-26 17:36:59,951 - __main__ - INFO - 实例 composite3_22 处理完成
