import time
import logging

class TimeTracker:
    """
    时间跟踪器，用于记录各个组件的执行时间
    """
    def __init__(self):
        self.component_timers = {}
        self.component_total_times = {}
        self.component_call_counts = {}
        self.logger = logging.getLogger(__name__)
    
    def start_component_timer(self, component_name):
        """
        开始计时特定组件
        
        参数:
            component_name: 组件名称
        """
        if component_name not in self.component_timers:
            self.component_total_times[component_name] = 0.0
            self.component_call_counts[component_name] = 0
        
        self.component_timers[component_name] = time.time()
        self.component_call_counts[component_name] += 1
    
    def end_component_timer(self, component_name):
        """
        结束计时特定组件并累计时间
        
        参数:
            component_name: 组件名称
        """
        if component_name in self.component_timers:
            elapsed_time = time.time() - self.component_timers[component_name]
            self.component_total_times[component_name] += elapsed_time
            del self.component_timers[component_name]
    
    def get_component_time(self, component_name):
        """
        获取特定组件的累计时间
        
        参数:
            component_name: 组件名称
            
        返回:
            float: 累计时间（秒）
        """
        return self.component_total_times.get(component_name, 0.0)
    
    def get_component_call_count(self, component_name):
        """
        获取特定组件的调用次数
        
        参数:
            component_name: 组件名称
            
        返回:
            int: 调用次数
        """
        return self.component_call_counts.get(component_name, 0)
    
    def get_all_component_times(self):
        """
        获取所有组件的累计时间
        
        返回:
            dict: 组件名称到累计时间的映射
        """
        return self.component_total_times.copy()
    
    def reset(self):
        """
        重置所有计时器
        """
        self.component_timers = {}
        self.component_total_times = {}
        self.component_call_counts = {}
    
    def print_summary(self):
        """
        打印时间统计摘要
        """
        if not self.component_total_times:
            print("没有记录任何组件时间")
            return
        
        print("\n时间统计摘要:")
        print("-" * 50)
        print(f"{'组件名称':<30} {'总时间(秒)':<15} {'调用次数':<10} {'平均时间(秒)':<15}")
        print("-" * 50)
        
        # 按总时间降序排序
        sorted_components = sorted(self.component_total_times.items(), key=lambda x: x[1], reverse=True)
        
        total_time = sum(self.component_total_times.values())
        
        for component, time_spent in sorted_components:
            call_count = self.component_call_counts.get(component, 0)
            avg_time = time_spent / call_count if call_count > 0 else 0
            percentage = (time_spent / total_time * 100) if total_time > 0 else 0
            
            print(f"{component:<30} {time_spent:<15.4f} {call_count:<10} {avg_time:<15.4f} ({percentage:.1f}%)")
        
        print("-" * 50)
        print(f"{'总计':<30} {total_time:<15.4f}")
        print("-" * 50)
        
        # 同时记录到日志
        self.logger.info("时间统计摘要:")
        for component, time_spent in sorted_components:
            call_count = self.component_call_counts.get(component, 0)
            avg_time = time_spent / call_count if call_count > 0 else 0
            percentage = (time_spent / total_time * 100) if total_time > 0 else 0
            
            self.logger.info(f"{component}: {time_spent:.4f}秒, {call_count}次调用, 平均{avg_time:.4f}秒 ({percentage:.1f}%)")

# 创建全局时间跟踪器实例
time_tracker = TimeTracker()