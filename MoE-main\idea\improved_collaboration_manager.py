"""
改进的专家协作管理器
实现专家协作机制优化、知识库集成和性能监控与自适应
"""

import logging
import time
import numpy as np
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque
import json


class ImprovedExpertCollaborationManager:
    """改进的专家协作管理器，提供智能协作和自适应优化"""
    
    def __init__(self, interface_llm, config=None):
        self.config = config or {}
        self.interface_llm = interface_llm
        self.experts = {}
        self.logger = logging.getLogger(__name__)
        
        # 专家协作网络
        self.expert_network = self._build_expert_network()
        
        # 协作历史记录
        self.collaboration_history = deque(maxlen=100)
        
        # 性能监控器
        self.performance_monitor = ExpertPerformanceMonitor()
        
        # 自适应控制器
        self.adaptation_controller = AdaptationController()
        
        # 知识库集成
        self.knowledge_base = None
        self._initialize_knowledge_base()
        
        # 专家间数据共享存储
        self.shared_data = {
            "high_quality_edges": [],
            "difficult_regions": [],
            "opportunity_regions": [],
            "elite_features": {},
            "population_diversity": 0.0,
            "convergence_level": 0.0,
            "expert_recommendations": {},
            "collaboration_context": {}
        }
        
        # 初始化专家
        self._initialize_experts()
        
    def _build_expert_network(self):
        """构建专家协作网络"""
        network = {
            'landscape': {
                'inputs': ['stats', 'path', 'elite'], 
                'outputs': ['strategy', 'exploration', 'exploitation'],
                'dependencies': [],
                'priority': 1
            },
            'strategy': {
                'inputs': ['landscape'], 
                'outputs': ['exploration', 'exploitation'],
                'dependencies': ['landscape'],
                'priority': 2
            },
            'exploration': {
                'inputs': ['strategy', 'landscape', 'path'], 
                'outputs': ['assessment'],
                'dependencies': ['strategy'],
                'priority': 3
            },
            'exploitation': {
                'inputs': ['strategy', 'elite', 'path'], 
                'outputs': ['assessment'],
                'dependencies': ['strategy'],
                'priority': 3
            },
            'assessment': {
                'inputs': ['exploration', 'exploitation', 'stats'], 
                'outputs': ['landscape'],
                'dependencies': ['exploration', 'exploitation'],
                'priority': 4
            }
        }
        return network
    
    def _initialize_knowledge_base(self):
        """初始化知识库"""
        try:
            from knowledge_base import ProblemSpecificKnowledgeBase
            self.knowledge_base = ProblemSpecificKnowledgeBase()
        except ImportError:
            self.logger.warning("知识库模块不可用")
            self.knowledge_base = None
    
    def _initialize_experts(self):
        """初始化所有专家模块"""
        # 算法实现的专家（不与LLM交互）
        from moe_main import StatsExpert, PathExpert, EliteExpert
        self.experts['stats'] = StatsExpert()
        self.experts['path'] = PathExpert()
        self.experts['elite'] = EliteExpert()
        
        # LLM交互的专家
        from moe_main import LandscapeExpert, StrategyExpert, ExplorationExpert, EvolutionAssessmentExpert
        self.experts['landscape'] = LandscapeExpert(self.interface_llm)
        self.experts['strategy'] = StrategyExpert(self.interface_llm)
        self.experts['exploration'] = ExplorationExpert(self.interface_llm)
        self.experts['assessment'] = EvolutionAssessmentExpert(self.interface_llm)
        
        # 开发专家需要特殊处理
        from moe_main import ExploitationExpert
        self.experts['exploitation'] = ExploitationExpert(self.interface_llm)
        
        self.logger.info("专家模块初始化完成")
    
    def collaborative_decision_making(self, current_state, iteration, total_iterations):
        """协作决策机制"""
        self.logger.info(f"开始协作决策 - 迭代 {iteration}/{total_iterations}")
        
        # 收集所有专家的建议
        expert_recommendations = {}
        expert_performance = {}
        
        # 按优先级顺序执行专家分析
        execution_order = sorted(self.expert_network.keys(), 
                                key=lambda x: self.expert_network[x]['priority'])
        
        for expert_name in execution_order:
            try:
                start_time = time.time()
                
                # 获取专家建议
                recommendation = self._get_expert_recommendation(
                    expert_name, current_state, iteration, total_iterations
                )
                
                execution_time = time.time() - start_time
                
                expert_recommendations[expert_name] = recommendation
                expert_performance[expert_name] = {
                    'execution_time': execution_time,
                    'success': recommendation is not None,
                    'iteration': iteration
                }
                
                # 更新共享数据
                self._update_shared_data(expert_name, recommendation)
                
            except Exception as e:
                self.logger.error(f"专家 {expert_name} 执行失败: {e}")
                expert_performance[expert_name] = {
                    'execution_time': 0,
                    'success': False,
                    'error': str(e),
                    'iteration': iteration
                }
        
        # 更新性能监控
        self.performance_monitor.update_performance(expert_performance)
        
        # 基于专家可信度加权融合建议
        final_decision = self._weighted_fusion(expert_recommendations)
        
        # 记录协作历史
        self._record_collaboration(iteration, expert_recommendations, final_decision)
        
        # 自适应调整
        self._adaptive_adjustment(expert_performance, iteration)
        
        return final_decision
    
    def _get_expert_recommendation(self, expert_name, current_state, iteration, total_iterations):
        """获取专家建议"""
        expert = self.experts.get(expert_name)
        if not expert:
            return None
        
        # 根据专家类型调用不同的方法
        if expert_name == 'landscape':
            return self._get_landscape_recommendation(expert, current_state, iteration, total_iterations)
        elif expert_name == 'strategy':
            return self._get_strategy_recommendation(expert, current_state, iteration)
        elif expert_name == 'exploration':
            return self._get_exploration_recommendation(expert, current_state)
        elif expert_name == 'exploitation':
            return self._get_exploitation_recommendation(expert, current_state)
        elif expert_name == 'assessment':
            return self._get_assessment_recommendation(expert, current_state, iteration, total_iterations)
        else:
            return None
    
    def _get_landscape_recommendation(self, expert, current_state, iteration, total_iterations):
        """获取景观分析建议"""
        try:
            populations = current_state.get('populations', [])
            if not populations:
                return None
            
            # 生成统计报告
            stats_report = self.experts['stats'].analyze(populations)
            path_report = self.experts['path'].analyze(populations)
            elite_report = self.experts['elite'].analyze(populations)
            
            # 调用景观专家
            landscape_report = expert.analyze(
                stats_report, path_report, elite_report, 
                iteration, total_iterations
            )
            
            return {
                'type': 'landscape_analysis',
                'report': landscape_report,
                'confidence': self._calculate_confidence(expert, 'landscape'),
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"景观分析失败: {e}")
            return None
    
    def _get_strategy_recommendation(self, expert, current_state, iteration):
        """获取策略选择建议"""
        try:
            landscape_report = self.shared_data.get('expert_recommendations', {}).get('landscape', {}).get('report')
            populations = current_state.get('populations', [])
            
            if not landscape_report or not populations:
                return None
            
            strategy_assignment, strategy_response = expert.analyze(
                landscape_report, populations, iteration
            )
            
            return {
                'type': 'strategy_assignment',
                'assignment': strategy_assignment,
                'response': strategy_response,
                'confidence': self._calculate_confidence(expert, 'strategy'),
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"策略选择失败: {e}")
            return None
    
    def _get_exploration_recommendation(self, expert, current_state):
        """获取探索建议"""
        try:
            # 获取探索相关的统计信息
            if hasattr(expert, 'get_generation_stats'):
                stats = expert.get_generation_stats()
                return {
                    'type': 'exploration_stats',
                    'stats': stats,
                    'confidence': stats.get('success_rate', 0.5),
                    'timestamp': time.time()
                }
            return None
            
        except Exception as e:
            self.logger.error(f"探索建议获取失败: {e}")
            return None
    
    def _get_exploitation_recommendation(self, expert, current_state):
        """获取开发建议"""
        try:
            # 获取开发相关的性能统计
            if hasattr(expert, 'adaptive_expert') and expert.adaptive_expert:
                stats = expert.adaptive_expert.get_performance_stats()
                return {
                    'type': 'exploitation_stats',
                    'stats': stats,
                    'confidence': self._calculate_exploitation_confidence(stats),
                    'timestamp': time.time()
                }
            return None
            
        except Exception as e:
            self.logger.error(f"开发建议获取失败: {e}")
            return None
    
    def _get_assessment_recommendation(self, expert, current_state, iteration, total_iterations):
        """获取评估建议"""
        try:
            # 获取评估统计信息
            if hasattr(expert, 'get_evaluation_statistics'):
                stats = expert.get_evaluation_statistics()
                return {
                    'type': 'assessment_stats',
                    'stats': stats,
                    'confidence': stats.get('average_score', 0.5),
                    'timestamp': time.time()
                }
            return None
            
        except Exception as e:
            self.logger.error(f"评估建议获取失败: {e}")
            return None
    
    def _calculate_confidence(self, expert, expert_type):
        """计算专家可信度"""
        performance_history = self.performance_monitor.get_expert_history(expert_type)
        if not performance_history:
            return 0.5
        
        recent_performance = performance_history[-10:]  # 最近10次
        success_rate = sum(1 for p in recent_performance if p.get('success', False)) / len(recent_performance)
        
        return success_rate
    
    def _calculate_exploitation_confidence(self, stats):
        """计算开发专家可信度"""
        if not stats:
            return 0.5
        
        search_stats = stats.get('search_stats', {})
        total_searches = search_stats.get('total_searches', 0)
        successful_improvements = search_stats.get('successful_improvements', 0)
        
        if total_searches == 0:
            return 0.5
        
        return successful_improvements / total_searches
    
    def _update_shared_data(self, expert_name, recommendation):
        """更新共享数据"""
        if recommendation:
            self.shared_data['expert_recommendations'][expert_name] = recommendation
            
            # 根据专家类型更新特定数据
            if expert_name == 'landscape' and 'report' in recommendation:
                report = recommendation['report']
                if isinstance(report, dict):
                    self.shared_data['difficult_regions'] = report.get('difficult_regions', [])
                    self.shared_data['opportunity_regions'] = report.get('opportunity_regions', [])
    
    def _weighted_fusion(self, expert_recommendations):
        """基于专家可信度加权融合建议"""
        fusion_result = {
            'landscape_analysis': None,
            'strategy_assignment': None,
            'exploration_guidance': None,
            'exploitation_guidance': None,
            'assessment_feedback': None,
            'overall_confidence': 0.0
        }
        
        total_confidence = 0.0
        confidence_count = 0
        
        for expert_name, recommendation in expert_recommendations.items():
            if recommendation and 'confidence' in recommendation:
                confidence = recommendation['confidence']
                total_confidence += confidence
                confidence_count += 1
                
                # 根据推荐类型分类存储
                rec_type = recommendation.get('type', '')
                if 'landscape' in rec_type:
                    fusion_result['landscape_analysis'] = recommendation
                elif 'strategy' in rec_type:
                    fusion_result['strategy_assignment'] = recommendation
                elif 'exploration' in rec_type:
                    fusion_result['exploration_guidance'] = recommendation
                elif 'exploitation' in rec_type:
                    fusion_result['exploitation_guidance'] = recommendation
                elif 'assessment' in rec_type:
                    fusion_result['assessment_feedback'] = recommendation
        
        # 计算总体可信度
        if confidence_count > 0:
            fusion_result['overall_confidence'] = total_confidence / confidence_count
        
        return fusion_result
    
    def _record_collaboration(self, iteration, expert_recommendations, final_decision):
        """记录协作历史"""
        collaboration_record = {
            'iteration': iteration,
            'expert_recommendations': expert_recommendations,
            'final_decision': final_decision,
            'timestamp': time.time(),
            'overall_confidence': final_decision.get('overall_confidence', 0)
        }
        
        self.collaboration_history.append(collaboration_record)
    
    def _adaptive_adjustment(self, expert_performance, iteration):
        """自适应调整"""
        # 基于性能调整专家权重和参数
        adjustments = self.adaptation_controller.suggest_adjustments(
            expert_performance, self.collaboration_history, iteration
        )
        
        if adjustments:
            self.logger.info(f"应用自适应调整: {adjustments}")
            self._apply_adjustments(adjustments)
    
    def _apply_adjustments(self, adjustments):
        """应用调整建议"""
        for expert_name, adjustment in adjustments.items():
            expert = self.experts.get(expert_name)
            if expert and hasattr(expert, 'apply_adjustment'):
                try:
                    expert.apply_adjustment(adjustment)
                except Exception as e:
                    self.logger.warning(f"应用调整到专家 {expert_name} 失败: {e}")
    
    def get_collaboration_statistics(self):
        """获取协作统计信息"""
        if not self.collaboration_history:
            return {}
        
        recent_records = list(self.collaboration_history)[-20:]
        
        avg_confidence = np.mean([r['overall_confidence'] for r in recent_records])
        expert_success_rates = {}
        
        for record in recent_records:
            for expert_name, recommendation in record['expert_recommendations'].items():
                if expert_name not in expert_success_rates:
                    expert_success_rates[expert_name] = []
                
                success = recommendation is not None and recommendation.get('confidence', 0) > 0.3
                expert_success_rates[expert_name].append(success)
        
        # 计算成功率
        for expert_name in expert_success_rates:
            successes = expert_success_rates[expert_name]
            expert_success_rates[expert_name] = sum(successes) / len(successes) if successes else 0
        
        return {
            'collaboration_count': len(self.collaboration_history),
            'average_confidence': avg_confidence,
            'expert_success_rates': expert_success_rates,
            'performance_monitor_stats': self.performance_monitor.get_summary(),
            'knowledge_base_available': self.knowledge_base is not None
        }


class ExpertPerformanceMonitor:
    """专家性能监控器"""
    
    def __init__(self, history_size=100):
        self.history_size = history_size
        self.performance_history = defaultdict(lambda: deque(maxlen=history_size))
        
    def update_performance(self, expert_performance):
        """更新专家性能记录"""
        for expert_name, performance in expert_performance.items():
            self.performance_history[expert_name].append(performance)
    
    def get_expert_history(self, expert_name):
        """获取专家历史性能"""
        return list(self.performance_history.get(expert_name, []))
    
    def get_summary(self):
        """获取性能摘要"""
        summary = {}
        for expert_name, history in self.performance_history.items():
            if history:
                recent_history = list(history)[-10:]
                avg_time = np.mean([h.get('execution_time', 0) for h in recent_history])
                success_rate = sum(1 for h in recent_history if h.get('success', False)) / len(recent_history)
                
                summary[expert_name] = {
                    'avg_execution_time': avg_time,
                    'success_rate': success_rate,
                    'total_executions': len(history)
                }
        
        return summary


class AdaptationController:
    """自适应控制器"""
    
    def __init__(self):
        self.adjustment_history = []
        
    def suggest_adjustments(self, expert_performance, collaboration_history, iteration):
        """建议调整方案"""
        adjustments = {}
        
        # 基于性能建议调整
        for expert_name, performance in expert_performance.items():
            if not performance.get('success', True):
                # 专家执行失败，建议降低权重或调整参数
                adjustments[expert_name] = {
                    'type': 'reduce_weight',
                    'reason': 'execution_failure',
                    'iteration': iteration
                }
            elif performance.get('execution_time', 0) > 10:  # 执行时间过长
                adjustments[expert_name] = {
                    'type': 'optimize_performance',
                    'reason': 'slow_execution',
                    'iteration': iteration
                }
        
        # 基于协作历史建议调整
        if len(collaboration_history) >= 5:
            recent_confidence = [r['overall_confidence'] for r in list(collaboration_history)[-5:]]
            if np.mean(recent_confidence) < 0.3:
                # 整体可信度低，建议重新校准
                adjustments['global'] = {
                    'type': 'recalibrate',
                    'reason': 'low_overall_confidence',
                    'iteration': iteration
                }
        
        return adjustments
