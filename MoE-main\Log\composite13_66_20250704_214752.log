2025-07-04 21:47:52,883 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-04 21:47:52,884 - __main__ - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-04 21:47:52,884 - <PERSON>atsExpert - INFO - 开始统计分析
2025-07-04 21:47:52,905 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9961.0, 'max': 116278.0, 'mean': 77574.3, 'std': 44629.40593386831}, 'diversity': 0.9225589225589226, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:47:52,905 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:47:52,920 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:47:52,920 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:47:52,921 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-04 21:47:53,462 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:47:53,465 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:47:53,465 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9961.0, mean 77574.3, max 116278.0, std 44629.40593386831
- diversity: 0.9225589225589226
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: [{'subpath': (19, 27, 37), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [55, 30, 52, 39, 7, 51, 5], 'cost': 16348.0, 'size': 7}, {'region': [57, 46, 28, 58, 39, 6, 43], 'cost': 15543.0, 'size': 7}, {'region': [56, 29, 61, 46, 59], 'cost': 11491.0, 'size': 5}]
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:47:53,465 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:48:03,592 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.75,
    "modality": "multi",
    "deceptiveness": "likely"
  },
  "population_state": {
    "diversity": 0.92,
    "convergence": 0.0,
    "clustering": "no clustering information"
  },
  "difficult_regions": [
    {
      "region": [
        55,
        30,
        52,
        39,
        7,
        51,
        5
      ],
      "reason": "Identified in 'difficult_regions_sample'.",
      "spatial_context": "Likely a dense area of nodes where finding the optimal ordering is challenging."
    },
    {
      "region": [
        57,
        46,
        28,
        58,
        39,
        6,
        43
      ],
      "reason": "Identified in 'difficult_regions_sample'.",
      "spatial_context": "Likely a dense area of nodes where finding the optimal ordering is challenging."
    },
    {
      "region": [
        56,
        29,
        61,
        46,
        59
      ],
      "reason": "Identified in 'difficult_regions_sample'.",
      "spatial_context": "Likely a dense area of nodes where finding the optimal ordering is challenging."
    }
  ],
  "opportunity_regions": [
    {
      "cells": [
        [
          0,
          0
        ],
        [
          2,
          0
        ],
        [
          2,
          2
        ],
        [
          0,
          2
        ],
        [
          1,
          1
        ],
        [
          1,
          2
        ]
      ],
      "reason": "High-density cells in the density grid.",
      "spatial_context": "Cells with higher node density present more opportunity to exploit and improve solutions."
    }
  ],
  "evolution_phase": "early exploration",
  "evolution_direction": {
    "recommended_focus": "explore and refine",
    "operators": [
      "Apply operators that promote exploration and diversification of solutions.",
      "Use operators like 2-opt or 3-opt variations with a focus on improving subpaths identified by 'common_subpaths_sample'.",
      "Explore the identified 'opportunity_regions' more aggressively, focusing initial exploration of solutions to these regions.",
      "Implement local search with targeted moves focused on regions in 'difficult_regions_sample'.",
      "Consider a population that favors exploration of 'opportunity_regions' while continuing to improve diversity."
    ]
  }
}
```
2025-07-04 21:48:03,593 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:48:03,594 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.75, 'modality': 'multi', 'deceptiveness': 'likely'}, 'population_state': {'diversity': 0.92, 'convergence': 0.0, 'clustering': 'no clustering information'}, 'difficult_regions': [{'region': [55, 30, 52, 39, 7, 51, 5], 'reason': "Identified in 'difficult_regions_sample'.", 'spatial_context': 'Likely a dense area of nodes where finding the optimal ordering is challenging.'}, {'region': [57, 46, 28, 58, 39, 6, 43], 'reason': "Identified in 'difficult_regions_sample'.", 'spatial_context': 'Likely a dense area of nodes where finding the optimal ordering is challenging.'}, {'region': [56, 29, 61, 46, 59], 'reason': "Identified in 'difficult_regions_sample'.", 'spatial_context': 'Likely a dense area of nodes where finding the optimal ordering is challenging.'}], 'opportunity_regions': [{'cells': [[0, 0], [2, 0], [2, 2], [0, 2], [1, 1], [1, 2]], 'reason': 'High-density cells in the density grid.', 'spatial_context': 'Cells with higher node density present more opportunity to exploit and improve solutions.'}], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'explore and refine', 'operators': ['Apply operators that promote exploration and diversification of solutions.', "Use operators like 2-opt or 3-opt variations with a focus on improving subpaths identified by 'common_subpaths_sample'.", "Explore the identified 'opportunity_regions' more aggressively, focusing initial exploration of solutions to these regions.", "Implement local search with targeted moves focused on regions in 'difficult_regions_sample'.", "Consider a population that favors exploration of 'opportunity_regions' while continuing to improve diversity."]}}
2025-07-04 21:48:03,594 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:48:03,594 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:48:03,595 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.92
  • best_cost: 9961.0
  • mean_cost: 77574.3
- Search-space: ruggedness 0.75, modality multi, deceptiveness likely
- Evolution phase: early exploration
- Landscape focus suggestion: explore and refine
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 3): {'region': [55, 30, , {'region': [57, 46, ...
- Opportunity regions (# 1): {'cells': [[0, 0], [
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:48:03,595 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:48:03,595 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:48:07,671 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High ruggedness, multi-modality, and likely deceptiveness indicate exploration is needed. The population's diversity is high so we explore, leveraging it to discover new search spaces."
}
```
2025-07-04 21:48:07,672 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 21:48:07,672 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 21:48:07,673 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High ruggedness, multi-modality, and likely deceptiveness indicate exploration is needed. The population's diversity is high so we explore, leveraging it to discover new search spaces."
}
```
2025-07-04 21:48:07,673 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:48:07,673 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 21:48:07,673 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High ruggedness, multi-modality, and likely deceptiveness indicate exploration is needed. The population's diversity is high so we explore, leveraging it to discover new search spaces."
}
```
2025-07-04 21:48:07,674 - __main__ - INFO - 开始进化阶段
2025-07-04 21:48:07,674 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:48:07,674 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:48:07,675 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9971.0 (rank 10.0%)
- Population diversity: 0.7553846153846154
- Evolution phase: early exploration
- Landscape recommends focus: explore and refine
- Opportunity regions (sample): {'cells': [[0, 
- Difficult regions to avoid (sample): {'region': [55,, {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:48:07,675 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:48:07,675 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:48:12,142 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Opportunity regions and sparse cells explored sequentially.",
  "strategy_comment": "Prioritizes opportunity regions, avoids difficult areas, aims for diversity via exploration."
}
```
2025-07-04 21:48:12,143 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:48:12,143 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:48:12,144 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:48:12,144 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:48:12,144 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:48:12,146 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9961.0 (rank 0.0%)
- Population diversity: 0.8215384615384616
- Evolution phase: early exploration
- Landscape recommends focus: explore and refine
- Opportunity regions (sample): {'cells': [[0, 
- Difficult regions to avoid (sample): {'region': [55,, {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:48:12,146 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:48:12,147 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:48:16,490 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Opportunity regions and avoids difficult regions.",
  "strategy_comment": "Explores opportunity regions while avoiding difficult areas. Prioritizes overall diversity."
}
```
2025-07-04 21:48:16,491 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:48:16,491 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:48:16,492 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:48:16,492 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:48:16,492 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:48:16,492 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10302.0 (rank 0.0%)
- Population diversity: 0.7984615384615384
- Evolution phase: early exploration
- Landscape recommends focus: explore and refine
- Opportunity regions (sample): {'cells': [[0, 
- Difficult regions to avoid (sample): {'region': [55,, {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:48:16,493 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:48:16,493 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:48:20,936 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "All nodes visited.",
  "strategy_comment": "A basic path covering all nodes, focusing on broad exploration for initial iteration."
}
```
2025-07-04 21:48:20,937 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:48:20,937 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:48:20,938 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:48:20,938 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:48:20,938 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:48:20,939 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 93085.0 (rank 30.0%)
- Population diversity: 0.7323076923076923
- Evolution phase: early exploration
- Landscape recommends focus: explore and refine
- Opportunity regions (sample): {'cells': [[0, 
- Difficult regions to avoid (sample): {'region': [55,, {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:48:20,939 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:48:20,939 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:48:26,748 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Opportunity regions and low-density cells adjacent to them.",
  "strategy_comment": "Prioritize exploration of opportunity regions while maintaining path validity and diversity."
}
```
2025-07-04 21:48:26,749 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:48:26,750 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:48:26,750 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:48:26,751 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-04 21:48:26,751 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:48:26,753 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:48:26,754 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 112660.0
2025-07-04 21:48:29,374 - ExploitationExpert - INFO - res_population_num: 1
2025-07-04 21:48:29,376 - ExploitationExpert - INFO - res_population_costs: [94015]
2025-07-04 21:48:29,376 - ExploitationExpert - INFO - res_populations: [array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64)]
2025-07-04 21:48:29,377 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:48:29,377 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([18, 42, 20, 19,  6, 47, 25, 10, 27, 48, 22, 45, 43, 61, 44,  9, 15,
       65, 23,  2, 49, 58, 32,  3, 41, 54, 17, 55, 39,  5, 31, 24, 46, 53,
       11, 51, 40, 13, 12, 56, 34, 38,  8, 57, 14, 16, 63, 60,  0, 64, 30,
       36, 59, 28, 26, 29, 33, 21, 52, 37, 62, 35,  7,  1,  4, 50]), 'cur_cost': 112660.0}, {'tour': [27, 40, 30, 39, 33, 50, 6, 65, 57, 17, 18, 49, 41, 5, 37, 13, 31, 44, 55, 29, 1, 21, 11, 42, 22, 45, 61, 12, 15, 43, 53, 20, 3, 24, 48, 9, 62, 23, 52, 19, 36, 7, 32, 46, 34, 64, 58, 4, 16, 60, 56, 38, 0, 54, 35, 26, 59, 47, 51, 63, 2, 25, 10, 28, 8, 14], 'cur_cost': 114280.0}, {'tour': [60, 3, 21, 54, 47, 8, 12, 35, 64, 61, 1, 27, 19, 34, 53, 10, 36, 40, 50, 51, 55, 16, 58, 13, 41, 15, 39, 46, 2, 20, 30, 43, 17, 38, 9, 6, 37, 63, 56, 31, 5, 45, 11, 62, 26, 18, 65, 14, 42, 23, 49, 57, 48, 44, 22, 0, 52, 28, 7, 32, 4, 29, 33, 25, 24, 59], 'cur_cost': 105624.0}, {'tour': [16, 62, 44, 31, 20, 28, 32, 1, 8, 6, 45, 19, 26, 0, 21, 18, 59, 37, 40, 42, 38, 43, 25, 49, 15, 46, 29, 41, 24, 35, 57, 48, 64, 56, 65, 10, 11, 27, 50, 23, 14, 22, 47, 58, 55, 30, 52, 39, 7, 51, 5, 36, 53, 34, 2, 9, 54, 60, 17, 12, 61, 4, 13, 33, 3, 63], 'cur_cost': 100115.0}, {'tour': [33, 26, 43, 29, 21, 17, 61, 22, 40, 53, 54, 2, 12, 0, 19, 28, 57, 23, 46, 51, 5, 58, 10, 4, 15, 55, 44, 11, 20, 48, 25, 9, 37, 47, 56, 18, 27, 64, 34, 42, 7, 39, 62, 45, 41, 14, 35, 60, 30, 50, 65, 13, 6, 59, 1, 49, 32, 31, 38, 16, 63, 24, 8, 36, 52, 3], 'cur_cost': 116278.0}, {'tour': [4, 32, 25, 21, 45, 55, 31, 61, 62, 2, 59, 22, 56, 40, 34, 54, 60, 16, 57, 46, 28, 58, 39, 6, 43, 30, 17, 15, 24, 53, 13, 41, 27, 42, 47, 38, 20, 1, 0, 63, 8, 19, 5, 26, 18, 12, 64, 50, 7, 48, 3, 37, 35, 44, 11, 51, 9, 52, 14, 23, 36, 33, 49, 65, 10, 29], 'cur_cost': 110365.0}]
2025-07-04 21:48:29,378 - ExploitationExpert - INFO - 局部搜索耗时: 2.62秒
2025-07-04 21:48:29,378 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-04 21:48:29,378 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-07-04 21:48:29,378 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 21:48:29,378 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:48:29,379 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 114280.0 (rank 80.0%)
- Population diversity: 0.6476923076923077
- Evolution phase: early exploration
- Landscape recommends focus: explore and refine
- Opportunity regions (sample): {'cells': [[0, 
- Difficult regions to avoid (sample): {'region': [55,, {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:48:29,379 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:48:29,379 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:48:37,208 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Exploiting opportunity and avoiding difficult ones.",
  "strategy_comment": "Simple linear traversal to explore the search space; seeks diversity via fundamental route structure."
}
```
2025-07-04 21:48:37,208 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:48:37,209 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:48:37,209 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:48:37,210 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 21:48:37,210 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:48:37,211 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 105624.0 (rank 60.0%)
- Population diversity: 0.5615384615384615
- Evolution phase: early exploration
- Landscape recommends focus: explore and refine
- Opportunity regions (sample): {'cells': [[0, 
- Difficult regions to avoid (sample): {'region': [55,, {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:48:37,211 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:48:37,212 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:48:43,238 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2],
  "targeted_regions": "Opportunity regions around nodes 0, 55. Explores sparse cells adjacently.",
  "strategy_comment": "Prioritized opportunity regions & avoided difficult cells by sweeping along a path. Increased diversity by reversing path."
}
```
2025-07-04 21:48:43,239 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:48:43,239 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17673.0, 路径: [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2]
2025-07-04 21:48:43,240 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}
2025-07-04 21:48:43,240 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 21:48:43,240 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:48:43,241 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:48:43,241 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 106139.0
2025-07-04 21:48:45,287 - ExploitationExpert - INFO - res_population_num: 2
2025-07-04 21:48:45,288 - ExploitationExpert - INFO - res_population_costs: [94015, 9558]
2025-07-04 21:48:45,288 - ExploitationExpert - INFO - res_populations: [array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:48:45,288 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:48:45,288 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([18, 42, 20, 19,  6, 47, 25, 10, 27, 48, 22, 45, 43, 61, 44,  9, 15,
       65, 23,  2, 49, 58, 32,  3, 41, 54, 17, 55, 39,  5, 31, 24, 46, 53,
       11, 51, 40, 13, 12, 56, 34, 38,  8, 57, 14, 16, 63, 60,  0, 64, 30,
       36, 59, 28, 26, 29, 33, 21, 52, 37, 62, 35,  7,  1,  4, 50]), 'cur_cost': 112660.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}, {'tour': array([46, 49, 43,  3, 53, 34, 55, 47, 15, 23, 36, 29, 64, 30, 60, 40, 13,
       63, 10, 19, 35, 44, 57, 16, 58, 39,  0, 18, 20, 61, 25,  2,  6,  9,
       45, 48, 12, 21, 59, 52, 41,  1, 37, 17, 14, 56, 27, 32, 28, 62, 54,
       31, 22, 11,  7, 26, 42,  5, 38, 51,  4, 50, 65,  8, 33, 24]), 'cur_cost': 106139.0}, {'tour': [33, 26, 43, 29, 21, 17, 61, 22, 40, 53, 54, 2, 12, 0, 19, 28, 57, 23, 46, 51, 5, 58, 10, 4, 15, 55, 44, 11, 20, 48, 25, 9, 37, 47, 56, 18, 27, 64, 34, 42, 7, 39, 62, 45, 41, 14, 35, 60, 30, 50, 65, 13, 6, 59, 1, 49, 32, 31, 38, 16, 63, 24, 8, 36, 52, 3], 'cur_cost': 116278.0}, {'tour': [4, 32, 25, 21, 45, 55, 31, 61, 62, 2, 59, 22, 56, 40, 34, 54, 60, 16, 57, 46, 28, 58, 39, 6, 43, 30, 17, 15, 24, 53, 13, 41, 27, 42, 47, 38, 20, 1, 0, 63, 8, 19, 5, 26, 18, 12, 64, 50, 7, 48, 3, 37, 35, 44, 11, 51, 9, 52, 14, 23, 36, 33, 49, 65, 10, 29], 'cur_cost': 110365.0}]
2025-07-04 21:48:45,293 - ExploitationExpert - INFO - 局部搜索耗时: 2.05秒
2025-07-04 21:48:45,293 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-04 21:48:45,293 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 21:48:45,294 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:48:45,294 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:48:45,294 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:48:45,295 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 115990.0
2025-07-04 21:48:45,798 - ExploitationExpert - INFO - res_population_num: 8
2025-07-04 21:48:45,798 - ExploitationExpert - INFO - res_population_costs: [94015, 9558, 9556, 9556, 9548, 9535, 9534, 9522]
2025-07-04 21:48:45,798 - ExploitationExpert - INFO - res_populations: [array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:48:45,804 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:48:45,804 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([18, 42, 20, 19,  6, 47, 25, 10, 27, 48, 22, 45, 43, 61, 44,  9, 15,
       65, 23,  2, 49, 58, 32,  3, 41, 54, 17, 55, 39,  5, 31, 24, 46, 53,
       11, 51, 40, 13, 12, 56, 34, 38,  8, 57, 14, 16, 63, 60,  0, 64, 30,
       36, 59, 28, 26, 29, 33, 21, 52, 37, 62, 35,  7,  1,  4, 50]), 'cur_cost': 112660.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}, {'tour': array([46, 49, 43,  3, 53, 34, 55, 47, 15, 23, 36, 29, 64, 30, 60, 40, 13,
       63, 10, 19, 35, 44, 57, 16, 58, 39,  0, 18, 20, 61, 25,  2,  6,  9,
       45, 48, 12, 21, 59, 52, 41,  1, 37, 17, 14, 56, 27, 32, 28, 62, 54,
       31, 22, 11,  7, 26, 42,  5, 38, 51,  4, 50, 65,  8, 33, 24]), 'cur_cost': 106139.0}, {'tour': array([20, 36, 21, 39, 16, 53,  9, 28, 32, 60, 48, 23, 45,  8, 55, 15, 31,
       10, 57, 24, 14,  7, 30, 50, 26, 12, 37,  5, 19, 47, 58,  2, 13,  6,
       44, 52, 43, 33, 62, 42, 59,  3,  4, 54, 25, 61, 38, 40, 22, 29, 11,
       18,  0, 35, 65, 63, 49, 64,  1, 34, 51, 46, 56, 27, 17, 41]), 'cur_cost': 115990.0}, {'tour': [4, 32, 25, 21, 45, 55, 31, 61, 62, 2, 59, 22, 56, 40, 34, 54, 60, 16, 57, 46, 28, 58, 39, 6, 43, 30, 17, 15, 24, 53, 13, 41, 27, 42, 47, 38, 20, 1, 0, 63, 8, 19, 5, 26, 18, 12, 64, 50, 7, 48, 3, 37, 35, 44, 11, 51, 9, 52, 14, 23, 36, 33, 49, 65, 10, 29], 'cur_cost': 110365.0}]
2025-07-04 21:48:45,806 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:48:45,807 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-04 21:48:45,807 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:48:45,808 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:48:45,808 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:48:45,808 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:48:45,809 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 95665.0
2025-07-04 21:48:46,314 - ExploitationExpert - INFO - res_population_num: 9
2025-07-04 21:48:46,314 - ExploitationExpert - INFO - res_population_costs: [94015, 9558, 9556, 9556, 9548, 9535, 9534, 9522, 9521]
2025-07-04 21:48:46,315 - ExploitationExpert - INFO - res_populations: [array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 21:48:46,319 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:48:46,319 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([18, 42, 20, 19,  6, 47, 25, 10, 27, 48, 22, 45, 43, 61, 44,  9, 15,
       65, 23,  2, 49, 58, 32,  3, 41, 54, 17, 55, 39,  5, 31, 24, 46, 53,
       11, 51, 40, 13, 12, 56, 34, 38,  8, 57, 14, 16, 63, 60,  0, 64, 30,
       36, 59, 28, 26, 29, 33, 21, 52, 37, 62, 35,  7,  1,  4, 50]), 'cur_cost': 112660.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}, {'tour': array([46, 49, 43,  3, 53, 34, 55, 47, 15, 23, 36, 29, 64, 30, 60, 40, 13,
       63, 10, 19, 35, 44, 57, 16, 58, 39,  0, 18, 20, 61, 25,  2,  6,  9,
       45, 48, 12, 21, 59, 52, 41,  1, 37, 17, 14, 56, 27, 32, 28, 62, 54,
       31, 22, 11,  7, 26, 42,  5, 38, 51,  4, 50, 65,  8, 33, 24]), 'cur_cost': 106139.0}, {'tour': array([20, 36, 21, 39, 16, 53,  9, 28, 32, 60, 48, 23, 45,  8, 55, 15, 31,
       10, 57, 24, 14,  7, 30, 50, 26, 12, 37,  5, 19, 47, 58,  2, 13,  6,
       44, 52, 43, 33, 62, 42, 59,  3,  4, 54, 25, 61, 38, 40, 22, 29, 11,
       18,  0, 35, 65, 63, 49, 64,  1, 34, 51, 46, 56, 27, 17, 41]), 'cur_cost': 115990.0}, {'tour': array([20,  7, 33, 51,  2, 61, 24, 40, 52, 58,  3, 10, 35, 26, 64, 28, 65,
       27, 60,  8,  6, 55, 14,  9, 56, 22, 50, 11, 25, 36, 30, 49, 41, 48,
       44, 29, 21, 53, 43, 19, 42, 46, 47, 38, 39, 13, 54,  5, 37, 57, 18,
       32, 63, 59, 31,  4, 34, 17,  1, 12, 15, 23, 62,  0, 16, 45]), 'cur_cost': 95665.0}]
2025-07-04 21:48:46,324 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:48:46,325 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-04 21:48:46,325 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:48:46,326 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}}]
2025-07-04 21:48:46,326 - __main__ - INFO - 进化阶段完成
2025-07-04 21:48:46,327 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:48:46,353 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 115990.0, 'mean': 50552.2, 'std': 46881.9348273938}, 'diversity': 0.7599326599326599, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:48:46,353 - __main__ - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-04 21:48:46,354 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment ---
2025-07-04 21:48:46,354 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:48:46,355 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 9 → 9
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.284 → 0.284 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:48:46,356 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:48:52,594 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_diversity_weight", "consider_increasing_mutation_rate"]
}
```

2025-07-04 21:48:52,595 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:48:52,595 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-04 21:48:52,596 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-04 21:48:52,596 - __main__ - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-04 21:48:52,596 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:48:52,613 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 115990.0, 'mean': 50552.2, 'std': 46881.9348273938}, 'diversity': 0.7599326599326599, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:48:52,613 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:48:52,616 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:48:52,616 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:48:52,617 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:48:52,619 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:48:52,622 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:48:52,623 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:48:52,623 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 11479.0, mean 50552.2, max 115990.0, std 46881.9348273938
- diversity: 0.7599326599326599
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.3947811447811448}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:48:52,624 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:49:04,077 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.3,
    "modality": "likely multi-modal",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.76,
    "convergence": 0.0,
    "clustering": "no clustering info"
  },
  "difficult_regions": [
    "Long edges (long_edge_ratio = 0.249) likely indicate regions where local search might struggle, potentially forming long edge corridors.",
    "Low density cells in the density_grid could be areas requiring more exploration."
  ],
  "opportunity_regions": [
    "High-density cells in the density_grid ([[14, 0, 0], [0, 12, 12], [14, 0, 14]]) suggest potential for exploiting high concentrations of nodes and edges.",
    "No high-quality edges or common subpaths were found. This does not mean they do not exist and further analysis with higher quality data is advised."
  ],
  "evolution_phase": "Early exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Apply operators that favor global search, such as mutation operators that swap or insert/delete large sections of the tour, or operators that allow exploration across distant areas of the space.",
      "Consider population diversification through techniques like random restarts or re-seeding parts of the population with new solutions. Given a high diversity value (0.76) this should not be excessive.",
      "Explore nodes with high density. Focus on exploring edges in the dense parts and try to avoid using long edges initially."
    ]
  }
}
```
2025-07-04 21:49:04,078 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:49:04,078 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3, 'modality': 'likely multi-modal', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.76, 'convergence': 0.0, 'clustering': 'no clustering info'}, 'difficult_regions': ['Long edges (long_edge_ratio = 0.249) likely indicate regions where local search might struggle, potentially forming long edge corridors.', 'Low density cells in the density_grid could be areas requiring more exploration.'], 'opportunity_regions': ['High-density cells in the density_grid ([[14, 0, 0], [0, 12, 12], [14, 0, 14]]) suggest potential for exploiting high concentrations of nodes and edges.', 'No high-quality edges or common subpaths were found. This does not mean they do not exist and further analysis with higher quality data is advised.'], 'evolution_phase': 'Early exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Apply operators that favor global search, such as mutation operators that swap or insert/delete large sections of the tour, or operators that allow exploration across distant areas of the space.', 'Consider population diversification through techniques like random restarts or re-seeding parts of the population with new solutions. Given a high diversity value (0.76) this should not be excessive.', 'Explore nodes with high density. Focus on exploring edges in the dense parts and try to avoid using long edges initially.']}}
2025-07-04 21:49:04,078 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:49:04,079 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:49:04,079 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.76
  • best_cost: 11479.0
  • mean_cost: 50552.2
- Search-space: ruggedness 0.3, modality likely multi-modal, deceptiveness unknown
- Evolution phase: Early exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): Long edges (long_edg, Low density cells in
- Opportunity regions (# 2): High-density cells i, No high-quality edge
- Last-iteration feedback: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:49:04,079 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:49:04,080 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:49:08,152 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with low diversity and recommended focus on exploration. Prioritize exploring new areas of the search space."
}
```
2025-07-04 21:49:08,153 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:49:08,153 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:49:08,154 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with low diversity and recommended focus on exploration. Prioritize exploring new areas of the search space."
}
```
2025-07-04 21:49:08,154 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:49:08,155 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:49:08,155 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with low diversity and recommended focus on exploration. Prioritize exploring new areas of the search space."
}
```
2025-07-04 21:49:08,155 - __main__ - INFO - 开始进化阶段
2025-07-04 21:49:08,156 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:49:08,156 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:49:08,156 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.563076923076923
- Evolution phase: Early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): High-density ce, No high-quality
- Difficult regions to avoid (sample): Long edges (lon, Low density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:49:08,156 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:49:08,156 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:49:21,365 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Max retries exceeded with url: /v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))
2025-07-04 21:49:23,367 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-04 21:49:28,696 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Low-density cells adjacent to potential opportunity regions (implicitly).",
  "strategy_comment": "Initial path covering all nodes. Focused on broad coverage and avoiding long edges."
}
```
2025-07-04 21:49:28,696 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:49:28,696 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:49:28,699 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:49:28,699 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:49:28,699 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:49:28,700 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.563076923076923
- Evolution phase: Early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): High-density ce, No high-quality
- Difficult regions to avoid (sample): Long edges (lon, Low density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:49:28,700 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:49:28,700 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:49:36,914 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Low-density cells adjacent to opportunity regions",
  "strategy_comment": "Prioritizes exploration of unexplored areas by connecting nodes sequentially and avoiding long edges."
}
```
2025-07-04 21:49:36,914 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:49:36,915 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:49:36,915 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:49:36,916 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:49:36,916 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:49:36,916 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.563076923076923
- Evolution phase: Early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): High-density ce, No high-quality
- Difficult regions to avoid (sample): Long edges (lon, Low density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:49:36,917 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:49:36,917 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:49:57,975 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4],
  "targeted_regions": "Exploits low-density cells near opportunity regions.",
  "strategy_comment": "Prioritizes low-density exploration while maintaining path feasibility."
}
```
2025-07-04 21:49:57,976 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:49:57,977 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17777.0, 路径: [0, 1, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4]
2025-07-04 21:49:57,977 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4], 'cur_cost': 17777.0}
2025-07-04 21:49:57,977 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:49:57,978 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:49:57,978 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.563076923076923
- Evolution phase: Early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): High-density ce, No high-quality
- Difficult regions to avoid (sample): Long edges (lon, Low density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:49:57,979 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:49:57,979 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:50:02,794 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [5, 8, 12, 18, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 60, 57, 54, 51, 48, 45, 42, 39, 36, 33, 30, 27, 24, 21, 17, 14, 11, 7, 4, 1, 0, 2, 3, 6, 9, 10, 13, 15, 16, 19, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 63],
  "targeted_regions": "Adjacent to high-density ce.",
  "strategy_comment": "Explores sparse areas near opportunity regions while avoiding long edges and seeking diversity."
}
```
2025-07-04 21:50:02,795 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:50:02,795 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25899.0, 路径: [5, 8, 12, 18, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 60, 57, 54, 51, 48, 45, 42, 39, 36, 33, 30, 27, 24, 21, 17, 14, 11, 7, 4, 1, 0, 2, 3, 6, 9, 10, 13, 15, 16, 19, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 63, 65]
2025-07-04 21:50:02,796 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [5, 8, 12, 18, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 60, 57, 54, 51, 48, 45, 42, 39, 36, 33, 30, 27, 24, 21, 17, 14, 11, 7, 4, 1, 0, 2, 3, 6, 9, 10, 13, 15, 16, 19, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 63, 65], 'cur_cost': 25899.0}
2025-07-04 21:50:02,796 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:50:02,796 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:50:02,796 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 112660.0 (rank 80.0%)
- Population diversity: 0.64
- Evolution phase: Early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): High-density ce, No high-quality
- Difficult regions to avoid (sample): Long edges (lon, Low density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:50:02,798 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:50:02,798 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:50:08,775 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 12, 23, 31, 45, 58, 60, 65, 64, 63, 62, 61, 59, 57, 55, 52, 50, 48, 46, 43, 40, 38, 35, 33, 30, 28, 26, 24, 21, 19, 17, 15, 13, 10, 8, 6, 3, 1, 2, 5, 7, 9, 11, 14, 16, 18, 20, 22, 25, 27, 29, 32, 34, 36, 37, 39, 41, 42, 44, 47, 49, 51, 53, 54, 56],
  "targeted_regions": "Sparse cells adjacent to opportunity regions, avoids long edges.",
  "strategy_comment": "Explores low-density areas near potential gains. Prioritizes diversity and cost effectiveness."
}
```
2025-07-04 21:50:08,776 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:50:08,777 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25969.0, 路径: [0, 4, 12, 23, 31, 45, 58, 60, 65, 64, 63, 62, 61, 59, 57, 55, 52, 50, 48, 46, 43, 40, 38, 35, 33, 30, 28, 26, 24, 21, 19, 17, 15, 13, 10, 8, 6, 3, 1, 2, 5, 7, 9, 11, 14, 16, 18, 20, 22, 25, 27, 29, 32, 34, 36, 37, 39, 41, 42, 44, 47, 49, 51, 53, 54, 56]
2025-07-04 21:50:08,777 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 12, 23, 31, 45, 58, 60, 65, 64, 63, 62, 61, 59, 57, 55, 52, 50, 48, 46, 43, 40, 38, 35, 33, 30, 28, 26, 24, 21, 19, 17, 15, 13, 10, 8, 6, 3, 1, 2, 5, 7, 9, 11, 14, 16, 18, 20, 22, 25, 27, 29, 32, 34, 36, 37, 39, 41, 42, 44, 47, 49, 51, 53, 54, 56], 'cur_cost': 25969.0}
2025-07-04 21:50:08,777 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 21:50:08,777 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:50:08,778 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.5692307692307692
- Evolution phase: Early exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): High-density ce, No high-quality
- Difficult regions to avoid (sample): Long edges (lon, Low density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:50:08,778 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:50:08,778 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:50:11,932 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59],
  "targeted_regions": "Sparse cells around nodes 0, 65.",
  "strategy_comment": "Explores low density areas around nodes 0 and 65 and prioritizes diversity early in exploration."
}
```
2025-07-04 21:50:11,933 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:50:11,933 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25989.0, 路径: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59]
2025-07-04 21:50:11,934 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}
2025-07-04 21:50:11,934 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 21:50:11,934 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:50:11,934 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:50:11,935 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 99942.0
2025-07-04 21:50:12,438 - ExploitationExpert - INFO - res_population_num: 12
2025-07-04 21:50:12,438 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015, 9521, 9521, 9521]
2025-07-04 21:50:12,438 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 21:50:12,443 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:50:12,443 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4], 'cur_cost': 17777.0}, {'tour': [5, 8, 12, 18, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 60, 57, 54, 51, 48, 45, 42, 39, 36, 33, 30, 27, 24, 21, 17, 14, 11, 7, 4, 1, 0, 2, 3, 6, 9, 10, 13, 15, 16, 19, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 63, 65], 'cur_cost': 25899.0}, {'tour': [0, 4, 12, 23, 31, 45, 58, 60, 65, 64, 63, 62, 61, 59, 57, 55, 52, 50, 48, 46, 43, 40, 38, 35, 33, 30, 28, 26, 24, 21, 19, 17, 15, 13, 10, 8, 6, 3, 1, 2, 5, 7, 9, 11, 14, 16, 18, 20, 22, 25, 27, 29, 32, 34, 36, 37, 39, 41, 42, 44, 47, 49, 51, 53, 54, 56], 'cur_cost': 25969.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}, {'tour': array([19,  5,  4, 24, 50, 55, 18, 11, 25, 53, 39, 43, 22, 38, 21, 36, 59,
       65, 30, 23, 33, 15, 28,  9, 60, 34, 47, 44, 37, 29,  6, 57, 46, 49,
       35, 64, 26, 12,  2,  3, 42, 52,  8, 40, 14, 16, 63, 58, 13, 41, 51,
       48,  0, 54, 45, 61, 56, 27, 32, 62, 17, 20, 10,  7,  1, 31]), 'cur_cost': 99942.0}, {'tour': array([46, 49, 43,  3, 53, 34, 55, 47, 15, 23, 36, 29, 64, 30, 60, 40, 13,
       63, 10, 19, 35, 44, 57, 16, 58, 39,  0, 18, 20, 61, 25,  2,  6,  9,
       45, 48, 12, 21, 59, 52, 41,  1, 37, 17, 14, 56, 27, 32, 28, 62, 54,
       31, 22, 11,  7, 26, 42,  5, 38, 51,  4, 50, 65,  8, 33, 24]), 'cur_cost': 106139.0}, {'tour': array([20, 36, 21, 39, 16, 53,  9, 28, 32, 60, 48, 23, 45,  8, 55, 15, 31,
       10, 57, 24, 14,  7, 30, 50, 26, 12, 37,  5, 19, 47, 58,  2, 13,  6,
       44, 52, 43, 33, 62, 42, 59,  3,  4, 54, 25, 61, 38, 40, 22, 29, 11,
       18,  0, 35, 65, 63, 49, 64,  1, 34, 51, 46, 56, 27, 17, 41]), 'cur_cost': 115990.0}, {'tour': array([20,  7, 33, 51,  2, 61, 24, 40, 52, 58,  3, 10, 35, 26, 64, 28, 65,
       27, 60,  8,  6, 55, 14,  9, 56, 22, 50, 11, 25, 36, 30, 49, 41, 48,
       44, 29, 21, 53, 43, 19, 42, 46, 47, 38, 39, 13, 54,  5, 37, 57, 18,
       32, 63, 59, 31,  4, 34, 17,  1, 12, 15, 23, 62,  0, 16, 45]), 'cur_cost': 95665.0}]
2025-07-04 21:50:12,445 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:50:12,445 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-04 21:50:12,446 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 21:50:12,446 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 21:50:12,446 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:50:12,446 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:50:12,447 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 110290.0
2025-07-04 21:50:14,115 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 21:50:14,115 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:50:14,116 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 21:50:14,123 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:50:14,123 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4], 'cur_cost': 17777.0}, {'tour': [5, 8, 12, 18, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 60, 57, 54, 51, 48, 45, 42, 39, 36, 33, 30, 27, 24, 21, 17, 14, 11, 7, 4, 1, 0, 2, 3, 6, 9, 10, 13, 15, 16, 19, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 63, 65], 'cur_cost': 25899.0}, {'tour': [0, 4, 12, 23, 31, 45, 58, 60, 65, 64, 63, 62, 61, 59, 57, 55, 52, 50, 48, 46, 43, 40, 38, 35, 33, 30, 28, 26, 24, 21, 19, 17, 15, 13, 10, 8, 6, 3, 1, 2, 5, 7, 9, 11, 14, 16, 18, 20, 22, 25, 27, 29, 32, 34, 36, 37, 39, 41, 42, 44, 47, 49, 51, 53, 54, 56], 'cur_cost': 25969.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}, {'tour': array([19,  5,  4, 24, 50, 55, 18, 11, 25, 53, 39, 43, 22, 38, 21, 36, 59,
       65, 30, 23, 33, 15, 28,  9, 60, 34, 47, 44, 37, 29,  6, 57, 46, 49,
       35, 64, 26, 12,  2,  3, 42, 52,  8, 40, 14, 16, 63, 58, 13, 41, 51,
       48,  0, 54, 45, 61, 56, 27, 32, 62, 17, 20, 10,  7,  1, 31]), 'cur_cost': 99942.0}, {'tour': array([49, 14,  2, 34, 23, 21,  8, 20,  5, 39, 52, 16, 19, 61, 38,  1,  7,
       37, 36, 46, 35, 30, 63, 42, 24,  3, 27, 56,  9, 60, 62, 45, 44, 64,
       12, 28, 33,  0, 41, 55, 48, 50, 13, 43, 57, 26, 32, 54, 15, 25,  4,
       11, 31, 65, 22, 47, 40, 29,  6, 53, 51, 18, 58, 17, 59, 10]), 'cur_cost': 110290.0}, {'tour': array([20, 36, 21, 39, 16, 53,  9, 28, 32, 60, 48, 23, 45,  8, 55, 15, 31,
       10, 57, 24, 14,  7, 30, 50, 26, 12, 37,  5, 19, 47, 58,  2, 13,  6,
       44, 52, 43, 33, 62, 42, 59,  3,  4, 54, 25, 61, 38, 40, 22, 29, 11,
       18,  0, 35, 65, 63, 49, 64,  1, 34, 51, 46, 56, 27, 17, 41]), 'cur_cost': 115990.0}, {'tour': array([20,  7, 33, 51,  2, 61, 24, 40, 52, 58,  3, 10, 35, 26, 64, 28, 65,
       27, 60,  8,  6, 55, 14,  9, 56, 22, 50, 11, 25, 36, 30, 49, 41, 48,
       44, 29, 21, 53, 43, 19, 42, 46, 47, 38, 39, 13, 54,  5, 37, 57, 18,
       32, 63, 59, 31,  4, 34, 17,  1, 12, 15, 23, 62,  0, 16, 45]), 'cur_cost': 95665.0}]
2025-07-04 21:50:14,125 - ExploitationExpert - INFO - 局部搜索耗时: 1.68秒
2025-07-04 21:50:14,126 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-04 21:50:14,126 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 21:50:14,126 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:50:14,126 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:50:14,126 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:50:14,127 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 106216.0
2025-07-04 21:50:14,629 - ExploitationExpert - INFO - res_population_num: 23
2025-07-04 21:50:14,629 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:50:14,629 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 21:50:14,637 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:50:14,638 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4], 'cur_cost': 17777.0}, {'tour': [5, 8, 12, 18, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 60, 57, 54, 51, 48, 45, 42, 39, 36, 33, 30, 27, 24, 21, 17, 14, 11, 7, 4, 1, 0, 2, 3, 6, 9, 10, 13, 15, 16, 19, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 63, 65], 'cur_cost': 25899.0}, {'tour': [0, 4, 12, 23, 31, 45, 58, 60, 65, 64, 63, 62, 61, 59, 57, 55, 52, 50, 48, 46, 43, 40, 38, 35, 33, 30, 28, 26, 24, 21, 19, 17, 15, 13, 10, 8, 6, 3, 1, 2, 5, 7, 9, 11, 14, 16, 18, 20, 22, 25, 27, 29, 32, 34, 36, 37, 39, 41, 42, 44, 47, 49, 51, 53, 54, 56], 'cur_cost': 25969.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}, {'tour': array([19,  5,  4, 24, 50, 55, 18, 11, 25, 53, 39, 43, 22, 38, 21, 36, 59,
       65, 30, 23, 33, 15, 28,  9, 60, 34, 47, 44, 37, 29,  6, 57, 46, 49,
       35, 64, 26, 12,  2,  3, 42, 52,  8, 40, 14, 16, 63, 58, 13, 41, 51,
       48,  0, 54, 45, 61, 56, 27, 32, 62, 17, 20, 10,  7,  1, 31]), 'cur_cost': 99942.0}, {'tour': array([49, 14,  2, 34, 23, 21,  8, 20,  5, 39, 52, 16, 19, 61, 38,  1,  7,
       37, 36, 46, 35, 30, 63, 42, 24,  3, 27, 56,  9, 60, 62, 45, 44, 64,
       12, 28, 33,  0, 41, 55, 48, 50, 13, 43, 57, 26, 32, 54, 15, 25,  4,
       11, 31, 65, 22, 47, 40, 29,  6, 53, 51, 18, 58, 17, 59, 10]), 'cur_cost': 110290.0}, {'tour': array([44, 59, 35, 33, 40, 55, 10, 18,  4,  0, 12, 27, 23, 24, 41, 47, 17,
       39, 62, 42,  7, 21,  5, 38, 32, 34, 58, 19, 56, 53, 60, 49, 48, 51,
       52, 26, 28, 65,  9, 36, 37,  6, 14, 16, 64, 20,  2, 25, 43, 57, 11,
       63, 29, 30, 45,  1, 15, 22, 54, 50,  3, 31, 61,  8, 46, 13]), 'cur_cost': 106216.0}, {'tour': array([20,  7, 33, 51,  2, 61, 24, 40, 52, 58,  3, 10, 35, 26, 64, 28, 65,
       27, 60,  8,  6, 55, 14,  9, 56, 22, 50, 11, 25, 36, 30, 49, 41, 48,
       44, 29, 21, 53, 43, 19, 42, 46, 47, 38, 39, 13, 54,  5, 37, 57, 18,
       32, 63, 59, 31,  4, 34, 17,  1, 12, 15, 23, 62,  0, 16, 45]), 'cur_cost': 95665.0}]
2025-07-04 21:50:14,640 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:50:14,640 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-04 21:50:14,640 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:50:14,641 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:50:14,641 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:50:14,641 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:50:14,642 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 109530.0
2025-07-04 21:50:15,144 - ExploitationExpert - INFO - res_population_num: 23
2025-07-04 21:50:15,144 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:50:15,144 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 21:50:15,153 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:50:15,153 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4], 'cur_cost': 17777.0}, {'tour': [5, 8, 12, 18, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 60, 57, 54, 51, 48, 45, 42, 39, 36, 33, 30, 27, 24, 21, 17, 14, 11, 7, 4, 1, 0, 2, 3, 6, 9, 10, 13, 15, 16, 19, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 63, 65], 'cur_cost': 25899.0}, {'tour': [0, 4, 12, 23, 31, 45, 58, 60, 65, 64, 63, 62, 61, 59, 57, 55, 52, 50, 48, 46, 43, 40, 38, 35, 33, 30, 28, 26, 24, 21, 19, 17, 15, 13, 10, 8, 6, 3, 1, 2, 5, 7, 9, 11, 14, 16, 18, 20, 22, 25, 27, 29, 32, 34, 36, 37, 39, 41, 42, 44, 47, 49, 51, 53, 54, 56], 'cur_cost': 25969.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}, {'tour': array([19,  5,  4, 24, 50, 55, 18, 11, 25, 53, 39, 43, 22, 38, 21, 36, 59,
       65, 30, 23, 33, 15, 28,  9, 60, 34, 47, 44, 37, 29,  6, 57, 46, 49,
       35, 64, 26, 12,  2,  3, 42, 52,  8, 40, 14, 16, 63, 58, 13, 41, 51,
       48,  0, 54, 45, 61, 56, 27, 32, 62, 17, 20, 10,  7,  1, 31]), 'cur_cost': 99942.0}, {'tour': array([49, 14,  2, 34, 23, 21,  8, 20,  5, 39, 52, 16, 19, 61, 38,  1,  7,
       37, 36, 46, 35, 30, 63, 42, 24,  3, 27, 56,  9, 60, 62, 45, 44, 64,
       12, 28, 33,  0, 41, 55, 48, 50, 13, 43, 57, 26, 32, 54, 15, 25,  4,
       11, 31, 65, 22, 47, 40, 29,  6, 53, 51, 18, 58, 17, 59, 10]), 'cur_cost': 110290.0}, {'tour': array([44, 59, 35, 33, 40, 55, 10, 18,  4,  0, 12, 27, 23, 24, 41, 47, 17,
       39, 62, 42,  7, 21,  5, 38, 32, 34, 58, 19, 56, 53, 60, 49, 48, 51,
       52, 26, 28, 65,  9, 36, 37,  6, 14, 16, 64, 20,  2, 25, 43, 57, 11,
       63, 29, 30, 45,  1, 15, 22, 54, 50,  3, 31, 61,  8, 46, 13]), 'cur_cost': 106216.0}, {'tour': array([ 6, 17, 60, 22, 11, 27, 44, 26, 52, 20, 62, 29, 43, 58, 15, 14, 35,
       49, 13, 12, 50, 34,  0, 25, 54, 63, 24, 61, 53, 23, 31, 32, 56, 21,
       37,  8, 59, 16, 41,  4, 33, 19, 51, 46, 10,  3, 39, 28, 42, 55, 30,
        9,  5,  1, 38, 40, 36, 64, 18, 57,  2, 48, 47, 45, 65,  7]), 'cur_cost': 109530.0}]
2025-07-04 21:50:15,155 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:50:15,155 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-04 21:50:15,155 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:50:15,156 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4], 'cur_cost': 17777.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [5, 8, 12, 18, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 60, 57, 54, 51, 48, 45, 42, 39, 36, 33, 30, 27, 24, 21, 17, 14, 11, 7, 4, 1, 0, 2, 3, 6, 9, 10, 13, 15, 16, 19, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 63, 65], 'cur_cost': 25899.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 12, 23, 31, 45, 58, 60, 65, 64, 63, 62, 61, 59, 57, 55, 52, 50, 48, 46, 43, 40, 38, 35, 33, 30, 28, 26, 24, 21, 19, 17, 15, 13, 10, 8, 6, 3, 1, 2, 5, 7, 9, 11, 14, 16, 18, 20, 22, 25, 27, 29, 32, 34, 36, 37, 39, 41, 42, 44, 47, 49, 51, 53, 54, 56], 'cur_cost': 25969.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 64, 63, 62, 61, 56, 51, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 2, 3, 4, 7, 8, 9, 12, 13, 14, 17, 18, 19, 22, 23, 24, 27, 28, 29, 32, 33, 34, 37, 38, 39, 42, 43, 44, 47, 48, 49, 52, 53, 54, 57, 58, 59], 'cur_cost': 25989.0}}]
2025-07-04 21:50:15,156 - __main__ - INFO - 进化阶段完成
2025-07-04 21:50:15,156 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:50:15,174 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 110290.0, 'mean': 54457.0, 'std': 42866.572552514626}, 'diversity': 0.8915824915824915, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:50:15,175 - __main__ - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-04 21:50:15,175 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment ---
2025-07-04 21:50:15,177 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:50:15,177 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 23 → 23
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.118 → 0.118 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:50:15,177 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:50:18,387 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0.0, "exploit": 0.0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio_to_0.1", "increase_diversity_weight_to_0.2"]
}
```
2025-07-04 21:50:18,388 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:50:18,388 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-04 21:50:18,389 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-04 21:50:18,389 - __main__ - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-07-04 21:50:18,389 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:50:18,408 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 110290.0, 'mean': 54457.0, 'std': 42866.572552514626}, 'diversity': 0.8915824915824915, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:50:18,408 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:50:18,411 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:50:18,412 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:50:18,412 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:50:18,422 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:50:18,424 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:50:18,426 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:50:18,426 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 11479.0, mean 54457.0, max 110290.0, std 42866.572552514626
- diversity: 0.8915824915824915
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.2912324829320877}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:50:18,426 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:50:25,625 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.3,
    "modality": "multi",
    "deceptiveness": "potentially moderate"
  },
  "population_state": {
    "diversity": 0.89,
    "convergence": 0.0,
    "clustering": "no clustering info"
  },
  "difficult_regions": [
    "Long edge corridors (edge_len_stats indicates long edges, and long_edge_ratio is 0.249). Further analysis of edge directions in direction_hist could reveal specific corridor orientations. Investigate edges with length > Q3. Examine spatial distribution to see if high density nodes span or are adjacent to long edges.",
    "Low density regions in density_grid (specifically, cells with few nodes). These could represent areas where the population has not explored enough."
  ],
  "opportunity_regions": [
    "High-density cells in density_grid: Specifically the top-left (14 nodes), center-center (12 nodes) and bottom-right (14 nodes) cells. These areas indicate high node concentration and potential for improvement in local search.",
    "Examine potential high-quality edges in high-density cells. Focus on those with shortest edge lengths between nodes in the regions. This will allow to identify promising edges, that the best solutions might share. High-quality edges could be detected by building elite solutions from a subset of high-density nodes."
  ],
  "evolution_phase": "early exploration",
  "evolution_direction": {
    "recommended_focus": "explore and exploit",
    "operators": [
      "Diversify population by using exploration operators like mutation or crossover that promote exploration in under-explored regions.",
      "Apply local search operators (e.g., 2-opt, 3-opt) to exploit opportunity regions by optimizing sub-tours of the solution within high-density cells. Prioritize local search based on the identification of high-quality edges in the opportunity regions.",
      "Consider a hybrid strategy: apply exploration operators globally, and then apply local search in high-density areas. This will help to explore and exploit the solution space, and optimize good sub-tours.",
      "Increase mutation rate, especially during exploration phases. The goal is to improve population diversity to escape local optima."
    ]
  }
}
```
2025-07-04 21:50:25,627 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:50:25,627 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3, 'modality': 'multi', 'deceptiveness': 'potentially moderate'}, 'population_state': {'diversity': 0.89, 'convergence': 0.0, 'clustering': 'no clustering info'}, 'difficult_regions': ['Long edge corridors (edge_len_stats indicates long edges, and long_edge_ratio is 0.249). Further analysis of edge directions in direction_hist could reveal specific corridor orientations. Investigate edges with length > Q3. Examine spatial distribution to see if high density nodes span or are adjacent to long edges.', 'Low density regions in density_grid (specifically, cells with few nodes). These could represent areas where the population has not explored enough.'], 'opportunity_regions': ['High-density cells in density_grid: Specifically the top-left (14 nodes), center-center (12 nodes) and bottom-right (14 nodes) cells. These areas indicate high node concentration and potential for improvement in local search.', 'Examine potential high-quality edges in high-density cells. Focus on those with shortest edge lengths between nodes in the regions. This will allow to identify promising edges, that the best solutions might share. High-quality edges could be detected by building elite solutions from a subset of high-density nodes.'], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'explore and exploit', 'operators': ['Diversify population by using exploration operators like mutation or crossover that promote exploration in under-explored regions.', 'Apply local search operators (e.g., 2-opt, 3-opt) to exploit opportunity regions by optimizing sub-tours of the solution within high-density cells. Prioritize local search based on the identification of high-quality edges in the opportunity regions.', 'Consider a hybrid strategy: apply exploration operators globally, and then apply local search in high-density areas. This will help to explore and exploit the solution space, and optimize good sub-tours.', 'Increase mutation rate, especially during exploration phases. The goal is to improve population diversity to escape local optima.']}}
2025-07-04 21:50:25,628 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:50:25,628 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:50:25,628 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.89
  • best_cost: 11479.0
  • mean_cost: 54457.0
- Search-space: ruggedness 0.3, modality multi, deceptiveness potentially moderate
- Evolution phase: early exploration
- Landscape focus suggestion: explore and exploit
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): Long edge corridors , Low density regions 
- Opportunity regions (# 2): High-density cells i, Examine potential hi
- Last-iteration feedback: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0.0, "exploit": 0.0},
  "balance_state...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:50:25,629 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:50:25,629 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:50:29,307 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity. Population benefits from exploring new areas to discover and exploit promising regions."
}
```
2025-07-04 21:50:29,308 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:50:29,308 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:50:29,308 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity. Population benefits from exploring new areas to discover and exploit promising regions."
}
```
2025-07-04 21:50:29,309 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:50:29,309 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:50:29,309 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity. Population benefits from exploring new areas to discover and exploit promising regions."
}
```
2025-07-04 21:50:29,310 - __main__ - INFO - 开始进化阶段
2025-07-04 21:50:29,310 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:50:29,310 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:50:29,310 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6676923076923077
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): High-density ce, Examine potenti
- Difficult regions to avoid (sample): Long edge corri, Low density reg
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:50:29,312 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:50:29,312 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:50:34,847 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0,
    52,
    17,
    23,
    47,
    58,
    34,
    41,
    61,
    1,
    25,
    10,
    63,
    13,
    38,
    44,
    3,
    29,
    64,
    53,
    5,
    31,
    27,
    62,
    8,
    35,
    20,
    39,
    48,
    42,
    55,
    15,
    50,
    46,
    40,
    30,
    21,
    2,
    59,
    36,
    9,
    56,
    24,
    11,
    32,
    4,
    16,
    12,
    65,
    51,
    45,
    19,
    37,
    57,
    26,
    6,
    22,
    49,
    33,
    14,
    28,
    7,
    60,
    54,
    18
  ],
  "targeted_regions": "Exploits opportunity region around nodes 17, 23.  Explores sparse areas near 58 and 34.",
  "strategy_comment": "Prioritizes opportunity regions while weaving through sparse cells for enhanced exploration and diversity."
}
```
2025-07-04 21:50:34,849 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:50:34,849 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103094.0, 路径: [0, 52, 17, 23, 47, 58, 34, 41, 61, 1, 25, 10, 63, 13, 38, 44, 3, 29, 64, 53, 5, 31, 27, 62, 8, 35, 20, 39, 48, 42, 55, 15, 50, 46, 40, 30, 21, 2, 59, 36, 9, 56, 24, 11, 32, 4, 16, 12, 65, 51, 45, 19, 37, 57, 26, 6, 22, 49, 33, 14, 28, 7, 60, 54, 18, 43]
2025-07-04 21:50:34,849 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 52, 17, 23, 47, 58, 34, 41, 61, 1, 25, 10, 63, 13, 38, 44, 3, 29, 64, 53, 5, 31, 27, 62, 8, 35, 20, 39, 48, 42, 55, 15, 50, 46, 40, 30, 21, 2, 59, 36, 9, 56, 24, 11, 32, 4, 16, 12, 65, 51, 45, 19, 37, 57, 26, 6, 22, 49, 33, 14, 28, 7, 60, 54, 18, 43], 'cur_cost': 103094.0}
2025-07-04 21:50:34,850 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:50:34,850 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:50:34,851 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.7584615384615384
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): High-density ce, Examine potenti
- Difficult regions to avoid (sample): Long edge corri, Low density reg
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:50:34,851 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:50:34,851 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:50:39,534 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 6, 7, 8, 14, 15, 16, 22, 23, 24, 30, 31, 32, 38, 39, 40, 46, 47, 48, 54, 55, 56, 62, 63, 64, 65, 60, 59, 53, 52, 46, 45, 44, 38, 37, 36, 30, 29, 28, 22, 21, 20, 14, 13, 12, 6, 5, 4, 3, 9, 10, 11, 17, 18, 19, 25, 26, 27, 33, 34, 35, 41, 42, 43, 49, 50, 51, 57, 58, 61],
  "targeted_regions": "Opportunity regions and sparse cells adjacent to high density.",
  "strategy_comment": "Prioritize visiting opportunity regions and sparse cells to enhance diversity while avoiding difficult regions."
}
```
2025-07-04 21:50:39,534 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:50:39,535 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35586.0, 路径: [0, 1, 2, 6, 7, 8, 14, 15, 16, 22, 23, 24, 30, 31, 32, 38, 39, 40, 46, 47, 48, 54, 55, 56, 62, 63, 64, 65, 60, 59, 53, 52, 45, 44, 37, 36, 29, 28, 21, 20, 13, 12, 5, 4, 3, 9, 10, 11, 17, 18, 19, 25, 26, 27, 33, 34, 35, 41, 42, 43, 49, 58, 61, 50, 57, 51]
2025-07-04 21:50:39,535 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 6, 7, 8, 14, 15, 16, 22, 23, 24, 30, 31, 32, 38, 39, 40, 46, 47, 48, 54, 55, 56, 62, 63, 64, 65, 60, 59, 53, 52, 45, 44, 37, 36, 29, 28, 21, 20, 13, 12, 5, 4, 3, 9, 10, 11, 17, 18, 19, 25, 26, 27, 33, 34, 35, 41, 42, 43, 49, 58, 61, 50, 57, 51], 'cur_cost': 35586.0}
2025-07-04 21:50:39,535 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:50:39,535 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:50:39,536 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17777.0 (rank 0.0%)
- Population diversity: 0.7738461538461539
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): High-density ce, Examine potenti
- Difficult regions to avoid (sample): Long edge corri, Low density reg
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:50:39,536 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:50:39,537 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:50:49,058 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 0],
  "targeted_regions": "Low-density cells adjacent to opportunity regions.",
  "strategy_comment": "Prioritizes sparse cells near opportunities while attempting diversity through a different starting point."
}
```
2025-07-04 21:50:49,059 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:50:49,059 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13563.0, 路径: [65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 0]
2025-07-04 21:50:49,061 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 0], 'cur_cost': 13563.0}
2025-07-04 21:50:49,061 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:50:49,061 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:50:49,062 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 25899.0 (rank 10.0%)
- Population diversity: 0.7630769230769231
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): High-density ce, Examine potenti
- Difficult regions to avoid (sample): Long edge corri, Low density reg
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:50:49,062 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:50:49,063 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:50:54,660 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 18, 25, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 31, 30, 29, 28, 27, 26, 24, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 3, 2, 1],
  "targeted_regions": "Exploits opportunity regions and sparse cells.",
  "strategy_comment": "Prioritizes opportunities, balances exploration with avoiding difficult areas for path efficiency."
}
```
2025-07-04 21:50:54,661 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:50:54,661 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15811.0, 路径: [0, 4, 18, 25, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 31, 30, 29, 28, 27, 26, 24, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 3, 2, 1]
2025-07-04 21:50:54,662 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 4, 18, 25, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 31, 30, 29, 28, 27, 26, 24, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 3, 2, 1], 'cur_cost': 15811.0}
2025-07-04 21:50:54,662 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:50:54,662 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:50:54,663 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 25969.0 (rank 20.0%)
- Population diversity: 0.7153846153846154
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): High-density ce, Examine potenti
- Difficult regions to avoid (sample): Long edge corri, Low density reg
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:50:54,663 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:50:54,663 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:51:05,197 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29,
    31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57,
    59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46,
    44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18,
    16, 14, 12, 10, 8, 6, 4, 2
  ],
  "targeted_regions": "Exploits edges near opportunity regions and connects sparse areas.",
  "strategy_comment": "Prioritizes exploration of low-density cells, while connecting to high potential regions for cost effectiveness."
}
```
2025-07-04 21:51:05,198 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:51:05,199 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17673.0, 路径: [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2]
2025-07-04 21:51:05,199 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}
2025-07-04 21:51:05,199 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-04 21:51:05,199 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:51:05,200 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:51:05,200 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 107404.0
2025-07-04 21:51:05,703 - ExploitationExpert - INFO - res_population_num: 25
2025-07-04 21:51:05,703 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015, 9521, 9521]
2025-07-04 21:51:05,703 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:51:05,712 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:51:05,712 - ExploitationExpert - INFO - populations: [{'tour': [0, 52, 17, 23, 47, 58, 34, 41, 61, 1, 25, 10, 63, 13, 38, 44, 3, 29, 64, 53, 5, 31, 27, 62, 8, 35, 20, 39, 48, 42, 55, 15, 50, 46, 40, 30, 21, 2, 59, 36, 9, 56, 24, 11, 32, 4, 16, 12, 65, 51, 45, 19, 37, 57, 26, 6, 22, 49, 33, 14, 28, 7, 60, 54, 18, 43], 'cur_cost': 103094.0}, {'tour': [0, 1, 2, 6, 7, 8, 14, 15, 16, 22, 23, 24, 30, 31, 32, 38, 39, 40, 46, 47, 48, 54, 55, 56, 62, 63, 64, 65, 60, 59, 53, 52, 45, 44, 37, 36, 29, 28, 21, 20, 13, 12, 5, 4, 3, 9, 10, 11, 17, 18, 19, 25, 26, 27, 33, 34, 35, 41, 42, 43, 49, 58, 61, 50, 57, 51], 'cur_cost': 35586.0}, {'tour': [65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 0], 'cur_cost': 13563.0}, {'tour': [0, 4, 18, 25, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 31, 30, 29, 28, 27, 26, 24, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 3, 2, 1], 'cur_cost': 15811.0}, {'tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}, {'tour': array([65, 49,  7, 39, 14, 33, 57, 21, 24, 52, 60, 15, 19, 20, 63, 40,  6,
       38, 43,  8, 41, 58, 27, 45, 18, 34, 36, 37, 30,  0, 31, 28, 62, 10,
       12, 16, 59, 26, 22, 55, 47, 23, 50, 48, 46, 25,  5, 17,  2, 51, 29,
       53, 61, 35, 54, 44, 42,  3,  4, 13, 11, 56, 32, 64,  1,  9]), 'cur_cost': 107404.0}, {'tour': array([19,  5,  4, 24, 50, 55, 18, 11, 25, 53, 39, 43, 22, 38, 21, 36, 59,
       65, 30, 23, 33, 15, 28,  9, 60, 34, 47, 44, 37, 29,  6, 57, 46, 49,
       35, 64, 26, 12,  2,  3, 42, 52,  8, 40, 14, 16, 63, 58, 13, 41, 51,
       48,  0, 54, 45, 61, 56, 27, 32, 62, 17, 20, 10,  7,  1, 31]), 'cur_cost': 99942.0}, {'tour': array([49, 14,  2, 34, 23, 21,  8, 20,  5, 39, 52, 16, 19, 61, 38,  1,  7,
       37, 36, 46, 35, 30, 63, 42, 24,  3, 27, 56,  9, 60, 62, 45, 44, 64,
       12, 28, 33,  0, 41, 55, 48, 50, 13, 43, 57, 26, 32, 54, 15, 25,  4,
       11, 31, 65, 22, 47, 40, 29,  6, 53, 51, 18, 58, 17, 59, 10]), 'cur_cost': 110290.0}, {'tour': array([44, 59, 35, 33, 40, 55, 10, 18,  4,  0, 12, 27, 23, 24, 41, 47, 17,
       39, 62, 42,  7, 21,  5, 38, 32, 34, 58, 19, 56, 53, 60, 49, 48, 51,
       52, 26, 28, 65,  9, 36, 37,  6, 14, 16, 64, 20,  2, 25, 43, 57, 11,
       63, 29, 30, 45,  1, 15, 22, 54, 50,  3, 31, 61,  8, 46, 13]), 'cur_cost': 106216.0}, {'tour': array([ 6, 17, 60, 22, 11, 27, 44, 26, 52, 20, 62, 29, 43, 58, 15, 14, 35,
       49, 13, 12, 50, 34,  0, 25, 54, 63, 24, 61, 53, 23, 31, 32, 56, 21,
       37,  8, 59, 16, 41,  4, 33, 19, 51, 46, 10,  3, 39, 28, 42, 55, 30,
        9,  5,  1, 38, 40, 36, 64, 18, 57,  2, 48, 47, 45, 65,  7]), 'cur_cost': 109530.0}]
2025-07-04 21:51:05,715 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:51:05,715 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-04 21:51:05,715 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-04 21:51:05,715 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 21:51:05,715 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:51:05,716 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:51:05,717 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 100683.0
2025-07-04 21:51:06,220 - ExploitationExpert - INFO - res_population_num: 27
2025-07-04 21:51:06,220 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015, 9521, 9521, 9521, 9521]
2025-07-04 21:51:06,220 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 21:51:06,231 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:51:06,231 - ExploitationExpert - INFO - populations: [{'tour': [0, 52, 17, 23, 47, 58, 34, 41, 61, 1, 25, 10, 63, 13, 38, 44, 3, 29, 64, 53, 5, 31, 27, 62, 8, 35, 20, 39, 48, 42, 55, 15, 50, 46, 40, 30, 21, 2, 59, 36, 9, 56, 24, 11, 32, 4, 16, 12, 65, 51, 45, 19, 37, 57, 26, 6, 22, 49, 33, 14, 28, 7, 60, 54, 18, 43], 'cur_cost': 103094.0}, {'tour': [0, 1, 2, 6, 7, 8, 14, 15, 16, 22, 23, 24, 30, 31, 32, 38, 39, 40, 46, 47, 48, 54, 55, 56, 62, 63, 64, 65, 60, 59, 53, 52, 45, 44, 37, 36, 29, 28, 21, 20, 13, 12, 5, 4, 3, 9, 10, 11, 17, 18, 19, 25, 26, 27, 33, 34, 35, 41, 42, 43, 49, 58, 61, 50, 57, 51], 'cur_cost': 35586.0}, {'tour': [65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 0], 'cur_cost': 13563.0}, {'tour': [0, 4, 18, 25, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 31, 30, 29, 28, 27, 26, 24, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 3, 2, 1], 'cur_cost': 15811.0}, {'tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}, {'tour': array([65, 49,  7, 39, 14, 33, 57, 21, 24, 52, 60, 15, 19, 20, 63, 40,  6,
       38, 43,  8, 41, 58, 27, 45, 18, 34, 36, 37, 30,  0, 31, 28, 62, 10,
       12, 16, 59, 26, 22, 55, 47, 23, 50, 48, 46, 25,  5, 17,  2, 51, 29,
       53, 61, 35, 54, 44, 42,  3,  4, 13, 11, 56, 32, 64,  1,  9]), 'cur_cost': 107404.0}, {'tour': array([38, 43, 42, 52,  0, 11, 65,  4, 10, 19, 27,  2, 17, 64, 16, 35, 46,
       54, 44, 53, 25, 22, 29,  6, 55,  5, 34, 32, 20, 24, 26,  8, 59, 41,
       13, 33, 50, 61, 40, 31, 23,  1, 57, 28, 21, 36, 56, 14, 15, 18, 48,
        7, 39, 30, 63, 37, 58, 62, 49, 47, 51, 45, 12,  9,  3, 60]), 'cur_cost': 100683.0}, {'tour': array([49, 14,  2, 34, 23, 21,  8, 20,  5, 39, 52, 16, 19, 61, 38,  1,  7,
       37, 36, 46, 35, 30, 63, 42, 24,  3, 27, 56,  9, 60, 62, 45, 44, 64,
       12, 28, 33,  0, 41, 55, 48, 50, 13, 43, 57, 26, 32, 54, 15, 25,  4,
       11, 31, 65, 22, 47, 40, 29,  6, 53, 51, 18, 58, 17, 59, 10]), 'cur_cost': 110290.0}, {'tour': array([44, 59, 35, 33, 40, 55, 10, 18,  4,  0, 12, 27, 23, 24, 41, 47, 17,
       39, 62, 42,  7, 21,  5, 38, 32, 34, 58, 19, 56, 53, 60, 49, 48, 51,
       52, 26, 28, 65,  9, 36, 37,  6, 14, 16, 64, 20,  2, 25, 43, 57, 11,
       63, 29, 30, 45,  1, 15, 22, 54, 50,  3, 31, 61,  8, 46, 13]), 'cur_cost': 106216.0}, {'tour': array([ 6, 17, 60, 22, 11, 27, 44, 26, 52, 20, 62, 29, 43, 58, 15, 14, 35,
       49, 13, 12, 50, 34,  0, 25, 54, 63, 24, 61, 53, 23, 31, 32, 56, 21,
       37,  8, 59, 16, 41,  4, 33, 19, 51, 46, 10,  3, 39, 28, 42, 55, 30,
        9,  5,  1, 38, 40, 36, 64, 18, 57,  2, 48, 47, 45, 65,  7]), 'cur_cost': 109530.0}]
2025-07-04 21:51:06,233 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:51:06,233 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-07-04 21:51:06,234 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 21:51:06,234 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 21:51:06,234 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:51:06,234 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:51:06,235 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 109155.0
2025-07-04 21:51:06,738 - ExploitationExpert - INFO - res_population_num: 28
2025-07-04 21:51:06,738 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:51:06,739 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 21:51:06,747 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:51:06,748 - ExploitationExpert - INFO - populations: [{'tour': [0, 52, 17, 23, 47, 58, 34, 41, 61, 1, 25, 10, 63, 13, 38, 44, 3, 29, 64, 53, 5, 31, 27, 62, 8, 35, 20, 39, 48, 42, 55, 15, 50, 46, 40, 30, 21, 2, 59, 36, 9, 56, 24, 11, 32, 4, 16, 12, 65, 51, 45, 19, 37, 57, 26, 6, 22, 49, 33, 14, 28, 7, 60, 54, 18, 43], 'cur_cost': 103094.0}, {'tour': [0, 1, 2, 6, 7, 8, 14, 15, 16, 22, 23, 24, 30, 31, 32, 38, 39, 40, 46, 47, 48, 54, 55, 56, 62, 63, 64, 65, 60, 59, 53, 52, 45, 44, 37, 36, 29, 28, 21, 20, 13, 12, 5, 4, 3, 9, 10, 11, 17, 18, 19, 25, 26, 27, 33, 34, 35, 41, 42, 43, 49, 58, 61, 50, 57, 51], 'cur_cost': 35586.0}, {'tour': [65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 0], 'cur_cost': 13563.0}, {'tour': [0, 4, 18, 25, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 31, 30, 29, 28, 27, 26, 24, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 3, 2, 1], 'cur_cost': 15811.0}, {'tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}, {'tour': array([65, 49,  7, 39, 14, 33, 57, 21, 24, 52, 60, 15, 19, 20, 63, 40,  6,
       38, 43,  8, 41, 58, 27, 45, 18, 34, 36, 37, 30,  0, 31, 28, 62, 10,
       12, 16, 59, 26, 22, 55, 47, 23, 50, 48, 46, 25,  5, 17,  2, 51, 29,
       53, 61, 35, 54, 44, 42,  3,  4, 13, 11, 56, 32, 64,  1,  9]), 'cur_cost': 107404.0}, {'tour': array([38, 43, 42, 52,  0, 11, 65,  4, 10, 19, 27,  2, 17, 64, 16, 35, 46,
       54, 44, 53, 25, 22, 29,  6, 55,  5, 34, 32, 20, 24, 26,  8, 59, 41,
       13, 33, 50, 61, 40, 31, 23,  1, 57, 28, 21, 36, 56, 14, 15, 18, 48,
        7, 39, 30, 63, 37, 58, 62, 49, 47, 51, 45, 12,  9,  3, 60]), 'cur_cost': 100683.0}, {'tour': array([64, 60, 22, 51, 38, 61, 46, 59,  1, 55, 30,  2, 19, 31,  4, 48, 50,
       13, 57, 56, 24, 39, 34, 16, 10, 36, 25, 37, 65, 41, 26, 21, 53, 63,
       49, 14, 58, 15,  5,  9, 27, 11,  3, 32, 54, 17,  8, 35, 20,  7, 29,
       12, 62, 28, 44, 43,  0, 42, 23,  6, 18, 33, 40, 47, 52, 45]), 'cur_cost': 109155.0}, {'tour': array([44, 59, 35, 33, 40, 55, 10, 18,  4,  0, 12, 27, 23, 24, 41, 47, 17,
       39, 62, 42,  7, 21,  5, 38, 32, 34, 58, 19, 56, 53, 60, 49, 48, 51,
       52, 26, 28, 65,  9, 36, 37,  6, 14, 16, 64, 20,  2, 25, 43, 57, 11,
       63, 29, 30, 45,  1, 15, 22, 54, 50,  3, 31, 61,  8, 46, 13]), 'cur_cost': 106216.0}, {'tour': array([ 6, 17, 60, 22, 11, 27, 44, 26, 52, 20, 62, 29, 43, 58, 15, 14, 35,
       49, 13, 12, 50, 34,  0, 25, 54, 63, 24, 61, 53, 23, 31, 32, 56, 21,
       37,  8, 59, 16, 41,  4, 33, 19, 51, 46, 10,  3, 39, 28, 42, 55, 30,
        9,  5,  1, 38, 40, 36, 64, 18, 57,  2, 48, 47, 45, 65,  7]), 'cur_cost': 109530.0}]
2025-07-04 21:51:06,750 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:51:06,751 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-07-04 21:51:06,751 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 21:51:06,751 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:51:06,753 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:51:06,753 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:51:06,753 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 114639.0
2025-07-04 21:51:07,257 - ExploitationExpert - INFO - res_population_num: 29
2025-07-04 21:51:07,257 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:51:07,257 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 21:51:07,267 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:51:07,268 - ExploitationExpert - INFO - populations: [{'tour': [0, 52, 17, 23, 47, 58, 34, 41, 61, 1, 25, 10, 63, 13, 38, 44, 3, 29, 64, 53, 5, 31, 27, 62, 8, 35, 20, 39, 48, 42, 55, 15, 50, 46, 40, 30, 21, 2, 59, 36, 9, 56, 24, 11, 32, 4, 16, 12, 65, 51, 45, 19, 37, 57, 26, 6, 22, 49, 33, 14, 28, 7, 60, 54, 18, 43], 'cur_cost': 103094.0}, {'tour': [0, 1, 2, 6, 7, 8, 14, 15, 16, 22, 23, 24, 30, 31, 32, 38, 39, 40, 46, 47, 48, 54, 55, 56, 62, 63, 64, 65, 60, 59, 53, 52, 45, 44, 37, 36, 29, 28, 21, 20, 13, 12, 5, 4, 3, 9, 10, 11, 17, 18, 19, 25, 26, 27, 33, 34, 35, 41, 42, 43, 49, 58, 61, 50, 57, 51], 'cur_cost': 35586.0}, {'tour': [65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 0], 'cur_cost': 13563.0}, {'tour': [0, 4, 18, 25, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 31, 30, 29, 28, 27, 26, 24, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 3, 2, 1], 'cur_cost': 15811.0}, {'tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}, {'tour': array([65, 49,  7, 39, 14, 33, 57, 21, 24, 52, 60, 15, 19, 20, 63, 40,  6,
       38, 43,  8, 41, 58, 27, 45, 18, 34, 36, 37, 30,  0, 31, 28, 62, 10,
       12, 16, 59, 26, 22, 55, 47, 23, 50, 48, 46, 25,  5, 17,  2, 51, 29,
       53, 61, 35, 54, 44, 42,  3,  4, 13, 11, 56, 32, 64,  1,  9]), 'cur_cost': 107404.0}, {'tour': array([38, 43, 42, 52,  0, 11, 65,  4, 10, 19, 27,  2, 17, 64, 16, 35, 46,
       54, 44, 53, 25, 22, 29,  6, 55,  5, 34, 32, 20, 24, 26,  8, 59, 41,
       13, 33, 50, 61, 40, 31, 23,  1, 57, 28, 21, 36, 56, 14, 15, 18, 48,
        7, 39, 30, 63, 37, 58, 62, 49, 47, 51, 45, 12,  9,  3, 60]), 'cur_cost': 100683.0}, {'tour': array([64, 60, 22, 51, 38, 61, 46, 59,  1, 55, 30,  2, 19, 31,  4, 48, 50,
       13, 57, 56, 24, 39, 34, 16, 10, 36, 25, 37, 65, 41, 26, 21, 53, 63,
       49, 14, 58, 15,  5,  9, 27, 11,  3, 32, 54, 17,  8, 35, 20,  7, 29,
       12, 62, 28, 44, 43,  0, 42, 23,  6, 18, 33, 40, 47, 52, 45]), 'cur_cost': 109155.0}, {'tour': array([38,  6, 46, 26, 12, 49, 34, 59, 15, 52, 29, 42, 28,  3,  7, 44, 24,
        8,  9, 39,  1, 33,  2, 36, 47, 64, 20, 54, 41, 55, 13,  4, 48, 18,
       23, 22, 60, 27, 21, 32, 56, 43, 30, 25, 63, 14, 45, 53, 62, 16, 31,
       37, 58, 57, 11, 10, 19, 65,  5, 61,  0, 40, 17, 50, 51, 35]), 'cur_cost': 114639.0}, {'tour': array([ 6, 17, 60, 22, 11, 27, 44, 26, 52, 20, 62, 29, 43, 58, 15, 14, 35,
       49, 13, 12, 50, 34,  0, 25, 54, 63, 24, 61, 53, 23, 31, 32, 56, 21,
       37,  8, 59, 16, 41,  4, 33, 19, 51, 46, 10,  3, 39, 28, 42, 55, 30,
        9,  5,  1, 38, 40, 36, 64, 18, 57,  2, 48, 47, 45, 65,  7]), 'cur_cost': 109530.0}]
2025-07-04 21:51:07,269 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:51:07,270 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-07-04 21:51:07,270 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:51:07,270 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:51:07,270 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:51:07,270 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:51:07,272 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 108295.0
2025-07-04 21:51:07,775 - ExploitationExpert - INFO - res_population_num: 31
2025-07-04 21:51:07,776 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:51:07,776 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:51:07,786 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:51:07,787 - ExploitationExpert - INFO - populations: [{'tour': [0, 52, 17, 23, 47, 58, 34, 41, 61, 1, 25, 10, 63, 13, 38, 44, 3, 29, 64, 53, 5, 31, 27, 62, 8, 35, 20, 39, 48, 42, 55, 15, 50, 46, 40, 30, 21, 2, 59, 36, 9, 56, 24, 11, 32, 4, 16, 12, 65, 51, 45, 19, 37, 57, 26, 6, 22, 49, 33, 14, 28, 7, 60, 54, 18, 43], 'cur_cost': 103094.0}, {'tour': [0, 1, 2, 6, 7, 8, 14, 15, 16, 22, 23, 24, 30, 31, 32, 38, 39, 40, 46, 47, 48, 54, 55, 56, 62, 63, 64, 65, 60, 59, 53, 52, 45, 44, 37, 36, 29, 28, 21, 20, 13, 12, 5, 4, 3, 9, 10, 11, 17, 18, 19, 25, 26, 27, 33, 34, 35, 41, 42, 43, 49, 58, 61, 50, 57, 51], 'cur_cost': 35586.0}, {'tour': [65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 0], 'cur_cost': 13563.0}, {'tour': [0, 4, 18, 25, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 31, 30, 29, 28, 27, 26, 24, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 3, 2, 1], 'cur_cost': 15811.0}, {'tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}, {'tour': array([65, 49,  7, 39, 14, 33, 57, 21, 24, 52, 60, 15, 19, 20, 63, 40,  6,
       38, 43,  8, 41, 58, 27, 45, 18, 34, 36, 37, 30,  0, 31, 28, 62, 10,
       12, 16, 59, 26, 22, 55, 47, 23, 50, 48, 46, 25,  5, 17,  2, 51, 29,
       53, 61, 35, 54, 44, 42,  3,  4, 13, 11, 56, 32, 64,  1,  9]), 'cur_cost': 107404.0}, {'tour': array([38, 43, 42, 52,  0, 11, 65,  4, 10, 19, 27,  2, 17, 64, 16, 35, 46,
       54, 44, 53, 25, 22, 29,  6, 55,  5, 34, 32, 20, 24, 26,  8, 59, 41,
       13, 33, 50, 61, 40, 31, 23,  1, 57, 28, 21, 36, 56, 14, 15, 18, 48,
        7, 39, 30, 63, 37, 58, 62, 49, 47, 51, 45, 12,  9,  3, 60]), 'cur_cost': 100683.0}, {'tour': array([64, 60, 22, 51, 38, 61, 46, 59,  1, 55, 30,  2, 19, 31,  4, 48, 50,
       13, 57, 56, 24, 39, 34, 16, 10, 36, 25, 37, 65, 41, 26, 21, 53, 63,
       49, 14, 58, 15,  5,  9, 27, 11,  3, 32, 54, 17,  8, 35, 20,  7, 29,
       12, 62, 28, 44, 43,  0, 42, 23,  6, 18, 33, 40, 47, 52, 45]), 'cur_cost': 109155.0}, {'tour': array([38,  6, 46, 26, 12, 49, 34, 59, 15, 52, 29, 42, 28,  3,  7, 44, 24,
        8,  9, 39,  1, 33,  2, 36, 47, 64, 20, 54, 41, 55, 13,  4, 48, 18,
       23, 22, 60, 27, 21, 32, 56, 43, 30, 25, 63, 14, 45, 53, 62, 16, 31,
       37, 58, 57, 11, 10, 19, 65,  5, 61,  0, 40, 17, 50, 51, 35]), 'cur_cost': 114639.0}, {'tour': array([35, 51, 65, 46, 43,  8,  1, 20, 55, 64, 14, 25,  0,  4, 59, 52, 32,
       57, 30, 10, 40, 54, 48, 18, 60, 50, 12, 37, 53, 44,  5, 36, 62, 16,
       19, 11, 45,  6, 56, 34, 38, 39, 28,  2, 47, 17, 42, 27, 26, 24, 31,
       29, 49, 63, 13, 23, 58, 41, 21,  7, 15, 61,  3,  9, 22, 33]), 'cur_cost': 108295.0}]
2025-07-04 21:51:07,790 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:51:07,790 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-07-04 21:51:07,791 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:51:07,791 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 52, 17, 23, 47, 58, 34, 41, 61, 1, 25, 10, 63, 13, 38, 44, 3, 29, 64, 53, 5, 31, 27, 62, 8, 35, 20, 39, 48, 42, 55, 15, 50, 46, 40, 30, 21, 2, 59, 36, 9, 56, 24, 11, 32, 4, 16, 12, 65, 51, 45, 19, 37, 57, 26, 6, 22, 49, 33, 14, 28, 7, 60, 54, 18, 43], 'cur_cost': 103094.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 6, 7, 8, 14, 15, 16, 22, 23, 24, 30, 31, 32, 38, 39, 40, 46, 47, 48, 54, 55, 56, 62, 63, 64, 65, 60, 59, 53, 52, 45, 44, 37, 36, 29, 28, 21, 20, 13, 12, 5, 4, 3, 9, 10, 11, 17, 18, 19, 25, 26, 27, 33, 34, 35, 41, 42, 43, 49, 58, 61, 50, 57, 51], 'cur_cost': 35586.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 0], 'cur_cost': 13563.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 18, 25, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 31, 30, 29, 28, 27, 26, 24, 23, 22, 21, 20, 19, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 3, 2, 1], 'cur_cost': 15811.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}}]
2025-07-04 21:51:07,791 - __main__ - INFO - 进化阶段完成
2025-07-04 21:51:07,792 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:51:07,810 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 13563.0, 'max': 114639.0, 'mean': 72590.3, 'std': 42900.93750269334}, 'diversity': 0.9245791245791246, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 1, 2, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:51:07,810 - __main__ - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-07-04 21:51:07,810 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment ---
2025-07-04 21:51:07,813 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:51:07,813 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 31 → 31
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.088 → 0.088 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:51:07,813 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:51:13,938 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_mutation_rate", "consider_increasing_diversity_weight", "reduce_elitism_percentage"]
}
```
2025-07-04 21:51:13,939 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:51:13,939 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-07-04 21:51:13,939 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-04 21:51:13,940 - __main__ - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-07-04 21:51:13,940 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:51:13,959 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 13563.0, 'max': 114639.0, 'mean': 72590.3, 'std': 42900.93750269334}, 'diversity': 0.9245791245791246, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 1, 2, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:51:13,959 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:51:13,964 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:51:13,964 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:51:13,964 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:51:13,982 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:51:13,984 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:51:13,987 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:51:13,988 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 13563.0, mean 72590.3, max 114639.0, std 42900.93750269334
- diversity: 0.9245791245791246
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.26881720430107525}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:51:13,988 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:51:28,938 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.4,
    "modality": "multi",
    "deceptiveness": "potential"
  },
  "population_state": {
    "diversity": 0.92,
    "convergence": 0.0,
    "clustering": "no_clustering"
  },
  "difficult_regions": [
    {"description": "Long edge corridors likely exist based on long_edge_ratio (0.249), potentially spanning regions with lower density. Further investigation required.", "cells": []}
  ],
  "opportunity_regions": [
    {"description": "High-density cells, specifically those in the grid with counts of 14 (likely near the edges) and 12, represent areas of high connectivity and potential for exploitation. Further edge analysis required to determine specific high-quality edges.", "cells": [[0, 0], [2, 0], [1,1], [1,2], [2,2]]}
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Apply large neighborhood search operators (e.g., 2-opt, 3-opt) with a bias towards exploiting high-density grid cells.",
      "Use edge recombination operators biased towards edges frequently present in high-density grid cells.",
      "Employ diversification operators to maintain high diversity and explore regions linked by long edges."
    ]
  }
}
```
2025-07-04 21:51:28,939 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:51:28,939 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.4, 'modality': 'multi', 'deceptiveness': 'potential'}, 'population_state': {'diversity': 0.92, 'convergence': 0.0, 'clustering': 'no_clustering'}, 'difficult_regions': [{'description': 'Long edge corridors likely exist based on long_edge_ratio (0.249), potentially spanning regions with lower density. Further investigation required.', 'cells': []}], 'opportunity_regions': [{'description': 'High-density cells, specifically those in the grid with counts of 14 (likely near the edges) and 12, represent areas of high connectivity and potential for exploitation. Further edge analysis required to determine specific high-quality edges.', 'cells': [[0, 0], [2, 0], [1, 1], [1, 2], [2, 2]]}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Apply large neighborhood search operators (e.g., 2-opt, 3-opt) with a bias towards exploiting high-density grid cells.', 'Use edge recombination operators biased towards edges frequently present in high-density grid cells.', 'Employ diversification operators to maintain high diversity and explore regions linked by long edges.']}}
2025-07-04 21:51:28,939 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:51:28,939 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:51:28,940 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.92
  • best_cost: 13563.0
  • mean_cost: 72590.3
- Search-space: ruggedness 0.4, modality multi, deceptiveness potential
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 1): {'description': 'Lon
- Opportunity regions (# 1): {'description': 'Hig
- Last-iteration feedback: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:51:28,940 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:51:28,940 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:51:32,859 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early stagnation and high diversity suggests we should explore to escape potentially local optima."
}
```
2025-07-04 21:51:32,861 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore']
2025-07-04 21:51:32,861 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore']
2025-07-04 21:51:32,861 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early stagnation and high diversity suggests we should explore to escape potentially local optima."
}
```
2025-07-04 21:51:32,861 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:51:32,862 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore']
2025-07-04 21:51:32,862 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early stagnation and high diversity suggests we should explore to escape potentially local optima."
}
```
2025-07-04 21:51:32,862 - __main__ - INFO - 开始进化阶段
2025-07-04 21:51:32,863 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:51:32,863 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:51:32,863 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 103094.0 (rank 50.0%)
- Population diversity: 0.7507692307692307
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:51:32,863 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:51:32,864 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:51:40,081 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 6, 11, 18, 25, 32, 39, 46, 53, 60, 61, 62, 63, 64, 65, 64, 59, 52, 45, 38, 31, 24, 17, 10, 5, 2, 3, 4, 7, 8, 9, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 54, 55, 56, 57, 58, 0],
  "targeted_regions": "Exploits sparse cells adjacent to potential opportunity regions.",
  "strategy_comment": "Prioritizes visiting the opportunity regions and their neighbors, aiming for diversity with a long route."
}
```
2025-07-04 21:51:40,082 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:51:40,082 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25906.0, 路径: [0, 1, 6, 11, 18, 25, 32, 39, 46, 53, 60, 61, 62, 63, 64, 65, 59, 52, 45, 38, 31, 24, 17, 10, 5, 2, 3, 4, 7, 8, 9, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 54, 55, 56, 57, 58]
2025-07-04 21:51:40,082 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 6, 11, 18, 25, 32, 39, 46, 53, 60, 61, 62, 63, 64, 65, 59, 52, 45, 38, 31, 24, 17, 10, 5, 2, 3, 4, 7, 8, 9, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 54, 55, 56, 57, 58], 'cur_cost': 25906.0}
2025-07-04 21:51:40,083 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:51:40,083 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:51:40,084 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 35586.0 (rank 40.0%)
- Population diversity: 0.6984615384615385
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:51:40,084 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:51:40,084 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:51:42,951 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 61, 57, 53, 49, 45, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 63, 59, 55, 51, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3],
  "targeted_regions": "sparse cells adjacent to regions 0-64",
  "strategy_comment": "Explores low-density cells near opportunity regions for improved diversity."
}
```
2025-07-04 21:51:42,952 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:51:42,952 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34121.0, 路径: [0, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 61, 57, 53, 49, 45, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 63, 59, 55, 51, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 65]
2025-07-04 21:51:42,952 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 61, 57, 53, 49, 45, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 63, 59, 55, 51, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 65], 'cur_cost': 34121.0}
2025-07-04 21:51:42,953 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:51:42,953 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:51:42,953 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13563.0 (rank 0.0%)
- Population diversity: 0.7446153846153846
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:51:42,954 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:51:42,954 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:51:48,741 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
    20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37,
    38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55,
    56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "targeted_regions": "Exploration of sparse and opportunity regions.",
  "strategy_comment": "Aims to cover unexplored areas while maintaining reasonable path cost and promoting diversity."
}
```
2025-07-04 21:51:48,742 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:51:48,742 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:51:48,743 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:51:48,743 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:51:48,743 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:51:48,744 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15811.0 (rank 10.0%)
- Population diversity: 0.7415384615384616
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:51:48,744 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:51:48,746 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:51:54,625 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 12, 21, 35, 48, 55, 60, 64, 65, 63, 59, 54, 50, 44, 38, 30, 24, 18, 10, 5, 13, 22, 36, 49, 56, 61, 62, 58, 53, 47, 41, 33, 27, 20, 14, 8, 2, 6, 11, 19, 26, 32, 39, 45, 51, 57, 3, 7, 15, 23, 29, 37, 43, 52, 46, 40, 34, 28, 25, 17, 9, 1, 16, 31, 42],
  "targeted_regions": "Utilizes opportunity regions and sparse cells in the lower-left quadrant. Avoids difficult areas.",
  "strategy_comment": "Explored sparse cells adjacent to opportunity regions, while trying to balance cost and diversity."
}
```
2025-07-04 21:51:54,626 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:51:54,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61334.0, 路径: [0, 4, 12, 21, 35, 48, 55, 60, 64, 65, 63, 59, 54, 50, 44, 38, 30, 24, 18, 10, 5, 13, 22, 36, 49, 56, 61, 62, 58, 53, 47, 41, 33, 27, 20, 14, 8, 2, 6, 11, 19, 26, 32, 39, 45, 51, 57, 3, 7, 15, 23, 29, 37, 43, 52, 46, 40, 34, 28, 25, 17, 9, 1, 16, 31, 42]
2025-07-04 21:51:54,627 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 4, 12, 21, 35, 48, 55, 60, 64, 65, 63, 59, 54, 50, 44, 38, 30, 24, 18, 10, 5, 13, 22, 36, 49, 56, 61, 62, 58, 53, 47, 41, 33, 27, 20, 14, 8, 2, 6, 11, 19, 26, 32, 39, 45, 51, 57, 3, 7, 15, 23, 29, 37, 43, 52, 46, 40, 34, 28, 25, 17, 9, 1, 16, 31, 42], 'cur_cost': 61334.0}
2025-07-04 21:51:54,627 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:51:54,627 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:51:54,628 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17673.0 (rank 10.0%)
- Population diversity: 0.8107692307692308
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:51:54,628 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:51:54,628 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:51:58,625 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
    20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37,
    38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55,
    56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "targeted_regions": "Explores opportunity regions, avoiding difficult areas and sparse cells.",
  "strategy_comment": "Prioritizes exploration of the landscape, focusing on opportunity regions while avoiding problem areas."
}
```
2025-07-04 21:51:58,626 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:51:58,627 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:51:58,627 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:51:58,627 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-04 21:51:58,628 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:51:58,628 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:51:58,628 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 106757.0
2025-07-04 21:51:59,132 - ExploitationExpert - INFO - res_population_num: 31
2025-07-04 21:51:59,132 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015]
2025-07-04 21:51:59,132 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64)]
2025-07-04 21:51:59,143 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:51:59,143 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 6, 11, 18, 25, 32, 39, 46, 53, 60, 61, 62, 63, 64, 65, 59, 52, 45, 38, 31, 24, 17, 10, 5, 2, 3, 4, 7, 8, 9, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 54, 55, 56, 57, 58], 'cur_cost': 25906.0}, {'tour': [0, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 61, 57, 53, 49, 45, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 63, 59, 55, 51, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 65], 'cur_cost': 34121.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 4, 12, 21, 35, 48, 55, 60, 64, 65, 63, 59, 54, 50, 44, 38, 30, 24, 18, 10, 5, 13, 22, 36, 49, 56, 61, 62, 58, 53, 47, 41, 33, 27, 20, 14, 8, 2, 6, 11, 19, 26, 32, 39, 45, 51, 57, 3, 7, 15, 23, 29, 37, 43, 52, 46, 40, 34, 28, 25, 17, 9, 1, 16, 31, 42], 'cur_cost': 61334.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 12, 60, 32, 46, 41, 13,  9, 45,  4,  7,  6, 56,  2, 64, 28, 58,
       34, 18,  5, 63, 29, 37, 10, 27, 49, 51, 59, 43, 42, 35, 38, 39, 31,
       53,  8, 23, 48, 54, 50, 16, 65, 24, 11, 61, 57, 19, 22, 55, 26, 15,
       20, 30, 14, 47, 25, 44,  1, 52,  3, 40,  0, 62, 33, 17, 36]), 'cur_cost': 106757.0}, {'tour': array([38, 43, 42, 52,  0, 11, 65,  4, 10, 19, 27,  2, 17, 64, 16, 35, 46,
       54, 44, 53, 25, 22, 29,  6, 55,  5, 34, 32, 20, 24, 26,  8, 59, 41,
       13, 33, 50, 61, 40, 31, 23,  1, 57, 28, 21, 36, 56, 14, 15, 18, 48,
        7, 39, 30, 63, 37, 58, 62, 49, 47, 51, 45, 12,  9,  3, 60]), 'cur_cost': 100683.0}, {'tour': array([64, 60, 22, 51, 38, 61, 46, 59,  1, 55, 30,  2, 19, 31,  4, 48, 50,
       13, 57, 56, 24, 39, 34, 16, 10, 36, 25, 37, 65, 41, 26, 21, 53, 63,
       49, 14, 58, 15,  5,  9, 27, 11,  3, 32, 54, 17,  8, 35, 20,  7, 29,
       12, 62, 28, 44, 43,  0, 42, 23,  6, 18, 33, 40, 47, 52, 45]), 'cur_cost': 109155.0}, {'tour': array([38,  6, 46, 26, 12, 49, 34, 59, 15, 52, 29, 42, 28,  3,  7, 44, 24,
        8,  9, 39,  1, 33,  2, 36, 47, 64, 20, 54, 41, 55, 13,  4, 48, 18,
       23, 22, 60, 27, 21, 32, 56, 43, 30, 25, 63, 14, 45, 53, 62, 16, 31,
       37, 58, 57, 11, 10, 19, 65,  5, 61,  0, 40, 17, 50, 51, 35]), 'cur_cost': 114639.0}, {'tour': array([35, 51, 65, 46, 43,  8,  1, 20, 55, 64, 14, 25,  0,  4, 59, 52, 32,
       57, 30, 10, 40, 54, 48, 18, 60, 50, 12, 37, 53, 44,  5, 36, 62, 16,
       19, 11, 45,  6, 56, 34, 38, 39, 28,  2, 47, 17, 42, 27, 26, 24, 31,
       29, 49, 63, 13, 23, 58, 41, 21,  7, 15, 61,  3,  9, 22, 33]), 'cur_cost': 108295.0}]
2025-07-04 21:51:59,145 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:51:59,146 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-07-04 21:51:59,146 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-04 21:51:59,146 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 21:51:59,146 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:51:59,147 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100683.0 (rank 50.0%)
- Population diversity: 0.7384615384615385
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:51:59,147 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:51:59,147 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:52:04,424 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Explore entire graph, focus on sparse cells via traversal.",
  "strategy_comment": "Prioritizing exploration of the entire graph structure to identify and leverage sparse cells."
}
```
2025-07-04 21:52:04,425 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:52:04,425 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:52:04,426 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:52:04,426 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 21:52:04,427 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:52:04,427 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:52:04,427 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 100048.0
2025-07-04 21:52:04,930 - ExploitationExpert - INFO - res_population_num: 33
2025-07-04 21:52:04,930 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015, 9521, 9521]
2025-07-04 21:52:04,930 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:52:04,942 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:52:04,943 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 6, 11, 18, 25, 32, 39, 46, 53, 60, 61, 62, 63, 64, 65, 59, 52, 45, 38, 31, 24, 17, 10, 5, 2, 3, 4, 7, 8, 9, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 54, 55, 56, 57, 58], 'cur_cost': 25906.0}, {'tour': [0, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 61, 57, 53, 49, 45, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 63, 59, 55, 51, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 65], 'cur_cost': 34121.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 4, 12, 21, 35, 48, 55, 60, 64, 65, 63, 59, 54, 50, 44, 38, 30, 24, 18, 10, 5, 13, 22, 36, 49, 56, 61, 62, 58, 53, 47, 41, 33, 27, 20, 14, 8, 2, 6, 11, 19, 26, 32, 39, 45, 51, 57, 3, 7, 15, 23, 29, 37, 43, 52, 46, 40, 34, 28, 25, 17, 9, 1, 16, 31, 42], 'cur_cost': 61334.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 12, 60, 32, 46, 41, 13,  9, 45,  4,  7,  6, 56,  2, 64, 28, 58,
       34, 18,  5, 63, 29, 37, 10, 27, 49, 51, 59, 43, 42, 35, 38, 39, 31,
       53,  8, 23, 48, 54, 50, 16, 65, 24, 11, 61, 57, 19, 22, 55, 26, 15,
       20, 30, 14, 47, 25, 44,  1, 52,  3, 40,  0, 62, 33, 17, 36]), 'cur_cost': 106757.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([50, 44, 10, 33, 25,  4, 20, 15, 54,  8, 60, 11, 24, 14,  5, 19, 63,
       22, 43, 27, 51, 26, 46, 45, 40, 41, 64,  3, 16, 56,  2,  7, 34, 47,
       17, 52, 65, 23, 28,  0, 29, 61, 55, 53, 42, 48, 36, 35, 21, 12, 57,
       37,  1, 58, 30, 39, 13, 32, 38,  9, 59, 31, 62, 49, 18,  6]), 'cur_cost': 100048.0}, {'tour': array([38,  6, 46, 26, 12, 49, 34, 59, 15, 52, 29, 42, 28,  3,  7, 44, 24,
        8,  9, 39,  1, 33,  2, 36, 47, 64, 20, 54, 41, 55, 13,  4, 48, 18,
       23, 22, 60, 27, 21, 32, 56, 43, 30, 25, 63, 14, 45, 53, 62, 16, 31,
       37, 58, 57, 11, 10, 19, 65,  5, 61,  0, 40, 17, 50, 51, 35]), 'cur_cost': 114639.0}, {'tour': array([35, 51, 65, 46, 43,  8,  1, 20, 55, 64, 14, 25,  0,  4, 59, 52, 32,
       57, 30, 10, 40, 54, 48, 18, 60, 50, 12, 37, 53, 44,  5, 36, 62, 16,
       19, 11, 45,  6, 56, 34, 38, 39, 28,  2, 47, 17, 42, 27, 26, 24, 31,
       29, 49, 63, 13, 23, 58, 41, 21,  7, 15, 61,  3,  9, 22, 33]), 'cur_cost': 108295.0}]
2025-07-04 21:52:04,945 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:52:04,945 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-07-04 21:52:04,945 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 21:52:04,945 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:52:04,945 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:52:04,946 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:52:04,946 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 112307.0
2025-07-04 21:52:05,449 - ExploitationExpert - INFO - res_population_num: 34
2025-07-04 21:52:05,450 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015, 9521, 9521, 9521]
2025-07-04 21:52:05,450 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:52:05,461 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:52:05,461 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 6, 11, 18, 25, 32, 39, 46, 53, 60, 61, 62, 63, 64, 65, 59, 52, 45, 38, 31, 24, 17, 10, 5, 2, 3, 4, 7, 8, 9, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 54, 55, 56, 57, 58], 'cur_cost': 25906.0}, {'tour': [0, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 61, 57, 53, 49, 45, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 63, 59, 55, 51, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 65], 'cur_cost': 34121.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 4, 12, 21, 35, 48, 55, 60, 64, 65, 63, 59, 54, 50, 44, 38, 30, 24, 18, 10, 5, 13, 22, 36, 49, 56, 61, 62, 58, 53, 47, 41, 33, 27, 20, 14, 8, 2, 6, 11, 19, 26, 32, 39, 45, 51, 57, 3, 7, 15, 23, 29, 37, 43, 52, 46, 40, 34, 28, 25, 17, 9, 1, 16, 31, 42], 'cur_cost': 61334.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 12, 60, 32, 46, 41, 13,  9, 45,  4,  7,  6, 56,  2, 64, 28, 58,
       34, 18,  5, 63, 29, 37, 10, 27, 49, 51, 59, 43, 42, 35, 38, 39, 31,
       53,  8, 23, 48, 54, 50, 16, 65, 24, 11, 61, 57, 19, 22, 55, 26, 15,
       20, 30, 14, 47, 25, 44,  1, 52,  3, 40,  0, 62, 33, 17, 36]), 'cur_cost': 106757.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([50, 44, 10, 33, 25,  4, 20, 15, 54,  8, 60, 11, 24, 14,  5, 19, 63,
       22, 43, 27, 51, 26, 46, 45, 40, 41, 64,  3, 16, 56,  2,  7, 34, 47,
       17, 52, 65, 23, 28,  0, 29, 61, 55, 53, 42, 48, 36, 35, 21, 12, 57,
       37,  1, 58, 30, 39, 13, 32, 38,  9, 59, 31, 62, 49, 18,  6]), 'cur_cost': 100048.0}, {'tour': array([40, 34, 52, 24, 13, 18, 46, 42, 35, 53, 21,  9, 41, 31, 11, 28, 60,
       10, 23, 15, 33, 37, 36,  1, 43, 22, 45, 30, 44, 51, 58, 49,  7, 16,
        4,  2, 48, 63, 20, 59, 54, 25, 55, 47, 26, 32, 19, 50, 57,  8, 56,
        0, 17, 29, 61,  6, 27, 14, 39, 62,  3, 64, 38,  5, 12, 65]), 'cur_cost': 112307.0}, {'tour': array([35, 51, 65, 46, 43,  8,  1, 20, 55, 64, 14, 25,  0,  4, 59, 52, 32,
       57, 30, 10, 40, 54, 48, 18, 60, 50, 12, 37, 53, 44,  5, 36, 62, 16,
       19, 11, 45,  6, 56, 34, 38, 39, 28,  2, 47, 17, 42, 27, 26, 24, 31,
       29, 49, 63, 13, 23, 58, 41, 21,  7, 15, 61,  3,  9, 22, 33]), 'cur_cost': 108295.0}]
2025-07-04 21:52:05,463 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:52:05,463 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-07-04 21:52:05,464 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:52:05,464 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-04 21:52:05,464 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:52:05,466 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 108295.0 (rank 80.0%)
- Population diversity: 0.6569230769230769
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:52:05,466 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:52:05,466 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:52:10,566 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 56, 49, 42, 35, 28, 21, 14, 7, 2, 3, 4, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 10, 9, 8, 15, 16, 17, 24, 31, 38, 45, 52, 59, 65, 64, 63, 57, 50, 43, 36, 29, 22, 1, 23, 30, 37, 44, 51, 58],
  "targeted_regions": "Explores a spiral outwards, touching sparse cells.",
  "strategy_comment": "Spiral outwards from origin, ensuring sparse cells are visited early to boost exploration."
}
```
2025-07-04 21:52:10,567 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:52:10,568 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52189.0, 路径: [0, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 56, 49, 42, 35, 28, 21, 14, 7, 2, 3, 4, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 10, 9, 8, 15, 16, 17, 24, 31, 38, 45, 52, 59, 65, 64, 63, 57, 50, 43, 36, 29, 22, 23, 30, 37, 44, 51, 58]
2025-07-04 21:52:10,568 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 56, 49, 42, 35, 28, 21, 14, 7, 2, 3, 4, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 10, 9, 8, 15, 16, 17, 24, 31, 38, 45, 52, 59, 65, 64, 63, 57, 50, 43, 36, 29, 22, 23, 30, 37, 44, 51, 58], 'cur_cost': 52189.0}
2025-07-04 21:52:10,568 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 11, 18, 25, 32, 39, 46, 53, 60, 61, 62, 63, 64, 65, 59, 52, 45, 38, 31, 24, 17, 10, 5, 2, 3, 4, 7, 8, 9, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 47, 48, 49, 50, 51, 54, 55, 56, 57, 58], 'cur_cost': 25906.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 61, 57, 53, 49, 45, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 63, 59, 55, 51, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 65], 'cur_cost': 34121.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 12, 21, 35, 48, 55, 60, 64, 65, 63, 59, 54, 50, 44, 38, 30, 24, 18, 10, 5, 13, 22, 36, 49, 56, 61, 62, 58, 53, 47, 41, 33, 27, 20, 14, 8, 2, 6, 11, 19, 26, 32, 39, 45, 51, 57, 3, 7, 15, 23, 29, 37, 43, 52, 46, 40, 34, 28, 25, 17, 9, 1, 16, 31, 42], 'cur_cost': 61334.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 56, 49, 42, 35, 28, 21, 14, 7, 2, 3, 4, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 10, 9, 8, 15, 16, 17, 24, 31, 38, 45, 52, 59, 65, 64, 63, 57, 50, 43, 36, 29, 22, 23, 30, 37, 44, 51, 58], 'cur_cost': 52189.0}}]
2025-07-04 21:52:10,570 - __main__ - INFO - 进化阶段完成
2025-07-04 21:52:10,570 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:52:10,586 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 112307.0, 'mean': 52709.9, 'std': 38668.3373044407}, 'diversity': 0.8430976430976429, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 3, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:52:10,587 - __main__ - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-07-04 21:52:10,587 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment ---
2025-07-04 21:52:10,589 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:52:10,590 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 34 → 34
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.080 → 0.080 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:52:10,590 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:52:16,950 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {"explore": 0.0, "exploit": 0.0},
  "balance_state": "stagnant",
  "recommendations": [
    "increase_explore_rate",
    "decrease_exploit_rate",
    "increase_mutation_rate",
    "increase_diversity_weight"
  ]
}
```
2025-07-04 21:52:16,951 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:52:16,952 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-07-04 21:52:16,952 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-04 21:52:16,952 - __main__ - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-07-04 21:52:16,953 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:52:16,969 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 112307.0, 'mean': 52709.9, 'std': 38668.3373044407}, 'diversity': 0.8430976430976429, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 3, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:52:16,970 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:52:16,975 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:52:16,975 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:52:16,975 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:52:16,995 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:52:16,996 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:52:16,998 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:52:16,998 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 11479.0, mean 52709.9, max 112307.0, std 38668.3373044407
- diversity: 0.8430976430976429
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.2595473451088425}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:52:16,999 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:52:25,118 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.3,
    "modality": "likely multi-modal",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.843,
    "convergence": 0.0,
    "clustering": "No clustering information"
  },
  "difficult_regions": [
    "Potential difficult regions: High edge lengths (q3=2469.0, max=3032.0) and a long edge ratio of 0.249 suggest possible bottlenecks or long-distance connections. These might indicate 'corridors' that are hard to traverse efficiently, or that are disrupted by local optima."
  ],
  "opportunity_regions": [
    "Potential opportunity regions: High-density cells in the density grid (e.g., those with 14, 12, or 14 nodes) can indicate areas where edges or clusters of nodes are tightly packed. These regions might be good areas to exploit for improvements.",
    "Consider exploiting the density of nodes within cells of the density grid to construct or refine tour paths"
  ],
  "evolution_phase": "Exploration/Early stage",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Explore operator suggestions: Consider mutation operators that introduce large changes to the current solutions to overcome potential long-distance bottleneck edges.",
      "Explore operator suggestions: Local search operations focused on high-density regions to efficiently improve paths in the identified opportunity areas.",
      "Exploration through diversifying operators to sample different edge combinations while maintaining the current degree of diversity (0.843)."
    ]
  }
}
```
2025-07-04 21:52:25,119 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:52:25,119 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.3, 'modality': 'likely multi-modal', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.843, 'convergence': 0.0, 'clustering': 'No clustering information'}, 'difficult_regions': ["Potential difficult regions: High edge lengths (q3=2469.0, max=3032.0) and a long edge ratio of 0.249 suggest possible bottlenecks or long-distance connections. These might indicate 'corridors' that are hard to traverse efficiently, or that are disrupted by local optima."], 'opportunity_regions': ['Potential opportunity regions: High-density cells in the density grid (e.g., those with 14, 12, or 14 nodes) can indicate areas where edges or clusters of nodes are tightly packed. These regions might be good areas to exploit for improvements.', 'Consider exploiting the density of nodes within cells of the density grid to construct or refine tour paths'], 'evolution_phase': 'Exploration/Early stage', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Explore operator suggestions: Consider mutation operators that introduce large changes to the current solutions to overcome potential long-distance bottleneck edges.', 'Explore operator suggestions: Local search operations focused on high-density regions to efficiently improve paths in the identified opportunity areas.', 'Exploration through diversifying operators to sample different edge combinations while maintaining the current degree of diversity (0.843).']}}
2025-07-04 21:52:25,120 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:52:25,120 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:52:25,121 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.843
  • best_cost: 11479.0
  • mean_cost: 52709.9
- Search-space: ruggedness 0.3, modality likely multi-modal, deceptiveness unknown
- Evolution phase: Exploration/Early stage
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 1): Potential difficult 
- Opportunity regions (# 2): Potential opportunit, Consider exploiting 
- Last-iteration feedback: ```json
{
  "evolution_stage": "early_stagnation",
  "strategy_effectiveness": {"explore": 0.0, "exploit": 0.0},
  "bala...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:52:25,121 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:52:25,122 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:52:29,069 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early stage, low diversity, and explore is recommended. High explore ratio prioritizes diversification of solutions to discover new regions of the search space."
}
```
2025-07-04 21:52:29,071 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-07-04 21:52:29,071 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-07-04 21:52:29,071 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early stage, low diversity, and explore is recommended. High explore ratio prioritizes diversification of solutions to discover new regions of the search space."
}
```
2025-07-04 21:52:29,072 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:52:29,072 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-07-04 21:52:29,072 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early stage, low diversity, and explore is recommended. High explore ratio prioritizes diversification of solutions to discover new regions of the search space."
}
```
2025-07-04 21:52:29,073 - __main__ - INFO - 开始进化阶段
2025-07-04 21:52:29,073 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:52:29,073 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:52:29,074 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 25906.0 (rank 30.0%)
- Population diversity: 0.6169230769230769
- Evolution phase: Exploration/Early stage
- Landscape recommends focus: explore
- Opportunity regions (sample): Potential oppor, Consider exploi
- Difficult regions to avoid (sample): Potential diffi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:52:29,074 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:52:29,083 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:52:34,918 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65],
  "targeted_regions": "Opportunity regions and explores sparse cells systematically.",
  "strategy_comment": "Systematically explores all nodes, emphasizing low-density areas identified as opportunities, and maximizing diversity."
}
```
2025-07-04 21:52:34,919 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:52:34,919 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19756.0, 路径: [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65]
2025-07-04 21:52:34,919 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}
2025-07-04 21:52:34,921 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:52:34,921 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:52:34,921 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 34121.0 (rank 40.0%)
- Population diversity: 0.696923076923077
- Evolution phase: Exploration/Early stage
- Landscape recommends focus: explore
- Opportunity regions (sample): Potential oppor, Consider exploi
- Difficult regions to avoid (sample): Potential diffi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:52:34,922 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:52:34,922 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:52:38,213 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [23, 12, 34, 45, 56, 7, 18, 29, 40, 51, 62, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 5, 16, 27, 38, 49, 60, 1, 10, 21, 32, 43, 54, 65, 6, 17, 28, 39, 50, 61, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 63, 4, 15, 26, 37, 48, 59, 0, 11, 22, 33, 44, 55],
  "targeted_regions": "Exploits sparse cells and opportunity regions, avoiding difficult areas.",
  "strategy_comment": "Prioritizes exploration of low-density areas to maximize diversity and potential for optimization."
}
```
2025-07-04 21:52:38,214 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:52:38,214 - ExplorationExpert - INFO - 探索路径生成完成，成本: 92951.0, 路径: [23, 12, 34, 45, 56, 7, 18, 29, 40, 51, 62, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 5, 16, 27, 38, 49, 60, 1, 10, 21, 32, 43, 54, 65, 6, 17, 28, 39, 50, 61, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 63, 4, 15, 26, 37, 48, 59, 0, 11, 22, 33, 44, 55]
2025-07-04 21:52:38,216 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [23, 12, 34, 45, 56, 7, 18, 29, 40, 51, 62, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 5, 16, 27, 38, 49, 60, 1, 10, 21, 32, 43, 54, 65, 6, 17, 28, 39, 50, 61, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 63, 4, 15, 26, 37, 48, 59, 0, 11, 22, 33, 44, 55], 'cur_cost': 92951.0}
2025-07-04 21:52:38,216 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:52:38,216 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:52:38,217 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.7076923076923077
- Evolution phase: Exploration/Early stage
- Landscape recommends focus: explore
- Opportunity regions (sample): Potential oppor, Consider exploi
- Difficult regions to avoid (sample): Potential diffi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:52:38,217 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:52:38,218 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:52:45,943 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [1, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 61, 62, 63, 64, 65, 0],
  "targeted_regions": "sparse cells 58-65, opportunity region (1,60)",
  "strategy_comment": "Explores the periphery, targeting sparse cells adjacent to opportunity regions to increase diversity."
}
```
2025-07-04 21:52:45,944 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:52:45,945 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13394.0, 路径: [1, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 61, 62, 63, 64, 65, 0]
2025-07-04 21:52:45,945 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [1, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 61, 62, 63, 64, 65, 0], 'cur_cost': 13394.0}
2025-07-04 21:52:45,945 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:52:45,946 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:52:45,946 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 61334.0 (rank 50.0%)
- Population diversity: 0.7092307692307692
- Evolution phase: Exploration/Early stage
- Landscape recommends focus: explore
- Opportunity regions (sample): Potential oppor, Consider exploi
- Difficult regions to avoid (sample): Potential diffi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:52:45,947 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:52:45,947 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:52:50,646 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63],
  "targeted_regions": "Sparse cells along diagonal, opportunity regions",
  "strategy_comment": "Prioritized sparse cells & opportunity regions to increase diversity. Avoids difficult areas."
}
```
2025-07-04 21:52:50,647 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:52:50,648 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25941.0, 路径: [1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63]
2025-07-04 21:52:50,649 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25941.0}
2025-07-04 21:52:50,649 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:52:50,649 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:52:50,650 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.7292307692307692
- Evolution phase: Exploration/Early stage
- Landscape recommends focus: explore
- Opportunity regions (sample): Potential oppor, Consider exploi
- Difficult regions to avoid (sample): Potential diffi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:52:50,650 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:52:50,651 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:52:55,241 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19,
    20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37,
    38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55,
    56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "targeted_regions": "Opportunity and Sparse cell exploration",
  "strategy_comment": "Initial path, exploring all nodes to increase diversity, targeting opportunity regions."
}
```
2025-07-04 21:52:55,241 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:52:55,242 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:52:55,242 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:52:55,242 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-04 21:52:55,243 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:52:55,243 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:52:55,243 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 102221.0
2025-07-04 21:52:55,747 - ExploitationExpert - INFO - res_population_num: 34
2025-07-04 21:52:55,747 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015]
2025-07-04 21:52:55,748 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64)]
2025-07-04 21:52:55,759 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:52:55,759 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}, {'tour': [23, 12, 34, 45, 56, 7, 18, 29, 40, 51, 62, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 5, 16, 27, 38, 49, 60, 1, 10, 21, 32, 43, 54, 65, 6, 17, 28, 39, 50, 61, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 63, 4, 15, 26, 37, 48, 59, 0, 11, 22, 33, 44, 55], 'cur_cost': 92951.0}, {'tour': [1, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 61, 62, 63, 64, 65, 0], 'cur_cost': 13394.0}, {'tour': [1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25941.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 9, 38, 40, 42, 43, 11, 54,  8, 12, 50,  6,  3, 51, 60, 13, 45, 48,
       15, 56, 34, 24,  7, 41, 58, 30, 20, 55, 47, 61, 52, 35, 65, 63, 21,
       36, 31,  0, 57,  5, 62, 27, 49, 10, 32, 44, 22, 19, 28, 59, 23,  2,
       16, 46, 14, 33, 18,  4, 26, 29, 25, 39,  1, 64, 53, 17, 37]), 'cur_cost': 102221.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([50, 44, 10, 33, 25,  4, 20, 15, 54,  8, 60, 11, 24, 14,  5, 19, 63,
       22, 43, 27, 51, 26, 46, 45, 40, 41, 64,  3, 16, 56,  2,  7, 34, 47,
       17, 52, 65, 23, 28,  0, 29, 61, 55, 53, 42, 48, 36, 35, 21, 12, 57,
       37,  1, 58, 30, 39, 13, 32, 38,  9, 59, 31, 62, 49, 18,  6]), 'cur_cost': 100048.0}, {'tour': array([40, 34, 52, 24, 13, 18, 46, 42, 35, 53, 21,  9, 41, 31, 11, 28, 60,
       10, 23, 15, 33, 37, 36,  1, 43, 22, 45, 30, 44, 51, 58, 49,  7, 16,
        4,  2, 48, 63, 20, 59, 54, 25, 55, 47, 26, 32, 19, 50, 57,  8, 56,
        0, 17, 29, 61,  6, 27, 14, 39, 62,  3, 64, 38,  5, 12, 65]), 'cur_cost': 112307.0}, {'tour': [0, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 56, 49, 42, 35, 28, 21, 14, 7, 2, 3, 4, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 10, 9, 8, 15, 16, 17, 24, 31, 38, 45, 52, 59, 65, 64, 63, 57, 50, 43, 36, 29, 22, 23, 30, 37, 44, 51, 58], 'cur_cost': 52189.0}]
2025-07-04 21:52:55,761 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:52:55,761 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-07-04 21:52:55,761 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-04 21:52:55,761 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 21:52:55,761 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:52:55,762 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:52:55,762 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 114061.0
2025-07-04 21:52:56,266 - ExploitationExpert - INFO - res_population_num: 34
2025-07-04 21:52:56,267 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015]
2025-07-04 21:52:56,267 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64)]
2025-07-04 21:52:56,278 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:52:56,278 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}, {'tour': [23, 12, 34, 45, 56, 7, 18, 29, 40, 51, 62, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 5, 16, 27, 38, 49, 60, 1, 10, 21, 32, 43, 54, 65, 6, 17, 28, 39, 50, 61, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 63, 4, 15, 26, 37, 48, 59, 0, 11, 22, 33, 44, 55], 'cur_cost': 92951.0}, {'tour': [1, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 61, 62, 63, 64, 65, 0], 'cur_cost': 13394.0}, {'tour': [1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25941.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 9, 38, 40, 42, 43, 11, 54,  8, 12, 50,  6,  3, 51, 60, 13, 45, 48,
       15, 56, 34, 24,  7, 41, 58, 30, 20, 55, 47, 61, 52, 35, 65, 63, 21,
       36, 31,  0, 57,  5, 62, 27, 49, 10, 32, 44, 22, 19, 28, 59, 23,  2,
       16, 46, 14, 33, 18,  4, 26, 29, 25, 39,  1, 64, 53, 17, 37]), 'cur_cost': 102221.0}, {'tour': array([ 5, 40, 16, 32, 57, 59, 17, 47, 13, 60, 30, 53, 12,  1, 29, 54, 27,
       48,  8,  7, 41, 62, 26, 34, 45, 24, 42, 33, 37, 43, 19, 39, 18,  4,
       51, 65, 36, 28, 22, 31, 10, 58,  9, 61, 44, 25, 21, 52, 63, 38,  0,
        6, 56, 20,  3, 35, 11, 64, 23,  2, 15, 46, 14, 55, 49, 50]), 'cur_cost': 114061.0}, {'tour': array([50, 44, 10, 33, 25,  4, 20, 15, 54,  8, 60, 11, 24, 14,  5, 19, 63,
       22, 43, 27, 51, 26, 46, 45, 40, 41, 64,  3, 16, 56,  2,  7, 34, 47,
       17, 52, 65, 23, 28,  0, 29, 61, 55, 53, 42, 48, 36, 35, 21, 12, 57,
       37,  1, 58, 30, 39, 13, 32, 38,  9, 59, 31, 62, 49, 18,  6]), 'cur_cost': 100048.0}, {'tour': array([40, 34, 52, 24, 13, 18, 46, 42, 35, 53, 21,  9, 41, 31, 11, 28, 60,
       10, 23, 15, 33, 37, 36,  1, 43, 22, 45, 30, 44, 51, 58, 49,  7, 16,
        4,  2, 48, 63, 20, 59, 54, 25, 55, 47, 26, 32, 19, 50, 57,  8, 56,
        0, 17, 29, 61,  6, 27, 14, 39, 62,  3, 64, 38,  5, 12, 65]), 'cur_cost': 112307.0}, {'tour': [0, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 56, 49, 42, 35, 28, 21, 14, 7, 2, 3, 4, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 10, 9, 8, 15, 16, 17, 24, 31, 38, 45, 52, 59, 65, 64, 63, 57, 50, 43, 36, 29, 22, 23, 30, 37, 44, 51, 58], 'cur_cost': 52189.0}]
2025-07-04 21:52:56,280 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:52:56,280 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-07-04 21:52:56,280 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 21:52:56,280 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 21:52:56,281 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:52:56,282 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:52:56,282 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 114400.0
2025-07-04 21:52:56,786 - ExploitationExpert - INFO - res_population_num: 34
2025-07-04 21:52:56,786 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015]
2025-07-04 21:52:56,786 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64)]
2025-07-04 21:52:56,798 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:52:56,798 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}, {'tour': [23, 12, 34, 45, 56, 7, 18, 29, 40, 51, 62, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 5, 16, 27, 38, 49, 60, 1, 10, 21, 32, 43, 54, 65, 6, 17, 28, 39, 50, 61, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 63, 4, 15, 26, 37, 48, 59, 0, 11, 22, 33, 44, 55], 'cur_cost': 92951.0}, {'tour': [1, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 61, 62, 63, 64, 65, 0], 'cur_cost': 13394.0}, {'tour': [1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25941.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 9, 38, 40, 42, 43, 11, 54,  8, 12, 50,  6,  3, 51, 60, 13, 45, 48,
       15, 56, 34, 24,  7, 41, 58, 30, 20, 55, 47, 61, 52, 35, 65, 63, 21,
       36, 31,  0, 57,  5, 62, 27, 49, 10, 32, 44, 22, 19, 28, 59, 23,  2,
       16, 46, 14, 33, 18,  4, 26, 29, 25, 39,  1, 64, 53, 17, 37]), 'cur_cost': 102221.0}, {'tour': array([ 5, 40, 16, 32, 57, 59, 17, 47, 13, 60, 30, 53, 12,  1, 29, 54, 27,
       48,  8,  7, 41, 62, 26, 34, 45, 24, 42, 33, 37, 43, 19, 39, 18,  4,
       51, 65, 36, 28, 22, 31, 10, 58,  9, 61, 44, 25, 21, 52, 63, 38,  0,
        6, 56, 20,  3, 35, 11, 64, 23,  2, 15, 46, 14, 55, 49, 50]), 'cur_cost': 114061.0}, {'tour': array([62, 11, 44, 54, 45, 47, 10,  8, 32, 19, 16,  5, 23, 43, 37, 57, 21,
        7, 33, 49, 15, 46, 55, 34, 25, 41, 40, 27, 42, 12, 31, 22, 17, 59,
       13, 52, 26, 24,  3, 38, 28,  1,  0, 63,  6,  4, 53, 39, 30, 60, 20,
       56,  2, 61, 48, 58, 18, 65, 50, 29, 64, 35,  9, 36, 51, 14]), 'cur_cost': 114400.0}, {'tour': array([40, 34, 52, 24, 13, 18, 46, 42, 35, 53, 21,  9, 41, 31, 11, 28, 60,
       10, 23, 15, 33, 37, 36,  1, 43, 22, 45, 30, 44, 51, 58, 49,  7, 16,
        4,  2, 48, 63, 20, 59, 54, 25, 55, 47, 26, 32, 19, 50, 57,  8, 56,
        0, 17, 29, 61,  6, 27, 14, 39, 62,  3, 64, 38,  5, 12, 65]), 'cur_cost': 112307.0}, {'tour': [0, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 56, 49, 42, 35, 28, 21, 14, 7, 2, 3, 4, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 10, 9, 8, 15, 16, 17, 24, 31, 38, 45, 52, 59, 65, 64, 63, 57, 50, 43, 36, 29, 22, 23, 30, 37, 44, 51, 58], 'cur_cost': 52189.0}]
2025-07-04 21:52:56,800 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:52:56,800 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-07-04 21:52:56,801 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 21:52:56,801 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:52:56,801 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:52:56,801 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:52:56,802 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 92030.0
2025-07-04 21:52:57,306 - ExploitationExpert - INFO - res_population_num: 34
2025-07-04 21:52:57,306 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9534, 9535, 9548, 9556, 9556, 9558, 94015]
2025-07-04 21:52:57,306 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 22, 14, 15, 17, 12, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       18, 17, 12, 22, 23, 16, 19, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17, 18, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 42, 48, 43, 40,
       49, 47, 46, 45, 50, 51, 38, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 65, 63,
       52, 64, 57, 54, 60, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 16, 18, 17, 12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1],
      dtype=int64), array([ 0, 49,  5, 40, 48, 33,  9, 41, 17,  4, 62, 14, 36, 26, 45, 20, 15,
       37,  3, 38, 50, 44, 23, 13, 35, 42,  8, 19, 53, 12, 52, 63, 18,  7,
       25, 28, 51, 30, 34, 27, 31,  6, 47, 54,  2, 64, 65, 22,  1, 24, 60,
       58, 57, 56, 29, 61, 46, 59, 55, 10, 21, 43, 16, 39, 32, 11],
      dtype=int64)]
2025-07-04 21:52:57,319 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:52:57,319 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}, {'tour': [23, 12, 34, 45, 56, 7, 18, 29, 40, 51, 62, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 5, 16, 27, 38, 49, 60, 1, 10, 21, 32, 43, 54, 65, 6, 17, 28, 39, 50, 61, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 63, 4, 15, 26, 37, 48, 59, 0, 11, 22, 33, 44, 55], 'cur_cost': 92951.0}, {'tour': [1, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 61, 62, 63, 64, 65, 0], 'cur_cost': 13394.0}, {'tour': [1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25941.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 9, 38, 40, 42, 43, 11, 54,  8, 12, 50,  6,  3, 51, 60, 13, 45, 48,
       15, 56, 34, 24,  7, 41, 58, 30, 20, 55, 47, 61, 52, 35, 65, 63, 21,
       36, 31,  0, 57,  5, 62, 27, 49, 10, 32, 44, 22, 19, 28, 59, 23,  2,
       16, 46, 14, 33, 18,  4, 26, 29, 25, 39,  1, 64, 53, 17, 37]), 'cur_cost': 102221.0}, {'tour': array([ 5, 40, 16, 32, 57, 59, 17, 47, 13, 60, 30, 53, 12,  1, 29, 54, 27,
       48,  8,  7, 41, 62, 26, 34, 45, 24, 42, 33, 37, 43, 19, 39, 18,  4,
       51, 65, 36, 28, 22, 31, 10, 58,  9, 61, 44, 25, 21, 52, 63, 38,  0,
        6, 56, 20,  3, 35, 11, 64, 23,  2, 15, 46, 14, 55, 49, 50]), 'cur_cost': 114061.0}, {'tour': array([62, 11, 44, 54, 45, 47, 10,  8, 32, 19, 16,  5, 23, 43, 37, 57, 21,
        7, 33, 49, 15, 46, 55, 34, 25, 41, 40, 27, 42, 12, 31, 22, 17, 59,
       13, 52, 26, 24,  3, 38, 28,  1,  0, 63,  6,  4, 53, 39, 30, 60, 20,
       56,  2, 61, 48, 58, 18, 65, 50, 29, 64, 35,  9, 36, 51, 14]), 'cur_cost': 114400.0}, {'tour': array([16, 57,  5, 27, 36, 48, 23, 47, 40, 17, 35, 31, 19, 34, 24, 55, 54,
        2, 44, 26, 51,  7,  0, 12, 13, 59, 18, 45, 21, 15, 41, 49, 56, 33,
        6, 37, 63, 65, 10, 61,  9,  1, 22, 28, 38, 32,  4, 25, 42, 46, 30,
       62, 58, 20, 29, 14, 64, 60, 11, 53, 52, 43, 50,  3, 39,  8]), 'cur_cost': 92030.0}, {'tour': [0, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 56, 49, 42, 35, 28, 21, 14, 7, 2, 3, 4, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 10, 9, 8, 15, 16, 17, 24, 31, 38, 45, 52, 59, 65, 64, 63, 57, 50, 43, 36, 29, 22, 23, 30, 37, 44, 51, 58], 'cur_cost': 52189.0}]
2025-07-04 21:52:57,321 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 21:52:57,321 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-07-04 21:52:57,322 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:52:57,322 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-04 21:52:57,322 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:52:57,323 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 52189.0 (rank 40.0%)
- Population diversity: 0.803076923076923
- Evolution phase: Exploration/Early stage
- Landscape recommends focus: explore
- Opportunity regions (sample): Potential oppor, Consider exploi
- Difficult regions to avoid (sample): Potential diffi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:52:57,323 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:52:57,323 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:53:03,055 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2],
  "targeted_regions": "Sparse cells and potential oppor regions",
  "strategy_comment": "Explores perimeter, hitting sparse areas, and considering opportunity regions."
}
```
2025-07-04 21:53:03,056 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:53:03,056 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17673.0, 路径: [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2]
2025-07-04 21:53:03,057 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}
2025-07-04 21:53:03,057 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [23, 12, 34, 45, 56, 7, 18, 29, 40, 51, 62, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 5, 16, 27, 38, 49, 60, 1, 10, 21, 32, 43, 54, 65, 6, 17, 28, 39, 50, 61, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 63, 4, 15, 26, 37, 48, 59, 0, 11, 22, 33, 44, 55], 'cur_cost': 92951.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [1, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 61, 62, 63, 64, 65, 0], 'cur_cost': 13394.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25941.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2], 'cur_cost': 17673.0}}]
2025-07-04 21:53:03,057 - __main__ - INFO - 进化阶段完成
2025-07-04 21:53:03,058 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:53:03,076 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 114400.0, 'mean': 60390.6, 'std': 43442.26605783819}, 'diversity': 0.9303030303030302, 'clusters': {'clusters': 8, 'cluster_sizes': [2, 1, 2, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:53:03,076 - __main__ - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-07-04 21:53:03,077 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment ---
2025-07-04 21:53:03,079 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:53:03,079 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 34 → 34
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.080 → 0.080 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:53:03,080 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:53:06,967 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {"explore": 0.0, "exploit": 0.0},
  "balance_state": "stagnant",
  "recommendations": ["increase_mutation_rate", "increase_explore_ratio_to_0.2", "increase_diversity_weight_to_0.5"]
}
```
2025-07-04 21:53:06,968 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:53:06,968 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-07-04 21:53:06,984 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-04 21:53:06,984 - __main__ - INFO - 实例 composite13_66 处理完成
