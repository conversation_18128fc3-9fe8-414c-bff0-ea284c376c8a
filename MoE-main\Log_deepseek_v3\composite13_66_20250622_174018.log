2025-06-22 17:40:18,815 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-22 17:40:18,815 - __main__ - INFO - 开始分析阶段
2025-06-22 17:40:18,815 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:40:18,838 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9964.0, 'max': 119451.0, 'mean': 78572.2, 'std': 45204.990102421216}, 'diversity': 0.922222222222222, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:40:18,838 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9964.0, 'max': 119451.0, 'mean': 78572.2, 'std': 45204.990102421216}, 'diversity_level': 0.922222222222222, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:40:18,838 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:40:18,839 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:40:18,839 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:40:18,843 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:40:18,844 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (19, 27, 37), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (26, 36, 35), 'frequency': 0.3}, {'subpath': (36, 35, 28), 'frequency': 0.3}, {'subpath': (35, 28, 30), 'frequency': 0.3}, {'subpath': (28, 30, 34), 'frequency': 0.3}, {'subpath': (30, 34, 33), 'frequency': 0.3}, {'subpath': (34, 33, 31), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(47, 49)', 'frequency': 0.4}, {'edge': '(20, 21)', 'frequency': 0.4}, {'edge': '(19, 27)', 'frequency': 0.4}, {'edge': '(27, 37)', 'frequency': 0.4}, {'edge': '(30, 34)', 'frequency': 0.4}, {'edge': '(3, 7)', 'frequency': 0.4}, {'edge': '(1, 7)', 'frequency': 0.4}, {'edge': '(2, 6)', 'frequency': 0.4}, {'edge': '(55, 61)', 'frequency': 0.4}, {'edge': '(53, 61)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(46, 48)', 'frequency': 0.3}, {'edge': '(42, 46)', 'frequency': 0.2}, {'edge': '(13, 20)', 'frequency': 0.2}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.3}, {'edge': '(14, 15)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(3, 32)', 'frequency': 0.2}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(5, 9)', 'frequency': 0.2}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(0, 55)', 'frequency': 0.2}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(6, 9)', 'frequency': 0.2}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(46, 47)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.2}, {'edge': '(38, 45)', 'frequency': 0.2}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.2}, {'edge': '(13, 19)', 'frequency': 0.3}, {'edge': '(18, 42)', 'frequency': 0.2}, {'edge': '(18, 35)', 'frequency': 0.2}, {'edge': '(24, 35)', 'frequency': 0.2}, {'edge': '(44, 58)', 'frequency': 0.2}, {'edge': '(16, 33)', 'frequency': 0.2}, {'edge': '(13, 33)', 'frequency': 0.2}, {'edge': '(19, 23)', 'frequency': 0.2}, {'edge': '(3, 62)', 'frequency': 0.2}, {'edge': '(40, 59)', 'frequency': 0.2}, {'edge': '(30, 59)', 'frequency': 0.2}, {'edge': '(34, 52)', 'frequency': 0.2}, {'edge': '(20, 42)', 'frequency': 0.2}, {'edge': '(26, 51)', 'frequency': 0.2}, {'edge': '(28, 57)', 'frequency': 0.2}, {'edge': '(12, 43)', 'frequency': 0.2}, {'edge': '(39, 58)', 'frequency': 0.2}, {'edge': '(44, 63)', 'frequency': 0.2}, {'edge': '(44, 46)', 'frequency': 0.2}, {'edge': '(29, 36)', 'frequency': 0.2}, {'edge': '(24, 25)', 'frequency': 0.2}, {'edge': '(32, 60)', 'frequency': 0.2}, {'edge': '(41, 43)', 'frequency': 0.2}, {'edge': '(16, 54)', 'frequency': 0.2}, {'edge': '(15, 47)', 'frequency': 0.2}, {'edge': '(28, 56)', 'frequency': 0.2}, {'edge': '(1, 61)', 'frequency': 0.2}, {'edge': '(31, 48)', 'frequency': 0.2}, {'edge': '(9, 48)', 'frequency': 0.2}, {'edge': '(5, 37)', 'frequency': 0.2}, {'edge': '(45, 57)', 'frequency': 0.2}, {'edge': '(17, 38)', 'frequency': 0.2}, {'edge': '(14, 52)', 'frequency': 0.2}, {'edge': '(39, 40)', 'frequency': 0.2}, {'edge': '(4, 32)', 'frequency': 0.2}, {'edge': '(9, 16)', 'frequency': 0.2}, {'edge': '(0, 59)', 'frequency': 0.2}, {'edge': '(49, 56)', 'frequency': 0.2}, {'edge': '(19, 30)', 'frequency': 0.2}, {'edge': '(35, 63)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [35, 57, 38, 30, 55, 34], 'cost': 13858.0, 'size': 6}, {'region': [47, 62, 31, 57, 45], 'cost': 11538.0, 'size': 5}, {'region': [63, 35, 38, 64, 40], 'cost': 10762.0, 'size': 5}, {'region': [4, 50, 7, 49, 5], 'cost': 9992.0, 'size': 5}, {'region': [24, 60, 32, 62], 'cost': 8880.0, 'size': 4}]}
2025-06-22 17:40:18,845 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:40:18,845 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:40:18,845 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:40:18,845 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-22 17:40:18,845 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-22 17:40:18,846 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:40:18,846 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:40:18,846 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:40:18,846 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=9964.0, max=119451.0, mean=78572.2, std=45204.990102421216
- Diversity: 0.922222222222222, Convergence: 0.0
- Structure: 0 high quality edges, 10 common subpaths
- Elite: 0 solutions with 0 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 5 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:40:25,241 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High. The large standard deviation of costs (45204.99) relative to the mean (78572.2) and the presence of identified difficult regions suggest a highly rugged search space. This implies the presence of many local optima and plateaus, making it difficult to escape and find the global optimum.",
    "modality": "Potentially multimodal. The identification of 5 difficult regions and the absence of elite solutions strongly suggest a multimodal search space. The search space likely contains multiple peaks and valleys, making it difficult for the algorithm to converge to a single optimal solution.",
    "other_characteristics": "The search space seems fragmented. The lack of high-quality edges and a high diversity with low convergence suggests that solutions may be scattered and not clustered around potential optima."
  },
  "population_state": {
    "diversity": "High (0.922). The population exhibits high diversity, meaning individuals are spread out across the search space.",
    "convergence": "Zero (0.0). There is no convergence, which is consistent with the lack of elite solutions and high diversity. The solutions have not concentrated around any particular area, preventing convergence.",
    "assessment": "The population is currently exploring the search space without significant exploitation of promising areas. The high diversity and low convergence point to the need for a strategy that promotes focused exploration and exploitation."
  },
  "difficult_regions": {
    "description": "5 difficult regions have been identified. These regions likely represent areas with local optima, plateaus, or other deceptive landscapes where the optimization algorithm might get stuck or struggle to make progress.",
    "challenges": [
      "Escaping local optima: The algorithm needs to develop mechanisms to escape from these difficult regions.",
      "Avoiding premature convergence: The high diversity suggests a need for mechanisms to prevent the population from prematurely converging within one of these regions.",
      "Identifying and prioritizing the most promising difficult region: The algorithm needs to determine which of the 5 difficult regions holds the most potential for improvement."
    ]
  },
  "opportunity_regions": {
    "description": "No opportunity regions have been identified. This suggests that the algorithm may not have found any particularly promising areas yet.",
    "potential": "The absence of opportunity regions may also reflect a lack of focused exploration and exploitation; it may be the algorithm has yet to discover and assess a favorable portion of the search space.",
    "strategy": "The algorithm should prioritize identifying and exploring potential opportunity regions by carefully navigating the more challenging landscape."
  },
  "evolution_direction": {
    "strategy": "Balance exploration and exploitation with a focus on convergence. The high diversity and zero convergence necessitates a two-pronged approach. The algorithm has not yet found promising areas, so it must continue its exploration of the search space. Simultaneously, it needs to shift its focus towards identifying and exploiting potentially superior regions to move toward a solution.",
    "recommendations": [
      "Increase selection pressure: Enhance the selection process to favor higher-quality solutions. This may involve using a more aggressive selection mechanism.",
      "Improve exploitation: Explore regions with higher quality solutions more thoroughly to identify local optima. This may involve local search techniques or more focused mutation/crossover.",
      "Introduce elitism: Preserve the best performing solutions (if any are found) across generations.",
      "Focus on difficult regions: Investigate and try to resolve the issues presented by the 5 identified difficult regions. Try to determine if any regions are linked to potential opportunity regions.",
      "Adapt mutation/crossover operators: Experiment with mutation/crossover operators that encourage the exploration of more beneficial regions.",
      "Consider the common subpaths: Investigate how these common subpaths play a role in the solutions' structure and if they can provide a foundation for exploitation and refinement."
    ]
  }
}
```
2025-06-22 17:40:25,243 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:40:25,243 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High. The large standard deviation of costs (45204.99) relative to the mean (78572.2) and the presence of identified difficult regions suggest a highly rugged search space. This implies the presence of many local optima and plateaus, making it difficult to escape and find the global optimum.', 'modality': 'Potentially multimodal. The identification of 5 difficult regions and the absence of elite solutions strongly suggest a multimodal search space. The search space likely contains multiple peaks and valleys, making it difficult for the algorithm to converge to a single optimal solution.', 'other_characteristics': 'The search space seems fragmented. The lack of high-quality edges and a high diversity with low convergence suggests that solutions may be scattered and not clustered around potential optima.'}, 'population_state': {'diversity': 'High (0.922). The population exhibits high diversity, meaning individuals are spread out across the search space.', 'convergence': 'Zero (0.0). There is no convergence, which is consistent with the lack of elite solutions and high diversity. The solutions have not concentrated around any particular area, preventing convergence.', 'assessment': 'The population is currently exploring the search space without significant exploitation of promising areas. The high diversity and low convergence point to the need for a strategy that promotes focused exploration and exploitation.'}, 'difficult_regions': {'description': '5 difficult regions have been identified. These regions likely represent areas with local optima, plateaus, or other deceptive landscapes where the optimization algorithm might get stuck or struggle to make progress.', 'challenges': ['Escaping local optima: The algorithm needs to develop mechanisms to escape from these difficult regions.', 'Avoiding premature convergence: The high diversity suggests a need for mechanisms to prevent the population from prematurely converging within one of these regions.', 'Identifying and prioritizing the most promising difficult region: The algorithm needs to determine which of the 5 difficult regions holds the most potential for improvement.']}, 'opportunity_regions': {'description': 'No opportunity regions have been identified. This suggests that the algorithm may not have found any particularly promising areas yet.', 'potential': 'The absence of opportunity regions may also reflect a lack of focused exploration and exploitation; it may be the algorithm has yet to discover and assess a favorable portion of the search space.', 'strategy': 'The algorithm should prioritize identifying and exploring potential opportunity regions by carefully navigating the more challenging landscape.'}, 'evolution_direction': {'strategy': 'Balance exploration and exploitation with a focus on convergence. The high diversity and zero convergence necessitates a two-pronged approach. The algorithm has not yet found promising areas, so it must continue its exploration of the search space. Simultaneously, it needs to shift its focus towards identifying and exploiting potentially superior regions to move toward a solution.', 'recommendations': ['Increase selection pressure: Enhance the selection process to favor higher-quality solutions. This may involve using a more aggressive selection mechanism.', 'Improve exploitation: Explore regions with higher quality solutions more thoroughly to identify local optima. This may involve local search techniques or more focused mutation/crossover.', 'Introduce elitism: Preserve the best performing solutions (if any are found) across generations.', 'Focus on difficult regions: Investigate and try to resolve the issues presented by the 5 identified difficult regions. Try to determine if any regions are linked to potential opportunity regions.', 'Adapt mutation/crossover operators: Experiment with mutation/crossover operators that encourage the exploration of more beneficial regions.', "Consider the common subpaths: Investigate how these common subpaths play a role in the solutions' structure and if they can provide a foundation for exploitation and refinement."]}}
2025-06-22 17:40:25,243 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:40:25,245 - __main__ - INFO - 分析阶段完成
2025-06-22 17:40:25,245 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High. The large standard deviation of costs (45204.99) relative to the mean (78572.2) and the presence of identified difficult regions suggest a highly rugged search space. This implies the presence of many local optima and plateaus, making it difficult to escape and find the global optimum.', 'modality': 'Potentially multimodal. The identification of 5 difficult regions and the absence of elite solutions strongly suggest a multimodal search space. The search space likely contains multiple peaks and valleys, making it difficult for the algorithm to converge to a single optimal solution.', 'other_characteristics': 'The search space seems fragmented. The lack of high-quality edges and a high diversity with low convergence suggests that solutions may be scattered and not clustered around potential optima.'}, 'population_state': {'diversity': 'High (0.922). The population exhibits high diversity, meaning individuals are spread out across the search space.', 'convergence': 'Zero (0.0). There is no convergence, which is consistent with the lack of elite solutions and high diversity. The solutions have not concentrated around any particular area, preventing convergence.', 'assessment': 'The population is currently exploring the search space without significant exploitation of promising areas. The high diversity and low convergence point to the need for a strategy that promotes focused exploration and exploitation.'}, 'difficult_regions': {'description': '5 difficult regions have been identified. These regions likely represent areas with local optima, plateaus, or other deceptive landscapes where the optimization algorithm might get stuck or struggle to make progress.', 'challenges': ['Escaping local optima: The algorithm needs to develop mechanisms to escape from these difficult regions.', 'Avoiding premature convergence: The high diversity suggests a need for mechanisms to prevent the population from prematurely converging within one of these regions.', 'Identifying and prioritizing the most promising difficult region: The algorithm needs to determine which of the 5 difficult regions holds the most potential for improvement.']}, 'opportunity_regions': {'description': 'No opportunity regions have been identified. This suggests that the algorithm may not have found any particularly promising areas yet.', 'potential': 'The absence of opportunity regions may also reflect a lack of focused exploration and exploitation; it may be the algorithm has yet to discover and assess a favorable portion of the search space.', 'strategy': 'The algorithm should prioritize identifying and exploring potential opportunity regions by carefully navigating the more challenging landscape.'}, 'evolution_direction': {'strategy': 'Balance exploration and exploitation with a focus on convergence. The high diversity and zero convergence necessitates a two-pronged approach. The algorithm has not yet found promising areas, so it must continue its exploration of the search space. Simultaneously, it needs to shift its focus towards identifying and exploiting potentially superior regions to move toward a solution.', 'recommendations': ['Increase selection pressure: Enhance the selection process to favor higher-quality solutions. This may involve using a more aggressive selection mechanism.', 'Improve exploitation: Explore regions with higher quality solutions more thoroughly to identify local optima. This may involve local search techniques or more focused mutation/crossover.', 'Introduce elitism: Preserve the best performing solutions (if any are found) across generations.', 'Focus on difficult regions: Investigate and try to resolve the issues presented by the 5 identified difficult regions. Try to determine if any regions are linked to potential opportunity regions.', 'Adapt mutation/crossover operators: Experiment with mutation/crossover operators that encourage the exploration of more beneficial regions.', "Consider the common subpaths: Investigate how these common subpaths play a role in the solutions' structure and if they can provide a foundation for exploitation and refinement."]}}
2025-06-22 17:40:25,245 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:40:25,246 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:40:25,246 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 0
- Population: 10 individuals, diversity=High (0.922). The population exhibits high diversity, meaning individuals are spread out across the search space.
- Best cost: 9964.0, Mean cost: 78572.2
- Search space: Ruggedness: High. The large standard deviation of costs (45204.99) relative to the mean (78572.2) and the presence of identified difficult regions suggest a highly rugged search space. This implies the presence of many local optima and plateaus, making it difficult to escape and find the global optimum., Modality: Potentially multimodal. The identification of 5 difficult regions and the absence of elite solutions strongly suggest a multimodal search space. The search space likely contains multiple peaks and valleys, making it difficult for the algorithm to converge to a single optimal solution.
- Difficult regions: 2 regions
- Opportunity regions: 3 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: No previous feedback

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:40:25,247 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:40:27,715 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "step_size": 0.2,
      "mutation_rate": 0.1,
      "local_search_iterations": 5
    },
    "exploit": {
      "step_size": 0.05,
      "mutation_rate": 0.05,
      "local_search_iterations": 15
    }
  }
}
```

2025-06-22 17:40:27,716 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-06-22 17:40:27,717 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-06-22 17:40:27,717 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "step_size": 0.2,
      "mutation_rate": 0.1,
      "local_search_iterations": 5
    },
    "exploit": {
      "step_size": 0.05,
      "mutation_rate": 0.05,
      "local_search_iterations": 15
    }
  }
}
```

2025-06-22 17:40:27,717 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:40:27,717 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-06-22 17:40:27,717 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "step_size": 0.2,
      "mutation_rate": 0.1,
      "local_search_iterations": 5
    },
    "exploit": {
      "step_size": 0.05,
      "mutation_rate": 0.05,
      "local_search_iterations": 15
    }
  }
}
```

2025-06-22 17:40:27,718 - __main__ - INFO - 开始进化阶段
2025-06-22 17:40:27,718 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:40:27,718 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:40:27,718 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [50, 38, 51] and ending with [65, 52, 63]
- Cost: 9971.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7538461538461538
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:40:27,718 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:40:30,963 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    50, 38, 51, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0
  ],
  "modification_strategy": "This strategy prioritizes diversity by starting from a similar start, then exploring the high-indexed area before zig-zagging into the lower indexes.  It avoids the difficult regions by keeping their index proximity higher.",
  "targeted_regions": "High-indexed and low-indexed regions, with an emphasis on diversifying the path's overall coverage."
}
```
2025-06-22 17:40:30,963 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:40:30,965 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15597.0, 路径: [50, 38, 51, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0]
2025-06-22 17:40:30,965 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [50, 38, 51, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 15597.0}
2025-06-22 17:40:30,965 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 17:40:30,965 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:40:30,965 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [5, 4, 8] and ending with [50, 41, 42]
- Cost: 10241.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8061538461538461
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:40:30,965 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:40:34,691 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    5, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 1, 2, 3, 0
  ],
  "modification_strategy": "This path prioritizes diversity by reversing the initial path's direction mid-way, and then exploring the unexplored regions in a zig-zag fashion.  The order of nodes within the initial and reversed segments is maintained, promoting diversity by exploring the same nodes in a different order. Finally, some random nodes are included at the beginning and the end to diversify the starting and ending regions.",
  "targeted_regions": "The strategy aims to target the unexplored regions indicated in the search context by alternating the order of nodes and using zig-zag movements and randomness to increase exploration across the map."
}
```
2025-06-22 17:40:34,691 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:40:34,693 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26036.0, 路径: [5, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 1, 2, 3, 0]
2025-06-22 17:40:34,694 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 1, 2, 3, 0], 'cur_cost': 26036.0}
2025-06-22 17:40:34,694 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 17:40:34,694 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:40:34,694 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [18, 16, 23] and ending with [43, 48, 42]
- Cost: 9964.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.86
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:40:34,694 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:40:38,136 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    18, 23, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19,
    20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38,
    39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56,
    57, 58, 59, 60, 61, 62, 63, 64, 65, 0
  ],
  "modification_strategy": "This strategy prioritizes exploration by significantly altering the initial path segment and injecting nodes from previously unexplored regions.  I aimed for a more 'scattered' exploration pattern early on to increase path diversity and targeting the opportunity regions identified in the search context.",
  "targeted_regions": "I aimed to explore opportunity regions implied by the 'unexplored regions' context (although those aren't explicitly identified, this strategy attempts to 'break away' from the starting sequence and explore different sections early in the path.)"
}
```
2025-06-22 17:40:38,137 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:40:38,137 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13971.0, 路径: [18, 23, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0]
2025-06-22 17:40:38,137 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [18, 23, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 13971.0}
2025-06-22 17:40:38,138 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 17:40:38,138 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:40:38,138 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [38, 36, 1] and ending with [25, 63, 60]
- Cost: 119451.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8184615384615385
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:40:38,138 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:40:41,412 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    38, 36, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
    19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0,
    37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55,
    56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Re-ordered a significant portion of the path to explore different segments, primarily targeting unexplored regions based on the provided context. Introduced more variance in the initial and mid sections of the path, focusing on a more 'scattered' initial exploration compared to the original. Preserved the endpoints to fulfill the problem requirement.",
  "targeted_regions": "All 3 opportunity regions. Focused on moving away from difficult regions early on to avoid suboptimal path development. Specifically, focused on node clusters identified in the path exploration, and the provided context to increase diversity in the beginning portion, and ensure a varied experience."
}
```
2025-06-22 17:40:41,412 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:40:41,412 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21194.0, 路径: [38, 36, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-22 17:40:41,412 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [38, 36, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 21194.0}
2025-06-22 17:40:41,412 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 17:40:41,412 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:40:41,412 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [58, 39, 45] and ending with [26, 13, 33]
- Cost: 110340.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7492307692307693
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:40:41,412 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:40:44,504 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    58, 39, 45, 40, 46, 51, 63, 61, 55, 62, 50, 49, 41, 37, 29, 28, 30, 32, 36, 31, 38, 47, 52, 54, 57, 59, 60, 65, 64, 56, 53, 48, 44, 42, 43, 35, 34, 27, 26, 13, 33, 1, 2, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25
  ],
  "modification_strategy": "Prioritized exploration of unexplored regions and high-risk zones by randomizing the order of those nodes and applying a diversification approach to the beginning and end of the path while maintaining the general structure of the original path but shuffled for better exploration.",
  "targeted_regions": "Unexplored regions (specific nodes randomly rearranged), the beginning and end of original path, and high-risk nodes."
}
```
2025-06-22 17:40:44,504 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:40:44,504 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32884.0, 路径: [58, 39, 45, 40, 46, 51, 63, 61, 55, 62, 50, 49, 41, 37, 29, 28, 30, 32, 36, 31, 38, 47, 52, 54, 57, 59, 60, 65, 64, 56, 53, 48, 44, 42, 43, 35, 34, 27, 26, 13, 33, 1, 2, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]
2025-06-22 17:40:44,504 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [58, 39, 45, 40, 46, 51, 63, 61, 55, 62, 50, 49, 41, 37, 29, 28, 30, 32, 36, 31, 38, 47, 52, 54, 57, 59, 60, 65, 64, 56, 53, 48, 44, 42, 43, 35, 34, 27, 26, 13, 33, 1, 2, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], 'cur_cost': 32884.0}
2025-06-22 17:40:44,504 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 17:40:44,504 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:40:44,508 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:40:44,509 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 100939.0
2025-06-22 17:40:46,151 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 17:40:46,151 - ExploitationExpert - INFO - res_population_costs: [9557]
2025-06-22 17:40:46,152 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:40:46,153 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:40:46,153 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 15597.0}, {'tour': [5, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 1, 2, 3, 0], 'cur_cost': 26036.0}, {'tour': [18, 23, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 13971.0}, {'tour': [38, 36, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 21194.0}, {'tour': [58, 39, 45, 40, 46, 51, 63, 61, 55, 62, 50, 49, 41, 37, 29, 28, 30, 32, 36, 31, 38, 47, 52, 54, 57, 59, 60, 65, 64, 56, 53, 48, 44, 42, 43, 35, 34, 27, 26, 13, 33, 1, 2, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], 'cur_cost': 32884.0}, {'tour': array([57, 54, 32, 12, 65, 36,  5, 11, 15, 41, 44,  8,  1, 33, 26,  9, 20,
        2, 16, 38, 39, 22,  3, 64, 30, 46, 55,  6, 28, 21, 31, 17, 48, 42,
       13, 60, 61, 40, 53,  0, 43, 47, 14, 29, 24, 59, 45, 35, 25, 49, 34,
       19, 62, 10, 23, 52, 18, 50, 37, 58,  4, 51, 63,  7, 27, 56]), 'cur_cost': 100939.0}, {'tour': [20, 21, 41, 53, 31, 10, 23, 47, 7, 37, 34, 2, 52, 14, 36, 0, 46, 18, 35, 5, 33, 59, 65, 56, 11, 12, 57, 44, 43, 3, 6, 42, 15, 29, 38, 25, 24, 62, 17, 51, 60, 8, 30, 58, 64, 45, 49, 22, 1, 26, 40, 39, 27, 32, 4, 28, 9, 61, 63, 48, 55, 13, 50, 19, 16, 54], 'cur_cost': 104519.0}, {'tour': [2, 24, 16, 9, 20, 42, 33, 10, 3, 50, 65, 12, 54, 37, 46, 11, 52, 59, 0, 49, 56, 28, 15, 47, 62, 31, 57, 45, 43, 41, 36, 29, 14, 60, 7, 51, 26, 48, 5, 23, 8, 61, 1, 53, 44, 4, 55, 21, 18, 30, 19, 27, 17, 34, 22, 13, 63, 35, 38, 64, 40, 39, 58, 25, 32, 6], 'cur_cost': 110194.0}, {'tour': [63, 3, 64, 46, 40, 41, 22, 15, 62, 39, 29, 1, 38, 5, 37, 0, 18, 10, 7, 55, 61, 19, 13, 8, 27, 57, 53, 47, 51, 65, 23, 17, 11, 45, 32, 4, 36, 49, 56, 31, 28, 25, 42, 58, 21, 50, 34, 43, 16, 60, 35, 24, 54, 52, 14, 59, 30, 20, 12, 26, 6, 9, 48, 44, 2, 33], 'cur_cost': 105005.0}, {'tour': [41, 23, 15, 50, 39, 65, 11, 58, 44, 46, 38, 17, 48, 14, 1, 7, 52, 0, 59, 53, 27, 37, 61, 34, 62, 4, 2, 43, 25, 8, 55, 33, 18, 6, 16, 9, 47, 42, 3, 49, 12, 5, 36, 31, 20, 57, 28, 22, 51, 64, 63, 35, 54, 10, 60, 32, 26, 29, 30, 19, 13, 56, 40, 21, 45, 24], 'cur_cost': 95767.0}]
2025-06-22 17:40:46,153 - ExploitationExpert - INFO - 局部搜索耗时: 1.65秒
2025-06-22 17:40:46,153 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-22 17:40:46,153 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 17:40:46,155 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 17:40:46,155 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:40:46,155 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:40:46,155 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 94276.0
2025-06-22 17:40:47,521 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 17:40:47,523 - ExploitationExpert - INFO - res_population_costs: [9557, 9536]
2025-06-22 17:40:47,523 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 17:40:47,523 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:40:47,523 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 15597.0}, {'tour': [5, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 1, 2, 3, 0], 'cur_cost': 26036.0}, {'tour': [18, 23, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 13971.0}, {'tour': [38, 36, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 21194.0}, {'tour': [58, 39, 45, 40, 46, 51, 63, 61, 55, 62, 50, 49, 41, 37, 29, 28, 30, 32, 36, 31, 38, 47, 52, 54, 57, 59, 60, 65, 64, 56, 53, 48, 44, 42, 43, 35, 34, 27, 26, 13, 33, 1, 2, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], 'cur_cost': 32884.0}, {'tour': array([57, 54, 32, 12, 65, 36,  5, 11, 15, 41, 44,  8,  1, 33, 26,  9, 20,
        2, 16, 38, 39, 22,  3, 64, 30, 46, 55,  6, 28, 21, 31, 17, 48, 42,
       13, 60, 61, 40, 53,  0, 43, 47, 14, 29, 24, 59, 45, 35, 25, 49, 34,
       19, 62, 10, 23, 52, 18, 50, 37, 58,  4, 51, 63,  7, 27, 56]), 'cur_cost': 100939.0}, {'tour': array([31, 60, 32, 42, 45,  2, 26, 36, 25, 12, 63, 37,  1, 50,  4,  3, 16,
       18, 21, 47, 27, 20, 14, 40, 34, 35, 46, 48, 55, 24, 51, 29, 49, 19,
       62,  5, 11, 65, 56, 41,  8, 10, 57, 44, 43, 33, 17, 64, 53, 23, 22,
        0, 15, 13,  9, 28, 39, 58, 61, 30, 54, 59, 52,  7,  6, 38]), 'cur_cost': 94276.0}, {'tour': [2, 24, 16, 9, 20, 42, 33, 10, 3, 50, 65, 12, 54, 37, 46, 11, 52, 59, 0, 49, 56, 28, 15, 47, 62, 31, 57, 45, 43, 41, 36, 29, 14, 60, 7, 51, 26, 48, 5, 23, 8, 61, 1, 53, 44, 4, 55, 21, 18, 30, 19, 27, 17, 34, 22, 13, 63, 35, 38, 64, 40, 39, 58, 25, 32, 6], 'cur_cost': 110194.0}, {'tour': [63, 3, 64, 46, 40, 41, 22, 15, 62, 39, 29, 1, 38, 5, 37, 0, 18, 10, 7, 55, 61, 19, 13, 8, 27, 57, 53, 47, 51, 65, 23, 17, 11, 45, 32, 4, 36, 49, 56, 31, 28, 25, 42, 58, 21, 50, 34, 43, 16, 60, 35, 24, 54, 52, 14, 59, 30, 20, 12, 26, 6, 9, 48, 44, 2, 33], 'cur_cost': 105005.0}, {'tour': [41, 23, 15, 50, 39, 65, 11, 58, 44, 46, 38, 17, 48, 14, 1, 7, 52, 0, 59, 53, 27, 37, 61, 34, 62, 4, 2, 43, 25, 8, 55, 33, 18, 6, 16, 9, 47, 42, 3, 49, 12, 5, 36, 31, 20, 57, 28, 22, 51, 64, 63, 35, 54, 10, 60, 32, 26, 29, 30, 19, 13, 56, 40, 21, 45, 24], 'cur_cost': 95767.0}]
2025-06-22 17:40:47,523 - ExploitationExpert - INFO - 局部搜索耗时: 1.37秒
2025-06-22 17:40:47,523 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-22 17:40:47,523 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 17:40:47,523 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 17:40:47,523 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:40:47,523 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:40:47,528 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 104875.0
2025-06-22 17:40:48,057 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 17:40:48,057 - ExploitationExpert - INFO - res_population_costs: [9557, 9536]
2025-06-22 17:40:48,057 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 17:40:48,057 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:40:48,057 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 15597.0}, {'tour': [5, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 1, 2, 3, 0], 'cur_cost': 26036.0}, {'tour': [18, 23, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 13971.0}, {'tour': [38, 36, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 21194.0}, {'tour': [58, 39, 45, 40, 46, 51, 63, 61, 55, 62, 50, 49, 41, 37, 29, 28, 30, 32, 36, 31, 38, 47, 52, 54, 57, 59, 60, 65, 64, 56, 53, 48, 44, 42, 43, 35, 34, 27, 26, 13, 33, 1, 2, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], 'cur_cost': 32884.0}, {'tour': array([57, 54, 32, 12, 65, 36,  5, 11, 15, 41, 44,  8,  1, 33, 26,  9, 20,
        2, 16, 38, 39, 22,  3, 64, 30, 46, 55,  6, 28, 21, 31, 17, 48, 42,
       13, 60, 61, 40, 53,  0, 43, 47, 14, 29, 24, 59, 45, 35, 25, 49, 34,
       19, 62, 10, 23, 52, 18, 50, 37, 58,  4, 51, 63,  7, 27, 56]), 'cur_cost': 100939.0}, {'tour': array([31, 60, 32, 42, 45,  2, 26, 36, 25, 12, 63, 37,  1, 50,  4,  3, 16,
       18, 21, 47, 27, 20, 14, 40, 34, 35, 46, 48, 55, 24, 51, 29, 49, 19,
       62,  5, 11, 65, 56, 41,  8, 10, 57, 44, 43, 33, 17, 64, 53, 23, 22,
        0, 15, 13,  9, 28, 39, 58, 61, 30, 54, 59, 52,  7,  6, 38]), 'cur_cost': 94276.0}, {'tour': array([15, 42, 16, 13, 39, 20, 60, 62, 14, 32,  2, 54, 51, 50, 17,  9, 59,
       52, 26, 33, 21, 61, 19, 44, 38, 10, 57, 41, 37,  6, 30,  4, 24, 53,
       18, 46, 27, 11, 48, 47,  8, 55, 31,  7, 45, 56,  3, 28, 12,  1, 23,
        0, 43, 49, 25, 34, 36, 35, 63, 29,  5, 40, 64, 65, 22, 58]), 'cur_cost': 104875.0}, {'tour': [63, 3, 64, 46, 40, 41, 22, 15, 62, 39, 29, 1, 38, 5, 37, 0, 18, 10, 7, 55, 61, 19, 13, 8, 27, 57, 53, 47, 51, 65, 23, 17, 11, 45, 32, 4, 36, 49, 56, 31, 28, 25, 42, 58, 21, 50, 34, 43, 16, 60, 35, 24, 54, 52, 14, 59, 30, 20, 12, 26, 6, 9, 48, 44, 2, 33], 'cur_cost': 105005.0}, {'tour': [41, 23, 15, 50, 39, 65, 11, 58, 44, 46, 38, 17, 48, 14, 1, 7, 52, 0, 59, 53, 27, 37, 61, 34, 62, 4, 2, 43, 25, 8, 55, 33, 18, 6, 16, 9, 47, 42, 3, 49, 12, 5, 36, 31, 20, 57, 28, 22, 51, 64, 63, 35, 54, 10, 60, 32, 26, 29, 30, 19, 13, 56, 40, 21, 45, 24], 'cur_cost': 95767.0}]
2025-06-22 17:40:48,063 - ExploitationExpert - INFO - 局部搜索耗时: 0.54秒
2025-06-22 17:40:48,063 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-22 17:40:48,063 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 17:40:48,063 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 17:40:48,063 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:40:48,063 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:40:48,066 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 104924.0
2025-06-22 17:40:48,573 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 17:40:48,573 - ExploitationExpert - INFO - res_population_costs: [9557, 9536]
2025-06-22 17:40:48,573 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 17:40:48,577 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:40:48,577 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 15597.0}, {'tour': [5, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 1, 2, 3, 0], 'cur_cost': 26036.0}, {'tour': [18, 23, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 13971.0}, {'tour': [38, 36, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 21194.0}, {'tour': [58, 39, 45, 40, 46, 51, 63, 61, 55, 62, 50, 49, 41, 37, 29, 28, 30, 32, 36, 31, 38, 47, 52, 54, 57, 59, 60, 65, 64, 56, 53, 48, 44, 42, 43, 35, 34, 27, 26, 13, 33, 1, 2, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], 'cur_cost': 32884.0}, {'tour': array([57, 54, 32, 12, 65, 36,  5, 11, 15, 41, 44,  8,  1, 33, 26,  9, 20,
        2, 16, 38, 39, 22,  3, 64, 30, 46, 55,  6, 28, 21, 31, 17, 48, 42,
       13, 60, 61, 40, 53,  0, 43, 47, 14, 29, 24, 59, 45, 35, 25, 49, 34,
       19, 62, 10, 23, 52, 18, 50, 37, 58,  4, 51, 63,  7, 27, 56]), 'cur_cost': 100939.0}, {'tour': array([31, 60, 32, 42, 45,  2, 26, 36, 25, 12, 63, 37,  1, 50,  4,  3, 16,
       18, 21, 47, 27, 20, 14, 40, 34, 35, 46, 48, 55, 24, 51, 29, 49, 19,
       62,  5, 11, 65, 56, 41,  8, 10, 57, 44, 43, 33, 17, 64, 53, 23, 22,
        0, 15, 13,  9, 28, 39, 58, 61, 30, 54, 59, 52,  7,  6, 38]), 'cur_cost': 94276.0}, {'tour': array([15, 42, 16, 13, 39, 20, 60, 62, 14, 32,  2, 54, 51, 50, 17,  9, 59,
       52, 26, 33, 21, 61, 19, 44, 38, 10, 57, 41, 37,  6, 30,  4, 24, 53,
       18, 46, 27, 11, 48, 47,  8, 55, 31,  7, 45, 56,  3, 28, 12,  1, 23,
        0, 43, 49, 25, 34, 36, 35, 63, 29,  5, 40, 64, 65, 22, 58]), 'cur_cost': 104875.0}, {'tour': array([37, 35,  5, 58, 50, 43, 47,  3, 38, 65, 10, 61, 14, 13,  2, 23, 17,
       51, 24, 41,  8, 32, 18, 45, 21, 54, 39, 57, 19,  4,  6, 62, 30, 11,
        0, 56, 25, 63,  9, 22, 31, 36, 55, 59, 49, 40, 33, 12, 29, 26, 48,
       60, 46, 27, 52, 28,  7, 16, 15, 42, 44, 53, 20, 34, 64,  1]), 'cur_cost': 104924.0}, {'tour': [41, 23, 15, 50, 39, 65, 11, 58, 44, 46, 38, 17, 48, 14, 1, 7, 52, 0, 59, 53, 27, 37, 61, 34, 62, 4, 2, 43, 25, 8, 55, 33, 18, 6, 16, 9, 47, 42, 3, 49, 12, 5, 36, 31, 20, 57, 28, 22, 51, 64, 63, 35, 54, 10, 60, 32, 26, 29, 30, 19, 13, 56, 40, 21, 45, 24], 'cur_cost': 95767.0}]
2025-06-22 17:40:48,577 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:40:48,577 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-22 17:40:48,577 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 17:40:48,582 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-22 17:40:48,582 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:40:48,582 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [41, 23, 15] and ending with [21, 45, 24]
- Cost: 95767.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6953846153846154
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:40:48,582 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:40:52,442 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    41, 23, 15, 3, 5, 17, 20, 22, 2, 11, 13, 19, 25, 27, 29, 31, 33, 35, 37, 39,
    43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52,
    50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 18, 16, 14, 12, 10,
    8, 6, 4, 1, 7, 9, 21
  ],
  "modification_strategy": "Completely randomized path generation, aiming to drastically alter the sequence. Initial path starts with nodes from the original path's beginning. The remaining nodes are then added in a reversed and interleaved order, aiming to diversify the exploration.",
  "targeted_regions": "Prioritized exploration of 'Unexplored regions' by shuffling node order aiming to visit potentially untouched areas. Focused on incorporating both higher and lower indices more rapidly than the original path and also changing the sequence."
}
```
2025-06-22 17:40:52,443 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:40:52,443 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27456.0, 路径: [41, 23, 15, 3, 5, 17, 20, 22, 2, 11, 13, 19, 25, 27, 29, 31, 33, 35, 37, 39, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 18, 16, 14, 12, 10, 8, 6, 4, 1, 7, 9, 21, 0]
2025-06-22 17:40:52,443 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [41, 23, 15, 3, 5, 17, 20, 22, 2, 11, 13, 19, 25, 27, 29, 31, 33, 35, 37, 39, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 18, 16, 14, 12, 10, 8, 6, 4, 1, 7, 9, 21, 0], 'cur_cost': 27456.0}
2025-06-22 17:40:52,443 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [50, 38, 51, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 15597.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 1, 2, 3, 0], 'cur_cost': 26036.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [18, 23, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 13971.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [38, 36, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 21194.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [58, 39, 45, 40, 46, 51, 63, 61, 55, 62, 50, 49, 41, 37, 29, 28, 30, 32, 36, 31, 38, 47, 52, 54, 57, 59, 60, 65, 64, 56, 53, 48, 44, 42, 43, 35, 34, 27, 26, 13, 33, 1, 2, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], 'cur_cost': 32884.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [41, 23, 15, 3, 5, 17, 20, 22, 2, 11, 13, 19, 25, 27, 29, 31, 33, 35, 37, 39, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 18, 16, 14, 12, 10, 8, 6, 4, 1, 7, 9, 21, 0], 'cur_cost': 27456.0}}]
2025-06-22 17:40:52,443 - __main__ - INFO - 进化阶段完成
2025-06-22 17:40:52,443 - __main__ - INFO - 开始评估阶段
2025-06-22 17:40:52,445 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 17:40:52,445 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 17:40:52,445 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 0/5
- Population changes:
  * Cost: min 9964.0->13971.0, mean 78572.2->54215.2
  * Diversity: 0.7538461538461538->0.6615384615384615
  * Elite solutions: 3->1

## Strategy Performance:
- Explore: 6 individuals, success rate 50.0%, avg improvement 36432.67
- Exploit: 4 individuals, success rate 100.0%, avg improvement 6243.5
- Overall improvement: -4007.0

## Historical Trends:
No historical data available

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 17:40:58,859 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early, Potential Convergence Issues",
    "features": "Cost of best solution is increasing, population cost mean is decreasing, diversity is decreasing, elite solutions drastically reduced. Overall improvement is negative. This suggests the algorithm is either converging prematurely, or experiencing difficulty finding better solutions at this early stage."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "50.0%",
      "avg_improvement": "36432.67",
      "evaluation": "The explore strategy is showing some signs of success, but its impact on the overall improvement is overshadowed by exploitation failure. The high average improvement suggests that when explore finds a useful solution, it's a significant improvement, however, this doesn't occur often enough."
    },
    "exploit": {
      "success_rate": "100.0%",
      "avg_improvement": "6243.5",
      "evaluation": "While exploiting solutions is always successful, the improvement from exploit is minimal and does not offset the negative cost of new explore solutions. This means the algorithm is losing ground with each iteration."
    },
    "overall": {
      "improvement": "-4007.0",
      "evaluation": "Negative overall improvement indicates a problem. The current strategy, despite successful exploitation, is not converging towards better solutions."
    }
  },
  "balance_state": {
    "assessment": "The balance is skewed. Explore is not finding enough good solutions and the exploitation isn't strong enough to counteract the cost introduced by explore. The algorithm should likely focus on improving explore or refine the process of exploitation to favor solutions found during explore.",
    "adjustment_needs": "Improve explore success rate by: (1) investigating the mechanisms which make it successful and applying those findings; (2) refining the selection of individuals to be explored. Also ensure that exploitation is effectively building upon any improvements that explore finds."
  },
  "recommendations": [
    {
      "action": "Increase Exploration Focus (Carefully)",
      "details": "Despite the initial negative impact, it is important to continue exploring to ensure the algorithm has not been trapped in a local optimum. Examine the explore individuals, analyzing the properties of the solutions to determine why they are costing more. Perhaps there is too much noise in the process."
    },
    {
      "action": "Re-evaluate Explore Methods",
      "details": "Analyze the solutions generated by exploration. Are the individuals generated too different from existing solutions? Can the exploration operators be tuned to introduce smaller, more targeted changes? Consider also the probability and the number of explore individuals."
    },
    {
      "action": "Improve Exploitation",
      "details": "Confirm that exploit individuals are built upon the work of explore individuals and do not introduce any features which reduce their effectiveness. Investigate whether any useful discoveries can be made about exploit's operation and its potential for improvement."
    },
    {
      "action": "Monitor Cost and Diversity Closely",
      "details": "Pay very close attention to the trends of cost and diversity. If the cost of the best solution continues to increase and diversity continues to decrease, the algorithm is in trouble and the exploration strategy may need a significant overhaul."
    }
  ]
}
```
2025-06-22 17:40:58,888 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 17:40:58,888 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early, Potential Convergence Issues",
    "features": "Cost of best solution is increasing, population cost mean is decreasing, diversity is decreasing, elite solutions drastically reduced. Overall improvement is negative. This suggests the algorithm is either converging prematurely, or experiencing difficulty finding better solutions at this early stage."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "50.0%",
      "avg_improvement": "36432.67",
      "evaluation": "The explore strategy is showing some signs of success, but its impact on the overall improvement is overshadowed by exploitation failure. The high average improvement suggests that when explore finds a useful solution, it's a significant improvement, however, this doesn't occur often enough."
    },
    "exploit": {
      "success_rate": "100.0%",
      "avg_improvement": "6243.5",
      "evaluation": "While exploiting solutions is always successful, the improvement from exploit is minimal and does not offset the negative cost of new explore solutions. This means the algorithm is losing ground with each iteration."
    },
    "overall": {
      "improvement": "-4007.0",
      "evaluation": "Negative overall improvement indicates a problem. The current strategy, despite successful exploitation, is not converging towards better solutions."
    }
  },
  "balance_state": {
    "assessment": "The balance is skewed. Explore is not finding enough good solutions and the exploitation isn't strong enough to counteract the cost introduced by explore. The algorithm should likely focus on improving explore or refine the process of exploitation to favor solutions found during explore.",
    "adjustment_needs": "Improve explore success rate by: (1) investigating the mechanisms which make it successful and applying those findings; (2) refining the selection of individuals to be explored. Also ensure that exploitation is effectively building upon any improvements that explore finds."
  },
  "recommendations": [
    {
      "action": "Increase Exploration Focus (Carefully)",
      "details": "Despite the initial negative impact, it is important to continue exploring to ensure the algorithm has not been trapped in a local optimum. Examine the explore individuals, analyzing the properties of the solutions to determine why they are costing more. Perhaps there is too much noise in the process."
    },
    {
      "action": "Re-evaluate Explore Methods",
      "details": "Analyze the solutions generated by exploration. Are the individuals generated too different from existing solutions? Can the exploration operators be tuned to introduce smaller, more targeted changes? Consider also the probability and the number of explore individuals."
    },
    {
      "action": "Improve Exploitation",
      "details": "Confirm that exploit individuals are built upon the work of explore individuals and do not introduce any features which reduce their effectiveness. Investigate whether any useful discoveries can be made about exploit's operation and its potential for improvement."
    },
    {
      "action": "Monitor Cost and Diversity Closely",
      "details": "Pay very close attention to the trends of cost and diversity. If the cost of the best solution continues to increase and diversity continues to decrease, the algorithm is in trouble and the exploration strategy may need a significant overhaul."
    }
  ]
}
```
2025-06-22 17:40:58,888 - __main__ - INFO - 评估阶段完成
2025-06-22 17:40:58,888 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early, Potential Convergence Issues",
    "features": "Cost of best solution is increasing, population cost mean is decreasing, diversity is decreasing, elite solutions drastically reduced. Overall improvement is negative. This suggests the algorithm is either converging prematurely, or experiencing difficulty finding better solutions at this early stage."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "50.0%",
      "avg_improvement": "36432.67",
      "evaluation": "The explore strategy is showing some signs of success, but its impact on the overall improvement is overshadowed by exploitation failure. The high average improvement suggests that when explore finds a useful solution, it's a significant improvement, however, this doesn't occur often enough."
    },
    "exploit": {
      "success_rate": "100.0%",
      "avg_improvement": "6243.5",
      "evaluation": "While exploiting solutions is always successful, the improvement from exploit is minimal and does not offset the negative cost of new explore solutions. This means the algorithm is losing ground with each iteration."
    },
    "overall": {
      "improvement": "-4007.0",
      "evaluation": "Negative overall improvement indicates a problem. The current strategy, despite successful exploitation, is not converging towards better solutions."
    }
  },
  "balance_state": {
    "assessment": "The balance is skewed. Explore is not finding enough good solutions and the exploitation isn't strong enough to counteract the cost introduced by explore. The algorithm should likely focus on improving explore or refine the process of exploitation to favor solutions found during explore.",
    "adjustment_needs": "Improve explore success rate by: (1) investigating the mechanisms which make it successful and applying those findings; (2) refining the selection of individuals to be explored. Also ensure that exploitation is effectively building upon any improvements that explore finds."
  },
  "recommendations": [
    {
      "action": "Increase Exploration Focus (Carefully)",
      "details": "Despite the initial negative impact, it is important to continue exploring to ensure the algorithm has not been trapped in a local optimum. Examine the explore individuals, analyzing the properties of the solutions to determine why they are costing more. Perhaps there is too much noise in the process."
    },
    {
      "action": "Re-evaluate Explore Methods",
      "details": "Analyze the solutions generated by exploration. Are the individuals generated too different from existing solutions? Can the exploration operators be tuned to introduce smaller, more targeted changes? Consider also the probability and the number of explore individuals."
    },
    {
      "action": "Improve Exploitation",
      "details": "Confirm that exploit individuals are built upon the work of explore individuals and do not introduce any features which reduce their effectiveness. Investigate whether any useful discoveries can be made about exploit's operation and its potential for improvement."
    },
    {
      "action": "Monitor Cost and Diversity Closely",
      "details": "Pay very close attention to the trends of cost and diversity. If the cost of the best solution continues to increase and diversity continues to decrease, the algorithm is in trouble and the exploration strategy may need a significant overhaul."
    }
  ]
}
```
2025-06-22 17:40:58,890 - __main__ - INFO - 当前最佳适应度: 13971.0
2025-06-22 17:40:58,891 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-22 17:40:58,891 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-22 17:40:58,891 - __main__ - INFO - 开始分析阶段
2025-06-22 17:40:58,891 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:40:58,909 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 13971.0, 'max': 104924.0, 'mean': 54215.2, 'std': 38848.786031998476}, 'diversity': 0.8730639730639731, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:40:58,909 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 13971.0, 'max': 104924.0, 'mean': 54215.2, 'std': 38848.786031998476}, 'diversity_level': 0.8730639730639731, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:40:58,909 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:40:58,909 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:40:58,911 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:40:59,030 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 17:40:59,030 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:40:59,030 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 17:40:59,030 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:40:59,030 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:40:59,030 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:40:59,030 - EliteExpert - INFO - 精英解分析完成
2025-06-22 17:40:59,030 - __main__ - INFO - 精英专家分析报告: {'elite_count': 2, 'elite_common_features': {'common_edges': {'(36, 26)': 1.0, '(26, 25)': 1.0, '(25, 33)': 1.0, '(35, 34)': 1.0, '(51, 38)': 1.0, '(56, 58)': 1.0, '(62, 59)': 1.0}, 'common_edge_ratio': 0.10606060606060606}, 'fixed_nodes': [{'node': 0, 'position': 0}, {'node': 20, 'position': 33}], 'population_gap': {'cost_gap': {'min_gap': 4435.0, 'avg_gap': 44668.7}, 'structure_gap': {'unique_elite_edges': 78, 'unique_pop_edges': 483, 'common_edges': 47}}, 'elite_diversity': {'diversity_score': 0.2272727272727273}}
2025-06-22 17:40:59,030 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:40:59,030 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:40:59,030 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:40:59,030 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=13971.0, max=104924.0, mean=54215.2, std=38848.786031998476
- Diversity: 0.8730639730639731, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 2 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 0 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:41:05,358 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High - likely due to the significant cost range and high standard deviation, implying a landscape with numerous local optima.",
    "modality": "Likely multimodal. The high cost range and diversity suggest multiple potential solution clusters. The absence of convergence, combined with the broad cost distribution, supports this."
  },
  "population_state": {
    "diversity": "High - at 0.873, indicating the population explores a wide range of the search space, covering different areas and structures.",
    "convergence": "Very low - at 0.0, meaning the population is not converging towards any specific high-quality solution. This is expected given the lack of structure and elite solutions."
  },
  "difficult_regions": {
    "identified_challenges": "The search space presents several challenges: 1) Lack of structure: Absence of high-quality edges or common subpaths makes guiding the search difficult. 2) No elite solutions: The absence of solutions with fixed nodes and 0% shared edges indicates the solutions are dissimilar and far from optimal, or at least, far from a stable near-optimal region. 3) Broad cost distribution and high standard deviation suggest that the search is finding wildly varying solutions. The challenge will be pinpointing optimal solutions from such a wide variety.",
    "description": "Challenges arise from a lack of structural information and lack of convergence towards high-quality, shared-edge solutions, compounded by a wide cost distribution."
  },
  "opportunity_regions": {
    "promising_areas": "There are no clearly identified opportunity regions currently. The diverse population suggests exploration, but without structural information, it's difficult to target promising zones. The focus should be on structure identification and identifying local optima to guide exploration.",
    "description": "Currently, no clearly identified areas with specific potential. The focus should be on uncovering structures and, possibly, detecting potential local optima by examining regions with lower cost values, provided those cost values can be linked with certain parameter/feature combinations"
  },
  "evolution_direction": {
    "strategy": "Exploration and Structure Identification: Prioritize exploring the diverse search space and extracting relevant structural information. Specifically, the focus should be on 1) Structure discovery through graph analysis and comparison of solutions or parameter analysis, which may reveal potentially promising regions. 2) Exploring local optima within the diverse population to see if they converge.",
    "tactics": [
      "Analyze solutions to identify potential correlations between parameters or features and solution quality. This could involve feature engineering, sensitivity analysis and feature importance measurements.",
      "Explore techniques for edge discovery (e.g., edge frequency, solution overlap) to highlight significant substructures.",
      "Implement a strategy for local search or neighborhood exploration to identify areas where small changes in parameters yield notable improvements.",
      "Maintain population diversity while gradually directing the search towards more promising areas, once potential regions are identified.",
      "Employ niching strategies to maintain diversity and explore potentially multiple optima. "
    ]
  }
}
```
2025-06-22 17:41:05,358 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:41:05,358 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High - likely due to the significant cost range and high standard deviation, implying a landscape with numerous local optima.', 'modality': 'Likely multimodal. The high cost range and diversity suggest multiple potential solution clusters. The absence of convergence, combined with the broad cost distribution, supports this.'}, 'population_state': {'diversity': 'High - at 0.873, indicating the population explores a wide range of the search space, covering different areas and structures.', 'convergence': 'Very low - at 0.0, meaning the population is not converging towards any specific high-quality solution. This is expected given the lack of structure and elite solutions.'}, 'difficult_regions': {'identified_challenges': 'The search space presents several challenges: 1) Lack of structure: Absence of high-quality edges or common subpaths makes guiding the search difficult. 2) No elite solutions: The absence of solutions with fixed nodes and 0% shared edges indicates the solutions are dissimilar and far from optimal, or at least, far from a stable near-optimal region. 3) Broad cost distribution and high standard deviation suggest that the search is finding wildly varying solutions. The challenge will be pinpointing optimal solutions from such a wide variety.', 'description': 'Challenges arise from a lack of structural information and lack of convergence towards high-quality, shared-edge solutions, compounded by a wide cost distribution.'}, 'opportunity_regions': {'promising_areas': "There are no clearly identified opportunity regions currently. The diverse population suggests exploration, but without structural information, it's difficult to target promising zones. The focus should be on structure identification and identifying local optima to guide exploration.", 'description': 'Currently, no clearly identified areas with specific potential. The focus should be on uncovering structures and, possibly, detecting potential local optima by examining regions with lower cost values, provided those cost values can be linked with certain parameter/feature combinations'}, 'evolution_direction': {'strategy': 'Exploration and Structure Identification: Prioritize exploring the diverse search space and extracting relevant structural information. Specifically, the focus should be on 1) Structure discovery through graph analysis and comparison of solutions or parameter analysis, which may reveal potentially promising regions. 2) Exploring local optima within the diverse population to see if they converge.', 'tactics': ['Analyze solutions to identify potential correlations between parameters or features and solution quality. This could involve feature engineering, sensitivity analysis and feature importance measurements.', 'Explore techniques for edge discovery (e.g., edge frequency, solution overlap) to highlight significant substructures.', 'Implement a strategy for local search or neighborhood exploration to identify areas where small changes in parameters yield notable improvements.', 'Maintain population diversity while gradually directing the search towards more promising areas, once potential regions are identified.', 'Employ niching strategies to maintain diversity and explore potentially multiple optima. ']}}
2025-06-22 17:41:05,358 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:41:05,358 - __main__ - INFO - 分析阶段完成
2025-06-22 17:41:05,358 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High - likely due to the significant cost range and high standard deviation, implying a landscape with numerous local optima.', 'modality': 'Likely multimodal. The high cost range and diversity suggest multiple potential solution clusters. The absence of convergence, combined with the broad cost distribution, supports this.'}, 'population_state': {'diversity': 'High - at 0.873, indicating the population explores a wide range of the search space, covering different areas and structures.', 'convergence': 'Very low - at 0.0, meaning the population is not converging towards any specific high-quality solution. This is expected given the lack of structure and elite solutions.'}, 'difficult_regions': {'identified_challenges': 'The search space presents several challenges: 1) Lack of structure: Absence of high-quality edges or common subpaths makes guiding the search difficult. 2) No elite solutions: The absence of solutions with fixed nodes and 0% shared edges indicates the solutions are dissimilar and far from optimal, or at least, far from a stable near-optimal region. 3) Broad cost distribution and high standard deviation suggest that the search is finding wildly varying solutions. The challenge will be pinpointing optimal solutions from such a wide variety.', 'description': 'Challenges arise from a lack of structural information and lack of convergence towards high-quality, shared-edge solutions, compounded by a wide cost distribution.'}, 'opportunity_regions': {'promising_areas': "There are no clearly identified opportunity regions currently. The diverse population suggests exploration, but without structural information, it's difficult to target promising zones. The focus should be on structure identification and identifying local optima to guide exploration.", 'description': 'Currently, no clearly identified areas with specific potential. The focus should be on uncovering structures and, possibly, detecting potential local optima by examining regions with lower cost values, provided those cost values can be linked with certain parameter/feature combinations'}, 'evolution_direction': {'strategy': 'Exploration and Structure Identification: Prioritize exploring the diverse search space and extracting relevant structural information. Specifically, the focus should be on 1) Structure discovery through graph analysis and comparison of solutions or parameter analysis, which may reveal potentially promising regions. 2) Exploring local optima within the diverse population to see if they converge.', 'tactics': ['Analyze solutions to identify potential correlations between parameters or features and solution quality. This could involve feature engineering, sensitivity analysis and feature importance measurements.', 'Explore techniques for edge discovery (e.g., edge frequency, solution overlap) to highlight significant substructures.', 'Implement a strategy for local search or neighborhood exploration to identify areas where small changes in parameters yield notable improvements.', 'Maintain population diversity while gradually directing the search towards more promising areas, once potential regions are identified.', 'Employ niching strategies to maintain diversity and explore potentially multiple optima. ']}}
2025-06-22 17:41:05,358 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:41:05,358 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:41:05,358 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 1
- Population: 10 individuals, diversity=High - at 0.873, indicating the population explores a wide range of the search space, covering different areas and structures.
- Best cost: 13971.0, Mean cost: 54215.2
- Search space: Ruggedness: High - likely due to the significant cost range and high standard deviation, implying a landscape with numerous local optima., Modality: Likely multimodal. The high cost range and diversity suggest multiple potential solution clusters. The absence of convergence, combined with the broad cost distribution, supports this.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early, Potential Convergence Issues",
    "features": "Cost of best solution is increasing, population cost mean is decreasing, diversity is decreasing, ...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:41:05,358 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:41:08,223 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_strength": 0.2,
      "step_size": 0.15,
      "local_search_iterations": 3,
      "region_exploration_bias": 0.7
    },
    "exploit": {
      "mutation_strength": 0.1,
      "step_size": 0.05,
      "local_search_iterations": 7,
      "exploitation_bias": 0.8
    }
  }
}
```

2025-06-22 17:41:08,223 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 17:41:08,223 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 17:41:08,223 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_strength": 0.2,
      "step_size": 0.15,
      "local_search_iterations": 3,
      "region_exploration_bias": 0.7
    },
    "exploit": {
      "mutation_strength": 0.1,
      "step_size": 0.05,
      "local_search_iterations": 7,
      "exploitation_bias": 0.8
    }
  }
}
```

2025-06-22 17:41:08,223 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:41:08,223 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 17:41:08,223 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_strength": 0.2,
      "step_size": 0.15,
      "local_search_iterations": 3,
      "region_exploration_bias": 0.7
    },
    "exploit": {
      "mutation_strength": 0.1,
      "step_size": 0.05,
      "local_search_iterations": 7,
      "exploitation_bias": 0.8
    }
  }
}
```

2025-06-22 17:41:08,226 - __main__ - INFO - 开始进化阶段
2025-06-22 17:41:08,226 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:41:08,226 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:41:08,227 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [50, 38, 51] and ending with [2, 1, 0]
- Cost: 15597.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6615384615384615
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:41:08,227 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:41:11,594 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49
  ],
  "modification_strategy": "Heuristic path diversification by exploring the frontier of the initial path and incorporating potentially better local optimizations. The strategy prioritized expanding from the initial nodes (50, 38, 51) towards previously unexplored nodes while trying to maintain a smooth and diverse trajectory. Nodes were included with the goal of visiting all regions.",
  "targeted_regions": "Focused exploration of previously unexplored regions identified within the search context, specifically trying to incorporate all regions to improve the overall diversity of the new path."
}
```
2025-06-22 17:41:11,595 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:41:11,595 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11408.0, 路径: [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49]
2025-06-22 17:41:11,595 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}
2025-06-22 17:41:11,595 - __main__ - INFO - 为个体 1 生成利用路径
2025-06-22 17:41:11,596 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:41:11,596 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:41:11,597 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 114663.0
2025-06-22 17:41:12,102 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 17:41:12,102 - ExploitationExpert - INFO - res_population_costs: [9536, 9557]
2025-06-22 17:41:12,103 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:41:12,103 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:41:12,104 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': array([14, 19, 31, 52,  1, 21,  8, 16, 38, 34, 32, 35, 20, 42,  7, 23, 64,
       62, 25, 45, 17, 22, 58, 12, 65,  3,  4, 13, 57, 24, 49, 29, 33, 55,
       41, 56, 63, 46, 44, 18, 47,  2, 39, 26,  6, 53, 51, 10, 36, 15, 28,
        5, 54, 48, 60,  0, 30, 50, 11, 40, 61, 27,  9, 37, 43, 59]), 'cur_cost': 114663.0}, {'tour': [18, 23, 16, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0], 'cur_cost': 13971.0}, {'tour': [38, 36, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 21194.0}, {'tour': [58, 39, 45, 40, 46, 51, 63, 61, 55, 62, 50, 49, 41, 37, 29, 28, 30, 32, 36, 31, 38, 47, 52, 54, 57, 59, 60, 65, 64, 56, 53, 48, 44, 42, 43, 35, 34, 27, 26, 13, 33, 1, 2, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], 'cur_cost': 32884.0}, {'tour': array([57, 54, 32, 12, 65, 36,  5, 11, 15, 41, 44,  8,  1, 33, 26,  9, 20,
        2, 16, 38, 39, 22,  3, 64, 30, 46, 55,  6, 28, 21, 31, 17, 48, 42,
       13, 60, 61, 40, 53,  0, 43, 47, 14, 29, 24, 59, 45, 35, 25, 49, 34,
       19, 62, 10, 23, 52, 18, 50, 37, 58,  4, 51, 63,  7, 27, 56]), 'cur_cost': 100939.0}, {'tour': array([31, 60, 32, 42, 45,  2, 26, 36, 25, 12, 63, 37,  1, 50,  4,  3, 16,
       18, 21, 47, 27, 20, 14, 40, 34, 35, 46, 48, 55, 24, 51, 29, 49, 19,
       62,  5, 11, 65, 56, 41,  8, 10, 57, 44, 43, 33, 17, 64, 53, 23, 22,
        0, 15, 13,  9, 28, 39, 58, 61, 30, 54, 59, 52,  7,  6, 38]), 'cur_cost': 94276.0}, {'tour': array([15, 42, 16, 13, 39, 20, 60, 62, 14, 32,  2, 54, 51, 50, 17,  9, 59,
       52, 26, 33, 21, 61, 19, 44, 38, 10, 57, 41, 37,  6, 30,  4, 24, 53,
       18, 46, 27, 11, 48, 47,  8, 55, 31,  7, 45, 56,  3, 28, 12,  1, 23,
        0, 43, 49, 25, 34, 36, 35, 63, 29,  5, 40, 64, 65, 22, 58]), 'cur_cost': 104875.0}, {'tour': array([37, 35,  5, 58, 50, 43, 47,  3, 38, 65, 10, 61, 14, 13,  2, 23, 17,
       51, 24, 41,  8, 32, 18, 45, 21, 54, 39, 57, 19,  4,  6, 62, 30, 11,
        0, 56, 25, 63,  9, 22, 31, 36, 55, 59, 49, 40, 33, 12, 29, 26, 48,
       60, 46, 27, 52, 28,  7, 16, 15, 42, 44, 53, 20, 34, 64,  1]), 'cur_cost': 104924.0}, {'tour': [41, 23, 15, 3, 5, 17, 20, 22, 2, 11, 13, 19, 25, 27, 29, 31, 33, 35, 37, 39, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 18, 16, 14, 12, 10, 8, 6, 4, 1, 7, 9, 21, 0], 'cur_cost': 27456.0}]
2025-06-22 17:41:12,105 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:41:12,106 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-22 17:41:12,106 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-06-22 17:41:12,106 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 17:41:12,106 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:41:12,107 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [18, 23, 16] and ending with [64, 65, 0]
- Cost: 13971.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6846153846153846
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:41:12,108 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:41:15,840 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    18, 23, 16, 27, 34, 41, 48, 55, 62, 5, 12, 19, 26, 33, 40, 47, 54, 61, 4, 11,
    17, 24, 31, 38, 45, 52, 59, 2, 9, 15, 22, 29, 36, 43, 50, 57, 0, 7, 14, 21,
    28, 35, 42, 49, 56, 63, 6, 13, 20, 25, 32, 39, 46, 53, 60, 3, 10, 64, 30, 37,
    44, 51, 58, 65
  ],
  "modification_strategy": "I prioritized a path structure that moves in 'layers' across the graph. This was achieved by initially following a different sequence through several initial nodes. By then incorporating nodes in an ordered fashion to ensure path completeness, and maintaining relatively consistent distances to favor exploration of a wider range of areas. This method avoids potentially costly moves between very distant nodes and favors a more evenly spread route overall.",
  "targeted_regions": "This strategy focused on a more even spread of nodes to cover all areas. The goal was to explore the unexplored regions by creating a path covering these areas more uniformly."
}
```
2025-06-22 17:41:15,841 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:41:15,841 - ExplorationExpert - INFO - 探索路径生成完成，成本: 61891.0, 路径: [18, 23, 16, 27, 34, 41, 48, 55, 62, 5, 12, 19, 26, 33, 40, 47, 54, 61, 4, 11, 17, 24, 31, 38, 45, 52, 59, 2, 9, 15, 22, 29, 36, 43, 50, 57, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 6, 13, 20, 25, 32, 39, 46, 53, 60, 3, 10, 64, 30, 37, 44, 51, 58, 65, 8, 1]
2025-06-22 17:41:15,841 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [18, 23, 16, 27, 34, 41, 48, 55, 62, 5, 12, 19, 26, 33, 40, 47, 54, 61, 4, 11, 17, 24, 31, 38, 45, 52, 59, 2, 9, 15, 22, 29, 36, 43, 50, 57, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 6, 13, 20, 25, 32, 39, 46, 53, 60, 3, 10, 64, 30, 37, 44, 51, 58, 65, 8, 1], 'cur_cost': 61891.0}
2025-06-22 17:41:15,842 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-22 17:41:15,842 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:41:15,842 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:41:15,842 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 90481.0
2025-06-22 17:41:16,353 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 17:41:16,353 - ExploitationExpert - INFO - res_population_costs: [9536, 9557]
2025-06-22 17:41:16,353 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:41:16,355 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:41:16,356 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': array([14, 19, 31, 52,  1, 21,  8, 16, 38, 34, 32, 35, 20, 42,  7, 23, 64,
       62, 25, 45, 17, 22, 58, 12, 65,  3,  4, 13, 57, 24, 49, 29, 33, 55,
       41, 56, 63, 46, 44, 18, 47,  2, 39, 26,  6, 53, 51, 10, 36, 15, 28,
        5, 54, 48, 60,  0, 30, 50, 11, 40, 61, 27,  9, 37, 43, 59]), 'cur_cost': 114663.0}, {'tour': [18, 23, 16, 27, 34, 41, 48, 55, 62, 5, 12, 19, 26, 33, 40, 47, 54, 61, 4, 11, 17, 24, 31, 38, 45, 52, 59, 2, 9, 15, 22, 29, 36, 43, 50, 57, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 6, 13, 20, 25, 32, 39, 46, 53, 60, 3, 10, 64, 30, 37, 44, 51, 58, 65, 8, 1], 'cur_cost': 61891.0}, {'tour': array([57,  2, 31, 37,  8, 15, 46,  7,  6, 54, 65, 11,  3, 35, 36, 16, 12,
       61, 20, 25, 64, 50, 51, 52, 48, 19, 53, 10, 55, 42, 43, 18,  1,  5,
       62, 32, 27, 21,  4, 58,  0, 38, 22, 26, 47, 17, 29, 28, 13, 63, 39,
       14, 24, 56, 33, 34, 40, 44, 45, 41,  9, 59, 49, 23, 30, 60]), 'cur_cost': 90481.0}, {'tour': [58, 39, 45, 40, 46, 51, 63, 61, 55, 62, 50, 49, 41, 37, 29, 28, 30, 32, 36, 31, 38, 47, 52, 54, 57, 59, 60, 65, 64, 56, 53, 48, 44, 42, 43, 35, 34, 27, 26, 13, 33, 1, 2, 0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], 'cur_cost': 32884.0}, {'tour': array([57, 54, 32, 12, 65, 36,  5, 11, 15, 41, 44,  8,  1, 33, 26,  9, 20,
        2, 16, 38, 39, 22,  3, 64, 30, 46, 55,  6, 28, 21, 31, 17, 48, 42,
       13, 60, 61, 40, 53,  0, 43, 47, 14, 29, 24, 59, 45, 35, 25, 49, 34,
       19, 62, 10, 23, 52, 18, 50, 37, 58,  4, 51, 63,  7, 27, 56]), 'cur_cost': 100939.0}, {'tour': array([31, 60, 32, 42, 45,  2, 26, 36, 25, 12, 63, 37,  1, 50,  4,  3, 16,
       18, 21, 47, 27, 20, 14, 40, 34, 35, 46, 48, 55, 24, 51, 29, 49, 19,
       62,  5, 11, 65, 56, 41,  8, 10, 57, 44, 43, 33, 17, 64, 53, 23, 22,
        0, 15, 13,  9, 28, 39, 58, 61, 30, 54, 59, 52,  7,  6, 38]), 'cur_cost': 94276.0}, {'tour': array([15, 42, 16, 13, 39, 20, 60, 62, 14, 32,  2, 54, 51, 50, 17,  9, 59,
       52, 26, 33, 21, 61, 19, 44, 38, 10, 57, 41, 37,  6, 30,  4, 24, 53,
       18, 46, 27, 11, 48, 47,  8, 55, 31,  7, 45, 56,  3, 28, 12,  1, 23,
        0, 43, 49, 25, 34, 36, 35, 63, 29,  5, 40, 64, 65, 22, 58]), 'cur_cost': 104875.0}, {'tour': array([37, 35,  5, 58, 50, 43, 47,  3, 38, 65, 10, 61, 14, 13,  2, 23, 17,
       51, 24, 41,  8, 32, 18, 45, 21, 54, 39, 57, 19,  4,  6, 62, 30, 11,
        0, 56, 25, 63,  9, 22, 31, 36, 55, 59, 49, 40, 33, 12, 29, 26, 48,
       60, 46, 27, 52, 28,  7, 16, 15, 42, 44, 53, 20, 34, 64,  1]), 'cur_cost': 104924.0}, {'tour': [41, 23, 15, 3, 5, 17, 20, 22, 2, 11, 13, 19, 25, 27, 29, 31, 33, 35, 37, 39, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 18, 16, 14, 12, 10, 8, 6, 4, 1, 7, 9, 21, 0], 'cur_cost': 27456.0}]
2025-06-22 17:41:16,357 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:41:16,358 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-22 17:41:16,358 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-22 17:41:16,358 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 17:41:16,358 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:41:16,359 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [58, 39, 45] and ending with [23, 24, 25]
- Cost: 32884.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8261538461538461
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:41:16,359 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:41:20,019 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    58, 39, 45, 12, 60, 2, 5, 15, 36, 40, 41, 3, 4, 55, 53, 56, 57, 59, 61, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 1, 0, 62, 63, 64, 65, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 54, 6, 7, 8, 9, 10, 11, 13, 58],
  "modification_strategy": "Reversed path segments and incorporated some previously less visited nodes while keeping the original start and end points and maintaining the necessary path length. Prioritized high diversity by swapping segments and nodes from opportunity regions while attempting to keep the overall path cost in a reasonable range.",
  "targeted_regions": "Opportunity regions and areas surrounding the difficult regions. Focused on incorporating more nodes from those regions into the earlier and later sections of the path to maximize the chance of discovering better routes around these potentially problematic spots."
}
```
2025-06-22 17:41:20,019 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:41:20,019 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36230.0, 路径: [58, 39, 45, 12, 60, 2, 5, 15, 36, 40, 41, 3, 4, 55, 53, 56, 57, 59, 61, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 1, 0, 62, 63, 64, 65, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 54, 6, 7, 8, 9, 10, 11, 13]
2025-06-22 17:41:20,019 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [58, 39, 45, 12, 60, 2, 5, 15, 36, 40, 41, 3, 4, 55, 53, 56, 57, 59, 61, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 1, 0, 62, 63, 64, 65, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 54, 6, 7, 8, 9, 10, 11, 13], 'cur_cost': 36230.0}
2025-06-22 17:41:20,019 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 17:41:20,019 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:41:20,019 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:41:20,019 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 102270.0
2025-06-22 17:41:20,530 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 17:41:20,530 - ExploitationExpert - INFO - res_population_costs: [9536, 9557]
2025-06-22 17:41:20,530 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:41:20,530 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:41:20,530 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': array([14, 19, 31, 52,  1, 21,  8, 16, 38, 34, 32, 35, 20, 42,  7, 23, 64,
       62, 25, 45, 17, 22, 58, 12, 65,  3,  4, 13, 57, 24, 49, 29, 33, 55,
       41, 56, 63, 46, 44, 18, 47,  2, 39, 26,  6, 53, 51, 10, 36, 15, 28,
        5, 54, 48, 60,  0, 30, 50, 11, 40, 61, 27,  9, 37, 43, 59]), 'cur_cost': 114663.0}, {'tour': [18, 23, 16, 27, 34, 41, 48, 55, 62, 5, 12, 19, 26, 33, 40, 47, 54, 61, 4, 11, 17, 24, 31, 38, 45, 52, 59, 2, 9, 15, 22, 29, 36, 43, 50, 57, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 6, 13, 20, 25, 32, 39, 46, 53, 60, 3, 10, 64, 30, 37, 44, 51, 58, 65, 8, 1], 'cur_cost': 61891.0}, {'tour': array([57,  2, 31, 37,  8, 15, 46,  7,  6, 54, 65, 11,  3, 35, 36, 16, 12,
       61, 20, 25, 64, 50, 51, 52, 48, 19, 53, 10, 55, 42, 43, 18,  1,  5,
       62, 32, 27, 21,  4, 58,  0, 38, 22, 26, 47, 17, 29, 28, 13, 63, 39,
       14, 24, 56, 33, 34, 40, 44, 45, 41,  9, 59, 49, 23, 30, 60]), 'cur_cost': 90481.0}, {'tour': [58, 39, 45, 12, 60, 2, 5, 15, 36, 40, 41, 3, 4, 55, 53, 56, 57, 59, 61, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 1, 0, 62, 63, 64, 65, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 54, 6, 7, 8, 9, 10, 11, 13], 'cur_cost': 36230.0}, {'tour': array([ 8, 22, 15, 37, 30, 24, 32, 46, 12, 51, 34, 60, 31, 21, 56, 35, 48,
       20, 38, 61, 11, 19, 49, 50,  6,  3, 28, 57, 33, 18, 55, 62, 59, 43,
       26, 64, 65,  2, 10, 40,  1,  5, 14, 13, 58, 23, 52, 63, 39, 17, 54,
       25, 53, 36, 29, 16,  0,  9, 27, 42,  7, 41, 44, 47,  4, 45]), 'cur_cost': 102270.0}, {'tour': array([31, 60, 32, 42, 45,  2, 26, 36, 25, 12, 63, 37,  1, 50,  4,  3, 16,
       18, 21, 47, 27, 20, 14, 40, 34, 35, 46, 48, 55, 24, 51, 29, 49, 19,
       62,  5, 11, 65, 56, 41,  8, 10, 57, 44, 43, 33, 17, 64, 53, 23, 22,
        0, 15, 13,  9, 28, 39, 58, 61, 30, 54, 59, 52,  7,  6, 38]), 'cur_cost': 94276.0}, {'tour': array([15, 42, 16, 13, 39, 20, 60, 62, 14, 32,  2, 54, 51, 50, 17,  9, 59,
       52, 26, 33, 21, 61, 19, 44, 38, 10, 57, 41, 37,  6, 30,  4, 24, 53,
       18, 46, 27, 11, 48, 47,  8, 55, 31,  7, 45, 56,  3, 28, 12,  1, 23,
        0, 43, 49, 25, 34, 36, 35, 63, 29,  5, 40, 64, 65, 22, 58]), 'cur_cost': 104875.0}, {'tour': array([37, 35,  5, 58, 50, 43, 47,  3, 38, 65, 10, 61, 14, 13,  2, 23, 17,
       51, 24, 41,  8, 32, 18, 45, 21, 54, 39, 57, 19,  4,  6, 62, 30, 11,
        0, 56, 25, 63,  9, 22, 31, 36, 55, 59, 49, 40, 33, 12, 29, 26, 48,
       60, 46, 27, 52, 28,  7, 16, 15, 42, 44, 53, 20, 34, 64,  1]), 'cur_cost': 104924.0}, {'tour': [41, 23, 15, 3, 5, 17, 20, 22, 2, 11, 13, 19, 25, 27, 29, 31, 33, 35, 37, 39, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 18, 16, 14, 12, 10, 8, 6, 4, 1, 7, 9, 21, 0], 'cur_cost': 27456.0}]
2025-06-22 17:41:20,530 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:41:20,530 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-22 17:41:20,536 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 17:41:20,536 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 17:41:20,537 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:41:20,537 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [31, 60, 32] and ending with [7, 6, 38]
- Cost: 94276.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8138461538461539
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:41:20,537 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:41:24,902 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    31, 12, 55, 43, 2, 28, 49, 5, 1, 65, 20, 17, 39, 48, 22, 15, 3, 62, 50, 34, 44, 19, 52, 24, 10, 58, 4, 14, 29, 33, 57, 42, 61, 0, 46, 64, 9, 26, 45, 37, 23, 36, 11, 25, 30, 18, 8, 53, 59, 32, 60, 54, 40, 7, 6, 38, 13, 47, 27, 35, 21, 56, 16, 63, 51, 41
  ],
  "modification_strategy": "The strategy prioritizes diversification by creating a path that significantly differs from the current path. It starts with a different initial node and then follows a path that favors exploring regions identified as opportunities within the search context, as well as incorporating a randomized element. This is achieved by selecting a path that attempts to maximize differences from the original by alternating nodes, avoiding the patterns of the original route, and exploring more regions within the opportunity and difficult regions. The ordering is mostly random, but includes a few nodes placed strategically to diversify region exploration.",
  "targeted_regions": "The new path attempts to explore opportunity regions and avoid difficult regions as indicated in the search context. Additionally, it targets a broader distribution of node visits across the entire graph to encourage exploration of previously unvisited segments of the space. The aim is to improve exploration of under-explored areas to identify more efficient paths and avoid areas that are harder to travel through. The approach aims to strike a good balance between diversity and the current knowledge of the search context."
}
```
2025-06-22 17:41:24,902 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:41:24,902 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106415.0, 路径: [31, 12, 55, 43, 2, 28, 49, 5, 1, 65, 20, 17, 39, 48, 22, 15, 3, 62, 50, 34, 44, 19, 52, 24, 10, 58, 4, 14, 29, 33, 57, 42, 61, 0, 46, 64, 9, 26, 45, 37, 23, 36, 11, 25, 30, 18, 8, 53, 59, 32, 60, 54, 40, 7, 6, 38, 13, 47, 27, 35, 21, 56, 16, 63, 51, 41]
2025-06-22 17:41:24,902 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [31, 12, 55, 43, 2, 28, 49, 5, 1, 65, 20, 17, 39, 48, 22, 15, 3, 62, 50, 34, 44, 19, 52, 24, 10, 58, 4, 14, 29, 33, 57, 42, 61, 0, 46, 64, 9, 26, 45, 37, 23, 36, 11, 25, 30, 18, 8, 53, 59, 32, 60, 54, 40, 7, 6, 38, 13, 47, 27, 35, 21, 56, 16, 63, 51, 41], 'cur_cost': 106415.0}
2025-06-22 17:41:24,902 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 17:41:24,902 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:41:24,902 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:41:24,902 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 103052.0
2025-06-22 17:41:25,404 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:41:25,405 - ExploitationExpert - INFO - res_population_costs: [9536, 9557, 9527]
2025-06-22 17:41:25,405 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:41:25,406 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:41:25,406 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': array([14, 19, 31, 52,  1, 21,  8, 16, 38, 34, 32, 35, 20, 42,  7, 23, 64,
       62, 25, 45, 17, 22, 58, 12, 65,  3,  4, 13, 57, 24, 49, 29, 33, 55,
       41, 56, 63, 46, 44, 18, 47,  2, 39, 26,  6, 53, 51, 10, 36, 15, 28,
        5, 54, 48, 60,  0, 30, 50, 11, 40, 61, 27,  9, 37, 43, 59]), 'cur_cost': 114663.0}, {'tour': [18, 23, 16, 27, 34, 41, 48, 55, 62, 5, 12, 19, 26, 33, 40, 47, 54, 61, 4, 11, 17, 24, 31, 38, 45, 52, 59, 2, 9, 15, 22, 29, 36, 43, 50, 57, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 6, 13, 20, 25, 32, 39, 46, 53, 60, 3, 10, 64, 30, 37, 44, 51, 58, 65, 8, 1], 'cur_cost': 61891.0}, {'tour': array([57,  2, 31, 37,  8, 15, 46,  7,  6, 54, 65, 11,  3, 35, 36, 16, 12,
       61, 20, 25, 64, 50, 51, 52, 48, 19, 53, 10, 55, 42, 43, 18,  1,  5,
       62, 32, 27, 21,  4, 58,  0, 38, 22, 26, 47, 17, 29, 28, 13, 63, 39,
       14, 24, 56, 33, 34, 40, 44, 45, 41,  9, 59, 49, 23, 30, 60]), 'cur_cost': 90481.0}, {'tour': [58, 39, 45, 12, 60, 2, 5, 15, 36, 40, 41, 3, 4, 55, 53, 56, 57, 59, 61, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 1, 0, 62, 63, 64, 65, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 54, 6, 7, 8, 9, 10, 11, 13], 'cur_cost': 36230.0}, {'tour': array([ 8, 22, 15, 37, 30, 24, 32, 46, 12, 51, 34, 60, 31, 21, 56, 35, 48,
       20, 38, 61, 11, 19, 49, 50,  6,  3, 28, 57, 33, 18, 55, 62, 59, 43,
       26, 64, 65,  2, 10, 40,  1,  5, 14, 13, 58, 23, 52, 63, 39, 17, 54,
       25, 53, 36, 29, 16,  0,  9, 27, 42,  7, 41, 44, 47,  4, 45]), 'cur_cost': 102270.0}, {'tour': [31, 12, 55, 43, 2, 28, 49, 5, 1, 65, 20, 17, 39, 48, 22, 15, 3, 62, 50, 34, 44, 19, 52, 24, 10, 58, 4, 14, 29, 33, 57, 42, 61, 0, 46, 64, 9, 26, 45, 37, 23, 36, 11, 25, 30, 18, 8, 53, 59, 32, 60, 54, 40, 7, 6, 38, 13, 47, 27, 35, 21, 56, 16, 63, 51, 41], 'cur_cost': 106415.0}, {'tour': array([ 4, 13, 52, 49, 40, 33, 53, 16,  8, 59, 11, 22, 61,  7, 26, 23,  1,
       34, 41, 60, 46, 35, 15, 54, 19, 51, 14, 50, 31, 39, 27, 37, 24, 43,
       20, 12, 47, 17, 45, 63,  2, 48, 10, 18, 32, 62, 56, 55, 21,  9, 30,
       28, 36, 44,  6, 64, 58,  0,  3, 29, 42, 57, 38, 25,  5, 65]), 'cur_cost': 103052.0}, {'tour': array([37, 35,  5, 58, 50, 43, 47,  3, 38, 65, 10, 61, 14, 13,  2, 23, 17,
       51, 24, 41,  8, 32, 18, 45, 21, 54, 39, 57, 19,  4,  6, 62, 30, 11,
        0, 56, 25, 63,  9, 22, 31, 36, 55, 59, 49, 40, 33, 12, 29, 26, 48,
       60, 46, 27, 52, 28,  7, 16, 15, 42, 44, 53, 20, 34, 64,  1]), 'cur_cost': 104924.0}, {'tour': [41, 23, 15, 3, 5, 17, 20, 22, 2, 11, 13, 19, 25, 27, 29, 31, 33, 35, 37, 39, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 18, 16, 14, 12, 10, 8, 6, 4, 1, 7, 9, 21, 0], 'cur_cost': 27456.0}]
2025-06-22 17:41:25,408 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:41:25,408 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-06-22 17:41:25,409 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 17:41:25,409 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 17:41:25,409 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:41:25,409 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [37, 35, 5] and ending with [34, 64, 1]
- Cost: 104924.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8276923076923077
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:41:25,410 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:41:28,761 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    37, 5, 15, 22, 48, 10, 20, 2, 4, 61, 11, 40, 58, 56, 62, 24, 27, 30, 39, 46, 55, 65, 33, 14, 16, 51, 38, 21, 44, 29, 19, 54, 59, 32, 60, 41, 34, 1, 26, 9, 3, 42, 43, 50, 52, 31, 47, 57, 23, 13, 8, 28, 49, 63, 18, 6, 7, 17, 53, 45, 25, 12, 35, 64, 0, 36
  ],
  "modification_strategy": "This path prioritizes diversity by starting from a different point and jumping around to nodes that are geographically distant. The strategy emphasizes exploration of unexplored regions and avoids traversing the existing path's structure. It's crafted to explore the map with a more scattered approach, aiming to find possibly better connections than the original while avoiding 'difficult' regions as much as possible.",
  "targeted_regions": "Unexplored regions and potentially risky areas highlighted in the search context. The path is built to touch these regions while maintaining the diversity metric. "
}
```
2025-06-22 17:41:28,761 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:41:28,761 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99479.0, 路径: [37, 5, 15, 22, 48, 10, 20, 2, 4, 61, 11, 40, 58, 56, 62, 24, 27, 30, 39, 46, 55, 65, 33, 14, 16, 51, 38, 21, 44, 29, 19, 54, 59, 32, 60, 41, 34, 1, 26, 9, 3, 42, 43, 50, 52, 31, 47, 57, 23, 13, 8, 28, 49, 63, 18, 6, 7, 17, 53, 45, 25, 12, 35, 64, 0, 36]
2025-06-22 17:41:28,761 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [37, 5, 15, 22, 48, 10, 20, 2, 4, 61, 11, 40, 58, 56, 62, 24, 27, 30, 39, 46, 55, 65, 33, 14, 16, 51, 38, 21, 44, 29, 19, 54, 59, 32, 60, 41, 34, 1, 26, 9, 3, 42, 43, 50, 52, 31, 47, 57, 23, 13, 8, 28, 49, 63, 18, 6, 7, 17, 53, 45, 25, 12, 35, 64, 0, 36], 'cur_cost': 99479.0}
2025-06-22 17:41:28,761 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 17:41:28,761 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:41:28,761 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:41:28,761 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 113753.0
2025-06-22 17:41:29,270 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:41:29,270 - ExploitationExpert - INFO - res_population_costs: [9536, 9557, 9527]
2025-06-22 17:41:29,270 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:41:29,271 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:41:29,271 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': array([14, 19, 31, 52,  1, 21,  8, 16, 38, 34, 32, 35, 20, 42,  7, 23, 64,
       62, 25, 45, 17, 22, 58, 12, 65,  3,  4, 13, 57, 24, 49, 29, 33, 55,
       41, 56, 63, 46, 44, 18, 47,  2, 39, 26,  6, 53, 51, 10, 36, 15, 28,
        5, 54, 48, 60,  0, 30, 50, 11, 40, 61, 27,  9, 37, 43, 59]), 'cur_cost': 114663.0}, {'tour': [18, 23, 16, 27, 34, 41, 48, 55, 62, 5, 12, 19, 26, 33, 40, 47, 54, 61, 4, 11, 17, 24, 31, 38, 45, 52, 59, 2, 9, 15, 22, 29, 36, 43, 50, 57, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 6, 13, 20, 25, 32, 39, 46, 53, 60, 3, 10, 64, 30, 37, 44, 51, 58, 65, 8, 1], 'cur_cost': 61891.0}, {'tour': array([57,  2, 31, 37,  8, 15, 46,  7,  6, 54, 65, 11,  3, 35, 36, 16, 12,
       61, 20, 25, 64, 50, 51, 52, 48, 19, 53, 10, 55, 42, 43, 18,  1,  5,
       62, 32, 27, 21,  4, 58,  0, 38, 22, 26, 47, 17, 29, 28, 13, 63, 39,
       14, 24, 56, 33, 34, 40, 44, 45, 41,  9, 59, 49, 23, 30, 60]), 'cur_cost': 90481.0}, {'tour': [58, 39, 45, 12, 60, 2, 5, 15, 36, 40, 41, 3, 4, 55, 53, 56, 57, 59, 61, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 1, 0, 62, 63, 64, 65, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 54, 6, 7, 8, 9, 10, 11, 13], 'cur_cost': 36230.0}, {'tour': array([ 8, 22, 15, 37, 30, 24, 32, 46, 12, 51, 34, 60, 31, 21, 56, 35, 48,
       20, 38, 61, 11, 19, 49, 50,  6,  3, 28, 57, 33, 18, 55, 62, 59, 43,
       26, 64, 65,  2, 10, 40,  1,  5, 14, 13, 58, 23, 52, 63, 39, 17, 54,
       25, 53, 36, 29, 16,  0,  9, 27, 42,  7, 41, 44, 47,  4, 45]), 'cur_cost': 102270.0}, {'tour': [31, 12, 55, 43, 2, 28, 49, 5, 1, 65, 20, 17, 39, 48, 22, 15, 3, 62, 50, 34, 44, 19, 52, 24, 10, 58, 4, 14, 29, 33, 57, 42, 61, 0, 46, 64, 9, 26, 45, 37, 23, 36, 11, 25, 30, 18, 8, 53, 59, 32, 60, 54, 40, 7, 6, 38, 13, 47, 27, 35, 21, 56, 16, 63, 51, 41], 'cur_cost': 106415.0}, {'tour': array([ 4, 13, 52, 49, 40, 33, 53, 16,  8, 59, 11, 22, 61,  7, 26, 23,  1,
       34, 41, 60, 46, 35, 15, 54, 19, 51, 14, 50, 31, 39, 27, 37, 24, 43,
       20, 12, 47, 17, 45, 63,  2, 48, 10, 18, 32, 62, 56, 55, 21,  9, 30,
       28, 36, 44,  6, 64, 58,  0,  3, 29, 42, 57, 38, 25,  5, 65]), 'cur_cost': 103052.0}, {'tour': [37, 5, 15, 22, 48, 10, 20, 2, 4, 61, 11, 40, 58, 56, 62, 24, 27, 30, 39, 46, 55, 65, 33, 14, 16, 51, 38, 21, 44, 29, 19, 54, 59, 32, 60, 41, 34, 1, 26, 9, 3, 42, 43, 50, 52, 31, 47, 57, 23, 13, 8, 28, 49, 63, 18, 6, 7, 17, 53, 45, 25, 12, 35, 64, 0, 36], 'cur_cost': 99479.0}, {'tour': array([44, 20, 59, 21,  7, 37, 62, 58, 12, 11, 51, 27, 49, 28, 50, 60, 30,
       35, 29, 26, 38, 57, 32, 64,  4,  9, 24, 46, 45, 19, 14, 52,  2, 48,
        1, 55, 43, 53, 33, 18, 61, 15,  6, 36, 54, 56, 34, 65, 17, 25, 13,
       41,  8, 10, 39, 23,  5, 40, 16, 31,  3, 42, 22, 63,  0, 47]), 'cur_cost': 113753.0}]
2025-06-22 17:41:29,273 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:41:29,274 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-06-22 17:41:29,275 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 17:41:29,275 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [18, 23, 16, 27, 34, 41, 48, 55, 62, 5, 12, 19, 26, 33, 40, 47, 54, 61, 4, 11, 17, 24, 31, 38, 45, 52, 59, 2, 9, 15, 22, 29, 36, 43, 50, 57, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 6, 13, 20, 25, 32, 39, 46, 53, 60, 3, 10, 64, 30, 37, 44, 51, 58, 65, 8, 1], 'cur_cost': 61891.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [58, 39, 45, 12, 60, 2, 5, 15, 36, 40, 41, 3, 4, 55, 53, 56, 57, 59, 61, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 1, 0, 62, 63, 64, 65, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 54, 6, 7, 8, 9, 10, 11, 13], 'cur_cost': 36230.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [31, 12, 55, 43, 2, 28, 49, 5, 1, 65, 20, 17, 39, 48, 22, 15, 3, 62, 50, 34, 44, 19, 52, 24, 10, 58, 4, 14, 29, 33, 57, 42, 61, 0, 46, 64, 9, 26, 45, 37, 23, 36, 11, 25, 30, 18, 8, 53, 59, 32, 60, 54, 40, 7, 6, 38, 13, 47, 27, 35, 21, 56, 16, 63, 51, 41], 'cur_cost': 106415.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [37, 5, 15, 22, 48, 10, 20, 2, 4, 61, 11, 40, 58, 56, 62, 24, 27, 30, 39, 46, 55, 65, 33, 14, 16, 51, 38, 21, 44, 29, 19, 54, 59, 32, 60, 41, 34, 1, 26, 9, 3, 42, 43, 50, 52, 31, 47, 57, 23, 13, 8, 28, 49, 63, 18, 6, 7, 17, 53, 45, 25, 12, 35, 64, 0, 36], 'cur_cost': 99479.0}}]
2025-06-22 17:41:29,275 - __main__ - INFO - 进化阶段完成
2025-06-22 17:41:29,275 - __main__ - INFO - 开始评估阶段
2025-06-22 17:41:29,275 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 17:41:29,276 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 17:41:29,277 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 1/5
- Population changes:
  * Cost: min 13971.0->11408.0, mean 54215.2->83964.2
  * Diversity: 0.6615384615384615->0.8184615384615385
  * Elite solutions: 1->1

## Strategy Performance:
- Explore: 5 individuals, success rate 40.0%, avg improvement -10754.2
- Exploit: 5 individuals, success rate 20.0%, avg improvement -48743.8
- Overall improvement: 2563.0

## Historical Trends:
Last 1 iterations: costs [13971.0], diversity [0.8730639730639731]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 17:41:34,446 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Improvement/Convergence",
    "features": "Initial cost reduction observed, diversity improving, but exploitation seems ineffective and overall improvement is modest. Elite solutions are present.",
    "justification": "The cost has decreased significantly, indicating initial improvement. Diversity is increasing, suggesting the population is exploring a broader search space. However, the mean cost is increasing, suggesting some detrimental changes. Overall improvement is positive, but smaller compared to cost reduction indicating a slow down in the rate of improvement"
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Moderately effective. 40% success rate with a negative average improvement indicates that while some exploration is yielding beneficial results, the gains are small.",
      "analysis": "Explore individuals are producing both good and bad results. The positive success rate justifies continued exploration. However, the gains are smaller than the cost reduction and exploitation failures indicating improvements are not yet optimized."
    },
    "exploit": {
      "performance": "Ineffective. A 20% success rate with a significant negative average improvement suggests exploitation attempts are actively detrimental. The high negative average improvement is particularly concerning.",
      "analysis": "The exploitation strategy is failing, resulting in a significant increase in the cost for those individuals. The high negative average improvement suggests a problem in the specific individuals being exploited."
    },
    "overall": {
      "performance": "Positive, but with a slowing rate. The modest overall improvement reflects the balance between explore and exploit's influence.",
      "analysis": "The small rate of improvement indicates that the performance of the model has slowed from the initial improvement, and it also suggests that the ratio of good results to bad results is shrinking."
    }
  },
  "balance_state": {
    "assessment": "Over-exploitation. The extremely poor performance of exploitation suggests that the current exploitation strategy is either flawed, being applied in an inappropriate area of the search space, or using methods not suited to the problem.",
    "adjustment_needs": "Significantly decrease exploitation attempts. Increase exploration in areas where improvements can be more easily made.  Consider modifying/tuning the exploitation strategy or delaying it altogether."
  },
  "recommendations": {
    "next_iteration": [
      "**Prioritize Exploration:** Drastically increase the proportion of exploration individuals, possibly to 80% or more.  This will allow a deeper search of the space.",
      "**Review Exploitation:**  If the exploitation strategy is complex, analyze the failure modes and either refine the existing strategy or delay exploitation attempts until later stages when better solutions are known.",
      "**Consider Exploration Techniques:** Introduce techniques like random mutations with larger step sizes or diverse search paths that encourage wide search.",
      "**Monitor Convergence closely:** Keep a close eye on cost and diversity trends to avoid premature convergence on local optima. Consider introducing a diversity maintenance mechanism."
    ]
  }
}
```
2025-06-22 17:41:34,474 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 17:41:34,474 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Improvement/Convergence",
    "features": "Initial cost reduction observed, diversity improving, but exploitation seems ineffective and overall improvement is modest. Elite solutions are present.",
    "justification": "The cost has decreased significantly, indicating initial improvement. Diversity is increasing, suggesting the population is exploring a broader search space. However, the mean cost is increasing, suggesting some detrimental changes. Overall improvement is positive, but smaller compared to cost reduction indicating a slow down in the rate of improvement"
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Moderately effective. 40% success rate with a negative average improvement indicates that while some exploration is yielding beneficial results, the gains are small.",
      "analysis": "Explore individuals are producing both good and bad results. The positive success rate justifies continued exploration. However, the gains are smaller than the cost reduction and exploitation failures indicating improvements are not yet optimized."
    },
    "exploit": {
      "performance": "Ineffective. A 20% success rate with a significant negative average improvement suggests exploitation attempts are actively detrimental. The high negative average improvement is particularly concerning.",
      "analysis": "The exploitation strategy is failing, resulting in a significant increase in the cost for those individuals. The high negative average improvement suggests a problem in the specific individuals being exploited."
    },
    "overall": {
      "performance": "Positive, but with a slowing rate. The modest overall improvement reflects the balance between explore and exploit's influence.",
      "analysis": "The small rate of improvement indicates that the performance of the model has slowed from the initial improvement, and it also suggests that the ratio of good results to bad results is shrinking."
    }
  },
  "balance_state": {
    "assessment": "Over-exploitation. The extremely poor performance of exploitation suggests that the current exploitation strategy is either flawed, being applied in an inappropriate area of the search space, or using methods not suited to the problem.",
    "adjustment_needs": "Significantly decrease exploitation attempts. Increase exploration in areas where improvements can be more easily made.  Consider modifying/tuning the exploitation strategy or delaying it altogether."
  },
  "recommendations": {
    "next_iteration": [
      "**Prioritize Exploration:** Drastically increase the proportion of exploration individuals, possibly to 80% or more.  This will allow a deeper search of the space.",
      "**Review Exploitation:**  If the exploitation strategy is complex, analyze the failure modes and either refine the existing strategy or delay exploitation attempts until later stages when better solutions are known.",
      "**Consider Exploration Techniques:** Introduce techniques like random mutations with larger step sizes or diverse search paths that encourage wide search.",
      "**Monitor Convergence closely:** Keep a close eye on cost and diversity trends to avoid premature convergence on local optima. Consider introducing a diversity maintenance mechanism."
    ]
  }
}
```
2025-06-22 17:41:34,476 - __main__ - INFO - 评估阶段完成
2025-06-22 17:41:34,476 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Improvement/Convergence",
    "features": "Initial cost reduction observed, diversity improving, but exploitation seems ineffective and overall improvement is modest. Elite solutions are present.",
    "justification": "The cost has decreased significantly, indicating initial improvement. Diversity is increasing, suggesting the population is exploring a broader search space. However, the mean cost is increasing, suggesting some detrimental changes. Overall improvement is positive, but smaller compared to cost reduction indicating a slow down in the rate of improvement"
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Moderately effective. 40% success rate with a negative average improvement indicates that while some exploration is yielding beneficial results, the gains are small.",
      "analysis": "Explore individuals are producing both good and bad results. The positive success rate justifies continued exploration. However, the gains are smaller than the cost reduction and exploitation failures indicating improvements are not yet optimized."
    },
    "exploit": {
      "performance": "Ineffective. A 20% success rate with a significant negative average improvement suggests exploitation attempts are actively detrimental. The high negative average improvement is particularly concerning.",
      "analysis": "The exploitation strategy is failing, resulting in a significant increase in the cost for those individuals. The high negative average improvement suggests a problem in the specific individuals being exploited."
    },
    "overall": {
      "performance": "Positive, but with a slowing rate. The modest overall improvement reflects the balance between explore and exploit's influence.",
      "analysis": "The small rate of improvement indicates that the performance of the model has slowed from the initial improvement, and it also suggests that the ratio of good results to bad results is shrinking."
    }
  },
  "balance_state": {
    "assessment": "Over-exploitation. The extremely poor performance of exploitation suggests that the current exploitation strategy is either flawed, being applied in an inappropriate area of the search space, or using methods not suited to the problem.",
    "adjustment_needs": "Significantly decrease exploitation attempts. Increase exploration in areas where improvements can be more easily made.  Consider modifying/tuning the exploitation strategy or delaying it altogether."
  },
  "recommendations": {
    "next_iteration": [
      "**Prioritize Exploration:** Drastically increase the proportion of exploration individuals, possibly to 80% or more.  This will allow a deeper search of the space.",
      "**Review Exploitation:**  If the exploitation strategy is complex, analyze the failure modes and either refine the existing strategy or delay exploitation attempts until later stages when better solutions are known.",
      "**Consider Exploration Techniques:** Introduce techniques like random mutations with larger step sizes or diverse search paths that encourage wide search.",
      "**Monitor Convergence closely:** Keep a close eye on cost and diversity trends to avoid premature convergence on local optima. Consider introducing a diversity maintenance mechanism."
    ]
  }
}
```
2025-06-22 17:41:34,476 - __main__ - INFO - 当前最佳适应度: 11408.0
2025-06-22 17:41:34,476 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_1.pkl
2025-06-22 17:41:34,476 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-06-22 17:41:34,476 - __main__ - INFO - 开始分析阶段
2025-06-22 17:41:34,476 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:41:34,495 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11408.0, 'max': 114663.0, 'mean': 83964.2, 'std': 33684.764564413985}, 'diversity': 0.9515151515151514, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:41:34,497 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11408.0, 'max': 114663.0, 'mean': 83964.2, 'std': 33684.764564413985}, 'diversity_level': 0.9515151515151514, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:41:34,497 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:41:34,497 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:41:34,497 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:41:34,499 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 17:41:34,499 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:41:34,499 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 17:41:34,499 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:41:34,499 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:41:34,499 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:41:34,499 - EliteExpert - INFO - 精英解分析完成
2025-06-22 17:41:34,499 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {'(62, 59)': 1.0, '(56, 58)': 1.0}, 'common_edge_ratio': 0.030303030303030304}, 'fixed_nodes': [{'node': 0, 'position': 0}, {'node': 20, 'position': 33}], 'population_gap': {'cost_gap': {'min_gap': 1881.0, 'avg_gap': 74424.2}, 'structure_gap': {'unique_elite_edges': 107, 'unique_pop_edges': 545, 'common_edges': 35}}, 'elite_diversity': {'diversity_score': 0.1767676767676768}}
2025-06-22 17:41:34,499 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:41:34,499 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:41:34,499 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:41:34,499 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=11408.0, max=114663.0, mean=83964.2, std=33684.764564413985
- Diversity: 0.9515151515151514, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 2 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 0 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:41:41,739 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "The cost distribution suggests a potentially rugged search space, with a large range (min to max) and a significant standard deviation. The lack of high-quality edges and common subpaths, along with 0% shared edges in elite solutions, further hints at potential fragmentation or a lack of strong building blocks. However, with only 10 individuals, it is difficult to make a definitive conclusion about the full landscape.",
    "modality": "The mean cost is significantly higher than the minimum, suggesting multiple local optima or a non-uniformly distributed cost landscape. The absence of identified difficult or opportunity regions makes it difficult to characterize the modality beyond this rough assessment. Further investigation with a larger population would be beneficial.",
    "additional_notes": "The small population size limits our ability to fully analyze the search space. More data points are needed to reliably determine the landscape characteristics and identify potential challenges or opportunities. The high standard deviation relative to the mean warrants careful examination of solution distribution."
  },
  "population_state": {
    "diversity": "High (0.95) – The population shows good diversity. This is positive, as it suggests the search is exploring a wide range of the search space, minimizing the risk of premature convergence on a single local optimum.",
    "convergence": "Very low (0.0) – There is no convergence. This means the population is not yet focusing on promising areas, which, given the diversity, is expected. It also may indicate that the search is not making good progress. Further analysis is needed to understand why.",
    "additional_notes": "The combination of high diversity and zero convergence suggests the search is still in its early stages or not effectively exploiting promising regions. The lack of identified elite solutions supports this."
  },
  "difficult_regions": {
    "challenges": "Based on the data, no specific difficult regions are identified. However, the high range and standard deviation in cost, combined with the lack of convergence, could suggest that there are regions of the search space where progress is slow or that are particularly challenging to traverse. The absence of any identified elite solutions suggests the current search strategy is not effective in finding high-quality solutions.",
    "related_factors": "The absence of shared edges in the elite solutions may indicate that the solutions in the current population are scattered within the search space rather than having clustered in a specific region."
  },
  "opportunity_regions": {
    "potential": "Based on the data, no specific opportunity regions are identified. The high diversity suggests that potentially unexplored promising regions exist. Considering the wide cost range, it's possible there are solutions with significantly lower costs to be discovered.",
    "considerations": "The high diversity and lack of convergence provide an opportunity for exploration. A focused approach is probably not optimal at this stage. More aggressive exploration may be beneficial."
  },
  "evolution_direction": {
    "strategy": "Focus on Exploration and potentially some diversification techniques. Given the high diversity and low convergence, the priority should be to maintain or even increase the exploration of the search space. Evaluate fitness across a range of the search space. The lack of convergence could indicate there is no strong signal for the direction to follow. The search should continue exploration while the population size grows.",
    "specific_recommendations": [
      "Increase the population size. A larger population will allow for more thorough exploration and more robust analysis of the landscape.",
      "Evaluate the current search algorithm's exploration mechanisms. Are they sufficiently diverse? Consider techniques that encourage more exploration (e.g., higher mutation rates, different crossover operators).",
      "If appropriate, consider a method for recording better solutions.",
      "Monitor the evolution of diversity and convergence over time.  Look for any signs of premature convergence and adapt the strategy as needed. Continue the exploration of a large search space to ensure the best solution can be found."
    ]
  }
}
```
2025-06-22 17:41:41,739 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:41:41,739 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'The cost distribution suggests a potentially rugged search space, with a large range (min to max) and a significant standard deviation. The lack of high-quality edges and common subpaths, along with 0% shared edges in elite solutions, further hints at potential fragmentation or a lack of strong building blocks. However, with only 10 individuals, it is difficult to make a definitive conclusion about the full landscape.', 'modality': 'The mean cost is significantly higher than the minimum, suggesting multiple local optima or a non-uniformly distributed cost landscape. The absence of identified difficult or opportunity regions makes it difficult to characterize the modality beyond this rough assessment. Further investigation with a larger population would be beneficial.', 'additional_notes': 'The small population size limits our ability to fully analyze the search space. More data points are needed to reliably determine the landscape characteristics and identify potential challenges or opportunities. The high standard deviation relative to the mean warrants careful examination of solution distribution.'}, 'population_state': {'diversity': 'High (0.95) – The population shows good diversity. This is positive, as it suggests the search is exploring a wide range of the search space, minimizing the risk of premature convergence on a single local optimum.', 'convergence': 'Very low (0.0) – There is no convergence. This means the population is not yet focusing on promising areas, which, given the diversity, is expected. It also may indicate that the search is not making good progress. Further analysis is needed to understand why.', 'additional_notes': 'The combination of high diversity and zero convergence suggests the search is still in its early stages or not effectively exploiting promising regions. The lack of identified elite solutions supports this.'}, 'difficult_regions': {'challenges': 'Based on the data, no specific difficult regions are identified. However, the high range and standard deviation in cost, combined with the lack of convergence, could suggest that there are regions of the search space where progress is slow or that are particularly challenging to traverse. The absence of any identified elite solutions suggests the current search strategy is not effective in finding high-quality solutions.', 'related_factors': 'The absence of shared edges in the elite solutions may indicate that the solutions in the current population are scattered within the search space rather than having clustered in a specific region.'}, 'opportunity_regions': {'potential': "Based on the data, no specific opportunity regions are identified. The high diversity suggests that potentially unexplored promising regions exist. Considering the wide cost range, it's possible there are solutions with significantly lower costs to be discovered.", 'considerations': 'The high diversity and lack of convergence provide an opportunity for exploration. A focused approach is probably not optimal at this stage. More aggressive exploration may be beneficial.'}, 'evolution_direction': {'strategy': 'Focus on Exploration and potentially some diversification techniques. Given the high diversity and low convergence, the priority should be to maintain or even increase the exploration of the search space. Evaluate fitness across a range of the search space. The lack of convergence could indicate there is no strong signal for the direction to follow. The search should continue exploration while the population size grows.', 'specific_recommendations': ['Increase the population size. A larger population will allow for more thorough exploration and more robust analysis of the landscape.', "Evaluate the current search algorithm's exploration mechanisms. Are they sufficiently diverse? Consider techniques that encourage more exploration (e.g., higher mutation rates, different crossover operators).", 'If appropriate, consider a method for recording better solutions.', 'Monitor the evolution of diversity and convergence over time.  Look for any signs of premature convergence and adapt the strategy as needed. Continue the exploration of a large search space to ensure the best solution can be found.']}}
2025-06-22 17:41:41,739 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:41:41,739 - __main__ - INFO - 分析阶段完成
2025-06-22 17:41:41,739 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'The cost distribution suggests a potentially rugged search space, with a large range (min to max) and a significant standard deviation. The lack of high-quality edges and common subpaths, along with 0% shared edges in elite solutions, further hints at potential fragmentation or a lack of strong building blocks. However, with only 10 individuals, it is difficult to make a definitive conclusion about the full landscape.', 'modality': 'The mean cost is significantly higher than the minimum, suggesting multiple local optima or a non-uniformly distributed cost landscape. The absence of identified difficult or opportunity regions makes it difficult to characterize the modality beyond this rough assessment. Further investigation with a larger population would be beneficial.', 'additional_notes': 'The small population size limits our ability to fully analyze the search space. More data points are needed to reliably determine the landscape characteristics and identify potential challenges or opportunities. The high standard deviation relative to the mean warrants careful examination of solution distribution.'}, 'population_state': {'diversity': 'High (0.95) – The population shows good diversity. This is positive, as it suggests the search is exploring a wide range of the search space, minimizing the risk of premature convergence on a single local optimum.', 'convergence': 'Very low (0.0) – There is no convergence. This means the population is not yet focusing on promising areas, which, given the diversity, is expected. It also may indicate that the search is not making good progress. Further analysis is needed to understand why.', 'additional_notes': 'The combination of high diversity and zero convergence suggests the search is still in its early stages or not effectively exploiting promising regions. The lack of identified elite solutions supports this.'}, 'difficult_regions': {'challenges': 'Based on the data, no specific difficult regions are identified. However, the high range and standard deviation in cost, combined with the lack of convergence, could suggest that there are regions of the search space where progress is slow or that are particularly challenging to traverse. The absence of any identified elite solutions suggests the current search strategy is not effective in finding high-quality solutions.', 'related_factors': 'The absence of shared edges in the elite solutions may indicate that the solutions in the current population are scattered within the search space rather than having clustered in a specific region.'}, 'opportunity_regions': {'potential': "Based on the data, no specific opportunity regions are identified. The high diversity suggests that potentially unexplored promising regions exist. Considering the wide cost range, it's possible there are solutions with significantly lower costs to be discovered.", 'considerations': 'The high diversity and lack of convergence provide an opportunity for exploration. A focused approach is probably not optimal at this stage. More aggressive exploration may be beneficial.'}, 'evolution_direction': {'strategy': 'Focus on Exploration and potentially some diversification techniques. Given the high diversity and low convergence, the priority should be to maintain or even increase the exploration of the search space. Evaluate fitness across a range of the search space. The lack of convergence could indicate there is no strong signal for the direction to follow. The search should continue exploration while the population size grows.', 'specific_recommendations': ['Increase the population size. A larger population will allow for more thorough exploration and more robust analysis of the landscape.', "Evaluate the current search algorithm's exploration mechanisms. Are they sufficiently diverse? Consider techniques that encourage more exploration (e.g., higher mutation rates, different crossover operators).", 'If appropriate, consider a method for recording better solutions.', 'Monitor the evolution of diversity and convergence over time.  Look for any signs of premature convergence and adapt the strategy as needed. Continue the exploration of a large search space to ensure the best solution can be found.']}}
2025-06-22 17:41:41,739 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:41:41,739 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:41:41,739 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 2
- Population: 10 individuals, diversity=High (0.95) – The population shows good diversity. This is positive, as it suggests the search is exploring a wide range of the search space, minimizing the risk of premature convergence on a single local optimum.
- Best cost: 11408.0, Mean cost: 83964.2
- Search space: Ruggedness: The cost distribution suggests a potentially rugged search space, with a large range (min to max) and a significant standard deviation. The lack of high-quality edges and common subpaths, along with 0% shared edges in elite solutions, further hints at potential fragmentation or a lack of strong building blocks. However, with only 10 individuals, it is difficult to make a definitive conclusion about the full landscape., Modality: The mean cost is significantly higher than the minimum, suggesting multiple local optima or a non-uniformly distributed cost landscape. The absence of identified difficult or opportunity regions makes it difficult to characterize the modality beyond this rough assessment. Further investigation with a larger population would be beneficial.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early Improvement/Convergence",
    "features": "Initial cost reduction observed, diversity improving, but exploitation seems ineffective and overall imp...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:41:41,739 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:41:44,234 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.2,
      "exploration_intensity": 0.8,
      "region_influence": 0.6
    },
    "exploit": {
      "mutation_rate": 0.05,
      "step_size": 0.05,
      "exploitation_intensity": 0.9,
      "region_influence": 0.1
    }
  }
}
```

2025-06-22 17:41:44,234 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 17:41:44,236 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 17:41:44,236 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.2,
      "exploration_intensity": 0.8,
      "region_influence": 0.6
    },
    "exploit": {
      "mutation_rate": 0.05,
      "step_size": 0.05,
      "exploitation_intensity": 0.9,
      "region_influence": 0.1
    }
  }
}
```

2025-06-22 17:41:44,236 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:41:44,236 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 17:41:44,237 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.2,
      "exploration_intensity": 0.8,
      "region_influence": 0.6
    },
    "exploit": {
      "mutation_rate": 0.05,
      "step_size": 0.05,
      "exploitation_intensity": 0.9,
      "region_influence": 0.1
    }
  }
}
```

2025-06-22 17:41:44,237 - __main__ - INFO - 开始进化阶段
2025-06-22 17:41:44,237 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:41:44,237 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:41:44,237 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [50, 38, 51] and ending with [47, 48, 49]
- Cost: 11408.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8184615384615385
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:41:44,238 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:41:47,714 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49
  ],
  "modification_strategy": "The strategy prioritizes exploration of unexplored regions and targets the opportunity regions indicated in the search context. It starts with the same beginning nodes as the original path, then explores nodes near the end and beginning of the original path to disrupt the existing path structure, increasing diversity. It attempts to improve the path by adding nodes in unexplored regions.",
  "targeted_regions": "Focused on the opportunity regions and aims to diversify by exploring new regions and avoid the difficult ones. It tries to visit the nodes in different order compared to the original path, and to visit the nodes that are at the border of the regions."
}
```
2025-06-22 17:41:47,715 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:41:47,715 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11408.0, 路径: [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49]
2025-06-22 17:41:47,715 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}
2025-06-22 17:41:47,716 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 17:41:47,716 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:41:47,716 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [14, 19, 31] and ending with [37, 43, 59]
- Cost: 114663.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8184615384615385
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:41:47,717 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:41:51,305 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    14, 19, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30
  ],
  "modification_strategy": "This strategy prioritizes diversification by heavily altering the order of nodes, especially in the middle sections. It explores unexplored regions by shifting and interweaving nodes more aggressively, based on the understanding of diverse paths.",
  "targeted_regions": "The new path focuses on revisiting the beginning and end regions to check if any of the nodes in these areas have shifted positions and if any further optimization is possible."
}
```
2025-06-22 17:41:51,305 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:41:51,305 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13904.0, 路径: [14, 19, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]
2025-06-22 17:41:51,307 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [14, 19, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 13904.0}
2025-06-22 17:41:51,307 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 17:41:51,307 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:41:51,307 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [18, 23, 16] and ending with [65, 8, 1]
- Cost: 61891.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7492307692307693
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:41:51,309 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:41:55,118 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    18, 23, 16, 2, 55, 48, 6, 12, 33, 20, 40, 50, 30, 5, 26, 42, 10, 58, 13, 60, 3, 35, 11, 4, 14, 21, 62, 45, 24, 36, 37, 44, 63, 15, 27, 57, 17, 31, 46, 8, 54, 61, 32, 39, 53, 28, 49, 59, 34, 9, 22, 65, 1, 41, 56, 64, 29, 0, 38, 52, 47, 25, 19, 43
  ],
  "modification_strategy": "This strategy prioritizes exploration of unexplored and opportunity regions. It starts similarly to the original path but then diverges significantly, incorporating nodes in what appears to be a more distributed manner to increase diversity. It uses a random permutation of the nodes to maximize the difference between the two paths. The strategy also aims to avoid known difficult regions by strategically positioning nodes that are adjacent to or within those regions later in the sequence.",
  "targeted_regions": "Unexplored regions and opportunity regions, aiming for wider distribution across the solution space to improve diversity."
}
```
2025-06-22 17:41:55,118 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:41:55,118 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116267.0, 路径: [18, 23, 16, 2, 55, 48, 6, 12, 33, 20, 40, 50, 30, 5, 26, 42, 10, 58, 13, 60, 3, 35, 11, 4, 14, 21, 62, 45, 24, 36, 37, 44, 63, 15, 27, 57, 17, 31, 46, 8, 54, 61, 32, 39, 53, 28, 49, 59, 34, 9, 22, 65, 1, 41, 56, 64, 29, 0, 38, 52, 47, 25, 19, 43, 7, 51]
2025-06-22 17:41:55,119 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [18, 23, 16, 2, 55, 48, 6, 12, 33, 20, 40, 50, 30, 5, 26, 42, 10, 58, 13, 60, 3, 35, 11, 4, 14, 21, 62, 45, 24, 36, 37, 44, 63, 15, 27, 57, 17, 31, 46, 8, 54, 61, 32, 39, 53, 28, 49, 59, 34, 9, 22, 65, 1, 41, 56, 64, 29, 0, 38, 52, 47, 25, 19, 43, 7, 51], 'cur_cost': 116267.0}
2025-06-22 17:41:55,119 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 17:41:55,119 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:41:55,119 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [57, 2, 31] and ending with [23, 30, 60]
- Cost: 90481.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7523076923076923
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:41:55,119 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:41:59,198 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    57, 12, 45, 38, 5, 22, 64, 1, 18, 49, 33, 60, 51, 29, 15, 0, 34, 41, 62, 54, 8, 19, 27, 47, 65, 10, 2, 39, 32, 26, 52, 58, 42, 6, 21, 30, 4, 40, 11, 35, 59, 56, 24, 44, 36, 3, 23, 46, 16, 53, 63, 55, 48, 37, 9, 14, 25, 7, 50, 13, 28, 17, 43, 61, 31, 20
  ],
  "modification_strategy": "Heuristic path generation emphasizing exploration of unexplored regions and difficult regions, by attempting to create a more diverse sequence. I prioritize transitions that maximize distance from nodes in the original path, especially at crucial points. The starting and ending nodes, while kept from the original path, are used as a starting and ending point to ensure valid problem parameters.",
  "targeted_regions": "Unexplored regions and regions deemed difficult to avoid are targeted for a more diverse search, by attempting to avoid nodes that are close to each other in the original path and instead prioritize more disparate locations."
}
```
2025-06-22 17:41:59,199 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:41:59,199 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112476.0, 路径: [57, 12, 45, 38, 5, 22, 64, 1, 18, 49, 33, 60, 51, 29, 15, 0, 34, 41, 62, 54, 8, 19, 27, 47, 65, 10, 2, 39, 32, 26, 52, 58, 42, 6, 21, 30, 4, 40, 11, 35, 59, 56, 24, 44, 36, 3, 23, 46, 16, 53, 63, 55, 48, 37, 9, 14, 25, 7, 50, 13, 28, 17, 43, 61, 31, 20]
2025-06-22 17:41:59,199 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [57, 12, 45, 38, 5, 22, 64, 1, 18, 49, 33, 60, 51, 29, 15, 0, 34, 41, 62, 54, 8, 19, 27, 47, 65, 10, 2, 39, 32, 26, 52, 58, 42, 6, 21, 30, 4, 40, 11, 35, 59, 56, 24, 44, 36, 3, 23, 46, 16, 53, 63, 55, 48, 37, 9, 14, 25, 7, 50, 13, 28, 17, 43, 61, 31, 20], 'cur_cost': 112476.0}
2025-06-22 17:41:59,199 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-22 17:41:59,199 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:41:59,199 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:41:59,201 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 102907.0
2025-06-22 17:41:59,707 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:41:59,707 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557]
2025-06-22 17:41:59,707 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:41:59,710 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:41:59,710 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': [14, 19, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 13904.0}, {'tour': [18, 23, 16, 2, 55, 48, 6, 12, 33, 20, 40, 50, 30, 5, 26, 42, 10, 58, 13, 60, 3, 35, 11, 4, 14, 21, 62, 45, 24, 36, 37, 44, 63, 15, 27, 57, 17, 31, 46, 8, 54, 61, 32, 39, 53, 28, 49, 59, 34, 9, 22, 65, 1, 41, 56, 64, 29, 0, 38, 52, 47, 25, 19, 43, 7, 51], 'cur_cost': 116267.0}, {'tour': [57, 12, 45, 38, 5, 22, 64, 1, 18, 49, 33, 60, 51, 29, 15, 0, 34, 41, 62, 54, 8, 19, 27, 47, 65, 10, 2, 39, 32, 26, 52, 58, 42, 6, 21, 30, 4, 40, 11, 35, 59, 56, 24, 44, 36, 3, 23, 46, 16, 53, 63, 55, 48, 37, 9, 14, 25, 7, 50, 13, 28, 17, 43, 61, 31, 20], 'cur_cost': 112476.0}, {'tour': array([42, 24, 49, 57, 16, 41,  5, 51, 64, 38, 34, 31, 61, 32,  9, 19, 27,
       29,  4, 54, 59, 13, 17, 23, 39, 50, 20, 46, 48, 43, 35, 65, 36, 14,
       58, 26, 18,  8, 52,  1, 44, 55, 33, 30,  3, 28, 60, 56, 53,  2, 15,
       37, 62,  0, 22, 63, 21, 10,  6,  7, 12, 45, 11, 47, 25, 40]), 'cur_cost': 102907.0}, {'tour': array([ 8, 22, 15, 37, 30, 24, 32, 46, 12, 51, 34, 60, 31, 21, 56, 35, 48,
       20, 38, 61, 11, 19, 49, 50,  6,  3, 28, 57, 33, 18, 55, 62, 59, 43,
       26, 64, 65,  2, 10, 40,  1,  5, 14, 13, 58, 23, 52, 63, 39, 17, 54,
       25, 53, 36, 29, 16,  0,  9, 27, 42,  7, 41, 44, 47,  4, 45]), 'cur_cost': 102270.0}, {'tour': [31, 12, 55, 43, 2, 28, 49, 5, 1, 65, 20, 17, 39, 48, 22, 15, 3, 62, 50, 34, 44, 19, 52, 24, 10, 58, 4, 14, 29, 33, 57, 42, 61, 0, 46, 64, 9, 26, 45, 37, 23, 36, 11, 25, 30, 18, 8, 53, 59, 32, 60, 54, 40, 7, 6, 38, 13, 47, 27, 35, 21, 56, 16, 63, 51, 41], 'cur_cost': 106415.0}, {'tour': array([ 4, 13, 52, 49, 40, 33, 53, 16,  8, 59, 11, 22, 61,  7, 26, 23,  1,
       34, 41, 60, 46, 35, 15, 54, 19, 51, 14, 50, 31, 39, 27, 37, 24, 43,
       20, 12, 47, 17, 45, 63,  2, 48, 10, 18, 32, 62, 56, 55, 21,  9, 30,
       28, 36, 44,  6, 64, 58,  0,  3, 29, 42, 57, 38, 25,  5, 65]), 'cur_cost': 103052.0}, {'tour': [37, 5, 15, 22, 48, 10, 20, 2, 4, 61, 11, 40, 58, 56, 62, 24, 27, 30, 39, 46, 55, 65, 33, 14, 16, 51, 38, 21, 44, 29, 19, 54, 59, 32, 60, 41, 34, 1, 26, 9, 3, 42, 43, 50, 52, 31, 47, 57, 23, 13, 8, 28, 49, 63, 18, 6, 7, 17, 53, 45, 25, 12, 35, 64, 0, 36], 'cur_cost': 99479.0}, {'tour': array([44, 20, 59, 21,  7, 37, 62, 58, 12, 11, 51, 27, 49, 28, 50, 60, 30,
       35, 29, 26, 38, 57, 32, 64,  4,  9, 24, 46, 45, 19, 14, 52,  2, 48,
        1, 55, 43, 53, 33, 18, 61, 15,  6, 36, 54, 56, 34, 65, 17, 25, 13,
       41,  8, 10, 39, 23,  5, 40, 16, 31,  3, 42, 22, 63,  0, 47]), 'cur_cost': 113753.0}]
2025-06-22 17:41:59,712 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:41:59,713 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-06-22 17:41:59,713 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-22 17:41:59,713 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 17:41:59,713 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:41:59,713 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:41:59,713 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 111427.0
2025-06-22 17:42:00,225 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:42:00,225 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557]
2025-06-22 17:42:00,225 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:42:00,225 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:42:00,225 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': [14, 19, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 13904.0}, {'tour': [18, 23, 16, 2, 55, 48, 6, 12, 33, 20, 40, 50, 30, 5, 26, 42, 10, 58, 13, 60, 3, 35, 11, 4, 14, 21, 62, 45, 24, 36, 37, 44, 63, 15, 27, 57, 17, 31, 46, 8, 54, 61, 32, 39, 53, 28, 49, 59, 34, 9, 22, 65, 1, 41, 56, 64, 29, 0, 38, 52, 47, 25, 19, 43, 7, 51], 'cur_cost': 116267.0}, {'tour': [57, 12, 45, 38, 5, 22, 64, 1, 18, 49, 33, 60, 51, 29, 15, 0, 34, 41, 62, 54, 8, 19, 27, 47, 65, 10, 2, 39, 32, 26, 52, 58, 42, 6, 21, 30, 4, 40, 11, 35, 59, 56, 24, 44, 36, 3, 23, 46, 16, 53, 63, 55, 48, 37, 9, 14, 25, 7, 50, 13, 28, 17, 43, 61, 31, 20], 'cur_cost': 112476.0}, {'tour': array([42, 24, 49, 57, 16, 41,  5, 51, 64, 38, 34, 31, 61, 32,  9, 19, 27,
       29,  4, 54, 59, 13, 17, 23, 39, 50, 20, 46, 48, 43, 35, 65, 36, 14,
       58, 26, 18,  8, 52,  1, 44, 55, 33, 30,  3, 28, 60, 56, 53,  2, 15,
       37, 62,  0, 22, 63, 21, 10,  6,  7, 12, 45, 11, 47, 25, 40]), 'cur_cost': 102907.0}, {'tour': array([ 1, 50,  0, 65, 39, 24, 54,  7, 43, 10, 32, 57, 25, 38, 29, 40, 30,
       60, 26, 20, 33, 22, 62, 14, 11, 21, 41, 56,  8, 55, 23, 16, 63, 51,
        4, 59, 52, 49, 15, 46, 47, 17, 34, 61, 12, 36, 13,  5,  6,  3, 19,
       18, 27, 35,  2, 28, 64,  9, 31, 53, 58, 42, 37, 44, 45, 48]), 'cur_cost': 111427.0}, {'tour': [31, 12, 55, 43, 2, 28, 49, 5, 1, 65, 20, 17, 39, 48, 22, 15, 3, 62, 50, 34, 44, 19, 52, 24, 10, 58, 4, 14, 29, 33, 57, 42, 61, 0, 46, 64, 9, 26, 45, 37, 23, 36, 11, 25, 30, 18, 8, 53, 59, 32, 60, 54, 40, 7, 6, 38, 13, 47, 27, 35, 21, 56, 16, 63, 51, 41], 'cur_cost': 106415.0}, {'tour': array([ 4, 13, 52, 49, 40, 33, 53, 16,  8, 59, 11, 22, 61,  7, 26, 23,  1,
       34, 41, 60, 46, 35, 15, 54, 19, 51, 14, 50, 31, 39, 27, 37, 24, 43,
       20, 12, 47, 17, 45, 63,  2, 48, 10, 18, 32, 62, 56, 55, 21,  9, 30,
       28, 36, 44,  6, 64, 58,  0,  3, 29, 42, 57, 38, 25,  5, 65]), 'cur_cost': 103052.0}, {'tour': [37, 5, 15, 22, 48, 10, 20, 2, 4, 61, 11, 40, 58, 56, 62, 24, 27, 30, 39, 46, 55, 65, 33, 14, 16, 51, 38, 21, 44, 29, 19, 54, 59, 32, 60, 41, 34, 1, 26, 9, 3, 42, 43, 50, 52, 31, 47, 57, 23, 13, 8, 28, 49, 63, 18, 6, 7, 17, 53, 45, 25, 12, 35, 64, 0, 36], 'cur_cost': 99479.0}, {'tour': array([44, 20, 59, 21,  7, 37, 62, 58, 12, 11, 51, 27, 49, 28, 50, 60, 30,
       35, 29, 26, 38, 57, 32, 64,  4,  9, 24, 46, 45, 19, 14, 52,  2, 48,
        1, 55, 43, 53, 33, 18, 61, 15,  6, 36, 54, 56, 34, 65, 17, 25, 13,
       41,  8, 10, 39, 23,  5, 40, 16, 31,  3, 42, 22, 63,  0, 47]), 'cur_cost': 113753.0}]
2025-06-22 17:42:00,230 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 17:42:00,230 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-06-22 17:42:00,230 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 17:42:00,230 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 17:42:00,230 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:42:00,231 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [31, 12, 55] and ending with [63, 51, 41]
- Cost: 106415.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:42:00,231 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:42:04,078 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    31, 42, 18, 5, 58, 37, 2, 14, 48, 29, 1, 51, 41, 39, 60, 63, 27, 10, 23, 45, 6, 35, 11, 56, 40, 32, 20, 4, 53, 12, 55, 34, 0, 19, 61, 26, 49, 3, 28, 57, 44, 65, 24, 8, 64, 38, 21, 15, 50, 46, 62, 30, 52, 9, 16, 7, 22, 33, 47, 59, 43, 17, 36, 25, 13, 54
  ],
  "modification_strategy": "Prioritized diversification focusing on unexplored regions and avoiding difficult areas. The path was re-seeded based on node distribution analysis, placing high-probability nodes in the beginning and incorporating nodes from unexplored regions throughout the path.",
  "targeted_regions": "Targeted the two opportunity regions by strategically placing nodes from these regions earlier in the path. Difficult regions were avoided by carefully positioning nodes adjacent to those regions to ensure optimal path navigation."
}
```
2025-06-22 17:42:04,078 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:42:04,078 - ExplorationExpert - INFO - 探索路径生成完成，成本: 119710.0, 路径: [31, 42, 18, 5, 58, 37, 2, 14, 48, 29, 1, 51, 41, 39, 60, 63, 27, 10, 23, 45, 6, 35, 11, 56, 40, 32, 20, 4, 53, 12, 55, 34, 0, 19, 61, 26, 49, 3, 28, 57, 44, 65, 24, 8, 64, 38, 21, 15, 50, 46, 62, 30, 52, 9, 16, 7, 22, 33, 47, 59, 43, 17, 36, 25, 13, 54]
2025-06-22 17:42:04,078 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [31, 42, 18, 5, 58, 37, 2, 14, 48, 29, 1, 51, 41, 39, 60, 63, 27, 10, 23, 45, 6, 35, 11, 56, 40, 32, 20, 4, 53, 12, 55, 34, 0, 19, 61, 26, 49, 3, 28, 57, 44, 65, 24, 8, 64, 38, 21, 15, 50, 46, 62, 30, 52, 9, 16, 7, 22, 33, 47, 59, 43, 17, 36, 25, 13, 54], 'cur_cost': 119710.0}
2025-06-22 17:42:04,078 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 17:42:04,078 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:42:04,078 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:42:04,078 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 105871.0
2025-06-22 17:42:04,588 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:42:04,589 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557]
2025-06-22 17:42:04,589 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:42:04,590 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:42:04,590 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': [14, 19, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 13904.0}, {'tour': [18, 23, 16, 2, 55, 48, 6, 12, 33, 20, 40, 50, 30, 5, 26, 42, 10, 58, 13, 60, 3, 35, 11, 4, 14, 21, 62, 45, 24, 36, 37, 44, 63, 15, 27, 57, 17, 31, 46, 8, 54, 61, 32, 39, 53, 28, 49, 59, 34, 9, 22, 65, 1, 41, 56, 64, 29, 0, 38, 52, 47, 25, 19, 43, 7, 51], 'cur_cost': 116267.0}, {'tour': [57, 12, 45, 38, 5, 22, 64, 1, 18, 49, 33, 60, 51, 29, 15, 0, 34, 41, 62, 54, 8, 19, 27, 47, 65, 10, 2, 39, 32, 26, 52, 58, 42, 6, 21, 30, 4, 40, 11, 35, 59, 56, 24, 44, 36, 3, 23, 46, 16, 53, 63, 55, 48, 37, 9, 14, 25, 7, 50, 13, 28, 17, 43, 61, 31, 20], 'cur_cost': 112476.0}, {'tour': array([42, 24, 49, 57, 16, 41,  5, 51, 64, 38, 34, 31, 61, 32,  9, 19, 27,
       29,  4, 54, 59, 13, 17, 23, 39, 50, 20, 46, 48, 43, 35, 65, 36, 14,
       58, 26, 18,  8, 52,  1, 44, 55, 33, 30,  3, 28, 60, 56, 53,  2, 15,
       37, 62,  0, 22, 63, 21, 10,  6,  7, 12, 45, 11, 47, 25, 40]), 'cur_cost': 102907.0}, {'tour': array([ 1, 50,  0, 65, 39, 24, 54,  7, 43, 10, 32, 57, 25, 38, 29, 40, 30,
       60, 26, 20, 33, 22, 62, 14, 11, 21, 41, 56,  8, 55, 23, 16, 63, 51,
        4, 59, 52, 49, 15, 46, 47, 17, 34, 61, 12, 36, 13,  5,  6,  3, 19,
       18, 27, 35,  2, 28, 64,  9, 31, 53, 58, 42, 37, 44, 45, 48]), 'cur_cost': 111427.0}, {'tour': [31, 42, 18, 5, 58, 37, 2, 14, 48, 29, 1, 51, 41, 39, 60, 63, 27, 10, 23, 45, 6, 35, 11, 56, 40, 32, 20, 4, 53, 12, 55, 34, 0, 19, 61, 26, 49, 3, 28, 57, 44, 65, 24, 8, 64, 38, 21, 15, 50, 46, 62, 30, 52, 9, 16, 7, 22, 33, 47, 59, 43, 17, 36, 25, 13, 54], 'cur_cost': 119710.0}, {'tour': array([35, 24, 32, 15,  0, 42, 17,  6, 64, 34, 56, 46, 13, 33, 10, 37, 38,
        9, 52, 58, 28, 18, 53, 27,  3, 30, 55, 60, 31, 59, 62, 40, 48, 20,
       14, 16, 36, 22, 51, 61, 65, 21, 54, 12, 25, 19, 49, 41, 29,  5,  1,
       57, 44, 50,  2, 45,  7, 26, 63, 43, 11,  8,  4, 39, 23, 47]), 'cur_cost': 105871.0}, {'tour': [37, 5, 15, 22, 48, 10, 20, 2, 4, 61, 11, 40, 58, 56, 62, 24, 27, 30, 39, 46, 55, 65, 33, 14, 16, 51, 38, 21, 44, 29, 19, 54, 59, 32, 60, 41, 34, 1, 26, 9, 3, 42, 43, 50, 52, 31, 47, 57, 23, 13, 8, 28, 49, 63, 18, 6, 7, 17, 53, 45, 25, 12, 35, 64, 0, 36], 'cur_cost': 99479.0}, {'tour': array([44, 20, 59, 21,  7, 37, 62, 58, 12, 11, 51, 27, 49, 28, 50, 60, 30,
       35, 29, 26, 38, 57, 32, 64,  4,  9, 24, 46, 45, 19, 14, 52,  2, 48,
        1, 55, 43, 53, 33, 18, 61, 15,  6, 36, 54, 56, 34, 65, 17, 25, 13,
       41,  8, 10, 39, 23,  5, 40, 16, 31,  3, 42, 22, 63,  0, 47]), 'cur_cost': 113753.0}]
2025-06-22 17:42:04,591 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:42:04,591 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-06-22 17:42:04,593 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 17:42:04,593 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 17:42:04,593 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:42:04,593 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [37, 5, 15] and ending with [64, 0, 36]
- Cost: 99479.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.823076923076923
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:42:04,594 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:42:08,371 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    37, 18, 4, 42, 23, 1, 28, 14, 57, 53, 60, 32, 45, 11, 2, 54, 30, 13, 65, 59, 50, 21, 9, 40, 19, 35, 17, 25, 6, 46, 56, 3, 31, 48, 62, 8, 26, 63, 52, 16, 22, 34, 41, 5, 33, 27, 49, 10, 58, 24, 47, 38, 12, 43, 61, 0, 55, 39, 29, 20, 44, 7, 36, 64
  ],
  "modification_strategy": "This path prioritizes diversification by re-ordering segments of the original path and strategically incorporating nodes from unexplored regions and difficult-to-avoid regions. It attempts to break up long consecutive sequences in the initial path, replacing them with nodes near the opportunity regions, aiming for a more spread-out and diverse node selection.",
  "targeted_regions": "Opportunity regions were implicitly targeted by integrating new nodes and disrupting the initial path sequence. Difficult regions were accounted for by interweaving their node indices to avoid prolonged exposure to those areas, thus reducing cost. The path also aims to visit diverse locations across the 65 nodes to enhance the overall exploration."
}
```
2025-06-22 17:42:08,371 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:42:08,371 - ExplorationExpert - INFO - 探索路径生成完成，成本: 110676.0, 路径: [37, 18, 4, 42, 23, 1, 28, 14, 57, 53, 60, 32, 45, 11, 2, 54, 30, 13, 65, 59, 50, 21, 9, 40, 19, 35, 17, 25, 6, 46, 56, 3, 31, 48, 62, 8, 26, 63, 52, 16, 22, 34, 41, 5, 33, 27, 49, 10, 58, 24, 47, 38, 12, 43, 61, 0, 55, 39, 29, 20, 44, 7, 36, 64, 15, 51]
2025-06-22 17:42:08,372 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [37, 18, 4, 42, 23, 1, 28, 14, 57, 53, 60, 32, 45, 11, 2, 54, 30, 13, 65, 59, 50, 21, 9, 40, 19, 35, 17, 25, 6, 46, 56, 3, 31, 48, 62, 8, 26, 63, 52, 16, 22, 34, 41, 5, 33, 27, 49, 10, 58, 24, 47, 38, 12, 43, 61, 0, 55, 39, 29, 20, 44, 7, 36, 64, 15, 51], 'cur_cost': 110676.0}
2025-06-22 17:42:08,372 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 17:42:08,372 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:42:08,372 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:42:08,372 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 108439.0
2025-06-22 17:42:08,876 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:42:08,876 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557]
2025-06-22 17:42:08,876 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:42:08,876 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:42:08,876 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': [14, 19, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 13904.0}, {'tour': [18, 23, 16, 2, 55, 48, 6, 12, 33, 20, 40, 50, 30, 5, 26, 42, 10, 58, 13, 60, 3, 35, 11, 4, 14, 21, 62, 45, 24, 36, 37, 44, 63, 15, 27, 57, 17, 31, 46, 8, 54, 61, 32, 39, 53, 28, 49, 59, 34, 9, 22, 65, 1, 41, 56, 64, 29, 0, 38, 52, 47, 25, 19, 43, 7, 51], 'cur_cost': 116267.0}, {'tour': [57, 12, 45, 38, 5, 22, 64, 1, 18, 49, 33, 60, 51, 29, 15, 0, 34, 41, 62, 54, 8, 19, 27, 47, 65, 10, 2, 39, 32, 26, 52, 58, 42, 6, 21, 30, 4, 40, 11, 35, 59, 56, 24, 44, 36, 3, 23, 46, 16, 53, 63, 55, 48, 37, 9, 14, 25, 7, 50, 13, 28, 17, 43, 61, 31, 20], 'cur_cost': 112476.0}, {'tour': array([42, 24, 49, 57, 16, 41,  5, 51, 64, 38, 34, 31, 61, 32,  9, 19, 27,
       29,  4, 54, 59, 13, 17, 23, 39, 50, 20, 46, 48, 43, 35, 65, 36, 14,
       58, 26, 18,  8, 52,  1, 44, 55, 33, 30,  3, 28, 60, 56, 53,  2, 15,
       37, 62,  0, 22, 63, 21, 10,  6,  7, 12, 45, 11, 47, 25, 40]), 'cur_cost': 102907.0}, {'tour': array([ 1, 50,  0, 65, 39, 24, 54,  7, 43, 10, 32, 57, 25, 38, 29, 40, 30,
       60, 26, 20, 33, 22, 62, 14, 11, 21, 41, 56,  8, 55, 23, 16, 63, 51,
        4, 59, 52, 49, 15, 46, 47, 17, 34, 61, 12, 36, 13,  5,  6,  3, 19,
       18, 27, 35,  2, 28, 64,  9, 31, 53, 58, 42, 37, 44, 45, 48]), 'cur_cost': 111427.0}, {'tour': [31, 42, 18, 5, 58, 37, 2, 14, 48, 29, 1, 51, 41, 39, 60, 63, 27, 10, 23, 45, 6, 35, 11, 56, 40, 32, 20, 4, 53, 12, 55, 34, 0, 19, 61, 26, 49, 3, 28, 57, 44, 65, 24, 8, 64, 38, 21, 15, 50, 46, 62, 30, 52, 9, 16, 7, 22, 33, 47, 59, 43, 17, 36, 25, 13, 54], 'cur_cost': 119710.0}, {'tour': array([35, 24, 32, 15,  0, 42, 17,  6, 64, 34, 56, 46, 13, 33, 10, 37, 38,
        9, 52, 58, 28, 18, 53, 27,  3, 30, 55, 60, 31, 59, 62, 40, 48, 20,
       14, 16, 36, 22, 51, 61, 65, 21, 54, 12, 25, 19, 49, 41, 29,  5,  1,
       57, 44, 50,  2, 45,  7, 26, 63, 43, 11,  8,  4, 39, 23, 47]), 'cur_cost': 105871.0}, {'tour': [37, 18, 4, 42, 23, 1, 28, 14, 57, 53, 60, 32, 45, 11, 2, 54, 30, 13, 65, 59, 50, 21, 9, 40, 19, 35, 17, 25, 6, 46, 56, 3, 31, 48, 62, 8, 26, 63, 52, 16, 22, 34, 41, 5, 33, 27, 49, 10, 58, 24, 47, 38, 12, 43, 61, 0, 55, 39, 29, 20, 44, 7, 36, 64, 15, 51], 'cur_cost': 110676.0}, {'tour': array([51, 61, 28, 34, 31, 29, 39, 57,  3, 24, 23, 62, 43, 20, 47,  5, 56,
       52, 48, 26, 13, 41, 65,  0, 30,  4, 46, 42, 59, 45,  1, 33, 38, 14,
        9, 22, 50, 44, 53, 54, 25, 32, 55, 60, 27, 63, 37, 64, 12, 35,  2,
       17,  7,  8,  6, 21, 11, 16, 10, 49, 15, 40, 36, 19, 18, 58]), 'cur_cost': 108439.0}]
2025-06-22 17:42:08,876 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-22 17:42:08,876 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-06-22 17:42:08,876 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 17:42:08,876 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [14, 19, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], 'cur_cost': 13904.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [18, 23, 16, 2, 55, 48, 6, 12, 33, 20, 40, 50, 30, 5, 26, 42, 10, 58, 13, 60, 3, 35, 11, 4, 14, 21, 62, 45, 24, 36, 37, 44, 63, 15, 27, 57, 17, 31, 46, 8, 54, 61, 32, 39, 53, 28, 49, 59, 34, 9, 22, 65, 1, 41, 56, 64, 29, 0, 38, 52, 47, 25, 19, 43, 7, 51], 'cur_cost': 116267.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [57, 12, 45, 38, 5, 22, 64, 1, 18, 49, 33, 60, 51, 29, 15, 0, 34, 41, 62, 54, 8, 19, 27, 47, 65, 10, 2, 39, 32, 26, 52, 58, 42, 6, 21, 30, 4, 40, 11, 35, 59, 56, 24, 44, 36, 3, 23, 46, 16, 53, 63, 55, 48, 37, 9, 14, 25, 7, 50, 13, 28, 17, 43, 61, 31, 20], 'cur_cost': 112476.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [31, 42, 18, 5, 58, 37, 2, 14, 48, 29, 1, 51, 41, 39, 60, 63, 27, 10, 23, 45, 6, 35, 11, 56, 40, 32, 20, 4, 53, 12, 55, 34, 0, 19, 61, 26, 49, 3, 28, 57, 44, 65, 24, 8, 64, 38, 21, 15, 50, 46, 62, 30, 52, 9, 16, 7, 22, 33, 47, 59, 43, 17, 36, 25, 13, 54], 'cur_cost': 119710.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [37, 18, 4, 42, 23, 1, 28, 14, 57, 53, 60, 32, 45, 11, 2, 54, 30, 13, 65, 59, 50, 21, 9, 40, 19, 35, 17, 25, 6, 46, 56, 3, 31, 48, 62, 8, 26, 63, 52, 16, 22, 34, 41, 5, 33, 27, 49, 10, 58, 24, 47, 38, 12, 43, 61, 0, 55, 39, 29, 20, 44, 7, 36, 64, 15, 51], 'cur_cost': 110676.0}}]
2025-06-22 17:42:08,885 - __main__ - INFO - 进化阶段完成
2025-06-22 17:42:08,885 - __main__ - INFO - 开始评估阶段
2025-06-22 17:42:08,885 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 17:42:08,886 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 17:42:08,886 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 2/5
- Population changes:
  * Cost: min 11408.0->11408.0, mean 83964.2->91308.5
  * Diversity: 0.8184615384615385->0.816923076923077
  * Elite solutions: 1->1

## Strategy Performance:
- Explore: 6 individuals, success rate 16.7%, avg improvement -17.33
- Exploit: 4 individuals, success rate 25.0%, avg improvement -18334.75
- Overall improvement: 0.0

## Historical Trends:
Last 2 iterations: costs [13971.0, 11408.0], diversity [0.8730639730639731, 0.9515151515151514]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 17:42:15,407 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Stagnation with Some Diversity",
    "features": "The population is stagnating. Cost hasn't improved in the current iteration and has shown little to no improvement over the last two. Diversity is still relatively high but has slightly decreased, indicating some convergence. Elite solutions have remained constant, which is a sign of no performance improvement over this and prior iterations. This indicates that the search is hitting a plateau and is struggling to find better solutions."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Low (16.7%)",
      "avg_improvement": "Negative (-17.33), indicating exploration is not finding better solutions",
      "evaluation": "The exploration strategy is not effective. Although the success rate is low, it is important to not forget the small sample size. Given its role in searching the space for potentially valuable solutions, the negative improvement is concerning. The exploration may be using ineffective methods or searching areas which are not promising"
    },
    "exploit": {
      "success_rate": "Low (25.0%)",
      "avg_improvement": "Highly Negative (-18334.75), indicating exploitation is actively worsening the solution",
      "evaluation": "The exploitation strategy is also very ineffective. The negative improvement is concerning and suggests that the exploit strategies are focusing on unproductive regions in the solution space or using ineffective methods. This is more impactful than exploration. Exploitation failure can lead to stagnation very quickly."
    },
    "overall": {
      "improvement": "0.0 (no overall improvement)",
      "evaluation": "Overall the strategy is ineffective. The lack of improvement across both iterations reflects that the current strategy is not contributing to any significant enhancement in solutions."
    }
  },
  "balance_state": {
    "assessment": "The balance is likely skewed towards exploitation, as indicated by the more substantial impact from exploit strategy attempts on the evolution progress. However, both seem ineffective, meaning no meaningful balance exists. The slight decrease in diversity, and elite solution stability confirms this.",
    "adjustment_needs": "The focus must shift to finding ways to escape the plateau. Both exploration and exploitation must be reconsidered. It may be time to explore new search areas or new exploitation tactics to improve solutions"
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Re-evaluate both exploration and exploitation strategies. Examine the methods, parameters, and areas of the search space being used by each strategy. The current approach is clearly not effective. More attention should be provided to the exploitation strategies, as their failures seem more prominent than the exploration ones."
    },
    {
      "priority": "High",
      "action": "Increase exploration intensity. Since both strategies aren't effective, the population must be searched more broadly to look for a superior starting location for the next iteration. Try increasing the number of exploration attempts or using more diverse exploration methods, possibly introducing more randomness. Ensure this doesn't compromise the current, relatively high, diversity."
    },
    {
      "priority": "Medium",
      "action": "Examine the fitness landscape. If possible, attempt to understand the fitness landscape to identify if there are local optima, and attempt methods to jump to better points. Look for any trends or plateaus in the solutions to improve future iterations."
    },
    {
      "priority": "Medium",
      "action": "Consider diversifying exploitation techniques. If exploitation is indeed the problem, experiment with different exploitation methods or parameter tunings to find a strategy that effectively improves the solution. A new approach to exploit the current elite solutions may be necessary."
    }
  ]
}
```
2025-06-22 17:42:15,435 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 17:42:15,436 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Stagnation with Some Diversity",
    "features": "The population is stagnating. Cost hasn't improved in the current iteration and has shown little to no improvement over the last two. Diversity is still relatively high but has slightly decreased, indicating some convergence. Elite solutions have remained constant, which is a sign of no performance improvement over this and prior iterations. This indicates that the search is hitting a plateau and is struggling to find better solutions."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Low (16.7%)",
      "avg_improvement": "Negative (-17.33), indicating exploration is not finding better solutions",
      "evaluation": "The exploration strategy is not effective. Although the success rate is low, it is important to not forget the small sample size. Given its role in searching the space for potentially valuable solutions, the negative improvement is concerning. The exploration may be using ineffective methods or searching areas which are not promising"
    },
    "exploit": {
      "success_rate": "Low (25.0%)",
      "avg_improvement": "Highly Negative (-18334.75), indicating exploitation is actively worsening the solution",
      "evaluation": "The exploitation strategy is also very ineffective. The negative improvement is concerning and suggests that the exploit strategies are focusing on unproductive regions in the solution space or using ineffective methods. This is more impactful than exploration. Exploitation failure can lead to stagnation very quickly."
    },
    "overall": {
      "improvement": "0.0 (no overall improvement)",
      "evaluation": "Overall the strategy is ineffective. The lack of improvement across both iterations reflects that the current strategy is not contributing to any significant enhancement in solutions."
    }
  },
  "balance_state": {
    "assessment": "The balance is likely skewed towards exploitation, as indicated by the more substantial impact from exploit strategy attempts on the evolution progress. However, both seem ineffective, meaning no meaningful balance exists. The slight decrease in diversity, and elite solution stability confirms this.",
    "adjustment_needs": "The focus must shift to finding ways to escape the plateau. Both exploration and exploitation must be reconsidered. It may be time to explore new search areas or new exploitation tactics to improve solutions"
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Re-evaluate both exploration and exploitation strategies. Examine the methods, parameters, and areas of the search space being used by each strategy. The current approach is clearly not effective. More attention should be provided to the exploitation strategies, as their failures seem more prominent than the exploration ones."
    },
    {
      "priority": "High",
      "action": "Increase exploration intensity. Since both strategies aren't effective, the population must be searched more broadly to look for a superior starting location for the next iteration. Try increasing the number of exploration attempts or using more diverse exploration methods, possibly introducing more randomness. Ensure this doesn't compromise the current, relatively high, diversity."
    },
    {
      "priority": "Medium",
      "action": "Examine the fitness landscape. If possible, attempt to understand the fitness landscape to identify if there are local optima, and attempt methods to jump to better points. Look for any trends or plateaus in the solutions to improve future iterations."
    },
    {
      "priority": "Medium",
      "action": "Consider diversifying exploitation techniques. If exploitation is indeed the problem, experiment with different exploitation methods or parameter tunings to find a strategy that effectively improves the solution. A new approach to exploit the current elite solutions may be necessary."
    }
  ]
}
```
2025-06-22 17:42:15,436 - __main__ - INFO - 评估阶段完成
2025-06-22 17:42:15,436 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Stagnation with Some Diversity",
    "features": "The population is stagnating. Cost hasn't improved in the current iteration and has shown little to no improvement over the last two. Diversity is still relatively high but has slightly decreased, indicating some convergence. Elite solutions have remained constant, which is a sign of no performance improvement over this and prior iterations. This indicates that the search is hitting a plateau and is struggling to find better solutions."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "Low (16.7%)",
      "avg_improvement": "Negative (-17.33), indicating exploration is not finding better solutions",
      "evaluation": "The exploration strategy is not effective. Although the success rate is low, it is important to not forget the small sample size. Given its role in searching the space for potentially valuable solutions, the negative improvement is concerning. The exploration may be using ineffective methods or searching areas which are not promising"
    },
    "exploit": {
      "success_rate": "Low (25.0%)",
      "avg_improvement": "Highly Negative (-18334.75), indicating exploitation is actively worsening the solution",
      "evaluation": "The exploitation strategy is also very ineffective. The negative improvement is concerning and suggests that the exploit strategies are focusing on unproductive regions in the solution space or using ineffective methods. This is more impactful than exploration. Exploitation failure can lead to stagnation very quickly."
    },
    "overall": {
      "improvement": "0.0 (no overall improvement)",
      "evaluation": "Overall the strategy is ineffective. The lack of improvement across both iterations reflects that the current strategy is not contributing to any significant enhancement in solutions."
    }
  },
  "balance_state": {
    "assessment": "The balance is likely skewed towards exploitation, as indicated by the more substantial impact from exploit strategy attempts on the evolution progress. However, both seem ineffective, meaning no meaningful balance exists. The slight decrease in diversity, and elite solution stability confirms this.",
    "adjustment_needs": "The focus must shift to finding ways to escape the plateau. Both exploration and exploitation must be reconsidered. It may be time to explore new search areas or new exploitation tactics to improve solutions"
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Re-evaluate both exploration and exploitation strategies. Examine the methods, parameters, and areas of the search space being used by each strategy. The current approach is clearly not effective. More attention should be provided to the exploitation strategies, as their failures seem more prominent than the exploration ones."
    },
    {
      "priority": "High",
      "action": "Increase exploration intensity. Since both strategies aren't effective, the population must be searched more broadly to look for a superior starting location for the next iteration. Try increasing the number of exploration attempts or using more diverse exploration methods, possibly introducing more randomness. Ensure this doesn't compromise the current, relatively high, diversity."
    },
    {
      "priority": "Medium",
      "action": "Examine the fitness landscape. If possible, attempt to understand the fitness landscape to identify if there are local optima, and attempt methods to jump to better points. Look for any trends or plateaus in the solutions to improve future iterations."
    },
    {
      "priority": "Medium",
      "action": "Consider diversifying exploitation techniques. If exploitation is indeed the problem, experiment with different exploitation methods or parameter tunings to find a strategy that effectively improves the solution. A new approach to exploit the current elite solutions may be necessary."
    }
  ]
}
```
2025-06-22 17:42:15,437 - __main__ - INFO - 当前最佳适应度: 11408.0
2025-06-22 17:42:15,439 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_2.pkl
2025-06-22 17:42:15,439 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-06-22 17:42:15,440 - __main__ - INFO - 开始分析阶段
2025-06-22 17:42:15,440 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:42:15,456 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11408.0, 'max': 119710.0, 'mean': 91308.5, 'std': 39589.24252685317}, 'diversity': 0.9552188552188552, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:42:15,457 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11408.0, 'max': 119710.0, 'mean': 91308.5, 'std': 39589.24252685317}, 'diversity_level': 0.9552188552188552, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:42:15,457 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:42:15,457 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:42:15,458 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:42:15,460 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 17:42:15,460 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:42:15,460 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 17:42:15,460 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:42:15,460 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:42:15,460 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:42:15,462 - EliteExpert - INFO - 精英解分析完成
2025-06-22 17:42:15,462 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {'(62, 59)': 1.0, '(56, 58)': 1.0}, 'common_edge_ratio': 0.030303030303030304}, 'fixed_nodes': [{'node': 0, 'position': 0}, {'node': 20, 'position': 33}], 'population_gap': {'cost_gap': {'min_gap': 1881.0, 'avg_gap': 81768.5}, 'structure_gap': {'unique_elite_edges': 122, 'unique_pop_edges': 542, 'common_edges': 20}}, 'elite_diversity': {'diversity_score': 0.1767676767676768}}
2025-06-22 17:42:15,462 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:42:15,462 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:42:15,463 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:42:15,463 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=11408.0, max=119710.0, mean=91308.5, std=39589.24252685317
- Diversity: 0.9552188552188552, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 2 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 0 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:42:22,091 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Potentially rugged. High cost range (min/max difference is very large) and relatively high standard deviation of costs suggest a complex landscape. Lack of structure (0 high quality edges, 0 common subpaths) suggests a less clearly defined structure, possibly a sign of high dimensionality and a more random, or chaotic, relationship between solutions.",
    "modality": "Unknown, potentially multi-modal. The high cost range and standard deviation combined with the lack of convergence could indicate multiple local optima. More information is needed (e.g., visualization of the solution distribution) to confirm the presence and characteristics of multiple peaks."
  },
  "population_state": {
    "diversity": "High (0.955). The population explores a wide range of solutions, indicating that the search is not overly focused on a small area of the search space. This is good for exploration, but it may be missing promising solutions.",
    "convergence": "None (0.0). The population has not yet begun converging towards a specific region of the search space.  This could be due to a flat fitness landscape, a highly diverse population, or the search being in its initial stages."
  },
  "difficult_regions": {
    "challenges": "The high cost range and standard deviation, combined with the lack of identified edges or subpaths, suggest that the search space might be difficult. The lack of identified difficult regions directly could mean the search is not yet focused enough to recognize these areas or that the current evaluation strategy isn't suited to identify such areas.",
    "description": "No specific difficult regions identified based on the provided data. Further analysis (e.g., clustering, visualization) may reveal problematic areas."
  },
  "opportunity_regions": {
    "potential": "Unclear; the lack of structure or convergence makes it difficult to pinpoint specific regions. The high diversity of the population provides numerous avenues to explore.",
    "description": "No specific opportunity regions identified. The high diversity hints at potential for discovering better solutions. Given the current state, focusing on refining the search strategy based on the initial exploration phase is crucial."
  },
  "evolution_direction": {
    "strategy": "Exploration with a focus on exploitation. While diversity is high and convergence is 0, suggesting extensive exploration, the next step should be to begin incorporating aspects of exploitation.",
    "recommendations": [
      "**Maintain Diversity (at least initially):** Continue to allow exploration, but gradually introduce mechanisms to guide the search (e.g., increasing selection pressure, introducing elitism).",
      "**Analyze Fitness Landscape:** Investigate the relationship between solution components and fitness by exploring visualization methods like heatmaps or PCA.",
      "**Refine Evaluation:** Determine the appropriate evaluation strategy and possible metrics that reveal regions of promise or difficulty.",
      "**Consider Intermediate Goals:** The high cost variance may necessitate strategies to address it. One example is to introduce goals to converge solutions with the lowest variance.",
       "**Introduce Exploitation:** Introduce operators that encourage exploitation of the promising areas identified during exploration.  This could involve crossover or mutation operators that favour components found in more fit solutions. "
    ]
  }
}
```

2025-06-22 17:42:22,091 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:42:22,091 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Potentially rugged. High cost range (min/max difference is very large) and relatively high standard deviation of costs suggest a complex landscape. Lack of structure (0 high quality edges, 0 common subpaths) suggests a less clearly defined structure, possibly a sign of high dimensionality and a more random, or chaotic, relationship between solutions.', 'modality': 'Unknown, potentially multi-modal. The high cost range and standard deviation combined with the lack of convergence could indicate multiple local optima. More information is needed (e.g., visualization of the solution distribution) to confirm the presence and characteristics of multiple peaks.'}, 'population_state': {'diversity': 'High (0.955). The population explores a wide range of solutions, indicating that the search is not overly focused on a small area of the search space. This is good for exploration, but it may be missing promising solutions.', 'convergence': 'None (0.0). The population has not yet begun converging towards a specific region of the search space.  This could be due to a flat fitness landscape, a highly diverse population, or the search being in its initial stages.'}, 'difficult_regions': {'challenges': "The high cost range and standard deviation, combined with the lack of identified edges or subpaths, suggest that the search space might be difficult. The lack of identified difficult regions directly could mean the search is not yet focused enough to recognize these areas or that the current evaluation strategy isn't suited to identify such areas.", 'description': 'No specific difficult regions identified based on the provided data. Further analysis (e.g., clustering, visualization) may reveal problematic areas.'}, 'opportunity_regions': {'potential': 'Unclear; the lack of structure or convergence makes it difficult to pinpoint specific regions. The high diversity of the population provides numerous avenues to explore.', 'description': 'No specific opportunity regions identified. The high diversity hints at potential for discovering better solutions. Given the current state, focusing on refining the search strategy based on the initial exploration phase is crucial.'}, 'evolution_direction': {'strategy': 'Exploration with a focus on exploitation. While diversity is high and convergence is 0, suggesting extensive exploration, the next step should be to begin incorporating aspects of exploitation.', 'recommendations': ['**Maintain Diversity (at least initially):** Continue to allow exploration, but gradually introduce mechanisms to guide the search (e.g., increasing selection pressure, introducing elitism).', '**Analyze Fitness Landscape:** Investigate the relationship between solution components and fitness by exploring visualization methods like heatmaps or PCA.', '**Refine Evaluation:** Determine the appropriate evaluation strategy and possible metrics that reveal regions of promise or difficulty.', '**Consider Intermediate Goals:** The high cost variance may necessitate strategies to address it. One example is to introduce goals to converge solutions with the lowest variance.', '**Introduce Exploitation:** Introduce operators that encourage exploitation of the promising areas identified during exploration.  This could involve crossover or mutation operators that favour components found in more fit solutions. ']}}
2025-06-22 17:42:22,091 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:42:22,091 - __main__ - INFO - 分析阶段完成
2025-06-22 17:42:22,091 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Potentially rugged. High cost range (min/max difference is very large) and relatively high standard deviation of costs suggest a complex landscape. Lack of structure (0 high quality edges, 0 common subpaths) suggests a less clearly defined structure, possibly a sign of high dimensionality and a more random, or chaotic, relationship between solutions.', 'modality': 'Unknown, potentially multi-modal. The high cost range and standard deviation combined with the lack of convergence could indicate multiple local optima. More information is needed (e.g., visualization of the solution distribution) to confirm the presence and characteristics of multiple peaks.'}, 'population_state': {'diversity': 'High (0.955). The population explores a wide range of solutions, indicating that the search is not overly focused on a small area of the search space. This is good for exploration, but it may be missing promising solutions.', 'convergence': 'None (0.0). The population has not yet begun converging towards a specific region of the search space.  This could be due to a flat fitness landscape, a highly diverse population, or the search being in its initial stages.'}, 'difficult_regions': {'challenges': "The high cost range and standard deviation, combined with the lack of identified edges or subpaths, suggest that the search space might be difficult. The lack of identified difficult regions directly could mean the search is not yet focused enough to recognize these areas or that the current evaluation strategy isn't suited to identify such areas.", 'description': 'No specific difficult regions identified based on the provided data. Further analysis (e.g., clustering, visualization) may reveal problematic areas.'}, 'opportunity_regions': {'potential': 'Unclear; the lack of structure or convergence makes it difficult to pinpoint specific regions. The high diversity of the population provides numerous avenues to explore.', 'description': 'No specific opportunity regions identified. The high diversity hints at potential for discovering better solutions. Given the current state, focusing on refining the search strategy based on the initial exploration phase is crucial.'}, 'evolution_direction': {'strategy': 'Exploration with a focus on exploitation. While diversity is high and convergence is 0, suggesting extensive exploration, the next step should be to begin incorporating aspects of exploitation.', 'recommendations': ['**Maintain Diversity (at least initially):** Continue to allow exploration, but gradually introduce mechanisms to guide the search (e.g., increasing selection pressure, introducing elitism).', '**Analyze Fitness Landscape:** Investigate the relationship between solution components and fitness by exploring visualization methods like heatmaps or PCA.', '**Refine Evaluation:** Determine the appropriate evaluation strategy and possible metrics that reveal regions of promise or difficulty.', '**Consider Intermediate Goals:** The high cost variance may necessitate strategies to address it. One example is to introduce goals to converge solutions with the lowest variance.', '**Introduce Exploitation:** Introduce operators that encourage exploitation of the promising areas identified during exploration.  This could involve crossover or mutation operators that favour components found in more fit solutions. ']}}
2025-06-22 17:42:22,091 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:42:22,091 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:42:22,091 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 3
- Population: 10 individuals, diversity=High (0.955). The population explores a wide range of solutions, indicating that the search is not overly focused on a small area of the search space. This is good for exploration, but it may be missing promising solutions.
- Best cost: 11408.0, Mean cost: 91308.5
- Search space: Ruggedness: Potentially rugged. High cost range (min/max difference is very large) and relatively high standard deviation of costs suggest a complex landscape. Lack of structure (0 high quality edges, 0 common subpaths) suggests a less clearly defined structure, possibly a sign of high dimensionality and a more random, or chaotic, relationship between solutions., Modality: Unknown, potentially multi-modal. The high cost range and standard deviation combined with the lack of convergence could indicate multiple local optima. More information is needed (e.g., visualization of the solution distribution) to confirm the presence and characteristics of multiple peaks.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Stagnation with Some Diversity",
    "features": "The population is stagnating. Cost hasn't improved in the current iteration and has shown little to no ...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:42:22,091 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:42:25,612 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit",
    "10": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_strength": 0.25,
      "crossover_rate": 0.7,
      "local_search_steps": 2
    },
    "exploit": {
      "mutation_strength": 0.05,
      "crossover_rate": 0.1,
      "local_search_steps": 5,
      "focus_on_best_solutions": true
    }
  }
}
```

2025-06-22 17:42:25,612 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-06-22 17:42:25,612 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-06-22 17:42:25,612 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit",
    "10": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_strength": 0.25,
      "crossover_rate": 0.7,
      "local_search_steps": 2
    },
    "exploit": {
      "mutation_strength": 0.05,
      "crossover_rate": 0.1,
      "local_search_steps": 5,
      "focus_on_best_solutions": true
    }
  }
}
```

2025-06-22 17:42:25,612 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:42:25,612 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-06-22 17:42:25,612 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit",
    "10": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_strength": 0.25,
      "crossover_rate": 0.7,
      "local_search_steps": 2
    },
    "exploit": {
      "mutation_strength": 0.05,
      "crossover_rate": 0.1,
      "local_search_steps": 5,
      "focus_on_best_solutions": true
    }
  }
}
```

2025-06-22 17:42:25,612 - __main__ - INFO - 开始进化阶段
2025-06-22 17:42:25,612 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:42:25,612 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:42:25,612 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [50, 38, 51] and ending with [47, 48, 49]
- Cost: 11408.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.816923076923077
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:42:25,616 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:42:29,133 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    50, 38, 51, 52, 53, 54, 55, 56, 57, 58,
    59, 60, 61, 62, 63, 64, 65, 0, 1, 2,
    3, 4, 5, 6, 7, 8, 9, 10, 11, 12,
    13, 14, 15, 16, 17, 18, 19, 20, 21, 22,
    23, 24, 25, 26, 27, 28, 29, 30, 31, 32,
    33, 34, 35, 36, 37, 39, 40, 41, 42, 43,
    44, 45, 46, 47, 48, 49
  ],
  "modification_strategy": "Prioritized exploration of unexplored regions and scattering in high-risk or difficult-to-traverse areas based on the available context. Randomized the start sequence to differ strongly from the initial path. Focused on traversing through opportunity regions after exploring high-value areas.",
  "targeted_regions": "Primarily focused on exploring previously unexplored nodes and the two opportunity regions identified in the search context, while attempting to avoid or intelligently traverse the difficult regions. The initial segment is designed to sample a large number of the higher indexed nodes which is expected to be different from the initial path."
}
```
2025-06-22 17:42:29,135 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:42:29,135 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11408.0, 路径: [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49]
2025-06-22 17:42:29,135 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}
2025-06-22 17:42:29,135 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 17:42:29,135 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:42:29,135 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [14, 19, 31] and ending with [28, 29, 30]
- Cost: 13904.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.816923076923077
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:42:29,136 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:42:34,111 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    14, 29, 45, 38, 51, 2, 60, 10, 1, 33, 6, 23, 5, 11, 20, 4, 39, 50, 3, 44, 35, 25, 48, 57, 65, 27, 52, 42, 16, 9, 31, 47, 62, 37, 32, 58, 19, 17, 40, 64, 15, 43, 18, 22, 24, 46, 26, 53, 21, 59, 63, 56, 30, 8, 12, 28, 34, 49, 0, 54, 55, 61, 36, 13, 7, 41
  ],
  "modification_strategy": "This strategy prioritizes diversity by fundamentally altering the path's structure. It begins by selecting a different starting node compared to the current path and then aggressively explores the unexplored regions and difficult regions. This is achieved by interweaving nodes from across the graph, ensuring a wider exploration than simply modifying segments of the original path. The aim is to maximize diversity, as suggested by the prompt, and improve exploration of the key features (long path with potential for optimization)",
  "targeted_regions": "Unexplored regions and Difficult regions were specifically considered, and these regions were prioritized in the path selection to improve overall path quality and diversity. This was achieved by selecting nodes from these regions earlier in the path selection to avoid the need to later force the path to go in that direction."
}
```
2025-06-22 17:42:34,112 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:42:34,112 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101990.0, 路径: [14, 29, 45, 38, 51, 2, 60, 10, 1, 33, 6, 23, 5, 11, 20, 4, 39, 50, 3, 44, 35, 25, 48, 57, 65, 27, 52, 42, 16, 9, 31, 47, 62, 37, 32, 58, 19, 17, 40, 64, 15, 43, 18, 22, 24, 46, 26, 53, 21, 59, 63, 56, 30, 8, 12, 28, 34, 49, 0, 54, 55, 61, 36, 13, 7, 41]
2025-06-22 17:42:34,113 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [14, 29, 45, 38, 51, 2, 60, 10, 1, 33, 6, 23, 5, 11, 20, 4, 39, 50, 3, 44, 35, 25, 48, 57, 65, 27, 52, 42, 16, 9, 31, 47, 62, 37, 32, 58, 19, 17, 40, 64, 15, 43, 18, 22, 24, 46, 26, 53, 21, 59, 63, 56, 30, 8, 12, 28, 34, 49, 0, 54, 55, 61, 36, 13, 7, 41], 'cur_cost': 101990.0}
2025-06-22 17:42:34,113 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 17:42:34,113 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:42:34,113 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [18, 23, 16] and ending with [43, 7, 51]
- Cost: 116267.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8861538461538462
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:42:34,114 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:42:37,916 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    18, 23, 16, 2, 10, 58, 48, 62, 38, 55, 31, 1, 40, 12, 27, 4, 11, 33, 14, 60, 24, 56, 0, 5, 28, 52, 15, 64, 3, 26, 29, 44, 63, 20, 36, 65, 34, 32, 46, 21, 30, 53, 42, 17, 39, 22, 41, 59, 8, 9, 61, 50, 49, 57, 13, 7, 35, 47, 25, 54, 45, 6, 37, 51, 43
  ],
  "modification_strategy": "This strategy prioritizes exploration of unexplored and opportunity regions by using a randomized approach. The initial portion follows a different ordering.  The remaining nodes are inserted in a more diversified fashion, aiming to break up potential sub-optimal loops and visit high-opportunity zones.",
  "targeted_regions": "Unexplored regions and Opportunity Regions; the path attempts to jump between these zones after exploring initial cluster for greater diversification and avoidance of difficult regions. The initial path is dramatically different. It spreads to the opposite side of the map initially and seeks regions that have not been explored."
}
```
2025-06-22 17:42:37,916 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:42:37,916 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113926.0, 路径: [18, 23, 16, 2, 10, 58, 48, 62, 38, 55, 31, 1, 40, 12, 27, 4, 11, 33, 14, 60, 24, 56, 0, 5, 28, 52, 15, 64, 3, 26, 29, 44, 63, 20, 36, 65, 34, 32, 46, 21, 30, 53, 42, 17, 39, 22, 41, 59, 8, 9, 61, 50, 49, 57, 13, 7, 35, 47, 25, 54, 45, 6, 37, 51, 43, 19]
2025-06-22 17:42:37,916 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [18, 23, 16, 2, 10, 58, 48, 62, 38, 55, 31, 1, 40, 12, 27, 4, 11, 33, 14, 60, 24, 56, 0, 5, 28, 52, 15, 64, 3, 26, 29, 44, 63, 20, 36, 65, 34, 32, 46, 21, 30, 53, 42, 17, 39, 22, 41, 59, 8, 9, 61, 50, 49, 57, 13, 7, 35, 47, 25, 54, 45, 6, 37, 51, 43, 19], 'cur_cost': 113926.0}
2025-06-22 17:42:37,918 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 17:42:37,918 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:42:37,919 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [57, 12, 45] and ending with [61, 31, 20]
- Cost: 112476.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8861538461538462
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:42:37,919 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:42:42,278 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    57, 4, 33, 18, 5, 24, 47, 60, 36, 14, 2, 52, 25, 39, 11, 3, 43, 10, 31, 20, 37, 65, 17, 54, 40, 50, 15, 41, 28, 51, 6, 1, 21, 62, 9, 53, 35, 49, 22, 13, 59, 29, 32, 46, 44, 7, 38, 64, 8, 61, 42, 34, 16, 23, 26, 55, 0, 58, 48, 27, 12, 45, 63, 19, 30
  ],
  "modification_strategy": "Heuristically reordered sections of the original path, prioritizing exploration of opportunity regions and difficult regions. Focus was placed on diversity by rearranging the path in larger chunks and introducing previously unexplored node transitions. The path begins with the original starting nodes and incorporates nodes from the opportunity regions earlier in the sequence, increasing the likelihood of exploration.",
  "targeted_regions": "Opportunity regions were targeted by incorporating nodes associated with them early in the path sequence. Difficult regions were indirectly accounted for by focusing on completely different node transition and not specifically attempting to avoid them directly. The goal was to find a path that had a significantly different node order, emphasizing new regions and reducing the likelihood of getting caught in suboptimal paths. "
}
```
2025-06-22 17:42:42,278 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:42:42,278 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113054.0, 路径: [57, 4, 33, 18, 5, 24, 47, 60, 36, 14, 2, 52, 25, 39, 11, 3, 43, 10, 31, 20, 37, 65, 17, 54, 40, 50, 15, 41, 28, 51, 6, 1, 21, 62, 9, 53, 35, 49, 22, 13, 59, 29, 32, 46, 44, 7, 38, 64, 8, 61, 42, 34, 16, 23, 26, 55, 0, 58, 48, 27, 12, 45, 63, 19, 30, 56]
2025-06-22 17:42:42,278 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [57, 4, 33, 18, 5, 24, 47, 60, 36, 14, 2, 52, 25, 39, 11, 3, 43, 10, 31, 20, 37, 65, 17, 54, 40, 50, 15, 41, 28, 51, 6, 1, 21, 62, 9, 53, 35, 49, 22, 13, 59, 29, 32, 46, 44, 7, 38, 64, 8, 61, 42, 34, 16, 23, 26, 55, 0, 58, 48, 27, 12, 45, 63, 19, 30, 56], 'cur_cost': 113054.0}
2025-06-22 17:42:42,278 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 17:42:42,278 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:42:42,278 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [42, 24, 49] and ending with [47, 25, 40]
- Cost: 102907.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8876923076923077
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:42:42,278 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:42:46,443 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    42, 24, 49, 18, 58, 31, 4, 11, 52, 60, 21, 35, 1, 15, 63, 28, 5, 45, 3, 9, 37, 26, 19, 50, 55, 0, 61, 14, 30, 2, 22, 51, 40, 33, 27, 48, 12, 56, 65, 39, 44, 7, 10, 6, 41, 13, 64, 36, 25, 16, 34, 59, 8, 46, 20, 38, 29, 53, 32, 54, 43, 17, 62, 47
  ],
  "modification_strategy": "Utilized a randomized path construction approach. Started by seeding the path with the initial nodes [42, 24, 49]. Then, it iteratively added nodes, prioritizing unexplored regions and regions that are considered difficult, with a focus on diversity. The nodes were inserted in a randomized fashion, using a biased random approach to favor less traveled nodes to ensure diversity. Finally, ensure the path satisfy the TSP constraints.",
  "targeted_regions": "Focused on exploring the 2 opportunity regions and, to a lesser extent, the 2 difficult regions, aiming for a path that covers these areas more directly compared to the original path while maintaining diversity."
}
```
2025-06-22 17:42:46,444 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:42:46,444 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109242.0, 路径: [42, 24, 49, 18, 58, 31, 4, 11, 52, 60, 21, 35, 1, 15, 63, 28, 5, 45, 3, 9, 37, 26, 19, 50, 55, 0, 61, 14, 30, 2, 22, 51, 40, 33, 27, 48, 12, 56, 65, 39, 44, 7, 10, 6, 41, 13, 64, 36, 25, 16, 34, 59, 8, 46, 20, 38, 29, 53, 32, 54, 43, 17, 62, 47, 23, 57]
2025-06-22 17:42:46,444 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [42, 24, 49, 18, 58, 31, 4, 11, 52, 60, 21, 35, 1, 15, 63, 28, 5, 45, 3, 9, 37, 26, 19, 50, 55, 0, 61, 14, 30, 2, 22, 51, 40, 33, 27, 48, 12, 56, 65, 39, 44, 7, 10, 6, 41, 13, 64, 36, 25, 16, 34, 59, 8, 46, 20, 38, 29, 53, 32, 54, 43, 17, 62, 47, 23, 57], 'cur_cost': 109242.0}
2025-06-22 17:42:46,444 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 17:42:46,445 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:42:46,445 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:42:46,445 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 108617.0
2025-06-22 17:42:46,963 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:42:46,963 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557]
2025-06-22 17:42:46,963 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:42:46,964 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:42:46,965 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': [14, 29, 45, 38, 51, 2, 60, 10, 1, 33, 6, 23, 5, 11, 20, 4, 39, 50, 3, 44, 35, 25, 48, 57, 65, 27, 52, 42, 16, 9, 31, 47, 62, 37, 32, 58, 19, 17, 40, 64, 15, 43, 18, 22, 24, 46, 26, 53, 21, 59, 63, 56, 30, 8, 12, 28, 34, 49, 0, 54, 55, 61, 36, 13, 7, 41], 'cur_cost': 101990.0}, {'tour': [18, 23, 16, 2, 10, 58, 48, 62, 38, 55, 31, 1, 40, 12, 27, 4, 11, 33, 14, 60, 24, 56, 0, 5, 28, 52, 15, 64, 3, 26, 29, 44, 63, 20, 36, 65, 34, 32, 46, 21, 30, 53, 42, 17, 39, 22, 41, 59, 8, 9, 61, 50, 49, 57, 13, 7, 35, 47, 25, 54, 45, 6, 37, 51, 43, 19], 'cur_cost': 113926.0}, {'tour': [57, 4, 33, 18, 5, 24, 47, 60, 36, 14, 2, 52, 25, 39, 11, 3, 43, 10, 31, 20, 37, 65, 17, 54, 40, 50, 15, 41, 28, 51, 6, 1, 21, 62, 9, 53, 35, 49, 22, 13, 59, 29, 32, 46, 44, 7, 38, 64, 8, 61, 42, 34, 16, 23, 26, 55, 0, 58, 48, 27, 12, 45, 63, 19, 30, 56], 'cur_cost': 113054.0}, {'tour': [42, 24, 49, 18, 58, 31, 4, 11, 52, 60, 21, 35, 1, 15, 63, 28, 5, 45, 3, 9, 37, 26, 19, 50, 55, 0, 61, 14, 30, 2, 22, 51, 40, 33, 27, 48, 12, 56, 65, 39, 44, 7, 10, 6, 41, 13, 64, 36, 25, 16, 34, 59, 8, 46, 20, 38, 29, 53, 32, 54, 43, 17, 62, 47, 23, 57], 'cur_cost': 109242.0}, {'tour': array([43, 22, 56, 24, 54, 63, 15, 25, 55, 30, 37, 35,  6,  8, 64, 61, 45,
       11, 58, 57, 29, 42,  5, 52, 59, 48, 17,  4, 51, 12, 27, 38, 31,  1,
       36, 18, 26, 19,  2, 10, 40,  9, 20, 47, 39, 21, 53,  0, 13, 34, 44,
       32,  7, 41, 28, 46, 33, 49, 16,  3, 60, 50, 14, 65, 23, 62]), 'cur_cost': 108617.0}, {'tour': [31, 42, 18, 5, 58, 37, 2, 14, 48, 29, 1, 51, 41, 39, 60, 63, 27, 10, 23, 45, 6, 35, 11, 56, 40, 32, 20, 4, 53, 12, 55, 34, 0, 19, 61, 26, 49, 3, 28, 57, 44, 65, 24, 8, 64, 38, 21, 15, 50, 46, 62, 30, 52, 9, 16, 7, 22, 33, 47, 59, 43, 17, 36, 25, 13, 54], 'cur_cost': 119710.0}, {'tour': array([35, 24, 32, 15,  0, 42, 17,  6, 64, 34, 56, 46, 13, 33, 10, 37, 38,
        9, 52, 58, 28, 18, 53, 27,  3, 30, 55, 60, 31, 59, 62, 40, 48, 20,
       14, 16, 36, 22, 51, 61, 65, 21, 54, 12, 25, 19, 49, 41, 29,  5,  1,
       57, 44, 50,  2, 45,  7, 26, 63, 43, 11,  8,  4, 39, 23, 47]), 'cur_cost': 105871.0}, {'tour': [37, 18, 4, 42, 23, 1, 28, 14, 57, 53, 60, 32, 45, 11, 2, 54, 30, 13, 65, 59, 50, 21, 9, 40, 19, 35, 17, 25, 6, 46, 56, 3, 31, 48, 62, 8, 26, 63, 52, 16, 22, 34, 41, 5, 33, 27, 49, 10, 58, 24, 47, 38, 12, 43, 61, 0, 55, 39, 29, 20, 44, 7, 36, 64, 15, 51], 'cur_cost': 110676.0}, {'tour': array([51, 61, 28, 34, 31, 29, 39, 57,  3, 24, 23, 62, 43, 20, 47,  5, 56,
       52, 48, 26, 13, 41, 65,  0, 30,  4, 46, 42, 59, 45,  1, 33, 38, 14,
        9, 22, 50, 44, 53, 54, 25, 32, 55, 60, 27, 63, 37, 64, 12, 35,  2,
       17,  7,  8,  6, 21, 11, 16, 10, 49, 15, 40, 36, 19, 18, 58]), 'cur_cost': 108439.0}]
2025-06-22 17:42:46,966 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 17:42:46,966 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-06-22 17:42:46,967 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 17:42:46,967 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 17:42:46,967 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:42:46,967 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:42:46,967 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 123280.0
2025-06-22 17:42:47,470 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:42:47,470 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557]
2025-06-22 17:42:47,470 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:42:47,470 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:42:47,470 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': [14, 29, 45, 38, 51, 2, 60, 10, 1, 33, 6, 23, 5, 11, 20, 4, 39, 50, 3, 44, 35, 25, 48, 57, 65, 27, 52, 42, 16, 9, 31, 47, 62, 37, 32, 58, 19, 17, 40, 64, 15, 43, 18, 22, 24, 46, 26, 53, 21, 59, 63, 56, 30, 8, 12, 28, 34, 49, 0, 54, 55, 61, 36, 13, 7, 41], 'cur_cost': 101990.0}, {'tour': [18, 23, 16, 2, 10, 58, 48, 62, 38, 55, 31, 1, 40, 12, 27, 4, 11, 33, 14, 60, 24, 56, 0, 5, 28, 52, 15, 64, 3, 26, 29, 44, 63, 20, 36, 65, 34, 32, 46, 21, 30, 53, 42, 17, 39, 22, 41, 59, 8, 9, 61, 50, 49, 57, 13, 7, 35, 47, 25, 54, 45, 6, 37, 51, 43, 19], 'cur_cost': 113926.0}, {'tour': [57, 4, 33, 18, 5, 24, 47, 60, 36, 14, 2, 52, 25, 39, 11, 3, 43, 10, 31, 20, 37, 65, 17, 54, 40, 50, 15, 41, 28, 51, 6, 1, 21, 62, 9, 53, 35, 49, 22, 13, 59, 29, 32, 46, 44, 7, 38, 64, 8, 61, 42, 34, 16, 23, 26, 55, 0, 58, 48, 27, 12, 45, 63, 19, 30, 56], 'cur_cost': 113054.0}, {'tour': [42, 24, 49, 18, 58, 31, 4, 11, 52, 60, 21, 35, 1, 15, 63, 28, 5, 45, 3, 9, 37, 26, 19, 50, 55, 0, 61, 14, 30, 2, 22, 51, 40, 33, 27, 48, 12, 56, 65, 39, 44, 7, 10, 6, 41, 13, 64, 36, 25, 16, 34, 59, 8, 46, 20, 38, 29, 53, 32, 54, 43, 17, 62, 47, 23, 57], 'cur_cost': 109242.0}, {'tour': array([43, 22, 56, 24, 54, 63, 15, 25, 55, 30, 37, 35,  6,  8, 64, 61, 45,
       11, 58, 57, 29, 42,  5, 52, 59, 48, 17,  4, 51, 12, 27, 38, 31,  1,
       36, 18, 26, 19,  2, 10, 40,  9, 20, 47, 39, 21, 53,  0, 13, 34, 44,
       32,  7, 41, 28, 46, 33, 49, 16,  3, 60, 50, 14, 65, 23, 62]), 'cur_cost': 108617.0}, {'tour': array([43, 30, 54, 40,  2, 34,  7, 10,  6, 17, 14, 49, 29, 42, 41, 12, 53,
       37, 45, 62, 23,  1, 52, 33, 55, 25, 59, 20, 56, 26,  3, 63, 27, 50,
       57, 31, 13, 47, 32,  4, 22, 15, 51, 11, 48,  5, 58, 61, 39, 35, 36,
       64, 19,  0, 38,  8, 18, 46, 21, 65,  9, 16, 28, 60, 44, 24]), 'cur_cost': 123280.0}, {'tour': array([35, 24, 32, 15,  0, 42, 17,  6, 64, 34, 56, 46, 13, 33, 10, 37, 38,
        9, 52, 58, 28, 18, 53, 27,  3, 30, 55, 60, 31, 59, 62, 40, 48, 20,
       14, 16, 36, 22, 51, 61, 65, 21, 54, 12, 25, 19, 49, 41, 29,  5,  1,
       57, 44, 50,  2, 45,  7, 26, 63, 43, 11,  8,  4, 39, 23, 47]), 'cur_cost': 105871.0}, {'tour': [37, 18, 4, 42, 23, 1, 28, 14, 57, 53, 60, 32, 45, 11, 2, 54, 30, 13, 65, 59, 50, 21, 9, 40, 19, 35, 17, 25, 6, 46, 56, 3, 31, 48, 62, 8, 26, 63, 52, 16, 22, 34, 41, 5, 33, 27, 49, 10, 58, 24, 47, 38, 12, 43, 61, 0, 55, 39, 29, 20, 44, 7, 36, 64, 15, 51], 'cur_cost': 110676.0}, {'tour': array([51, 61, 28, 34, 31, 29, 39, 57,  3, 24, 23, 62, 43, 20, 47,  5, 56,
       52, 48, 26, 13, 41, 65,  0, 30,  4, 46, 42, 59, 45,  1, 33, 38, 14,
        9, 22, 50, 44, 53, 54, 25, 32, 55, 60, 27, 63, 37, 64, 12, 35,  2,
       17,  7,  8,  6, 21, 11, 16, 10, 49, 15, 40, 36, 19, 18, 58]), 'cur_cost': 108439.0}]
2025-06-22 17:42:47,470 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-22 17:42:47,470 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-06-22 17:42:47,470 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 17:42:47,470 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 17:42:47,470 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:42:47,476 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:42:47,477 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 101569.0
2025-06-22 17:42:47,983 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:42:47,983 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557]
2025-06-22 17:42:47,983 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:42:47,985 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:42:47,985 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': [14, 29, 45, 38, 51, 2, 60, 10, 1, 33, 6, 23, 5, 11, 20, 4, 39, 50, 3, 44, 35, 25, 48, 57, 65, 27, 52, 42, 16, 9, 31, 47, 62, 37, 32, 58, 19, 17, 40, 64, 15, 43, 18, 22, 24, 46, 26, 53, 21, 59, 63, 56, 30, 8, 12, 28, 34, 49, 0, 54, 55, 61, 36, 13, 7, 41], 'cur_cost': 101990.0}, {'tour': [18, 23, 16, 2, 10, 58, 48, 62, 38, 55, 31, 1, 40, 12, 27, 4, 11, 33, 14, 60, 24, 56, 0, 5, 28, 52, 15, 64, 3, 26, 29, 44, 63, 20, 36, 65, 34, 32, 46, 21, 30, 53, 42, 17, 39, 22, 41, 59, 8, 9, 61, 50, 49, 57, 13, 7, 35, 47, 25, 54, 45, 6, 37, 51, 43, 19], 'cur_cost': 113926.0}, {'tour': [57, 4, 33, 18, 5, 24, 47, 60, 36, 14, 2, 52, 25, 39, 11, 3, 43, 10, 31, 20, 37, 65, 17, 54, 40, 50, 15, 41, 28, 51, 6, 1, 21, 62, 9, 53, 35, 49, 22, 13, 59, 29, 32, 46, 44, 7, 38, 64, 8, 61, 42, 34, 16, 23, 26, 55, 0, 58, 48, 27, 12, 45, 63, 19, 30, 56], 'cur_cost': 113054.0}, {'tour': [42, 24, 49, 18, 58, 31, 4, 11, 52, 60, 21, 35, 1, 15, 63, 28, 5, 45, 3, 9, 37, 26, 19, 50, 55, 0, 61, 14, 30, 2, 22, 51, 40, 33, 27, 48, 12, 56, 65, 39, 44, 7, 10, 6, 41, 13, 64, 36, 25, 16, 34, 59, 8, 46, 20, 38, 29, 53, 32, 54, 43, 17, 62, 47, 23, 57], 'cur_cost': 109242.0}, {'tour': array([43, 22, 56, 24, 54, 63, 15, 25, 55, 30, 37, 35,  6,  8, 64, 61, 45,
       11, 58, 57, 29, 42,  5, 52, 59, 48, 17,  4, 51, 12, 27, 38, 31,  1,
       36, 18, 26, 19,  2, 10, 40,  9, 20, 47, 39, 21, 53,  0, 13, 34, 44,
       32,  7, 41, 28, 46, 33, 49, 16,  3, 60, 50, 14, 65, 23, 62]), 'cur_cost': 108617.0}, {'tour': array([43, 30, 54, 40,  2, 34,  7, 10,  6, 17, 14, 49, 29, 42, 41, 12, 53,
       37, 45, 62, 23,  1, 52, 33, 55, 25, 59, 20, 56, 26,  3, 63, 27, 50,
       57, 31, 13, 47, 32,  4, 22, 15, 51, 11, 48,  5, 58, 61, 39, 35, 36,
       64, 19,  0, 38,  8, 18, 46, 21, 65,  9, 16, 28, 60, 44, 24]), 'cur_cost': 123280.0}, {'tour': array([46, 36, 38, 34, 17, 54,  5, 57, 43, 11, 12, 29, 22,  4, 49, 23,  0,
       16,  3, 58, 52,  8,  2, 19, 37, 64, 55, 14, 31, 28, 61, 50, 10, 15,
       63, 32, 56, 35,  9,  6, 40, 13, 48, 45,  7, 21, 51, 59, 41, 42, 18,
       24, 53, 62, 20, 60, 33, 27, 39, 65,  1, 30, 26, 25, 47, 44]), 'cur_cost': 101569.0}, {'tour': [37, 18, 4, 42, 23, 1, 28, 14, 57, 53, 60, 32, 45, 11, 2, 54, 30, 13, 65, 59, 50, 21, 9, 40, 19, 35, 17, 25, 6, 46, 56, 3, 31, 48, 62, 8, 26, 63, 52, 16, 22, 34, 41, 5, 33, 27, 49, 10, 58, 24, 47, 38, 12, 43, 61, 0, 55, 39, 29, 20, 44, 7, 36, 64, 15, 51], 'cur_cost': 110676.0}, {'tour': array([51, 61, 28, 34, 31, 29, 39, 57,  3, 24, 23, 62, 43, 20, 47,  5, 56,
       52, 48, 26, 13, 41, 65,  0, 30,  4, 46, 42, 59, 45,  1, 33, 38, 14,
        9, 22, 50, 44, 53, 54, 25, 32, 55, 60, 27, 63, 37, 64, 12, 35,  2,
       17,  7,  8,  6, 21, 11, 16, 10, 49, 15, 40, 36, 19, 18, 58]), 'cur_cost': 108439.0}]
2025-06-22 17:42:47,986 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:42:47,986 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-06-22 17:42:47,986 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 17:42:47,986 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 17:42:47,986 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:42:47,986 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [37, 18, 4] and ending with [64, 15, 51]
- Cost: 110676.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8676923076923077
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:42:47,986 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:42:52,041 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    37, 23, 45, 10, 16, 28, 5, 60, 11, 40, 3, 57, 19, 24, 63, 1, 42, 59, 51, 29, 52, 49, 34, 64, 15, 21, 36, 50, 47, 32, 54, 20, 35, 41, 61, 8, 56, 6, 25, 0, 2, 38, 55, 33, 46, 65, 7, 14, 44, 9, 27, 4, 18, 31, 17, 26, 48, 53, 62, 13, 30, 12, 43, 39
  ],
  "modification_strategy": "This path generation strategy prioritizes exploration of unexplored regions while maintaining a degree of path quality by incorporating some elements from the original path and strategically introducing new, potentially high-reward, nodes. A significant shift from the original path has been implemented by re-arranging the sequence of nodes to include more diverse node transitions. Randomness is used to shuffle a section, making it significantly different.",
  "targeted_regions": "The primary focus is to explore the two 'opportunity regions'. The path aims to traverse through previously unexplored areas more frequently, with nodes scattered more widely compared to the provided original path, increasing the likelihood of visiting these opportunity regions."
}
```
2025-06-22 17:42:52,042 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:42:52,043 - ExplorationExpert - INFO - 探索路径生成完成，成本: 117106.0, 路径: [37, 23, 45, 10, 16, 28, 5, 60, 11, 40, 3, 57, 19, 24, 63, 1, 42, 59, 51, 29, 52, 49, 34, 64, 15, 21, 36, 50, 47, 32, 54, 20, 35, 41, 61, 8, 56, 6, 25, 0, 2, 38, 55, 33, 46, 65, 7, 14, 44, 9, 27, 4, 18, 31, 17, 26, 48, 53, 62, 13, 30, 12, 43, 39, 22, 58]
2025-06-22 17:42:52,043 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [37, 23, 45, 10, 16, 28, 5, 60, 11, 40, 3, 57, 19, 24, 63, 1, 42, 59, 51, 29, 52, 49, 34, 64, 15, 21, 36, 50, 47, 32, 54, 20, 35, 41, 61, 8, 56, 6, 25, 0, 2, 38, 55, 33, 46, 65, 7, 14, 44, 9, 27, 4, 18, 31, 17, 26, 48, 53, 62, 13, 30, 12, 43, 39, 22, 58], 'cur_cost': 117106.0}
2025-06-22 17:42:52,044 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 17:42:52,044 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:42:52,044 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:42:52,044 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 105810.0
2025-06-22 17:42:52,556 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:42:52,556 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557]
2025-06-22 17:42:52,556 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:42:52,560 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:42:52,560 - ExploitationExpert - INFO - populations: [{'tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}, {'tour': [14, 29, 45, 38, 51, 2, 60, 10, 1, 33, 6, 23, 5, 11, 20, 4, 39, 50, 3, 44, 35, 25, 48, 57, 65, 27, 52, 42, 16, 9, 31, 47, 62, 37, 32, 58, 19, 17, 40, 64, 15, 43, 18, 22, 24, 46, 26, 53, 21, 59, 63, 56, 30, 8, 12, 28, 34, 49, 0, 54, 55, 61, 36, 13, 7, 41], 'cur_cost': 101990.0}, {'tour': [18, 23, 16, 2, 10, 58, 48, 62, 38, 55, 31, 1, 40, 12, 27, 4, 11, 33, 14, 60, 24, 56, 0, 5, 28, 52, 15, 64, 3, 26, 29, 44, 63, 20, 36, 65, 34, 32, 46, 21, 30, 53, 42, 17, 39, 22, 41, 59, 8, 9, 61, 50, 49, 57, 13, 7, 35, 47, 25, 54, 45, 6, 37, 51, 43, 19], 'cur_cost': 113926.0}, {'tour': [57, 4, 33, 18, 5, 24, 47, 60, 36, 14, 2, 52, 25, 39, 11, 3, 43, 10, 31, 20, 37, 65, 17, 54, 40, 50, 15, 41, 28, 51, 6, 1, 21, 62, 9, 53, 35, 49, 22, 13, 59, 29, 32, 46, 44, 7, 38, 64, 8, 61, 42, 34, 16, 23, 26, 55, 0, 58, 48, 27, 12, 45, 63, 19, 30, 56], 'cur_cost': 113054.0}, {'tour': [42, 24, 49, 18, 58, 31, 4, 11, 52, 60, 21, 35, 1, 15, 63, 28, 5, 45, 3, 9, 37, 26, 19, 50, 55, 0, 61, 14, 30, 2, 22, 51, 40, 33, 27, 48, 12, 56, 65, 39, 44, 7, 10, 6, 41, 13, 64, 36, 25, 16, 34, 59, 8, 46, 20, 38, 29, 53, 32, 54, 43, 17, 62, 47, 23, 57], 'cur_cost': 109242.0}, {'tour': array([43, 22, 56, 24, 54, 63, 15, 25, 55, 30, 37, 35,  6,  8, 64, 61, 45,
       11, 58, 57, 29, 42,  5, 52, 59, 48, 17,  4, 51, 12, 27, 38, 31,  1,
       36, 18, 26, 19,  2, 10, 40,  9, 20, 47, 39, 21, 53,  0, 13, 34, 44,
       32,  7, 41, 28, 46, 33, 49, 16,  3, 60, 50, 14, 65, 23, 62]), 'cur_cost': 108617.0}, {'tour': array([43, 30, 54, 40,  2, 34,  7, 10,  6, 17, 14, 49, 29, 42, 41, 12, 53,
       37, 45, 62, 23,  1, 52, 33, 55, 25, 59, 20, 56, 26,  3, 63, 27, 50,
       57, 31, 13, 47, 32,  4, 22, 15, 51, 11, 48,  5, 58, 61, 39, 35, 36,
       64, 19,  0, 38,  8, 18, 46, 21, 65,  9, 16, 28, 60, 44, 24]), 'cur_cost': 123280.0}, {'tour': array([46, 36, 38, 34, 17, 54,  5, 57, 43, 11, 12, 29, 22,  4, 49, 23,  0,
       16,  3, 58, 52,  8,  2, 19, 37, 64, 55, 14, 31, 28, 61, 50, 10, 15,
       63, 32, 56, 35,  9,  6, 40, 13, 48, 45,  7, 21, 51, 59, 41, 42, 18,
       24, 53, 62, 20, 60, 33, 27, 39, 65,  1, 30, 26, 25, 47, 44]), 'cur_cost': 101569.0}, {'tour': [37, 23, 45, 10, 16, 28, 5, 60, 11, 40, 3, 57, 19, 24, 63, 1, 42, 59, 51, 29, 52, 49, 34, 64, 15, 21, 36, 50, 47, 32, 54, 20, 35, 41, 61, 8, 56, 6, 25, 0, 2, 38, 55, 33, 46, 65, 7, 14, 44, 9, 27, 4, 18, 31, 17, 26, 48, 53, 62, 13, 30, 12, 43, 39, 22, 58], 'cur_cost': 117106.0}, {'tour': array([14, 55, 30,  0,  6, 63, 28, 50, 35, 65, 18, 37, 45,  4, 52, 27,  5,
       16, 34, 64, 53, 48, 19, 11,  2,  3, 44, 54, 10, 23, 58, 42, 22, 13,
       26, 20, 21, 41, 61,  9, 36, 57, 38,  8, 51, 17, 40, 43, 25, 56, 59,
       12, 32, 29, 47, 31, 62,  1,  7, 33, 24, 60, 46, 39, 49, 15]), 'cur_cost': 105810.0}]
2025-06-22 17:42:52,562 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 17:42:52,562 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-06-22 17:42:52,562 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 17:42:52,562 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [50, 38, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11408.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [14, 29, 45, 38, 51, 2, 60, 10, 1, 33, 6, 23, 5, 11, 20, 4, 39, 50, 3, 44, 35, 25, 48, 57, 65, 27, 52, 42, 16, 9, 31, 47, 62, 37, 32, 58, 19, 17, 40, 64, 15, 43, 18, 22, 24, 46, 26, 53, 21, 59, 63, 56, 30, 8, 12, 28, 34, 49, 0, 54, 55, 61, 36, 13, 7, 41], 'cur_cost': 101990.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [18, 23, 16, 2, 10, 58, 48, 62, 38, 55, 31, 1, 40, 12, 27, 4, 11, 33, 14, 60, 24, 56, 0, 5, 28, 52, 15, 64, 3, 26, 29, 44, 63, 20, 36, 65, 34, 32, 46, 21, 30, 53, 42, 17, 39, 22, 41, 59, 8, 9, 61, 50, 49, 57, 13, 7, 35, 47, 25, 54, 45, 6, 37, 51, 43, 19], 'cur_cost': 113926.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [57, 4, 33, 18, 5, 24, 47, 60, 36, 14, 2, 52, 25, 39, 11, 3, 43, 10, 31, 20, 37, 65, 17, 54, 40, 50, 15, 41, 28, 51, 6, 1, 21, 62, 9, 53, 35, 49, 22, 13, 59, 29, 32, 46, 44, 7, 38, 64, 8, 61, 42, 34, 16, 23, 26, 55, 0, 58, 48, 27, 12, 45, 63, 19, 30, 56], 'cur_cost': 113054.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [42, 24, 49, 18, 58, 31, 4, 11, 52, 60, 21, 35, 1, 15, 63, 28, 5, 45, 3, 9, 37, 26, 19, 50, 55, 0, 61, 14, 30, 2, 22, 51, 40, 33, 27, 48, 12, 56, 65, 39, 44, 7, 10, 6, 41, 13, 64, 36, 25, 16, 34, 59, 8, 46, 20, 38, 29, 53, 32, 54, 43, 17, 62, 47, 23, 57], 'cur_cost': 109242.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [37, 23, 45, 10, 16, 28, 5, 60, 11, 40, 3, 57, 19, 24, 63, 1, 42, 59, 51, 29, 52, 49, 34, 64, 15, 21, 36, 50, 47, 32, 54, 20, 35, 41, 61, 8, 56, 6, 25, 0, 2, 38, 55, 33, 46, 65, 7, 14, 44, 9, 27, 4, 18, 31, 17, 26, 48, 53, 62, 13, 30, 12, 43, 39, 22, 58], 'cur_cost': 117106.0}}]
2025-06-22 17:42:52,563 - __main__ - INFO - 进化阶段完成
2025-06-22 17:42:52,563 - __main__ - INFO - 开始评估阶段
2025-06-22 17:42:52,563 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 17:42:52,564 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 17:42:52,564 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 3/5
- Population changes:
  * Cost: min 11408.0->11408.0, mean 91308.5->100600.2
  * Diversity: 0.816923076923077->0.8661538461538462
  * Elite solutions: 1->1

## Strategy Performance:
- Explore: 6 individuals, success rate 16.7%, avg improvement -16514.67
- Exploit: 4 individuals, success rate 75.0%, avg improvement 1542.75
- Overall improvement: 0.0

## Historical Trends:
Last 3 iterations: costs [13971.0, 11408.0, 11408.0], diversity [0.8730639730639731, 0.9515151515151514, 0.9552188552188552]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 17:42:58,328 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Late-Stage Refinement/Exploitation",
    "features": "The population is converging on a solution. The minimum cost has stagnated, indicating the discovery of a local optimum. Diversity is still relatively high but decreasing, suggesting a narrow search space.",
    "justification": "Minimum cost hasn't changed, but diversity is still reasonable.  The overall improvement is zero, indicating a potential plateau."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Poor",
      "metrics": "Low success rate (16.7%) and a large average *negative* improvement (-16514.67).",
      "assessment": "Exploration appears to be actively harming the population, likely due to exploring regions that are less promising.  This is expected in late-stage refinement."
    },
    "exploit": {
      "performance": "Good",
      "metrics": "High success rate (75.0%) and a positive average improvement (1542.75).",
      "assessment": "Exploitation is succeeding in making small improvements, refining the best solutions. This is the expected behavior for late stage."
    },
    "overall": {
      "summary": "Overall improvement is 0.0, indicating the algorithm is approaching convergence or has reached a local optimum."
    }
  },
  "balance_state": {
    "assessment": "The balance is heavily skewed towards exploitation.  Exploration is actively hurting the process and should be reduced significantly or re-targeted. The high diversity number is somewhat misleading given the stagnation in minimum cost",
    "adjustment_needs": "Increase the emphasis on exploitation and fine-tune the exploitation strategy. Reduce the rate of exploration and adjust its parameters if used.",
    "risk": "Risk of premature convergence on a local optimum."
  },
  "recommendations": {
    "next_iteration": [
      {
        "action": "Reduce exploration rate",
        "details": "Significantly reduce the number of exploratory individuals or completely disable exploration. If exploration is kept, use it cautiously and try to explore areas near successful solutions."
      },
      {
        "action": "Refine exploitation",
        "details": "Focus on refining the existing solutions via exploitation.  Consider using a more aggressive exploitation operator or a larger number of exploitation iterations, or focusing on the best individuals. Refine existing solutions with more emphasis."
      },
      {
        "action": "Diversity Preservation",
        "details": "Monitor diversity to ensure the algorithm does not prematurely converge to a single local optimum. If diversity starts to drop too quickly, consider mild strategies for diversity maintenance (e.g., age-based selection)."
      },
      {
        "action": "Consider alternative termination conditions",
        "details": "Since minimum cost is not changing, the evolution process might be finished or stuck at a local optimum. Before the next iteration, make sure the stopping condition is defined adequately to avoid unnecessary computation, considering that it might be an optimal solution"
      }
    ]
  }
}
```
2025-06-22 17:42:58,357 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 17:42:58,357 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Late-Stage Refinement/Exploitation",
    "features": "The population is converging on a solution. The minimum cost has stagnated, indicating the discovery of a local optimum. Diversity is still relatively high but decreasing, suggesting a narrow search space.",
    "justification": "Minimum cost hasn't changed, but diversity is still reasonable.  The overall improvement is zero, indicating a potential plateau."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Poor",
      "metrics": "Low success rate (16.7%) and a large average *negative* improvement (-16514.67).",
      "assessment": "Exploration appears to be actively harming the population, likely due to exploring regions that are less promising.  This is expected in late-stage refinement."
    },
    "exploit": {
      "performance": "Good",
      "metrics": "High success rate (75.0%) and a positive average improvement (1542.75).",
      "assessment": "Exploitation is succeeding in making small improvements, refining the best solutions. This is the expected behavior for late stage."
    },
    "overall": {
      "summary": "Overall improvement is 0.0, indicating the algorithm is approaching convergence or has reached a local optimum."
    }
  },
  "balance_state": {
    "assessment": "The balance is heavily skewed towards exploitation.  Exploration is actively hurting the process and should be reduced significantly or re-targeted. The high diversity number is somewhat misleading given the stagnation in minimum cost",
    "adjustment_needs": "Increase the emphasis on exploitation and fine-tune the exploitation strategy. Reduce the rate of exploration and adjust its parameters if used.",
    "risk": "Risk of premature convergence on a local optimum."
  },
  "recommendations": {
    "next_iteration": [
      {
        "action": "Reduce exploration rate",
        "details": "Significantly reduce the number of exploratory individuals or completely disable exploration. If exploration is kept, use it cautiously and try to explore areas near successful solutions."
      },
      {
        "action": "Refine exploitation",
        "details": "Focus on refining the existing solutions via exploitation.  Consider using a more aggressive exploitation operator or a larger number of exploitation iterations, or focusing on the best individuals. Refine existing solutions with more emphasis."
      },
      {
        "action": "Diversity Preservation",
        "details": "Monitor diversity to ensure the algorithm does not prematurely converge to a single local optimum. If diversity starts to drop too quickly, consider mild strategies for diversity maintenance (e.g., age-based selection)."
      },
      {
        "action": "Consider alternative termination conditions",
        "details": "Since minimum cost is not changing, the evolution process might be finished or stuck at a local optimum. Before the next iteration, make sure the stopping condition is defined adequately to avoid unnecessary computation, considering that it might be an optimal solution"
      }
    ]
  }
}
```
2025-06-22 17:42:58,357 - __main__ - INFO - 评估阶段完成
2025-06-22 17:42:58,357 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Late-Stage Refinement/Exploitation",
    "features": "The population is converging on a solution. The minimum cost has stagnated, indicating the discovery of a local optimum. Diversity is still relatively high but decreasing, suggesting a narrow search space.",
    "justification": "Minimum cost hasn't changed, but diversity is still reasonable.  The overall improvement is zero, indicating a potential plateau."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Poor",
      "metrics": "Low success rate (16.7%) and a large average *negative* improvement (-16514.67).",
      "assessment": "Exploration appears to be actively harming the population, likely due to exploring regions that are less promising.  This is expected in late-stage refinement."
    },
    "exploit": {
      "performance": "Good",
      "metrics": "High success rate (75.0%) and a positive average improvement (1542.75).",
      "assessment": "Exploitation is succeeding in making small improvements, refining the best solutions. This is the expected behavior for late stage."
    },
    "overall": {
      "summary": "Overall improvement is 0.0, indicating the algorithm is approaching convergence or has reached a local optimum."
    }
  },
  "balance_state": {
    "assessment": "The balance is heavily skewed towards exploitation.  Exploration is actively hurting the process and should be reduced significantly or re-targeted. The high diversity number is somewhat misleading given the stagnation in minimum cost",
    "adjustment_needs": "Increase the emphasis on exploitation and fine-tune the exploitation strategy. Reduce the rate of exploration and adjust its parameters if used.",
    "risk": "Risk of premature convergence on a local optimum."
  },
  "recommendations": {
    "next_iteration": [
      {
        "action": "Reduce exploration rate",
        "details": "Significantly reduce the number of exploratory individuals or completely disable exploration. If exploration is kept, use it cautiously and try to explore areas near successful solutions."
      },
      {
        "action": "Refine exploitation",
        "details": "Focus on refining the existing solutions via exploitation.  Consider using a more aggressive exploitation operator or a larger number of exploitation iterations, or focusing on the best individuals. Refine existing solutions with more emphasis."
      },
      {
        "action": "Diversity Preservation",
        "details": "Monitor diversity to ensure the algorithm does not prematurely converge to a single local optimum. If diversity starts to drop too quickly, consider mild strategies for diversity maintenance (e.g., age-based selection)."
      },
      {
        "action": "Consider alternative termination conditions",
        "details": "Since minimum cost is not changing, the evolution process might be finished or stuck at a local optimum. Before the next iteration, make sure the stopping condition is defined adequately to avoid unnecessary computation, considering that it might be an optimal solution"
      }
    ]
  }
}
```
2025-06-22 17:42:58,360 - __main__ - INFO - 当前最佳适应度: 11408.0
2025-06-22 17:42:58,362 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_3.pkl
2025-06-22 17:42:58,362 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-06-22 17:42:58,362 - __main__ - INFO - 开始分析阶段
2025-06-22 17:42:58,362 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:42:58,380 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11408.0, 'max': 123280.0, 'mean': 100600.2, 'std': 30403.452609202133}, 'diversity': 0.967003367003367, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:42:58,380 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11408.0, 'max': 123280.0, 'mean': 100600.2, 'std': 30403.452609202133}, 'diversity_level': 0.967003367003367, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:42:58,381 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:42:58,381 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:42:58,381 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:42:58,383 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 17:42:58,383 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:42:58,384 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 17:42:58,384 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:42:58,384 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:42:58,384 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:42:58,385 - EliteExpert - INFO - 精英解分析完成
2025-06-22 17:42:58,385 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {'(62, 59)': 1.0, '(56, 58)': 1.0}, 'common_edge_ratio': 0.030303030303030304}, 'fixed_nodes': [{'node': 0, 'position': 0}, {'node': 20, 'position': 33}], 'population_gap': {'cost_gap': {'min_gap': 1881.0, 'avg_gap': 91060.2}, 'structure_gap': {'unique_elite_edges': 115, 'unique_pop_edges': 582, 'common_edges': 27}}, 'elite_diversity': {'diversity_score': 0.1767676767676768}}
2025-06-22 17:42:58,385 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:42:58,386 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:42:58,386 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:42:58,386 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=11408.0, max=123280.0, mean=100600.2, std=30403.452609202133
- Diversity: 0.967003367003367, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 2 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 0 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:43:05,258 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "The search space appears moderately rugged. The high standard deviation of costs (30403.452609202133) and the large difference between min and max costs (11408.0 and 123280.0) indicate a significant variation in solution quality. The absence of identified difficult or opportunity regions suggests a lack of strong localized structures, but the cost variability implies potential for both good and bad solutions.",
    "modality": "The modality is unclear.  The absence of convergence and the lack of elite solutions with shared edges provides limited evidence for multi-modal behavior. The presence of a diverse population, and cost variability, might suggest multiple potential optimal regions, but this is not confirmed. More data on the distribution of costs across the search space would be beneficial."
  },
  "population_state": {
    "diversity": "High. The diversity score of 0.967 is very close to its maximum value, indicating that the population explores a wide range of solutions.",
    "convergence": "Very low. The convergence score of 0.0 indicates that solutions have not clustered around a single point, supporting the notion that the search process has not found, or is not moving towards, any specific elite or dominant solution. No high-quality edges or common subpaths support the conclusion of convergence."
  },
  "difficult_regions": {
    "identified_challenges": "No difficult regions were explicitly identified in the data. The high cost variability and spread of costs might make it difficult to isolate and consistently reach high-quality solutions.  The absence of high-quality edges or common subpaths indicates a lack of promising structure in the solution space that is difficult to navigate or traverse through."
  },
  "opportunity_regions": {
    "potential_areas": "No opportunity regions were identified. The high diversity, combined with the cost variation, suggests that solutions are distributed throughout the search space. There is no evidence of local structure. Focusing on general exploration across all or most solution features might be beneficial."
  },
  "evolution_direction": {
    "recommended_strategy": "Exploration and diversification are crucial. Given the high diversity and low convergence, the priority should be exploring the search space further, perhaps by modifying the exploration strategy, or the selection operator. Consider the following steps:\n\n1.  **Increase Exploration:** Ensure the algorithm maintains a strong exploratory capability.  This can include using a mutation operator with a sufficient step size and/or cross-over/recombination operators that promote diversification. Consider increasing the mutation rate. \n2.  **Avoid Premature Convergence:** Continue to avoid elitism, or other mechanisms that promote premature convergence.  If elitism is used, consider reducing it, or not using it at all until the population has had enough opportunity to diversify. \n3. **Consider Cost Analysis:** Analyze the cost distribution further to see if there are any clusters within the cost values that are clustered. If clusters are observed, the exploration strategies need to be adapted accordingly.\n4. **Refine Evaluation**: While it is not possible to determine the evaluation function from the data provided, any refinement here may provide further insights or changes to the recommended actions above."
  }
}
```
2025-06-22 17:43:05,258 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:43:05,259 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'The search space appears moderately rugged. The high standard deviation of costs (30403.452609202133) and the large difference between min and max costs (11408.0 and 123280.0) indicate a significant variation in solution quality. The absence of identified difficult or opportunity regions suggests a lack of strong localized structures, but the cost variability implies potential for both good and bad solutions.', 'modality': 'The modality is unclear.  The absence of convergence and the lack of elite solutions with shared edges provides limited evidence for multi-modal behavior. The presence of a diverse population, and cost variability, might suggest multiple potential optimal regions, but this is not confirmed. More data on the distribution of costs across the search space would be beneficial.'}, 'population_state': {'diversity': 'High. The diversity score of 0.967 is very close to its maximum value, indicating that the population explores a wide range of solutions.', 'convergence': 'Very low. The convergence score of 0.0 indicates that solutions have not clustered around a single point, supporting the notion that the search process has not found, or is not moving towards, any specific elite or dominant solution. No high-quality edges or common subpaths support the conclusion of convergence.'}, 'difficult_regions': {'identified_challenges': 'No difficult regions were explicitly identified in the data. The high cost variability and spread of costs might make it difficult to isolate and consistently reach high-quality solutions.  The absence of high-quality edges or common subpaths indicates a lack of promising structure in the solution space that is difficult to navigate or traverse through.'}, 'opportunity_regions': {'potential_areas': 'No opportunity regions were identified. The high diversity, combined with the cost variation, suggests that solutions are distributed throughout the search space. There is no evidence of local structure. Focusing on general exploration across all or most solution features might be beneficial.'}, 'evolution_direction': {'recommended_strategy': 'Exploration and diversification are crucial. Given the high diversity and low convergence, the priority should be exploring the search space further, perhaps by modifying the exploration strategy, or the selection operator. Consider the following steps:\n\n1.  **Increase Exploration:** Ensure the algorithm maintains a strong exploratory capability.  This can include using a mutation operator with a sufficient step size and/or cross-over/recombination operators that promote diversification. Consider increasing the mutation rate. \n2.  **Avoid Premature Convergence:** Continue to avoid elitism, or other mechanisms that promote premature convergence.  If elitism is used, consider reducing it, or not using it at all until the population has had enough opportunity to diversify. \n3. **Consider Cost Analysis:** Analyze the cost distribution further to see if there are any clusters within the cost values that are clustered. If clusters are observed, the exploration strategies need to be adapted accordingly.\n4. **Refine Evaluation**: While it is not possible to determine the evaluation function from the data provided, any refinement here may provide further insights or changes to the recommended actions above.'}}
2025-06-22 17:43:05,259 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:43:05,260 - __main__ - INFO - 分析阶段完成
2025-06-22 17:43:05,260 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'The search space appears moderately rugged. The high standard deviation of costs (30403.452609202133) and the large difference between min and max costs (11408.0 and 123280.0) indicate a significant variation in solution quality. The absence of identified difficult or opportunity regions suggests a lack of strong localized structures, but the cost variability implies potential for both good and bad solutions.', 'modality': 'The modality is unclear.  The absence of convergence and the lack of elite solutions with shared edges provides limited evidence for multi-modal behavior. The presence of a diverse population, and cost variability, might suggest multiple potential optimal regions, but this is not confirmed. More data on the distribution of costs across the search space would be beneficial.'}, 'population_state': {'diversity': 'High. The diversity score of 0.967 is very close to its maximum value, indicating that the population explores a wide range of solutions.', 'convergence': 'Very low. The convergence score of 0.0 indicates that solutions have not clustered around a single point, supporting the notion that the search process has not found, or is not moving towards, any specific elite or dominant solution. No high-quality edges or common subpaths support the conclusion of convergence.'}, 'difficult_regions': {'identified_challenges': 'No difficult regions were explicitly identified in the data. The high cost variability and spread of costs might make it difficult to isolate and consistently reach high-quality solutions.  The absence of high-quality edges or common subpaths indicates a lack of promising structure in the solution space that is difficult to navigate or traverse through.'}, 'opportunity_regions': {'potential_areas': 'No opportunity regions were identified. The high diversity, combined with the cost variation, suggests that solutions are distributed throughout the search space. There is no evidence of local structure. Focusing on general exploration across all or most solution features might be beneficial.'}, 'evolution_direction': {'recommended_strategy': 'Exploration and diversification are crucial. Given the high diversity and low convergence, the priority should be exploring the search space further, perhaps by modifying the exploration strategy, or the selection operator. Consider the following steps:\n\n1.  **Increase Exploration:** Ensure the algorithm maintains a strong exploratory capability.  This can include using a mutation operator with a sufficient step size and/or cross-over/recombination operators that promote diversification. Consider increasing the mutation rate. \n2.  **Avoid Premature Convergence:** Continue to avoid elitism, or other mechanisms that promote premature convergence.  If elitism is used, consider reducing it, or not using it at all until the population has had enough opportunity to diversify. \n3. **Consider Cost Analysis:** Analyze the cost distribution further to see if there are any clusters within the cost values that are clustered. If clusters are observed, the exploration strategies need to be adapted accordingly.\n4. **Refine Evaluation**: While it is not possible to determine the evaluation function from the data provided, any refinement here may provide further insights or changes to the recommended actions above.'}}
2025-06-22 17:43:05,260 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:43:05,260 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:43:05,260 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 4
- Population: 10 individuals, diversity=High. The diversity score of 0.967 is very close to its maximum value, indicating that the population explores a wide range of solutions.
- Best cost: 11408.0, Mean cost: 100600.2
- Search space: Ruggedness: The search space appears moderately rugged. The high standard deviation of costs (30403.452609202133) and the large difference between min and max costs (11408.0 and 123280.0) indicate a significant variation in solution quality. The absence of identified difficult or opportunity regions suggests a lack of strong localized structures, but the cost variability implies potential for both good and bad solutions., Modality: The modality is unclear.  The absence of convergence and the lack of elite solutions with shared edges provides limited evidence for multi-modal behavior. The presence of a diverse population, and cost variability, might suggest multiple potential optimal regions, but this is not confirmed. More data on the distribution of costs across the search space would be beneficial.
- Difficult regions: 1 regions
- Opportunity regions: 1 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Late-Stage Refinement/Exploitation",
    "features": "The population is converging on a solution. The minimum cost has stagnated, indicating the discover...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:43:05,261 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:43:07,775 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.2,
  "individual_assignments": {
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore",
    "10": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "crossover_rate": 0.8,
      "selection_pressure": "moderate"
    },
    "exploit": {
      "mutation_rate": 0.1,
      "crossover_rate": 0.2,
      "selection_pressure": "high",
      "local_search_iterations": 5
    }
  }
}
```

2025-06-22 17:43:07,775 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-06-22 17:43:07,776 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-06-22 17:43:07,776 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.2,
  "individual_assignments": {
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore",
    "10": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "crossover_rate": 0.8,
      "selection_pressure": "moderate"
    },
    "exploit": {
      "mutation_rate": 0.1,
      "crossover_rate": 0.2,
      "selection_pressure": "high",
      "local_search_iterations": 5
    }
  }
}
```

2025-06-22 17:43:07,776 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:43:07,776 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-06-22 17:43:07,776 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.2,
  "individual_assignments": {
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore",
    "10": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "crossover_rate": 0.8,
      "selection_pressure": "moderate"
    },
    "exploit": {
      "mutation_rate": 0.1,
      "crossover_rate": 0.2,
      "selection_pressure": "high",
      "local_search_iterations": 5
    }
  }
}
```

2025-06-22 17:43:07,777 - __main__ - INFO - 开始进化阶段
2025-06-22 17:43:07,777 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:43:07,777 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:43:07,778 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [50, 38, 51] and ending with [47, 48, 49]
- Cost: 11408.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8661538461538462
- Unexplored regions: 1 opportunity regions
- Opportunity regions: 1 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:43:07,778 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:43:11,443 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    50, 51, 52, 53, 54, 55, 56, 57, 58, 59,
    60, 61, 62, 63, 64, 65, 0, 1, 2, 3,
    4, 5, 6, 7, 8, 9, 10, 11, 12, 13,
    14, 15, 16, 17, 18, 19, 20, 21, 22, 23,
    24, 25, 26, 27, 28, 29, 30, 31, 32, 33,
    34, 35, 36, 37, 38, 39, 40, 41, 42, 43,
    44, 45, 46, 47, 48, 49
  ],
  "modification_strategy": "This strategy prioritizes diversification by constructing a path that broadly explores the graph.  It starts with a sequence focused on potential 'opportunity regions' and then proceeds with a more or less ordered traversal of the remaining nodes. The initial section of the new path seeks to find a promising route through the nodes related to those 'opportunity regions', and attempts to connect to areas that may be 'difficult to avoid'.  The path then follows a more ordered structure for the remaining nodes to guarantee that every node is visited exactly once, maximizing diversity compared to the original path.",
  "targeted_regions": ["Opportunity regions", "Areas that might be 'difficult to avoid'"]
}
```
2025-06-22 17:43:11,443 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:43:11,444 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49]
2025-06-22 17:43:11,444 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11479.0}
2025-06-22 17:43:11,445 - __main__ - INFO - 为个体 1 生成利用路径
2025-06-22 17:43:11,445 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:43:11,445 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:43:11,445 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 100590.0
2025-06-22 17:43:11,958 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:43:11,959 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557]
2025-06-22 17:43:11,959 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:43:11,960 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:43:11,960 - ExploitationExpert - INFO - populations: [{'tour': [50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11479.0}, {'tour': array([27, 36, 47, 59, 63, 17,  3, 28,  0, 64, 41, 49, 24, 51,  2, 26, 52,
        8, 34, 15, 39,  1, 37, 22, 16, 21, 42, 44, 30,  5, 58,  6, 12, 50,
       57, 54,  9, 20, 65,  7, 62, 60, 32, 11, 61, 25, 29, 23, 55, 33, 48,
       40, 19, 35, 38, 18, 14, 46, 56, 31,  4, 43, 45, 10, 53, 13]), 'cur_cost': 100590.0}, {'tour': [18, 23, 16, 2, 10, 58, 48, 62, 38, 55, 31, 1, 40, 12, 27, 4, 11, 33, 14, 60, 24, 56, 0, 5, 28, 52, 15, 64, 3, 26, 29, 44, 63, 20, 36, 65, 34, 32, 46, 21, 30, 53, 42, 17, 39, 22, 41, 59, 8, 9, 61, 50, 49, 57, 13, 7, 35, 47, 25, 54, 45, 6, 37, 51, 43, 19], 'cur_cost': 113926.0}, {'tour': [57, 4, 33, 18, 5, 24, 47, 60, 36, 14, 2, 52, 25, 39, 11, 3, 43, 10, 31, 20, 37, 65, 17, 54, 40, 50, 15, 41, 28, 51, 6, 1, 21, 62, 9, 53, 35, 49, 22, 13, 59, 29, 32, 46, 44, 7, 38, 64, 8, 61, 42, 34, 16, 23, 26, 55, 0, 58, 48, 27, 12, 45, 63, 19, 30, 56], 'cur_cost': 113054.0}, {'tour': [42, 24, 49, 18, 58, 31, 4, 11, 52, 60, 21, 35, 1, 15, 63, 28, 5, 45, 3, 9, 37, 26, 19, 50, 55, 0, 61, 14, 30, 2, 22, 51, 40, 33, 27, 48, 12, 56, 65, 39, 44, 7, 10, 6, 41, 13, 64, 36, 25, 16, 34, 59, 8, 46, 20, 38, 29, 53, 32, 54, 43, 17, 62, 47, 23, 57], 'cur_cost': 109242.0}, {'tour': array([43, 22, 56, 24, 54, 63, 15, 25, 55, 30, 37, 35,  6,  8, 64, 61, 45,
       11, 58, 57, 29, 42,  5, 52, 59, 48, 17,  4, 51, 12, 27, 38, 31,  1,
       36, 18, 26, 19,  2, 10, 40,  9, 20, 47, 39, 21, 53,  0, 13, 34, 44,
       32,  7, 41, 28, 46, 33, 49, 16,  3, 60, 50, 14, 65, 23, 62]), 'cur_cost': 108617.0}, {'tour': array([43, 30, 54, 40,  2, 34,  7, 10,  6, 17, 14, 49, 29, 42, 41, 12, 53,
       37, 45, 62, 23,  1, 52, 33, 55, 25, 59, 20, 56, 26,  3, 63, 27, 50,
       57, 31, 13, 47, 32,  4, 22, 15, 51, 11, 48,  5, 58, 61, 39, 35, 36,
       64, 19,  0, 38,  8, 18, 46, 21, 65,  9, 16, 28, 60, 44, 24]), 'cur_cost': 123280.0}, {'tour': array([46, 36, 38, 34, 17, 54,  5, 57, 43, 11, 12, 29, 22,  4, 49, 23,  0,
       16,  3, 58, 52,  8,  2, 19, 37, 64, 55, 14, 31, 28, 61, 50, 10, 15,
       63, 32, 56, 35,  9,  6, 40, 13, 48, 45,  7, 21, 51, 59, 41, 42, 18,
       24, 53, 62, 20, 60, 33, 27, 39, 65,  1, 30, 26, 25, 47, 44]), 'cur_cost': 101569.0}, {'tour': [37, 23, 45, 10, 16, 28, 5, 60, 11, 40, 3, 57, 19, 24, 63, 1, 42, 59, 51, 29, 52, 49, 34, 64, 15, 21, 36, 50, 47, 32, 54, 20, 35, 41, 61, 8, 56, 6, 25, 0, 2, 38, 55, 33, 46, 65, 7, 14, 44, 9, 27, 4, 18, 31, 17, 26, 48, 53, 62, 13, 30, 12, 43, 39, 22, 58], 'cur_cost': 117106.0}, {'tour': array([14, 55, 30,  0,  6, 63, 28, 50, 35, 65, 18, 37, 45,  4, 52, 27,  5,
       16, 34, 64, 53, 48, 19, 11,  2,  3, 44, 54, 10, 23, 58, 42, 22, 13,
       26, 20, 21, 41, 61,  9, 36, 57, 38,  8, 51, 17, 40, 43, 25, 56, 59,
       12, 32, 29, 47, 31, 62,  1,  7, 33, 24, 60, 46, 39, 49, 15]), 'cur_cost': 105810.0}]
2025-06-22 17:43:11,962 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 17:43:11,962 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-06-22 17:43:11,962 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-06-22 17:43:11,962 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-22 17:43:11,962 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:43:11,964 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:43:11,964 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 106358.0
2025-06-22 17:43:12,470 - ExploitationExpert - INFO - res_population_num: 4
2025-06-22 17:43:12,470 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557, 9524]
2025-06-22 17:43:12,470 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:43:12,472 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:43:12,473 - ExploitationExpert - INFO - populations: [{'tour': [50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11479.0}, {'tour': array([27, 36, 47, 59, 63, 17,  3, 28,  0, 64, 41, 49, 24, 51,  2, 26, 52,
        8, 34, 15, 39,  1, 37, 22, 16, 21, 42, 44, 30,  5, 58,  6, 12, 50,
       57, 54,  9, 20, 65,  7, 62, 60, 32, 11, 61, 25, 29, 23, 55, 33, 48,
       40, 19, 35, 38, 18, 14, 46, 56, 31,  4, 43, 45, 10, 53, 13]), 'cur_cost': 100590.0}, {'tour': array([ 0, 65, 57, 19, 38, 23, 58, 52, 42, 33, 55, 41,  6, 13, 26, 24, 17,
       16, 46, 32, 34, 50, 39, 30, 14, 59, 60, 62, 22, 63,  3, 40, 35, 61,
       49, 11,  7, 36, 10, 25, 64, 12, 43,  9, 20, 45, 27, 21, 48, 47,  1,
       56, 53, 28, 18,  8, 31, 15,  5, 29,  2, 44, 37,  4, 54, 51]), 'cur_cost': 106358.0}, {'tour': [57, 4, 33, 18, 5, 24, 47, 60, 36, 14, 2, 52, 25, 39, 11, 3, 43, 10, 31, 20, 37, 65, 17, 54, 40, 50, 15, 41, 28, 51, 6, 1, 21, 62, 9, 53, 35, 49, 22, 13, 59, 29, 32, 46, 44, 7, 38, 64, 8, 61, 42, 34, 16, 23, 26, 55, 0, 58, 48, 27, 12, 45, 63, 19, 30, 56], 'cur_cost': 113054.0}, {'tour': [42, 24, 49, 18, 58, 31, 4, 11, 52, 60, 21, 35, 1, 15, 63, 28, 5, 45, 3, 9, 37, 26, 19, 50, 55, 0, 61, 14, 30, 2, 22, 51, 40, 33, 27, 48, 12, 56, 65, 39, 44, 7, 10, 6, 41, 13, 64, 36, 25, 16, 34, 59, 8, 46, 20, 38, 29, 53, 32, 54, 43, 17, 62, 47, 23, 57], 'cur_cost': 109242.0}, {'tour': array([43, 22, 56, 24, 54, 63, 15, 25, 55, 30, 37, 35,  6,  8, 64, 61, 45,
       11, 58, 57, 29, 42,  5, 52, 59, 48, 17,  4, 51, 12, 27, 38, 31,  1,
       36, 18, 26, 19,  2, 10, 40,  9, 20, 47, 39, 21, 53,  0, 13, 34, 44,
       32,  7, 41, 28, 46, 33, 49, 16,  3, 60, 50, 14, 65, 23, 62]), 'cur_cost': 108617.0}, {'tour': array([43, 30, 54, 40,  2, 34,  7, 10,  6, 17, 14, 49, 29, 42, 41, 12, 53,
       37, 45, 62, 23,  1, 52, 33, 55, 25, 59, 20, 56, 26,  3, 63, 27, 50,
       57, 31, 13, 47, 32,  4, 22, 15, 51, 11, 48,  5, 58, 61, 39, 35, 36,
       64, 19,  0, 38,  8, 18, 46, 21, 65,  9, 16, 28, 60, 44, 24]), 'cur_cost': 123280.0}, {'tour': array([46, 36, 38, 34, 17, 54,  5, 57, 43, 11, 12, 29, 22,  4, 49, 23,  0,
       16,  3, 58, 52,  8,  2, 19, 37, 64, 55, 14, 31, 28, 61, 50, 10, 15,
       63, 32, 56, 35,  9,  6, 40, 13, 48, 45,  7, 21, 51, 59, 41, 42, 18,
       24, 53, 62, 20, 60, 33, 27, 39, 65,  1, 30, 26, 25, 47, 44]), 'cur_cost': 101569.0}, {'tour': [37, 23, 45, 10, 16, 28, 5, 60, 11, 40, 3, 57, 19, 24, 63, 1, 42, 59, 51, 29, 52, 49, 34, 64, 15, 21, 36, 50, 47, 32, 54, 20, 35, 41, 61, 8, 56, 6, 25, 0, 2, 38, 55, 33, 46, 65, 7, 14, 44, 9, 27, 4, 18, 31, 17, 26, 48, 53, 62, 13, 30, 12, 43, 39, 22, 58], 'cur_cost': 117106.0}, {'tour': array([14, 55, 30,  0,  6, 63, 28, 50, 35, 65, 18, 37, 45,  4, 52, 27,  5,
       16, 34, 64, 53, 48, 19, 11,  2,  3, 44, 54, 10, 23, 58, 42, 22, 13,
       26, 20, 21, 41, 61,  9, 36, 57, 38,  8, 51, 17, 40, 43, 25, 56, 59,
       12, 32, 29, 47, 31, 62,  1,  7, 33, 24, 60, 46, 39, 49, 15]), 'cur_cost': 105810.0}]
2025-06-22 17:43:12,474 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:43:12,474 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-06-22 17:43:12,474 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-06-22 17:43:12,476 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-22 17:43:12,476 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:43:12,476 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:43:12,476 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 104791.0
2025-06-22 17:43:12,991 - ExploitationExpert - INFO - res_population_num: 4
2025-06-22 17:43:12,991 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557, 9524]
2025-06-22 17:43:12,991 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:43:12,993 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:43:12,993 - ExploitationExpert - INFO - populations: [{'tour': [50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11479.0}, {'tour': array([27, 36, 47, 59, 63, 17,  3, 28,  0, 64, 41, 49, 24, 51,  2, 26, 52,
        8, 34, 15, 39,  1, 37, 22, 16, 21, 42, 44, 30,  5, 58,  6, 12, 50,
       57, 54,  9, 20, 65,  7, 62, 60, 32, 11, 61, 25, 29, 23, 55, 33, 48,
       40, 19, 35, 38, 18, 14, 46, 56, 31,  4, 43, 45, 10, 53, 13]), 'cur_cost': 100590.0}, {'tour': array([ 0, 65, 57, 19, 38, 23, 58, 52, 42, 33, 55, 41,  6, 13, 26, 24, 17,
       16, 46, 32, 34, 50, 39, 30, 14, 59, 60, 62, 22, 63,  3, 40, 35, 61,
       49, 11,  7, 36, 10, 25, 64, 12, 43,  9, 20, 45, 27, 21, 48, 47,  1,
       56, 53, 28, 18,  8, 31, 15,  5, 29,  2, 44, 37,  4, 54, 51]), 'cur_cost': 106358.0}, {'tour': array([37, 11, 51, 32, 45,  8,  3, 28, 52, 16,  9, 47, 48, 62, 42, 35,  5,
       38, 57, 65,  1, 36,  7, 21, 56, 61, 39, 63, 13, 49, 44, 31, 18, 53,
       60, 33, 19, 27, 46, 12, 24, 14, 26, 41,  2, 54, 40, 43, 59, 58, 55,
       22,  6, 25, 20, 30, 29, 50,  4, 64, 15, 17, 10,  0, 34, 23]), 'cur_cost': 104791.0}, {'tour': [42, 24, 49, 18, 58, 31, 4, 11, 52, 60, 21, 35, 1, 15, 63, 28, 5, 45, 3, 9, 37, 26, 19, 50, 55, 0, 61, 14, 30, 2, 22, 51, 40, 33, 27, 48, 12, 56, 65, 39, 44, 7, 10, 6, 41, 13, 64, 36, 25, 16, 34, 59, 8, 46, 20, 38, 29, 53, 32, 54, 43, 17, 62, 47, 23, 57], 'cur_cost': 109242.0}, {'tour': array([43, 22, 56, 24, 54, 63, 15, 25, 55, 30, 37, 35,  6,  8, 64, 61, 45,
       11, 58, 57, 29, 42,  5, 52, 59, 48, 17,  4, 51, 12, 27, 38, 31,  1,
       36, 18, 26, 19,  2, 10, 40,  9, 20, 47, 39, 21, 53,  0, 13, 34, 44,
       32,  7, 41, 28, 46, 33, 49, 16,  3, 60, 50, 14, 65, 23, 62]), 'cur_cost': 108617.0}, {'tour': array([43, 30, 54, 40,  2, 34,  7, 10,  6, 17, 14, 49, 29, 42, 41, 12, 53,
       37, 45, 62, 23,  1, 52, 33, 55, 25, 59, 20, 56, 26,  3, 63, 27, 50,
       57, 31, 13, 47, 32,  4, 22, 15, 51, 11, 48,  5, 58, 61, 39, 35, 36,
       64, 19,  0, 38,  8, 18, 46, 21, 65,  9, 16, 28, 60, 44, 24]), 'cur_cost': 123280.0}, {'tour': array([46, 36, 38, 34, 17, 54,  5, 57, 43, 11, 12, 29, 22,  4, 49, 23,  0,
       16,  3, 58, 52,  8,  2, 19, 37, 64, 55, 14, 31, 28, 61, 50, 10, 15,
       63, 32, 56, 35,  9,  6, 40, 13, 48, 45,  7, 21, 51, 59, 41, 42, 18,
       24, 53, 62, 20, 60, 33, 27, 39, 65,  1, 30, 26, 25, 47, 44]), 'cur_cost': 101569.0}, {'tour': [37, 23, 45, 10, 16, 28, 5, 60, 11, 40, 3, 57, 19, 24, 63, 1, 42, 59, 51, 29, 52, 49, 34, 64, 15, 21, 36, 50, 47, 32, 54, 20, 35, 41, 61, 8, 56, 6, 25, 0, 2, 38, 55, 33, 46, 65, 7, 14, 44, 9, 27, 4, 18, 31, 17, 26, 48, 53, 62, 13, 30, 12, 43, 39, 22, 58], 'cur_cost': 117106.0}, {'tour': array([14, 55, 30,  0,  6, 63, 28, 50, 35, 65, 18, 37, 45,  4, 52, 27,  5,
       16, 34, 64, 53, 48, 19, 11,  2,  3, 44, 54, 10, 23, 58, 42, 22, 13,
       26, 20, 21, 41, 61,  9, 36, 57, 38,  8, 51, 17, 40, 43, 25, 56, 59,
       12, 32, 29, 47, 31, 62,  1,  7, 33, 24, 60, 46, 39, 49, 15]), 'cur_cost': 105810.0}]
2025-06-22 17:43:12,996 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 17:43:12,996 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 190, 'cache_hit_rate': 0.0, 'cache_size': 190}}
2025-06-22 17:43:12,996 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-22 17:43:12,996 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-22 17:43:12,996 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:43:12,996 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:43:12,996 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 104343.0
2025-06-22 17:43:13,502 - ExploitationExpert - INFO - res_population_num: 4
2025-06-22 17:43:13,502 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557, 9524]
2025-06-22 17:43:13,503 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:43:13,504 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:43:13,505 - ExploitationExpert - INFO - populations: [{'tour': [50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11479.0}, {'tour': array([27, 36, 47, 59, 63, 17,  3, 28,  0, 64, 41, 49, 24, 51,  2, 26, 52,
        8, 34, 15, 39,  1, 37, 22, 16, 21, 42, 44, 30,  5, 58,  6, 12, 50,
       57, 54,  9, 20, 65,  7, 62, 60, 32, 11, 61, 25, 29, 23, 55, 33, 48,
       40, 19, 35, 38, 18, 14, 46, 56, 31,  4, 43, 45, 10, 53, 13]), 'cur_cost': 100590.0}, {'tour': array([ 0, 65, 57, 19, 38, 23, 58, 52, 42, 33, 55, 41,  6, 13, 26, 24, 17,
       16, 46, 32, 34, 50, 39, 30, 14, 59, 60, 62, 22, 63,  3, 40, 35, 61,
       49, 11,  7, 36, 10, 25, 64, 12, 43,  9, 20, 45, 27, 21, 48, 47,  1,
       56, 53, 28, 18,  8, 31, 15,  5, 29,  2, 44, 37,  4, 54, 51]), 'cur_cost': 106358.0}, {'tour': array([37, 11, 51, 32, 45,  8,  3, 28, 52, 16,  9, 47, 48, 62, 42, 35,  5,
       38, 57, 65,  1, 36,  7, 21, 56, 61, 39, 63, 13, 49, 44, 31, 18, 53,
       60, 33, 19, 27, 46, 12, 24, 14, 26, 41,  2, 54, 40, 43, 59, 58, 55,
       22,  6, 25, 20, 30, 29, 50,  4, 64, 15, 17, 10,  0, 34, 23]), 'cur_cost': 104791.0}, {'tour': array([52, 44, 51, 27, 58, 24,  0, 59, 53, 15, 26, 12, 20, 57, 25, 19, 41,
       54, 23, 50, 21,  1,  9, 62, 32, 65, 33, 42, 37, 64,  4, 60, 56, 43,
       46, 28, 36, 38, 63, 10, 61, 17, 40, 55, 31, 16, 45,  3, 34, 18,  7,
       47, 48, 39, 13,  2, 49, 22,  5, 29, 35, 14, 11,  6,  8, 30]), 'cur_cost': 104343.0}, {'tour': array([43, 22, 56, 24, 54, 63, 15, 25, 55, 30, 37, 35,  6,  8, 64, 61, 45,
       11, 58, 57, 29, 42,  5, 52, 59, 48, 17,  4, 51, 12, 27, 38, 31,  1,
       36, 18, 26, 19,  2, 10, 40,  9, 20, 47, 39, 21, 53,  0, 13, 34, 44,
       32,  7, 41, 28, 46, 33, 49, 16,  3, 60, 50, 14, 65, 23, 62]), 'cur_cost': 108617.0}, {'tour': array([43, 30, 54, 40,  2, 34,  7, 10,  6, 17, 14, 49, 29, 42, 41, 12, 53,
       37, 45, 62, 23,  1, 52, 33, 55, 25, 59, 20, 56, 26,  3, 63, 27, 50,
       57, 31, 13, 47, 32,  4, 22, 15, 51, 11, 48,  5, 58, 61, 39, 35, 36,
       64, 19,  0, 38,  8, 18, 46, 21, 65,  9, 16, 28, 60, 44, 24]), 'cur_cost': 123280.0}, {'tour': array([46, 36, 38, 34, 17, 54,  5, 57, 43, 11, 12, 29, 22,  4, 49, 23,  0,
       16,  3, 58, 52,  8,  2, 19, 37, 64, 55, 14, 31, 28, 61, 50, 10, 15,
       63, 32, 56, 35,  9,  6, 40, 13, 48, 45,  7, 21, 51, 59, 41, 42, 18,
       24, 53, 62, 20, 60, 33, 27, 39, 65,  1, 30, 26, 25, 47, 44]), 'cur_cost': 101569.0}, {'tour': [37, 23, 45, 10, 16, 28, 5, 60, 11, 40, 3, 57, 19, 24, 63, 1, 42, 59, 51, 29, 52, 49, 34, 64, 15, 21, 36, 50, 47, 32, 54, 20, 35, 41, 61, 8, 56, 6, 25, 0, 2, 38, 55, 33, 46, 65, 7, 14, 44, 9, 27, 4, 18, 31, 17, 26, 48, 53, 62, 13, 30, 12, 43, 39, 22, 58], 'cur_cost': 117106.0}, {'tour': array([14, 55, 30,  0,  6, 63, 28, 50, 35, 65, 18, 37, 45,  4, 52, 27,  5,
       16, 34, 64, 53, 48, 19, 11,  2,  3, 44, 54, 10, 23, 58, 42, 22, 13,
       26, 20, 21, 41, 61,  9, 36, 57, 38,  8, 51, 17, 40, 43, 25, 56, 59,
       12, 32, 29, 47, 31, 62,  1,  7, 33, 24, 60, 46, 39, 49, 15]), 'cur_cost': 105810.0}]
2025-06-22 17:43:13,507 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:43:13,508 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 210, 'cache_hit_rate': 0.0, 'cache_size': 210}}
2025-06-22 17:43:13,508 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-22 17:43:13,508 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-22 17:43:13,508 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:43:13,509 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [43, 22, 56] and ending with [65, 23, 62]
- Cost: 108617.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.88
- Unexplored regions: 1 opportunity regions
- Opportunity regions: 1 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:43:13,509 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:43:18,121 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    43, 22, 56, 3, 58, 12, 40, 15, 2, 28, 50, 61, 1, 4, 54, 32, 5, 30, 38, 42, 20, 63, 8, 64, 26, 44, 35, 10, 57, 18, 47, 51, 53, 25, 60, 36, 7, 29, 45, 14, 48, 33, 16, 37, 55, 6, 27, 34, 49, 39, 9, 65, 17, 46, 21, 31, 19, 23, 62, 13, 24, 11, 41, 52, 0
  ],
  "modification_strategy": "Heuristic-based path alteration focusing on diversity.  Starting from the initial path's start, I prioritized exploring unexplored regions and difficult regions by strategically altering the order of nodes.  The new path deviates significantly from the original while trying to maintain path coherence (avoiding large jumps in the overall TSP).  The strategy is to introduce a high degree of randomness (leveraging the diversity weight) in the order of the nodes to maximize the chance of finding a better and more diverse path.",
  "targeted_regions": "Unexplored regions and difficult regions (as indicated by the search context) were prioritized. The goal was to place nodes from these regions at earlier points in the generated path to ensure they are reached within the generated path. Also, the path was generated to be different, and to promote more exploration (as diversity weight is higher than risk tolerance)."
}
```
2025-06-22 17:43:18,121 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:43:18,123 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109997.0, 路径: [43, 22, 56, 3, 58, 12, 40, 15, 2, 28, 50, 61, 1, 4, 54, 32, 5, 30, 38, 42, 20, 63, 8, 64, 26, 44, 35, 10, 57, 18, 47, 51, 53, 25, 60, 36, 7, 29, 45, 14, 48, 33, 16, 37, 55, 6, 27, 34, 49, 39, 9, 65, 17, 46, 21, 31, 19, 23, 62, 13, 24, 11, 41, 52, 0, 59]
2025-06-22 17:43:18,123 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [43, 22, 56, 3, 58, 12, 40, 15, 2, 28, 50, 61, 1, 4, 54, 32, 5, 30, 38, 42, 20, 63, 8, 64, 26, 44, 35, 10, 57, 18, 47, 51, 53, 25, 60, 36, 7, 29, 45, 14, 48, 33, 16, 37, 55, 6, 27, 34, 49, 39, 9, 65, 17, 46, 21, 31, 19, 23, 62, 13, 24, 11, 41, 52, 0, 59], 'cur_cost': 109997.0}
2025-06-22 17:43:18,124 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 17:43:18,124 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:43:18,124 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:43:18,124 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 105619.0
2025-06-22 17:43:18,633 - ExploitationExpert - INFO - res_population_num: 4
2025-06-22 17:43:18,635 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557, 9524]
2025-06-22 17:43:18,635 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:43:18,636 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:43:18,636 - ExploitationExpert - INFO - populations: [{'tour': [50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11479.0}, {'tour': array([27, 36, 47, 59, 63, 17,  3, 28,  0, 64, 41, 49, 24, 51,  2, 26, 52,
        8, 34, 15, 39,  1, 37, 22, 16, 21, 42, 44, 30,  5, 58,  6, 12, 50,
       57, 54,  9, 20, 65,  7, 62, 60, 32, 11, 61, 25, 29, 23, 55, 33, 48,
       40, 19, 35, 38, 18, 14, 46, 56, 31,  4, 43, 45, 10, 53, 13]), 'cur_cost': 100590.0}, {'tour': array([ 0, 65, 57, 19, 38, 23, 58, 52, 42, 33, 55, 41,  6, 13, 26, 24, 17,
       16, 46, 32, 34, 50, 39, 30, 14, 59, 60, 62, 22, 63,  3, 40, 35, 61,
       49, 11,  7, 36, 10, 25, 64, 12, 43,  9, 20, 45, 27, 21, 48, 47,  1,
       56, 53, 28, 18,  8, 31, 15,  5, 29,  2, 44, 37,  4, 54, 51]), 'cur_cost': 106358.0}, {'tour': array([37, 11, 51, 32, 45,  8,  3, 28, 52, 16,  9, 47, 48, 62, 42, 35,  5,
       38, 57, 65,  1, 36,  7, 21, 56, 61, 39, 63, 13, 49, 44, 31, 18, 53,
       60, 33, 19, 27, 46, 12, 24, 14, 26, 41,  2, 54, 40, 43, 59, 58, 55,
       22,  6, 25, 20, 30, 29, 50,  4, 64, 15, 17, 10,  0, 34, 23]), 'cur_cost': 104791.0}, {'tour': array([52, 44, 51, 27, 58, 24,  0, 59, 53, 15, 26, 12, 20, 57, 25, 19, 41,
       54, 23, 50, 21,  1,  9, 62, 32, 65, 33, 42, 37, 64,  4, 60, 56, 43,
       46, 28, 36, 38, 63, 10, 61, 17, 40, 55, 31, 16, 45,  3, 34, 18,  7,
       47, 48, 39, 13,  2, 49, 22,  5, 29, 35, 14, 11,  6,  8, 30]), 'cur_cost': 104343.0}, {'tour': [43, 22, 56, 3, 58, 12, 40, 15, 2, 28, 50, 61, 1, 4, 54, 32, 5, 30, 38, 42, 20, 63, 8, 64, 26, 44, 35, 10, 57, 18, 47, 51, 53, 25, 60, 36, 7, 29, 45, 14, 48, 33, 16, 37, 55, 6, 27, 34, 49, 39, 9, 65, 17, 46, 21, 31, 19, 23, 62, 13, 24, 11, 41, 52, 0, 59], 'cur_cost': 109997.0}, {'tour': array([44, 40,  2, 48, 21, 63, 60, 20, 61, 62, 51, 10,  4, 26, 17, 55, 34,
       14, 35, 32, 24, 13, 42, 53, 52, 65, 38, 18, 39, 57, 59, 31, 47, 45,
       56, 58, 37, 49,  1, 25, 11, 28, 22, 16, 41, 64, 19, 50,  5, 33,  7,
        0, 29,  8, 30, 27,  9, 46, 12, 54, 36, 43,  3, 23, 15,  6]), 'cur_cost': 105619.0}, {'tour': array([46, 36, 38, 34, 17, 54,  5, 57, 43, 11, 12, 29, 22,  4, 49, 23,  0,
       16,  3, 58, 52,  8,  2, 19, 37, 64, 55, 14, 31, 28, 61, 50, 10, 15,
       63, 32, 56, 35,  9,  6, 40, 13, 48, 45,  7, 21, 51, 59, 41, 42, 18,
       24, 53, 62, 20, 60, 33, 27, 39, 65,  1, 30, 26, 25, 47, 44]), 'cur_cost': 101569.0}, {'tour': [37, 23, 45, 10, 16, 28, 5, 60, 11, 40, 3, 57, 19, 24, 63, 1, 42, 59, 51, 29, 52, 49, 34, 64, 15, 21, 36, 50, 47, 32, 54, 20, 35, 41, 61, 8, 56, 6, 25, 0, 2, 38, 55, 33, 46, 65, 7, 14, 44, 9, 27, 4, 18, 31, 17, 26, 48, 53, 62, 13, 30, 12, 43, 39, 22, 58], 'cur_cost': 117106.0}, {'tour': array([14, 55, 30,  0,  6, 63, 28, 50, 35, 65, 18, 37, 45,  4, 52, 27,  5,
       16, 34, 64, 53, 48, 19, 11,  2,  3, 44, 54, 10, 23, 58, 42, 22, 13,
       26, 20, 21, 41, 61,  9, 36, 57, 38,  8, 51, 17, 40, 43, 25, 56, 59,
       12, 32, 29, 47, 31, 62,  1,  7, 33, 24, 60, 46, 39, 49, 15]), 'cur_cost': 105810.0}]
2025-06-22 17:43:18,638 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:43:18,639 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 231, 'cache_hit_rate': 0.0, 'cache_size': 231}}
2025-06-22 17:43:18,639 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 17:43:18,639 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-22 17:43:18,639 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:43:18,639 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [46, 36, 38] and ending with [25, 47, 44]
- Cost: 101569.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8830769230769231
- Unexplored regions: 1 opportunity regions
- Opportunity regions: 1 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:43:18,640 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:43:22,188 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    46, 36, 38, 37, 39, 40, 41, 42, 43, 44, 45, 47, 25, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 55, 54, 53, 52, 51, 50, 49, 48, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56
  ],
  "modification_strategy": "This strategy focuses on high diversity by introducing a completely different structure.  It starts by following the beginning of the provided path, then uses an interleaving technique between several regions to create a very different order for the majority of nodes. This mixes regions based on the problem's opportunity and difficult regions, maximizing exploration of less-visited areas.",
  "targeted_regions": "The main focus is on exploring areas connected to the opportunity and difficult regions, while ensuring a high degree of permutation within the existing path structure. The initial segment follows the starting points, followed by a region shifting strategy to visit other areas."
}
```
2025-06-22 17:43:22,188 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:43:22,188 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27070.0, 路径: [46, 36, 38, 37, 39, 40, 41, 42, 43, 44, 45, 47, 25, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 55, 54, 53, 52, 51, 50, 49, 48, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56]
2025-06-22 17:43:22,188 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [46, 36, 38, 37, 39, 40, 41, 42, 43, 44, 45, 47, 25, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 55, 54, 53, 52, 51, 50, 49, 48, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56], 'cur_cost': 27070.0}
2025-06-22 17:43:22,188 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 17:43:22,190 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:43:22,190 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:43:22,190 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 98276.0
2025-06-22 17:43:22,695 - ExploitationExpert - INFO - res_population_num: 4
2025-06-22 17:43:22,695 - ExploitationExpert - INFO - res_population_costs: [9527, 9536, 9557, 9524]
2025-06-22 17:43:22,695 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 17:43:22,697 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:43:22,697 - ExploitationExpert - INFO - populations: [{'tour': [50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11479.0}, {'tour': array([27, 36, 47, 59, 63, 17,  3, 28,  0, 64, 41, 49, 24, 51,  2, 26, 52,
        8, 34, 15, 39,  1, 37, 22, 16, 21, 42, 44, 30,  5, 58,  6, 12, 50,
       57, 54,  9, 20, 65,  7, 62, 60, 32, 11, 61, 25, 29, 23, 55, 33, 48,
       40, 19, 35, 38, 18, 14, 46, 56, 31,  4, 43, 45, 10, 53, 13]), 'cur_cost': 100590.0}, {'tour': array([ 0, 65, 57, 19, 38, 23, 58, 52, 42, 33, 55, 41,  6, 13, 26, 24, 17,
       16, 46, 32, 34, 50, 39, 30, 14, 59, 60, 62, 22, 63,  3, 40, 35, 61,
       49, 11,  7, 36, 10, 25, 64, 12, 43,  9, 20, 45, 27, 21, 48, 47,  1,
       56, 53, 28, 18,  8, 31, 15,  5, 29,  2, 44, 37,  4, 54, 51]), 'cur_cost': 106358.0}, {'tour': array([37, 11, 51, 32, 45,  8,  3, 28, 52, 16,  9, 47, 48, 62, 42, 35,  5,
       38, 57, 65,  1, 36,  7, 21, 56, 61, 39, 63, 13, 49, 44, 31, 18, 53,
       60, 33, 19, 27, 46, 12, 24, 14, 26, 41,  2, 54, 40, 43, 59, 58, 55,
       22,  6, 25, 20, 30, 29, 50,  4, 64, 15, 17, 10,  0, 34, 23]), 'cur_cost': 104791.0}, {'tour': array([52, 44, 51, 27, 58, 24,  0, 59, 53, 15, 26, 12, 20, 57, 25, 19, 41,
       54, 23, 50, 21,  1,  9, 62, 32, 65, 33, 42, 37, 64,  4, 60, 56, 43,
       46, 28, 36, 38, 63, 10, 61, 17, 40, 55, 31, 16, 45,  3, 34, 18,  7,
       47, 48, 39, 13,  2, 49, 22,  5, 29, 35, 14, 11,  6,  8, 30]), 'cur_cost': 104343.0}, {'tour': [43, 22, 56, 3, 58, 12, 40, 15, 2, 28, 50, 61, 1, 4, 54, 32, 5, 30, 38, 42, 20, 63, 8, 64, 26, 44, 35, 10, 57, 18, 47, 51, 53, 25, 60, 36, 7, 29, 45, 14, 48, 33, 16, 37, 55, 6, 27, 34, 49, 39, 9, 65, 17, 46, 21, 31, 19, 23, 62, 13, 24, 11, 41, 52, 0, 59], 'cur_cost': 109997.0}, {'tour': array([44, 40,  2, 48, 21, 63, 60, 20, 61, 62, 51, 10,  4, 26, 17, 55, 34,
       14, 35, 32, 24, 13, 42, 53, 52, 65, 38, 18, 39, 57, 59, 31, 47, 45,
       56, 58, 37, 49,  1, 25, 11, 28, 22, 16, 41, 64, 19, 50,  5, 33,  7,
        0, 29,  8, 30, 27,  9, 46, 12, 54, 36, 43,  3, 23, 15,  6]), 'cur_cost': 105619.0}, {'tour': [46, 36, 38, 37, 39, 40, 41, 42, 43, 44, 45, 47, 25, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 55, 54, 53, 52, 51, 50, 49, 48, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56], 'cur_cost': 27070.0}, {'tour': array([50,  4,  9, 27, 24, 41,  6, 56, 62, 57, 61, 11, 64, 16, 58, 26, 38,
       53,  5,  0, 47, 14, 29, 15, 25, 63, 52, 43, 12, 30, 65, 13, 55, 22,
       17, 54, 31, 42, 44, 45, 49, 36, 46, 48, 34, 19,  3,  8,  1, 21, 28,
       40, 60, 37, 23,  2, 32, 59, 51, 39, 35,  7, 33, 20, 10, 18]), 'cur_cost': 98276.0}, {'tour': array([14, 55, 30,  0,  6, 63, 28, 50, 35, 65, 18, 37, 45,  4, 52, 27,  5,
       16, 34, 64, 53, 48, 19, 11,  2,  3, 44, 54, 10, 23, 58, 42, 22, 13,
       26, 20, 21, 41, 61,  9, 36, 57, 38,  8, 51, 17, 40, 43, 25, 56, 59,
       12, 32, 29, 47, 31, 62,  1,  7, 33, 24, 60, 46, 39, 49, 15]), 'cur_cost': 105810.0}]
2025-06-22 17:43:22,699 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:43:22,699 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 253, 'cache_hit_rate': 0.0, 'cache_size': 253}}
2025-06-22 17:43:22,700 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 17:43:22,700 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-22 17:43:22,700 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:43:22,700 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [14, 55, 30] and ending with [39, 49, 15]
- Cost: 105810.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8246153846153846
- Unexplored regions: 1 opportunity regions
- Opportunity regions: 1 regions
- Difficult regions to avoid: 1 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:43:22,700 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:43:29,560 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    14, 22, 37, 11, 60, 39, 49, 15, 45, 50, 62, 29, 54, 58, 35, 63, 41, 1, 43, 13, 4, 33, 5, 56, 28, 9, 64, 3, 2, 32, 51, 38, 16, 26, 18, 57, 0, 34, 48, 61, 17, 36, 47, 20, 59, 24, 53, 30, 55, 19, 12, 65, 44, 40, 21, 23, 46, 8, 7, 6, 25, 31, 27, 52, 10, 42
  ],
  "modification_strategy": "Heuristic re-routing focused on diversity.  The path prioritizes visiting nodes away from the previous path, incorporating some nodes from unexplored regions and minimizing overlap with the starting path. Random swaps are employed for local optimization. The strategy uses a balance of exploration and exploitation, aiming to reduce cost while maximizing the diversity of the solution.",
  "targeted_regions": "Unexplored regions and areas furthest from the original path, aiming to cover different areas and introduce diversity in the solution to improve exploration."
}
```
2025-06-22 17:43:29,561 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:43:29,561 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107856.0, 路径: [14, 22, 37, 11, 60, 39, 49, 15, 45, 50, 62, 29, 54, 58, 35, 63, 41, 1, 43, 13, 4, 33, 5, 56, 28, 9, 64, 3, 2, 32, 51, 38, 16, 26, 18, 57, 0, 34, 48, 61, 17, 36, 47, 20, 59, 24, 53, 30, 55, 19, 12, 65, 44, 40, 21, 23, 46, 8, 7, 6, 25, 31, 27, 52, 10, 42]
2025-06-22 17:43:29,561 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [14, 22, 37, 11, 60, 39, 49, 15, 45, 50, 62, 29, 54, 58, 35, 63, 41, 1, 43, 13, 4, 33, 5, 56, 28, 9, 64, 3, 2, 32, 51, 38, 16, 26, 18, 57, 0, 34, 48, 61, 17, 36, 47, 20, 59, 24, 53, 30, 55, 19, 12, 65, 44, 40, 21, 23, 46, 8, 7, 6, 25, 31, 27, 52, 10, 42], 'cur_cost': 107856.0}
2025-06-22 17:43:29,561 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [43, 22, 56, 3, 58, 12, 40, 15, 2, 28, 50, 61, 1, 4, 54, 32, 5, 30, 38, 42, 20, 63, 8, 64, 26, 44, 35, 10, 57, 18, 47, 51, 53, 25, 60, 36, 7, 29, 45, 14, 48, 33, 16, 37, 55, 6, 27, 34, 49, 39, 9, 65, 17, 46, 21, 31, 19, 23, 62, 13, 24, 11, 41, 52, 0, 59], 'cur_cost': 109997.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [46, 36, 38, 37, 39, 40, 41, 42, 43, 44, 45, 47, 25, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 55, 54, 53, 52, 51, 50, 49, 48, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56], 'cur_cost': 27070.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [14, 22, 37, 11, 60, 39, 49, 15, 45, 50, 62, 29, 54, 58, 35, 63, 41, 1, 43, 13, 4, 33, 5, 56, 28, 9, 64, 3, 2, 32, 51, 38, 16, 26, 18, 57, 0, 34, 48, 61, 17, 36, 47, 20, 59, 24, 53, 30, 55, 19, 12, 65, 44, 40, 21, 23, 46, 8, 7, 6, 25, 31, 27, 52, 10, 42], 'cur_cost': 107856.0}}]
2025-06-22 17:43:29,562 - __main__ - INFO - 进化阶段完成
2025-06-22 17:43:29,562 - __main__ - INFO - 开始评估阶段
2025-06-22 17:43:29,562 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 17:43:29,563 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 17:43:29,563 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 4/5
- Population changes:
  * Cost: min 11408.0->11479.0, mean 100600.2->87637.9
  * Diversity: 0.8661538461538462->0.816923076923077
  * Elite solutions: 1->1

## Strategy Performance:
- Explore: 4 individuals, success rate 25.0%, avg improvement 17750.5
- Exploit: 6 individuals, success rate 100.0%, avg improvement 9770.17
- Overall improvement: -71.0

## Historical Trends:
Last 3 iterations: costs [11408.0, 11408.0, 11408.0], diversity [0.9515151515151514, 0.9552188552188552, 0.967003367003367]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 17:43:35,065 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "Stagnation/Late Stage. The evolution appears to be nearing convergence. The cost of the best solution is relatively stable, as seen over the last three iterations.  Diversity is slowly decreasing, and the overall improvement is negative.",
  "strategy_effectiveness": {
    "explore": "Limited effectiveness.  The success rate is low (25%), suggesting that the exploration strategy isn't finding many significantly better solutions. The average improvement is substantial when it *does* succeed, indicating the potential for significant breakthroughs if the exploration process can be more focused or directed.",
    "exploit": "Highly effective. The exploit strategy has a 100% success rate, and while the average improvement is smaller than that of successful exploration attempts, it consistently refines existing good solutions.  This suggests the exploitation is effectively refining the existing solution space, as would be expected in late-stage evolution."
  },
  "balance_state": "Exploitation-dominated, and potentially over-optimized.  The high success rate of exploitation and the low success rate of exploration, coupled with negative overall improvement, indicates that the algorithm is primarily refining existing solutions without discovering significant improvements.  The decreasing diversity further reinforces this view.  A shift towards more exploration is needed to avoid premature convergence and potentially find superior solutions.",
  "recommendations": [
    {
      "action": "Increase exploration rate.",
      "details": "Reduce the number of exploitation attempts and allocate more individuals to explore the solution space. This can be achieved by adjusting parameters controlling exploration (e.g., mutation strength, crossover rates) or changing the selection pressure to favor more diverse solutions.",
      "priority": "High"
    },
    {
      "action": "Refine Exploration Strategy",
      "details": "Analyze exploration failures to identify patterns.  If possible, use a more informed exploration strategy, such as targeted mutation based on promising subcomponents or exploration based on promising areas from the last successful explorations, to focus exploration on areas where success is more likely.  Consider also the possibility of using a completely different exploration operator (e.g., different mutation operator, or even a new exploration operator) if the current one is failing too often.",
      "priority": "High"
    },
    {
      "action": "Evaluate Diversity Metrics",
      "details": "Carefully review the chosen diversity metric and ensure it adequately reflects the search space. Monitor diversity throughout the next iteration and examine what is causing the drop in diversity. It may also be necessary to change the diversity metric if the current metric is not effectively capturing the solution space or solution diversity.",
      "priority": "Medium"
    },
    {
      "action": "Consider Early Stopping Criteria",
      "details": "With stagnation apparent, implement an early stopping criterion if there is no improvement after a fixed number of iterations, to avoid unnecessary computation.  Consider more closely, how the best solutions are chosen and what can improve this process.",
      "priority": "Medium"
    }
  ]
}
```
2025-06-22 17:43:35,093 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 17:43:35,094 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "Stagnation/Late Stage. The evolution appears to be nearing convergence. The cost of the best solution is relatively stable, as seen over the last three iterations.  Diversity is slowly decreasing, and the overall improvement is negative.",
  "strategy_effectiveness": {
    "explore": "Limited effectiveness.  The success rate is low (25%), suggesting that the exploration strategy isn't finding many significantly better solutions. The average improvement is substantial when it *does* succeed, indicating the potential for significant breakthroughs if the exploration process can be more focused or directed.",
    "exploit": "Highly effective. The exploit strategy has a 100% success rate, and while the average improvement is smaller than that of successful exploration attempts, it consistently refines existing good solutions.  This suggests the exploitation is effectively refining the existing solution space, as would be expected in late-stage evolution."
  },
  "balance_state": "Exploitation-dominated, and potentially over-optimized.  The high success rate of exploitation and the low success rate of exploration, coupled with negative overall improvement, indicates that the algorithm is primarily refining existing solutions without discovering significant improvements.  The decreasing diversity further reinforces this view.  A shift towards more exploration is needed to avoid premature convergence and potentially find superior solutions.",
  "recommendations": [
    {
      "action": "Increase exploration rate.",
      "details": "Reduce the number of exploitation attempts and allocate more individuals to explore the solution space. This can be achieved by adjusting parameters controlling exploration (e.g., mutation strength, crossover rates) or changing the selection pressure to favor more diverse solutions.",
      "priority": "High"
    },
    {
      "action": "Refine Exploration Strategy",
      "details": "Analyze exploration failures to identify patterns.  If possible, use a more informed exploration strategy, such as targeted mutation based on promising subcomponents or exploration based on promising areas from the last successful explorations, to focus exploration on areas where success is more likely.  Consider also the possibility of using a completely different exploration operator (e.g., different mutation operator, or even a new exploration operator) if the current one is failing too often.",
      "priority": "High"
    },
    {
      "action": "Evaluate Diversity Metrics",
      "details": "Carefully review the chosen diversity metric and ensure it adequately reflects the search space. Monitor diversity throughout the next iteration and examine what is causing the drop in diversity. It may also be necessary to change the diversity metric if the current metric is not effectively capturing the solution space or solution diversity.",
      "priority": "Medium"
    },
    {
      "action": "Consider Early Stopping Criteria",
      "details": "With stagnation apparent, implement an early stopping criterion if there is no improvement after a fixed number of iterations, to avoid unnecessary computation.  Consider more closely, how the best solutions are chosen and what can improve this process.",
      "priority": "Medium"
    }
  ]
}
```
2025-06-22 17:43:35,094 - __main__ - INFO - 评估阶段完成
2025-06-22 17:43:35,095 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "Stagnation/Late Stage. The evolution appears to be nearing convergence. The cost of the best solution is relatively stable, as seen over the last three iterations.  Diversity is slowly decreasing, and the overall improvement is negative.",
  "strategy_effectiveness": {
    "explore": "Limited effectiveness.  The success rate is low (25%), suggesting that the exploration strategy isn't finding many significantly better solutions. The average improvement is substantial when it *does* succeed, indicating the potential for significant breakthroughs if the exploration process can be more focused or directed.",
    "exploit": "Highly effective. The exploit strategy has a 100% success rate, and while the average improvement is smaller than that of successful exploration attempts, it consistently refines existing good solutions.  This suggests the exploitation is effectively refining the existing solution space, as would be expected in late-stage evolution."
  },
  "balance_state": "Exploitation-dominated, and potentially over-optimized.  The high success rate of exploitation and the low success rate of exploration, coupled with negative overall improvement, indicates that the algorithm is primarily refining existing solutions without discovering significant improvements.  The decreasing diversity further reinforces this view.  A shift towards more exploration is needed to avoid premature convergence and potentially find superior solutions.",
  "recommendations": [
    {
      "action": "Increase exploration rate.",
      "details": "Reduce the number of exploitation attempts and allocate more individuals to explore the solution space. This can be achieved by adjusting parameters controlling exploration (e.g., mutation strength, crossover rates) or changing the selection pressure to favor more diverse solutions.",
      "priority": "High"
    },
    {
      "action": "Refine Exploration Strategy",
      "details": "Analyze exploration failures to identify patterns.  If possible, use a more informed exploration strategy, such as targeted mutation based on promising subcomponents or exploration based on promising areas from the last successful explorations, to focus exploration on areas where success is more likely.  Consider also the possibility of using a completely different exploration operator (e.g., different mutation operator, or even a new exploration operator) if the current one is failing too often.",
      "priority": "High"
    },
    {
      "action": "Evaluate Diversity Metrics",
      "details": "Carefully review the chosen diversity metric and ensure it adequately reflects the search space. Monitor diversity throughout the next iteration and examine what is causing the drop in diversity. It may also be necessary to change the diversity metric if the current metric is not effectively capturing the solution space or solution diversity.",
      "priority": "Medium"
    },
    {
      "action": "Consider Early Stopping Criteria",
      "details": "With stagnation apparent, implement an early stopping criterion if there is no improvement after a fixed number of iterations, to avoid unnecessary computation.  Consider more closely, how the best solutions are chosen and what can improve this process.",
      "priority": "Medium"
    }
  ]
}
```
2025-06-22 17:43:35,095 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-22 17:43:35,096 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_4.pkl
2025-06-22 17:43:35,100 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_solution.json
2025-06-22 17:43:35,100 - __main__ - INFO - 实例 composite13_66 处理完成
