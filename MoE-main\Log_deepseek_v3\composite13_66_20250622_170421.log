2025-06-22 17:04:21,884 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-22 17:04:21,885 - __main__ - INFO - 开始分析阶段
2025-06-22 17:04:21,885 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:04:21,904 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10007.0, 'max': 116636.0, 'mean': 76690.1, 'std': 44004.83140167679}, 'diversity': 0.9316498316498316, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:04:21,904 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10007.0, 'max': 116636.0, 'mean': 76690.1, 'std': 44004.83140167679}, 'diversity_level': 0.9316498316498316, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:04:21,905 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:04:21,905 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:04:21,906 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:04:21,911 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:04:21,911 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (64, 57, 54), 'frequency': 0.3}, {'subpath': (65, 52, 63), 'frequency': 0.3}, {'subpath': (22, 12, 17), 'frequency': 0.3}, {'subpath': (18, 16, 23), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (39, 44, 45), 'frequency': 0.3}, {'subpath': (44, 45, 38), 'frequency': 0.3}, {'subpath': (45, 38, 51), 'frequency': 0.3}, {'subpath': (38, 51, 50), 'frequency': 0.3}, {'subpath': (51, 50, 41), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(9, 11)', 'frequency': 0.4}, {'edge': '(55, 61)', 'frequency': 0.4}, {'edge': '(32, 40)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.2}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.2}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.2}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(14, 15)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.3}, {'edge': '(17, 18)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(13, 20)', 'frequency': 0.2}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(19, 21)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.2}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.2}, {'edge': '(28, 35)', 'frequency': 0.2}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.2}, {'edge': '(33, 34)', 'frequency': 0.2}, {'edge': '(31, 33)', 'frequency': 0.2}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.2}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(46, 48)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(39, 43)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.3}, {'edge': '(41, 42)', 'frequency': 0.3}, {'edge': '(36, 37)', 'frequency': 0.2}, {'edge': '(27, 33)', 'frequency': 0.2}, {'edge': '(15, 17)', 'frequency': 0.2}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(1, 3)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.2}, {'edge': '(8, 10)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(14, 27)', 'frequency': 0.2}, {'edge': '(0, 38)', 'frequency': 0.2}, {'edge': '(8, 28)', 'frequency': 0.2}, {'edge': '(28, 41)', 'frequency': 0.2}, {'edge': '(13, 56)', 'frequency': 0.2}, {'edge': '(14, 33)', 'frequency': 0.2}, {'edge': '(12, 45)', 'frequency': 0.2}, {'edge': '(18, 64)', 'frequency': 0.2}, {'edge': '(47, 59)', 'frequency': 0.2}, {'edge': '(10, 46)', 'frequency': 0.2}, {'edge': '(50, 65)', 'frequency': 0.2}, {'edge': '(5, 17)', 'frequency': 0.3}, {'edge': '(30, 39)', 'frequency': 0.2}, {'edge': '(2, 39)', 'frequency': 0.3}, {'edge': '(23, 59)', 'frequency': 0.2}, {'edge': '(42, 65)', 'frequency': 0.3}, {'edge': '(12, 54)', 'frequency': 0.2}, {'edge': '(3, 61)', 'frequency': 0.2}, {'edge': '(32, 51)', 'frequency': 0.2}, {'edge': '(9, 18)', 'frequency': 0.2}, {'edge': '(38, 49)', 'frequency': 0.2}, {'edge': '(27, 31)', 'frequency': 0.2}, {'edge': '(8, 58)', 'frequency': 0.2}, {'edge': '(48, 52)', 'frequency': 0.2}, {'edge': '(30, 31)', 'frequency': 0.2}, {'edge': '(1, 24)', 'frequency': 0.2}, {'edge': '(54, 59)', 'frequency': 0.2}, {'edge': '(0, 49)', 'frequency': 0.2}, {'edge': '(28, 63)', 'frequency': 0.2}, {'edge': '(13, 52)', 'frequency': 0.2}, {'edge': '(14, 26)', 'frequency': 0.2}, {'edge': '(38, 44)', 'frequency': 0.2}, {'edge': '(3, 37)', 'frequency': 0.2}, {'edge': '(39, 54)', 'frequency': 0.2}, {'edge': '(19, 33)', 'frequency': 0.2}, {'edge': '(43, 51)', 'frequency': 0.2}, {'edge': '(4, 31)', 'frequency': 0.2}, {'edge': '(15, 23)', 'frequency': 0.2}, {'edge': '(30, 43)', 'frequency': 0.2}, {'edge': '(20, 56)', 'frequency': 0.2}, {'edge': '(27, 49)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [50, 65, 27, 58, 48, 57, 42], 'cost': 17130.0, 'size': 7}, {'region': [0, 38, 34, 51, 8, 28, 41, 56], 'cost': 16349.0, 'size': 8}, {'region': [52, 32, 64, 28, 41, 33], 'cost': 13330.0, 'size': 6}, {'region': [35, 62, 40, 32, 10, 46], 'cost': 12459.0, 'size': 6}, {'region': [42, 65, 50, 25, 47, 24], 'cost': 12139.0, 'size': 6}]}
2025-06-22 17:04:21,912 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:04:21,913 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:04:21,913 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:04:21,913 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-22 17:04:21,913 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-22 17:04:21,913 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:04:21,913 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:04:21,913 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:04:21,914 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=10007.0, max=116636.0, mean=76690.1, std=44004.83140167679
- Diversity: 0.9316498316498316, Convergence: 0.0
- Structure: 0 high quality edges, 10 common subpaths
- Elite: 0 solutions with 0 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 5 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:04:29,753 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High. The significant cost range (min=10007.0, max=116636.0, std=44004.83) suggests a wide spread of solutions, implying a rugged landscape with potentially many local optima. The lack of convergence further supports this.",
    "modality": "Potentially Multi-modal.  The identification of 5 difficult regions suggests multiple areas where the search is getting stuck. The presence of common subpaths (10) and the lack of shared edges in elite solutions implies different solutions may use common building blocks, further suggesting a multi-modal landscape."
  },
  "population_state": {
    "diversity": "High (0.93). The population maintains a high degree of diversity, meaning the solutions are not highly similar.",
    "convergence": "Absent (0.0). The search has not yet converged. This, combined with high diversity, indicates the search has explored a wide range of the space but not found a clearly superior region."
  },
  "difficult_regions": {
    "description": "5 identified difficult regions suggest the presence of local optima or highly constrained areas. The search is likely getting trapped in these regions.",
    "challenges": [
      "Navigating local optima: Solutions might be converging towards suboptimal solutions within these regions.",
      "Overcoming barriers: The search needs to explore beyond these difficult regions to discover better solutions.",
      "Avoiding stagnation: The algorithm may be stuck within the difficult regions if it lacks the mechanisms to escape."
    ]
  },
  "opportunity_regions": {
    "description": "None identified explicitly. This suggests the exploration hasn't found areas of particular promise.  The high diversity and lack of convergence, however, indicate this might be due to the search still broadly exploring the space, rather than evidence against opportunity regions.",
    "potential": [
      "Exploration of new areas: Further exploration is required to discover high-quality areas.",
      "Targeting common subpaths:  Since there are common subpaths, identifying beneficial combinations of these subpaths might lead to good solutions."
    ]
  },
  "evolution_direction": {
    "strategy": "Focused Exploration & Diversification with occasional exploitation",
    "recommendations": [
      "Maintain current exploration strategy, but potentially increase exploration pressure. Explore a wider part of the search space.",
      "Increase the chance of escaping the difficult regions through larger mutations, or potentially using a mechanism for jumping away from local optima.",
      "Monitor common subpaths and their performance. Try to incorporate common and promising elements into improved solutions, by using them as a building block.",
      "If applicable to the search mechanism, introduce a mechanism for evaluating and exploiting promising regions as identified through exploration.",
      "Balance: Since the population is diverse but not converged, focus on balancing exploration and exploitation. More exploration initially to find potential promising areas, then slightly favoring exploitation after."
    ]
  }
}
```
2025-06-22 17:04:29,753 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:04:29,753 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High. The significant cost range (min=10007.0, max=116636.0, std=44004.83) suggests a wide spread of solutions, implying a rugged landscape with potentially many local optima. The lack of convergence further supports this.', 'modality': 'Potentially Multi-modal.  The identification of 5 difficult regions suggests multiple areas where the search is getting stuck. The presence of common subpaths (10) and the lack of shared edges in elite solutions implies different solutions may use common building blocks, further suggesting a multi-modal landscape.'}, 'population_state': {'diversity': 'High (0.93). The population maintains a high degree of diversity, meaning the solutions are not highly similar.', 'convergence': 'Absent (0.0). The search has not yet converged. This, combined with high diversity, indicates the search has explored a wide range of the space but not found a clearly superior region.'}, 'difficult_regions': {'description': '5 identified difficult regions suggest the presence of local optima or highly constrained areas. The search is likely getting trapped in these regions.', 'challenges': ['Navigating local optima: Solutions might be converging towards suboptimal solutions within these regions.', 'Overcoming barriers: The search needs to explore beyond these difficult regions to discover better solutions.', 'Avoiding stagnation: The algorithm may be stuck within the difficult regions if it lacks the mechanisms to escape.']}, 'opportunity_regions': {'description': "None identified explicitly. This suggests the exploration hasn't found areas of particular promise.  The high diversity and lack of convergence, however, indicate this might be due to the search still broadly exploring the space, rather than evidence against opportunity regions.", 'potential': ['Exploration of new areas: Further exploration is required to discover high-quality areas.', 'Targeting common subpaths:  Since there are common subpaths, identifying beneficial combinations of these subpaths might lead to good solutions.']}, 'evolution_direction': {'strategy': 'Focused Exploration & Diversification with occasional exploitation', 'recommendations': ['Maintain current exploration strategy, but potentially increase exploration pressure. Explore a wider part of the search space.', 'Increase the chance of escaping the difficult regions through larger mutations, or potentially using a mechanism for jumping away from local optima.', 'Monitor common subpaths and their performance. Try to incorporate common and promising elements into improved solutions, by using them as a building block.', 'If applicable to the search mechanism, introduce a mechanism for evaluating and exploiting promising regions as identified through exploration.', 'Balance: Since the population is diverse but not converged, focus on balancing exploration and exploitation. More exploration initially to find potential promising areas, then slightly favoring exploitation after.']}}
2025-06-22 17:04:29,753 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:04:29,753 - __main__ - INFO - 分析阶段完成
2025-06-22 17:04:29,753 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High. The significant cost range (min=10007.0, max=116636.0, std=44004.83) suggests a wide spread of solutions, implying a rugged landscape with potentially many local optima. The lack of convergence further supports this.', 'modality': 'Potentially Multi-modal.  The identification of 5 difficult regions suggests multiple areas where the search is getting stuck. The presence of common subpaths (10) and the lack of shared edges in elite solutions implies different solutions may use common building blocks, further suggesting a multi-modal landscape.'}, 'population_state': {'diversity': 'High (0.93). The population maintains a high degree of diversity, meaning the solutions are not highly similar.', 'convergence': 'Absent (0.0). The search has not yet converged. This, combined with high diversity, indicates the search has explored a wide range of the space but not found a clearly superior region.'}, 'difficult_regions': {'description': '5 identified difficult regions suggest the presence of local optima or highly constrained areas. The search is likely getting trapped in these regions.', 'challenges': ['Navigating local optima: Solutions might be converging towards suboptimal solutions within these regions.', 'Overcoming barriers: The search needs to explore beyond these difficult regions to discover better solutions.', 'Avoiding stagnation: The algorithm may be stuck within the difficult regions if it lacks the mechanisms to escape.']}, 'opportunity_regions': {'description': "None identified explicitly. This suggests the exploration hasn't found areas of particular promise.  The high diversity and lack of convergence, however, indicate this might be due to the search still broadly exploring the space, rather than evidence against opportunity regions.", 'potential': ['Exploration of new areas: Further exploration is required to discover high-quality areas.', 'Targeting common subpaths:  Since there are common subpaths, identifying beneficial combinations of these subpaths might lead to good solutions.']}, 'evolution_direction': {'strategy': 'Focused Exploration & Diversification with occasional exploitation', 'recommendations': ['Maintain current exploration strategy, but potentially increase exploration pressure. Explore a wider part of the search space.', 'Increase the chance of escaping the difficult regions through larger mutations, or potentially using a mechanism for jumping away from local optima.', 'Monitor common subpaths and their performance. Try to incorporate common and promising elements into improved solutions, by using them as a building block.', 'If applicable to the search mechanism, introduce a mechanism for evaluating and exploiting promising regions as identified through exploration.', 'Balance: Since the population is diverse but not converged, focus on balancing exploration and exploitation. More exploration initially to find potential promising areas, then slightly favoring exploitation after.']}}
2025-06-22 17:04:29,753 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:04:29,753 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:04:29,753 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 0
- Population: 10 individuals, diversity=High (0.93). The population maintains a high degree of diversity, meaning the solutions are not highly similar.
- Best cost: 10007.0, Mean cost: 76690.1
- Search space: Ruggedness: High. The significant cost range (min=10007.0, max=116636.0, std=44004.83) suggests a wide spread of solutions, implying a rugged landscape with potentially many local optima. The lack of convergence further supports this., Modality: Potentially Multi-modal.  The identification of 5 difficult regions suggests multiple areas where the search is getting stuck. The presence of common subpaths (10) and the lack of shared edges in elite solutions implies different solutions may use common building blocks, further suggesting a multi-modal landscape.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: No previous feedback

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:04:29,753 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:04:34,252 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "step_size": 0.05,
      "exploration_method": "random_walk"
    },
    "exploit": {
      "crossover_rate": 0.7,
      "selection_pressure": 2.0,
      "exploitation_method": "local_search"
    }
  }
}
```

2025-06-22 17:04:34,252 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore']
2025-06-22 17:04:34,252 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "step_size": 0.05,
      "exploration_method": "random_walk"
    },
    "exploit": {
      "crossover_rate": 0.7,
      "selection_pressure": 2.0,
      "exploitation_method": "local_search"
    }
  }
}
```

2025-06-22 17:04:34,252 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:04:34,252 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore']
2025-06-22 17:04:34,252 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "step_size": 0.05,
      "exploration_method": "random_walk"
    },
    "exploit": {
      "crossover_rate": 0.7,
      "selection_pressure": 2.0,
      "exploitation_method": "local_search"
    }
  }
}
```

2025-06-22 17:04:34,252 - __main__ - INFO - 开始进化阶段
2025-06-22 17:04:34,252 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:04:34,252 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:04:34,252 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [1, 7, 3] and ending with [50, 41, 42]
- Cost: 10333.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7661538461538462
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:04:34,252 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:04:40,554 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    1, 8, 15, 22, 29, 36, 43, 50, 49, 48, 47, 46, 45, 44, 37, 30, 23, 16, 9, 2, 3, 4, 5, 6, 7, 14, 21, 28, 35, 42, 41, 40, 39, 38, 31, 24, 17, 10, 11, 12, 13, 20, 27, 34, 33, 32, 25, 18, 19, 26, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66
  ],
  "modification_strategy": "This path generation strategy prioritized diversity by: 1) reversing a significant portion of the original path's sequence (nodes 50 to 41). 2) Incorporating more exploration of the opportunity regions, starting from node 8 and extending to nodes 16, 23, etc. 3) Adding new nodes (51-66) in sequence after the original ending to explore unexplored regions and increase path length. 4) Focusing on path segments that were less visited and incorporating them early in the new path.",
  "targeted_regions": [
    "Opportunity Regions (nodes 8, 15, 22, 29...)",
    "Regions beyond the original path's ending (nodes 51-66)",
    "Under-explored segments of the original path (reverse of nodes near the end)"
  ]
}
```

2025-06-22 17:04:40,554 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:04:40,554 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30347.0, 路径: [1, 8, 15, 22, 29, 36, 43, 50, 49, 48, 47, 46, 45, 44, 37, 30, 23, 16, 9, 2, 3, 4, 5, 6, 7, 14, 21, 28, 35, 42, 41, 40, 39, 38, 31, 24, 17, 10, 11, 12, 13, 20, 27, 34, 33, 32, 25, 18, 19, 26, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66]
2025-06-22 17:04:40,554 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 8, 15, 22, 29, 36, 43, 50, 49, 48, 47, 46, 45, 44, 37, 30, 23, 16, 9, 2, 3, 4, 5, 6, 7, 14, 21, 28, 35, 42, 41, 40, 39, 38, 31, 24, 17, 10, 11, 12, 13, 20, 27, 34, 33, 32, 25, 18, 19, 26, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66], 'cur_cost': 30347.0}
2025-06-22 17:04:40,554 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 17:04:40,554 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:04:40,554 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [24, 31, 25] and ending with [43, 48, 42]
- Cost: 10007.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.82
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:04:40,554 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:04:45,603 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    24, 31, 35, 38, 40, 41, 47, 52, 55, 58, 61, 63, 65, 59, 53, 49, 44, 39, 36, 32, 26, 20, 15, 10, 6, 2, 1, 4, 8, 12, 18, 22, 28, 34, 37, 43, 48, 42
  ],
  "modification_strategy": "This path prioritizes diversity by deviating early from the original path and exploring unexplored regions with the highest opportunity score, while minimizing visits to the difficult regions. The path zig-zags, maximizing coverage within the available nodes, focusing on early exploration of areas not visited in the initial path and then aiming for key targets.",
  "targeted_regions": [
    "Unexplored regions 1 and 2",
    "Areas with high opportunity scores (relative to the initial path)",
    "Avoidance of difficult regions as much as possible."
  ]
}
```
2025-06-22 17:04:45,603 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:04:45,603 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20448.0, 路径: [24, 31, 35, 38, 40, 41, 47, 52, 55, 58, 61, 63, 65, 59, 53, 49, 44, 39, 36, 32, 26, 20, 15, 10, 6, 2, 1, 4, 8, 12, 18, 22, 28, 34, 37, 43, 48, 42]
2025-06-22 17:04:45,605 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [24, 31, 35, 38, 40, 41, 47, 52, 55, 58, 61, 63, 65, 59, 53, 49, 44, 39, 36, 32, 26, 20, 15, 10, 6, 2, 1, 4, 8, 12, 18, 22, 28, 34, 37, 43, 48, 42], 'cur_cost': 20448.0}
2025-06-22 17:04:45,605 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 17:04:45,605 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:04:45,605 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [59, 62, 53] and ending with [50, 41, 42]
- Cost: 10029.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8665594855305466
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:04:45,605 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:04:51,437 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [59, 62, 47, 38, 29, 20, 11, 2, 1, 8, 17, 26, 35, 44, 53, 54, 55, 56, 57, 58, 60, 61, 52, 43, 34, 25, 16, 7, 14, 23, 32, 41, 40, 31, 22, 13, 4, 5, 6, 15, 24, 33, 42, 51, 50, 49, 48, 39, 30, 21, 12, 3, 9, 18, 27, 36, 45, 54, 55, 56, 57, 58, 59, 63, 64, 65, 66, 41, 42],
  "modification_strategy": "Prioritized exploration of opportunity regions with increased emphasis on diversity.  The strategy leverages the diversity weight to avoid highly traversed regions while incorporating nodes in potentially rich areas. The path tries to deviate significantly from the original path early, exploring different branches before converging towards the end. Nodes in difficult regions are still avoided, or traversed carefully to balance risk and exploration.",
  "targeted_regions": "Opportunity Regions: All Opportunity Regions were targeted, and this path makes an effort to go through them."
}
```
2025-06-22 17:04:51,437 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:04:51,437 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52618.0, 路径: [59, 62, 47, 38, 29, 20, 11, 2, 1, 8, 17, 26, 35, 44, 53, 54, 55, 56, 57, 58, 60, 61, 52, 43, 34, 25, 16, 7, 14, 23, 32, 41, 40, 31, 22, 13, 4, 5, 6, 15, 24, 33, 42, 51, 50, 49, 48, 39, 30, 21, 12, 3, 9, 18, 27, 36, 45, 54, 55, 56, 57, 58, 59, 63, 64, 65, 66, 41, 42]
2025-06-22 17:04:51,437 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [59, 62, 47, 38, 29, 20, 11, 2, 1, 8, 17, 26, 35, 44, 53, 54, 55, 56, 57, 58, 60, 61, 52, 43, 34, 25, 16, 7, 14, 23, 32, 41, 40, 31, 22, 13, 4, 5, 6, 15, 24, 33, 42, 51, 50, 49, 48, 39, 30, 21, 12, 3, 9, 18, 27, 36, 45, 54, 55, 56, 57, 58, 59, 63, 64, 65, 66, 41, 42], 'cur_cost': 52618.0}
2025-06-22 17:04:51,437 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-22 17:04:51,437 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:04:51,437 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:04:51,442 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 113743.0
2025-06-22 17:04:53,022 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 17:04:53,022 - ExploitationExpert - INFO - res_population_costs: [9539]
2025-06-22 17:04:53,022 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 17:04:53,022 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:04:53,022 - ExploitationExpert - INFO - populations: [{'tour': [1, 8, 15, 22, 29, 36, 43, 50, 49, 48, 47, 46, 45, 44, 37, 30, 23, 16, 9, 2, 3, 4, 5, 6, 7, 14, 21, 28, 35, 42, 41, 40, 39, 38, 31, 24, 17, 10, 11, 12, 13, 20, 27, 34, 33, 32, 25, 18, 19, 26, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66], 'cur_cost': 30347.0}, {'tour': [24, 31, 35, 38, 40, 41, 47, 52, 55, 58, 61, 63, 65, 59, 53, 49, 44, 39, 36, 32, 26, 20, 15, 10, 6, 2, 1, 4, 8, 12, 18, 22, 28, 34, 37, 43, 48, 42], 'cur_cost': 20448.0}, {'tour': [59, 62, 47, 38, 29, 20, 11, 2, 1, 8, 17, 26, 35, 44, 53, 54, 55, 56, 57, 58, 60, 61, 52, 43, 34, 25, 16, 7, 14, 23, 32, 41, 40, 31, 22, 13, 4, 5, 6, 15, 24, 33, 42, 51, 50, 49, 48, 39, 30, 21, 12, 3, 9, 18, 27, 36, 45, 54, 55, 56, 57, 58, 59, 63, 64, 65, 66, 41, 42], 'cur_cost': 52618.0}, {'tour': array([ 2, 30, 15, 33, 51, 60, 32, 64, 59, 53, 35, 43, 40, 56, 27, 19,  6,
       44, 36, 37, 25, 23, 47, 12, 38,  5, 45, 20,  9, 39, 52, 21, 18, 63,
        0, 13, 42, 34,  7, 16, 41, 11,  8, 50, 57, 61, 31, 22, 26, 62, 29,
       49, 14, 28, 58,  4, 54, 48,  3, 55,  1, 46, 17, 10, 65, 24]), 'cur_cost': 113743.0}, {'tour': [50, 13, 24, 26, 34, 41, 14, 53, 7, 46, 59, 23, 19, 64, 60, 2, 37, 44, 29, 22, 30, 39, 11, 36, 65, 42, 62, 15, 0, 43, 5, 54, 12, 16, 35, 6, 55, 61, 3, 4, 32, 51, 63, 1, 18, 9, 56, 57, 38, 49, 33, 27, 31, 40, 58, 8, 28, 21, 10, 45, 25, 20, 17, 47, 48, 52], 'cur_cost': 100727.0}, {'tour': [42, 32, 62, 7, 30, 31, 48, 50, 16, 22, 20, 8, 10, 24, 1, 47, 23, 6, 59, 54, 12, 64, 2, 19, 49, 0, 28, 63, 15, 55, 58, 13, 52, 39, 51, 41, 11, 9, 18, 61, 21, 57, 26, 14, 44, 38, 27, 45, 60, 43, 35, 29, 46, 25, 65, 37, 3, 34, 56, 5, 33, 36, 40, 4, 53, 17], 'cur_cost': 105378.0}, {'tour': [36, 20, 60, 56, 14, 26, 28, 22, 64, 18, 65, 1, 54, 39, 50, 11, 34, 13, 52, 0, 38, 49, 58, 42, 53, 29, 6, 27, 30, 31, 41, 45, 9, 44, 7, 61, 3, 35, 62, 40, 32, 10, 46, 19, 33, 55, 16, 63, 59, 47, 2, 48, 21, 37, 12, 23, 57, 4, 24, 8, 43, 51, 5, 17, 15, 25], 'cur_cost': 107222.0}, {'tour': [61, 22, 39, 2, 3, 21, 34, 46, 11, 62, 33, 19, 38, 35, 7, 64, 41, 29, 28, 13, 56, 8, 53, 63, 55, 14, 27, 5, 9, 4, 31, 59, 23, 15, 47, 50, 10, 60, 57, 52, 18, 42, 65, 49, 0, 17, 40, 54, 32, 37, 36, 44, 20, 26, 58, 6, 12, 45, 16, 30, 43, 51, 48, 24, 25, 1], 'cur_cost': 93763.0}, {'tour': [43, 47, 12, 14, 33, 64, 56, 20, 58, 8, 25, 11, 48, 52, 31, 4, 46, 42, 41, 17, 18, 38, 9, 16, 49, 27, 2, 15, 23, 40, 50, 45, 21, 29, 65, 7, 22, 44, 13, 60, 39, 57, 0, 35, 63, 28, 37, 3, 5, 62, 51, 32, 36, 6, 1, 24, 34, 53, 26, 59, 54, 55, 19, 61, 10, 30], 'cur_cost': 99891.0}, {'tour': [48, 63, 43, 16, 52, 32, 64, 28, 41, 33, 30, 19, 5, 17, 23, 53, 44, 38, 42, 65, 50, 25, 47, 24, 11, 4, 34, 37, 58, 21, 1, 22, 35, 56, 20, 55, 29, 45, 59, 8, 10, 57, 13, 2, 39, 54, 7, 40, 60, 31, 27, 49, 61, 9, 46, 14, 15, 51, 18, 0, 6, 26, 12, 3, 36, 62], 'cur_cost': 116636.0}]
2025-06-22 17:04:53,022 - ExploitationExpert - INFO - 局部搜索耗时: 1.59秒
2025-06-22 17:04:53,022 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-22 17:04:53,038 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-22 17:04:53,038 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 17:04:53,038 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:04:53,038 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [50, 13, 24] and ending with [47, 48, 52]
- Cost: 100727.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8576
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:04:53,038 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:04:58,109 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [50, 3, 17, 34, 60, 41, 27, 15, 8, 2, 11, 25, 49, 58, 36, 63, 53, 31, 4, 22, 12, 20, 40, 59, 65, 42, 26, 18, 1, 14, 23, 39, 56, 61, 37, 9, 5, 32, 21, 38, 64, 45, 55, 62, 44, 10, 29, 54, 33, 19, 35, 43, 51, 28, 6, 48, 7, 57, 46, 30, 16, 47, 52],
  "modification_strategy": "This strategy prioritizes diversity by drastically altering the sequence of nodes compared to the existing path. The aim is to 'scatter' exploration across the entire network, hitting different regions and attempting to uncover unexplored or underexplored areas indicated by 'opportunity regions'.  The starting and ending nodes are maintained to honor the path constraints.  Intermediate nodes were chosen with a combination of random selection and slight consideration of proximity to unexplored areas (to minimize the impact of risk tolerance and ensure reasonable path quality).",
  "targeted_regions": ["Opportunity Region 1", "Opportunity Region 2"]
}
```
2025-06-22 17:04:58,109 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:04:58,109 - ExplorationExpert - INFO - 探索路径生成完成，成本: 110678.0, 路径: [50, 3, 17, 34, 60, 41, 27, 15, 8, 2, 11, 25, 49, 58, 36, 63, 53, 31, 4, 22, 12, 20, 40, 59, 65, 42, 26, 18, 1, 14, 23, 39, 56, 61, 37, 9, 5, 32, 21, 38, 64, 45, 55, 62, 44, 10, 29, 54, 33, 19, 35, 43, 51, 28, 6, 48, 7, 57, 46, 30, 16, 47, 52]
2025-06-22 17:04:58,109 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [50, 3, 17, 34, 60, 41, 27, 15, 8, 2, 11, 25, 49, 58, 36, 63, 53, 31, 4, 22, 12, 20, 40, 59, 65, 42, 26, 18, 1, 14, 23, 39, 56, 61, 37, 9, 5, 32, 21, 38, 64, 45, 55, 62, 44, 10, 29, 54, 33, 19, 35, 43, 51, 28, 6, 48, 7, 57, 46, 30, 16, 47, 52], 'cur_cost': 110678.0}
2025-06-22 17:04:58,109 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 17:04:58,109 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:04:58,109 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:04:58,109 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 112808.0
2025-06-22 17:04:59,498 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 17:04:59,498 - ExploitationExpert - INFO - res_population_costs: [9539]
2025-06-22 17:04:59,498 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 17:04:59,498 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:04:59,498 - ExploitationExpert - INFO - populations: [{'tour': [1, 8, 15, 22, 29, 36, 43, 50, 49, 48, 47, 46, 45, 44, 37, 30, 23, 16, 9, 2, 3, 4, 5, 6, 7, 14, 21, 28, 35, 42, 41, 40, 39, 38, 31, 24, 17, 10, 11, 12, 13, 20, 27, 34, 33, 32, 25, 18, 19, 26, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66], 'cur_cost': 30347.0}, {'tour': [24, 31, 35, 38, 40, 41, 47, 52, 55, 58, 61, 63, 65, 59, 53, 49, 44, 39, 36, 32, 26, 20, 15, 10, 6, 2, 1, 4, 8, 12, 18, 22, 28, 34, 37, 43, 48, 42], 'cur_cost': 20448.0}, {'tour': [59, 62, 47, 38, 29, 20, 11, 2, 1, 8, 17, 26, 35, 44, 53, 54, 55, 56, 57, 58, 60, 61, 52, 43, 34, 25, 16, 7, 14, 23, 32, 41, 40, 31, 22, 13, 4, 5, 6, 15, 24, 33, 42, 51, 50, 49, 48, 39, 30, 21, 12, 3, 9, 18, 27, 36, 45, 54, 55, 56, 57, 58, 59, 63, 64, 65, 66, 41, 42], 'cur_cost': 52618.0}, {'tour': array([ 2, 30, 15, 33, 51, 60, 32, 64, 59, 53, 35, 43, 40, 56, 27, 19,  6,
       44, 36, 37, 25, 23, 47, 12, 38,  5, 45, 20,  9, 39, 52, 21, 18, 63,
        0, 13, 42, 34,  7, 16, 41, 11,  8, 50, 57, 61, 31, 22, 26, 62, 29,
       49, 14, 28, 58,  4, 54, 48,  3, 55,  1, 46, 17, 10, 65, 24]), 'cur_cost': 113743.0}, {'tour': [50, 3, 17, 34, 60, 41, 27, 15, 8, 2, 11, 25, 49, 58, 36, 63, 53, 31, 4, 22, 12, 20, 40, 59, 65, 42, 26, 18, 1, 14, 23, 39, 56, 61, 37, 9, 5, 32, 21, 38, 64, 45, 55, 62, 44, 10, 29, 54, 33, 19, 35, 43, 51, 28, 6, 48, 7, 57, 46, 30, 16, 47, 52], 'cur_cost': 110678.0}, {'tour': array([59, 16, 39, 60, 14, 63, 49, 40, 52, 24, 53, 38,  7, 25, 18, 30, 29,
       17, 37, 26,  0, 44, 27, 22, 62, 19, 42, 56, 20, 65, 21, 33, 45, 34,
        6, 36, 47, 55, 35, 13,  9, 10, 11, 64, 58, 61, 23,  4, 43, 28,  3,
       12,  8, 41, 46, 54, 32, 50, 57,  1,  5, 15,  2, 31, 51, 48]), 'cur_cost': 112808.0}, {'tour': [36, 20, 60, 56, 14, 26, 28, 22, 64, 18, 65, 1, 54, 39, 50, 11, 34, 13, 52, 0, 38, 49, 58, 42, 53, 29, 6, 27, 30, 31, 41, 45, 9, 44, 7, 61, 3, 35, 62, 40, 32, 10, 46, 19, 33, 55, 16, 63, 59, 47, 2, 48, 21, 37, 12, 23, 57, 4, 24, 8, 43, 51, 5, 17, 15, 25], 'cur_cost': 107222.0}, {'tour': [61, 22, 39, 2, 3, 21, 34, 46, 11, 62, 33, 19, 38, 35, 7, 64, 41, 29, 28, 13, 56, 8, 53, 63, 55, 14, 27, 5, 9, 4, 31, 59, 23, 15, 47, 50, 10, 60, 57, 52, 18, 42, 65, 49, 0, 17, 40, 54, 32, 37, 36, 44, 20, 26, 58, 6, 12, 45, 16, 30, 43, 51, 48, 24, 25, 1], 'cur_cost': 93763.0}, {'tour': [43, 47, 12, 14, 33, 64, 56, 20, 58, 8, 25, 11, 48, 52, 31, 4, 46, 42, 41, 17, 18, 38, 9, 16, 49, 27, 2, 15, 23, 40, 50, 45, 21, 29, 65, 7, 22, 44, 13, 60, 39, 57, 0, 35, 63, 28, 37, 3, 5, 62, 51, 32, 36, 6, 1, 24, 34, 53, 26, 59, 54, 55, 19, 61, 10, 30], 'cur_cost': 99891.0}, {'tour': [48, 63, 43, 16, 52, 32, 64, 28, 41, 33, 30, 19, 5, 17, 23, 53, 44, 38, 42, 65, 50, 25, 47, 24, 11, 4, 34, 37, 58, 21, 1, 22, 35, 56, 20, 55, 29, 45, 59, 8, 10, 57, 13, 2, 39, 54, 7, 40, 60, 31, 27, 49, 61, 9, 46, 14, 15, 51, 18, 0, 6, 26, 12, 3, 36, 62], 'cur_cost': 116636.0}]
2025-06-22 17:04:59,498 - ExploitationExpert - INFO - 局部搜索耗时: 1.39秒
2025-06-22 17:04:59,498 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-22 17:04:59,498 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 17:04:59,498 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 17:04:59,498 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:04:59,498 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [36, 20, 60] and ending with [17, 15, 25]
- Cost: 107222.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8681672025723473
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:04:59,504 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:05:05,178 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    36, 20, 45, 1, 3, 62, 58, 22, 4, 10, 39, 42, 49, 52, 34, 11, 14, 5, 50, 2, 6, 16, 63, 38, 41, 47, 24, 28, 30, 53, 59, 61, 9, 56, 21, 48, 33, 12, 54, 18, 35, 26, 7, 27, 37, 55, 19, 60, 23, 31, 13, 40, 51, 29, 32, 8, 44, 43, 46, 17, 15, 25
  ],
  "modification_strategy": "This path prioritizes diversity by re-routing through unexplored regions and minimizing overlap with the original path. The strategy focuses on exploring the two identified 'opportunity regions' and balancing risk with the specified risk tolerance. The initial portion of the path is altered to deviate early. Major segments are reconstructed to include nodes from opportunity regions, and then the remaining nodes are sequenced in order based on nearest neighbor and adjusted based on risk. Finally, the end nodes have been adjusted to connect to the end target [17,15,25].",
  "targeted_regions": ["Opportunity region 1", "Opportunity region 2"]
}
```
2025-06-22 17:05:05,178 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:05:05,178 - ExplorationExpert - INFO - 探索路径生成完成，成本: 78777.0, 路径: [36, 20, 45, 1, 3, 62, 58, 22, 4, 10, 39, 42, 49, 52, 34, 11, 14, 5, 50, 2, 6, 16, 63, 38, 41, 47, 24, 28, 30, 53, 59, 61, 9, 56, 21, 48, 33, 12, 54, 18, 35, 26, 7, 27, 37, 55, 19, 60, 23, 31, 13, 40, 51, 29, 32, 8, 44, 43, 46, 17, 15, 25]
2025-06-22 17:05:05,178 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [36, 20, 45, 1, 3, 62, 58, 22, 4, 10, 39, 42, 49, 52, 34, 11, 14, 5, 50, 2, 6, 16, 63, 38, 41, 47, 24, 28, 30, 53, 59, 61, 9, 56, 21, 48, 33, 12, 54, 18, 35, 26, 7, 27, 37, 55, 19, 60, 23, 31, 13, 40, 51, 29, 32, 8, 44, 43, 46, 17, 15, 25], 'cur_cost': 78777.0}
2025-06-22 17:05:05,178 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 17:05:05,178 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:05:05,178 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:05:05,178 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 119620.0
2025-06-22 17:05:05,743 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 17:05:05,744 - ExploitationExpert - INFO - res_population_costs: [9539]
2025-06-22 17:05:05,744 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 17:05:05,744 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:05:05,744 - ExploitationExpert - INFO - populations: [{'tour': [1, 8, 15, 22, 29, 36, 43, 50, 49, 48, 47, 46, 45, 44, 37, 30, 23, 16, 9, 2, 3, 4, 5, 6, 7, 14, 21, 28, 35, 42, 41, 40, 39, 38, 31, 24, 17, 10, 11, 12, 13, 20, 27, 34, 33, 32, 25, 18, 19, 26, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66], 'cur_cost': 30347.0}, {'tour': [24, 31, 35, 38, 40, 41, 47, 52, 55, 58, 61, 63, 65, 59, 53, 49, 44, 39, 36, 32, 26, 20, 15, 10, 6, 2, 1, 4, 8, 12, 18, 22, 28, 34, 37, 43, 48, 42], 'cur_cost': 20448.0}, {'tour': [59, 62, 47, 38, 29, 20, 11, 2, 1, 8, 17, 26, 35, 44, 53, 54, 55, 56, 57, 58, 60, 61, 52, 43, 34, 25, 16, 7, 14, 23, 32, 41, 40, 31, 22, 13, 4, 5, 6, 15, 24, 33, 42, 51, 50, 49, 48, 39, 30, 21, 12, 3, 9, 18, 27, 36, 45, 54, 55, 56, 57, 58, 59, 63, 64, 65, 66, 41, 42], 'cur_cost': 52618.0}, {'tour': array([ 2, 30, 15, 33, 51, 60, 32, 64, 59, 53, 35, 43, 40, 56, 27, 19,  6,
       44, 36, 37, 25, 23, 47, 12, 38,  5, 45, 20,  9, 39, 52, 21, 18, 63,
        0, 13, 42, 34,  7, 16, 41, 11,  8, 50, 57, 61, 31, 22, 26, 62, 29,
       49, 14, 28, 58,  4, 54, 48,  3, 55,  1, 46, 17, 10, 65, 24]), 'cur_cost': 113743.0}, {'tour': [50, 3, 17, 34, 60, 41, 27, 15, 8, 2, 11, 25, 49, 58, 36, 63, 53, 31, 4, 22, 12, 20, 40, 59, 65, 42, 26, 18, 1, 14, 23, 39, 56, 61, 37, 9, 5, 32, 21, 38, 64, 45, 55, 62, 44, 10, 29, 54, 33, 19, 35, 43, 51, 28, 6, 48, 7, 57, 46, 30, 16, 47, 52], 'cur_cost': 110678.0}, {'tour': array([59, 16, 39, 60, 14, 63, 49, 40, 52, 24, 53, 38,  7, 25, 18, 30, 29,
       17, 37, 26,  0, 44, 27, 22, 62, 19, 42, 56, 20, 65, 21, 33, 45, 34,
        6, 36, 47, 55, 35, 13,  9, 10, 11, 64, 58, 61, 23,  4, 43, 28,  3,
       12,  8, 41, 46, 54, 32, 50, 57,  1,  5, 15,  2, 31, 51, 48]), 'cur_cost': 112808.0}, {'tour': [36, 20, 45, 1, 3, 62, 58, 22, 4, 10, 39, 42, 49, 52, 34, 11, 14, 5, 50, 2, 6, 16, 63, 38, 41, 47, 24, 28, 30, 53, 59, 61, 9, 56, 21, 48, 33, 12, 54, 18, 35, 26, 7, 27, 37, 55, 19, 60, 23, 31, 13, 40, 51, 29, 32, 8, 44, 43, 46, 17, 15, 25], 'cur_cost': 78777.0}, {'tour': array([45, 65, 42, 24, 61, 26,  8, 43, 29,  7, 44, 37, 62, 27, 36, 23, 50,
        0, 57, 40, 20, 10,  1, 47, 39, 53, 14, 16, 51, 25, 22, 64, 41, 63,
       33, 11, 52,  6, 35, 21, 59, 32, 56, 58,  9, 38, 30, 34, 49, 19, 48,
       54, 55, 13, 60, 18, 28, 17,  3, 15, 31,  5, 46,  4, 12,  2]), 'cur_cost': 119620.0}, {'tour': [43, 47, 12, 14, 33, 64, 56, 20, 58, 8, 25, 11, 48, 52, 31, 4, 46, 42, 41, 17, 18, 38, 9, 16, 49, 27, 2, 15, 23, 40, 50, 45, 21, 29, 65, 7, 22, 44, 13, 60, 39, 57, 0, 35, 63, 28, 37, 3, 5, 62, 51, 32, 36, 6, 1, 24, 34, 53, 26, 59, 54, 55, 19, 61, 10, 30], 'cur_cost': 99891.0}, {'tour': [48, 63, 43, 16, 52, 32, 64, 28, 41, 33, 30, 19, 5, 17, 23, 53, 44, 38, 42, 65, 50, 25, 47, 24, 11, 4, 34, 37, 58, 21, 1, 22, 35, 56, 20, 55, 29, 45, 59, 8, 10, 57, 13, 2, 39, 54, 7, 40, 60, 31, 27, 49, 61, 9, 46, 14, 15, 51, 18, 0, 6, 26, 12, 3, 36, 62], 'cur_cost': 116636.0}]
2025-06-22 17:05:05,744 - ExploitationExpert - INFO - 局部搜索耗时: 0.57秒
2025-06-22 17:05:05,744 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-22 17:05:05,744 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 17:05:05,748 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 17:05:05,748 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:05:05,748 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:05:05,749 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 92804.0
2025-06-22 17:05:06,265 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 17:05:06,265 - ExploitationExpert - INFO - res_population_costs: [9539]
2025-06-22 17:05:06,266 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 56, 58, 60, 54, 57, 64, 62,
       59, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 17:05:06,267 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:05:06,267 - ExploitationExpert - INFO - populations: [{'tour': [1, 8, 15, 22, 29, 36, 43, 50, 49, 48, 47, 46, 45, 44, 37, 30, 23, 16, 9, 2, 3, 4, 5, 6, 7, 14, 21, 28, 35, 42, 41, 40, 39, 38, 31, 24, 17, 10, 11, 12, 13, 20, 27, 34, 33, 32, 25, 18, 19, 26, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66], 'cur_cost': 30347.0}, {'tour': [24, 31, 35, 38, 40, 41, 47, 52, 55, 58, 61, 63, 65, 59, 53, 49, 44, 39, 36, 32, 26, 20, 15, 10, 6, 2, 1, 4, 8, 12, 18, 22, 28, 34, 37, 43, 48, 42], 'cur_cost': 20448.0}, {'tour': [59, 62, 47, 38, 29, 20, 11, 2, 1, 8, 17, 26, 35, 44, 53, 54, 55, 56, 57, 58, 60, 61, 52, 43, 34, 25, 16, 7, 14, 23, 32, 41, 40, 31, 22, 13, 4, 5, 6, 15, 24, 33, 42, 51, 50, 49, 48, 39, 30, 21, 12, 3, 9, 18, 27, 36, 45, 54, 55, 56, 57, 58, 59, 63, 64, 65, 66, 41, 42], 'cur_cost': 52618.0}, {'tour': array([ 2, 30, 15, 33, 51, 60, 32, 64, 59, 53, 35, 43, 40, 56, 27, 19,  6,
       44, 36, 37, 25, 23, 47, 12, 38,  5, 45, 20,  9, 39, 52, 21, 18, 63,
        0, 13, 42, 34,  7, 16, 41, 11,  8, 50, 57, 61, 31, 22, 26, 62, 29,
       49, 14, 28, 58,  4, 54, 48,  3, 55,  1, 46, 17, 10, 65, 24]), 'cur_cost': 113743.0}, {'tour': [50, 3, 17, 34, 60, 41, 27, 15, 8, 2, 11, 25, 49, 58, 36, 63, 53, 31, 4, 22, 12, 20, 40, 59, 65, 42, 26, 18, 1, 14, 23, 39, 56, 61, 37, 9, 5, 32, 21, 38, 64, 45, 55, 62, 44, 10, 29, 54, 33, 19, 35, 43, 51, 28, 6, 48, 7, 57, 46, 30, 16, 47, 52], 'cur_cost': 110678.0}, {'tour': array([59, 16, 39, 60, 14, 63, 49, 40, 52, 24, 53, 38,  7, 25, 18, 30, 29,
       17, 37, 26,  0, 44, 27, 22, 62, 19, 42, 56, 20, 65, 21, 33, 45, 34,
        6, 36, 47, 55, 35, 13,  9, 10, 11, 64, 58, 61, 23,  4, 43, 28,  3,
       12,  8, 41, 46, 54, 32, 50, 57,  1,  5, 15,  2, 31, 51, 48]), 'cur_cost': 112808.0}, {'tour': [36, 20, 45, 1, 3, 62, 58, 22, 4, 10, 39, 42, 49, 52, 34, 11, 14, 5, 50, 2, 6, 16, 63, 38, 41, 47, 24, 28, 30, 53, 59, 61, 9, 56, 21, 48, 33, 12, 54, 18, 35, 26, 7, 27, 37, 55, 19, 60, 23, 31, 13, 40, 51, 29, 32, 8, 44, 43, 46, 17, 15, 25], 'cur_cost': 78777.0}, {'tour': array([45, 65, 42, 24, 61, 26,  8, 43, 29,  7, 44, 37, 62, 27, 36, 23, 50,
        0, 57, 40, 20, 10,  1, 47, 39, 53, 14, 16, 51, 25, 22, 64, 41, 63,
       33, 11, 52,  6, 35, 21, 59, 32, 56, 58,  9, 38, 30, 34, 49, 19, 48,
       54, 55, 13, 60, 18, 28, 17,  3, 15, 31,  5, 46,  4, 12,  2]), 'cur_cost': 119620.0}, {'tour': array([33,  4,  8, 63, 10, 57, 11, 47, 17, 32, 45, 25, 34, 18, 43, 37,  2,
       61, 58, 40, 38, 48, 12, 51, 31,  6, 53, 14, 24, 23, 50, 65, 36, 26,
       56, 54, 41, 19, 22,  5, 55,  0,  9, 28, 46, 52, 21, 59, 30,  1, 16,
        3, 64, 62,  7, 42, 49, 39, 29, 44, 13, 20, 60, 27, 35, 15]), 'cur_cost': 92804.0}, {'tour': [48, 63, 43, 16, 52, 32, 64, 28, 41, 33, 30, 19, 5, 17, 23, 53, 44, 38, 42, 65, 50, 25, 47, 24, 11, 4, 34, 37, 58, 21, 1, 22, 35, 56, 20, 55, 29, 45, 59, 8, 10, 57, 13, 2, 39, 54, 7, 40, 60, 31, 27, 49, 61, 9, 46, 14, 15, 51, 18, 0, 6, 26, 12, 3, 36, 62], 'cur_cost': 116636.0}]
2025-06-22 17:05:06,268 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 17:05:06,268 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-22 17:05:06,269 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 17:05:06,269 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-22 17:05:06,269 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:05:06,270 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [48, 63, 43] and ending with [3, 36, 62]
- Cost: 116636.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8754045307443366
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:05:06,270 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:05:11,856 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [48, 5, 14, 22, 58, 1, 17, 40, 51, 30, 42, 65, 2, 59, 29, 31, 45, 25, 55, 61, 13, 24, 4, 11, 28, 50, 39, 53, 15, 34, 6, 21, 49, 10, 32, 54, 46, 18, 33, 37, 56, 47, 27, 9, 20, 64, 60, 19, 38, 8, 52, 7, 41, 12, 66, 23, 16, 44, 35, 63, 43, 57, 62, 3],
  "modification_strategy": "Prioritized exploration of opportunity regions and increased path diversity. The strategy involved a re-evaluation of unexplored regions, and a deviation from the original path's early segments to facilitate the exploration of promising areas. The overall approach targeted new, diverse segments to promote a varied route through the network, focusing on avoiding difficult regions.",
  "targeted_regions": [ "Unexplored regions", "Opportunity regions"]
}
```
2025-06-22 17:05:11,858 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:05:11,858 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103754.0, 路径: [48, 5, 14, 22, 58, 1, 17, 40, 51, 30, 42, 65, 2, 59, 29, 31, 45, 25, 55, 61, 13, 24, 4, 11, 28, 50, 39, 53, 15, 34, 6, 21, 49, 10, 32, 54, 46, 18, 33, 37, 56, 47, 27, 9, 20, 64, 60, 19, 38, 8, 52, 7, 41, 12, 66, 23, 16, 44, 35, 63, 43, 57, 62, 3]
2025-06-22 17:05:11,858 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [48, 5, 14, 22, 58, 1, 17, 40, 51, 30, 42, 65, 2, 59, 29, 31, 45, 25, 55, 61, 13, 24, 4, 11, 28, 50, 39, 53, 15, 34, 6, 21, 49, 10, 32, 54, 46, 18, 33, 37, 56, 47, 27, 9, 20, 64, 60, 19, 38, 8, 52, 7, 41, 12, 66, 23, 16, 44, 35, 63, 43, 57, 62, 3], 'cur_cost': 103754.0}
2025-06-22 17:05:11,858 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [1, 8, 15, 22, 29, 36, 43, 50, 49, 48, 47, 46, 45, 44, 37, 30, 23, 16, 9, 2, 3, 4, 5, 6, 7, 14, 21, 28, 35, 42, 41, 40, 39, 38, 31, 24, 17, 10, 11, 12, 13, 20, 27, 34, 33, 32, 25, 18, 19, 26, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66], 'cur_cost': 30347.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [24, 31, 35, 38, 40, 41, 47, 52, 55, 58, 61, 63, 65, 59, 53, 49, 44, 39, 36, 32, 26, 20, 15, 10, 6, 2, 1, 4, 8, 12, 18, 22, 28, 34, 37, 43, 48, 42], 'cur_cost': 20448.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [59, 62, 47, 38, 29, 20, 11, 2, 1, 8, 17, 26, 35, 44, 53, 54, 55, 56, 57, 58, 60, 61, 52, 43, 34, 25, 16, 7, 14, 23, 32, 41, 40, 31, 22, 13, 4, 5, 6, 15, 24, 33, 42, 51, 50, 49, 48, 39, 30, 21, 12, 3, 9, 18, 27, 36, 45, 54, 55, 56, 57, 58, 59, 63, 64, 65, 66, 41, 42], 'cur_cost': 52618.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [50, 3, 17, 34, 60, 41, 27, 15, 8, 2, 11, 25, 49, 58, 36, 63, 53, 31, 4, 22, 12, 20, 40, 59, 65, 42, 26, 18, 1, 14, 23, 39, 56, 61, 37, 9, 5, 32, 21, 38, 64, 45, 55, 62, 44, 10, 29, 54, 33, 19, 35, 43, 51, 28, 6, 48, 7, 57, 46, 30, 16, 47, 52], 'cur_cost': 110678.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [36, 20, 45, 1, 3, 62, 58, 22, 4, 10, 39, 42, 49, 52, 34, 11, 14, 5, 50, 2, 6, 16, 63, 38, 41, 47, 24, 28, 30, 53, 59, 61, 9, 56, 21, 48, 33, 12, 54, 18, 35, 26, 7, 27, 37, 55, 19, 60, 23, 31, 13, 40, 51, 29, 32, 8, 44, 43, 46, 17, 15, 25], 'cur_cost': 78777.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [48, 5, 14, 22, 58, 1, 17, 40, 51, 30, 42, 65, 2, 59, 29, 31, 45, 25, 55, 61, 13, 24, 4, 11, 28, 50, 39, 53, 15, 34, 6, 21, 49, 10, 32, 54, 46, 18, 33, 37, 56, 47, 27, 9, 20, 64, 60, 19, 38, 8, 52, 7, 41, 12, 66, 23, 16, 44, 35, 63, 43, 57, 62, 3], 'cur_cost': 103754.0}}]
2025-06-22 17:05:11,858 - __main__ - INFO - 进化阶段完成
2025-06-22 17:05:11,858 - __main__ - INFO - 开始评估阶段
2025-06-22 17:05:11,858 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 17:05:11,858 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 17:05:11,858 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 0/5
- Population changes:
  * Cost: min 10007.0->20448.0, mean 76690.1->83559.7
  * Diversity: 0.7661538461538462->0.8685064935064936
  * Elite solutions: 3->1

## Strategy Performance:
- Explore: 6 individuals, success rate 33.3%, avg improvement -6944.67
- Exploit: 4 individuals, success rate 25.0%, avg improvement -6757.0
- Overall improvement: -10441.0

## Historical Trends:
No historical data available

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 17:05:20,110 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage - Exploration Focused",
    "features": "The evolution is in its early stages, with only a single iteration completed. Cost has increased significantly for both minimum and average, suggesting the algorithm is struggling to find improvements. Diversity has increased, which is expected during exploration. The number of elite solutions decreased, indicating a potential decline in the best solutions.",
    "details": "Iteration 0/5 indicates early exploration, and the cost increase suggests the search space may have challenging regions or the current mutation/crossover operators are not yet effective in finding better solutions. The increasing diversity is a positive sign as it helps to explore a wider portion of the search space."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "33.3%",
      "avg_improvement": "-6944.67",
      "assessment": "Explore has a moderate success rate (33.3%), suggesting some potential for finding better solutions in unexplored areas. The negative average improvement indicates that overall, exploration is currently leading to a decrease in solution quality. This is common at the initial stages.",
      "action": "Maintain explore strategy, but review explore parameters (mutation rate, step size if applicable) to potentially encourage more targeted exploration."
    },
    "exploit": {
      "success_rate": "25.0%",
      "avg_improvement": "-6757.0",
      "assessment": "Exploit has a low success rate (25.0%) and also shows negative average improvement. This means that refining current solutions are currently ineffective. Consider whether exploit is premature, or if it needs to be adjusted.",
      "action": "Review exploit parameters (e.g. crossover, fine-tuning). Consider delaying the use of exploit until better solutions are found through exploration or reducing the frequency of exploitation."
    },
    "overall": {
      "improvement": "-10441.0",
      "assessment": "Overall, the evolution is experiencing negative improvement. The algorithm is likely struggling to move towards better solutions. This suggests the initial parameters or operators need adjustment to be more effective."
    }
  },
  "balance_state": {
    "assessment": "Currently, the balance leans towards exploration, but both strategies are not effective and the overall result is negative. Exploration is needed to navigate the search space and discover promising regions. However, exploit is not currently helping to refine these.",
    "adjustment_needs": "The initial parameters and operators may need adjustment. The balance should likely lean more toward exploration, or refine the exploration strategy to be more effective. Exploitation should be reviewed if it is causing a decline in solution quality."
  },
  "recommendations": [
    {
      "type": "Parameter Tuning",
      "description": "Carefully review and adjust parameters such as mutation rates, step sizes (if applicable), and crossover parameters. Experiment with different values to see if they improve performance. Specifically, consider a slight increase in the explore step size to allow broader exploration if the problem size is not so huge.",
      "rationale": "Parameter tuning is crucial in early stages to find effective settings for the specific problem instance and operators."
    },
    {
      "type": "Operator Review",
      "description": "Analyze the mutation and crossover operators to ensure they are suitable for the problem and that they can guide towards better solutions. Consider alternative operators if the current ones appear ineffective.",
      "rationale": "Poorly chosen operators can hinder the progress of evolution."
    },
    {
      "type": "Exploration Strategy Adjustment",
      "description": "Review the exploration strategy. The success rate is low. Evaluate whether the exploration is too broad and lacking focus, or if the parameters and initial population needs improvement. Adjust explore parameters (mutation or step size) to promote more focused exploration if needed.",
      "rationale": "Improve the chance of finding solutions."
    },
     {
      "type": "Exploit Strategy Review",
      "description": "Consider reducing the frequency of exploit strategy. Review and potentially adjust the exploit operator(s) (crossover or fine-tuning).",
      "rationale": "Since the success rate of exploit is low and the average improvement is negative, exploiting solutions that are not promising may decrease solution quality."
    }
  ]
}
```
2025-06-22 17:05:20,131 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 17:05:20,131 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage - Exploration Focused",
    "features": "The evolution is in its early stages, with only a single iteration completed. Cost has increased significantly for both minimum and average, suggesting the algorithm is struggling to find improvements. Diversity has increased, which is expected during exploration. The number of elite solutions decreased, indicating a potential decline in the best solutions.",
    "details": "Iteration 0/5 indicates early exploration, and the cost increase suggests the search space may have challenging regions or the current mutation/crossover operators are not yet effective in finding better solutions. The increasing diversity is a positive sign as it helps to explore a wider portion of the search space."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "33.3%",
      "avg_improvement": "-6944.67",
      "assessment": "Explore has a moderate success rate (33.3%), suggesting some potential for finding better solutions in unexplored areas. The negative average improvement indicates that overall, exploration is currently leading to a decrease in solution quality. This is common at the initial stages.",
      "action": "Maintain explore strategy, but review explore parameters (mutation rate, step size if applicable) to potentially encourage more targeted exploration."
    },
    "exploit": {
      "success_rate": "25.0%",
      "avg_improvement": "-6757.0",
      "assessment": "Exploit has a low success rate (25.0%) and also shows negative average improvement. This means that refining current solutions are currently ineffective. Consider whether exploit is premature, or if it needs to be adjusted.",
      "action": "Review exploit parameters (e.g. crossover, fine-tuning). Consider delaying the use of exploit until better solutions are found through exploration or reducing the frequency of exploitation."
    },
    "overall": {
      "improvement": "-10441.0",
      "assessment": "Overall, the evolution is experiencing negative improvement. The algorithm is likely struggling to move towards better solutions. This suggests the initial parameters or operators need adjustment to be more effective."
    }
  },
  "balance_state": {
    "assessment": "Currently, the balance leans towards exploration, but both strategies are not effective and the overall result is negative. Exploration is needed to navigate the search space and discover promising regions. However, exploit is not currently helping to refine these.",
    "adjustment_needs": "The initial parameters and operators may need adjustment. The balance should likely lean more toward exploration, or refine the exploration strategy to be more effective. Exploitation should be reviewed if it is causing a decline in solution quality."
  },
  "recommendations": [
    {
      "type": "Parameter Tuning",
      "description": "Carefully review and adjust parameters such as mutation rates, step sizes (if applicable), and crossover parameters. Experiment with different values to see if they improve performance. Specifically, consider a slight increase in the explore step size to allow broader exploration if the problem size is not so huge.",
      "rationale": "Parameter tuning is crucial in early stages to find effective settings for the specific problem instance and operators."
    },
    {
      "type": "Operator Review",
      "description": "Analyze the mutation and crossover operators to ensure they are suitable for the problem and that they can guide towards better solutions. Consider alternative operators if the current ones appear ineffective.",
      "rationale": "Poorly chosen operators can hinder the progress of evolution."
    },
    {
      "type": "Exploration Strategy Adjustment",
      "description": "Review the exploration strategy. The success rate is low. Evaluate whether the exploration is too broad and lacking focus, or if the parameters and initial population needs improvement. Adjust explore parameters (mutation or step size) to promote more focused exploration if needed.",
      "rationale": "Improve the chance of finding solutions."
    },
     {
      "type": "Exploit Strategy Review",
      "description": "Consider reducing the frequency of exploit strategy. Review and potentially adjust the exploit operator(s) (crossover or fine-tuning).",
      "rationale": "Since the success rate of exploit is low and the average improvement is negative, exploiting solutions that are not promising may decrease solution quality."
    }
  ]
}
```
2025-06-22 17:05:20,131 - __main__ - INFO - 评估阶段完成
2025-06-22 17:05:20,132 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage - Exploration Focused",
    "features": "The evolution is in its early stages, with only a single iteration completed. Cost has increased significantly for both minimum and average, suggesting the algorithm is struggling to find improvements. Diversity has increased, which is expected during exploration. The number of elite solutions decreased, indicating a potential decline in the best solutions.",
    "details": "Iteration 0/5 indicates early exploration, and the cost increase suggests the search space may have challenging regions or the current mutation/crossover operators are not yet effective in finding better solutions. The increasing diversity is a positive sign as it helps to explore a wider portion of the search space."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "33.3%",
      "avg_improvement": "-6944.67",
      "assessment": "Explore has a moderate success rate (33.3%), suggesting some potential for finding better solutions in unexplored areas. The negative average improvement indicates that overall, exploration is currently leading to a decrease in solution quality. This is common at the initial stages.",
      "action": "Maintain explore strategy, but review explore parameters (mutation rate, step size if applicable) to potentially encourage more targeted exploration."
    },
    "exploit": {
      "success_rate": "25.0%",
      "avg_improvement": "-6757.0",
      "assessment": "Exploit has a low success rate (25.0%) and also shows negative average improvement. This means that refining current solutions are currently ineffective. Consider whether exploit is premature, or if it needs to be adjusted.",
      "action": "Review exploit parameters (e.g. crossover, fine-tuning). Consider delaying the use of exploit until better solutions are found through exploration or reducing the frequency of exploitation."
    },
    "overall": {
      "improvement": "-10441.0",
      "assessment": "Overall, the evolution is experiencing negative improvement. The algorithm is likely struggling to move towards better solutions. This suggests the initial parameters or operators need adjustment to be more effective."
    }
  },
  "balance_state": {
    "assessment": "Currently, the balance leans towards exploration, but both strategies are not effective and the overall result is negative. Exploration is needed to navigate the search space and discover promising regions. However, exploit is not currently helping to refine these.",
    "adjustment_needs": "The initial parameters and operators may need adjustment. The balance should likely lean more toward exploration, or refine the exploration strategy to be more effective. Exploitation should be reviewed if it is causing a decline in solution quality."
  },
  "recommendations": [
    {
      "type": "Parameter Tuning",
      "description": "Carefully review and adjust parameters such as mutation rates, step sizes (if applicable), and crossover parameters. Experiment with different values to see if they improve performance. Specifically, consider a slight increase in the explore step size to allow broader exploration if the problem size is not so huge.",
      "rationale": "Parameter tuning is crucial in early stages to find effective settings for the specific problem instance and operators."
    },
    {
      "type": "Operator Review",
      "description": "Analyze the mutation and crossover operators to ensure they are suitable for the problem and that they can guide towards better solutions. Consider alternative operators if the current ones appear ineffective.",
      "rationale": "Poorly chosen operators can hinder the progress of evolution."
    },
    {
      "type": "Exploration Strategy Adjustment",
      "description": "Review the exploration strategy. The success rate is low. Evaluate whether the exploration is too broad and lacking focus, or if the parameters and initial population needs improvement. Adjust explore parameters (mutation or step size) to promote more focused exploration if needed.",
      "rationale": "Improve the chance of finding solutions."
    },
     {
      "type": "Exploit Strategy Review",
      "description": "Consider reducing the frequency of exploit strategy. Review and potentially adjust the exploit operator(s) (crossover or fine-tuning).",
      "rationale": "Since the success rate of exploit is low and the average improvement is negative, exploiting solutions that are not promising may decrease solution quality."
    }
  ]
}
```
2025-06-22 17:05:20,133 - __main__ - INFO - 当前最佳适应度: 20448.0
2025-06-22 17:05:20,133 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-22 17:05:20,133 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-22 17:05:20,133 - __main__ - INFO - 开始分析阶段
2025-06-22 17:05:20,133 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:05:20,141 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 20448.0, 'max': 119620.0, 'mean': 83559.7, 'std': 34771.32447017226}, 'diversity': 0.9949494949494949, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:05:20,142 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 20448.0, 'max': 119620.0, 'mean': 83559.7, 'std': 34771.32447017226}, 'diversity_level': 0.9949494949494949, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:05:20,142 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:05:20,142 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:05:20,142 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:05:20,142 - PathExpert - WARNING - 发现无效的城市索引: [66], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:05:20,142 - PathExpert - WARNING - 发现无效的城市索引: [66], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:05:20,143 - PathExpert - WARNING - 发现无效的城市索引: [66], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:05:20,145 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=31, tour_length=38
2025-06-22 17:05:20,145 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=31, city2=35, tour_length=38
2025-06-22 17:05:20,146 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=38, tour_length=38
2025-06-22 17:05:20,146 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=40, tour_length=38
2025-06-22 17:05:20,146 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=41, tour_length=38
2025-06-22 17:05:20,146 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=41, city2=47, tour_length=38
2025-06-22 17:05:20,146 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=52, tour_length=38
2025-06-22 17:05:20,146 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=55, tour_length=38
2025-06-22 17:05:20,146 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=58, tour_length=38
2025-06-22 17:05:20,146 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=61, tour_length=38
2025-06-22 17:05:20,147 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=61, city2=63, tour_length=38
2025-06-22 17:05:20,147 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=63, city2=65, tour_length=38
2025-06-22 17:05:20,147 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=65, city2=59, tour_length=38
2025-06-22 17:05:20,147 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=53, tour_length=38
2025-06-22 17:05:20,147 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=53, city2=49, tour_length=38
2025-06-22 17:05:20,147 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=44, tour_length=38
2025-06-22 17:05:20,147 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=44, city2=39, tour_length=38
2025-06-22 17:05:20,147 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=39, city2=36, tour_length=38
2025-06-22 17:05:20,149 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=36, city2=32, tour_length=38
2025-06-22 17:05:20,149 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=26, tour_length=38
2025-06-22 17:05:20,149 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=26, city2=20, tour_length=38
2025-06-22 17:05:20,149 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=20, city2=15, tour_length=38
2025-06-22 17:05:20,149 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=10, tour_length=38
2025-06-22 17:05:20,149 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=6, tour_length=38
2025-06-22 17:05:20,149 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=2, tour_length=38
2025-06-22 17:05:20,150 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=1, tour_length=38
2025-06-22 17:05:20,150 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=4, tour_length=38
2025-06-22 17:05:20,150 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=8, tour_length=38
2025-06-22 17:05:20,150 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=8, city2=12, tour_length=38
2025-06-22 17:05:20,151 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=18, tour_length=38
2025-06-22 17:05:20,151 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=22, tour_length=38
2025-06-22 17:05:20,151 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=28, tour_length=38
2025-06-22 17:05:20,151 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=28, city2=34, tour_length=38
2025-06-22 17:05:20,151 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=34, city2=37, tour_length=38
2025-06-22 17:05:20,151 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=37, city2=43, tour_length=38
2025-06-22 17:05:20,151 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=43, city2=48, tour_length=38
2025-06-22 17:05:20,151 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=42, tour_length=38
2025-06-22 17:05:20,152 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=24, tour_length=38
2025-06-22 17:05:20,152 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 17:05:20,152 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:05:20,153 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 17:05:20,153 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:05:20,153 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:05:20,153 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:05:20,153 - EliteExpert - INFO - 精英解分析完成
2025-06-22 17:05:20,154 - __main__ - INFO - 精英专家分析报告: {'elite_count': 1, 'elite_common_features': {'common_edges': {'(0, 1)': 1.0, '(1, 7)': 1.0, '(7, 3)': 1.0, '(3, 27)': 1.0, '(27, 37)': 1.0, '(37, 36)': 1.0, '(36, 26)': 1.0, '(26, 25)': 1.0, '(25, 31)': 1.0, '(31, 24)': 1.0, '(24, 29)': 1.0, '(29, 32)': 1.0, '(32, 33)': 1.0, '(33, 28)': 1.0, '(28, 30)': 1.0, '(30, 35)': 1.0, '(35, 34)': 1.0, '(34, 42)': 1.0, '(42, 50)': 1.0, '(50, 45)': 1.0, '(45, 38)': 1.0, '(38, 51)': 1.0, '(51, 41)': 1.0, '(41, 44)': 1.0, '(44, 39)': 1.0, '(39, 46)': 1.0, '(46, 47)': 1.0, '(47, 49)': 1.0, '(49, 48)': 1.0, '(48, 43)': 1.0, '(43, 40)': 1.0, '(40, 21)': 1.0, '(21, 20)': 1.0, '(20, 13)': 1.0, '(13, 19)': 1.0, '(19, 16)': 1.0, '(16, 18)': 1.0, '(18, 17)': 1.0, '(17, 12)': 1.0, '(12, 23)': 1.0, '(23, 22)': 1.0, '(22, 15)': 1.0, '(15, 14)': 1.0, '(14, 56)': 1.0, '(56, 58)': 1.0, '(58, 60)': 1.0, '(60, 54)': 1.0, '(54, 57)': 1.0, '(57, 64)': 1.0, '(64, 62)': 1.0, '(62, 59)': 1.0, '(59, 53)': 1.0, '(53, 65)': 1.0, '(65, 52)': 1.0, '(52, 63)': 1.0, '(63, 61)': 1.0, '(61, 55)': 1.0, '(55, 10)': 1.0, '(10, 8)': 1.0, '(8, 2)': 1.0, '(2, 6)': 1.0, '(6, 4)': 1.0, '(4, 5)': 1.0, '(5, 9)': 1.0, '(9, 11)': 1.0, '(11, 0)': 1.0}, 'common_edge_ratio': 1.0}, 'fixed_nodes': [], 'population_gap': {'cost_gap': {'min_gap': 10909.0, 'avg_gap': 74020.7}, 'structure_gap': {'unique_elite_edges': 51, 'unique_pop_edges': 559, 'common_edges': 15}}, 'elite_diversity': {'diversity_score': 0}}
2025-06-22 17:05:20,154 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:05:20,154 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:05:20,154 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:05:20,155 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=20448.0, max=119620.0, mean=83559.7, std=34771.32447017226
- Diversity: 0.9949494949494949, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 0 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 0 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:05:27,668 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely smooth due to the lack of structure (0 high-quality edges, 0 common subpaths) and high diversity.  The large cost range and standard deviation hint at some variance, but the absence of strong structural features suggests a potentially unimodal search space or a space with a less complex landscape.",
    "modality": "Potentially unimodal or with a few dominant modes. The lack of convergence and identified difficult regions, coupled with the high diversity, suggests that the solutions haven't clustered around any specific optima. However, the wide cost range indicates the possibility of multiple, possibly widely-separated, potential solutions.",
    "cost_distribution": "The cost distribution, based on the provided summary, shows significant variance. The spread between min and max, along with the standard deviation, indicates a wide range of solution quality. This could indicate a search space with varying difficulty levels across different regions or simply a current state where exploration is still wide."
  },
  "population_state": {
    "diversity": "High (0.9949), indicating a good spread of solutions across the search space. This is positive for exploration.",
    "convergence": "None (0.0), indicating that the population has not yet converged towards a specific solution or region. This is expected given the high diversity.",
    "elite_solutions": "No elite solutions identified (0 with 0 fixed nodes). This confirms that the current population hasn't found any significantly better solutions compared to others and that the search hasn't pinpointed any local optima yet."
  },
  "difficult_regions": "None identified (0 identified difficult regions). The analysis suggests that the search has not yet encountered any areas that are particularly challenging to explore, or if they do exist, they are not pronounced enough to be identified. The high diversity reinforces this, as solutions haven't clustered around any poorly performing areas.",
  "opportunity_regions": "None identified (0 identified opportunity regions). The current state shows no specific areas or features of the solutions to suggest that some regions are especially promising in this stage. The high diversity and lack of structure, however, indicate that extensive exploration is still required to define the regions of opportunity.",
  "evolution_direction": {
    "strategy": "Exploration is key. Given the high diversity and lack of convergence, the focus should be on continuing to explore the search space. The algorithm should prioritize diversification to discover potentially promising regions. Avoiding any strong exploitation strategy until more regions are identified would be suitable.",
    "recommendations": [
      "Maintain or increase mutation rate to promote exploration.",
      "Consider adaptive parameter tuning based on exploration metrics (e.g., if diversity drops, increase exploration).",
      "Avoid strong selection pressures to preserve diversity.",
      "Evaluate the population frequently to look for signs of convergence or potentially promising regions.",
      "Once a good solution is identified, try to exploit the edges and subpaths."
    ]
  }
}
```
2025-06-22 17:05:27,668 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:05:27,668 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Likely smooth due to the lack of structure (0 high-quality edges, 0 common subpaths) and high diversity.  The large cost range and standard deviation hint at some variance, but the absence of strong structural features suggests a potentially unimodal search space or a space with a less complex landscape.', 'modality': "Potentially unimodal or with a few dominant modes. The lack of convergence and identified difficult regions, coupled with the high diversity, suggests that the solutions haven't clustered around any specific optima. However, the wide cost range indicates the possibility of multiple, possibly widely-separated, potential solutions.", 'cost_distribution': 'The cost distribution, based on the provided summary, shows significant variance. The spread between min and max, along with the standard deviation, indicates a wide range of solution quality. This could indicate a search space with varying difficulty levels across different regions or simply a current state where exploration is still wide.'}, 'population_state': {'diversity': 'High (0.9949), indicating a good spread of solutions across the search space. This is positive for exploration.', 'convergence': 'None (0.0), indicating that the population has not yet converged towards a specific solution or region. This is expected given the high diversity.', 'elite_solutions': "No elite solutions identified (0 with 0 fixed nodes). This confirms that the current population hasn't found any significantly better solutions compared to others and that the search hasn't pinpointed any local optima yet."}, 'difficult_regions': "None identified (0 identified difficult regions). The analysis suggests that the search has not yet encountered any areas that are particularly challenging to explore, or if they do exist, they are not pronounced enough to be identified. The high diversity reinforces this, as solutions haven't clustered around any poorly performing areas.", 'opportunity_regions': 'None identified (0 identified opportunity regions). The current state shows no specific areas or features of the solutions to suggest that some regions are especially promising in this stage. The high diversity and lack of structure, however, indicate that extensive exploration is still required to define the regions of opportunity.', 'evolution_direction': {'strategy': 'Exploration is key. Given the high diversity and lack of convergence, the focus should be on continuing to explore the search space. The algorithm should prioritize diversification to discover potentially promising regions. Avoiding any strong exploitation strategy until more regions are identified would be suitable.', 'recommendations': ['Maintain or increase mutation rate to promote exploration.', 'Consider adaptive parameter tuning based on exploration metrics (e.g., if diversity drops, increase exploration).', 'Avoid strong selection pressures to preserve diversity.', 'Evaluate the population frequently to look for signs of convergence or potentially promising regions.', 'Once a good solution is identified, try to exploit the edges and subpaths.']}}
2025-06-22 17:05:27,668 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:05:27,668 - __main__ - INFO - 分析阶段完成
2025-06-22 17:05:27,668 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Likely smooth due to the lack of structure (0 high-quality edges, 0 common subpaths) and high diversity.  The large cost range and standard deviation hint at some variance, but the absence of strong structural features suggests a potentially unimodal search space or a space with a less complex landscape.', 'modality': "Potentially unimodal or with a few dominant modes. The lack of convergence and identified difficult regions, coupled with the high diversity, suggests that the solutions haven't clustered around any specific optima. However, the wide cost range indicates the possibility of multiple, possibly widely-separated, potential solutions.", 'cost_distribution': 'The cost distribution, based on the provided summary, shows significant variance. The spread between min and max, along with the standard deviation, indicates a wide range of solution quality. This could indicate a search space with varying difficulty levels across different regions or simply a current state where exploration is still wide.'}, 'population_state': {'diversity': 'High (0.9949), indicating a good spread of solutions across the search space. This is positive for exploration.', 'convergence': 'None (0.0), indicating that the population has not yet converged towards a specific solution or region. This is expected given the high diversity.', 'elite_solutions': "No elite solutions identified (0 with 0 fixed nodes). This confirms that the current population hasn't found any significantly better solutions compared to others and that the search hasn't pinpointed any local optima yet."}, 'difficult_regions': "None identified (0 identified difficult regions). The analysis suggests that the search has not yet encountered any areas that are particularly challenging to explore, or if they do exist, they are not pronounced enough to be identified. The high diversity reinforces this, as solutions haven't clustered around any poorly performing areas.", 'opportunity_regions': 'None identified (0 identified opportunity regions). The current state shows no specific areas or features of the solutions to suggest that some regions are especially promising in this stage. The high diversity and lack of structure, however, indicate that extensive exploration is still required to define the regions of opportunity.', 'evolution_direction': {'strategy': 'Exploration is key. Given the high diversity and lack of convergence, the focus should be on continuing to explore the search space. The algorithm should prioritize diversification to discover potentially promising regions. Avoiding any strong exploitation strategy until more regions are identified would be suitable.', 'recommendations': ['Maintain or increase mutation rate to promote exploration.', 'Consider adaptive parameter tuning based on exploration metrics (e.g., if diversity drops, increase exploration).', 'Avoid strong selection pressures to preserve diversity.', 'Evaluate the population frequently to look for signs of convergence or potentially promising regions.', 'Once a good solution is identified, try to exploit the edges and subpaths.']}}
2025-06-22 17:05:27,668 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:05:27,668 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:05:27,668 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 1
- Population: 10 individuals, diversity=High (0.9949), indicating a good spread of solutions across the search space. This is positive for exploration.
- Best cost: 20448.0, Mean cost: 83559.7
- Search space: Ruggedness: Likely smooth due to the lack of structure (0 high-quality edges, 0 common subpaths) and high diversity.  The large cost range and standard deviation hint at some variance, but the absence of strong structural features suggests a potentially unimodal search space or a space with a less complex landscape., Modality: Potentially unimodal or with a few dominant modes. The lack of convergence and identified difficult regions, coupled with the high diversity, suggests that the solutions haven't clustered around any specific optima. However, the wide cost range indicates the possibility of multiple, possibly widely-separated, potential solutions.
- Difficult regions: 340 regions
- Opportunity regions: 334 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early Stage - Exploration Focused",
    "features": "The evolution is in its early stages, with only a single iteration completed. Cost has increased sig...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:05:27,668 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:05:32,440 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size_factor": 0.08,
      "exploration_radius_factor": 0.15,
      "local_search_depth": 2,
      "adaptive_step_size": true
    },
    "exploit": {
      "crossover_rate": 0.7,
      "mutation_rate": 0.05,
      "local_search_depth": 5,
      "elitism": true,
      "elitism_proportion": 0.1
    }
  }
}
```

2025-06-22 17:05:32,443 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore']
2025-06-22 17:05:32,443 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size_factor": 0.08,
      "exploration_radius_factor": 0.15,
      "local_search_depth": 2,
      "adaptive_step_size": true
    },
    "exploit": {
      "crossover_rate": 0.7,
      "mutation_rate": 0.05,
      "local_search_depth": 5,
      "elitism": true,
      "elitism_proportion": 0.1
    }
  }
}
```

2025-06-22 17:05:32,443 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:05:32,443 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore']
2025-06-22 17:05:32,443 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size_factor": 0.08,
      "exploration_radius_factor": 0.15,
      "local_search_depth": 2,
      "adaptive_step_size": true
    },
    "exploit": {
      "crossover_rate": 0.7,
      "mutation_rate": 0.05,
      "local_search_depth": 5,
      "elitism": true,
      "elitism_proportion": 0.1
    }
  }
}
```

2025-06-22 17:05:32,445 - __main__ - INFO - 开始进化阶段
2025-06-22 17:05:32,445 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:05:32,446 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:05:32,446 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [1, 8, 15] and ending with [64, 65, 66]
- Cost: 30347.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8685064935064936
- Unexplored regions: 334 opportunity regions
- Opportunity regions: 334 regions
- Difficult regions to avoid: 340 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:05:32,446 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:05:37,182 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    1, 2, 3, 9, 16, 23, 24, 31, 38, 45, 46, 53, 60, 61, 62, 63, 64, 65, 66
  ],
  "modification_strategy": "Heavily biased towards diversity. Focused on exploring regions that are currently unexplored or under-explored in the existing path. Prioritized nodes located in opportunity regions and tried to avoid difficult regions while keeping a reasonable path length. The first few nodes follow the existing path to establish a baseline and then diverge to create a more explorative route. The rest of the path is built to maximize diversity.",
  "targeted_regions": [
    "Opportunity regions around node 9, 23, 31, 38, 45, 53, 60",
    "Region clusters characterized by high exploration potential",
    "Regions with low visitation frequency (to enhance diversity)"
  ]
}
```
2025-06-22 17:05:37,182 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:05:37,182 - ExplorationExpert - INFO - 探索路径生成完成，成本: 8011.0, 路径: [1, 2, 3, 9, 16, 23, 24, 31, 38, 45, 46, 53, 60, 61, 62, 63, 64, 65, 66]
2025-06-22 17:05:37,182 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [1, 2, 3, 9, 16, 23, 24, 31, 38, 45, 46, 53, 60, 61, 62, 63, 64, 65, 66], 'cur_cost': 8011.0}
2025-06-22 17:05:37,182 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 17:05:37,182 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:05:37,185 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 38 nodes, starting with [24, 31, 35] and ending with [43, 48, 42]
- Cost: 20448.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8875219683655536
- Unexplored regions: 334 opportunity regions
- Opportunity regions: 334 regions
- Difficult regions to avoid: 340 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:05:37,185 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:05:43,254 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    24, 1, 17, 29, 19, 8, 33, 4, 14, 25, 36, 2, 40, 10, 20, 26, 38, 11, 39, 3, 44, 13, 47, 7, 30, 16, 32, 21, 41, 5, 12, 27, 37, 45, 6, 18, 46, 9, 42
  ],
  "modification_strategy": "This strategy focuses on a highly diverse path by deviating significantly from the original path early and frequently. It prioritizes exploration of less visited regions and opportunities identified in the search context.  The path attempts to weave through several previously unexplored or sparsely explored opportunity regions, taking into account the difficult regions to avoid. The node selection favors choices that maximize the change in direction at each step to promote further diversification while allowing some quality focus.",
  "targeted_regions": "Opportunity regions identified in the search context. Specifically targeting regions identified as having lower visit counts and higher potential for discovery. Regions 1, 4, 17, 19, 29, 33, 8, 14, 25, 36, 2, 40, 10, 20, 26, 38, 11, 39, 3, 44, 13, 47, 7, 30, 16, 32, 21, 41, 5, 12, 27, 37, 45, 6, 18, 46, 9, 42 were prioritized for inclusion, aiming to maximize exploration of currently less explored or difficult areas, while aiming to avoid some of the riskier regions."
}
```
2025-06-22 17:05:43,254 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:05:43,256 - ExplorationExpert - INFO - 探索路径生成完成，成本: 66635.0, 路径: [24, 1, 17, 29, 19, 8, 33, 4, 14, 25, 36, 2, 40, 10, 20, 26, 38, 11, 39, 3, 44, 13, 47, 7, 30, 16, 32, 21, 41, 5, 12, 27, 37, 45, 6, 18, 46, 9, 42]
2025-06-22 17:05:43,256 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [24, 1, 17, 29, 19, 8, 33, 4, 14, 25, 36, 2, 40, 10, 20, 26, 38, 11, 39, 3, 44, 13, 47, 7, 30, 16, 32, 21, 41, 5, 12, 27, 37, 45, 6, 18, 46, 9, 42], 'cur_cost': 66635.0}
2025-06-22 17:05:43,256 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 17:05:43,256 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:05:43,256 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 69 nodes, starting with [59, 62, 47] and ending with [66, 41, 42]
- Cost: 52618.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8947368421052632
- Unexplored regions: 334 opportunity regions
- Opportunity regions: 334 regions
- Difficult regions to avoid: 340 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:05:43,256 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:05:49,260 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    59, 62, 22, 17, 8, 10, 11, 20, 27, 33, 35, 40, 45, 50, 54, 55, 58, 60, 65, 67, 69, 71, 73, 75, 77, 79, 81, 83, 85, 87, 89, 91, 93, 95, 97, 99, 101, 103, 105, 107, 109, 111, 113, 115, 117, 119, 121, 123, 125, 127, 129, 131, 133, 135, 137, 139, 141, 143, 145, 147, 149, 151, 48, 49, 42
  ],
  "modification_strategy": "Heavily prioritizing exploration of unexplored regions and nodes within these areas. The path deviates significantly from the original by exploring nodes in previously untouched sections of the search space.  We focus on nodes furthest away from the original path, aiming to maximize diversity by utilizing the diversity weight and risk tolerance.",
  "targeted_regions": "Focusing on exploring the larger number of opportunity regions identified within the search context. The strategy prioritizes nodes within the unexplored regions, and those geographically separate and distinct from the original path's trajectory. Specific regions targeted include those identified with higher potential returns, indicated in the opportunity regions data and avoiding the difficult regions."
}
```
2025-06-22 17:05:49,260 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:05:49,260 - ExplorationExpert - INFO - 探索路径生成完成，成本: nan, 路径: [59, 62, 22, 17, 8, 10, 11, 20, 27, 33, 35, 40, 45, 50, 54, 55, 58, 60, 65, 67, 69, 71, 73, 75, 77, 79, 81, 83, 85, 87, 89, 91, 93, 95, 97, 99, 101, 103, 105, 107, 109, 111, 113, 115, 117, 119, 121, 123, 125, 127, 129, 131, 133, 135, 137, 139, 141, 143, 145, 147, 149, 151, 48, 49, 42]
2025-06-22 17:05:49,260 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [59, 62, 22, 17, 8, 10, 11, 20, 27, 33, 35, 40, 45, 50, 54, 55, 58, 60, 65, 67, 69, 71, 73, 75, 77, 79, 81, 83, 85, 87, 89, 91, 93, 95, 97, 99, 101, 103, 105, 107, 109, 111, 113, 115, 117, 119, 121, 123, 125, 127, 129, 131, 133, 135, 137, 139, 141, 143, 145, 147, 149, 151, 48, 49, 42], 'cur_cost': nan}
2025-06-22 17:05:49,260 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 17:05:49,260 - ExplorationExpert - INFO - 开始生成探索路径
