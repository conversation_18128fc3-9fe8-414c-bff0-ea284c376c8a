"""
增强的策略选择专家
实现强化学习机制、多层次策略分配和历史策略效果学习
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Any
import json
import copy
from collections import defaultdict, deque


class AdaptiveStrategyExpert:
    """自适应策略选择专家，集成强化学习和多层次决策"""
    
    def __init__(self, learning_rate=0.1, memory_size=50):
        self.logger = logging.getLogger(__name__)
        self.learning_rate = learning_rate
        self.memory_size = memory_size
        
        # 策略性能历史记录
        self.strategy_performance_history = {
            'explore': deque(maxlen=memory_size),
            'exploit': deque(maxlen=memory_size),
            'balance': deque(maxlen=memory_size)
        }
        
        # 策略价值估计
        self.strategy_values = {
            'explore': 0.5,
            'exploit': 0.5,
            'balance': 0.5
        }
        
        # 上下文相关的策略性能
        self.context_strategy_performance = defaultdict(lambda: defaultdict(list))
        
        # 个体特征到策略的映射学习
        self.individual_strategy_mapping = {}
        
    def analyze(self, landscape_report, populations, iteration, strategy_feedback=None):
        """主要分析函数，返回策略分配和相关信息"""
        self.logger.info("开始自适应策略分析")
        
        # 解析景观报告
        landscape_data = self._parse_landscape_report(landscape_report)
        
        # 更新策略性能（如果有反馈）
        if strategy_feedback:
            self._update_strategy_performance(strategy_feedback)
        
        # 多层次策略分配
        strategy_assignment = self._hierarchical_strategy_assignment(
            populations, landscape_data, iteration
        )
        
        # 生成策略报告
        strategy_report = self._generate_strategy_report(
            strategy_assignment, landscape_data, populations
        )
        
        self.logger.info(f"策略分配完成: {strategy_assignment}")
        return strategy_assignment, strategy_report
    
    def _parse_landscape_report(self, landscape_report):
        """解析景观分析报告"""
        if isinstance(landscape_report, str):
            try:
                import re
                json_match = re.search(r'```json\s*({[\s\S]*?})\s*```', landscape_report)
                if json_match:
                    landscape_data = json.loads(json_match.group(1))
                else:
                    landscape_data = self._create_default_landscape_data()
            except Exception:
                landscape_data = self._create_default_landscape_data()
        elif isinstance(landscape_report, dict):
            landscape_data = landscape_report
        else:
            landscape_data = self._create_default_landscape_data()
        
        return landscape_data
    
    def _create_default_landscape_data(self):
        """创建默认的景观数据"""
        return {
            "search_difficulty": "medium",
            "population_status": {"diversity": 0.5, "convergence": 0.5, "phase": "exploration"},
            "recommended_action": {"focus": "balance", "intensity": 0.5},
            "target_regions": {"opportunities": [], "avoid": []}
        }
    
    def _update_strategy_performance(self, strategy_feedback):
        """更新策略性能记录"""
        try:
            if isinstance(strategy_feedback, dict):
                for strategy, performance in strategy_feedback.items():
                    if strategy in self.strategy_performance_history:
                        success_rate = performance.get('success_rate', 0.5)
                        improvement = performance.get('avg_improvement', 0)
                        
                        # 记录性能
                        self.strategy_performance_history[strategy].append({
                            'success_rate': success_rate,
                            'improvement': improvement,
                            'timestamp': len(self.strategy_performance_history[strategy])
                        })
                        
                        # 更新策略价值（指数移动平均）
                        old_value = self.strategy_values[strategy]
                        self.strategy_values[strategy] = (
                            old_value * (1 - self.learning_rate) + 
                            success_rate * self.learning_rate
                        )
        except Exception as e:
            self.logger.warning(f"更新策略性能时出错: {e}")
    
    def _hierarchical_strategy_assignment(self, populations, landscape_data, iteration):
        """分层策略分配：全局 -> 群体 -> 个体"""
        
        # 1. 全局策略决策
        global_strategy = self._determine_global_strategy(landscape_data, iteration)
        
        # 2. 群体聚类和策略分配
        clusters = self._cluster_population(populations)
        cluster_strategies = self._assign_cluster_strategies(clusters, global_strategy, landscape_data)
        
        # 3. 个体策略细化
        individual_strategies = []
        for i, individual in enumerate(populations):
            cluster_id = self._get_individual_cluster(individual, clusters)
            base_strategy = cluster_strategies.get(cluster_id, global_strategy)
            final_strategy = self._refine_individual_strategy(individual, base_strategy, landscape_data)
            individual_strategies.append(final_strategy)
        
        return individual_strategies
    
    def _determine_global_strategy(self, landscape_data, iteration):
        """确定全局策略方向"""
        population_status = landscape_data.get("population_status", {})
        recommended_action = landscape_data.get("recommended_action", {})
        
        diversity = population_status.get("diversity", 0.5)
        convergence = population_status.get("convergence", 0.5)
        recommended_focus = recommended_action.get("focus", "balance")
        
        # 基于策略价值和当前状态决策
        strategy_scores = {}
        
        # 探索策略评分
        explore_score = self.strategy_values['explore']
        if diversity < 0.3:  # 多样性低，需要探索
            explore_score += 0.3
        if recommended_focus == "explore":
            explore_score += 0.2
        
        # 开发策略评分
        exploit_score = self.strategy_values['exploit']
        if convergence < 0.3:  # 收敛性低，需要开发
            exploit_score += 0.3
        if recommended_focus == "exploit":
            exploit_score += 0.2
        
        # 平衡策略评分
        balance_score = self.strategy_values['balance']
        if 0.3 <= diversity <= 0.7 and 0.3 <= convergence <= 0.7:
            balance_score += 0.2
        
        strategy_scores = {
            'explore': explore_score,
            'exploit': exploit_score,
            'balance': balance_score
        }
        
        # 选择最高评分的策略
        global_strategy = max(strategy_scores, key=strategy_scores.get)
        
        self.logger.info(f"全局策略决策: {global_strategy}, 评分: {strategy_scores}")
        return global_strategy
    
    def _cluster_population(self, populations):
        """对种群进行聚类分析"""
        if not populations:
            return {}
        
        clusters = {}
        
        # 基于成本进行简单聚类
        costs = [p.get("cur_cost", float('inf')) for p in populations]
        if not costs:
            return {i: 0 for i in range(len(populations))}
        
        # 将个体分为三类：精英、中等、较差
        sorted_indices = sorted(range(len(costs)), key=lambda i: costs[i])
        n = len(sorted_indices)
        
        for i, idx in enumerate(sorted_indices):
            if i < n // 3:
                clusters[idx] = 0  # 精英群体
            elif i < 2 * n // 3:
                clusters[idx] = 1  # 中等群体
            else:
                clusters[idx] = 2  # 较差群体
        
        return clusters
    
    def _assign_cluster_strategies(self, clusters, global_strategy, landscape_data):
        """为不同聚类分配策略"""
        cluster_strategies = {}
        
        # 根据全局策略和聚类特征分配策略
        if global_strategy == "explore":
            cluster_strategies = {0: "exploit", 1: "explore", 2: "explore"}  # 精英开发，其他探索
        elif global_strategy == "exploit":
            cluster_strategies = {0: "exploit", 1: "exploit", 2: "explore"}  # 前两类开发，最差探索
        else:  # balance
            cluster_strategies = {0: "exploit", 1: "balance", 2: "explore"}  # 平衡分配
        
        return cluster_strategies
    
    def _get_individual_cluster(self, individual, clusters):
        """获取个体所属的聚类"""
        # 这里需要根据individual在populations中的索引来查找
        # 简化实现，返回默认聚类
        return 1  # 默认中等群体
    
    def _refine_individual_strategy(self, individual, base_strategy, landscape_data):
        """细化个体策略"""
        # 基于个体特征进行策略微调
        cost = individual.get("cur_cost", float('inf'))
        
        # 如果个体表现特别好或特别差，可能需要调整策略
        if base_strategy == "balance":
            # 对于平衡策略，根据个体表现进行微调
            if hasattr(self, '_population_cost_stats'):
                mean_cost = self._population_cost_stats.get('mean', cost)
                if cost < mean_cost * 0.9:  # 表现很好
                    return "exploit"
                elif cost > mean_cost * 1.1:  # 表现较差
                    return "explore"
        
        return base_strategy
    
    def _generate_strategy_report(self, strategy_assignment, landscape_data, populations):
        """生成策略分配报告"""
        strategy_counts = {}
        for strategy in strategy_assignment:
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        
        total = len(strategy_assignment)
        strategy_ratios = {k: v/total for k, v in strategy_counts.items()}
        
        report = {
            "global_strategy_values": self.strategy_values.copy(),
            "strategy_assignment": strategy_assignment,
            "strategy_distribution": strategy_counts,
            "strategy_ratios": strategy_ratios,
            "landscape_context": landscape_data,
            "assignment_rationale": self._generate_rationale(strategy_ratios, landscape_data)
        }
        
        return report
    
    def _generate_rationale(self, strategy_ratios, landscape_data):
        """生成策略分配的理由说明"""
        rationale_parts = []
        
        population_status = landscape_data.get("population_status", {})
        diversity = population_status.get("diversity", 0.5)
        
        if strategy_ratios.get("explore", 0) > 0.5:
            rationale_parts.append("高探索比例以增加多样性")
        elif strategy_ratios.get("exploit", 0) > 0.5:
            rationale_parts.append("高开发比例以改进解质量")
        else:
            rationale_parts.append("平衡探索开发以维持搜索效率")
        
        if diversity < 0.3:
            rationale_parts.append("多样性低需要更多探索")
        elif diversity > 0.7:
            rationale_parts.append("多样性高可以专注开发")
        
        return "; ".join(rationale_parts)
    
    def get_performance_stats(self):
        """获取策略性能统计信息"""
        stats = {}
        for strategy, history in self.strategy_performance_history.items():
            if history:
                recent_performance = list(history)[-10:]  # 最近10次
                avg_success = np.mean([p['success_rate'] for p in recent_performance])
                avg_improvement = np.mean([p['improvement'] for p in recent_performance])
                stats[strategy] = {
                    'avg_success_rate': avg_success,
                    'avg_improvement': avg_improvement,
                    'sample_count': len(recent_performance),
                    'current_value': self.strategy_values[strategy]
                }
            else:
                stats[strategy] = {
                    'avg_success_rate': 0.5,
                    'avg_improvement': 0.0,
                    'sample_count': 0,
                    'current_value': self.strategy_values[strategy]
                }
        
        return stats
