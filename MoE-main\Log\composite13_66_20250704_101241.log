2025-07-04 10:12:41,128 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-04 10:12:41,128 - __main__ - INFO - 开始分析阶段
2025-07-04 10:12:41,128 - StatsExpert - INFO - 开始统计分析
2025-07-04 10:12:41,148 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9945.0, 'max': 119963.0, 'mean': 79037.7, 'std': 45634.199289677475}, 'diversity': 0.9175084175084174, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 10:12:41,149 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9945.0, 'max': 119963.0, 'mean': 79037.7, 'std': 45634.199289677475}, 'diversity_level': 0.9175084175084174, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-04 10:12:41,149 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-04 10:12:41,149 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-04 10:12:41,149 - PathExpert - INFO - 开始路径结构分析
2025-07-04 10:12:41,157 - PathExpert - INFO - 路径结构分析完成
2025-07-04 10:12:41,157 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (16, 18), 'frequency': 0.5, 'avg_cost': 17.0}, {'edge': (29, 32), 'frequency': 0.5, 'avg_cost': 24.0}], 'common_subpaths': [{'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (26, 36, 35), 'frequency': 0.3}, {'subpath': (36, 35, 28), 'frequency': 0.3}, {'subpath': (35, 28, 30), 'frequency': 0.3}, {'subpath': (28, 30, 34), 'frequency': 0.3}, {'subpath': (30, 34, 33), 'frequency': 0.3}, {'subpath': (34, 33, 31), 'frequency': 0.3}, {'subpath': (33, 31, 24), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(16, 23)', 'frequency': 0.4}, {'edge': '(16, 18)', 'frequency': 0.5}, {'edge': '(20, 21)', 'frequency': 0.4}, {'edge': '(28, 30)', 'frequency': 0.4}, {'edge': '(31, 33)', 'frequency': 0.4}, {'edge': '(29, 32)', 'frequency': 0.5}, {'edge': '(55, 61)', 'frequency': 0.4}, {'edge': '(52, 65)', 'frequency': 0.4}, {'edge': '(38, 45)', 'frequency': 0.4}, {'edge': '(40, 49)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(14, 15)', 'frequency': 0.2}, {'edge': '(13, 19)', 'frequency': 0.2}, {'edge': '(13, 20)', 'frequency': 0.3}, {'edge': '(21, 27)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(3, 32)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 7)', 'frequency': 0.2}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(5, 9)', 'frequency': 0.2}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(0, 55)', 'frequency': 0.2}, {'edge': '(53, 61)', 'frequency': 0.2}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.2}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.2}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.2}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.3}, {'edge': '(41, 46)', 'frequency': 0.2}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.2}, {'edge': '(40, 43)', 'frequency': 0.3}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(63, 65)', 'frequency': 0.2}, {'edge': '(10, 15)', 'frequency': 0.2}, {'edge': '(15, 22)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(46, 48)', 'frequency': 0.3}, {'edge': '(41, 42)', 'frequency': 0.3}, {'edge': '(49, 63)', 'frequency': 0.2}, {'edge': '(23, 54)', 'frequency': 0.2}, {'edge': '(27, 56)', 'frequency': 0.2}, {'edge': '(1, 64)', 'frequency': 0.2}, {'edge': '(34, 57)', 'frequency': 0.2}, {'edge': '(8, 31)', 'frequency': 0.2}, {'edge': '(17, 21)', 'frequency': 0.2}, {'edge': '(43, 46)', 'frequency': 0.2}, {'edge': '(48, 60)', 'frequency': 0.2}, {'edge': '(11, 20)', 'frequency': 0.2}, {'edge': '(36, 58)', 'frequency': 0.2}, {'edge': '(7, 35)', 'frequency': 0.2}, {'edge': '(7, 28)', 'frequency': 0.2}, {'edge': '(38, 61)', 'frequency': 0.2}, {'edge': '(15, 51)', 'frequency': 0.2}, {'edge': '(13, 18)', 'frequency': 0.2}, {'edge': '(10, 44)', 'frequency': 0.3}, {'edge': '(24, 39)', 'frequency': 0.2}, {'edge': '(39, 54)', 'frequency': 0.2}, {'edge': '(6, 59)', 'frequency': 0.2}, {'edge': '(42, 65)', 'frequency': 0.2}, {'edge': '(12, 33)', 'frequency': 0.2}, {'edge': '(35, 49)', 'frequency': 0.2}, {'edge': '(10, 23)', 'frequency': 0.2}, {'edge': '(5, 61)', 'frequency': 0.2}, {'edge': '(22, 45)', 'frequency': 0.2}, {'edge': '(11, 17)', 'frequency': 0.3}, {'edge': '(50, 56)', 'frequency': 0.2}, {'edge': '(24, 41)', 'frequency': 0.2}, {'edge': '(46, 54)', 'frequency': 0.2}, {'edge': '(20, 39)', 'frequency': 0.3}, {'edge': '(1, 36)', 'frequency': 0.2}, {'edge': '(34, 59)', 'frequency': 0.2}, {'edge': '(5, 33)', 'frequency': 0.2}, {'edge': '(1, 29)', 'frequency': 0.2}, {'edge': '(2, 49)', 'frequency': 0.2}, {'edge': '(31, 40)', 'frequency': 0.2}, {'edge': '(38, 47)', 'frequency': 0.2}, {'edge': '(34, 55)', 'frequency': 0.2}, {'edge': '(30, 45)', 'frequency': 0.2}, {'edge': '(26, 60)', 'frequency': 0.2}, {'edge': '(0, 35)', 'frequency': 0.2}, {'edge': '(16, 33)', 'frequency': 0.2}, {'edge': '(52, 54)', 'frequency': 0.2}, {'edge': '(0, 44)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [30, 45, 10, 38, 58, 27, 41], 'cost': 15058.0, 'size': 7}, {'region': [54, 46, 53, 41, 56, 35], 'cost': 14190.0, 'size': 6}, {'region': [43, 57, 26, 60, 29], 'cost': 11702.0, 'size': 5}, {'region': [65, 51, 61, 49, 2], 'cost': 10984.0, 'size': 5}, {'region': [59, 44, 24, 64, 39], 'cost': 10714.0, 'size': 5}]}
2025-07-04 10:12:41,158 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-04 10:12:41,158 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-04 10:12:41,159 - EliteExpert - INFO - 开始精英解分析
2025-07-04 10:12:41,159 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-04 10:12:41,159 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-07-04 10:12:41,159 - __main__ - INFO - 更新共享数据: elite_features
2025-07-04 10:12:41,159 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 10:12:41,637 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 10:12:41,637 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9945.0, mean 79037.7, max 119963.0, std 45634.199289677475
- diversity: 0.9175084175084174
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (16, 18), 'frequency': 0.5, 'avg_cost': 17.0}, {'edge': (29, 32), 'frequency': 0.5, 'avg_cost': 24.0}]
- common_subpaths_sample: [{'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [30, 45, 10, 38, 58, 27, 41], 'cost': 15058.0, 'size': 7}, {'region': [54, 46, 53, 41, 56, 35], 'cost': 14190.0, 'size': 6}, {'region': [43, 57, 26, 60, 29], 'cost': 11702.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state: (Object. MUST contain a numerical 'diversity_score' key alongside any textual descriptions)
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "balance"}
}
```

2025-07-04 10:12:41,638 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:12:46,157 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.75,
    "modality": "likely multi-modal",
    "deceptiveness": "moderate"
  },
  "population_state": {
    "diversity_score": 0.9175,
    "description": "High diversity, low convergence. The population is exploring a wide range of solutions.  No clustering information available."
  },
  "difficult_regions": [
    {
      "region": [30, 45, 10, 38, 58, 27, 41],
      "spatial_reasoning": "Corresponds to potentially long edges within a difficult region, aligning with the structural signal and high cost."
    },
    {
      "region": [54, 46, 53, 41, 56, 35],
      "spatial_reasoning": "Another region indicated by a structural signal with relatively high cost, possibly containing long edges/poor connections."
    },
    {
      "region": [43, 57, 26, 60, 29],
      "spatial_reasoning": "Third difficult region identified through structural signals with high cost."
    }
  ],
  "opportunity_regions": [
      {
          "cells": [2, 5, 8],
          "spatial_reasoning": "High density cells (12-14 nodes each) indicate potential areas for exploitation and local improvement. More data needed for deeper insight into node connections."
      }
  ],
  "evolution_phase": "Exploration",
  "evolution_direction": {
    "recommended_focus": "exploration & exploitation",
    "operator_suggestions": "1. **Exploration:** Increase mutation rate slightly to continue exploring diverse solutions, avoiding premature convergence. 2. **Exploitation:** Apply local search operators (2-opt, 3-opt) to the high-density cells identified in opportunity regions and the difficult regions to refine solutions within promising areas and escape local optima within difficult regions."
  }
}
```
2025-07-04 10:12:46,158 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 10:12:46,158 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 0.75, 'modality': 'likely multi-modal', 'deceptiveness': 'moderate'}, 'population_state': {'diversity_score': 0.9175, 'description': 'High diversity, low convergence. The population is exploring a wide range of solutions.  No clustering information available.'}, 'difficult_regions': [{'region': [30, 45, 10, 38, 58, 27, 41], 'spatial_reasoning': 'Corresponds to potentially long edges within a difficult region, aligning with the structural signal and high cost.'}, {'region': [54, 46, 53, 41, 56, 35], 'spatial_reasoning': 'Another region indicated by a structural signal with relatively high cost, possibly containing long edges/poor connections.'}, {'region': [43, 57, 26, 60, 29], 'spatial_reasoning': 'Third difficult region identified through structural signals with high cost.'}], 'opportunity_regions': [{'cells': [2, 5, 8], 'spatial_reasoning': 'High density cells (12-14 nodes each) indicate potential areas for exploitation and local improvement. More data needed for deeper insight into node connections.'}], 'evolution_phase': 'Exploration', 'evolution_direction': {'recommended_focus': 'exploration & exploitation', 'operator_suggestions': '1. **Exploration:** Increase mutation rate slightly to continue exploring diverse solutions, avoiding premature convergence. 2. **Exploitation:** Apply local search operators (2-opt, 3-opt) to the high-density cells identified in opportunity regions and the difficult regions to refine solutions within promising areas and escape local optima within difficult regions.'}}
2025-07-04 10:12:46,158 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-04 10:12:46,158 - __main__ - INFO - 分析阶段完成
2025-07-04 10:12:46,158 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.75, 'modality': 'likely multi-modal', 'deceptiveness': 'moderate'}, 'population_state': {'diversity_score': 0.9175, 'description': 'High diversity, low convergence. The population is exploring a wide range of solutions.  No clustering information available.'}, 'difficult_regions': [{'region': [30, 45, 10, 38, 58, 27, 41], 'spatial_reasoning': 'Corresponds to potentially long edges within a difficult region, aligning with the structural signal and high cost.'}, {'region': [54, 46, 53, 41, 56, 35], 'spatial_reasoning': 'Another region indicated by a structural signal with relatively high cost, possibly containing long edges/poor connections.'}, {'region': [43, 57, 26, 60, 29], 'spatial_reasoning': 'Third difficult region identified through structural signals with high cost.'}], 'opportunity_regions': [{'cells': [2, 5, 8], 'spatial_reasoning': 'High density cells (12-14 nodes each) indicate potential areas for exploitation and local improvement. More data needed for deeper insight into node connections.'}], 'evolution_phase': 'Exploration', 'evolution_direction': {'recommended_focus': 'exploration & exploitation', 'operator_suggestions': '1. **Exploration:** Increase mutation rate slightly to continue exploring diverse solutions, avoiding premature convergence. 2. **Exploitation:** Apply local search operators (2-opt, 3-opt) to the high-density cells identified in opportunity regions and the difficult regions to refine solutions within promising areas and escape local optima within difficult regions.'}}
2025-07-04 10:12:46,161 - __main__ - INFO - 开始策略分配阶段
2025-07-04 10:12:46,161 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 10:12:46,161 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.9175
  • best_cost: 9945.0
  • mean_cost: 79037.7
- Search-space: ruggedness 0.75, modality likely multi-modal, deceptiveness moderate
- Evolution phase: Exploration
- Landscape focus suggestion: exploration & exploitation
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 3): 3 items, first 2: [{'region': [30, 45, 10, 38, 58, 27, 41], 'spatial_reasoning': 'Corresponds to potentially long edges within a difficult region, aligning with the structural signal and high cost.'}, {'region': [54, 46, 53, 41, 56, 35], 'spatial_reasoning': 'Another region indicated by a structural signal with relatively high cost, possibly containing long edges/poor connections.'}]
- Opportunity regions (# 1): [{'cells': [2, 5, 8], 'spatial_reasoning': 'High density cells (12-14 nodes each) indicate potential areas for exploitation and local improvement. More data needed for deeper insight into node connections.'}]
- Last-iteration feedback: N/A

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 10:12:46,161 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 10:12:46,161 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:12:48,427 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High population diversity, exploration phase. Identify long edges & optimize dense regions, hence mostly explore."
}
```
2025-07-04 10:12:48,427 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 10:12:48,427 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 10:12:48,427 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High population diversity, exploration phase. Identify long edges & optimize dense regions, hence mostly explore."
}
```
2025-07-04 10:12:48,429 - __main__ - INFO - 策略分配阶段完成
2025-07-04 10:12:48,429 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 10:12:48,429 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High population diversity, exploration phase. Identify long edges & optimize dense regions, hence mostly explore."
}
```
2025-07-04 10:12:48,429 - __main__ - INFO - 开始进化阶段
2025-07-04 10:12:48,429 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 10:12:48,429 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:12:48,429 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10030.0 (rank 10.0%)
- Population diversity: 0.7415384615384616
- Evolution phase: Exploration
- Landscape recommends focus: exploration & exploitation
- Opportunity regions (sample): {'cells': [2, 5
- Difficult regions to avoid (sample): {'region': [30,, {'region': [54,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:12:48,429 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:12:48,429 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:12:51,632 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0],
  "targeted_regions": "Opportunity regions 2 and 5, avoiding region 30 and 54.",
  "strategy_comment": "Prioritizes opportunity regions at the beginning, then loops to avoid difficulties and preserve diversity."
}
```
2025-07-04 10:12:51,632 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:12:51,632 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17693.0, 路径: [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0]
2025-07-04 10:12:51,632 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}
2025-07-04 10:12:51,632 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 10:12:51,632 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:12:51,632 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10097.0 (rank 10.0%)
- Population diversity: 0.7907692307692308
- Evolution phase: Exploration
- Landscape recommends focus: exploration & exploitation
- Opportunity regions (sample): {'cells': [2, 5
- Difficult regions to avoid (sample): {'region': [30,, {'region': [54,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:12:51,632 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:12:51,632 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:12:54,758 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0],
  "targeted_regions": "Opportunity regions (2, 5) and avoids difficult regions (30, 54).",
  "strategy_comment": "Prioritizes opportunity regions and adjacent low-density cells, circumventing difficult areas to increase diversity."
}
```
2025-07-04 10:12:54,758 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:12:54,758 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17693.0, 路径: [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0]
2025-07-04 10:12:54,758 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}
2025-07-04 10:12:54,758 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 10:12:54,758 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:12:54,758 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9945.0 (rank 0.0%)
- Population diversity: 0.7707692307692308
- Evolution phase: Exploration
- Landscape recommends focus: exploration & exploitation
- Opportunity regions (sample): {'cells': [2, 5
- Difficult regions to avoid (sample): {'region': [30,, {'region': [54,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:12:54,758 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:12:54,758 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:12:58,092 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0],
  "targeted_regions": "Opportunity regions: 2, 5. Avoid: 30, 54.",
  "strategy_comment": "Prioritizes exploration of opportunity regions (2, 5) while avoiding the difficult ones (30, 54)."
}
```
2025-07-04 10:12:58,092 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:12:58,092 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17693.0, 路径: [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0]
2025-07-04 10:12:58,092 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}
2025-07-04 10:12:58,092 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 10:12:58,092 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:12:58,092 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100953.0 (rank 50.0%)
- Population diversity: 0.7
- Evolution phase: Exploration
- Landscape recommends focus: exploration & exploitation
- Opportunity regions (sample): {'cells': [2, 5
- Difficult regions to avoid (sample): {'region': [30,, {'region': [54,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:12:58,092 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:12:58,092 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:13:01,452 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 5, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 54, 30, 4],
  "targeted_regions": "opportunity regions(2,5), avoid difficult region 30, 54",
  "strategy_comment": "Prioritizes opportunity cells. Avoids obstacles. Builds from existing high performing sections for efficiency."
}
```
2025-07-04 10:13:01,453 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:13:01,454 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15406.0, 路径: [0, 2, 5, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 54, 30, 4, 3]
2025-07-04 10:13:01,454 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 5, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 54, 30, 4, 3], 'cur_cost': 15406.0}
2025-07-04 10:13:01,454 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-04 10:13:01,455 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:13:01,456 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:13:01,457 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 93149.0
2025-07-04 10:13:04,121 - ExploitationExpert - INFO - res_population_num: 1
2025-07-04 10:13:04,121 - ExploitationExpert - INFO - res_population_costs: [102364]
2025-07-04 10:13:04,124 - ExploitationExpert - INFO - res_populations: [array([ 0, 38, 32, 29, 17,  7, 60, 48,  9, 52, 54, 58, 36, 24, 46, 22, 45,
       50,  3, 61,  5, 14, 37, 39, 25, 28, 40, 13, 47,  1, 15, 53, 44, 10,
       23, 55, 21, 27,  8, 51, 57, 41, 30,  2, 35, 49, 18, 20, 43, 34, 16,
       64, 63, 19,  4, 12, 33, 42, 65, 62, 56, 59,  6, 31, 11, 26],
      dtype=int64)]
2025-07-04 10:13:04,124 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:13:04,124 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [0, 2, 5, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 54, 30, 4, 3], 'cur_cost': 15406.0}, {'tour': array([13, 30,  6, 34, 21,  5,  2,  7, 18, 62, 15,  0, 10,  1,  4, 32,  3,
       55, 40, 48, 59, 31, 64, 57, 44, 39, 42, 41, 16, 65, 27, 37, 45, 46,
       63, 52, 26, 14, 17,  9, 56, 43, 51, 58, 47, 19, 20, 60,  8, 61, 38,
       22, 53, 33, 50, 25, 49, 36, 11, 54, 29, 23, 24, 28, 35, 12]), 'cur_cost': 93149.0}, {'tour': [10, 29, 2, 11, 17, 56, 50, 37, 41, 24, 9, 60, 61, 54, 46, 25, 35, 49, 40, 64, 20, 39, 58, 30, 31, 8, 48, 36, 1, 16, 18, 4, 32, 0, 27, 13, 19, 28, 7, 59, 34, 57, 15, 22, 21, 65, 52, 63, 43, 53, 14, 55, 6, 3, 12, 51, 5, 33, 62, 26, 23, 47, 45, 38, 42, 44], 'cur_cost': 99978.0}, {'tour': [50, 29, 1, 52, 3, 58, 62, 13, 37, 23, 10, 15, 65, 51, 61, 49, 2, 28, 5, 57, 7, 14, 63, 42, 31, 40, 6, 43, 19, 54, 46, 53, 41, 56, 35, 17, 47, 38, 60, 33, 12, 9, 26, 4, 55, 34, 11, 45, 30, 8, 18, 32, 16, 0, 22, 48, 21, 59, 44, 24, 64, 39, 20, 27, 36, 25], 'cur_cost': 119963.0}, {'tour': [54, 45, 16, 23, 28, 30, 59, 19, 49, 50, 63, 22, 57, 14, 52, 48, 56, 13, 18, 39, 27, 62, 8, 64, 7, 29, 58, 42, 9, 32, 26, 60, 3, 20, 1, 40, 25, 65, 35, 0, 5, 21, 53, 10, 61, 38, 31, 2, 37, 24, 41, 55, 34, 51, 15, 33, 4, 47, 44, 46, 12, 43, 11, 17, 36, 6], 'cur_cost': 113359.0}, {'tour': [7, 41, 46, 43, 21, 17, 11, 63, 49, 42, 12, 5, 61, 2, 26, 27, 56, 32, 29, 64, 1, 36, 20, 39, 33, 16, 13, 55, 3, 10, 18, 37, 8, 60, 40, 52, 54, 23, 31, 59, 6, 58, 51, 48, 28, 53, 9, 62, 38, 47, 14, 4, 34, 50, 57, 24, 22, 45, 25, 19, 15, 35, 0, 44, 30, 65], 'cur_cost': 99490.0}, {'tour': [21, 20, 11, 4, 48, 25, 62, 64, 12, 15, 47, 2, 49, 14, 61, 40, 31, 43, 57, 26, 60, 29, 1, 59, 34, 28, 52, 54, 39, 24, 56, 50, 13, 36, 55, 37, 9, 8, 6, 53, 3, 44, 0, 17, 30, 45, 10, 38, 58, 27, 41, 42, 65, 63, 51, 23, 19, 22, 32, 46, 18, 16, 33, 5, 7, 35], 'cur_cost': 112572.0}]
2025-07-04 10:13:04,126 - ExploitationExpert - INFO - 局部搜索耗时: 2.67秒
2025-07-04 10:13:04,126 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-04 10:13:04,126 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-07-04 10:13:04,126 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 10:13:04,127 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:13:04,127 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 99978.0 (rank 60.0%)
- Population diversity: 0.7107692307692308
- Evolution phase: Exploration
- Landscape recommends focus: exploration & exploitation
- Opportunity regions (sample): {'cells': [2, 5
- Difficult regions to avoid (sample): {'region': [30,, {'region': [54,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:13:04,128 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:13:04,128 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:13:07,252 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 3, 4],
  "targeted_regions": "opportunity regions (2,5), avoid region 30,54 and sparse cells around opportunity regions",
  "strategy_comment": "Prioritizes opportunity regions and adjacent sparse cells while avoiding identified difficult areas to increase path diversity."
}
```
2025-07-04 10:13:07,253 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:13:07,253 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11392.0, 路径: [2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 3, 4]
2025-07-04 10:13:07,254 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 3, 4], 'cur_cost': 11392.0}
2025-07-04 10:13:07,254 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 10:13:07,254 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 10:13:07,255 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 119963.0 (rank 90.0%)
- Population diversity: 0.6430769230769231
- Evolution phase: Exploration
- Landscape recommends focus: exploration & exploitation
- Opportunity regions (sample): {'cells': [2, 5
- Difficult regions to avoid (sample): {'region': [30,, {'region': [54,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 10:13:07,255 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 10:13:07,256 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 10:13:10,276 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0],
  "targeted_regions": "opportunity regions: 2, 5. Avoids difficult regions.",
  "strategy_comment": "Prioritizes opportunity regions while avoiding difficult ones. Aims for path diversity through re-ordering."
}
```
2025-07-04 10:13:10,276 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 10:13:10,277 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17693.0, 路径: [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0]
2025-07-04 10:13:10,277 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}
2025-07-04 10:13:10,277 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 10:13:10,277 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:13:10,277 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:13:10,277 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 103540.0
2025-07-04 10:13:11,243 - ExploitationExpert - INFO - res_population_num: 2
2025-07-04 10:13:11,245 - ExploitationExpert - INFO - res_population_costs: [102364, 9559]
2025-07-04 10:13:11,245 - ExploitationExpert - INFO - res_populations: [array([ 0, 38, 32, 29, 17,  7, 60, 48,  9, 52, 54, 58, 36, 24, 46, 22, 45,
       50,  3, 61,  5, 14, 37, 39, 25, 28, 40, 13, 47,  1, 15, 53, 44, 10,
       23, 55, 21, 27,  8, 51, 57, 41, 30,  2, 35, 49, 18, 20, 43, 34, 16,
       64, 63, 19,  4, 12, 33, 42, 65, 62, 56, 59,  6, 31, 11, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:13:11,246 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:13:11,247 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [0, 2, 5, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 54, 30, 4, 3], 'cur_cost': 15406.0}, {'tour': array([13, 30,  6, 34, 21,  5,  2,  7, 18, 62, 15,  0, 10,  1,  4, 32,  3,
       55, 40, 48, 59, 31, 64, 57, 44, 39, 42, 41, 16, 65, 27, 37, 45, 46,
       63, 52, 26, 14, 17,  9, 56, 43, 51, 58, 47, 19, 20, 60,  8, 61, 38,
       22, 53, 33, 50, 25, 49, 36, 11, 54, 29, 23, 24, 28, 35, 12]), 'cur_cost': 93149.0}, {'tour': [2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 3, 4], 'cur_cost': 11392.0}, {'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': array([15, 59, 33, 34,  5, 42, 40, 17, 22, 31, 16, 49,  6, 11, 20, 43, 44,
       10, 25, 18, 53, 56, 23, 29, 14, 60, 58, 32, 24, 28, 55, 57, 13, 65,
        3, 19, 63, 45, 30, 51, 48, 46, 27,  2, 52, 26,  4, 38, 54, 37, 62,
       21, 41, 64,  0, 12, 50,  7, 47, 39,  8,  9, 36, 61,  1, 35]), 'cur_cost': 103540.0}, {'tour': [7, 41, 46, 43, 21, 17, 11, 63, 49, 42, 12, 5, 61, 2, 26, 27, 56, 32, 29, 64, 1, 36, 20, 39, 33, 16, 13, 55, 3, 10, 18, 37, 8, 60, 40, 52, 54, 23, 31, 59, 6, 58, 51, 48, 28, 53, 9, 62, 38, 47, 14, 4, 34, 50, 57, 24, 22, 45, 25, 19, 15, 35, 0, 44, 30, 65], 'cur_cost': 99490.0}, {'tour': [21, 20, 11, 4, 48, 25, 62, 64, 12, 15, 47, 2, 49, 14, 61, 40, 31, 43, 57, 26, 60, 29, 1, 59, 34, 28, 52, 54, 39, 24, 56, 50, 13, 36, 55, 37, 9, 8, 6, 53, 3, 44, 0, 17, 30, 45, 10, 38, 58, 27, 41, 42, 65, 63, 51, 23, 19, 22, 32, 46, 18, 16, 33, 5, 7, 35], 'cur_cost': 112572.0}]
2025-07-04 10:13:11,248 - ExploitationExpert - INFO - 局部搜索耗时: 0.97秒
2025-07-04 10:13:11,248 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-04 10:13:11,248 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 10:13:11,249 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 10:13:11,249 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:13:11,249 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:13:11,249 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 118938.0
2025-07-04 10:13:11,751 - ExploitationExpert - INFO - res_population_num: 8
2025-07-04 10:13:11,752 - ExploitationExpert - INFO - res_population_costs: [102364, 9559, 9527, 9527, 9524, 9521, 9521, 9521]
2025-07-04 10:13:11,752 - ExploitationExpert - INFO - res_populations: [array([ 0, 38, 32, 29, 17,  7, 60, 48,  9, 52, 54, 58, 36, 24, 46, 22, 45,
       50,  3, 61,  5, 14, 37, 39, 25, 28, 40, 13, 47,  1, 15, 53, 44, 10,
       23, 55, 21, 27,  8, 51, 57, 41, 30,  2, 35, 49, 18, 20, 43, 34, 16,
       64, 63, 19,  4, 12, 33, 42, 65, 62, 56, 59,  6, 31, 11, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 23, 22, 12, 17, 15, 14, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:13:11,755 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:13:11,755 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [0, 2, 5, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 54, 30, 4, 3], 'cur_cost': 15406.0}, {'tour': array([13, 30,  6, 34, 21,  5,  2,  7, 18, 62, 15,  0, 10,  1,  4, 32,  3,
       55, 40, 48, 59, 31, 64, 57, 44, 39, 42, 41, 16, 65, 27, 37, 45, 46,
       63, 52, 26, 14, 17,  9, 56, 43, 51, 58, 47, 19, 20, 60,  8, 61, 38,
       22, 53, 33, 50, 25, 49, 36, 11, 54, 29, 23, 24, 28, 35, 12]), 'cur_cost': 93149.0}, {'tour': [2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 3, 4], 'cur_cost': 11392.0}, {'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': array([15, 59, 33, 34,  5, 42, 40, 17, 22, 31, 16, 49,  6, 11, 20, 43, 44,
       10, 25, 18, 53, 56, 23, 29, 14, 60, 58, 32, 24, 28, 55, 57, 13, 65,
        3, 19, 63, 45, 30, 51, 48, 46, 27,  2, 52, 26,  4, 38, 54, 37, 62,
       21, 41, 64,  0, 12, 50,  7, 47, 39,  8,  9, 36, 61,  1, 35]), 'cur_cost': 103540.0}, {'tour': array([12, 26, 11, 33,  8, 61, 25, 48, 28, 46, 56, 34, 38, 30, 36,  7, 43,
       52, 20, 16, 62, 23, 40, 55, 32, 47,  2, 14, 37, 42, 51, 57, 22, 53,
       31,  6, 10, 63,  9, 54, 24, 21, 27, 18, 19, 49,  1,  5, 15, 58, 45,
       17, 65, 44, 64,  0, 39,  3, 59, 35, 13, 41, 50, 60, 29,  4]), 'cur_cost': 118938.0}, {'tour': [21, 20, 11, 4, 48, 25, 62, 64, 12, 15, 47, 2, 49, 14, 61, 40, 31, 43, 57, 26, 60, 29, 1, 59, 34, 28, 52, 54, 39, 24, 56, 50, 13, 36, 55, 37, 9, 8, 6, 53, 3, 44, 0, 17, 30, 45, 10, 38, 58, 27, 41, 42, 65, 63, 51, 23, 19, 22, 32, 46, 18, 16, 33, 5, 7, 35], 'cur_cost': 112572.0}]
2025-07-04 10:13:11,757 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:13:11,757 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-04 10:13:11,757 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 10:13:11,757 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 10:13:11,758 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 10:13:11,758 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 10:13:11,758 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 94247.0
2025-07-04 10:13:12,263 - ExploitationExpert - INFO - res_population_num: 14
2025-07-04 10:13:12,263 - ExploitationExpert - INFO - res_population_costs: [102364, 9559, 9527, 9527, 9524, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 10:13:12,264 - ExploitationExpert - INFO - res_populations: [array([ 0, 38, 32, 29, 17,  7, 60, 48,  9, 52, 54, 58, 36, 24, 46, 22, 45,
       50,  3, 61,  5, 14, 37, 39, 25, 28, 40, 13, 47,  1, 15, 53, 44, 10,
       23, 55, 21, 27,  8, 51, 57, 41, 30,  2, 35, 49, 18, 20, 43, 34, 16,
       64, 63, 19,  4, 12, 33, 42, 65, 62, 56, 59,  6, 31, 11, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 19, 16, 23, 22, 14, 15, 17, 12, 18, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 33, 31, 25, 26, 36, 37,
       27, 19, 16, 18, 23, 22, 12, 17, 15, 14, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-04 10:13:12,268 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 10:13:12,270 - ExploitationExpert - INFO - populations: [{'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': [0, 2, 5, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 54, 30, 4, 3], 'cur_cost': 15406.0}, {'tour': array([13, 30,  6, 34, 21,  5,  2,  7, 18, 62, 15,  0, 10,  1,  4, 32,  3,
       55, 40, 48, 59, 31, 64, 57, 44, 39, 42, 41, 16, 65, 27, 37, 45, 46,
       63, 52, 26, 14, 17,  9, 56, 43, 51, 58, 47, 19, 20, 60,  8, 61, 38,
       22, 53, 33, 50, 25, 49, 36, 11, 54, 29, 23, 24, 28, 35, 12]), 'cur_cost': 93149.0}, {'tour': [2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 3, 4], 'cur_cost': 11392.0}, {'tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}, {'tour': array([15, 59, 33, 34,  5, 42, 40, 17, 22, 31, 16, 49,  6, 11, 20, 43, 44,
       10, 25, 18, 53, 56, 23, 29, 14, 60, 58, 32, 24, 28, 55, 57, 13, 65,
        3, 19, 63, 45, 30, 51, 48, 46, 27,  2, 52, 26,  4, 38, 54, 37, 62,
       21, 41, 64,  0, 12, 50,  7, 47, 39,  8,  9, 36, 61,  1, 35]), 'cur_cost': 103540.0}, {'tour': array([12, 26, 11, 33,  8, 61, 25, 48, 28, 46, 56, 34, 38, 30, 36,  7, 43,
       52, 20, 16, 62, 23, 40, 55, 32, 47,  2, 14, 37, 42, 51, 57, 22, 53,
       31,  6, 10, 63,  9, 54, 24, 21, 27, 18, 19, 49,  1,  5, 15, 58, 45,
       17, 65, 44, 64,  0, 39,  3, 59, 35, 13, 41, 50, 60, 29,  4]), 'cur_cost': 118938.0}, {'tour': array([30, 23, 38, 47, 61, 64, 37, 15, 54, 65, 63, 24, 52, 28, 11,  9, 27,
       20, 46,  0, 13, 57,  6, 40, 33, 29, 45, 16, 19, 12, 39,  3, 59,  8,
       42, 18, 35, 50, 34, 43,  2, 60, 25, 10, 31, 32,  5, 36, 26, 51, 21,
       58,  7, 53,  4, 48, 44, 14, 22, 17, 41, 49, 56, 55, 62,  1]), 'cur_cost': 94247.0}]
2025-07-04 10:13:12,272 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 10:13:12,272 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-04 10:13:12,272 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 10:13:12,272 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 5, 1, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 54, 30, 4, 3], 'cur_cost': 15406.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 3, 4], 'cur_cost': 11392.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [2, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 3, 1, 0], 'cur_cost': 17693.0}}]
2025-07-04 10:13:12,272 - __main__ - INFO - 进化阶段完成
2025-07-04 10:13:12,272 - __main__ - INFO - 开始评估阶段
2025-07-04 10:13:12,274 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
