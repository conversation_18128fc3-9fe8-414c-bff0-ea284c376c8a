import logging
import logging.config
import os
from datetime import datetime
from typing import Dict, Any, Optional

# 默认日志格式
DEFAULT_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 默认日期格式
DEFAULT_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# 默认日志级别
DEFAULT_LOG_LEVEL = logging.INFO

# 全局日志配置状态
_is_configured = False

def setup_logging(log_dir: Optional[str] = None, 
                 log_file: Optional[str] = None, 
                 level: int = DEFAULT_LOG_LEVEL, 
                 log_format: str = DEFAULT_LOG_FORMAT,
                 date_format: str = DEFAULT_DATE_FORMAT) -> None:
    """
    设置基本的日志配置
    
    Args:
        log_dir: 日志目录
        log_file: 日志文件名
        level: 日志级别
        log_format: 日志格式
        date_format: 日期格式
    """
    global _is_configured
    
    # 如果已经配置过，则不再重复配置
    if _is_configured:
        return
    
    # 确保日志目录存在
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)
    
    # 配置根日志记录器
    if log_file:
        # 文件处理器
        logging.basicConfig(
            level=level,
            format=log_format,
            datefmt=date_format,
            filename=os.path.join(log_dir, log_file) if log_dir else log_file,
            filemode='w',
            encoding='utf-8'
        )
        
        # 添加控制台处理器
        console = logging.StreamHandler()
        console.setLevel(level)
        formatter = logging.Formatter(log_format, datefmt=date_format)
        console.setFormatter(formatter)
        logging.getLogger('').addHandler(console)
    else:
        # 仅控制台输出
        logging.basicConfig(
            level=level,
            format=log_format,
            datefmt=date_format
        )
    
    # 标记为已配置
    _is_configured = True
    
    # 创建一个日志记录器用于记录配置信息
    logger = logging.getLogger('log_config')
    logger.info(f"日志系统已初始化，级别: {logging.getLevelName(level)}")
    if log_file:
        logger.info(f"日志文件: {log_file}")

def setup_advanced_logging(config: Dict[str, Any]) -> None:
    """
    使用dictConfig设置更高级的日志配置
    
    Args:
        config: 日志配置字典
    """
    global _is_configured
    
    # 应用配置
    logging.config.dictConfig(config)
    
    # 标记为已配置
    _is_configured = True
    
    # 创建一个日志记录器用于记录配置信息
    logger = logging.getLogger('log_config')
    logger.info("高级日志系统已初始化")

def get_instance_log_file(instance_name: str, log_dir: str) -> str:
    """
    为特定实例生成日志文件名
    
    Args:
        instance_name: 实例名称
        log_dir: 日志目录
        
    Returns:
        str: 日志文件路径
    """
    # 确保日志目录存在
    os.makedirs(log_dir, exist_ok=True)
    
    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"{instance_name}_{timestamp}.log"
    
    return os.path.join(log_dir, log_file)

def get_logger(name: str, level: Optional[int] = None) -> logging.Logger:
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别，如果为None则使用父记录器的级别
        
    Returns:
        logging.Logger: 日志记录器
    """
    logger = logging.getLogger(name)
    
    # 如果指定了级别，则设置
    if level is not None:
        logger.setLevel(level)
    
    return logger