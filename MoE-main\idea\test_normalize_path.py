import numpy as np
import matplotlib.pyplot as plt
import random
import time

# 导入normalize_path函数
from gls_evol_enhanced import normalize_path

def generate_path_variants(original_path):
    """生成原始路径的所有变体（旋转和反转）"""
    n = len(original_path)
    variants = []
    
    # 生成所有旋转变体
    for i in range(n):
        rotated = original_path[i:] + original_path[:i]
        variants.append(rotated)
    
    # 生成所有反转后的旋转变体
    reversed_path = original_path[::-1]
    for i in range(n):
        rotated = reversed_path[i:] + reversed_path[:i]
        variants.append(rotated)
    
    return variants

def test_normalize_path_uniqueness():
    """测试normalize_path函数是否能对同一TSP路径的所有变体生成唯一的标准化表示"""
    print("测试normalize_path函数的唯一性...")
    
    # 测试用例1：小规模路径
    original_path = [0, 1, 2, 3, 4]
    test_uniqueness(original_path, "小规模路径")
    
    # 测试用例2：中等规模路径
    original_path = list(range(10))
    test_uniqueness(original_path, "中等规模路径")
    
    # 测试用例3：大规模路径
    original_path = list(range(20))
    test_uniqueness(original_path, "大规模路径")
    
    # 测试用例4：随机路径
    random_path = list(range(15))
    random.shuffle(random_path)
    test_uniqueness(random_path, "随机路径")
    
    # 测试用例5：包含重复元素的路径
    path_with_duplicates = [0, 1, 2, 1, 3, 4]
    test_uniqueness(path_with_duplicates, "包含重复元素的路径")

def test_uniqueness(original_path, test_name):
    """测试特定路径的所有变体是否能被标准化为相同的表示"""
    print(f"\n测试: {test_name}")
    print(f"原始路径: {original_path}")
    
    # 生成所有变体
    variants = generate_path_variants(original_path)
    print(f"生成了 {len(variants)} 个变体")
    
    # 标准化所有变体
    normalized_variants = [normalize_path(np.array(variant)) for variant in variants]
    
    # 检查所有标准化后的变体是否相同
    first_normalized = normalized_variants[0]
    all_same = all(np.array_equal(first_normalized, variant) for variant in normalized_variants)
    
    if all_same:
        print(f"✓ 所有变体都被标准化为相同的表示: {first_normalized}")
    else:
        print("✗ 标准化失败 - 不同变体产生了不同的标准化表示:")
        unique_normalized = []
        for i, variant in enumerate(normalized_variants):
            is_unique = True
            for unique in unique_normalized:
                if np.array_equal(variant, unique):
                    is_unique = False
                    break
            if is_unique:
                unique_normalized.append(variant)
                print(f"变体 {i}: {variants[i]} -> {variant}")
    
    return all_same

def analyze_normalize_path_function():
    """分析normalize_path函数的实现逻辑"""
    print("\n分析normalize_path函数的实现逻辑:")
    print("1. 函数首先将输入路径转换为numpy数组")
    print("2. 创建一个扩展路径，包含原始路径的两个副本，用于处理旋转")
    print("3. 创建路径的反转版本，并将其添加到扩展路径中")
    print("4. 假设原始路径是最小标准化路径")
    print("5. 检查所有可能的旋转，寻找字典序最小的路径")
    
    # 分析函数的局限性
    print("\n函数的局限性:")
    print("1. 函数使用np.all(candidate_rotation < min_path)比较整个数组，这可能导致某些情况下无法找到真正的最小表示")
    print("2. 函数没有正确处理反转路径的所有旋转变体")
    print("3. 比较逻辑可能不够严格，应该使用字典序比较而不是简单的数组比较")
    
    # 提出改进建议
    print("\n改进建议:")
    print("1. 使用字典序比较来确定最小表示")
    print("2. 正确处理所有旋转和反转变体")
    print("3. 添加更多的测试用例验证函数的正确性")

def improved_normalize_path(path):
    """改进的标准化路径函数，使用字典序比较确保唯一表示"""
    path = np.array(path, dtype=np.int64)
    
    if len(path) == 0:
        return path  # 如果路径为空，直接返回
    
    n = len(path)
    min_path = None
    min_representation = None
    
    # 检查所有旋转变体
    for i in range(n):
        # 生成旋转变体
        rotated = np.concatenate([path[i:], path[:i]])
        # 计算字典序表示
        representation = tuple(rotated)
        
        # 如果这是第一个变体或者比当前最小的更小，则更新
        if min_representation is None or representation < min_representation:
            min_representation = representation
            min_path = rotated
    
    # 检查所有反转后的旋转变体
    reversed_path = path[::-1]
    for i in range(n):
        # 生成反转后的旋转变体
        rotated = np.concatenate([reversed_path[i:], reversed_path[:i]])
        # 计算字典序表示
        representation = tuple(rotated)
        
        # 如果比当前最小的更小，则更新
        if representation < min_representation:
            min_representation = representation
            min_path = rotated
    
    return min_path

def compare_implementations():
    """比较原始和改进的normalize_path实现"""
    print("\n比较原始和改进的normalize_path实现:")
    
    # 测试用例
    test_paths = [
        [0, 1, 2, 3, 4],  # 简单路径
        [3, 1, 4, 2, 0],  # 随机路径
        [5, 2, 8, 1, 3, 7, 4, 6],  # 较长路径
        [1, 1, 2, 3, 1]   # 包含重复元素的路径
    ]
    
    for i, path in enumerate(test_paths):
        print(f"\n测试路径 {i+1}: {path}")
        
        # 生成变体
        variants = generate_path_variants(path)
        print(f"生成了 {len(variants)} 个变体")
        
        # 使用原始函数标准化
        original_normalized = [normalize_path(np.array(variant)) for variant in variants]
        original_unique = set(tuple(norm) for norm in original_normalized)
        
        # 使用改进函数标准化
        improved_normalized = [improved_normalize_path(np.array(variant)) for variant in variants]
        improved_unique = set(tuple(norm) for norm in improved_normalized)
        
        print(f"原始函数产生了 {len(original_unique)} 个不同的标准化表示")
        print(f"改进函数产生了 {len(improved_unique)} 个不同的标准化表示")
        
        if len(original_unique) == 1:
            print("✓ 原始函数成功标准化所有变体为相同表示")
        else:
            print("✗ 原始函数未能标准化所有变体为相同表示")
            
        if len(improved_unique) == 1:
            print("✓ 改进函数成功标准化所有变体为相同表示")
        else:
            print("✗ 改进函数未能标准化所有变体为相同表示")

def benchmark_performance():
    """比较两种实现的性能"""
    print("\n性能基准测试:")
    
    # 创建不同大小的测试路径
    sizes = [10, 50, 100, 200, 500]
    original_times = []
    improved_times = []
    
    for size in sizes:
        path = list(range(size))
        random.shuffle(path)
        path = np.array(path)
        
        # 测试原始函数性能
        start_time = time.time()
        normalize_path(path)
        original_time = time.time() - start_time
        original_times.append(original_time)
        
        # 测试改进函数性能
        start_time = time.time()
        improved_normalize_path(path)
        improved_time = time.time() - start_time
        improved_times.append(improved_time)
        
        print(f"路径大小: {size}, 原始函数: {original_time:.6f}秒, 改进函数: {improved_time:.6f}秒")
    
    # 绘制性能比较图
    plt.figure(figsize=(10, 6))
    plt.plot(sizes, original_times, 'o-', label='原始函数')
    plt.plot(sizes, improved_times, 'o-', label='改进函数')
    plt.xlabel('路径大小')
    plt.ylabel('执行时间 (秒)')
    plt.title('normalize_path函数性能比较')
    plt.legend()
    plt.grid(True)
    plt.savefig('normalize_path_performance.png')
    print("性能比较图已保存为 'normalize_path_performance.png'")

if __name__ == "__main__":
    print("=== normalize_path函数分析 ===")
    
    # 分析函数实现逻辑
    analyze_normalize_path_function()
    
    # 测试函数的唯一性
    test_normalize_path_uniqueness()
    
    # 比较两种实现
    compare_implementations()
    
    # 性能基准测试
    # 取消下面的注释来运行性能测试
    # benchmark_performance()
    
    print("\n分析完成!")