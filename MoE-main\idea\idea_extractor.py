import re
import json
import logging

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

class IdeaExtractor:
    """从LLM响应中提取想法和路径的工具类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def extract_exploration_path(self, response, original_tour=None):
        """从探索响应中提取路径
        
        参数:
            response (str): LLM的响应文本
            original_tour (list): 原始路径，用于回退
            
        返回:
            dict: 包含提取的路径和其他信息
        """
        self.logger.info("开始从探索响应中提取路径")
        
        try:
            # 尝试提取JSON格式的路径
            json_match = re.search(r'```json\s*({[\s\S]*?})\s*```', response)
            if json_match:
                path_data = json.loads(json_match.group(1))
                self.logger.info("成功从JSON中提取路径")
                # 检查并转换键名，确保返回的字典包含new_tour键
                if "new_path" in path_data and "new_tour" not in path_data:
                    path_data["new_tour"] = path_data["new_path"]
                return path_data
            
            # 尝试提取路径列表
            path_match = re.search(r'\[\s*(\d+(?:\s*,\s*\d+)*)\s*\]', response)
            if path_match:
                path_str = path_match.group(1)
                path = [int(x.strip()) for x in path_str.split(',')]
                self.logger.info(f"成功提取路径列表: {path}")
                # 返回包含new_tour和cur_cost字段的字典，以匹配moe_main.py中的期望
                return {"new_tour": path, "cur_cost": 0}  # 这里设置默认成本为0，实际应根据路径计算
            
            # 如果无法提取，返回原始路径
            self.logger.warning("无法从响应中提取路径，返回原始路径")
            return {"new_tour": original_tour, "cur_cost": 0} if original_tour else {}
            
        except Exception as e:
            self.logger.error(f"提取探索路径时出错: {str(e)}")
            return {"new_tour": original_tour, "cur_cost": 0} if original_tour else {}
    
    def extract_exploitation_path(self, response, original_tour=None):
        """从利用响应中提取路径
        
        参数:
            response (str): LLM的响应文本
            original_tour (list): 原始路径，用于回退
            
        返回:
            dict: 包含提取的路径和其他信息
        """
        self.logger.info("开始从利用响应中提取路径")
        
        try:
            # 尝试提取JSON格式的路径
            json_match = re.search(r'```json\s*({[\s\S]*?})\s*```', response)
            if json_match:
                path_data = json.loads(json_match.group(1))
                self.logger.info("成功从JSON中提取路径")
                # 检查并转换键名，确保返回的字典包含new_tour键
                if "new_path" in path_data and "new_tour" not in path_data:
                    path_data["new_tour"] = path_data["new_path"]
                return path_data
            
            # 尝试提取路径列表
            path_match = re.search(r'\[\s*(\d+(?:\s*,\s*\d+)*)\s*\]', response)
            if path_match:
                path_str = path_match.group(1)
                path = [int(x.strip()) for x in path_str.split(',')]
                self.logger.info(f"成功提取路径列表: {path}")
                # 返回包含new_tour和cur_cost字段的字典，以匹配moe_main.py中的期望
                return {"new_tour": path, "cur_cost": 0}  # 这里设置默认成本为0，实际应根据路径计算
            
            # 如果无法提取，返回原始路径
            self.logger.warning("无法从响应中提取路径，返回原始路径")
            return {"new_tour": original_tour, "cur_cost": 0} if original_tour else {}
            
        except Exception as e:
            self.logger.error(f"提取利用路径时出错: {str(e)}")
            return {"new_tour": original_tour, "cur_cost": 0} if original_tour else {}
    
    def extract_ideas(self, response):
        """从响应中提取想法
        
        参数:
            response (str): LLM的响应文本
            
        返回:
            list: 提取的想法列表
        """
        self.logger.info("开始从响应中提取想法")
        
        try:
            # 尝试提取JSON格式的想法
            json_match = re.search(r'```json\s*({[\s\S]*?})\s*```', response)
            if json_match:
                ideas_data = json.loads(json_match.group(1))
                if "ideas" in ideas_data:
                    self.logger.info(f"成功从JSON中提取想法: {len(ideas_data['ideas'])}个")
                    return ideas_data["ideas"]
            
            # 尝试提取列表格式的想法
            ideas = []
            idea_matches = re.findall(r'\d+\.\s*([^\n]+)', response)
            if idea_matches:
                ideas = idea_matches
                self.logger.info(f"成功提取列表格式的想法: {len(ideas)}个")
                return ideas
            
            # 如果无法提取，返回空列表
            self.logger.warning("无法从响应中提取想法，返回空列表")
            return []
            
        except Exception as e:
            self.logger.error(f"提取想法时出错: {str(e)}")
            return []