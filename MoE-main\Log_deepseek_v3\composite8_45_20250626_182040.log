2025-06-26 18:20:40,183 - __main__ - INFO - composite8_45 开始进化第 1 代
2025-06-26 18:20:40,185 - __main__ - INFO - 开始分析阶段
2025-06-26 18:20:40,185 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:20:40,193 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11360.0, 'max': 106571.0, 'mean': 64067.3, 'std': 35389.57004273999}, 'diversity': 0.899753086419753, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:20:40,195 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11360.0, 'max': 106571.0, 'mean': 64067.3, 'std': 35389.57004273999}, 'diversity_level': 0.899753086419753, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[690, 2780], [665, 2760], [633, 2709], [617, 2736], [662, 2776], [620, 2758], [638, 2774], [638, 2735], [660, 2733], [637, 2749], [709, 2734], [2544, 1844], [2556, 1799], [2529, 1872], [2558, 1831], [2569, 1874], [2516, 1787], [2512, 1852], [2528, 1816], [2505, 1831], [2545, 1820], [2539, 1799], [4451, 2836], [4390, 2772], [4477, 2811], [4450, 2809], [4408, 2821], [4427, 2814], [4469, 2788], [4414, 2795], [4440, 2773], [4389, 2814], [4418, 2758], [2671, 406], [2631, 439], [2685, 455], [2692, 425], [2640, 406], [2684, 391], [2647, 391], [2609, 410], [2631, 463], [2620, 463], [2671, 419], [2652, 420]], 'distance_matrix': array([[   0.,   32.,   91., ..., 3016., 3082., 3069.],
       [  32.,    0.,   60., ..., 3016., 3083., 3070.],
       [  91.,   60.,    0., ..., 2999., 3066., 3052.],
       ...,
       [3016., 3016., 2999., ...,    0.,   67.,   54.],
       [3082., 3083., 3066., ...,   67.,    0.,   19.],
       [3069., 3070., 3052., ...,   54.,   19.,    0.]])}
2025-06-26 18:20:40,205 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:20:40,205 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:20:40,205 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:20:40,209 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:20:40,209 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (16, 21, 12), 'frequency': 0.3}, {'subpath': (21, 12, 20), 'frequency': 0.3}, {'subpath': (12, 20, 14), 'frequency': 0.3}, {'subpath': (20, 14, 11), 'frequency': 0.3}, {'subpath': (14, 11, 13), 'frequency': 0.3}, {'subpath': (11, 13, 17), 'frequency': 0.3}, {'subpath': (13, 17, 19), 'frequency': 0.3}, {'subpath': (17, 19, 18), 'frequency': 0.3}, {'subpath': (19, 18, 15), 'frequency': 0.3}, {'subpath': (18, 15, 23), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(41, 42)', 'frequency': 0.4}, {'edge': '(33, 43)', 'frequency': 0.4}, {'edge': '(35, 36)', 'frequency': 0.4}, {'edge': '(15, 23)', 'frequency': 0.5}, {'edge': '(23, 32)', 'frequency': 0.4}, {'edge': '(30, 32)', 'frequency': 0.4}, {'edge': '(26, 31)', 'frequency': 0.4}, {'edge': '(10, 22)', 'frequency': 0.4}, {'edge': '(7, 9)', 'frequency': 0.4}, {'edge': '(5, 9)', 'frequency': 0.4}, {'edge': '(2, 3)', 'frequency': 0.4}, {'edge': '(37, 44)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(34, 41)', 'frequency': 0.2}, {'edge': '(40, 42)', 'frequency': 0.2}, {'edge': '(37, 40)', 'frequency': 0.2}, {'edge': '(37, 39)', 'frequency': 0.3}, {'edge': '(33, 39)', 'frequency': 0.2}, {'edge': '(43, 44)', 'frequency': 0.2}, {'edge': '(35, 38)', 'frequency': 0.2}, {'edge': '(16, 21)', 'frequency': 0.3}, {'edge': '(12, 21)', 'frequency': 0.3}, {'edge': '(12, 20)', 'frequency': 0.3}, {'edge': '(14, 20)', 'frequency': 0.3}, {'edge': '(11, 14)', 'frequency': 0.3}, {'edge': '(11, 13)', 'frequency': 0.3}, {'edge': '(13, 17)', 'frequency': 0.3}, {'edge': '(17, 19)', 'frequency': 0.3}, {'edge': '(18, 19)', 'frequency': 0.3}, {'edge': '(15, 18)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(24, 28)', 'frequency': 0.3}, {'edge': '(24, 25)', 'frequency': 0.3}, {'edge': '(25, 27)', 'frequency': 0.3}, {'edge': '(26, 27)', 'frequency': 0.3}, {'edge': '(29, 31)', 'frequency': 0.3}, {'edge': '(22, 29)', 'frequency': 0.3}, {'edge': '(8, 10)', 'frequency': 0.3}, {'edge': '(7, 8)', 'frequency': 0.3}, {'edge': '(3, 5)', 'frequency': 0.3}, {'edge': '(1, 2)', 'frequency': 0.3}, {'edge': '(1, 4)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.3}, {'edge': '(0, 6)', 'frequency': 0.3}, {'edge': '(36, 38)', 'frequency': 0.3}, {'edge': '(34, 40)', 'frequency': 0.2}, {'edge': '(16, 40)', 'frequency': 0.3}, {'edge': '(17, 38)', 'frequency': 0.2}, {'edge': '(19, 39)', 'frequency': 0.2}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(32, 33)', 'frequency': 0.2}, {'edge': '(16, 25)', 'frequency': 0.2}, {'edge': '(16, 22)', 'frequency': 0.2}, {'edge': '(18, 22)', 'frequency': 0.2}, {'edge': '(3, 34)', 'frequency': 0.2}, {'edge': '(6, 15)', 'frequency': 0.2}, {'edge': '(24, 43)', 'frequency': 0.2}, {'edge': '(31, 44)', 'frequency': 0.2}, {'edge': '(27, 28)', 'frequency': 0.2}, {'edge': '(8, 25)', 'frequency': 0.2}, {'edge': '(6, 28)', 'frequency': 0.2}, {'edge': '(10, 13)', 'frequency': 0.2}, {'edge': '(4, 21)', 'frequency': 0.2}, {'edge': '(5, 23)', 'frequency': 0.3}, {'edge': '(18, 35)', 'frequency': 0.2}, {'edge': '(27, 29)', 'frequency': 0.2}, {'edge': '(18, 37)', 'frequency': 0.2}, {'edge': '(0, 12)', 'frequency': 0.2}, {'edge': '(12, 31)', 'frequency': 0.2}, {'edge': '(25, 32)', 'frequency': 0.2}, {'edge': '(30, 39)', 'frequency': 0.2}, {'edge': '(19, 43)', 'frequency': 0.2}, {'edge': '(1, 6)', 'frequency': 0.2}, {'edge': '(21, 36)', 'frequency': 0.2}, {'edge': '(11, 21)', 'frequency': 0.2}, {'edge': '(2, 11)', 'frequency': 0.2}, {'edge': '(9, 34)', 'frequency': 0.2}, {'edge': '(10, 34)', 'frequency': 0.2}, {'edge': '(22, 25)', 'frequency': 0.2}, {'edge': '(0, 15)', 'frequency': 0.2}, {'edge': '(0, 40)', 'frequency': 0.2}, {'edge': '(3, 13)', 'frequency': 0.3}]}, 'low_quality_regions': [{'region': [34, 2, 43, 30, 8, 31], 'cost': 16545.0, 'size': 6}, {'region': [0, 29, 1, 27, 4], 'cost': 15000.0, 'size': 5}, {'region': [39, 2, 31, 9, 26], 'cost': 14353.0, 'size': 5}, {'region': [25, 8, 33, 30, 39], 'cost': 12803.0, 'size': 5}, {'region': [42, 10, 37, 23, 1], 'cost': 12661.0, 'size': 5}]}
2025-06-26 18:20:40,209 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:20:40,209 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:20:40,209 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:20:40,209 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 18:20:40,209 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 18:20:40,209 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:20:40,210 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:20:40,213 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:20:40,213 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 11360.0, mean 64067.3, max 106571.0, std 35389.57004273999
- diversity: 0.899753086419753
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [79778.0, 53337.0, 41956.0] (Δ 18911.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: [{'subpath': (16, 21, 12), 'frequency': 0.3}, {'subpath': (21, 12, 20), 'frequency': 0.3}, {'subpath': (12, 20, 14), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [34, 2, 43, 30, 8, 31], 'cost': 16545.0, 'size': 6}, {'region': [0, 29, 1, 27, 4], 'cost': 15000.0, 'size': 5}, {'region': [39, 2, 31, 9, 26], 'cost': 14353.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [617.0, 391.0, 4477.0, 2836.0]
- centroid/spread: [2569.689, 1916.622] | rms 1644.594
- density_grid(3x3): [[0, 12, 0], [0, 11, 0], [11, 0, 11]]
- edge_len_stats(min,q1,med,q3,max): [11.0, 1369.25, 2115.5, 3021.25, 3861.0]
- long_edge_ratio: 0.251
- direction_hist(8 sectors): [6, 5, 6, 9, 5, 2, 3, 8]
- nn_median_dist: 21.024

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:20:40,213 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:21:07,602 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 11.0 to 3861.0, Q3 at 3021.25 indicates significant variance)",
    "modality": "Potentially multimodal (direction histogram shows uneven distribution with sectors 3 and 7 having higher counts)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.251 suggests some edges may mislead local search)"
  },
  "population_state": {
    "coverage": "Good initial diversity (0.899) but uneven spatial distribution (density_grid shows clusters in NW/SW/SE cells with empty NE/Central cells)",
    "convergence": "Early stage (0.0 convergence, recent best costs show large Δ 18911.0 per iteration)"
  },
  "difficult_regions": [
    {"region": [34, 2, 43, 30, 8, 31], "reason": "High cost (16545.0) for size 6, overlaps with long-edge corridor"},
    {"region": [0, 29, 1, 27, 4], "reason": "High cost (15000.0) cluster in low-density NW grid cell"},
    {"region": [39, 2, 31, 9, 26], "reason": "High cost (14353.0) with directional bias (sector 7)"}
  ],
  "opportunity_regions": [
    {"region": [16, 21, 12, 20, 14], "reason": "Frequent subpaths (0.3 frequency) in medium-density SW cell"},
    {"region": "NE grid cell", "reason": "Currently empty in density_grid but adjacent to high-cost regions - potential for exploration"}
  ],
  "evolution_phase": "Exploration (iter 0/5, no elites yet, high diversity)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Edge-recombination crossover to preserve common subpaths like (16,21,12)",
    "2. Directed mutation towards NE grid cell to improve coverage",
    "3. Local search intensification around [21,12,20] subpath",
    "4. Penalize long edges (>3000) in selection to reduce ruggedness"
  ]
}
```
2025-06-26 18:21:07,602 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:21:07,602 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 11.0 to 3861.0, Q3 at 3021.25 indicates significant variance)', 'modality': 'Potentially multimodal (direction histogram shows uneven distribution with sectors 3 and 7 having higher counts)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.251 suggests some edges may mislead local search)'}, 'population_state': {'coverage': 'Good initial diversity (0.899) but uneven spatial distribution (density_grid shows clusters in NW/SW/SE cells with empty NE/Central cells)', 'convergence': 'Early stage (0.0 convergence, recent best costs show large Δ 18911.0 per iteration)'}, 'difficult_regions': [{'region': [34, 2, 43, 30, 8, 31], 'reason': 'High cost (16545.0) for size 6, overlaps with long-edge corridor'}, {'region': [0, 29, 1, 27, 4], 'reason': 'High cost (15000.0) cluster in low-density NW grid cell'}, {'region': [39, 2, 31, 9, 26], 'reason': 'High cost (14353.0) with directional bias (sector 7)'}], 'opportunity_regions': [{'region': [16, 21, 12, 20, 14], 'reason': 'Frequent subpaths (0.3 frequency) in medium-density SW cell'}, {'region': 'NE grid cell', 'reason': 'Currently empty in density_grid but adjacent to high-cost regions - potential for exploration'}], 'evolution_phase': 'Exploration (iter 0/5, no elites yet, high diversity)', 'evolution_direction': ['Operator suggestions:', '1. Edge-recombination crossover to preserve common subpaths like (16,21,12)', '2. Directed mutation towards NE grid cell to improve coverage', '3. Local search intensification around [21,12,20] subpath', '4. Penalize long edges (>3000) in selection to reduce ruggedness']}
2025-06-26 18:21:07,602 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:21:07,602 - __main__ - INFO - 分析阶段完成
2025-06-26 18:21:07,602 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 11.0 to 3861.0, Q3 at 3021.25 indicates significant variance)', 'modality': 'Potentially multimodal (direction histogram shows uneven distribution with sectors 3 and 7 having higher counts)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.251 suggests some edges may mislead local search)'}, 'population_state': {'coverage': 'Good initial diversity (0.899) but uneven spatial distribution (density_grid shows clusters in NW/SW/SE cells with empty NE/Central cells)', 'convergence': 'Early stage (0.0 convergence, recent best costs show large Δ 18911.0 per iteration)'}, 'difficult_regions': [{'region': [34, 2, 43, 30, 8, 31], 'reason': 'High cost (16545.0) for size 6, overlaps with long-edge corridor'}, {'region': [0, 29, 1, 27, 4], 'reason': 'High cost (15000.0) cluster in low-density NW grid cell'}, {'region': [39, 2, 31, 9, 26], 'reason': 'High cost (14353.0) with directional bias (sector 7)'}], 'opportunity_regions': [{'region': [16, 21, 12, 20, 14], 'reason': 'Frequent subpaths (0.3 frequency) in medium-density SW cell'}, {'region': 'NE grid cell', 'reason': 'Currently empty in density_grid but adjacent to high-cost regions - potential for exploration'}], 'evolution_phase': 'Exploration (iter 0/5, no elites yet, high diversity)', 'evolution_direction': ['Operator suggestions:', '1. Edge-recombination crossover to preserve common subpaths like (16,21,12)', '2. Directed mutation towards NE grid cell to improve coverage', '3. Local search intensification around [21,12,20] subpath', '4. Penalize long edges (>3000) in selection to reduce ruggedness']}
2025-06-26 18:21:07,602 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:21:07,602 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:21:07,602 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 11360.0
  • mean_cost: 64067.3
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 11.0 to 3861.0, Q3 at 3021.25 indicates significant variance), modality Potentially multimodal (direction histogram shows uneven distribution with sectors 3 and 7 having higher counts), deceptiveness Possible deceptive regions (long_edge_ratio of 0.251 suggests some edges may mislead local search)
- Evolution phase: Exploration (iter 0/5, no elites yet, high diversity)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [34, 2, 4, {'region': [0, 29, 1...
- Opportunity regions (# 2): {'region': [16, 21, , {'region': 'NE grid 
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:21:07,602 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:21:07,602 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:21:17,558 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and no elites favors exploration. Top 80% explore to discover promising regions, bottom 20% exploit to refine."
}
```
2025-06-26 18:21:17,558 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:21:17,558 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:21:17,558 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and no elites favors exploration. Top 80% explore to discover promising regions, bottom 20% exploit to refine."
}
```
2025-06-26 18:21:17,558 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:21:17,558 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:21:17,558 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and no elites favors exploration. Top 80% explore to discover promising regions, bottom 20% exploit to refine."
}
```
2025-06-26 18:21:17,564 - __main__ - INFO - 开始进化阶段
2025-06-26 18:21:17,564 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:21:17,564 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:21:17,564 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11434.0 (rank 20.0%)
- Population diversity: 0.6954545454545454
- Evolution phase: Exploration (iter 0/5, no elites yet, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [16,, {'region': 'NE 
- Difficult regions to avoid (sample): {'region': [34,, {'region': [0, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:21:17,564 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:21:17,564 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:21:29,301 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0],
  "targeted_regions": "NE and adjacent sparse cells",
  "strategy_comment": "Prioritize NE region, spiral outward to balance exploration"
}
```
2025-06-26 18:21:29,301 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:21:29,301 - ExplorationExpert - INFO - 探索路径生成完成，成本: 46445.0, 路径: [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0]
2025-06-26 18:21:29,301 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}
2025-06-26 18:21:29,301 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:21:29,301 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:21:29,301 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11425.0 (rank 10.0%)
- Population diversity: 0.7681818181818182
- Evolution phase: Exploration (iter 0/5, no elites yet, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [16,, {'region': 'NE 
- Difficult regions to avoid (sample): {'region': [34,, {'region': [0, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:21:29,301 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:21:29,301 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:21:41,860 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [16, 12, 8, 4, 0, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 42, 38, 34, 30, 26, 22, 18, 14, 10, 6, 2, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 44, 40, 36, 32, 28, 24, 20],
  "targeted_regions": "NE opportunity regions and adjacent sparse cells",
  "strategy_comment": "Spiral pattern focusing on NE, avoiding difficult SW regions"
}
```
2025-06-26 18:21:41,860 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:21:41,860 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30231.0, 路径: [16, 12, 8, 4, 0, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 42, 38, 34, 30, 26, 22, 18, 14, 10, 6, 2, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 44, 40, 36, 32, 28, 24, 20]
2025-06-26 18:21:41,860 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [16, 12, 8, 4, 0, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 42, 38, 34, 30, 26, 22, 18, 14, 10, 6, 2, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 44, 40, 36, 32, 28, 24, 20], 'cur_cost': 30231.0}
2025-06-26 18:21:41,866 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:21:41,866 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:21:41,866 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11360.0 (rank 0.0%)
- Population diversity: 0.7636363636363637
- Evolution phase: Exploration (iter 0/5, no elites yet, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [16,, {'region': 'NE 
- Difficult regions to avoid (sample): {'region': [34,, {'region': [0, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:21:41,866 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:21:41,866 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:21:52,822 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0],
  "targeted_regions": "NE opportunity region, sparse adjacent cells",
  "strategy_comment": "Spiral pattern from NE, avoiding dense SW clusters"
}
```
2025-06-26 18:21:52,822 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:21:52,822 - ExplorationExpert - INFO - 探索路径生成完成，成本: 46445.0, 路径: [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0]
2025-06-26 18:21:52,822 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}
2025-06-26 18:21:52,825 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:21:52,825 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:21:52,825 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 78636.0 (rank 40.0%)
- Population diversity: 0.6977272727272728
- Evolution phase: Exploration (iter 0/5, no elites yet, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [16,, {'region': 'NE 
- Difficult regions to avoid (sample): {'region': [34,, {'region': [0, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:21:52,825 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:21:52,827 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:22:03,826 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0],
  "targeted_regions": "NE opportunity regions and adjacent sparse cells",
  "strategy_comment": "Spiral pattern focusing on NE, avoiding difficult SW"
}
```
2025-06-26 18:22:03,826 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:22:03,826 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34403.0, 路径: [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0]
2025-06-26 18:22:03,826 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0], 'cur_cost': 34403.0}
2025-06-26 18:22:03,826 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:22:03,832 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:22:03,832 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106571.0 (rank 90.0%)
- Population diversity: 0.6409090909090909
- Evolution phase: Exploration (iter 0/5, no elites yet, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [16,, {'region': 'NE 
- Difficult regions to avoid (sample): {'region': [34,, {'region': [0, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:22:03,832 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:22:03,832 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:22:15,719 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 0],
  "targeted_regions": "NE opportunity regions, sparse adjacent cells",
  "strategy_comment": "Spiral pattern focusing on NE, avoiding difficult SW"
}
```
2025-06-26 18:22:15,719 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:22:15,719 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34350.0, 路径: [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 0]
2025-06-26 18:22:15,719 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 0], 'cur_cost': 34350.0}
2025-06-26 18:22:15,719 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:22:15,719 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:22:15,727 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 86659.0 (rank 70.0%)
- Population diversity: 0.5568181818181818
- Evolution phase: Exploration (iter 0/5, no elites yet, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [16,, {'region': 'NE 
- Difficult regions to avoid (sample): {'region': [34,, {'region': [0, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:22:15,728 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:22:15,728 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:22:27,788 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [16, 42, 39, 12, 28, 5, 23, 37, 8, 19, 31, 44, 2, 14, 26, 7, 33, 10, 21, 38, 3, 17, 29, 41, 6, 20, 32, 45, 9, 22, 36, 1, 13, 25, 40, 4, 18, 30, 43, 11, 24, 35, 0, 15, 27],
  "targeted_regions": "NE opportunity regions and adjacent sparse cells",
  "strategy_comment": "Prioritize NE clusters while avoiding high-cost SW zones"
}
```
2025-06-26 18:22:27,788 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:22:27,788 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113162.0, 路径: [16, 42, 39, 12, 28, 5, 23, 37, 8, 19, 31, 44, 2, 14, 26, 7, 33, 10, 21, 38, 3, 17, 29, 41, 6, 20, 32, 18, 9, 22, 36, 1, 13, 25, 40, 4, 30, 43, 11, 24, 35, 0, 15, 27, 34]
2025-06-26 18:22:27,788 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [16, 42, 39, 12, 28, 5, 23, 37, 8, 19, 31, 44, 2, 14, 26, 7, 33, 10, 21, 38, 3, 17, 29, 41, 6, 20, 32, 18, 9, 22, 36, 1, 13, 25, 40, 4, 30, 43, 11, 24, 35, 0, 15, 27, 34], 'cur_cost': 113162.0}
2025-06-26 18:22:27,788 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:22:27,788 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:22:27,788 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 76731.0 (rank 50.0%)
- Population diversity: 0.5613636363636364
- Evolution phase: Exploration (iter 0/5, no elites yet, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [16,, {'region': 'NE 
- Difficult regions to avoid (sample): {'region': [34,, {'region': [0, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:22:27,788 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:22:27,788 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:22:38,889 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0],
  "targeted_regions": "NE and adjacent sparse cells",
  "strategy_comment": "Spiral pattern focusing on NE, avoiding difficult SW regions"
}
```
2025-06-26 18:22:38,894 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:22:38,894 - ExplorationExpert - INFO - 探索路径生成完成，成本: 34403.0, 路径: [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0]
2025-06-26 18:22:38,894 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0], 'cur_cost': 34403.0}
2025-06-26 18:22:38,895 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 18:22:38,896 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:22:38,896 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 89909.0 (rank 80.0%)
- Population diversity: 0.47954545454545455
- Evolution phase: Exploration (iter 0/5, no elites yet, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [16,, {'region': 'NE 
- Difficult regions to avoid (sample): {'region': [34,, {'region': [0, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:22:38,896 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:22:38,896 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:22:50,771 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0],
  "targeted_regions": "NE opportunity regions, sparse cells near 16",
  "strategy_comment": "Prioritize NE, spiral outward avoiding dense clusters"
}
```
2025-06-26 18:22:50,775 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:22:50,775 - ExplorationExpert - INFO - 探索路径生成完成，成本: 46445.0, 路径: [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0]
2025-06-26 18:22:50,775 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}
2025-06-26 18:22:50,775 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:22:50,775 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:22:50,776 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:22:50,776 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 88665.0
2025-06-26 18:22:51,278 - ExploitationExpert - INFO - res_population_num: 16
2025-06-26 18:22:51,278 - ExploitationExpert - INFO - res_population_costs: [10979, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973]
2025-06-26 18:22:51,278 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-06-26 18:22:51,283 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:22:51,284 - ExploitationExpert - INFO - populations: [{'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}, {'tour': [16, 12, 8, 4, 0, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 42, 38, 34, 30, 26, 22, 18, 14, 10, 6, 2, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 44, 40, 36, 32, 28, 24, 20], 'cur_cost': 30231.0}, {'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}, {'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0], 'cur_cost': 34403.0}, {'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 0], 'cur_cost': 34350.0}, {'tour': [16, 42, 39, 12, 28, 5, 23, 37, 8, 19, 31, 44, 2, 14, 26, 7, 33, 10, 21, 38, 3, 17, 29, 41, 6, 20, 32, 18, 9, 22, 36, 1, 13, 25, 40, 4, 30, 43, 11, 24, 35, 0, 15, 27, 34], 'cur_cost': 113162.0}, {'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0], 'cur_cost': 34403.0}, {'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}, {'tour': array([ 7, 44, 34, 37, 38,  4, 13, 25, 30, 36,  8, 14,  5,  2, 15, 16,  9,
       28, 40, 11, 20, 26, 12, 32, 33,  6, 35, 24, 23,  1, 10, 31, 39, 29,
        0, 27, 22, 43, 19,  3, 17, 42, 21, 18, 41]), 'cur_cost': 88665.0}, {'tour': [26, 7, 0, 12, 5, 10, 34, 39, 18, 37, 44, 31, 17, 28, 27, 9, 41, 36, 22, 11, 3, 13, 2, 14, 33, 19, 30, 20, 42, 4, 21, 6, 43, 38, 1, 35, 40, 16, 25, 32, 23, 24, 29, 15, 8], 'cur_cost': 78741.0}]
2025-06-26 18:22:51,284 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:22:51,285 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 235, 'skip_rate': 0.03829787234042553, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 226, 'cache_hits': 172, 'similarity_calculations': 3768, 'cache_hit_rate': 0.045647558386411886, 'cache_size': 3596}}
2025-06-26 18:22:51,285 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:22:51,285 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:22:51,285 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:22:51,286 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:22:51,286 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 88333.0
2025-06-26 18:22:51,787 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 18:22:51,787 - ExploitationExpert - INFO - res_population_costs: [10979, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973]
2025-06-26 18:22:51,787 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64)]
2025-06-26 18:22:51,792 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:22:51,793 - ExploitationExpert - INFO - populations: [{'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}, {'tour': [16, 12, 8, 4, 0, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 42, 38, 34, 30, 26, 22, 18, 14, 10, 6, 2, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 44, 40, 36, 32, 28, 24, 20], 'cur_cost': 30231.0}, {'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}, {'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0], 'cur_cost': 34403.0}, {'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 0], 'cur_cost': 34350.0}, {'tour': [16, 42, 39, 12, 28, 5, 23, 37, 8, 19, 31, 44, 2, 14, 26, 7, 33, 10, 21, 38, 3, 17, 29, 41, 6, 20, 32, 18, 9, 22, 36, 1, 13, 25, 40, 4, 30, 43, 11, 24, 35, 0, 15, 27, 34], 'cur_cost': 113162.0}, {'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0], 'cur_cost': 34403.0}, {'tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}, {'tour': array([ 7, 44, 34, 37, 38,  4, 13, 25, 30, 36,  8, 14,  5,  2, 15, 16,  9,
       28, 40, 11, 20, 26, 12, 32, 33,  6, 35, 24, 23,  1, 10, 31, 39, 29,
        0, 27, 22, 43, 19,  3, 17, 42, 21, 18, 41]), 'cur_cost': 88665.0}, {'tour': array([18, 35,  0, 12, 38, 43, 22, 27, 10, 17, 33, 14, 42, 11, 16,  5, 44,
       26, 19, 31, 21, 32, 40, 20,  2,  8, 23,  4, 34, 37,  1,  9, 36, 24,
       13, 41,  3,  7, 15,  6, 28, 30, 29, 39, 25]), 'cur_cost': 88333.0}]
2025-06-26 18:22:51,794 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:22:51,794 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 236, 'skip_rate': 0.038135593220338986, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 227, 'cache_hits': 172, 'similarity_calculations': 3769, 'cache_hit_rate': 0.04563544706818785, 'cache_size': 3597}}
2025-06-26 18:22:51,794 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:22:51,794 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [16, 12, 8, 4, 0, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 42, 38, 34, 30, 26, 22, 18, 14, 10, 6, 2, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 44, 40, 36, 32, 28, 24, 20], 'cur_cost': 30231.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0], 'cur_cost': 34403.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 0], 'cur_cost': 34350.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [16, 42, 39, 12, 28, 5, 23, 37, 8, 19, 31, 44, 2, 14, 26, 7, 33, 10, 21, 38, 3, 17, 29, 41, 6, 20, 32, 18, 9, 22, 36, 1, 13, 25, 40, 4, 30, 43, 11, 24, 35, 0, 15, 27, 34], 'cur_cost': 113162.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 43, 42, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 39, 35, 31, 27, 23, 19, 15, 11, 7, 3, 0], 'cur_cost': 34403.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [16, 12, 8, 4, 20, 24, 28, 32, 36, 40, 44, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 3, 7, 11, 15, 19, 23, 27, 31, 35, 39, 43, 0], 'cur_cost': 46445.0}}]
2025-06-26 18:22:51,795 - __main__ - INFO - 进化阶段完成
2025-06-26 18:22:51,795 - __main__ - INFO - 开始评估阶段
2025-06-26 18:22:51,795 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:22:51,795 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:22:51,795 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 11360.0 → 30231.0 (Δ -18871.0)
- Mean cost: 64067.3 → 56288.2
- Diversity: 0.695 → 0.398 (Δ -0.2977)

#### Elite metrics
- Elite count: 0 → 18
- Best elite cost: 11360.0 → 10973 (Δ 387.0)

#### Strategy performance
- Explore: 8 inds, success 50.0% , avg Δ 10855.12
- Exploit: 2 inds, success 50.0% , avg Δ -4525.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 5
- Historical trends: Last 3 iterations: costs [79778.0, 53337.0, 41956.0], diversity [0.8703703703703705, 0.9089947089947091, 0.9058201058201057]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:22:51,796 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:23:04,792 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "50% success rate with significant average improvement (Δ10855.12)"
    },
    "exploit": {
      "contribution": "low",
      "reason": "50% success rate but negative average impact (Δ-4525.0)"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": "slight reduction in explore"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.6,
    "additional_notes": "Monitor elite count stability; consider increasing exploit attempts if elite diversity drops further"
  }
}
```
2025-06-26 18:23:04,807 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:23:04,807 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "50% success rate with significant average improvement (Δ10855.12)"
    },
    "exploit": {
      "contribution": "low",
      "reason": "50% success rate but negative average impact (Δ-4525.0)"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": "slight reduction in explore"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.6,
    "additional_notes": "Monitor elite count stability; consider increasing exploit attempts if elite diversity drops further"
  }
}
```
2025-06-26 18:23:04,807 - __main__ - INFO - 评估阶段完成
2025-06-26 18:23:04,808 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "50% success rate with significant average improvement (Δ10855.12)"
    },
    "exploit": {
      "contribution": "low",
      "reason": "50% success rate but negative average impact (Δ-4525.0)"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": "slight reduction in explore"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.6,
    "additional_notes": "Monitor elite count stability; consider increasing exploit attempts if elite diversity drops further"
  }
}
```
2025-06-26 18:23:04,809 - __main__ - INFO - 当前最佳适应度: 30231.0
2025-06-26 18:23:04,810 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite8_45_route_0.pkl
2025-06-26 18:23:04,810 - __main__ - INFO - composite8_45 开始进化第 2 代
2025-06-26 18:23:04,810 - __main__ - INFO - 开始分析阶段
2025-06-26 18:23:04,810 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:23:04,819 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 30231.0, 'max': 113162.0, 'mean': 56288.2, 'std': 27782.382042582307}, 'diversity': 0.5688888888888889, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:23:04,819 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 30231.0, 'max': 113162.0, 'mean': 56288.2, 'std': 27782.382042582307}, 'diversity_level': 0.5688888888888889, 'convergence_level': 0.0, 'clustering_info': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'coordinates': [[690, 2780], [665, 2760], [633, 2709], [617, 2736], [662, 2776], [620, 2758], [638, 2774], [638, 2735], [660, 2733], [637, 2749], [709, 2734], [2544, 1844], [2556, 1799], [2529, 1872], [2558, 1831], [2569, 1874], [2516, 1787], [2512, 1852], [2528, 1816], [2505, 1831], [2545, 1820], [2539, 1799], [4451, 2836], [4390, 2772], [4477, 2811], [4450, 2809], [4408, 2821], [4427, 2814], [4469, 2788], [4414, 2795], [4440, 2773], [4389, 2814], [4418, 2758], [2671, 406], [2631, 439], [2685, 455], [2692, 425], [2640, 406], [2684, 391], [2647, 391], [2609, 410], [2631, 463], [2620, 463], [2671, 419], [2652, 420]], 'distance_matrix': array([[   0.,   32.,   91., ..., 3016., 3082., 3069.],
       [  32.,    0.,   60., ..., 3016., 3083., 3070.],
       [  91.,   60.,    0., ..., 2999., 3066., 3052.],
       ...,
       [3016., 3016., 2999., ...,    0.,   67.,   54.],
       [3082., 3083., 3066., ...,   67.,    0.,   19.],
       [3069., 3070., 3052., ...,   54.,   19.,    0.]])}
2025-06-26 18:23:04,819 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:23:04,819 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:23:04,819 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:23:04,821 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:23:04,821 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:23:04,821 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:23:04,821 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:23:04,821 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:23:04,823 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:23:04,827 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:23:04,827 - __main__ - INFO - 精英专家分析报告: {'elite_count': 18, 'elite_common_features': {'common_edges': {'(38, 33)': 0.7222222222222222, '(33, 43)': 0.7222222222222222}, 'common_edge_ratio': 0.044444444444444446}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 19258.0, 'avg_gap': 45314.86666666666}, 'structure_gap': {'unique_elite_edges': 111, 'unique_pop_edges': 198, 'common_edges': 26}}, 'elite_diversity': {'diversity_score': 0.22454611474219321}}
2025-06-26 18:23:04,828 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:23:04,828 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:23:04,828 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:23:04,830 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 30231.0, mean 56288.2, max 113162.0, std 27782.382042582307
- diversity: 0.5688888888888889
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [53337.0, 41956.0, 30231.0] (Δ 11553.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [617.0, 391.0, 4477.0, 2836.0]
- centroid/spread: [2569.689, 1916.622] | rms 1644.594
- density_grid(3x3): [[0, 12, 0], [0, 11, 0], [11, 0, 11]]
- edge_len_stats(min,q1,med,q3,max): [11.0, 1369.25, 2115.5, 3021.25, 3861.0]
- long_edge_ratio: 0.251
- direction_hist(8 sectors): [6, 5, 6, 9, 5, 2, 3, 8]
- nn_median_dist: 21.024

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:23:04,830 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:23:28,144 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 11.0 to 3861.0, std dev ~27782)",
    "modality": "Potentially multimodal (direction histogram shows varied edge orientations)",
    "deceptiveness": "Possible due to long_edge_ratio (25.1% edges > median length) and uneven density_grid"
  },
  "population_state": {
    "coverage": "Patchy (density_grid shows clusters in 3 cells, empty in 6)",
    "convergence": "Early stage (diversity=0.57, no stagnation detected)",
    "elite_influence": "Minimal (0 elites, only 1 fixed node)"
  },
  "difficult_regions": [
    {
      "type": "Long-edge corridor",
      "evidence": "q3 edge length=3021.25, max=3861.0, long_edge_ratio=0.251"
    },
    {
      "type": "Low-density zone",
      "evidence": "Empty density_grid cells (top-left, center-right, etc.)"
    }
  ],
  "opportunity_regions": [
    {
      "type": "High-density cell",
      "location": "Bottom-left (11 nodes) and top-right (11 nodes)",
      "evidence": "density_grid hotspots"
    },
    {
      "type": "Short-edge cluster",
      "evidence": "min edge length=11.0, nn_median_dist=21.024"
    }
  ],
  "evolution_phase": "Exploration (iter 1/5, no convergence)",
  "evolution_direction": [
    "Prioritize density-guided operators for opportunity regions (e.g. localized 2-opt in high-density cells)",
    "Add distance-aware mutation (e.g. penalize long edges in difficult regions)",
    "Maintain diversity via restart mechanisms in low-density zones"
  ]
}
```
2025-06-26 18:23:28,151 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:23:28,151 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 11.0 to 3861.0, std dev ~27782)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations)', 'deceptiveness': 'Possible due to long_edge_ratio (25.1% edges > median length) and uneven density_grid'}, 'population_state': {'coverage': 'Patchy (density_grid shows clusters in 3 cells, empty in 6)', 'convergence': 'Early stage (diversity=0.57, no stagnation detected)', 'elite_influence': 'Minimal (0 elites, only 1 fixed node)'}, 'difficult_regions': [{'type': 'Long-edge corridor', 'evidence': 'q3 edge length=3021.25, max=3861.0, long_edge_ratio=0.251'}, {'type': 'Low-density zone', 'evidence': 'Empty density_grid cells (top-left, center-right, etc.)'}], 'opportunity_regions': [{'type': 'High-density cell', 'location': 'Bottom-left (11 nodes) and top-right (11 nodes)', 'evidence': 'density_grid hotspots'}, {'type': 'Short-edge cluster', 'evidence': 'min edge length=11.0, nn_median_dist=21.024'}], 'evolution_phase': 'Exploration (iter 1/5, no convergence)', 'evolution_direction': ['Prioritize density-guided operators for opportunity regions (e.g. localized 2-opt in high-density cells)', 'Add distance-aware mutation (e.g. penalize long edges in difficult regions)', 'Maintain diversity via restart mechanisms in low-density zones']}
2025-06-26 18:23:28,152 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:23:28,152 - __main__ - INFO - 分析阶段完成
2025-06-26 18:23:28,152 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 11.0 to 3861.0, std dev ~27782)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations)', 'deceptiveness': 'Possible due to long_edge_ratio (25.1% edges > median length) and uneven density_grid'}, 'population_state': {'coverage': 'Patchy (density_grid shows clusters in 3 cells, empty in 6)', 'convergence': 'Early stage (diversity=0.57, no stagnation detected)', 'elite_influence': 'Minimal (0 elites, only 1 fixed node)'}, 'difficult_regions': [{'type': 'Long-edge corridor', 'evidence': 'q3 edge length=3021.25, max=3861.0, long_edge_ratio=0.251'}, {'type': 'Low-density zone', 'evidence': 'Empty density_grid cells (top-left, center-right, etc.)'}], 'opportunity_regions': [{'type': 'High-density cell', 'location': 'Bottom-left (11 nodes) and top-right (11 nodes)', 'evidence': 'density_grid hotspots'}, {'type': 'Short-edge cluster', 'evidence': 'min edge length=11.0, nn_median_dist=21.024'}], 'evolution_phase': 'Exploration (iter 1/5, no convergence)', 'evolution_direction': ['Prioritize density-guided operators for opportunity regions (e.g. localized 2-opt in high-density cells)', 'Add distance-aware mutation (e.g. penalize long edges in difficult regions)', 'Maintain diversity via restart mechanisms in low-density zones']}
2025-06-26 18:23:28,152 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:23:28,152 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:23:28,152 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 30231.0
  • mean_cost: 56288.2
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 11.0 to 3861.0, std dev ~27782), modality Potentially multimodal (direction histogram shows varied edge orientations), deceptiveness Possible due to long_edge_ratio (25.1% edges > median length) and uneven density_grid
- Evolution phase: Exploration (iter 1/5, no convergence)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'Long-edge , {'type': 'Low-densit
- Opportunity regions (# 2): {'type': 'High-densi, {'type': 'Short-edge
- Last-iteration feedback: ```json
{
  "evolution_stage": "early-mid",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:23:28,154 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:23:28,154 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:23:38,100 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 18:23:38,100 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:23:38,100 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:23:38,100 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 18:23:38,100 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:23:38,100 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:23:38,100 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 18:23:38,100 - __main__ - INFO - 开始进化阶段
2025-06-26 18:23:38,100 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:23:38,100 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:23:38,100 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 46445.0 (rank 40.0%)
- Population diversity: 0.3977272727272727
- Evolution phase: Exploration (iter 1/5, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:23:38,100 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:23:38,100 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:23:50,237 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Sequential exploration to target sparse regions"
}
```
2025-06-26 18:23:50,237 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:23:50,239 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:23:50,239 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:23:50,239 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:23:50,239 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:23:50,239 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 30231.0 (rank 10.0%)
- Population diversity: 0.47045454545454546
- Evolution phase: Exploration (iter 1/5, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:23:50,239 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:23:50,239 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:24:01,394 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Sequential exploration to target sparse regions"
}
```
2025-06-26 18:24:01,394 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:24:01,394 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:24:01,394 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:24:01,394 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:24:01,394 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:24:01,394 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 46445.0 (rank 50.0%)
- Population diversity: 0.4681818181818182
- Evolution phase: Exploration (iter 1/5, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:24:01,397 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:24:01,397 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:24:12,209 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Sequential exploration to maximize diversity"
}
```
2025-06-26 18:24:12,209 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:24:12,209 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:24:12,209 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:24:12,209 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:24:12,212 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:24:12,212 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 34403.0 (rank 40.0%)
- Population diversity: 0.4681818181818182
- Evolution phase: Exploration (iter 1/5, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:24:12,212 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:24:12,212 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:24:23,250 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "High-, Short",
  "strategy_comment": "Sequential exploration with minor shuffles"
}
```
2025-06-26 18:24:23,250 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:24:23,250 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:24:23,250 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:24:23,250 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:24:23,250 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:24:23,250 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 34350.0 (rank 40.0%)
- Population diversity: 0.4681818181818182
- Evolution phase: Exploration (iter 1/5, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:24:23,250 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:24:23,250 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:24:34,266 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Balanced exploration with modular jumps"
}
```
2025-06-26 18:24:34,266 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:24:34,266 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112974.0, 路径: [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44]
2025-06-26 18:24:34,266 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}
2025-06-26 18:24:34,266 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:24:34,266 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:24:34,266 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 113162.0 (rank 90.0%)
- Population diversity: 0.5318181818181819
- Evolution phase: Exploration (iter 1/5, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:24:34,266 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:24:34,266 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:24:45,299 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 35, 40, 44, 39, 34, 29, 24, 19, 14, 9, 4, 1, 6, 11, 16, 21, 26, 31, 36, 41, 42, 37, 32, 27, 22, 17, 12, 7, 2, 8, 13, 18, 23, 28, 33, 38, 43, 3],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Balanced exploration with alternating high/low density regions"
}
```
2025-06-26 18:24:45,299 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:24:45,299 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40477.0, 路径: [0, 5, 10, 15, 20, 25, 30, 35, 40, 44, 39, 34, 29, 24, 19, 14, 9, 4, 1, 6, 11, 16, 21, 26, 31, 36, 41, 42, 37, 32, 27, 22, 17, 12, 7, 2, 8, 13, 18, 23, 28, 33, 38, 43, 3]
2025-06-26 18:24:45,306 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 44, 39, 34, 29, 24, 19, 14, 9, 4, 1, 6, 11, 16, 21, 26, 31, 36, 41, 42, 37, 32, 27, 22, 17, 12, 7, 2, 8, 13, 18, 23, 28, 33, 38, 43, 3], 'cur_cost': 40477.0}
2025-06-26 18:24:45,306 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:24:45,306 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:24:45,307 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 34403.0 (rank 40.0%)
- Population diversity: 0.5431818181818182
- Evolution phase: Exploration (iter 1/5, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:24:45,307 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:24:45,307 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:24:56,767 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Balanced exploration with modular jumps"
}
```
2025-06-26 18:24:56,767 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:24:56,767 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112974.0, 路径: [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44]
2025-06-26 18:24:56,767 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}
2025-06-26 18:24:56,772 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 18:24:56,772 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:24:56,772 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 46445.0 (rank 50.0%)
- Population diversity: 0.5409090909090909
- Evolution phase: Exploration (iter 1/5, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:24:56,772 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:24:56,772 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:25:06,681 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Sequential exploration with sparse adjacency"
}
```
2025-06-26 18:25:06,681 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:25:06,681 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:25:06,681 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:25:06,681 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:25:06,681 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:25:06,681 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:25:06,681 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 99332.0
2025-06-26 18:25:07,183 - ExploitationExpert - INFO - res_population_num: 20
2025-06-26 18:25:07,183 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979, 10973, 10973]
2025-06-26 18:25:07,183 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64)]
2025-06-26 18:25:07,189 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:25:07,189 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 44, 39, 34, 29, 24, 19, 14, 9, 4, 1, 6, 11, 16, 21, 26, 31, 36, 41, 42, 37, 32, 27, 22, 17, 12, 7, 2, 8, 13, 18, 23, 28, 33, 38, 43, 3], 'cur_cost': 40477.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': array([ 8, 28, 35, 32, 41, 37, 23, 22, 18, 38, 36, 27, 26, 17,  4, 15, 43,
       10,  6, 40, 12, 31, 42, 25, 14,  2, 30, 13, 11,  1, 16,  9, 33, 21,
       24,  0, 20,  5, 19, 44,  3, 39, 29,  7, 34]), 'cur_cost': 99332.0}, {'tour': array([18, 35,  0, 12, 38, 43, 22, 27, 10, 17, 33, 14, 42, 11, 16,  5, 44,
       26, 19, 31, 21, 32, 40, 20,  2,  8, 23,  4, 34, 37,  1,  9, 36, 24,
       13, 41,  3,  7, 15,  6, 28, 30, 29, 39, 25]), 'cur_cost': 88333.0}]
2025-06-26 18:25:07,189 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:25:07,190 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 237, 'skip_rate': 0.0379746835443038, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 228, 'cache_hits': 172, 'similarity_calculations': 3771, 'cache_hit_rate': 0.045611243701935825, 'cache_size': 3599}}
2025-06-26 18:25:07,190 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:25:07,190 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:25:07,190 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:25:07,190 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:25:07,191 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 72363.0
2025-06-26 18:25:07,693 - ExploitationExpert - INFO - res_population_num: 20
2025-06-26 18:25:07,694 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979, 10973, 10973]
2025-06-26 18:25:07,694 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64)]
2025-06-26 18:25:07,699 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:25:07,699 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 44, 39, 34, 29, 24, 19, 14, 9, 4, 1, 6, 11, 16, 21, 26, 31, 36, 41, 42, 37, 32, 27, 22, 17, 12, 7, 2, 8, 13, 18, 23, 28, 33, 38, 43, 3], 'cur_cost': 40477.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': array([ 8, 28, 35, 32, 41, 37, 23, 22, 18, 38, 36, 27, 26, 17,  4, 15, 43,
       10,  6, 40, 12, 31, 42, 25, 14,  2, 30, 13, 11,  1, 16,  9, 33, 21,
       24,  0, 20,  5, 19, 44,  3, 39, 29,  7, 34]), 'cur_cost': 99332.0}, {'tour': array([13, 28, 23,  5, 24, 27, 31, 15, 42, 16, 41, 22, 26,  0,  1, 12, 32,
       38, 34, 11, 21, 40, 35, 43, 14,  3,  9, 17,  6,  2, 10, 37,  7, 29,
        4, 39, 18, 30, 25, 36, 44,  8, 20, 19, 33]), 'cur_cost': 72363.0}]
2025-06-26 18:25:07,700 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:25:07,700 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 238, 'skip_rate': 0.037815126050420166, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 229, 'cache_hits': 172, 'similarity_calculations': 3774, 'cache_hit_rate': 0.04557498675145734, 'cache_size': 3602}}
2025-06-26 18:25:07,700 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:25:07,700 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 44, 39, 34, 29, 24, 19, 14, 9, 4, 1, 6, 11, 16, 21, 26, 31, 36, 41, 42, 37, 32, 27, 22, 17, 12, 7, 2, 8, 13, 18, 23, 28, 33, 38, 43, 3], 'cur_cost': 40477.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}]
2025-06-26 18:25:07,701 - __main__ - INFO - 进化阶段完成
2025-06-26 18:25:07,701 - __main__ - INFO - 开始评估阶段
2025-06-26 18:25:07,701 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:25:07,702 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:25:07,702 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 30231.0 → 12157.0 (Δ 18074.0)
- Mean cost: 56288.2 → 49890.5
- Diversity: 0.398 → 0.468 (Δ 0.0705)

#### Elite metrics
- Elite count: 18 → 20
- Best elite cost: 10973 → 10973 (Δ 0)

#### Strategy performance
- Explore: 8 inds, success 75.0% , avg Δ 7334.25
- Exploit: 2 inds, success 50.0% , avg Δ 2651.5

#### Other indicators
- No-change individuals: 0
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [53337.0, 41956.0, 30231.0], diversity [0.9089947089947091, 0.9058201058201057, 0.5688888888888889]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:25:07,702 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:25:20,844 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 75.0,
      "average_delta": 7334.25,
      "contribution": "high"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": 2651.5,
      "contribution": "moderate"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": "slight reduction in explore"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "additional_suggestions": "Maintain high diversity focus while slightly rebalancing toward exploit to capitalize on current exploration success"
  }
}
```
2025-06-26 18:25:20,859 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:25:20,859 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 75.0,
      "average_delta": 7334.25,
      "contribution": "high"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": 2651.5,
      "contribution": "moderate"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": "slight reduction in explore"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "additional_suggestions": "Maintain high diversity focus while slightly rebalancing toward exploit to capitalize on current exploration success"
  }
}
```
2025-06-26 18:25:20,859 - __main__ - INFO - 评估阶段完成
2025-06-26 18:25:20,860 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 75.0,
      "average_delta": 7334.25,
      "contribution": "high"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": 2651.5,
      "contribution": "moderate"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": "slight reduction in explore"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "additional_suggestions": "Maintain high diversity focus while slightly rebalancing toward exploit to capitalize on current exploration success"
  }
}
```
2025-06-26 18:25:20,861 - __main__ - INFO - 当前最佳适应度: 12157.0
2025-06-26 18:25:20,862 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite8_45_route_1.pkl
2025-06-26 18:25:20,862 - __main__ - INFO - composite8_45 开始进化第 3 代
2025-06-26 18:25:20,862 - __main__ - INFO - 开始分析阶段
2025-06-26 18:25:20,862 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:25:20,871 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 12157.0, 'max': 112974.0, 'mean': 49890.5, 'std': 42563.16508026629}, 'diversity': 0.7274074074074073, 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:25:20,872 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 12157.0, 'max': 112974.0, 'mean': 49890.5, 'std': 42563.16508026629}, 'diversity_level': 0.7274074074074073, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'coordinates': [[690, 2780], [665, 2760], [633, 2709], [617, 2736], [662, 2776], [620, 2758], [638, 2774], [638, 2735], [660, 2733], [637, 2749], [709, 2734], [2544, 1844], [2556, 1799], [2529, 1872], [2558, 1831], [2569, 1874], [2516, 1787], [2512, 1852], [2528, 1816], [2505, 1831], [2545, 1820], [2539, 1799], [4451, 2836], [4390, 2772], [4477, 2811], [4450, 2809], [4408, 2821], [4427, 2814], [4469, 2788], [4414, 2795], [4440, 2773], [4389, 2814], [4418, 2758], [2671, 406], [2631, 439], [2685, 455], [2692, 425], [2640, 406], [2684, 391], [2647, 391], [2609, 410], [2631, 463], [2620, 463], [2671, 419], [2652, 420]], 'distance_matrix': array([[   0.,   32.,   91., ..., 3016., 3082., 3069.],
       [  32.,    0.,   60., ..., 3016., 3083., 3070.],
       [  91.,   60.,    0., ..., 2999., 3066., 3052.],
       ...,
       [3016., 3016., 2999., ...,    0.,   67.,   54.],
       [3082., 3083., 3066., ...,   67.,    0.,   19.],
       [3069., 3070., 3052., ...,   54.,   19.,    0.]])}
2025-06-26 18:25:20,872 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:25:20,872 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:25:20,872 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:25:20,874 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:25:20,874 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:25:20,875 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:25:20,875 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:25:20,876 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:25:20,876 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:25:20,879 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:25:20,879 - __main__ - INFO - 精英专家分析报告: {'elite_count': 20, 'elite_common_features': {'common_edges': {'(38, 33)': 0.7, '(33, 43)': 0.7, '(22, 25)': 0.7}, 'common_edge_ratio': 0.06666666666666667}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1184.0, 'avg_gap': 38917.2}, 'structure_gap': {'unique_elite_edges': 112, 'unique_pop_edges': 193, 'common_edges': 26}}, 'elite_diversity': {'diversity_score': 0.23660818713450293}}
2025-06-26 18:25:20,879 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:25:20,879 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:25:20,883 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:25:20,883 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 12157.0, mean 49890.5, max 112974.0, std 42563.16508026629
- diversity: 0.7274074074074073
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [41956.0, 30231.0, 12157.0] (Δ 14899.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [617.0, 391.0, 4477.0, 2836.0]
- centroid/spread: [2569.689, 1916.622] | rms 1644.594
- density_grid(3x3): [[0, 12, 0], [0, 11, 0], [11, 0, 11]]
- edge_len_stats(min,q1,med,q3,max): [11.0, 1369.25, 2115.5, 3021.25, 3861.0]
- long_edge_ratio: 0.251
- direction_hist(8 sectors): [6, 5, 6, 9, 5, 2, 3, 8]
- nn_median_dist: 21.024

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:25:20,884 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:25:39,555 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 11.0 to 3861.0, with significant standard deviation)",
    "modality": "Potentially multimodal (varied direction histogram and edge length distribution)",
    "deceptiveness": "Possible presence (long_edge_ratio of 0.251 suggests some deceptive long edges)"
  },
  "population_state": {
    "coverage": "Moderate (diversity 0.727, but density_grid shows uneven distribution with some empty cells)",
    "convergence": "Early stage (convergence 0.0, recent best costs show improvement but no stagnation)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges in max quartile 3021.25-3861.0)",
    "Low-density grid cells (top-left and bottom-center in 3x3 grid)"
  ],
  "opportunity_regions": [
    "High-density grid cells (center-left and bottom-right in 3x3 grid)",
    "Short-edge clusters (edges in min quartile 11.0-1369.25)"
  ],
  "evolution_phase": "Exploration (early iteration with improving costs and no convergence)",
  "evolution_direction": [
    "Prioritize edge recombination operators for high-density regions",
    "Apply local optimization on short-edge clusters",
    "Use mutation operators to explore long-edge corridors",
    "Maintain diversity through spatial-aware selection"
  ]
}
```
2025-06-26 18:25:39,555 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:25:39,555 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 11.0 to 3861.0, with significant standard deviation)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible presence (long_edge_ratio of 0.251 suggests some deceptive long edges)'}, 'population_state': {'coverage': 'Moderate (diversity 0.727, but density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs show improvement but no stagnation)'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile 3021.25-3861.0)', 'Low-density grid cells (top-left and bottom-center in 3x3 grid)'], 'opportunity_regions': ['High-density grid cells (center-left and bottom-right in 3x3 grid)', 'Short-edge clusters (edges in min quartile 11.0-1369.25)'], 'evolution_phase': 'Exploration (early iteration with improving costs and no convergence)', 'evolution_direction': ['Prioritize edge recombination operators for high-density regions', 'Apply local optimization on short-edge clusters', 'Use mutation operators to explore long-edge corridors', 'Maintain diversity through spatial-aware selection']}
2025-06-26 18:25:39,555 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:25:39,557 - __main__ - INFO - 分析阶段完成
2025-06-26 18:25:39,557 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 11.0 to 3861.0, with significant standard deviation)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible presence (long_edge_ratio of 0.251 suggests some deceptive long edges)'}, 'population_state': {'coverage': 'Moderate (diversity 0.727, but density_grid shows uneven distribution with some empty cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs show improvement but no stagnation)'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile 3021.25-3861.0)', 'Low-density grid cells (top-left and bottom-center in 3x3 grid)'], 'opportunity_regions': ['High-density grid cells (center-left and bottom-right in 3x3 grid)', 'Short-edge clusters (edges in min quartile 11.0-1369.25)'], 'evolution_phase': 'Exploration (early iteration with improving costs and no convergence)', 'evolution_direction': ['Prioritize edge recombination operators for high-density regions', 'Apply local optimization on short-edge clusters', 'Use mutation operators to explore long-edge corridors', 'Maintain diversity through spatial-aware selection']}
2025-06-26 18:25:39,557 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:25:39,557 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:25:39,557 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 12157.0
  • mean_cost: 49890.5
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 11.0 to 3861.0, with significant standard deviation), modality Potentially multimodal (varied direction histogram and edge length distribution), deceptiveness Possible presence (long_edge_ratio of 0.251 suggests some deceptive long edges)
- Evolution phase: Exploration (early iteration with improving costs and no convergence)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Low-density grid cel
- Opportunity regions (# 2): High-density grid ce, Short-edge clusters 
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 75.0,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:25:39,557 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:25:39,557 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:25:49,019 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 30% individuals exploit opportunity regions."
}
```
2025-06-26 18:25:49,019 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:25:49,019 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:25:49,019 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 30% individuals exploit opportunity regions."
}
```
2025-06-26 18:25:49,019 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:25:49,024 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:25:49,024 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 30% individuals exploit opportunity regions."
}
```
2025-06-26 18:25:49,024 - __main__ - INFO - 开始进化阶段
2025-06-26 18:25:49,024 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:25:49,024 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:25:49,025 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12157.0 (rank 0.0%)
- Population diversity: 0.4681818181818182
- Evolution phase: Exploration (early iteration with improving costs and no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:25:49,025 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:25:49,025 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:26:00,736 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  "targeted_regions": "High-density grid, Short-edge clusters",
  "strategy_comment": "Prioritize dense regions first, then loop back through sparse areas"
}
```
2025-06-26 18:26:00,736 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:26:00,736 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12577.0, 路径: [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
2025-06-26 18:26:00,736 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 12577.0}
2025-06-26 18:26:00,736 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:26:00,736 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:26:00,736 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12157.0 (rank 0.0%)
- Population diversity: 0.47045454545454546
- Evolution phase: Exploration (early iteration with improving costs and no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:26:00,736 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:26:00,736 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:26:12,412 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "High-density gr, Short-edge clus",
  "strategy_comment": "Sequential sweep through opportunity regions"
}
```
2025-06-26 18:26:12,412 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:26:12,416 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:26:12,416 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:26:12,416 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:26:12,416 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:26:12,416 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12157.0 (rank 0.0%)
- Population diversity: 0.47045454545454546
- Evolution phase: Exploration (early iteration with improving costs and no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:26:12,417 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:26:12,417 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:26:23,153 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "High-density gr, Short-edge clus",
  "strategy_comment": "Sequential traversal to explore adjacency"
}
```
2025-06-26 18:26:23,156 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:26:23,156 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:26:23,156 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:26:23,156 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:26:23,156 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:26:23,158 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12157.0 (rank 0.0%)
- Population diversity: 0.47045454545454546
- Evolution phase: Exploration (early iteration with improving costs and no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:26:23,158 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:26:23,158 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:26:34,619 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "High-density gr, Short-edge clus",
  "strategy_comment": "Sequential traversal targeting high-density clusters"
}
```
2025-06-26 18:26:34,619 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:26:34,619 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:26:34,626 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:26:34,626 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:26:34,626 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:26:34,626 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 112974.0 (rank 80.0%)
- Population diversity: 0.47045454545454546
- Evolution phase: Exploration (early iteration with improving costs and no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:26:34,626 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:26:34,626 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:26:45,436 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "High-density gr, Short-edge clus",
  "strategy_comment": "Alternating high-density traversal with sparse adjacency"
}
```
2025-06-26 18:26:45,442 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:26:45,442 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16049.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 18:26:45,442 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16049.0}
2025-06-26 18:26:45,442 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:26:45,442 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:26:45,442 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 40477.0 (rank 60.0%)
- Population diversity: 0.5613636363636364
- Evolution phase: Exploration (early iteration with improving costs and no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:26:45,444 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:26:45,444 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:27:01,378 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "High-density gr, Short-edge clus",
  "strategy_comment": "Sequential path targeting high-density and short-edge clusters"
}
```
2025-06-26 18:27:01,384 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:27:01,384 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:27:01,384 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:27:01,384 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:27:01,384 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:27:01,384 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 112974.0 (rank 90.0%)
- Population diversity: 0.47045454545454546
- Evolution phase: Exploration (early iteration with improving costs and no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:27:01,384 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:27:01,384 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:27:11,642 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "High-density gr, Short-edge clus",
  "strategy_comment": "Sequential exploration of adjacent nodes"
}
```
2025-06-26 18:27:11,642 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:27:11,648 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:27:11,648 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:27:11,649 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:27:11,650 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:27:11,650 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:27:11,650 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 86026.0
2025-06-26 18:27:12,154 - ExploitationExpert - INFO - res_population_num: 20
2025-06-26 18:27:12,155 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979]
2025-06-26 18:27:12,155 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-06-26 18:27:12,161 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:27:12,161 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 12577.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16049.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': array([21, 40, 13, 30, 28,  4, 25, 24,  2,  9, 35, 29, 37,  5, 31, 19, 39,
       12, 44, 43, 20, 15,  7,  8, 18, 32, 27, 26,  1, 22, 41, 34,  0, 23,
       38, 17, 33, 36, 14,  6, 16,  3, 11, 10, 42]), 'cur_cost': 86026.0}, {'tour': array([ 8, 28, 35, 32, 41, 37, 23, 22, 18, 38, 36, 27, 26, 17,  4, 15, 43,
       10,  6, 40, 12, 31, 42, 25, 14,  2, 30, 13, 11,  1, 16,  9, 33, 21,
       24,  0, 20,  5, 19, 44,  3, 39, 29,  7, 34]), 'cur_cost': 99332.0}, {'tour': array([13, 28, 23,  5, 24, 27, 31, 15, 42, 16, 41, 22, 26,  0,  1, 12, 32,
       38, 34, 11, 21, 40, 35, 43, 14,  3,  9, 17,  6,  2, 10, 37,  7, 29,
        4, 39, 18, 30, 25, 36, 44,  8, 20, 19, 33]), 'cur_cost': 72363.0}]
2025-06-26 18:27:12,162 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:27:12,162 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 239, 'skip_rate': 0.03765690376569038, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 230, 'cache_hits': 172, 'similarity_calculations': 3778, 'cache_hit_rate': 0.04552673372154579, 'cache_size': 3606}}
2025-06-26 18:27:12,162 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:27:12,163 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:27:12,163 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:27:12,163 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:27:12,163 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 86034.0
2025-06-26 18:27:12,666 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 18:27:12,666 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979, 10973]
2025-06-26 18:27:12,666 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64)]
2025-06-26 18:27:12,677 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:27:12,677 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 12577.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16049.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': array([21, 40, 13, 30, 28,  4, 25, 24,  2,  9, 35, 29, 37,  5, 31, 19, 39,
       12, 44, 43, 20, 15,  7,  8, 18, 32, 27, 26,  1, 22, 41, 34,  0, 23,
       38, 17, 33, 36, 14,  6, 16,  3, 11, 10, 42]), 'cur_cost': 86026.0}, {'tour': array([33, 29, 26, 41, 15, 30, 40, 39, 32,  1, 12, 24, 13,  7, 43, 36, 11,
        3, 34, 22, 20, 37, 21, 10,  4, 17, 35,  5, 44, 42, 18,  2,  9, 27,
       31, 28,  0, 23, 25, 38, 16,  8, 19, 14,  6]), 'cur_cost': 86034.0}, {'tour': array([13, 28, 23,  5, 24, 27, 31, 15, 42, 16, 41, 22, 26,  0,  1, 12, 32,
       38, 34, 11, 21, 40, 35, 43, 14,  3,  9, 17,  6,  2, 10, 37,  7, 29,
        4, 39, 18, 30, 25, 36, 44,  8, 20, 19, 33]), 'cur_cost': 72363.0}]
2025-06-26 18:27:12,679 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 18:27:12,679 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 240, 'skip_rate': 0.0375, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 231, 'cache_hits': 172, 'similarity_calculations': 3783, 'cache_hit_rate': 0.045466560930478454, 'cache_size': 3611}}
2025-06-26 18:27:12,679 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:27:12,679 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:27:12,679 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:27:12,679 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:27:12,682 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 89067.0
2025-06-26 18:27:13,184 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 18:27:13,184 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979, 10973]
2025-06-26 18:27:13,184 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64)]
2025-06-26 18:27:13,191 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:27:13,192 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 12577.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16049.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': array([21, 40, 13, 30, 28,  4, 25, 24,  2,  9, 35, 29, 37,  5, 31, 19, 39,
       12, 44, 43, 20, 15,  7,  8, 18, 32, 27, 26,  1, 22, 41, 34,  0, 23,
       38, 17, 33, 36, 14,  6, 16,  3, 11, 10, 42]), 'cur_cost': 86026.0}, {'tour': array([33, 29, 26, 41, 15, 30, 40, 39, 32,  1, 12, 24, 13,  7, 43, 36, 11,
        3, 34, 22, 20, 37, 21, 10,  4, 17, 35,  5, 44, 42, 18,  2,  9, 27,
       31, 28,  0, 23, 25, 38, 16,  8, 19, 14,  6]), 'cur_cost': 86034.0}, {'tour': array([33, 36, 44, 16,  7, 21, 19,  1, 14, 11, 35, 32, 25, 34, 13,  5, 10,
       38, 22,  2, 15, 30, 41,  0,  4, 18,  3, 31, 24, 42, 17, 39, 28, 20,
        8, 29, 27, 37, 26, 40,  6, 12, 23,  9, 43]), 'cur_cost': 89067.0}]
2025-06-26 18:27:13,194 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:27:13,194 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 241, 'skip_rate': 0.03734439834024896, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 232, 'cache_hits': 172, 'similarity_calculations': 3789, 'cache_hit_rate': 0.04539456320929005, 'cache_size': 3617}}
2025-06-26 18:27:13,194 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:27:13,194 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 12577.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 16049.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}]
2025-06-26 18:27:13,195 - __main__ - INFO - 进化阶段完成
2025-06-26 18:27:13,195 - __main__ - INFO - 开始评估阶段
2025-06-26 18:27:13,195 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:27:13,196 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:27:13,196 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 12157.0 → 12157.0 (Δ 0.0)
- Mean cost: 49890.5 → 35053.8
- Diversity: 0.468 → 0.459 (Δ -0.0091)

#### Elite metrics
- Elite count: 20 → 21
- Best elite cost: 10973 → 10973 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 42.9% , avg Δ 32234.57
- Exploit: 3 inds, success 33.3% , avg Δ -25758.33

#### Other indicators
- No-change individuals: 3
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [41956.0, 30231.0, 12157.0], diversity [0.9058201058201057, 0.5688888888888889, 0.7274074074074073]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:27:13,196 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:27:23,559 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "moderate",
      "success_rate": 42.9,
      "average_delta": 32234.57
    },
    "exploit": {
      "contribution": "low",
      "success_rate": 33.3,
      "average_delta": -25758.33
    }
  },
  "balance_state": "explore-heavy with insufficient exploit success",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_attempts": "increase by 2-3 individuals"
  }
}
```
2025-06-26 18:27:23,576 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:27:23,576 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "moderate",
      "success_rate": 42.9,
      "average_delta": 32234.57
    },
    "exploit": {
      "contribution": "low",
      "success_rate": 33.3,
      "average_delta": -25758.33
    }
  },
  "balance_state": "explore-heavy with insufficient exploit success",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_attempts": "increase by 2-3 individuals"
  }
}
```
2025-06-26 18:27:23,577 - __main__ - INFO - 评估阶段完成
2025-06-26 18:27:23,577 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "moderate",
      "success_rate": 42.9,
      "average_delta": 32234.57
    },
    "exploit": {
      "contribution": "low",
      "success_rate": 33.3,
      "average_delta": -25758.33
    }
  },
  "balance_state": "explore-heavy with insufficient exploit success",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_attempts": "increase by 2-3 individuals"
  }
}
```
2025-06-26 18:27:23,577 - __main__ - INFO - 当前最佳适应度: 12157.0
2025-06-26 18:27:23,577 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite8_45_route_2.pkl
2025-06-26 18:27:23,577 - __main__ - INFO - composite8_45 开始进化第 4 代
2025-06-26 18:27:23,577 - __main__ - INFO - 开始分析阶段
2025-06-26 18:27:23,577 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:27:23,588 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 12157.0, 'max': 89067.0, 'mean': 35053.8, 'std': 34062.11671578853}, 'diversity': 0.6385185185185184, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:27:23,588 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 12157.0, 'max': 89067.0, 'mean': 35053.8, 'std': 34062.11671578853}, 'diversity_level': 0.6385185185185184, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'coordinates': [[690, 2780], [665, 2760], [633, 2709], [617, 2736], [662, 2776], [620, 2758], [638, 2774], [638, 2735], [660, 2733], [637, 2749], [709, 2734], [2544, 1844], [2556, 1799], [2529, 1872], [2558, 1831], [2569, 1874], [2516, 1787], [2512, 1852], [2528, 1816], [2505, 1831], [2545, 1820], [2539, 1799], [4451, 2836], [4390, 2772], [4477, 2811], [4450, 2809], [4408, 2821], [4427, 2814], [4469, 2788], [4414, 2795], [4440, 2773], [4389, 2814], [4418, 2758], [2671, 406], [2631, 439], [2685, 455], [2692, 425], [2640, 406], [2684, 391], [2647, 391], [2609, 410], [2631, 463], [2620, 463], [2671, 419], [2652, 420]], 'distance_matrix': array([[   0.,   32.,   91., ..., 3016., 3082., 3069.],
       [  32.,    0.,   60., ..., 3016., 3083., 3070.],
       [  91.,   60.,    0., ..., 2999., 3066., 3052.],
       ...,
       [3016., 3016., 2999., ...,    0.,   67.,   54.],
       [3082., 3083., 3066., ...,   67.,    0.,   19.],
       [3069., 3070., 3052., ...,   54.,   19.,    0.]])}
2025-06-26 18:27:23,589 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:27:23,589 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:27:23,589 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:27:23,591 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:27:23,591 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:27:23,591 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:27:23,591 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:27:23,591 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:27:23,592 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:27:23,598 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:27:23,598 - __main__ - INFO - 精英专家分析报告: {'elite_count': 21, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1184.0, 'avg_gap': 24080.51428571429}, 'structure_gap': {'unique_elite_edges': 93, 'unique_pop_edges': 179, 'common_edges': 45}}, 'elite_diversity': {'diversity_score': 0.23978835978835977}}
2025-06-26 18:27:23,598 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:27:23,599 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:27:23,600 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:27:23,600 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 12157.0, mean 35053.8, max 89067.0, std 34062.11671578853
- diversity: 0.6385185185185184
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [30231.0, 12157.0, 12157.0] (Δ 9037.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [617.0, 391.0, 4477.0, 2836.0]
- centroid/spread: [2569.689, 1916.622] | rms 1644.594
- density_grid(3x3): [[0, 12, 0], [0, 11, 0], [11, 0, 11]]
- edge_len_stats(min,q1,med,q3,max): [11.0, 1369.25, 2115.5, 3021.25, 3861.0]
- long_edge_ratio: 0.251
- direction_hist(8 sectors): [6, 5, 6, 9, 5, 2, 3, 8]
- nn_median_dist: 21.024

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:27:23,601 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:27:49,781 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high ruggedness indicated by wide edge length statistics (min 11.0 to max 3861.0) and a long_edge_ratio of 0.251, suggesting varied terrain with some challenging long edges.",
    "modality": "Potential multimodality suggested by the diversity score (0.638) and the presence of both high and low edge lengths, indicating multiple local optima.",
    "deceptiveness": "Possible deceptiveness due to the lack of clustering information and the presence of long edges that may mislead search algorithms."
  },
  "population_state": {
    "coverage": "Uneven coverage with high density in certain grid cells (e.g., [0,1] and [2,0]) and sparse in others, indicating potential exploration gaps.",
    "convergence": "No convergence detected (convergence score 0.0), with recent best costs showing improvement (Δ 9037.00 per iteration)."
  },
  "difficult_regions": {
    "low_density_cells": ["[0,0]", "[1,0]", "[1,2]", "[2,1]"],
    "long_edge_corridors": "Edges with lengths in the upper quartile (q3 3021.25 to max 3861.0) may represent difficult regions."
  },
  "opportunity_regions": {
    "high_density_cells": ["[0,1]", "[2,0]", "[2,2]"],
    "potential_subpaths": "No common subpaths identified, but high-density cells may contain exploitable node clusters."
  },
  "evolution_phase": "Early exploration phase with improving solutions but no convergence, indicating ongoing diversification.",
  "evolution_direction": {
    "operator_suggestions": [
      "Enhance exploitation in high-density cells (e.g., [0,1], [2,0]) using local search or path refinement operators.",
      "Address difficult regions with adaptive operators (e.g., long-edge recombination or targeted mutation).",
      "Maintain diversity with global operators (e.g., crossover) to explore low-density areas."
    ]
  }
}
```
2025-06-26 18:27:49,781 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:27:49,786 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high ruggedness indicated by wide edge length statistics (min 11.0 to max 3861.0) and a long_edge_ratio of 0.251, suggesting varied terrain with some challenging long edges.', 'modality': 'Potential multimodality suggested by the diversity score (0.638) and the presence of both high and low edge lengths, indicating multiple local optima.', 'deceptiveness': 'Possible deceptiveness due to the lack of clustering information and the presence of long edges that may mislead search algorithms.'}, 'population_state': {'coverage': 'Uneven coverage with high density in certain grid cells (e.g., [0,1] and [2,0]) and sparse in others, indicating potential exploration gaps.', 'convergence': 'No convergence detected (convergence score 0.0), with recent best costs showing improvement (Δ 9037.00 per iteration).'}, 'difficult_regions': {'low_density_cells': ['[0,0]', '[1,0]', '[1,2]', '[2,1]'], 'long_edge_corridors': 'Edges with lengths in the upper quartile (q3 3021.25 to max 3861.0) may represent difficult regions.'}, 'opportunity_regions': {'high_density_cells': ['[0,1]', '[2,0]', '[2,2]'], 'potential_subpaths': 'No common subpaths identified, but high-density cells may contain exploitable node clusters.'}, 'evolution_phase': 'Early exploration phase with improving solutions but no convergence, indicating ongoing diversification.', 'evolution_direction': {'operator_suggestions': ['Enhance exploitation in high-density cells (e.g., [0,1], [2,0]) using local search or path refinement operators.', 'Address difficult regions with adaptive operators (e.g., long-edge recombination or targeted mutation).', 'Maintain diversity with global operators (e.g., crossover) to explore low-density areas.']}}
2025-06-26 18:27:49,786 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:27:49,786 - __main__ - INFO - 分析阶段完成
2025-06-26 18:27:49,786 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high ruggedness indicated by wide edge length statistics (min 11.0 to max 3861.0) and a long_edge_ratio of 0.251, suggesting varied terrain with some challenging long edges.', 'modality': 'Potential multimodality suggested by the diversity score (0.638) and the presence of both high and low edge lengths, indicating multiple local optima.', 'deceptiveness': 'Possible deceptiveness due to the lack of clustering information and the presence of long edges that may mislead search algorithms.'}, 'population_state': {'coverage': 'Uneven coverage with high density in certain grid cells (e.g., [0,1] and [2,0]) and sparse in others, indicating potential exploration gaps.', 'convergence': 'No convergence detected (convergence score 0.0), with recent best costs showing improvement (Δ 9037.00 per iteration).'}, 'difficult_regions': {'low_density_cells': ['[0,0]', '[1,0]', '[1,2]', '[2,1]'], 'long_edge_corridors': 'Edges with lengths in the upper quartile (q3 3021.25 to max 3861.0) may represent difficult regions.'}, 'opportunity_regions': {'high_density_cells': ['[0,1]', '[2,0]', '[2,2]'], 'potential_subpaths': 'No common subpaths identified, but high-density cells may contain exploitable node clusters.'}, 'evolution_phase': 'Early exploration phase with improving solutions but no convergence, indicating ongoing diversification.', 'evolution_direction': {'operator_suggestions': ['Enhance exploitation in high-density cells (e.g., [0,1], [2,0]) using local search or path refinement operators.', 'Address difficult regions with adaptive operators (e.g., long-edge recombination or targeted mutation).', 'Maintain diversity with global operators (e.g., crossover) to explore low-density areas.']}}
2025-06-26 18:27:49,786 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:27:49,788 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:27:49,788 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 12157.0
  • mean_cost: 35053.8
- Search-space: ruggedness Moderate to high ruggedness indicated by wide edge length statistics (min 11.0 to max 3861.0) and a long_edge_ratio of 0.251, suggesting varied terrain with some challenging long edges., modality Potential multimodality suggested by the diversity score (0.638) and the presence of both high and low edge lengths, indicating multiple local optima., deceptiveness Possible deceptiveness due to the lack of clustering information and the presence of long edges that may mislead search algorithms.
- Evolution phase: Early exploration phase with improving solutions but no convergence, indicating ongoing diversification.
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): low_density_cells, long_edge_corridors
- Opportunity regions (# 2): high_density_cells, potential_subpaths
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribut...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:27:49,788 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:27:49,788 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:27:59,260 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 18:27:59,261 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:27:59,262 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:27:59,262 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 18:27:59,262 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:27:59,262 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:27:59,262 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 18:27:59,262 - __main__ - INFO - 开始进化阶段
2025-06-26 18:27:59,264 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:27:59,264 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:27:59,264 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12577.0 (rank 50.0%)
- Population diversity: 0.4590909090909091
- Evolution phase: Early exploration phase with improving solutions but no convergence, indicating ongoing diversification.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:27:59,264 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:27:59,264 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:28:10,565 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 7, 14, 21, 28, 35, 42, 43, 36, 29, 22, 15, 8, 1, 2, 9, 16, 23, 30, 37, 44, 38, 31, 24, 17, 10, 3, 4, 11, 18, 25, 32, 39],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Leverage adjacent sparse cells while avoiding long edges"
}
```
2025-06-26 18:28:10,565 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:28:10,565 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54698.0, 路径: [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 7, 14, 21, 28, 35, 42, 43, 36, 29, 22, 15, 8, 1, 2, 9, 16, 23, 30, 37, 44, 38, 31, 24, 17, 10, 3, 4, 11, 18, 25, 32, 39]
2025-06-26 18:28:10,565 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 7, 14, 21, 28, 35, 42, 43, 36, 29, 22, 15, 8, 1, 2, 9, 16, 23, 30, 37, 44, 38, 31, 24, 17, 10, 3, 4, 11, 18, 25, 32, 39], 'cur_cost': 54698.0}
2025-06-26 18:28:10,565 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:28:10,565 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:28:10,565 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12157.0 (rank 0.0%)
- Population diversity: 0.5295454545454545
- Evolution phase: Early exploration phase with improving solutions but no convergence, indicating ongoing diversification.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:28:10,565 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:28:10,565 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:28:21,654 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Sequential exploration of opportunity regions"
}
```
2025-06-26 18:28:21,654 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:28:21,654 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:28:21,654 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:28:21,654 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:28:21,654 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:28:21,654 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12157.0 (rank 0.0%)
- Population diversity: 0.5295454545454545
- Evolution phase: Early exploration phase with improving solutions but no convergence, indicating ongoing diversification.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:28:21,654 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:28:21,654 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:28:33,321 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Alternate even-odd sequencing to explore sparse cells"
}
```
2025-06-26 18:28:33,329 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:28:33,329 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22150.0, 路径: [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43]
2025-06-26 18:28:33,329 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43], 'cur_cost': 22150.0}
2025-06-26 18:28:33,329 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:28:33,329 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:28:33,329 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12157.0 (rank 0.0%)
- Population diversity: 0.5363636363636364
- Evolution phase: Early exploration phase with improving solutions but no convergence, indicating ongoing diversification.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:28:33,329 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:28:33,329 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:28:45,022 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Prioritize opportunity regions while maintaining diversity"
}
```
2025-06-26 18:28:45,022 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:28:45,030 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:28:45,030 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:28:45,030 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:28:45,030 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:28:45,030 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 16049.0 (rank 40.0%)
- Population diversity: 0.5363636363636364
- Evolution phase: Early exploration phase with improving solutions but no convergence, indicating ongoing diversification.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:28:45,030 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:28:45,030 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:28:56,405 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Diagonal traversal to balance exploration and cost"
}
```
2025-06-26 18:28:56,405 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:28:56,411 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109171.0, 路径: [0, 12, 23, 34, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1]
2025-06-26 18:28:56,411 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1], 'cur_cost': 109171.0}
2025-06-26 18:28:56,411 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:28:56,411 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:28:56,411 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12157.0 (rank 0.0%)
- Population diversity: 0.6136363636363636
- Evolution phase: Early exploration phase with improving solutions but no convergence, indicating ongoing diversification.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:28:56,413 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:28:56,413 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:29:07,905 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Sequential exploration of opportunity regions"
}
```
2025-06-26 18:29:07,905 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:29:07,905 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:29:07,905 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:29:07,905 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 18:29:07,905 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:29:07,905 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:29:07,905 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 84474.0
2025-06-26 18:29:07,909 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：229,跳过局部搜索
2025-06-26 18:29:07,909 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 18:29:07,909 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 5, 36, 41, 37, 39, 25,  1,  2,  4,  8, 24, 17, 35, 29, 40, 30, 16,
       15,  9, 14, 13,  6, 23, 27, 21,  0, 34, 43, 10, 31, 44,  7, 18, 26,
       28, 33, 42, 32, 38, 20, 22, 11,  3, 19, 12]), 'cur_cost': 84474.0}
2025-06-26 18:29:07,909 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:29:07,909 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:29:07,909 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:29:07,909 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 90973.0
2025-06-26 18:29:08,410 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 18:29:08,410 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979]
2025-06-26 18:29:08,410 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-06-26 18:29:08,419 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:29:08,419 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 7, 14, 21, 28, 35, 42, 43, 36, 29, 22, 15, 8, 1, 2, 9, 16, 23, 30, 37, 44, 38, 31, 24, 17, 10, 3, 4, 11, 18, 25, 32, 39], 'cur_cost': 54698.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43], 'cur_cost': 22150.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 12, 23, 34, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1], 'cur_cost': 109171.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': array([ 5, 36, 41, 37, 39, 25,  1,  2,  4,  8, 24, 17, 35, 29, 40, 30, 16,
       15,  9, 14, 13,  6, 23, 27, 21,  0, 34, 43, 10, 31, 44,  7, 18, 26,
       28, 33, 42, 32, 38, 20, 22, 11,  3, 19, 12]), 'cur_cost': 84474.0}, {'tour': array([15, 23, 38, 27, 39,  6, 44, 31, 28, 11, 34, 25,  0,  9,  1, 22, 14,
       17,  2,  5, 20, 19,  8, 18, 10, 24, 40, 42,  7, 36, 21, 29, 26, 13,
       37, 41, 32, 33, 30, 12,  3, 43,  4, 35, 16]), 'cur_cost': 90973.0}, {'tour': array([33, 29, 26, 41, 15, 30, 40, 39, 32,  1, 12, 24, 13,  7, 43, 36, 11,
        3, 34, 22, 20, 37, 21, 10,  4, 17, 35,  5, 44, 42, 18,  2,  9, 27,
       31, 28,  0, 23, 25, 38, 16,  8, 19, 14,  6]), 'cur_cost': 86034.0}, {'tour': array([33, 36, 44, 16,  7, 21, 19,  1, 14, 11, 35, 32, 25, 34, 13,  5, 10,
       38, 22,  2, 15, 30, 41,  0,  4, 18,  3, 31, 24, 42, 17, 39, 28, 20,
        8, 29, 27, 37, 26, 40,  6, 12, 23,  9, 43]), 'cur_cost': 89067.0}]
2025-06-26 18:29:08,422 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:29:08,422 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 243, 'skip_rate': 0.0411522633744856, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 233, 'cache_hits': 176, 'similarity_calculations': 3801, 'cache_hit_rate': 0.04630360431465404, 'cache_size': 3625}}
2025-06-26 18:29:08,422 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:29:08,422 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:29:08,422 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:29:08,422 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:29:08,423 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 105729.0
2025-06-26 18:29:08,927 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 18:29:08,927 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979]
2025-06-26 18:29:08,927 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-06-26 18:29:08,936 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:29:08,936 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 7, 14, 21, 28, 35, 42, 43, 36, 29, 22, 15, 8, 1, 2, 9, 16, 23, 30, 37, 44, 38, 31, 24, 17, 10, 3, 4, 11, 18, 25, 32, 39], 'cur_cost': 54698.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43], 'cur_cost': 22150.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 12, 23, 34, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1], 'cur_cost': 109171.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': array([ 5, 36, 41, 37, 39, 25,  1,  2,  4,  8, 24, 17, 35, 29, 40, 30, 16,
       15,  9, 14, 13,  6, 23, 27, 21,  0, 34, 43, 10, 31, 44,  7, 18, 26,
       28, 33, 42, 32, 38, 20, 22, 11,  3, 19, 12]), 'cur_cost': 84474.0}, {'tour': array([15, 23, 38, 27, 39,  6, 44, 31, 28, 11, 34, 25,  0,  9,  1, 22, 14,
       17,  2,  5, 20, 19,  8, 18, 10, 24, 40, 42,  7, 36, 21, 29, 26, 13,
       37, 41, 32, 33, 30, 12,  3, 43,  4, 35, 16]), 'cur_cost': 90973.0}, {'tour': array([ 9, 41,  4, 23, 11, 26, 38, 27, 43,  2, 35, 37, 42,  3, 44, 31, 21,
       30, 24, 36, 25,  5, 15, 28,  7, 13, 17, 39, 10, 18,  6, 12, 22, 33,
       19, 16,  8, 40, 32, 34,  0, 20, 14,  1, 29]), 'cur_cost': 105729.0}, {'tour': array([33, 36, 44, 16,  7, 21, 19,  1, 14, 11, 35, 32, 25, 34, 13,  5, 10,
       38, 22,  2, 15, 30, 41,  0,  4, 18,  3, 31, 24, 42, 17, 39, 28, 20,
        8, 29, 27, 37, 26, 40,  6, 12, 23,  9, 43]), 'cur_cost': 89067.0}]
2025-06-26 18:29:08,938 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 18:29:08,938 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 244, 'skip_rate': 0.040983606557377046, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 234, 'cache_hits': 176, 'similarity_calculations': 3809, 'cache_hit_rate': 0.04620635337358887, 'cache_size': 3633}}
2025-06-26 18:29:08,939 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:29:08,939 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:29:08,939 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:29:08,939 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:29:08,940 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 83444.0
2025-06-26 18:29:09,441 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 18:29:09,441 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979]
2025-06-26 18:29:09,443 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-06-26 18:29:09,458 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:29:09,459 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 7, 14, 21, 28, 35, 42, 43, 36, 29, 22, 15, 8, 1, 2, 9, 16, 23, 30, 37, 44, 38, 31, 24, 17, 10, 3, 4, 11, 18, 25, 32, 39], 'cur_cost': 54698.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43], 'cur_cost': 22150.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 12, 23, 34, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1], 'cur_cost': 109171.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': array([ 5, 36, 41, 37, 39, 25,  1,  2,  4,  8, 24, 17, 35, 29, 40, 30, 16,
       15,  9, 14, 13,  6, 23, 27, 21,  0, 34, 43, 10, 31, 44,  7, 18, 26,
       28, 33, 42, 32, 38, 20, 22, 11,  3, 19, 12]), 'cur_cost': 84474.0}, {'tour': array([15, 23, 38, 27, 39,  6, 44, 31, 28, 11, 34, 25,  0,  9,  1, 22, 14,
       17,  2,  5, 20, 19,  8, 18, 10, 24, 40, 42,  7, 36, 21, 29, 26, 13,
       37, 41, 32, 33, 30, 12,  3, 43,  4, 35, 16]), 'cur_cost': 90973.0}, {'tour': array([ 9, 41,  4, 23, 11, 26, 38, 27, 43,  2, 35, 37, 42,  3, 44, 31, 21,
       30, 24, 36, 25,  5, 15, 28,  7, 13, 17, 39, 10, 18,  6, 12, 22, 33,
       19, 16,  8, 40, 32, 34,  0, 20, 14,  1, 29]), 'cur_cost': 105729.0}, {'tour': array([14, 11, 36,  5, 32, 34, 10, 42,  8,  2,  4, 18, 17, 35, 19, 30,  1,
        6, 38, 23, 39, 33,  7, 24, 27, 37, 44, 12, 13,  3, 40, 16, 28, 22,
       26, 29, 21, 15, 41, 20, 43,  9, 25,  0, 31]), 'cur_cost': 83444.0}]
2025-06-26 18:29:09,461 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 18:29:09,462 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 245, 'skip_rate': 0.04081632653061224, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 235, 'cache_hits': 176, 'similarity_calculations': 3818, 'cache_hit_rate': 0.04609743321110529, 'cache_size': 3642}}
2025-06-26 18:29:09,462 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:29:09,462 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 26, 33, 40, 41, 34, 27, 20, 13, 6, 7, 14, 21, 28, 35, 42, 43, 36, 29, 22, 15, 8, 1, 2, 9, 16, 23, 30, 37, 44, 38, 31, 24, 17, 10, 3, 4, 11, 18, 25, 32, 39], 'cur_cost': 54698.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43], 'cur_cost': 22150.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1], 'cur_cost': 109171.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5, 36, 41, 37, 39, 25,  1,  2,  4,  8, 24, 17, 35, 29, 40, 30, 16,
       15,  9, 14, 13,  6, 23, 27, 21,  0, 34, 43, 10, 31, 44,  7, 18, 26,
       28, 33, 42, 32, 38, 20, 22, 11,  3, 19, 12]), 'cur_cost': 84474.0}}]
2025-06-26 18:29:09,463 - __main__ - INFO - 进化阶段完成
2025-06-26 18:29:09,464 - __main__ - INFO - 开始评估阶段
2025-06-26 18:29:09,464 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:29:09,465 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:29:09,465 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 12157.0 → 12157.0 (Δ 0.0)
- Mean cost: 35053.8 → 58711.0
- Diversity: 0.459 → 0.691 (Δ 0.2318)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 10973 → 10973 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 0.0% , avg Δ -24206.0
- Exploit: 4 inds, success 25.0% , avg Δ -22834.0

#### Other indicators
- No-change individuals: 3
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [30231.0, 12157.0, 12157.0], diversity [0.5688888888888889, 0.7274074074074073, 0.6385185185185184]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:29:09,465 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:29:24,847 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "average_delta": -24206.0,
      "contribution_to_elite": "none (0% success)"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -22834.0,
      "contribution_to_elite": "moderate (25% success)"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "needs adjustment (explore ineffective, exploit marginally better)",
    "diversity_trend": "increasing (0.459 → 0.691)",
    "cost_trend": "stagnant (best elite unchanged, mean cost increased)"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "mutation_intensity": "increase by 20%",
    "elite_preservation": "maintain current count (21)"
  }
}
```
2025-06-26 18:29:24,864 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:29:24,865 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "average_delta": -24206.0,
      "contribution_to_elite": "none (0% success)"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -22834.0,
      "contribution_to_elite": "moderate (25% success)"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "needs adjustment (explore ineffective, exploit marginally better)",
    "diversity_trend": "increasing (0.459 → 0.691)",
    "cost_trend": "stagnant (best elite unchanged, mean cost increased)"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "mutation_intensity": "increase by 20%",
    "elite_preservation": "maintain current count (21)"
  }
}
```
2025-06-26 18:29:24,865 - __main__ - INFO - 评估阶段完成
2025-06-26 18:29:24,865 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "average_delta": -24206.0,
      "contribution_to_elite": "none (0% success)"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -22834.0,
      "contribution_to_elite": "moderate (25% success)"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "needs adjustment (explore ineffective, exploit marginally better)",
    "diversity_trend": "increasing (0.459 → 0.691)",
    "cost_trend": "stagnant (best elite unchanged, mean cost increased)"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "mutation_intensity": "increase by 20%",
    "elite_preservation": "maintain current count (21)"
  }
}
```
2025-06-26 18:29:24,865 - __main__ - INFO - 当前最佳适应度: 12157.0
2025-06-26 18:29:24,867 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite8_45_route_3.pkl
2025-06-26 18:29:24,867 - __main__ - INFO - composite8_45 开始进化第 5 代
2025-06-26 18:29:24,867 - __main__ - INFO - 开始分析阶段
2025-06-26 18:29:24,868 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:29:24,877 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 12157.0, 'max': 109171.0, 'mean': 58711.0, 'std': 38636.54961820478}, 'diversity': 0.891358024691358, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 3, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:29:24,878 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 12157.0, 'max': 109171.0, 'mean': 58711.0, 'std': 38636.54961820478}, 'diversity_level': 0.891358024691358, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [1, 3, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[690, 2780], [665, 2760], [633, 2709], [617, 2736], [662, 2776], [620, 2758], [638, 2774], [638, 2735], [660, 2733], [637, 2749], [709, 2734], [2544, 1844], [2556, 1799], [2529, 1872], [2558, 1831], [2569, 1874], [2516, 1787], [2512, 1852], [2528, 1816], [2505, 1831], [2545, 1820], [2539, 1799], [4451, 2836], [4390, 2772], [4477, 2811], [4450, 2809], [4408, 2821], [4427, 2814], [4469, 2788], [4414, 2795], [4440, 2773], [4389, 2814], [4418, 2758], [2671, 406], [2631, 439], [2685, 455], [2692, 425], [2640, 406], [2684, 391], [2647, 391], [2609, 410], [2631, 463], [2620, 463], [2671, 419], [2652, 420]], 'distance_matrix': array([[   0.,   32.,   91., ..., 3016., 3082., 3069.],
       [  32.,    0.,   60., ..., 3016., 3083., 3070.],
       [  91.,   60.,    0., ..., 2999., 3066., 3052.],
       ...,
       [3016., 3016., 2999., ...,    0.,   67.,   54.],
       [3082., 3083., 3066., ...,   67.,    0.,   19.],
       [3069., 3070., 3052., ...,   54.,   19.,    0.]])}
2025-06-26 18:29:24,878 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:29:24,878 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:29:24,878 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:29:24,882 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:29:24,882 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:29:24,882 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:29:24,882 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:29:24,884 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:29:24,884 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:29:24,889 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:29:24,889 - __main__ - INFO - 精英专家分析报告: {'elite_count': 21, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1184.0, 'avg_gap': 47737.71428571429}, 'structure_gap': {'unique_elite_edges': 96, 'unique_pop_edges': 287, 'common_edges': 42}}, 'elite_diversity': {'diversity_score': 0.23978835978835977}}
2025-06-26 18:29:24,890 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:29:24,890 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:29:24,892 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:29:24,893 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 12157.0, mean 58711.0, max 109171.0, std 38636.54961820478
- diversity: 0.891358024691358
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [12157.0, 12157.0, 12157.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [617.0, 391.0, 4477.0, 2836.0]
- centroid/spread: [2569.689, 1916.622] | rms 1644.594
- density_grid(3x3): [[0, 12, 0], [0, 11, 0], [11, 0, 11]]
- edge_len_stats(min,q1,med,q3,max): [11.0, 1369.25, 2115.5, 3021.25, 3861.0]
- long_edge_ratio: 0.251
- direction_hist(8 sectors): [6, 5, 6, 9, 5, 2, 3, 8]
- nn_median_dist: 21.024

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:29:24,893 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:29:55,067 - InterfaceAPI.deepseek - ERROR - DeepSeek API请求网络错误 (尝试 1/3): HTTPSConnectionPool(host='api.deepseek.com', port=443): Read timed out.
2025-06-26 18:29:55,067 - InterfaceAPI.deepseek - INFO - 等待 2 秒后重试...
2025-06-26 18:29:57,069 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 2/3)
2025-06-26 18:30:27,323 - InterfaceAPI.deepseek - ERROR - DeepSeek API请求网络错误 (尝试 2/3): HTTPSConnectionPool(host='api.deepseek.com', port=443): Read timed out.
2025-06-26 18:30:27,323 - InterfaceAPI.deepseek - INFO - 等待 2 秒后重试...
2025-06-26 18:30:29,325 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 3/3)
2025-06-26 18:30:57,660 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high ruggedness indicated by wide range in edge lengths (11.0 to 3861.0) and significant standard deviation in costs.",
    "modality": "Potential multimodality suggested by high diversity (0.891) and lack of convergence (0.0), with multiple local optima possible.",
    "deceptiveness": "Possible deceptiveness due to long-edge ratio (0.251) and stagnation in recent best costs despite high diversity."
  },
  "population_state": {
    "coverage": "Uneven coverage with high density in certain grid cells (e.g., [0,1], [2,0], [2,2]) and sparse coverage in others, suggesting incomplete exploration.",
    "convergence": "No convergence (0.0) with recent stagnation in best costs, indicating potential plateau in search progress."
  },
  "difficult_regions": {
    "long_edge_corridors": "Regions with edge lengths in upper quartile (3021.25 to 3861.0) may represent difficult-to-optimize paths.",
    "low_density_cells": "Cells with 0 or low node counts (e.g., [0,0], [1,0], [1,2]) suggest unexplored or challenging regions."
  },
  "opportunity_regions": {
    "high_density_cells": "Cells with high node counts (e.g., [0,1] with 12 nodes, [2,0] and [2,2] with 11 nodes) may contain promising subpaths to exploit.",
    "centroid_proximity": "Nodes near the centroid (2569.689, 1916.622) could be focal points for further optimization."
  },
  "evolution_phase": "Mid-phase exploration with stagnation, characterized by high diversity but no convergence and recent cost stagnation.",
  "evolution_direction": {
    "suggested_operators": [
      "Targeted local search in high-density cells to exploit potential promising regions.",
      "Diversification operators (e.g., mutation or restart) to explore low-density cells and break stagnation.",
      "Edge recombination or repair operators to address long-edge corridors and difficult regions.",
      "Direction-aware operators (e.g., sector-based crossover) based on direction histogram imbalances (e.g., sectors with counts 2, 3, 5)."
    ]
  }
}
```
2025-06-26 18:30:57,660 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:30:57,660 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high ruggedness indicated by wide range in edge lengths (11.0 to 3861.0) and significant standard deviation in costs.', 'modality': 'Potential multimodality suggested by high diversity (0.891) and lack of convergence (0.0), with multiple local optima possible.', 'deceptiveness': 'Possible deceptiveness due to long-edge ratio (0.251) and stagnation in recent best costs despite high diversity.'}, 'population_state': {'coverage': 'Uneven coverage with high density in certain grid cells (e.g., [0,1], [2,0], [2,2]) and sparse coverage in others, suggesting incomplete exploration.', 'convergence': 'No convergence (0.0) with recent stagnation in best costs, indicating potential plateau in search progress.'}, 'difficult_regions': {'long_edge_corridors': 'Regions with edge lengths in upper quartile (3021.25 to 3861.0) may represent difficult-to-optimize paths.', 'low_density_cells': 'Cells with 0 or low node counts (e.g., [0,0], [1,0], [1,2]) suggest unexplored or challenging regions.'}, 'opportunity_regions': {'high_density_cells': 'Cells with high node counts (e.g., [0,1] with 12 nodes, [2,0] and [2,2] with 11 nodes) may contain promising subpaths to exploit.', 'centroid_proximity': 'Nodes near the centroid (2569.689, 1916.622) could be focal points for further optimization.'}, 'evolution_phase': 'Mid-phase exploration with stagnation, characterized by high diversity but no convergence and recent cost stagnation.', 'evolution_direction': {'suggested_operators': ['Targeted local search in high-density cells to exploit potential promising regions.', 'Diversification operators (e.g., mutation or restart) to explore low-density cells and break stagnation.', 'Edge recombination or repair operators to address long-edge corridors and difficult regions.', 'Direction-aware operators (e.g., sector-based crossover) based on direction histogram imbalances (e.g., sectors with counts 2, 3, 5).']}}
2025-06-26 18:30:57,660 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:30:57,668 - __main__ - INFO - 分析阶段完成
2025-06-26 18:30:57,668 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high ruggedness indicated by wide range in edge lengths (11.0 to 3861.0) and significant standard deviation in costs.', 'modality': 'Potential multimodality suggested by high diversity (0.891) and lack of convergence (0.0), with multiple local optima possible.', 'deceptiveness': 'Possible deceptiveness due to long-edge ratio (0.251) and stagnation in recent best costs despite high diversity.'}, 'population_state': {'coverage': 'Uneven coverage with high density in certain grid cells (e.g., [0,1], [2,0], [2,2]) and sparse coverage in others, suggesting incomplete exploration.', 'convergence': 'No convergence (0.0) with recent stagnation in best costs, indicating potential plateau in search progress.'}, 'difficult_regions': {'long_edge_corridors': 'Regions with edge lengths in upper quartile (3021.25 to 3861.0) may represent difficult-to-optimize paths.', 'low_density_cells': 'Cells with 0 or low node counts (e.g., [0,0], [1,0], [1,2]) suggest unexplored or challenging regions.'}, 'opportunity_regions': {'high_density_cells': 'Cells with high node counts (e.g., [0,1] with 12 nodes, [2,0] and [2,2] with 11 nodes) may contain promising subpaths to exploit.', 'centroid_proximity': 'Nodes near the centroid (2569.689, 1916.622) could be focal points for further optimization.'}, 'evolution_phase': 'Mid-phase exploration with stagnation, characterized by high diversity but no convergence and recent cost stagnation.', 'evolution_direction': {'suggested_operators': ['Targeted local search in high-density cells to exploit potential promising regions.', 'Diversification operators (e.g., mutation or restart) to explore low-density cells and break stagnation.', 'Edge recombination or repair operators to address long-edge corridors and difficult regions.', 'Direction-aware operators (e.g., sector-based crossover) based on direction histogram imbalances (e.g., sectors with counts 2, 3, 5).']}}
2025-06-26 18:30:57,668 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:30:57,668 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:30:57,668 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 12157.0
  • mean_cost: 58711.0
- Search-space: ruggedness Moderate to high ruggedness indicated by wide range in edge lengths (11.0 to 3861.0) and significant standard deviation in costs., modality Potential multimodality suggested by high diversity (0.891) and lack of convergence (0.0), with multiple local optima possible., deceptiveness Possible deceptiveness due to long-edge ratio (0.251) and stagnation in recent best costs despite high diversity.
- Evolution phase: Mid-phase exploration with stagnation, characterized by high diversity but no convergence and recent cost stagnation.
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_cells
- Opportunity regions (# 2): high_density_cells, centroid_proximity
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:30:57,668 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:30:57,668 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:31:05,210 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest prioritizing exploration (70%), with top performers exploiting known good regions."
}
```
2025-06-26 18:31:05,212 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 18:31:05,212 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 18:31:05,212 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest prioritizing exploration (70%), with top performers exploiting known good regions."
}
```
2025-06-26 18:31:05,213 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:31:05,213 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 18:31:05,213 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest prioritizing exploration (70%), with top performers exploiting known good regions."
}
```
2025-06-26 18:31:05,213 - __main__ - INFO - 开始进化阶段
2025-06-26 18:31:05,213 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:31:05,213 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:31:05,213 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 54698.0 (rank 40.0%)
- Population diversity: 0.6909090909090909
- Evolution phase: Mid-phase exploration with stagnation, characterized by high diversity but no convergence and recent cost stagnation.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, centroid_proxim
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:31:05,213 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:31:05,213 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:31:16,809 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44],
  "targeted_regions": "high_density_ce, centroid_proxim",
  "strategy_comment": "Leverage modular jumps to explore sparse cells near centroids"
}
```
2025-06-26 18:31:16,809 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:31:16,809 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112974.0, 路径: [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44]
2025-06-26 18:31:16,809 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}
2025-06-26 18:31:16,810 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:31:16,810 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:31:16,810 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12157.0 (rank 0.0%)
- Population diversity: 0.6909090909090909
- Evolution phase: Mid-phase exploration with stagnation, characterized by high diversity but no convergence and recent cost stagnation.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, centroid_proxim
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:31:16,810 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:31:16,810 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:31:29,018 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44],
  "targeted_regions": "high_density_ce, centroid_proxim",
  "strategy_comment": "Balanced spiral pattern targeting dense cells near centroid"
}
```
2025-06-26 18:31:29,023 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:31:29,023 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112974.0, 路径: [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44]
2025-06-26 18:31:29,023 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}
2025-06-26 18:31:29,023 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-26 18:31:29,023 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:31:29,025 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:31:29,025 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 98213.0
2025-06-26 18:31:29,529 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 18:31:29,529 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979]
2025-06-26 18:31:29,529 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-06-26 18:31:29,536 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:31:29,537 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': array([34, 21, 22, 11, 43,  2, 14, 13, 35, 40,  9, 33, 29, 27,  8, 20, 44,
       23, 39,  4, 41, 15, 37,  0, 12, 17, 25, 24,  7, 28, 30, 31,  6, 26,
        5, 32, 19, 42, 10, 36, 18, 38,  1, 16,  3]), 'cur_cost': 98213.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 12, 23, 34, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1], 'cur_cost': 109171.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [5, 36, 41, 37, 39, 25, 1, 2, 4, 8, 24, 17, 35, 29, 40, 30, 16, 15, 9, 14, 13, 6, 23, 27, 21, 0, 34, 43, 10, 31, 44, 7, 18, 26, 28, 33, 42, 32, 38, 20, 22, 11, 3, 19, 12], 'cur_cost': 84474.0}, {'tour': array([15, 23, 38, 27, 39,  6, 44, 31, 28, 11, 34, 25,  0,  9,  1, 22, 14,
       17,  2,  5, 20, 19,  8, 18, 10, 24, 40, 42,  7, 36, 21, 29, 26, 13,
       37, 41, 32, 33, 30, 12,  3, 43,  4, 35, 16]), 'cur_cost': 90973.0}, {'tour': array([ 9, 41,  4, 23, 11, 26, 38, 27, 43,  2, 35, 37, 42,  3, 44, 31, 21,
       30, 24, 36, 25,  5, 15, 28,  7, 13, 17, 39, 10, 18,  6, 12, 22, 33,
       19, 16,  8, 40, 32, 34,  0, 20, 14,  1, 29]), 'cur_cost': 105729.0}, {'tour': array([14, 11, 36,  5, 32, 34, 10, 42,  8,  2,  4, 18, 17, 35, 19, 30,  1,
        6, 38, 23, 39, 33,  7, 24, 27, 37, 44, 12, 13,  3, 40, 16, 28, 22,
       26, 29, 21, 15, 41, 20, 43,  9, 25,  0, 31]), 'cur_cost': 83444.0}]
2025-06-26 18:31:29,538 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:31:29,538 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 246, 'skip_rate': 0.04065040650406504, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 236, 'cache_hits': 176, 'similarity_calculations': 3828, 'cache_hit_rate': 0.04597701149425287, 'cache_size': 3652}}
2025-06-26 18:31:29,538 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-06-26 18:31:29,540 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:31:29,540 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:31:29,540 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12157.0 (rank 0.0%)
- Population diversity: 0.6704545454545454
- Evolution phase: Mid-phase exploration with stagnation, characterized by high diversity but no convergence and recent cost stagnation.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, centroid_proxim
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:31:29,540 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:31:29,540 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:31:41,203 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44],
  "targeted_regions": "high_density_ce, centroid_proxim",
  "strategy_comment": "Leverage modular pattern to explore adjacent low-density cells"
}
```
2025-06-26 18:31:41,203 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:31:41,203 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112972.0, 路径: [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44]
2025-06-26 18:31:41,203 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112972.0}
2025-06-26 18:31:41,204 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-26 18:31:41,204 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:31:41,204 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:31:41,204 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 94931.0
2025-06-26 18:31:41,705 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 18:31:41,705 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979]
2025-06-26 18:31:41,706 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-06-26 18:31:41,714 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:31:41,714 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': array([34, 21, 22, 11, 43,  2, 14, 13, 35, 40,  9, 33, 29, 27,  8, 20, 44,
       23, 39,  4, 41, 15, 37,  0, 12, 17, 25, 24,  7, 28, 30, 31,  6, 26,
        5, 32, 19, 42, 10, 36, 18, 38,  1, 16,  3]), 'cur_cost': 98213.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112972.0}, {'tour': array([43,  7, 12, 34, 32, 10, 16, 42, 31,  1,  8, 44, 30, 28, 40, 15,  2,
       38, 22, 23, 41, 33,  4, 27, 36, 39, 29, 11,  9, 14, 18, 17, 25, 24,
        6, 21, 35, 26, 19,  0, 37, 20,  3, 13,  5]), 'cur_cost': 94931.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [5, 36, 41, 37, 39, 25, 1, 2, 4, 8, 24, 17, 35, 29, 40, 30, 16, 15, 9, 14, 13, 6, 23, 27, 21, 0, 34, 43, 10, 31, 44, 7, 18, 26, 28, 33, 42, 32, 38, 20, 22, 11, 3, 19, 12], 'cur_cost': 84474.0}, {'tour': array([15, 23, 38, 27, 39,  6, 44, 31, 28, 11, 34, 25,  0,  9,  1, 22, 14,
       17,  2,  5, 20, 19,  8, 18, 10, 24, 40, 42,  7, 36, 21, 29, 26, 13,
       37, 41, 32, 33, 30, 12,  3, 43,  4, 35, 16]), 'cur_cost': 90973.0}, {'tour': array([ 9, 41,  4, 23, 11, 26, 38, 27, 43,  2, 35, 37, 42,  3, 44, 31, 21,
       30, 24, 36, 25,  5, 15, 28,  7, 13, 17, 39, 10, 18,  6, 12, 22, 33,
       19, 16,  8, 40, 32, 34,  0, 20, 14,  1, 29]), 'cur_cost': 105729.0}, {'tour': array([14, 11, 36,  5, 32, 34, 10, 42,  8,  2,  4, 18, 17, 35, 19, 30,  1,
        6, 38, 23, 39, 33,  7, 24, 27, 37, 44, 12, 13,  3, 40, 16, 28, 22,
       26, 29, 21, 15, 41, 20, 43,  9, 25,  0, 31]), 'cur_cost': 83444.0}]
2025-06-26 18:31:41,717 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:31:41,717 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 247, 'skip_rate': 0.04048582995951417, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 237, 'cache_hits': 176, 'similarity_calculations': 3839, 'cache_hit_rate': 0.045845272206303724, 'cache_size': 3663}}
2025-06-26 18:31:41,718 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-26 18:31:41,718 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:31:41,718 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:31:41,718 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 12157.0 (rank 0.0%)
- Population diversity: 0.6818181818181818
- Evolution phase: Mid-phase exploration with stagnation, characterized by high diversity but no convergence and recent cost stagnation.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, centroid_proxim
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:31:41,718 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:31:41,718 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:31:53,267 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44],
  "targeted_regions": "high_density_ce, centroid_proxim",
  "strategy_comment": "Balanced exploration of opportunity regions"
}
```
2025-06-26 18:31:53,267 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:31:53,273 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12157.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]
2025-06-26 18:31:53,273 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}
2025-06-26 18:31:53,273 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:31:53,273 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:31:53,273 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 84474.0 (rank 20.0%)
- Population diversity: 0.6818181818181818
- Evolution phase: Mid-phase exploration with stagnation, characterized by high diversity but no convergence and recent cost stagnation.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, centroid_proxim
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:31:53,275 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:31:53,275 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:32:04,709 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 44, 11, 23, 35, 2, 14, 26, 38, 4, 16, 28, 40, 7, 19, 31, 43, 10, 22, 34, 1, 13, 25, 37, 3, 15, 27, 39, 6, 18, 30, 42, 9, 21, 33],
  "targeted_regions": "high_density_ce, centroid_proxim",
  "strategy_comment": "Balanced exploration of dense and sparse cells with centroid proximity"
}
```
2025-06-26 18:32:04,709 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:32:04,709 - ExplorationExpert - INFO - 探索路径生成完成，成本: 115216.0, 路径: [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 44, 11, 23, 35, 2, 14, 26, 38, 4, 16, 28, 40, 7, 19, 31, 43, 10, 22, 34, 1, 13, 25, 37, 3, 15, 27, 39, 6, 18, 30, 42, 9, 21, 33]
2025-06-26 18:32:04,709 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 44, 11, 23, 35, 2, 14, 26, 38, 4, 16, 28, 40, 7, 19, 31, 43, 10, 22, 34, 1, 13, 25, 37, 3, 15, 27, 39, 6, 18, 30, 42, 9, 21, 33], 'cur_cost': 115216.0}
2025-06-26 18:32:04,716 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:32:04,716 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:32:04,716 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:32:04,716 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 83022.0
2025-06-26 18:32:05,219 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 18:32:05,219 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979]
2025-06-26 18:32:05,220 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-06-26 18:32:05,228 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:32:05,228 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': array([34, 21, 22, 11, 43,  2, 14, 13, 35, 40,  9, 33, 29, 27,  8, 20, 44,
       23, 39,  4, 41, 15, 37,  0, 12, 17, 25, 24,  7, 28, 30, 31,  6, 26,
        5, 32, 19, 42, 10, 36, 18, 38,  1, 16,  3]), 'cur_cost': 98213.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112972.0}, {'tour': array([43,  7, 12, 34, 32, 10, 16, 42, 31,  1,  8, 44, 30, 28, 40, 15,  2,
       38, 22, 23, 41, 33,  4, 27, 36, 39, 29, 11,  9, 14, 18, 17, 25, 24,
        6, 21, 35, 26, 19,  0, 37, 20,  3, 13,  5]), 'cur_cost': 94931.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 44, 11, 23, 35, 2, 14, 26, 38, 4, 16, 28, 40, 7, 19, 31, 43, 10, 22, 34, 1, 13, 25, 37, 3, 15, 27, 39, 6, 18, 30, 42, 9, 21, 33], 'cur_cost': 115216.0}, {'tour': array([ 3, 21, 20, 10, 24, 27, 36, 34, 16, 39, 28, 30, 22,  8, 43, 17, 31,
       37, 44, 42,  1, 29, 26, 33, 14,  4, 35, 19, 41, 40, 23,  5, 38, 15,
       25,  9, 32,  2,  0, 18, 12,  7, 13, 11,  6]), 'cur_cost': 83022.0}, {'tour': array([ 9, 41,  4, 23, 11, 26, 38, 27, 43,  2, 35, 37, 42,  3, 44, 31, 21,
       30, 24, 36, 25,  5, 15, 28,  7, 13, 17, 39, 10, 18,  6, 12, 22, 33,
       19, 16,  8, 40, 32, 34,  0, 20, 14,  1, 29]), 'cur_cost': 105729.0}, {'tour': array([14, 11, 36,  5, 32, 34, 10, 42,  8,  2,  4, 18, 17, 35, 19, 30,  1,
        6, 38, 23, 39, 33,  7, 24, 27, 37, 44, 12, 13,  3, 40, 16, 28, 22,
       26, 29, 21, 15, 41, 20, 43,  9, 25,  0, 31]), 'cur_cost': 83444.0}]
2025-06-26 18:32:05,231 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:32:05,231 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 248, 'skip_rate': 0.04032258064516129, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 238, 'cache_hits': 176, 'similarity_calculations': 3851, 'cache_hit_rate': 0.045702414957153985, 'cache_size': 3675}}
2025-06-26 18:32:05,231 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:32:05,231 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-26 18:32:05,232 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:32:05,232 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 105729.0 (rank 50.0%)
- Population diversity: 0.6227272727272727
- Evolution phase: Mid-phase exploration with stagnation, characterized by high diversity but no convergence and recent cost stagnation.
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, centroid_proxim
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 44]  
• Path length == 45 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:32:05,232 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:32:05,232 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:32:16,260 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 12, 18, 22, 25, 29, 33, 37, 41, 44, 40, 36, 32, 28, 24, 20, 16, 13, 9, 5, 1, 2, 6, 10, 14, 17, 21, 26, 30, 34, 38, 42, 39, 35, 31, 27, 23, 19, 15, 11, 8, 4, 43],
  "targeted_regions": "high_density_ce, centroid_proxim, adjacent low-density",
  "strategy_comment": "Spiral pattern balancing opportunity regions and diversity"
}
```
2025-06-26 18:32:16,260 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:32:16,260 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36539.0, 路径: [0, 3, 7, 12, 18, 22, 25, 29, 33, 37, 41, 44, 40, 36, 32, 28, 24, 20, 16, 13, 9, 5, 1, 2, 6, 10, 14, 17, 21, 26, 30, 34, 38, 42, 39, 35, 31, 27, 23, 19, 15, 11, 8, 4, 43]
2025-06-26 18:32:16,260 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 3, 7, 12, 18, 22, 25, 29, 33, 37, 41, 44, 40, 36, 32, 28, 24, 20, 16, 13, 9, 5, 1, 2, 6, 10, 14, 17, 21, 26, 30, 34, 38, 42, 39, 35, 31, 27, 23, 19, 15, 11, 8, 4, 43], 'cur_cost': 36539.0}
2025-06-26 18:32:16,260 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:32:16,260 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:32:16,268 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:32:16,268 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 95204.0
2025-06-26 18:32:16,769 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 18:32:16,769 - ExploitationExpert - INFO - res_population_costs: [10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10973, 10979]
2025-06-26 18:32:16,769 - ExploitationExpert - INFO - res_populations: [array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  9,  7,  2,  3,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  2,  3,  7,  9,  5,  6,  4,  1], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  5,  3,  2,  7,  9,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 22, 25, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 39, 37, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 39, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 39, 37, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       13, 11, 14, 20, 12, 21, 16, 18, 19, 17, 10], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 25, 22, 27, 29, 26, 31], dtype=int64), array([ 0, 31, 26, 29, 27, 25, 22, 24, 28, 30, 32, 23, 35, 36, 38, 33, 43,
       44, 37, 39, 40, 34, 41, 42, 16, 18, 21, 12, 20, 14, 11, 15, 13, 17,
       19, 10,  8,  7,  2,  3,  5,  9,  6,  4,  1], dtype=int64), array([ 0,  1,  4,  6,  5,  9,  7,  3,  2,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 39, 37, 44, 43, 33, 38, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64), array([ 0, 10, 17, 19, 18, 16, 21, 12, 20, 14, 11, 13, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 39, 37, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 25, 22, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 22, 25, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0, 10, 13, 17, 19, 18, 16, 21, 12, 20, 14, 11, 15, 23, 29, 31, 26,
       27, 22, 25, 24, 28, 30, 32, 35, 36, 38, 33, 43, 44, 37, 39, 40, 34,
       41, 42,  2,  3,  5,  6,  9,  7,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  7,  9,  6,  5,  3,  2, 42, 41, 34, 40, 37, 39, 44,
       43, 33, 38, 36, 35, 32, 30, 28, 24, 25, 22, 27, 26, 31, 29, 23, 15,
       11, 14, 20, 12, 21, 16, 18, 19, 17, 13, 10], dtype=int64), array([ 0,  1,  4,  6,  9,  5,  3,  2,  7,  8, 10, 19, 17, 13, 15, 11, 14,
       20, 12, 21, 18, 16, 42, 41, 34, 40, 37, 44, 39, 38, 33, 43, 36, 35,
       23, 32, 30, 28, 24, 22, 25, 27, 29, 26, 31], dtype=int64)]
2025-06-26 18:32:16,776 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:32:16,777 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}, {'tour': array([34, 21, 22, 11, 43,  2, 14, 13, 35, 40,  9, 33, 29, 27,  8, 20, 44,
       23, 39,  4, 41, 15, 37,  0, 12, 17, 25, 24,  7, 28, 30, 31,  6, 26,
        5, 32, 19, 42, 10, 36, 18, 38,  1, 16,  3]), 'cur_cost': 98213.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112972.0}, {'tour': array([43,  7, 12, 34, 32, 10, 16, 42, 31,  1,  8, 44, 30, 28, 40, 15,  2,
       38, 22, 23, 41, 33,  4, 27, 36, 39, 29, 11,  9, 14, 18, 17, 25, 24,
        6, 21, 35, 26, 19,  0, 37, 20,  3, 13,  5]), 'cur_cost': 94931.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}, {'tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 44, 11, 23, 35, 2, 14, 26, 38, 4, 16, 28, 40, 7, 19, 31, 43, 10, 22, 34, 1, 13, 25, 37, 3, 15, 27, 39, 6, 18, 30, 42, 9, 21, 33], 'cur_cost': 115216.0}, {'tour': array([ 3, 21, 20, 10, 24, 27, 36, 34, 16, 39, 28, 30, 22,  8, 43, 17, 31,
       37, 44, 42,  1, 29, 26, 33, 14,  4, 35, 19, 41, 40, 23,  5, 38, 15,
       25,  9, 32,  2,  0, 18, 12,  7, 13, 11,  6]), 'cur_cost': 83022.0}, {'tour': [0, 3, 7, 12, 18, 22, 25, 29, 33, 37, 41, 44, 40, 36, 32, 28, 24, 20, 16, 13, 9, 5, 1, 2, 6, 10, 14, 17, 21, 26, 30, 34, 38, 42, 39, 35, 31, 27, 23, 19, 15, 11, 8, 4, 43], 'cur_cost': 36539.0}, {'tour': array([ 6, 34,  0, 13, 27, 19, 44, 29, 39, 28, 18, 20, 26,  1, 12, 25, 31,
       22, 42, 40,  7, 15, 23, 36,  4,  3, 17, 35, 32,  8, 10, 21, 43, 16,
       38, 33,  5, 41,  9, 30,  2, 11, 14, 24, 37]), 'cur_cost': 95204.0}]
2025-06-26 18:32:16,778 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:32:16,778 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 249, 'skip_rate': 0.040160642570281124, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 239, 'cache_hits': 176, 'similarity_calculations': 3864, 'cache_hit_rate': 0.045548654244306416, 'cache_size': 3688}}
2025-06-26 18:32:16,778 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:32:16,778 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112974.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 40, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 42, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 41, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31, 43, 44], 'cur_cost': 112972.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44], 'cur_cost': 12157.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 5, 17, 29, 41, 8, 20, 32, 44, 11, 23, 35, 2, 14, 26, 38, 4, 16, 28, 40, 7, 19, 31, 43, 10, 22, 34, 1, 13, 25, 37, 3, 15, 27, 39, 6, 18, 30, 42, 9, 21, 33], 'cur_cost': 115216.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 12, 18, 22, 25, 29, 33, 37, 41, 44, 40, 36, 32, 28, 24, 20, 16, 13, 9, 5, 1, 2, 6, 10, 14, 17, 21, 26, 30, 34, 38, 42, 39, 35, 31, 27, 23, 19, 15, 11, 8, 4, 43], 'cur_cost': 36539.0}}]
2025-06-26 18:32:16,778 - __main__ - INFO - 进化阶段完成
2025-06-26 18:32:16,780 - __main__ - INFO - 开始评估阶段
2025-06-26 18:32:16,780 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:32:16,781 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:32:16,781 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 12157.0 → 12157.0 (Δ 0.0)
- Mean cost: 58711.0 → 87420.2
- Diversity: 0.691 → 0.664 (Δ -0.0273)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 10973 → 10973 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ -36910.0
- Exploit: 4 inds, success 50.0% , avg Δ -16408.0

#### Other indicators
- No-change individuals: 1
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [12157.0, 12157.0, 12157.0], diversity [0.7274074074074073, 0.6385185185185184, 0.891358024691358]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:32:16,782 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:32:30,973 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "plateau_with_potential_stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -36910.0,
      "contribution_to_elite_improvement": "moderate_negative_impact"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -16408.0,
      "contribution_to_elite_improvement": "high_positive_impact"
    }
  },
  "balance_state": {
    "current_explore_exploit_ratio": "1.5:1",
    "assessment": "exploit_underutilized",
    "suggested_action": "increase_exploit_ratio"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "exploit_ratio": 0.6,
    "diversity_weight": 0.7,
    "additional_actions": "increase_mutation_rate_for_explore_strategy"
  }
}
```
2025-06-26 18:32:30,990 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:32:30,990 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "plateau_with_potential_stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -36910.0,
      "contribution_to_elite_improvement": "moderate_negative_impact"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -16408.0,
      "contribution_to_elite_improvement": "high_positive_impact"
    }
  },
  "balance_state": {
    "current_explore_exploit_ratio": "1.5:1",
    "assessment": "exploit_underutilized",
    "suggested_action": "increase_exploit_ratio"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "exploit_ratio": 0.6,
    "diversity_weight": 0.7,
    "additional_actions": "increase_mutation_rate_for_explore_strategy"
  }
}
```
2025-06-26 18:32:30,991 - __main__ - INFO - 评估阶段完成
2025-06-26 18:32:30,991 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "plateau_with_potential_stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -36910.0,
      "contribution_to_elite_improvement": "moderate_negative_impact"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -16408.0,
      "contribution_to_elite_improvement": "high_positive_impact"
    }
  },
  "balance_state": {
    "current_explore_exploit_ratio": "1.5:1",
    "assessment": "exploit_underutilized",
    "suggested_action": "increase_exploit_ratio"
  },
  "recommendations": {
    "explore_ratio": 0.4,
    "exploit_ratio": 0.6,
    "diversity_weight": 0.7,
    "additional_actions": "increase_mutation_rate_for_explore_strategy"
  }
}
```
2025-06-26 18:32:30,991 - __main__ - INFO - 当前最佳适应度: 12157.0
2025-06-26 18:32:30,991 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite8_45_route_4.pkl
2025-06-26 18:32:31,000 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite8_45_solution.json
2025-06-26 18:32:31,000 - __main__ - INFO - 实例 composite8_45 处理完成
