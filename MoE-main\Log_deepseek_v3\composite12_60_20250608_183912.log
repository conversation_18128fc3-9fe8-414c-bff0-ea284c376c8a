2025-06-08 18:39:12,751 - __main__ - INFO - composite12_60 开始进化第 1 代
2025-06-08 18:39:12,752 - __main__ - INFO - 开始分析阶段
2025-06-08 18:39:12,752 - StatsExpert - INFO - 开始统计分析
2025-06-08 18:39:12,765 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9984.0, 'max': 99407.0, 'mean': 69679.1, 'std': 39076.625968601744}, 'diversity': 0.9151851851851853, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-08 18:39:12,766 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9984.0, 'max': 99407.0, 'mean': 69679.1, 'std': 39076.625968601744}, 'diversity_level': 0.9151851851851853, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-08 18:39:12,767 - PathExpert - INFO - 开始路径结构分析
2025-06-08 18:39:12,770 - PathExpert - INFO - 路径结构分析完成
2025-06-08 18:39:12,772 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (15, 22), 'frequency': 0.5, 'avg_cost': 34.0}], 'common_subpaths': [{'subpath': (12, 23, 16), 'frequency': 0.3}, {'subpath': (23, 16, 20), 'frequency': 0.3}, {'subpath': (16, 20, 22), 'frequency': 0.3}, {'subpath': (20, 22, 15), 'frequency': 0.3}, {'subpath': (22, 15, 17), 'frequency': 0.3}, {'subpath': (15, 17, 19), 'frequency': 0.3}, {'subpath': (17, 19, 14), 'frequency': 0.3}, {'subpath': (19, 14, 21), 'frequency': 0.3}, {'subpath': (14, 21, 18), 'frequency': 0.3}, {'subpath': (21, 18, 13), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(50, 12)', 'frequency': 0.4}, {'edge': '(22, 15)', 'frequency': 0.4}, {'edge': '(33, 24)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(39, 46)', 'frequency': 0.2}, {'edge': '(46, 40)', 'frequency': 0.2}, {'edge': '(40, 37)', 'frequency': 0.2}, {'edge': '(37, 47)', 'frequency': 0.3}, {'edge': '(47, 42)', 'frequency': 0.2}, {'edge': '(42, 38)', 'frequency': 0.2}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(45, 36)', 'frequency': 0.2}, {'edge': '(36, 43)', 'frequency': 0.2}, {'edge': '(43, 41)', 'frequency': 0.2}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(6, 5)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.2}, {'edge': '(1, 10)', 'frequency': 0.2}, {'edge': '(10, 11)', 'frequency': 0.2}, {'edge': '(2, 7)', 'frequency': 0.2}, {'edge': '(3, 9)', 'frequency': 0.2}, {'edge': '(59, 51)', 'frequency': 0.2}, {'edge': '(52, 57)', 'frequency': 0.2}, {'edge': '(57, 55)', 'frequency': 0.2}, {'edge': '(55, 56)', 'frequency': 0.2}, {'edge': '(56, 49)', 'frequency': 0.2}, {'edge': '(49, 58)', 'frequency': 0.3}, {'edge': '(58, 54)', 'frequency': 0.2}, {'edge': '(54, 53)', 'frequency': 0.3}, {'edge': '(53, 48)', 'frequency': 0.2}, {'edge': '(48, 50)', 'frequency': 0.2}, {'edge': '(12, 23)', 'frequency': 0.3}, {'edge': '(23, 16)', 'frequency': 0.3}, {'edge': '(16, 20)', 'frequency': 0.3}, {'edge': '(20, 22)', 'frequency': 0.3}, {'edge': '(15, 17)', 'frequency': 0.3}, {'edge': '(17, 19)', 'frequency': 0.3}, {'edge': '(19, 14)', 'frequency': 0.3}, {'edge': '(14, 21)', 'frequency': 0.3}, {'edge': '(21, 18)', 'frequency': 0.3}, {'edge': '(18, 13)', 'frequency': 0.3}, {'edge': '(32, 26)', 'frequency': 0.2}, {'edge': '(35, 25)', 'frequency': 0.2}, {'edge': '(28, 27)', 'frequency': 0.3}, {'edge': '(27, 29)', 'frequency': 0.3}, {'edge': '(29, 33)', 'frequency': 0.3}, {'edge': '(24, 30)', 'frequency': 0.2}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(8, 7)', 'frequency': 0.2}, {'edge': '(43, 44)', 'frequency': 0.2}, {'edge': '(13, 35)', 'frequency': 0.2}, {'edge': '(56, 55)', 'frequency': 0.2}, {'edge': '(10, 1)', 'frequency': 0.2}, {'edge': '(34, 36)', 'frequency': 0.3}, {'edge': '(42, 47)', 'frequency': 0.2}, {'edge': '(46, 39)', 'frequency': 0.2}, {'edge': '(53, 9)', 'frequency': 0.2}, {'edge': '(29, 10)', 'frequency': 0.2}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(8, 59)', 'frequency': 0.2}, {'edge': '(2, 26)', 'frequency': 0.2}, {'edge': '(13, 42)', 'frequency': 0.2}, {'edge': '(53, 28)', 'frequency': 0.2}, {'edge': '(2, 38)', 'frequency': 0.2}, {'edge': '(27, 18)', 'frequency': 0.2}, {'edge': '(47, 21)', 'frequency': 0.2}, {'edge': '(39, 52)', 'frequency': 0.2}, {'edge': '(4, 1)', 'frequency': 0.2}, {'edge': '(7, 22)', 'frequency': 0.2}, {'edge': '(16, 41)', 'frequency': 0.2}, {'edge': '(18, 47)', 'frequency': 0.2}, {'edge': '(57, 23)', 'frequency': 0.2}, {'edge': '(23, 36)', 'frequency': 0.2}, {'edge': '(28, 50)', 'frequency': 0.2}, {'edge': '(33, 43)', 'frequency': 0.2}, {'edge': '(25, 30)', 'frequency': 0.2}, {'edge': '(15, 48)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [23, 36, 18, 33, 43, 35, 44, 58], 'cost': 17797.0, 'size': 8}, {'region': [55, 42, 59, 43, 48, 35, 51, 44], 'cost': 15984.0, 'size': 8}, {'region': [30, 13, 42, 27, 18, 40], 'cost': 13926.0, 'size': 6}, {'region': [15, 26, 13, 8, 16, 41], 'cost': 12580.0, 'size': 6}, {'region': [55, 45, 12, 37, 15], 'cost': 11746.0, 'size': 5}]}
2025-06-08 18:39:12,772 - EliteExpert - INFO - 开始精英解分析
2025-06-08 18:39:12,773 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-08 18:39:12,773 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-08 18:39:12,773 - LandscapeExpert - INFO - 开始景观分析
2025-06-08 18:39:12,773 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-08 18:39:12,773 - LandscapeExpert - INFO - 发送给LLM的提示词: 
# Landscape Analysis Task

## Role Definition
You are an expert in evolutionary algorithms and combinatorial optimization, specializing in search space analysis. You excel at analyzing population statistics, path structures, and elite solutions to provide insights into the search landscape.

## Current Population Statistics
- Population Size: 10
- Cost Statistics: Min=9984.0, Max=99407.0, Mean=69679.1, Std=39076.625968601744
- Diversity Level: 0.9151851851851853
- Convergence Level: 0.0
- Clustering Information: {"clusters": 9, "cluster_sizes": [2, 1, 1, 1, 1, 1, 1, 1, 1]}

## Path Structure Analysis
- High Quality Edges: [{"edge": [15, 22], "frequency": 0.5, "avg_cost": 34.0}]
- Common Subpaths: [{"subpath": [12, 23, 16], "frequency": 0.3}, {"subpath": [23, 16, 20], "frequency": 0.3}, {"subpath": [16, 20, 22], "frequency": 0.3}, {"subpath": [20, 22, 15], "frequency": 0.3}, {"subpath": [22, 15, 17], "frequency": 0.3}, {"subpath": [15, 17, 19], "frequency": 0.3}, {"subpath": [17, 19, 14], "frequency": 0.3}, {"subpath": [19, 14, 21], "frequency": 0.3}, {"subpath": [14, 21, 18], "frequency": 0.3}, {"subpath": [21, 18, 13], "frequency": 0.3}]
- Edge Frequency Distribution: {"high_frequency_edges": [], "medium_frequency_edges": [{"edge": "(50, 12)", "frequency": 0.4}, {"edge": "(22, 15)", "frequency": 0.4}, {"edge": "(33, 24)", "frequency": 0.4}], "low_frequency_edges": [{"edge": "(39, 46)", "frequency": 0.2}, {"edge": "(46, 40)", "frequency": 0.2}, {"edge": "(40, 37)", "frequency": 0.2}, {"edge": "(37, 47)", "frequency": 0.3}, {"edge": "(47, 42)", "frequency": 0.2}, {"edge": "(42, 38)", "frequency": 0.2}, {"edge": "(38, 45)", "frequency": 0.3}, {"edge": "(45, 36)", "frequency": 0.2}, {"edge": "(36, 43)", "frequency": 0.2}, {"edge": "(43, 41)", "frequency": 0.2}, {"edge": "(4, 6)", "frequency": 0.2}, {"edge": "(6, 5)", "frequency": 0.2}, {"edge": "(0, 1)", "frequency": 0.2}, {"edge": "(1, 10)", "frequency": 0.2}, {"edge": "(10, 11)", "frequency": 0.2}, {"edge": "(2, 7)", "frequency": 0.2}, {"edge": "(3, 9)", "frequency": 0.2}, {"edge": "(59, 51)", "frequency": 0.2}, {"edge": "(52, 57)", "frequency": 0.2}, {"edge": "(57, 55)", "frequency": 0.2}, {"edge": "(55, 56)", "frequency": 0.2}, {"edge": "(56, 49)", "frequency": 0.2}, {"edge": "(49, 58)", "frequency": 0.3}, {"edge": "(58, 54)", "frequency": 0.2}, {"edge": "(54, 53)", "frequency": 0.3}, {"edge": "(53, 48)", "frequency": 0.2}, {"edge": "(48, 50)", "frequency": 0.2}, {"edge": "(12, 23)", "frequency": 0.3}, {"edge": "(23, 16)", "frequency": 0.3}, {"edge": "(16, 20)", "frequency": 0.3}, {"edge": "(20, 22)", "frequency": 0.3}, {"edge": "(15, 17)", "frequency": 0.3}, {"edge": "(17, 19)", "frequency": 0.3}, {"edge": "(19, 14)", "frequency": 0.3}, {"edge": "(14, 21)", "frequency": 0.3}, {"edge": "(21, 18)", "frequency": 0.3}, {"edge": "(18, 13)", "frequency": 0.3}, {"edge": "(32, 26)", "frequency": 0.2}, {"edge": "(35, 25)", "frequency": 0.2}, {"edge": "(28, 27)", "frequency": 0.3}, {"edge": "(27, 29)", "frequency": 0.3}, {"edge": "(29, 33)", "frequency": 0.3}, {"edge": "(24, 30)", "frequency": 0.2}, {"edge": "(30, 34)", "frequency": 0.3}, {"edge": "(8, 7)", "frequency": 0.2}, {"edge": "(43, 44)", "frequency": 0.2}, {"edge": "(13, 35)", "frequency": 0.2}, {"edge": "(56, 55)", "frequency": 0.2}, {"edge": "(10, 1)", "frequency": 0.2}, {"edge": "(34, 36)", "frequency": 0.3}, {"edge": "(42, 47)", "frequency": 0.2}, {"edge": "(46, 39)", "frequency": 0.2}, {"edge": "(53, 9)", "frequency": 0.2}, {"edge": "(29, 10)", "frequency": 0.2}, {"edge": "(4, 8)", "frequency": 0.2}, {"edge": "(8, 59)", "frequency": 0.2}, {"edge": "(2, 26)", "frequency": 0.2}, {"edge": "(13, 42)", "frequency": 0.2}, {"edge": "(53, 28)", "frequency": 0.2}, {"edge": "(2, 38)", "frequency": 0.2}, {"edge": "(27, 18)", "frequency": 0.2}, {"edge": "(47, 21)", "frequency": 0.2}, {"edge": "(39, 52)", "frequency": 0.2}, {"edge": "(4, 1)", "frequency": 0.2}, {"edge": "(7, 22)", "frequency": 0.2}, {"edge": "(16, 41)", "frequency": 0.2}, {"edge": "(18, 47)", "frequency": 0.2}, {"edge": "(57, 23)", "frequency": 0.2}, {"edge": "(23, 36)", "frequency": 0.2}, {"edge": "(28, 50)", "frequency": 0.2}, {"edge": "(33, 43)", "frequency": 0.2}, {"edge": "(25, 30)", "frequency": 0.2}, {"edge": "(15, 48)", "frequency": 0.2}]}
- Low Quality Regions: [{"region": [23, 36, 18, 33, 43, 35, 44, 58], "cost": 17797.0, "size": 8}, {"region": [55, 42, 59, 43, 48, 35, 51, 44], "cost": 15984.0, "size": 8}, {"region": [30, 13, 42, 27, 18, 40], "cost": 13926.0, "size": 6}, {"region": [15, 26, 13, 8, 16, 41], "cost": 12580.0, "size": 6}, {"region": [55, 45, 12, 37, 15], "cost": 11746.0, "size": 5}]

## Elite Solution Analysis
- Number of Elite Solutions: 0
- Common Features: {}
- Fixed Nodes: []
- Population Gap: {}
- Elite Diversity: {}

## Historical Trends
No historical trend data

# Analysis Request
Based on the above information, please provide a comprehensive landscape analysis of the search space and current population state. Your analysis should include:

1. Overall search space characteristics
2. Current population state assessment
3. Identification of difficult regions and search challenges
4. Identification of opportunity regions with potential for improvement
5. Recommended evolution direction and strategy balance

2025-06-08 18:39:16,694 - LandscapeExpert - INFO - LLM返回的分析结果: None
2025-06-08 18:39:16,694 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-08 18:39:16,694 - __main__ - INFO - 景观专家分析报告: None
2025-06-08 18:39:16,694 - __main__ - INFO - 分析阶段完成
2025-06-08 18:39:16,694 - __main__ - INFO - 景观分析完整报告: None
2025-06-08 18:39:16,694 - __main__ - INFO - 开始策略分配阶段
2025-06-08 18:39:16,694 - StrategyExpert - INFO - 开始策略分配分析
2025-06-08 18:39:16,694 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
# Strategy Selection Task

## Role Definition
You are an expert in evolutionary algorithms and combinatorial optimization. You excel at analyzing search space characteristics and population states to select the optimal balance between exploration and exploitation, and to assign appropriate strategies to different individuals.

## Current Iteration: 0

## Population Information
[{"id": 0, "cost": 10038.0, "diversity_contribution": null}, {"id": 1, "cost": 9984.0, "diversity_contribution": null}, {"id": 2, "cost": 10246.0, "diversity_contribution": null}, {"id": 3, "cost": 96923.0, "diversity_contribution": null}, {"id": 4, "cost": 99407.0, "diversity_contribution": null}, {"id": 5, "cost": 97248.0, "diversity_contribution": null}, {"id": 6, "cost": 92365.0, "diversity_contribution": null}, {"id": 7, "cost": 91098.0, "diversity_contribution": null}, {"id": 8, "cost": 93926.0, "diversity_contribution": null}, {"id": 9, "cost": 95556.0, "diversity_contribution": null}]

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Population State: Population status extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Recommended Evolution Direction: Suggestions on evolutionary direction extracted from landscape analysis

## Previous Feedback (if available)
None

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. The above data comes from the landscape analysis expert's analysis results and the previous generation's assessment feedback. We need you to determine the optimal exploration/exploitation balance for the current iteration and assign appropriate strategies to each individual.

# Strategy Selection Request
Please determine the optimal strategy allocation plan for the current iteration. Please follow these steps in your thinking:

## Step 1: Analyze Current Search State
- Evaluate search space characteristics (ruggedness, modality, deceptiveness, etc.)
- Analyze the current state of the population (exploration phase, diversity, convergence, etc.)
- Consider feedback and effects from the previous generation (if available)
- Identify the main challenges and opportunities currently faced

## Step 2: Determine Global Exploration Ratio
- Based on search space characteristics, determine the base exploration ratio
- Adjust the exploration ratio based on population state
- Consider the impact of evolutionary stage on exploration needs
- Analyze the effects of the previous generation's strategy to further adjust the ratio
- Determine the final global exploration ratio (between 0.0 and 1.0)

## Step 3: Build Strategy Profiles for Individuals
- Analyze the characteristics of each individual (quality, diversity contribution, historical performance, etc.)
- Evaluate the degree to which each individual is suitable for exploration or exploitation
- Consider the relationship between individuals and difficult/opportunity regions
- Build strategy tendency profiles for each individual

## Step 4: Assign Individual Strategies
- Based on the global exploration ratio, determine the number of individuals to assign exploration strategies
- Prioritize assigning exploration strategies to individuals most suitable for exploration
- Assign exploitation strategies to the remaining individuals
- Ensure strategy allocation complies with the global exploration ratio
- Record the rationale for each allocation decision

## Step 5: Determine Strategy Parameters
- Based on search space characteristics and population state, determine exploration strategy parameters
  - Diversity weight: controls the diversity level of generated solutions
  - Knowledge usage weight: controls the degree of historical knowledge utilization
  - Risk tolerance: controls the possibility of accepting suboptimal solutions
- Based on search space characteristics and population state, determine exploitation strategy parameters
  - Local search depth: controls the depth of local search
  - Quality edge usage: controls the degree of utilizing known high-quality edges
  - Elite influence: controls the influence of elite solutions on new solution generation

Please return your analysis results in JSON format with the following sections:
```json
{
  "global_explore_ratio": float,
  "justification": string,
  "individual_assignments": {
    "individual_id": "explore|exploit",
    ...
  },
  "assignment_rationale": {
    "individual_id": string,
    ...
  },
  "strategy_parameters": {
    "explore": {
      "diversity_weight": float,
      "knowledge_usage_weight": float,
      "risk_tolerance": float
    },
    "exploit": {
      "local_search_depth": int,
      "quality_edge_usage": float,
      "elite_influence": float
    }
  }
}
```

Additionally, please provide a brief summary explaining your strategy selection logic and its expected effects.

2025-06-08 18:39:16,698 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-08 18:39:20,765 - StrategyExpert - INFO - LLM返回的策略分配结果: None
2025-06-08 18:39:20,765 - StrategyExpert - ERROR - 解析策略分配结果时出错: expected string or bytes-like object, got 'NoneType'
2025-06-08 18:39:20,765 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-08 18:39:20,765 - __main__ - INFO - 策略分配报告: None
2025-06-08 18:39:20,765 - __main__ - INFO - 策略分配阶段完成
2025-06-08 18:39:20,765 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-08 18:39:20,765 - __main__ - INFO - 策略分配完整报告: None
2025-06-08 18:39:20,766 - __main__ - INFO - 开始进化阶段
2025-06-08 18:39:20,766 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-08 18:39:20,766 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 18:39:20,778 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 41, 44, 4, 6, 5, 0, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34]

## Path Cost: 10038.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9151851851851853

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 18:39:20,779 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 18:39:25,305 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-08 18:39:25,305 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 18:39:25,307 - IdeaExtractor - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-08 18:39:25,307 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 18:39:25,307 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10038.0, 路径: [39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 41, 44, 4, 6, 5, 0, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34]
2025-06-08 18:39:25,307 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 41, 44, 4, 6, 5, 0, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34], 'cur_cost': 10038.0}
2025-06-08 18:39:25,308 - __main__ - INFO - 为个体 1 生成利用路径
2025-06-08 18:39:25,308 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:39:25,308 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:39:25,308 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 106718.0
2025-06-08 18:39:25,310 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,310 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,314 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,320 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,320 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,325 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,325 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,327 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,330 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,333 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,335 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,342 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:39:25,343 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,344 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,349 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,354 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,356 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,356 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,368 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,375 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,380 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,383 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,389 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,397 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,404 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,407 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:39:25,409 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,418 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,429 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,430 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,432 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,439 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,440 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,446 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,447 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,447 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,448 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,449 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,450 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,453 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,455 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,456 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,456 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,457 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,459 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,460 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: critical_edge
2025-06-08 18:39:25,461 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,462 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,464 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: cluster_based
2025-06-08 18:39:25,464 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,465 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,467 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,469 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,470 - root - INFO - 拓扑感知扰动用时: 0.0012秒，使用策略: segment_preservation
2025-06-08 18:39:25,470 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,473 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,474 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,478 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: critical_edge
2025-06-08 18:39:25,479 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,481 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,482 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,483 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,483 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,484 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,486 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,487 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,490 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,493 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:39:25,494 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,496 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,499 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,501 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,504 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,506 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,508 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,510 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: critical_edge
2025-06-08 18:39:25,511 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,512 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,513 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,515 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,517 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,518 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,519 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,521 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,523 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,524 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,524 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,525 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,526 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,529 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:39:25,529 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,531 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,532 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,534 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: critical_edge
2025-06-08 18:39:25,535 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,539 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,540 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,542 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,542 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,543 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,544 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,544 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,548 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,549 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,550 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,550 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,552 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,553 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,554 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,555 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,556 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,557 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,559 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:39:25,559 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,562 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,563 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,563 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,564 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,566 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,567 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,569 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,571 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,572 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,575 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,577 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,579 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,580 - root - INFO - 拓扑感知扰动用时: 0.0008秒，使用策略: segment_preservation
2025-06-08 18:39:25,582 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,583 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,585 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,586 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,587 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:39:25,588 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,589 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,590 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,590 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,594 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,596 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,597 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,598 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,600 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: segment_preservation
2025-06-08 18:39:25,601 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,601 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,603 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,605 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,605 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,607 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,607 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,607 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,609 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,611 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: critical_edge
2025-06-08 18:39:25,613 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,614 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: critical_edge
2025-06-08 18:39:25,614 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,616 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,618 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,619 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,620 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,621 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,623 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,626 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,627 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,628 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,629 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,630 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,631 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,632 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:39:25,632 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,632 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,635 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,636 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,638 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,638 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,640 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,641 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,641 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,643 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,646 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,648 - root - INFO - 拓扑感知扰动用时: 0.0017秒，使用策略: segment_preservation
2025-06-08 18:39:25,649 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:39:25,651 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,653 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: critical_edge
2025-06-08 18:39:25,654 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,654 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,656 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,656 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,658 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,659 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,660 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,661 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,663 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,663 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,665 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,665 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,667 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,669 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,670 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,671 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,672 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,674 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,678 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,679 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,681 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,682 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,684 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,685 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,686 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,687 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,690 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,691 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,693 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,697 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: critical_edge
2025-06-08 18:39:25,698 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,699 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,700 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,703 - root - INFO - 拓扑感知扰动用时: 0.0014秒，使用策略: segment_preservation
2025-06-08 18:39:25,704 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,705 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,709 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,711 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,712 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,714 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,716 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:39:25,718 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:39:25,720 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,722 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: critical_edge
2025-06-08 18:39:25,723 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:25,725 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:25,726 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,726 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,728 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,730 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,730 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,732 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,736 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,738 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,740 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,742 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,746 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,746 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,748 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,748 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,750 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,752 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: segment_preservation
2025-06-08 18:39:25,754 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,756 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,758 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,760 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,761 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,761 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,763 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: critical_edge
2025-06-08 18:39:25,765 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,768 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,768 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,772 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,774 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,776 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,777 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,779 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,780 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,781 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,781 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,782 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,785 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,788 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,790 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:39:25,790 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,791 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,792 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: critical_edge
2025-06-08 18:39:25,792 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,793 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,794 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,797 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:39:25,797 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,799 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,802 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:39:25,803 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,805 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,806 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,806 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,808 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,808 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:25,809 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,809 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:25,811 - ExploitationExpert - INFO - res_population_num: 20
2025-06-08 18:39:25,813 - ExploitationExpert - INFO - res_population_costs: [9653, 9650, 9639, 9630, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-08 18:39:25,813 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 50, 58,
       49, 57, 52, 59, 48, 53, 55, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 55, 53, 48, 59, 52, 57, 49, 58, 50, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       50, 52, 59, 48, 53, 55, 57, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64)]
2025-06-08 18:39:25,817 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:39:25,817 - ExploitationExpert - INFO - populations: [{'tour': [39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 41, 44, 4, 6, 5, 0, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34], 'cur_cost': 10038.0}, {'tour': array([59, 58, 54,  5, 43, 27, 11, 31,  4, 51, 30, 15, 38, 24, 34,  7, 40,
       10, 21, 42, 39, 25, 20,  2, 28, 50, 37, 17, 47,  8, 33, 13, 45, 32,
       44,  0,  1, 41, 18, 35, 53,  9, 57, 46,  6, 14, 22, 36, 12, 52, 19,
       56, 48, 26, 55,  3, 16, 49, 23, 29]), 'cur_cost': 106718.0}, {'tour': [12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 0, 4, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10246.0}, {'tour': [23, 0, 34, 6, 1, 51, 54, 53, 9, 21, 24, 56, 55, 45, 12, 37, 15, 20, 3, 31, 28, 17, 27, 16, 19, 44, 30, 40, 11, 58, 5, 29, 10, 43, 57, 39, 36, 32, 4, 8, 59, 50, 33, 2, 26, 46, 35, 7, 41, 18, 25, 13, 42, 47, 22, 14, 49, 38, 52, 48], 'cur_cost': 96923.0}, {'tour': [52, 23, 44, 19, 5, 56, 1, 53, 28, 43, 41, 2, 38, 55, 11, 49, 9, 48, 14, 22, 51, 35, 15, 12, 4, 8, 10, 17, 0, 7, 59, 24, 31, 50, 57, 34, 54, 58, 6, 30, 13, 42, 27, 18, 40, 45, 26, 36, 37, 29, 47, 21, 33, 32, 16, 3, 20, 25, 46, 39], 'cur_cost': 99407.0}, {'tour': [10, 33, 24, 25, 40, 3, 46, 4, 1, 45, 57, 9, 7, 22, 15, 26, 13, 8, 16, 41, 5, 49, 58, 20, 14, 39, 0, 32, 54, 17, 47, 36, 53, 31, 18, 21, 52, 37, 12, 38, 56, 50, 55, 42, 59, 43, 48, 35, 51, 44, 11, 28, 30, 6, 34, 19, 2, 27, 23, 29], 'cur_cost': 97248.0}, {'tour': [50, 12, 15, 54, 51, 32, 1, 52, 46, 38, 31, 10, 19, 37, 9, 2, 48, 11, 27, 18, 47, 41, 34, 25, 4, 7, 43, 24, 6, 26, 29, 14, 8, 42, 21, 35, 45, 55, 3, 56, 59, 22, 57, 23, 36, 20, 58, 40, 39, 5, 44, 13, 0, 49, 17, 16, 30, 33, 53, 28], 'cur_cost': 92365.0}, {'tour': [3, 19, 50, 12, 5, 59, 58, 29, 25, 39, 13, 35, 32, 20, 57, 40, 9, 47, 7, 48, 8, 52, 15, 22, 55, 54, 14, 23, 27, 6, 18, 24, 11, 56, 30, 17, 51, 4, 33, 43, 28, 10, 1, 16, 0, 2, 21, 42, 31, 44, 46, 37, 26, 53, 41, 38, 45, 49, 34, 36], 'cur_cost': 91098.0}, {'tour': [26, 20, 51, 40, 53, 38, 4, 1, 19, 55, 22, 13, 5, 58, 33, 28, 35, 21, 34, 36, 31, 29, 3, 18, 47, 46, 41, 32, 0, 8, 7, 12, 9, 50, 6, 25, 30, 39, 27, 44, 59, 10, 11, 57, 14, 37, 49, 52, 43, 23, 42, 15, 48, 17, 56, 24, 45, 54, 16, 2], 'cur_cost': 93926.0}, {'tour': [16, 41, 19, 15, 48, 34, 8, 59, 45, 3, 53, 9, 6, 24, 28, 50, 56, 31, 12, 29, 7, 22, 4, 10, 37, 47, 21, 46, 49, 14, 17, 54, 55, 1, 42, 5, 32, 25, 30, 2, 38, 40, 27, 20, 11, 51, 13, 26, 39, 52, 0, 57, 23, 36, 18, 33, 43, 35, 44, 58], 'cur_cost': 95556.0}]
2025-06-08 18:39:25,817 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-08 18:39:25,817 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 51, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 51, 'cache_hits': 0, 'similarity_calculations': 600, 'cache_hit_rate': 0.0, 'cache_size': 600}}
2025-06-08 18:39:25,817 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-06-08 18:39:25,817 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-08 18:39:25,817 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 18:39:25,828 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 0, 4, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44]

## Path Cost: 10246.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9455555555555555

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 18:39:25,828 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 18:39:30,013 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-08 18:39:30,013 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 18:39:30,013 - IdeaExtractor - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-08 18:39:30,013 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 18:39:30,013 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10246.0, 路径: [12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 0, 4, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44]
2025-06-08 18:39:30,013 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 0, 4, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10246.0}
2025-06-08 18:39:30,013 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-08 18:39:30,013 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:39:30,013 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:39:30,015 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 101951.0
2025-06-08 18:39:30,017 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,021 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,031 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,035 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:30,036 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,043 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 18:39:30,044 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,050 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,057 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,060 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,064 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,067 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:30,070 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,072 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,073 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,079 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:30,090 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:30,093 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:39:30,094 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,095 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,110 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:30,116 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,126 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,136 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,144 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,161 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: segment_preservation
2025-06-08 18:39:30,166 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,174 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,185 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:30,187 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,199 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,200 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,211 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,215 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,216 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,246 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:30,255 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,262 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,273 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,274 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,278 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,281 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:39:30,286 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:30,288 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,290 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:30,291 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,293 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,298 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,302 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,304 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,307 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:30,310 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: segment_preservation
2025-06-08 18:39:30,317 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,319 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: segment_preservation
2025-06-08 18:39:30,325 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:30,327 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: critical_edge
2025-06-08 18:39:30,332 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: cluster_based
2025-06-08 18:39:30,334 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,335 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,337 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:39:30,341 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,342 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,343 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:30,348 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,350 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:30,352 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: pattern_based
2025-06-08 18:39:30,353 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:30,355 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: critical_edge
2025-06-08 18:39:30,357 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:30,363 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,364 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,368 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:30,369 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,372 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,375 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,380 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,382 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:39:30,383 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,384 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,389 - root - INFO - 拓扑感知扰动用时: 0.0008秒，使用策略: segment_preservation
2025-06-08 18:39:30,390 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,394 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:30,400 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:30,405 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:30,406 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,409 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,412 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,415 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:30,416 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,418 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,423 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,426 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,427 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,431 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:30,432 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,433 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,440 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,443 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,447 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,451 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: pattern_based
2025-06-08 18:39:30,452 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,457 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,459 - root - INFO - 拓扑感知扰动用时: 0.0019秒，使用策略: pattern_based
2025-06-08 18:39:30,460 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: critical_edge
2025-06-08 18:39:30,461 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,464 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,465 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,467 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,469 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:39:30,472 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:30,473 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,478 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,479 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:30,481 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,483 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:30,490 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,492 - root - INFO - 拓扑感知扰动用时: 0.0023秒，使用策略: pattern_based
2025-06-08 18:39:30,496 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,499 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:39:30,502 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,504 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:39:30,505 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: segment_preservation
2025-06-08 18:39:30,506 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,512 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: segment_preservation
2025-06-08 18:39:30,514 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:30,516 - ExploitationExpert - INFO - res_population_num: 26
2025-06-08 18:39:30,518 - ExploitationExpert - INFO - res_population_costs: [9653, 9650, 9639, 9630, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-08 18:39:30,518 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 50, 58,
       49, 57, 52, 59, 48, 53, 55, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 55, 53, 48, 59, 52, 57, 49, 58, 50, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       50, 52, 59, 48, 53, 55, 57, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64)]
2025-06-08 18:39:30,531 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:39:30,531 - ExploitationExpert - INFO - populations: [{'tour': [39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 41, 44, 4, 6, 5, 0, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34], 'cur_cost': 10038.0}, {'tour': array([59, 58, 54,  5, 43, 27, 11, 31,  4, 51, 30, 15, 38, 24, 34,  7, 40,
       10, 21, 42, 39, 25, 20,  2, 28, 50, 37, 17, 47,  8, 33, 13, 45, 32,
       44,  0,  1, 41, 18, 35, 53,  9, 57, 46,  6, 14, 22, 36, 12, 52, 19,
       56, 48, 26, 55,  3, 16, 49, 23, 29]), 'cur_cost': 106718.0}, {'tour': [12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 0, 4, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10246.0}, {'tour': array([31, 27, 30, 47, 33, 10, 44, 52, 13, 58, 32, 57, 11, 51, 59,  2, 37,
        1,  0, 14,  4, 54, 35, 55, 56, 25, 15, 50, 48, 46,  5,  8, 42, 40,
       19, 34, 38, 21,  9, 12, 17, 26,  7, 23,  3, 53, 16, 43, 24, 20, 39,
       36, 22,  6, 29, 49, 28, 18, 45, 41]), 'cur_cost': 101951.0}, {'tour': [52, 23, 44, 19, 5, 56, 1, 53, 28, 43, 41, 2, 38, 55, 11, 49, 9, 48, 14, 22, 51, 35, 15, 12, 4, 8, 10, 17, 0, 7, 59, 24, 31, 50, 57, 34, 54, 58, 6, 30, 13, 42, 27, 18, 40, 45, 26, 36, 37, 29, 47, 21, 33, 32, 16, 3, 20, 25, 46, 39], 'cur_cost': 99407.0}, {'tour': [10, 33, 24, 25, 40, 3, 46, 4, 1, 45, 57, 9, 7, 22, 15, 26, 13, 8, 16, 41, 5, 49, 58, 20, 14, 39, 0, 32, 54, 17, 47, 36, 53, 31, 18, 21, 52, 37, 12, 38, 56, 50, 55, 42, 59, 43, 48, 35, 51, 44, 11, 28, 30, 6, 34, 19, 2, 27, 23, 29], 'cur_cost': 97248.0}, {'tour': [50, 12, 15, 54, 51, 32, 1, 52, 46, 38, 31, 10, 19, 37, 9, 2, 48, 11, 27, 18, 47, 41, 34, 25, 4, 7, 43, 24, 6, 26, 29, 14, 8, 42, 21, 35, 45, 55, 3, 56, 59, 22, 57, 23, 36, 20, 58, 40, 39, 5, 44, 13, 0, 49, 17, 16, 30, 33, 53, 28], 'cur_cost': 92365.0}, {'tour': [3, 19, 50, 12, 5, 59, 58, 29, 25, 39, 13, 35, 32, 20, 57, 40, 9, 47, 7, 48, 8, 52, 15, 22, 55, 54, 14, 23, 27, 6, 18, 24, 11, 56, 30, 17, 51, 4, 33, 43, 28, 10, 1, 16, 0, 2, 21, 42, 31, 44, 46, 37, 26, 53, 41, 38, 45, 49, 34, 36], 'cur_cost': 91098.0}, {'tour': [26, 20, 51, 40, 53, 38, 4, 1, 19, 55, 22, 13, 5, 58, 33, 28, 35, 21, 34, 36, 31, 29, 3, 18, 47, 46, 41, 32, 0, 8, 7, 12, 9, 50, 6, 25, 30, 39, 27, 44, 59, 10, 11, 57, 14, 37, 49, 52, 43, 23, 42, 15, 48, 17, 56, 24, 45, 54, 16, 2], 'cur_cost': 93926.0}, {'tour': [16, 41, 19, 15, 48, 34, 8, 59, 45, 3, 53, 9, 6, 24, 28, 50, 56, 31, 12, 29, 7, 22, 4, 10, 37, 47, 21, 46, 49, 14, 17, 54, 55, 1, 42, 5, 32, 25, 30, 2, 38, 40, 27, 20, 11, 51, 13, 26, 39, 52, 0, 57, 23, 36, 18, 33, 43, 35, 44, 58], 'cur_cost': 95556.0}]
2025-06-08 18:39:30,532 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-08 18:39:30,532 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 52, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 52, 'cache_hits': 0, 'similarity_calculations': 601, 'cache_hit_rate': 0.0, 'cache_size': 601}}
2025-06-08 18:39:30,532 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-08 18:39:30,532 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-08 18:39:30,532 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 18:39:30,553 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[52, 23, 44, 19, 5, 56, 1, 53, 28, 43, 41, 2, 38, 55, 11, 49, 9, 48, 14, 22, 51, 35, 15, 12, 4, 8, 10, 17, 0, 7, 59, 24, 31, 50, 57, 34, 54, 58, 6, 30, 13, 42, 27, 18, 40, 45, 26, 36, 37, 29, 47, 21, 33, 32, 16, 3, 20, 25, 46, 39]

## Path Cost: 99407.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9477777777777779

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 18:39:30,555 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 18:39:35,394 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-08 18:39:35,394 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 18:39:35,394 - IdeaExtractor - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-08 18:39:35,394 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 18:39:35,397 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99407.0, 路径: [52, 23, 44, 19, 5, 56, 1, 53, 28, 43, 41, 2, 38, 55, 11, 49, 9, 48, 14, 22, 51, 35, 15, 12, 4, 8, 10, 17, 0, 7, 59, 24, 31, 50, 57, 34, 54, 58, 6, 30, 13, 42, 27, 18, 40, 45, 26, 36, 37, 29, 47, 21, 33, 32, 16, 3, 20, 25, 46, 39]
2025-06-08 18:39:35,397 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [52, 23, 44, 19, 5, 56, 1, 53, 28, 43, 41, 2, 38, 55, 11, 49, 9, 48, 14, 22, 51, 35, 15, 12, 4, 8, 10, 17, 0, 7, 59, 24, 31, 50, 57, 34, 54, 58, 6, 30, 13, 42, 27, 18, 40, 45, 26, 36, 37, 29, 47, 21, 33, 32, 16, 3, 20, 25, 46, 39], 'cur_cost': 99407.0}
2025-06-08 18:39:35,397 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-08 18:39:35,397 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:39:35,397 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:39:35,397 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 99440.0
2025-06-08 18:39:35,404 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,415 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:35,427 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:35,447 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:35,461 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,463 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,478 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:39:35,480 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:35,492 - root - INFO - 拓扑感知扰动用时: 0.0003秒，使用策略: pattern_based
2025-06-08 18:39:35,497 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,500 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:35,502 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:35,509 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,514 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:35,535 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,566 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:39:35,569 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,579 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,594 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: pattern_based
2025-06-08 18:39:35,606 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,611 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,612 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,616 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:35,619 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:35,629 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:35,631 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:35,638 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: pattern_based
2025-06-08 18:39:35,648 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:35,662 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,667 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,678 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,683 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:35,686 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,698 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:39:35,701 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,702 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,704 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,705 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,709 - root - INFO - 拓扑感知扰动用时: 0.0014秒，使用策略: critical_edge
2025-06-08 18:39:35,711 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,713 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:35,715 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:35,720 - root - INFO - 拓扑感知扰动用时: 0.0007秒，使用策略: pattern_based
2025-06-08 18:39:35,722 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:35,725 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:35,727 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,729 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:35,731 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,732 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: segment_preservation
2025-06-08 18:39:35,733 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:35,735 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:35,738 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:39:35,740 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:39:35,742 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:39:35,746 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:35,747 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,751 - root - INFO - 拓扑感知扰动用时: 0.0007秒，使用策略: pattern_based
2025-06-08 18:39:35,753 - root - INFO - 拓扑感知扰动用时: 0.0008秒，使用策略: pattern_based
2025-06-08 18:39:35,754 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,755 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,757 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: segment_preservation
2025-06-08 18:39:35,760 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,762 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: critical_edge
2025-06-08 18:39:35,763 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:35,764 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,765 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:35,766 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:35,769 - root - INFO - 拓扑感知扰动用时: 0.0013秒，使用策略: pattern_based
2025-06-08 18:39:35,770 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,771 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:35,776 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,778 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:39:35,778 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,782 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:35,785 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:35,788 - root - INFO - 拓扑感知扰动用时: 0.0007秒，使用策略: pattern_based
2025-06-08 18:39:35,791 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,793 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,797 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,798 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:35,801 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,802 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,804 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,809 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:39:35,812 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,814 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:35,815 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,816 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,820 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:35,822 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,826 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:39:35,829 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: segment_preservation
2025-06-08 18:39:35,833 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,838 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:35,844 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:35,847 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,850 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:35,856 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:35,860 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: pattern_based
2025-06-08 18:39:35,863 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: adaptive_random
2025-06-08 18:39:35,869 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:35,874 - root - INFO - 拓扑感知扰动用时: 0.0015秒，使用策略: pattern_based
2025-06-08 18:39:35,886 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:35,889 - root - INFO - 拓扑感知扰动用时: 0.0011秒，使用策略: critical_edge
2025-06-08 18:39:35,891 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:35,900 - ExploitationExpert - INFO - res_population_num: 28
2025-06-08 18:39:35,900 - ExploitationExpert - INFO - res_population_costs: [9653, 9650, 9639, 9630, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-08 18:39:35,900 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 50, 58,
       49, 57, 52, 59, 48, 53, 55, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 55, 53, 48, 59, 52, 57, 49, 58, 50, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       50, 52, 59, 48, 53, 55, 57, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64)]
2025-06-08 18:39:35,917 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:39:35,917 - ExploitationExpert - INFO - populations: [{'tour': [39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 41, 44, 4, 6, 5, 0, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34], 'cur_cost': 10038.0}, {'tour': array([59, 58, 54,  5, 43, 27, 11, 31,  4, 51, 30, 15, 38, 24, 34,  7, 40,
       10, 21, 42, 39, 25, 20,  2, 28, 50, 37, 17, 47,  8, 33, 13, 45, 32,
       44,  0,  1, 41, 18, 35, 53,  9, 57, 46,  6, 14, 22, 36, 12, 52, 19,
       56, 48, 26, 55,  3, 16, 49, 23, 29]), 'cur_cost': 106718.0}, {'tour': [12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 0, 4, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10246.0}, {'tour': array([31, 27, 30, 47, 33, 10, 44, 52, 13, 58, 32, 57, 11, 51, 59,  2, 37,
        1,  0, 14,  4, 54, 35, 55, 56, 25, 15, 50, 48, 46,  5,  8, 42, 40,
       19, 34, 38, 21,  9, 12, 17, 26,  7, 23,  3, 53, 16, 43, 24, 20, 39,
       36, 22,  6, 29, 49, 28, 18, 45, 41]), 'cur_cost': 101951.0}, {'tour': [52, 23, 44, 19, 5, 56, 1, 53, 28, 43, 41, 2, 38, 55, 11, 49, 9, 48, 14, 22, 51, 35, 15, 12, 4, 8, 10, 17, 0, 7, 59, 24, 31, 50, 57, 34, 54, 58, 6, 30, 13, 42, 27, 18, 40, 45, 26, 36, 37, 29, 47, 21, 33, 32, 16, 3, 20, 25, 46, 39], 'cur_cost': 99407.0}, {'tour': array([43, 50, 21, 24, 38, 28, 15, 35, 20, 58, 33, 16,  7, 42, 31, 47,  5,
       22, 27, 45, 41,  8, 49,  1, 55, 36, 53, 19, 46, 32, 52,  0, 26, 44,
       14, 56, 13, 59, 34,  3, 54, 23, 17, 30, 51, 18, 29,  9, 10, 11, 57,
       48, 12,  6, 25,  4, 39, 40, 37,  2]), 'cur_cost': 99440.0}, {'tour': [50, 12, 15, 54, 51, 32, 1, 52, 46, 38, 31, 10, 19, 37, 9, 2, 48, 11, 27, 18, 47, 41, 34, 25, 4, 7, 43, 24, 6, 26, 29, 14, 8, 42, 21, 35, 45, 55, 3, 56, 59, 22, 57, 23, 36, 20, 58, 40, 39, 5, 44, 13, 0, 49, 17, 16, 30, 33, 53, 28], 'cur_cost': 92365.0}, {'tour': [3, 19, 50, 12, 5, 59, 58, 29, 25, 39, 13, 35, 32, 20, 57, 40, 9, 47, 7, 48, 8, 52, 15, 22, 55, 54, 14, 23, 27, 6, 18, 24, 11, 56, 30, 17, 51, 4, 33, 43, 28, 10, 1, 16, 0, 2, 21, 42, 31, 44, 46, 37, 26, 53, 41, 38, 45, 49, 34, 36], 'cur_cost': 91098.0}, {'tour': [26, 20, 51, 40, 53, 38, 4, 1, 19, 55, 22, 13, 5, 58, 33, 28, 35, 21, 34, 36, 31, 29, 3, 18, 47, 46, 41, 32, 0, 8, 7, 12, 9, 50, 6, 25, 30, 39, 27, 44, 59, 10, 11, 57, 14, 37, 49, 52, 43, 23, 42, 15, 48, 17, 56, 24, 45, 54, 16, 2], 'cur_cost': 93926.0}, {'tour': [16, 41, 19, 15, 48, 34, 8, 59, 45, 3, 53, 9, 6, 24, 28, 50, 56, 31, 12, 29, 7, 22, 4, 10, 37, 47, 21, 46, 49, 14, 17, 54, 55, 1, 42, 5, 32, 25, 30, 2, 38, 40, 27, 20, 11, 51, 13, 26, 39, 52, 0, 57, 23, 36, 18, 33, 43, 35, 44, 58], 'cur_cost': 95556.0}]
2025-06-08 18:39:35,921 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-08 18:39:35,921 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 53, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 53, 'cache_hits': 0, 'similarity_calculations': 603, 'cache_hit_rate': 0.0, 'cache_size': 603}}
2025-06-08 18:39:35,922 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-08 18:39:35,922 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-08 18:39:35,922 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 18:39:35,947 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[50, 12, 15, 54, 51, 32, 1, 52, 46, 38, 31, 10, 19, 37, 9, 2, 48, 11, 27, 18, 47, 41, 34, 25, 4, 7, 43, 24, 6, 26, 29, 14, 8, 42, 21, 35, 45, 55, 3, 56, 59, 22, 57, 23, 36, 20, 58, 40, 39, 5, 44, 13, 0, 49, 17, 16, 30, 33, 53, 28]

## Path Cost: 92365.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9492592592592592

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 18:39:35,947 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-08 18:39:39,957 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-08 18:39:39,959 - IdeaExtractor - INFO - 开始从探索响应中提取路径
2025-06-08 18:39:39,959 - IdeaExtractor - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-08 18:39:39,959 - ExplorationExpert - INFO - 计算路径成本
2025-06-08 18:39:39,959 - ExplorationExpert - INFO - 探索路径生成完成，成本: 92365.0, 路径: [50, 12, 15, 54, 51, 32, 1, 52, 46, 38, 31, 10, 19, 37, 9, 2, 48, 11, 27, 18, 47, 41, 34, 25, 4, 7, 43, 24, 6, 26, 29, 14, 8, 42, 21, 35, 45, 55, 3, 56, 59, 22, 57, 23, 36, 20, 58, 40, 39, 5, 44, 13, 0, 49, 17, 16, 30, 33, 53, 28]
2025-06-08 18:39:39,959 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [50, 12, 15, 54, 51, 32, 1, 52, 46, 38, 31, 10, 19, 37, 9, 2, 48, 11, 27, 18, 47, 41, 34, 25, 4, 7, 43, 24, 6, 26, 29, 14, 8, 42, 21, 35, 45, 55, 3, 56, 59, 22, 57, 23, 36, 20, 58, 40, 39, 5, 44, 13, 0, 49, 17, 16, 30, 33, 53, 28], 'cur_cost': 92365.0}
2025-06-08 18:39:39,960 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-08 18:39:39,960 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-08 18:39:39,960 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-08 18:39:39,960 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 101159.0
2025-06-08 18:39:39,963 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:39,968 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:40,001 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:39:40,005 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:40,013 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,021 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,025 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,030 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:39:40,032 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:40,035 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,043 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,057 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:40,066 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,092 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,094 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:39:40,100 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,118 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,122 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,128 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:40,141 - root - INFO - 拓扑感知扰动用时: 0.0006秒，使用策略: pattern_based
2025-06-08 18:39:40,143 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,145 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,145 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:40,175 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,177 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,193 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,194 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,198 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:40,210 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:40,218 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:39:40,223 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:40,234 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:39:40,243 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,255 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,257 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,260 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,265 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,269 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:40,271 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,279 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:40,280 - root - INFO - 拓扑感知扰动用时: 0.0004秒，使用策略: pattern_based
2025-06-08 18:39:40,285 - root - INFO - 拓扑感知扰动用时: 0.0021秒，使用策略: pattern_based
2025-06-08 18:39:40,290 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:40,291 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:40,291 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,294 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,297 - root - INFO - 拓扑感知扰动用时: 0.0005秒，使用策略: pattern_based
2025-06-08 18:39:40,305 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:39:40,309 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,311 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:40,312 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:40,314 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,316 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,325 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:40,328 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:40,333 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:40,335 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,337 - root - INFO - 拓扑感知扰动用时: 0.0009秒，使用策略: pattern_based
2025-06-08 18:39:40,345 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:39:40,348 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: critical_edge
2025-06-08 18:39:40,352 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,356 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:40,361 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:40,362 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,365 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:40,367 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:39:40,370 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:40,374 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,379 - root - INFO - 拓扑感知扰动用时: 0.0010秒，使用策略: pattern_based
2025-06-08 18:39:40,381 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,386 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:40,389 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,394 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: segment_preservation
2025-06-08 18:39:40,395 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,397 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,399 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,407 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,420 - root - INFO - 拓扑感知扰动用时: 0.0020秒，使用策略: pattern_based
2025-06-08 18:39:40,430 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,431 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,435 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,445 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,454 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: pattern_based
2025-06-08 18:39:40,458 - root - INFO - 拓扑感知扰动用时: 0.0000秒，使用策略: cluster_based
2025-06-08 18:39:40,464 - ExploitationExpert - INFO - res_population_num: 31
2025-06-08 18:39:40,465 - ExploitationExpert - INFO - res_population_costs: [9653, 9650, 9639, 9630, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-08 18:39:40,466 - ExploitationExpert - INFO - res_populations: [array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 50, 58,
       49, 56, 55, 57, 52, 59, 48, 53, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 50, 58,
       49, 57, 52, 59, 48, 53, 55, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 55, 53, 48, 59, 52, 57, 49, 58, 50, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       50, 52, 59, 48, 53, 55, 57, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 32, 26, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17,
       15, 19, 14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-06-08 18:39:40,484 - ExploitationExpert - INFO - populations_num: 10
2025-06-08 18:39:40,485 - ExploitationExpert - INFO - populations: [{'tour': [39, 46, 40, 37, 47, 42, 38, 45, 36, 43, 41, 44, 4, 6, 5, 0, 1, 10, 11, 2, 7, 8, 3, 9, 59, 51, 52, 57, 55, 56, 49, 58, 54, 53, 48, 50, 12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 31, 32, 26, 35, 25, 28, 27, 29, 33, 24, 30, 34], 'cur_cost': 10038.0}, {'tour': array([59, 58, 54,  5, 43, 27, 11, 31,  4, 51, 30, 15, 38, 24, 34,  7, 40,
       10, 21, 42, 39, 25, 20,  2, 28, 50, 37, 17, 47,  8, 33, 13, 45, 32,
       44,  0,  1, 41, 18, 35, 53,  9, 57, 46,  6, 14, 22, 36, 12, 52, 19,
       56, 48, 26, 55,  3, 16, 49, 23, 29]), 'cur_cost': 106718.0}, {'tour': [12, 23, 16, 20, 22, 15, 17, 19, 14, 21, 18, 13, 54, 56, 55, 53, 57, 52, 59, 51, 50, 58, 49, 48, 9, 3, 2, 7, 11, 10, 1, 0, 4, 6, 5, 8, 26, 32, 31, 25, 35, 28, 27, 29, 33, 24, 30, 34, 36, 45, 38, 42, 47, 37, 40, 46, 39, 41, 43, 44], 'cur_cost': 10246.0}, {'tour': array([31, 27, 30, 47, 33, 10, 44, 52, 13, 58, 32, 57, 11, 51, 59,  2, 37,
        1,  0, 14,  4, 54, 35, 55, 56, 25, 15, 50, 48, 46,  5,  8, 42, 40,
       19, 34, 38, 21,  9, 12, 17, 26,  7, 23,  3, 53, 16, 43, 24, 20, 39,
       36, 22,  6, 29, 49, 28, 18, 45, 41]), 'cur_cost': 101951.0}, {'tour': [52, 23, 44, 19, 5, 56, 1, 53, 28, 43, 41, 2, 38, 55, 11, 49, 9, 48, 14, 22, 51, 35, 15, 12, 4, 8, 10, 17, 0, 7, 59, 24, 31, 50, 57, 34, 54, 58, 6, 30, 13, 42, 27, 18, 40, 45, 26, 36, 37, 29, 47, 21, 33, 32, 16, 3, 20, 25, 46, 39], 'cur_cost': 99407.0}, {'tour': array([43, 50, 21, 24, 38, 28, 15, 35, 20, 58, 33, 16,  7, 42, 31, 47,  5,
       22, 27, 45, 41,  8, 49,  1, 55, 36, 53, 19, 46, 32, 52,  0, 26, 44,
       14, 56, 13, 59, 34,  3, 54, 23, 17, 30, 51, 18, 29,  9, 10, 11, 57,
       48, 12,  6, 25,  4, 39, 40, 37,  2]), 'cur_cost': 99440.0}, {'tour': [50, 12, 15, 54, 51, 32, 1, 52, 46, 38, 31, 10, 19, 37, 9, 2, 48, 11, 27, 18, 47, 41, 34, 25, 4, 7, 43, 24, 6, 26, 29, 14, 8, 42, 21, 35, 45, 55, 3, 56, 59, 22, 57, 23, 36, 20, 58, 40, 39, 5, 44, 13, 0, 49, 17, 16, 30, 33, 53, 28], 'cur_cost': 92365.0}, {'tour': array([24, 51, 25, 38, 27, 56, 15, 44,  3, 36,  2, 35, 59, 37, 11, 33, 28,
       21,  1, 54,  9, 12, 41, 13, 10, 53,  0, 42, 45, 26, 39, 58, 43,  5,
       20, 17, 57, 31, 16, 32, 46, 40, 19, 47, 55, 30, 34, 29,  6, 18,  8,
       52, 49, 48,  4, 50,  7, 14, 23, 22]), 'cur_cost': 101159.0}, {'tour': [26, 20, 51, 40, 53, 38, 4, 1, 19, 55, 22, 13, 5, 58, 33, 28, 35, 21, 34, 36, 31, 29, 3, 18, 47, 46, 41, 32, 0, 8, 7, 12, 9, 50, 6, 25, 30, 39, 27, 44, 59, 10, 11, 57, 14, 37, 49, 52, 43, 23, 42, 15, 48, 17, 56, 24, 45, 54, 16, 2], 'cur_cost': 93926.0}, {'tour': [16, 41, 19, 15, 48, 34, 8, 59, 45, 3, 53, 9, 6, 24, 28, 50, 56, 31, 12, 29, 7, 22, 4, 10, 37, 47, 21, 46, 49, 14, 17, 54, 55, 1, 42, 5, 32, 25, 30, 2, 38, 40, 27, 20, 11, 51, 13, 26, 39, 52, 0, 57, 23, 36, 18, 33, 43, 35, 44, 58], 'cur_cost': 95556.0}]
2025-06-08 18:39:40,487 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-08 18:39:40,487 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 54, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 54, 'cache_hits': 0, 'similarity_calculations': 606, 'cache_hit_rate': 0.0, 'cache_size': 606}}
2025-06-08 18:39:40,489 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-08 18:39:40,489 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-08 18:39:40,489 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-08 18:39:40,517 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
# Exploration Path Generation Task

## Role Definition
You are an expert in combinatorial optimization and exploration strategies. You excel at generating diverse paths, exploring unknown regions of the search space, and discovering potentially high-quality solutions.

## Current Path
[26, 20, 51, 40, 53, 38, 4, 1, 19, 55, 22, 13, 5, 58, 33, 28, 35, 21, 34, 36, 31, 29, 3, 18, 47, 46, 41, 32, 0, 8, 7, 12, 9, 50, 6, 25, 30, 39, 27, 44, 59, 10, 11, 57, 14, 37, 49, 52, 43, 23, 42, 15, 48, 17, 56, 24, 45, 54, 16, 2]

## Path Cost: 93926.0

## Problem Features
TSP problem characteristics

## Landscape Analysis
- Search Space Features: Search spatial features extracted from landscape analysis
- Difficult Regions: Difficult areas extracted from landscape analysis
- Opportunity Regions: Opportunity regions extracted from landscape analysis
- Population Diversity: 0.9511111111111112

## Strategy Parameters
- Diversity Weight: 0.7
- Knowledge Usage Weight: 0.3
- Risk Tolerance: 0.6

## Retrieved Knowledge
No retrieved knowledge

## Problem Background
We are using an evolutionary algorithm to solve a complex combinatorial optimization problem. Your task is to generate an exploratory new path for the current individual to explore different regions of the search space, increase population diversity, and discover potentially high-quality solutions.

# Exploration Request
Please generate a new path that explores different regions of the search space. Please follow these steps in your thinking:

## Step 1: Analyze Current Path
- Identify key features and structural patterns of the current path
- Evaluate the strengths and weaknesses of the current path
- Determine which parts to preserve and which to change

## Step 2: Determine Exploration Strategy
- Based on the diversity weight, determine the degree of mutation
- Based on the knowledge usage weight, determine the application degree of historical knowledge
- Based on the risk tolerance, determine the aggressiveness of exploration
- Select appropriate exploration operations (e.g., large-scale restructuring, regional reconstruction, random mutation)

## Step 3: Select Target Regions
- Analyze the potential value of opportunity regions
- Evaluate the coverage of various regions by the current population
- Select the most valuable and under-covered regions as exploration targets
- Determine difficult regions to avoid

## Step 4: Apply Historical Knowledge
- Analyze relevant exploration experiences from the retrieved knowledge
- Extract strategies and patterns applicable to the current situation
- Determine how to incorporate this knowledge into the new path generation

## Step 5: Generate New Path
- Apply the selected exploration strategy
- Ensure the new path differs significantly from the current path
- Ensure the new path targets the selected target regions
- Ensure the new path avoids difficult regions
- Ensure the new path incorporates relevant historical knowledge

Please provide your exploration suggestion in the following JSON format:
```json
{
  "new_path": [node_indices],
  "modification_strategy": string,
  "targeted_regions": [string, ...],
  "avoided_regions": [string, ...],
  "knowledge_application": string,
  "diversity_contribution": string,
  "expected_benefits": string
}
```

Additionally, please provide a brief explanation of your exploration strategy and expected effects.

2025-06-08 18:39:40,519 - ExplorationExpert - INFO - 调用LLM生成探索路径
