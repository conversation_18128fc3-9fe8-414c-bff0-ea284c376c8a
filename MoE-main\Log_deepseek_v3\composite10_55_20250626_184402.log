2025-06-26 18:44:02,475 - __main__ - INFO - composite10_55 开始进化第 1 代
2025-06-26 18:44:02,475 - __main__ - INFO - 开始分析阶段
2025-06-26 18:44:02,475 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:44:02,484 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10764.0, 'max': 114945.0, 'mean': 75892.7, 'std': 43052.90543981904}, 'diversity': 0.9195959595959597, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:44:02,489 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10764.0, 'max': 114945.0, 'mean': 75892.7, 'std': 43052.90543981904}, 'diversity_level': 0.9195959595959597, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[819, 516], [794, 496], [762, 445], [746, 472], [791, 512], [749, 494], [767, 510], [767, 471], [789, 469], [766, 485], [838, 470], [455, 3402], [394, 3338], [481, 3377], [454, 3375], [412, 3387], [431, 3380], [473, 3354], [418, 3361], [444, 3339], [393, 3380], [422, 3324], [1942, 1370], [1925, 1357], [1877, 1382], [1902, 1350], [1872, 1350], [1963, 1385], [1912, 1401], [1951, 1341], [1871, 1399], [1890, 1327], [1935, 1392], [2950, 892], [3005, 873], [2949, 919], [2966, 905], [2983, 890], [2980, 914], [2997, 855], [3018, 887], [3003, 907], [2971, 883], [2959, 855], [1636, 3351], [1588, 3390], [1672, 3368], [1599, 3369], [1618, 3412], [1677, 3356], [1625, 3388], [1585, 3375], [1611, 3388], [1614, 3342], [1621, 3360]], 'distance_matrix': array([[   0.,   32.,   91., ..., 2979., 2936., 2955.],
       [  32.,    0.,   60., ..., 3005., 2962., 2981.],
       [  91.,   60.,    0., ..., 3063., 3020., 3039.],
       ...,
       [2979., 3005., 3063., ...,    0.,   46.,   30.],
       [2936., 2962., 3020., ...,   46.,    0.,   19.],
       [2955., 2981., 3039., ...,   30.,   19.,    0.]])}
2025-06-26 18:44:02,498 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:44:02,499 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:44:02,499 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:44:02,504 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:44:02,504 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (37, 42), 'frequency': 0.5, 'avg_cost': 14.0}], 'common_subpaths': [{'subpath': (8, 7, 9), 'frequency': 0.3}, {'subpath': (7, 9, 5), 'frequency': 0.3}, {'subpath': (9, 5, 3), 'frequency': 0.3}, {'subpath': (5, 3, 2), 'frequency': 0.3}, {'subpath': (14, 16, 15), 'frequency': 0.3}, {'subpath': (16, 15, 20), 'frequency': 0.3}, {'subpath': (15, 20, 18), 'frequency': 0.3}, {'subpath': (20, 18, 12), 'frequency': 0.3}, {'subpath': (51, 45, 52), 'frequency': 0.3}, {'subpath': (45, 52, 50), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(37, 42)', 'frequency': 0.5}, {'edge': '(34, 40)', 'frequency': 0.4}, {'edge': '(22, 23)', 'frequency': 0.4}, {'edge': '(23, 25)', 'frequency': 0.4}, {'edge': '(1, 4)', 'frequency': 0.4}, {'edge': '(19, 21)', 'frequency': 0.4}, {'edge': '(14, 16)', 'frequency': 0.4}, {'edge': '(15, 16)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(36, 38)', 'frequency': 0.3}, {'edge': '(37, 38)', 'frequency': 0.3}, {'edge': '(42, 43)', 'frequency': 0.2}, {'edge': '(39, 43)', 'frequency': 0.2}, {'edge': '(34, 39)', 'frequency': 0.3}, {'edge': '(40, 41)', 'frequency': 0.3}, {'edge': '(29, 35)', 'frequency': 0.3}, {'edge': '(22, 29)', 'frequency': 0.2}, {'edge': '(25, 31)', 'frequency': 0.3}, {'edge': '(26, 31)', 'frequency': 0.3}, {'edge': '(24, 26)', 'frequency': 0.3}, {'edge': '(24, 30)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.2}, {'edge': '(28, 32)', 'frequency': 0.3}, {'edge': '(27, 32)', 'frequency': 0.2}, {'edge': '(0, 27)', 'frequency': 0.2}, {'edge': '(0, 4)', 'frequency': 0.3}, {'edge': '(1, 8)', 'frequency': 0.2}, {'edge': '(7, 8)', 'frequency': 0.3}, {'edge': '(7, 9)', 'frequency': 0.3}, {'edge': '(5, 9)', 'frequency': 0.3}, {'edge': '(3, 5)', 'frequency': 0.3}, {'edge': '(2, 3)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(10, 21)', 'frequency': 0.2}, {'edge': '(17, 19)', 'frequency': 0.2}, {'edge': '(13, 17)', 'frequency': 0.3}, {'edge': '(13, 14)', 'frequency': 0.2}, {'edge': '(15, 20)', 'frequency': 0.3}, {'edge': '(18, 20)', 'frequency': 0.3}, {'edge': '(12, 18)', 'frequency': 0.3}, {'edge': '(11, 12)', 'frequency': 0.2}, {'edge': '(11, 51)', 'frequency': 0.2}, {'edge': '(45, 51)', 'frequency': 0.3}, {'edge': '(45, 52)', 'frequency': 0.3}, {'edge': '(50, 52)', 'frequency': 0.3}, {'edge': '(48, 50)', 'frequency': 0.3}, {'edge': '(47, 48)', 'frequency': 0.2}, {'edge': '(47, 54)', 'frequency': 0.2}, {'edge': '(44, 54)', 'frequency': 0.3}, {'edge': '(44, 53)', 'frequency': 0.2}, {'edge': '(46, 53)', 'frequency': 0.2}, {'edge': '(46, 49)', 'frequency': 0.3}, {'edge': '(33, 49)', 'frequency': 0.2}, {'edge': '(33, 35)', 'frequency': 0.2}, {'edge': '(44, 49)', 'frequency': 0.2}, {'edge': '(10, 41)', 'frequency': 0.2}, {'edge': '(32, 33)', 'frequency': 0.2}, {'edge': '(12, 36)', 'frequency': 0.2}, {'edge': '(21, 24)', 'frequency': 0.2}, {'edge': '(3, 46)', 'frequency': 0.2}, {'edge': '(2, 50)', 'frequency': 0.2}, {'edge': '(6, 29)', 'frequency': 0.3}, {'edge': '(15, 47)', 'frequency': 0.2}, {'edge': '(4, 7)', 'frequency': 0.2}, {'edge': '(13, 41)', 'frequency': 0.2}, {'edge': '(22, 45)', 'frequency': 0.2}, {'edge': '(5, 18)', 'frequency': 0.2}, {'edge': '(20, 43)', 'frequency': 0.2}, {'edge': '(22, 28)', 'frequency': 0.2}, {'edge': '(20, 30)', 'frequency': 0.2}, {'edge': '(1, 25)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(5, 17)', 'frequency': 0.3}, {'edge': '(5, 27)', 'frequency': 0.3}, {'edge': '(11, 34)', 'frequency': 0.2}, {'edge': '(35, 39)', 'frequency': 0.2}, {'edge': '(35, 44)', 'frequency': 0.2}, {'edge': '(8, 40)', 'frequency': 0.2}, {'edge': '(7, 22)', 'frequency': 0.2}, {'edge': '(6, 28)', 'frequency': 0.2}, {'edge': '(20, 45)', 'frequency': 0.2}, {'edge': '(26, 46)', 'frequency': 0.2}, {'edge': '(21, 33)', 'frequency': 0.2}, {'edge': '(11, 52)', 'frequency': 0.2}, {'edge': '(1, 47)', 'frequency': 0.2}, {'edge': '(21, 41)', 'frequency': 0.2}, {'edge': '(17, 50)', 'frequency': 0.2}, {'edge': '(2, 45)', 'frequency': 0.2}, {'edge': '(25, 51)', 'frequency': 0.2}, {'edge': '(9, 43)', 'frequency': 0.2}, {'edge': '(21, 44)', 'frequency': 0.2}, {'edge': '(8, 37)', 'frequency': 0.2}, {'edge': '(49, 52)', 'frequency': 0.2}, {'edge': '(0, 29)', 'frequency': 0.2}, {'edge': '(15, 27)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [32, 12, 10, 52, 7, 47], 'cost': 14455.0, 'size': 6}, {'region': [28, 13, 0, 50, 38, 48], 'cost': 13970.0, 'size': 6}, {'region': [27, 19, 41, 12, 36], 'cost': 13111.0, 'size': 5}, {'region': [2, 18, 38, 19, 9], 'cost': 12860.0, 'size': 5}, {'region': [0, 53, 42, 13, 41], 'cost': 12799.0, 'size': 5}]}
2025-06-26 18:44:02,506 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:44:02,506 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:44:02,506 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:44:02,506 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 18:44:02,506 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 18:44:02,506 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:44:02,506 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:44:02,509 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:44:02,509 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 10764.0, mean 75892.7, max 114945.0, std 43052.90543981904
- diversity: 0.9195959595959597
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [52432.0, 8076.0, 49879.0] (Δ 22178.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (37, 42), 'frequency': 0.5, 'avg_cost': 14.0}]
- common_subpaths_sample: [{'subpath': (8, 7, 9), 'frequency': 0.3}, {'subpath': (7, 9, 5), 'frequency': 0.3}, {'subpath': (9, 5, 3), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [32, 12, 10, 52, 7, 47], 'cost': 14455.0, 'size': 6}, {'region': [28, 13, 0, 50, 38, 48], 'cost': 13970.0, 'size': 6}, {'region': [27, 19, 41, 12, 36], 'cost': 13111.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [393.0, 445.0, 3018.0, 3412.0]
- centroid/spread: [1546.036, 1896.182] | rms 1526.132
- density_grid(3x3): [[11, 11, 11], [0, 0, 0], [11, 11, 0]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 1167.0, 2061.0, 2863.0, 3627.0]
- long_edge_ratio: 0.248
- direction_hist(8 sectors): [7, 3, 8, 11, 5, 4, 5, 11]
- nn_median_dist: 20.616

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:44:02,510 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:44:30,792 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 13.0 to 3627.0 with Q1-Q3 spread of 1167-2863)",
    "modality": "Potentially multimodal (high diversity score of 0.92, no clustering detected yet)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.248 suggests some misleading long connections)",
    "spatial_distribution": "Clustered distribution (density_grid shows clear concentration in top-left and bottom-left quadrants)"
  },
  "population_state": {
    "coverage": "Good initial exploration (high diversity 0.92, but empty middle grid cells suggest unexplored regions)",
    "convergence": "Early exploration phase (0.0 convergence score, no stagnation detected)",
    "quality_spread": "Wide range of solution quality (min 10764 vs max 114945, std 43052.9)"
  },
  "difficult_regions": [
    {"nodes": [32, 12, 10, 52, 7, 47], "reason": "High-cost region (14455) with spatial concentration in dense quadrant"},
    {"nodes": [28, 13, 0, 50, 38, 48], "reason": "High-cost cluster (13970) near grid centroid"},
    {"nodes": [27, 19, 41, 12, 36], "reason": "Costly subpath (13111) connecting dense regions"}
  ],
  "opportunity_regions": [
    {"nodes": [37, 42], "reason": "High-quality edge (avg cost 14.0, frequency 50%) in dense region"},
    {"subpaths": ["8-7-9", "7-9-5", "9-5-3"], "reason": "Frequent low-cost patterns (30% occurrence)"}
  ],
  "evolution_phase": "Early exploration (iteration 0/5)",
  "evolution_direction": [
    "Operator suggestions: Focused mutation around high-quality edges (37-42)",
    "Path optimization: Exploit common subpaths (8-7-9-5-3 sequences)",
    "Diversity maintenance: Force exploration of empty grid cells (middle-right regions)",
    "Intensification: Local search around current best (Δ22178 improvement rate)"
  ]
}
```
2025-06-26 18:44:30,798 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:44:30,799 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 3627.0 with Q1-Q3 spread of 1167-2863)', 'modality': 'Potentially multimodal (high diversity score of 0.92, no clustering detected yet)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.248 suggests some misleading long connections)', 'spatial_distribution': 'Clustered distribution (density_grid shows clear concentration in top-left and bottom-left quadrants)'}, 'population_state': {'coverage': 'Good initial exploration (high diversity 0.92, but empty middle grid cells suggest unexplored regions)', 'convergence': 'Early exploration phase (0.0 convergence score, no stagnation detected)', 'quality_spread': 'Wide range of solution quality (min 10764 vs max 114945, std 43052.9)'}, 'difficult_regions': [{'nodes': [32, 12, 10, 52, 7, 47], 'reason': 'High-cost region (14455) with spatial concentration in dense quadrant'}, {'nodes': [28, 13, 0, 50, 38, 48], 'reason': 'High-cost cluster (13970) near grid centroid'}, {'nodes': [27, 19, 41, 12, 36], 'reason': 'Costly subpath (13111) connecting dense regions'}], 'opportunity_regions': [{'nodes': [37, 42], 'reason': 'High-quality edge (avg cost 14.0, frequency 50%) in dense region'}, {'subpaths': ['8-7-9', '7-9-5', '9-5-3'], 'reason': 'Frequent low-cost patterns (30% occurrence)'}], 'evolution_phase': 'Early exploration (iteration 0/5)', 'evolution_direction': ['Operator suggestions: Focused mutation around high-quality edges (37-42)', 'Path optimization: Exploit common subpaths (8-7-9-5-3 sequences)', 'Diversity maintenance: Force exploration of empty grid cells (middle-right regions)', 'Intensification: Local search around current best (Δ22178 improvement rate)']}
2025-06-26 18:44:30,799 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:44:30,799 - __main__ - INFO - 分析阶段完成
2025-06-26 18:44:30,799 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 3627.0 with Q1-Q3 spread of 1167-2863)', 'modality': 'Potentially multimodal (high diversity score of 0.92, no clustering detected yet)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.248 suggests some misleading long connections)', 'spatial_distribution': 'Clustered distribution (density_grid shows clear concentration in top-left and bottom-left quadrants)'}, 'population_state': {'coverage': 'Good initial exploration (high diversity 0.92, but empty middle grid cells suggest unexplored regions)', 'convergence': 'Early exploration phase (0.0 convergence score, no stagnation detected)', 'quality_spread': 'Wide range of solution quality (min 10764 vs max 114945, std 43052.9)'}, 'difficult_regions': [{'nodes': [32, 12, 10, 52, 7, 47], 'reason': 'High-cost region (14455) with spatial concentration in dense quadrant'}, {'nodes': [28, 13, 0, 50, 38, 48], 'reason': 'High-cost cluster (13970) near grid centroid'}, {'nodes': [27, 19, 41, 12, 36], 'reason': 'Costly subpath (13111) connecting dense regions'}], 'opportunity_regions': [{'nodes': [37, 42], 'reason': 'High-quality edge (avg cost 14.0, frequency 50%) in dense region'}, {'subpaths': ['8-7-9', '7-9-5', '9-5-3'], 'reason': 'Frequent low-cost patterns (30% occurrence)'}], 'evolution_phase': 'Early exploration (iteration 0/5)', 'evolution_direction': ['Operator suggestions: Focused mutation around high-quality edges (37-42)', 'Path optimization: Exploit common subpaths (8-7-9-5-3 sequences)', 'Diversity maintenance: Force exploration of empty grid cells (middle-right regions)', 'Intensification: Local search around current best (Δ22178 improvement rate)']}
2025-06-26 18:44:30,799 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:44:30,799 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:44:30,799 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 10764.0
  • mean_cost: 75892.7
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 13.0 to 3627.0 with Q1-Q3 spread of 1167-2863), modality Potentially multimodal (high diversity score of 0.92, no clustering detected yet), deceptiveness Possible deceptive regions (long_edge_ratio of 0.248 suggests some misleading long connections)
- Evolution phase: Early exploration (iteration 0/5)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'nodes': [32, 12, 1, {'nodes': [28, 13, 0...
- Opportunity regions (# 2): {'nodes': [37, 42], , {'subpaths': ['8-7-9
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:44:30,799 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:44:30,799 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:44:39,490 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% exploit to anchor promising paths."
}
```
2025-06-26 18:44:39,491 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:44:39,491 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:44:39,491 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% exploit to anchor promising paths."
}
```
2025-06-26 18:44:39,491 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:44:39,491 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:44:39,491 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% exploit to anchor promising paths."
}
```
2025-06-26 18:44:39,493 - __main__ - INFO - 开始进化阶段
2025-06-26 18:44:39,493 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:44:39,493 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:44:39,493 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10764.0 (rank 0.0%)
- Population diversity: 0.7407407407407407
- Evolution phase: Early exploration (iteration 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'nodes': [37, , {'subpaths': ['
- Difficult regions to avoid (sample): {'nodes': [32, , {'nodes': [28, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:44:39,494 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:44:39,495 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:44:52,547 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32],
  "targeted_regions": "Prioritized node 37, avoided 32 & 28 until end",
  "strategy_comment": "Early opportunity focus, deferred difficult nodes"
}
```
2025-06-26 18:44:52,547 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:44:52,547 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19544.0, 路径: [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32]
2025-06-26 18:44:52,549 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}
2025-06-26 18:44:52,549 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:44:52,549 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:44:52,549 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10797.0 (rank 0.0%)
- Population diversity: 0.7925925925925926
- Evolution phase: Early exploration (iteration 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'nodes': [37, , {'subpaths': ['
- Difficult regions to avoid (sample): {'nodes': [32, , {'nodes': [28, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:44:52,550 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:44:52,550 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:45:05,666 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32],
  "targeted_regions": "Opportunity node 37, avoiding 32 & 28 until end",
  "strategy_comment": "Prioritize opportunity node early, defer difficult regions"
}
```
2025-06-26 18:45:05,669 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:45:05,669 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19544.0, 路径: [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32]
2025-06-26 18:45:05,669 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}
2025-06-26 18:45:05,671 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:45:05,671 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:45:05,671 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11222.0 (rank 0.0%)
- Population diversity: 0.762962962962963
- Evolution phase: Early exploration (iteration 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'nodes': [37, , {'subpaths': ['
- Difficult regions to avoid (sample): {'nodes': [32, , {'nodes': [28, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:45:05,671 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:45:05,671 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:45:18,911 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "Nodes 37 and adjacent sparse cells",
  "strategy_comment": "Sequential path targeting opportunity regions while avoiding difficult nodes"
}
```
2025-06-26 18:45:18,911 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:45:18,911 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:45:18,911 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:45:18,913 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:45:18,913 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:45:18,913 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 114945.0 (rank 90.0%)
- Population diversity: 0.7074074074074074
- Evolution phase: Early exploration (iteration 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'nodes': [37, , {'subpaths': ['
- Difficult regions to avoid (sample): {'nodes': [32, , {'nodes': [28, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:45:18,913 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:45:18,913 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:45:32,131 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32],
  "targeted_regions": "Focus on node 37 and adjacent low-density cells, avoid 28 and 32",
  "strategy_comment": "Prioritize opportunity regions while skipping difficult nodes"
}
```
2025-06-26 18:45:32,131 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:45:32,131 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19544.0, 路径: [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32]
2025-06-26 18:45:32,131 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}
2025-06-26 18:45:32,139 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:45:32,139 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:45:32,139 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101078.0 (rank 60.0%)
- Population diversity: 0.6314814814814815
- Evolution phase: Early exploration (iteration 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'nodes': [37, , {'subpaths': ['
- Difficult regions to avoid (sample): {'nodes': [32, , {'nodes': [28, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:45:32,139 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:45:32,139 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:45:47,011 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 37, 12, 45, 23, 5, 18, 42, 7, 29, 51, 34, 16, 48, 3, 21, 54, 9, 39, 25, 14, 47, 30, 8, 41, 19, 52, 36, 11, 44, 22, 6, 17, 43, 10, 33, 15, 49, 4, 20, 53, 28, 2, 24, 50, 13, 46, 27, 1, 38, 26, 40, 31, 35, 32],
  "targeted_regions": "Nodes 37, 45, 42, 51, 48, 54, 39, 47, 52, 44",
  "strategy_comment": "Prioritized opportunity nodes, avoided dense clusters, balanced exploration"
}
```
2025-06-26 18:45:47,011 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:45:47,011 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113865.0, 路径: [0, 37, 12, 45, 23, 5, 18, 42, 7, 29, 51, 34, 16, 48, 3, 21, 54, 9, 39, 25, 14, 47, 30, 8, 41, 19, 52, 36, 11, 44, 22, 6, 17, 43, 10, 33, 15, 49, 4, 20, 53, 28, 2, 24, 50, 13, 46, 27, 1, 38, 26, 40, 31, 35, 32]
2025-06-26 18:45:47,011 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 37, 12, 45, 23, 5, 18, 42, 7, 29, 51, 34, 16, 48, 3, 21, 54, 9, 39, 25, 14, 47, 30, 8, 41, 19, 52, 36, 11, 44, 22, 6, 17, 43, 10, 33, 15, 49, 4, 20, 53, 28, 2, 24, 50, 13, 46, 27, 1, 38, 26, 40, 31, 35, 32], 'cur_cost': 113865.0}
2025-06-26 18:45:47,011 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:45:47,011 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:45:47,011 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 113338.0 (rank 80.0%)
- Population diversity: 0.6388888888888888
- Evolution phase: Early exploration (iteration 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'nodes': [37, , {'subpaths': ['
- Difficult regions to avoid (sample): {'nodes': [32, , {'nodes': [28, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:45:47,011 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:45:47,011 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:46:04,721 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 37, 12, 45, 19, 3, 8, 22, 41, 54, 30, 15, 48, 7, 33, 10, 26, 44, 17, 51, 5, 39, 21, 46, 14, 50, 2, 36, 11, 42, 25, 53, 9, 34, 16, 49, 6, 38, 20, 47, 13, 52, 4, 40, 23, 43, 18, 29, 1, 35, 24, 31, 27, 28, 32],
  "targeted_regions": "Nodes 37, 45, 22, 41, 48, 44, 51, 46, 50, 42, 53",
  "strategy_comment": "Prioritize opportunity regions while avoiding 32 and 28 late"
}
```
2025-06-26 18:46:04,721 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:46:04,721 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105775.0, 路径: [0, 37, 12, 45, 19, 3, 8, 22, 41, 54, 30, 15, 48, 7, 33, 10, 26, 44, 17, 51, 5, 39, 21, 46, 14, 50, 2, 36, 11, 42, 25, 53, 9, 34, 16, 49, 6, 38, 20, 47, 13, 52, 4, 40, 23, 43, 18, 29, 1, 35, 24, 31, 27, 28, 32]
2025-06-26 18:46:04,721 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 37, 12, 45, 19, 3, 8, 22, 41, 54, 30, 15, 48, 7, 33, 10, 26, 44, 17, 51, 5, 39, 21, 46, 14, 50, 2, 36, 11, 42, 25, 53, 9, 34, 16, 49, 6, 38, 20, 47, 13, 52, 4, 40, 23, 43, 18, 29, 1, 35, 24, 31, 27, 28, 32], 'cur_cost': 105775.0}
2025-06-26 18:46:04,721 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:46:04,721 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:46:04,721 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106132.0 (rank 80.0%)
- Population diversity: 0.6240740740740741
- Evolution phase: Early exploration (iteration 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'nodes': [37, , {'subpaths': ['
- Difficult regions to avoid (sample): {'nodes': [32, , {'nodes': [28, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:46:04,721 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:46:04,721 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:46:18,892 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 37, 5, 12, 19, 26, 33, 40, 47, 54, 8, 15, 22, 29, 36, 43, 50, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 1, 9, 16, 23, 30, 39, 46, 53, 7, 14, 21, 28, 35, 42, 49, 2, 11, 18, 25, 32, 44, 51, 4],
  "targeted_regions": "Nodes 37 and adjacent low-density areas",
  "strategy_comment": "Prioritize opportunity regions while avoiding difficult nodes 32, 28"
}
```
2025-06-26 18:46:18,892 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:46:18,892 - ExplorationExpert - INFO - 探索路径生成完成，成本: 90363.0, 路径: [0, 37, 5, 12, 19, 26, 33, 40, 47, 54, 8, 15, 22, 29, 36, 43, 50, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 1, 9, 16, 23, 30, 39, 46, 53, 7, 14, 21, 28, 35, 42, 49, 2, 11, 18, 25, 32, 44, 51, 4]
2025-06-26 18:46:18,892 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 37, 5, 12, 19, 26, 33, 40, 47, 54, 8, 15, 22, 29, 36, 43, 50, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 1, 9, 16, 23, 30, 39, 46, 53, 7, 14, 21, 28, 35, 42, 49, 2, 11, 18, 25, 32, 44, 51, 4], 'cur_cost': 90363.0}
2025-06-26 18:46:18,900 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 18:46:18,900 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:46:18,900 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 93049.0 (rank 50.0%)
- Population diversity: 0.6407407407407407
- Evolution phase: Early exploration (iteration 0/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'nodes': [37, , {'subpaths': ['
- Difficult regions to avoid (sample): {'nodes': [32, , {'nodes': [28, ...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:46:18,900 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:46:18,900 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:46:32,136 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32],
  "targeted_regions": "Focus on node 37 and adjacent sparse cells, avoid 28 & 32",
  "strategy_comment": "Prioritize opportunity regions while balancing diversity"
}
```
2025-06-26 18:46:32,145 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:46:32,145 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19544.0, 路径: [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32]
2025-06-26 18:46:32,145 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}
2025-06-26 18:46:32,145 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:46:32,145 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:46:32,145 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:46:32,145 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108971.0
2025-06-26 18:46:32,647 - ExploitationExpert - INFO - res_population_num: 10
2025-06-26 18:46:32,647 - ExploitationExpert - INFO - res_population_costs: [10509, 10467, 10450, 10450, 10444, 10442, 10442, 10442, 10442, 10442]
2025-06-26 18:46:32,647 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64)]
2025-06-26 18:46:32,649 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:46:32,651 - ExploitationExpert - INFO - populations: [{'tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}, {'tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}, {'tour': [0, 37, 12, 45, 23, 5, 18, 42, 7, 29, 51, 34, 16, 48, 3, 21, 54, 9, 39, 25, 14, 47, 30, 8, 41, 19, 52, 36, 11, 44, 22, 6, 17, 43, 10, 33, 15, 49, 4, 20, 53, 28, 2, 24, 50, 13, 46, 27, 1, 38, 26, 40, 31, 35, 32], 'cur_cost': 113865.0}, {'tour': [0, 37, 12, 45, 19, 3, 8, 22, 41, 54, 30, 15, 48, 7, 33, 10, 26, 44, 17, 51, 5, 39, 21, 46, 14, 50, 2, 36, 11, 42, 25, 53, 9, 34, 16, 49, 6, 38, 20, 47, 13, 52, 4, 40, 23, 43, 18, 29, 1, 35, 24, 31, 27, 28, 32], 'cur_cost': 105775.0}, {'tour': [0, 37, 5, 12, 19, 26, 33, 40, 47, 54, 8, 15, 22, 29, 36, 43, 50, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 1, 9, 16, 23, 30, 39, 46, 53, 7, 14, 21, 28, 35, 42, 49, 2, 11, 18, 25, 32, 44, 51, 4], 'cur_cost': 90363.0}, {'tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}, {'tour': array([26, 35, 51, 13, 25,  4, 21,  9, 11, 14,  3, 15, 37, 20,  5, 53, 44,
       10, 28,  8, 17, 38, 50, 45, 32, 43, 41, 49,  6,  2, 33, 23, 19, 48,
       29, 12, 24, 36, 22, 39, 31, 46, 27,  7, 42, 40, 54, 52, 30,  0, 34,
       18,  1, 47, 16]), 'cur_cost': 108971.0}, {'tour': [48, 40, 31, 50, 11, 17, 54, 42, 0, 29, 4, 7, 22, 16, 30, 32, 33, 35, 39, 8, 9, 36, 19, 20, 37, 38, 26, 46, 14, 45, 2, 51, 23, 25, 1, 53, 13, 43, 6, 28, 12, 52, 47, 34, 49, 18, 5, 27, 15, 3, 10, 41, 44, 21, 24], 'cur_cost': 93597.0}]
2025-06-26 18:46:32,651 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:46:32,651 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 267, 'skip_rate': 0.03745318352059925, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 257, 'cache_hits': 176, 'similarity_calculations': 4000, 'cache_hit_rate': 0.044, 'cache_size': 3824}}
2025-06-26 18:46:32,651 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:46:32,651 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:46:32,651 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:46:32,651 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:46:32,653 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 113141.0
2025-06-26 18:46:33,155 - ExploitationExpert - INFO - res_population_num: 11
2025-06-26 18:46:33,155 - ExploitationExpert - INFO - res_population_costs: [10509, 10467, 10450, 10450, 10444, 10442, 10442, 10442, 10442, 10442, 10442]
2025-06-26 18:46:33,156 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64)]
2025-06-26 18:46:33,160 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:46:33,160 - ExploitationExpert - INFO - populations: [{'tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}, {'tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}, {'tour': [0, 37, 12, 45, 23, 5, 18, 42, 7, 29, 51, 34, 16, 48, 3, 21, 54, 9, 39, 25, 14, 47, 30, 8, 41, 19, 52, 36, 11, 44, 22, 6, 17, 43, 10, 33, 15, 49, 4, 20, 53, 28, 2, 24, 50, 13, 46, 27, 1, 38, 26, 40, 31, 35, 32], 'cur_cost': 113865.0}, {'tour': [0, 37, 12, 45, 19, 3, 8, 22, 41, 54, 30, 15, 48, 7, 33, 10, 26, 44, 17, 51, 5, 39, 21, 46, 14, 50, 2, 36, 11, 42, 25, 53, 9, 34, 16, 49, 6, 38, 20, 47, 13, 52, 4, 40, 23, 43, 18, 29, 1, 35, 24, 31, 27, 28, 32], 'cur_cost': 105775.0}, {'tour': [0, 37, 5, 12, 19, 26, 33, 40, 47, 54, 8, 15, 22, 29, 36, 43, 50, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 1, 9, 16, 23, 30, 39, 46, 53, 7, 14, 21, 28, 35, 42, 49, 2, 11, 18, 25, 32, 44, 51, 4], 'cur_cost': 90363.0}, {'tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}, {'tour': array([26, 35, 51, 13, 25,  4, 21,  9, 11, 14,  3, 15, 37, 20,  5, 53, 44,
       10, 28,  8, 17, 38, 50, 45, 32, 43, 41, 49,  6,  2, 33, 23, 19, 48,
       29, 12, 24, 36, 22, 39, 31, 46, 27,  7, 42, 40, 54, 52, 30,  0, 34,
       18,  1, 47, 16]), 'cur_cost': 108971.0}, {'tour': array([34, 37,  3, 50, 13,  2, 19, 30,  5,  6, 52,  1,  4, 22, 31, 38, 51,
       42, 39, 53, 35, 23,  0, 11, 54, 12, 27, 45,  9, 46, 28, 17, 44, 16,
       43, 40, 49, 24, 20, 10, 32, 47,  7, 15,  8, 48, 29, 41, 25, 21, 36,
       18, 26, 14, 33]), 'cur_cost': 113141.0}]
2025-06-26 18:46:33,161 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:46:33,161 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 268, 'skip_rate': 0.03731343283582089, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 258, 'cache_hits': 176, 'similarity_calculations': 4001, 'cache_hit_rate': 0.043989002749312674, 'cache_size': 3825}}
2025-06-26 18:46:33,161 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:46:33,161 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 37, 12, 45, 23, 5, 18, 42, 7, 29, 51, 34, 16, 48, 3, 21, 54, 9, 39, 25, 14, 47, 30, 8, 41, 19, 52, 36, 11, 44, 22, 6, 17, 43, 10, 33, 15, 49, 4, 20, 53, 28, 2, 24, 50, 13, 46, 27, 1, 38, 26, 40, 31, 35, 32], 'cur_cost': 113865.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 37, 12, 45, 19, 3, 8, 22, 41, 54, 30, 15, 48, 7, 33, 10, 26, 44, 17, 51, 5, 39, 21, 46, 14, 50, 2, 36, 11, 42, 25, 53, 9, 34, 16, 49, 6, 38, 20, 47, 13, 52, 4, 40, 23, 43, 18, 29, 1, 35, 24, 31, 27, 28, 32], 'cur_cost': 105775.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 37, 5, 12, 19, 26, 33, 40, 47, 54, 8, 15, 22, 29, 36, 43, 50, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 1, 9, 16, 23, 30, 39, 46, 53, 7, 14, 21, 28, 35, 42, 49, 2, 11, 18, 25, 32, 44, 51, 4], 'cur_cost': 90363.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 37, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 28, 32], 'cur_cost': 19544.0}}]
2025-06-26 18:46:33,162 - __main__ - INFO - 进化阶段完成
2025-06-26 18:46:33,162 - __main__ - INFO - 开始评估阶段
2025-06-26 18:46:33,162 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:46:33,163 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:46:33,163 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 10764.0 → 14860.0 (Δ -4096.0)
- Mean cost: 75892.7 → 62515.1
- Diversity: 0.741 → 0.572 (Δ -0.1685)

#### Elite metrics
- Elite count: 0 → 11
- Best elite cost: 10764.0 → 10442 (Δ 322.0)

#### Strategy performance
- Explore: 8 inds, success 50.0% , avg Δ 19785.75
- Exploit: 2 inds, success 0.0% , avg Δ -12255.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [52432.0, 8076.0, 49879.0], diversity [0.6361111111111111, 0.6319444444444444, 0.6398148148148148]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:46:33,164 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:46:47,136 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early-to-mid",
  "strategy_effectiveness": {
    "explore": {
      "contribution_to_elites": "high",
      "success_rate": 50.0,
      "average_delta": 19785.75
    },
    "exploit": {
      "contribution_to_elites": "low",
      "success_rate": 0.0,
      "average_delta": -12255.0
    }
  },
  "balance_state": {
    "current_ratio": "80/20 (explore/exploit)",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": "slight rebalancing toward exploit"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "exploit_attempts": "increase by 1-2 individuals",
    "mutation_rate": "consider slight increase"
  }
}
```
2025-06-26 18:46:47,160 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:46:47,160 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early-to-mid",
  "strategy_effectiveness": {
    "explore": {
      "contribution_to_elites": "high",
      "success_rate": 50.0,
      "average_delta": 19785.75
    },
    "exploit": {
      "contribution_to_elites": "low",
      "success_rate": 0.0,
      "average_delta": -12255.0
    }
  },
  "balance_state": {
    "current_ratio": "80/20 (explore/exploit)",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": "slight rebalancing toward exploit"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "exploit_attempts": "increase by 1-2 individuals",
    "mutation_rate": "consider slight increase"
  }
}
```
2025-06-26 18:46:47,160 - __main__ - INFO - 评估阶段完成
2025-06-26 18:46:47,160 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early-to-mid",
  "strategy_effectiveness": {
    "explore": {
      "contribution_to_elites": "high",
      "success_rate": 50.0,
      "average_delta": 19785.75
    },
    "exploit": {
      "contribution_to_elites": "low",
      "success_rate": 0.0,
      "average_delta": -12255.0
    }
  },
  "balance_state": {
    "current_ratio": "80/20 (explore/exploit)",
    "assessment": "explore-heavy but effective",
    "needs_adjustment": "slight rebalancing toward exploit"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "exploit_attempts": "increase by 1-2 individuals",
    "mutation_rate": "consider slight increase"
  }
}
```
2025-06-26 18:46:47,160 - __main__ - INFO - 当前最佳适应度: 14860.0
2025-06-26 18:46:47,163 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite10_55_route_0.pkl
2025-06-26 18:46:47,163 - __main__ - INFO - composite10_55 开始进化第 2 代
2025-06-26 18:46:47,163 - __main__ - INFO - 开始分析阶段
2025-06-26 18:46:47,163 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:46:47,176 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 14860.0, 'max': 113865.0, 'mean': 62515.1, 'std': 44341.78549055055}, 'diversity': 0.7684848484848484, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:46:47,177 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 14860.0, 'max': 113865.0, 'mean': 62515.1, 'std': 44341.78549055055}, 'diversity_level': 0.7684848484848484, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'coordinates': [[819, 516], [794, 496], [762, 445], [746, 472], [791, 512], [749, 494], [767, 510], [767, 471], [789, 469], [766, 485], [838, 470], [455, 3402], [394, 3338], [481, 3377], [454, 3375], [412, 3387], [431, 3380], [473, 3354], [418, 3361], [444, 3339], [393, 3380], [422, 3324], [1942, 1370], [1925, 1357], [1877, 1382], [1902, 1350], [1872, 1350], [1963, 1385], [1912, 1401], [1951, 1341], [1871, 1399], [1890, 1327], [1935, 1392], [2950, 892], [3005, 873], [2949, 919], [2966, 905], [2983, 890], [2980, 914], [2997, 855], [3018, 887], [3003, 907], [2971, 883], [2959, 855], [1636, 3351], [1588, 3390], [1672, 3368], [1599, 3369], [1618, 3412], [1677, 3356], [1625, 3388], [1585, 3375], [1611, 3388], [1614, 3342], [1621, 3360]], 'distance_matrix': array([[   0.,   32.,   91., ..., 2979., 2936., 2955.],
       [  32.,    0.,   60., ..., 3005., 2962., 2981.],
       [  91.,   60.,    0., ..., 3063., 3020., 3039.],
       ...,
       [2979., 3005., 3063., ...,    0.,   46.,   30.],
       [2936., 2962., 3020., ...,   46.,    0.,   19.],
       [2955., 2981., 3039., ...,   30.,   19.,    0.]])}
2025-06-26 18:46:47,178 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:46:47,178 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:46:47,178 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:46:47,178 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:46:47,178 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:46:47,178 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:46:47,178 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:46:47,178 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:46:47,178 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:46:47,178 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:46:47,178 - __main__ - INFO - 精英专家分析报告: {'elite_count': 11, 'elite_common_features': {'common_edges': {'(19, 21)': 0.7272727272727273, '(12, 18)': 0.7272727272727273}, 'common_edge_ratio': 0.03636363636363636}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 4418.0, 'avg_gap': 52063.1}, 'structure_gap': {'unique_elite_edges': 127, 'unique_pop_edges': 299, 'common_edges': 20}}, 'elite_diversity': {'diversity_score': 0.17983471074380164}}
2025-06-26 18:46:47,178 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:46:47,178 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:46:47,186 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:46:47,186 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 14860.0, mean 62515.1, max 113865.0, std 44341.78549055055
- diversity: 0.7684848484848484
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [8076.0, 49879.0, 14860.0] (Δ 17509.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [393.0, 445.0, 3018.0, 3412.0]
- centroid/spread: [1546.036, 1896.182] | rms 1526.132
- density_grid(3x3): [[11, 11, 11], [0, 0, 0], [11, 11, 0]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 1167.0, 2061.0, 2863.0, 3627.0]
- long_edge_ratio: 0.248
- direction_hist(8 sectors): [7, 3, 8, 11, 5, 4, 5, 11]
- nn_median_dist: 20.616

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:46:47,186 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:47:07,038 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 13.0 to 3627.0, with significant standard deviation)",
    "modality": "Potentially multimodal (varied direction histogram and edge length distribution)",
    "deceptiveness": "Possible deceptive regions (presence of long edges and uneven spatial distribution)"
  },
  "population_state": {
    "coverage": "Partial (density_grid shows clusters in some cells, others empty)",
    "convergence": "Early stage (diversity 0.768, convergence 0.0)",
    "spread": "Concentrated in specific regions (centroid at [1546.036, 1896.182] with some outliers)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Edges with lengths in q3-max range (2863.0-3627.0)",
    "low_density_cells": "Cells with 0 density in density_grid"
  },
  "opportunity_regions": {
    "high_density_cells": "Cells with 11 density in density_grid",
    "short_edge_clusters": "Edges with lengths in min-q1 range (13.0-1167.0)"
  },
  "evolution_phase": "Exploration (high diversity, no convergence, early iteration)",
  "evolution_direction": {
    "suggested_operators": [
      "Edge recombination to exploit high-density regions",
      "Local search around short-edge clusters",
      "Diversity preservation for low-density cells",
      "Path relinking between different high-density clusters"
    ]
  }
}
```
2025-06-26 18:47:07,045 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:47:07,045 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 3627.0, with significant standard deviation)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible deceptive regions (presence of long edges and uneven spatial distribution)'}, 'population_state': {'coverage': 'Partial (density_grid shows clusters in some cells, others empty)', 'convergence': 'Early stage (diversity 0.768, convergence 0.0)', 'spread': 'Concentrated in specific regions (centroid at [1546.036, 1896.182] with some outliers)'}, 'difficult_regions': {'long_edge_corridors': 'Edges with lengths in q3-max range (2863.0-3627.0)', 'low_density_cells': 'Cells with 0 density in density_grid'}, 'opportunity_regions': {'high_density_cells': 'Cells with 11 density in density_grid', 'short_edge_clusters': 'Edges with lengths in min-q1 range (13.0-1167.0)'}, 'evolution_phase': 'Exploration (high diversity, no convergence, early iteration)', 'evolution_direction': {'suggested_operators': ['Edge recombination to exploit high-density regions', 'Local search around short-edge clusters', 'Diversity preservation for low-density cells', 'Path relinking between different high-density clusters']}}
2025-06-26 18:47:07,046 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:47:07,046 - __main__ - INFO - 分析阶段完成
2025-06-26 18:47:07,047 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 3627.0, with significant standard deviation)', 'modality': 'Potentially multimodal (varied direction histogram and edge length distribution)', 'deceptiveness': 'Possible deceptive regions (presence of long edges and uneven spatial distribution)'}, 'population_state': {'coverage': 'Partial (density_grid shows clusters in some cells, others empty)', 'convergence': 'Early stage (diversity 0.768, convergence 0.0)', 'spread': 'Concentrated in specific regions (centroid at [1546.036, 1896.182] with some outliers)'}, 'difficult_regions': {'long_edge_corridors': 'Edges with lengths in q3-max range (2863.0-3627.0)', 'low_density_cells': 'Cells with 0 density in density_grid'}, 'opportunity_regions': {'high_density_cells': 'Cells with 11 density in density_grid', 'short_edge_clusters': 'Edges with lengths in min-q1 range (13.0-1167.0)'}, 'evolution_phase': 'Exploration (high diversity, no convergence, early iteration)', 'evolution_direction': {'suggested_operators': ['Edge recombination to exploit high-density regions', 'Local search around short-edge clusters', 'Diversity preservation for low-density cells', 'Path relinking between different high-density clusters']}}
2025-06-26 18:47:07,047 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:47:07,047 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:47:07,047 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 14860.0
  • mean_cost: 62515.1
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 13.0 to 3627.0, with significant standard deviation), modality Potentially multimodal (varied direction histogram and edge length distribution), deceptiveness Possible deceptive regions (presence of long edges and uneven spatial distribution)
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_cells
- Opportunity regions (# 2): high_density_cells, short_edge_clusters
- Last-iteration feedback: ```json
{
  "evolution_stage": "early-to-mid",
  "strategy_effectiveness": {
    "explore": {
      "contribution_to_eli...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:47:07,047 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:47:07,047 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:47:16,949 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity suggests strong exploration focus. Top 30% performers exploit opportunity regions while others explore rugged landscape."
}
```
2025-06-26 18:47:16,949 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:47:16,949 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:47:16,949 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity suggests strong exploration focus. Top 30% performers exploit opportunity regions while others explore rugged landscape."
}
```
2025-06-26 18:47:16,949 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:47:16,949 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:47:16,949 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with high diversity suggests strong exploration focus. Top 30% performers exploit opportunity regions while others explore rugged landscape."
}
```
2025-06-26 18:47:16,953 - __main__ - INFO - 开始进化阶段
2025-06-26 18:47:16,953 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:47:16,953 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:47:16,953 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19544.0 (rank 10.0%)
- Population diversity: 0.5722222222222222
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:47:16,953 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:47:16,953 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:47:29,714 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters while avoiding long edges"
}
```
2025-06-26 18:47:29,714 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:47:29,714 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:47:29,714 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:47:29,714 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:47:29,714 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:47:29,714 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19544.0 (rank 20.0%)
- Population diversity: 0.5722222222222222
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:47:29,714 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:47:29,714 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:47:42,415 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters with short edges"
}
```
2025-06-26 18:47:42,415 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:47:42,415 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:47:42,419 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:47:42,419 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:47:42,419 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:47:42,419 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.5722222222222222
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:47:42,419 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:47:42,419 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:47:54,828 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Sequential exploration of opportunity regions"
}
```
2025-06-26 18:47:54,828 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:47:54,828 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:47:54,830 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:47:54,830 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:47:54,830 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:47:54,830 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19544.0 (rank 30.0%)
- Population diversity: 0.5722222222222222
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:47:54,830 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:47:54,830 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:48:07,344 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Alternating sweep through odd/even nodes to explore sparse cells"
}
```
2025-06-26 18:48:07,344 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:48:07,344 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20991.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 18:48:07,344 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 20991.0}
2025-06-26 18:48:07,344 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:48:07,347 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:48:07,347 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 113865.0 (rank 90.0%)
- Population diversity: 0.6555555555555556
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:48:07,347 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:48:07,347 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:48:20,205 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Sequential exploration of adjacent nodes"
}
```
2025-06-26 18:48:20,205 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:48:20,212 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:48:20,212 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:48:20,212 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:48:20,212 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:48:20,213 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 105775.0 (rank 70.0%)
- Population diversity: 0.5722222222222222
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:48:20,213 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:48:20,213 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:48:32,490 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Sequential exploration of opportunity regions"
}
```
2025-06-26 18:48:32,496 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:48:32,496 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:48:32,496 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:48:32,496 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:48:32,496 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:48:32,498 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 90363.0 (rank 70.0%)
- Population diversity: 0.48333333333333334
- Evolution phase: Exploration (high diversity, no convergence, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:48:32,498 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:48:32,498 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:48:44,808 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Reverse initial segment to explore sparse cells"
}
```
2025-06-26 18:48:44,808 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:48:44,808 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20485.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:48:44,808 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 20485.0}
2025-06-26 18:48:44,808 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:48:44,808 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:48:44,808 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:48:44,816 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 96186.0
2025-06-26 18:48:45,318 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:48:45,319 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509, 10442, 10442, 10442]
2025-06-26 18:48:45,319 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64)]
2025-06-26 18:48:45,323 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:48:45,323 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 20991.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 20485.0}, {'tour': array([45, 51, 38, 13, 19, 40,  8,  0, 23, 35, 44, 37, 33, 30, 53, 15, 10,
       31,  6, 42,  2,  5, 39, 50, 12, 18,  3,  4, 32, 27, 11, 25, 52, 26,
       36,  7,  1, 28, 43, 20, 54, 34, 17, 24, 29, 47,  9, 14, 49, 41, 48,
       16, 21, 46, 22]), 'cur_cost': 96186.0}, {'tour': array([26, 35, 51, 13, 25,  4, 21,  9, 11, 14,  3, 15, 37, 20,  5, 53, 44,
       10, 28,  8, 17, 38, 50, 45, 32, 43, 41, 49,  6,  2, 33, 23, 19, 48,
       29, 12, 24, 36, 22, 39, 31, 46, 27,  7, 42, 40, 54, 52, 30,  0, 34,
       18,  1, 47, 16]), 'cur_cost': 108971.0}, {'tour': array([34, 37,  3, 50, 13,  2, 19, 30,  5,  6, 52,  1,  4, 22, 31, 38, 51,
       42, 39, 53, 35, 23,  0, 11, 54, 12, 27, 45,  9, 46, 28, 17, 44, 16,
       43, 40, 49, 24, 20, 10, 32, 47,  7, 15,  8, 48, 29, 41, 25, 21, 36,
       18, 26, 14, 33]), 'cur_cost': 113141.0}]
2025-06-26 18:48:45,324 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 18:48:45,324 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 269, 'skip_rate': 0.03717472118959108, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 259, 'cache_hits': 176, 'similarity_calculations': 4003, 'cache_hit_rate': 0.043967024731451414, 'cache_size': 3827}}
2025-06-26 18:48:45,324 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:48:45,325 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:48:45,325 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:48:45,326 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:48:45,326 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 92160.0
2025-06-26 18:48:45,827 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:48:45,828 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509, 10442, 10442, 10442]
2025-06-26 18:48:45,828 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64)]
2025-06-26 18:48:45,833 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:48:45,833 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 20991.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 20485.0}, {'tour': array([45, 51, 38, 13, 19, 40,  8,  0, 23, 35, 44, 37, 33, 30, 53, 15, 10,
       31,  6, 42,  2,  5, 39, 50, 12, 18,  3,  4, 32, 27, 11, 25, 52, 26,
       36,  7,  1, 28, 43, 20, 54, 34, 17, 24, 29, 47,  9, 14, 49, 41, 48,
       16, 21, 46, 22]), 'cur_cost': 96186.0}, {'tour': array([19,  1,  6, 17, 42, 26, 14, 12, 21, 33, 45, 54, 40, 37, 51,  4, 22,
       34, 48, 35, 10, 39, 38,  7, 20, 18, 52,  9, 24, 31, 25,  3, 32, 15,
        2, 29, 43,  8, 44, 50, 53,  5, 28, 46, 30, 11, 16, 49, 27, 41,  0,
       23, 36, 47, 13]), 'cur_cost': 92160.0}, {'tour': array([34, 37,  3, 50, 13,  2, 19, 30,  5,  6, 52,  1,  4, 22, 31, 38, 51,
       42, 39, 53, 35, 23,  0, 11, 54, 12, 27, 45,  9, 46, 28, 17, 44, 16,
       43, 40, 49, 24, 20, 10, 32, 47,  7, 15,  8, 48, 29, 41, 25, 21, 36,
       18, 26, 14, 33]), 'cur_cost': 113141.0}]
2025-06-26 18:48:45,834 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:48:45,834 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 270, 'skip_rate': 0.037037037037037035, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 260, 'cache_hits': 176, 'similarity_calculations': 4006, 'cache_hit_rate': 0.04393409885172242, 'cache_size': 3830}}
2025-06-26 18:48:45,834 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:48:45,834 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:48:45,835 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:48:45,835 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:48:45,836 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 107224.0
2025-06-26 18:48:46,339 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:48:46,339 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509, 10442, 10442, 10442]
2025-06-26 18:48:46,339 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64)]
2025-06-26 18:48:46,344 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:48:46,344 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 20991.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 20485.0}, {'tour': array([45, 51, 38, 13, 19, 40,  8,  0, 23, 35, 44, 37, 33, 30, 53, 15, 10,
       31,  6, 42,  2,  5, 39, 50, 12, 18,  3,  4, 32, 27, 11, 25, 52, 26,
       36,  7,  1, 28, 43, 20, 54, 34, 17, 24, 29, 47,  9, 14, 49, 41, 48,
       16, 21, 46, 22]), 'cur_cost': 96186.0}, {'tour': array([19,  1,  6, 17, 42, 26, 14, 12, 21, 33, 45, 54, 40, 37, 51,  4, 22,
       34, 48, 35, 10, 39, 38,  7, 20, 18, 52,  9, 24, 31, 25,  3, 32, 15,
        2, 29, 43,  8, 44, 50, 53,  5, 28, 46, 30, 11, 16, 49, 27, 41,  0,
       23, 36, 47, 13]), 'cur_cost': 92160.0}, {'tour': array([25, 28,  3, 18, 27, 13, 41, 10,  6,  0, 11,  5, 19, 31,  9, 38, 24,
       15, 54, 30, 22, 48, 32,  2, 20, 45, 50, 34, 29, 12, 33, 14, 35,  1,
       39,  7, 23, 26, 44, 40, 52, 51, 42, 43, 46, 16, 49, 36,  8, 37, 53,
       17, 21,  4, 47]), 'cur_cost': 107224.0}]
2025-06-26 18:48:46,344 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:48:46,344 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 271, 'skip_rate': 0.03690036900369004, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 261, 'cache_hits': 176, 'similarity_calculations': 4010, 'cache_hit_rate': 0.04389027431421447, 'cache_size': 3834}}
2025-06-26 18:48:46,344 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:48:46,344 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 20991.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 20485.0}}]
2025-06-26 18:48:46,344 - __main__ - INFO - 进化阶段完成
2025-06-26 18:48:46,344 - __main__ - INFO - 开始评估阶段
2025-06-26 18:48:46,344 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:48:46,344 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:48:46,344 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 14860.0 → 14860.0 (Δ 0.0)
- Mean cost: 62515.1 → 41134.6
- Diversity: 0.572 → 0.483 (Δ -0.0889)

#### Elite metrics
- Elite count: 11 → 14
- Best elite cost: 10442 → 10442 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 71.4% , avg Δ 38245.57
- Exploit: 3 inds, success 66.7% , avg Δ -17971.33

#### Other indicators
- No-change individuals: 1
- Regression individuals: 2
- Historical trends: Last 3 iterations: costs [8076.0, 49879.0, 14860.0], diversity [0.6319444444444444, 0.6398148148148148, 0.7684848484848484]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:48:46,344 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:48:58,763 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 71.4,
      "average_improvement": 38245.57,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": -17971.33,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": "explore-heavy with potential over-exploitation",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "increase slightly",
    "elite_preservation": "maintain current count"
  }
}
```
2025-06-26 18:48:58,780 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:48:58,780 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 71.4,
      "average_improvement": 38245.57,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": -17971.33,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": "explore-heavy with potential over-exploitation",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "increase slightly",
    "elite_preservation": "maintain current count"
  }
}
```
2025-06-26 18:48:58,780 - __main__ - INFO - 评估阶段完成
2025-06-26 18:48:58,783 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 71.4,
      "average_improvement": 38245.57,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": -17971.33,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": "explore-heavy with potential over-exploitation",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "increase slightly",
    "elite_preservation": "maintain current count"
  }
}
```
2025-06-26 18:48:58,783 - __main__ - INFO - 当前最佳适应度: 14860.0
2025-06-26 18:48:58,783 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite10_55_route_1.pkl
2025-06-26 18:48:58,783 - __main__ - INFO - composite10_55 开始进化第 3 代
2025-06-26 18:48:58,783 - __main__ - INFO - 开始分析阶段
2025-06-26 18:48:58,783 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:48:58,797 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 14860.0, 'max': 107224.0, 'mean': 41134.6, 'std': 37796.82593340345}, 'diversity': 0.6517171717171717, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:48:58,798 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 14860.0, 'max': 107224.0, 'mean': 41134.6, 'std': 37796.82593340345}, 'diversity_level': 0.6517171717171717, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'coordinates': [[819, 516], [794, 496], [762, 445], [746, 472], [791, 512], [749, 494], [767, 510], [767, 471], [789, 469], [766, 485], [838, 470], [455, 3402], [394, 3338], [481, 3377], [454, 3375], [412, 3387], [431, 3380], [473, 3354], [418, 3361], [444, 3339], [393, 3380], [422, 3324], [1942, 1370], [1925, 1357], [1877, 1382], [1902, 1350], [1872, 1350], [1963, 1385], [1912, 1401], [1951, 1341], [1871, 1399], [1890, 1327], [1935, 1392], [2950, 892], [3005, 873], [2949, 919], [2966, 905], [2983, 890], [2980, 914], [2997, 855], [3018, 887], [3003, 907], [2971, 883], [2959, 855], [1636, 3351], [1588, 3390], [1672, 3368], [1599, 3369], [1618, 3412], [1677, 3356], [1625, 3388], [1585, 3375], [1611, 3388], [1614, 3342], [1621, 3360]], 'distance_matrix': array([[   0.,   32.,   91., ..., 2979., 2936., 2955.],
       [  32.,    0.,   60., ..., 3005., 2962., 2981.],
       [  91.,   60.,    0., ..., 3063., 3020., 3039.],
       ...,
       [2979., 3005., 3063., ...,    0.,   46.,   30.],
       [2936., 2962., 3020., ...,   46.,    0.,   19.],
       [2955., 2981., 3039., ...,   30.,   19.,    0.]])}
2025-06-26 18:48:58,798 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:48:58,799 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:48:58,799 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:48:58,800 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:48:58,800 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:48:58,801 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:48:58,801 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:48:58,801 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:48:58,801 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:48:58,804 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:48:58,805 - __main__ - INFO - 精英专家分析报告: {'elite_count': 14, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 4418.0, 'avg_gap': 30684.742857142854}, 'structure_gap': {'unique_elite_edges': 120, 'unique_pop_edges': 238, 'common_edges': 41}}, 'elite_diversity': {'diversity_score': 0.1916083916083916}}
2025-06-26 18:48:58,805 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:48:58,805 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:48:58,807 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:48:58,808 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 14860.0, mean 41134.6, max 107224.0, std 37796.82593340345
- diversity: 0.6517171717171717
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [49879.0, 14860.0, 14860.0] (Δ 17509.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [393.0, 445.0, 3018.0, 3412.0]
- centroid/spread: [1546.036, 1896.182] | rms 1526.132
- density_grid(3x3): [[11, 11, 11], [0, 0, 0], [11, 11, 0]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 1167.0, 2061.0, 2863.0, 3627.0]
- long_edge_ratio: 0.248
- direction_hist(8 sectors): [7, 3, 8, 11, 5, 4, 5, 11]
- nn_median_dist: 20.616

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:48:58,808 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:49:21,871 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 13.0 to 3627.0, with significant standard deviation)",
    "modality": "Potentially multimodal (presence of both short and long edges, with no clear dominant direction in direction_hist)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.248 suggests some areas may mislead towards suboptimal paths)"
  },
  "population_state": {
    "coverage": "Partial (density_grid shows clusters in top-left and bottom-left cells, but empty middle row and missing bottom-right)",
    "convergence": "Early stage (diversity 0.65, no elite solutions yet, but recent best costs show improvement)"
  },
  "difficult_regions": [
    {"type": "long-edge corridor", "evidence": "edge_len_stats q3=2863.0, max=3627.0"},
    {"type": "low-density middle", "evidence": "density_grid shows empty middle row"}
  ],
  "opportunity_regions": [
    {"type": "high-density clusters", "evidence": "density_grid top-left and bottom-left cells (11 nodes each)"},
    {"type": "short-edge zones", "evidence": "edge_len_stats min=13.0, q1=1167.0"}
  ],
  "evolution_phase": "Exploration (iteration 2/5, no elites, improving but diverse population)",
  "evolution_direction": [
    "Prioritize exploitation in high-density cells with local search operators",
    "Use edge-recombination crossover to preserve promising short-edge sequences",
    "Add directed mutation towards sparse regions (middle row)",
    "Maintain diversity through restart mechanisms if stagnation occurs"
  ]
}
```
2025-06-26 18:49:21,871 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:49:21,871 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 13.0 to 3627.0, with significant standard deviation)', 'modality': 'Potentially multimodal (presence of both short and long edges, with no clear dominant direction in direction_hist)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.248 suggests some areas may mislead towards suboptimal paths)'}, 'population_state': {'coverage': 'Partial (density_grid shows clusters in top-left and bottom-left cells, but empty middle row and missing bottom-right)', 'convergence': 'Early stage (diversity 0.65, no elite solutions yet, but recent best costs show improvement)'}, 'difficult_regions': [{'type': 'long-edge corridor', 'evidence': 'edge_len_stats q3=2863.0, max=3627.0'}, {'type': 'low-density middle', 'evidence': 'density_grid shows empty middle row'}], 'opportunity_regions': [{'type': 'high-density clusters', 'evidence': 'density_grid top-left and bottom-left cells (11 nodes each)'}, {'type': 'short-edge zones', 'evidence': 'edge_len_stats min=13.0, q1=1167.0'}], 'evolution_phase': 'Exploration (iteration 2/5, no elites, improving but diverse population)', 'evolution_direction': ['Prioritize exploitation in high-density cells with local search operators', 'Use edge-recombination crossover to preserve promising short-edge sequences', 'Add directed mutation towards sparse regions (middle row)', 'Maintain diversity through restart mechanisms if stagnation occurs']}
2025-06-26 18:49:21,871 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:49:21,875 - __main__ - INFO - 分析阶段完成
2025-06-26 18:49:21,875 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 13.0 to 3627.0, with significant standard deviation)', 'modality': 'Potentially multimodal (presence of both short and long edges, with no clear dominant direction in direction_hist)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.248 suggests some areas may mislead towards suboptimal paths)'}, 'population_state': {'coverage': 'Partial (density_grid shows clusters in top-left and bottom-left cells, but empty middle row and missing bottom-right)', 'convergence': 'Early stage (diversity 0.65, no elite solutions yet, but recent best costs show improvement)'}, 'difficult_regions': [{'type': 'long-edge corridor', 'evidence': 'edge_len_stats q3=2863.0, max=3627.0'}, {'type': 'low-density middle', 'evidence': 'density_grid shows empty middle row'}], 'opportunity_regions': [{'type': 'high-density clusters', 'evidence': 'density_grid top-left and bottom-left cells (11 nodes each)'}, {'type': 'short-edge zones', 'evidence': 'edge_len_stats min=13.0, q1=1167.0'}], 'evolution_phase': 'Exploration (iteration 2/5, no elites, improving but diverse population)', 'evolution_direction': ['Prioritize exploitation in high-density cells with local search operators', 'Use edge-recombination crossover to preserve promising short-edge sequences', 'Add directed mutation towards sparse regions (middle row)', 'Maintain diversity through restart mechanisms if stagnation occurs']}
2025-06-26 18:49:21,875 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:49:21,875 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:49:21,876 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 14860.0
  • mean_cost: 41134.6
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 13.0 to 3627.0, with significant standard deviation), modality Potentially multimodal (presence of both short and long edges, with no clear dominant direction in direction_hist), deceptiveness Possible deceptive regions (long_edge_ratio of 0.248 suggests some areas may mislead towards suboptimal paths)
- Evolution phase: Exploration (iteration 2/5, no elites, improving but diverse population)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'long-edge , {'type': 'low-densit
- Opportunity regions (# 2): {'type': 'high-densi, {'type': 'short-edge
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:49:21,876 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:49:21,876 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:49:31,371 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and landscape focus suggestion 'balance' favor exploration. Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 18:49:31,371 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:49:31,371 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:49:31,371 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and landscape focus suggestion 'balance' favor exploration. Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 18:49:31,371 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:49:31,371 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:49:31,374 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and landscape focus suggestion 'balance' favor exploration. Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 18:49:31,374 - __main__ - INFO - 开始进化阶段
2025-06-26 18:49:31,374 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:49:31,374 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:49:31,374 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.48333333333333334
- Evolution phase: Exploration (iteration 2/5, no elites, improving but diverse population)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:49:31,374 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:49:31,374 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:49:43,923 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high-opportunity and sparse cells",
  "strategy_comment": "Sequential sweep through nodes, prioritizing opportunity regions"
}
```
2025-06-26 18:49:43,923 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:49:43,923 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:49:43,923 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:49:43,923 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:49:43,923 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:49:43,923 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.48333333333333334
- Evolution phase: Exploration (iteration 2/5, no elites, improving but diverse population)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:49:43,929 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:49:43,929 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:49:56,356 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high-opportunity, low-density adjacent",
  "strategy_comment": "Prioritize sparse cells near high-opportunity regions"
}
```
2025-06-26 18:49:56,356 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:49:56,358 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:49:56,358 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:49:56,358 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:49:56,358 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:49:56,358 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.48333333333333334
- Evolution phase: Exploration (iteration 2/5, no elites, improving but diverse population)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:49:56,358 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:49:56,358 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:50:10,560 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high-opportunity, low-density adjacent",
  "strategy_comment": "Prioritize sparse cells near high-value regions while avoiding long-low areas"
}
```
2025-06-26 18:50:10,560 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:50:10,567 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:50:10,567 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:50:10,567 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:50:10,567 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:50:10,568 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 20991.0 (rank 60.0%)
- Population diversity: 0.48333333333333334
- Evolution phase: Exploration (iteration 2/5, no elites, improving but diverse population)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:50:10,569 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:50:10,569 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:50:22,912 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53],
  "targeted_regions": "low-density and high-opportunity",
  "strategy_comment": "Alternate even-odd nodes to explore sparse regions"
}
```
2025-06-26 18:50:22,912 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:50:22,912 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26857.0, 路径: [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53]
2025-06-26 18:50:22,912 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}
2025-06-26 18:50:22,915 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:50:22,915 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:50:22,915 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.48703703703703705
- Evolution phase: Exploration (iteration 2/5, no elites, improving but diverse population)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:50:22,915 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:50:22,915 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:50:35,622 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 16, 22, 28, 34, 40, 46, 51, 47, 41, 35, 29, 23, 17, 52, 53],
  "targeted_regions": "high-opportunity and adjacent low-density cells",
  "strategy_comment": "Spiral pattern covering sparse edges, avoiding long-low regions"
}
```
2025-06-26 18:50:35,626 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:50:35,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 58906.0, 路径: [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 16, 22, 28, 34, 40, 46, 51, 47, 41, 35, 29, 23, 17, 52, 53]
2025-06-26 18:50:35,626 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 16, 22, 28, 34, 40, 46, 51, 47, 41, 35, 29, 23, 17, 52, 53], 'cur_cost': 58906.0}
2025-06-26 18:50:35,626 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:50:35,626 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:50:35,629 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.5685185185185185
- Evolution phase: Exploration (iteration 2/5, no elites, improving but diverse population)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:50:35,629 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:50:35,629 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:50:47,091 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high-opportunity, low-density",
  "strategy_comment": "Sequential exploration of adjacent nodes"
}
```
2025-06-26 18:50:47,091 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:50:47,092 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:50:47,092 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:50:47,092 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 18:50:47,092 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:50:47,092 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:50:47,093 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 111169.0
2025-06-26 18:50:47,595 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:50:47,595 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509]
2025-06-26 18:50:47,595 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64)]
2025-06-26 18:50:47,599 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:50:47,599 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}, {'tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 16, 22, 28, 34, 40, 46, 51, 47, 41, 35, 29, 23, 17, 52, 53], 'cur_cost': 58906.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': array([17,  1, 50, 41, 25, 36, 47, 53,  8, 32,  0,  5, 52,  2,  4, 13, 15,
       42, 19, 48, 20, 28, 29, 12, 43,  3, 31, 33,  9, 46, 16, 39, 27, 21,
       35, 23, 40, 45, 51, 22,  6, 11, 37, 10, 14, 30, 26, 49, 38, 44, 18,
       34,  7, 24, 54]), 'cur_cost': 111169.0}, {'tour': array([45, 51, 38, 13, 19, 40,  8,  0, 23, 35, 44, 37, 33, 30, 53, 15, 10,
       31,  6, 42,  2,  5, 39, 50, 12, 18,  3,  4, 32, 27, 11, 25, 52, 26,
       36,  7,  1, 28, 43, 20, 54, 34, 17, 24, 29, 47,  9, 14, 49, 41, 48,
       16, 21, 46, 22]), 'cur_cost': 96186.0}, {'tour': array([19,  1,  6, 17, 42, 26, 14, 12, 21, 33, 45, 54, 40, 37, 51,  4, 22,
       34, 48, 35, 10, 39, 38,  7, 20, 18, 52,  9, 24, 31, 25,  3, 32, 15,
        2, 29, 43,  8, 44, 50, 53,  5, 28, 46, 30, 11, 16, 49, 27, 41,  0,
       23, 36, 47, 13]), 'cur_cost': 92160.0}, {'tour': array([25, 28,  3, 18, 27, 13, 41, 10,  6,  0, 11,  5, 19, 31,  9, 38, 24,
       15, 54, 30, 22, 48, 32,  2, 20, 45, 50, 34, 29, 12, 33, 14, 35,  1,
       39,  7, 23, 26, 44, 40, 52, 51, 42, 43, 46, 16, 49, 36,  8, 37, 53,
       17, 21,  4, 47]), 'cur_cost': 107224.0}]
2025-06-26 18:50:47,601 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:50:47,601 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 272, 'skip_rate': 0.03676470588235294, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 262, 'cache_hits': 176, 'similarity_calculations': 4015, 'cache_hit_rate': 0.043835616438356165, 'cache_size': 3839}}
2025-06-26 18:50:47,602 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 18:50:47,602 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:50:47,602 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:50:47,602 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:50:47,602 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 102070.0
2025-06-26 18:50:48,105 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:50:48,105 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509]
2025-06-26 18:50:48,105 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64)]
2025-06-26 18:50:48,110 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:50:48,110 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}, {'tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 16, 22, 28, 34, 40, 46, 51, 47, 41, 35, 29, 23, 17, 52, 53], 'cur_cost': 58906.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': array([17,  1, 50, 41, 25, 36, 47, 53,  8, 32,  0,  5, 52,  2,  4, 13, 15,
       42, 19, 48, 20, 28, 29, 12, 43,  3, 31, 33,  9, 46, 16, 39, 27, 21,
       35, 23, 40, 45, 51, 22,  6, 11, 37, 10, 14, 30, 26, 49, 38, 44, 18,
       34,  7, 24, 54]), 'cur_cost': 111169.0}, {'tour': array([18, 45,  0, 50, 53,  5, 34, 14, 51, 16, 25, 36, 28,  4, 33, 21, 27,
       13, 41,  6, 48,  2,  8, 23, 32, 26, 29,  3, 10, 37, 12, 40,  7, 49,
       15, 20, 42, 54, 35, 39, 30, 31, 47, 17, 22, 43, 24, 44, 19,  9, 38,
        1, 52, 11, 46]), 'cur_cost': 102070.0}, {'tour': array([19,  1,  6, 17, 42, 26, 14, 12, 21, 33, 45, 54, 40, 37, 51,  4, 22,
       34, 48, 35, 10, 39, 38,  7, 20, 18, 52,  9, 24, 31, 25,  3, 32, 15,
        2, 29, 43,  8, 44, 50, 53,  5, 28, 46, 30, 11, 16, 49, 27, 41,  0,
       23, 36, 47, 13]), 'cur_cost': 92160.0}, {'tour': array([25, 28,  3, 18, 27, 13, 41, 10,  6,  0, 11,  5, 19, 31,  9, 38, 24,
       15, 54, 30, 22, 48, 32,  2, 20, 45, 50, 34, 29, 12, 33, 14, 35,  1,
       39,  7, 23, 26, 44, 40, 52, 51, 42, 43, 46, 16, 49, 36,  8, 37, 53,
       17, 21,  4, 47]), 'cur_cost': 107224.0}]
2025-06-26 18:50:48,111 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:50:48,111 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 273, 'skip_rate': 0.03663003663003663, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 263, 'cache_hits': 176, 'similarity_calculations': 4021, 'cache_hit_rate': 0.04377020641631435, 'cache_size': 3845}}
2025-06-26 18:50:48,111 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:50:48,113 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:50:48,113 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:50:48,113 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:50:48,113 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 102169.0
2025-06-26 18:50:48,614 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:50:48,615 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509]
2025-06-26 18:50:48,615 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64)]
2025-06-26 18:50:48,620 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:50:48,620 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}, {'tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 16, 22, 28, 34, 40, 46, 51, 47, 41, 35, 29, 23, 17, 52, 53], 'cur_cost': 58906.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': array([17,  1, 50, 41, 25, 36, 47, 53,  8, 32,  0,  5, 52,  2,  4, 13, 15,
       42, 19, 48, 20, 28, 29, 12, 43,  3, 31, 33,  9, 46, 16, 39, 27, 21,
       35, 23, 40, 45, 51, 22,  6, 11, 37, 10, 14, 30, 26, 49, 38, 44, 18,
       34,  7, 24, 54]), 'cur_cost': 111169.0}, {'tour': array([18, 45,  0, 50, 53,  5, 34, 14, 51, 16, 25, 36, 28,  4, 33, 21, 27,
       13, 41,  6, 48,  2,  8, 23, 32, 26, 29,  3, 10, 37, 12, 40,  7, 49,
       15, 20, 42, 54, 35, 39, 30, 31, 47, 17, 22, 43, 24, 44, 19,  9, 38,
        1, 52, 11, 46]), 'cur_cost': 102070.0}, {'tour': array([35, 23, 11, 13,  3, 27, 34, 36, 33, 54, 51,  0, 53,  1, 18,  8, 38,
       48, 30, 49, 17, 45, 44, 20, 25, 14,  9, 43, 21, 47,  6, 39, 50, 32,
       16, 12, 52, 28,  2, 29, 19, 42, 41,  7, 37, 10, 22,  4, 24, 31, 40,
       15, 46, 26,  5]), 'cur_cost': 102169.0}, {'tour': array([25, 28,  3, 18, 27, 13, 41, 10,  6,  0, 11,  5, 19, 31,  9, 38, 24,
       15, 54, 30, 22, 48, 32,  2, 20, 45, 50, 34, 29, 12, 33, 14, 35,  1,
       39,  7, 23, 26, 44, 40, 52, 51, 42, 43, 46, 16, 49, 36,  8, 37, 53,
       17, 21,  4, 47]), 'cur_cost': 107224.0}]
2025-06-26 18:50:48,621 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:50:48,621 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 274, 'skip_rate': 0.0364963503649635, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 264, 'cache_hits': 176, 'similarity_calculations': 4028, 'cache_hit_rate': 0.04369414101290963, 'cache_size': 3852}}
2025-06-26 18:50:48,621 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:50:48,623 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:50:48,623 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:50:48,623 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:50:48,623 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 104577.0
2025-06-26 18:50:49,126 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:50:49,126 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509]
2025-06-26 18:50:49,126 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64)]
2025-06-26 18:50:49,130 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:50:49,130 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}, {'tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 16, 22, 28, 34, 40, 46, 51, 47, 41, 35, 29, 23, 17, 52, 53], 'cur_cost': 58906.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': array([17,  1, 50, 41, 25, 36, 47, 53,  8, 32,  0,  5, 52,  2,  4, 13, 15,
       42, 19, 48, 20, 28, 29, 12, 43,  3, 31, 33,  9, 46, 16, 39, 27, 21,
       35, 23, 40, 45, 51, 22,  6, 11, 37, 10, 14, 30, 26, 49, 38, 44, 18,
       34,  7, 24, 54]), 'cur_cost': 111169.0}, {'tour': array([18, 45,  0, 50, 53,  5, 34, 14, 51, 16, 25, 36, 28,  4, 33, 21, 27,
       13, 41,  6, 48,  2,  8, 23, 32, 26, 29,  3, 10, 37, 12, 40,  7, 49,
       15, 20, 42, 54, 35, 39, 30, 31, 47, 17, 22, 43, 24, 44, 19,  9, 38,
        1, 52, 11, 46]), 'cur_cost': 102070.0}, {'tour': array([35, 23, 11, 13,  3, 27, 34, 36, 33, 54, 51,  0, 53,  1, 18,  8, 38,
       48, 30, 49, 17, 45, 44, 20, 25, 14,  9, 43, 21, 47,  6, 39, 50, 32,
       16, 12, 52, 28,  2, 29, 19, 42, 41,  7, 37, 10, 22,  4, 24, 31, 40,
       15, 46, 26,  5]), 'cur_cost': 102169.0}, {'tour': array([28, 16, 49, 53, 19, 18, 26, 35, 39, 13,  2, 43, 25, 27, 10, 46, 15,
        1,  4, 31, 14, 36, 50,  8,  5, 23, 38,  7, 54, 41, 48, 42,  9, 17,
       52, 32, 29, 45, 30, 24,  6, 22,  0, 21, 51, 37, 44, 34, 12, 11, 47,
       40,  3, 33, 20]), 'cur_cost': 104577.0}]
2025-06-26 18:50:49,132 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:50:49,132 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 275, 'skip_rate': 0.03636363636363636, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 265, 'cache_hits': 176, 'similarity_calculations': 4036, 'cache_hit_rate': 0.04360753221010902, 'cache_size': 3860}}
2025-06-26 18:50:49,132 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:50:49,132 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 12, 18, 24, 30, 36, 42, 48, 54, 49, 43, 37, 31, 25, 19, 13, 8, 4, 1, 5, 9, 14, 20, 26, 32, 38, 44, 50, 45, 39, 33, 27, 21, 15, 10, 6, 2, 11, 16, 22, 28, 34, 40, 46, 51, 47, 41, 35, 29, 23, 17, 52, 53], 'cur_cost': 58906.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}]
2025-06-26 18:50:49,132 - __main__ - INFO - 进化阶段完成
2025-06-26 18:50:49,132 - __main__ - INFO - 开始评估阶段
2025-06-26 18:50:49,134 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:50:49,134 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:50:49,134 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 14860.0 → 14860.0 (Δ 0.0)
- Mean cost: 41134.6 → 56518.8
- Diversity: 0.483 → 0.644 (Δ 0.1611)

#### Elite metrics
- Elite count: 14 → 14
- Best elite cost: 10442 → 10442 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 0.0% , avg Δ -8318.67
- Exploit: 4 inds, success 25.0% , avg Δ -25982.5

#### Other indicators
- No-change individuals: 4
- Regression individuals: 5
- Historical trends: Last 3 iterations: costs [49879.0, 14860.0, 14860.0], diversity [0.6398148148148148, 0.7684848484848484, 0.6517171717171717]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:50:49,134 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:50:59,933 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success rate, negative avg delta)",
    "exploit": "moderately effective (25% success rate, significant negative avg delta)"
  },
  "balance_state": "exploit-heavy but explore needs refinement",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_actions": [
      "introduce new exploration operators",
      "enforce stricter elitism to prevent regression"
    ]
  }
}
```
2025-06-26 18:50:59,955 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:50:59,956 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success rate, negative avg delta)",
    "exploit": "moderately effective (25% success rate, significant negative avg delta)"
  },
  "balance_state": "exploit-heavy but explore needs refinement",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_actions": [
      "introduce new exploration operators",
      "enforce stricter elitism to prevent regression"
    ]
  }
}
```
2025-06-26 18:50:59,956 - __main__ - INFO - 评估阶段完成
2025-06-26 18:50:59,956 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success rate, negative avg delta)",
    "exploit": "moderately effective (25% success rate, significant negative avg delta)"
  },
  "balance_state": "exploit-heavy but explore needs refinement",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_actions": [
      "introduce new exploration operators",
      "enforce stricter elitism to prevent regression"
    ]
  }
}
```
2025-06-26 18:50:59,956 - __main__ - INFO - 当前最佳适应度: 14860.0
2025-06-26 18:50:59,959 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite10_55_route_2.pkl
2025-06-26 18:50:59,960 - __main__ - INFO - composite10_55 开始进化第 4 代
2025-06-26 18:50:59,960 - __main__ - INFO - 开始分析阶段
2025-06-26 18:50:59,960 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:50:59,969 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 14860.0, 'max': 111169.0, 'mean': 56518.8, 'std': 41572.1434395678}, 'diversity': 0.8436363636363637, 'clusters': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:50:59,969 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 14860.0, 'max': 111169.0, 'mean': 56518.8, 'std': 41572.1434395678}, 'diversity_level': 0.8436363636363637, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[819, 516], [794, 496], [762, 445], [746, 472], [791, 512], [749, 494], [767, 510], [767, 471], [789, 469], [766, 485], [838, 470], [455, 3402], [394, 3338], [481, 3377], [454, 3375], [412, 3387], [431, 3380], [473, 3354], [418, 3361], [444, 3339], [393, 3380], [422, 3324], [1942, 1370], [1925, 1357], [1877, 1382], [1902, 1350], [1872, 1350], [1963, 1385], [1912, 1401], [1951, 1341], [1871, 1399], [1890, 1327], [1935, 1392], [2950, 892], [3005, 873], [2949, 919], [2966, 905], [2983, 890], [2980, 914], [2997, 855], [3018, 887], [3003, 907], [2971, 883], [2959, 855], [1636, 3351], [1588, 3390], [1672, 3368], [1599, 3369], [1618, 3412], [1677, 3356], [1625, 3388], [1585, 3375], [1611, 3388], [1614, 3342], [1621, 3360]], 'distance_matrix': array([[   0.,   32.,   91., ..., 2979., 2936., 2955.],
       [  32.,    0.,   60., ..., 3005., 2962., 2981.],
       [  91.,   60.,    0., ..., 3063., 3020., 3039.],
       ...,
       [2979., 3005., 3063., ...,    0.,   46.,   30.],
       [2936., 2962., 3020., ...,   46.,    0.,   19.],
       [2955., 2981., 3039., ...,   30.,   19.,    0.]])}
2025-06-26 18:50:59,969 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:50:59,969 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:50:59,969 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:50:59,976 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:50:59,976 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:50:59,976 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:50:59,977 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:50:59,977 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:50:59,977 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:50:59,979 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:50:59,979 - __main__ - INFO - 精英专家分析报告: {'elite_count': 14, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 4418.0, 'avg_gap': 46068.94285714286}, 'structure_gap': {'unique_elite_edges': 112, 'unique_pop_edges': 318, 'common_edges': 49}}, 'elite_diversity': {'diversity_score': 0.1916083916083916}}
2025-06-26 18:50:59,979 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:50:59,981 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:50:59,982 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:50:59,982 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 14860.0, mean 56518.8, max 111169.0, std 41572.1434395678
- diversity: 0.8436363636363637
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [14860.0, 14860.0, 14860.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [393.0, 445.0, 3018.0, 3412.0]
- centroid/spread: [1546.036, 1896.182] | rms 1526.132
- density_grid(3x3): [[11, 11, 11], [0, 0, 0], [11, 11, 0]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 1167.0, 2061.0, 2863.0, 3627.0]
- long_edge_ratio: 0.248
- direction_hist(8 sectors): [7, 3, 8, 11, 5, 4, 5, 11]
- nn_median_dist: 20.616

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:50:59,983 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:51:28,245 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 13.0 to 3627.0, std 41572.14)",
    "modality": "Potentially multimodal (direction_hist shows varied edge directions, no clear single peak)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio 0.248 suggests some long edges may mislead search)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (diversity 0.84, density_grid shows active cells)",
    "convergence": "Early stage (convergence 0.0, recent stagnation detected)",
    "elite_influence": "Minimal (elite_count 0, no common edges detected)"
  },
  "difficult_regions": [
    {
      "type": "long-edge corridor",
      "evidence": "edge_len_stats q3=2863.0, max=3627.0",
      "nodes": "Nodes connected by edges in top 25% length range"
    },
    {
      "type": "low-density zone",
      "evidence": "density_grid shows empty middle row",
      "nodes": "Nodes in grid coordinates (x,y) where y≈1896.182±spread"
    }
  ],
  "opportunity_regions": [
    {
      "type": "high-density cluster",
      "evidence": "density_grid top-left 3 cells show 11 nodes each",
      "nodes": "Nodes in bounding_box [393,445]×[3018,3412]"
    },
    {
      "type": "short-edge neighborhood",
      "evidence": "nn_median_dist 20.616, edge_len_stats min=13.0",
      "nodes": "Nodes with nearest-neighbor distances <21 units"
    }
  ],
  "evolution_phase": "Early exploration (iter 3/5, population still diverse)",
  "evolution_direction": [
    "Intensify local search in high-density clusters (2-opt around centroid [1546,1896])",
    "Path relinking between long-edge corridors and short-edge neighborhoods",
    "Adaptive mutation: higher probability for long edges (target difficult regions)",
    "Diversity preservation: enforce 30% solutions must visit low-density zones"
  ]
}
```
2025-06-26 18:51:28,246 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:51:28,246 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 3627.0, std 41572.14)', 'modality': 'Potentially multimodal (direction_hist shows varied edge directions, no clear single peak)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio 0.248 suggests some long edges may mislead search)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.84, density_grid shows active cells)', 'convergence': 'Early stage (convergence 0.0, recent stagnation detected)', 'elite_influence': 'Minimal (elite_count 0, no common edges detected)'}, 'difficult_regions': [{'type': 'long-edge corridor', 'evidence': 'edge_len_stats q3=2863.0, max=3627.0', 'nodes': 'Nodes connected by edges in top 25% length range'}, {'type': 'low-density zone', 'evidence': 'density_grid shows empty middle row', 'nodes': 'Nodes in grid coordinates (x,y) where y≈1896.182±spread'}], 'opportunity_regions': [{'type': 'high-density cluster', 'evidence': 'density_grid top-left 3 cells show 11 nodes each', 'nodes': 'Nodes in bounding_box [393,445]×[3018,3412]'}, {'type': 'short-edge neighborhood', 'evidence': 'nn_median_dist 20.616, edge_len_stats min=13.0', 'nodes': 'Nodes with nearest-neighbor distances <21 units'}], 'evolution_phase': 'Early exploration (iter 3/5, population still diverse)', 'evolution_direction': ['Intensify local search in high-density clusters (2-opt around centroid [1546,1896])', 'Path relinking between long-edge corridors and short-edge neighborhoods', 'Adaptive mutation: higher probability for long edges (target difficult regions)', 'Diversity preservation: enforce 30% solutions must visit low-density zones']}
2025-06-26 18:51:28,246 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:51:28,246 - __main__ - INFO - 分析阶段完成
2025-06-26 18:51:28,246 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 3627.0, std 41572.14)', 'modality': 'Potentially multimodal (direction_hist shows varied edge directions, no clear single peak)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio 0.248 suggests some long edges may mislead search)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.84, density_grid shows active cells)', 'convergence': 'Early stage (convergence 0.0, recent stagnation detected)', 'elite_influence': 'Minimal (elite_count 0, no common edges detected)'}, 'difficult_regions': [{'type': 'long-edge corridor', 'evidence': 'edge_len_stats q3=2863.0, max=3627.0', 'nodes': 'Nodes connected by edges in top 25% length range'}, {'type': 'low-density zone', 'evidence': 'density_grid shows empty middle row', 'nodes': 'Nodes in grid coordinates (x,y) where y≈1896.182±spread'}], 'opportunity_regions': [{'type': 'high-density cluster', 'evidence': 'density_grid top-left 3 cells show 11 nodes each', 'nodes': 'Nodes in bounding_box [393,445]×[3018,3412]'}, {'type': 'short-edge neighborhood', 'evidence': 'nn_median_dist 20.616, edge_len_stats min=13.0', 'nodes': 'Nodes with nearest-neighbor distances <21 units'}], 'evolution_phase': 'Early exploration (iter 3/5, population still diverse)', 'evolution_direction': ['Intensify local search in high-density clusters (2-opt around centroid [1546,1896])', 'Path relinking between long-edge corridors and short-edge neighborhoods', 'Adaptive mutation: higher probability for long edges (target difficult regions)', 'Diversity preservation: enforce 30% solutions must visit low-density zones']}
2025-06-26 18:51:28,246 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:51:28,248 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:51:28,248 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 14860.0
  • mean_cost: 56518.8
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 13.0 to 3627.0, std 41572.14), modality Potentially multimodal (direction_hist shows varied edge directions, no clear single peak), deceptiveness Possible deceptive regions (long_edge_ratio 0.248 suggests some long edges may mislead search)
- Evolution phase: Early exploration (iter 3/5, population still diverse)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'long-edge , {'type': 'low-densit
- Opportunity regions (# 2): {'type': 'high-densi, {'type': 'short-edge
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineff...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:51:28,248 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:51:28,248 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:51:38,224 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 18:51:38,224 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:51:38,224 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:51:38,224 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 18:51:38,224 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:51:38,224 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:51:38,224 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 18:51:38,231 - __main__ - INFO - 开始进化阶段
2025-06-26 18:51:38,231 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:51:38,231 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:51:38,232 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.6444444444444445
- Evolution phase: Early exploration (iter 3/5, population still diverse)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:51:38,232 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:51:38,232 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:51:51,301 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 12, 18, 22, 26, 30, 34, 38, 42, 45, 48, 51, 54, 50, 47, 44, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 4, 8, 11, 15, 19, 23, 27, 31, 35, 39, 43, 46, 49, 52, 53, 40, 36, 32, 28, 24, 20, 16, 14, 10, 6, 2],
  "targeted_regions": "high-density and short-distance",
  "strategy_comment": "Leverage adjacent low-density cells near opportunity regions"
}
```
2025-06-26 18:51:51,309 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:51:51,310 - ExplorationExpert - INFO - 探索路径生成完成，成本: 39346.0, 路径: [0, 3, 7, 12, 18, 22, 26, 30, 34, 38, 42, 45, 48, 51, 54, 50, 47, 44, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 4, 8, 11, 15, 19, 23, 27, 31, 35, 39, 43, 46, 49, 52, 53, 40, 36, 32, 28, 24, 20, 16, 14, 10, 6, 2]
2025-06-26 18:51:51,310 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 7, 12, 18, 22, 26, 30, 34, 38, 42, 45, 48, 51, 54, 50, 47, 44, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 4, 8, 11, 15, 19, 23, 27, 31, 35, 39, 43, 46, 49, 52, 53, 40, 36, 32, 28, 24, 20, 16, 14, 10, 6, 2], 'cur_cost': 39346.0}
2025-06-26 18:51:51,310 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:51:51,310 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:51:51,310 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.7148148148148148
- Evolution phase: Early exploration (iter 3/5, population still diverse)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:51:51,310 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:51:51,310 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:52:04,187 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 13, 9, 5, 1, 4, 8, 14, 19, 24, 28, 32, 36, 40, 44, 48, 52, 49, 45, 41, 37, 33, 29, 25, 20, 16, 11, 6, 2, 10, 15, 23, 53],
  "targeted_regions": "high-density and short-distance opportunity regions",
  "strategy_comment": "Balanced exploration of high-density and sparse cells"
}
```
2025-06-26 18:52:04,187 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:52:04,187 - ExplorationExpert - INFO - 探索路径生成完成，成本: 49515.0, 路径: [0, 3, 7, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 13, 9, 5, 1, 4, 8, 14, 19, 24, 28, 32, 36, 40, 44, 48, 52, 49, 45, 41, 37, 33, 29, 25, 20, 16, 11, 6, 2, 10, 15, 23, 53]
2025-06-26 18:52:04,187 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 7, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 13, 9, 5, 1, 4, 8, 14, 19, 24, 28, 32, 36, 40, 44, 48, 52, 49, 45, 41, 37, 33, 29, 25, 20, 16, 11, 6, 2, 10, 15, 23, 53], 'cur_cost': 49515.0}
2025-06-26 18:52:04,187 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:52:04,190 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:52:04,190 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.7444444444444445
- Evolution phase: Early exploration (iter 3/5, population still diverse)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:52:04,190 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:52:04,191 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:52:16,998 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high-opportunity, low-density",
  "strategy_comment": "Prioritize unexplored low-density cells near opportunities"
}
```
2025-06-26 18:52:17,006 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:52:17,006 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:52:17,006 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:52:17,006 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:52:17,006 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:52:17,006 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26857.0 (rank 20.0%)
- Population diversity: 0.7444444444444445
- Evolution phase: Early exploration (iter 3/5, population still diverse)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:52:17,006 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:52:17,006 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:52:30,818 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high-opportunity and adjacent low-density",
  "strategy_comment": "Leverage clustered jumps in high-opportunity zones, then fill sparse edges"
}
```
2025-06-26 18:52:30,822 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:52:30,822 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105578.0, 路径: [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:52:30,822 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}
2025-06-26 18:52:30,822 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:52:30,822 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:52:30,822 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 58906.0 (rank 40.0%)
- Population diversity: 0.7333333333333333
- Evolution phase: Early exploration (iter 3/5, population still diverse)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:52:30,822 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:52:30,822 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:52:42,978 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53],
  "targeted_regions": "low-density adjacent to opportunity",
  "strategy_comment": "Alternate high/low nodes to explore sparse regions"
}
```
2025-06-26 18:52:42,978 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:52:42,978 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26857.0, 路径: [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53]
2025-06-26 18:52:42,978 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}
2025-06-26 18:52:42,978 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:52:42,978 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:52:42,978 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.7592592592592593
- Evolution phase: Early exploration (iter 3/5, population still diverse)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high-, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long-, {'type': 'low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:52:42,978 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:52:42,978 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:52:55,875 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high-opportunity, low-density adjacent",
  "strategy_comment": "Prioritize sparse cells near opportunities, avoid long-low"
}
```
2025-06-26 18:52:55,875 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:52:55,875 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:52:55,875 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:52:55,875 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 18:52:55,875 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:52:55,875 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:52:55,875 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 113183.0
2025-06-26 18:52:56,382 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:52:56,382 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509]
2025-06-26 18:52:56,383 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64)]
2025-06-26 18:52:56,386 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:52:56,386 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 12, 18, 22, 26, 30, 34, 38, 42, 45, 48, 51, 54, 50, 47, 44, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 4, 8, 11, 15, 19, 23, 27, 31, 35, 39, 43, 46, 49, 52, 53, 40, 36, 32, 28, 24, 20, 16, 14, 10, 6, 2], 'cur_cost': 39346.0}, {'tour': [0, 3, 7, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 13, 9, 5, 1, 4, 8, 14, 19, 24, 28, 32, 36, 40, 44, 48, 52, 49, 45, 41, 37, 33, 29, 25, 20, 16, 11, 6, 2, 10, 15, 23, 53], 'cur_cost': 49515.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': array([30, 49, 39,  8, 36, 45, 34, 14, 27, 43,  1, 54, 20,  3, 21, 41, 25,
       23, 42, 47,  4,  7, 11, 51, 16, 31, 29, 44, 26, 50, 33, 18, 53, 52,
       40, 22, 32, 17, 10, 48, 13,  2, 19, 38, 24, 12, 28, 37,  6,  5, 15,
       46,  0, 35,  9]), 'cur_cost': 113183.0}, {'tour': array([18, 45,  0, 50, 53,  5, 34, 14, 51, 16, 25, 36, 28,  4, 33, 21, 27,
       13, 41,  6, 48,  2,  8, 23, 32, 26, 29,  3, 10, 37, 12, 40,  7, 49,
       15, 20, 42, 54, 35, 39, 30, 31, 47, 17, 22, 43, 24, 44, 19,  9, 38,
        1, 52, 11, 46]), 'cur_cost': 102070.0}, {'tour': array([35, 23, 11, 13,  3, 27, 34, 36, 33, 54, 51,  0, 53,  1, 18,  8, 38,
       48, 30, 49, 17, 45, 44, 20, 25, 14,  9, 43, 21, 47,  6, 39, 50, 32,
       16, 12, 52, 28,  2, 29, 19, 42, 41,  7, 37, 10, 22,  4, 24, 31, 40,
       15, 46, 26,  5]), 'cur_cost': 102169.0}, {'tour': array([28, 16, 49, 53, 19, 18, 26, 35, 39, 13,  2, 43, 25, 27, 10, 46, 15,
        1,  4, 31, 14, 36, 50,  8,  5, 23, 38,  7, 54, 41, 48, 42,  9, 17,
       52, 32, 29, 45, 30, 24,  6, 22,  0, 21, 51, 37, 44, 34, 12, 11, 47,
       40,  3, 33, 20]), 'cur_cost': 104577.0}]
2025-06-26 18:52:56,388 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:52:56,389 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 276, 'skip_rate': 0.036231884057971016, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 266, 'cache_hits': 176, 'similarity_calculations': 4045, 'cache_hit_rate': 0.04351050679851669, 'cache_size': 3869}}
2025-06-26 18:52:56,389 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 18:52:56,389 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:52:56,389 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:52:56,390 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:52:56,390 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 106135.0
2025-06-26 18:52:56,893 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:52:56,894 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509]
2025-06-26 18:52:56,894 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64)]
2025-06-26 18:52:56,897 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:52:56,897 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 12, 18, 22, 26, 30, 34, 38, 42, 45, 48, 51, 54, 50, 47, 44, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 4, 8, 11, 15, 19, 23, 27, 31, 35, 39, 43, 46, 49, 52, 53, 40, 36, 32, 28, 24, 20, 16, 14, 10, 6, 2], 'cur_cost': 39346.0}, {'tour': [0, 3, 7, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 13, 9, 5, 1, 4, 8, 14, 19, 24, 28, 32, 36, 40, 44, 48, 52, 49, 45, 41, 37, 33, 29, 25, 20, 16, 11, 6, 2, 10, 15, 23, 53], 'cur_cost': 49515.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': array([30, 49, 39,  8, 36, 45, 34, 14, 27, 43,  1, 54, 20,  3, 21, 41, 25,
       23, 42, 47,  4,  7, 11, 51, 16, 31, 29, 44, 26, 50, 33, 18, 53, 52,
       40, 22, 32, 17, 10, 48, 13,  2, 19, 38, 24, 12, 28, 37,  6,  5, 15,
       46,  0, 35,  9]), 'cur_cost': 113183.0}, {'tour': array([12,  4, 52,  1, 54, 24, 30, 32, 47, 25,  7, 21, 36, 29, 20, 42, 48,
       28, 53, 51, 11, 44, 26, 19, 16, 13,  5,  8, 27, 22, 14,  6, 38,  2,
       41, 37,  9, 23,  3, 31, 15, 43, 18, 45, 39, 35, 50, 46, 49, 40, 10,
       34, 17,  0, 33]), 'cur_cost': 106135.0}, {'tour': array([35, 23, 11, 13,  3, 27, 34, 36, 33, 54, 51,  0, 53,  1, 18,  8, 38,
       48, 30, 49, 17, 45, 44, 20, 25, 14,  9, 43, 21, 47,  6, 39, 50, 32,
       16, 12, 52, 28,  2, 29, 19, 42, 41,  7, 37, 10, 22,  4, 24, 31, 40,
       15, 46, 26,  5]), 'cur_cost': 102169.0}, {'tour': array([28, 16, 49, 53, 19, 18, 26, 35, 39, 13,  2, 43, 25, 27, 10, 46, 15,
        1,  4, 31, 14, 36, 50,  8,  5, 23, 38,  7, 54, 41, 48, 42,  9, 17,
       52, 32, 29, 45, 30, 24,  6, 22,  0, 21, 51, 37, 44, 34, 12, 11, 47,
       40,  3, 33, 20]), 'cur_cost': 104577.0}]
2025-06-26 18:52:56,899 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:52:56,899 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 277, 'skip_rate': 0.036101083032490974, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 267, 'cache_hits': 176, 'similarity_calculations': 4055, 'cache_hit_rate': 0.04340320591861899, 'cache_size': 3879}}
2025-06-26 18:52:56,899 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:52:56,899 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:52:56,899 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:52:56,901 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:52:56,901 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 106569.0
2025-06-26 18:52:57,404 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:52:57,405 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509]
2025-06-26 18:52:57,405 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64)]
2025-06-26 18:52:57,408 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:52:57,408 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 12, 18, 22, 26, 30, 34, 38, 42, 45, 48, 51, 54, 50, 47, 44, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 4, 8, 11, 15, 19, 23, 27, 31, 35, 39, 43, 46, 49, 52, 53, 40, 36, 32, 28, 24, 20, 16, 14, 10, 6, 2], 'cur_cost': 39346.0}, {'tour': [0, 3, 7, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 13, 9, 5, 1, 4, 8, 14, 19, 24, 28, 32, 36, 40, 44, 48, 52, 49, 45, 41, 37, 33, 29, 25, 20, 16, 11, 6, 2, 10, 15, 23, 53], 'cur_cost': 49515.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': array([30, 49, 39,  8, 36, 45, 34, 14, 27, 43,  1, 54, 20,  3, 21, 41, 25,
       23, 42, 47,  4,  7, 11, 51, 16, 31, 29, 44, 26, 50, 33, 18, 53, 52,
       40, 22, 32, 17, 10, 48, 13,  2, 19, 38, 24, 12, 28, 37,  6,  5, 15,
       46,  0, 35,  9]), 'cur_cost': 113183.0}, {'tour': array([12,  4, 52,  1, 54, 24, 30, 32, 47, 25,  7, 21, 36, 29, 20, 42, 48,
       28, 53, 51, 11, 44, 26, 19, 16, 13,  5,  8, 27, 22, 14,  6, 38,  2,
       41, 37,  9, 23,  3, 31, 15, 43, 18, 45, 39, 35, 50, 46, 49, 40, 10,
       34, 17,  0, 33]), 'cur_cost': 106135.0}, {'tour': array([20,  9, 15, 46, 11, 43,  0, 36, 27,  5, 42,  1, 45,  8, 29, 39, 26,
       12, 49, 10, 52,  4, 16,  2, 40,  3, 35, 41, 54, 18, 37, 28, 17,  6,
       23, 34, 47, 19, 22, 32, 14, 31, 51, 33, 38, 44, 48,  7, 30, 25, 50,
       24, 53, 13, 21]), 'cur_cost': 106569.0}, {'tour': array([28, 16, 49, 53, 19, 18, 26, 35, 39, 13,  2, 43, 25, 27, 10, 46, 15,
        1,  4, 31, 14, 36, 50,  8,  5, 23, 38,  7, 54, 41, 48, 42,  9, 17,
       52, 32, 29, 45, 30, 24,  6, 22,  0, 21, 51, 37, 44, 34, 12, 11, 47,
       40,  3, 33, 20]), 'cur_cost': 104577.0}]
2025-06-26 18:52:57,411 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:52:57,411 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 278, 'skip_rate': 0.03597122302158273, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 268, 'cache_hits': 176, 'similarity_calculations': 4066, 'cache_hit_rate': 0.04328578455484506, 'cache_size': 3890}}
2025-06-26 18:52:57,411 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:52:57,411 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:52:57,411 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:52:57,412 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:52:57,412 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 118691.0
2025-06-26 18:52:57,914 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:52:57,914 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509]
2025-06-26 18:52:57,914 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64)]
2025-06-26 18:52:57,917 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:52:57,917 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 12, 18, 22, 26, 30, 34, 38, 42, 45, 48, 51, 54, 50, 47, 44, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 4, 8, 11, 15, 19, 23, 27, 31, 35, 39, 43, 46, 49, 52, 53, 40, 36, 32, 28, 24, 20, 16, 14, 10, 6, 2], 'cur_cost': 39346.0}, {'tour': [0, 3, 7, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 13, 9, 5, 1, 4, 8, 14, 19, 24, 28, 32, 36, 40, 44, 48, 52, 49, 45, 41, 37, 33, 29, 25, 20, 16, 11, 6, 2, 10, 15, 23, 53], 'cur_cost': 49515.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': array([30, 49, 39,  8, 36, 45, 34, 14, 27, 43,  1, 54, 20,  3, 21, 41, 25,
       23, 42, 47,  4,  7, 11, 51, 16, 31, 29, 44, 26, 50, 33, 18, 53, 52,
       40, 22, 32, 17, 10, 48, 13,  2, 19, 38, 24, 12, 28, 37,  6,  5, 15,
       46,  0, 35,  9]), 'cur_cost': 113183.0}, {'tour': array([12,  4, 52,  1, 54, 24, 30, 32, 47, 25,  7, 21, 36, 29, 20, 42, 48,
       28, 53, 51, 11, 44, 26, 19, 16, 13,  5,  8, 27, 22, 14,  6, 38,  2,
       41, 37,  9, 23,  3, 31, 15, 43, 18, 45, 39, 35, 50, 46, 49, 40, 10,
       34, 17,  0, 33]), 'cur_cost': 106135.0}, {'tour': array([20,  9, 15, 46, 11, 43,  0, 36, 27,  5, 42,  1, 45,  8, 29, 39, 26,
       12, 49, 10, 52,  4, 16,  2, 40,  3, 35, 41, 54, 18, 37, 28, 17,  6,
       23, 34, 47, 19, 22, 32, 14, 31, 51, 33, 38, 44, 48,  7, 30, 25, 50,
       24, 53, 13, 21]), 'cur_cost': 106569.0}, {'tour': array([20, 23, 14,  0, 16, 37, 47, 35, 25,  6, 17,  2, 28, 13, 43, 22,  4,
       41, 11, 26, 15, 40, 46, 50, 49, 12,  3, 30, 48,  8, 45, 36, 53,  9,
       19, 42, 33,  1, 52, 44, 10, 21,  5, 54, 24, 34, 27, 29, 32, 18, 31,
       39, 38,  7, 51]), 'cur_cost': 118691.0}]
2025-06-26 18:52:57,921 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:52:57,921 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 279, 'skip_rate': 0.035842293906810034, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 269, 'cache_hits': 176, 'similarity_calculations': 4078, 'cache_hit_rate': 0.04315841098577734, 'cache_size': 3902}}
2025-06-26 18:52:57,921 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:52:57,922 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 12, 18, 22, 26, 30, 34, 38, 42, 45, 48, 51, 54, 50, 47, 44, 41, 37, 33, 29, 25, 21, 17, 13, 9, 5, 1, 4, 8, 11, 15, 19, 23, 27, 31, 35, 39, 43, 46, 49, 52, 53, 40, 36, 32, 28, 24, 20, 16, 14, 10, 6, 2], 'cur_cost': 39346.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 13, 9, 5, 1, 4, 8, 14, 19, 24, 28, 32, 36, 40, 44, 48, 52, 49, 45, 41, 37, 33, 29, 25, 20, 16, 11, 6, 2, 10, 15, 23, 53], 'cur_cost': 49515.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}]
2025-06-26 18:52:57,922 - __main__ - INFO - 进化阶段完成
2025-06-26 18:52:57,922 - __main__ - INFO - 开始评估阶段
2025-06-26 18:52:57,922 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:52:57,923 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:52:57,923 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 14860.0 → 14860.0 (Δ 0.0)
- Mean cost: 56518.8 → 69559.4
- Diversity: 0.644 → 0.748 (Δ 0.1037)

#### Elite metrics
- Elite count: 14 → 14
- Best elite cost: 10442 → 10442 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ -17635.5
- Exploit: 4 inds, success 0.0% , avg Δ -6148.25

#### Other indicators
- No-change individuals: 2
- Regression individuals: 7
- Historical trends: Last 3 iterations: costs [14860.0, 14860.0, 14860.0], diversity [0.7684848484848484, 0.6517171717171717, 0.8436363636363637]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:52:57,924 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:53:14,191 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_improvement": -17635.5,
      "contribution_to_elite": "moderate (only successful strategy)"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_improvement": -6148.25,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "explore is productive but exploit is failing; imbalance exists",
    "diversity_trend": "increasing (0.644 → 0.748) but historical volatility suggests instability"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_actions": [
      "increase mutation intensity for exploit strategies",
      "introduce periodic large-scale exploration bursts",
      "implement stagnation detection (e.g., restart if no improvement for 5 iterations)"
    ]
  }
}
```
2025-06-26 18:53:14,209 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:53:14,209 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_improvement": -17635.5,
      "contribution_to_elite": "moderate (only successful strategy)"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_improvement": -6148.25,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "explore is productive but exploit is failing; imbalance exists",
    "diversity_trend": "increasing (0.644 → 0.748) but historical volatility suggests instability"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_actions": [
      "increase mutation intensity for exploit strategies",
      "introduce periodic large-scale exploration bursts",
      "implement stagnation detection (e.g., restart if no improvement for 5 iterations)"
    ]
  }
}
```
2025-06-26 18:53:14,209 - __main__ - INFO - 评估阶段完成
2025-06-26 18:53:14,209 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_improvement": -17635.5,
      "contribution_to_elite": "moderate (only successful strategy)"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_improvement": -6148.25,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "explore is productive but exploit is failing; imbalance exists",
    "diversity_trend": "increasing (0.644 → 0.748) but historical volatility suggests instability"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_actions": [
      "increase mutation intensity for exploit strategies",
      "introduce periodic large-scale exploration bursts",
      "implement stagnation detection (e.g., restart if no improvement for 5 iterations)"
    ]
  }
}
```
2025-06-26 18:53:14,209 - __main__ - INFO - 当前最佳适应度: 14860.0
2025-06-26 18:53:14,209 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite10_55_route_3.pkl
2025-06-26 18:53:14,209 - __main__ - INFO - composite10_55 开始进化第 5 代
2025-06-26 18:53:14,209 - __main__ - INFO - 开始分析阶段
2025-06-26 18:53:14,209 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:53:14,226 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 14860.0, 'max': 118691.0, 'mean': 69559.4, 'std': 41765.929112615224}, 'diversity': 0.9333333333333335, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 1, 2, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:53:14,226 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 14860.0, 'max': 118691.0, 'mean': 69559.4, 'std': 41765.929112615224}, 'diversity_level': 0.9333333333333335, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 1, 2, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[819, 516], [794, 496], [762, 445], [746, 472], [791, 512], [749, 494], [767, 510], [767, 471], [789, 469], [766, 485], [838, 470], [455, 3402], [394, 3338], [481, 3377], [454, 3375], [412, 3387], [431, 3380], [473, 3354], [418, 3361], [444, 3339], [393, 3380], [422, 3324], [1942, 1370], [1925, 1357], [1877, 1382], [1902, 1350], [1872, 1350], [1963, 1385], [1912, 1401], [1951, 1341], [1871, 1399], [1890, 1327], [1935, 1392], [2950, 892], [3005, 873], [2949, 919], [2966, 905], [2983, 890], [2980, 914], [2997, 855], [3018, 887], [3003, 907], [2971, 883], [2959, 855], [1636, 3351], [1588, 3390], [1672, 3368], [1599, 3369], [1618, 3412], [1677, 3356], [1625, 3388], [1585, 3375], [1611, 3388], [1614, 3342], [1621, 3360]], 'distance_matrix': array([[   0.,   32.,   91., ..., 2979., 2936., 2955.],
       [  32.,    0.,   60., ..., 3005., 2962., 2981.],
       [  91.,   60.,    0., ..., 3063., 3020., 3039.],
       ...,
       [2979., 3005., 3063., ...,    0.,   46.,   30.],
       [2936., 2962., 3020., ...,   46.,    0.,   19.],
       [2955., 2981., 3039., ...,   30.,   19.,    0.]])}
2025-06-26 18:53:14,226 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:53:14,226 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:53:14,226 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:53:14,226 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:53:14,226 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:53:14,226 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:53:14,226 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:53:14,226 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:53:14,232 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:53:14,236 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:53:14,236 - __main__ - INFO - 精英专家分析报告: {'elite_count': 14, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 4418.0, 'avg_gap': 59109.54285714285}, 'structure_gap': {'unique_elite_edges': 109, 'unique_pop_edges': 393, 'common_edges': 52}}, 'elite_diversity': {'diversity_score': 0.1916083916083916}}
2025-06-26 18:53:14,236 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:53:14,237 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:53:14,239 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:53:14,239 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 14860.0, mean 69559.4, max 118691.0, std 41765.929112615224
- diversity: 0.9333333333333335
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [14860.0, 14860.0, 14860.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [393.0, 445.0, 3018.0, 3412.0]
- centroid/spread: [1546.036, 1896.182] | rms 1526.132
- density_grid(3x3): [[11, 11, 11], [0, 0, 0], [11, 11, 0]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 1167.0, 2061.0, 2863.0, 3627.0]
- long_edge_ratio: 0.248
- direction_hist(8 sectors): [7, 3, 8, 11, 5, 4, 5, 11]
- nn_median_dist: 20.616

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:53:14,239 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:53:41,707 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 13.0 to 3627.0, high std in cost_stats)",
    "modality": "Potentially multimodal (high diversity, no clustering info, but stagnation in recent best costs suggests local optima)",
    "deceptiveness": "Possible (long_edge_ratio 0.248 indicates some deceptive long edges, direction_hist shows uneven distribution)"
  },
  "population_state": {
    "coverage": "Partial (density_grid shows concentrated nodes in 2/3 rows, diversity 0.933 suggests good spread)",
    "convergence": "Stagnant (Δ 0.00 in recent best costs, convergence 0.0)",
    "elite_quality": "Weak (elite_count 0, fixed_nodes_sample uninformative)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Edges in q3-max range (2863.0-3627.0)",
    "low_density_zones": "Middle row of density_grid (empty cells)",
    "direction_bias": "Sectors 3 & 7 (11 edges each) may create directional bottlenecks"
  },
  "opportunity_regions": {
    "high_density_clusters": "Top-left and top-center grid cells (11 nodes each)",
    "short_edge_zones": "Edges in min-q1 range (13.0-1167.0)",
    "centroid_proximity": "Nodes near [1546.036, 1896.182] (RMS 1526.132 suggests compactness)"
  },
  "evolution_phase": "Mid-phase stagnation (iter 4/5 with population_size 10, best cost plateaued)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Directed mutation targeting long edges (>2863.0) with 2-opt",
    "2. Diversity injection in empty grid cells (middle row)",
    "3. Path consolidation around high-density clusters (top grid cells)",
    "4. Sector-balanced crossover (address direction_hist biases)"
  ]
}
```
2025-06-26 18:53:41,711 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:53:41,711 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 13.0 to 3627.0, high std in cost_stats)', 'modality': 'Potentially multimodal (high diversity, no clustering info, but stagnation in recent best costs suggests local optima)', 'deceptiveness': 'Possible (long_edge_ratio 0.248 indicates some deceptive long edges, direction_hist shows uneven distribution)'}, 'population_state': {'coverage': 'Partial (density_grid shows concentrated nodes in 2/3 rows, diversity 0.933 suggests good spread)', 'convergence': 'Stagnant (Δ 0.00 in recent best costs, convergence 0.0)', 'elite_quality': 'Weak (elite_count 0, fixed_nodes_sample uninformative)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in q3-max range (2863.0-3627.0)', 'low_density_zones': 'Middle row of density_grid (empty cells)', 'direction_bias': 'Sectors 3 & 7 (11 edges each) may create directional bottlenecks'}, 'opportunity_regions': {'high_density_clusters': 'Top-left and top-center grid cells (11 nodes each)', 'short_edge_zones': 'Edges in min-q1 range (13.0-1167.0)', 'centroid_proximity': 'Nodes near [1546.036, 1896.182] (RMS 1526.132 suggests compactness)'}, 'evolution_phase': 'Mid-phase stagnation (iter 4/5 with population_size 10, best cost plateaued)', 'evolution_direction': ['Operator suggestions:', '1. Directed mutation targeting long edges (>2863.0) with 2-opt', '2. Diversity injection in empty grid cells (middle row)', '3. Path consolidation around high-density clusters (top grid cells)', '4. Sector-balanced crossover (address direction_hist biases)']}
2025-06-26 18:53:41,712 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:53:41,712 - __main__ - INFO - 分析阶段完成
2025-06-26 18:53:41,712 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 13.0 to 3627.0, high std in cost_stats)', 'modality': 'Potentially multimodal (high diversity, no clustering info, but stagnation in recent best costs suggests local optima)', 'deceptiveness': 'Possible (long_edge_ratio 0.248 indicates some deceptive long edges, direction_hist shows uneven distribution)'}, 'population_state': {'coverage': 'Partial (density_grid shows concentrated nodes in 2/3 rows, diversity 0.933 suggests good spread)', 'convergence': 'Stagnant (Δ 0.00 in recent best costs, convergence 0.0)', 'elite_quality': 'Weak (elite_count 0, fixed_nodes_sample uninformative)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in q3-max range (2863.0-3627.0)', 'low_density_zones': 'Middle row of density_grid (empty cells)', 'direction_bias': 'Sectors 3 & 7 (11 edges each) may create directional bottlenecks'}, 'opportunity_regions': {'high_density_clusters': 'Top-left and top-center grid cells (11 nodes each)', 'short_edge_zones': 'Edges in min-q1 range (13.0-1167.0)', 'centroid_proximity': 'Nodes near [1546.036, 1896.182] (RMS 1526.132 suggests compactness)'}, 'evolution_phase': 'Mid-phase stagnation (iter 4/5 with population_size 10, best cost plateaued)', 'evolution_direction': ['Operator suggestions:', '1. Directed mutation targeting long edges (>2863.0) with 2-opt', '2. Diversity injection in empty grid cells (middle row)', '3. Path consolidation around high-density clusters (top grid cells)', '4. Sector-balanced crossover (address direction_hist biases)']}
2025-06-26 18:53:41,712 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:53:41,712 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:53:41,712 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 14860.0
  • mean_cost: 69559.4
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 13.0 to 3627.0, high std in cost_stats), modality Potentially multimodal (high diversity, no clustering info, but stagnation in recent best costs suggests local optima), deceptiveness Possible (long_edge_ratio 0.248 indicates some deceptive long edges, direction_hist shows uneven distribution)
- Evolution phase: Mid-phase stagnation (iter 4/5 with population_size 10, best cost plateaued)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): long_edge_corridors, low_density_zones...
- Opportunity regions (# 3): high_density_cluster, short_edge_zones...
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:53:41,712 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:53:41,712 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:53:51,330 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "exploit",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest prioritizing exploration (70%), with top performers exploiting known good regions."
}
```
2025-06-26 18:53:51,330 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 18:53:51,330 - __main__ - INFO - 策略分配报告: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 18:53:51,330 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "exploit",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest prioritizing exploration (70%), with top performers exploiting known good regions."
}
```
2025-06-26 18:53:51,330 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:53:51,330 - __main__ - INFO - 策略分配: ['exploit', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 18:53:51,330 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "exploit",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest prioritizing exploration (70%), with top performers exploiting known good regions."
}
```
2025-06-26 18:53:51,330 - __main__ - INFO - 开始进化阶段
2025-06-26 18:53:51,330 - __main__ - INFO - 为个体 0 生成利用路径
2025-06-26 18:53:51,330 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:53:51,333 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:53:51,333 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 114426.0
2025-06-26 18:53:51,835 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:53:51,835 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509]
2025-06-26 18:53:51,835 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64)]
2025-06-26 18:53:51,840 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:53:51,840 - ExploitationExpert - INFO - populations: [{'tour': array([23, 44,  0, 36, 20,  4, 47,  3, 41, 48, 18, 49, 26, 15, 29, 53, 43,
       45, 13,  7, 30, 38, 28,  6, 50, 32, 31, 42, 22, 25, 54, 39,  9, 17,
       21, 46, 40, 52, 51, 37, 16,  8, 19, 10, 14,  1,  2, 24, 33,  5, 12,
       27, 11, 35, 34]), 'cur_cost': 114426.0}, {'tour': [0, 3, 7, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 13, 9, 5, 1, 4, 8, 14, 19, 24, 28, 32, 36, 40, 44, 48, 52, 49, 45, 41, 37, 33, 29, 25, 20, 16, 11, 6, 2, 10, 15, 23, 53], 'cur_cost': 49515.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53], 'cur_cost': 26857.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': array([30, 49, 39,  8, 36, 45, 34, 14, 27, 43,  1, 54, 20,  3, 21, 41, 25,
       23, 42, 47,  4,  7, 11, 51, 16, 31, 29, 44, 26, 50, 33, 18, 53, 52,
       40, 22, 32, 17, 10, 48, 13,  2, 19, 38, 24, 12, 28, 37,  6,  5, 15,
       46,  0, 35,  9]), 'cur_cost': 113183.0}, {'tour': array([12,  4, 52,  1, 54, 24, 30, 32, 47, 25,  7, 21, 36, 29, 20, 42, 48,
       28, 53, 51, 11, 44, 26, 19, 16, 13,  5,  8, 27, 22, 14,  6, 38,  2,
       41, 37,  9, 23,  3, 31, 15, 43, 18, 45, 39, 35, 50, 46, 49, 40, 10,
       34, 17,  0, 33]), 'cur_cost': 106135.0}, {'tour': array([20,  9, 15, 46, 11, 43,  0, 36, 27,  5, 42,  1, 45,  8, 29, 39, 26,
       12, 49, 10, 52,  4, 16,  2, 40,  3, 35, 41, 54, 18, 37, 28, 17,  6,
       23, 34, 47, 19, 22, 32, 14, 31, 51, 33, 38, 44, 48,  7, 30, 25, 50,
       24, 53, 13, 21]), 'cur_cost': 106569.0}, {'tour': array([20, 23, 14,  0, 16, 37, 47, 35, 25,  6, 17,  2, 28, 13, 43, 22,  4,
       41, 11, 26, 15, 40, 46, 50, 49, 12,  3, 30, 48,  8, 45, 36, 53,  9,
       19, 42, 33,  1, 52, 44, 10, 21,  5, 54, 24, 34, 27, 29, 32, 18, 31,
       39, 38,  7, 51]), 'cur_cost': 118691.0}]
2025-06-26 18:53:51,842 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:53:51,842 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 280, 'skip_rate': 0.03571428571428571, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 270, 'cache_hits': 176, 'similarity_calculations': 4091, 'cache_hit_rate': 0.04302126619408458, 'cache_size': 3915}}
2025-06-26 18:53:51,843 - __main__ - WARNING - 个体 0 的利用路径生成失败，保留原个体
2025-06-26 18:53:51,843 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:53:51,843 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:53:51,844 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 49515.0 (rank 30.0%)
- Population diversity: 0.7870370370370371
- Evolution phase: Mid-phase stagnation (iter 4/5 with population_size 10, best cost plateaued)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone...
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:53:51,844 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:53:51,844 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:54:03,914 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Prioritize dense clusters with short edges, then spiral outward"
}
```
2025-06-26 18:54:03,914 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:54:03,921 - ExplorationExpert - INFO - 探索路径生成完成，成本: 20485.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:54:03,921 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 20485.0}
2025-06-26 18:54:03,921 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:54:03,921 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:54:03,922 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.7
- Evolution phase: Mid-phase stagnation (iter 4/5 with population_size 10, best cost plateaued)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone...
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:54:03,923 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:54:03,923 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:54:17,936 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 11, 22, 33, 44, 5, 16, 27, 38, 49, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1],
  "targeted_regions": "high_density_cl, short_edge_zone, adjacent low-density cells",
  "strategy_comment": "Prioritize opportunity regions with spiral pattern to balance diversity and cost"
}
```
2025-06-26 18:54:17,936 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:54:17,936 - ExplorationExpert - INFO - 探索路径生成完成，成本: 131602.0, 路径: [0, 12, 23, 34, 45, 11, 22, 33, 44, 5, 16, 27, 38, 49, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1]
2025-06-26 18:54:17,936 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 5, 16, 27, 38, 49, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1], 'cur_cost': 131602.0}
2025-06-26 18:54:17,944 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:54:17,944 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:54:17,944 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 105578.0 (rank 30.0%)
- Population diversity: 0.7314814814814815
- Evolution phase: Mid-phase stagnation (iter 4/5 with population_size 10, best cost plateaued)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone...
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:54:17,944 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:54:17,944 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:54:31,274 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Leverage clustered nodes in opportunity regions, avoid long edges"
}
```
2025-06-26 18:54:31,274 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:54:31,276 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105578.0, 路径: [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:54:31,276 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}
2025-06-26 18:54:31,276 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:54:31,276 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:54:31,278 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26857.0 (rank 20.0%)
- Population diversity: 0.7314814814814815
- Evolution phase: Mid-phase stagnation (iter 4/5 with population_size 10, best cost plateaued)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone...
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:54:31,278 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:54:31,278 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:54:44,133 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Leverage clustered nodes with short edges, then spiral outward"
}
```
2025-06-26 18:54:44,133 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:54:44,133 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105578.0, 路径: [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:54:44,133 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}
2025-06-26 18:54:44,135 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:54:44,135 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:54:44,135 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 14860.0 (rank 0.0%)
- Population diversity: 0.6444444444444445
- Evolution phase: Mid-phase stagnation (iter 4/5 with population_size 10, best cost plateaued)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone...
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:54:44,135 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:54:44,135 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:54:56,776 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Sequential exploration of opportunity regions"
}
```
2025-06-26 18:54:56,780 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:54:56,780 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14860.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]
2025-06-26 18:54:56,780 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}
2025-06-26 18:54:56,780 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:54:56,780 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:54:56,781 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 113183.0 (rank 60.0%)
- Population diversity: 0.6444444444444445
- Evolution phase: Mid-phase stagnation (iter 4/5 with population_size 10, best cost plateaued)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone...
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:54:56,781 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:54:56,781 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:55:10,205 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 47, 48, 49, 50, 51, 52, 53, 54, 46],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Leverage clustered nodes in opportunity zones while avoiding long edges"
}
```
2025-06-26 18:55:10,205 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:55:10,213 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105560.0, 路径: [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 47, 48, 49, 50, 51, 52, 53, 54, 46]
2025-06-26 18:55:10,213 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 47, 48, 49, 50, 51, 52, 53, 54, 46], 'cur_cost': 105560.0}
2025-06-26 18:55:10,213 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:55:10,213 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:55:10,213 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:55:10,213 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 101314.0
2025-06-26 18:55:10,714 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:55:10,714 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509]
2025-06-26 18:55:10,715 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64)]
2025-06-26 18:55:10,719 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:55:10,719 - ExploitationExpert - INFO - populations: [{'tour': array([23, 44,  0, 36, 20,  4, 47,  3, 41, 48, 18, 49, 26, 15, 29, 53, 43,
       45, 13,  7, 30, 38, 28,  6, 50, 32, 31, 42, 22, 25, 54, 39,  9, 17,
       21, 46, 40, 52, 51, 37, 16,  8, 19, 10, 14,  1,  2, 24, 33,  5, 12,
       27, 11, 35, 34]), 'cur_cost': 114426.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 20485.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 5, 16, 27, 38, 49, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1], 'cur_cost': 131602.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 47, 48, 49, 50, 51, 52, 53, 54, 46], 'cur_cost': 105560.0}, {'tour': array([ 0, 42, 54,  3, 16, 24, 51, 32, 41, 28, 38, 27,  8, 19, 52, 31, 10,
       11,  5, 18, 37, 48, 12, 47, 20, 17,  6, 53, 36, 43, 49, 45, 30, 46,
       13,  1, 33, 40, 21, 14, 29, 22, 26, 34,  9, 44, 50,  4,  7, 39,  2,
       15, 35, 25, 23]), 'cur_cost': 101314.0}, {'tour': array([20,  9, 15, 46, 11, 43,  0, 36, 27,  5, 42,  1, 45,  8, 29, 39, 26,
       12, 49, 10, 52,  4, 16,  2, 40,  3, 35, 41, 54, 18, 37, 28, 17,  6,
       23, 34, 47, 19, 22, 32, 14, 31, 51, 33, 38, 44, 48,  7, 30, 25, 50,
       24, 53, 13, 21]), 'cur_cost': 106569.0}, {'tour': array([20, 23, 14,  0, 16, 37, 47, 35, 25,  6, 17,  2, 28, 13, 43, 22,  4,
       41, 11, 26, 15, 40, 46, 50, 49, 12,  3, 30, 48,  8, 45, 36, 53,  9,
       19, 42, 33,  1, 52, 44, 10, 21,  5, 54, 24, 34, 27, 29, 32, 18, 31,
       39, 38,  7, 51]), 'cur_cost': 118691.0}]
2025-06-26 18:55:10,721 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:55:10,721 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 281, 'skip_rate': 0.03558718861209965, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 271, 'cache_hits': 176, 'similarity_calculations': 4105, 'cache_hit_rate': 0.04287454323995128, 'cache_size': 3929}}
2025-06-26 18:55:10,722 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:55:10,722 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-26 18:55:10,722 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:55:10,723 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106569.0 (rank 60.0%)
- Population diversity: 0.5574074074074075
- Evolution phase: Mid-phase stagnation (iter 4/5 with population_size 10, best cost plateaued)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone...
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 54]  
• Path length == 55 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:55:10,723 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:55:10,723 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:55:24,025 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 34, 7, 45, 23, 8, 19, 37, 52, 14, 29, 41, 3, 16, 48, 25, 5, 31, 43, 10, 21, 39, 54, 2, 17, 33, 50, 9, 26, 44, 6, 20, 36, 51, 13, 28, 42, 4, 15, 47, 24, 1, 18, 35, 49, 11, 27, 46, 22, 40, 53, 30, 38, 32],
  "targeted_regions": "high_density_cl, short_edge_zone, adjacent low-density cells",
  "strategy_comment": "Blend opportunity regions with sparse exploration, avoiding long edges"
}
```
2025-06-26 18:55:24,025 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:55:24,025 - ExplorationExpert - INFO - 探索路径生成完成，成本: 125398.0, 路径: [0, 12, 34, 7, 45, 23, 8, 19, 37, 52, 14, 29, 41, 3, 16, 48, 25, 5, 31, 43, 10, 21, 39, 54, 2, 17, 33, 50, 9, 26, 44, 6, 20, 36, 51, 13, 28, 42, 4, 15, 47, 24, 1, 18, 35, 49, 11, 27, 46, 22, 40, 53, 30, 38, 32]
2025-06-26 18:55:24,025 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 12, 34, 7, 45, 23, 8, 19, 37, 52, 14, 29, 41, 3, 16, 48, 25, 5, 31, 43, 10, 21, 39, 54, 2, 17, 33, 50, 9, 26, 44, 6, 20, 36, 51, 13, 28, 42, 4, 15, 47, 24, 1, 18, 35, 49, 11, 27, 46, 22, 40, 53, 30, 38, 32], 'cur_cost': 125398.0}
2025-06-26 18:55:24,025 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:55:24,025 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:55:24,025 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:55:24,025 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 103764.0
2025-06-26 18:55:24,527 - ExploitationExpert - INFO - res_population_num: 14
2025-06-26 18:55:24,528 - ExploitationExpert - INFO - res_population_costs: [10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10442, 10444, 10450, 10450, 10467, 10509]
2025-06-26 18:55:24,528 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 33, 42, 37, 36, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0,  4,  1,  8,  7,  2,  3,  5,  9,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       11, 14, 17, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 30, 24,
       26, 31, 25, 23, 28, 32, 27, 22, 29, 35, 36, 33, 42, 37, 38, 41, 40,
       34, 39, 43, 10], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 37, 42, 33, 36, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  9,  5,  3,  2,
        7,  8,  1,  4], dtype=int64), array([ 0, 10, 43, 39, 34, 40, 41, 38, 36, 37, 42, 33, 35, 29, 22, 27, 32,
       28, 23, 25, 31, 26, 24, 30, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 17, 14, 11, 16, 15, 20, 12, 18, 19, 21,  6,  5,  3,  2,  7,
        9,  8,  1,  4], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 17, 13, 11, 14, 16, 15, 20, 18, 12, 21, 19,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  9,  7,  2,  3,  5,  6, 19, 21, 12, 18, 20, 15, 16,
       14, 11, 13, 17, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 29, 23, 22, 27, 32, 28, 30, 24,
       26, 25, 31, 10], dtype=int64), array([ 0, 10, 31, 25, 26, 24, 30, 28, 32, 27, 22, 23, 29, 33, 37, 42, 43,
       39, 34, 40, 41, 38, 36, 35, 49, 46, 44, 53, 54, 50, 48, 52, 47, 51,
       45, 13, 11, 16, 15, 20, 12, 18, 14, 17, 19, 21,  6,  5,  9,  7,  3,
        2,  8,  1,  4], dtype=int64), array([ 0,  4,  1,  8,  2,  3,  7,  9,  5,  6, 21, 19, 17, 14, 18, 12, 20,
       15, 16, 11, 13, 45, 51, 47, 52, 48, 50, 54, 53, 44, 46, 49, 35, 36,
       38, 41, 40, 34, 39, 43, 42, 37, 33, 27, 32, 28, 30, 24, 26, 25, 23,
       22, 29, 31, 10], dtype=int64)]
2025-06-26 18:55:24,532 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:55:24,532 - ExploitationExpert - INFO - populations: [{'tour': array([23, 44,  0, 36, 20,  4, 47,  3, 41, 48, 18, 49, 26, 15, 29, 53, 43,
       45, 13,  7, 30, 38, 28,  6, 50, 32, 31, 42, 22, 25, 54, 39,  9, 17,
       21, 46, 40, 52, 51, 37, 16,  8, 19, 10, 14,  1,  2, 24, 33,  5, 12,
       27, 11, 35, 34]), 'cur_cost': 114426.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 20485.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 5, 16, 27, 38, 49, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1], 'cur_cost': 131602.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}, {'tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 47, 48, 49, 50, 51, 52, 53, 54, 46], 'cur_cost': 105560.0}, {'tour': array([ 0, 42, 54,  3, 16, 24, 51, 32, 41, 28, 38, 27,  8, 19, 52, 31, 10,
       11,  5, 18, 37, 48, 12, 47, 20, 17,  6, 53, 36, 43, 49, 45, 30, 46,
       13,  1, 33, 40, 21, 14, 29, 22, 26, 34,  9, 44, 50,  4,  7, 39,  2,
       15, 35, 25, 23]), 'cur_cost': 101314.0}, {'tour': [0, 12, 34, 7, 45, 23, 8, 19, 37, 52, 14, 29, 41, 3, 16, 48, 25, 5, 31, 43, 10, 21, 39, 54, 2, 17, 33, 50, 9, 26, 44, 6, 20, 36, 51, 13, 28, 42, 4, 15, 47, 24, 1, 18, 35, 49, 11, 27, 46, 22, 40, 53, 30, 38, 32], 'cur_cost': 125398.0}, {'tour': array([33, 16, 41,  7,  3, 20, 14, 10, 48, 21,  5, 53,  8, 32,  9, 49, 23,
       18, 17,  4,  6,  2, 36, 15, 42, 31, 24, 34, 46,  1, 44, 11, 37, 22,
       26, 50, 29, 38, 45, 12, 43, 13, 52, 35, 39, 27, 28, 25, 47,  0, 54,
       19, 51, 30, 40]), 'cur_cost': 103764.0}]
2025-06-26 18:55:24,533 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:55:24,533 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 282, 'skip_rate': 0.03546099290780142, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 272, 'cache_hits': 176, 'similarity_calculations': 4120, 'cache_hit_rate': 0.04271844660194175, 'cache_size': 3944}}
2025-06-26 18:55:24,534 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:55:24,534 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 20485.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 5, 16, 27, 38, 49, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1], 'cur_cost': 131602.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 105578.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], 'cur_cost': 14860.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 11, 22, 33, 44, 10, 21, 32, 43, 9, 20, 31, 42, 8, 19, 30, 41, 7, 18, 29, 40, 6, 17, 28, 39, 5, 16, 27, 38, 4, 15, 26, 37, 3, 14, 25, 36, 2, 13, 24, 35, 1, 47, 48, 49, 50, 51, 52, 53, 54, 46], 'cur_cost': 105560.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 34, 7, 45, 23, 8, 19, 37, 52, 14, 29, 41, 3, 16, 48, 25, 5, 31, 43, 10, 21, 39, 54, 2, 17, 33, 50, 9, 26, 44, 6, 20, 36, 51, 13, 28, 42, 4, 15, 47, 24, 1, 18, 35, 49, 11, 27, 46, 22, 40, 53, 30, 38, 32], 'cur_cost': 125398.0}}]
2025-06-26 18:55:24,534 - __main__ - INFO - 进化阶段完成
2025-06-26 18:55:24,535 - __main__ - INFO - 开始评估阶段
2025-06-26 18:55:24,535 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:55:24,536 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:55:24,536 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 14860.0 → 14860.0 (Δ 0.0)
- Mean cost: 69559.4 → 92856.5
- Diversity: 0.748 → 0.559 (Δ -0.1889)

#### Elite metrics
- Elite count: 14 → 14
- Best elite cost: 10442 → 10442 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 28.6% , avg Δ -25377.0
- Exploit: 3 inds, success 66.7% , avg Δ -18444.0

#### Other indicators
- No-change individuals: 2
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [14860.0, 14860.0, 14860.0], diversity [0.6517171717171717, 0.8436363636363637, 0.9333333333333335]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:55:24,536 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:55:39,072 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "plateau_with_partial_stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -25377.0,
      "contribution_to_elite": "moderate_but_inconsistent"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": -18444.0,
      "contribution_to_elite": "high_and_consistent"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "exploit_heavy",
    "diversity_decline": "significant(-0.1889)",
    "stagnation_risk": "moderate_due_to_no_elite_improvement"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_intensity": "increase_moderately",
    "elite_preservation": "maintain_current_count(14)"
  }
}
```
2025-06-26 18:55:39,093 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:55:39,093 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "plateau_with_partial_stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -25377.0,
      "contribution_to_elite": "moderate_but_inconsistent"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": -18444.0,
      "contribution_to_elite": "high_and_consistent"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "exploit_heavy",
    "diversity_decline": "significant(-0.1889)",
    "stagnation_risk": "moderate_due_to_no_elite_improvement"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_intensity": "increase_moderately",
    "elite_preservation": "maintain_current_count(14)"
  }
}
```
2025-06-26 18:55:39,093 - __main__ - INFO - 评估阶段完成
2025-06-26 18:55:39,093 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "plateau_with_partial_stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_improvement": -25377.0,
      "contribution_to_elite": "moderate_but_inconsistent"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": -18444.0,
      "contribution_to_elite": "high_and_consistent"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "exploit_heavy",
    "diversity_decline": "significant(-0.1889)",
    "stagnation_risk": "moderate_due_to_no_elite_improvement"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_intensity": "increase_moderately",
    "elite_preservation": "maintain_current_count(14)"
  }
}
```
2025-06-26 18:55:39,093 - __main__ - INFO - 当前最佳适应度: 14860.0
2025-06-26 18:55:39,093 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite10_55_route_4.pkl
2025-06-26 18:55:39,098 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite10_55_solution.json
2025-06-26 18:55:39,098 - __main__ - INFO - 实例 composite10_55 处理完成
