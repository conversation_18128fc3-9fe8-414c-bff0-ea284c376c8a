2025-06-26 20:53:14,998 - __main__ - INFO - lin105 开始进化第 1 代
2025-06-26 20:53:14,998 - __main__ - INFO - 开始分析阶段
2025-06-26 20:53:14,999 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:53:15,042 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 17099.0, 'max': 129170.0, 'mean': 91782.5, 'std': 48136.98188762981}, 'diversity': 0.9263492063492063, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:53:15,042 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 17099.0, 'max': 129170.0, 'mean': 91782.5, 'std': 48136.98188762981}, 'diversity_level': 0.9263492063492063, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[63, 71], [94, 71], [142, 370], [173, 1276], [205, 1213], [213, 69], [244, 69], [276, 630], [283, 732], [362, 69], [394, 69], [449, 370], [480, 1276], [512, 1213], [528, 157], [583, 630], [591, 732], [638, 654], [638, 496], [638, 314], [638, 142], [669, 142], [677, 315], [677, 496], [677, 654], [709, 654], [709, 496], [709, 315], [701, 142], [764, 220], [811, 189], [843, 173], [858, 370], [890, 1276], [921, 1213], [992, 630], [1000, 732], [1197, 1276], [1228, 1213], [1276, 205], [1299, 630], [1307, 732], [1362, 654], [1362, 496], [1362, 291], [1425, 654], [1425, 496], [1425, 291], [1417, 173], [1488, 291], [1488, 496], [1488, 654], [1551, 654], [1551, 496], [1551, 291], [1614, 291], [1614, 496], [1614, 654], [1732, 189], [1811, 1276], [1843, 1213], [1913, 630], [1921, 732], [2087, 370], [2118, 1276], [2150, 1213], [2189, 205], [2220, 189], [2220, 630], [2228, 732], [2244, 142], [2276, 315], [2276, 496], [2276, 654], [2315, 654], [2315, 496], [2315, 315], [2331, 142], [2346, 315], [2346, 496], [2346, 654], [2362, 142], [2402, 157], [2402, 220], [2480, 142], [2496, 370], [2528, 1276], [2559, 1213], [2630, 630], [2638, 732], [2756, 69], [2787, 69], [2803, 370], [2835, 1276], [2866, 1213], [2906, 69], [2937, 69], [2937, 630], [2945, 732], [3016, 1276], [3055, 69], [3087, 69], [606, 220], [1165, 370], [1780, 370]], 'distance_matrix': array([[   0.,   31.,  309., ...,  563., 1142., 1743.],
       [  31.,    0.,  303., ...,  533., 1112., 1712.],
       [ 309.,  303.,    0., ...,  488., 1023., 1638.],
       ...,
       [ 563.,  533.,  488., ...,    0.,  579., 1184.],
       [1142., 1112., 1023., ...,  579.,    0.,  615.],
       [1743., 1712., 1638., ..., 1184.,  615.,    0.]])}
2025-06-26 20:53:15,052 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:53:15,052 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:53:15,052 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:53:15,058 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:53:15,061 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (71, 67, 66), 'frequency': 0.3}, {'subpath': (67, 66, 70), 'frequency': 0.3}, {'subpath': (66, 70, 77), 'frequency': 0.3}, {'subpath': (70, 77, 81), 'frequency': 0.3}, {'subpath': (77, 81, 82), 'frequency': 0.3}, {'subpath': (81, 82, 83), 'frequency': 0.3}, {'subpath': (82, 83, 84), 'frequency': 0.3}, {'subpath': (83, 84, 85), 'frequency': 0.3}, {'subpath': (73, 74, 80), 'frequency': 0.3}, {'subpath': (54, 49, 47), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(39, 44)', 'frequency': 0.4}, {'edge': '(42, 45)', 'frequency': 0.4}, {'edge': '(45, 51)', 'frequency': 0.4}, {'edge': '(35, 36)', 'frequency': 0.4}, {'edge': '(18, 23)', 'frequency': 0.4}, {'edge': '(20, 21)', 'frequency': 0.4}, {'edge': '(9, 10)', 'frequency': 0.4}, {'edge': '(5, 6)', 'frequency': 0.4}, {'edge': '(2, 7)', 'frequency': 0.4}, {'edge': '(34, 37)', 'frequency': 0.4}, {'edge': '(38, 59)', 'frequency': 0.4}, {'edge': '(91, 92)', 'frequency': 0.4}, {'edge': '(100, 101)', 'frequency': 0.4}, {'edge': '(63, 65)', 'frequency': 0.4}, {'edge': '(46, 59)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(76, 78)', 'frequency': 0.3}, {'edge': '(67, 71)', 'frequency': 0.3}, {'edge': '(66, 67)', 'frequency': 0.3}, {'edge': '(66, 70)', 'frequency': 0.3}, {'edge': '(70, 77)', 'frequency': 0.3}, {'edge': '(77, 81)', 'frequency': 0.3}, {'edge': '(81, 82)', 'frequency': 0.3}, {'edge': '(82, 83)', 'frequency': 0.3}, {'edge': '(83, 84)', 'frequency': 0.3}, {'edge': '(84, 85)', 'frequency': 0.3}, {'edge': '(75, 79)', 'frequency': 0.3}, {'edge': '(72, 75)', 'frequency': 0.3}, {'edge': '(68, 72)', 'frequency': 0.3}, {'edge': '(73, 74)', 'frequency': 0.3}, {'edge': '(74, 80)', 'frequency': 0.3}, {'edge': '(62, 69)', 'frequency': 0.3}, {'edge': '(61, 62)', 'frequency': 0.3}, {'edge': '(61, 104)', 'frequency': 0.3}, {'edge': '(54, 55)', 'frequency': 0.2}, {'edge': '(49, 54)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(44, 47)', 'frequency': 0.3}, {'edge': '(39, 48)', 'frequency': 0.3}, {'edge': '(48, 58)', 'frequency': 0.2}, {'edge': '(53, 56)', 'frequency': 0.2}, {'edge': '(50, 53)', 'frequency': 0.3}, {'edge': '(46, 50)', 'frequency': 0.3}, {'edge': '(43, 46)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.2}, {'edge': '(40, 42)', 'frequency': 0.3}, {'edge': '(51, 52)', 'frequency': 0.2}, {'edge': '(52, 57)', 'frequency': 0.3}, {'edge': '(36, 41)', 'frequency': 0.2}, {'edge': '(24, 25)', 'frequency': 0.3}, {'edge': '(17, 24)', 'frequency': 0.3}, {'edge': '(15, 17)', 'frequency': 0.3}, {'edge': '(15, 16)', 'frequency': 0.3}, {'edge': '(23, 26)', 'frequency': 0.3}, {'edge': '(22, 27)', 'frequency': 0.3}, {'edge': '(19, 22)', 'frequency': 0.3}, {'edge': '(20, 102)', 'frequency': 0.3}, {'edge': '(21, 28)', 'frequency': 0.3}, {'edge': '(28, 29)', 'frequency': 0.3}, {'edge': '(29, 30)', 'frequency': 0.3}, {'edge': '(30, 31)', 'frequency': 0.3}, {'edge': '(10, 14)', 'frequency': 0.3}, {'edge': '(6, 9)', 'frequency': 0.3}, {'edge': '(0, 1)', 'frequency': 0.3}, {'edge': '(7, 8)', 'frequency': 0.3}, {'edge': '(8, 11)', 'frequency': 0.2}, {'edge': '(11, 13)', 'frequency': 0.2}, {'edge': '(12, 13)', 'frequency': 0.3}, {'edge': '(4, 12)', 'frequency': 0.3}, {'edge': '(3, 4)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(37, 38)', 'frequency': 0.3}, {'edge': '(59, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(64, 65)', 'frequency': 0.3}, {'edge': '(86, 87)', 'frequency': 0.3}, {'edge': '(93, 94)', 'frequency': 0.3}, {'edge': '(94, 99)', 'frequency': 0.3}, {'edge': '(97, 98)', 'frequency': 0.3}, {'edge': '(92, 97)', 'frequency': 0.3}, {'edge': '(90, 91)', 'frequency': 0.3}, {'edge': '(90, 95)', 'frequency': 0.3}, {'edge': '(95, 96)', 'frequency': 0.3}, {'edge': '(96, 100)', 'frequency': 0.3}, {'edge': '(88, 89)', 'frequency': 0.3}, {'edge': '(63, 89)', 'frequency': 0.2}, {'edge': '(56, 57)', 'frequency': 0.2}, {'edge': '(58, 104)', 'frequency': 0.2}, {'edge': '(69, 73)', 'frequency': 0.2}, {'edge': '(68, 80)', 'frequency': 0.2}, {'edge': '(78, 79)', 'frequency': 0.2}, {'edge': '(71, 76)', 'frequency': 0.2}, {'edge': '(85, 88)', 'frequency': 0.2}, {'edge': '(89, 98)', 'frequency': 0.2}, {'edge': '(63, 101)', 'frequency': 0.2}, {'edge': '(13, 33)', 'frequency': 0.3}, {'edge': '(3, 8)', 'frequency': 0.2}, {'edge': '(1, 2)', 'frequency': 0.2}, {'edge': '(0, 5)', 'frequency': 0.2}, {'edge': '(14, 102)', 'frequency': 0.2}, {'edge': '(27, 31)', 'frequency': 0.2}, {'edge': '(18, 19)', 'frequency': 0.2}, {'edge': '(25, 26)', 'frequency': 0.2}, {'edge': '(11, 16)', 'frequency': 0.2}, {'edge': '(11, 32)', 'frequency': 0.2}, {'edge': '(32, 35)', 'frequency': 0.3}, {'edge': '(87, 103)', 'frequency': 0.2}, {'edge': '(86, 93)', 'frequency': 0.2}, {'edge': '(55, 58)', 'frequency': 0.2}, {'edge': '(36, 103)', 'frequency': 0.2}, {'edge': '(1, 66)', 'frequency': 0.2}, {'edge': '(84, 95)', 'frequency': 0.2}, {'edge': '(60, 100)', 'frequency': 0.2}, {'edge': '(76, 97)', 'frequency': 0.2}, {'edge': '(12, 86)', 'frequency': 0.2}, {'edge': '(29, 91)', 'frequency': 0.2}, {'edge': '(2, 47)', 'frequency': 0.2}, {'edge': '(74, 77)', 'frequency': 0.2}, {'edge': '(70, 104)', 'frequency': 0.2}, {'edge': '(57, 93)', 'frequency': 0.2}, {'edge': '(54, 73)', 'frequency': 0.2}, {'edge': '(25, 33)', 'frequency': 0.2}, {'edge': '(4, 51)', 'frequency': 0.2}, {'edge': '(17, 48)', 'frequency': 0.2}, {'edge': '(18, 37)', 'frequency': 0.2}, {'edge': '(66, 102)', 'frequency': 0.2}, {'edge': '(27, 88)', 'frequency': 0.2}, {'edge': '(7, 85)', 'frequency': 0.2}, {'edge': '(5, 61)', 'frequency': 0.2}, {'edge': '(28, 78)', 'frequency': 0.2}, {'edge': '(47, 95)', 'frequency': 0.2}, {'edge': '(12, 14)', 'frequency': 0.2}, {'edge': '(12, 23)', 'frequency': 0.2}, {'edge': '(51, 80)', 'frequency': 0.2}, {'edge': '(39, 76)', 'frequency': 0.2}, {'edge': '(34, 40)', 'frequency': 0.2}, {'edge': '(37, 104)', 'frequency': 0.2}, {'edge': '(58, 74)', 'frequency': 0.2}, {'edge': '(70, 71)', 'frequency': 0.2}, {'edge': '(71, 100)', 'frequency': 0.2}, {'edge': '(23, 72)', 'frequency': 0.2}, {'edge': '(80, 88)', 'frequency': 0.2}, {'edge': '(6, 35)', 'frequency': 0.2}, {'edge': '(38, 66)', 'frequency': 0.2}, {'edge': '(2, 88)', 'frequency': 0.2}, {'edge': '(4, 99)', 'frequency': 0.2}, {'edge': '(33, 53)', 'frequency': 0.2}, {'edge': '(69, 102)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [82, 17, 101, 2, 67], 'cost': 9397.0, 'size': 5}, {'region': [75, 19, 93, 8, 76], 'cost': 8768.0, 'size': 5}, {'region': [36, 96, 31, 99, 34], 'cost': 8677.0, 'size': 5}, {'region': [6, 62, 1, 66, 102], 'cost': 7428.0, 'size': 5}, {'region': [61, 0, 90, 15], 'cost': 6870.0, 'size': 4}]}
2025-06-26 20:53:15,062 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:53:15,062 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:53:15,062 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:53:15,062 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 20:53:15,063 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 20:53:15,063 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:53:15,063 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:53:15,066 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:53:15,067 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 17099.0, mean 91782.5, max 129170.0, std 48136.98188762981
- diversity: 0.9263492063492063
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [164593.0, 163098.0, 158735.0] (Δ 2929.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: [{'subpath': (71, 67, 66), 'frequency': 0.3}, {'subpath': (67, 66, 70), 'frequency': 0.3}, {'subpath': (66, 70, 77), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [82, 17, 101, 2, 67], 'cost': 9397.0, 'size': 5}, {'region': [75, 19, 93, 8, 76], 'cost': 8768.0, 'size': 5}, {'region': [36, 96, 31, 99, 34], 'cost': 8677.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [63.0, 69.0, 3087.0, 1276.0]
- centroid/spread: [1538.962, 525.343] | rms 953.602
- density_grid(3x3): [[20, 10, 20], [12, 14, 12], [6, 4, 7]]
- edge_len_stats(min,q1,med,q3,max): [31.0, 664.0, 1070.0, 1638.0, 3189.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [0, 2, 39, 4, 14, 31, 13, 1]
- nn_median_dist: 63.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:53:15,067 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:53:36,913 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "multimodal",
    "deceptiveness": "present",
    "edge_length_variability": "high",
    "long_edge_ratio": "significant"
  },
  "population_state": {
    "coverage": "moderate",
    "convergence": "none",
    "diversity": "high",
    "spread": "wide",
    "density_distribution": "uneven"
  },
  "difficult_regions": [
    {"region": [82, 17, 101, 2, 67], "reason": "high cost and size"},
    {"region": [75, 19, 93, 8, 76], "reason": "high cost and size"},
    {"region": [36, 96, 31, 99, 34], "reason": "high cost and size"}
  ],
  "opportunity_regions": [],
  "evolution_phase": "initial_exploration",
  "evolution_direction": {
    "suggested_operators": [
      "local_search_around_high_density_cells",
      "crossover_to_preserve_common_subpaths",
      "mutation_to_exploit_direction_bias",
      "intensification_around_common_subpaths"
    ],
    "focus_areas": [
      "exploit_high_density_cells",
      "address_long_edge_corridors",
      "enhance_population_diversity"
    ]
  }
}
```
2025-06-26 20:53:36,914 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:53:36,914 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'multimodal', 'deceptiveness': 'present', 'edge_length_variability': 'high', 'long_edge_ratio': 'significant'}, 'population_state': {'coverage': 'moderate', 'convergence': 'none', 'diversity': 'high', 'spread': 'wide', 'density_distribution': 'uneven'}, 'difficult_regions': [{'region': [82, 17, 101, 2, 67], 'reason': 'high cost and size'}, {'region': [75, 19, 93, 8, 76], 'reason': 'high cost and size'}, {'region': [36, 96, 31, 99, 34], 'reason': 'high cost and size'}], 'opportunity_regions': [], 'evolution_phase': 'initial_exploration', 'evolution_direction': {'suggested_operators': ['local_search_around_high_density_cells', 'crossover_to_preserve_common_subpaths', 'mutation_to_exploit_direction_bias', 'intensification_around_common_subpaths'], 'focus_areas': ['exploit_high_density_cells', 'address_long_edge_corridors', 'enhance_population_diversity']}}
2025-06-26 20:53:36,914 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:53:36,914 - __main__ - INFO - 分析阶段完成
2025-06-26 20:53:36,914 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'multimodal', 'deceptiveness': 'present', 'edge_length_variability': 'high', 'long_edge_ratio': 'significant'}, 'population_state': {'coverage': 'moderate', 'convergence': 'none', 'diversity': 'high', 'spread': 'wide', 'density_distribution': 'uneven'}, 'difficult_regions': [{'region': [82, 17, 101, 2, 67], 'reason': 'high cost and size'}, {'region': [75, 19, 93, 8, 76], 'reason': 'high cost and size'}, {'region': [36, 96, 31, 99, 34], 'reason': 'high cost and size'}], 'opportunity_regions': [], 'evolution_phase': 'initial_exploration', 'evolution_direction': {'suggested_operators': ['local_search_around_high_density_cells', 'crossover_to_preserve_common_subpaths', 'mutation_to_exploit_direction_bias', 'intensification_around_common_subpaths'], 'focus_areas': ['exploit_high_density_cells', 'address_long_edge_corridors', 'enhance_population_diversity']}}
2025-06-26 20:53:36,914 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:53:36,914 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:53:36,914 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 17099.0
  • mean_cost: 91782.5
- Search-space: ruggedness moderate, modality multimodal, deceptiveness present
- Evolution phase: initial_exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [82, 17, , {'region': [75, 19, ...
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:53:36,914 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:53:36,914 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:53:47,987 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and initial exploration phase favor exploration; top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 20:53:47,987 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 20:53:47,989 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 20:53:47,989 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and initial exploration phase favor exploration; top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 20:53:47,989 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:53:47,989 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 20:53:47,989 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and initial exploration phase favor exploration; top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 20:53:47,990 - __main__ - INFO - 开始进化阶段
2025-06-26 20:53:47,990 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:53:47,991 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:53:47,991 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17099.0 (rank 0.0%)
- Population diversity: 0.7701923076923077
- Evolution phase: initial_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [82,, {'region': [75,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:53:47,991 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:53:47,992 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:54:11,354 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104],
  "targeted_regions": "Avoided regions 82 and 75, targeted sparse cells",
  "strategy_comment": "Prioritize unexplored areas while avoiding difficult regions"
}
```
2025-06-26 20:54:11,354 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:54:11,354 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41383.0, 路径: [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104]
2025-06-26 20:54:11,365 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}
2025-06-26 20:54:11,365 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:54:11,365 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:54:11,366 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19114.0 (rank 0.0%)
- Population diversity: 0.8048076923076923
- Evolution phase: initial_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [82,, {'region': [75,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:54:11,366 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:54:11,366 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:54:33,526 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104],
  "targeted_regions": "Prioritized sparse cells 82 and 75 early",
  "strategy_comment": "Early sparse node insertion, then greedy"
}
```
2025-06-26 20:54:33,526 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:54:33,526 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41383.0, 路径: [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104]
2025-06-26 20:54:33,526 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}
2025-06-26 20:54:33,526 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:54:33,526 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:54:33,526 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19168.0 (rank 0.0%)
- Population diversity: 0.8009615384615385
- Evolution phase: initial_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [82,, {'region': [75,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:54:33,526 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:54:33,526 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:54:54,807 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104],
  "targeted_regions": "Prioritized sparse cells 82 and 75 early",
  "strategy_comment": "Early sparse node insertion, then greedy"
}
```
2025-06-26 20:54:54,807 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:54:54,809 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41383.0, 路径: [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104]
2025-06-26 20:54:54,809 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}
2025-06-26 20:54:54,809 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:54:54,809 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:54:54,810 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 128932.0 (rank 80.0%)
- Population diversity: 0.7461538461538462
- Evolution phase: initial_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [82,, {'region': [75,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:54:54,811 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:54:54,811 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:55:18,332 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 75, 82],
  "targeted_regions": "Avoided regions 75 and 82, explored sparse cells",
  "strategy_comment": "Shift difficult regions to end, balance diversity"
}
```
2025-06-26 20:55:18,341 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:55:18,341 - ExplorationExpert - INFO - 探索路径生成完成，成本: 37960.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 75, 82]
2025-06-26 20:55:18,341 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 75, 82], 'cur_cost': 37960.0}
2025-06-26 20:55:18,342 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:55:18,342 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:55:18,342 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 124531.0 (rank 80.0%)
- Population diversity: 0.6634615384615384
- Evolution phase: initial_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [82,, {'region': [75,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:55:18,342 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:55:18,342 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:55:40,314 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104],
  "targeted_regions": "Prioritized sparse nodes 82, 75 early",
  "strategy_comment": "Early sparse node insertion to boost diversity"
}
```
2025-06-26 20:55:40,314 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:55:40,320 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41383.0, 路径: [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104]
2025-06-26 20:55:40,320 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}
2025-06-26 20:55:40,320 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:55:40,320 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:55:40,320 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 122682.0 (rank 80.0%)
- Population diversity: 0.5740384615384615
- Evolution phase: initial_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [82,, {'region': [75,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:55:40,320 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:55:40,320 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:56:02,318 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104],
  "targeted_regions": "Avoided difficult regions 82 and 75 early",
  "strategy_comment": "Prioritize sparse cells early, then standard sequence"
}
```
2025-06-26 20:56:02,319 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:56:02,319 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41383.0, 路径: [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104]
2025-06-26 20:56:02,321 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}
2025-06-26 20:56:02,321 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:56:02,321 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:56:02,321 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 121031.0 (rank 80.0%)
- Population diversity: 0.4807692307692308
- Evolution phase: initial_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [82,, {'region': [75,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:56:02,321 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:56:02,321 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:56:24,941 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104],
  "targeted_regions": "Avoided regions 82 and 75 early",
  "strategy_comment": "Avoid difficult regions early, then standard sequence"
}
```
2025-06-26 20:56:24,941 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:56:24,942 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41383.0, 路径: [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104]
2025-06-26 20:56:24,942 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}
2025-06-26 20:56:24,943 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 20:56:24,943 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:56:24,944 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 118018.0 (rank 70.0%)
- Population diversity: 0.38653846153846155
- Evolution phase: initial_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): {'region': [82,, {'region': [75,...
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:56:24,944 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:56:24,944 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:56:50,221 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104],
  "targeted_regions": "Avoided difficult regions 82, 75",
  "strategy_comment": "Prioritize sparse cells, avoid difficult regions"
}
```
2025-06-26 20:56:50,224 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:56:50,224 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41383.0, 路径: [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104]
2025-06-26 20:56:50,224 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}
2025-06-26 20:56:50,224 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:56:50,224 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:56:50,224 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:56:50,224 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 125264.0
2025-06-26 20:56:50,728 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 20:56:50,729 - ExploitationExpert - INFO - res_population_costs: [15406, 15382, 15230, 15200, 15100, 14947, 14944, 14575, 14545, 14514, 14401, 14379]
2025-06-26 20:56:50,729 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 20:56:50,736 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:56:50,736 - ExploitationExpert - INFO - populations: [{'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 75, 82], 'cur_cost': 37960.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': array([ 72,  94,  29,  66,  74,  60,  15,  64,  17,  43,  34,  47,  71,
        62,   4,  35,  45,  99,   5,  22, 103,  90,  63,  92,  70,  38,
        85,  86,  14, 102,  76,  10,  98,  79, 104,  97,  82,  19,  23,
        49,  57,  96,  42,  61,  56,   8,  21,  53,  91,  69,  65,  58,
        11,  52,  67,   9,  78,  93,  18,  95,  68,  16,  89,  48,  80,
        39,  46,  84,  54,  27,  30,  44,  31,  20,  26,   6,  73, 101,
         3,  59,  77,  12,  50,  13,  36,  83,   1,  24,  28,  81,  40,
        25,  32,  88,   0,  75,  87,   2,  55,   7,  51,  41,  33,  37,
       100]), 'cur_cost': 125264.0}, {'tour': [51, 36, 98, 32, 15, 73, 54, 60, 62, 5, 61, 96, 89, 80, 84, 56, 43, 82, 38, 55, 19, 71, 23, 72, 34, 40, 85, 50, 86, 10, 79, 1, 7, 87, 59, 0, 100, 101, 90, 24, 31, 12, 20, 21, 44, 16, 48, 91, 78, 75, 49, 41, 102, 69, 27, 70, 67, 64, 94, 6, 35, 29, 14, 93, 92, 26, 77, 9, 45, 42, 58, 103, 57, 68, 99, 4, 8, 11, 22, 63, 97, 53, 33, 13, 25, 46, 65, 37, 18, 66, 28, 30, 81, 52, 39, 76, 17, 74, 104, 83, 3, 2, 88, 95, 47], 'cur_cost': 118080.0}]
2025-06-26 20:56:50,737 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:56:50,737 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 72, 'skip_rate': 0.041666666666666664, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 69, 'cache_hits': 37, 'similarity_calculations': 584, 'cache_hit_rate': 0.06335616438356165, 'cache_size': 547}}
2025-06-26 20:56:50,737 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:56:50,737 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:56:50,737 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:56:50,737 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:56:50,739 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 122177.0
2025-06-26 20:56:51,240 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 20:56:51,241 - ExploitationExpert - INFO - res_population_costs: [15406, 15382, 15230, 15200, 15100, 14947, 14944, 14575, 14545, 14514, 14401, 14379]
2025-06-26 20:56:51,241 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 20:56:51,246 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:56:51,246 - ExploitationExpert - INFO - populations: [{'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 75, 82], 'cur_cost': 37960.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}, {'tour': array([ 72,  94,  29,  66,  74,  60,  15,  64,  17,  43,  34,  47,  71,
        62,   4,  35,  45,  99,   5,  22, 103,  90,  63,  92,  70,  38,
        85,  86,  14, 102,  76,  10,  98,  79, 104,  97,  82,  19,  23,
        49,  57,  96,  42,  61,  56,   8,  21,  53,  91,  69,  65,  58,
        11,  52,  67,   9,  78,  93,  18,  95,  68,  16,  89,  48,  80,
        39,  46,  84,  54,  27,  30,  44,  31,  20,  26,   6,  73, 101,
         3,  59,  77,  12,  50,  13,  36,  83,   1,  24,  28,  81,  40,
        25,  32,  88,   0,  75,  87,   2,  55,   7,  51,  41,  33,  37,
       100]), 'cur_cost': 125264.0}, {'tour': array([ 75,  93,  11,  17, 100, 102,  80,  46,  14,  74,   6,  73,  54,
        16,  49,  26,  96,  85,  66,   7,  64,   9, 104,  10,  33,  56,
        63,   3,  15,  25,  86,  88,  34,  23,  69, 101,  52,  42,  84,
        81,  59,  97,  43,  58,  77, 103,  21,  48,  31,  22,  32,   1,
        94,  12,  30,  27,  62,  60,  61,  44,   2,  13,  41,  89,  72,
        99,  35,  92,  19,  50,   5,  28,  71,  95,  68,   0,  45,  82,
        65,  38,  20,   8,  79,  83,  18,  51,  91,  39,  29,  57,  37,
        76,  78,  98,  70,   4,  24,  40,  47,  90,  36,  55,  87,  67,
        53]), 'cur_cost': 122177.0}]
2025-06-26 20:56:51,249 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:56:51,249 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 73, 'skip_rate': 0.0410958904109589, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 70, 'cache_hits': 37, 'similarity_calculations': 585, 'cache_hit_rate': 0.06324786324786325, 'cache_size': 548}}
2025-06-26 20:56:51,249 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:56:51,249 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 75, 82], 'cur_cost': 37960.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 82, 75, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 41383.0}}]
2025-06-26 20:56:51,250 - __main__ - INFO - 进化阶段完成
2025-06-26 20:56:51,250 - __main__ - INFO - 开始评估阶段
2025-06-26 20:56:51,250 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:56:51,251 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:56:51,251 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 17099.0 → 37960.0 (Δ -20861.0)
- Mean cost: 91782.5 → 57508.2
- Diversity: 0.770 → 0.297 (Δ -0.4731)

#### Elite metrics
- Elite count: 0 → 12
- Best elite cost: 17099.0 → 14379 (Δ 2720.0)

#### Strategy performance
- Explore: 8 inds, success 62.5% , avg Δ 42866.75
- Exploit: 2 inds, success 50.0% , avg Δ -95.5

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [164593.0, 163098.0, 158735.0], diversity [0.8533333333333332, 0.9137777777777777, 0.8788888888888888]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:56:51,252 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:57:05,285 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 62.5,
      "average_delta": 42866.75,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -95.5,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore-heavy but effective, exploit underutilized"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_suggestions": "Increase exploit attempts slightly while maintaining strong exploration focus"
  }
}
```
2025-06-26 20:57:05,350 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:57:05,351 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 62.5,
      "average_delta": 42866.75,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -95.5,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore-heavy but effective, exploit underutilized"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_suggestions": "Increase exploit attempts slightly while maintaining strong exploration focus"
  }
}
```
2025-06-26 20:57:05,351 - __main__ - INFO - 评估阶段完成
2025-06-26 20:57:05,351 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 62.5,
      "average_delta": 42866.75,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -95.5,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "4:1 (explore:exploit)",
    "assessment": "explore-heavy but effective, exploit underutilized"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_suggestions": "Increase exploit attempts slightly while maintaining strong exploration focus"
  }
}
```
2025-06-26 20:57:05,352 - __main__ - INFO - 当前最佳适应度: 37960.0
2025-06-26 20:57:05,352 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\lin105_route_0.pkl
2025-06-26 20:57:05,352 - __main__ - INFO - lin105 开始进化第 2 代
2025-06-26 20:57:05,352 - __main__ - INFO - 开始分析阶段
2025-06-26 20:57:05,352 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:57:05,385 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 37960.0, 'max': 125264.0, 'mean': 57508.2, 'std': 33128.82245960457}, 'diversity': 0.37671957671957673, 'clusters': {'clusters': 3, 'cluster_sizes': [8, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:57:05,385 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 37960.0, 'max': 125264.0, 'mean': 57508.2, 'std': 33128.82245960457}, 'diversity_level': 0.37671957671957673, 'convergence_level': 0.0, 'clustering_info': {'clusters': 3, 'cluster_sizes': [8, 1, 1]}, 'coordinates': [[63, 71], [94, 71], [142, 370], [173, 1276], [205, 1213], [213, 69], [244, 69], [276, 630], [283, 732], [362, 69], [394, 69], [449, 370], [480, 1276], [512, 1213], [528, 157], [583, 630], [591, 732], [638, 654], [638, 496], [638, 314], [638, 142], [669, 142], [677, 315], [677, 496], [677, 654], [709, 654], [709, 496], [709, 315], [701, 142], [764, 220], [811, 189], [843, 173], [858, 370], [890, 1276], [921, 1213], [992, 630], [1000, 732], [1197, 1276], [1228, 1213], [1276, 205], [1299, 630], [1307, 732], [1362, 654], [1362, 496], [1362, 291], [1425, 654], [1425, 496], [1425, 291], [1417, 173], [1488, 291], [1488, 496], [1488, 654], [1551, 654], [1551, 496], [1551, 291], [1614, 291], [1614, 496], [1614, 654], [1732, 189], [1811, 1276], [1843, 1213], [1913, 630], [1921, 732], [2087, 370], [2118, 1276], [2150, 1213], [2189, 205], [2220, 189], [2220, 630], [2228, 732], [2244, 142], [2276, 315], [2276, 496], [2276, 654], [2315, 654], [2315, 496], [2315, 315], [2331, 142], [2346, 315], [2346, 496], [2346, 654], [2362, 142], [2402, 157], [2402, 220], [2480, 142], [2496, 370], [2528, 1276], [2559, 1213], [2630, 630], [2638, 732], [2756, 69], [2787, 69], [2803, 370], [2835, 1276], [2866, 1213], [2906, 69], [2937, 69], [2937, 630], [2945, 732], [3016, 1276], [3055, 69], [3087, 69], [606, 220], [1165, 370], [1780, 370]], 'distance_matrix': array([[   0.,   31.,  309., ...,  563., 1142., 1743.],
       [  31.,    0.,  303., ...,  533., 1112., 1712.],
       [ 309.,  303.,    0., ...,  488., 1023., 1638.],
       ...,
       [ 563.,  533.,  488., ...,    0.,  579., 1184.],
       [1142., 1112., 1023., ...,  579.,    0.,  615.],
       [1743., 1712., 1638., ..., 1184.,  615.,    0.]])}
2025-06-26 20:57:05,385 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:57:05,385 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:57:05,385 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:57:05,391 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:57:05,391 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:57:05,391 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:57:05,391 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:57:05,391 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:57:05,391 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:57:05,396 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:57:05,396 - __main__ - INFO - 精英专家分析报告: {'elite_count': 12, 'elite_common_features': {'common_edges': {'(49, 54)': 0.75, '(54, 55)': 0.75, '(55, 58)': 0.75}, 'common_edge_ratio': 0.02857142857142857}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 23581.0, 'avg_gap': 42622.95}, 'structure_gap': {'unique_elite_edges': 256, 'unique_pop_edges': 270, 'common_edges': 45}}, 'elite_diversity': {'diversity_score': 0.16666666666666663}}
2025-06-26 20:57:05,397 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:57:05,397 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:57:05,400 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:57:05,400 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 37960.0, mean 57508.2, max 125264.0, std 33128.82245960457
- diversity: 0.37671957671957673
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [163098.0, 158735.0, 37960.0] (Δ 62569.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [63.0, 69.0, 3087.0, 1276.0]
- centroid/spread: [1538.962, 525.343] | rms 953.602
- density_grid(3x3): [[20, 10, 20], [12, 14, 12], [6, 4, 7]]
- edge_len_stats(min,q1,med,q3,max): [31.0, 664.0, 1070.0, 1638.0, 3189.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [0, 2, 39, 4, 14, 31, 13, 1]
- nn_median_dist: 63.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:57:05,401 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:57:33,273 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 31.0 to 3189.0, std dev ~33128.8)",
    "modality": "Potentially multimodal (high cost variance, recent best costs show large jumps)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio 0.249 suggests some misleading long edges)"
  },
  "population_state": {
    "coverage": "Patchy (density_grid shows uneven distribution with high-density cells near edges)",
    "convergence": "Early stage (diversity 0.377, convergence 0.0)",
    "quality_spread": "Wide (min 37960 to max 125264, elite_count 0)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Northwest quadrant (density_grid[2][0]=6) and central-east (density_grid[1][2]=12)",
    "low_density_zones": "South-central region (density_grid[2][1]=4)"
  },
  "opportunity_regions": {
    "high_density_clusters": [
      "Northeast (density_grid[0][2]=20)",
      "Southwest (density_grid[2][0]=6 combined with direction_hist sector 2 dominance)"
    ],
    "promising_directions": "Sector 2 (39 edges) and sector 5 (31 edges) from direction_hist"
  },
  "evolution_phase": "Exploration (no elites, high diversity, recent Δ62569 cost improvements)",
  "evolution_direction": [
    "Prioritize 2-opt near high-density clusters",
    "Apply directed mutation favoring sectors 2/5 edges",
    "Introduce distance-aware crossover (nn_median_dist=63.0)",
    "Suppress long-edge generation in northwest quadrant"
  ]
}
```
2025-06-26 20:57:33,273 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:57:33,278 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 31.0 to 3189.0, std dev ~33128.8)', 'modality': 'Potentially multimodal (high cost variance, recent best costs show large jumps)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio 0.249 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Patchy (density_grid shows uneven distribution with high-density cells near edges)', 'convergence': 'Early stage (diversity 0.377, convergence 0.0)', 'quality_spread': 'Wide (min 37960 to max 125264, elite_count 0)'}, 'difficult_regions': {'long_edge_corridors': 'Northwest quadrant (density_grid[2][0]=6) and central-east (density_grid[1][2]=12)', 'low_density_zones': 'South-central region (density_grid[2][1]=4)'}, 'opportunity_regions': {'high_density_clusters': ['Northeast (density_grid[0][2]=20)', 'Southwest (density_grid[2][0]=6 combined with direction_hist sector 2 dominance)'], 'promising_directions': 'Sector 2 (39 edges) and sector 5 (31 edges) from direction_hist'}, 'evolution_phase': 'Exploration (no elites, high diversity, recent Δ62569 cost improvements)', 'evolution_direction': ['Prioritize 2-opt near high-density clusters', 'Apply directed mutation favoring sectors 2/5 edges', 'Introduce distance-aware crossover (nn_median_dist=63.0)', 'Suppress long-edge generation in northwest quadrant']}
2025-06-26 20:57:33,278 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:57:33,278 - __main__ - INFO - 分析阶段完成
2025-06-26 20:57:33,278 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 31.0 to 3189.0, std dev ~33128.8)', 'modality': 'Potentially multimodal (high cost variance, recent best costs show large jumps)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio 0.249 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Patchy (density_grid shows uneven distribution with high-density cells near edges)', 'convergence': 'Early stage (diversity 0.377, convergence 0.0)', 'quality_spread': 'Wide (min 37960 to max 125264, elite_count 0)'}, 'difficult_regions': {'long_edge_corridors': 'Northwest quadrant (density_grid[2][0]=6) and central-east (density_grid[1][2]=12)', 'low_density_zones': 'South-central region (density_grid[2][1]=4)'}, 'opportunity_regions': {'high_density_clusters': ['Northeast (density_grid[0][2]=20)', 'Southwest (density_grid[2][0]=6 combined with direction_hist sector 2 dominance)'], 'promising_directions': 'Sector 2 (39 edges) and sector 5 (31 edges) from direction_hist'}, 'evolution_phase': 'Exploration (no elites, high diversity, recent Δ62569 cost improvements)', 'evolution_direction': ['Prioritize 2-opt near high-density clusters', 'Apply directed mutation favoring sectors 2/5 edges', 'Introduce distance-aware crossover (nn_median_dist=63.0)', 'Suppress long-edge generation in northwest quadrant']}
2025-06-26 20:57:33,278 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:57:33,278 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:57:33,278 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 37960.0
  • mean_cost: 57508.2
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 31.0 to 3189.0, std dev ~33128.8), modality Potentially multimodal (high cost variance, recent best costs show large jumps), deceptiveness Possible deceptive regions (long_edge_ratio 0.249 suggests some misleading long edges)
- Evolution phase: Exploration (no elites, high diversity, recent Δ62569 cost improvements)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_zones
- Opportunity regions (# 2): high_density_cluster, promising_directions
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 62.5,
     ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:57:33,278 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:57:33,280 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:57:44,286 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early stage with low diversity and exploration success; balance focus suggests 70% explore to discover promising regions while exploiting top 30%."
}
```
2025-06-26 20:57:44,286 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:57:44,286 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:57:44,286 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early stage with low diversity and exploration success; balance focus suggests 70% explore to discover promising regions while exploiting top 30%."
}
```
2025-06-26 20:57:44,286 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:57:44,286 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:57:44,286 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early stage with low diversity and exploration success; balance focus suggests 70% explore to discover promising regions while exploiting top 30%."
}
```
2025-06-26 20:57:44,286 - __main__ - INFO - 开始进化阶段
2025-06-26 20:57:44,286 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:57:44,286 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:57:44,286 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41383.0 (rank 10.0%)
- Population diversity: 0.2971153846153846
- Evolution phase: Exploration (no elites, high diversity, recent Δ62569 cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_direc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:57:44,286 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:57:44,286 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:58:06,733 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95],
  "targeted_regions": "high_density_cl, promising_direc",
  "strategy_comment": "Leverage modular jumps to explore sparse cells near opportunities"
}
```
2025-06-26 20:58:06,733 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:58:06,733 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87110.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95]
2025-06-26 20:58:06,733 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}
2025-06-26 20:58:06,733 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:58:06,733 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:58:06,733 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41383.0 (rank 10.0%)
- Population diversity: 0.39326923076923076
- Evolution phase: Exploration (no elites, high diversity, recent Δ62569 cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_direc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:58:06,733 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:58:06,733 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:58:28,719 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104],
  "targeted_regions": "high_density_cl, promising_direc, adjacent low-density cells",
  "strategy_comment": "Cluster hops in opportunity zones, skip long edges"
}
```
2025-06-26 20:58:28,720 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:58:28,720 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109830.0, 路径: [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104]
2025-06-26 20:58:28,720 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104], 'cur_cost': 109830.0}
2025-06-26 20:58:28,721 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:58:28,721 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:58:28,721 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41383.0 (rank 10.0%)
- Population diversity: 0.4653846153846154
- Evolution phase: Exploration (no elites, high diversity, recent Δ62569 cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_direc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:58:28,721 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:58:28,721 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:58:51,334 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95],
  "targeted_regions": "high_density_cl, promising_direc",
  "strategy_comment": "Modular traversal to balance exploration and cost"
}
```
2025-06-26 20:58:51,334 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:58:51,334 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87110.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95]
2025-06-26 20:58:51,334 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}
2025-06-26 20:58:51,337 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:58:51,337 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:58:51,337 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 37960.0 (rank 0.0%)
- Population diversity: 0.4653846153846154
- Evolution phase: Exploration (no elites, high diversity, recent Δ62569 cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_direc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:58:51,337 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:58:51,337 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:59:13,604 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104],
  "targeted_regions": "high_density_cl, promising_direc",
  "strategy_comment": "Prioritize opportunity regions, avoid long edges"
}
```
2025-06-26 20:59:13,604 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:59:13,604 - ExplorationExpert - INFO - 探索路径生成完成，成本: 122705.0, 路径: [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 101, 102, 103]
2025-06-26 20:59:13,604 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 101, 102, 103], 'cur_cost': 122705.0}
2025-06-26 20:59:13,604 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:59:13,604 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:59:13,604 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41383.0 (rank 0.0%)
- Population diversity: 0.5201923076923077
- Evolution phase: Exploration (no elites, high diversity, recent Δ62569 cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_direc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:59:13,609 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:59:13,609 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:59:36,000 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103],
  "targeted_regions": "high_density_cl, promising_direc",
  "strategy_comment": "Leverage grid-like traversal for diversity, avoid long edges"
}
```
2025-06-26 20:59:36,000 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:59:36,005 - ExplorationExpert - INFO - 探索路径生成完成，成本: 86026.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103]
2025-06-26 20:59:36,005 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103], 'cur_cost': 86026.0}
2025-06-26 20:59:36,005 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:59:36,005 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:59:36,005 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41383.0 (rank 0.0%)
- Population diversity: 0.5298076923076923
- Evolution phase: Exploration (no elites, high diversity, recent Δ62569 cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_direc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:59:36,007 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:59:36,007 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:59:57,317 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95],
  "targeted_regions": "high_density_cl, promising_direc",
  "strategy_comment": "Leverage modular jumps to explore sparse cells"
}
```
2025-06-26 20:59:57,323 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:59:57,323 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87110.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95]
2025-06-26 20:59:57,323 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}
2025-06-26 20:59:57,323 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:59:57,323 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:59:57,326 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41383.0 (rank 0.0%)
- Population diversity: 0.5298076923076923
- Evolution phase: Exploration (no elites, high diversity, recent Δ62569 cost improvements)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, promising_direc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:59:57,326 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:59:57,326 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:00:18,865 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95],
  "targeted_regions": "high_density_cl, promising_direc",
  "strategy_comment": "Leverage modular jumps to explore sparse cells"
}
```
2025-06-26 21:00:18,865 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:00:18,865 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87110.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95]
2025-06-26 21:00:18,865 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}
2025-06-26 21:00:18,865 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 21:00:18,865 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:00:18,865 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:00:18,865 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 124879.0
2025-06-26 21:00:19,367 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:00:19,367 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:00:19,367 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:00:19,375 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:00:19,375 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104], 'cur_cost': 109830.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 101, 102, 103], 'cur_cost': 122705.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103], 'cur_cost': 86026.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([ 33,  69,  61,  94,  44,  27,  64,  35,  30,  63,  59,  47,  95,
        32,   2,   0,  37,  28,  15,  70,  68,  89,  48,  41,  17,  78,
        96,   7,  84,  65,  40,  24,  18,  46,   4, 104,  14,  98,  71,
        49,  52,  43,  13,  82,  76,   3,  51,  83,  92,  99, 102,  25,
        60,   1,  93, 103,  26,  67,   8,  34,  38,  72,   9,  55,  45,
        90,  12,  73, 101,  80,  54,  75, 100,  79,  16,  36,  19,  77,
        62,  66,   6,  85,  57,  11,  58,  87,  74,  10,  21,   5,  22,
        56,  31,  23,  53,  91,  29,  97,  42,  86,  50,  20,  88,  81,
        39]), 'cur_cost': 124879.0}, {'tour': array([ 72,  94,  29,  66,  74,  60,  15,  64,  17,  43,  34,  47,  71,
        62,   4,  35,  45,  99,   5,  22, 103,  90,  63,  92,  70,  38,
        85,  86,  14, 102,  76,  10,  98,  79, 104,  97,  82,  19,  23,
        49,  57,  96,  42,  61,  56,   8,  21,  53,  91,  69,  65,  58,
        11,  52,  67,   9,  78,  93,  18,  95,  68,  16,  89,  48,  80,
        39,  46,  84,  54,  27,  30,  44,  31,  20,  26,   6,  73, 101,
         3,  59,  77,  12,  50,  13,  36,  83,   1,  24,  28,  81,  40,
        25,  32,  88,   0,  75,  87,   2,  55,   7,  51,  41,  33,  37,
       100]), 'cur_cost': 125264.0}, {'tour': array([ 75,  93,  11,  17, 100, 102,  80,  46,  14,  74,   6,  73,  54,
        16,  49,  26,  96,  85,  66,   7,  64,   9, 104,  10,  33,  56,
        63,   3,  15,  25,  86,  88,  34,  23,  69, 101,  52,  42,  84,
        81,  59,  97,  43,  58,  77, 103,  21,  48,  31,  22,  32,   1,
        94,  12,  30,  27,  62,  60,  61,  44,   2,  13,  41,  89,  72,
        99,  35,  92,  19,  50,   5,  28,  71,  95,  68,   0,  45,  82,
        65,  38,  20,   8,  79,  83,  18,  51,  91,  39,  29,  57,  37,
        76,  78,  98,  70,   4,  24,  40,  47,  90,  36,  55,  87,  67,
        53]), 'cur_cost': 122177.0}]
2025-06-26 21:00:19,377 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:00:19,378 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 74, 'skip_rate': 0.04054054054054054, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 71, 'cache_hits': 37, 'similarity_calculations': 587, 'cache_hit_rate': 0.06303236797274275, 'cache_size': 550}}
2025-06-26 21:00:19,379 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 21:00:19,379 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 21:00:19,379 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:00:19,379 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:00:19,379 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 119303.0
2025-06-26 21:00:19,880 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:00:19,882 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:00:19,882 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:00:19,888 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:00:19,888 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104], 'cur_cost': 109830.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 101, 102, 103], 'cur_cost': 122705.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103], 'cur_cost': 86026.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([ 33,  69,  61,  94,  44,  27,  64,  35,  30,  63,  59,  47,  95,
        32,   2,   0,  37,  28,  15,  70,  68,  89,  48,  41,  17,  78,
        96,   7,  84,  65,  40,  24,  18,  46,   4, 104,  14,  98,  71,
        49,  52,  43,  13,  82,  76,   3,  51,  83,  92,  99, 102,  25,
        60,   1,  93, 103,  26,  67,   8,  34,  38,  72,   9,  55,  45,
        90,  12,  73, 101,  80,  54,  75, 100,  79,  16,  36,  19,  77,
        62,  66,   6,  85,  57,  11,  58,  87,  74,  10,  21,   5,  22,
        56,  31,  23,  53,  91,  29,  97,  42,  86,  50,  20,  88,  81,
        39]), 'cur_cost': 124879.0}, {'tour': array([  5, 103,  96,  64,  18,   2,  10,  59,  40,  67, 102,  83,  46,
        55,  22,  21,  38,  72,  24,  66,  98,  82,  15,  73,  52,  79,
        95,  87,  51, 100,  33,  76,  43,  28,  88,  81,  70,  99,   0,
        37,  30,  14,  57,  75, 104,  78,  92, 101,  29,  31,  20,  26,
        11,  84,  32,  49,   1,  69,  53,  39,  35,  36,  60,  93,   4,
         7,  94,  13,  85,  97,  23,  86,  61,  50,   9,  44,  17,  65,
        68,  74,  58,  27,   3,  80,  47,  71,  90,  54,  42,  89,  56,
         6,  34,  91,  41,   8,  62,  12,  16,  19,  77,  63,  45,  25,
        48]), 'cur_cost': 119303.0}, {'tour': array([ 75,  93,  11,  17, 100, 102,  80,  46,  14,  74,   6,  73,  54,
        16,  49,  26,  96,  85,  66,   7,  64,   9, 104,  10,  33,  56,
        63,   3,  15,  25,  86,  88,  34,  23,  69, 101,  52,  42,  84,
        81,  59,  97,  43,  58,  77, 103,  21,  48,  31,  22,  32,   1,
        94,  12,  30,  27,  62,  60,  61,  44,   2,  13,  41,  89,  72,
        99,  35,  92,  19,  50,   5,  28,  71,  95,  68,   0,  45,  82,
        65,  38,  20,   8,  79,  83,  18,  51,  91,  39,  29,  57,  37,
        76,  78,  98,  70,   4,  24,  40,  47,  90,  36,  55,  87,  67,
        53]), 'cur_cost': 122177.0}]
2025-06-26 21:00:19,891 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:00:19,892 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 75, 'skip_rate': 0.04, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 72, 'cache_hits': 37, 'similarity_calculations': 590, 'cache_hit_rate': 0.06271186440677966, 'cache_size': 553}}
2025-06-26 21:00:19,892 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 21:00:19,892 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 21:00:19,893 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:00:19,893 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:00:19,893 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 121491.0
2025-06-26 21:00:20,397 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:00:20,397 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:00:20,397 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:00:20,404 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:00:20,404 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104], 'cur_cost': 109830.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 101, 102, 103], 'cur_cost': 122705.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103], 'cur_cost': 86026.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([ 33,  69,  61,  94,  44,  27,  64,  35,  30,  63,  59,  47,  95,
        32,   2,   0,  37,  28,  15,  70,  68,  89,  48,  41,  17,  78,
        96,   7,  84,  65,  40,  24,  18,  46,   4, 104,  14,  98,  71,
        49,  52,  43,  13,  82,  76,   3,  51,  83,  92,  99, 102,  25,
        60,   1,  93, 103,  26,  67,   8,  34,  38,  72,   9,  55,  45,
        90,  12,  73, 101,  80,  54,  75, 100,  79,  16,  36,  19,  77,
        62,  66,   6,  85,  57,  11,  58,  87,  74,  10,  21,   5,  22,
        56,  31,  23,  53,  91,  29,  97,  42,  86,  50,  20,  88,  81,
        39]), 'cur_cost': 124879.0}, {'tour': array([  5, 103,  96,  64,  18,   2,  10,  59,  40,  67, 102,  83,  46,
        55,  22,  21,  38,  72,  24,  66,  98,  82,  15,  73,  52,  79,
        95,  87,  51, 100,  33,  76,  43,  28,  88,  81,  70,  99,   0,
        37,  30,  14,  57,  75, 104,  78,  92, 101,  29,  31,  20,  26,
        11,  84,  32,  49,   1,  69,  53,  39,  35,  36,  60,  93,   4,
         7,  94,  13,  85,  97,  23,  86,  61,  50,   9,  44,  17,  65,
        68,  74,  58,  27,   3,  80,  47,  71,  90,  54,  42,  89,  56,
         6,  34,  91,  41,   8,  62,  12,  16,  19,  77,  63,  45,  25,
        48]), 'cur_cost': 119303.0}, {'tour': array([ 75,  52,  19,  64,  35,  17,  79,  16,  69,  21,  66,  53, 103,
        49,  83,   4, 102,   3,  15,  30,  48,  98,  87,  10,  68,  76,
        33,  44,   8,  28,   1,  22,  54,  29,  77,  73,   7,  42,  43,
        93, 101,  32,  86,   5,  57, 100,  85,  84,   0,  92,  82,  88,
        91,  20,  26,  78,  60,  81,  36,  46,  37,  90,  72,  55,  27,
         6,  40,  38,  41,  47,  71,  23,  65,  31,   2,  89,  34,  12,
        50,  56,  13,  24,  11,  97,  74,  70,   9,  61,  14,  45,  80,
        96,  67,  51,  63, 104,  99,  59,  62,  95,  18,  94,  58,  39,
        25]), 'cur_cost': 121491.0}]
2025-06-26 21:00:20,406 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:00:20,406 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 76, 'skip_rate': 0.039473684210526314, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 73, 'cache_hits': 37, 'similarity_calculations': 594, 'cache_hit_rate': 0.06228956228956229, 'cache_size': 557}}
2025-06-26 21:00:20,407 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 21:00:20,407 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104], 'cur_cost': 109830.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 101, 102, 103], 'cur_cost': 122705.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103], 'cur_cost': 86026.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}}]
2025-06-26 21:00:20,408 - __main__ - INFO - 进化阶段完成
2025-06-26 21:00:20,408 - __main__ - INFO - 开始评估阶段
2025-06-26 21:00:20,408 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 21:00:20,409 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 21:00:20,410 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 37960.0 → 86026.0 (Δ -48066.0)
- Mean cost: 57508.2 → 103267.4
- Diversity: 0.297 → 0.530 (Δ 0.2327)

#### Elite metrics
- Elite count: 12 → 12
- Best elite cost: 14379 → 14379 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 0.0% , avg Δ -54391.86
- Exploit: 3 inds, success 66.7% , avg Δ -25616.33

#### Other indicators
- No-change individuals: 0
- Regression individuals: 8
- Historical trends: Last 3 iterations: costs [163098.0, 158735.0, 37960.0], diversity [0.9137777777777777, 0.8788888888888888, 0.37671957671957673]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 21:00:20,410 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:00:39,420 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early-mid evolution with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "cost_impact": -54391.86,
      "contribution_to_elite": "none (no success)"
    },
    "exploit": {
      "success_rate": 66.7,
      "cost_impact": -25616.33,
      "contribution_to_elite": "primary (only improving strategy)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "issue": "explore completely ineffective despite high allocation",
    "diversity_trend": "rising sharply (0.297→0.530) without elite improvement"
  },
  "recommendations": {
    "immediate_actions": {
      "explore_ratio": 0.4,
      "exploit_ratio": 0.6,
      "diversity_weight": 0.7,
      "mutation_intensity": "increase by 20%"
    },
    "monitoring_suggestions": [
      "Track explore success rate for 2 iterations",
      "If diversity exceeds 0.6 without elite improvement, implement elitism"
    ]
  }
}
```
2025-06-26 21:00:39,488 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 21:00:39,488 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early-mid evolution with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "cost_impact": -54391.86,
      "contribution_to_elite": "none (no success)"
    },
    "exploit": {
      "success_rate": 66.7,
      "cost_impact": -25616.33,
      "contribution_to_elite": "primary (only improving strategy)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "issue": "explore completely ineffective despite high allocation",
    "diversity_trend": "rising sharply (0.297→0.530) without elite improvement"
  },
  "recommendations": {
    "immediate_actions": {
      "explore_ratio": 0.4,
      "exploit_ratio": 0.6,
      "diversity_weight": 0.7,
      "mutation_intensity": "increase by 20%"
    },
    "monitoring_suggestions": [
      "Track explore success rate for 2 iterations",
      "If diversity exceeds 0.6 without elite improvement, implement elitism"
    ]
  }
}
```
2025-06-26 21:00:39,488 - __main__ - INFO - 评估阶段完成
2025-06-26 21:00:39,488 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early-mid evolution with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "cost_impact": -54391.86,
      "contribution_to_elite": "none (no success)"
    },
    "exploit": {
      "success_rate": 66.7,
      "cost_impact": -25616.33,
      "contribution_to_elite": "primary (only improving strategy)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "issue": "explore completely ineffective despite high allocation",
    "diversity_trend": "rising sharply (0.297→0.530) without elite improvement"
  },
  "recommendations": {
    "immediate_actions": {
      "explore_ratio": 0.4,
      "exploit_ratio": 0.6,
      "diversity_weight": 0.7,
      "mutation_intensity": "increase by 20%"
    },
    "monitoring_suggestions": [
      "Track explore success rate for 2 iterations",
      "If diversity exceeds 0.6 without elite improvement, implement elitism"
    ]
  }
}
```
2025-06-26 21:00:39,488 - __main__ - INFO - 当前最佳适应度: 86026.0
2025-06-26 21:00:39,488 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\lin105_route_1.pkl
2025-06-26 21:00:39,488 - __main__ - INFO - lin105 开始进化第 3 代
2025-06-26 21:00:39,488 - __main__ - INFO - 开始分析阶段
2025-06-26 21:00:39,488 - StatsExpert - INFO - 开始统计分析
2025-06-26 21:00:39,529 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 86026.0, 'max': 124879.0, 'mean': 103267.4, 'std': 16788.98896419912}, 'diversity': 0.737989417989418, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 21:00:39,529 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 86026.0, 'max': 124879.0, 'mean': 103267.4, 'std': 16788.98896419912}, 'diversity_level': 0.737989417989418, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'coordinates': [[63, 71], [94, 71], [142, 370], [173, 1276], [205, 1213], [213, 69], [244, 69], [276, 630], [283, 732], [362, 69], [394, 69], [449, 370], [480, 1276], [512, 1213], [528, 157], [583, 630], [591, 732], [638, 654], [638, 496], [638, 314], [638, 142], [669, 142], [677, 315], [677, 496], [677, 654], [709, 654], [709, 496], [709, 315], [701, 142], [764, 220], [811, 189], [843, 173], [858, 370], [890, 1276], [921, 1213], [992, 630], [1000, 732], [1197, 1276], [1228, 1213], [1276, 205], [1299, 630], [1307, 732], [1362, 654], [1362, 496], [1362, 291], [1425, 654], [1425, 496], [1425, 291], [1417, 173], [1488, 291], [1488, 496], [1488, 654], [1551, 654], [1551, 496], [1551, 291], [1614, 291], [1614, 496], [1614, 654], [1732, 189], [1811, 1276], [1843, 1213], [1913, 630], [1921, 732], [2087, 370], [2118, 1276], [2150, 1213], [2189, 205], [2220, 189], [2220, 630], [2228, 732], [2244, 142], [2276, 315], [2276, 496], [2276, 654], [2315, 654], [2315, 496], [2315, 315], [2331, 142], [2346, 315], [2346, 496], [2346, 654], [2362, 142], [2402, 157], [2402, 220], [2480, 142], [2496, 370], [2528, 1276], [2559, 1213], [2630, 630], [2638, 732], [2756, 69], [2787, 69], [2803, 370], [2835, 1276], [2866, 1213], [2906, 69], [2937, 69], [2937, 630], [2945, 732], [3016, 1276], [3055, 69], [3087, 69], [606, 220], [1165, 370], [1780, 370]], 'distance_matrix': array([[   0.,   31.,  309., ...,  563., 1142., 1743.],
       [  31.,    0.,  303., ...,  533., 1112., 1712.],
       [ 309.,  303.,    0., ...,  488., 1023., 1638.],
       ...,
       [ 563.,  533.,  488., ...,    0.,  579., 1184.],
       [1142., 1112., 1023., ...,  579.,    0.,  615.],
       [1743., 1712., 1638., ..., 1184.,  615.,    0.]])}
2025-06-26 21:00:39,529 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 21:00:39,529 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 21:00:39,529 - PathExpert - INFO - 开始路径结构分析
2025-06-26 21:00:39,539 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 21:00:39,539 - PathExpert - INFO - 路径结构分析完成
2025-06-26 21:00:39,539 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 21:00:39,539 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 21:00:39,539 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 21:00:39,543 - EliteExpert - INFO - 开始精英解分析
2025-06-26 21:00:39,547 - EliteExpert - INFO - 精英解分析完成
2025-06-26 21:00:39,547 - __main__ - INFO - 精英专家分析报告: {'elite_count': 12, 'elite_common_features': {'common_edges': {'(49, 54)': 0.75, '(54, 55)': 0.75, '(55, 58)': 0.75}, 'common_edge_ratio': 0.02857142857142857}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 71647.0, 'avg_gap': 88382.15}, 'structure_gap': {'unique_elite_edges': 292, 'unique_pop_edges': 567, 'common_edges': 9}}, 'elite_diversity': {'diversity_score': 0.16666666666666663}}
2025-06-26 21:00:39,547 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 21:00:39,547 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 21:00:39,547 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 21:00:39,547 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 86026.0, mean 103267.4, max 124879.0, std 16788.98896419912
- diversity: 0.737989417989418
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [158735.0, 37960.0, 86026.0] (Δ 60387.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [63.0, 69.0, 3087.0, 1276.0]
- centroid/spread: [1538.962, 525.343] | rms 953.602
- density_grid(3x3): [[20, 10, 20], [12, 14, 12], [6, 4, 7]]
- edge_len_stats(min,q1,med,q3,max): [31.0, 664.0, 1070.0, 1638.0, 3189.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [0, 2, 39, 4, 14, 31, 13, 1]
- nn_median_dist: 63.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 21:00:39,547 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:00:59,283 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "multimodal",
    "deceptiveness": "low",
    "edge_length_variability": "high",
    "long_edge_ratio": "moderate"
  },
  "population_state": {
    "coverage": "moderate",
    "convergence": "none",
    "diversity": "high",
    "density_distribution": "uneven",
    "spatial_spread": "wide"
  },
  "difficult_regions": {
    "low_density_cells": ["bottom-left", "bottom-right"],
    "long_edge_corridors": ["top-right to bottom-left"],
    "potential_barriers": ["north-south axis"]
  },
  "opportunity_regions": {
    "high_density_cells": ["top-left", "center", "top-right"],
    "short_edge_clusters": ["center region"],
    "potential_hubs": ["node 0 (fixed position)"]
  },
  "evolution_phase": "exploration",
  "evolution_direction": [
    "Increase exploitation in high-density opportunity regions",
    "Maintain exploration in difficult regions",
    "Consider edge recombination operators focusing on short-edge clusters",
    "Implement local search around fixed nodes",
    "Monitor direction histograms for potential path optimization"
  ]
}
```
2025-06-26 21:00:59,283 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 21:00:59,285 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high', 'long_edge_ratio': 'moderate'}, 'population_state': {'coverage': 'moderate', 'convergence': 'none', 'diversity': 'high', 'density_distribution': 'uneven', 'spatial_spread': 'wide'}, 'difficult_regions': {'low_density_cells': ['bottom-left', 'bottom-right'], 'long_edge_corridors': ['top-right to bottom-left'], 'potential_barriers': ['north-south axis']}, 'opportunity_regions': {'high_density_cells': ['top-left', 'center', 'top-right'], 'short_edge_clusters': ['center region'], 'potential_hubs': ['node 0 (fixed position)']}, 'evolution_phase': 'exploration', 'evolution_direction': ['Increase exploitation in high-density opportunity regions', 'Maintain exploration in difficult regions', 'Consider edge recombination operators focusing on short-edge clusters', 'Implement local search around fixed nodes', 'Monitor direction histograms for potential path optimization']}
2025-06-26 21:00:59,285 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 21:00:59,285 - __main__ - INFO - 分析阶段完成
2025-06-26 21:00:59,285 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high', 'long_edge_ratio': 'moderate'}, 'population_state': {'coverage': 'moderate', 'convergence': 'none', 'diversity': 'high', 'density_distribution': 'uneven', 'spatial_spread': 'wide'}, 'difficult_regions': {'low_density_cells': ['bottom-left', 'bottom-right'], 'long_edge_corridors': ['top-right to bottom-left'], 'potential_barriers': ['north-south axis']}, 'opportunity_regions': {'high_density_cells': ['top-left', 'center', 'top-right'], 'short_edge_clusters': ['center region'], 'potential_hubs': ['node 0 (fixed position)']}, 'evolution_phase': 'exploration', 'evolution_direction': ['Increase exploitation in high-density opportunity regions', 'Maintain exploration in difficult regions', 'Consider edge recombination operators focusing on short-edge clusters', 'Implement local search around fixed nodes', 'Monitor direction histograms for potential path optimization']}
2025-06-26 21:00:59,286 - __main__ - INFO - 开始策略分配阶段
2025-06-26 21:00:59,286 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 21:00:59,286 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 86026.0
  • mean_cost: 103267.4
- Search-space: ruggedness moderate, modality multimodal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): low_density_cells, long_edge_corridors...
- Opportunity regions (# 3): high_density_cells, short_edge_clusters...
- Last-iteration feedback: ```json
{
  "evolution_stage": "early-mid evolution with signs of stagnation",
  "strategy_effectiveness": {
    "explor...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 21:00:59,286 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 21:00:59,286 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:01:09,801 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest prioritizing exploration. Top 60% explore to diversify; bottom 40% exploit existing high-density regions."
}
```
2025-06-26 21:01:09,801 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 21:01:09,802 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 21:01:09,802 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest prioritizing exploration. Top 60% explore to diversify; bottom 40% exploit existing high-density regions."
}
```
2025-06-26 21:01:09,802 - __main__ - INFO - 策略分配阶段完成
2025-06-26 21:01:09,802 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 21:01:09,802 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest prioritizing exploration. Top 60% explore to diversify; bottom 40% exploit existing high-density regions."
}
```
2025-06-26 21:01:09,802 - __main__ - INFO - 开始进化阶段
2025-06-26 21:01:09,802 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 21:01:09,802 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:01:09,802 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 87110.0 (rank 10.0%)
- Population diversity: 0.5298076923076923
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:01:09,802 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:01:09,802 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:01:32,500 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104],
  "targeted_regions": "high_density_ce, short_edge_clus, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of opportunity regions with sparse adjacency"
}
```
2025-06-26 21:01:32,501 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:01:32,501 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109830.0, 路径: [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104]
2025-06-26 21:01:32,501 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104], 'cur_cost': 109830.0}
2025-06-26 21:01:32,501 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 21:01:32,501 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:01:32,501 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 109830.0 (rank 40.0%)
- Population diversity: 0.5298076923076923
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:01:32,501 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:01:32,501 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:01:55,182 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Structured leapfrog to explore sparse cells"
}
```
2025-06-26 21:01:55,182 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:01:55,182 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87110.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95]
2025-06-26 21:01:55,182 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}
2025-06-26 21:01:55,182 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 21:01:55,182 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:01:55,182 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 87110.0 (rank 10.0%)
- Population diversity: 0.5298076923076923
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:01:55,182 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:01:55,182 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:02:24,922 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Leverage modular jumps to explore sparse cells"
}
```
2025-06-26 21:02:24,923 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:02:24,923 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87110.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95]
2025-06-26 21:02:24,924 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}
2025-06-26 21:02:24,924 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 21:02:24,924 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:02:24,925 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 122705.0 (rank 80.0%)
- Population diversity: 0.5298076923076923
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:02:24,925 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:02:24,926 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:02:47,363 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Leverage clustered nodes with short edges, skip long corridors"
}
```
2025-06-26 21:02:47,364 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:02:47,364 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87110.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95]
2025-06-26 21:02:47,364 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}
2025-06-26 21:02:47,365 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 21:02:47,365 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:02:47,365 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 86026.0 (rank 0.0%)
- Population diversity: 0.4778846153846154
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:02:47,365 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:02:47,365 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:03:08,916 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 34, 56, 78, 90, 1, 23, 45, 67, 89, 2, 24, 46, 68, 80, 3, 25, 47, 69, 81, 4, 26, 48, 70, 82, 5, 27, 49, 71, 83, 6, 28, 50, 72, 84, 7, 29, 51, 73, 85, 8, 30, 52, 74, 86, 9, 31, 53, 75, 87, 10, 32, 54, 76, 88, 11, 33, 55, 77, 79, 13, 35, 57, 59, 14, 36, 58, 60, 15, 37, 61, 62, 16, 38, 63, 64, 17, 39, 65, 66, 18, 40, 41, 19, 42, 43, 20, 44, 21, 22, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104],
  "targeted_regions": "high_density_ce, short_edge_clus, adjacent low-density cells",
  "strategy_comment": "Prioritize opportunity regions with sparse cell adjacency, avoid long edges"
}
```
2025-06-26 21:03:08,916 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:03:08,916 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99688.0, 路径: [0, 12, 34, 56, 78, 90, 1, 23, 45, 67, 89, 2, 24, 46, 68, 80, 3, 25, 47, 69, 81, 4, 26, 48, 70, 82, 5, 27, 49, 71, 83, 6, 28, 50, 72, 84, 7, 29, 51, 73, 85, 8, 30, 52, 74, 86, 9, 31, 53, 75, 87, 10, 32, 54, 76, 88, 11, 33, 55, 77, 79, 13, 35, 57, 59, 14, 36, 58, 60, 15, 37, 61, 62, 16, 38, 63, 64, 17, 39, 65, 66, 18, 40, 41, 19, 42, 43, 20, 44, 21, 22, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104]
2025-06-26 21:03:08,916 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 34, 56, 78, 90, 1, 23, 45, 67, 89, 2, 24, 46, 68, 80, 3, 25, 47, 69, 81, 4, 26, 48, 70, 82, 5, 27, 49, 71, 83, 6, 28, 50, 72, 84, 7, 29, 51, 73, 85, 8, 30, 52, 74, 86, 9, 31, 53, 75, 87, 10, 32, 54, 76, 88, 11, 33, 55, 77, 79, 13, 35, 57, 59, 14, 36, 58, 60, 15, 37, 61, 62, 16, 38, 63, 64, 17, 39, 65, 66, 18, 40, 41, 19, 42, 43, 20, 44, 21, 22, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 99688.0}
2025-06-26 21:03:08,918 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 21:03:08,918 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:03:08,918 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 87110.0 (rank 0.0%)
- Population diversity: 0.5125
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:03:08,918 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:03:08,918 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:03:31,639 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Sequential traversal with modular jumps to explore sparse cells"
}
```
2025-06-26 21:03:31,639 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:03:31,644 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87110.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95]
2025-06-26 21:03:31,644 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}
2025-06-26 21:03:31,644 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 21:03:31,644 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:03:31,644 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:03:31,644 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 114002.0
2025-06-26 21:03:32,145 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:03:32,145 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:03:32,145 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:03:32,151 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:03:32,151 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104], 'cur_cost': 109830.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 34, 56, 78, 90, 1, 23, 45, 67, 89, 2, 24, 46, 68, 80, 3, 25, 47, 69, 81, 4, 26, 48, 70, 82, 5, 27, 49, 71, 83, 6, 28, 50, 72, 84, 7, 29, 51, 73, 85, 8, 30, 52, 74, 86, 9, 31, 53, 75, 87, 10, 32, 54, 76, 88, 11, 33, 55, 77, 79, 13, 35, 57, 59, 14, 36, 58, 60, 15, 37, 61, 62, 16, 38, 63, 64, 17, 39, 65, 66, 18, 40, 41, 19, 42, 43, 20, 44, 21, 22, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 99688.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([ 79,  70,   9,  10,  69,  64, 102, 104,  54,  96,  46,  28,  36,
        45,  26,  67,  89,  15,  25,  29,  27,  76,  63,  75,  94,  22,
        19,  14,   0,  16,  98,  66,  95,   1,   8,  24,  33,  39,  53,
        18,  93,  87,  73,  91,  37,  61,  13,  48,  31,  44,  41,  88,
        80,  20,  68,   3,  57,  92, 101,  52,  59,   4,  12,  50, 103,
        43,  86,  56,  81,  82,  71,  90,  42,  84,  99,  21,  17,  60,
        85,  51,  83,  38,  74,  47,  62,  32,  55,  97,  35,   6,  49,
         2,   5,  34,  58,  11, 100,  30,  65,  40,  77,  23,  72,  78,
         7]), 'cur_cost': 114002.0}, {'tour': array([ 33,  69,  61,  94,  44,  27,  64,  35,  30,  63,  59,  47,  95,
        32,   2,   0,  37,  28,  15,  70,  68,  89,  48,  41,  17,  78,
        96,   7,  84,  65,  40,  24,  18,  46,   4, 104,  14,  98,  71,
        49,  52,  43,  13,  82,  76,   3,  51,  83,  92,  99, 102,  25,
        60,   1,  93, 103,  26,  67,   8,  34,  38,  72,   9,  55,  45,
        90,  12,  73, 101,  80,  54,  75, 100,  79,  16,  36,  19,  77,
        62,  66,   6,  85,  57,  11,  58,  87,  74,  10,  21,   5,  22,
        56,  31,  23,  53,  91,  29,  97,  42,  86,  50,  20,  88,  81,
        39]), 'cur_cost': 124879.0}, {'tour': array([  5, 103,  96,  64,  18,   2,  10,  59,  40,  67, 102,  83,  46,
        55,  22,  21,  38,  72,  24,  66,  98,  82,  15,  73,  52,  79,
        95,  87,  51, 100,  33,  76,  43,  28,  88,  81,  70,  99,   0,
        37,  30,  14,  57,  75, 104,  78,  92, 101,  29,  31,  20,  26,
        11,  84,  32,  49,   1,  69,  53,  39,  35,  36,  60,  93,   4,
         7,  94,  13,  85,  97,  23,  86,  61,  50,   9,  44,  17,  65,
        68,  74,  58,  27,   3,  80,  47,  71,  90,  54,  42,  89,  56,
         6,  34,  91,  41,   8,  62,  12,  16,  19,  77,  63,  45,  25,
        48]), 'cur_cost': 119303.0}, {'tour': array([ 75,  52,  19,  64,  35,  17,  79,  16,  69,  21,  66,  53, 103,
        49,  83,   4, 102,   3,  15,  30,  48,  98,  87,  10,  68,  76,
        33,  44,   8,  28,   1,  22,  54,  29,  77,  73,   7,  42,  43,
        93, 101,  32,  86,   5,  57, 100,  85,  84,   0,  92,  82,  88,
        91,  20,  26,  78,  60,  81,  36,  46,  37,  90,  72,  55,  27,
         6,  40,  38,  41,  47,  71,  23,  65,  31,   2,  89,  34,  12,
        50,  56,  13,  24,  11,  97,  74,  70,   9,  61,  14,  45,  80,
        96,  67,  51,  63, 104,  99,  59,  62,  95,  18,  94,  58,  39,
        25]), 'cur_cost': 121491.0}]
2025-06-26 21:03:32,154 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:03:32,154 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 77, 'skip_rate': 0.03896103896103896, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 74, 'cache_hits': 37, 'similarity_calculations': 599, 'cache_hit_rate': 0.06176961602671119, 'cache_size': 562}}
2025-06-26 21:03:32,154 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 21:03:32,155 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 21:03:32,155 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:03:32,155 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:03:32,156 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 125889.0
2025-06-26 21:03:32,659 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:03:32,659 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:03:32,661 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:03:32,666 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:03:32,668 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104], 'cur_cost': 109830.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 34, 56, 78, 90, 1, 23, 45, 67, 89, 2, 24, 46, 68, 80, 3, 25, 47, 69, 81, 4, 26, 48, 70, 82, 5, 27, 49, 71, 83, 6, 28, 50, 72, 84, 7, 29, 51, 73, 85, 8, 30, 52, 74, 86, 9, 31, 53, 75, 87, 10, 32, 54, 76, 88, 11, 33, 55, 77, 79, 13, 35, 57, 59, 14, 36, 58, 60, 15, 37, 61, 62, 16, 38, 63, 64, 17, 39, 65, 66, 18, 40, 41, 19, 42, 43, 20, 44, 21, 22, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 99688.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([ 79,  70,   9,  10,  69,  64, 102, 104,  54,  96,  46,  28,  36,
        45,  26,  67,  89,  15,  25,  29,  27,  76,  63,  75,  94,  22,
        19,  14,   0,  16,  98,  66,  95,   1,   8,  24,  33,  39,  53,
        18,  93,  87,  73,  91,  37,  61,  13,  48,  31,  44,  41,  88,
        80,  20,  68,   3,  57,  92, 101,  52,  59,   4,  12,  50, 103,
        43,  86,  56,  81,  82,  71,  90,  42,  84,  99,  21,  17,  60,
        85,  51,  83,  38,  74,  47,  62,  32,  55,  97,  35,   6,  49,
         2,   5,  34,  58,  11, 100,  30,  65,  40,  77,  23,  72,  78,
         7]), 'cur_cost': 114002.0}, {'tour': array([ 21,  29,  77, 101,  94,  48,  52,  61,   0,  43,  84,  89,   1,
        53,  67,  14,  98,  65,  73,  38,  83,  37,  47,  95,  74,  72,
        15,  57,  81,  16,  18,  26,  23,  80,  24,  28,  75,  31,  88,
        19,  55,  92,   7,  59,  10,  62,  44,  46,  11,  17,   5,  78,
        64,  85,   9,  97,  27,  42,  79,  58,  30,  12,  41,  93,  60,
       104,  91,  13, 103,  32,  22,  20,  39,  68, 100,   4,  40,  87,
        49,  86,  76,  51,  90,  63,   3,   6,  36,  50,   2,  71,  56,
        70,  99,  66,  45,  54,  35,  69,  82, 102,  34,  25,  96,  33,
         8]), 'cur_cost': 125889.0}, {'tour': array([  5, 103,  96,  64,  18,   2,  10,  59,  40,  67, 102,  83,  46,
        55,  22,  21,  38,  72,  24,  66,  98,  82,  15,  73,  52,  79,
        95,  87,  51, 100,  33,  76,  43,  28,  88,  81,  70,  99,   0,
        37,  30,  14,  57,  75, 104,  78,  92, 101,  29,  31,  20,  26,
        11,  84,  32,  49,   1,  69,  53,  39,  35,  36,  60,  93,   4,
         7,  94,  13,  85,  97,  23,  86,  61,  50,   9,  44,  17,  65,
        68,  74,  58,  27,   3,  80,  47,  71,  90,  54,  42,  89,  56,
         6,  34,  91,  41,   8,  62,  12,  16,  19,  77,  63,  45,  25,
        48]), 'cur_cost': 119303.0}, {'tour': array([ 75,  52,  19,  64,  35,  17,  79,  16,  69,  21,  66,  53, 103,
        49,  83,   4, 102,   3,  15,  30,  48,  98,  87,  10,  68,  76,
        33,  44,   8,  28,   1,  22,  54,  29,  77,  73,   7,  42,  43,
        93, 101,  32,  86,   5,  57, 100,  85,  84,   0,  92,  82,  88,
        91,  20,  26,  78,  60,  81,  36,  46,  37,  90,  72,  55,  27,
         6,  40,  38,  41,  47,  71,  23,  65,  31,   2,  89,  34,  12,
        50,  56,  13,  24,  11,  97,  74,  70,   9,  61,  14,  45,  80,
        96,  67,  51,  63, 104,  99,  59,  62,  95,  18,  94,  58,  39,
        25]), 'cur_cost': 121491.0}]
2025-06-26 21:03:32,670 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:03:32,670 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 78, 'skip_rate': 0.038461538461538464, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 75, 'cache_hits': 37, 'similarity_calculations': 605, 'cache_hit_rate': 0.06115702479338843, 'cache_size': 568}}
2025-06-26 21:03:32,670 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 21:03:32,671 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 21:03:32,671 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:03:32,671 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:03:32,671 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 116540.0
2025-06-26 21:03:33,175 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:03:33,175 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:03:33,175 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:03:33,181 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:03:33,181 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104], 'cur_cost': 109830.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 34, 56, 78, 90, 1, 23, 45, 67, 89, 2, 24, 46, 68, 80, 3, 25, 47, 69, 81, 4, 26, 48, 70, 82, 5, 27, 49, 71, 83, 6, 28, 50, 72, 84, 7, 29, 51, 73, 85, 8, 30, 52, 74, 86, 9, 31, 53, 75, 87, 10, 32, 54, 76, 88, 11, 33, 55, 77, 79, 13, 35, 57, 59, 14, 36, 58, 60, 15, 37, 61, 62, 16, 38, 63, 64, 17, 39, 65, 66, 18, 40, 41, 19, 42, 43, 20, 44, 21, 22, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 99688.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([ 79,  70,   9,  10,  69,  64, 102, 104,  54,  96,  46,  28,  36,
        45,  26,  67,  89,  15,  25,  29,  27,  76,  63,  75,  94,  22,
        19,  14,   0,  16,  98,  66,  95,   1,   8,  24,  33,  39,  53,
        18,  93,  87,  73,  91,  37,  61,  13,  48,  31,  44,  41,  88,
        80,  20,  68,   3,  57,  92, 101,  52,  59,   4,  12,  50, 103,
        43,  86,  56,  81,  82,  71,  90,  42,  84,  99,  21,  17,  60,
        85,  51,  83,  38,  74,  47,  62,  32,  55,  97,  35,   6,  49,
         2,   5,  34,  58,  11, 100,  30,  65,  40,  77,  23,  72,  78,
         7]), 'cur_cost': 114002.0}, {'tour': array([ 21,  29,  77, 101,  94,  48,  52,  61,   0,  43,  84,  89,   1,
        53,  67,  14,  98,  65,  73,  38,  83,  37,  47,  95,  74,  72,
        15,  57,  81,  16,  18,  26,  23,  80,  24,  28,  75,  31,  88,
        19,  55,  92,   7,  59,  10,  62,  44,  46,  11,  17,   5,  78,
        64,  85,   9,  97,  27,  42,  79,  58,  30,  12,  41,  93,  60,
       104,  91,  13, 103,  32,  22,  20,  39,  68, 100,   4,  40,  87,
        49,  86,  76,  51,  90,  63,   3,   6,  36,  50,   2,  71,  56,
        70,  99,  66,  45,  54,  35,  69,  82, 102,  34,  25,  96,  33,
         8]), 'cur_cost': 125889.0}, {'tour': array([ 77,  55,  92,  81,  29,  42,  36,   9,  40, 101,  71, 100,  22,
        34,  91,  32,   2,  82,  24,  25,  39,  88,  98,  37,  84,  13,
        21,   6,  54,  58,  15,  52,  45,  74,  67,  70,  87,  48,  38,
        16,  68,  43,  63,  53,   3,  51,  20,   4,  69,  96,  17,  66,
        18,   1,  90,  72,  62,  95,  50,  78,  93,  94,  79,  56,  47,
        23,  44,  30,  46,  76,  61,  49,  89,  28,  83,  80,  41,  75,
        33, 103, 104,   7,  11,  26,  65,  31, 102,  10,  97,  99,  60,
        73,  57,  12,  85,   8,  59,  27,  19,   0,  35,  14,  64,  86,
         5]), 'cur_cost': 116540.0}, {'tour': array([ 75,  52,  19,  64,  35,  17,  79,  16,  69,  21,  66,  53, 103,
        49,  83,   4, 102,   3,  15,  30,  48,  98,  87,  10,  68,  76,
        33,  44,   8,  28,   1,  22,  54,  29,  77,  73,   7,  42,  43,
        93, 101,  32,  86,   5,  57, 100,  85,  84,   0,  92,  82,  88,
        91,  20,  26,  78,  60,  81,  36,  46,  37,  90,  72,  55,  27,
         6,  40,  38,  41,  47,  71,  23,  65,  31,   2,  89,  34,  12,
        50,  56,  13,  24,  11,  97,  74,  70,   9,  61,  14,  45,  80,
        96,  67,  51,  63, 104,  99,  59,  62,  95,  18,  94,  58,  39,
        25]), 'cur_cost': 121491.0}]
2025-06-26 21:03:33,183 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:03:33,185 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 79, 'skip_rate': 0.0379746835443038, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 76, 'cache_hits': 37, 'similarity_calculations': 612, 'cache_hit_rate': 0.06045751633986928, 'cache_size': 575}}
2025-06-26 21:03:33,185 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 21:03:33,185 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 21:03:33,185 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:03:33,186 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:03:33,186 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 121375.0
2025-06-26 21:03:33,688 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:03:33,689 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:03:33,689 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:03:33,695 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:03:33,695 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104], 'cur_cost': 109830.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 34, 56, 78, 90, 1, 23, 45, 67, 89, 2, 24, 46, 68, 80, 3, 25, 47, 69, 81, 4, 26, 48, 70, 82, 5, 27, 49, 71, 83, 6, 28, 50, 72, 84, 7, 29, 51, 73, 85, 8, 30, 52, 74, 86, 9, 31, 53, 75, 87, 10, 32, 54, 76, 88, 11, 33, 55, 77, 79, 13, 35, 57, 59, 14, 36, 58, 60, 15, 37, 61, 62, 16, 38, 63, 64, 17, 39, 65, 66, 18, 40, 41, 19, 42, 43, 20, 44, 21, 22, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 99688.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([ 79,  70,   9,  10,  69,  64, 102, 104,  54,  96,  46,  28,  36,
        45,  26,  67,  89,  15,  25,  29,  27,  76,  63,  75,  94,  22,
        19,  14,   0,  16,  98,  66,  95,   1,   8,  24,  33,  39,  53,
        18,  93,  87,  73,  91,  37,  61,  13,  48,  31,  44,  41,  88,
        80,  20,  68,   3,  57,  92, 101,  52,  59,   4,  12,  50, 103,
        43,  86,  56,  81,  82,  71,  90,  42,  84,  99,  21,  17,  60,
        85,  51,  83,  38,  74,  47,  62,  32,  55,  97,  35,   6,  49,
         2,   5,  34,  58,  11, 100,  30,  65,  40,  77,  23,  72,  78,
         7]), 'cur_cost': 114002.0}, {'tour': array([ 21,  29,  77, 101,  94,  48,  52,  61,   0,  43,  84,  89,   1,
        53,  67,  14,  98,  65,  73,  38,  83,  37,  47,  95,  74,  72,
        15,  57,  81,  16,  18,  26,  23,  80,  24,  28,  75,  31,  88,
        19,  55,  92,   7,  59,  10,  62,  44,  46,  11,  17,   5,  78,
        64,  85,   9,  97,  27,  42,  79,  58,  30,  12,  41,  93,  60,
       104,  91,  13, 103,  32,  22,  20,  39,  68, 100,   4,  40,  87,
        49,  86,  76,  51,  90,  63,   3,   6,  36,  50,   2,  71,  56,
        70,  99,  66,  45,  54,  35,  69,  82, 102,  34,  25,  96,  33,
         8]), 'cur_cost': 125889.0}, {'tour': array([ 77,  55,  92,  81,  29,  42,  36,   9,  40, 101,  71, 100,  22,
        34,  91,  32,   2,  82,  24,  25,  39,  88,  98,  37,  84,  13,
        21,   6,  54,  58,  15,  52,  45,  74,  67,  70,  87,  48,  38,
        16,  68,  43,  63,  53,   3,  51,  20,   4,  69,  96,  17,  66,
        18,   1,  90,  72,  62,  95,  50,  78,  93,  94,  79,  56,  47,
        23,  44,  30,  46,  76,  61,  49,  89,  28,  83,  80,  41,  75,
        33, 103, 104,   7,  11,  26,  65,  31, 102,  10,  97,  99,  60,
        73,  57,  12,  85,   8,  59,  27,  19,   0,  35,  14,  64,  86,
         5]), 'cur_cost': 116540.0}, {'tour': array([ 57,  80,  24,  85,  44,  51,  77,  87,  95,  11,  74,  60,  46,
        75,  25,  49,  21,  38,  35,   8,  92,  84,  62,  15,  47,  16,
        34,  37,   5,  53,  70,  83, 100,  20,  29,  10,  91,  79,  96,
        52,   1,  58,  22,   3,  13,  33,  98,  67,  66,  28,   4,  23,
        65,  43,   9,  94,  26,  42,  19,  69,  81,  63,  18, 104,  12,
        50,  32,  45,  59,  78,  89,  14,  68,  86,  99,  31,  41,   2,
        82,  61,   0, 101,  30,  73,  76,  88,  93,  71, 102,   7,  39,
        90,   6,  40,  72,  48,  56,  27,  64,  17,  97,  55,  36, 103,
        54]), 'cur_cost': 121375.0}]
2025-06-26 21:03:33,697 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:03:33,698 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 80, 'skip_rate': 0.0375, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 77, 'cache_hits': 37, 'similarity_calculations': 620, 'cache_hit_rate': 0.05967741935483871, 'cache_size': 583}}
2025-06-26 21:03:33,698 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 21:03:33,698 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 40, 62, 84, 6, 18, 30, 52, 74, 96, 29, 41, 63, 85, 7, 19, 31, 53, 75, 97, 20, 42, 64, 86, 8, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 11, 33, 55, 77, 99, 22, 44, 66, 88, 100, 101, 102, 103, 104], 'cur_cost': 109830.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 34, 56, 78, 90, 1, 23, 45, 67, 89, 2, 24, 46, 68, 80, 3, 25, 47, 69, 81, 4, 26, 48, 70, 82, 5, 27, 49, 71, 83, 6, 28, 50, 72, 84, 7, 29, 51, 73, 85, 8, 30, 52, 74, 86, 9, 31, 53, 75, 87, 10, 32, 54, 76, 88, 11, 33, 55, 77, 79, 13, 35, 57, 59, 14, 36, 58, 60, 15, 37, 61, 62, 16, 38, 63, 64, 17, 39, 65, 66, 18, 40, 41, 19, 42, 43, 20, 44, 21, 22, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 99688.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}}]
2025-06-26 21:03:33,699 - __main__ - INFO - 进化阶段完成
2025-06-26 21:03:33,699 - __main__ - INFO - 开始评估阶段
2025-06-26 21:03:33,699 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 21:03:33,701 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 21:03:33,701 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 86026.0 → 87110.0 (Δ -1084.0)
- Mean cost: 103267.4 → 103576.4
- Diversity: 0.530 → 0.604 (Δ 0.074)

#### Elite metrics
- Elite count: 12 → 12
- Best elite cost: 14379 → 14379 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 33.3% , avg Δ 3655.5
- Exploit: 4 inds, success 50.0% , avg Δ -6255.75

#### Other indicators
- No-change individuals: 2
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [158735.0, 37960.0, 86026.0], diversity [0.8788888888888888, 0.37671957671957673, 0.737989417989418]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 21:03:33,701 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:03:47,287 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low success rate (33.3%) but high potential impact (avg Δ +3655.5)",
    "exploit": "higher success rate (50.0%) with significant cost reduction (avg Δ -6255.75)"
  },
  "balance_state": "exploit is more effective but population diversity is increasing (0.530→0.604)",
  "recommendations": {
    "explore_ratio": 0.4,
    "exploit_ratio": 0.6,
    "diversity_weight": 0.7,
    "mutation_rate": "consider slight increase to escape local optima",
    "elite_preservation": "maintain current elite count (12)"
  }
}
```
2025-06-26 21:03:47,354 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 21:03:47,354 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low success rate (33.3%) but high potential impact (avg Δ +3655.5)",
    "exploit": "higher success rate (50.0%) with significant cost reduction (avg Δ -6255.75)"
  },
  "balance_state": "exploit is more effective but population diversity is increasing (0.530→0.604)",
  "recommendations": {
    "explore_ratio": 0.4,
    "exploit_ratio": 0.6,
    "diversity_weight": 0.7,
    "mutation_rate": "consider slight increase to escape local optima",
    "elite_preservation": "maintain current elite count (12)"
  }
}
```
2025-06-26 21:03:47,354 - __main__ - INFO - 评估阶段完成
2025-06-26 21:03:47,354 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low success rate (33.3%) but high potential impact (avg Δ +3655.5)",
    "exploit": "higher success rate (50.0%) with significant cost reduction (avg Δ -6255.75)"
  },
  "balance_state": "exploit is more effective but population diversity is increasing (0.530→0.604)",
  "recommendations": {
    "explore_ratio": 0.4,
    "exploit_ratio": 0.6,
    "diversity_weight": 0.7,
    "mutation_rate": "consider slight increase to escape local optima",
    "elite_preservation": "maintain current elite count (12)"
  }
}
```
2025-06-26 21:03:47,354 - __main__ - INFO - 当前最佳适应度: 87110.0
2025-06-26 21:03:47,362 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\lin105_route_2.pkl
2025-06-26 21:03:47,362 - __main__ - INFO - lin105 开始进化第 4 代
2025-06-26 21:03:47,362 - __main__ - INFO - 开始分析阶段
2025-06-26 21:03:47,362 - StatsExpert - INFO - 开始统计分析
2025-06-26 21:03:47,401 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 87110.0, 'max': 125889.0, 'mean': 103576.4, 'std': 14933.810044325593}, 'diversity': 0.8156613756613754, 'clusters': {'clusters': 7, 'cluster_sizes': [1, 4, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 21:03:47,404 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 87110.0, 'max': 125889.0, 'mean': 103576.4, 'std': 14933.810044325593}, 'diversity_level': 0.8156613756613754, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [1, 4, 1, 1, 1, 1, 1]}, 'coordinates': [[63, 71], [94, 71], [142, 370], [173, 1276], [205, 1213], [213, 69], [244, 69], [276, 630], [283, 732], [362, 69], [394, 69], [449, 370], [480, 1276], [512, 1213], [528, 157], [583, 630], [591, 732], [638, 654], [638, 496], [638, 314], [638, 142], [669, 142], [677, 315], [677, 496], [677, 654], [709, 654], [709, 496], [709, 315], [701, 142], [764, 220], [811, 189], [843, 173], [858, 370], [890, 1276], [921, 1213], [992, 630], [1000, 732], [1197, 1276], [1228, 1213], [1276, 205], [1299, 630], [1307, 732], [1362, 654], [1362, 496], [1362, 291], [1425, 654], [1425, 496], [1425, 291], [1417, 173], [1488, 291], [1488, 496], [1488, 654], [1551, 654], [1551, 496], [1551, 291], [1614, 291], [1614, 496], [1614, 654], [1732, 189], [1811, 1276], [1843, 1213], [1913, 630], [1921, 732], [2087, 370], [2118, 1276], [2150, 1213], [2189, 205], [2220, 189], [2220, 630], [2228, 732], [2244, 142], [2276, 315], [2276, 496], [2276, 654], [2315, 654], [2315, 496], [2315, 315], [2331, 142], [2346, 315], [2346, 496], [2346, 654], [2362, 142], [2402, 157], [2402, 220], [2480, 142], [2496, 370], [2528, 1276], [2559, 1213], [2630, 630], [2638, 732], [2756, 69], [2787, 69], [2803, 370], [2835, 1276], [2866, 1213], [2906, 69], [2937, 69], [2937, 630], [2945, 732], [3016, 1276], [3055, 69], [3087, 69], [606, 220], [1165, 370], [1780, 370]], 'distance_matrix': array([[   0.,   31.,  309., ...,  563., 1142., 1743.],
       [  31.,    0.,  303., ...,  533., 1112., 1712.],
       [ 309.,  303.,    0., ...,  488., 1023., 1638.],
       ...,
       [ 563.,  533.,  488., ...,    0.,  579., 1184.],
       [1142., 1112., 1023., ...,  579.,    0.,  615.],
       [1743., 1712., 1638., ..., 1184.,  615.,    0.]])}
2025-06-26 21:03:47,404 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 21:03:47,404 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 21:03:47,404 - PathExpert - INFO - 开始路径结构分析
2025-06-26 21:03:47,404 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 21:03:47,404 - PathExpert - INFO - 路径结构分析完成
2025-06-26 21:03:47,404 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 21:03:47,404 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 21:03:47,404 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 21:03:47,411 - EliteExpert - INFO - 开始精英解分析
2025-06-26 21:03:47,416 - EliteExpert - INFO - 精英解分析完成
2025-06-26 21:03:47,416 - __main__ - INFO - 精英专家分析报告: {'elite_count': 12, 'elite_common_features': {'common_edges': {'(49, 54)': 0.75, '(54, 55)': 0.75, '(55, 58)': 0.75}, 'common_edge_ratio': 0.02857142857142857}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 72731.0, 'avg_gap': 88691.15}, 'structure_gap': {'unique_elite_edges': 279, 'unique_pop_edges': 624, 'common_edges': 22}}, 'elite_diversity': {'diversity_score': 0.16666666666666663}}
2025-06-26 21:03:47,416 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 21:03:47,416 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 21:03:47,418 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 21:03:47,418 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 87110.0, mean 103576.4, max 125889.0, std 14933.810044325593
- diversity: 0.8156613756613754
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [37960.0, 86026.0, 87110.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [63.0, 69.0, 3087.0, 1276.0]
- centroid/spread: [1538.962, 525.343] | rms 953.602
- density_grid(3x3): [[20, 10, 20], [12, 14, 12], [6, 4, 7]]
- edge_len_stats(min,q1,med,q3,max): [31.0, 664.0, 1070.0, 1638.0, 3189.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [0, 2, 39, 4, 14, 31, 13, 1]
- nn_median_dist: 63.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 21:03:47,419 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:04:10,992 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 31.0 to 3189.0, with significant std deviation in cost stats)",
    "modality": "Potentially multimodal (diverse direction histogram and no clustering detected yet)",
    "deceptiveness": "Possible mild deceptiveness (long_edge_ratio of 0.249 suggests some misleading long edges)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (diversity=0.816, density_grid shows varied distribution)",
    "convergence": "Early exploration phase (convergence=0.0, recent best costs show improvement but no stagnation)"
  },
  "difficult_regions": [
    {"type": "Long-edge corridors", "evidence": "25% edges >1638.0 units (q3)"},
    {"type": "Low-density regions", "evidence": "Bottom-left grid cell (6 nodes) and bottom-middle (4 nodes)"}
  ],
  "opportunity_regions": [
    {"type": "High-density clusters", "evidence": "Top-left/top-right grid cells (20 nodes each)"},
    {"type": "Directional bias", "evidence": "Sectors 2+5 dominate direction histogram (70/104 edges)"}
  ],
  "evolution_phase": "Early exploration (iter 3/5)",
  "evolution_direction": [
    "Prioritize 2-opt near high-density clusters",
    "Add path-relinking between directional bias sectors",
    "Suppress long-edge mutations (penalize >q3 edges)",
    "Implement diversity preservation in low-density cells"
  ]
}
```
2025-06-26 21:04:10,992 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 21:04:10,992 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 31.0 to 3189.0, with significant std deviation in cost stats)', 'modality': 'Potentially multimodal (diverse direction histogram and no clustering detected yet)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio of 0.249 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity=0.816, density_grid shows varied distribution)', 'convergence': 'Early exploration phase (convergence=0.0, recent best costs show improvement but no stagnation)'}, 'difficult_regions': [{'type': 'Long-edge corridors', 'evidence': '25% edges >1638.0 units (q3)'}, {'type': 'Low-density regions', 'evidence': 'Bottom-left grid cell (6 nodes) and bottom-middle (4 nodes)'}], 'opportunity_regions': [{'type': 'High-density clusters', 'evidence': 'Top-left/top-right grid cells (20 nodes each)'}, {'type': 'Directional bias', 'evidence': 'Sectors 2+5 dominate direction histogram (70/104 edges)'}], 'evolution_phase': 'Early exploration (iter 3/5)', 'evolution_direction': ['Prioritize 2-opt near high-density clusters', 'Add path-relinking between directional bias sectors', 'Suppress long-edge mutations (penalize >q3 edges)', 'Implement diversity preservation in low-density cells']}
2025-06-26 21:04:10,992 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 21:04:10,992 - __main__ - INFO - 分析阶段完成
2025-06-26 21:04:10,994 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 31.0 to 3189.0, with significant std deviation in cost stats)', 'modality': 'Potentially multimodal (diverse direction histogram and no clustering detected yet)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio of 0.249 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity=0.816, density_grid shows varied distribution)', 'convergence': 'Early exploration phase (convergence=0.0, recent best costs show improvement but no stagnation)'}, 'difficult_regions': [{'type': 'Long-edge corridors', 'evidence': '25% edges >1638.0 units (q3)'}, {'type': 'Low-density regions', 'evidence': 'Bottom-left grid cell (6 nodes) and bottom-middle (4 nodes)'}], 'opportunity_regions': [{'type': 'High-density clusters', 'evidence': 'Top-left/top-right grid cells (20 nodes each)'}, {'type': 'Directional bias', 'evidence': 'Sectors 2+5 dominate direction histogram (70/104 edges)'}], 'evolution_phase': 'Early exploration (iter 3/5)', 'evolution_direction': ['Prioritize 2-opt near high-density clusters', 'Add path-relinking between directional bias sectors', 'Suppress long-edge mutations (penalize >q3 edges)', 'Implement diversity preservation in low-density cells']}
2025-06-26 21:04:10,994 - __main__ - INFO - 开始策略分配阶段
2025-06-26 21:04:10,994 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 21:04:10,994 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 87110.0
  • mean_cost: 103576.4
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 31.0 to 3189.0, with significant std deviation in cost stats), modality Potentially multimodal (diverse direction histogram and no clustering detected yet), deceptiveness Possible mild deceptiveness (long_edge_ratio of 0.249 suggests some misleading long edges)
- Evolution phase: Early exploration (iter 3/5)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'Long-edge , {'type': 'Low-densit
- Opportunity regions (# 2): {'type': 'High-densi, {'type': 'Directiona
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low s...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 21:04:10,994 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 21:04:10,994 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:04:21,280 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration; top 30% performers exploit while others explore."
}
```
2025-06-26 21:04:21,280 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 21:04:21,280 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 21:04:21,283 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration; top 30% performers exploit while others explore."
}
```
2025-06-26 21:04:21,283 - __main__ - INFO - 策略分配阶段完成
2025-06-26 21:04:21,283 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 21:04:21,283 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration; top 30% performers exploit while others explore."
}
```
2025-06-26 21:04:21,283 - __main__ - INFO - 开始进化阶段
2025-06-26 21:04:21,283 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 21:04:21,283 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:04:21,283 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 109830.0 (rank 50.0%)
- Population diversity: 0.6038461538461538
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:04:21,283 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:04:21,283 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:04:42,794 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95],
  "targeted_regions": "High-density and low-density adjacent cells",
  "strategy_comment": "Balanced exploration with structured jumps"
}
```
2025-06-26 21:04:42,795 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:04:42,795 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87110.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95]
2025-06-26 21:04:42,795 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}
2025-06-26 21:04:42,795 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 21:04:42,796 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:04:42,796 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 87110.0 (rank 0.0%)
- Population diversity: 0.5644230769230769
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:04:42,796 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:04:42,796 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:05:03,792 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104],
  "targeted_regions": "High-opportunity and adjacent low-density cells",
  "strategy_comment": "Balanced exploration with opportunistic jumps"
}
```
2025-06-26 21:05:03,792 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:05:03,792 - ExplorationExpert - INFO - 探索路径生成完成，成本: 123889.0, 路径: [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 102, 103, 101]
2025-06-26 21:05:03,792 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 102, 103, 101], 'cur_cost': 123889.0}
2025-06-26 21:05:03,792 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 21:05:03,792 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:05:03,792 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 87110.0 (rank 0.0%)
- Population diversity: 0.6221153846153846
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:05:03,792 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:05:03,792 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:05:25,216 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104],
  "targeted_regions": "High-opportunity and low-density areas",
  "strategy_comment": "Sequential exploration with opportunistic jumps"
}
```
2025-06-26 21:05:25,219 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:05:25,219 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36480.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104]
2025-06-26 21:05:25,219 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 36480.0}
2025-06-26 21:05:25,220 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-26 21:05:25,220 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:05:25,220 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:05:25,220 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 123464.0
2025-06-26 21:05:25,220 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：73,跳过局部搜索
2025-06-26 21:05:25,220 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 21:05:25,220 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 40,   1,  72,  15,  50,  32,  79,  34,   5,  59,  42,   8,  29,
        64, 101,  87,  53,   2,   0,  13,  55,  21, 104,  91,  48,  97,
        38,  10,   9,  71,  22,  16,  35,  58, 102,  75,  28,  11,  76,
        89,  51,  54,   4,  60,  30,  81,  43,  19,  57,  14,  24,  88,
        18,  41,  82,  25,   6,  36,  84,  67,  83,  31,  99,  39, 100,
        96,  86,  90,  92,  85,  95,  63,  52,  23,  20,  66,  56,  70,
        78,  12,  61,  98,  17,  68, 103,  93,  47,  73,  27,  94,  37,
        74,  33,  80,  77,  69,  44,  62,  65,   7,  45,  49,  26,   3,
        46]), 'cur_cost': 123464.0}
2025-06-26 21:05:25,220 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-26 21:05:25,220 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:05:25,220 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:05:25,220 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 119871.0
2025-06-26 21:05:25,725 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:05:25,727 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:05:25,727 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:05:25,732 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:05:25,732 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 102, 103, 101], 'cur_cost': 123889.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 36480.0}, {'tour': array([ 40,   1,  72,  15,  50,  32,  79,  34,   5,  59,  42,   8,  29,
        64, 101,  87,  53,   2,   0,  13,  55,  21, 104,  91,  48,  97,
        38,  10,   9,  71,  22,  16,  35,  58, 102,  75,  28,  11,  76,
        89,  51,  54,   4,  60,  30,  81,  43,  19,  57,  14,  24,  88,
        18,  41,  82,  25,   6,  36,  84,  67,  83,  31,  99,  39, 100,
        96,  86,  90,  92,  85,  95,  63,  52,  23,  20,  66,  56,  70,
        78,  12,  61,  98,  17,  68, 103,  93,  47,  73,  27,  94,  37,
        74,  33,  80,  77,  69,  44,  62,  65,   7,  45,  49,  26,   3,
        46]), 'cur_cost': 123464.0}, {'tour': array([ 87,  51,  10,  98,  11,   2,  38,  73,  59,  95,  19, 103,  68,
        25,  49,  39,  50,   9,  61,  60,   5,  80,  43,  47,  40,   6,
       101,  45,  84,  90,  72,  96,  97,  18,  66,   0,  12,  28,  92,
        34,   8,  69,  71,  89,  77,  70,  44,  48,  14,  35,   4,  54,
        46,  22,  75,  76,  41,  65,  26,   1,   3,  67,  93,  57,  85,
        37,  91,  33,  88, 102,  17,  32,  21,  58,  82,  81,  64,  79,
        83,  99,  31,  36,  94,  42,  13,  20,  15,  23,  63,  74,  24,
        62,  30,  16,  55,  52, 100, 104,  29,  56,  78,  86,  53,   7,
        27]), 'cur_cost': 119871.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([ 79,  70,   9,  10,  69,  64, 102, 104,  54,  96,  46,  28,  36,
        45,  26,  67,  89,  15,  25,  29,  27,  76,  63,  75,  94,  22,
        19,  14,   0,  16,  98,  66,  95,   1,   8,  24,  33,  39,  53,
        18,  93,  87,  73,  91,  37,  61,  13,  48,  31,  44,  41,  88,
        80,  20,  68,   3,  57,  92, 101,  52,  59,   4,  12,  50, 103,
        43,  86,  56,  81,  82,  71,  90,  42,  84,  99,  21,  17,  60,
        85,  51,  83,  38,  74,  47,  62,  32,  55,  97,  35,   6,  49,
         2,   5,  34,  58,  11, 100,  30,  65,  40,  77,  23,  72,  78,
         7]), 'cur_cost': 114002.0}, {'tour': array([ 21,  29,  77, 101,  94,  48,  52,  61,   0,  43,  84,  89,   1,
        53,  67,  14,  98,  65,  73,  38,  83,  37,  47,  95,  74,  72,
        15,  57,  81,  16,  18,  26,  23,  80,  24,  28,  75,  31,  88,
        19,  55,  92,   7,  59,  10,  62,  44,  46,  11,  17,   5,  78,
        64,  85,   9,  97,  27,  42,  79,  58,  30,  12,  41,  93,  60,
       104,  91,  13, 103,  32,  22,  20,  39,  68, 100,   4,  40,  87,
        49,  86,  76,  51,  90,  63,   3,   6,  36,  50,   2,  71,  56,
        70,  99,  66,  45,  54,  35,  69,  82, 102,  34,  25,  96,  33,
         8]), 'cur_cost': 125889.0}, {'tour': array([ 77,  55,  92,  81,  29,  42,  36,   9,  40, 101,  71, 100,  22,
        34,  91,  32,   2,  82,  24,  25,  39,  88,  98,  37,  84,  13,
        21,   6,  54,  58,  15,  52,  45,  74,  67,  70,  87,  48,  38,
        16,  68,  43,  63,  53,   3,  51,  20,   4,  69,  96,  17,  66,
        18,   1,  90,  72,  62,  95,  50,  78,  93,  94,  79,  56,  47,
        23,  44,  30,  46,  76,  61,  49,  89,  28,  83,  80,  41,  75,
        33, 103, 104,   7,  11,  26,  65,  31, 102,  10,  97,  99,  60,
        73,  57,  12,  85,   8,  59,  27,  19,   0,  35,  14,  64,  86,
         5]), 'cur_cost': 116540.0}, {'tour': array([ 57,  80,  24,  85,  44,  51,  77,  87,  95,  11,  74,  60,  46,
        75,  25,  49,  21,  38,  35,   8,  92,  84,  62,  15,  47,  16,
        34,  37,   5,  53,  70,  83, 100,  20,  29,  10,  91,  79,  96,
        52,   1,  58,  22,   3,  13,  33,  98,  67,  66,  28,   4,  23,
        65,  43,   9,  94,  26,  42,  19,  69,  81,  63,  18, 104,  12,
        50,  32,  45,  59,  78,  89,  14,  68,  86,  99,  31,  41,   2,
        82,  61,   0, 101,  30,  73,  76,  88,  93,  71, 102,   7,  39,
        90,   6,  40,  72,  48,  56,  27,  64,  17,  97,  55,  36, 103,
        54]), 'cur_cost': 121375.0}]
2025-06-26 21:05:25,735 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:05:25,735 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 82, 'skip_rate': 0.04878048780487805, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 78, 'cache_hits': 42, 'similarity_calculations': 635, 'cache_hit_rate': 0.06614173228346457, 'cache_size': 593}}
2025-06-26 21:05:25,736 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-26 21:05:25,736 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 21:05:25,736 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:05:25,737 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 87110.0 (rank 10.0%)
- Population diversity: 0.8317307692307693
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:05:25,737 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:05:25,737 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:05:47,549 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 81, 92, 25, 47, 69, 82, 3, 15, 37, 59, 83, 93, 26, 48, 70, 84, 4, 16, 38, 60, 85, 94, 27, 49, 71, 86, 5, 17, 39, 61, 87, 95, 28, 50, 72, 88, 6, 18, 40, 62, 96, 29, 51, 73, 97, 19, 41, 63, 98, 30, 52, 74, 99, 20, 42, 64, 100, 31, 53, 75, 101, 21, 43, 65, 102, 32, 54, 76, 103, 22, 44, 66, 104, 7, 8, 9, 10, 11, 33, 55, 77],
  "targeted_regions": "High-opportunity and low-density adjacent cells",
  "strategy_comment": "Balance exploration of sparse regions with cost efficiency"
}
```
2025-06-26 21:05:47,549 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:05:47,549 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112305.0, 路径: [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 81, 92, 25, 47, 69, 82, 3, 15, 37, 59, 83, 93, 26, 48, 70, 84, 4, 16, 38, 60, 85, 94, 27, 49, 71, 86, 5, 17, 39, 61, 87, 95, 28, 50, 72, 88, 6, 18, 40, 62, 96, 29, 51, 73, 97, 19, 41, 63, 98, 30, 52, 74, 99, 20, 42, 64, 100, 31, 53, 75, 101, 21, 43, 65, 102, 32, 54, 76, 103, 22, 44, 66, 104, 7, 8, 9, 10, 11, 33, 55, 77]
2025-06-26 21:05:47,549 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 81, 92, 25, 47, 69, 82, 3, 15, 37, 59, 83, 93, 26, 48, 70, 84, 4, 16, 38, 60, 85, 94, 27, 49, 71, 86, 5, 17, 39, 61, 87, 95, 28, 50, 72, 88, 6, 18, 40, 62, 96, 29, 51, 73, 97, 19, 41, 63, 98, 30, 52, 74, 99, 20, 42, 64, 100, 31, 53, 75, 101, 21, 43, 65, 102, 32, 54, 76, 103, 22, 44, 66, 104, 7, 8, 9, 10, 11, 33, 55, 77], 'cur_cost': 112305.0}
2025-06-26 21:05:47,549 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 21:05:47,549 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:05:47,555 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 114002.0 (rank 30.0%)
- Population diversity: 0.8846153846153846
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:05:47,555 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:05:47,555 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:06:09,460 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95],
  "targeted_regions": "Low-density adjacent to opportunity regions",
  "strategy_comment": "Balanced exploration with modular jumps"
}
```
2025-06-26 21:06:09,460 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:06:09,460 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87110.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95]
2025-06-26 21:06:09,460 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}
2025-06-26 21:06:09,460 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 21:06:09,463 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:06:09,463 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:06:09,463 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 120806.0
2025-06-26 21:06:09,965 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:06:09,966 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:06:09,966 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:06:09,972 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:06:09,972 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 102, 103, 101], 'cur_cost': 123889.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 36480.0}, {'tour': array([ 40,   1,  72,  15,  50,  32,  79,  34,   5,  59,  42,   8,  29,
        64, 101,  87,  53,   2,   0,  13,  55,  21, 104,  91,  48,  97,
        38,  10,   9,  71,  22,  16,  35,  58, 102,  75,  28,  11,  76,
        89,  51,  54,   4,  60,  30,  81,  43,  19,  57,  14,  24,  88,
        18,  41,  82,  25,   6,  36,  84,  67,  83,  31,  99,  39, 100,
        96,  86,  90,  92,  85,  95,  63,  52,  23,  20,  66,  56,  70,
        78,  12,  61,  98,  17,  68, 103,  93,  47,  73,  27,  94,  37,
        74,  33,  80,  77,  69,  44,  62,  65,   7,  45,  49,  26,   3,
        46]), 'cur_cost': 123464.0}, {'tour': array([ 87,  51,  10,  98,  11,   2,  38,  73,  59,  95,  19, 103,  68,
        25,  49,  39,  50,   9,  61,  60,   5,  80,  43,  47,  40,   6,
       101,  45,  84,  90,  72,  96,  97,  18,  66,   0,  12,  28,  92,
        34,   8,  69,  71,  89,  77,  70,  44,  48,  14,  35,   4,  54,
        46,  22,  75,  76,  41,  65,  26,   1,   3,  67,  93,  57,  85,
        37,  91,  33,  88, 102,  17,  32,  21,  58,  82,  81,  64,  79,
        83,  99,  31,  36,  94,  42,  13,  20,  15,  23,  63,  74,  24,
        62,  30,  16,  55,  52, 100, 104,  29,  56,  78,  86,  53,   7,
        27]), 'cur_cost': 119871.0}, {'tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 81, 92, 25, 47, 69, 82, 3, 15, 37, 59, 83, 93, 26, 48, 70, 84, 4, 16, 38, 60, 85, 94, 27, 49, 71, 86, 5, 17, 39, 61, 87, 95, 28, 50, 72, 88, 6, 18, 40, 62, 96, 29, 51, 73, 97, 19, 41, 63, 98, 30, 52, 74, 99, 20, 42, 64, 100, 31, 53, 75, 101, 21, 43, 65, 102, 32, 54, 76, 103, 22, 44, 66, 104, 7, 8, 9, 10, 11, 33, 55, 77], 'cur_cost': 112305.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([102,  48,  90,  63,   1, 104,  14,  61,  77,  39,  56, 101,  17,
        20,   0,  18,  86,  99,  82,  50,  57,  12,  88,  81,  32,  38,
         2,  87,  64,  15,  55,  59,  89,  58,   6,  40,  94,  95,   9,
         7,  73,  51,   8,   3,  19,  91,  37,  52,  45,  67,  84,  92,
        43,  93,  60,  69,  62,  29,  80,  74,  72,   5,  23,  36,  22,
        25,  98,  35,  78,  54,  96,  49,  85,  47,  44,  31,  75,  30,
        28,  26,  79,  24,  53,  83,  71,  65,  13,  42,  34,  46,  10,
        33,   4, 103,  97,  27,  68, 100,  11,  76,  41,  66,  21,  70,
        16]), 'cur_cost': 120806.0}, {'tour': array([ 77,  55,  92,  81,  29,  42,  36,   9,  40, 101,  71, 100,  22,
        34,  91,  32,   2,  82,  24,  25,  39,  88,  98,  37,  84,  13,
        21,   6,  54,  58,  15,  52,  45,  74,  67,  70,  87,  48,  38,
        16,  68,  43,  63,  53,   3,  51,  20,   4,  69,  96,  17,  66,
        18,   1,  90,  72,  62,  95,  50,  78,  93,  94,  79,  56,  47,
        23,  44,  30,  46,  76,  61,  49,  89,  28,  83,  80,  41,  75,
        33, 103, 104,   7,  11,  26,  65,  31, 102,  10,  97,  99,  60,
        73,  57,  12,  85,   8,  59,  27,  19,   0,  35,  14,  64,  86,
         5]), 'cur_cost': 116540.0}, {'tour': array([ 57,  80,  24,  85,  44,  51,  77,  87,  95,  11,  74,  60,  46,
        75,  25,  49,  21,  38,  35,   8,  92,  84,  62,  15,  47,  16,
        34,  37,   5,  53,  70,  83, 100,  20,  29,  10,  91,  79,  96,
        52,   1,  58,  22,   3,  13,  33,  98,  67,  66,  28,   4,  23,
        65,  43,   9,  94,  26,  42,  19,  69,  81,  63,  18, 104,  12,
        50,  32,  45,  59,  78,  89,  14,  68,  86,  99,  31,  41,   2,
        82,  61,   0, 101,  30,  73,  76,  88,  93,  71, 102,   7,  39,
        90,   6,  40,  72,  48,  56,  27,  64,  17,  97,  55,  36, 103,
        54]), 'cur_cost': 121375.0}]
2025-06-26 21:06:09,975 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:06:09,975 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 83, 'skip_rate': 0.04819277108433735, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 79, 'cache_hits': 42, 'similarity_calculations': 645, 'cache_hit_rate': 0.06511627906976744, 'cache_size': 603}}
2025-06-26 21:06:09,976 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 21:06:09,976 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-26 21:06:09,976 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:06:09,976 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 116540.0 (rank 40.0%)
- Population diversity: 0.7923076923076923
- Evolution phase: Early exploration (iter 3/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:06:09,976 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:06:09,976 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:06:39,412 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 26, 59, 82, 15, 48, 71, 4, 27, 60, 83, 16, 49, 72, 5, 28, 61, 84, 17, 50, 73, 6, 29, 62, 85, 18, 51, 74, 7, 30, 63, 86, 19, 52, 75, 8, 31, 64, 87, 20, 53, 76, 9, 32, 65, 88, 21, 54, 77, 10, 33, 66, 99, 22, 55, 78, 11, 44, 77, 100, 33, 66, 99, 22, 55, 88, 21, 54, 87, 20, 53, 86, 19, 52, 85, 18, 51, 84, 17, 50, 83, 16, 49, 82, 15, 48, 81, 14, 47, 80, 13, 46, 79, 12, 45, 78, 11, 44, 77, 10, 43, 76, 9, 42, 75, 8, 41, 74, 7, 40, 73, 6, 39, 72, 5, 38, 71, 4, 37, 70, 3, 36, 69, 2, 35, 68, 1, 34, 67, 0],
  "targeted_regions": "High-opportunity and adjacent low-density cells",
  "strategy_comment": "Balanced exploration of sparse regions while avoiding difficult areas"
}
```
2025-06-26 21:06:39,412 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:06:39,412 - ExplorationExpert - INFO - 探索路径生成完成，成本: 135562.0, 路径: [0, 12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 26, 59, 82, 15, 48, 71, 4, 27, 60, 83, 16, 49, 72, 5, 28, 61, 84, 17, 50, 73, 6, 29, 62, 85, 18, 51, 74, 7, 30, 63, 86, 19, 52, 75, 8, 31, 64, 87, 20, 53, 76, 9, 32, 65, 88, 21, 54, 77, 10, 33, 66, 99, 22, 55, 11, 44, 100, 41, 42, 38, 101, 92, 25, 95, 40, 69, 97, 43, 37, 93, 98, 36, 96, 58, 81, 39, 103, 102, 94, 104]
2025-06-26 21:06:39,412 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 26, 59, 82, 15, 48, 71, 4, 27, 60, 83, 16, 49, 72, 5, 28, 61, 84, 17, 50, 73, 6, 29, 62, 85, 18, 51, 74, 7, 30, 63, 86, 19, 52, 75, 8, 31, 64, 87, 20, 53, 76, 9, 32, 65, 88, 21, 54, 77, 10, 33, 66, 99, 22, 55, 11, 44, 100, 41, 42, 38, 101, 92, 25, 95, 40, 69, 97, 43, 37, 93, 98, 36, 96, 58, 81, 39, 103, 102, 94, 104], 'cur_cost': 135562.0}
2025-06-26 21:06:39,413 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 21:06:39,413 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:06:39,413 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:06:39,413 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 124936.0
2025-06-26 21:06:39,915 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:06:39,917 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:06:39,917 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:06:39,922 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:06:39,923 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 102, 103, 101], 'cur_cost': 123889.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 36480.0}, {'tour': array([ 40,   1,  72,  15,  50,  32,  79,  34,   5,  59,  42,   8,  29,
        64, 101,  87,  53,   2,   0,  13,  55,  21, 104,  91,  48,  97,
        38,  10,   9,  71,  22,  16,  35,  58, 102,  75,  28,  11,  76,
        89,  51,  54,   4,  60,  30,  81,  43,  19,  57,  14,  24,  88,
        18,  41,  82,  25,   6,  36,  84,  67,  83,  31,  99,  39, 100,
        96,  86,  90,  92,  85,  95,  63,  52,  23,  20,  66,  56,  70,
        78,  12,  61,  98,  17,  68, 103,  93,  47,  73,  27,  94,  37,
        74,  33,  80,  77,  69,  44,  62,  65,   7,  45,  49,  26,   3,
        46]), 'cur_cost': 123464.0}, {'tour': array([ 87,  51,  10,  98,  11,   2,  38,  73,  59,  95,  19, 103,  68,
        25,  49,  39,  50,   9,  61,  60,   5,  80,  43,  47,  40,   6,
       101,  45,  84,  90,  72,  96,  97,  18,  66,   0,  12,  28,  92,
        34,   8,  69,  71,  89,  77,  70,  44,  48,  14,  35,   4,  54,
        46,  22,  75,  76,  41,  65,  26,   1,   3,  67,  93,  57,  85,
        37,  91,  33,  88, 102,  17,  32,  21,  58,  82,  81,  64,  79,
        83,  99,  31,  36,  94,  42,  13,  20,  15,  23,  63,  74,  24,
        62,  30,  16,  55,  52, 100, 104,  29,  56,  78,  86,  53,   7,
        27]), 'cur_cost': 119871.0}, {'tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 81, 92, 25, 47, 69, 82, 3, 15, 37, 59, 83, 93, 26, 48, 70, 84, 4, 16, 38, 60, 85, 94, 27, 49, 71, 86, 5, 17, 39, 61, 87, 95, 28, 50, 72, 88, 6, 18, 40, 62, 96, 29, 51, 73, 97, 19, 41, 63, 98, 30, 52, 74, 99, 20, 42, 64, 100, 31, 53, 75, 101, 21, 43, 65, 102, 32, 54, 76, 103, 22, 44, 66, 104, 7, 8, 9, 10, 11, 33, 55, 77], 'cur_cost': 112305.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([102,  48,  90,  63,   1, 104,  14,  61,  77,  39,  56, 101,  17,
        20,   0,  18,  86,  99,  82,  50,  57,  12,  88,  81,  32,  38,
         2,  87,  64,  15,  55,  59,  89,  58,   6,  40,  94,  95,   9,
         7,  73,  51,   8,   3,  19,  91,  37,  52,  45,  67,  84,  92,
        43,  93,  60,  69,  62,  29,  80,  74,  72,   5,  23,  36,  22,
        25,  98,  35,  78,  54,  96,  49,  85,  47,  44,  31,  75,  30,
        28,  26,  79,  24,  53,  83,  71,  65,  13,  42,  34,  46,  10,
        33,   4, 103,  97,  27,  68, 100,  11,  76,  41,  66,  21,  70,
        16]), 'cur_cost': 120806.0}, {'tour': [0, 12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 26, 59, 82, 15, 48, 71, 4, 27, 60, 83, 16, 49, 72, 5, 28, 61, 84, 17, 50, 73, 6, 29, 62, 85, 18, 51, 74, 7, 30, 63, 86, 19, 52, 75, 8, 31, 64, 87, 20, 53, 76, 9, 32, 65, 88, 21, 54, 77, 10, 33, 66, 99, 22, 55, 11, 44, 100, 41, 42, 38, 101, 92, 25, 95, 40, 69, 97, 43, 37, 93, 98, 36, 96, 58, 81, 39, 103, 102, 94, 104], 'cur_cost': 135562.0}, {'tour': array([ 91,   5,  83,  81,  31,  48,   8,  33,  86,  18,  32,  98,  69,
        40,  94,  70,  72,  13,  47, 100,  39,  21,  41,  57, 104,  93,
         2,  66,  77,  88,   0, 101,  74,  16,  38,  67,  54,  35,  26,
        19,  52,  23,  79,  51,  36,  24,  92,  95,  45,  62,  68,   3,
        56,  42,  75,  20,  90,  14,   4,  65,  63,  84,  25,  85, 103,
         7,  34,  59,   9,  28,  43,  50,  64,  53,  80,  78,  55,   6,
        37,  61,  99,  71,  11, 102,  82,  58,  46,  73,  10,  29,  44,
        49,  96,  17,  30,  97,  27,  60,  22,   1,  87,  76,  15,  12,
        89]), 'cur_cost': 124936.0}]
2025-06-26 21:06:39,925 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:06:39,925 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 84, 'skip_rate': 0.047619047619047616, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 80, 'cache_hits': 42, 'similarity_calculations': 656, 'cache_hit_rate': 0.06402439024390244, 'cache_size': 614}}
2025-06-26 21:06:39,926 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 21:06:39,926 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 45, 23, 67, 89, 34, 56, 78, 90, 1, 13, 46, 24, 68, 91, 35, 57, 79, 2, 14, 47, 25, 69, 92, 36, 58, 80, 3, 15, 48, 26, 70, 93, 37, 59, 81, 4, 16, 49, 27, 71, 94, 38, 60, 82, 5, 17, 50, 28, 72, 95, 39, 61, 83, 6, 18, 51, 29, 73, 96, 40, 62, 84, 7, 19, 52, 30, 74, 97, 41, 63, 85, 8, 20, 53, 31, 75, 98, 42, 64, 86, 9, 21, 54, 32, 76, 99, 43, 65, 87, 10, 22, 55, 33, 77, 100, 44, 66, 88, 11, 104, 102, 103, 101], 'cur_cost': 123889.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 36480.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 40,   1,  72,  15,  50,  32,  79,  34,   5,  59,  42,   8,  29,
        64, 101,  87,  53,   2,   0,  13,  55,  21, 104,  91,  48,  97,
        38,  10,   9,  71,  22,  16,  35,  58, 102,  75,  28,  11,  76,
        89,  51,  54,   4,  60,  30,  81,  43,  19,  57,  14,  24,  88,
        18,  41,  82,  25,   6,  36,  84,  67,  83,  31,  99,  39, 100,
        96,  86,  90,  92,  85,  95,  63,  52,  23,  20,  66,  56,  70,
        78,  12,  61,  98,  17,  68, 103,  93,  47,  73,  27,  94,  37,
        74,  33,  80,  77,  69,  44,  62,  65,   7,  45,  49,  26,   3,
        46]), 'cur_cost': 123464.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 81, 92, 25, 47, 69, 82, 3, 15, 37, 59, 83, 93, 26, 48, 70, 84, 4, 16, 38, 60, 85, 94, 27, 49, 71, 86, 5, 17, 39, 61, 87, 95, 28, 50, 72, 88, 6, 18, 40, 62, 96, 29, 51, 73, 97, 19, 41, 63, 98, 30, 52, 74, 99, 20, 42, 64, 100, 31, 53, 75, 101, 21, 43, 65, 102, 32, 54, 76, 103, 22, 44, 66, 104, 7, 8, 9, 10, 11, 33, 55, 77], 'cur_cost': 112305.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 26, 59, 82, 15, 48, 71, 4, 27, 60, 83, 16, 49, 72, 5, 28, 61, 84, 17, 50, 73, 6, 29, 62, 85, 18, 51, 74, 7, 30, 63, 86, 19, 52, 75, 8, 31, 64, 87, 20, 53, 76, 9, 32, 65, 88, 21, 54, 77, 10, 33, 66, 99, 22, 55, 11, 44, 100, 41, 42, 38, 101, 92, 25, 95, 40, 69, 97, 43, 37, 93, 98, 36, 96, 58, 81, 39, 103, 102, 94, 104], 'cur_cost': 135562.0}}]
2025-06-26 21:06:39,927 - __main__ - INFO - 进化阶段完成
2025-06-26 21:06:39,927 - __main__ - INFO - 开始评估阶段
2025-06-26 21:06:39,927 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 21:06:39,929 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 21:06:39,929 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 87110.0 → 36480.0 (Δ 50630.0)
- Mean cost: 103576.4 → 107153.3
- Diversity: 0.604 → 0.787 (Δ 0.1827)

#### Elite metrics
- Elite count: 12 → 12
- Best elite cost: 14379 → 14379 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 50.0% , avg Δ 3207.67
- Exploit: 4 inds, success 25.0% , avg Δ -13753.75

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [37960.0, 86026.0, 87110.0], diversity [0.37671957671957673, 0.737989417989418, 0.8156613756613754]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 21:06:39,929 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:06:54,590 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.5,
      "average_improvement": 3207.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 0.25,
      "average_improvement": -13753.75,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "explore-heavy (6:4)",
    "assessment": "explore is more effective but exploit is regressing; needs rebalancing"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider adding small mutation rate to exploit strategies to prevent regression"
  }
}
```
2025-06-26 21:06:54,668 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 21:06:54,668 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.5,
      "average_improvement": 3207.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 0.25,
      "average_improvement": -13753.75,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "explore-heavy (6:4)",
    "assessment": "explore is more effective but exploit is regressing; needs rebalancing"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider adding small mutation rate to exploit strategies to prevent regression"
  }
}
```
2025-06-26 21:06:54,668 - __main__ - INFO - 评估阶段完成
2025-06-26 21:06:54,668 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.5,
      "average_improvement": 3207.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 0.25,
      "average_improvement": -13753.75,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": {
    "current_ratio": "explore-heavy (6:4)",
    "assessment": "explore is more effective but exploit is regressing; needs rebalancing"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider adding small mutation rate to exploit strategies to prevent regression"
  }
}
```
2025-06-26 21:06:54,668 - __main__ - INFO - 当前最佳适应度: 36480.0
2025-06-26 21:06:54,672 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\lin105_route_3.pkl
2025-06-26 21:06:54,672 - __main__ - INFO - lin105 开始进化第 5 代
2025-06-26 21:06:54,672 - __main__ - INFO - 开始分析阶段
2025-06-26 21:06:54,672 - StatsExpert - INFO - 开始统计分析
2025-06-26 21:06:54,708 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 36480.0, 'max': 135562.0, 'mean': 107153.3, 'std': 28004.036227122688}, 'diversity': 0.9424338624338623, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 21:06:54,715 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 36480.0, 'max': 135562.0, 'mean': 107153.3, 'std': 28004.036227122688}, 'diversity_level': 0.9424338624338623, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[63, 71], [94, 71], [142, 370], [173, 1276], [205, 1213], [213, 69], [244, 69], [276, 630], [283, 732], [362, 69], [394, 69], [449, 370], [480, 1276], [512, 1213], [528, 157], [583, 630], [591, 732], [638, 654], [638, 496], [638, 314], [638, 142], [669, 142], [677, 315], [677, 496], [677, 654], [709, 654], [709, 496], [709, 315], [701, 142], [764, 220], [811, 189], [843, 173], [858, 370], [890, 1276], [921, 1213], [992, 630], [1000, 732], [1197, 1276], [1228, 1213], [1276, 205], [1299, 630], [1307, 732], [1362, 654], [1362, 496], [1362, 291], [1425, 654], [1425, 496], [1425, 291], [1417, 173], [1488, 291], [1488, 496], [1488, 654], [1551, 654], [1551, 496], [1551, 291], [1614, 291], [1614, 496], [1614, 654], [1732, 189], [1811, 1276], [1843, 1213], [1913, 630], [1921, 732], [2087, 370], [2118, 1276], [2150, 1213], [2189, 205], [2220, 189], [2220, 630], [2228, 732], [2244, 142], [2276, 315], [2276, 496], [2276, 654], [2315, 654], [2315, 496], [2315, 315], [2331, 142], [2346, 315], [2346, 496], [2346, 654], [2362, 142], [2402, 157], [2402, 220], [2480, 142], [2496, 370], [2528, 1276], [2559, 1213], [2630, 630], [2638, 732], [2756, 69], [2787, 69], [2803, 370], [2835, 1276], [2866, 1213], [2906, 69], [2937, 69], [2937, 630], [2945, 732], [3016, 1276], [3055, 69], [3087, 69], [606, 220], [1165, 370], [1780, 370]], 'distance_matrix': array([[   0.,   31.,  309., ...,  563., 1142., 1743.],
       [  31.,    0.,  303., ...,  533., 1112., 1712.],
       [ 309.,  303.,    0., ...,  488., 1023., 1638.],
       ...,
       [ 563.,  533.,  488., ...,    0.,  579., 1184.],
       [1142., 1112., 1023., ...,  579.,    0.,  615.],
       [1743., 1712., 1638., ..., 1184.,  615.,    0.]])}
2025-06-26 21:06:54,715 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 21:06:54,715 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 21:06:54,715 - PathExpert - INFO - 开始路径结构分析
2025-06-26 21:06:54,719 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 21:06:54,719 - PathExpert - INFO - 路径结构分析完成
2025-06-26 21:06:54,719 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 21:06:54,719 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 21:06:54,719 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 21:06:54,722 - EliteExpert - INFO - 开始精英解分析
2025-06-26 21:06:54,726 - EliteExpert - INFO - 精英解分析完成
2025-06-26 21:06:54,727 - __main__ - INFO - 精英专家分析报告: {'elite_count': 12, 'elite_common_features': {'common_edges': {'(49, 54)': 0.75, '(54, 55)': 0.75, '(55, 58)': 0.75}, 'common_edge_ratio': 0.02857142857142857}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 22101.0, 'avg_gap': 92268.05}, 'structure_gap': {'unique_elite_edges': 244, 'unique_pop_edges': 809, 'common_edges': 57}}, 'elite_diversity': {'diversity_score': 0.16666666666666663}}
2025-06-26 21:06:54,727 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 21:06:54,727 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 21:06:54,728 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 21:06:54,728 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 36480.0, mean 107153.3, max 135562.0, std 28004.036227122688
- diversity: 0.9424338624338623
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [86026.0, 87110.0, 36480.0] (Δ 25315.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [63.0, 69.0, 3087.0, 1276.0]
- centroid/spread: [1538.962, 525.343] | rms 953.602
- density_grid(3x3): [[20, 10, 20], [12, 14, 12], [6, 4, 7]]
- edge_len_stats(min,q1,med,q3,max): [31.0, 664.0, 1070.0, 1638.0, 3189.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [0, 2, 39, 4, 14, 31, 13, 1]
- nn_median_dist: 63.0

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 21:06:54,730 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:07:18,403 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 31.0 to 3189.0, with significant std deviation in costs)",
    "modality": "Potentially multimodal (high diversity score, no clustering detected, recent best costs show large jumps)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.249 suggests some paths may lead away from optima)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (diversity 0.94, density grid shows activity in all cells)",
    "convergence_status": "Early exploration phase (0.0 convergence, no elite solutions yet)"
  },
  "difficult_regions": [
    "Northwest quadrant (lowest density grid cell with only 4 nodes)",
    "Long edges in edge_len_stats (top quartile >1638.0 length)"
  ],
  "opportunity_regions": [
    "Central-east region (highest density grid cell with 20 nodes)",
    "Southeast directional sector (31 edges in sector 5)"
  ],
  "evolution_phase": "Early exploration",
  "evolution_direction": [
    "Intensify search in high-density opportunity regions using path consolidation operators",
    "Apply edge recombination in long-edge corridors to reduce fragmentation",
    "Maintain diversity through restart mechanisms in low-density regions",
    "Implement directed local search based on directional histogram biases"
  ]
}
```
2025-06-26 21:07:18,403 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 21:07:18,403 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 31.0 to 3189.0, with significant std deviation in costs)', 'modality': 'Potentially multimodal (high diversity score, no clustering detected, recent best costs show large jumps)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.249 suggests some paths may lead away from optima)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.94, density grid shows activity in all cells)', 'convergence_status': 'Early exploration phase (0.0 convergence, no elite solutions yet)'}, 'difficult_regions': ['Northwest quadrant (lowest density grid cell with only 4 nodes)', 'Long edges in edge_len_stats (top quartile >1638.0 length)'], 'opportunity_regions': ['Central-east region (highest density grid cell with 20 nodes)', 'Southeast directional sector (31 edges in sector 5)'], 'evolution_phase': 'Early exploration', 'evolution_direction': ['Intensify search in high-density opportunity regions using path consolidation operators', 'Apply edge recombination in long-edge corridors to reduce fragmentation', 'Maintain diversity through restart mechanisms in low-density regions', 'Implement directed local search based on directional histogram biases']}
2025-06-26 21:07:18,403 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 21:07:18,403 - __main__ - INFO - 分析阶段完成
2025-06-26 21:07:18,403 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 31.0 to 3189.0, with significant std deviation in costs)', 'modality': 'Potentially multimodal (high diversity score, no clustering detected, recent best costs show large jumps)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.249 suggests some paths may lead away from optima)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.94, density grid shows activity in all cells)', 'convergence_status': 'Early exploration phase (0.0 convergence, no elite solutions yet)'}, 'difficult_regions': ['Northwest quadrant (lowest density grid cell with only 4 nodes)', 'Long edges in edge_len_stats (top quartile >1638.0 length)'], 'opportunity_regions': ['Central-east region (highest density grid cell with 20 nodes)', 'Southeast directional sector (31 edges in sector 5)'], 'evolution_phase': 'Early exploration', 'evolution_direction': ['Intensify search in high-density opportunity regions using path consolidation operators', 'Apply edge recombination in long-edge corridors to reduce fragmentation', 'Maintain diversity through restart mechanisms in low-density regions', 'Implement directed local search based on directional histogram biases']}
2025-06-26 21:07:18,403 - __main__ - INFO - 开始策略分配阶段
2025-06-26 21:07:18,403 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 21:07:18,403 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 36480.0
  • mean_cost: 107153.3
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 31.0 to 3189.0, with significant std deviation in costs), modality Potentially multimodal (high diversity score, no clustering detected, recent best costs show large jumps), deceptiveness Possible deceptive regions (long_edge_ratio of 0.249 suggests some paths may lead away from optima)
- Evolution phase: Early exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Northwest quadrant (, Long edges in edge_l
- Opportunity regions (# 2): Central-east region , Southeast directiona
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 21:07:18,403 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 21:07:18,403 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:07:29,303 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 60% explore to discover new regions, bottom 40% exploit current best."
}
```
2025-06-26 21:07:29,303 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 21:07:29,303 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 21:07:29,303 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 60% explore to discover new regions, bottom 40% exploit current best."
}
```
2025-06-26 21:07:29,303 - __main__ - INFO - 策略分配阶段完成
2025-06-26 21:07:29,303 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 21:07:29,303 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration. Top 60% explore to discover new regions, bottom 40% exploit current best."
}
```
2025-06-26 21:07:29,303 - __main__ - INFO - 开始进化阶段
2025-06-26 21:07:29,303 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 21:07:29,303 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:07:29,303 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 87110.0 (rank 10.0%)
- Population diversity: 0.7865384615384615
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): Central-east re, Southeast direc
- Difficult regions to avoid (sample): Northwest quadr, Long edges in e
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:07:29,303 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:07:29,303 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:07:51,085 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103, 104],
  "targeted_regions": "Central-east, Southeast",
  "strategy_comment": "Leverage grid traversal to explore sparse cells while avoiding difficult regions"
}
```
2025-06-26 21:07:51,085 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:07:51,085 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87221.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103, 104]
2025-06-26 21:07:51,085 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 87221.0}
2025-06-26 21:07:51,085 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 21:07:51,085 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:07:51,085 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 123889.0 (rank 70.0%)
- Population diversity: 0.7961538461538461
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): Central-east re, Southeast direc
- Difficult regions to avoid (sample): Northwest quadr, Long edges in e
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:07:51,085 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:07:51,089 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:08:13,418 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 105, 94, 82, 70, 58, 46, 34, 22, 10, 6, 18, 30, 42, 54, 66, 78, 90, 102, 103, 91, 79, 67, 55, 43, 31, 19, 7, 3, 15, 27, 39, 51, 63, 75, 87, 99, 98, 86, 74, 62, 50, 38, 26, 14, 2, 13, 25, 37, 49, 61, 73, 85, 97, 95, 83, 71, 59, 47, 35, 23, 11, 1],
  "targeted_regions": "Central-east, Southeast",
  "strategy_comment": "Spiral pattern through opportunity regions, avoiding northwest"
}
```
2025-06-26 21:08:13,418 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:08:13,420 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64920.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 91, 94, 82, 70, 58, 46, 34, 22, 10, 6, 18, 30, 42, 54, 66, 78, 90, 102, 103, 79, 67, 55, 43, 31, 19, 7, 3, 15, 27, 39, 51, 63, 75, 87, 99, 98, 86, 74, 62, 50, 38, 26, 14, 2, 13, 25, 37, 49, 61, 73, 85, 97, 95, 83, 71, 59, 47, 35, 23, 11, 1]
2025-06-26 21:08:13,420 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 91, 94, 82, 70, 58, 46, 34, 22, 10, 6, 18, 30, 42, 54, 66, 78, 90, 102, 103, 79, 67, 55, 43, 31, 19, 7, 3, 15, 27, 39, 51, 63, 75, 87, 99, 98, 86, 74, 62, 50, 38, 26, 14, 2, 13, 25, 37, 49, 61, 73, 85, 97, 95, 83, 71, 59, 47, 35, 23, 11, 1], 'cur_cost': 64920.0}
2025-06-26 21:08:13,420 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 21:08:13,420 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:08:13,420 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 36480.0 (rank 0.0%)
- Population diversity: 0.760576923076923
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): Central-east re, Southeast direc
- Difficult regions to avoid (sample): Northwest quadr, Long edges in e
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:08:13,420 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:08:13,420 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:08:34,524 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 67, 78, 89, 100, 11, 22, 33, 44, 55, 66, 77, 88, 99, 10, 21, 32, 43, 54, 65, 76, 87, 98, 9, 20, 31, 42, 53, 64, 75, 86, 97, 8, 19, 30, 41, 52, 63, 74, 85, 96, 7, 18, 29, 40, 51, 62, 73, 84, 95, 6, 17, 28, 39, 50, 61, 72, 83, 94, 5, 16, 27, 38, 49, 60, 71, 82, 93, 4, 15, 26, 37, 48, 59, 70, 81, 92, 3, 14, 25, 36, 47, 58, 69, 80, 91, 2, 13, 24, 35, 46, 57, 68, 79, 90, 1, 104, 103, 102, 101],
  "targeted_regions": "Central-east, Southeast",
  "strategy_comment": "Sequential sweep through opportunity regions, avoiding northwest"
}
```
2025-06-26 21:08:34,524 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:08:34,524 - ExplorationExpert - INFO - 探索路径生成完成，成本: 90867.0, 路径: [0, 12, 23, 34, 45, 56, 67, 78, 89, 100, 11, 22, 33, 44, 55, 66, 77, 88, 99, 10, 21, 32, 43, 54, 65, 76, 87, 98, 9, 20, 31, 42, 53, 64, 75, 86, 97, 8, 19, 30, 41, 52, 63, 74, 85, 96, 7, 18, 29, 40, 51, 62, 73, 84, 95, 6, 17, 28, 39, 50, 61, 72, 83, 94, 5, 16, 27, 38, 49, 60, 71, 82, 93, 4, 15, 26, 37, 48, 59, 70, 81, 92, 3, 14, 25, 36, 47, 58, 69, 80, 91, 2, 13, 24, 35, 46, 57, 68, 79, 90, 1, 104, 103, 102, 101]
2025-06-26 21:08:34,527 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 67, 78, 89, 100, 11, 22, 33, 44, 55, 66, 77, 88, 99, 10, 21, 32, 43, 54, 65, 76, 87, 98, 9, 20, 31, 42, 53, 64, 75, 86, 97, 8, 19, 30, 41, 52, 63, 74, 85, 96, 7, 18, 29, 40, 51, 62, 73, 84, 95, 6, 17, 28, 39, 50, 61, 72, 83, 94, 5, 16, 27, 38, 49, 60, 71, 82, 93, 4, 15, 26, 37, 48, 59, 70, 81, 92, 3, 14, 25, 36, 47, 58, 69, 80, 91, 2, 13, 24, 35, 46, 57, 68, 79, 90, 1, 104, 103, 102, 101], 'cur_cost': 90867.0}
2025-06-26 21:08:34,527 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 21:08:34,527 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:08:34,527 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 123464.0 (rank 70.0%)
- Population diversity: 0.760576923076923
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): Central-east re, Southeast direc
- Difficult regions to avoid (sample): Northwest quadr, Long edges in e
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:08:34,527 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:08:34,527 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:08:55,973 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 102, 90, 78, 66, 54, 42, 30, 18, 6, 10, 22, 34, 46, 58, 70, 82, 94, 103, 91, 79, 67, 55, 43, 31, 19, 7, 11, 23, 35, 47, 59, 71, 83, 95, 97, 85, 73, 61, 49, 37, 25, 13, 1, 14, 26, 38, 50, 62, 74, 86, 98, 99, 87, 75, 63, 51, 39, 27, 15, 3, 2],
  "targeted_regions": "Central-east, Southeast",
  "strategy_comment": "Leverage diagonal traversal to explore sparse cells while avoiding northwest"
}
```
2025-06-26 21:08:55,975 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:08:55,975 - ExplorationExpert - INFO - 探索路径生成完成，成本: 68101.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 102, 90, 78, 66, 54, 42, 30, 18, 6, 10, 22, 34, 46, 58, 70, 82, 94, 103, 91, 79, 67, 55, 43, 31, 19, 7, 11, 23, 35, 47, 59, 71, 83, 95, 97, 85, 73, 61, 49, 37, 25, 13, 1, 14, 26, 38, 50, 62, 74, 86, 98, 99, 87, 75, 63, 51, 39, 27, 15, 3, 2]
2025-06-26 21:08:55,975 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 102, 90, 78, 66, 54, 42, 30, 18, 6, 10, 22, 34, 46, 58, 70, 82, 94, 103, 91, 79, 67, 55, 43, 31, 19, 7, 11, 23, 35, 47, 59, 71, 83, 95, 97, 85, 73, 61, 49, 37, 25, 13, 1, 14, 26, 38, 50, 62, 74, 86, 98, 99, 87, 75, 63, 51, 39, 27, 15, 3, 2], 'cur_cost': 68101.0}
2025-06-26 21:08:55,975 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 21:08:55,975 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:08:55,975 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 119871.0 (rank 60.0%)
- Population diversity: 0.6759615384615385
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): Central-east re, Southeast direc
- Difficult regions to avoid (sample): Northwest quadr, Long edges in e
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:08:55,978 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:08:55,978 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:09:18,375 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 1, 13, 25, 37, 49, 61, 73, 85, 97, 89, 77, 65, 53, 41, 29, 17, 5, 2, 14, 26, 38, 50, 62, 74, 86, 98, 90, 78, 66, 54, 42, 30, 18, 6, 3, 15, 27, 39, 51, 63, 75, 87, 99, 91, 79, 67, 55, 43, 31, 19, 7, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 102, 103, 93, 81, 69, 57, 45, 33, 21, 9, 10, 22, 34, 46, 58, 70, 82, 94, 95, 83, 71, 59, 47, 35, 23, 11],
  "targeted_regions": "Central-east, Southeast",
  "strategy_comment": "Spiral pattern focusing on opportunity regions, avoiding northwest"
}
```
2025-06-26 21:09:18,375 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:09:18,375 - ExplorationExpert - INFO - 探索路径生成完成，成本: 67361.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 1, 13, 25, 37, 49, 61, 73, 85, 97, 89, 77, 65, 53, 41, 29, 17, 5, 2, 14, 26, 38, 50, 62, 74, 86, 98, 90, 78, 66, 54, 42, 30, 18, 6, 3, 15, 27, 39, 51, 63, 75, 87, 99, 91, 79, 67, 55, 43, 31, 19, 7, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 102, 103, 93, 81, 69, 57, 45, 33, 21, 9, 10, 22, 34, 46, 58, 70, 82, 94, 95, 83, 71, 59, 47, 35, 23, 11]
2025-06-26 21:09:18,375 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 1, 13, 25, 37, 49, 61, 73, 85, 97, 89, 77, 65, 53, 41, 29, 17, 5, 2, 14, 26, 38, 50, 62, 74, 86, 98, 90, 78, 66, 54, 42, 30, 18, 6, 3, 15, 27, 39, 51, 63, 75, 87, 99, 91, 79, 67, 55, 43, 31, 19, 7, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 102, 103, 93, 81, 69, 57, 45, 33, 21, 9, 10, 22, 34, 46, 58, 70, 82, 94, 95, 83, 71, 59, 47, 35, 23, 11], 'cur_cost': 67361.0}
2025-06-26 21:09:18,375 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 21:09:18,377 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 21:09:18,377 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 112305.0 (rank 60.0%)
- Population diversity: 0.5961538461538461
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): Central-east re, Southeast direc
- Difficult regions to avoid (sample): Northwest quadr, Long edges in e
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 104]  
• Path length == 105 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 21:09:18,379 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 21:09:18,379 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:09:39,243 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95],
  "targeted_regions": "Central-east, Southeast",
  "strategy_comment": "Sequential sweep through opportunity regions, avoiding northwest"
}
```
2025-06-26 21:09:39,244 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 21:09:39,245 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87110.0, 路径: [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95]
2025-06-26 21:09:39,245 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}
2025-06-26 21:09:39,245 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 21:09:39,245 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:09:39,245 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:09:39,246 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 124615.0
2025-06-26 21:09:39,246 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：73,跳过局部搜索
2025-06-26 21:09:39,247 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 21:09:39,247 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([ 12,  66,  21,  98,  17,  70,  47,  25,  44,  19,  13,  76,  90,
        60,   6,  42,  92,  31,  99,  26,  20,  82,  54,  93,  62,   7,
        24,  74,  43,  80,  34,  87,  95,  75,  55,   9,  49,  46,  10,
        63,  64,  14,  45,  73,  32,  67,  52,  71,  94,  23,  50,  33,
        68,  40,  65,  89,  83,  51,  58,   4,  41, 101,   2,  30, 104,
        11,  56,  22, 102,  27, 103,  72,  15,  69,  79,   5,   0,  38,
        53,  97,  37,  57,  36,   3,   8,   1,  78,  85,  35,  61,  28,
        29,  16, 100,  96,  48,  18,  77,  84,  81,  59,  39,  91,  86,
        88]), 'cur_cost': 124615.0}
2025-06-26 21:09:39,247 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 21:09:39,247 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:09:39,248 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:09:39,248 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 129186.0
2025-06-26 21:09:39,752 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:09:39,752 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:09:39,753 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:09:39,760 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:09:39,760 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 87221.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 91, 94, 82, 70, 58, 46, 34, 22, 10, 6, 18, 30, 42, 54, 66, 78, 90, 102, 103, 79, 67, 55, 43, 31, 19, 7, 3, 15, 27, 39, 51, 63, 75, 87, 99, 98, 86, 74, 62, 50, 38, 26, 14, 2, 13, 25, 37, 49, 61, 73, 85, 97, 95, 83, 71, 59, 47, 35, 23, 11, 1], 'cur_cost': 64920.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 78, 89, 100, 11, 22, 33, 44, 55, 66, 77, 88, 99, 10, 21, 32, 43, 54, 65, 76, 87, 98, 9, 20, 31, 42, 53, 64, 75, 86, 97, 8, 19, 30, 41, 52, 63, 74, 85, 96, 7, 18, 29, 40, 51, 62, 73, 84, 95, 6, 17, 28, 39, 50, 61, 72, 83, 94, 5, 16, 27, 38, 49, 60, 71, 82, 93, 4, 15, 26, 37, 48, 59, 70, 81, 92, 3, 14, 25, 36, 47, 58, 69, 80, 91, 2, 13, 24, 35, 46, 57, 68, 79, 90, 1, 104, 103, 102, 101], 'cur_cost': 90867.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 102, 90, 78, 66, 54, 42, 30, 18, 6, 10, 22, 34, 46, 58, 70, 82, 94, 103, 91, 79, 67, 55, 43, 31, 19, 7, 11, 23, 35, 47, 59, 71, 83, 95, 97, 85, 73, 61, 49, 37, 25, 13, 1, 14, 26, 38, 50, 62, 74, 86, 98, 99, 87, 75, 63, 51, 39, 27, 15, 3, 2], 'cur_cost': 68101.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 1, 13, 25, 37, 49, 61, 73, 85, 97, 89, 77, 65, 53, 41, 29, 17, 5, 2, 14, 26, 38, 50, 62, 74, 86, 98, 90, 78, 66, 54, 42, 30, 18, 6, 3, 15, 27, 39, 51, 63, 75, 87, 99, 91, 79, 67, 55, 43, 31, 19, 7, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 102, 103, 93, 81, 69, 57, 45, 33, 21, 9, 10, 22, 34, 46, 58, 70, 82, 94, 95, 83, 71, 59, 47, 35, 23, 11], 'cur_cost': 67361.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([ 12,  66,  21,  98,  17,  70,  47,  25,  44,  19,  13,  76,  90,
        60,   6,  42,  92,  31,  99,  26,  20,  82,  54,  93,  62,   7,
        24,  74,  43,  80,  34,  87,  95,  75,  55,   9,  49,  46,  10,
        63,  64,  14,  45,  73,  32,  67,  52,  71,  94,  23,  50,  33,
        68,  40,  65,  89,  83,  51,  58,   4,  41, 101,   2,  30, 104,
        11,  56,  22, 102,  27, 103,  72,  15,  69,  79,   5,   0,  38,
        53,  97,  37,  57,  36,   3,   8,   1,  78,  85,  35,  61,  28,
        29,  16, 100,  96,  48,  18,  77,  84,  81,  59,  39,  91,  86,
        88]), 'cur_cost': 124615.0}, {'tour': array([ 40,   8,  86, 103,  76,  70,  73,  10,  29,  27,  90,  75,  81,
        78,  18,  50,  77,  83,  74,  34,  89,  14,  84,  47,   4,   1,
        82,  96,  12,  43,  91,   7,  64,  37,  35,  58,  55,  26,  60,
        17,  24,  32,  36, 100,  21,  49,  45,  95,  38,  33,  54,   0,
        66,   5,  69,  15,  52,  13,  39,  99,  93,  92,  46,  30,  65,
        56,   3,  63,  16,  28,  31,  79,  41,  20,  42, 102,  87,  80,
        71,   2,  97,  88,  19,  51,  23,  44,  68,  61,  85,   6, 104,
         9, 101,  94,  11,  98,  48,  57,  22,  62,  67,  25,  53,  72,
        59]), 'cur_cost': 129186.0}, {'tour': [0, 12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 26, 59, 82, 15, 48, 71, 4, 27, 60, 83, 16, 49, 72, 5, 28, 61, 84, 17, 50, 73, 6, 29, 62, 85, 18, 51, 74, 7, 30, 63, 86, 19, 52, 75, 8, 31, 64, 87, 20, 53, 76, 9, 32, 65, 88, 21, 54, 77, 10, 33, 66, 99, 22, 55, 11, 44, 100, 41, 42, 38, 101, 92, 25, 95, 40, 69, 97, 43, 37, 93, 98, 36, 96, 58, 81, 39, 103, 102, 94, 104], 'cur_cost': 135562.0}, {'tour': array([ 91,   5,  83,  81,  31,  48,   8,  33,  86,  18,  32,  98,  69,
        40,  94,  70,  72,  13,  47, 100,  39,  21,  41,  57, 104,  93,
         2,  66,  77,  88,   0, 101,  74,  16,  38,  67,  54,  35,  26,
        19,  52,  23,  79,  51,  36,  24,  92,  95,  45,  62,  68,   3,
        56,  42,  75,  20,  90,  14,   4,  65,  63,  84,  25,  85, 103,
         7,  34,  59,   9,  28,  43,  50,  64,  53,  80,  78,  55,   6,
        37,  61,  99,  71,  11, 102,  82,  58,  46,  73,  10,  29,  44,
        49,  96,  17,  30,  97,  27,  60,  22,   1,  87,  76,  15,  12,
        89]), 'cur_cost': 124936.0}]
2025-06-26 21:09:39,762 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:09:39,763 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 86, 'skip_rate': 0.05813953488372093, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 81, 'cache_hits': 48, 'similarity_calculations': 674, 'cache_hit_rate': 0.0712166172106825, 'cache_size': 626}}
2025-06-26 21:09:39,763 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 21:09:39,763 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 21:09:39,763 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:09:39,763 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:09:39,763 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 134529.0
2025-06-26 21:09:40,264 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:09:40,264 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:09:40,264 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:09:40,270 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:09:40,271 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 87221.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 91, 94, 82, 70, 58, 46, 34, 22, 10, 6, 18, 30, 42, 54, 66, 78, 90, 102, 103, 79, 67, 55, 43, 31, 19, 7, 3, 15, 27, 39, 51, 63, 75, 87, 99, 98, 86, 74, 62, 50, 38, 26, 14, 2, 13, 25, 37, 49, 61, 73, 85, 97, 95, 83, 71, 59, 47, 35, 23, 11, 1], 'cur_cost': 64920.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 78, 89, 100, 11, 22, 33, 44, 55, 66, 77, 88, 99, 10, 21, 32, 43, 54, 65, 76, 87, 98, 9, 20, 31, 42, 53, 64, 75, 86, 97, 8, 19, 30, 41, 52, 63, 74, 85, 96, 7, 18, 29, 40, 51, 62, 73, 84, 95, 6, 17, 28, 39, 50, 61, 72, 83, 94, 5, 16, 27, 38, 49, 60, 71, 82, 93, 4, 15, 26, 37, 48, 59, 70, 81, 92, 3, 14, 25, 36, 47, 58, 69, 80, 91, 2, 13, 24, 35, 46, 57, 68, 79, 90, 1, 104, 103, 102, 101], 'cur_cost': 90867.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 102, 90, 78, 66, 54, 42, 30, 18, 6, 10, 22, 34, 46, 58, 70, 82, 94, 103, 91, 79, 67, 55, 43, 31, 19, 7, 11, 23, 35, 47, 59, 71, 83, 95, 97, 85, 73, 61, 49, 37, 25, 13, 1, 14, 26, 38, 50, 62, 74, 86, 98, 99, 87, 75, 63, 51, 39, 27, 15, 3, 2], 'cur_cost': 68101.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 1, 13, 25, 37, 49, 61, 73, 85, 97, 89, 77, 65, 53, 41, 29, 17, 5, 2, 14, 26, 38, 50, 62, 74, 86, 98, 90, 78, 66, 54, 42, 30, 18, 6, 3, 15, 27, 39, 51, 63, 75, 87, 99, 91, 79, 67, 55, 43, 31, 19, 7, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 102, 103, 93, 81, 69, 57, 45, 33, 21, 9, 10, 22, 34, 46, 58, 70, 82, 94, 95, 83, 71, 59, 47, 35, 23, 11], 'cur_cost': 67361.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([ 12,  66,  21,  98,  17,  70,  47,  25,  44,  19,  13,  76,  90,
        60,   6,  42,  92,  31,  99,  26,  20,  82,  54,  93,  62,   7,
        24,  74,  43,  80,  34,  87,  95,  75,  55,   9,  49,  46,  10,
        63,  64,  14,  45,  73,  32,  67,  52,  71,  94,  23,  50,  33,
        68,  40,  65,  89,  83,  51,  58,   4,  41, 101,   2,  30, 104,
        11,  56,  22, 102,  27, 103,  72,  15,  69,  79,   5,   0,  38,
        53,  97,  37,  57,  36,   3,   8,   1,  78,  85,  35,  61,  28,
        29,  16, 100,  96,  48,  18,  77,  84,  81,  59,  39,  91,  86,
        88]), 'cur_cost': 124615.0}, {'tour': array([ 40,   8,  86, 103,  76,  70,  73,  10,  29,  27,  90,  75,  81,
        78,  18,  50,  77,  83,  74,  34,  89,  14,  84,  47,   4,   1,
        82,  96,  12,  43,  91,   7,  64,  37,  35,  58,  55,  26,  60,
        17,  24,  32,  36, 100,  21,  49,  45,  95,  38,  33,  54,   0,
        66,   5,  69,  15,  52,  13,  39,  99,  93,  92,  46,  30,  65,
        56,   3,  63,  16,  28,  31,  79,  41,  20,  42, 102,  87,  80,
        71,   2,  97,  88,  19,  51,  23,  44,  68,  61,  85,   6, 104,
         9, 101,  94,  11,  98,  48,  57,  22,  62,  67,  25,  53,  72,
        59]), 'cur_cost': 129186.0}, {'tour': array([  2,  53,  58,  63,  37,  99,  46,   4,  65,   1, 103,  30,  10,
        90,  82,  57,  91,  22,  34,  11,  94,  26,  86,  25,  98,   0,
        93,   9,  88,  36,   6,  89,  49,  96,  31,  23,  87,  24,  64,
        71,  62,   8,  38,  67,  41,  51,  33,  81,  60,  61,  32, 101,
        43,  97,  12,  80,  17,   7,  18,  40,  29,  76,  44,  95,  35,
        56,  92,  79, 104,  84,  27,  66,   3,  45,  14, 102,  52,  15,
        39,   5,  72,  78,  83,  74,  59,  13,  21,  69,  42,  19,  16,
        77,  73,  85,  28,  68,  54,  50,  75,  48,  70,  55,  47, 100,
        20]), 'cur_cost': 134529.0}, {'tour': array([ 91,   5,  83,  81,  31,  48,   8,  33,  86,  18,  32,  98,  69,
        40,  94,  70,  72,  13,  47, 100,  39,  21,  41,  57, 104,  93,
         2,  66,  77,  88,   0, 101,  74,  16,  38,  67,  54,  35,  26,
        19,  52,  23,  79,  51,  36,  24,  92,  95,  45,  62,  68,   3,
        56,  42,  75,  20,  90,  14,   4,  65,  63,  84,  25,  85, 103,
         7,  34,  59,   9,  28,  43,  50,  64,  53,  80,  78,  55,   6,
        37,  61,  99,  71,  11, 102,  82,  58,  46,  73,  10,  29,  44,
        49,  96,  17,  30,  97,  27,  60,  22,   1,  87,  76,  15,  12,
        89]), 'cur_cost': 124936.0}]
2025-06-26 21:09:40,273 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:09:40,274 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 87, 'skip_rate': 0.05747126436781609, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 82, 'cache_hits': 48, 'similarity_calculations': 687, 'cache_hit_rate': 0.06986899563318777, 'cache_size': 639}}
2025-06-26 21:09:40,274 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 21:09:40,274 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 21:09:40,274 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 21:09:40,274 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 21:09:40,275 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 121867.0
2025-06-26 21:09:40,776 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 21:09:40,778 - ExploitationExpert - INFO - res_population_costs: [14379, 14401, 14514, 14545, 14575, 14944, 14947, 15100, 15200, 15230, 15382, 15406]
2025-06-26 21:09:40,778 - ExploitationExpert - INFO - res_populations: [array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,
        60,  64,  65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  92,
       101, 100,  96,  95,  91,  90,  84,  83,  82,  81,  77,  70,  67,
        66,  63,  71,  76,  78,  85,  79,  75,  72,  80,  74,  73,  68,
        69,  62,  61, 104,  58,  55,  54,  49,  47,  44,  48,  39, 103,
        43,  46,  50,  53,  56,  57,  52,  51,  45,  42,  40,  41,  36,
        35,  25,  24,  17,  16,  15,  26,  23,  18,  11,  19,  22,  27,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,  71,
        63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,  96,
       100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  61,
        62,  69,  68,  73,  74,  80,  89,  88,  85,  78,  76,  71,  79,
        75,  72,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,
        95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  27,  22,  19,  11,  18,  23,  26,  15,  16,  17,  24,
        25,  36,  35, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,
        56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,  62,
        61,  63,  71,  76,  78,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  79,  75,  72,  68,  69,
        73,  74,  80,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,
        64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,   7,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  44,  47,  49,  54,  55,  58,
       104,  56,  53,  50,  46,  43,  40,  41,  42,  45,  51,  52,  57,
        61,  62,  69,  68,  73,  74,  80,  72,  75,  79,  85,  78,  76,
        71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,  91,  95,
        96, 100, 101,  92,  88,  89,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32,  35,  36, 103,  39,  48,  47,  44,  43,  40,  41,  42,
        45,  51,  52,  57,  56,  53,  50,  46,  49,  54,  55,  58, 104,
        61,  62,  69,  68,  72,  75,  79,  73,  74,  80,  89,  88,  85,
        78,  76,  71,  63,  66,  67,  70,  77,  81,  82,  83,  84,  90,
        91,  95,  96, 100, 101,  92,  97,  98,  99,  94,  93,  87,  86,
        65,  64,  60,  59,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  59,  60,
        64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63,  71,
        76,  78,  85,  88,  89,  80,  74,  73,  79,  75,  72,  68,  69,
        62,  61, 104,  58,  55,  54,  53,  56,  57,  52,  51,  45,  42,
        41,  40,  43,  46,  50,  49,  47,  44,  48,  39, 103,  36,  35,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  39,  48,  44,  47,  49,  54,  55,  58, 104,  63,
        66,  67,  70,  77,  81,  82,  83,  78,  76,  71,  72,  68,  69,
        73,  74,  80,  75,  79,  85,  84,  90,  91,  95,  96, 100, 101,
        92,  88,  89,  97,  98,  99,  94,  93,  87,  86,  65,  64,  59,
        60,  62,  61,  56,  53,  50,  46,  43,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   1,   5,   6,   9,  10,  14, 102,  20,  21,  28,  29,  30,
        31,  32, 103,  43,  46,  50,  53,  56,  47,  44,  39,  48,  49,
        54,  55,  58, 104,  63,  66,  67,  70,  77,  81,  82,  83,  84,
        90,  91,  95,  96, 100, 101,  92,  85,  78,  76,  71,  72,  68,
        69,  73,  74,  80,  75,  79,  88,  89,  97,  98,  99,  94,  93,
        87,  86,  65,  64,  59,  60,  62,  61,  57,  52,  51,  45,  42,
        40,  41,  35,  36,  38,  37,  34,  33,  13,  12,   3,   4,   8,
         7,  16,  15,  17,  24,  25,  26,  23,  18,  27,  22,  19,  11,
         2], dtype=int64), array([  0,   2,  11,  19,  22,  27,  18,  23,  26,  25,  24,  17,  15,
        16,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  36,  35,
        41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,  65,
        86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,  74,
        73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,  95,
        91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,  56,
        53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39, 103,
        32,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,  11,  19,  22,  27,  32,  35,  36,  25,  24,  26,  23,
        18,  15,  17,  16,   7,   8,   4,   3,  12,  13,  33,  34,  37,
        38,  41,  40,  42,  45,  51,  52,  57,  61,  62,  60,  59,  64,
        65,  86,  87,  93,  94,  99,  98,  97,  89,  88,  79,  75,  80,
        74,  73,  69,  68,  72,  71,  76,  78,  85,  92, 101, 100,  96,
        95,  91,  90,  84,  83,  82,  81,  77,  70,  67,  66,  63, 104,
        56,  53,  50,  46,  43,  44,  47,  49,  54,  55,  58,  48,  39,
       103,  31,  30,  29,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64), array([  0,   2,   7,   8,   4,   3,  12,  13,  33,  34,  37,  38,  41,
        40,  42,  45,  51,  46,  43,  44,  47,  50,  53,  56,  52,  57,
        60,  59,  64,  65,  86,  87,  93,  94,  99,  98,  97,  92, 101,
       100,  96,  95,  91,  90,  84,  82,  81,  77,  83,  78,  76,  71,
        70,  67,  66,  63,  72,  75,  79,  85,  88,  89,  80,  74,  73,
        68,  69,  62,  61, 104,  58,  55,  54,  49,  48,  39, 103,  36,
        35,  32,  26,  23,  18,  25,  24,  17,  16,  15,  11,  19,  22,
        27,  29,  30,  31,  28,  21,  20, 102,  14,  10,   9,   6,   5,
         1], dtype=int64)]
2025-06-26 21:09:40,784 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 21:09:40,785 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 87221.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 91, 94, 82, 70, 58, 46, 34, 22, 10, 6, 18, 30, 42, 54, 66, 78, 90, 102, 103, 79, 67, 55, 43, 31, 19, 7, 3, 15, 27, 39, 51, 63, 75, 87, 99, 98, 86, 74, 62, 50, 38, 26, 14, 2, 13, 25, 37, 49, 61, 73, 85, 97, 95, 83, 71, 59, 47, 35, 23, 11, 1], 'cur_cost': 64920.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 78, 89, 100, 11, 22, 33, 44, 55, 66, 77, 88, 99, 10, 21, 32, 43, 54, 65, 76, 87, 98, 9, 20, 31, 42, 53, 64, 75, 86, 97, 8, 19, 30, 41, 52, 63, 74, 85, 96, 7, 18, 29, 40, 51, 62, 73, 84, 95, 6, 17, 28, 39, 50, 61, 72, 83, 94, 5, 16, 27, 38, 49, 60, 71, 82, 93, 4, 15, 26, 37, 48, 59, 70, 81, 92, 3, 14, 25, 36, 47, 58, 69, 80, 91, 2, 13, 24, 35, 46, 57, 68, 79, 90, 1, 104, 103, 102, 101], 'cur_cost': 90867.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 102, 90, 78, 66, 54, 42, 30, 18, 6, 10, 22, 34, 46, 58, 70, 82, 94, 103, 91, 79, 67, 55, 43, 31, 19, 7, 11, 23, 35, 47, 59, 71, 83, 95, 97, 85, 73, 61, 49, 37, 25, 13, 1, 14, 26, 38, 50, 62, 74, 86, 98, 99, 87, 75, 63, 51, 39, 27, 15, 3, 2], 'cur_cost': 68101.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 1, 13, 25, 37, 49, 61, 73, 85, 97, 89, 77, 65, 53, 41, 29, 17, 5, 2, 14, 26, 38, 50, 62, 74, 86, 98, 90, 78, 66, 54, 42, 30, 18, 6, 3, 15, 27, 39, 51, 63, 75, 87, 99, 91, 79, 67, 55, 43, 31, 19, 7, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 102, 103, 93, 81, 69, 57, 45, 33, 21, 9, 10, 22, 34, 46, 58, 70, 82, 94, 95, 83, 71, 59, 47, 35, 23, 11], 'cur_cost': 67361.0}, {'tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}, {'tour': array([ 12,  66,  21,  98,  17,  70,  47,  25,  44,  19,  13,  76,  90,
        60,   6,  42,  92,  31,  99,  26,  20,  82,  54,  93,  62,   7,
        24,  74,  43,  80,  34,  87,  95,  75,  55,   9,  49,  46,  10,
        63,  64,  14,  45,  73,  32,  67,  52,  71,  94,  23,  50,  33,
        68,  40,  65,  89,  83,  51,  58,   4,  41, 101,   2,  30, 104,
        11,  56,  22, 102,  27, 103,  72,  15,  69,  79,   5,   0,  38,
        53,  97,  37,  57,  36,   3,   8,   1,  78,  85,  35,  61,  28,
        29,  16, 100,  96,  48,  18,  77,  84,  81,  59,  39,  91,  86,
        88]), 'cur_cost': 124615.0}, {'tour': array([ 40,   8,  86, 103,  76,  70,  73,  10,  29,  27,  90,  75,  81,
        78,  18,  50,  77,  83,  74,  34,  89,  14,  84,  47,   4,   1,
        82,  96,  12,  43,  91,   7,  64,  37,  35,  58,  55,  26,  60,
        17,  24,  32,  36, 100,  21,  49,  45,  95,  38,  33,  54,   0,
        66,   5,  69,  15,  52,  13,  39,  99,  93,  92,  46,  30,  65,
        56,   3,  63,  16,  28,  31,  79,  41,  20,  42, 102,  87,  80,
        71,   2,  97,  88,  19,  51,  23,  44,  68,  61,  85,   6, 104,
         9, 101,  94,  11,  98,  48,  57,  22,  62,  67,  25,  53,  72,
        59]), 'cur_cost': 129186.0}, {'tour': array([  2,  53,  58,  63,  37,  99,  46,   4,  65,   1, 103,  30,  10,
        90,  82,  57,  91,  22,  34,  11,  94,  26,  86,  25,  98,   0,
        93,   9,  88,  36,   6,  89,  49,  96,  31,  23,  87,  24,  64,
        71,  62,   8,  38,  67,  41,  51,  33,  81,  60,  61,  32, 101,
        43,  97,  12,  80,  17,   7,  18,  40,  29,  76,  44,  95,  35,
        56,  92,  79, 104,  84,  27,  66,   3,  45,  14, 102,  52,  15,
        39,   5,  72,  78,  83,  74,  59,  13,  21,  69,  42,  19,  16,
        77,  73,  85,  28,  68,  54,  50,  75,  48,  70,  55,  47, 100,
        20]), 'cur_cost': 134529.0}, {'tour': array([ 74,  68,  87,  32,  64,  39,  28,   7,  67,   3,  65, 102,  24,
        98,  89,   2, 103,  34,  36,  60,  46,   6,  44,  62,  18,  50,
        90,  15,   8,  92,  27,  19,  35,  59,  63,  49,  52,  17,  22,
        10,  73,  45,  79,   9,  97,  14,  13,  77,  95,  51,  86,  81,
        12,  85,  31,  38,  47, 100,  91,  53,  66,  75,  42,  55,  21,
         5,  11,   1,  88,  96,  76,  40,  78,  30,   4,  41,  48,  61,
        16,  37,  26,  82,   0,  54,  33,  23, 101,  70,  20,  57,  56,
        72,  93,  29,  84,  94,  69,  43,  58,  80,  83, 104,  25,  71,
        99]), 'cur_cost': 121867.0}]
2025-06-26 21:09:40,788 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 21:09:40,788 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 88, 'skip_rate': 0.056818181818181816, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 83, 'cache_hits': 48, 'similarity_calculations': 701, 'cache_hit_rate': 0.06847360912981455, 'cache_size': 653}}
2025-06-26 21:09:40,788 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 21:09:40,788 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 11, 23, 35, 47, 59, 71, 83, 95, 10, 22, 34, 46, 58, 70, 82, 94, 9, 21, 33, 45, 57, 69, 81, 93, 8, 20, 32, 44, 56, 68, 80, 92, 7, 19, 31, 43, 55, 67, 79, 91, 6, 18, 30, 42, 54, 66, 78, 90, 5, 17, 29, 41, 53, 65, 77, 89, 4, 16, 28, 40, 52, 64, 76, 88, 3, 15, 27, 39, 51, 63, 75, 87, 2, 14, 26, 38, 50, 62, 74, 86, 1, 13, 25, 37, 49, 61, 73, 85, 97, 98, 99, 100, 101, 102, 103, 104], 'cur_cost': 87221.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 91, 94, 82, 70, 58, 46, 34, 22, 10, 6, 18, 30, 42, 54, 66, 78, 90, 102, 103, 79, 67, 55, 43, 31, 19, 7, 3, 15, 27, 39, 51, 63, 75, 87, 99, 98, 86, 74, 62, 50, 38, 26, 14, 2, 13, 25, 37, 49, 61, 73, 85, 97, 95, 83, 71, 59, 47, 35, 23, 11, 1], 'cur_cost': 64920.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 67, 78, 89, 100, 11, 22, 33, 44, 55, 66, 77, 88, 99, 10, 21, 32, 43, 54, 65, 76, 87, 98, 9, 20, 31, 42, 53, 64, 75, 86, 97, 8, 19, 30, 41, 52, 63, 74, 85, 96, 7, 18, 29, 40, 51, 62, 73, 84, 95, 6, 17, 28, 39, 50, 61, 72, 83, 94, 5, 16, 27, 38, 49, 60, 71, 82, 93, 4, 15, 26, 37, 48, 59, 70, 81, 92, 3, 14, 25, 36, 47, 58, 69, 80, 91, 2, 13, 24, 35, 46, 57, 68, 79, 90, 1, 104, 103, 102, 101], 'cur_cost': 90867.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 89, 77, 65, 53, 41, 29, 17, 5, 9, 21, 33, 45, 57, 69, 81, 93, 102, 90, 78, 66, 54, 42, 30, 18, 6, 10, 22, 34, 46, 58, 70, 82, 94, 103, 91, 79, 67, 55, 43, 31, 19, 7, 11, 23, 35, 47, 59, 71, 83, 95, 97, 85, 73, 61, 49, 37, 25, 13, 1, 14, 26, 38, 50, 62, 74, 86, 98, 99, 87, 75, 63, 51, 39, 27, 15, 3, 2], 'cur_cost': 68101.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 104, 92, 80, 68, 56, 44, 32, 20, 8, 1, 13, 25, 37, 49, 61, 73, 85, 97, 89, 77, 65, 53, 41, 29, 17, 5, 2, 14, 26, 38, 50, 62, 74, 86, 98, 90, 78, 66, 54, 42, 30, 18, 6, 3, 15, 27, 39, 51, 63, 75, 87, 99, 91, 79, 67, 55, 43, 31, 19, 7, 4, 16, 28, 40, 52, 64, 76, 88, 100, 101, 102, 103, 93, 81, 69, 57, 45, 33, 21, 9, 10, 22, 34, 46, 58, 70, 82, 94, 95, 83, 71, 59, 47, 35, 23, 11], 'cur_cost': 67361.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 72, 84, 96, 1, 13, 25, 37, 49, 61, 73, 85, 97, 2, 14, 26, 38, 50, 62, 74, 86, 98, 3, 15, 27, 39, 51, 63, 75, 87, 99, 4, 16, 28, 40, 52, 64, 76, 88, 100, 5, 17, 29, 41, 53, 65, 77, 89, 101, 6, 18, 30, 42, 54, 66, 78, 90, 102, 7, 19, 31, 43, 55, 67, 79, 91, 103, 8, 20, 32, 44, 56, 68, 80, 92, 104, 9, 21, 33, 45, 57, 69, 81, 93, 10, 22, 34, 46, 58, 70, 82, 94, 11, 23, 35, 47, 59, 71, 83, 95], 'cur_cost': 87110.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 12,  66,  21,  98,  17,  70,  47,  25,  44,  19,  13,  76,  90,
        60,   6,  42,  92,  31,  99,  26,  20,  82,  54,  93,  62,   7,
        24,  74,  43,  80,  34,  87,  95,  75,  55,   9,  49,  46,  10,
        63,  64,  14,  45,  73,  32,  67,  52,  71,  94,  23,  50,  33,
        68,  40,  65,  89,  83,  51,  58,   4,  41, 101,   2,  30, 104,
        11,  56,  22, 102,  27, 103,  72,  15,  69,  79,   5,   0,  38,
        53,  97,  37,  57,  36,   3,   8,   1,  78,  85,  35,  61,  28,
        29,  16, 100,  96,  48,  18,  77,  84,  81,  59,  39,  91,  86,
        88]), 'cur_cost': 124615.0}}]
2025-06-26 21:09:40,789 - __main__ - INFO - 进化阶段完成
2025-06-26 21:09:40,789 - __main__ - INFO - 开始评估阶段
2025-06-26 21:09:40,789 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 21:09:40,789 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 21:09:40,789 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 36480.0 → 64920.0 (Δ -28440.0)
- Mean cost: 107153.3 → 97577.7
- Diversity: 0.787 → 0.613 (Δ -0.1731)

#### Elite metrics
- Elite count: 12 → 12
- Best elite cost: 14379 → 14379 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 66.7% , avg Δ 22923.17
- Exploit: 4 inds, success 50.0% , avg Δ -10445.75

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [86026.0, 87110.0, 36480.0], diversity [0.737989417989418, 0.8156613756613754, 0.9424338624338623]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 21:09:40,792 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 21:09:58,430 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": 22923.17,
      "contribution_to_elite": "moderate (improves mean cost but not elite)"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -10445.75,
      "contribution_to_elite": "low (no elite improvement, some regression)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "explore is more effective but diversity is dropping sharply (-0.1731)"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.85,
    "additional_actions": "introduce 2-3 random immigrants to counter regression"
  }
}
```
2025-06-26 21:09:58,505 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 21:09:58,505 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": 22923.17,
      "contribution_to_elite": "moderate (improves mean cost but not elite)"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -10445.75,
      "contribution_to_elite": "low (no elite improvement, some regression)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "explore is more effective but diversity is dropping sharply (-0.1731)"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.85,
    "additional_actions": "introduce 2-3 random immigrants to counter regression"
  }
}
```
2025-06-26 21:09:58,506 - __main__ - INFO - 评估阶段完成
2025-06-26 21:09:58,506 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": 22923.17,
      "contribution_to_elite": "moderate (improves mean cost but not elite)"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -10445.75,
      "contribution_to_elite": "low (no elite improvement, some regression)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "explore is more effective but diversity is dropping sharply (-0.1731)"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.85,
    "additional_actions": "introduce 2-3 random immigrants to counter regression"
  }
}
```
2025-06-26 21:09:58,506 - __main__ - INFO - 当前最佳适应度: 64920.0
2025-06-26 21:09:58,508 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\lin105_route_4.pkl
2025-06-26 21:09:58,522 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\lin105_solution.json
2025-06-26 21:09:58,522 - __main__ - INFO - 实例 lin105 处理完成
