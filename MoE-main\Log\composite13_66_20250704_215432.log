2025-07-04 21:54:32,612 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-04 21:54:32,612 - __main__ - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-04 21:54:32,612 - <PERSON>atsExpert - INFO - 开始统计分析
2025-07-04 21:54:32,632 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9975.0, 'max': 113872.0, 'mean': 79612.1, 'std': 45953.441689279374}, 'diversity': 0.9228956228956229, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:54:32,632 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:54:32,639 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:54:32,639 - <PERSON>Expert - INFO - 开始精英解分析
2025-07-04 21:54:32,639 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-04 21:54:33,141 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:54:33,143 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:54:33,144 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9975.0, mean 79612.1, max 113872.0, std 45953.441689279374
- diversity: 0.9228956228956229
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: [{'subpath': (40, 49, 47), 'frequency': 0.3}, {'subpath': (49, 47, 46), 'frequency': 0.3}, {'subpath': (47, 46, 48), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [10, 38, 25, 60, 30, 54], 'cost': 13683.0, 'size': 6}, {'region': [57, 34, 61, 26, 64], 'cost': 11805.0, 'size': 5}, {'region': [26, 61, 38, 1], 'cost': 8295.0, 'size': 4}]
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:54:33,145 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:54:39,808 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.7,
    "modality": "multi",
    "deceptiveness": "potential"
  },
  "population_state": {
    "diversity": 0.92,
    "convergence": 0.0,
    "clustering": "no clustering"
  },
  "difficult_regions": [
    {
      "region": [
        "identified as regions with long edges and potential for local optima entrapment",
        "Example: Difficult regions identified from the structural signals and spatial analysis (e.g., regions involving nodes with long edges, as indicated by the long_edge_ratio). Refine the list by considering edges in proximity to identified high density clusters or low density corridors."
      ],
      "rationale": "long edge ratio (0.249) is high, indicating ruggedness. Combined with structural signals of difficult regions (e.g., regions [26, 61, 38, 1]), it indicates potentially challenging landscape features."
    }
  ],
  "opportunity_regions": [
    {
      "region": [
        "cells containing high densities of nodes in the density grid should be considered. These regions could be good candidates for local exploitation as population search progress."
      ],
      "rationale": "Density grid analysis shows high density cells, suggesting opportunity for exploitation. Specifically, cells [1,1] (centroid) and cells at corners. This points toward potentially good clusters to refine/exploit."
    }
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "exploration and exploitation",
    "operators": [
      "Apply Edge Exchange/2-opt within high-density grid cells to refine and exploit the good local clusters.",
      "Use a mutation operator that explores around common subpaths (e.g., those identified in 'common_subpaths_sample') for local optimization and exploitation.",
      "Perform long-range moves in the population by including edge swaps between the different spatial densities. A focus on moving across different spatial sections can provide significant improvements.",
      "Consider local search operators such as 2-opt or k-opt to improve solutions within the opportunity regions (high density cells).",
      "Use a diversified population and apply local search to different solutions. This avoids early convergence."
    ]
  }
}
```
2025-07-04 21:54:39,808 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:54:39,808 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.7, 'modality': 'multi', 'deceptiveness': 'potential'}, 'population_state': {'diversity': 0.92, 'convergence': 0.0, 'clustering': 'no clustering'}, 'difficult_regions': [{'region': ['identified as regions with long edges and potential for local optima entrapment', 'Example: Difficult regions identified from the structural signals and spatial analysis (e.g., regions involving nodes with long edges, as indicated by the long_edge_ratio). Refine the list by considering edges in proximity to identified high density clusters or low density corridors.'], 'rationale': 'long edge ratio (0.249) is high, indicating ruggedness. Combined with structural signals of difficult regions (e.g., regions [26, 61, 38, 1]), it indicates potentially challenging landscape features.'}], 'opportunity_regions': [{'region': ['cells containing high densities of nodes in the density grid should be considered. These regions could be good candidates for local exploitation as population search progress.'], 'rationale': 'Density grid analysis shows high density cells, suggesting opportunity for exploitation. Specifically, cells [1,1] (centroid) and cells at corners. This points toward potentially good clusters to refine/exploit.'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'exploration and exploitation', 'operators': ['Apply Edge Exchange/2-opt within high-density grid cells to refine and exploit the good local clusters.', "Use a mutation operator that explores around common subpaths (e.g., those identified in 'common_subpaths_sample') for local optimization and exploitation.", 'Perform long-range moves in the population by including edge swaps between the different spatial densities. A focus on moving across different spatial sections can provide significant improvements.', 'Consider local search operators such as 2-opt or k-opt to improve solutions within the opportunity regions (high density cells).', 'Use a diversified population and apply local search to different solutions. This avoids early convergence.']}}
2025-07-04 21:54:39,811 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:54:39,811 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:54:39,812 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.92
  • best_cost: 9975.0
  • mean_cost: 79612.1
- Search-space: ruggedness 0.7, modality multi, deceptiveness potential
- Evolution phase: exploration
- Landscape focus suggestion: exploration and exploitation
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 1): {'region': ['identif
- Opportunity regions (# 1): {'region': ['cells c
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:54:39,812 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:54:39,812 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:54:46,199 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase. Prioritizing exploration to explore the search space. Some exploiting to improve on best individual."
}
```
2025-07-04 21:54:46,200 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-04 21:54:46,201 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-04 21:54:46,201 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase. Prioritizing exploration to explore the search space. Some exploiting to improve on best individual."
}
```
2025-07-04 21:54:46,201 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:54:46,201 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-04 21:54:46,202 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase. Prioritizing exploration to explore the search space. Some exploiting to improve on best individual."
}
```
2025-07-04 21:54:46,202 - __main__ - INFO - 开始进化阶段
2025-07-04 21:54:46,202 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:54:46,203 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:54:46,203 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9975.0 (rank 0.0%)
- Population diversity: 0.7415384615384616
- Evolution phase: exploration
- Landscape recommends focus: exploration and exploitation
- Opportunity regions (sample): {'region': ['ce
- Difficult regions to avoid (sample): {'region': ['id
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:54:46,204 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:54:46,204 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:54:50,041 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "ce (opportunity) and avoids id (difficult)",
  "strategy_comment": "Starts and ends exploring the opportunity region whilst navigating avoiding the difficult regions."
}
```
2025-07-04 21:54:50,042 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:54:50,043 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:54:50,043 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:54:50,043 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:54:50,044 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:54:50,044 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10333.0 (rank 10.0%)
- Population diversity: 0.8046153846153846
- Evolution phase: exploration
- Landscape recommends focus: exploration and exploitation
- Opportunity regions (sample): {'region': ['ce
- Difficult regions to avoid (sample): {'region': ['id
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:54:50,045 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:54:50,045 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:55:09,767 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59],
  "targeted_regions": "ce, sparse nodes near high value nodes.",
  "strategy_comment": "Prioritizes exploration of sparse areas while connecting to the opportunity regions and avoiding difficult regions."
}
```
2025-07-04 21:55:09,768 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:55:09,768 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59]
2025-07-04 21:55:09,769 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 11479.0}
2025-07-04 21:55:09,769 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:55:09,769 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:55:09,769 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10045.0 (rank 0.0%)
- Population diversity: 0.7953846153846154
- Evolution phase: exploration
- Landscape recommends focus: exploration and exploitation
- Opportunity regions (sample): {'region': ['ce
- Difficult regions to avoid (sample): {'region': ['id
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:55:09,771 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:55:09,771 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:55:18,565 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65],
  "targeted_regions": "Opportunity regions (ce) by initial sequence.",
  "strategy_comment": "Prioritize exploration through opportunistic region integration and even node distribution."
}
```
2025-07-04 21:55:18,566 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:55:18,566 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19756.0, 路径: [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65]
2025-07-04 21:55:18,567 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}
2025-07-04 21:55:18,567 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:55:18,568 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:55:18,568 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 109045.0 (rank 40.0%)
- Population diversity: 0.803076923076923
- Evolution phase: exploration
- Landscape recommends focus: exploration and exploitation
- Opportunity regions (sample): {'region': ['ce
- Difficult regions to avoid (sample): {'region': ['id
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:55:18,569 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:55:18,569 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:55:24,256 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "ce, exploring sparse areas adjecent to opportunity regions.",
  "strategy_comment": "Create a path that follows the opportunity region and avoids difficult ones."
}
```
2025-07-04 21:55:24,258 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:55:24,258 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:55:24,259 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:55:24,259 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:55:24,259 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:55:24,260 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 90811.0 (rank 40.0%)
- Population diversity: 0.7230769230769231
- Evolution phase: exploration
- Landscape recommends focus: exploration and exploitation
- Opportunity regions (sample): {'region': ['ce
- Difficult regions to avoid (sample): {'region': ['id
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:55:24,260 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:55:24,261 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:55:38,530 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22],
  "targeted_regions": "ce, sparse cells",
  "strategy_comment": "Prioritizes opportunity region 'ce' and explores neighboring sparse cells to diversify path."
}
```
2025-07-04 21:55:38,531 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:55:38,531 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]
2025-07-04 21:55:38,531 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], 'cur_cost': 11479.0}
2025-07-04 21:55:38,533 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-04 21:55:38,533 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:55:38,535 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:55:38,535 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 99051.0
2025-07-04 21:55:40,973 - ExploitationExpert - INFO - res_population_num: 1
2025-07-04 21:55:40,974 - ExploitationExpert - INFO - res_population_costs: [101527]
2025-07-04 21:55:40,974 - ExploitationExpert - INFO - res_populations: [array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64)]
2025-07-04 21:55:40,975 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:55:40,975 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 11479.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], 'cur_cost': 11479.0}, {'tour': array([22,  1, 51, 24, 17, 11, 56, 23,  2, 54,  4, 16,  8, 28, 58,  7, 49,
       33, 48, 63, 52, 46,  3, 60,  9, 36,  5, 19, 13, 37, 14, 62, 45, 50,
       18, 30, 64, 38, 26, 27,  6, 41, 47, 34, 25,  0, 61, 31, 53, 57, 12,
       20, 35, 29, 32, 39, 15, 44, 10, 59, 55, 42, 40, 43, 21, 65]), 'cur_cost': 99051.0}, {'tour': [1, 8, 24, 3, 64, 65, 42, 41, 53, 32, 25, 9, 14, 55, 50, 57, 16, 2, 47, 10, 56, 4, 63, 21, 6, 33, 60, 39, 51, 40, 11, 45, 36, 34, 17, 27, 12, 30, 58, 0, 46, 37, 54, 15, 35, 20, 18, 29, 48, 62, 49, 13, 52, 23, 5, 28, 19, 44, 59, 43, 7, 22, 31, 26, 61, 38], 'cur_cost': 113872.0}, {'tour': [49, 54, 43, 22, 29, 15, 32, 10, 33, 20, 17, 19, 30, 28, 65, 63, 8, 27, 47, 2, 36, 24, 60, 52, 0, 44, 40, 1, 48, 59, 16, 64, 51, 42, 57, 45, 14, 41, 6, 37, 11, 23, 50, 3, 7, 38, 5, 26, 31, 46, 55, 21, 56, 35, 61, 58, 39, 13, 9, 34, 12, 4, 62, 25, 53, 18], 'cur_cost': 113384.0}, {'tour': [27, 17, 55, 22, 32, 60, 35, 48, 29, 37, 13, 56, 6, 58, 38, 5, 44, 47, 7, 42, 33, 41, 49, 52, 3, 14, 45, 9, 43, 1, 57, 34, 61, 26, 64, 4, 36, 20, 8, 50, 18, 10, 11, 2, 53, 51, 40, 31, 63, 39, 30, 0, 16, 28, 54, 23, 62, 65, 15, 25, 19, 12, 46, 59, 21, 24], 'cur_cost': 113840.0}, {'tour': [28, 15, 4, 27, 31, 21, 3, 63, 18, 42, 7, 39, 0, 13, 35, 52, 44, 49, 17, 58, 61, 5, 36, 48, 32, 50, 1, 26, 55, 11, 22, 34, 40, 41, 23, 62, 56, 46, 16, 53, 29, 33, 57, 6, 43, 65, 37, 47, 8, 19, 12, 10, 38, 25, 60, 30, 54, 64, 20, 45, 51, 24, 59, 14, 2, 9], 'cur_cost': 112447.0}]
2025-07-04 21:55:40,976 - ExploitationExpert - INFO - 局部搜索耗时: 2.44秒
2025-07-04 21:55:40,976 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-04 21:55:40,976 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-04 21:55:40,976 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 21:55:40,976 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:55:40,977 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:55:40,977 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 104325.0
2025-07-04 21:55:41,884 - ExploitationExpert - INFO - res_population_num: 2
2025-07-04 21:55:41,884 - ExploitationExpert - INFO - res_population_costs: [101527, 9579]
2025-07-04 21:55:41,884 - ExploitationExpert - INFO - res_populations: [array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:55:41,885 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:55:41,885 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 11479.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], 'cur_cost': 11479.0}, {'tour': array([22,  1, 51, 24, 17, 11, 56, 23,  2, 54,  4, 16,  8, 28, 58,  7, 49,
       33, 48, 63, 52, 46,  3, 60,  9, 36,  5, 19, 13, 37, 14, 62, 45, 50,
       18, 30, 64, 38, 26, 27,  6, 41, 47, 34, 25,  0, 61, 31, 53, 57, 12,
       20, 35, 29, 32, 39, 15, 44, 10, 59, 55, 42, 40, 43, 21, 65]), 'cur_cost': 99051.0}, {'tour': array([51,  4, 40,  2, 56, 24,  8, 18, 34, 41, 54,  3, 57, 25, 37,  0, 14,
        9, 17, 22, 59, 55, 58, 53, 36, 10, 20, 13, 63, 12, 38, 61, 43, 45,
       32,  6, 33, 28, 23, 62, 31, 44, 46, 60,  1, 19,  7, 15, 47, 48, 26,
       27, 42, 16, 64, 21, 35, 39, 50,  5, 65, 11, 29, 49, 30, 52]), 'cur_cost': 104325.0}, {'tour': [49, 54, 43, 22, 29, 15, 32, 10, 33, 20, 17, 19, 30, 28, 65, 63, 8, 27, 47, 2, 36, 24, 60, 52, 0, 44, 40, 1, 48, 59, 16, 64, 51, 42, 57, 45, 14, 41, 6, 37, 11, 23, 50, 3, 7, 38, 5, 26, 31, 46, 55, 21, 56, 35, 61, 58, 39, 13, 9, 34, 12, 4, 62, 25, 53, 18], 'cur_cost': 113384.0}, {'tour': [27, 17, 55, 22, 32, 60, 35, 48, 29, 37, 13, 56, 6, 58, 38, 5, 44, 47, 7, 42, 33, 41, 49, 52, 3, 14, 45, 9, 43, 1, 57, 34, 61, 26, 64, 4, 36, 20, 8, 50, 18, 10, 11, 2, 53, 51, 40, 31, 63, 39, 30, 0, 16, 28, 54, 23, 62, 65, 15, 25, 19, 12, 46, 59, 21, 24], 'cur_cost': 113840.0}, {'tour': [28, 15, 4, 27, 31, 21, 3, 63, 18, 42, 7, 39, 0, 13, 35, 52, 44, 49, 17, 58, 61, 5, 36, 48, 32, 50, 1, 26, 55, 11, 22, 34, 40, 41, 23, 62, 56, 46, 16, 53, 29, 33, 57, 6, 43, 65, 37, 47, 8, 19, 12, 10, 38, 25, 60, 30, 54, 64, 20, 45, 51, 24, 59, 14, 2, 9], 'cur_cost': 112447.0}]
2025-07-04 21:55:41,887 - ExploitationExpert - INFO - 局部搜索耗时: 0.91秒
2025-07-04 21:55:41,887 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-04 21:55:41,887 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 21:55:41,887 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 21:55:41,888 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:55:41,888 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:55:41,888 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 108132.0
2025-07-04 21:55:42,392 - ExploitationExpert - INFO - res_population_num: 11
2025-07-04 21:55:42,392 - ExploitationExpert - INFO - res_population_costs: [101527, 9579, 9573, 9542, 9542, 9530, 9526, 9526, 9521, 9521, 9521]
2025-07-04 21:55:42,392 - ExploitationExpert - INFO - res_populations: [array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 21:55:42,397 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:55:42,397 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 11479.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], 'cur_cost': 11479.0}, {'tour': array([22,  1, 51, 24, 17, 11, 56, 23,  2, 54,  4, 16,  8, 28, 58,  7, 49,
       33, 48, 63, 52, 46,  3, 60,  9, 36,  5, 19, 13, 37, 14, 62, 45, 50,
       18, 30, 64, 38, 26, 27,  6, 41, 47, 34, 25,  0, 61, 31, 53, 57, 12,
       20, 35, 29, 32, 39, 15, 44, 10, 59, 55, 42, 40, 43, 21, 65]), 'cur_cost': 99051.0}, {'tour': array([51,  4, 40,  2, 56, 24,  8, 18, 34, 41, 54,  3, 57, 25, 37,  0, 14,
        9, 17, 22, 59, 55, 58, 53, 36, 10, 20, 13, 63, 12, 38, 61, 43, 45,
       32,  6, 33, 28, 23, 62, 31, 44, 46, 60,  1, 19,  7, 15, 47, 48, 26,
       27, 42, 16, 64, 21, 35, 39, 50,  5, 65, 11, 29, 49, 30, 52]), 'cur_cost': 104325.0}, {'tour': array([21, 53, 64, 23, 56, 54, 52, 49, 10, 12, 29, 39, 65, 38, 11, 30, 32,
       62, 14, 41, 25, 15, 43,  7, 63, 13, 34,  9, 20, 51, 19, 48, 50,  5,
       28, 47, 45, 60,  8, 59,  3, 16, 37, 31,  1, 26, 55, 58, 18, 44, 57,
       27,  4, 61, 35, 22, 42, 24, 17, 36, 40, 33,  6, 46,  0,  2]), 'cur_cost': 108132.0}, {'tour': [27, 17, 55, 22, 32, 60, 35, 48, 29, 37, 13, 56, 6, 58, 38, 5, 44, 47, 7, 42, 33, 41, 49, 52, 3, 14, 45, 9, 43, 1, 57, 34, 61, 26, 64, 4, 36, 20, 8, 50, 18, 10, 11, 2, 53, 51, 40, 31, 63, 39, 30, 0, 16, 28, 54, 23, 62, 65, 15, 25, 19, 12, 46, 59, 21, 24], 'cur_cost': 113840.0}, {'tour': [28, 15, 4, 27, 31, 21, 3, 63, 18, 42, 7, 39, 0, 13, 35, 52, 44, 49, 17, 58, 61, 5, 36, 48, 32, 50, 1, 26, 55, 11, 22, 34, 40, 41, 23, 62, 56, 46, 16, 53, 29, 33, 57, 6, 43, 65, 37, 47, 8, 19, 12, 10, 38, 25, 60, 30, 54, 64, 20, 45, 51, 24, 59, 14, 2, 9], 'cur_cost': 112447.0}]
2025-07-04 21:55:42,399 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:55:42,399 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-04 21:55:42,399 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 21:55:42,399 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-04 21:55:42,400 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:55:42,400 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 113840.0 (rank 90.0%)
- Population diversity: 0.6430769230769231
- Evolution phase: exploration
- Landscape recommends focus: exploration and exploitation
- Opportunity regions (sample): {'region': ['ce
- Difficult regions to avoid (sample): {'region': ['id
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:55:42,400 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:55:42,401 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:55:58,186 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65
  ],
  "targeted_regions": "ce adjacency; sparse cell exploration",
  "strategy_comment": "Initial path with random walk exploration, prioritizing CE regions while avoiding ID regions."
}
```
2025-07-04 21:55:58,187 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:55:58,188 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:55:58,188 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:55:58,189 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:55:58,189 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:55:58,189 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:55:58,189 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 109157.0
2025-07-04 21:55:58,693 - ExploitationExpert - INFO - res_population_num: 15
2025-07-04 21:55:58,693 - ExploitationExpert - INFO - res_population_costs: [101527, 9579, 9573, 9542, 9542, 9530, 9526, 9526, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:55:58,693 - ExploitationExpert - INFO - res_populations: [array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 21:55:58,698 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:55:58,698 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 11479.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], 'cur_cost': 11479.0}, {'tour': array([22,  1, 51, 24, 17, 11, 56, 23,  2, 54,  4, 16,  8, 28, 58,  7, 49,
       33, 48, 63, 52, 46,  3, 60,  9, 36,  5, 19, 13, 37, 14, 62, 45, 50,
       18, 30, 64, 38, 26, 27,  6, 41, 47, 34, 25,  0, 61, 31, 53, 57, 12,
       20, 35, 29, 32, 39, 15, 44, 10, 59, 55, 42, 40, 43, 21, 65]), 'cur_cost': 99051.0}, {'tour': array([51,  4, 40,  2, 56, 24,  8, 18, 34, 41, 54,  3, 57, 25, 37,  0, 14,
        9, 17, 22, 59, 55, 58, 53, 36, 10, 20, 13, 63, 12, 38, 61, 43, 45,
       32,  6, 33, 28, 23, 62, 31, 44, 46, 60,  1, 19,  7, 15, 47, 48, 26,
       27, 42, 16, 64, 21, 35, 39, 50,  5, 65, 11, 29, 49, 30, 52]), 'cur_cost': 104325.0}, {'tour': array([21, 53, 64, 23, 56, 54, 52, 49, 10, 12, 29, 39, 65, 38, 11, 30, 32,
       62, 14, 41, 25, 15, 43,  7, 63, 13, 34,  9, 20, 51, 19, 48, 50,  5,
       28, 47, 45, 60,  8, 59,  3, 16, 37, 31,  1, 26, 55, 58, 18, 44, 57,
       27,  4, 61, 35, 22, 42, 24, 17, 36, 40, 33,  6, 46,  0,  2]), 'cur_cost': 108132.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 7, 25, 45,  2, 61, 12, 53, 26, 34, 39, 15, 57, 38,  9, 31, 18, 54,
       29, 49, 60, 20, 48, 22, 52, 42, 21, 36, 23, 51, 19, 65, 55, 44, 13,
       47, 59,  1, 32, 37,  5, 41, 56,  3,  4, 30, 24, 10, 16, 43, 40, 11,
        6, 33, 58, 64,  8, 46, 62, 50, 17, 27, 28, 14, 35,  0, 63]), 'cur_cost': 109157.0}]
2025-07-04 21:55:58,701 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:55:58,701 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-04 21:55:58,701 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:55:58,702 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65], 'cur_cost': 19756.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], 'cur_cost': 11479.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-07-04 21:55:58,702 - __main__ - INFO - 进化阶段完成
2025-07-04 21:55:58,702 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:55:58,719 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 109157.0, 'mean': 49781.6, 'std': 45353.77167821878}, 'diversity': 0.7572390572390573, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:55:58,719 - __main__ - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-04 21:55:58,719 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment ---
2025-07-04 21:55:58,721 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:55:58,721 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 15 → 15
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.183 → 0.183 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:55:58,722 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:56:07,081 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0.0, "exploit": 0.0},
  "balance_state": "stagnant",
  "recommendations": [
    "increase explore_ratio to 0.8",
    "increase diversity_weight to 0.5"
  ]
}
```

2025-07-04 21:56:07,082 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:56:07,082 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-04 21:56:07,083 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-04 21:56:07,083 - __main__ - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-04 21:56:07,084 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:56:07,100 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 109157.0, 'mean': 49781.6, 'std': 45353.77167821878}, 'diversity': 0.7572390572390573, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:56:07,100 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:56:07,102 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:56:07,103 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:56:07,103 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:56:07,108 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:56:07,110 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:56:07,112 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:56:07,112 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 11479.0, mean 49781.6, max 109157.0, std 45353.77167821878
- diversity: 0.7572390572390573
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.33492063492063495}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:56:07,113 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:56:16,762 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.4,
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.757,
    "convergence": 0.0,
    "clustering": "no clustering information"
  },
  "difficult_regions": [
    {
      "description": "Potential difficult region due to long edges.",
      "coordinates": "Edges exceeding the 75th percentile length (2469.0)"
    }
  ],
  "opportunity_regions": [
    {
      "description": "High-density cell, potentially good area to exploit nodes/edges.",
      "coordinates": "(0,0), (2,0), (1,1), (2,2)"
    }
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Apply diversification operators (e.g., large mutation or crossover) to explore different regions of the search space.",
      "Utilize 2-opt or 3-opt operators to improve paths within the higher density regions",
       "Consider using edge swapping operators, biasing them toward edges in the high-density areas (0,0) and (2,2) and/or avoiding long edges."
    ]
  }
}
```
2025-07-04 21:56:16,763 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:56:16,764 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.4, 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.757, 'convergence': 0.0, 'clustering': 'no clustering information'}, 'difficult_regions': [{'description': 'Potential difficult region due to long edges.', 'coordinates': 'Edges exceeding the 75th percentile length (2469.0)'}], 'opportunity_regions': [{'description': 'High-density cell, potentially good area to exploit nodes/edges.', 'coordinates': '(0,0), (2,0), (1,1), (2,2)'}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Apply diversification operators (e.g., large mutation or crossover) to explore different regions of the search space.', 'Utilize 2-opt or 3-opt operators to improve paths within the higher density regions', 'Consider using edge swapping operators, biasing them toward edges in the high-density areas (0,0) and (2,2) and/or avoiding long edges.']}}
2025-07-04 21:56:16,764 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:56:16,764 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:56:16,764 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.757
  • best_cost: 11479.0
  • mean_cost: 49781.6
- Search-space: ruggedness 0.4, modality multi, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 1): {'description': 'Pot
- Opportunity regions (# 1): {'description': 'Hig
- Last-iteration feedback: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0.0, "exploit": 0.0},
  "balance_state...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:56:16,764 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:56:16,765 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:56:22,462 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Iteration 1, exploration phase and high diversity. Prioritize exploration with a strong bias, allowing some exploitation of the best individuals."
}
```
2025-07-04 21:56:22,463 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 21:56:22,464 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 21:56:22,464 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Iteration 1, exploration phase and high diversity. Prioritize exploration with a strong bias, allowing some exploitation of the best individuals."
}
```
2025-07-04 21:56:22,464 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:56:22,465 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-04 21:56:22,465 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Iteration 1, exploration phase and high diversity. Prioritize exploration with a strong bias, allowing some exploitation of the best individuals."
}
```
2025-07-04 21:56:22,465 - __main__ - INFO - 开始进化阶段
2025-07-04 21:56:22,466 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:56:22,466 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:56:22,466 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.5584615384615385
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:56:22,467 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:56:22,467 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:56:37,501 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 2, 7, 14, 21, 28, 35, 42, 49, 56, 63, 58, 51, 44, 37, 30, 23, 16, 9, 45, 38, 31, 24, 17, 10, 52, 59],
  "targeted_regions": "Sparse cells along perimeter",
  "strategy_comment": "Prioritizes unexplored areas and opportunity regions while maintaining path structure."
}
```
2025-07-04 21:56:37,502 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:56:37,502 - ExplorationExpert - INFO - 探索路径生成完成，成本: 55210.0, 路径: [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 2, 7, 14, 21, 28, 35, 42, 49, 56, 63, 58, 51, 44, 37, 30, 23, 16, 9, 45, 38, 31, 24, 17, 10, 52, 59]
2025-07-04 21:56:37,503 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 2, 7, 14, 21, 28, 35, 42, 49, 56, 63, 58, 51, 44, 37, 30, 23, 16, 9, 45, 38, 31, 24, 17, 10, 52, 59], 'cur_cost': 55210.0}
2025-07-04 21:56:37,503 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:56:37,504 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:56:37,504 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6461538461538462
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:56:37,505 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:56:37,505 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:56:43,202 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57,
    58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26,
    27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42
  ],
  "targeted_regions": "Exploits sparsely explored area (43-65) with a long traversal.",
  "strategy_comment": "Prioritizes exploration of sparse regions via a contiguous, branching approach."
}
```
2025-07-04 21:56:43,202 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:56:43,203 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16510.0, 路径: [0, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42]
2025-07-04 21:56:43,203 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 16510.0}
2025-07-04 21:56:43,204 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:56:43,204 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:56:43,205 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19756.0 (rank 40.0%)
- Population diversity: 0.6492307692307693
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:56:43,205 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:56:43,205 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:56:47,932 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [4, 42, 58, 31, 17, 5, 6, 20, 28, 53, 48, 60, 45, 14, 63, 13, 30, 64, 37, 1, 33, 55, 36, 2, 40, 10, 18, 65, 47, 56, 26, 24, 49, 11, 23, 52, 39, 50, 35, 9, 29, 43, 21, 32, 16, 59, 25, 41, 15, 44, 61, 22, 34, 54, 0, 51, 38, 12, 62, 57, 7, 46, 8, 27, 19, 3],
  "targeted_regions": "Explores low density areas and connects opportunity regions.",
  "strategy_comment": "Prioritizes unexplored zones near opportunity regions, creating path diversity while seeking efficient connections."
}
```
2025-07-04 21:56:47,932 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:56:47,932 - ExplorationExpert - INFO - 探索路径生成完成，成本: 124870.0, 路径: [4, 42, 58, 31, 17, 5, 6, 20, 28, 53, 48, 60, 45, 14, 63, 13, 30, 64, 37, 1, 33, 55, 36, 2, 40, 10, 18, 65, 47, 56, 26, 24, 49, 11, 23, 52, 39, 50, 35, 9, 29, 43, 21, 32, 16, 59, 25, 41, 15, 44, 61, 22, 34, 54, 0, 51, 38, 12, 62, 57, 7, 46, 8, 27, 19, 3]
2025-07-04 21:56:47,934 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [4, 42, 58, 31, 17, 5, 6, 20, 28, 53, 48, 60, 45, 14, 63, 13, 30, 64, 37, 1, 33, 55, 36, 2, 40, 10, 18, 65, 47, 56, 26, 24, 49, 11, 23, 52, 39, 50, 35, 9, 29, 43, 21, 32, 16, 59, 25, 41, 15, 44, 61, 22, 34, 54, 0, 51, 38, 12, 62, 57, 7, 46, 8, 27, 19, 3], 'cur_cost': 124870.0}
2025-07-04 21:56:47,934 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:56:47,934 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:56:47,935 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6476923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:56:47,935 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:56:47,935 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:56:55,200 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Visit all nodes for initial exploration.",
  "strategy_comment": "Basic full path to maximize exploration and cover all areas in the initial phase."
}
```
2025-07-04 21:56:55,201 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:56:55,202 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:56:55,202 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:56:55,202 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:56:55,202 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:56:55,203 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6476923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:56:55,203 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:56:55,203 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:57:06,513 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 18, 25, 31, 38, 44, 50, 57, 64, 63, 56, 49, 43, 37, 30, 24, 17, 11, 4, 1, 6, 13, 19, 26, 32, 39, 45, 51, 58, 65, 62, 55, 48, 42, 36, 29, 23, 16, 10, 3, 2, 7, 14, 20, 27, 33, 40, 46, 52, 59, 61, 54, 47, 41, 35, 28, 22, 15, 9, 8, 21, 34, 53, 60],
  "targeted_regions": "sparse cells adjacent to nodes 0, 5, 12, and 18",
  "strategy_comment": "Prioritizes exploration of sparse cells and incorporates nodes identified as having exploration opportunities."
}
```
2025-07-04 21:57:06,514 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:57:06,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 52642.0, 路径: [0, 5, 12, 18, 25, 31, 38, 44, 50, 57, 64, 63, 56, 49, 43, 37, 30, 24, 17, 11, 4, 1, 6, 13, 19, 26, 32, 39, 45, 51, 58, 65, 62, 55, 48, 42, 36, 29, 23, 16, 10, 3, 2, 7, 14, 20, 27, 33, 40, 46, 52, 59, 61, 54, 47, 41, 35, 28, 22, 15, 9, 8, 21, 34, 53, 60]
2025-07-04 21:57:06,515 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 12, 18, 25, 31, 38, 44, 50, 57, 64, 63, 56, 49, 43, 37, 30, 24, 17, 11, 4, 1, 6, 13, 19, 26, 32, 39, 45, 51, 58, 65, 62, 55, 48, 42, 36, 29, 23, 16, 10, 3, 2, 7, 14, 20, 27, 33, 40, 46, 52, 59, 61, 54, 47, 41, 35, 28, 22, 15, 9, 8, 21, 34, 53, 60], 'cur_cost': 52642.0}
2025-07-04 21:57:06,515 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 21:57:06,515 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:57:06,516 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 99051.0 (rank 50.0%)
- Population diversity: 0.683076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:57:06,516 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:57:06,516 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:57:20,982 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Max retries exceeded with url: /v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:2406)')))
2025-07-04 21:57:22,984 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-04 21:57:29,970 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 6, 9, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 3, 0, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2],
  "targeted_regions": "Sparse cells and opportunity region bridging.",
  "strategy_comment": "Prioritizes unexplored low-density regions to increase path diversity and integrates with the core structure."
}
```
2025-07-04 21:57:29,970 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:57:29,970 - ExplorationExpert - INFO - 探索路径生成完成，成本: 27898.0, 路径: [12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 6, 9, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 3, 0, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2]
2025-07-04 21:57:29,972 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 6, 9, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 3, 0, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 27898.0}
2025-07-04 21:57:29,972 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-04 21:57:29,973 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:57:29,973 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 104325.0 (rank 60.0%)
- Population diversity: 0.6984615384615385
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':
- Difficult regions to avoid (sample): {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:57:29,974 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:57:29,974 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:57:55,062 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 4, 6, 7, 9, 10, 12, 13, 15, 16, 18, 19, 21, 22, 24, 25, 27, 28, 30, 31, 33, 34, 36, 37, 39, 40, 42, 43, 45, 46, 48, 49, 51, 52, 54, 55, 57, 58, 60, 61, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 3],
  "targeted_regions": "Sparse cells around nodes 0, 1, 2, 3 and opportunity regions.",
  "strategy_comment": "Prioritize sparse cell exploration and opportunity regions, creating a diverse path with reasonable cost."
}
```
2025-07-04 21:57:55,063 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:57:55,064 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17670.0, 路径: [0, 1, 2, 4, 6, 7, 9, 10, 12, 13, 15, 16, 18, 19, 21, 22, 24, 25, 27, 28, 30, 31, 33, 34, 36, 37, 39, 40, 42, 43, 45, 46, 48, 49, 51, 52, 54, 55, 57, 58, 60, 61, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 3]
2025-07-04 21:57:55,064 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 4, 6, 7, 9, 10, 12, 13, 15, 16, 18, 19, 21, 22, 24, 25, 27, 28, 30, 31, 33, 34, 36, 37, 39, 40, 42, 43, 45, 46, 48, 49, 51, 52, 54, 55, 57, 58, 60, 61, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 3], 'cur_cost': 17670.0}
2025-07-04 21:57:55,064 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 21:57:55,065 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:57:55,065 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:57:55,065 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 102413.0
2025-07-04 21:57:55,568 - ExploitationExpert - INFO - res_population_num: 19
2025-07-04 21:57:55,569 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527, 9521, 9521, 9521, 9521]
2025-07-04 21:57:55,569 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-04 21:57:55,577 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:57:55,577 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 2, 7, 14, 21, 28, 35, 42, 49, 56, 63, 58, 51, 44, 37, 30, 23, 16, 9, 45, 38, 31, 24, 17, 10, 52, 59], 'cur_cost': 55210.0}, {'tour': [0, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 16510.0}, {'tour': [4, 42, 58, 31, 17, 5, 6, 20, 28, 53, 48, 60, 45, 14, 63, 13, 30, 64, 37, 1, 33, 55, 36, 2, 40, 10, 18, 65, 47, 56, 26, 24, 49, 11, 23, 52, 39, 50, 35, 9, 29, 43, 21, 32, 16, 59, 25, 41, 15, 44, 61, 22, 34, 54, 0, 51, 38, 12, 62, 57, 7, 46, 8, 27, 19, 3], 'cur_cost': 124870.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 5, 12, 18, 25, 31, 38, 44, 50, 57, 64, 63, 56, 49, 43, 37, 30, 24, 17, 11, 4, 1, 6, 13, 19, 26, 32, 39, 45, 51, 58, 65, 62, 55, 48, 42, 36, 29, 23, 16, 10, 3, 2, 7, 14, 20, 27, 33, 40, 46, 52, 59, 61, 54, 47, 41, 35, 28, 22, 15, 9, 8, 21, 34, 53, 60], 'cur_cost': 52642.0}, {'tour': [12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 6, 9, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 3, 0, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 27898.0}, {'tour': [0, 1, 2, 4, 6, 7, 9, 10, 12, 13, 15, 16, 18, 19, 21, 22, 24, 25, 27, 28, 30, 31, 33, 34, 36, 37, 39, 40, 42, 43, 45, 46, 48, 49, 51, 52, 54, 55, 57, 58, 60, 61, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 3], 'cur_cost': 17670.0}, {'tour': array([34, 61, 40, 28,  2, 31, 26, 56, 11, 58, 55, 18, 45, 42, 62, 43, 51,
       12, 64, 16, 24, 10, 33, 59, 21, 25, 19, 50, 63, 65,  7, 35,  4, 60,
       27, 39, 49, 54, 57, 13,  3,  5, 47, 30, 37, 20,  9,  6, 53,  1, 29,
       38, 52, 46, 14, 17, 48, 15, 36, 32, 44,  8, 22, 41, 23,  0]), 'cur_cost': 102413.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([ 7, 25, 45,  2, 61, 12, 53, 26, 34, 39, 15, 57, 38,  9, 31, 18, 54,
       29, 49, 60, 20, 48, 22, 52, 42, 21, 36, 23, 51, 19, 65, 55, 44, 13,
       47, 59,  1, 32, 37,  5, 41, 56,  3,  4, 30, 24, 10, 16, 43, 40, 11,
        6, 33, 58, 64,  8, 46, 62, 50, 17, 27, 28, 14, 35,  0, 63]), 'cur_cost': 109157.0}]
2025-07-04 21:57:55,579 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:57:55,579 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-04 21:57:55,579 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 21:57:55,579 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:57:55,579 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:57:55,580 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:57:55,580 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 109613.0
2025-07-04 21:57:56,813 - ExploitationExpert - INFO - res_population_num: 21
2025-07-04 21:57:56,814 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:57:56,814 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:57:56,822 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:57:56,822 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 2, 7, 14, 21, 28, 35, 42, 49, 56, 63, 58, 51, 44, 37, 30, 23, 16, 9, 45, 38, 31, 24, 17, 10, 52, 59], 'cur_cost': 55210.0}, {'tour': [0, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 16510.0}, {'tour': [4, 42, 58, 31, 17, 5, 6, 20, 28, 53, 48, 60, 45, 14, 63, 13, 30, 64, 37, 1, 33, 55, 36, 2, 40, 10, 18, 65, 47, 56, 26, 24, 49, 11, 23, 52, 39, 50, 35, 9, 29, 43, 21, 32, 16, 59, 25, 41, 15, 44, 61, 22, 34, 54, 0, 51, 38, 12, 62, 57, 7, 46, 8, 27, 19, 3], 'cur_cost': 124870.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 5, 12, 18, 25, 31, 38, 44, 50, 57, 64, 63, 56, 49, 43, 37, 30, 24, 17, 11, 4, 1, 6, 13, 19, 26, 32, 39, 45, 51, 58, 65, 62, 55, 48, 42, 36, 29, 23, 16, 10, 3, 2, 7, 14, 20, 27, 33, 40, 46, 52, 59, 61, 54, 47, 41, 35, 28, 22, 15, 9, 8, 21, 34, 53, 60], 'cur_cost': 52642.0}, {'tour': [12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 6, 9, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 3, 0, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 27898.0}, {'tour': [0, 1, 2, 4, 6, 7, 9, 10, 12, 13, 15, 16, 18, 19, 21, 22, 24, 25, 27, 28, 30, 31, 33, 34, 36, 37, 39, 40, 42, 43, 45, 46, 48, 49, 51, 52, 54, 55, 57, 58, 60, 61, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 3], 'cur_cost': 17670.0}, {'tour': array([34, 61, 40, 28,  2, 31, 26, 56, 11, 58, 55, 18, 45, 42, 62, 43, 51,
       12, 64, 16, 24, 10, 33, 59, 21, 25, 19, 50, 63, 65,  7, 35,  4, 60,
       27, 39, 49, 54, 57, 13,  3,  5, 47, 30, 37, 20,  9,  6, 53,  1, 29,
       38, 52, 46, 14, 17, 48, 15, 36, 32, 44,  8, 22, 41, 23,  0]), 'cur_cost': 102413.0}, {'tour': array([60, 62, 25, 53, 17, 18,  2, 28, 65, 22, 32, 36, 45, 10, 51, 50, 43,
       33, 56,  1, 55, 35, 42, 44, 54, 48, 52,  8, 30, 47,  4, 14,  6, 40,
        0, 11, 20, 23, 58, 27, 64, 31, 41,  7, 57,  3, 49, 19, 59, 38, 21,
       34, 29, 13, 26, 46, 16, 24,  9, 61, 12, 15, 37, 39,  5, 63]), 'cur_cost': 109613.0}, {'tour': array([ 7, 25, 45,  2, 61, 12, 53, 26, 34, 39, 15, 57, 38,  9, 31, 18, 54,
       29, 49, 60, 20, 48, 22, 52, 42, 21, 36, 23, 51, 19, 65, 55, 44, 13,
       47, 59,  1, 32, 37,  5, 41, 56,  3,  4, 30, 24, 10, 16, 43, 40, 11,
        6, 33, 58, 64,  8, 46, 62, 50, 17, 27, 28, 14, 35,  0, 63]), 'cur_cost': 109157.0}]
2025-07-04 21:57:56,824 - ExploitationExpert - INFO - 局部搜索耗时: 1.24秒
2025-07-04 21:57:56,824 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-04 21:57:56,825 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:57:56,825 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:57:56,825 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:57:56,825 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:57:56,826 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 104507.0
2025-07-04 21:57:57,329 - ExploitationExpert - INFO - res_population_num: 24
2025-07-04 21:57:57,329 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 21:57:57,330 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 21:57:57,339 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:57:57,339 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 2, 7, 14, 21, 28, 35, 42, 49, 56, 63, 58, 51, 44, 37, 30, 23, 16, 9, 45, 38, 31, 24, 17, 10, 52, 59], 'cur_cost': 55210.0}, {'tour': [0, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 16510.0}, {'tour': [4, 42, 58, 31, 17, 5, 6, 20, 28, 53, 48, 60, 45, 14, 63, 13, 30, 64, 37, 1, 33, 55, 36, 2, 40, 10, 18, 65, 47, 56, 26, 24, 49, 11, 23, 52, 39, 50, 35, 9, 29, 43, 21, 32, 16, 59, 25, 41, 15, 44, 61, 22, 34, 54, 0, 51, 38, 12, 62, 57, 7, 46, 8, 27, 19, 3], 'cur_cost': 124870.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 5, 12, 18, 25, 31, 38, 44, 50, 57, 64, 63, 56, 49, 43, 37, 30, 24, 17, 11, 4, 1, 6, 13, 19, 26, 32, 39, 45, 51, 58, 65, 62, 55, 48, 42, 36, 29, 23, 16, 10, 3, 2, 7, 14, 20, 27, 33, 40, 46, 52, 59, 61, 54, 47, 41, 35, 28, 22, 15, 9, 8, 21, 34, 53, 60], 'cur_cost': 52642.0}, {'tour': [12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 6, 9, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 3, 0, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 27898.0}, {'tour': [0, 1, 2, 4, 6, 7, 9, 10, 12, 13, 15, 16, 18, 19, 21, 22, 24, 25, 27, 28, 30, 31, 33, 34, 36, 37, 39, 40, 42, 43, 45, 46, 48, 49, 51, 52, 54, 55, 57, 58, 60, 61, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 3], 'cur_cost': 17670.0}, {'tour': array([34, 61, 40, 28,  2, 31, 26, 56, 11, 58, 55, 18, 45, 42, 62, 43, 51,
       12, 64, 16, 24, 10, 33, 59, 21, 25, 19, 50, 63, 65,  7, 35,  4, 60,
       27, 39, 49, 54, 57, 13,  3,  5, 47, 30, 37, 20,  9,  6, 53,  1, 29,
       38, 52, 46, 14, 17, 48, 15, 36, 32, 44,  8, 22, 41, 23,  0]), 'cur_cost': 102413.0}, {'tour': array([60, 62, 25, 53, 17, 18,  2, 28, 65, 22, 32, 36, 45, 10, 51, 50, 43,
       33, 56,  1, 55, 35, 42, 44, 54, 48, 52,  8, 30, 47,  4, 14,  6, 40,
        0, 11, 20, 23, 58, 27, 64, 31, 41,  7, 57,  3, 49, 19, 59, 38, 21,
       34, 29, 13, 26, 46, 16, 24,  9, 61, 12, 15, 37, 39,  5, 63]), 'cur_cost': 109613.0}, {'tour': array([42, 59, 54, 23,  9, 15, 37, 20, 12,  1,  0,  2, 45, 26, 38, 33, 30,
       57, 47, 48, 58, 11,  4, 14,  3, 34, 61, 44, 49, 35, 50, 43,  5, 39,
       62, 25, 29,  6, 32, 31, 64, 28, 56, 16, 21, 60, 53, 18, 22, 10, 36,
       24, 52,  7, 40, 65, 13, 55, 17, 27, 46, 51, 41, 63, 19,  8]), 'cur_cost': 104507.0}]
2025-07-04 21:57:57,340 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:57:57,340 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-04 21:57:57,341 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:57:57,341 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 6, 13, 20, 27, 34, 41, 48, 55, 62, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 2, 7, 14, 21, 28, 35, 42, 49, 56, 63, 58, 51, 44, 37, 30, 23, 16, 9, 45, 38, 31, 24, 17, 10, 52, 59], 'cur_cost': 55210.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42], 'cur_cost': 16510.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [4, 42, 58, 31, 17, 5, 6, 20, 28, 53, 48, 60, 45, 14, 63, 13, 30, 64, 37, 1, 33, 55, 36, 2, 40, 10, 18, 65, 47, 56, 26, 24, 49, 11, 23, 52, 39, 50, 35, 9, 29, 43, 21, 32, 16, 59, 25, 41, 15, 44, 61, 22, 34, 54, 0, 51, 38, 12, 62, 57, 7, 46, 8, 27, 19, 3], 'cur_cost': 124870.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 18, 25, 31, 38, 44, 50, 57, 64, 63, 56, 49, 43, 37, 30, 24, 17, 11, 4, 1, 6, 13, 19, 26, 32, 39, 45, 51, 58, 65, 62, 55, 48, 42, 36, 29, 23, 16, 10, 3, 2, 7, 14, 20, 27, 33, 40, 46, 52, 59, 61, 54, 47, 41, 35, 28, 22, 15, 9, 8, 21, 34, 53, 60], 'cur_cost': 52642.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 6, 9, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 3, 0, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2], 'cur_cost': 27898.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 4, 6, 7, 9, 10, 12, 13, 15, 16, 18, 19, 21, 22, 24, 25, 27, 28, 30, 31, 33, 34, 36, 37, 39, 40, 42, 43, 45, 46, 48, 49, 51, 52, 54, 55, 57, 58, 60, 61, 63, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 3], 'cur_cost': 17670.0}}]
2025-07-04 21:57:57,341 - __main__ - INFO - 进化阶段完成
2025-07-04 21:57:57,342 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:57:57,360 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 124870.0, 'mean': 62281.2, 'std': 41883.62766236946}, 'diversity': 0.9195286195286194, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:57:57,361 - __main__ - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-04 21:57:57,361 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment ---
2025-07-04 21:57:57,364 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:57:57,364 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 24 → 24
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.116 → 0.116 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:57:57,365 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:58:09,652 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_diversity_weight"]
}
```
2025-07-04 21:58:09,653 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 21:58:09,653 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-04 21:58:09,653 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-04 21:58:09,654 - __main__ - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-07-04 21:58:09,654 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:58:09,672 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 124870.0, 'mean': 62281.2, 'std': 41883.62766236946}, 'diversity': 0.9195286195286194, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:58:09,673 - PathExpert - INFO - 开始路径结构分析
2025-07-04 21:58:09,675 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 21:58:09,677 - PathExpert - INFO - 路径结构分析完成
2025-07-04 21:58:09,677 - EliteExpert - INFO - 开始精英解分析
2025-07-04 21:58:09,688 - EliteExpert - INFO - 精英解分析完成
2025-07-04 21:58:09,690 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 21:58:09,692 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 21:58:09,692 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 11479.0, mean 62281.2, max 124870.0, std 41883.62766236946
- diversity: 0.9195286195286194
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.2894707949055776}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 21:58:09,693 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:58:15,056 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "medium",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.9195286195286194,
    "convergence": 0.0,
    "clustering": "no information"
  },
  "difficult_regions": [
    "Edges with lengths around the median (1900.0) may present difficulty.",
    "Long edge ratio (0.249) suggests potential difficulties due to the presence of long edges"
  ],
  "opportunity_regions": [
    "Cells with high density in density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]] represent potential areas for exploitation."
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "Increase mutation rate: to facilitate exploration of the search space.",
      "Implement a diversification strategy in selection: to prevent premature convergence.",
      "Utilize edge recombination operators to help escape local optima."
    ]
  }
}
```
2025-07-04 21:58:15,057 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 21:58:15,057 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'medium', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9195286195286194, 'convergence': 0.0, 'clustering': 'no information'}, 'difficult_regions': ['Edges with lengths around the median (1900.0) may present difficulty.', 'Long edge ratio (0.249) suggests potential difficulties due to the presence of long edges'], 'opportunity_regions': ['Cells with high density in density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]] represent potential areas for exploitation.'], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['Increase mutation rate: to facilitate exploration of the search space.', 'Implement a diversification strategy in selection: to prevent premature convergence.', 'Utilize edge recombination operators to help escape local optima.']}}
2025-07-04 21:58:15,057 - __main__ - INFO - 开始策略分配阶段
2025-07-04 21:58:15,057 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 21:58:15,059 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.92
  • best_cost: 11479.0
  • mean_cost: 62281.2
- Search-space: ruggedness medium, modality multi, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): Edges with lengths a, Long edge ratio (0.2
- Opportunity regions (# 1): Cells with high dens
- Last-iteration feedback: ```json
{
  "evolution_stage": "initial",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 21:58:15,059 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 21:58:15,059 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:58:19,643 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Recommended to explore, diversity high but still early in evolution. Prioritize exploring a majority, some exploiting."
}
```
2025-07-04 21:58:19,644 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:58:19,644 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:58:19,644 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Recommended to explore, diversity high but still early in evolution. Prioritize exploring a majority, some exploiting."
}
```
2025-07-04 21:58:19,645 - __main__ - INFO - 策略分配阶段完成
2025-07-04 21:58:19,645 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-04 21:58:19,645 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Recommended to explore, diversity high but still early in evolution. Prioritize exploring a majority, some exploiting."
}
```
2025-07-04 21:58:19,645 - __main__ - INFO - 开始进化阶段
2025-07-04 21:58:19,645 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 21:58:19,645 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:58:19,645 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 55210.0 (rank 50.0%)
- Population diversity: 0.7123076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): Cells with high
- Difficult regions to avoid (sample): Edges with leng, Long edge ratio
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:58:19,647 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:58:19,647 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:58:24,902 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 23, 34, 45, 56, 61, 65, 60, 55, 50, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 7, 14, 19, 24, 29, 34, 39, 44, 49, 54, 59, 64, 63, 58, 53, 48, 43, 38, 33, 28, 23, 18, 13, 8, 2, 9, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 51, 40, 35, 30, 25, 20, 15, 10, 4, 3],
  "targeted_regions": "Explores low-density cells near nodes 0, 65 and other nodes.",
  "strategy_comment": "Prioritizes exploration of sparse regions and utilizes diverse connections to increase variation."
}
```
2025-07-04 21:58:24,903 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:58:24,903 - ExplorationExpert - INFO - 探索路径生成完成，成本: 46496.0, 路径: [0, 5, 12, 23, 34, 45, 56, 61, 65, 60, 55, 50, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 7, 14, 19, 24, 29, 39, 44, 49, 54, 59, 64, 63, 58, 53, 48, 43, 38, 33, 28, 18, 13, 8, 2, 9, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 51, 40, 35, 30, 25, 20, 15, 10, 4, 3]
2025-07-04 21:58:24,903 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 12, 23, 34, 45, 56, 61, 65, 60, 55, 50, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 7, 14, 19, 24, 29, 39, 44, 49, 54, 59, 64, 63, 58, 53, 48, 43, 38, 33, 28, 18, 13, 8, 2, 9, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 51, 40, 35, 30, 25, 20, 15, 10, 4, 3], 'cur_cost': 46496.0}
2025-07-04 21:58:24,903 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 21:58:24,903 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:58:24,903 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 16510.0 (rank 10.0%)
- Population diversity: 0.7476923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): Cells with high
- Difficult regions to avoid (sample): Edges with leng, Long edge ratio
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:58:24,903 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:58:24,907 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:58:45,067 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Max retries exceeded with url: /v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:2406)')))
2025-07-04 21:58:47,068 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-04 21:59:01,136 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 62, 55, 48, 41, 34, 27, 20, 13, 6, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 10, 17, 24, 31, 38, 45, 52, 59],
  "targeted_regions": "Sparse cells around nodes 5, 12, 19.",
  "strategy_comment": "Exploiting sparse cell adjacencies, incorporating diverse edge lengths, avoiding problematic edges for exploration."
}
```
2025-07-04 21:59:01,137 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:59:01,137 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54472.0, 路径: [5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 62, 55, 48, 41, 34, 27, 20, 13, 6, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 10, 17, 24, 31, 38, 45, 52, 59]
2025-07-04 21:59:01,137 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 62, 55, 48, 41, 34, 27, 20, 13, 6, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 10, 17, 24, 31, 38, 45, 52, 59], 'cur_cost': 54472.0}
2025-07-04 21:59:01,137 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 21:59:01,138 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:59:01,139 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 124870.0 (rank 90.0%)
- Population diversity: 0.7876923076923077
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): Cells with high
- Difficult regions to avoid (sample): Edges with leng, Long edge ratio
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:59:01,139 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:59:01,139 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:59:06,546 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "Explore low-density cells adjacent to 0-65.",
  "strategy_comment": "Simple path, visits all nodes to explore sparse cells, avoiding long edges."
}
```
2025-07-04 21:59:06,547 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:59:06,548 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:59:06,548 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:59:06,548 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 21:59:06,549 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:59:06,549 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6984615384615385
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): Cells with high
- Difficult regions to avoid (sample): Edges with leng, Long edge ratio
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:59:06,550 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:59:06,550 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:59:17,449 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 15, 23, 31, 39, 47, 55, 63, 62, 54, 46, 38, 30, 22, 14, 6, 7, 1, 9, 17, 25, 33, 41, 49, 57, 56, 48, 40, 32, 24, 16, 8, 2, 10, 18, 26, 34, 42, 50, 58, 60, 61, 53, 45, 37, 29, 21, 13, 5, 4, 3, 11, 19, 27, 35, 43, 51, 59, 52, 44, 36, 28, 20, 12, 65],
  "targeted_regions": "Explores peripheral nodes and connects via moderate edges.",
  "strategy_comment": "Prioritizes visiting unexplored cells, starting from the edges, to increase path diversity."
}
```
2025-07-04 21:59:17,450 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:59:17,450 - ExplorationExpert - INFO - 探索路径生成完成，成本: 62430.0, 路径: [0, 15, 23, 31, 39, 47, 55, 63, 62, 54, 46, 38, 30, 22, 14, 6, 7, 1, 9, 17, 25, 33, 41, 49, 57, 56, 48, 40, 32, 24, 16, 8, 2, 10, 18, 26, 34, 42, 50, 58, 60, 61, 53, 45, 37, 29, 21, 13, 5, 4, 3, 11, 19, 27, 35, 43, 51, 59, 52, 44, 36, 28, 20, 12, 65, 64]
2025-07-04 21:59:17,451 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 15, 23, 31, 39, 47, 55, 63, 62, 54, 46, 38, 30, 22, 14, 6, 7, 1, 9, 17, 25, 33, 41, 49, 57, 56, 48, 40, 32, 24, 16, 8, 2, 10, 18, 26, 34, 42, 50, 58, 60, 61, 53, 45, 37, 29, 21, 13, 5, 4, 3, 11, 19, 27, 35, 43, 51, 59, 52, 44, 36, 28, 20, 12, 65, 64], 'cur_cost': 62430.0}
2025-07-04 21:59:17,451 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 21:59:17,451 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:59:17,452 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 52642.0 (rank 40.0%)
- Population diversity: 0.7723076923076924
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): Cells with high
- Difficult regions to avoid (sample): Edges with leng, Long edge ratio
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:59:17,453 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:59:17,453 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:59:25,189 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65
  ],
  "targeted_regions": "Explores all regions.",
  "strategy_comment": "Initial path focusing on universal exploration and ensuring full coverage for diversity."
}
```
2025-07-04 21:59:25,190 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:59:25,191 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 21:59:25,191 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-04 21:59:25,191 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-04 21:59:25,191 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 21:59:25,192 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 27898.0 (rank 30.0%)
- Population diversity: 0.7323076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): Cells with high
- Difficult regions to avoid (sample): Edges with leng, Long edge ratio
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 21:59:25,192 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 21:59:25,192 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:59:34,579 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2],
  "targeted_regions": "Explores the periphery and connects across the central area.",
  "strategy_comment": "Exploration focus. Prioritizes edge exploration, avoids long edges, leverages sparse regions."
}
```
2025-07-04 21:59:34,580 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 21:59:34,580 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11494.0, 路径: [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2]
2025-07-04 21:59:34,581 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}
2025-07-04 21:59:34,581 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 21:59:34,581 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:59:34,582 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:59:34,582 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 97593.0
2025-07-04 21:59:35,085 - ExploitationExpert - INFO - res_population_num: 25
2025-07-04 21:59:35,085 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527, 9521]
2025-07-04 21:59:35,086 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:59:35,095 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:59:35,095 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 23, 34, 45, 56, 61, 65, 60, 55, 50, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 7, 14, 19, 24, 29, 39, 44, 49, 54, 59, 64, 63, 58, 53, 48, 43, 38, 33, 28, 18, 13, 8, 2, 9, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 51, 40, 35, 30, 25, 20, 15, 10, 4, 3], 'cur_cost': 46496.0}, {'tour': [5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 62, 55, 48, 41, 34, 27, 20, 13, 6, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 10, 17, 24, 31, 38, 45, 52, 59], 'cur_cost': 54472.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 15, 23, 31, 39, 47, 55, 63, 62, 54, 46, 38, 30, 22, 14, 6, 7, 1, 9, 17, 25, 33, 41, 49, 57, 56, 48, 40, 32, 24, 16, 8, 2, 10, 18, 26, 34, 42, 50, 58, 60, 61, 53, 45, 37, 29, 21, 13, 5, 4, 3, 11, 19, 27, 35, 43, 51, 59, 52, 44, 36, 28, 20, 12, 65, 64], 'cur_cost': 62430.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}, {'tour': array([ 0, 51, 16, 52, 18, 32, 34, 26,  4, 24, 14, 31,  2, 60, 44, 62, 58,
       12,  6, 15, 40, 45, 56, 10, 64, 19, 23, 57, 50,  9,  7, 41, 27, 13,
        3, 33, 39, 42, 47, 48, 43,  1, 21, 25,  8, 11, 65, 63,  5, 38, 37,
       59, 22, 54, 49, 61, 53, 17, 55, 35, 36, 46, 28, 20, 30, 29]), 'cur_cost': 97593.0}, {'tour': array([34, 61, 40, 28,  2, 31, 26, 56, 11, 58, 55, 18, 45, 42, 62, 43, 51,
       12, 64, 16, 24, 10, 33, 59, 21, 25, 19, 50, 63, 65,  7, 35,  4, 60,
       27, 39, 49, 54, 57, 13,  3,  5, 47, 30, 37, 20,  9,  6, 53,  1, 29,
       38, 52, 46, 14, 17, 48, 15, 36, 32, 44,  8, 22, 41, 23,  0]), 'cur_cost': 102413.0}, {'tour': array([60, 62, 25, 53, 17, 18,  2, 28, 65, 22, 32, 36, 45, 10, 51, 50, 43,
       33, 56,  1, 55, 35, 42, 44, 54, 48, 52,  8, 30, 47,  4, 14,  6, 40,
        0, 11, 20, 23, 58, 27, 64, 31, 41,  7, 57,  3, 49, 19, 59, 38, 21,
       34, 29, 13, 26, 46, 16, 24,  9, 61, 12, 15, 37, 39,  5, 63]), 'cur_cost': 109613.0}, {'tour': array([42, 59, 54, 23,  9, 15, 37, 20, 12,  1,  0,  2, 45, 26, 38, 33, 30,
       57, 47, 48, 58, 11,  4, 14,  3, 34, 61, 44, 49, 35, 50, 43,  5, 39,
       62, 25, 29,  6, 32, 31, 64, 28, 56, 16, 21, 60, 53, 18, 22, 10, 36,
       24, 52,  7, 40, 65, 13, 55, 17, 27, 46, 51, 41, 63, 19,  8]), 'cur_cost': 104507.0}]
2025-07-04 21:59:35,096 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:59:35,097 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-04 21:59:35,097 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 21:59:35,097 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 21:59:35,097 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:59:35,098 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:59:35,098 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 109363.0
2025-07-04 21:59:35,600 - root - WARNING - 无法找到足够的不重叠段 (找到 2/4)，使用退化策略
2025-07-04 21:59:35,600 - ExploitationExpert - INFO - res_population_num: 25
2025-07-04 21:59:35,600 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527, 9521]
2025-07-04 21:59:35,600 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-04 21:59:35,610 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:59:35,610 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 23, 34, 45, 56, 61, 65, 60, 55, 50, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 7, 14, 19, 24, 29, 39, 44, 49, 54, 59, 64, 63, 58, 53, 48, 43, 38, 33, 28, 18, 13, 8, 2, 9, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 51, 40, 35, 30, 25, 20, 15, 10, 4, 3], 'cur_cost': 46496.0}, {'tour': [5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 62, 55, 48, 41, 34, 27, 20, 13, 6, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 10, 17, 24, 31, 38, 45, 52, 59], 'cur_cost': 54472.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 15, 23, 31, 39, 47, 55, 63, 62, 54, 46, 38, 30, 22, 14, 6, 7, 1, 9, 17, 25, 33, 41, 49, 57, 56, 48, 40, 32, 24, 16, 8, 2, 10, 18, 26, 34, 42, 50, 58, 60, 61, 53, 45, 37, 29, 21, 13, 5, 4, 3, 11, 19, 27, 35, 43, 51, 59, 52, 44, 36, 28, 20, 12, 65, 64], 'cur_cost': 62430.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}, {'tour': array([ 0, 51, 16, 52, 18, 32, 34, 26,  4, 24, 14, 31,  2, 60, 44, 62, 58,
       12,  6, 15, 40, 45, 56, 10, 64, 19, 23, 57, 50,  9,  7, 41, 27, 13,
        3, 33, 39, 42, 47, 48, 43,  1, 21, 25,  8, 11, 65, 63,  5, 38, 37,
       59, 22, 54, 49, 61, 53, 17, 55, 35, 36, 46, 28, 20, 30, 29]), 'cur_cost': 97593.0}, {'tour': array([ 0,  3, 50, 23, 54, 29, 36, 51,  1, 44, 41, 43, 24,  7, 63, 30, 12,
       65, 52, 32, 11, 46, 16, 59, 27, 19, 47, 17, 38, 62, 15, 49, 40, 13,
       45, 21, 22, 53, 25, 33, 60,  6, 34, 56, 14,  8, 26, 58,  4, 20, 35,
       31, 64, 18, 28, 48,  9, 42,  2,  5, 39, 10, 37, 61, 55, 57]), 'cur_cost': 109363.0}, {'tour': array([60, 62, 25, 53, 17, 18,  2, 28, 65, 22, 32, 36, 45, 10, 51, 50, 43,
       33, 56,  1, 55, 35, 42, 44, 54, 48, 52,  8, 30, 47,  4, 14,  6, 40,
        0, 11, 20, 23, 58, 27, 64, 31, 41,  7, 57,  3, 49, 19, 59, 38, 21,
       34, 29, 13, 26, 46, 16, 24,  9, 61, 12, 15, 37, 39,  5, 63]), 'cur_cost': 109613.0}, {'tour': array([42, 59, 54, 23,  9, 15, 37, 20, 12,  1,  0,  2, 45, 26, 38, 33, 30,
       57, 47, 48, 58, 11,  4, 14,  3, 34, 61, 44, 49, 35, 50, 43,  5, 39,
       62, 25, 29,  6, 32, 31, 64, 28, 56, 16, 21, 60, 53, 18, 22, 10, 36,
       24, 52,  7, 40, 65, 13, 55, 17, 27, 46, 51, 41, 63, 19,  8]), 'cur_cost': 104507.0}]
2025-07-04 21:59:35,613 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:59:35,613 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-04 21:59:35,614 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 21:59:35,614 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 21:59:35,614 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:59:35,615 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:59:35,615 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 106066.0
2025-07-04 21:59:36,117 - ExploitationExpert - INFO - res_population_num: 26
2025-07-04 21:59:36,117 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527, 9521, 9521]
2025-07-04 21:59:36,117 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 21:59:36,127 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:59:36,127 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 23, 34, 45, 56, 61, 65, 60, 55, 50, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 7, 14, 19, 24, 29, 39, 44, 49, 54, 59, 64, 63, 58, 53, 48, 43, 38, 33, 28, 18, 13, 8, 2, 9, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 51, 40, 35, 30, 25, 20, 15, 10, 4, 3], 'cur_cost': 46496.0}, {'tour': [5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 62, 55, 48, 41, 34, 27, 20, 13, 6, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 10, 17, 24, 31, 38, 45, 52, 59], 'cur_cost': 54472.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 15, 23, 31, 39, 47, 55, 63, 62, 54, 46, 38, 30, 22, 14, 6, 7, 1, 9, 17, 25, 33, 41, 49, 57, 56, 48, 40, 32, 24, 16, 8, 2, 10, 18, 26, 34, 42, 50, 58, 60, 61, 53, 45, 37, 29, 21, 13, 5, 4, 3, 11, 19, 27, 35, 43, 51, 59, 52, 44, 36, 28, 20, 12, 65, 64], 'cur_cost': 62430.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}, {'tour': array([ 0, 51, 16, 52, 18, 32, 34, 26,  4, 24, 14, 31,  2, 60, 44, 62, 58,
       12,  6, 15, 40, 45, 56, 10, 64, 19, 23, 57, 50,  9,  7, 41, 27, 13,
        3, 33, 39, 42, 47, 48, 43,  1, 21, 25,  8, 11, 65, 63,  5, 38, 37,
       59, 22, 54, 49, 61, 53, 17, 55, 35, 36, 46, 28, 20, 30, 29]), 'cur_cost': 97593.0}, {'tour': array([ 0,  3, 50, 23, 54, 29, 36, 51,  1, 44, 41, 43, 24,  7, 63, 30, 12,
       65, 52, 32, 11, 46, 16, 59, 27, 19, 47, 17, 38, 62, 15, 49, 40, 13,
       45, 21, 22, 53, 25, 33, 60,  6, 34, 56, 14,  8, 26, 58,  4, 20, 35,
       31, 64, 18, 28, 48,  9, 42,  2,  5, 39, 10, 37, 61, 55, 57]), 'cur_cost': 109363.0}, {'tour': array([ 8, 37,  5, 53, 11,  0, 22, 64, 46, 21, 17, 14, 58, 27, 30, 28, 43,
       15, 23, 36, 32, 51, 25, 62, 42, 56,  9, 33,  6, 50, 59, 49, 38, 57,
        7, 31, 16, 55, 44,  1,  4, 29, 13, 10, 19, 39, 26, 20, 48, 35, 52,
       40, 41, 54,  2,  3, 65, 61, 18, 34, 24, 63, 45, 60, 47, 12]), 'cur_cost': 106066.0}, {'tour': array([42, 59, 54, 23,  9, 15, 37, 20, 12,  1,  0,  2, 45, 26, 38, 33, 30,
       57, 47, 48, 58, 11,  4, 14,  3, 34, 61, 44, 49, 35, 50, 43,  5, 39,
       62, 25, 29,  6, 32, 31, 64, 28, 56, 16, 21, 60, 53, 18, 22, 10, 36,
       24, 52,  7, 40, 65, 13, 55, 17, 27, 46, 51, 41, 63, 19,  8]), 'cur_cost': 104507.0}]
2025-07-04 21:59:36,129 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:59:36,130 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-07-04 21:59:36,130 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 21:59:36,130 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 21:59:36,130 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 21:59:36,131 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 21:59:36,131 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 99633.0
2025-07-04 21:59:36,634 - ExploitationExpert - INFO - res_population_num: 26
2025-07-04 21:59:36,634 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527, 9521, 9521]
2025-07-04 21:59:36,635 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 21:59:36,643 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 21:59:36,644 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 23, 34, 45, 56, 61, 65, 60, 55, 50, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 7, 14, 19, 24, 29, 39, 44, 49, 54, 59, 64, 63, 58, 53, 48, 43, 38, 33, 28, 18, 13, 8, 2, 9, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 51, 40, 35, 30, 25, 20, 15, 10, 4, 3], 'cur_cost': 46496.0}, {'tour': [5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 62, 55, 48, 41, 34, 27, 20, 13, 6, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 10, 17, 24, 31, 38, 45, 52, 59], 'cur_cost': 54472.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 15, 23, 31, 39, 47, 55, 63, 62, 54, 46, 38, 30, 22, 14, 6, 7, 1, 9, 17, 25, 33, 41, 49, 57, 56, 48, 40, 32, 24, 16, 8, 2, 10, 18, 26, 34, 42, 50, 58, 60, 61, 53, 45, 37, 29, 21, 13, 5, 4, 3, 11, 19, 27, 35, 43, 51, 59, 52, 44, 36, 28, 20, 12, 65, 64], 'cur_cost': 62430.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}, {'tour': array([ 0, 51, 16, 52, 18, 32, 34, 26,  4, 24, 14, 31,  2, 60, 44, 62, 58,
       12,  6, 15, 40, 45, 56, 10, 64, 19, 23, 57, 50,  9,  7, 41, 27, 13,
        3, 33, 39, 42, 47, 48, 43,  1, 21, 25,  8, 11, 65, 63,  5, 38, 37,
       59, 22, 54, 49, 61, 53, 17, 55, 35, 36, 46, 28, 20, 30, 29]), 'cur_cost': 97593.0}, {'tour': array([ 0,  3, 50, 23, 54, 29, 36, 51,  1, 44, 41, 43, 24,  7, 63, 30, 12,
       65, 52, 32, 11, 46, 16, 59, 27, 19, 47, 17, 38, 62, 15, 49, 40, 13,
       45, 21, 22, 53, 25, 33, 60,  6, 34, 56, 14,  8, 26, 58,  4, 20, 35,
       31, 64, 18, 28, 48,  9, 42,  2,  5, 39, 10, 37, 61, 55, 57]), 'cur_cost': 109363.0}, {'tour': array([ 8, 37,  5, 53, 11,  0, 22, 64, 46, 21, 17, 14, 58, 27, 30, 28, 43,
       15, 23, 36, 32, 51, 25, 62, 42, 56,  9, 33,  6, 50, 59, 49, 38, 57,
        7, 31, 16, 55, 44,  1,  4, 29, 13, 10, 19, 39, 26, 20, 48, 35, 52,
       40, 41, 54,  2,  3, 65, 61, 18, 34, 24, 63, 45, 60, 47, 12]), 'cur_cost': 106066.0}, {'tour': array([ 7, 60,  8, 49, 24, 32, 35,  6,  4, 25, 26, 52, 28, 15,  0, 18, 65,
       63, 14, 16, 30, 29, 17, 33,  5, 44, 21, 64, 40, 54, 23, 11, 61, 58,
       27, 53, 41, 12, 43, 34, 48, 20, 22, 62, 50, 38, 19, 59, 13, 46,  2,
       55, 42, 37, 31, 45, 36, 51, 57, 47, 39,  1,  9, 56, 10,  3]), 'cur_cost': 99633.0}]
2025-07-04 21:59:36,645 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-04 21:59:36,645 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-07-04 21:59:36,645 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 21:59:36,646 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 23, 34, 45, 56, 61, 65, 60, 55, 50, 46, 41, 36, 31, 26, 21, 16, 11, 6, 1, 7, 14, 19, 24, 29, 39, 44, 49, 54, 59, 64, 63, 58, 53, 48, 43, 38, 33, 28, 18, 13, 8, 2, 9, 17, 22, 27, 32, 37, 42, 47, 52, 57, 62, 51, 40, 35, 30, 25, 20, 15, 10, 4, 3], 'cur_cost': 46496.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 12, 19, 26, 33, 40, 47, 54, 61, 60, 53, 46, 39, 32, 25, 18, 11, 4, 1, 0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 62, 55, 48, 41, 34, 27, 20, 13, 6, 2, 9, 16, 23, 30, 37, 44, 51, 58, 65, 64, 57, 50, 43, 36, 29, 22, 15, 8, 3, 10, 17, 24, 31, 38, 45, 52, 59], 'cur_cost': 54472.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 15, 23, 31, 39, 47, 55, 63, 62, 54, 46, 38, 30, 22, 14, 6, 7, 1, 9, 17, 25, 33, 41, 49, 57, 56, 48, 40, 32, 24, 16, 8, 2, 10, 18, 26, 34, 42, 50, 58, 60, 61, 53, 45, 37, 29, 21, 13, 5, 4, 3, 11, 19, 27, 35, 43, 51, 59, 52, 44, 36, 28, 20, 12, 65, 64], 'cur_cost': 62430.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2], 'cur_cost': 11494.0}}]
2025-07-04 21:59:36,646 - __main__ - INFO - 进化阶段完成
2025-07-04 21:59:36,646 - StatsExpert - INFO - 开始统计分析
2025-07-04 21:59:36,665 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 109363.0, 'mean': 61050.5, 'std': 38486.33988638046}, 'diversity': 0.8979797979797979, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 3, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 21:59:36,665 - __main__ - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-07-04 21:59:36,666 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment ---
2025-07-04 21:59:36,667 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 21:59:36,667 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 26 → 26
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.107 → 0.107 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 21:59:36,669 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 21:59:56,873 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Max retries exceeded with url: /v1beta/models/gemini-2.0-flash-lite:generateContent?key=AIzaSyACLpBLxXj9c-9JtIjBN12VSxXQJGNR5GQ (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:2406)')))
2025-07-04 21:59:58,874 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-04 22:00:03,219 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": 0,
    "exploit": 0
  },
  "balance_state": "stagnant",
  "recommendations": [
    "increase explore_ratio to 0.2",
    "increase mutation rate to 0.05",
    "if possible, adjust exploration parameters to create greater diversity, such as increasing the range of values explored during mutation",
    "monitor diversity closely, and decrease explore_ratio if diversity increases too rapidly without improvement"
  ]
}
```
2025-07-04 22:00:03,220 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 22:00:03,220 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-07-04 22:00:03,220 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-04 22:00:03,222 - __main__ - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-07-04 22:00:03,222 - StatsExpert - INFO - 开始统计分析
2025-07-04 22:00:03,239 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 109363.0, 'mean': 61050.5, 'std': 38486.33988638046}, 'diversity': 0.8979797979797979, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 3, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 22:00:03,239 - PathExpert - INFO - 开始路径结构分析
2025-07-04 22:00:03,244 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 22:00:03,244 - PathExpert - INFO - 路径结构分析完成
2025-07-04 22:00:03,245 - EliteExpert - INFO - 开始精英解分析
2025-07-04 22:00:03,253 - EliteExpert - INFO - 精英解分析完成
2025-07-04 22:00:03,259 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 22:00:03,262 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 22:00:03,262 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 11479.0, mean 61050.5, max 109363.0, std 38486.33988638046
- diversity: 0.8979797979797979
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.2853146853146853}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 22:00:03,263 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:00:10,119 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.5,
    "modality": "moderate",
    "deceptiveness": "potentially high"
  },
  "population_state": {
    "diversity": 0.898,
    "convergence": 0.0,
    "clustering": "No clustering information"
  },
  "difficult_regions": [
    "Region potentially spanning cells with low node density, especially considering the high long edge ratio. Investigate areas in the grid with low node density combined with long edges as potential bottlenecks."
  ],
  "opportunity_regions": [
    "High-density cells (identified by density_grid) likely contain promising node clusters for exploitation. Focus on those cells to refine the solution. Cells [0,0], [1,1], [2,0], [2,2] deserve focus."
  ],
  "evolution_phase": "early exploration",
  "evolution_direction": {
    "recommended_focus": "explore and exploit",
    "operators": [
      "Edge exchange operators within the high-density cells (e.g., 2-opt, 3-opt) to refine promising node clusters (exploit).",
      "Local search operators that can jump out from local optima in potentially difficult regions (explore), like a more aggressive edge exchange algorithm or diversification methods.",
      "Recombination/crossover operators biased toward combining high-quality edges and/or subpaths when they become available from structural signals. (future)",
      "Introduce diversification if convergence is not reached (if the diversity score drops significantly, and the best cost is not improving)"
    ]
  }
}
```
2025-07-04 22:00:10,119 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 22:00:10,119 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.5, 'modality': 'moderate', 'deceptiveness': 'potentially high'}, 'population_state': {'diversity': 0.898, 'convergence': 0.0, 'clustering': 'No clustering information'}, 'difficult_regions': ['Region potentially spanning cells with low node density, especially considering the high long edge ratio. Investigate areas in the grid with low node density combined with long edges as potential bottlenecks.'], 'opportunity_regions': ['High-density cells (identified by density_grid) likely contain promising node clusters for exploitation. Focus on those cells to refine the solution. Cells [0,0], [1,1], [2,0], [2,2] deserve focus.'], 'evolution_phase': 'early exploration', 'evolution_direction': {'recommended_focus': 'explore and exploit', 'operators': ['Edge exchange operators within the high-density cells (e.g., 2-opt, 3-opt) to refine promising node clusters (exploit).', 'Local search operators that can jump out from local optima in potentially difficult regions (explore), like a more aggressive edge exchange algorithm or diversification methods.', 'Recombination/crossover operators biased toward combining high-quality edges and/or subpaths when they become available from structural signals. (future)', 'Introduce diversification if convergence is not reached (if the diversity score drops significantly, and the best cost is not improving)']}}
2025-07-04 22:00:10,119 - __main__ - INFO - 开始策略分配阶段
2025-07-04 22:00:10,119 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 22:00:10,119 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.898
  • best_cost: 11479.0
  • mean_cost: 61050.5
- Search-space: ruggedness 0.5, modality moderate, deceptiveness potentially high
- Evolution phase: early exploration
- Landscape focus suggestion: explore and exploit
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 1): Region potentially s
- Opportunity regions (# 1): High-density cells (
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": 0,
    "exploit": 0
  },
  "balance...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 22:00:10,119 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 22:00:10,119 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:00:40,129 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=30)
2025-07-04 22:00:42,130 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-04 22:00:49,643 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Early exploration stage with moderate diversity. Prioritize exploration, using high global ratio, with some individuals for exploiting promising areas."
}
```
2025-07-04 22:00:49,644 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-04 22:00:49,644 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-04 22:00:49,644 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Early exploration stage with moderate diversity. Prioritize exploration, using high global ratio, with some individuals for exploiting promising areas."
}
```
2025-07-04 22:00:49,645 - __main__ - INFO - 策略分配阶段完成
2025-07-04 22:00:49,645 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit']
2025-07-04 22:00:49,645 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Early exploration stage with moderate diversity. Prioritize exploration, using high global ratio, with some individuals for exploiting promising areas."
}
```
2025-07-04 22:00:49,646 - __main__ - INFO - 开始进化阶段
2025-07-04 22:00:49,646 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 22:00:49,646 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:00:49,647 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 46496.0 (rank 30.0%)
- Population diversity: 0.7107692307692308
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Region potentia
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:00:49,648 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:00:49,648 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:00:55,082 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [10, 12, 13, 11, 18, 19, 17, 24, 25, 26, 27, 20, 21, 22, 23, 30, 31, 32, 33, 28, 29, 34, 35, 36, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 42, 41, 40, 39, 38, 37, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 14, 15, 16, 0],
  "targeted_regions": "High-density ce:17-27, Sparse cells 0,1, 10-13, 34-37, 54-65",
  "strategy_comment": "Prioritized opportunity regions and sparse cells while attempting diversification and avoiding difficult areas."
}
```
2025-07-04 22:00:55,083 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:00:55,083 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26354.0, 路径: [10, 12, 13, 11, 18, 19, 17, 24, 25, 26, 27, 20, 21, 22, 23, 30, 31, 32, 33, 28, 29, 34, 35, 36, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 42, 41, 40, 39, 38, 37, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 14, 15, 16, 0]
2025-07-04 22:00:55,084 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [10, 12, 13, 11, 18, 19, 17, 24, 25, 26, 27, 20, 21, 22, 23, 30, 31, 32, 33, 28, 29, 34, 35, 36, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 42, 41, 40, 39, 38, 37, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 14, 15, 16, 0], 'cur_cost': 26354.0}
2025-07-04 22:00:55,085 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 22:00:55,085 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:00:55,086 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 54472.0 (rank 40.0%)
- Population diversity: 0.6476923076923077
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Region potentia
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:00:55,086 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:00:55,086 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:01:02,732 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2],
  "targeted_regions": "Opportunity region around nodes 0-65",
  "strategy_comment": "Traverses opportunity regions, increasing diversity by exploring a different order."
}
```
2025-07-04 22:01:02,732 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:01:02,732 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17700.0, 路径: [0, 1, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 3]
2025-07-04 22:01:02,732 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 3], 'cur_cost': 17700.0}
2025-07-04 22:01:02,732 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 22:01:02,732 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:01:02,732 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6446153846153846
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Region potentia
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:01:02,732 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:01:02,732 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:01:26,903 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41],
  "targeted_regions": "Explores high-density region (42-65) and connects back to low-density nodes.",
  "strategy_comment": "Prioritizes opportunity regions and aims to maximize diversity through exploration of unexplored areas."
}
```
2025-07-04 22:01:26,903 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:01:26,904 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41]
2025-07-04 22:01:26,904 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 11479.0}
2025-07-04 22:01:26,904 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 22:01:26,905 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:01:26,905 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 62430.0 (rank 50.0%)
- Population diversity: 0.6461538461538462
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Region potentia
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:01:26,906 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:01:26,906 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:01:34,887 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [10, 12, 11, 13, 14, 15, 16, 17, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 18, 19, 20, 21, 22, 23, 24],
  "targeted_regions": "Explore low-density cells near opportunity regions.",
  "strategy_comment": "Prioritizes exploration of unexplored areas adjacent to high-density regions to improve path diversity."
}
```
2025-07-04 22:01:34,887 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:01:34,889 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18152.0, 路径: [10, 12, 11, 13, 14, 15, 16, 17, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 18, 19, 20, 21, 22, 23, 24]
2025-07-04 22:01:34,889 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [10, 12, 11, 13, 14, 15, 16, 17, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 18, 19, 20, 21, 22, 23, 24], 'cur_cost': 18152.0}
2025-07-04 22:01:34,889 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 22:01:34,890 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:01:34,890 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.5784615384615385
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Region potentia
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:01:34,891 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:01:34,891 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:01:45,259 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [4, 12, 15, 22, 23, 21, 20, 19, 18, 17, 16, 11, 10, 9, 8, 7, 6, 5, 13, 14, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 24, 25, 26, 27],
  "targeted_regions": "Opportunity regions (around nodes 15-23, 28-38)",
  "strategy_comment": "Prioritizes opportunity regions and attempts to minimize difficult region crossings."
}
```
2025-07-04 22:01:45,260 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:01:45,260 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17628.0, 路径: [4, 12, 15, 22, 23, 21, 20, 19, 18, 17, 16, 11, 10, 9, 8, 7, 6, 5, 13, 14, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 24, 25, 26, 27]
2025-07-04 22:01:45,261 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [4, 12, 15, 22, 23, 21, 20, 19, 18, 17, 16, 11, 10, 9, 8, 7, 6, 5, 13, 14, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 24, 25, 26, 27], 'cur_cost': 17628.0}
2025-07-04 22:01:45,261 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-04 22:01:45,261 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 22:01:45,261 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 22:01:45,262 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 105027.0
2025-07-04 22:01:45,764 - ExploitationExpert - INFO - res_population_num: 28
2025-07-04 22:01:45,764 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527, 9521, 9521]
2025-07-04 22:01:45,764 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 22:01:45,776 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 22:01:45,776 - ExploitationExpert - INFO - populations: [{'tour': [10, 12, 13, 11, 18, 19, 17, 24, 25, 26, 27, 20, 21, 22, 23, 30, 31, 32, 33, 28, 29, 34, 35, 36, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 42, 41, 40, 39, 38, 37, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 14, 15, 16, 0], 'cur_cost': 26354.0}, {'tour': [0, 1, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 3], 'cur_cost': 17700.0}, {'tour': [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 11479.0}, {'tour': [10, 12, 11, 13, 14, 15, 16, 17, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 18, 19, 20, 21, 22, 23, 24], 'cur_cost': 18152.0}, {'tour': [4, 12, 15, 22, 23, 21, 20, 19, 18, 17, 16, 11, 10, 9, 8, 7, 6, 5, 13, 14, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 24, 25, 26, 27], 'cur_cost': 17628.0}, {'tour': array([33, 36, 49, 24, 61, 31, 11,  3, 48, 40, 63, 41, 17, 53, 35, 23, 52,
       50, 60, 16,  6, 15,  4, 57, 47, 22,  8, 29, 44, 42, 55, 38, 13, 32,
       27,  1,  5, 65,  7, 54, 64, 14, 37, 46,  0, 12, 26, 51, 45, 58, 10,
       30,  9, 20, 34, 43, 28, 62, 56, 39, 19,  2, 59, 18, 21, 25]), 'cur_cost': 105027.0}, {'tour': array([ 0, 51, 16, 52, 18, 32, 34, 26,  4, 24, 14, 31,  2, 60, 44, 62, 58,
       12,  6, 15, 40, 45, 56, 10, 64, 19, 23, 57, 50,  9,  7, 41, 27, 13,
        3, 33, 39, 42, 47, 48, 43,  1, 21, 25,  8, 11, 65, 63,  5, 38, 37,
       59, 22, 54, 49, 61, 53, 17, 55, 35, 36, 46, 28, 20, 30, 29]), 'cur_cost': 97593.0}, {'tour': array([ 0,  3, 50, 23, 54, 29, 36, 51,  1, 44, 41, 43, 24,  7, 63, 30, 12,
       65, 52, 32, 11, 46, 16, 59, 27, 19, 47, 17, 38, 62, 15, 49, 40, 13,
       45, 21, 22, 53, 25, 33, 60,  6, 34, 56, 14,  8, 26, 58,  4, 20, 35,
       31, 64, 18, 28, 48,  9, 42,  2,  5, 39, 10, 37, 61, 55, 57]), 'cur_cost': 109363.0}, {'tour': array([ 8, 37,  5, 53, 11,  0, 22, 64, 46, 21, 17, 14, 58, 27, 30, 28, 43,
       15, 23, 36, 32, 51, 25, 62, 42, 56,  9, 33,  6, 50, 59, 49, 38, 57,
        7, 31, 16, 55, 44,  1,  4, 29, 13, 10, 19, 39, 26, 20, 48, 35, 52,
       40, 41, 54,  2,  3, 65, 61, 18, 34, 24, 63, 45, 60, 47, 12]), 'cur_cost': 106066.0}, {'tour': array([ 7, 60,  8, 49, 24, 32, 35,  6,  4, 25, 26, 52, 28, 15,  0, 18, 65,
       63, 14, 16, 30, 29, 17, 33,  5, 44, 21, 64, 40, 54, 23, 11, 61, 58,
       27, 53, 41, 12, 43, 34, 48, 20, 22, 62, 50, 38, 19, 59, 13, 46,  2,
       55, 42, 37, 31, 45, 36, 51, 57, 47, 39,  1,  9, 56, 10,  3]), 'cur_cost': 99633.0}]
2025-07-04 22:01:45,779 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 22:01:45,779 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-07-04 22:01:45,779 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-04 22:01:45,779 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 22:01:45,780 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 22:01:45,780 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 22:01:45,780 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 108523.0
2025-07-04 22:01:46,284 - ExploitationExpert - INFO - res_population_num: 29
2025-07-04 22:01:46,284 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527, 9521, 9521, 9521]
2025-07-04 22:01:46,284 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 22:01:46,294 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 22:01:46,295 - ExploitationExpert - INFO - populations: [{'tour': [10, 12, 13, 11, 18, 19, 17, 24, 25, 26, 27, 20, 21, 22, 23, 30, 31, 32, 33, 28, 29, 34, 35, 36, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 42, 41, 40, 39, 38, 37, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 14, 15, 16, 0], 'cur_cost': 26354.0}, {'tour': [0, 1, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 3], 'cur_cost': 17700.0}, {'tour': [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 11479.0}, {'tour': [10, 12, 11, 13, 14, 15, 16, 17, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 18, 19, 20, 21, 22, 23, 24], 'cur_cost': 18152.0}, {'tour': [4, 12, 15, 22, 23, 21, 20, 19, 18, 17, 16, 11, 10, 9, 8, 7, 6, 5, 13, 14, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 24, 25, 26, 27], 'cur_cost': 17628.0}, {'tour': array([33, 36, 49, 24, 61, 31, 11,  3, 48, 40, 63, 41, 17, 53, 35, 23, 52,
       50, 60, 16,  6, 15,  4, 57, 47, 22,  8, 29, 44, 42, 55, 38, 13, 32,
       27,  1,  5, 65,  7, 54, 64, 14, 37, 46,  0, 12, 26, 51, 45, 58, 10,
       30,  9, 20, 34, 43, 28, 62, 56, 39, 19,  2, 59, 18, 21, 25]), 'cur_cost': 105027.0}, {'tour': array([50,  0, 38, 12, 53, 47, 34, 31, 16, 40, 35, 11, 42, 65, 25,  9, 33,
       51, 37, 14, 17, 24, 48, 57, 59, 32,  5,  2, 28, 58,  8, 54, 20, 15,
       45, 21, 39,  3, 63, 29, 36, 56, 64, 62,  7,  6, 46, 44, 22,  1, 26,
       13, 23, 43,  4, 49, 55, 10, 18, 27, 61, 52, 30, 60, 41, 19]), 'cur_cost': 108523.0}, {'tour': array([ 0,  3, 50, 23, 54, 29, 36, 51,  1, 44, 41, 43, 24,  7, 63, 30, 12,
       65, 52, 32, 11, 46, 16, 59, 27, 19, 47, 17, 38, 62, 15, 49, 40, 13,
       45, 21, 22, 53, 25, 33, 60,  6, 34, 56, 14,  8, 26, 58,  4, 20, 35,
       31, 64, 18, 28, 48,  9, 42,  2,  5, 39, 10, 37, 61, 55, 57]), 'cur_cost': 109363.0}, {'tour': array([ 8, 37,  5, 53, 11,  0, 22, 64, 46, 21, 17, 14, 58, 27, 30, 28, 43,
       15, 23, 36, 32, 51, 25, 62, 42, 56,  9, 33,  6, 50, 59, 49, 38, 57,
        7, 31, 16, 55, 44,  1,  4, 29, 13, 10, 19, 39, 26, 20, 48, 35, 52,
       40, 41, 54,  2,  3, 65, 61, 18, 34, 24, 63, 45, 60, 47, 12]), 'cur_cost': 106066.0}, {'tour': array([ 7, 60,  8, 49, 24, 32, 35,  6,  4, 25, 26, 52, 28, 15,  0, 18, 65,
       63, 14, 16, 30, 29, 17, 33,  5, 44, 21, 64, 40, 54, 23, 11, 61, 58,
       27, 53, 41, 12, 43, 34, 48, 20, 22, 62, 50, 38, 19, 59, 13, 46,  2,
       55, 42, 37, 31, 45, 36, 51, 57, 47, 39,  1,  9, 56, 10,  3]), 'cur_cost': 99633.0}]
2025-07-04 22:01:46,297 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 22:01:46,297 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-07-04 22:01:46,298 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 22:01:46,298 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 22:01:46,298 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 22:01:46,298 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 22:01:46,299 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 100569.0
2025-07-04 22:01:46,801 - ExploitationExpert - INFO - res_population_num: 29
2025-07-04 22:01:46,801 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527, 9521, 9521, 9521]
2025-07-04 22:01:46,801 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 22:01:46,812 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 22:01:46,812 - ExploitationExpert - INFO - populations: [{'tour': [10, 12, 13, 11, 18, 19, 17, 24, 25, 26, 27, 20, 21, 22, 23, 30, 31, 32, 33, 28, 29, 34, 35, 36, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 42, 41, 40, 39, 38, 37, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 14, 15, 16, 0], 'cur_cost': 26354.0}, {'tour': [0, 1, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 3], 'cur_cost': 17700.0}, {'tour': [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 11479.0}, {'tour': [10, 12, 11, 13, 14, 15, 16, 17, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 18, 19, 20, 21, 22, 23, 24], 'cur_cost': 18152.0}, {'tour': [4, 12, 15, 22, 23, 21, 20, 19, 18, 17, 16, 11, 10, 9, 8, 7, 6, 5, 13, 14, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 24, 25, 26, 27], 'cur_cost': 17628.0}, {'tour': array([33, 36, 49, 24, 61, 31, 11,  3, 48, 40, 63, 41, 17, 53, 35, 23, 52,
       50, 60, 16,  6, 15,  4, 57, 47, 22,  8, 29, 44, 42, 55, 38, 13, 32,
       27,  1,  5, 65,  7, 54, 64, 14, 37, 46,  0, 12, 26, 51, 45, 58, 10,
       30,  9, 20, 34, 43, 28, 62, 56, 39, 19,  2, 59, 18, 21, 25]), 'cur_cost': 105027.0}, {'tour': array([50,  0, 38, 12, 53, 47, 34, 31, 16, 40, 35, 11, 42, 65, 25,  9, 33,
       51, 37, 14, 17, 24, 48, 57, 59, 32,  5,  2, 28, 58,  8, 54, 20, 15,
       45, 21, 39,  3, 63, 29, 36, 56, 64, 62,  7,  6, 46, 44, 22,  1, 26,
       13, 23, 43,  4, 49, 55, 10, 18, 27, 61, 52, 30, 60, 41, 19]), 'cur_cost': 108523.0}, {'tour': array([59, 27, 49, 17, 20, 60, 43,  8, 51, 62, 41,  5, 47, 35,  1, 36,  0,
       29, 14,  2, 32, 38, 45, 52, 21, 26, 56, 28, 25, 58, 15, 12, 50, 31,
        7, 40, 44, 34, 37,  9, 53, 65, 57, 61, 55, 64, 63, 30, 10,  4,  6,
       46, 42, 13, 54, 48, 22, 24,  3, 39, 33, 18, 19, 16, 23, 11]), 'cur_cost': 100569.0}, {'tour': array([ 8, 37,  5, 53, 11,  0, 22, 64, 46, 21, 17, 14, 58, 27, 30, 28, 43,
       15, 23, 36, 32, 51, 25, 62, 42, 56,  9, 33,  6, 50, 59, 49, 38, 57,
        7, 31, 16, 55, 44,  1,  4, 29, 13, 10, 19, 39, 26, 20, 48, 35, 52,
       40, 41, 54,  2,  3, 65, 61, 18, 34, 24, 63, 45, 60, 47, 12]), 'cur_cost': 106066.0}, {'tour': array([ 7, 60,  8, 49, 24, 32, 35,  6,  4, 25, 26, 52, 28, 15,  0, 18, 65,
       63, 14, 16, 30, 29, 17, 33,  5, 44, 21, 64, 40, 54, 23, 11, 61, 58,
       27, 53, 41, 12, 43, 34, 48, 20, 22, 62, 50, 38, 19, 59, 13, 46,  2,
       55, 42, 37, 31, 45, 36, 51, 57, 47, 39,  1,  9, 56, 10,  3]), 'cur_cost': 99633.0}]
2025-07-04 22:01:46,815 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 22:01:46,815 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-07-04 22:01:46,815 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 22:01:46,816 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-04 22:01:46,816 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:01:46,817 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106066.0 (rank 80.0%)
- Population diversity: 0.6661538461538462
- Evolution phase: early exploration
- Landscape recommends focus: explore and exploit
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Region potentia
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:01:46,817 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:01:46,817 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:01:55,565 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 13, 14, 15, 11, 10, 9, 8, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 7, 6, 5, 4, 3, 2, 1, 0, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "High-density ce, explore sparse cells around nodes 11 and 25.",
  "strategy_comment": "Prioritized opportunity regions and sparse cells near them, aiming for diversity with a structured approach."
}
```
2025-07-04 22:01:55,565 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:01:55,565 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17471.0, 路径: [12, 13, 14, 15, 11, 10, 9, 8, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 7, 6, 5, 4, 3, 2, 1, 0, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-04 22:01:55,565 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [12, 13, 14, 15, 11, 10, 9, 8, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 7, 6, 5, 4, 3, 2, 1, 0, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 17471.0}
2025-07-04 22:01:55,567 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-04 22:01:55,568 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 22:01:55,568 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 22:01:55,568 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 112752.0
2025-07-04 22:01:56,070 - ExploitationExpert - INFO - res_population_num: 32
2025-07-04 22:01:56,070 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-04 22:01:56,070 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-04 22:01:56,083 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 22:01:56,084 - ExploitationExpert - INFO - populations: [{'tour': [10, 12, 13, 11, 18, 19, 17, 24, 25, 26, 27, 20, 21, 22, 23, 30, 31, 32, 33, 28, 29, 34, 35, 36, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 42, 41, 40, 39, 38, 37, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 14, 15, 16, 0], 'cur_cost': 26354.0}, {'tour': [0, 1, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 3], 'cur_cost': 17700.0}, {'tour': [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 11479.0}, {'tour': [10, 12, 11, 13, 14, 15, 16, 17, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 18, 19, 20, 21, 22, 23, 24], 'cur_cost': 18152.0}, {'tour': [4, 12, 15, 22, 23, 21, 20, 19, 18, 17, 16, 11, 10, 9, 8, 7, 6, 5, 13, 14, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 24, 25, 26, 27], 'cur_cost': 17628.0}, {'tour': array([33, 36, 49, 24, 61, 31, 11,  3, 48, 40, 63, 41, 17, 53, 35, 23, 52,
       50, 60, 16,  6, 15,  4, 57, 47, 22,  8, 29, 44, 42, 55, 38, 13, 32,
       27,  1,  5, 65,  7, 54, 64, 14, 37, 46,  0, 12, 26, 51, 45, 58, 10,
       30,  9, 20, 34, 43, 28, 62, 56, 39, 19,  2, 59, 18, 21, 25]), 'cur_cost': 105027.0}, {'tour': array([50,  0, 38, 12, 53, 47, 34, 31, 16, 40, 35, 11, 42, 65, 25,  9, 33,
       51, 37, 14, 17, 24, 48, 57, 59, 32,  5,  2, 28, 58,  8, 54, 20, 15,
       45, 21, 39,  3, 63, 29, 36, 56, 64, 62,  7,  6, 46, 44, 22,  1, 26,
       13, 23, 43,  4, 49, 55, 10, 18, 27, 61, 52, 30, 60, 41, 19]), 'cur_cost': 108523.0}, {'tour': array([59, 27, 49, 17, 20, 60, 43,  8, 51, 62, 41,  5, 47, 35,  1, 36,  0,
       29, 14,  2, 32, 38, 45, 52, 21, 26, 56, 28, 25, 58, 15, 12, 50, 31,
        7, 40, 44, 34, 37,  9, 53, 65, 57, 61, 55, 64, 63, 30, 10,  4,  6,
       46, 42, 13, 54, 48, 22, 24,  3, 39, 33, 18, 19, 16, 23, 11]), 'cur_cost': 100569.0}, {'tour': [12, 13, 14, 15, 11, 10, 9, 8, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 7, 6, 5, 4, 3, 2, 1, 0, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 17471.0}, {'tour': array([30, 13, 26, 42, 23, 58, 51, 21, 38, 24, 63,  1, 34,  4,  0, 10, 52,
       15, 29, 18, 45, 46, 19, 57, 39, 27, 40, 14, 28, 61, 41, 56, 44, 54,
        3,  8, 37, 17, 48, 55, 31, 62, 60, 20, 64,  2, 59, 50, 25, 43,  6,
       33, 11, 12,  9, 36,  7, 65,  5, 47, 32, 22, 16, 53, 49, 35]), 'cur_cost': 112752.0}]
2025-07-04 22:01:56,086 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 22:01:56,086 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-07-04 22:01:56,086 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-04 22:01:56,088 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [10, 12, 13, 11, 18, 19, 17, 24, 25, 26, 27, 20, 21, 22, 23, 30, 31, 32, 33, 28, 29, 34, 35, 36, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 42, 41, 40, 39, 38, 37, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 14, 15, 16, 0], 'cur_cost': 26354.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 3], 'cur_cost': 17700.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [10, 12, 11, 13, 14, 15, 16, 17, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 18, 19, 20, 21, 22, 23, 24], 'cur_cost': 18152.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [4, 12, 15, 22, 23, 21, 20, 19, 18, 17, 16, 11, 10, 9, 8, 7, 6, 5, 13, 14, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 24, 25, 26, 27], 'cur_cost': 17628.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [12, 13, 14, 15, 11, 10, 9, 8, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 7, 6, 5, 4, 3, 2, 1, 0, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 17471.0}}]
2025-07-04 22:01:56,088 - __main__ - INFO - 进化阶段完成
2025-07-04 22:01:56,088 - StatsExpert - INFO - 开始统计分析
2025-07-04 22:01:56,105 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 112752.0, 'mean': 53565.5, 'std': 43620.51089395905}, 'diversity': 0.7983164983164983, 'clusters': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 22:01:56,106 - __main__ - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-07-04 22:01:56,106 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment ---
2025-07-04 22:01:56,108 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 22:01:56,109 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 32 → 32
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.087 → 0.087 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 22:01:56,109 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:02:02,590 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio", "increase_diversity_weight", "consider_mutation_rate_increase"]
}
```
2025-07-04 22:02:02,590 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 22:02:02,590 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-07-04 22:02:02,590 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-04 22:02:02,590 - __main__ - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-07-04 22:02:02,590 - StatsExpert - INFO - 开始统计分析
2025-07-04 22:02:02,610 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 112752.0, 'mean': 53565.5, 'std': 43620.51089395905}, 'diversity': 0.7983164983164983, 'clusters': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 22:02:02,611 - PathExpert - INFO - 开始路径结构分析
2025-07-04 22:02:02,613 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-04 22:02:02,613 - PathExpert - INFO - 路径结构分析完成
2025-07-04 22:02:02,614 - EliteExpert - INFO - 开始精英解分析
2025-07-04 22:02:02,632 - EliteExpert - INFO - 精英解分析完成
2025-07-04 22:02:02,635 - LandscapeExpert - INFO - 开始景观分析
2025-07-04 22:02:02,637 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-04 22:02:02,637 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 11479.0, mean 53565.5, max 112752.0, std 43620.51089395905
- diversity: 0.7983164983164983
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {'common_edges': {}, 'common_edge_ratio': 0.0}
- elite_diversity: {'diversity_score': 0.26753421309872916}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-04 22:02:02,638 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:02:18,227 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": 0.6,
    "modality": "multi",
    "deceptiveness": "potentially high"
  },
  "population_state": {
    "diversity": 0.798,
    "convergence": 0.0,
    "clustering": "none"
  },
  "difficult_regions": [
    "Long edge corridors identified by edge_len_stats and long_edge_ratio. The 24.9% long edge ratio suggests potential bottlenecks and areas where small perturbations can lead to large cost changes."
  ],
  "opportunity_regions": [
    "High-density cells in the density_grid (e.g., those with the highest counts: [14, 12, 14]) represent areas where a concentration of nodes suggests that exploiting local structure could yield improvements. Investigate the edges within and connecting these areas."
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore & exploit local structure",
    "operators": [
      "Local Search operators (e.g., 2-opt, 3-opt, Lin-Kernighan) can be applied to promising solutions to refine them.",
      "Crossover operators to encourage diversity as the population is diverse, but is not converging (e.g., PMX, order crossover). These operators help the search jump from the initial population to new interesting areas in the search space.",
      "Edge-based crossover to help improve solution construction and local search"
    ]
  }
}
```
2025-07-04 22:02:18,228 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-04 22:02:18,229 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 0.6, 'modality': 'multi', 'deceptiveness': 'potentially high'}, 'population_state': {'diversity': 0.798, 'convergence': 0.0, 'clustering': 'none'}, 'difficult_regions': ['Long edge corridors identified by edge_len_stats and long_edge_ratio. The 24.9% long edge ratio suggests potential bottlenecks and areas where small perturbations can lead to large cost changes.'], 'opportunity_regions': ['High-density cells in the density_grid (e.g., those with the highest counts: [14, 12, 14]) represent areas where a concentration of nodes suggests that exploiting local structure could yield improvements. Investigate the edges within and connecting these areas.'], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore & exploit local structure', 'operators': ['Local Search operators (e.g., 2-opt, 3-opt, Lin-Kernighan) can be applied to promising solutions to refine them.', 'Crossover operators to encourage diversity as the population is diverse, but is not converging (e.g., PMX, order crossover). These operators help the search jump from the initial population to new interesting areas in the search space.', 'Edge-based crossover to help improve solution construction and local search']}}
2025-07-04 22:02:18,229 - __main__ - INFO - 开始策略分配阶段
2025-07-04 22:02:18,229 - StrategyExpert - INFO - 开始策略分配分析
2025-07-04 22:02:18,229 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.798
  • best_cost: 11479.0
  • mean_cost: 53565.5
- Search-space: ruggedness 0.6, modality multi, deceptiveness potentially high
- Evolution phase: exploration
- Landscape focus suggestion: explore & exploit local structure
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 1): Long edge corridors 
- Opportunity regions (# 1): High-density cells i
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "st...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-04 22:02:18,230 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-04 22:02:18,230 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:02:22,873 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early evolution, high diversity, and exploration focus necessitate a strong exploration bias. Individuals are split to maintain balance."
}
```
2025-07-04 22:02:22,873 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-07-04 22:02:22,874 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-07-04 22:02:22,874 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early evolution, high diversity, and exploration focus necessitate a strong exploration bias. Individuals are split to maintain balance."
}
```
2025-07-04 22:02:22,874 - __main__ - INFO - 策略分配阶段完成
2025-07-04 22:02:22,874 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'explore']
2025-07-04 22:02:22,875 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early evolution, high diversity, and exploration focus necessitate a strong exploration bias. Individuals are split to maintain balance."
}
```
2025-07-04 22:02:22,875 - __main__ - INFO - 开始进化阶段
2025-07-04 22:02:22,876 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-04 22:02:22,876 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:02:22,876 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26354.0 (rank 50.0%)
- Population diversity: 0.5923076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore & exploit local structure
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Long edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:02:22,877 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:02:22,877 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:02:28,781 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "High-density ce",
  "strategy_comment": "Prioritized opportunity regions and connected to a dense, less costly cell cluster."
}
```
2025-07-04 22:02:28,782 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:02:28,783 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11438.0, 路径: [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 3]
2025-07-04 22:02:28,783 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 3], 'cur_cost': 11438.0}
2025-07-04 22:02:28,784 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-04 22:02:28,784 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:02:28,784 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17700.0 (rank 40.0%)
- Population diversity: 0.5769230769230769
- Evolution phase: exploration
- Landscape recommends focus: explore & exploit local structure
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Long edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:02:28,785 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:02:28,785 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:02:37,709 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 6, 10, 15, 22, 28, 34, 40, 45, 51, 57, 60, 63, 65, 64, 61, 58, 52, 46, 41, 35, 29, 23, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 20, 21, 24, 25, 26, 27, 30, 31, 32, 33, 36, 37, 38, 39, 42, 43, 44, 47, 48, 49, 50, 53, 54, 55, 56, 59, 62],
  "targeted_regions": "High-density ce and sparse cells near opportunity regions.",
  "strategy_comment": "Prioritized high-density cells and explored low-density cells adjacent to opportunity regions."
}
```
2025-07-04 22:02:37,709 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:02:37,710 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25904.0, 路径: [0, 1, 6, 10, 15, 22, 28, 34, 40, 45, 51, 57, 60, 63, 65, 64, 61, 58, 52, 46, 41, 35, 29, 23, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 20, 21, 24, 25, 26, 27, 30, 31, 32, 33, 36, 37, 38, 39, 42, 43, 44, 47, 48, 49, 50, 53, 54, 55, 56, 59, 62]
2025-07-04 22:02:37,710 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 6, 10, 15, 22, 28, 34, 40, 45, 51, 57, 60, 63, 65, 64, 61, 58, 52, 46, 41, 35, 29, 23, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 20, 21, 24, 25, 26, 27, 30, 31, 32, 33, 36, 37, 38, 39, 42, 43, 44, 47, 48, 49, 50, 53, 54, 55, 56, 59, 62], 'cur_cost': 25904.0}
2025-07-04 22:02:37,711 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-04 22:02:37,711 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:02:37,711 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 10.0%)
- Population diversity: 0.5461538461538461
- Evolution phase: exploration
- Landscape recommends focus: explore & exploit local structure
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Long edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:02:37,712 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:02:37,712 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:02:43,607 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63],
  "targeted_regions": "High-density ce",
  "strategy_comment": "Prioritizes opportunity regions and low-density cells. Minimizes use of long edge corridors."
}
```
2025-07-04 22:02:43,608 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:02:43,609 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25911.0, 路径: [0, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63]
2025-07-04 22:02:43,609 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25911.0}
2025-07-04 22:02:43,609 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-04 22:02:43,610 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:02:43,610 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 18152.0 (rank 30.0%)
- Population diversity: 0.6061538461538462
- Evolution phase: exploration
- Landscape recommends focus: explore & exploit local structure
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Long edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:02:43,611 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:02:43,611 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:02:51,074 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 6, 12, 11, 10, 9, 8, 7, 13, 20, 27, 34, 41, 48, 55, 62, 63, 64, 65, 60, 54, 47, 40, 33, 26, 19, 18, 17, 16, 23, 30, 37, 44, 51, 58, 61, 59, 53, 46, 39, 32, 25, 14, 21, 28, 35, 42, 49, 56, 57, 50, 43, 36, 29, 22, 15, 2, 3, 4, 5, 6, 6, 6, 6, 6, 6, 6],
  "targeted_regions": "High-density cells (55-65), avoid long edge corridors.",
  "strategy_comment": "Prioritizes exploration around identified opportunity regions, while attempting to maintain diversity."
}
```
2025-07-04 22:02:51,074 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:02:51,076 - ExplorationExpert - INFO - 探索路径生成完成，成本: 56096.0, 路径: [0, 1, 6, 12, 11, 10, 9, 8, 7, 13, 20, 27, 34, 41, 48, 55, 62, 63, 64, 65, 60, 54, 47, 40, 33, 26, 19, 18, 17, 16, 23, 30, 37, 44, 51, 58, 61, 59, 53, 46, 39, 32, 25, 14, 21, 28, 35, 42, 49, 56, 57, 50, 43, 36, 29, 22, 15, 2, 3, 4, 5, 38, 52, 45, 24, 31]
2025-07-04 22:02:51,076 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 6, 12, 11, 10, 9, 8, 7, 13, 20, 27, 34, 41, 48, 55, 62, 63, 64, 65, 60, 54, 47, 40, 33, 26, 19, 18, 17, 16, 23, 30, 37, 44, 51, 58, 61, 59, 53, 46, 39, 32, 25, 14, 21, 28, 35, 42, 49, 56, 57, 50, 43, 36, 29, 22, 15, 2, 3, 4, 5, 38, 52, 45, 24, 31], 'cur_cost': 56096.0}
2025-07-04 22:02:51,076 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-04 22:02:51,076 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:02:51,076 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17628.0 (rank 20.0%)
- Population diversity: 0.6630769230769231
- Evolution phase: exploration
- Landscape recommends focus: explore & exploit local structure
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Long edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:02:51,078 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:02:51,078 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:03:05,553 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 7, 8, 9],
  "targeted_regions": "High-density cells (10-21).",
  "strategy_comment": "Exploits local structure within high-density regions and then diversifies, avoiding long edges."
}
```
2025-07-04 22:03:05,554 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:03:05,554 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11456.0, 路径: [5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 7, 8, 9]
2025-07-04 22:03:05,555 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 7, 8, 9], 'cur_cost': 11456.0}
2025-07-04 22:03:05,555 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-04 22:03:05,555 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 22:03:05,555 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 22:03:05,556 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 97968.0
2025-07-04 22:03:06,058 - ExploitationExpert - INFO - res_population_num: 32
2025-07-04 22:03:06,060 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527]
2025-07-04 22:03:06,060 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64)]
2025-07-04 22:03:06,073 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 22:03:06,074 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 3], 'cur_cost': 11438.0}, {'tour': [0, 1, 6, 10, 15, 22, 28, 34, 40, 45, 51, 57, 60, 63, 65, 64, 61, 58, 52, 46, 41, 35, 29, 23, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 20, 21, 24, 25, 26, 27, 30, 31, 32, 33, 36, 37, 38, 39, 42, 43, 44, 47, 48, 49, 50, 53, 54, 55, 56, 59, 62], 'cur_cost': 25904.0}, {'tour': [0, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25911.0}, {'tour': [0, 1, 6, 12, 11, 10, 9, 8, 7, 13, 20, 27, 34, 41, 48, 55, 62, 63, 64, 65, 60, 54, 47, 40, 33, 26, 19, 18, 17, 16, 23, 30, 37, 44, 51, 58, 61, 59, 53, 46, 39, 32, 25, 14, 21, 28, 35, 42, 49, 56, 57, 50, 43, 36, 29, 22, 15, 2, 3, 4, 5, 38, 52, 45, 24, 31], 'cur_cost': 56096.0}, {'tour': [5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 7, 8, 9], 'cur_cost': 11456.0}, {'tour': array([57, 58, 55, 46,  7,  4, 35, 33,  6,  3, 43,  1,  0, 13, 42, 11, 26,
       19, 28, 48, 44, 56, 47, 64, 36, 14, 38, 49, 63, 51,  8, 27, 25, 60,
       16, 18, 45, 24, 31, 32, 29, 10,  5, 52, 50, 40, 53, 39, 15, 21, 62,
       37, 17, 41, 20, 12, 30, 59,  2, 34, 22, 61, 65, 23, 54,  9]), 'cur_cost': 97968.0}, {'tour': array([50,  0, 38, 12, 53, 47, 34, 31, 16, 40, 35, 11, 42, 65, 25,  9, 33,
       51, 37, 14, 17, 24, 48, 57, 59, 32,  5,  2, 28, 58,  8, 54, 20, 15,
       45, 21, 39,  3, 63, 29, 36, 56, 64, 62,  7,  6, 46, 44, 22,  1, 26,
       13, 23, 43,  4, 49, 55, 10, 18, 27, 61, 52, 30, 60, 41, 19]), 'cur_cost': 108523.0}, {'tour': array([59, 27, 49, 17, 20, 60, 43,  8, 51, 62, 41,  5, 47, 35,  1, 36,  0,
       29, 14,  2, 32, 38, 45, 52, 21, 26, 56, 28, 25, 58, 15, 12, 50, 31,
        7, 40, 44, 34, 37,  9, 53, 65, 57, 61, 55, 64, 63, 30, 10,  4,  6,
       46, 42, 13, 54, 48, 22, 24,  3, 39, 33, 18, 19, 16, 23, 11]), 'cur_cost': 100569.0}, {'tour': [12, 13, 14, 15, 11, 10, 9, 8, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 7, 6, 5, 4, 3, 2, 1, 0, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 17471.0}, {'tour': array([30, 13, 26, 42, 23, 58, 51, 21, 38, 24, 63,  1, 34,  4,  0, 10, 52,
       15, 29, 18, 45, 46, 19, 57, 39, 27, 40, 14, 28, 61, 41, 56, 44, 54,
        3,  8, 37, 17, 48, 55, 31, 62, 60, 20, 64,  2, 59, 50, 25, 43,  6,
       33, 11, 12,  9, 36,  7, 65,  5, 47, 32, 22, 16, 53, 49, 35]), 'cur_cost': 112752.0}]
2025-07-04 22:03:06,077 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 22:03:06,077 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-07-04 22:03:06,077 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-04 22:03:06,077 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-04 22:03:06,078 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 22:03:06,078 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 22:03:06,078 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 109504.0
2025-07-04 22:03:06,582 - ExploitationExpert - INFO - res_population_num: 32
2025-07-04 22:03:06,583 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527]
2025-07-04 22:03:06,583 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64)]
2025-07-04 22:03:06,597 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 22:03:06,598 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 3], 'cur_cost': 11438.0}, {'tour': [0, 1, 6, 10, 15, 22, 28, 34, 40, 45, 51, 57, 60, 63, 65, 64, 61, 58, 52, 46, 41, 35, 29, 23, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 20, 21, 24, 25, 26, 27, 30, 31, 32, 33, 36, 37, 38, 39, 42, 43, 44, 47, 48, 49, 50, 53, 54, 55, 56, 59, 62], 'cur_cost': 25904.0}, {'tour': [0, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25911.0}, {'tour': [0, 1, 6, 12, 11, 10, 9, 8, 7, 13, 20, 27, 34, 41, 48, 55, 62, 63, 64, 65, 60, 54, 47, 40, 33, 26, 19, 18, 17, 16, 23, 30, 37, 44, 51, 58, 61, 59, 53, 46, 39, 32, 25, 14, 21, 28, 35, 42, 49, 56, 57, 50, 43, 36, 29, 22, 15, 2, 3, 4, 5, 38, 52, 45, 24, 31], 'cur_cost': 56096.0}, {'tour': [5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 7, 8, 9], 'cur_cost': 11456.0}, {'tour': array([57, 58, 55, 46,  7,  4, 35, 33,  6,  3, 43,  1,  0, 13, 42, 11, 26,
       19, 28, 48, 44, 56, 47, 64, 36, 14, 38, 49, 63, 51,  8, 27, 25, 60,
       16, 18, 45, 24, 31, 32, 29, 10,  5, 52, 50, 40, 53, 39, 15, 21, 62,
       37, 17, 41, 20, 12, 30, 59,  2, 34, 22, 61, 65, 23, 54,  9]), 'cur_cost': 97968.0}, {'tour': array([37, 27,  8,  1, 42, 43, 13, 33,  7, 45, 50, 25, 53,  0, 36, 24, 23,
       21, 65,  9, 17, 63, 18, 40, 12, 39, 26,  2, 34, 10, 16,  6, 61, 46,
       41, 22, 56, 55, 28, 15, 54,  4, 32, 44, 14, 60, 47, 30, 52, 19,  5,
       57, 35, 58, 48, 20, 62, 11, 29, 51, 64, 31,  3, 59, 38, 49]), 'cur_cost': 109504.0}, {'tour': array([59, 27, 49, 17, 20, 60, 43,  8, 51, 62, 41,  5, 47, 35,  1, 36,  0,
       29, 14,  2, 32, 38, 45, 52, 21, 26, 56, 28, 25, 58, 15, 12, 50, 31,
        7, 40, 44, 34, 37,  9, 53, 65, 57, 61, 55, 64, 63, 30, 10,  4,  6,
       46, 42, 13, 54, 48, 22, 24,  3, 39, 33, 18, 19, 16, 23, 11]), 'cur_cost': 100569.0}, {'tour': [12, 13, 14, 15, 11, 10, 9, 8, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 7, 6, 5, 4, 3, 2, 1, 0, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 17471.0}, {'tour': array([30, 13, 26, 42, 23, 58, 51, 21, 38, 24, 63,  1, 34,  4,  0, 10, 52,
       15, 29, 18, 45, 46, 19, 57, 39, 27, 40, 14, 28, 61, 41, 56, 44, 54,
        3,  8, 37, 17, 48, 55, 31, 62, 60, 20, 64,  2, 59, 50, 25, 43,  6,
       33, 11, 12,  9, 36,  7, 65,  5, 47, 32, 22, 16, 53, 49, 35]), 'cur_cost': 112752.0}]
2025-07-04 22:03:06,600 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 22:03:06,600 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-07-04 22:03:06,600 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-04 22:03:06,601 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-04 22:03:06,601 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 22:03:06,601 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 22:03:06,602 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 102457.0
2025-07-04 22:03:07,106 - ExploitationExpert - INFO - res_population_num: 32
2025-07-04 22:03:07,111 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527]
2025-07-04 22:03:07,112 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64)]
2025-07-04 22:03:07,124 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 22:03:07,126 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 3], 'cur_cost': 11438.0}, {'tour': [0, 1, 6, 10, 15, 22, 28, 34, 40, 45, 51, 57, 60, 63, 65, 64, 61, 58, 52, 46, 41, 35, 29, 23, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 20, 21, 24, 25, 26, 27, 30, 31, 32, 33, 36, 37, 38, 39, 42, 43, 44, 47, 48, 49, 50, 53, 54, 55, 56, 59, 62], 'cur_cost': 25904.0}, {'tour': [0, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25911.0}, {'tour': [0, 1, 6, 12, 11, 10, 9, 8, 7, 13, 20, 27, 34, 41, 48, 55, 62, 63, 64, 65, 60, 54, 47, 40, 33, 26, 19, 18, 17, 16, 23, 30, 37, 44, 51, 58, 61, 59, 53, 46, 39, 32, 25, 14, 21, 28, 35, 42, 49, 56, 57, 50, 43, 36, 29, 22, 15, 2, 3, 4, 5, 38, 52, 45, 24, 31], 'cur_cost': 56096.0}, {'tour': [5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 7, 8, 9], 'cur_cost': 11456.0}, {'tour': array([57, 58, 55, 46,  7,  4, 35, 33,  6,  3, 43,  1,  0, 13, 42, 11, 26,
       19, 28, 48, 44, 56, 47, 64, 36, 14, 38, 49, 63, 51,  8, 27, 25, 60,
       16, 18, 45, 24, 31, 32, 29, 10,  5, 52, 50, 40, 53, 39, 15, 21, 62,
       37, 17, 41, 20, 12, 30, 59,  2, 34, 22, 61, 65, 23, 54,  9]), 'cur_cost': 97968.0}, {'tour': array([37, 27,  8,  1, 42, 43, 13, 33,  7, 45, 50, 25, 53,  0, 36, 24, 23,
       21, 65,  9, 17, 63, 18, 40, 12, 39, 26,  2, 34, 10, 16,  6, 61, 46,
       41, 22, 56, 55, 28, 15, 54,  4, 32, 44, 14, 60, 47, 30, 52, 19,  5,
       57, 35, 58, 48, 20, 62, 11, 29, 51, 64, 31,  3, 59, 38, 49]), 'cur_cost': 109504.0}, {'tour': array([ 5, 56, 31, 14, 22, 65, 25, 60,  9, 36,  7, 52, 28, 19, 33, 57, 34,
       64, 30, 29, 24, 43, 45, 35, 46, 39, 23, 58, 53, 62,  4, 61,  2, 32,
       63, 49, 12, 20,  8, 17, 26, 59,  1, 48, 37,  0, 16, 44, 27, 42, 13,
       10, 47, 11,  3, 21, 40, 38, 50, 54, 18, 51, 41, 55, 15,  6]), 'cur_cost': 102457.0}, {'tour': [12, 13, 14, 15, 11, 10, 9, 8, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 7, 6, 5, 4, 3, 2, 1, 0, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 17471.0}, {'tour': array([30, 13, 26, 42, 23, 58, 51, 21, 38, 24, 63,  1, 34,  4,  0, 10, 52,
       15, 29, 18, 45, 46, 19, 57, 39, 27, 40, 14, 28, 61, 41, 56, 44, 54,
        3,  8, 37, 17, 48, 55, 31, 62, 60, 20, 64,  2, 59, 50, 25, 43,  6,
       33, 11, 12,  9, 36,  7, 65,  5, 47, 32, 22, 16, 53, 49, 35]), 'cur_cost': 112752.0}]
2025-07-04 22:03:07,128 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-07-04 22:03:07,128 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-07-04 22:03:07,128 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-04 22:03:07,129 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-04 22:03:07,129 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-04 22:03:07,129 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-04 22:03:07,130 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 106329.0
2025-07-04 22:03:07,632 - ExploitationExpert - INFO - res_population_num: 32
2025-07-04 22:03:07,634 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9530, 9542, 9542, 9573, 9579, 101527]
2025-07-04 22:03:07,634 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 13, 21, 20, 14, 15, 22,
       23, 16, 18, 12, 17,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 63, 52, 65, 61, 55, 56, 59, 62, 53, 64, 57, 54, 60, 58, 14,
       15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47,
       39, 44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31,
       25, 26, 36, 37, 27,  3,  9,  5,  4,  6,  2,  8, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 65, 63, 52, 54, 57, 64,
       53, 59, 62, 60, 58, 56, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 36, 26,
       35, 34, 30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 29, 21, 32, 33, 22, 34, 63, 13, 12, 42, 50, 31, 57, 60,  9,  8,
       64,  3, 30, 35, 15, 10, 65, 39, 48, 37, 11, 51, 19, 46, 28, 56, 47,
       43,  5, 53, 40, 23,  1, 20, 27,  7, 16, 59, 24,  2, 18, 52, 44, 14,
       55, 58, 25, 49, 41, 38, 61,  4, 54, 17, 62, 45,  6, 36, 26],
      dtype=int64)]
2025-07-04 22:03:07,650 - ExploitationExpert - INFO - populations_num: 10
2025-07-04 22:03:07,651 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 3], 'cur_cost': 11438.0}, {'tour': [0, 1, 6, 10, 15, 22, 28, 34, 40, 45, 51, 57, 60, 63, 65, 64, 61, 58, 52, 46, 41, 35, 29, 23, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 20, 21, 24, 25, 26, 27, 30, 31, 32, 33, 36, 37, 38, 39, 42, 43, 44, 47, 48, 49, 50, 53, 54, 55, 56, 59, 62], 'cur_cost': 25904.0}, {'tour': [0, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25911.0}, {'tour': [0, 1, 6, 12, 11, 10, 9, 8, 7, 13, 20, 27, 34, 41, 48, 55, 62, 63, 64, 65, 60, 54, 47, 40, 33, 26, 19, 18, 17, 16, 23, 30, 37, 44, 51, 58, 61, 59, 53, 46, 39, 32, 25, 14, 21, 28, 35, 42, 49, 56, 57, 50, 43, 36, 29, 22, 15, 2, 3, 4, 5, 38, 52, 45, 24, 31], 'cur_cost': 56096.0}, {'tour': [5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 7, 8, 9], 'cur_cost': 11456.0}, {'tour': array([57, 58, 55, 46,  7,  4, 35, 33,  6,  3, 43,  1,  0, 13, 42, 11, 26,
       19, 28, 48, 44, 56, 47, 64, 36, 14, 38, 49, 63, 51,  8, 27, 25, 60,
       16, 18, 45, 24, 31, 32, 29, 10,  5, 52, 50, 40, 53, 39, 15, 21, 62,
       37, 17, 41, 20, 12, 30, 59,  2, 34, 22, 61, 65, 23, 54,  9]), 'cur_cost': 97968.0}, {'tour': array([37, 27,  8,  1, 42, 43, 13, 33,  7, 45, 50, 25, 53,  0, 36, 24, 23,
       21, 65,  9, 17, 63, 18, 40, 12, 39, 26,  2, 34, 10, 16,  6, 61, 46,
       41, 22, 56, 55, 28, 15, 54,  4, 32, 44, 14, 60, 47, 30, 52, 19,  5,
       57, 35, 58, 48, 20, 62, 11, 29, 51, 64, 31,  3, 59, 38, 49]), 'cur_cost': 109504.0}, {'tour': array([ 5, 56, 31, 14, 22, 65, 25, 60,  9, 36,  7, 52, 28, 19, 33, 57, 34,
       64, 30, 29, 24, 43, 45, 35, 46, 39, 23, 58, 53, 62,  4, 61,  2, 32,
       63, 49, 12, 20,  8, 17, 26, 59,  1, 48, 37,  0, 16, 44, 27, 42, 13,
       10, 47, 11,  3, 21, 40, 38, 50, 54, 18, 51, 41, 55, 15,  6]), 'cur_cost': 102457.0}, {'tour': array([64, 65, 23, 38, 28, 31, 42, 20, 57, 56, 11, 37, 43, 63,  4, 61, 24,
       47, 33, 12, 54, 19,  3, 59, 32, 18, 62, 30, 16, 34, 51, 44, 45, 13,
       35, 58, 36,  9, 49,  0,  1, 22, 46, 55, 14, 17, 40,  2,  7, 52, 48,
       60, 41, 26,  6,  8, 15, 27, 25, 21,  5, 53, 39, 50, 10, 29]), 'cur_cost': 106329.0}, {'tour': array([30, 13, 26, 42, 23, 58, 51, 21, 38, 24, 63,  1, 34,  4,  0, 10, 52,
       15, 29, 18, 45, 46, 19, 57, 39, 27, 40, 14, 28, 61, 41, 56, 44, 54,
        3,  8, 37, 17, 48, 55, 31, 62, 60, 20, 64,  2, 59, 50, 25, 43,  6,
       33, 11, 12,  9, 36,  7, 65,  5, 47, 32, 22, 16, 53, 49, 35]), 'cur_cost': 112752.0}]
2025-07-04 22:03:07,654 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-04 22:03:07,654 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 171, 'cache_hit_rate': 0.0, 'cache_size': 171}}
2025-07-04 22:03:07,654 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-04 22:03:07,655 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-04 22:03:07,655 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-04 22:03:07,656 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 112752.0 (rank 90.0%)
- Population diversity: 0.7138461538461538
- Evolution phase: exploration
- Landscape recommends focus: explore & exploit local structure
- Opportunity regions (sample): High-density ce
- Difficult regions to avoid (sample): Long edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-04 22:03:07,656 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-04 22:03:07,656 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:03:17,418 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 61, 62, 63, 64, 65, 59, 53, 47, 41, 35, 29, 23, 17, 11, 5, 2, 7, 13, 19, 25, 31, 37, 43, 49, 55, 66, 60, 58, 52, 46, 40, 34, 28, 22, 16, 10, 4, 8, 14, 20, 26, 32, 38, 44, 50, 56, 33, 27, 21, 15, 9, 3, 65, 61, 64, 63],
  "targeted_regions": "High-density cells and sparse cells near opportunity.",
  "strategy_comment": "Explores high-density cells and adds edges to sparse regions while trying to avoid edge corridors."
}
```
2025-07-04 22:03:17,418 - ExplorationExpert - INFO - 计算路径成本
2025-07-04 22:03:17,418 - ExplorationExpert - INFO - 探索路径生成完成，成本: 54206.0, 路径: [0, 1, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 61, 62, 63, 64, 65, 59, 53, 47, 41, 35, 29, 23, 17, 11, 5, 2, 7, 13, 19, 25, 31, 37, 43, 49, 55, 16, 58, 52, 46, 40, 34, 28, 22, 10, 4, 8, 14, 20, 26, 32, 38, 44, 50, 56, 33, 27, 21, 15, 9, 3, 51, 45, 39, 57]
2025-07-04 22:03:17,418 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 1, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 61, 62, 63, 64, 65, 59, 53, 47, 41, 35, 29, 23, 17, 11, 5, 2, 7, 13, 19, 25, 31, 37, 43, 49, 55, 16, 58, 52, 46, 40, 34, 28, 22, 10, 4, 8, 14, 20, 26, 32, 38, 44, 50, 56, 33, 27, 21, 15, 9, 3, 51, 45, 39, 57], 'cur_cost': 54206.0}
2025-07-04 22:03:17,421 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 3], 'cur_cost': 11438.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 10, 15, 22, 28, 34, 40, 45, 51, 57, 60, 63, 65, 64, 61, 58, 52, 46, 41, 35, 29, 23, 16, 11, 7, 2, 3, 4, 5, 8, 9, 12, 13, 14, 17, 18, 19, 20, 21, 24, 25, 26, 27, 30, 31, 32, 33, 36, 37, 38, 39, 42, 43, 44, 47, 48, 49, 50, 53, 54, 55, 56, 59, 62], 'cur_cost': 25904.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 20, 17, 14, 11, 8, 5, 2, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63], 'cur_cost': 25911.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 12, 11, 10, 9, 8, 7, 13, 20, 27, 34, 41, 48, 55, 62, 63, 64, 65, 60, 54, 47, 40, 33, 26, 19, 18, 17, 16, 23, 30, 37, 44, 51, 58, 61, 59, 53, 46, 39, 32, 25, 14, 21, 28, 35, 42, 49, 56, 57, 50, 43, 36, 29, 22, 15, 2, 3, 4, 5, 38, 52, 45, 24, 31], 'cur_cost': 56096.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 7, 8, 9], 'cur_cost': 11456.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 61, 62, 63, 64, 65, 59, 53, 47, 41, 35, 29, 23, 17, 11, 5, 2, 7, 13, 19, 25, 31, 37, 43, 49, 55, 16, 58, 52, 46, 40, 34, 28, 22, 10, 4, 8, 14, 20, 26, 32, 38, 44, 50, 56, 33, 27, 21, 15, 9, 3, 51, 45, 39, 57], 'cur_cost': 54206.0}}]
2025-07-04 22:03:17,421 - __main__ - INFO - 进化阶段完成
2025-07-04 22:03:17,422 - StatsExpert - INFO - 开始统计分析
2025-07-04 22:03:17,430 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11438.0, 'max': 109504.0, 'mean': 60126.9, 'std': 38638.35356598415}, 'diversity': 0.8993265993265993, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-04 22:03:17,430 - __main__ - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-07-04 22:03:17,441 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment ---
2025-07-04 22:03:17,444 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-04 22:03:17,444 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 0 → 0 (Δ 0)
- Mean cost: 0 → 0
- Diversity: 0.000 → 0.000 (Δ 0)

#### Elite metrics
- Elite count: 32 → 32
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.087 → 0.087 (Δ 0.0)

#### Strategy performance
- Explore: 0 inds, success 0% , avg Δ 0
- Exploit: 0 inds, success 0% , avg Δ 0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 0
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-04 22:03:17,444 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-04 22:03:30,203 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "stagnant",
  "strategy_effectiveness": {"explore": 0, "exploit": 0},
  "balance_state": "stagnant",
  "recommendations": ["increase_explore_ratio_to_0.3", "increase_mutation_rate_by_0.1", "consider_increasing_diversity_weight"]
}
```
2025-07-04 22:03:30,203 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-04 22:03:30,205 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-07-04 22:03:30,227 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-04 22:03:30,228 - __main__ - INFO - 实例 composite13_66 处理完成
