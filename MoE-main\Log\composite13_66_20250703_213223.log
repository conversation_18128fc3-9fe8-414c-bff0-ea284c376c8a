2025-07-03 21:32:23,995 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-03 21:32:23,995 - __main__ - INFO - 开始分析阶段
2025-07-03 21:32:23,995 - StatsExpert - INFO - 开始统计分析
2025-07-03 21:32:24,019 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9945.0, 'max': 120103.0, 'mean': 81682.7, 'std': 47196.15916586857}, 'diversity': 0.9202020202020201, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 21:32:24,021 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9945.0, 'max': 120103.0, 'mean': 81682.7, 'std': 47196.15916586857}, 'diversity_level': 0.9202020202020201, 'convergence_level': 0.0, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [2, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [2, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 21:32:24,021 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 21:32:24,022 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 21:32:24,022 - PathExpert - INFO - 开始路径结构分析
2025-07-03 21:32:24,029 - PathExpert - INFO - 路径结构分析完成
2025-07-03 21:32:24,029 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (24, 29), 'frequency': 0.5, 'avg_cost': 41.0}], 'common_subpaths': [{'subpath': (39, 44, 45), 'frequency': 0.3}, {'subpath': (44, 45, 38), 'frequency': 0.3}, {'subpath': (45, 38, 51), 'frequency': 0.3}, {'subpath': (38, 51, 50), 'frequency': 0.3}, {'subpath': (51, 50, 41), 'frequency': 0.3}, {'subpath': (50, 41, 42), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (26, 36, 35), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(44, 45)', 'frequency': 0.4}, {'edge': '(50, 51)', 'frequency': 0.4}, {'edge': '(25, 37)', 'frequency': 0.4}, {'edge': '(35, 36)', 'frequency': 0.4}, {'edge': '(30, 34)', 'frequency': 0.4}, {'edge': '(31, 33)', 'frequency': 0.4}, {'edge': '(24, 31)', 'frequency': 0.4}, {'edge': '(24, 29)', 'frequency': 0.5}], 'low_frequency_edges': [{'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(46, 48)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.3}, {'edge': '(41, 42)', 'frequency': 0.3}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(13, 20)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.3}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(3, 32)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 7)', 'frequency': 0.2}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(0, 55)', 'frequency': 0.2}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.2}, {'edge': '(53, 62)', 'frequency': 0.2}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.2}, {'edge': '(60, 64)', 'frequency': 0.2}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(49, 63)', 'frequency': 0.2}, {'edge': '(2, 55)', 'frequency': 0.2}, {'edge': '(5, 8)', 'frequency': 0.2}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(6, 9)', 'frequency': 0.2}, {'edge': '(7, 11)', 'frequency': 0.3}, {'edge': '(1, 3)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.2}, {'edge': '(10, 15)', 'frequency': 0.2}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(17, 18)', 'frequency': 0.2}, {'edge': '(19, 21)', 'frequency': 0.2}, {'edge': '(14, 19)', 'frequency': 0.2}, {'edge': '(14, 27)', 'frequency': 0.2}, {'edge': '(32, 40)', 'frequency': 0.2}, {'edge': '(47, 49)', 'frequency': 0.2}, {'edge': '(39, 43)', 'frequency': 0.2}, {'edge': '(10, 20)', 'frequency': 0.2}, {'edge': '(16, 62)', 'frequency': 0.2}, {'edge': '(30, 45)', 'frequency': 0.2}, {'edge': '(2, 44)', 'frequency': 0.2}, {'edge': '(51, 55)', 'frequency': 0.2}, {'edge': '(18, 38)', 'frequency': 0.2}, {'edge': '(14, 61)', 'frequency': 0.2}, {'edge': '(1, 14)', 'frequency': 0.2}, {'edge': '(13, 28)', 'frequency': 0.2}, {'edge': '(0, 40)', 'frequency': 0.2}, {'edge': '(0, 64)', 'frequency': 0.2}, {'edge': '(26, 39)', 'frequency': 0.2}, {'edge': '(26, 33)', 'frequency': 0.2}, {'edge': '(41, 59)', 'frequency': 0.2}, {'edge': '(10, 59)', 'frequency': 0.2}, {'edge': '(37, 51)', 'frequency': 0.2}, {'edge': '(8, 23)', 'frequency': 0.2}, {'edge': '(10, 45)', 'frequency': 0.2}, {'edge': '(6, 27)', 'frequency': 0.2}, {'edge': '(31, 47)', 'frequency': 0.2}, {'edge': '(50, 54)', 'frequency': 0.2}, {'edge': '(25, 61)', 'frequency': 0.2}, {'edge': '(56, 60)', 'frequency': 0.2}, {'edge': '(11, 28)', 'frequency': 0.2}, {'edge': '(42, 62)', 'frequency': 0.2}, {'edge': '(12, 30)', 'frequency': 0.2}, {'edge': '(12, 43)', 'frequency': 0.2}, {'edge': '(38, 57)', 'frequency': 0.2}, {'edge': '(3, 38)', 'frequency': 0.2}, {'edge': '(23, 38)', 'frequency': 0.2}, {'edge': '(23, 49)', 'frequency': 0.2}, {'edge': '(29, 52)', 'frequency': 0.2}, {'edge': '(29, 40)', 'frequency': 0.2}, {'edge': '(46, 60)', 'frequency': 0.2}, {'edge': '(48, 59)', 'frequency': 0.2}, {'edge': '(25, 32)', 'frequency': 0.2}, {'edge': '(31, 41)', 'frequency': 0.2}, {'edge': '(2, 41)', 'frequency': 0.2}, {'edge': '(48, 62)', 'frequency': 0.2}, {'edge': '(5, 52)', 'frequency': 0.2}, {'edge': '(6, 42)', 'frequency': 0.2}, {'edge': '(11, 21)', 'frequency': 0.2}, {'edge': '(3, 36)', 'frequency': 0.2}, {'edge': '(5, 19)', 'frequency': 0.2}, {'edge': '(30, 58)', 'frequency': 0.2}, {'edge': '(0, 39)', 'frequency': 0.2}, {'edge': '(12, 50)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [44, 60, 46, 64, 42], 'cost': 11301.0, 'size': 5}, {'region': [11, 38, 57, 46, 63], 'cost': 11061.0, 'size': 5}, {'region': [50, 4, 51, 6, 42], 'cost': 10036.0, 'size': 5}, {'region': [30, 57, 36, 53], 'cost': 8901.0, 'size': 4}, {'region': [29, 63, 35, 53], 'cost': 8896.0, 'size': 4}]}
2025-07-03 21:32:24,030 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 21:32:24,031 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 21:32:24,031 - EliteExpert - INFO - 开始精英解分析
2025-07-03 21:32:24,031 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-03 21:32:24,031 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-07-03 21:32:24,031 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 21:32:24,031 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 21:32:24,854 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 21:32:24,854 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9945.0, mean 81682.7, max 120103.0, std 47196.15916586857
- diversity: 0.9202020202020201
- convergence: 0.0
- clustering: 9 clusters, sizes [2, 1, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (24, 29), 'frequency': 0.5, 'avg_cost': 41.0}]
- common_subpaths_sample: [{'subpath': (39, 44, 45), 'frequency': 0.3}, {'subpath': (44, 45, 38), 'frequency': 0.3}, {'subpath': (45, 38, 51), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [44, 60, 46, 64, 42], 'cost': 11301.0, 'size': 5}, {'region': [11, 38, 57, 46, 63], 'cost': 11061.0, 'size': 5}, {'region': [50, 4, 51, 6, 42], 'cost': 10036.0, 'size': 5}]
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 21:32:24,854 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:32:37,022 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.9202020202020201,
    "convergence": 0.0,
    "clustering": "strong"
  },
  "difficult_regions": [
    {"region": [44, 60, 46, 64, 42], "cost": 11301.0, "size": 5},
    {"region": [11, 38, 57, 46, 63], "cost": 11061.0, "size": 5},
    {"region": [50, 4, 51, 6, 42], "cost": 10036.0, "size": 5}
  ],
  "opportunity_regions": [],
  "evolution_phase": "initial",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["crossover", "mutation"]
  }
}
```
2025-07-03 21:32:37,022 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 21:32:37,022 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9202020202020201, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'region': [44, 60, 46, 64, 42], 'cost': 11301.0, 'size': 5}, {'region': [11, 38, 57, 46, 63], 'cost': 11061.0, 'size': 5}, {'region': [50, 4, 51, 6, 42], 'cost': 10036.0, 'size': 5}], 'opportunity_regions': [], 'evolution_phase': 'initial', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 21:32:37,022 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 21:32:37,024 - __main__ - INFO - 分析阶段完成
2025-07-03 21:32:37,024 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9202020202020201, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'region': [44, 60, 46, 64, 42], 'cost': 11301.0, 'size': 5}, {'region': [11, 38, 57, 46, 63], 'cost': 11061.0, 'size': 5}, {'region': [50, 4, 51, 6, 42], 'cost': 10036.0, 'size': 5}], 'opportunity_regions': [], 'evolution_phase': 'initial', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 21:32:37,024 - __main__ - INFO - 开始策略分配阶段
2025-07-03 21:32:37,025 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 21:32:37,025 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.92
  • best_cost: 9945.0
  • mean_cost: 81682.7
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: initial
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 3): {'region': [44, 60, , {'region': [11, 38, ...
- Opportunity regions (# 0): None
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 21:32:37,025 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 21:32:37,025 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:32:44,899 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 80% explore to map rugged landscape."
}
```
2025-07-03 21:32:44,899 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-03 21:32:44,899 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-03 21:32:44,899 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 80% explore to map rugged landscape."
}
```
2025-07-03 21:32:44,899 - __main__ - INFO - 策略分配阶段完成
2025-07-03 21:32:44,899 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-03 21:32:44,899 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 80% explore to map rugged landscape."
}
```
2025-07-03 21:32:44,899 - __main__ - INFO - 开始进化阶段
2025-07-03 21:32:44,899 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 21:32:44,899 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:32:44,899 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9945.0 (rank 0.0%)
- Population diversity: 0.74
  • Edge uniqueness: 0.740
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.870
- Evolution phase: initial
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: None
- Characteristics: No specific opportunity regions identified

### Difficult Regions
- Sample regions: {'region': [44,, {'region': [11,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [49, 40, 43, 48, 46, 47, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [44, 60, 46, 64, 42], "cost": 11301.0, "size": 5}, {"region": [11, 38, 57, 46, 63], "cost": 11061.0, "size": 5}, {"region": [50, 4, 51, 6, 42], "cost": 10036.0, "size": 5}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.74, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.8703125}

2025-07-03 21:32:44,899 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:32:44,899 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:32:56,922 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "unexplored_sequential",
  "strategy_comment": "Sequential path to maximize edge uniqueness"
}
```
2025-07-03 21:32:56,923 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:32:56,923 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-03 21:32:56,923 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-07-03 21:32:56,923 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 21:32:56,923 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:32:56,923 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10093.0 (rank 10.0%)
- Population diversity: 0.796923076923077
  • Edge uniqueness: 0.797
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.914
- Evolution phase: initial
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: None
- Characteristics: No specific opportunity regions identified

### Difficult Regions
- Sample regions: {'region': [44,, {'region': [11,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [63, 52, 65, 53, 62, 59, 56, 58, 60, 64, 57, 54, 61, 55, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [44, 60, 46, 64, 42], "cost": 11301.0, "size": 5}, {"region": [11, 38, 57, 46, 63], "cost": 11061.0, "size": 5}, {"region": [50, 4, 51, 6, 42], "cost": 10036.0, "size": 5}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.796923076923077, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9140625}

2025-07-03 21:32:56,923 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:32:56,923 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:33:09,990 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 3, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "avoiding_high_cost_regions",
  "strategy_comment": "Reordered start and avoided costly clusters"
}
```
2025-07-03 21:33:09,990 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:33:09,990 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10600.0, 路径: [0, 1, 3, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-03 21:33:09,990 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 3, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10600.0}
2025-07-03 21:33:09,990 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 21:33:09,990 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:33:09,994 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10043.0 (rank 0.0%)
- Population diversity: 0.7938461538461539
  • Edge uniqueness: 0.794
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.934
- Evolution phase: initial
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: None
- Characteristics: No specific opportunity regions identified

### Difficult Regions
- Sample regions: {'region': [44,, {'region': [11,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11, 7, 3, 1, 0, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [44, 60, 46, 64, 42], "cost": 11301.0, "size": 5}, {"region": [11, 38, 57, 46, 63], "cost": 11061.0, "size": 5}, {"region": [50, 4, 51, 6, 42], "cost": 10036.0, "size": 5}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.7938461538461539, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.934375}

2025-07-03 21:33:09,994 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:33:09,994 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:33:22,489 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11],
  "targeted_regions": "avoid_difficult_regions",
  "strategy_comment": "Reordered to bypass high-cost clusters and maintain diversity"
}
```
2025-07-03 21:33:22,489 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:33:22,489 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10120.0, 路径: [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11]
2025-07-03 21:33:22,489 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11], 'cur_cost': 10120.0}
2025-07-03 21:33:22,489 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 21:33:22,492 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:33:22,492 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 112239.0 (rank 50.0%)
- Population diversity: 0.796923076923077
  • Edge uniqueness: 0.797
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.930
- Evolution phase: initial
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: None
- Characteristics: No specific opportunity regions identified

### Difficult Regions
- Sample regions: {'region': [44,, {'region': [11,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48, 3, 65, 40, 0, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [44, 60, 46, 64, 42], "cost": 11301.0, "size": 5}, {"region": [11, 38, 57, 46, 63], "cost": 11061.0, "size": 5}, {"region": [50, 4, 51, 6, 42], "cost": 10036.0, "size": 5}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.796923076923077, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9296875}

2025-07-03 21:33:22,492 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:33:22,492 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:33:34,625 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48],
  "targeted_regions": "sparse_cells(0,3,65,40)",
  "strategy_comment": "Avoids difficult regions, explores sparse cells"
}
```
2025-07-03 21:33:34,626 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:33:34,626 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111560.0, 路径: [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48]
2025-07-03 21:33:34,626 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}
2025-07-03 21:33:34,626 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 21:33:34,626 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:33:34,626 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 30.0%)
- Population diversity: 0.796923076923077
  • Edge uniqueness: 0.797
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.930
- Evolution phase: initial
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: None
- Characteristics: No specific opportunity regions identified

### Difficult Regions
- Sample regions: {'region': [44,, {'region': [11,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [4, 41, 26, 21, 55, 53, 63, 51, 37, 23, 8, 32, 13, 10, 45, 17, 33, 64, 58, 6, 27, 47, 31, 16, 36, 50, 54, 19, 65, 29, 24, 0, 52, 15, 25, 61, 2, 14, 35, 44, 46, 49, 56, 60, 22, 59, 9, 18, 3, 28, 11, 42, 62, 7, 1, 48, 39, 34, 30, 12, 43, 20, 5, 40, 38, 57]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [44, 60, 46, 64, 42], "cost": 11301.0, "size": 5}, {"region": [11, 38, 57, 46, 63], "cost": 11061.0, "size": 5}, {"region": [50, 4, 51, 6, 42], "cost": 10036.0, "size": 5}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.796923076923077, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9296875}

2025-07-03 21:33:34,626 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:33:34,626 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:33:47,012 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57],
  "targeted_regions": "avoid_high_cost_regions",
  "strategy_comment": "Reversed core path to avoid costly regions while maintaining diversity"
}
```
2025-07-03 21:33:47,012 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:33:47,012 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105610.0, 路径: [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57]
2025-07-03 21:33:47,012 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}
2025-07-03 21:33:47,012 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 21:33:47,012 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:33:47,012 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 115516.0 (rank 70.0%)
- Population diversity: 0.7953846153846154
  • Edge uniqueness: 0.795
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.930
- Evolution phase: initial
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: None
- Characteristics: No specific opportunity regions identified

### Difficult Regions
- Sample regions: {'region': [44,, {'region': [11,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 44, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 11, 28, 45, 5, 27, 4, 63, 18]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [44, 60, 46, 64, 42], "cost": 11301.0, "size": 5}, {"region": [11, 38, 57, 46, 63], "cost": 11061.0, "size": 5}, {"region": [50, 4, 51, 6, 42], "cost": 10036.0, "size": 5}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.7953846153846154, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9296875}

2025-07-03 21:33:47,012 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:33:47,012 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:33:59,470 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44],
  "targeted_regions": "avoid_high_cost_regions",
  "strategy_comment": "Reordered to skip costly segments and explore sparse nodes"
}
```
2025-07-03 21:33:59,470 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:33:59,471 - ExplorationExpert - INFO - 探索路径生成完成，成本: 117581.0, 路径: [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44]
2025-07-03 21:33:59,472 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}
2025-07-03 21:33:59,472 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-03 21:33:59,472 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:33:59,473 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 114595.0 (rank 60.0%)
- Population diversity: 0.796923076923077
  • Edge uniqueness: 0.797
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.930
- Evolution phase: initial
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: None
- Characteristics: No specific opportunity regions identified

### Difficult Regions
- Sample regions: {'region': [44,, {'region': [11,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [3, 32, 35, 40, 28, 13, 47, 33, 31, 41, 2, 22, 10, 58, 65, 18, 54, 25, 43, 37, 46, 15, 7, 9, 48, 62, 30, 12, 57, 53, 49, 23, 38, 1, 44, 63, 14, 61, 19, 55, 45, 24, 52, 5, 16, 50, 4, 51, 6, 42, 20, 29, 36, 0, 64, 17, 34, 59, 26, 39, 56, 60, 8, 27, 21, 11]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [44, 60, 46, 64, 42], "cost": 11301.0, "size": 5}, {"region": [11, 38, 57, 46, 63], "cost": 11061.0, "size": 5}, {"region": [50, 4, 51, 6, 42], "cost": 10036.0, "size": 5}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.796923076923077, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9296875}

2025-07-03 21:33:59,474 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:33:59,474 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:34:11,581 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11],
  "targeted_regions": "avoiding_high_cost_regions",
  "strategy_comment": "Reversed path to avoid costly segments while maintaining diversity"
}
```
2025-07-03 21:34:11,582 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:34:11,582 - ExplorationExpert - INFO - 探索路径生成完成，成本: 114608.0, 路径: [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11]
2025-07-03 21:34:11,582 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11], 'cur_cost': 114608.0}
2025-07-03 21:34:11,582 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-03 21:34:11,582 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:34:11,582 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 120103.0 (rank 90.0%)
- Population diversity: 0.7984615384615384
  • Edge uniqueness: 0.798
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.928
- Evolution phase: initial
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: None
- Characteristics: No specific opportunity regions identified

### Difficult Regions
- Sample regions: {'region': [44,, {'region': [11,...
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [62, 23, 44, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 11, 38, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): [{"region": [44, 60, 46, 64, 42], "cost": 11301.0, "size": 5}, {"region": [11, 38, 57, 46, 63], "cost": 11061.0, "size": 5}, {"region": [50, 4, 51, 6, 42], "cost": 10036.0, "size": 5}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.7984615384615384, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.928125}

2025-07-03 21:34:11,582 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:34:11,582 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:34:24,876 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44],
  "targeted_regions": "avoided [44,60,46,64,42] and [11,38,57,46,63]",
  "strategy_comment": "Reordered to skip costly clusters, maintain diversity"
}
```
2025-07-03 21:34:24,876 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:34:24,876 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118573.0, 路径: [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44]
2025-07-03 21:34:24,876 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44], 'cur_cost': 118573.0}
2025-07-03 21:34:24,876 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 21:34:24,876 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 21:34:24,880 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 21:34:24,882 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 104573.0
2025-07-03 21:34:27,659 - ExploitationExpert - INFO - res_population_num: 1
2025-07-03 21:34:27,659 - ExploitationExpert - INFO - res_population_costs: [93889]
2025-07-03 21:34:27,659 - ExploitationExpert - INFO - res_populations: [array([ 0, 39, 29, 41, 25, 61, 53, 64, 54, 63, 59, 48, 22, 31, 24, 58, 20,
       16, 56, 47, 40,  4, 26, 23,  8, 28, 33, 34,  1, 11, 65, 12, 50, 51,
        9, 15, 30, 45, 44, 60, 46, 42, 43, 57, 55, 10, 32, 21,  7, 35, 49,
       62, 14, 37, 38, 18,  2, 13,  6, 27,  3, 36, 17,  5, 52, 19],
      dtype=int64)]
2025-07-03 21:34:27,659 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 21:34:27,659 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 3, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10600.0}, {'tour': [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11], 'cur_cost': 10120.0}, {'tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}, {'tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}, {'tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}, {'tour': [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11], 'cur_cost': 114608.0}, {'tour': [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44], 'cur_cost': 118573.0}, {'tour': array([63, 17, 21, 39, 29,  1, 18, 45, 59, 15,  0,  2, 19, 46, 42, 52, 27,
       33, 54, 24, 51, 28,  4, 11, 35, 14, 23, 32, 64, 43, 55, 61, 56, 47,
       65,  3, 49, 16,  6,  7, 60, 48, 58, 20, 30, 37, 50, 22, 13, 40,  9,
       62,  8, 57, 53, 34, 12, 10, 38, 26, 25, 41, 31, 44, 36,  5]), 'cur_cost': 104573.0}, {'tour': [22, 14, 38, 3, 60, 43, 32, 25, 37, 47, 12, 50, 54, 20, 53, 18, 24, 34, 21, 11, 7, 56, 45, 10, 5, 19, 15, 2, 44, 17, 1, 35, 36, 48, 62, 31, 41, 59, 16, 58, 30, 49, 63, 13, 52, 4, 64, 61, 26, 65, 9, 33, 8, 46, 6, 42, 28, 39, 0, 40, 29, 57, 27, 23, 51, 55], 'cur_cost': 117485.0}]
2025-07-03 21:34:27,675 - ExploitationExpert - INFO - 局部搜索耗时: 2.79秒
2025-07-03 21:34:27,675 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-03 21:34:27,675 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 21:34:27,675 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 21:34:27,676 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 21:34:27,676 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 21:34:27,677 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 96903.0
2025-07-03 21:34:28,717 - ExploitationExpert - INFO - res_population_num: 2
2025-07-03 21:34:28,717 - ExploitationExpert - INFO - res_population_costs: [93889, 9602]
2025-07-03 21:34:28,717 - ExploitationExpert - INFO - res_populations: [array([ 0, 39, 29, 41, 25, 61, 53, 64, 54, 63, 59, 48, 22, 31, 24, 58, 20,
       16, 56, 47, 40,  4, 26, 23,  8, 28, 33, 34,  1, 11, 65, 12, 50, 51,
        9, 15, 30, 45, 44, 60, 46, 42, 43, 57, 55, 10, 32, 21,  7, 35, 49,
       62, 14, 37, 38, 18,  2, 13,  6, 27,  3, 36, 17,  5, 52, 19],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 24, 29, 32, 33, 31, 25,
       28, 30, 34, 35, 26, 36, 37, 27, 18, 17, 12, 22, 15, 14, 23, 16, 19,
       13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64)]
2025-07-03 21:34:28,719 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 21:34:28,719 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 3, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10600.0}, {'tour': [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11], 'cur_cost': 10120.0}, {'tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}, {'tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}, {'tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}, {'tour': [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11], 'cur_cost': 114608.0}, {'tour': [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44], 'cur_cost': 118573.0}, {'tour': array([63, 17, 21, 39, 29,  1, 18, 45, 59, 15,  0,  2, 19, 46, 42, 52, 27,
       33, 54, 24, 51, 28,  4, 11, 35, 14, 23, 32, 64, 43, 55, 61, 56, 47,
       65,  3, 49, 16,  6,  7, 60, 48, 58, 20, 30, 37, 50, 22, 13, 40,  9,
       62,  8, 57, 53, 34, 12, 10, 38, 26, 25, 41, 31, 44, 36,  5]), 'cur_cost': 104573.0}, {'tour': array([ 5, 51,  2, 46, 34, 16, 65, 36,  6, 41,  7, 57, 62, 54, 45, 59, 61,
       25, 21, 11, 60,  1, 52, 31, 30, 27, 24, 18, 29, 22, 43, 40, 64,  0,
       58, 32, 13, 39, 47, 14, 38, 23, 35, 19, 49, 15, 53, 20,  4, 37, 63,
       28, 42, 44, 50, 12, 17, 10, 26,  3, 56, 55,  8,  9, 33, 48]), 'cur_cost': 96903.0}]
2025-07-03 21:34:28,719 - ExploitationExpert - INFO - 局部搜索耗时: 1.04秒
2025-07-03 21:34:28,719 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-03 21:34:28,719 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 21:34:28,719 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10600.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11], 'cur_cost': 10120.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11], 'cur_cost': 114608.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44], 'cur_cost': 118573.0}}]
2025-07-03 21:34:28,722 - __main__ - INFO - 进化阶段完成
2025-07-03 21:34:28,722 - __main__ - INFO - 开始评估阶段
2025-07-03 21:34:28,722 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 21:34:28,724 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 21:34:28,725 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9945.0 → 10120.0 (Δ -175.0)
- Mean cost: 81682.7 → 80160.7
- Diversity: 0.740 → 0.811 (Δ 0.0708)

#### Elite metrics
- Elite count: 0 → 2
- Best elite cost: 9945.0 → 9602 (Δ 343.0)
- Elite diversity: 0.000 → 0.969 (Δ 0.9692)

#### Strategy performance
- Explore: 8 inds, success 25.0% , avg Δ -774.0
- Exploit: 2 inds, success 100.0% , avg Δ 10706.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 21:34:28,725 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:34:35,022 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.25, "exploit": 1.0},
  "balance_state": "exploit-heavy",
  "recommendations": ["increase_explore_ratio_to_0.8", "increase_diversity_weight_to_0.9"]
}
```
2025-07-03 21:34:35,052 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 21:34:35,052 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.25, "exploit": 1.0},
  "balance_state": "exploit-heavy",
  "recommendations": ["increase_explore_ratio_to_0.8", "increase_diversity_weight_to_0.9"]
}
```
2025-07-03 21:34:35,052 - __main__ - INFO - 评估阶段完成
2025-07-03 21:34:35,052 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.25, "exploit": 1.0},
  "balance_state": "exploit-heavy",
  "recommendations": ["increase_explore_ratio_to_0.8", "increase_diversity_weight_to_0.9"]
}
```
2025-07-03 21:34:35,052 - __main__ - INFO - 当前最佳适应度: 10120.0
2025-07-03 21:34:35,060 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_0.pkl
2025-07-03 21:34:35,061 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-03 21:34:35,061 - __main__ - INFO - 开始分析阶段
2025-07-03 21:34:35,061 - StatsExpert - INFO - 开始统计分析
2025-07-03 21:34:35,082 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10120.0, 'max': 118573.0, 'mean': 80160.7, 'std': 45862.63684972769}, 'diversity': 0.9501683501683502, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 21:34:35,083 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10120.0, 'max': 118573.0, 'mean': 80160.7, 'std': 45862.63684972769}, 'diversity_level': 0.9501683501683502, 'convergence_level': 0.0, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 21:34:35,084 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 21:34:35,084 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 21:34:35,084 - PathExpert - INFO - 开始路径结构分析
2025-07-03 21:34:35,086 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 21:34:35,086 - PathExpert - INFO - 路径结构分析完成
2025-07-03 21:34:35,086 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 21:34:35,086 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 21:34:35,086 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 21:34:35,086 - EliteExpert - INFO - 开始精英解分析
2025-07-03 21:34:35,090 - EliteExpert - INFO - 精英解分析完成
2025-07-03 21:34:35,090 - __main__ - INFO - 精英专家分析报告: {'elite_count': 2, 'elite_common_features': {'common_edges': {'(50, 51)': 1.0, '(45, 44)': 1.0, '(61, 53)': 1.0, '(55, 10)': 1.0}, 'common_edge_ratio': 0.06060606060606061}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 518.0, 'avg_gap': 28415.199999999997}, 'structure_gap': {'unique_elite_edges': 82, 'unique_pop_edges': 527, 'common_edges': 46}}, 'elite_diversity': {'diversity_score': 0.9393939393939394}}
2025-07-03 21:34:35,090 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 21:34:35,090 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 21:34:35,093 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 21:34:35,094 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 10120.0, mean 80160.7, max 118573.0, std 45862.63684972769
- diversity: 0.9501683501683502
- convergence: 0.0
- clustering: 10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [10120.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.9393939393939394}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 21:34:35,094 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:34:49,256 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.9501683501683502,
    "convergence": 0.0,
    "clustering": "weak"
  },
  "difficult_regions": [
    {
      "description": "Low-density cells and long-edge corridors",
      "cells": [
        [0, 1],
        [0, 2],
        [1, 0],
        [2, 1],
        [2, 2]
      ]
    }
  ],
  "opportunity_regions": [
    {
      "description": "High-density cells",
      "cells": [
        [0, 0],
        [2, 0],
        [2, 2]
      ]
    }
  ],
  "evolution_phase": "initial_exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "increased_mutation_rate",
      "diversity_preservation",
      "local_search_around_high_density_cells"
    ]
  }
}
```
2025-07-03 21:34:49,257 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 21:34:49,257 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9501683501683502, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'description': 'Low-density cells and long-edge corridors', 'cells': [[0, 1], [0, 2], [1, 0], [2, 1], [2, 2]]}], 'opportunity_regions': [{'description': 'High-density cells', 'cells': [[0, 0], [2, 0], [2, 2]]}], 'evolution_phase': 'initial_exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['increased_mutation_rate', 'diversity_preservation', 'local_search_around_high_density_cells']}}
2025-07-03 21:34:49,257 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 21:34:49,257 - __main__ - INFO - 分析阶段完成
2025-07-03 21:34:49,259 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9501683501683502, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'description': 'Low-density cells and long-edge corridors', 'cells': [[0, 1], [0, 2], [1, 0], [2, 1], [2, 2]]}], 'opportunity_regions': [{'description': 'High-density cells', 'cells': [[0, 0], [2, 0], [2, 2]]}], 'evolution_phase': 'initial_exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['increased_mutation_rate', 'diversity_preservation', 'local_search_around_high_density_cells']}}
2025-07-03 21:34:49,259 - __main__ - INFO - 开始策略分配阶段
2025-07-03 21:34:49,259 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 21:34:49,259 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.95
  • best_cost: 10120.0
  • mean_cost: 80160.7
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: initial_exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 1): {'description': 'Low
- Opportunity regions (# 1): {'description': 'Hig
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.25, "exploit": 1.0},
  "balance_state"...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 21:34:49,259 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 21:34:49,259 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:34:57,082 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration focus; exploit top 20%."
}
```
2025-07-03 21:34:57,082 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-03 21:34:57,083 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-03 21:34:57,083 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration focus; exploit top 20%."
}
```
2025-07-03 21:34:57,083 - __main__ - INFO - 策略分配阶段完成
2025-07-03 21:34:57,083 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-03 21:34:57,083 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration focus; exploit top 20%."
}
```
2025-07-03 21:34:57,083 - __main__ - INFO - 开始进化阶段
2025-07-03 21:34:57,083 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 21:34:57,083 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:34:57,083 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 20.0%)
- Population diversity: 0.8107692307692308
  • Edge uniqueness: 0.811
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.930
- Evolution phase: initial_exploration
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells", "cells": [[0, 0], [2, 0], [2, 2]]}]
- Difficult regions (full): [{"description": "Low-density cells and long-edge corridors", "cells": [[0, 1], [0, 2], [1, 0], [2, 1], [2, 2]]}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8107692307692308, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9296875}

2025-07-03 21:34:57,083 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:34:57,083 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:35:09,507 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "High-density cells",
  "strategy_comment": "Prioritizes high-density cells early, avoids low-density corridors."
}
```
2025-07-03 21:35:09,508 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:35:09,508 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11474.0, 路径: [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-03 21:35:09,508 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}
2025-07-03 21:35:09,508 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 21:35:09,508 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:35:09,511 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10600.0 (rank 10.0%)
- Population diversity: 0.8092307692307692
  • Edge uniqueness: 0.809
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.930
- Evolution phase: initial_exploration
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 1, 3, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells", "cells": [[0, 0], [2, 0], [2, 2]]}]
- Difficult regions (full): [{"description": "Low-density cells and long-edge corridors", "cells": [[0, 1], [0, 2], [1, 0], [2, 1], [2, 2]]}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8092307692307692, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9296875}

2025-07-03 21:35:09,511 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:35:09,511 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:35:20,632 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "High-density cells",
  "strategy_comment": "Prioritizes dense regions while avoiding low-density areas."
}
```
2025-07-03 21:35:20,632 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:35:20,632 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10632.0, 路径: [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-03 21:35:20,632 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}
2025-07-03 21:35:20,634 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 21:35:20,634 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:35:20,635 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10120.0 (rank 0.0%)
- Population diversity: 0.8092307692307692
  • Edge uniqueness: 0.809
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.933
- Evolution phase: initial_exploration
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells", "cells": [[0, 0], [2, 0], [2, 2]]}]
- Difficult regions (full): [{"description": "Low-density cells and long-edge corridors", "cells": [[0, 1], [0, 2], [1, 0], [2, 1], [2, 2]]}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8092307692307692, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9328125}

2025-07-03 21:35:20,635 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:35:20,635 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:35:32,478 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 8, 5, 4, 6, 9, 11, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55],
  "targeted_regions": "High-density cells",
  "strategy_comment": "Reordered start to explore dense cells first."
}
```
2025-07-03 21:35:32,478 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:35:32,478 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10214.0, 路径: [0, 2, 8, 5, 4, 6, 9, 11, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55]
2025-07-03 21:35:32,478 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 2, 8, 5, 4, 6, 9, 11, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55], 'cur_cost': 10214.0}
2025-07-03 21:35:32,478 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 21:35:32,478 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:35:32,478 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111560.0 (rank 60.0%)
- Population diversity: 0.8076923076923077
  • Edge uniqueness: 0.808
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.933
- Evolution phase: initial_exploration
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells", "cells": [[0, 0], [2, 0], [2, 2]]}]
- Difficult regions (full): [{"description": "Low-density cells and long-edge corridors", "cells": [[0, 1], [0, 2], [1, 0], [2, 1], [2, 2]]}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8076923076923077, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9328125}

2025-07-03 21:35:32,482 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:35:32,482 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:35:44,637 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48],
  "targeted_regions": "High-density cells",
  "strategy_comment": "Leverages high-density cells while avoiding low-density areas."
}
```
2025-07-03 21:35:44,637 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:35:44,637 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111560.0, 路径: [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48]
2025-07-03 21:35:44,637 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}
2025-07-03 21:35:44,637 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 21:35:44,637 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:35:44,637 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 105610.0 (rank 50.0%)
- Population diversity: 0.8076923076923077
  • Edge uniqueness: 0.808
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.933
- Evolution phase: initial_exploration
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells", "cells": [[0, 0], [2, 0], [2, 2]]}]
- Difficult regions (full): [{"description": "Low-density cells and long-edge corridors", "cells": [[0, 1], [0, 2], [1, 0], [2, 1], [2, 2]]}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8076923076923077, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9328125}

2025-07-03 21:35:44,641 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:35:44,641 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:35:57,089 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57],
  "targeted_regions": "High-density cells",
  "strategy_comment": "Leverages high-density cells, avoids low-density corridors"
}
```
2025-07-03 21:35:57,089 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:35:57,090 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105610.0, 路径: [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57]
2025-07-03 21:35:57,090 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}
2025-07-03 21:35:57,090 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 21:35:57,090 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:35:57,092 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 117581.0 (rank 80.0%)
- Population diversity: 0.8076923076923077
  • Edge uniqueness: 0.808
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.933
- Evolution phase: initial_exploration
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells", "cells": [[0, 0], [2, 0], [2, 2]]}]
- Difficult regions (full): [{"description": "Low-density cells and long-edge corridors", "cells": [[0, 1], [0, 2], [1, 0], [2, 1], [2, 2]]}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8076923076923077, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9328125}

2025-07-03 21:35:57,092 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:35:57,092 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:36:11,301 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44],
  "targeted_regions": "High-density cells",
  "strategy_comment": "Focuses on high-density cells and avoids low-density areas."
}
```
2025-07-03 21:36:11,302 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:36:11,302 - ExplorationExpert - INFO - 探索路径生成完成，成本: 117581.0, 路径: [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44]
2025-07-03 21:36:11,302 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}
2025-07-03 21:36:11,302 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-03 21:36:11,302 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:36:11,304 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 114608.0 (rank 70.0%)
- Population diversity: 0.8076923076923077
  • Edge uniqueness: 0.808
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.933
- Evolution phase: initial_exploration
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells", "cells": [[0, 0], [2, 0], [2, 2]]}]
- Difficult regions (full): [{"description": "Low-density cells and long-edge corridors", "cells": [[0, 1], [0, 2], [1, 0], [2, 1], [2, 2]]}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8076923076923077, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9328125}

2025-07-03 21:36:11,304 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:36:11,304 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:36:23,531 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11],
  "targeted_regions": "High-density cells",
  "strategy_comment": "Leverages high-density cells while avoiding low-density areas."
}
```
2025-07-03 21:36:23,531 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:36:23,531 - ExplorationExpert - INFO - 探索路径生成完成，成本: 114608.0, 路径: [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11]
2025-07-03 21:36:23,531 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11], 'cur_cost': 114608.0}
2025-07-03 21:36:23,531 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-03 21:36:23,531 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:36:23,535 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 118573.0 (rank 90.0%)
- Population diversity: 0.8076923076923077
  • Edge uniqueness: 0.808
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.933
- Evolution phase: initial_exploration
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells", "cells": [[0, 0], [2, 0], [2, 2]]}]
- Difficult regions (full): [{"description": "Low-density cells and long-edge corridors", "cells": [[0, 1], [0, 2], [1, 0], [2, 1], [2, 2]]}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8076923076923077, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9328125}

2025-07-03 21:36:23,535 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:36:23,535 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:36:36,822 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44],
  "targeted_regions": "High-density cells",
  "strategy_comment": "Leverages high-density cells while avoiding low-density areas."
}
```
2025-07-03 21:36:36,822 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:36:36,823 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118573.0, 路径: [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44]
2025-07-03 21:36:36,823 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44], 'cur_cost': 118573.0}
2025-07-03 21:36:36,823 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 21:36:36,823 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 21:36:36,823 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 21:36:36,823 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 111128.0
2025-07-03 21:36:37,330 - ExploitationExpert - INFO - res_population_num: 8
2025-07-03 21:36:37,331 - ExploitationExpert - INFO - res_population_costs: [9602, 93889, 9585, 9571, 9555, 9533, 9521, 9521]
2025-07-03 21:36:37,331 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 24, 29, 32, 33, 31, 25,
       28, 30, 34, 35, 26, 36, 37, 27, 18, 17, 12, 22, 15, 14, 23, 16, 19,
       13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0, 39, 29, 41, 25, 61, 53, 64, 54, 63, 59, 48, 22, 31, 24, 58, 20,
       16, 56, 47, 40,  4, 26, 23,  8, 28, 33, 34,  1, 11, 65, 12, 50, 51,
        9, 15, 30, 45, 44, 60, 46, 42, 43, 57, 55, 10, 32, 21,  7, 35, 49,
       62, 14, 37, 38, 18,  2, 13,  6, 27,  3, 36, 17,  5, 52, 19],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-03 21:36:37,335 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 21:36:37,336 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}, {'tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}, {'tour': [0, 2, 8, 5, 4, 6, 9, 11, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55], 'cur_cost': 10214.0}, {'tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}, {'tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}, {'tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}, {'tour': [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11], 'cur_cost': 114608.0}, {'tour': [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44], 'cur_cost': 118573.0}, {'tour': array([24, 33, 26, 56, 49, 59, 55, 13, 41, 17, 38, 19, 43, 52, 46, 23, 42,
       32,  8, 53, 40, 29, 11, 51, 57, 64,  1, 14, 31, 48, 37, 60, 16, 45,
       44, 50,  9, 20,  0,  2, 21,  6, 36, 47, 30, 27, 65, 18, 10, 63, 58,
       25,  5, 28, 62, 12, 39,  7, 34,  3, 22, 54,  4, 15, 35, 61]), 'cur_cost': 111128.0}, {'tour': array([ 5, 51,  2, 46, 34, 16, 65, 36,  6, 41,  7, 57, 62, 54, 45, 59, 61,
       25, 21, 11, 60,  1, 52, 31, 30, 27, 24, 18, 29, 22, 43, 40, 64,  0,
       58, 32, 13, 39, 47, 14, 38, 23, 35, 19, 49, 15, 53, 20,  4, 37, 63,
       28, 42, 44, 50, 12, 17, 10, 26,  3, 56, 55,  8,  9, 33, 48]), 'cur_cost': 96903.0}]
2025-07-03 21:36:37,338 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 21:36:37,338 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-03 21:36:37,338 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 21:36:37,338 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 21:36:37,339 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 21:36:37,339 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 21:36:37,339 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 112693.0
2025-07-03 21:36:37,842 - ExploitationExpert - INFO - res_population_num: 12
2025-07-03 21:36:37,843 - ExploitationExpert - INFO - res_population_costs: [9602, 93889, 9585, 9571, 9555, 9533, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 21:36:37,843 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 24, 29, 32, 33, 31, 25,
       28, 30, 34, 35, 26, 36, 37, 27, 18, 17, 12, 22, 15, 14, 23, 16, 19,
       13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0, 39, 29, 41, 25, 61, 53, 64, 54, 63, 59, 48, 22, 31, 24, 58, 20,
       16, 56, 47, 40,  4, 26, 23,  8, 28, 33, 34,  1, 11, 65, 12, 50, 51,
        9, 15, 30, 45, 44, 60, 46, 42, 43, 57, 55, 10, 32, 21,  7, 35, 49,
       62, 14, 37, 38, 18,  2, 13,  6, 27,  3, 36, 17,  5, 52, 19],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 21:36:37,850 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 21:36:37,850 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}, {'tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}, {'tour': [0, 2, 8, 5, 4, 6, 9, 11, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55], 'cur_cost': 10214.0}, {'tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}, {'tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}, {'tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}, {'tour': [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11], 'cur_cost': 114608.0}, {'tour': [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44], 'cur_cost': 118573.0}, {'tour': array([24, 33, 26, 56, 49, 59, 55, 13, 41, 17, 38, 19, 43, 52, 46, 23, 42,
       32,  8, 53, 40, 29, 11, 51, 57, 64,  1, 14, 31, 48, 37, 60, 16, 45,
       44, 50,  9, 20,  0,  2, 21,  6, 36, 47, 30, 27, 65, 18, 10, 63, 58,
       25,  5, 28, 62, 12, 39,  7, 34,  3, 22, 54,  4, 15, 35, 61]), 'cur_cost': 111128.0}, {'tour': array([13,  7, 52, 31, 58, 40, 15, 35,  0,  6, 39, 27, 19, 23, 26,  9, 51,
       45,  8, 57, 32, 38, 62, 34, 61,  4, 11, 22, 28, 55, 64, 48, 17, 59,
       53, 46, 12, 65, 63, 24, 54, 10, 18, 43, 49, 37,  2, 42, 36, 41, 16,
       25, 14, 60, 30,  5, 47,  3, 56, 21, 29, 33, 50, 20,  1, 44]), 'cur_cost': 112693.0}]
2025-07-03 21:36:37,852 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 21:36:37,852 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-03 21:36:37,852 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 21:36:37,853 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11474.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 8, 5, 4, 6, 9, 11, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55], 'cur_cost': 10214.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 36, 29, 20, 42, 6, 51, 4, 50, 16, 5, 52, 24, 45, 55, 19, 61, 14, 63, 44, 1, 38, 23, 49, 53, 57, 12, 30, 62, 48, 9, 7, 15, 46, 37, 43, 25, 54, 18, 65, 58, 10, 22, 2, 41, 31, 33, 47, 13, 28, 40, 35, 32, 3, 21, 27, 8, 60, 56, 39, 26, 59, 34, 17, 64, 11], 'cur_cost': 114608.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44], 'cur_cost': 118573.0}}]
2025-07-03 21:36:37,853 - __main__ - INFO - 进化阶段完成
2025-07-03 21:36:37,853 - __main__ - INFO - 开始评估阶段
2025-07-03 21:36:37,854 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 21:36:37,855 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 21:36:37,855 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 10120.0 → 10214.0 (Δ -94.0)
- Mean cost: 80160.7 → 82407.3
- Diversity: 0.811 → 0.814 (Δ 0.0031)

#### Elite metrics
- Elite count: 2 → 12
- Best elite cost: 9602 → 9521 (Δ 81)
- Elite diversity: 0.969 → 0.223 (Δ -0.7462)

#### Strategy performance
- Explore: 8 inds, success 12.5% , avg Δ -15.12
- Exploit: 2 inds, success 0.0% , avg Δ -11172.5

#### Other indicators
- No-change individuals: 5
- Regression individuals: 4
- Historical trends: Last 1 iterations: costs [10120.0], diversity [0.9501683501683502]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 21:36:37,855 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:36:45,150 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.125,
    "exploit": 0.0
  },
  "balance_state": "unbalanced_explore_effective",
  "recommendations": [
    "increase_explore_ratio_to_0.7",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 21:36:45,177 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 21:36:45,177 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.125,
    "exploit": 0.0
  },
  "balance_state": "unbalanced_explore_effective",
  "recommendations": [
    "increase_explore_ratio_to_0.7",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 21:36:45,177 - __main__ - INFO - 评估阶段完成
2025-07-03 21:36:45,177 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.125,
    "exploit": 0.0
  },
  "balance_state": "unbalanced_explore_effective",
  "recommendations": [
    "increase_explore_ratio_to_0.7",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 21:36:45,177 - __main__ - INFO - 当前最佳适应度: 10214.0
2025-07-03 21:36:45,177 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_1.pkl
2025-07-03 21:36:45,177 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-03 21:36:45,177 - __main__ - INFO - 开始分析阶段
2025-07-03 21:36:45,177 - StatsExpert - INFO - 开始统计分析
2025-07-03 21:36:45,208 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10214.0, 'max': 118573.0, 'mean': 82407.3, 'std': 47018.59084457976}, 'diversity': 0.9508417508417508, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 21:36:45,209 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10214.0, 'max': 118573.0, 'mean': 82407.3, 'std': 47018.59084457976}, 'diversity_level': 0.9508417508417508, 'convergence_level': 0.0, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 21:36:45,209 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 21:36:45,211 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 21:36:45,211 - PathExpert - INFO - 开始路径结构分析
2025-07-03 21:36:45,215 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 21:36:45,215 - PathExpert - INFO - 路径结构分析完成
2025-07-03 21:36:45,215 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 21:36:45,215 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 21:36:45,215 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 21:36:45,216 - EliteExpert - INFO - 开始精英解分析
2025-07-03 21:36:45,219 - EliteExpert - INFO - 精英解分析完成
2025-07-03 21:36:45,219 - __main__ - INFO - 精英专家分析报告: {'elite_count': 12, 'elite_common_features': {'common_edges': {'(51, 38)': 0.75}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 693.0, 'avg_gap': 65835.55}, 'structure_gap': {'unique_elite_edges': 194, 'unique_pop_edges': 492, 'common_edges': 81}}, 'elite_diversity': {'diversity_score': 0.3753443526170799}}
2025-07-03 21:36:45,220 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 21:36:45,221 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 21:36:45,222 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 21:36:45,224 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 10214.0, mean 82407.3, max 118573.0, std 47018.59084457976
- diversity: 0.9508417508417508
- convergence: 0.0
- clustering: 10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [10120.0, 10214.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.3753443526170799}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 21:36:45,224 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:36:56,244 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.9508417508417508,
    "convergence": 0.0,
    "clustering": "weak"
  },
  "difficult_regions": [
    "low-density cells in the grid",
    "regions with long edges (ratio: 0.249)"
  ],
  "opportunity_regions": [
    "high-density cells in the grid (e.g., cells with 14 nodes)"
  ],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "increase population size to improve coverage",
      "use crossover operators to exploit high-density regions",
      "apply local search in high-density cells to refine solutions"
    ]
  }
}
```
2025-07-03 21:36:56,245 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 21:36:56,245 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9508417508417508, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': ['low-density cells in the grid', 'regions with long edges (ratio: 0.249)'], 'opportunity_regions': ['high-density cells in the grid (e.g., cells with 14 nodes)'], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['increase population size to improve coverage', 'use crossover operators to exploit high-density regions', 'apply local search in high-density cells to refine solutions']}}
2025-07-03 21:36:56,245 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 21:36:56,245 - __main__ - INFO - 分析阶段完成
2025-07-03 21:36:56,245 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9508417508417508, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': ['low-density cells in the grid', 'regions with long edges (ratio: 0.249)'], 'opportunity_regions': ['high-density cells in the grid (e.g., cells with 14 nodes)'], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['increase population size to improve coverage', 'use crossover operators to exploit high-density regions', 'apply local search in high-density cells to refine solutions']}}
2025-07-03 21:36:56,247 - __main__ - INFO - 开始策略分配阶段
2025-07-03 21:36:56,247 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 21:36:56,247 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.951
  • best_cost: 10214.0
  • mean_cost: 82407.3
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): low-density cells in, regions with long ed
- Opportunity regions (# 1): high-density cells i
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.125,
    "exploit": 0.0
  },
  "bal...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 21:36:56,247 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 21:36:56,248 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:37:04,823 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and rugged landscape favor exploration; top 60% explore, bottom 40% exploit."
}
```
2025-07-03 21:37:04,823 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-03 21:37:04,823 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-03 21:37:04,823 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and rugged landscape favor exploration; top 60% explore, bottom 40% exploit."
}
```
2025-07-03 21:37:04,823 - __main__ - INFO - 策略分配阶段完成
2025-07-03 21:37:04,823 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-03 21:37:04,823 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and rugged landscape favor exploration; top 60% explore, bottom 40% exploit."
}
```
2025-07-03 21:37:04,823 - __main__ - INFO - 开始进化阶段
2025-07-03 21:37:04,823 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 21:37:04,823 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:37:04,823 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11474.0 (rank 20.0%)
- Population diversity: 0.8138461538461539
  • Edge uniqueness: 0.814
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.933
- Evolution phase: early
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: high-density ce
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: low-density cel, regions with lo
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 2, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): ["high-density cells in the grid (e.g., cells with 14 nodes)"]
- Difficult regions (full): ["low-density cells in the grid", "regions with long edges (ratio: 0.249)"]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8138461538461539, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9328125}

2025-07-03 21:37:04,828 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:37:04,828 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:37:16,958 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 2, 4, 1, 3, 5, 7, 6, 8, 9, 10, 12, 11, 13, 14, 16, 15, 17, 18, 20, 19, 21, 22, 24, 23, 25, 26, 28, 27, 29, 30, 32, 31, 33, 34, 36, 35, 37, 38, 40, 39, 41, 42, 44, 43, 45, 46, 48, 47, 49, 50, 52, 51, 53, 54, 56, 55, 57, 58, 60, 59, 61, 62, 64, 63, 65],
  "targeted_regions": "high-density cells",
  "strategy_comment": "Alternates nodes to explore dense areas while avoiding long edges."
}
```
2025-07-03 21:37:16,958 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:37:16,958 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22007.0, 路径: [0, 2, 4, 1, 3, 5, 7, 6, 8, 9, 10, 12, 11, 13, 14, 16, 15, 17, 18, 20, 19, 21, 22, 24, 23, 25, 26, 28, 27, 29, 30, 32, 31, 33, 34, 36, 35, 37, 38, 40, 39, 41, 42, 44, 43, 45, 46, 48, 47, 49, 50, 52, 51, 53, 54, 56, 55, 57, 58, 60, 59, 61, 62, 64, 63, 65]
2025-07-03 21:37:16,958 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 2, 4, 1, 3, 5, 7, 6, 8, 9, 10, 12, 11, 13, 14, 16, 15, 17, 18, 20, 19, 21, 22, 24, 23, 25, 26, 28, 27, 29, 30, 32, 31, 33, 34, 36, 35, 37, 38, 40, 39, 41, 42, 44, 43, 45, 46, 48, 47, 49, 50, 52, 51, 53, 54, 56, 55, 57, 58, 60, 59, 61, 62, 64, 63, 65], 'cur_cost': 22007.0}
2025-07-03 21:37:16,958 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 21:37:16,961 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:37:16,962 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10632.0 (rank 10.0%)
- Population diversity: 0.8215384615384616
  • Edge uniqueness: 0.822
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.955
- Evolution phase: early
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: high-density ce
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: low-density cel, regions with lo
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): ["high-density cells in the grid (e.g., cells with 14 nodes)"]
- Difficult regions (full): ["low-density cells in the grid", "regions with long edges (ratio: 0.249)"]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8215384615384616, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9546875}

2025-07-03 21:37:16,962 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:37:16,962 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:37:29,736 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "high-density cells",
  "strategy_comment": "Focus on dense areas, avoid long edges."
}
```
2025-07-03 21:37:29,736 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:37:29,736 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10632.0, 路径: [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-03 21:37:29,736 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}
2025-07-03 21:37:29,739 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 21:37:29,739 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:37:29,739 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10214.0 (rank 0.0%)
- Population diversity: 0.8215384615384616
  • Edge uniqueness: 0.822
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.955
- Evolution phase: early
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: high-density ce
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: low-density cel, regions with lo
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 2, 8, 5, 4, 6, 9, 11, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): ["high-density cells in the grid (e.g., cells with 14 nodes)"]
- Difficult regions (full): ["low-density cells in the grid", "regions with long edges (ratio: 0.249)"]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8215384615384616, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9546875}

2025-07-03 21:37:29,739 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:37:29,739 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:37:41,991 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11],
  "targeted_regions": "high-density cells",
  "strategy_comment": "Reordered start to explore dense regions first"
}
```
2025-07-03 21:37:41,991 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:37:41,991 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10120.0, 路径: [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11]
2025-07-03 21:37:41,991 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11], 'cur_cost': 10120.0}
2025-07-03 21:37:41,991 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 21:37:41,991 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:37:41,991 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111560.0 (rank 50.0%)
- Population diversity: 0.823076923076923
  • Edge uniqueness: 0.823
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.955
- Evolution phase: early
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: high-density ce
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: low-density cel, regions with lo
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): ["high-density cells in the grid (e.g., cells with 14 nodes)"]
- Difficult regions (full): ["low-density cells in the grid", "regions with long edges (ratio: 0.249)"]
- Diversity metrics (detailed): {"edge_uniqueness": 0.823076923076923, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9546875}

2025-07-03 21:37:41,991 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:37:41,991 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:37:53,819 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48],
  "targeted_regions": "high-density cells",
  "strategy_comment": "Focus on dense regions while avoiding long edges."
}
```
2025-07-03 21:37:53,820 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:37:53,820 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111560.0, 路径: [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48]
2025-07-03 21:37:53,820 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}
2025-07-03 21:37:53,821 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 21:37:53,822 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:37:53,823 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 105610.0 (rank 30.0%)
- Population diversity: 0.823076923076923
  • Edge uniqueness: 0.823
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.955
- Evolution phase: early
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: high-density ce
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: low-density cel, regions with lo
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): ["high-density cells in the grid (e.g., cells with 14 nodes)"]
- Difficult regions (full): ["low-density cells in the grid", "regions with long edges (ratio: 0.249)"]
- Diversity metrics (detailed): {"edge_uniqueness": 0.823076923076923, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9546875}

2025-07-03 21:37:53,823 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:37:53,823 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:38:07,745 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57],
  "targeted_regions": "high-density cells",
  "strategy_comment": "Explores high-density cells while avoiding long edges."
}
```
2025-07-03 21:38:07,746 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:38:07,746 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105610.0, 路径: [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57]
2025-07-03 21:38:07,746 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}
2025-07-03 21:38:07,748 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 21:38:07,748 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:38:07,748 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 117581.0 (rank 80.0%)
- Population diversity: 0.823076923076923
  • Edge uniqueness: 0.823
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.955
- Evolution phase: early
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: high-density ce
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: low-density cel, regions with lo
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): ["high-density cells in the grid (e.g., cells with 14 nodes)"]
- Difficult regions (full): ["low-density cells in the grid", "regions with long edges (ratio: 0.249)"]
- Diversity metrics (detailed): {"edge_uniqueness": 0.823076923076923, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9546875}

2025-07-03 21:38:07,748 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:38:07,748 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:38:19,745 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44],
  "targeted_regions": "high-density cells",
  "strategy_comment": "Leverages dense clusters while avoiding long edges."
}
```
2025-07-03 21:38:19,745 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:38:19,745 - ExplorationExpert - INFO - 探索路径生成完成，成本: 117581.0, 路径: [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44]
2025-07-03 21:38:19,745 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}
2025-07-03 21:38:19,745 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 21:38:19,745 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 21:38:19,747 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 21:38:19,747 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 108812.0
2025-07-03 21:38:20,249 - ExploitationExpert - INFO - res_population_num: 17
2025-07-03 21:38:20,249 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9533, 9555, 9571, 9585, 9602, 93889, 9521, 9521, 9521, 9521, 9521]
2025-07-03 21:38:20,249 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 24, 29, 32, 33, 31, 25,
       28, 30, 34, 35, 26, 36, 37, 27, 18, 17, 12, 22, 15, 14, 23, 16, 19,
       13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0, 39, 29, 41, 25, 61, 53, 64, 54, 63, 59, 48, 22, 31, 24, 58, 20,
       16, 56, 47, 40,  4, 26, 23,  8, 28, 33, 34,  1, 11, 65, 12, 50, 51,
        9, 15, 30, 45, 44, 60, 46, 42, 43, 57, 55, 10, 32, 21,  7, 35, 49,
       62, 14, 37, 38, 18,  2, 13,  6, 27,  3, 36, 17,  5, 52, 19],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-03 21:38:20,256 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 21:38:20,258 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 1, 3, 5, 7, 6, 8, 9, 10, 12, 11, 13, 14, 16, 15, 17, 18, 20, 19, 21, 22, 24, 23, 25, 26, 28, 27, 29, 30, 32, 31, 33, 34, 36, 35, 37, 38, 40, 39, 41, 42, 44, 43, 45, 46, 48, 47, 49, 50, 52, 51, 53, 54, 56, 55, 57, 58, 60, 59, 61, 62, 64, 63, 65], 'cur_cost': 22007.0}, {'tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}, {'tour': [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11], 'cur_cost': 10120.0}, {'tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}, {'tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}, {'tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}, {'tour': array([58,  8, 51, 45,  5, 52, 13, 38, 18, 47, 61, 34, 46, 32, 22, 33, 63,
       62, 14, 53, 10,  4, 64, 28,  6, 60, 55, 37,  3, 24, 21,  9, 36, 42,
       40, 57,  7,  2, 30, 56, 12, 31, 25, 59, 39, 27, 17, 16, 11, 44, 43,
       35, 48, 15, 41, 54, 50, 19, 65,  1, 26, 20,  0, 49, 23, 29]), 'cur_cost': 108812.0}, {'tour': [62, 23, 54, 2, 41, 35, 47, 51, 37, 3, 36, 10, 20, 25, 14, 31, 38, 11, 57, 46, 63, 21, 15, 55, 0, 28, 6, 5, 19, 26, 52, 29, 48, 30, 58, 43, 12, 34, 9, 50, 45, 27, 42, 53, 59, 33, 13, 65, 32, 22, 49, 4, 39, 1, 8, 24, 56, 18, 60, 61, 17, 7, 64, 40, 16, 44], 'cur_cost': 118573.0}, {'tour': array([24, 33, 26, 56, 49, 59, 55, 13, 41, 17, 38, 19, 43, 52, 46, 23, 42,
       32,  8, 53, 40, 29, 11, 51, 57, 64,  1, 14, 31, 48, 37, 60, 16, 45,
       44, 50,  9, 20,  0,  2, 21,  6, 36, 47, 30, 27, 65, 18, 10, 63, 58,
       25,  5, 28, 62, 12, 39,  7, 34,  3, 22, 54,  4, 15, 35, 61]), 'cur_cost': 111128.0}, {'tour': array([13,  7, 52, 31, 58, 40, 15, 35,  0,  6, 39, 27, 19, 23, 26,  9, 51,
       45,  8, 57, 32, 38, 62, 34, 61,  4, 11, 22, 28, 55, 64, 48, 17, 59,
       53, 46, 12, 65, 63, 24, 54, 10, 18, 43, 49, 37,  2, 42, 36, 41, 16,
       25, 14, 60, 30,  5, 47,  3, 56, 21, 29, 33, 50, 20,  1, 44]), 'cur_cost': 112693.0}]
2025-07-03 21:38:20,259 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 21:38:20,260 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-03 21:38:20,260 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-03 21:38:20,260 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 21:38:20,260 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 21:38:20,261 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 21:38:20,261 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 97213.0
2025-07-03 21:38:21,913 - ExploitationExpert - INFO - res_population_num: 21
2025-07-03 21:38:21,913 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9533, 9555, 9571, 9585, 9602, 93889, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 21:38:21,913 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 24, 29, 32, 33, 31, 25,
       28, 30, 34, 35, 26, 36, 37, 27, 18, 17, 12, 22, 15, 14, 23, 16, 19,
       13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0, 39, 29, 41, 25, 61, 53, 64, 54, 63, 59, 48, 22, 31, 24, 58, 20,
       16, 56, 47, 40,  4, 26, 23,  8, 28, 33, 34,  1, 11, 65, 12, 50, 51,
        9, 15, 30, 45, 44, 60, 46, 42, 43, 57, 55, 10, 32, 21,  7, 35, 49,
       62, 14, 37, 38, 18,  2, 13,  6, 27,  3, 36, 17,  5, 52, 19],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-03 21:38:21,922 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 21:38:21,922 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 1, 3, 5, 7, 6, 8, 9, 10, 12, 11, 13, 14, 16, 15, 17, 18, 20, 19, 21, 22, 24, 23, 25, 26, 28, 27, 29, 30, 32, 31, 33, 34, 36, 35, 37, 38, 40, 39, 41, 42, 44, 43, 45, 46, 48, 47, 49, 50, 52, 51, 53, 54, 56, 55, 57, 58, 60, 59, 61, 62, 64, 63, 65], 'cur_cost': 22007.0}, {'tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}, {'tour': [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11], 'cur_cost': 10120.0}, {'tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}, {'tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}, {'tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}, {'tour': array([58,  8, 51, 45,  5, 52, 13, 38, 18, 47, 61, 34, 46, 32, 22, 33, 63,
       62, 14, 53, 10,  4, 64, 28,  6, 60, 55, 37,  3, 24, 21,  9, 36, 42,
       40, 57,  7,  2, 30, 56, 12, 31, 25, 59, 39, 27, 17, 16, 11, 44, 43,
       35, 48, 15, 41, 54, 50, 19, 65,  1, 26, 20,  0, 49, 23, 29]), 'cur_cost': 108812.0}, {'tour': array([24, 17, 65, 22, 36, 16, 48, 31, 26, 53, 46, 37, 35, 41, 30, 34, 19,
       18, 47, 63, 62, 39, 32,  9, 64, 14, 57, 27, 59, 45, 38,  5,  4, 20,
       12, 23, 29, 43,  8, 52,  0,  1, 56, 25, 42, 44, 13, 15, 50, 51,  7,
        6, 28, 60, 49, 40, 33, 58,  2, 54,  3, 21, 10, 61, 11, 55]), 'cur_cost': 97213.0}, {'tour': array([24, 33, 26, 56, 49, 59, 55, 13, 41, 17, 38, 19, 43, 52, 46, 23, 42,
       32,  8, 53, 40, 29, 11, 51, 57, 64,  1, 14, 31, 48, 37, 60, 16, 45,
       44, 50,  9, 20,  0,  2, 21,  6, 36, 47, 30, 27, 65, 18, 10, 63, 58,
       25,  5, 28, 62, 12, 39,  7, 34,  3, 22, 54,  4, 15, 35, 61]), 'cur_cost': 111128.0}, {'tour': array([13,  7, 52, 31, 58, 40, 15, 35,  0,  6, 39, 27, 19, 23, 26,  9, 51,
       45,  8, 57, 32, 38, 62, 34, 61,  4, 11, 22, 28, 55, 64, 48, 17, 59,
       53, 46, 12, 65, 63, 24, 54, 10, 18, 43, 49, 37,  2, 42, 36, 41, 16,
       25, 14, 60, 30,  5, 47,  3, 56, 21, 29, 33, 50, 20,  1, 44]), 'cur_cost': 112693.0}]
2025-07-03 21:38:21,925 - ExploitationExpert - INFO - 局部搜索耗时: 1.66秒
2025-07-03 21:38:21,926 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-07-03 21:38:21,926 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-03 21:38:21,926 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 21:38:21,926 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 21:38:21,926 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 21:38:21,927 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 110693.0
2025-07-03 21:38:22,432 - ExploitationExpert - INFO - res_population_num: 23
2025-07-03 21:38:22,432 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9533, 9555, 9571, 9585, 9602, 93889, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 21:38:22,432 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 24, 29, 32, 33, 31, 25,
       28, 30, 34, 35, 26, 36, 37, 27, 18, 17, 12, 22, 15, 14, 23, 16, 19,
       13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0, 39, 29, 41, 25, 61, 53, 64, 54, 63, 59, 48, 22, 31, 24, 58, 20,
       16, 56, 47, 40,  4, 26, 23,  8, 28, 33, 34,  1, 11, 65, 12, 50, 51,
        9, 15, 30, 45, 44, 60, 46, 42, 43, 57, 55, 10, 32, 21,  7, 35, 49,
       62, 14, 37, 38, 18,  2, 13,  6, 27,  3, 36, 17,  5, 52, 19],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-03 21:38:22,448 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 21:38:22,449 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 1, 3, 5, 7, 6, 8, 9, 10, 12, 11, 13, 14, 16, 15, 17, 18, 20, 19, 21, 22, 24, 23, 25, 26, 28, 27, 29, 30, 32, 31, 33, 34, 36, 35, 37, 38, 40, 39, 41, 42, 44, 43, 45, 46, 48, 47, 49, 50, 52, 51, 53, 54, 56, 55, 57, 58, 60, 59, 61, 62, 64, 63, 65], 'cur_cost': 22007.0}, {'tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}, {'tour': [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11], 'cur_cost': 10120.0}, {'tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}, {'tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}, {'tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}, {'tour': array([58,  8, 51, 45,  5, 52, 13, 38, 18, 47, 61, 34, 46, 32, 22, 33, 63,
       62, 14, 53, 10,  4, 64, 28,  6, 60, 55, 37,  3, 24, 21,  9, 36, 42,
       40, 57,  7,  2, 30, 56, 12, 31, 25, 59, 39, 27, 17, 16, 11, 44, 43,
       35, 48, 15, 41, 54, 50, 19, 65,  1, 26, 20,  0, 49, 23, 29]), 'cur_cost': 108812.0}, {'tour': array([24, 17, 65, 22, 36, 16, 48, 31, 26, 53, 46, 37, 35, 41, 30, 34, 19,
       18, 47, 63, 62, 39, 32,  9, 64, 14, 57, 27, 59, 45, 38,  5,  4, 20,
       12, 23, 29, 43,  8, 52,  0,  1, 56, 25, 42, 44, 13, 15, 50, 51,  7,
        6, 28, 60, 49, 40, 33, 58,  2, 54,  3, 21, 10, 61, 11, 55]), 'cur_cost': 97213.0}, {'tour': array([55, 56, 50,  3, 12,  5, 34, 51, 22, 29, 65,  4, 60, 16, 59, 36, 40,
       44, 46, 63, 17, 38, 10,  0, 28, 15, 52, 49, 64, 24, 20, 43, 54, 14,
       57, 48, 30, 32, 13, 39,  8,  1, 26, 18, 58,  9, 31, 37, 62, 23, 33,
        6, 11, 41, 47,  7, 42, 25, 53, 27, 21, 45, 19, 35,  2, 61]), 'cur_cost': 110693.0}, {'tour': array([13,  7, 52, 31, 58, 40, 15, 35,  0,  6, 39, 27, 19, 23, 26,  9, 51,
       45,  8, 57, 32, 38, 62, 34, 61,  4, 11, 22, 28, 55, 64, 48, 17, 59,
       53, 46, 12, 65, 63, 24, 54, 10, 18, 43, 49, 37,  2, 42, 36, 41, 16,
       25, 14, 60, 30,  5, 47,  3, 56, 21, 29, 33, 50, 20,  1, 44]), 'cur_cost': 112693.0}]
2025-07-03 21:38:22,450 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 21:38:22,450 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-07-03 21:38:22,450 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 21:38:22,450 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 21:38:22,450 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 21:38:22,450 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 21:38:22,450 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110643.0
2025-07-03 21:38:22,956 - ExploitationExpert - INFO - res_population_num: 23
2025-07-03 21:38:22,956 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9533, 9555, 9571, 9585, 9602, 93889, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 21:38:22,957 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 24, 29, 32, 33, 31, 25,
       28, 30, 34, 35, 26, 36, 37, 27, 18, 17, 12, 22, 15, 14, 23, 16, 19,
       13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0, 39, 29, 41, 25, 61, 53, 64, 54, 63, 59, 48, 22, 31, 24, 58, 20,
       16, 56, 47, 40,  4, 26, 23,  8, 28, 33, 34,  1, 11, 65, 12, 50, 51,
        9, 15, 30, 45, 44, 60, 46, 42, 43, 57, 55, 10, 32, 21,  7, 35, 49,
       62, 14, 37, 38, 18,  2, 13,  6, 27,  3, 36, 17,  5, 52, 19],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-03 21:38:22,967 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 21:38:22,967 - ExploitationExpert - INFO - populations: [{'tour': [0, 2, 4, 1, 3, 5, 7, 6, 8, 9, 10, 12, 11, 13, 14, 16, 15, 17, 18, 20, 19, 21, 22, 24, 23, 25, 26, 28, 27, 29, 30, 32, 31, 33, 34, 36, 35, 37, 38, 40, 39, 41, 42, 44, 43, 45, 46, 48, 47, 49, 50, 52, 51, 53, 54, 56, 55, 57, 58, 60, 59, 61, 62, 64, 63, 65], 'cur_cost': 22007.0}, {'tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}, {'tour': [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11], 'cur_cost': 10120.0}, {'tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}, {'tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}, {'tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}, {'tour': array([58,  8, 51, 45,  5, 52, 13, 38, 18, 47, 61, 34, 46, 32, 22, 33, 63,
       62, 14, 53, 10,  4, 64, 28,  6, 60, 55, 37,  3, 24, 21,  9, 36, 42,
       40, 57,  7,  2, 30, 56, 12, 31, 25, 59, 39, 27, 17, 16, 11, 44, 43,
       35, 48, 15, 41, 54, 50, 19, 65,  1, 26, 20,  0, 49, 23, 29]), 'cur_cost': 108812.0}, {'tour': array([24, 17, 65, 22, 36, 16, 48, 31, 26, 53, 46, 37, 35, 41, 30, 34, 19,
       18, 47, 63, 62, 39, 32,  9, 64, 14, 57, 27, 59, 45, 38,  5,  4, 20,
       12, 23, 29, 43,  8, 52,  0,  1, 56, 25, 42, 44, 13, 15, 50, 51,  7,
        6, 28, 60, 49, 40, 33, 58,  2, 54,  3, 21, 10, 61, 11, 55]), 'cur_cost': 97213.0}, {'tour': array([55, 56, 50,  3, 12,  5, 34, 51, 22, 29, 65,  4, 60, 16, 59, 36, 40,
       44, 46, 63, 17, 38, 10,  0, 28, 15, 52, 49, 64, 24, 20, 43, 54, 14,
       57, 48, 30, 32, 13, 39,  8,  1, 26, 18, 58,  9, 31, 37, 62, 23, 33,
        6, 11, 41, 47,  7, 42, 25, 53, 27, 21, 45, 19, 35,  2, 61]), 'cur_cost': 110693.0}, {'tour': array([62,  3, 46, 63,  2,  7, 10, 28, 51, 43,  9, 44, 53, 12, 41, 16, 59,
       26, 18, 36,  8, 22, 14,  6, 58, 23, 49,  0, 11, 34,  4, 38, 15,  1,
       42, 48, 21, 52, 35, 30, 37, 55, 33, 50, 25, 20, 24, 32, 19, 45, 61,
       17, 60, 39, 40, 64, 57, 27, 54, 47, 29, 65,  5, 56, 31, 13]), 'cur_cost': 110643.0}]
2025-07-03 21:38:22,969 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 21:38:22,969 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-07-03 21:38:22,970 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 21:38:22,970 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 1, 3, 5, 7, 6, 8, 9, 10, 12, 11, 13, 14, 16, 15, 17, 18, 20, 19, 21, 22, 24, 23, 25, 26, 28, 27, 29, 30, 32, 31, 33, 34, 36, 35, 37, 38, 40, 39, 41, 42, 44, 43, 45, 46, 48, 47, 49, 50, 52, 51, 53, 54, 56, 55, 57, 58, 60, 59, 61, 62, 64, 63, 65], 'cur_cost': 22007.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 3, 7, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 39, 43, 48, 46, 38, 51, 50, 41, 42, 45, 44, 60, 62, 59, 56, 58, 53, 64, 57, 54, 65, 52, 63, 61, 55, 2, 8, 5, 4, 6, 9, 11], 'cur_cost': 10120.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}}]
2025-07-03 21:38:22,970 - __main__ - INFO - 进化阶段完成
2025-07-03 21:38:22,970 - __main__ - INFO - 开始评估阶段
2025-07-03 21:38:22,970 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 21:38:22,973 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 21:38:22,973 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 10214.0 → 10120.0 (Δ 94.0)
- Mean cost: 82407.3 → 80487.1
- Diversity: 0.814 → 0.794 (Δ -0.02)

#### Elite metrics
- Elite count: 12 → 23
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.223 → 0.120 (Δ -0.1033)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ -1739.83
- Exploit: 4 inds, success 100.0% , avg Δ 7410.25

#### Other indicators
- No-change individuals: 4
- Regression individuals: 1
- Historical trends: Last 2 iterations: costs [10120.0, 10214.0], diversity [0.9501683501683502, 0.9508417508417508]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 21:38:22,975 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:38:30,231 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 1.0
  },
  "balance_state": "exploit_heavy",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 21:38:30,267 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 21:38:30,267 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 1.0
  },
  "balance_state": "exploit_heavy",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 21:38:30,267 - __main__ - INFO - 评估阶段完成
2025-07-03 21:38:30,267 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 1.0
  },
  "balance_state": "exploit_heavy",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 21:38:30,267 - __main__ - INFO - 当前最佳适应度: 10120.0
2025-07-03 21:38:30,267 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_2.pkl
2025-07-03 21:38:30,267 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-03 21:38:30,267 - __main__ - INFO - 开始分析阶段
2025-07-03 21:38:30,267 - StatsExpert - INFO - 开始统计分析
2025-07-03 21:38:30,298 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10120.0, 'max': 117581.0, 'mean': 80487.1, 'std': 43735.17906777107}, 'diversity': 0.9454545454545453, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 21:38:30,298 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10120.0, 'max': 117581.0, 'mean': 80487.1, 'std': 43735.17906777107}, 'diversity_level': 0.9454545454545453, 'convergence_level': 0.0, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 21:38:30,299 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 21:38:30,299 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 21:38:30,299 - PathExpert - INFO - 开始路径结构分析
2025-07-03 21:38:30,302 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 21:38:30,302 - PathExpert - INFO - 路径结构分析完成
2025-07-03 21:38:30,302 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 21:38:30,302 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 21:38:30,302 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 21:38:30,302 - EliteExpert - INFO - 开始精英解分析
2025-07-03 21:38:30,316 - EliteExpert - INFO - 精英解分析完成
2025-07-03 21:38:30,316 - __main__ - INFO - 精英专家分析报告: {'elite_count': 23, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 599.0, 'avg_gap': 67287.44782608697}, 'structure_gap': {'unique_elite_edges': 198, 'unique_pop_edges': 487, 'common_edges': 83}}, 'elite_diversity': {'diversity_score': 0.296861899628698}}
2025-07-03 21:38:30,317 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 21:38:30,317 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 21:38:30,320 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 21:38:30,321 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 10120.0, mean 80487.1, max 117581.0, std 43735.17906777107
- diversity: 0.9454545454545453
- convergence: 0.0
- clustering: 10 clusters, sizes [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [10120.0, 10214.0, 10120.0] (Δ 47.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.296861899628698}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 21:38:30,321 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:38:44,053 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.9454545454545453,
    "convergence": 0.0,
    "clustering": "weak"
  },
  "difficult_regions": [
    {
      "description": "Low-density cells in the grid (e.g., [0,1], [1,0], [1,1]) and long-edge corridors (long_edge_ratio: 0.249)"
    }
  ],
  "opportunity_regions": [
    {
      "description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"
    }
  ],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "increase mutation rate to enhance exploration",
      "use crossover operators that promote diversity",
      "focus on high-density regions for local search"
    ]
  }
}
```
2025-07-03 21:38:44,053 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 21:38:44,053 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9454545454545453, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'description': 'Low-density cells in the grid (e.g., [0,1], [1,0], [1,1]) and long-edge corridors (long_edge_ratio: 0.249)'}], 'opportunity_regions': [{'description': 'High-density cells in the grid (e.g., [0,0], [2,0], [2,2])'}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['increase mutation rate to enhance exploration', 'use crossover operators that promote diversity', 'focus on high-density regions for local search']}}
2025-07-03 21:38:44,053 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 21:38:44,053 - __main__ - INFO - 分析阶段完成
2025-07-03 21:38:44,053 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9454545454545453, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'description': 'Low-density cells in the grid (e.g., [0,1], [1,0], [1,1]) and long-edge corridors (long_edge_ratio: 0.249)'}], 'opportunity_regions': [{'description': 'High-density cells in the grid (e.g., [0,0], [2,0], [2,2])'}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['increase mutation rate to enhance exploration', 'use crossover operators that promote diversity', 'focus on high-density regions for local search']}}
2025-07-03 21:38:44,053 - __main__ - INFO - 开始策略分配阶段
2025-07-03 21:38:44,053 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 21:38:44,053 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.945
  • best_cost: 10120.0
  • mean_cost: 80487.1
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 1): {'description': 'Low
- Opportunity regions (# 1): {'description': 'Hig
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 1.0
  },
  "bal...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 21:38:44,053 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 21:38:44,053 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:38:52,746 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus suggest exploration bias; top performers exploit."
}
```
2025-07-03 21:38:52,746 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 21:38:52,746 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 21:38:52,748 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus suggest exploration bias; top performers exploit."
}
```
2025-07-03 21:38:52,748 - __main__ - INFO - 策略分配阶段完成
2025-07-03 21:38:52,748 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 21:38:52,748 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus suggest exploration bias; top performers exploit."
}
```
2025-07-03 21:38:52,748 - __main__ - INFO - 开始进化阶段
2025-07-03 21:38:52,748 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 21:38:52,748 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:38:52,748 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 22007.0 (rank 20.0%)
- Population diversity: 0.7938461538461539
  • Edge uniqueness: 0.794
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.956
- Evolution phase: early
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 2, 4, 1, 3, 5, 7, 6, 8, 9, 10, 12, 11, 13, 14, 16, 15, 17, 18, 20, 19, 21, 22, 24, 23, 25, 26, 28, 27, 29, 30, 32, 31, 33, 34, 36, 35, 37, 38, 40, 39, 41, 42, 44, 43, 45, 46, 48, 47, 49, 50, 52, 51, 53, 54, 56, 55, 57, 58, 60, 59, 61, 62, 64, 63, 65]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"}]
- Difficult regions (full): [{"description": "Low-density cells in the grid (e.g., [0,1], [1,0], [1,1]) and long-edge corridors (long_edge_ratio: 0.249)"}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.7938461538461539, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.95625}

2025-07-03 21:38:52,752 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:38:52,752 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:39:05,439 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 1, 5, 2, 4, 7, 6, 9, 8, 10, 12, 11, 14, 13, 16, 15, 18, 17, 20, 19, 22, 21, 24, 23, 26, 25, 28, 27, 30, 29, 32, 31, 34, 33, 36, 35, 38, 37, 40, 39, 42, 41, 44, 43, 46, 45, 48, 47, 50, 49, 52, 51, 54, 53, 56, 55, 58, 57, 60, 59, 62, 61, 64, 63, 65],
  "targeted_regions": "High-density cells [0,0], [2,0], [2,2]",
  "strategy_comment": "Prioritizes dense clusters, avoids low-density corridors"
}
```
2025-07-03 21:39:05,439 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:39:05,439 - ExplorationExpert - INFO - 探索路径生成完成，成本: 25983.0, 路径: [0, 3, 1, 5, 2, 4, 7, 6, 9, 8, 10, 12, 11, 14, 13, 16, 15, 18, 17, 20, 19, 22, 21, 24, 23, 26, 25, 28, 27, 30, 29, 32, 31, 34, 33, 36, 35, 38, 37, 40, 39, 42, 41, 44, 43, 46, 45, 48, 47, 50, 49, 52, 51, 54, 53, 56, 55, 58, 57, 60, 59, 62, 61, 64, 63, 65]
2025-07-03 21:39:05,439 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 1, 5, 2, 4, 7, 6, 9, 8, 10, 12, 11, 14, 13, 16, 15, 18, 17, 20, 19, 22, 21, 24, 23, 26, 25, 28, 27, 30, 29, 32, 31, 34, 33, 36, 35, 38, 37, 40, 39, 42, 41, 44, 43, 46, 45, 48, 47, 50, 49, 52, 51, 54, 53, 56, 55, 58, 57, 60, 59, 62, 61, 64, 63, 65], 'cur_cost': 25983.0}
2025-07-03 21:39:05,442 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 21:39:05,442 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:39:05,442 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10632.0 (rank 10.0%)
- Population diversity: 0.7938461538461539
  • Edge uniqueness: 0.794
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.955
- Evolution phase: early
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"}]
- Difficult regions (full): [{"description": "Low-density cells in the grid (e.g., [0,1], [1,0], [1,1]) and long-edge corridors (long_edge_ratio: 0.249)"}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.7938461538461539, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9546875}

2025-07-03 21:39:05,442 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:39:05,442 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:39:18,028 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "High-density cells [0,0], [2,0], [2,2]",
  "strategy_comment": "Focus on high-density cells, avoid low-density areas."
}
```
2025-07-03 21:39:18,028 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:39:18,028 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10632.0, 路径: [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-07-03 21:39:18,028 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}
2025-07-03 21:39:18,028 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-03 21:39:18,028 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 21:39:18,028 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 21:39:18,028 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 99011.0
2025-07-03 21:39:18,531 - ExploitationExpert - INFO - res_population_num: 24
2025-07-03 21:39:18,531 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9555, 9571, 9585, 9602, 93889, 9521]
2025-07-03 21:39:18,531 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 24, 29, 32, 33, 31, 25,
       28, 30, 34, 35, 26, 36, 37, 27, 18, 17, 12, 22, 15, 14, 23, 16, 19,
       13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0, 39, 29, 41, 25, 61, 53, 64, 54, 63, 59, 48, 22, 31, 24, 58, 20,
       16, 56, 47, 40,  4, 26, 23,  8, 28, 33, 34,  1, 11, 65, 12, 50, 51,
        9, 15, 30, 45, 44, 60, 46, 42, 43, 57, 55, 10, 32, 21,  7, 35, 49,
       62, 14, 37, 38, 18,  2, 13,  6, 27,  3, 36, 17,  5, 52, 19],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 21:39:18,541 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 21:39:18,542 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 1, 5, 2, 4, 7, 6, 9, 8, 10, 12, 11, 14, 13, 16, 15, 18, 17, 20, 19, 22, 21, 24, 23, 26, 25, 28, 27, 30, 29, 32, 31, 34, 33, 36, 35, 38, 37, 40, 39, 42, 41, 44, 43, 46, 45, 48, 47, 50, 49, 52, 51, 54, 53, 56, 55, 58, 57, 60, 59, 62, 61, 64, 63, 65], 'cur_cost': 25983.0}, {'tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}, {'tour': array([ 0, 57, 38, 61, 60, 20, 35, 29, 46,  1, 45, 50, 55, 22, 64, 30, 14,
       10, 37, 41, 11,  8, 47, 42, 49, 48, 40, 63, 12, 39, 24,  4, 33, 31,
       28, 32, 59,  9, 58,  7, 23, 19, 17, 44, 65, 36, 21, 16, 27,  5,  3,
       51, 25,  2, 52, 54, 43, 62, 34, 56, 13, 15,  6, 53, 18, 26]), 'cur_cost': 99011.0}, {'tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}, {'tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}, {'tour': [26, 33, 3, 38, 23, 49, 37, 50, 52, 29, 40, 8, 17, 20, 39, 6, 21, 41, 14, 1, 12, 24, 60, 46, 19, 2, 30, 57, 36, 53, 10, 59, 48, 16, 34, 64, 47, 31, 61, 62, 42, 32, 25, 9, 13, 15, 35, 22, 56, 43, 0, 7, 55, 58, 51, 65, 54, 28, 45, 5, 27, 4, 63, 18, 11, 44], 'cur_cost': 117581.0}, {'tour': array([58,  8, 51, 45,  5, 52, 13, 38, 18, 47, 61, 34, 46, 32, 22, 33, 63,
       62, 14, 53, 10,  4, 64, 28,  6, 60, 55, 37,  3, 24, 21,  9, 36, 42,
       40, 57,  7,  2, 30, 56, 12, 31, 25, 59, 39, 27, 17, 16, 11, 44, 43,
       35, 48, 15, 41, 54, 50, 19, 65,  1, 26, 20,  0, 49, 23, 29]), 'cur_cost': 108812.0}, {'tour': array([24, 17, 65, 22, 36, 16, 48, 31, 26, 53, 46, 37, 35, 41, 30, 34, 19,
       18, 47, 63, 62, 39, 32,  9, 64, 14, 57, 27, 59, 45, 38,  5,  4, 20,
       12, 23, 29, 43,  8, 52,  0,  1, 56, 25, 42, 44, 13, 15, 50, 51,  7,
        6, 28, 60, 49, 40, 33, 58,  2, 54,  3, 21, 10, 61, 11, 55]), 'cur_cost': 97213.0}, {'tour': array([55, 56, 50,  3, 12,  5, 34, 51, 22, 29, 65,  4, 60, 16, 59, 36, 40,
       44, 46, 63, 17, 38, 10,  0, 28, 15, 52, 49, 64, 24, 20, 43, 54, 14,
       57, 48, 30, 32, 13, 39,  8,  1, 26, 18, 58,  9, 31, 37, 62, 23, 33,
        6, 11, 41, 47,  7, 42, 25, 53, 27, 21, 45, 19, 35,  2, 61]), 'cur_cost': 110693.0}, {'tour': array([62,  3, 46, 63,  2,  7, 10, 28, 51, 43,  9, 44, 53, 12, 41, 16, 59,
       26, 18, 36,  8, 22, 14,  6, 58, 23, 49,  0, 11, 34,  4, 38, 15,  1,
       42, 48, 21, 52, 35, 30, 37, 55, 33, 50, 25, 20, 24, 32, 19, 45, 61,
       17, 60, 39, 40, 64, 57, 27, 54, 47, 29, 65,  5, 56, 31, 13]), 'cur_cost': 110643.0}]
2025-07-03 21:39:18,544 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 21:39:18,544 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-07-03 21:39:18,544 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-07-03 21:39:18,545 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 21:39:18,545 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:39:18,546 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111560.0 (rank 80.0%)
- Population diversity: 0.8507692307692307
  • Edge uniqueness: 0.851
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.998
- Evolution phase: early
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"}]
- Difficult regions (full): [{"description": "Low-density cells in the grid (e.g., [0,1], [1,0], [1,1]) and long-edge corridors (long_edge_ratio: 0.249)"}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8507692307692307, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9984375}

2025-07-03 21:39:18,547 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:39:18,547 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:39:32,435 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48],
  "targeted_regions": "High-density cells [0,0], [2,0], [2,2]",
  "strategy_comment": "Focus on dense cells, avoid low-density areas."
}
```
2025-07-03 21:39:32,435 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:39:32,435 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111560.0, 路径: [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48]
2025-07-03 21:39:32,435 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}
2025-07-03 21:39:32,435 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 21:39:32,435 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 21:39:32,435 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 105610.0 (rank 40.0%)
- Population diversity: 0.8507692307692307
  • Edge uniqueness: 0.851
  • Path shape diversity: 1.000
  • Subpath pattern variety: 0.998
- Evolution phase: early
- Landscape recommends focus: explore

### Opportunity Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics

### Difficult Regions
- Sample regions: {'description':
- Characteristics: Unknown characteristics
- Avoidance patterns: Use detours around identified regions, maintain distance of at least 2-3 nodes

### Spatial Context
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions
- High-density node clusters: Unknown node distribution

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"description": "High-density cells in the grid (e.g., [0,0], [2,0], [2,2])"}]
- Difficult regions (full): [{"description": "Low-density cells in the grid (e.g., [0,1], [1,0], [1,1]) and long-edge corridors (long_edge_ratio: 0.249)"}]
- Diversity metrics (detailed): {"edge_uniqueness": 0.8507692307692307, "path_shape_diversity": 1.0, "subpath_pattern_variety": 0.9984375}

2025-07-03 21:39:32,435 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 21:39:32,435 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 21:39:47,522 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57],
  "targeted_regions": "High-density cells [0,0], [2,0], [2,2]",
  "strategy_comment": "Focus on high-density cells, avoid low-density areas."
}
```
2025-07-03 21:39:47,522 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 21:39:47,522 - ExplorationExpert - INFO - 探索路径生成完成，成本: 105610.0, 路径: [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57]
2025-07-03 21:39:47,522 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}
2025-07-03 21:39:47,522 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-03 21:39:47,522 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 21:39:47,522 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 21:39:47,522 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 104198.0
2025-07-03 21:39:48,024 - ExploitationExpert - INFO - res_population_num: 25
2025-07-03 21:39:48,024 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9555, 9571, 9585, 9602, 93889, 9521, 9521]
2025-07-03 21:39:48,025 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 40, 43, 48, 49, 47, 46, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 41, 51, 38, 45, 50, 42, 46, 48, 43, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 53, 62,
       59, 56, 55, 61, 65, 52, 63, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 24, 29, 32, 33, 31, 25,
       28, 30, 34, 35, 26, 36, 37, 27, 18, 17, 12, 22, 15, 14, 23, 16, 19,
       13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0, 39, 29, 41, 25, 61, 53, 64, 54, 63, 59, 48, 22, 31, 24, 58, 20,
       16, 56, 47, 40,  4, 26, 23,  8, 28, 33, 34,  1, 11, 65, 12, 50, 51,
        9, 15, 30, 45, 44, 60, 46, 42, 43, 57, 55, 10, 32, 21,  7, 35, 49,
       62, 14, 37, 38, 18,  2, 13,  6, 27,  3, 36, 17,  5, 52, 19],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-03 21:39:48,034 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 21:39:48,035 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 1, 5, 2, 4, 7, 6, 9, 8, 10, 12, 11, 14, 13, 16, 15, 18, 17, 20, 19, 22, 21, 24, 23, 26, 25, 28, 27, 30, 29, 32, 31, 34, 33, 36, 35, 38, 37, 40, 39, 42, 41, 44, 43, 46, 45, 48, 47, 50, 49, 52, 51, 54, 53, 56, 55, 58, 57, 60, 59, 62, 61, 64, 63, 65], 'cur_cost': 25983.0}, {'tour': [0, 3, 1, 7, 11, 9, 6, 4, 5, 8, 2, 10, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 14, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 48, 46, 43, 39, 38, 45, 44, 42, 41, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 10632.0}, {'tour': array([ 0, 57, 38, 61, 60, 20, 35, 29, 46,  1, 45, 50, 55, 22, 64, 30, 14,
       10, 37, 41, 11,  8, 47, 42, 49, 48, 40, 63, 12, 39, 24,  4, 33, 31,
       28, 32, 59,  9, 58,  7, 23, 19, 17, 44, 65, 36, 21, 16, 27,  5,  3,
       51, 25,  2, 52, 54, 43, 62, 34, 56, 13, 15,  6, 53, 18, 26]), 'cur_cost': 99011.0}, {'tour': [0, 3, 65, 40, 64, 19, 6, 34, 36, 39, 26, 33, 41, 59, 10, 20, 57, 11, 52, 60, 16, 62, 45, 30, 23, 58, 9, 32, 24, 29, 63, 35, 53, 15, 47, 22, 50, 2, 44, 8, 37, 54, 7, 46, 51, 55, 38, 18, 49, 5, 31, 42, 12, 56, 17, 4, 61, 14, 1, 27, 25, 21, 43, 28, 13, 48], 'cur_cost': 111560.0}, {'tour': [0, 24, 29, 65, 19, 54, 50, 36, 16, 31, 47, 27, 6, 58, 64, 33, 17, 45, 10, 13, 32, 8, 23, 37, 51, 63, 53, 55, 21, 26, 41, 4, 40, 5, 20, 43, 12, 30, 34, 39, 48, 1, 7, 62, 42, 11, 28, 3, 18, 9, 59, 22, 60, 56, 49, 46, 44, 35, 14, 2, 61, 25, 15, 52, 38, 57], 'cur_cost': 105610.0}, {'tour': array([12, 38, 46, 54, 47, 42, 30, 20, 17, 32,  0, 15, 63,  6, 24, 55, 33,
       25, 31, 50, 28, 11, 37, 49, 56, 48,  5, 34, 57, 10, 62, 65,  3, 44,
       58, 59, 39, 21, 40, 52,  2, 60, 36, 26, 22, 64, 35,  4, 16, 13, 27,
       19,  1, 51, 23,  9, 18, 43, 14, 29, 41, 45,  7, 53,  8, 61]), 'cur_cost': 104198.0}, {'tour': array([58,  8, 51, 45,  5, 52, 13, 38, 18, 47, 61, 34, 46, 32, 22, 33, 63,
       62, 14, 53, 10,  4, 64, 28,  6, 60, 55, 37,  3, 24, 21,  9, 36, 42,
       40, 57,  7,  2, 30, 56, 12, 31, 25, 59, 39, 27, 17, 16, 11, 44, 43,
       35, 48, 15, 41, 54, 50, 19, 65,  1, 26, 20,  0, 49, 23, 29]), 'cur_cost': 108812.0}, {'tour': array([24, 17, 65, 22, 36, 16, 48, 31, 26, 53, 46, 37, 35, 41, 30, 34, 19,
       18, 47, 63, 62, 39, 32,  9, 64, 14, 57, 27, 59, 45, 38,  5,  4, 20,
       12, 23, 29, 43,  8, 52,  0,  1, 56, 25, 42, 44, 13, 15, 50, 51,  7,
        6, 28, 60, 49, 40, 33, 58,  2, 54,  3, 21, 10, 61, 11, 55]), 'cur_cost': 97213.0}, {'tour': array([55, 56, 50,  3, 12,  5, 34, 51, 22, 29, 65,  4, 60, 16, 59, 36, 40,
       44, 46, 63, 17, 38, 10,  0, 28, 15, 52, 49, 64, 24, 20, 43, 54, 14,
       57, 48, 30, 32, 13, 39,  8,  1, 26, 18, 58,  9, 31, 37, 62, 23, 33,
        6, 11, 41, 47,  7, 42, 25, 53, 27, 21, 45, 19, 35,  2, 61]), 'cur_cost': 110693.0}, {'tour': array([62,  3, 46, 63,  2,  7, 10, 28, 51, 43,  9, 44, 53, 12, 41, 16, 59,
       26, 18, 36,  8, 22, 14,  6, 58, 23, 49,  0, 11, 34,  4, 38, 15,  1,
       42, 48, 21, 52, 35, 30, 37, 55, 33, 50, 25, 20, 24, 32, 19, 45, 61,
       17, 60, 39, 40, 64, 57, 27, 54, 47, 29, 65,  5, 56, 31, 13]), 'cur_cost': 110643.0}]
2025-07-03 21:39:48,035 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 21:39:48,038 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-07-03 21:39:48,038 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-03 21:39:48,038 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-03 21:39:48,038 - ExplorationExpert - INFO - 开始生成探索路径
