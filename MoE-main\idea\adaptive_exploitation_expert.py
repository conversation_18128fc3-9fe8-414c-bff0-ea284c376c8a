"""
自适应开发专家
实现自适应局部搜索、记忆增强搜索和算子选择机制
"""

import numpy as np
import random
import logging
import time
from typing import List, Tuple, Dict, Any
from collections import defaultdict, deque
import copy


class AdaptiveExploitationExpert:
    """自适应开发专家，集成多种局部搜索算子和自适应选择机制"""
    
    def __init__(self, similarity_threshold=0.8, memory_size=100):
        self.logger = logging.getLogger(__name__)
        self.similarity_threshold = similarity_threshold
        self.memory_size = memory_size
        
        # 局部搜索算子及其性能记录
        self.search_operators = {
            '2-opt': self.two_opt,
            '3-opt': self.three_opt,
            'or-opt': self.or_opt,
            'swap': self.swap_operator,
            'insert': self.insert_operator
        }
        
        # 算子性能历史记录
        self.operator_performance = {op: deque(maxlen=memory_size) for op in self.search_operators}
        self.operator_success_rate = {op: 0.5 for op in self.search_operators}
        
        # 精英解记忆
        self.elite_memory = EliteMemory(memory_size)
        
        # 搜索统计
        self.search_stats = {
            'total_searches': 0,
            'successful_improvements': 0,
            'operator_usage': defaultdict(int),
            'average_improvement': 0.0
        }
        
    def generate_path(self, individual, landscape_report, populations, distance_matrix, **kwargs):
        """生成开发路径"""
        self.logger.info("开始自适应开发路径生成")
        
        try:
            # 提取精英解信息
            elite_solutions = self._extract_elite_solutions(populations, kwargs.get('res_populations', []))
            
            # 更新精英记忆
            self.elite_memory.update_memory(elite_solutions)
            
            # 执行自适应局部搜索
            improved_tour, improved_cost = self.adaptive_local_search(
                individual["tour"], distance_matrix, elite_solutions
            )
            
            # 记录搜索结果
            self._record_search_result(individual["cur_cost"], improved_cost)
            
            return {
                "new_tour": improved_tour,
                "cur_cost": improved_cost,
                "improvement": individual["cur_cost"] - improved_cost,
                "method": "adaptive_exploitation"
            }
            
        except Exception as e:
            self.logger.error(f"自适应开发失败: {e}")
            return self._fallback_exploitation(individual, distance_matrix)
    
    def adaptive_local_search(self, tour, distance_matrix, elite_solutions):
        """自适应局部搜索"""
        current_tour = np.array(tour)
        current_cost = self._calculate_tour_cost(current_tour, distance_matrix)
        best_tour = current_tour.copy()
        best_cost = current_cost
        
        # 记忆增强搜索
        if elite_solutions:
            memory_enhanced_tour = self.memory_enhanced_search(
                current_tour, distance_matrix, elite_solutions
            )
            memory_cost = self._calculate_tour_cost(memory_enhanced_tour, distance_matrix)
            
            if memory_cost < best_cost:
                best_tour = memory_enhanced_tour
                best_cost = memory_cost
                self.logger.info(f"记忆增强搜索改进: {current_cost} -> {memory_cost}")
        
        # 自适应算子选择和应用
        max_iterations = 10
        no_improvement_count = 0
        
        for iteration in range(max_iterations):
            if no_improvement_count >= 3:  # 连续3次无改进则停止
                break
            
            # 选择最佳算子
            selected_operator = self._select_best_operator()
            
            # 应用算子
            start_time = time.time()
            improved_tour = self.search_operators[selected_operator](best_tour, distance_matrix)
            search_time = time.time() - start_time
            
            improved_cost = self._calculate_tour_cost(improved_tour, distance_matrix)
            
            # 更新算子性能
            improvement = best_cost - improved_cost
            self._update_operator_performance(selected_operator, improvement, search_time)
            
            if improved_cost < best_cost:
                best_tour = improved_tour
                best_cost = improved_cost
                no_improvement_count = 0
                self.logger.debug(f"算子 {selected_operator} 改进: {improvement:.2f}")
            else:
                no_improvement_count += 1
        
        return best_tour, best_cost
    
    def memory_enhanced_search(self, current_tour, distance_matrix, elite_solutions):
        """基于精英解记忆的增强搜索"""
        # 提取精英解的共同特征
        common_edges = self.elite_memory.get_common_edges()
        common_patterns = self.elite_memory.get_common_patterns()
        
        # 保护性局部搜索：保护共同边
        protected_tour = self._protected_local_search(
            current_tour, distance_matrix, common_edges
        )
        
        # 模式引导搜索：尝试引入共同模式
        if common_patterns:
            pattern_guided_tour = self._pattern_guided_search(
                protected_tour, distance_matrix, common_patterns
            )
            return pattern_guided_tour
        
        return protected_tour
    
    def _select_best_operator(self):
        """基于性能历史选择最佳算子"""
        # 使用UCB (Upper Confidence Bound) 策略
        total_usage = sum(self.search_stats['operator_usage'].values())
        
        if total_usage == 0:
            return random.choice(list(self.search_operators.keys()))
        
        ucb_scores = {}
        for operator in self.search_operators:
            usage_count = self.search_stats['operator_usage'][operator]
            if usage_count == 0:
                ucb_scores[operator] = float('inf')  # 优先尝试未使用的算子
            else:
                success_rate = self.operator_success_rate[operator]
                exploration_bonus = np.sqrt(2 * np.log(total_usage) / usage_count)
                ucb_scores[operator] = success_rate + exploration_bonus
        
        return max(ucb_scores, key=ucb_scores.get)
    
    def _update_operator_performance(self, operator, improvement, search_time):
        """更新算子性能记录"""
        self.search_stats['operator_usage'][operator] += 1
        
        # 记录性能
        performance_record = {
            'improvement': improvement,
            'search_time': search_time,
            'success': improvement > 0
        }
        self.operator_performance[operator].append(performance_record)
        
        # 更新成功率
        recent_records = list(self.operator_performance[operator])[-20:]  # 最近20次
        if recent_records:
            success_count = sum(1 for r in recent_records if r['success'])
            self.operator_success_rate[operator] = success_count / len(recent_records)
    
    def two_opt(self, tour, distance_matrix):
        """2-opt局部搜索"""
        best_tour = tour.copy()
        best_cost = self._calculate_tour_cost(tour, distance_matrix)
        improved = True
        
        while improved:
            improved = False
            for i in range(len(tour)):
                for j in range(i + 2, len(tour)):
                    if j == len(tour) - 1 and i == 0:
                        continue  # 避免无效交换
                    
                    # 执行2-opt交换
                    new_tour = tour.copy()
                    new_tour[i+1:j+1] = new_tour[i+1:j+1][::-1]
                    
                    new_cost = self._calculate_tour_cost(new_tour, distance_matrix)
                    if new_cost < best_cost:
                        best_tour = new_tour
                        best_cost = new_cost
                        improved = True
                        break
                if improved:
                    break
            tour = best_tour
        
        return best_tour
    
    def three_opt(self, tour, distance_matrix):
        """简化的3-opt局部搜索"""
        best_tour = tour.copy()
        best_cost = self._calculate_tour_cost(tour, distance_matrix)
        n = len(tour)
        
        for i in range(n):
            for j in range(i + 2, n):
                for k in range(j + 2, n + (1 if i > 0 else 0)):
                    # 尝试3-opt的一种重连方式
                    new_tour = tour.copy()
                    if k < n:
                        new_tour[i+1:j+1] = new_tour[i+1:j+1][::-1]
                        new_tour[j+1:k+1] = new_tour[j+1:k+1][::-1]
                    
                    new_cost = self._calculate_tour_cost(new_tour, distance_matrix)
                    if new_cost < best_cost:
                        best_tour = new_tour
                        best_cost = new_cost
        
        return best_tour
    
    def or_opt(self, tour, distance_matrix):
        """Or-opt局部搜索"""
        best_tour = tour.copy()
        best_cost = self._calculate_tour_cost(tour, distance_matrix)
        
        # 尝试移动长度为1, 2, 3的子序列
        for length in [1, 2, 3]:
            for i in range(len(tour) - length + 1):
                for j in range(len(tour)):
                    if abs(i - j) <= length:
                        continue
                    
                    new_tour = tour.copy()
                    # 移除子序列
                    subseq = new_tour[i:i+length]
                    new_tour = np.concatenate([new_tour[:i], new_tour[i+length:]])
                    
                    # 插入到新位置
                    insert_pos = j if j < i else j - length
                    new_tour = np.concatenate([
                        new_tour[:insert_pos], 
                        subseq, 
                        new_tour[insert_pos:]
                    ])
                    
                    new_cost = self._calculate_tour_cost(new_tour, distance_matrix)
                    if new_cost < best_cost:
                        best_tour = new_tour
                        best_cost = new_cost
        
        return best_tour
    
    def swap_operator(self, tour, distance_matrix):
        """交换算子"""
        best_tour = tour.copy()
        best_cost = self._calculate_tour_cost(tour, distance_matrix)
        
        for i in range(len(tour)):
            for j in range(i + 1, len(tour)):
                new_tour = tour.copy()
                new_tour[i], new_tour[j] = new_tour[j], new_tour[i]
                
                new_cost = self._calculate_tour_cost(new_tour, distance_matrix)
                if new_cost < best_cost:
                    best_tour = new_tour
                    best_cost = new_cost
        
        return best_tour
    
    def insert_operator(self, tour, distance_matrix):
        """插入算子"""
        best_tour = tour.copy()
        best_cost = self._calculate_tour_cost(tour, distance_matrix)
        
        for i in range(len(tour)):
            for j in range(len(tour)):
                if i == j:
                    continue
                
                new_tour = tour.copy()
                # 移除元素i并插入到位置j
                element = new_tour[i]
                new_tour = np.delete(new_tour, i)
                insert_pos = j if j < i else j - 1
                new_tour = np.insert(new_tour, insert_pos, element)
                
                new_cost = self._calculate_tour_cost(new_tour, distance_matrix)
                if new_cost < best_cost:
                    best_tour = new_tour
                    best_cost = new_cost
        
        return best_tour
    
    def _protected_local_search(self, tour, distance_matrix, protected_edges):
        """保护性局部搜索，保护指定的边"""
        # 简化实现：避免破坏保护边的2-opt
        best_tour = tour.copy()
        best_cost = self._calculate_tour_cost(tour, distance_matrix)
        
        for i in range(len(tour)):
            for j in range(i + 2, len(tour)):
                # 检查是否会破坏保护边
                if self._would_break_protected_edges(tour, i, j, protected_edges):
                    continue
                
                new_tour = tour.copy()
                new_tour[i+1:j+1] = new_tour[i+1:j+1][::-1]
                
                new_cost = self._calculate_tour_cost(new_tour, distance_matrix)
                if new_cost < best_cost:
                    best_tour = new_tour
                    best_cost = new_cost
        
        return best_tour
    
    def _pattern_guided_search(self, tour, distance_matrix, patterns):
        """模式引导搜索"""
        # 简化实现：尝试引入高频模式
        best_tour = tour.copy()
        
        for pattern in patterns[:3]:  # 只考虑前3个最常见模式
            # 尝试在当前路径中引入模式
            modified_tour = self._try_introduce_pattern(best_tour, pattern)
            if modified_tour is not None:
                cost = self._calculate_tour_cost(modified_tour, distance_matrix)
                if cost < self._calculate_tour_cost(best_tour, distance_matrix):
                    best_tour = modified_tour
        
        return best_tour
    
    def _calculate_tour_cost(self, tour, distance_matrix):
        """计算路径总成本"""
        if len(tour) < 2:
            return float('inf')
        
        total_cost = 0
        for i in range(len(tour)):
            next_i = (i + 1) % len(tour)
            total_cost += distance_matrix[tour[i]][tour[next_i]]
        
        return total_cost
    
    def _extract_elite_solutions(self, populations, res_populations):
        """提取精英解"""
        all_solutions = populations + res_populations
        # 按成本排序，取前20%作为精英解
        sorted_solutions = sorted(all_solutions, key=lambda x: x.get("cur_cost", float('inf')))
        elite_count = max(1, len(sorted_solutions) // 5)
        return sorted_solutions[:elite_count]
    
    def _record_search_result(self, old_cost, new_cost):
        """记录搜索结果"""
        self.search_stats['total_searches'] += 1
        if new_cost < old_cost:
            self.search_stats['successful_improvements'] += 1
            improvement = old_cost - new_cost
            # 更新平均改进
            old_avg = self.search_stats['average_improvement']
            count = self.search_stats['successful_improvements']
            self.search_stats['average_improvement'] = (old_avg * (count - 1) + improvement) / count
    
    def _fallback_exploitation(self, individual, distance_matrix):
        """回退开发方法"""
        # 简单的2-opt作为回退
        improved_tour = self.two_opt(individual["tour"], distance_matrix)
        improved_cost = self._calculate_tour_cost(improved_tour, distance_matrix)
        
        return {
            "new_tour": improved_tour,
            "cur_cost": improved_cost,
            "improvement": individual["cur_cost"] - improved_cost,
            "method": "fallback_2opt"
        }
    
    def _would_break_protected_edges(self, tour, i, j, protected_edges):
        """检查操作是否会破坏保护边"""
        # 简化检查
        for edge in protected_edges:
            if (tour[i], tour[i+1]) == edge or (tour[i+1], tour[i]) == edge:
                return True
            if (tour[j], tour[(j+1) % len(tour)]) == edge or (tour[(j+1) % len(tour)], tour[j]) == edge:
                return True
        return False
    
    def _try_introduce_pattern(self, tour, pattern):
        """尝试在路径中引入模式"""
        # 简化实现：如果模式不在路径中，尝试插入
        if len(pattern) < 2:
            return None
        
        # 检查模式是否已存在
        for i in range(len(tour) - len(pattern) + 1):
            if list(tour[i:i+len(pattern)]) == pattern:
                return None  # 模式已存在
        
        # 尝试插入模式（简化版本）
        return tour  # 暂时返回原路径
    
    def get_performance_stats(self):
        """获取性能统计"""
        return {
            'search_stats': self.search_stats.copy(),
            'operator_success_rates': self.operator_success_rate.copy(),
            'elite_memory_size': len(self.elite_memory.memory),
            'total_operator_usage': dict(self.search_stats['operator_usage'])
        }


class EliteMemory:
    """精英解记忆管理"""
    
    def __init__(self, max_size=100):
        self.max_size = max_size
        self.memory = []
        self.common_edges = defaultdict(int)
        self.common_patterns = defaultdict(int)
    
    def update_memory(self, elite_solutions):
        """更新精英解记忆"""
        for solution in elite_solutions:
            if len(self.memory) >= self.max_size:
                self.memory.pop(0)  # 移除最旧的记录
            
            self.memory.append(solution)
            self._extract_features(solution)
    
    def _extract_features(self, solution):
        """提取解的特征"""
        tour = solution.get("tour", [])
        if len(tour) < 2:
            return
        
        # 提取边
        for i in range(len(tour)):
            next_i = (i + 1) % len(tour)
            edge = (min(tour[i], tour[next_i]), max(tour[i], tour[next_i]))
            self.common_edges[edge] += 1
        
        # 提取模式（长度为3的子序列）
        for i in range(len(tour) - 2):
            pattern = tuple(tour[i:i+3])
            self.common_patterns[pattern] += 1
    
    def get_common_edges(self, threshold=0.3):
        """获取常见边"""
        if not self.memory:
            return []
        
        min_frequency = len(self.memory) * threshold
        return [edge for edge, freq in self.common_edges.items() if freq >= min_frequency]
    
    def get_common_patterns(self, top_k=5):
        """获取常见模式"""
        sorted_patterns = sorted(self.common_patterns.items(), key=lambda x: x[1], reverse=True)
        return [pattern for pattern, freq in sorted_patterns[:top_k]]
