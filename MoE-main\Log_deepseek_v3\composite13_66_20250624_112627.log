2025-06-24 11:26:27,705 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-24 11:26:27,705 - __main__ - INFO - 开始分析阶段
2025-06-24 11:26:27,705 - StatsExpert - INFO - 开始统计分析
2025-06-24 11:26:27,729 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9948.0, 'max': 116215.0, 'mean': 76466.9, 'std': 44112.51417557154}, 'diversity': 0.9195286195286194, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-24 11:26:27,736 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9948.0, 'max': 116215.0, 'mean': 76466.9, 'std': 44112.51417557154}, 'diversity_level': 0.9195286195286194, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-24 11:26:27,737 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-24 11:26:27,737 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-24 11:26:27,737 - PathExpert - INFO - 开始路径结构分析
2025-06-24 11:26:27,742 - PathExpert - INFO - 路径结构分析完成
2025-06-24 11:26:27,742 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (16, 18), 'frequency': 0.5, 'avg_cost': 17.0}], 'common_subpaths': [{'subpath': (39, 44, 45), 'frequency': 0.3}, {'subpath': (44, 45, 38), 'frequency': 0.3}, {'subpath': (45, 38, 51), 'frequency': 0.3}, {'subpath': (38, 51, 50), 'frequency': 0.3}, {'subpath': (51, 50, 41), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (26, 36, 35), 'frequency': 0.3}, {'subpath': (36, 35, 28), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(41, 50)', 'frequency': 0.4}, {'edge': '(47, 49)', 'frequency': 0.4}, {'edge': '(16, 18)', 'frequency': 0.5}, {'edge': '(26, 36)', 'frequency': 0.4}, {'edge': '(4, 5)', 'frequency': 0.4}, {'edge': '(6, 10)', 'frequency': 0.4}, {'edge': '(54, 65)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(41, 46)', 'frequency': 0.2}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(42, 48)', 'frequency': 0.2}, {'edge': '(21, 42)', 'frequency': 0.2}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(13, 20)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(12, 18)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.3}, {'edge': '(14, 15)', 'frequency': 0.2}, {'edge': '(14, 17)', 'frequency': 0.3}, {'edge': '(17, 19)', 'frequency': 0.2}, {'edge': '(19, 27)', 'frequency': 0.3}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(3, 32)', 'frequency': 0.3}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 7)', 'frequency': 0.2}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(5, 9)', 'frequency': 0.2}, {'edge': '(4, 8)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(0, 55)', 'frequency': 0.2}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.2}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.2}, {'edge': '(60, 64)', 'frequency': 0.2}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(39, 63)', 'frequency': 0.2}, {'edge': '(53, 64)', 'frequency': 0.2}, {'edge': '(61, 63)', 'frequency': 0.2}, {'edge': '(5, 8)', 'frequency': 0.2}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(41, 42)', 'frequency': 0.2}, {'edge': '(32, 60)', 'frequency': 0.2}, {'edge': '(3, 28)', 'frequency': 0.2}, {'edge': '(13, 45)', 'frequency': 0.2}, {'edge': '(8, 62)', 'frequency': 0.2}, {'edge': '(17, 40)', 'frequency': 0.2}, {'edge': '(1, 25)', 'frequency': 0.2}, {'edge': '(44, 51)', 'frequency': 0.2}, {'edge': '(57, 59)', 'frequency': 0.3}, {'edge': '(31, 62)', 'frequency': 0.2}, {'edge': '(38, 42)', 'frequency': 0.2}, {'edge': '(41, 64)', 'frequency': 0.2}, {'edge': '(4, 56)', 'frequency': 0.2}, {'edge': '(11, 30)', 'frequency': 0.2}, {'edge': '(54, 55)', 'frequency': 0.2}, {'edge': '(57, 65)', 'frequency': 0.2}, {'edge': '(48, 53)', 'frequency': 0.2}, {'edge': '(15, 40)', 'frequency': 0.2}, {'edge': '(9, 51)', 'frequency': 0.2}, {'edge': '(14, 25)', 'frequency': 0.2}, {'edge': '(0, 44)', 'frequency': 0.2}, {'edge': '(15, 45)', 'frequency': 0.2}, {'edge': '(33, 63)', 'frequency': 0.2}, {'edge': '(60, 61)', 'frequency': 0.2}, {'edge': '(41, 43)', 'frequency': 0.2}, {'edge': '(44, 52)', 'frequency': 0.2}, {'edge': '(21, 39)', 'frequency': 0.2}, {'edge': '(46, 50)', 'frequency': 0.2}, {'edge': '(22, 57)', 'frequency': 0.2}, {'edge': '(35, 37)', 'frequency': 0.2}, {'edge': '(37, 48)', 'frequency': 0.2}, {'edge': '(55, 63)', 'frequency': 0.2}, {'edge': '(5, 11)', 'frequency': 0.2}, {'edge': '(12, 13)', 'frequency': 0.2}, {'edge': '(11, 38)', 'frequency': 0.3}, {'edge': '(24, 28)', 'frequency': 0.2}, {'edge': '(1, 46)', 'frequency': 0.2}, {'edge': '(43, 59)', 'frequency': 0.2}, {'edge': '(37, 55)', 'frequency': 0.2}, {'edge': '(10, 20)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [60, 38, 11, 45, 30, 50], 'cost': 12138.0, 'size': 6}, {'region': [36, 60, 32, 52, 28], 'cost': 11892.0, 'size': 5}, {'region': [27, 65, 38, 59, 36], 'cost': 11481.0, 'size': 5}, {'region': [8, 47, 62, 26, 63], 'cost': 11138.0, 'size': 5}, {'region': [56, 42, 3, 38, 64], 'cost': 10755.0, 'size': 5}]}
2025-06-24 11:26:27,744 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-24 11:26:27,744 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-24 11:26:27,744 - EliteExpert - INFO - 开始精英解分析
2025-06-24 11:26:27,744 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-24 11:26:27,745 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-24 11:26:27,745 - __main__ - INFO - 更新共享数据: elite_features
2025-06-24 11:26:27,745 - LandscapeExpert - INFO - 开始景观分析
2025-06-24 11:26:28,542 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-24 11:26:28,542 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9948.0, mean 76466.9, max 116215.0, std 44112.51417557154
- diversity: 0.9195286195286194
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (16, 18), 'frequency': 0.5, 'avg_cost': 17.0}]
- common_subpaths_sample: [{'subpath': (39, 44, 45), 'frequency': 0.3}, {'subpath': (44, 45, 38), 'frequency': 0.3}, {'subpath': (45, 38, 51), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [60, 38, 11, 45, 30, 50], 'cost': 12138.0, 'size': 6}, {'region': [36, 60, 32, 52, 28], 'cost': 11892.0, 'size': 5}, {'region': [27, 65, 38, 59, 36], 'cost': 11481.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-24 11:26:35,839 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "The significant difference between the minimum and maximum edge lengths (min: 10.0, max: 3032.0) suggests a rugged search space. The high standard deviation in cost further supports this.",
    "modality": "The lack of elite solutions and low `elite_common_edge_percentage` (0) indicate a multi-modal landscape with potentially many local optima.",
    "deceptiveness": "The `long_edge_ratio` of 0.249 indicates a significant proportion of long edges, which could make simple local search strategies deceptive, as long jumps might be necessary to escape local optima."
  },
  "population_state": {
    "coverage": "The diversity is relatively high (0.919), suggesting good population coverage of the search space. The density grid reveals uneven distribution with some cells being much denser than others.",
    "convergence": "Convergence is very low (0.0), meaning the population hasn't started to converge towards a solution. This aligns with the high diversity and lack of elite solutions."
  },
  "difficult_regions": [
    {
      "region": [
        60,
        38,
        11,
        45,
        30,
        50
      ],
      "cost": 12138.0,
      "size": 6,
      "spatial_context": "Nodes 60, 38, 11, 45, 30, 50 are located in a difficult region according to structural analysis. The spatial density analysis is needed for further insights.",
      "structural_context": "This region has a high cost, indicating a possible bottleneck or suboptimal connections between these nodes."
    },
    {
      "region": [
        36,
        60,
        32,
        52,
        28
      ],
      "cost": 11892.0,
      "size": 5,
      "spatial_context": "Nodes 36, 60, 32, 52, and 28 are located in a difficult region according to structural analysis. The spatial density analysis is needed for further insights.",
      "structural_context": "This region also has a high cost, suggesting issues in connecting these specific nodes."
    },
    {
      "region": [
        27,
        65,
        38,
        59,
        36
      ],
      "cost": 11481.0,
      "size": 5,
      "spatial_context": "Nodes 27, 65, 38, 59, and 36 are located in a difficult region according to structural analysis. The spatial density analysis is needed for further insights.",
      "structural_context": "Similar to the other regions, this region's high cost points to connectivity problems."
    },
    {
      "spatial_context": "Based on the spatial analysis, regions with long edges, especially those traversing from dense to sparse cells are likely difficult. Need coordinates to confirm nodes in [14, 0, 0], [0, 12, 12], [14, 0, 14] with long edges.",
      "structural_context": "The identified `common_subpaths_sample` such as (39, 44, 45) could be contributing to difficulty if their integration within the solution is consistently suboptimal."
    }
  ],
  "opportunity_regions": [
    {
      "spatial_context": "High-density cells in the density grid (top-left, middle-right/bottom-left/bottom-right) are potential opportunity regions, but without knowing node IDs located there, it's hard to define it. Consider improving connectivity within these high-density regions.",
      "structural_context": "The frequent edge (16, 18) with relatively low cost indicates a potentially good connection. Exploiting this edge by building solutions around it or swapping other less efficient edges with alternatives around these nodes is a good strategy.",
      "edge": "(16, 18)"
    }
  ],
  "evolution_phase": "Exploration",
  "evolution_direction": "Given the high diversity and low convergence, the algorithm is still in the exploration phase. Focus on improving exploration and delaying selection pressure. The lack of elites indicates that selection pressure may need to be lowered and exploration intensified to find more promising regions of the search space."
}
```
2025-06-24 11:26:35,839 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-24 11:26:35,843 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'The significant difference between the minimum and maximum edge lengths (min: 10.0, max: 3032.0) suggests a rugged search space. The high standard deviation in cost further supports this.', 'modality': 'The lack of elite solutions and low `elite_common_edge_percentage` (0) indicate a multi-modal landscape with potentially many local optima.', 'deceptiveness': 'The `long_edge_ratio` of 0.249 indicates a significant proportion of long edges, which could make simple local search strategies deceptive, as long jumps might be necessary to escape local optima.'}, 'population_state': {'coverage': 'The diversity is relatively high (0.919), suggesting good population coverage of the search space. The density grid reveals uneven distribution with some cells being much denser than others.', 'convergence': "Convergence is very low (0.0), meaning the population hasn't started to converge towards a solution. This aligns with the high diversity and lack of elite solutions."}, 'difficult_regions': [{'region': [60, 38, 11, 45, 30, 50], 'cost': 12138.0, 'size': 6, 'spatial_context': 'Nodes 60, 38, 11, 45, 30, 50 are located in a difficult region according to structural analysis. The spatial density analysis is needed for further insights.', 'structural_context': 'This region has a high cost, indicating a possible bottleneck or suboptimal connections between these nodes.'}, {'region': [36, 60, 32, 52, 28], 'cost': 11892.0, 'size': 5, 'spatial_context': 'Nodes 36, 60, 32, 52, and 28 are located in a difficult region according to structural analysis. The spatial density analysis is needed for further insights.', 'structural_context': 'This region also has a high cost, suggesting issues in connecting these specific nodes.'}, {'region': [27, 65, 38, 59, 36], 'cost': 11481.0, 'size': 5, 'spatial_context': 'Nodes 27, 65, 38, 59, and 36 are located in a difficult region according to structural analysis. The spatial density analysis is needed for further insights.', 'structural_context': "Similar to the other regions, this region's high cost points to connectivity problems."}, {'spatial_context': 'Based on the spatial analysis, regions with long edges, especially those traversing from dense to sparse cells are likely difficult. Need coordinates to confirm nodes in [14, 0, 0], [0, 12, 12], [14, 0, 14] with long edges.', 'structural_context': 'The identified `common_subpaths_sample` such as (39, 44, 45) could be contributing to difficulty if their integration within the solution is consistently suboptimal.'}], 'opportunity_regions': [{'spatial_context': "High-density cells in the density grid (top-left, middle-right/bottom-left/bottom-right) are potential opportunity regions, but without knowing node IDs located there, it's hard to define it. Consider improving connectivity within these high-density regions.", 'structural_context': 'The frequent edge (16, 18) with relatively low cost indicates a potentially good connection. Exploiting this edge by building solutions around it or swapping other less efficient edges with alternatives around these nodes is a good strategy.', 'edge': '(16, 18)'}], 'evolution_phase': 'Exploration', 'evolution_direction': 'Given the high diversity and low convergence, the algorithm is still in the exploration phase. Focus on improving exploration and delaying selection pressure. The lack of elites indicates that selection pressure may need to be lowered and exploration intensified to find more promising regions of the search space.'}
2025-06-24 11:26:35,843 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-24 11:26:35,843 - __main__ - INFO - 分析阶段完成
2025-06-24 11:26:35,843 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'The significant difference between the minimum and maximum edge lengths (min: 10.0, max: 3032.0) suggests a rugged search space. The high standard deviation in cost further supports this.', 'modality': 'The lack of elite solutions and low `elite_common_edge_percentage` (0) indicate a multi-modal landscape with potentially many local optima.', 'deceptiveness': 'The `long_edge_ratio` of 0.249 indicates a significant proportion of long edges, which could make simple local search strategies deceptive, as long jumps might be necessary to escape local optima.'}, 'population_state': {'coverage': 'The diversity is relatively high (0.919), suggesting good population coverage of the search space. The density grid reveals uneven distribution with some cells being much denser than others.', 'convergence': "Convergence is very low (0.0), meaning the population hasn't started to converge towards a solution. This aligns with the high diversity and lack of elite solutions."}, 'difficult_regions': [{'region': [60, 38, 11, 45, 30, 50], 'cost': 12138.0, 'size': 6, 'spatial_context': 'Nodes 60, 38, 11, 45, 30, 50 are located in a difficult region according to structural analysis. The spatial density analysis is needed for further insights.', 'structural_context': 'This region has a high cost, indicating a possible bottleneck or suboptimal connections between these nodes.'}, {'region': [36, 60, 32, 52, 28], 'cost': 11892.0, 'size': 5, 'spatial_context': 'Nodes 36, 60, 32, 52, and 28 are located in a difficult region according to structural analysis. The spatial density analysis is needed for further insights.', 'structural_context': 'This region also has a high cost, suggesting issues in connecting these specific nodes.'}, {'region': [27, 65, 38, 59, 36], 'cost': 11481.0, 'size': 5, 'spatial_context': 'Nodes 27, 65, 38, 59, and 36 are located in a difficult region according to structural analysis. The spatial density analysis is needed for further insights.', 'structural_context': "Similar to the other regions, this region's high cost points to connectivity problems."}, {'spatial_context': 'Based on the spatial analysis, regions with long edges, especially those traversing from dense to sparse cells are likely difficult. Need coordinates to confirm nodes in [14, 0, 0], [0, 12, 12], [14, 0, 14] with long edges.', 'structural_context': 'The identified `common_subpaths_sample` such as (39, 44, 45) could be contributing to difficulty if their integration within the solution is consistently suboptimal.'}], 'opportunity_regions': [{'spatial_context': "High-density cells in the density grid (top-left, middle-right/bottom-left/bottom-right) are potential opportunity regions, but without knowing node IDs located there, it's hard to define it. Consider improving connectivity within these high-density regions.", 'structural_context': 'The frequent edge (16, 18) with relatively low cost indicates a potentially good connection. Exploiting this edge by building solutions around it or swapping other less efficient edges with alternatives around these nodes is a good strategy.', 'edge': '(16, 18)'}], 'evolution_phase': 'Exploration', 'evolution_direction': 'Given the high diversity and low convergence, the algorithm is still in the exploration phase. Focus on improving exploration and delaying selection pressure. The lack of elites indicates that selection pressure may need to be lowered and exploration intensified to find more promising regions of the search space.'}
2025-06-24 11:26:35,843 - __main__ - INFO - 开始策略分配阶段
2025-06-24 11:26:35,843 - StrategyExpert - INFO - 开始策略分配分析
