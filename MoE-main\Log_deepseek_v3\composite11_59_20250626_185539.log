2025-06-26 18:55:39,108 - __main__ - INFO - composite11_59 开始进化第 1 代
2025-06-26 18:55:39,108 - __main__ - INFO - 开始分析阶段
2025-06-26 18:55:39,109 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:55:39,123 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 24875.0, 'max': 270428.0, 'mean': 185919.9, 'std': 105724.77573534974}, 'diversity': 0.9145009416195857, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:55:39,124 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 24875.0, 'max': 270428.0, 'mean': 185919.9, 'std': 105724.77573534974}, 'diversity_level': 0.9145009416195857, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[3890, 3658], [3945, 3639], [3889, 3685], [3906, 3671], [3923, 3656], [3920, 3680], [3937, 3621], [3958, 3653], [3943, 3673], [3911, 3649], [3899, 3621], [6525, 868], [6531, 888], [6591, 942], [6618, 929], [6548, 851], [6574, 854], [6559, 874], [6566, 942], [6530, 940], [6620, 894], [6550, 903], [6584, 917], [1472, 1211], [1423, 1273], [1442, 1244], [1468, 1281], [1500, 1235], [1466, 1296], [1426, 1232], [1450, 1264], [1492, 1248], [1428, 1251], [1497, 1264], [1494, 1225], [919, 6136], [910, 6189], [925, 6186], [901, 6225], [883, 6136], [863, 6149], [879, 6219], [889, 6175], [933, 6194], [927, 6171], [866, 6199], [908, 6153], [6619, 6506], [6551, 6515], [6568, 6446], [6535, 6492], [6603, 6457], [6565, 6513], [6532, 6513], [6541, 6471], [6603, 6486], [6596, 6495], [6584, 6473], [6556, 6526]], 'distance_matrix': array([[   0.,   58.,   27., ..., 3921., 3896., 3916.],
       [  58.,    0.,   72., ..., 3897., 3872., 3893.],
       [  27.,   72.,    0., ..., 3902., 3878., 3897.],
       ...,
       [3921., 3897., 3902., ...,    0.,   25.,   51.],
       [3896., 3872., 3878., ...,   25.,    0.,   60.],
       [3916., 3893., 3897., ...,   51.,   60.,    0.]])}
2025-06-26 18:55:39,134 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:55:39,134 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:55:39,134 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:55:39,139 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:55:39,140 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (4, 9), 'frequency': 0.5, 'avg_cost': 14.0}, {'edge': (41, 45), 'frequency': 0.5, 'avg_cost': 24.0}], 'common_subpaths': [{'subpath': (16, 17, 15), 'frequency': 0.3}, {'subpath': (17, 15, 11), 'frequency': 0.3}, {'subpath': (15, 11, 12), 'frequency': 0.3}, {'subpath': (11, 12, 21), 'frequency': 0.3}, {'subpath': (31, 27, 34), 'frequency': 0.3}, {'subpath': (27, 34, 23), 'frequency': 0.3}, {'subpath': (45, 41, 38), 'frequency': 0.3}, {'subpath': (53, 48, 58), 'frequency': 0.3}, {'subpath': (48, 58, 52), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(14, 20)', 'frequency': 0.4}, {'edge': '(1, 6)', 'frequency': 0.4}, {'edge': '(1, 7)', 'frequency': 0.4}, {'edge': '(3, 5)', 'frequency': 0.4}, {'edge': '(4, 9)', 'frequency': 0.5}, {'edge': '(26, 30)', 'frequency': 0.4}, {'edge': '(25, 32)', 'frequency': 0.4}, {'edge': '(41, 45)', 'frequency': 0.5}, {'edge': '(39, 40)', 'frequency': 0.4}, {'edge': '(55, 56)', 'frequency': 0.4}, {'edge': '(16, 20)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(16, 17)', 'frequency': 0.3}, {'edge': '(15, 17)', 'frequency': 0.3}, {'edge': '(11, 15)', 'frequency': 0.3}, {'edge': '(11, 12)', 'frequency': 0.3}, {'edge': '(12, 21)', 'frequency': 0.3}, {'edge': '(13, 22)', 'frequency': 0.3}, {'edge': '(13, 18)', 'frequency': 0.3}, {'edge': '(18, 19)', 'frequency': 0.3}, {'edge': '(7, 8)', 'frequency': 0.3}, {'edge': '(5, 8)', 'frequency': 0.3}, {'edge': '(0, 9)', 'frequency': 0.2}, {'edge': '(26, 28)', 'frequency': 0.3}, {'edge': '(25, 30)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.2}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(27, 31)', 'frequency': 0.3}, {'edge': '(27, 34)', 'frequency': 0.3}, {'edge': '(23, 34)', 'frequency': 0.3}, {'edge': '(23, 33)', 'frequency': 0.3}, {'edge': '(35, 46)', 'frequency': 0.3}, {'edge': '(44, 46)', 'frequency': 0.2}, {'edge': '(37, 43)', 'frequency': 0.3}, {'edge': '(36, 42)', 'frequency': 0.3}, {'edge': '(38, 41)', 'frequency': 0.3}, {'edge': '(48, 53)', 'frequency': 0.3}, {'edge': '(48, 58)', 'frequency': 0.3}, {'edge': '(52, 58)', 'frequency': 0.3}, {'edge': '(52, 56)', 'frequency': 0.2}, {'edge': '(55, 57)', 'frequency': 0.3}, {'edge': '(49, 54)', 'frequency': 0.3}, {'edge': '(50, 54)', 'frequency': 0.3}, {'edge': '(47, 50)', 'frequency': 0.2}, {'edge': '(16, 47)', 'frequency': 0.2}, {'edge': '(36, 37)', 'frequency': 0.2}, {'edge': '(43, 44)', 'frequency': 0.2}, {'edge': '(35, 39)', 'frequency': 0.3}, {'edge': '(40, 45)', 'frequency': 0.2}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(14, 22)', 'frequency': 0.2}, {'edge': '(21, 49)', 'frequency': 0.2}, {'edge': '(49, 57)', 'frequency': 0.2}, {'edge': '(47, 56)', 'frequency': 0.2}, {'edge': '(47, 51)', 'frequency': 0.2}, {'edge': '(51, 54)', 'frequency': 0.2}, {'edge': '(50, 53)', 'frequency': 0.2}, {'edge': '(23, 24)', 'frequency': 0.2}, {'edge': '(0, 2)', 'frequency': 0.2}, {'edge': '(43, 52)', 'frequency': 0.2}, {'edge': '(42, 46)', 'frequency': 0.2}, {'edge': '(16, 24)', 'frequency': 0.3}, {'edge': '(16, 26)', 'frequency': 0.2}, {'edge': '(13, 30)', 'frequency': 0.3}, {'edge': '(19, 43)', 'frequency': 0.2}, {'edge': '(2, 53)', 'frequency': 0.2}, {'edge': '(8, 46)', 'frequency': 0.2}, {'edge': '(29, 56)', 'frequency': 0.3}, {'edge': '(44, 49)', 'frequency': 0.2}, {'edge': '(18, 44)', 'frequency': 0.2}, {'edge': '(4, 34)', 'frequency': 0.2}, {'edge': '(4, 58)', 'frequency': 0.2}, {'edge': '(6, 53)', 'frequency': 0.2}, {'edge': '(12, 40)', 'frequency': 0.2}, {'edge': '(23, 42)', 'frequency': 0.2}, {'edge': '(33, 47)', 'frequency': 0.2}, {'edge': '(54, 58)', 'frequency': 0.2}, {'edge': '(7, 49)', 'frequency': 0.2}, {'edge': '(37, 48)', 'frequency': 0.2}, {'edge': '(27, 48)', 'frequency': 0.3}, {'edge': '(26, 51)', 'frequency': 0.2}, {'edge': '(16, 18)', 'frequency': 0.2}, {'edge': '(24, 55)', 'frequency': 0.2}, {'edge': '(45, 58)', 'frequency': 0.2}, {'edge': '(13, 45)', 'frequency': 0.2}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(44, 51)', 'frequency': 0.2}, {'edge': '(5, 42)', 'frequency': 0.2}, {'edge': '(30, 38)', 'frequency': 0.2}, {'edge': '(34, 36)', 'frequency': 0.2}, {'edge': '(37, 55)', 'frequency': 0.2}, {'edge': '(10, 17)', 'frequency': 0.2}, {'edge': '(12, 50)', 'frequency': 0.2}, {'edge': '(3, 48)', 'frequency': 0.2}, {'edge': '(3, 23)', 'frequency': 0.2}, {'edge': '(22, 29)', 'frequency': 0.2}, {'edge': '(6, 58)', 'frequency': 0.2}, {'edge': '(2, 33)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [28, 55, 43, 52, 12, 40, 19], 'cost': 39676.0, 'size': 7}, {'region': [50, 41, 14, 45, 15, 53], 'cost': 34734.0, 'size': 6}, {'region': [58, 39, 11, 43, 19], 'cost': 28804.0, 'size': 5}, {'region': [56, 29, 49, 44, 18], 'cost': 28039.0, 'size': 5}, {'region': [21, 41, 57, 44, 49], 'cost': 24797.0, 'size': 5}]}
2025-06-26 18:55:39,140 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:55:39,140 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:55:39,141 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:55:39,141 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 18:55:39,141 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 18:55:39,142 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:55:39,142 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:55:39,143 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:55:39,143 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 24875.0, mean 185919.9, max 270428.0, std 105724.77573534974
- diversity: 0.9145009416195857
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [14860.0, 14860.0, 14860.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (4, 9), 'frequency': 0.5, 'avg_cost': 14.0}, {'edge': (41, 45), 'frequency': 0.5, 'avg_cost': 24.0}]
- common_subpaths_sample: [{'subpath': (16, 17, 15), 'frequency': 0.3}, {'subpath': (17, 15, 11), 'frequency': 0.3}, {'subpath': (15, 11, 12), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [28, 55, 43, 52, 12, 40, 19], 'cost': 39676.0, 'size': 7}, {'region': [50, 41, 14, 45, 15, 53], 'cost': 34734.0, 'size': 6}, {'region': [58, 39, 11, 43, 19], 'cost': 28804.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [863.0, 851.0, 6620.0, 6526.0]
- centroid/spread: [3883.576, 3695.881] | rms 3403.363
- density_grid(3x3): [[12, 0, 12], [0, 11, 0], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [11.0, 3466.0, 4901.0, 5640.0, 7830.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [3, 7, 8, 8, 7, 6, 12, 7]
- nn_median_dist: 20.881

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:55:39,144 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:56:03,667 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High (wide edge length range from 11.0 to 7830.0, high std dev in cost_stats)",
    "modality": "Potentially multimodal (high diversity, no clustering info yet)",
    "deceptiveness": "Moderate (long_edge_ratio 0.25 suggests some misleading long edges)"
  },
  "population_state": {
    "coverage": "Good (diversity 0.91, density_grid shows spread but with gaps)",
    "convergence": "Early stage (0.0 convergence, recent stagnation detected)",
    "quality": "Highly variable (min cost 24875 vs max 270428)"
  },
  "difficult_regions": [
    {"region": [28, 55, 43, 52, 12, 40, 19], "reason": "High cost (39676) + spatial gaps in density_grid"},
    {"region": [50, 41, 14, 45, 15, 53], "reason": "High cost (34734) + appears in common_subpaths"},
    {"region": [58, 39, 11, 43, 19], "reason": "High cost (28804) + directional bias in hist"}
  ],
  "opportunity_regions": [
    {"potential_nodes": [4,9,41,45], "reason": "High-quality edges with low avg_cost (14-24)"},
    {"potential_subpaths": ["16-17-15", "17-15-11"], "reason": "Frequent in population (0.3 freq)"}
  ],
  "evolution_phase": "Early exploration",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Intensify around high-quality edges (e.g. edge recombination including (4,9))",
    "2. Path-guided mutation for common subpaths",
    "3. Targeted local search in difficult regions",
    "4. Diversity preservation (grid-based selection to cover gaps)"
  ]
}
```
2025-06-26 18:56:03,667 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:56:03,667 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High (wide edge length range from 11.0 to 7830.0, high std dev in cost_stats)', 'modality': 'Potentially multimodal (high diversity, no clustering info yet)', 'deceptiveness': 'Moderate (long_edge_ratio 0.25 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Good (diversity 0.91, density_grid shows spread but with gaps)', 'convergence': 'Early stage (0.0 convergence, recent stagnation detected)', 'quality': 'Highly variable (min cost 24875 vs max 270428)'}, 'difficult_regions': [{'region': [28, 55, 43, 52, 12, 40, 19], 'reason': 'High cost (39676) + spatial gaps in density_grid'}, {'region': [50, 41, 14, 45, 15, 53], 'reason': 'High cost (34734) + appears in common_subpaths'}, {'region': [58, 39, 11, 43, 19], 'reason': 'High cost (28804) + directional bias in hist'}], 'opportunity_regions': [{'potential_nodes': [4, 9, 41, 45], 'reason': 'High-quality edges with low avg_cost (14-24)'}, {'potential_subpaths': ['16-17-15', '17-15-11'], 'reason': 'Frequent in population (0.3 freq)'}], 'evolution_phase': 'Early exploration', 'evolution_direction': ['Operator suggestions:', '1. Intensify around high-quality edges (e.g. edge recombination including (4,9))', '2. Path-guided mutation for common subpaths', '3. Targeted local search in difficult regions', '4. Diversity preservation (grid-based selection to cover gaps)']}
2025-06-26 18:56:03,667 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:56:03,667 - __main__ - INFO - 分析阶段完成
2025-06-26 18:56:03,667 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High (wide edge length range from 11.0 to 7830.0, high std dev in cost_stats)', 'modality': 'Potentially multimodal (high diversity, no clustering info yet)', 'deceptiveness': 'Moderate (long_edge_ratio 0.25 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Good (diversity 0.91, density_grid shows spread but with gaps)', 'convergence': 'Early stage (0.0 convergence, recent stagnation detected)', 'quality': 'Highly variable (min cost 24875 vs max 270428)'}, 'difficult_regions': [{'region': [28, 55, 43, 52, 12, 40, 19], 'reason': 'High cost (39676) + spatial gaps in density_grid'}, {'region': [50, 41, 14, 45, 15, 53], 'reason': 'High cost (34734) + appears in common_subpaths'}, {'region': [58, 39, 11, 43, 19], 'reason': 'High cost (28804) + directional bias in hist'}], 'opportunity_regions': [{'potential_nodes': [4, 9, 41, 45], 'reason': 'High-quality edges with low avg_cost (14-24)'}, {'potential_subpaths': ['16-17-15', '17-15-11'], 'reason': 'Frequent in population (0.3 freq)'}], 'evolution_phase': 'Early exploration', 'evolution_direction': ['Operator suggestions:', '1. Intensify around high-quality edges (e.g. edge recombination including (4,9))', '2. Path-guided mutation for common subpaths', '3. Targeted local search in difficult regions', '4. Diversity preservation (grid-based selection to cover gaps)']}
2025-06-26 18:56:03,667 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:56:03,667 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:56:03,667 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 24875.0
  • mean_cost: 185919.9
- Search-space: ruggedness High (wide edge length range from 11.0 to 7830.0, high std dev in cost_stats), modality Potentially multimodal (high diversity, no clustering info yet), deceptiveness Moderate (long_edge_ratio 0.25 suggests some misleading long edges)
- Evolution phase: Early exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [28, 55, , {'region': [50, 41, ...
- Opportunity regions (# 2): {'potential_nodes': , {'potential_subpaths
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:56:03,671 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:56:03,671 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:56:13,198 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% exploit to anchor promising regions."
}
```
2025-06-26 18:56:13,198 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:56:13,198 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:56:13,198 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% exploit to anchor promising regions."
}
```
2025-06-26 18:56:13,198 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:56:13,198 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 18:56:13,198 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% exploit to anchor promising regions."
}
```
2025-06-26 18:56:13,198 - __main__ - INFO - 开始进化阶段
2025-06-26 18:56:13,198 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:56:13,198 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:56:13,198 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 25047.0 (rank 10.0%)
- Population diversity: 0.7362068965517241
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [28,, {'region': [50,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:56:13,198 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:56:13,198 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:56:26,234 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 12, 18, 22, 25, 29, 33, 37, 41, 45, 49, 52, 55, 58, 57, 54, 51, 48, 44, 40, 36, 32, 28, 24, 20, 16, 13, 9, 5, 1, 2, 6, 10, 14, 17, 21, 26, 30, 34, 38, 42, 46, 50, 53, 56, 59, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 8, 4],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Balanced exploration with spiral pattern avoiding dense clusters"
}
```
2025-06-26 18:56:26,234 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:56:26,234 - ExplorationExpert - INFO - 探索路径生成完成，成本: 80459.0, 路径: [0, 3, 7, 12, 18, 22, 25, 29, 33, 37, 41, 45, 49, 52, 55, 58, 57, 54, 51, 48, 44, 40, 36, 32, 28, 24, 20, 16, 13, 9, 5, 1, 2, 6, 10, 14, 17, 21, 26, 30, 34, 38, 42, 46, 50, 53, 56, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 8, 4]
2025-06-26 18:56:26,234 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 3, 7, 12, 18, 22, 25, 29, 33, 37, 41, 45, 49, 52, 55, 58, 57, 54, 51, 48, 44, 40, 36, 32, 28, 24, 20, 16, 13, 9, 5, 1, 2, 6, 10, 14, 17, 21, 26, 30, 34, 38, 42, 46, 50, 53, 56, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 8, 4], 'cur_cost': 80459.0}
2025-06-26 18:56:26,242 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:56:26,243 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:56:26,243 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 25107.0 (rank 10.0%)
- Population diversity: 0.7844827586206896
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [28,, {'region': [50,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:56:26,243 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:56:26,243 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:56:39,228 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 7, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 1, 4, 8, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 2, 5, 9, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 6, 10, 11],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Balanced exploration with spiral pattern avoiding dense clusters"
}
```
2025-06-26 18:56:39,234 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:56:39,234 - ExplorationExpert - INFO - 探索路径生成完成，成本: 80451.0, 路径: [0, 3, 7, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 1, 4, 8, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 2, 5, 9, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 6, 10, 11]
2025-06-26 18:56:39,234 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 7, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 1, 4, 8, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 2, 5, 9, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 6, 10, 11], 'cur_cost': 80451.0}
2025-06-26 18:56:39,234 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:56:39,234 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:56:39,234 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 24875.0 (rank 0.0%)
- Population diversity: 0.8206896551724138
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [28,, {'region': [50,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:56:39,234 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:56:39,237 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:56:52,039 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 10, 12, 15, 18, 20, 22, 25, 28, 30, 33, 35, 38, 40, 42, 45, 48, 50, 52, 55, 57, 1, 4, 6, 8, 11, 13, 16, 19, 21, 23, 26, 29, 31, 34, 36, 39, 41, 43, 46, 49, 51, 53, 56, 58, 2, 9, 14, 17, 24, 27, 32, 37, 44, 47, 54],
  "targeted_regions": "potential_nod and adjacent sparse cells",
  "strategy_comment": "Alternating clusters to balance exploration and cost"
}
```
2025-06-26 18:56:52,039 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:56:52,039 - ExplorationExpert - INFO - 探索路径生成完成，成本: 72867.0, 路径: [0, 3, 5, 7, 10, 12, 15, 18, 20, 22, 25, 28, 30, 33, 35, 38, 40, 42, 45, 48, 50, 52, 55, 57, 1, 4, 6, 8, 11, 13, 16, 19, 21, 23, 26, 29, 31, 34, 36, 39, 41, 43, 46, 49, 51, 53, 56, 58, 2, 9, 14, 17, 24, 27, 32, 37, 44, 47, 54]
2025-06-26 18:56:52,044 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 10, 12, 15, 18, 20, 22, 25, 28, 30, 33, 35, 38, 40, 42, 45, 48, 50, 52, 55, 57, 1, 4, 6, 8, 11, 13, 16, 19, 21, 23, 26, 29, 31, 34, 36, 39, 41, 43, 46, 49, 51, 53, 56, 58, 2, 9, 14, 17, 24, 27, 32, 37, 44, 47, 54], 'cur_cost': 72867.0}
2025-06-26 18:56:52,044 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:56:52,044 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:56:52,044 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 264235.0 (rank 80.0%)
- Population diversity: 0.803448275862069
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [28,, {'region': [50,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:56:52,044 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:56:52,044 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:57:04,833 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Balanced exploration with structured jumps to increase diversity"
}
```
2025-06-26 18:57:04,833 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:57:04,833 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118896.0, 路径: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54]
2025-06-26 18:57:04,833 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}
2025-06-26 18:57:04,833 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:57:04,833 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:57:04,833 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 270428.0 (rank 90.0%)
- Population diversity: 0.7948275862068965
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [28,, {'region': [50,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:57:04,833 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:57:04,833 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:57:18,186 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 18:57:18,186 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:57:18,186 - ExplorationExpert - INFO - 探索路径生成完成，成本: 279411.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47]
2025-06-26 18:57:18,186 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}
2025-06-26 18:57:18,186 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:57:18,186 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:57:18,186 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 236539.0 (rank 40.0%)
- Population diversity: 0.8086206896551724
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [28,, {'region': [50,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:57:18,186 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:57:18,186 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:57:31,606 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 28, 50],
  "targeted_regions": "Avoided regions 28 and 50, explored adjacent sparse cells",
  "strategy_comment": "Shift difficult regions to end, explore adjacent opportunities"
}
```
2025-06-26 18:57:31,606 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:57:31,606 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40596.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 28, 50]
2025-06-26 18:57:31,606 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 28, 50], 'cur_cost': 40596.0}
2025-06-26 18:57:31,606 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:57:31,606 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:57:31,606 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 255632.0 (rank 70.0%)
- Population diversity: 0.8120689655172414
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [28,, {'region': [50,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:57:31,606 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:57:31,614 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:57:44,742 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54],
  "targeted_regions": "Low-density cells near opportunity regions",
  "strategy_comment": "Balanced exploration with structured jumps"
}
```
2025-06-26 18:57:44,742 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:57:44,742 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118896.0, 路径: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54]
2025-06-26 18:57:44,742 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}
2025-06-26 18:57:44,750 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 18:57:44,750 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:57:44,750 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 252730.0 (rank 70.0%)
- Population diversity: 0.7448275862068966
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [28,, {'region': [50,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:57:44,750 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:57:44,750 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:57:58,896 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 18:57:58,896 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:57:58,896 - ExplorationExpert - INFO - 探索路径生成完成，成本: 279411.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47]
2025-06-26 18:57:58,896 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}
2025-06-26 18:57:58,896 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:57:58,896 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:57:58,896 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:57:58,896 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 258034.0
2025-06-26 18:57:59,398 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 18:57:59,399 - ExploitationExpert - INFO - res_population_costs: [24490, 24475, 24458, 24456, 24456, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451]
2025-06-26 18:57:59,399 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64)]
2025-06-26 18:57:59,404 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:57:59,404 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 12, 18, 22, 25, 29, 33, 37, 41, 45, 49, 52, 55, 58, 57, 54, 51, 48, 44, 40, 36, 32, 28, 24, 20, 16, 13, 9, 5, 1, 2, 6, 10, 14, 17, 21, 26, 30, 34, 38, 42, 46, 50, 53, 56, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 8, 4], 'cur_cost': 80459.0}, {'tour': [0, 3, 7, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 1, 4, 8, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 2, 5, 9, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 6, 10, 11], 'cur_cost': 80451.0}, {'tour': [0, 3, 5, 7, 10, 12, 15, 18, 20, 22, 25, 28, 30, 33, 35, 38, 40, 42, 45, 48, 50, 52, 55, 57, 1, 4, 6, 8, 11, 13, 16, 19, 21, 23, 26, 29, 31, 34, 36, 39, 41, 43, 46, 49, 51, 53, 56, 58, 2, 9, 14, 17, 24, 27, 32, 37, 44, 47, 54], 'cur_cost': 72867.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 28, 50], 'cur_cost': 40596.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': array([20, 10, 50, 13, 16, 31, 38, 19,  1, 36, 14,  4, 25, 43, 47,  8, 35,
       15, 39, 32, 54, 29, 18, 55,  9,  7,  3, 44, 53, 45, 49, 57, 21, 22,
        5, 11, 33, 42, 17, 51, 48,  2, 56, 58,  6, 37, 23, 30, 40, 12, 34,
       41, 26, 52, 28, 24, 27,  0, 46]), 'cur_cost': 258034.0}, {'tour': [32, 42, 47, 16, 1, 30, 13, 48, 18, 57, 40, 25, 26, 53, 19, 44, 51, 23, 5, 37, 55, 56, 29, 22, 33, 41, 24, 4, 14, 9, 38, 46, 11, 6, 10, 21, 20, 49, 0, 7, 43, 58, 45, 17, 34, 36, 8, 52, 15, 2, 31, 35, 39, 54, 3, 28, 27, 12, 50], 'cur_cost': 260271.0}]
2025-06-26 18:57:59,406 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:57:59,406 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 283, 'skip_rate': 0.0353356890459364, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 273, 'cache_hits': 176, 'similarity_calculations': 4120, 'cache_hit_rate': 0.04271844660194175, 'cache_size': 3944}}
2025-06-26 18:57:59,406 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:57:59,406 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:57:59,406 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:57:59,406 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:57:59,407 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 242967.0
2025-06-26 18:57:59,909 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 18:57:59,910 - ExploitationExpert - INFO - res_population_costs: [24490, 24475, 24458, 24456, 24456, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451]
2025-06-26 18:57:59,910 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64)]
2025-06-26 18:57:59,920 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:57:59,920 - ExploitationExpert - INFO - populations: [{'tour': [0, 3, 7, 12, 18, 22, 25, 29, 33, 37, 41, 45, 49, 52, 55, 58, 57, 54, 51, 48, 44, 40, 36, 32, 28, 24, 20, 16, 13, 9, 5, 1, 2, 6, 10, 14, 17, 21, 26, 30, 34, 38, 42, 46, 50, 53, 56, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 8, 4], 'cur_cost': 80459.0}, {'tour': [0, 3, 7, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 1, 4, 8, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 2, 5, 9, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 6, 10, 11], 'cur_cost': 80451.0}, {'tour': [0, 3, 5, 7, 10, 12, 15, 18, 20, 22, 25, 28, 30, 33, 35, 38, 40, 42, 45, 48, 50, 52, 55, 57, 1, 4, 6, 8, 11, 13, 16, 19, 21, 23, 26, 29, 31, 34, 36, 39, 41, 43, 46, 49, 51, 53, 56, 58, 2, 9, 14, 17, 24, 27, 32, 37, 44, 47, 54], 'cur_cost': 72867.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 28, 50], 'cur_cost': 40596.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': array([20, 10, 50, 13, 16, 31, 38, 19,  1, 36, 14,  4, 25, 43, 47,  8, 35,
       15, 39, 32, 54, 29, 18, 55,  9,  7,  3, 44, 53, 45, 49, 57, 21, 22,
        5, 11, 33, 42, 17, 51, 48,  2, 56, 58,  6, 37, 23, 30, 40, 12, 34,
       41, 26, 52, 28, 24, 27,  0, 46]), 'cur_cost': 258034.0}, {'tour': array([ 5, 42, 20, 45, 41,  4, 35,  6, 30,  0,  8, 19, 48, 55,  1, 43, 12,
       40, 32, 24, 16, 47, 10, 11, 28, 38, 22, 46, 53, 58, 37, 13, 34, 25,
       23, 51, 44, 26, 54,  9, 56, 50,  2, 33, 31, 18,  7, 29, 39, 52, 36,
       57, 49, 27, 21, 17,  3, 14, 15]), 'cur_cost': 242967.0}]
2025-06-26 18:57:59,921 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:57:59,922 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 284, 'skip_rate': 0.035211267605633804, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 274, 'cache_hits': 176, 'similarity_calculations': 4121, 'cache_hit_rate': 0.04270808056297015, 'cache_size': 3945}}
2025-06-26 18:57:59,922 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:57:59,922 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 12, 18, 22, 25, 29, 33, 37, 41, 45, 49, 52, 55, 58, 57, 54, 51, 48, 44, 40, 36, 32, 28, 24, 20, 16, 13, 9, 5, 1, 2, 6, 10, 14, 17, 21, 26, 30, 34, 38, 42, 46, 50, 53, 56, 47, 43, 39, 35, 31, 27, 23, 19, 15, 11, 8, 4], 'cur_cost': 80459.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 7, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 1, 4, 8, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 2, 5, 9, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 6, 10, 11], 'cur_cost': 80451.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 10, 12, 15, 18, 20, 22, 25, 28, 30, 33, 35, 38, 40, 42, 45, 48, 50, 52, 55, 57, 1, 4, 6, 8, 11, 13, 16, 19, 21, 23, 26, 29, 31, 34, 36, 39, 41, 43, 46, 49, 51, 53, 56, 58, 2, 9, 14, 17, 24, 27, 32, 37, 44, 47, 54], 'cur_cost': 72867.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 28, 50], 'cur_cost': 40596.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}}]
2025-06-26 18:57:59,923 - __main__ - INFO - 进化阶段完成
2025-06-26 18:57:59,923 - __main__ - INFO - 开始评估阶段
2025-06-26 18:57:59,923 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:57:59,925 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:57:59,925 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 24875.0 → 40596.0 (Δ -15721.0)
- Mean cost: 185919.9 → 157198.8
- Diversity: 0.736 → 0.674 (Δ -0.0621)

#### Elite metrics
- Elite count: 0 → 21
- Best elite cost: 24875.0 → 24451 (Δ 424.0)

#### Strategy performance
- Explore: 8 inds, success 37.5% , avg Δ 35450.75
- Exploit: 2 inds, success 50.0% , avg Δ 1802.5

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [14860.0, 14860.0, 14860.0], diversity [0.8436363636363637, 0.9333333333333335, 0.8092929292929294]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:57:59,926 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:58:11,123 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 37.5,
      "average_improvement": 35450.75,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_improvement": 1802.5,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy but inefficient, exploit underutilized",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_intensity": "increase mutation range by 20%"
  }
}
```
2025-06-26 18:58:11,150 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:58:11,150 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 37.5,
      "average_improvement": 35450.75,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_improvement": 1802.5,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy but inefficient, exploit underutilized",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_intensity": "increase mutation range by 20%"
  }
}
```
2025-06-26 18:58:11,150 - __main__ - INFO - 评估阶段完成
2025-06-26 18:58:11,150 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 37.5,
      "average_improvement": 35450.75,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_improvement": 1802.5,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy but inefficient, exploit underutilized",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_intensity": "increase mutation range by 20%"
  }
}
```
2025-06-26 18:58:11,150 - __main__ - INFO - 当前最佳适应度: 40596.0
2025-06-26 18:58:11,152 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite11_59_route_0.pkl
2025-06-26 18:58:11,152 - __main__ - INFO - composite11_59 开始进化第 2 代
2025-06-26 18:58:11,152 - __main__ - INFO - 开始分析阶段
2025-06-26 18:58:11,152 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:58:11,168 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 40596.0, 'max': 279411.0, 'mean': 157198.8, 'std': 91003.87985772915}, 'diversity': 0.9175141242937854, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 1, 2, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:58:11,168 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 40596.0, 'max': 279411.0, 'mean': 157198.8, 'std': 91003.87985772915}, 'diversity_level': 0.9175141242937854, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [1, 1, 1, 2, 2, 1, 1, 1]}, 'coordinates': [[3890, 3658], [3945, 3639], [3889, 3685], [3906, 3671], [3923, 3656], [3920, 3680], [3937, 3621], [3958, 3653], [3943, 3673], [3911, 3649], [3899, 3621], [6525, 868], [6531, 888], [6591, 942], [6618, 929], [6548, 851], [6574, 854], [6559, 874], [6566, 942], [6530, 940], [6620, 894], [6550, 903], [6584, 917], [1472, 1211], [1423, 1273], [1442, 1244], [1468, 1281], [1500, 1235], [1466, 1296], [1426, 1232], [1450, 1264], [1492, 1248], [1428, 1251], [1497, 1264], [1494, 1225], [919, 6136], [910, 6189], [925, 6186], [901, 6225], [883, 6136], [863, 6149], [879, 6219], [889, 6175], [933, 6194], [927, 6171], [866, 6199], [908, 6153], [6619, 6506], [6551, 6515], [6568, 6446], [6535, 6492], [6603, 6457], [6565, 6513], [6532, 6513], [6541, 6471], [6603, 6486], [6596, 6495], [6584, 6473], [6556, 6526]], 'distance_matrix': array([[   0.,   58.,   27., ..., 3921., 3896., 3916.],
       [  58.,    0.,   72., ..., 3897., 3872., 3893.],
       [  27.,   72.,    0., ..., 3902., 3878., 3897.],
       ...,
       [3921., 3897., 3902., ...,    0.,   25.,   51.],
       [3896., 3872., 3878., ...,   25.,    0.,   60.],
       [3916., 3893., 3897., ...,   51.,   60.,    0.]])}
2025-06-26 18:58:11,170 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:58:11,170 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:58:11,170 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:58:11,172 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:58:11,172 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:58:11,173 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:58:11,173 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:58:11,173 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:58:11,173 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:58:11,180 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:58:11,180 - __main__ - INFO - 精英专家分析报告: {'elite_count': 21, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 16145.0, 'avg_gap': 132743.99047619046}, 'structure_gap': {'unique_elite_edges': 136, 'unique_pop_edges': 363, 'common_edges': 51}}, 'elite_diversity': {'diversity_score': 0.20193704600484252}}
2025-06-26 18:58:11,180 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:58:11,180 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:58:11,183 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:58:11,184 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 40596.0, mean 157198.8, max 279411.0, std 91003.87985772915
- diversity: 0.9175141242937854
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [14860.0, 14860.0, 40596.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [863.0, 851.0, 6620.0, 6526.0]
- centroid/spread: [3883.576, 3695.881] | rms 3403.363
- density_grid(3x3): [[12, 0, 12], [0, 11, 0], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [11.0, 3466.0, 4901.0, 5640.0, 7830.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [3, 7, 8, 8, 7, 6, 12, 7]
- nn_median_dist: 20.881

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:58:11,184 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:58:30,971 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "potentially multimodal",
    "deceptiveness": "low",
    "edge_length_variability": "high (min 11.0, max 7830.0)",
    "long_edge_prevalence": "25% of edges are long"
  },
  "population_state": {
    "coverage": "uneven (clustered in corners with central void)",
    "diversity": "high (0.92)",
    "convergence": "none detected (0.0)",
    "elite_influence": "minimal (no elites yet)"
  },
  "difficult_regions": {
    "central_corridor": "low node density with long edges (q3 edge length 5640.0)",
    "north_south_axis": "sparse directional distribution in histogram sectors"
  },
  "opportunity_regions": {
    "corner_clusters": "high density (12 nodes each in 4/9 grid cells)",
    "short_edge_zones": "11.0min edge length suggests exploitable local optima"
  },
  "evolution_phase": "early exploration",
  "evolution_direction": [
    "Prioritize exploitation in corner clusters with 2-opt/local search",
    "Add targeted diversification via long-edge mutation in central corridor",
    "Implement direction-aware crossover using sector histogram biases",
    "Introduce distance-based selection pressure (nn_median_dist 20.88)"
  ]
}
```
2025-06-26 18:58:30,977 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:58:30,977 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high (min 11.0, max 7830.0)', 'long_edge_prevalence': '25% of edges are long'}, 'population_state': {'coverage': 'uneven (clustered in corners with central void)', 'diversity': 'high (0.92)', 'convergence': 'none detected (0.0)', 'elite_influence': 'minimal (no elites yet)'}, 'difficult_regions': {'central_corridor': 'low node density with long edges (q3 edge length 5640.0)', 'north_south_axis': 'sparse directional distribution in histogram sectors'}, 'opportunity_regions': {'corner_clusters': 'high density (12 nodes each in 4/9 grid cells)', 'short_edge_zones': '11.0min edge length suggests exploitable local optima'}, 'evolution_phase': 'early exploration', 'evolution_direction': ['Prioritize exploitation in corner clusters with 2-opt/local search', 'Add targeted diversification via long-edge mutation in central corridor', 'Implement direction-aware crossover using sector histogram biases', 'Introduce distance-based selection pressure (nn_median_dist 20.88)']}
2025-06-26 18:58:30,977 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:58:30,977 - __main__ - INFO - 分析阶段完成
2025-06-26 18:58:30,977 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high (min 11.0, max 7830.0)', 'long_edge_prevalence': '25% of edges are long'}, 'population_state': {'coverage': 'uneven (clustered in corners with central void)', 'diversity': 'high (0.92)', 'convergence': 'none detected (0.0)', 'elite_influence': 'minimal (no elites yet)'}, 'difficult_regions': {'central_corridor': 'low node density with long edges (q3 edge length 5640.0)', 'north_south_axis': 'sparse directional distribution in histogram sectors'}, 'opportunity_regions': {'corner_clusters': 'high density (12 nodes each in 4/9 grid cells)', 'short_edge_zones': '11.0min edge length suggests exploitable local optima'}, 'evolution_phase': 'early exploration', 'evolution_direction': ['Prioritize exploitation in corner clusters with 2-opt/local search', 'Add targeted diversification via long-edge mutation in central corridor', 'Implement direction-aware crossover using sector histogram biases', 'Introduce distance-based selection pressure (nn_median_dist 20.88)']}
2025-06-26 18:58:30,977 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:58:30,977 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:58:30,977 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 40596.0
  • mean_cost: 157198.8
- Search-space: ruggedness moderate, modality potentially multimodal, deceptiveness low
- Evolution phase: early exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): central_corridor, north_south_axis
- Opportunity regions (# 2): corner_clusters, short_edge_zones
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:58:30,977 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:58:30,977 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:58:40,641 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration; top 30% exploit to refine promising solutions."
}
```
2025-06-26 18:58:40,641 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:58:40,641 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:58:40,641 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration; top 30% exploit to refine promising solutions."
}
```
2025-06-26 18:58:40,641 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:58:40,641 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:58:40,641 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early exploration phase favor exploration; top 30% exploit to refine promising solutions."
}
```
2025-06-26 18:58:40,641 - __main__ - INFO - 开始进化阶段
2025-06-26 18:58:40,641 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:58:40,641 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:58:40,641 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 80459.0 (rank 30.0%)
- Population diversity: 0.6741379310344827
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): corner_clusters, short_edge_zone
- Difficult regions to avoid (sample): central_corrido, north_south_axi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:58:40,641 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:58:40,641 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:58:53,452 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "corner_clusters, short_edge_zone",
  "strategy_comment": "Prioritize corner clusters and short edges while avoiding central corridor"
}
```
2025-06-26 18:58:53,452 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:58:53,452 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 18:58:53,456 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 18:58:53,456 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:58:53,456 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:58:53,456 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 80451.0 (rank 30.0%)
- Population diversity: 0.6258620689655172
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): corner_clusters, short_edge_zone
- Difficult regions to avoid (sample): central_corrido, north_south_axi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:58:53,456 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:58:53,456 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:59:07,014 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "corner_clusters, short_edge_zone",
  "strategy_comment": "Prioritize corner clusters and edge zones while avoiding central corridor"
}
```
2025-06-26 18:59:07,023 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:59:07,023 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 18:59:07,023 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 18:59:07,023 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:59:07,023 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:59:07,025 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 72867.0 (rank 30.0%)
- Population diversity: 0.5793103448275863
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): corner_clusters, short_edge_zone
- Difficult regions to avoid (sample): central_corrido, north_south_axi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:59:07,025 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:59:07,025 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:59:20,467 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "corner_clusters, short_edge_zone",
  "strategy_comment": "Sequential exploration focusing on corners and edges"
}
```
2025-06-26 18:59:20,467 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:59:20,467 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 18:59:20,475 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 18:59:20,475 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:59:20,475 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:59:20,475 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 118896.0 (rank 40.0%)
- Population diversity: 0.49137931034482757
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): corner_clusters, short_edge_zone
- Difficult regions to avoid (sample): central_corrido, north_south_axi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:59:20,475 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:59:20,475 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:59:33,595 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "corner_clusters, short_edge_zone",
  "strategy_comment": "Prioritize corner clusters and edge zones while avoiding central areas"
}
```
2025-06-26 18:59:33,595 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:59:33,595 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 18:59:33,595 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 18:59:33,595 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:59:33,595 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:59:33,595 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 279411.0 (rank 80.0%)
- Population diversity: 0.49137931034482757
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): corner_clusters, short_edge_zone
- Difficult regions to avoid (sample): central_corrido, north_south_axi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:59:33,595 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:59:33,595 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:59:47,407 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "corner_clusters, short_edge_zone",
  "strategy_comment": "Prioritize corner clusters and edge zones while avoiding central areas"
}
```
2025-06-26 18:59:47,407 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:59:47,407 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 18:59:47,412 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 18:59:47,412 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:59:47,412 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:59:47,412 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 40596.0 (rank 50.0%)
- Population diversity: 0.49137931034482757
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): corner_clusters, short_edge_zone
- Difficult regions to avoid (sample): central_corrido, north_south_axi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:59:47,412 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:59:47,412 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:00:00,735 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "corner_clusters, short_edge_zone",
  "strategy_comment": "Prioritize corner clusters and edge zones, avoid central corridor"
}
```
2025-06-26 19:00:00,735 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:00:00,735 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 19:00:00,743 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 19:00:00,744 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 19:00:00,744 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:00:00,744 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 118896.0 (rank 60.0%)
- Population diversity: 0.4844827586206897
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): corner_clusters, short_edge_zone
- Difficult regions to avoid (sample): central_corrido, north_south_axi
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:00:00,745 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:00:00,746 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:00:14,280 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "corner_clusters, short_edge_zone",
  "strategy_comment": "Prioritize corner clusters and edge zones, avoid central corridor"
}
```
2025-06-26 19:00:14,280 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:00:14,280 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 19:00:14,289 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 19:00:14,289 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:00:14,289 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:00:14,289 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:00:14,289 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 222936.0
2025-06-26 19:00:14,790 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:00:14,791 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:00:14,791 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:00:14,799 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:00:14,799 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': array([23, 27,  3,  2, 26, 25, 43,  5, 52, 47, 57, 45, 35, 48, 58,  6, 10,
       46, 37, 50,  8, 14, 15, 17, 28, 51, 42, 32, 12, 36, 16, 41, 38, 29,
        7, 19,  1, 31, 22, 21, 33, 11, 44, 30, 54, 53, 13, 56,  4, 20, 18,
       34, 49,  0, 55, 24, 39,  9, 40]), 'cur_cost': 222936.0}, {'tour': array([20, 10, 50, 13, 16, 31, 38, 19,  1, 36, 14,  4, 25, 43, 47,  8, 35,
       15, 39, 32, 54, 29, 18, 55,  9,  7,  3, 44, 53, 45, 49, 57, 21, 22,
        5, 11, 33, 42, 17, 51, 48,  2, 56, 58,  6, 37, 23, 30, 40, 12, 34,
       41, 26, 52, 28, 24, 27,  0, 46]), 'cur_cost': 258034.0}, {'tour': array([ 5, 42, 20, 45, 41,  4, 35,  6, 30,  0,  8, 19, 48, 55,  1, 43, 12,
       40, 32, 24, 16, 47, 10, 11, 28, 38, 22, 46, 53, 58, 37, 13, 34, 25,
       23, 51, 44, 26, 54,  9, 56, 50,  2, 33, 31, 18,  7, 29, 39, 52, 36,
       57, 49, 27, 21, 17,  3, 14, 15]), 'cur_cost': 242967.0}]
2025-06-26 19:00:14,802 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:00:14,802 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 285, 'skip_rate': 0.03508771929824561, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 275, 'cache_hits': 176, 'similarity_calculations': 4123, 'cache_hit_rate': 0.04268736357021586, 'cache_size': 3947}}
2025-06-26 19:00:14,802 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:00:14,802 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:00:14,803 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:00:14,803 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:00:14,803 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 273981.0
2025-06-26 19:00:15,306 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:00:15,307 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:00:15,307 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:00:15,316 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:00:15,317 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': array([23, 27,  3,  2, 26, 25, 43,  5, 52, 47, 57, 45, 35, 48, 58,  6, 10,
       46, 37, 50,  8, 14, 15, 17, 28, 51, 42, 32, 12, 36, 16, 41, 38, 29,
        7, 19,  1, 31, 22, 21, 33, 11, 44, 30, 54, 53, 13, 56,  4, 20, 18,
       34, 49,  0, 55, 24, 39,  9, 40]), 'cur_cost': 222936.0}, {'tour': array([10, 53, 37, 18, 33, 58, 22, 21, 57, 35, 45, 17, 31, 23, 28, 41, 11,
       36,  4, 13,  7, 38, 34, 55, 32, 14, 27,  5,  9,  1, 26,  6, 47, 51,
       42, 20, 29, 12, 39, 52, 54, 46,  3, 44, 24, 16, 49, 25, 56, 30, 48,
       19,  2, 15,  8, 50, 43,  0, 40]), 'cur_cost': 273981.0}, {'tour': array([ 5, 42, 20, 45, 41,  4, 35,  6, 30,  0,  8, 19, 48, 55,  1, 43, 12,
       40, 32, 24, 16, 47, 10, 11, 28, 38, 22, 46, 53, 58, 37, 13, 34, 25,
       23, 51, 44, 26, 54,  9, 56, 50,  2, 33, 31, 18,  7, 29, 39, 52, 36,
       57, 49, 27, 21, 17,  3, 14, 15]), 'cur_cost': 242967.0}]
2025-06-26 19:00:15,317 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:00:15,319 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 286, 'skip_rate': 0.03496503496503497, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 276, 'cache_hits': 176, 'similarity_calculations': 4126, 'cache_hit_rate': 0.04265632573921473, 'cache_size': 3950}}
2025-06-26 19:00:15,319 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:00:15,319 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:00:15,320 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:00:15,320 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:00:15,320 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 244415.0
2025-06-26 19:00:15,823 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:00:15,823 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:00:15,823 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:00:15,833 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:00:15,833 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': array([23, 27,  3,  2, 26, 25, 43,  5, 52, 47, 57, 45, 35, 48, 58,  6, 10,
       46, 37, 50,  8, 14, 15, 17, 28, 51, 42, 32, 12, 36, 16, 41, 38, 29,
        7, 19,  1, 31, 22, 21, 33, 11, 44, 30, 54, 53, 13, 56,  4, 20, 18,
       34, 49,  0, 55, 24, 39,  9, 40]), 'cur_cost': 222936.0}, {'tour': array([10, 53, 37, 18, 33, 58, 22, 21, 57, 35, 45, 17, 31, 23, 28, 41, 11,
       36,  4, 13,  7, 38, 34, 55, 32, 14, 27,  5,  9,  1, 26,  6, 47, 51,
       42, 20, 29, 12, 39, 52, 54, 46,  3, 44, 24, 16, 49, 25, 56, 30, 48,
       19,  2, 15,  8, 50, 43,  0, 40]), 'cur_cost': 273981.0}, {'tour': array([13, 32,  1, 24, 44,  9, 28, 56, 38, 37, 53, 11, 48, 45, 42, 17, 10,
       41, 54, 25,  8, 31,  2,  7, 12, 21,  5, 19, 55, 27, 34,  4,  6, 14,
       30, 58, 36,  3, 22, 23, 26, 51,  0, 16, 20, 29, 50, 33, 57, 40, 18,
       35, 46, 43, 47, 52, 49, 39, 15]), 'cur_cost': 244415.0}]
2025-06-26 19:00:15,835 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:00:15,836 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 287, 'skip_rate': 0.03484320557491289, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 277, 'cache_hits': 176, 'similarity_calculations': 4130, 'cache_hit_rate': 0.04261501210653753, 'cache_size': 3954}}
2025-06-26 19:00:15,836 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:00:15,836 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}]
2025-06-26 19:00:15,836 - __main__ - INFO - 进化阶段完成
2025-06-26 19:00:15,837 - __main__ - INFO - 开始评估阶段
2025-06-26 19:00:15,837 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:00:15,838 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:00:15,838 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 40596.0 → 26245.0 (Δ 14351.0)
- Mean cost: 157198.8 → 92504.7
- Diversity: 0.674 → 0.374 (Δ -0.3)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 24451 → 24451 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 100.0% , avg Δ 86837.29
- Exploit: 3 inds, success 33.3% , avg Δ 13026.67

#### Other indicators
- No-change individuals: 0
- Regression individuals: 2
- Historical trends: Last 3 iterations: costs [14860.0, 14860.0, 40596.0], diversity [0.9333333333333335, 0.8092929292929294, 0.9175141242937854]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:00:15,838 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:00:30,371 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "100% success rate with large average cost reduction (Δ 86837.29)"
    },
    "exploit": {
      "contribution": "low",
      "reason": "only 33.3% success rate with modest average improvement (Δ 13026.67)"
    }
  },
  "balance_state": {
    "current_ratio": "70/30 explore/exploit",
    "assessment": "explore-heavy but effective given current performance",
    "needs_adjustment": false
  },
  "recommendations": {
    "parameter_changes": {
      "explore_ratio": "maintain at 0.7",
      "diversity_weight": "increase to 0.7",
      "mutation_intensity": "consider slight increase for exploit operations"
    },
    "rationale": "While exploration is highly effective, diversity has dropped significantly (-0.3). Increasing diversity weight will help maintain genetic variety without sacrificing current optimization momentum."
  }
}
```
2025-06-26 19:00:30,389 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:00:30,389 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "100% success rate with large average cost reduction (Δ 86837.29)"
    },
    "exploit": {
      "contribution": "low",
      "reason": "only 33.3% success rate with modest average improvement (Δ 13026.67)"
    }
  },
  "balance_state": {
    "current_ratio": "70/30 explore/exploit",
    "assessment": "explore-heavy but effective given current performance",
    "needs_adjustment": false
  },
  "recommendations": {
    "parameter_changes": {
      "explore_ratio": "maintain at 0.7",
      "diversity_weight": "increase to 0.7",
      "mutation_intensity": "consider slight increase for exploit operations"
    },
    "rationale": "While exploration is highly effective, diversity has dropped significantly (-0.3). Increasing diversity weight will help maintain genetic variety without sacrificing current optimization momentum."
  }
}
```
2025-06-26 19:00:30,389 - __main__ - INFO - 评估阶段完成
2025-06-26 19:00:30,389 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "100% success rate with large average cost reduction (Δ 86837.29)"
    },
    "exploit": {
      "contribution": "low",
      "reason": "only 33.3% success rate with modest average improvement (Δ 13026.67)"
    }
  },
  "balance_state": {
    "current_ratio": "70/30 explore/exploit",
    "assessment": "explore-heavy but effective given current performance",
    "needs_adjustment": false
  },
  "recommendations": {
    "parameter_changes": {
      "explore_ratio": "maintain at 0.7",
      "diversity_weight": "increase to 0.7",
      "mutation_intensity": "consider slight increase for exploit operations"
    },
    "rationale": "While exploration is highly effective, diversity has dropped significantly (-0.3). Increasing diversity weight will help maintain genetic variety without sacrificing current optimization momentum."
  }
}
```
2025-06-26 19:00:30,389 - __main__ - INFO - 当前最佳适应度: 26245.0
2025-06-26 19:00:30,396 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite11_59_route_1.pkl
2025-06-26 19:00:30,397 - __main__ - INFO - composite11_59 开始进化第 3 代
2025-06-26 19:00:30,397 - __main__ - INFO - 开始分析阶段
2025-06-26 19:00:30,397 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:00:30,409 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 26245.0, 'max': 273981.0, 'mean': 92504.7, 'std': 101860.26979941687}, 'diversity': 0.5092278719397363, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:00:30,410 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 26245.0, 'max': 273981.0, 'mean': 92504.7, 'std': 101860.26979941687}, 'diversity_level': 0.5092278719397363, 'convergence_level': 0.0, 'clustering_info': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'coordinates': [[3890, 3658], [3945, 3639], [3889, 3685], [3906, 3671], [3923, 3656], [3920, 3680], [3937, 3621], [3958, 3653], [3943, 3673], [3911, 3649], [3899, 3621], [6525, 868], [6531, 888], [6591, 942], [6618, 929], [6548, 851], [6574, 854], [6559, 874], [6566, 942], [6530, 940], [6620, 894], [6550, 903], [6584, 917], [1472, 1211], [1423, 1273], [1442, 1244], [1468, 1281], [1500, 1235], [1466, 1296], [1426, 1232], [1450, 1264], [1492, 1248], [1428, 1251], [1497, 1264], [1494, 1225], [919, 6136], [910, 6189], [925, 6186], [901, 6225], [883, 6136], [863, 6149], [879, 6219], [889, 6175], [933, 6194], [927, 6171], [866, 6199], [908, 6153], [6619, 6506], [6551, 6515], [6568, 6446], [6535, 6492], [6603, 6457], [6565, 6513], [6532, 6513], [6541, 6471], [6603, 6486], [6596, 6495], [6584, 6473], [6556, 6526]], 'distance_matrix': array([[   0.,   58.,   27., ..., 3921., 3896., 3916.],
       [  58.,    0.,   72., ..., 3897., 3872., 3893.],
       [  27.,   72.,    0., ..., 3902., 3878., 3897.],
       ...,
       [3921., 3897., 3902., ...,    0.,   25.,   51.],
       [3896., 3872., 3878., ...,   25.,    0.,   60.],
       [3916., 3893., 3897., ...,   51.,   60.,    0.]])}
2025-06-26 19:00:30,410 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:00:30,411 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:00:30,411 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:00:30,413 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 19:00:30,413 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:00:30,413 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 19:00:30,413 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:00:30,414 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:00:30,414 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:00:30,421 - EliteExpert - INFO - 精英解分析完成
2025-06-26 19:00:30,421 - __main__ - INFO - 精英专家分析报告: {'elite_count': 21, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1794.0, 'avg_gap': 68049.89047619047}, 'structure_gap': {'unique_elite_edges': 162, 'unique_pop_edges': 206, 'common_edges': 25}}, 'elite_diversity': {'diversity_score': 0.20193704600484252}}
2025-06-26 19:00:30,421 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:00:30,421 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:00:30,424 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:00:30,424 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 26245.0, mean 92504.7, max 273981.0, std 101860.26979941687
- diversity: 0.5092278719397363
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [14860.0, 40596.0, 26245.0] (Δ 7175.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [863.0, 851.0, 6620.0, 6526.0]
- centroid/spread: [3883.576, 3695.881] | rms 3403.363
- density_grid(3x3): [[12, 0, 12], [0, 11, 0], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [11.0, 3466.0, 4901.0, 5640.0, 7830.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [3, 7, 8, 8, 7, 6, 12, 7]
- nn_median_dist: 20.881

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:00:30,425 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:00:53,688 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "multimodal",
    "deceptiveness": "low",
    "edge_length_variability": "high",
    "long_edge_presence": "moderate"
  },
  "population_state": {
    "coverage": "partial",
    "convergence": "none",
    "diversity": "moderate",
    "spatial_distribution": "clustered",
    "potential_stagnation": "none"
  },
  "difficult_regions": [
    {
      "type": "long_edge_corridor",
      "characteristics": ["long edges", "low density"],
      "nodes_involved": []
    },
    {
      "type": "low_density_cell",
      "characteristics": ["sparse node distribution"],
      "cells": [[1,0], [1,2], [0,1], [2,1]]
    }
  ],
  "opportunity_regions": [
    {
      "type": "high_density_cell",
      "characteristics": ["frequent node visits"],
      "cells": [[0,0], [0,2], [2,0], [2,2]]
    },
    {
      "type": "short_edge_cluster",
      "characteristics": ["high edge concentration", "compact node distribution"],
      "nodes_involved": []
    }
  ],
  "evolution_phase": "early_exploration",
  "evolution_direction": {
    "suggested_operators": [
      "edge_recombination",
      "local_search_around_high_density_cells",
      "long_edge_avoidance_mutation",
      "diversity_preservation_selection"
    ],
    "focus_areas": [
      "exploit high-density clusters",
      "improve connectivity in sparse regions",
      "maintain population diversity"
    ]
  }
}
```
2025-06-26 19:00:53,688 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:00:53,688 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high', 'long_edge_presence': 'moderate'}, 'population_state': {'coverage': 'partial', 'convergence': 'none', 'diversity': 'moderate', 'spatial_distribution': 'clustered', 'potential_stagnation': 'none'}, 'difficult_regions': [{'type': 'long_edge_corridor', 'characteristics': ['long edges', 'low density'], 'nodes_involved': []}, {'type': 'low_density_cell', 'characteristics': ['sparse node distribution'], 'cells': [[1, 0], [1, 2], [0, 1], [2, 1]]}], 'opportunity_regions': [{'type': 'high_density_cell', 'characteristics': ['frequent node visits'], 'cells': [[0, 0], [0, 2], [2, 0], [2, 2]]}, {'type': 'short_edge_cluster', 'characteristics': ['high edge concentration', 'compact node distribution'], 'nodes_involved': []}], 'evolution_phase': 'early_exploration', 'evolution_direction': {'suggested_operators': ['edge_recombination', 'local_search_around_high_density_cells', 'long_edge_avoidance_mutation', 'diversity_preservation_selection'], 'focus_areas': ['exploit high-density clusters', 'improve connectivity in sparse regions', 'maintain population diversity']}}
2025-06-26 19:00:53,688 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:00:53,688 - __main__ - INFO - 分析阶段完成
2025-06-26 19:00:53,688 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high', 'long_edge_presence': 'moderate'}, 'population_state': {'coverage': 'partial', 'convergence': 'none', 'diversity': 'moderate', 'spatial_distribution': 'clustered', 'potential_stagnation': 'none'}, 'difficult_regions': [{'type': 'long_edge_corridor', 'characteristics': ['long edges', 'low density'], 'nodes_involved': []}, {'type': 'low_density_cell', 'characteristics': ['sparse node distribution'], 'cells': [[1, 0], [1, 2], [0, 1], [2, 1]]}], 'opportunity_regions': [{'type': 'high_density_cell', 'characteristics': ['frequent node visits'], 'cells': [[0, 0], [0, 2], [2, 0], [2, 2]]}, {'type': 'short_edge_cluster', 'characteristics': ['high edge concentration', 'compact node distribution'], 'nodes_involved': []}], 'evolution_phase': 'early_exploration', 'evolution_direction': {'suggested_operators': ['edge_recombination', 'local_search_around_high_density_cells', 'long_edge_avoidance_mutation', 'diversity_preservation_selection'], 'focus_areas': ['exploit high-density clusters', 'improve connectivity in sparse regions', 'maintain population diversity']}}
2025-06-26 19:00:53,688 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:00:53,688 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:00:53,688 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 26245.0
  • mean_cost: 92504.7
- Search-space: ruggedness moderate, modality multimodal, deceptiveness low
- Evolution phase: early_exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'long_edge_, {'type': 'low_densit
- Opportunity regions (# 2): {'type': 'high_densi, {'type': 'short_edge
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribut...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:00:53,688 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:00:53,688 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:01:03,044 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early_exploration phase favor exploration. Top 30% individuals exploit to refine solutions."
}
```
2025-06-26 19:01:03,044 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:01:03,044 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:01:03,044 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early_exploration phase favor exploration. Top 30% individuals exploit to refine solutions."
}
```
2025-06-26 19:01:03,044 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:01:03,044 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:01:03,044 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early_exploration phase favor exploration. Top 30% individuals exploit to refine solutions."
}
```
2025-06-26 19:01:03,044 - __main__ - INFO - 开始进化阶段
2025-06-26 19:01:03,044 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:01:03,044 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:01:03,044 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26245.0 (rank 0.0%)
- Population diversity: 0.3741379310344828
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high_, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long_, {'type': 'low_d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:01:03,044 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:01:03,044 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:01:16,083 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47],
  "targeted_regions": "high_density and low-density adjacent",
  "strategy_comment": "Structured leapfrog to explore sparse cells"
}
```
2025-06-26 19:01:16,083 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:01:16,083 - ExplorationExpert - INFO - 探索路径生成完成，成本: 279411.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47]
2025-06-26 19:01:16,084 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}
2025-06-26 19:01:16,084 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:01:16,084 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:01:16,084 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26245.0 (rank 0.0%)
- Population diversity: 0.4672413793103448
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high_, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long_, {'type': 'low_d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:01:16,084 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:01:16,084 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:01:29,358 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47],
  "targeted_regions": "low-density cells adjacent to opportunity regions",
  "strategy_comment": "Structured leapfrog pattern to explore sparse areas"
}
```
2025-06-26 19:01:29,358 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:01:29,358 - ExplorationExpert - INFO - 探索路径生成完成，成本: 279411.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47]
2025-06-26 19:01:29,358 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}
2025-06-26 19:01:29,358 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:01:29,358 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:01:29,358 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26245.0 (rank 0.0%)
- Population diversity: 0.4672413793103448
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high_, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long_, {'type': 'low_d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:01:29,366 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:01:29,366 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:01:42,678 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "high_density and low_density",
  "strategy_comment": "Balanced exploration of opportunity regions"
}
```
2025-06-26 19:01:42,678 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:01:42,678 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 19:01:42,678 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 19:01:42,678 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:01:42,678 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:01:42,678 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26245.0 (rank 0.0%)
- Population diversity: 0.4672413793103448
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high_, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long_, {'type': 'low_d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:01:42,678 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:01:42,678 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:01:55,606 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "high_opportunity and low-density adjacent",
  "strategy_comment": "Sequential exploration with focus on sparse areas"
}
```
2025-06-26 19:01:55,606 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:01:55,606 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 19:01:55,606 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 19:01:55,606 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:01:55,606 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:01:55,606 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26245.0 (rank 0.0%)
- Population diversity: 0.4672413793103448
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high_, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long_, {'type': 'low_d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:01:55,606 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:01:55,606 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:02:08,608 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "high_opportunity and low-density adjacent",
  "strategy_comment": "Sequential exploration with opportunistic detours"
}
```
2025-06-26 19:02:08,608 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:02:08,608 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 19:02:08,608 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 19:02:08,608 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:02:08,608 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:02:08,608 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26245.0 (rank 0.0%)
- Population diversity: 0.4672413793103448
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high_, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long_, {'type': 'low_d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:02:08,608 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:02:08,608 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:02:22,039 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "high_density and low_density adjacent cells",
  "strategy_comment": "Alternate high-low density traversal for diversity"
}
```
2025-06-26 19:02:22,042 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:02:22,042 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41975.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 19:02:22,042 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 41975.0}
2025-06-26 19:02:22,042 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 19:02:22,042 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:02:22,042 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26245.0 (rank 0.0%)
- Population diversity: 0.5568965517241379
- Evolution phase: early_exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'high_, {'type': 'short
- Difficult regions to avoid (sample): {'type': 'long_, {'type': 'low_d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:02:22,042 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:02:22,042 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:02:34,981 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54],
  "targeted_regions": "high_opportunity and low-density cells",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 19:02:34,981 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:02:34,981 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118896.0, 路径: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54]
2025-06-26 19:02:34,981 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}
2025-06-26 19:02:34,981 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:02:34,981 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:02:34,981 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:02:34,981 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 267963.0
2025-06-26 19:02:35,483 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:02:35,483 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:02:35,483 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:02:35,493 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:02:35,494 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 41975.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}, {'tour': array([18,  1,  3, 20, 56, 21, 11, 39, 45,  2, 19,  4, 54, 46, 29,  6, 24,
       25, 41, 15, 57, 40,  0, 32, 34, 17,  5, 44, 58, 36, 43,  8, 12, 23,
       47, 28, 48, 35, 10, 42, 16, 50, 30, 55, 27, 52, 22, 53,  9, 49, 51,
       26, 38, 13, 31,  7, 37, 33, 14]), 'cur_cost': 267963.0}, {'tour': array([10, 53, 37, 18, 33, 58, 22, 21, 57, 35, 45, 17, 31, 23, 28, 41, 11,
       36,  4, 13,  7, 38, 34, 55, 32, 14, 27,  5,  9,  1, 26,  6, 47, 51,
       42, 20, 29, 12, 39, 52, 54, 46,  3, 44, 24, 16, 49, 25, 56, 30, 48,
       19,  2, 15,  8, 50, 43,  0, 40]), 'cur_cost': 273981.0}, {'tour': array([13, 32,  1, 24, 44,  9, 28, 56, 38, 37, 53, 11, 48, 45, 42, 17, 10,
       41, 54, 25,  8, 31,  2,  7, 12, 21,  5, 19, 55, 27, 34,  4,  6, 14,
       30, 58, 36,  3, 22, 23, 26, 51,  0, 16, 20, 29, 50, 33, 57, 40, 18,
       35, 46, 43, 47, 52, 49, 39, 15]), 'cur_cost': 244415.0}]
2025-06-26 19:02:35,495 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:02:35,495 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 288, 'skip_rate': 0.034722222222222224, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 278, 'cache_hits': 176, 'similarity_calculations': 4135, 'cache_hit_rate': 0.04256348246674728, 'cache_size': 3959}}
2025-06-26 19:02:35,495 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:02:35,497 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:02:35,497 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:02:35,497 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:02:35,497 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 263409.0
2025-06-26 19:02:36,000 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:02:36,000 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:02:36,000 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:02:36,009 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:02:36,009 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 41975.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}, {'tour': array([18,  1,  3, 20, 56, 21, 11, 39, 45,  2, 19,  4, 54, 46, 29,  6, 24,
       25, 41, 15, 57, 40,  0, 32, 34, 17,  5, 44, 58, 36, 43,  8, 12, 23,
       47, 28, 48, 35, 10, 42, 16, 50, 30, 55, 27, 52, 22, 53,  9, 49, 51,
       26, 38, 13, 31,  7, 37, 33, 14]), 'cur_cost': 267963.0}, {'tour': array([24, 54, 13, 34,  3, 15, 45, 11, 43, 48, 51,  9, 32, 17,  5,  8, 28,
       36, 47,  0, 42, 52, 58,  4, 26, 57, 33, 40, 14, 19, 22, 20, 10, 46,
       44, 35,  2, 37, 55, 23,  6, 12, 29,  7, 50, 27, 38, 31, 56, 18, 30,
       25, 53,  1, 39, 21, 49, 16, 41]), 'cur_cost': 263409.0}, {'tour': array([13, 32,  1, 24, 44,  9, 28, 56, 38, 37, 53, 11, 48, 45, 42, 17, 10,
       41, 54, 25,  8, 31,  2,  7, 12, 21,  5, 19, 55, 27, 34,  4,  6, 14,
       30, 58, 36,  3, 22, 23, 26, 51,  0, 16, 20, 29, 50, 33, 57, 40, 18,
       35, 46, 43, 47, 52, 49, 39, 15]), 'cur_cost': 244415.0}]
2025-06-26 19:02:36,012 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:02:36,012 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 289, 'skip_rate': 0.03460207612456748, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 279, 'cache_hits': 176, 'similarity_calculations': 4141, 'cache_hit_rate': 0.04250181115672543, 'cache_size': 3965}}
2025-06-26 19:02:36,012 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:02:36,012 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:02:36,012 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:02:36,014 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:02:36,014 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 267327.0
2025-06-26 19:02:36,516 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:02:36,516 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:02:36,518 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:02:36,527 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:02:36,528 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 41975.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}, {'tour': array([18,  1,  3, 20, 56, 21, 11, 39, 45,  2, 19,  4, 54, 46, 29,  6, 24,
       25, 41, 15, 57, 40,  0, 32, 34, 17,  5, 44, 58, 36, 43,  8, 12, 23,
       47, 28, 48, 35, 10, 42, 16, 50, 30, 55, 27, 52, 22, 53,  9, 49, 51,
       26, 38, 13, 31,  7, 37, 33, 14]), 'cur_cost': 267963.0}, {'tour': array([24, 54, 13, 34,  3, 15, 45, 11, 43, 48, 51,  9, 32, 17,  5,  8, 28,
       36, 47,  0, 42, 52, 58,  4, 26, 57, 33, 40, 14, 19, 22, 20, 10, 46,
       44, 35,  2, 37, 55, 23,  6, 12, 29,  7, 50, 27, 38, 31, 56, 18, 30,
       25, 53,  1, 39, 21, 49, 16, 41]), 'cur_cost': 263409.0}, {'tour': array([ 0,  7, 58, 29, 31, 46, 45, 25, 49, 26, 36, 24, 12,  8, 38, 21,  1,
       40, 14, 13,  2, 54, 28, 44,  4, 27, 42, 18,  6,  3, 33, 15, 50, 34,
       55, 22, 20, 35, 19, 16, 32, 53, 23, 17, 41, 57, 30, 48, 47, 43, 37,
        9, 56, 11, 10, 51, 52,  5, 39]), 'cur_cost': 267327.0}]
2025-06-26 19:02:36,530 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:02:36,530 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 290, 'skip_rate': 0.034482758620689655, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 280, 'cache_hits': 176, 'similarity_calculations': 4148, 'cache_hit_rate': 0.04243008678881389, 'cache_size': 3972}}
2025-06-26 19:02:36,530 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:02:36,530 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 41975.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}}]
2025-06-26 19:02:36,531 - __main__ - INFO - 进化阶段完成
2025-06-26 19:02:36,531 - __main__ - INFO - 开始评估阶段
2025-06-26 19:02:36,531 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:02:36,533 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:02:36,533 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 26245.0 → 26245.0 (Δ 0.0)
- Mean cost: 92504.7 → 159712.7
- Diversity: 0.374 → 0.648 (Δ 0.2741)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 24451 → 24451 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 0.0% , avg Δ -87816.14
- Exploit: 3 inds, success 33.3% , avg Δ -19122.33

#### Other indicators
- No-change individuals: 3
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [14860.0, 40596.0, 26245.0], diversity [0.8092929292929294, 0.9175141242937854, 0.5092278719397363]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:02:36,533 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:02:46,684 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success, negative avg delta)",
    "exploit": "moderately effective (33.3% success, negative avg delta)"
  },
  "balance_state": "explore ratio too high given its ineffectiveness",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "mutation_intensity": "increase slightly",
    "elite_preservation": "maintain current count"
  }
}
```
2025-06-26 19:02:46,708 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:02:46,708 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success, negative avg delta)",
    "exploit": "moderately effective (33.3% success, negative avg delta)"
  },
  "balance_state": "explore ratio too high given its ineffectiveness",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "mutation_intensity": "increase slightly",
    "elite_preservation": "maintain current count"
  }
}
```
2025-06-26 19:02:46,708 - __main__ - INFO - 评估阶段完成
2025-06-26 19:02:46,708 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success, negative avg delta)",
    "exploit": "moderately effective (33.3% success, negative avg delta)"
  },
  "balance_state": "explore ratio too high given its ineffectiveness",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "mutation_intensity": "increase slightly",
    "elite_preservation": "maintain current count"
  }
}
```
2025-06-26 19:02:46,708 - __main__ - INFO - 当前最佳适应度: 26245.0
2025-06-26 19:02:46,712 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite11_59_route_2.pkl
2025-06-26 19:02:46,712 - __main__ - INFO - composite11_59 开始进化第 4 代
2025-06-26 19:02:46,712 - __main__ - INFO - 开始分析阶段
2025-06-26 19:02:46,712 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:02:46,725 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 26245.0, 'max': 279411.0, 'mean': 159712.7, 'std': 114750.29565282173}, 'diversity': 0.8873822975517892, 'clusters': {'clusters': 7, 'cluster_sizes': [2, 3, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:02:46,725 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 26245.0, 'max': 279411.0, 'mean': 159712.7, 'std': 114750.29565282173}, 'diversity_level': 0.8873822975517892, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [2, 3, 1, 1, 1, 1, 1]}, 'coordinates': [[3890, 3658], [3945, 3639], [3889, 3685], [3906, 3671], [3923, 3656], [3920, 3680], [3937, 3621], [3958, 3653], [3943, 3673], [3911, 3649], [3899, 3621], [6525, 868], [6531, 888], [6591, 942], [6618, 929], [6548, 851], [6574, 854], [6559, 874], [6566, 942], [6530, 940], [6620, 894], [6550, 903], [6584, 917], [1472, 1211], [1423, 1273], [1442, 1244], [1468, 1281], [1500, 1235], [1466, 1296], [1426, 1232], [1450, 1264], [1492, 1248], [1428, 1251], [1497, 1264], [1494, 1225], [919, 6136], [910, 6189], [925, 6186], [901, 6225], [883, 6136], [863, 6149], [879, 6219], [889, 6175], [933, 6194], [927, 6171], [866, 6199], [908, 6153], [6619, 6506], [6551, 6515], [6568, 6446], [6535, 6492], [6603, 6457], [6565, 6513], [6532, 6513], [6541, 6471], [6603, 6486], [6596, 6495], [6584, 6473], [6556, 6526]], 'distance_matrix': array([[   0.,   58.,   27., ..., 3921., 3896., 3916.],
       [  58.,    0.,   72., ..., 3897., 3872., 3893.],
       [  27.,   72.,    0., ..., 3902., 3878., 3897.],
       ...,
       [3921., 3897., 3902., ...,    0.,   25.,   51.],
       [3896., 3872., 3878., ...,   25.,    0.,   60.],
       [3916., 3893., 3897., ...,   51.,   60.,    0.]])}
2025-06-26 19:02:46,725 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:02:46,725 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:02:46,725 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:02:46,730 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 19:02:46,730 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:02:46,730 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 19:02:46,731 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:02:46,731 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:02:46,732 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:02:46,736 - EliteExpert - INFO - 精英解分析完成
2025-06-26 19:02:46,736 - __main__ - INFO - 精英专家分析报告: {'elite_count': 21, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1794.0, 'avg_gap': 135257.89047619049}, 'structure_gap': {'unique_elite_edges': 147, 'unique_pop_edges': 355, 'common_edges': 40}}, 'elite_diversity': {'diversity_score': 0.20193704600484252}}
2025-06-26 19:02:46,736 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:02:46,739 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:02:46,741 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:02:46,742 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 26245.0, mean 159712.7, max 279411.0, std 114750.29565282173
- diversity: 0.8873822975517892
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [40596.0, 26245.0, 26245.0] (Δ 7175.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [863.0, 851.0, 6620.0, 6526.0]
- centroid/spread: [3883.576, 3695.881] | rms 3403.363
- density_grid(3x3): [[12, 0, 12], [0, 11, 0], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [11.0, 3466.0, 4901.0, 5640.0, 7830.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [3, 7, 8, 8, 7, 6, 12, 7]
- nn_median_dist: 20.881

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:02:46,742 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:03:08,622 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 11.0 to 7830.0 with Q1-Q3 spread of 3466.0-5640.0)",
    "modality": "Potentially multimodal (direction histogram shows multiple peaks in sectors 3,4,6)",
    "deceptiveness": "Possible due to long-edge ratio (25%) and uneven spatial distribution (density_grid shows sparse central regions)"
  },
  "population_state": {
    "coverage": "Good but uneven (diversity=0.887, density_grid shows clustering in corners with empty center)",
    "convergence": "Early stage (convergence=0.0, recent best costs still improving by ~7175.5/iteration)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Edges in length quartiles Q3-max (5640.0-7830.0)",
    "low_density_zones": "Central grid cell (density=11 vs. corner cells=12)"
  },
  "opportunity_regions": {
    "high_density_clusters": "Corner grid cells (density=12)",
    "short_edge_concentrations": "Edges in length quartiles min-Q1 (11.0-3466.0)"
  },
  "evolution_phase": "Exploration-dominant (high diversity, no elites, improving costs)",
  "evolution_direction": [
    "Prioritize 2-opt for long-edge reduction",
    "Enhance exploitation in high-density corners with local search",
    "Maintain diversity through spatial-aware crossover (e.g. edge recombination focusing on short edges)",
    "Monitor central sparse region for potential constraint relaxation"
  ]
}
```
2025-06-26 19:03:08,624 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:03:08,624 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 11.0 to 7830.0 with Q1-Q3 spread of 3466.0-5640.0)', 'modality': 'Potentially multimodal (direction histogram shows multiple peaks in sectors 3,4,6)', 'deceptiveness': 'Possible due to long-edge ratio (25%) and uneven spatial distribution (density_grid shows sparse central regions)'}, 'population_state': {'coverage': 'Good but uneven (diversity=0.887, density_grid shows clustering in corners with empty center)', 'convergence': 'Early stage (convergence=0.0, recent best costs still improving by ~7175.5/iteration)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in length quartiles Q3-max (5640.0-7830.0)', 'low_density_zones': 'Central grid cell (density=11 vs. corner cells=12)'}, 'opportunity_regions': {'high_density_clusters': 'Corner grid cells (density=12)', 'short_edge_concentrations': 'Edges in length quartiles min-Q1 (11.0-3466.0)'}, 'evolution_phase': 'Exploration-dominant (high diversity, no elites, improving costs)', 'evolution_direction': ['Prioritize 2-opt for long-edge reduction', 'Enhance exploitation in high-density corners with local search', 'Maintain diversity through spatial-aware crossover (e.g. edge recombination focusing on short edges)', 'Monitor central sparse region for potential constraint relaxation']}
2025-06-26 19:03:08,624 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:03:08,624 - __main__ - INFO - 分析阶段完成
2025-06-26 19:03:08,624 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 11.0 to 7830.0 with Q1-Q3 spread of 3466.0-5640.0)', 'modality': 'Potentially multimodal (direction histogram shows multiple peaks in sectors 3,4,6)', 'deceptiveness': 'Possible due to long-edge ratio (25%) and uneven spatial distribution (density_grid shows sparse central regions)'}, 'population_state': {'coverage': 'Good but uneven (diversity=0.887, density_grid shows clustering in corners with empty center)', 'convergence': 'Early stage (convergence=0.0, recent best costs still improving by ~7175.5/iteration)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in length quartiles Q3-max (5640.0-7830.0)', 'low_density_zones': 'Central grid cell (density=11 vs. corner cells=12)'}, 'opportunity_regions': {'high_density_clusters': 'Corner grid cells (density=12)', 'short_edge_concentrations': 'Edges in length quartiles min-Q1 (11.0-3466.0)'}, 'evolution_phase': 'Exploration-dominant (high diversity, no elites, improving costs)', 'evolution_direction': ['Prioritize 2-opt for long-edge reduction', 'Enhance exploitation in high-density corners with local search', 'Maintain diversity through spatial-aware crossover (e.g. edge recombination focusing on short edges)', 'Monitor central sparse region for potential constraint relaxation']}
2025-06-26 19:03:08,624 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:03:08,624 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:03:08,624 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 26245.0
  • mean_cost: 159712.7
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 11.0 to 7830.0 with Q1-Q3 spread of 3466.0-5640.0), modality Potentially multimodal (direction histogram shows multiple peaks in sectors 3,4,6), deceptiveness Possible due to long-edge ratio (25%) and uneven spatial distribution (density_grid shows sparse central regions)
- Evolution phase: Exploration-dominant (high diversity, no elites, improving costs)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_zones
- Opportunity regions (# 2): high_density_cluster, short_edge_concentra
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineff...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:03:08,624 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:03:08,624 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:03:18,078 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration focus. Top 30% performers exploit; others explore to balance search."
}
```
2025-06-26 19:03:18,078 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 19:03:18,078 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 19:03:18,078 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration focus. Top 30% performers exploit; others explore to balance search."
}
```
2025-06-26 19:03:18,078 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:03:18,078 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 19:03:18,078 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration focus. Top 30% performers exploit; others explore to balance search."
}
```
2025-06-26 19:03:18,078 - __main__ - INFO - 开始进化阶段
2025-06-26 19:03:18,078 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:03:18,078 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:03:18,078 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 279411.0 (rank 80.0%)
- Population diversity: 0.6482758620689655
- Evolution phase: Exploration-dominant (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:03:18,078 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:03:18,078 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:03:30,922 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 4, 15, 26, 37, 48, 10, 21, 32, 43, 54, 5, 16, 27, 38, 49, 11, 22, 33, 44, 55, 6, 17, 28, 39, 50, 1],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Prioritize dense clusters with short edges, skip low-density zones"
}
```
2025-06-26 19:03:30,930 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:03:30,930 - ExplorationExpert - INFO - 探索路径生成完成，成本: 256225.0, 路径: [0, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 4, 15, 26, 37, 48, 10, 21, 32, 43, 54, 5, 16, 27, 38, 49, 11, 22, 33, 44, 55, 6, 17, 28, 39, 50, 1]
2025-06-26 19:03:30,930 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 4, 15, 26, 37, 48, 10, 21, 32, 43, 54, 5, 16, 27, 38, 49, 11, 22, 33, 44, 55, 6, 17, 28, 39, 50, 1], 'cur_cost': 256225.0}
2025-06-26 19:03:30,930 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:03:30,930 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:03:30,930 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 279411.0 (rank 90.0%)
- Population diversity: 0.7396551724137931
- Evolution phase: Exploration-dominant (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:03:30,933 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:03:30,933 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:03:44,929 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 26, 33, 40, 47, 54, 1, 8, 15, 22, 29, 36, 43, 50, 57, 4, 11, 18, 25, 32, 39, 46, 53, 2, 9, 16, 23, 30, 37, 44, 51, 58, 7, 14, 21, 28, 35, 42, 49, 56, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 55],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Leverage modular jumps to explore sparse cells"
}
```
2025-06-26 19:03:44,929 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:03:44,929 - ExplorationExpert - INFO - 探索路径生成完成，成本: 165452.0, 路径: [0, 5, 12, 19, 26, 33, 40, 47, 54, 1, 8, 15, 22, 29, 36, 43, 50, 57, 4, 11, 18, 25, 32, 39, 46, 53, 2, 9, 16, 23, 30, 37, 44, 51, 58, 7, 14, 21, 28, 35, 42, 49, 56, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 55]
2025-06-26 19:03:44,929 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 1, 8, 15, 22, 29, 36, 43, 50, 57, 4, 11, 18, 25, 32, 39, 46, 53, 2, 9, 16, 23, 30, 37, 44, 51, 58, 7, 14, 21, 28, 35, 42, 49, 56, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 55], 'cur_cost': 165452.0}
2025-06-26 19:03:44,929 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-26 19:03:44,929 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:03:44,929 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:03:44,929 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 242223.0
2025-06-26 19:03:45,430 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:03:45,430 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:03:45,430 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:03:45,440 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:03:45,440 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 4, 15, 26, 37, 48, 10, 21, 32, 43, 54, 5, 16, 27, 38, 49, 11, 22, 33, 44, 55, 6, 17, 28, 39, 50, 1], 'cur_cost': 256225.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 1, 8, 15, 22, 29, 36, 43, 50, 57, 4, 11, 18, 25, 32, 39, 46, 53, 2, 9, 16, 23, 30, 37, 44, 51, 58, 7, 14, 21, 28, 35, 42, 49, 56, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 55], 'cur_cost': 165452.0}, {'tour': array([16, 55, 38, 11, 10, 24, 48, 41,  8, 53, 29, 13, 33, 34, 45, 30, 23,
       31,  7, 26, 28,  9, 50, 35, 27, 51,  6,  3, 25, 42, 40, 54, 46, 37,
       12, 32, 21, 52, 47, 22, 58, 49, 15,  5, 56, 18,  4,  2, 36, 20, 17,
       43, 19, 44, 39, 57, 14,  1,  0]), 'cur_cost': 242223.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 41975.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}, {'tour': array([18,  1,  3, 20, 56, 21, 11, 39, 45,  2, 19,  4, 54, 46, 29,  6, 24,
       25, 41, 15, 57, 40,  0, 32, 34, 17,  5, 44, 58, 36, 43,  8, 12, 23,
       47, 28, 48, 35, 10, 42, 16, 50, 30, 55, 27, 52, 22, 53,  9, 49, 51,
       26, 38, 13, 31,  7, 37, 33, 14]), 'cur_cost': 267963.0}, {'tour': array([24, 54, 13, 34,  3, 15, 45, 11, 43, 48, 51,  9, 32, 17,  5,  8, 28,
       36, 47,  0, 42, 52, 58,  4, 26, 57, 33, 40, 14, 19, 22, 20, 10, 46,
       44, 35,  2, 37, 55, 23,  6, 12, 29,  7, 50, 27, 38, 31, 56, 18, 30,
       25, 53,  1, 39, 21, 49, 16, 41]), 'cur_cost': 263409.0}, {'tour': array([ 0,  7, 58, 29, 31, 46, 45, 25, 49, 26, 36, 24, 12,  8, 38, 21,  1,
       40, 14, 13,  2, 54, 28, 44,  4, 27, 42, 18,  6,  3, 33, 15, 50, 34,
       55, 22, 20, 35, 19, 16, 32, 53, 23, 17, 41, 57, 30, 48, 47, 43, 37,
        9, 56, 11, 10, 51, 52,  5, 39]), 'cur_cost': 267327.0}]
2025-06-26 19:03:45,442 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:03:45,442 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 10, 'total_searches': 291, 'skip_rate': 0.03436426116838488, 'estimated_time_saved': 50, 'path_optimizer': {'path_count': 281, 'cache_hits': 176, 'similarity_calculations': 4156, 'cache_hit_rate': 0.04234841193455245, 'cache_size': 3980}}
2025-06-26 19:03:45,443 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-06-26 19:03:45,443 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:03:45,443 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:03:45,444 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26245.0 (rank 0.0%)
- Population diversity: 0.8206896551724138
- Evolution phase: Exploration-dominant (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:03:45,445 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:03:45,445 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:03:58,714 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Structured leapfrog pattern to explore sparse cells"
}
```
2025-06-26 19:03:58,714 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:03:58,714 - ExplorationExpert - INFO - 探索路径生成完成，成本: 279411.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47]
2025-06-26 19:03:58,714 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}
2025-06-26 19:03:58,714 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-26 19:03:58,714 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:03:58,714 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:03:58,714 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 264755.0
2025-06-26 19:03:58,714 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：280,跳过局部搜索
2025-06-26 19:03:58,714 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 19:03:58,714 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([28, 54, 50, 34,  8, 15, 22, 49, 10, 29, 43, 32, 19, 47, 48, 27, 51,
       57, 46, 33, 24,  3, 42, 14, 30, 23, 11, 55, 36, 21, 25, 16, 18, 45,
        7,  2, 37,  6, 13, 39,  1, 41, 17, 38,  5, 26, 56, 20, 40, 31, 53,
        4, 58, 12,  9, 35, 44, 52,  0]), 'cur_cost': 264755.0}
2025-06-26 19:03:58,714 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:03:58,714 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:03:58,714 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 41975.0 (rank 0.0%)
- Population diversity: 0.8862068965517241
- Evolution phase: Exploration-dominant (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:03:58,714 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:03:58,714 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:04:13,187 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Prioritize dense clusters with short edges, reverse subpaths for diversity"
}
```
2025-06-26 19:04:13,193 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:04:13,193 - ExplorationExpert - INFO - 探索路径生成完成，成本: 33711.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 19:04:13,193 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 33711.0}
2025-06-26 19:04:13,193 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 19:04:13,193 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:04:13,193 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 118896.0 (rank 10.0%)
- Population diversity: 0.8862068965517241
- Evolution phase: Exploration-dominant (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:04:13,193 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:04:13,193 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:04:26,454 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 55, 58, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 11, 4, 1, 6, 13, 19, 23, 28, 32, 36, 40, 44, 48, 52, 56, 57, 53, 49, 45, 41, 37, 33, 29, 24, 20, 14, 7, 2, 8, 15, 25, 16, 9, 3, 10],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Spiral pattern through opportunity regions, avoiding long edges"
}
```
2025-06-26 19:04:26,454 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:04:26,454 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97989.0, 路径: [0, 5, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 55, 58, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 11, 4, 1, 6, 13, 19, 23, 28, 32, 36, 40, 44, 48, 52, 56, 57, 53, 49, 45, 41, 37, 33, 29, 24, 20, 14, 7, 2, 8, 15, 25, 16, 9, 3, 10]
2025-06-26 19:04:26,454 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 55, 58, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 11, 4, 1, 6, 13, 19, 23, 28, 32, 36, 40, 44, 48, 52, 56, 57, 53, 49, 45, 41, 37, 33, 29, 24, 20, 14, 7, 2, 8, 15, 25, 16, 9, 3, 10], 'cur_cost': 97989.0}
2025-06-26 19:04:26,454 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:04:26,458 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:04:26,458 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:04:26,458 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 250287.0
2025-06-26 19:04:26,960 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:04:26,960 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:04:26,960 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:04:26,970 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:04:26,970 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 4, 15, 26, 37, 48, 10, 21, 32, 43, 54, 5, 16, 27, 38, 49, 11, 22, 33, 44, 55, 6, 17, 28, 39, 50, 1], 'cur_cost': 256225.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 1, 8, 15, 22, 29, 36, 43, 50, 57, 4, 11, 18, 25, 32, 39, 46, 53, 2, 9, 16, 23, 30, 37, 44, 51, 58, 7, 14, 21, 28, 35, 42, 49, 56, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 55], 'cur_cost': 165452.0}, {'tour': array([16, 55, 38, 11, 10, 24, 48, 41,  8, 53, 29, 13, 33, 34, 45, 30, 23,
       31,  7, 26, 28,  9, 50, 35, 27, 51,  6,  3, 25, 42, 40, 54, 46, 37,
       12, 32, 21, 52, 47, 22, 58, 49, 15,  5, 56, 18,  4,  2, 36, 20, 17,
       43, 19, 44, 39, 57, 14,  1,  0]), 'cur_cost': 242223.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': array([28, 54, 50, 34,  8, 15, 22, 49, 10, 29, 43, 32, 19, 47, 48, 27, 51,
       57, 46, 33, 24,  3, 42, 14, 30, 23, 11, 55, 36, 21, 25, 16, 18, 45,
        7,  2, 37,  6, 13, 39,  1, 41, 17, 38,  5, 26, 56, 20, 40, 31, 53,
        4, 58, 12,  9, 35, 44, 52,  0]), 'cur_cost': 264755.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 33711.0}, {'tour': [0, 5, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 55, 58, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 11, 4, 1, 6, 13, 19, 23, 28, 32, 36, 40, 44, 48, 52, 56, 57, 53, 49, 45, 41, 37, 33, 29, 24, 20, 14, 7, 2, 8, 15, 25, 16, 9, 3, 10], 'cur_cost': 97989.0}, {'tour': array([ 0, 20, 23,  2, 53, 56, 13, 33, 17,  1, 14, 25, 37, 52, 11, 38, 55,
        8, 48, 51, 27, 46, 31, 16, 19, 49, 21, 44, 35, 22, 42,  5, 28, 24,
       18, 39, 32, 47, 12, 50, 30, 43,  9, 36, 41, 40, 54, 26, 15, 58, 57,
       10, 45, 29,  3,  4,  7, 34,  6]), 'cur_cost': 250287.0}, {'tour': array([24, 54, 13, 34,  3, 15, 45, 11, 43, 48, 51,  9, 32, 17,  5,  8, 28,
       36, 47,  0, 42, 52, 58,  4, 26, 57, 33, 40, 14, 19, 22, 20, 10, 46,
       44, 35,  2, 37, 55, 23,  6, 12, 29,  7, 50, 27, 38, 31, 56, 18, 30,
       25, 53,  1, 39, 21, 49, 16, 41]), 'cur_cost': 263409.0}, {'tour': array([ 0,  7, 58, 29, 31, 46, 45, 25, 49, 26, 36, 24, 12,  8, 38, 21,  1,
       40, 14, 13,  2, 54, 28, 44,  4, 27, 42, 18,  6,  3, 33, 15, 50, 34,
       55, 22, 20, 35, 19, 16, 32, 53, 23, 17, 41, 57, 30, 48, 47, 43, 37,
        9, 56, 11, 10, 51, 52,  5, 39]), 'cur_cost': 267327.0}]
2025-06-26 19:04:26,973 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:04:26,973 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 11, 'total_searches': 293, 'skip_rate': 0.03754266211604096, 'estimated_time_saved': 55, 'path_optimizer': {'path_count': 282, 'cache_hits': 184, 'similarity_calculations': 4174, 'cache_hit_rate': 0.04408241494968855, 'cache_size': 3990}}
2025-06-26 19:04:26,973 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:04:26,974 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-26 19:04:26,974 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:04:26,975 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 263409.0 (rank 60.0%)
- Population diversity: 0.8568965517241379
- Evolution phase: Exploration-dominant (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:04:26,976 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:04:26,976 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:04:41,059 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Leverage clustered nodes with short edges, skip long corridors"
}
```
2025-06-26 19:04:41,059 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:04:41,059 - ExplorationExpert - INFO - 探索路径生成完成，成本: 279411.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47]
2025-06-26 19:04:41,059 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}
2025-06-26 19:04:41,059 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:04:41,059 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:04:41,059 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:04:41,059 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 247723.0
2025-06-26 19:04:41,560 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:04:41,561 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:04:41,561 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:04:41,570 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:04:41,571 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 4, 15, 26, 37, 48, 10, 21, 32, 43, 54, 5, 16, 27, 38, 49, 11, 22, 33, 44, 55, 6, 17, 28, 39, 50, 1], 'cur_cost': 256225.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 1, 8, 15, 22, 29, 36, 43, 50, 57, 4, 11, 18, 25, 32, 39, 46, 53, 2, 9, 16, 23, 30, 37, 44, 51, 58, 7, 14, 21, 28, 35, 42, 49, 56, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 55], 'cur_cost': 165452.0}, {'tour': array([16, 55, 38, 11, 10, 24, 48, 41,  8, 53, 29, 13, 33, 34, 45, 30, 23,
       31,  7, 26, 28,  9, 50, 35, 27, 51,  6,  3, 25, 42, 40, 54, 46, 37,
       12, 32, 21, 52, 47, 22, 58, 49, 15,  5, 56, 18,  4,  2, 36, 20, 17,
       43, 19, 44, 39, 57, 14,  1,  0]), 'cur_cost': 242223.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': array([28, 54, 50, 34,  8, 15, 22, 49, 10, 29, 43, 32, 19, 47, 48, 27, 51,
       57, 46, 33, 24,  3, 42, 14, 30, 23, 11, 55, 36, 21, 25, 16, 18, 45,
        7,  2, 37,  6, 13, 39,  1, 41, 17, 38,  5, 26, 56, 20, 40, 31, 53,
        4, 58, 12,  9, 35, 44, 52,  0]), 'cur_cost': 264755.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 33711.0}, {'tour': [0, 5, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 55, 58, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 11, 4, 1, 6, 13, 19, 23, 28, 32, 36, 40, 44, 48, 52, 56, 57, 53, 49, 45, 41, 37, 33, 29, 24, 20, 14, 7, 2, 8, 15, 25, 16, 9, 3, 10], 'cur_cost': 97989.0}, {'tour': array([ 0, 20, 23,  2, 53, 56, 13, 33, 17,  1, 14, 25, 37, 52, 11, 38, 55,
        8, 48, 51, 27, 46, 31, 16, 19, 49, 21, 44, 35, 22, 42,  5, 28, 24,
       18, 39, 32, 47, 12, 50, 30, 43,  9, 36, 41, 40, 54, 26, 15, 58, 57,
       10, 45, 29,  3,  4,  7, 34,  6]), 'cur_cost': 250287.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': array([42, 12, 38, 16, 45, 48,  7,  8, 13, 57,  4, 51, 40, 39, 56, 32,  9,
       23, 26, 30, 29,  6, 21,  3, 53, 33, 35, 58, 50, 43, 11, 55, 49, 25,
        5, 37, 15, 28,  2, 54, 44, 52, 17, 46, 27,  1, 19, 14, 22, 41, 36,
       10, 47, 31, 34,  0, 20, 18, 24]), 'cur_cost': 247723.0}]
2025-06-26 19:04:41,573 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:04:41,573 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 11, 'total_searches': 294, 'skip_rate': 0.03741496598639456, 'estimated_time_saved': 55, 'path_optimizer': {'path_count': 283, 'cache_hits': 184, 'similarity_calculations': 4184, 'cache_hit_rate': 0.04397705544933078, 'cache_size': 4000}}
2025-06-26 19:04:41,573 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:04:41,573 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 2, 13, 24, 35, 46, 57, 8, 19, 30, 41, 52, 3, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 4, 15, 26, 37, 48, 10, 21, 32, 43, 54, 5, 16, 27, 38, 49, 11, 22, 33, 44, 55, 6, 17, 28, 39, 50, 1], 'cur_cost': 256225.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 1, 8, 15, 22, 29, 36, 43, 50, 57, 4, 11, 18, 25, 32, 39, 46, 53, 2, 9, 16, 23, 30, 37, 44, 51, 58, 7, 14, 21, 28, 35, 42, 49, 56, 3, 10, 17, 24, 31, 38, 45, 52, 6, 13, 20, 27, 34, 41, 48, 55], 'cur_cost': 165452.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([28, 54, 50, 34,  8, 15, 22, 49, 10, 29, 43, 32, 19, 47, 48, 27, 51,
       57, 46, 33, 24,  3, 42, 14, 30, 23, 11, 55, 36, 21, 25, 16, 18, 45,
        7,  2, 37,  6, 13, 39,  1, 41, 17, 38,  5, 26, 56, 20, 40, 31, 53,
        4, 58, 12,  9, 35, 44, 52,  0]), 'cur_cost': 264755.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 33711.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 18, 22, 27, 31, 35, 39, 43, 47, 51, 55, 58, 54, 50, 46, 42, 38, 34, 30, 26, 21, 17, 11, 4, 1, 6, 13, 19, 23, 28, 32, 36, 40, 44, 48, 52, 56, 57, 53, 49, 45, 41, 37, 33, 29, 24, 20, 14, 7, 2, 8, 15, 25, 16, 9, 3, 10], 'cur_cost': 97989.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}}]
2025-06-26 19:04:41,575 - __main__ - INFO - 进化阶段完成
2025-06-26 19:04:41,575 - __main__ - INFO - 开始评估阶段
2025-06-26 19:04:41,575 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:04:41,576 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:04:41,576 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 26245.0 → 33711.0 (Δ -7466.0)
- Mean cost: 159712.7 → 211718.7
- Diversity: 0.648 → 0.793 (Δ 0.1448)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 24451 → 24451 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 66.7% , avg Δ -17142.0
- Exploit: 4 inds, success 50.0% , avg Δ -104302.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [40596.0, 26245.0, 26245.0], diversity [0.9175141242937854, 0.5092278719397363, 0.8873822975517892]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:04:41,577 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:04:53,590 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "avg_improvement": -17142.0,
      "contribution": "moderate, maintains diversity"
    },
    "exploit": {
      "success_rate": 50.0,
      "avg_improvement": -104302.0,
      "contribution": "high when successful, but inconsistent"
    }
  },
  "balance_state": "exploit-heavy with room for more exploration",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider introducing new genetic operators to break stagnation"
  }
}
```
2025-06-26 19:04:53,613 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:04:53,613 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "avg_improvement": -17142.0,
      "contribution": "moderate, maintains diversity"
    },
    "exploit": {
      "success_rate": 50.0,
      "avg_improvement": -104302.0,
      "contribution": "high when successful, but inconsistent"
    }
  },
  "balance_state": "exploit-heavy with room for more exploration",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider introducing new genetic operators to break stagnation"
  }
}
```
2025-06-26 19:04:53,613 - __main__ - INFO - 评估阶段完成
2025-06-26 19:04:53,613 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "avg_improvement": -17142.0,
      "contribution": "moderate, maintains diversity"
    },
    "exploit": {
      "success_rate": 50.0,
      "avg_improvement": -104302.0,
      "contribution": "high when successful, but inconsistent"
    }
  },
  "balance_state": "exploit-heavy with room for more exploration",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider introducing new genetic operators to break stagnation"
  }
}
```
2025-06-26 19:04:53,613 - __main__ - INFO - 当前最佳适应度: 33711.0
2025-06-26 19:04:53,625 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite11_59_route_3.pkl
2025-06-26 19:04:53,626 - __main__ - INFO - composite11_59 开始进化第 5 代
2025-06-26 19:04:53,626 - __main__ - INFO - 开始分析阶段
2025-06-26 19:04:53,626 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:04:53,641 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 33711.0, 'max': 279411.0, 'mean': 211718.7, 'std': 80270.96858024076}, 'diversity': 0.9491525423728815, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 1, 1, 2, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:04:53,642 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 33711.0, 'max': 279411.0, 'mean': 211718.7, 'std': 80270.96858024076}, 'diversity_level': 0.9491525423728815, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 1, 1, 2, 1, 1, 1, 1, 1]}, 'coordinates': [[3890, 3658], [3945, 3639], [3889, 3685], [3906, 3671], [3923, 3656], [3920, 3680], [3937, 3621], [3958, 3653], [3943, 3673], [3911, 3649], [3899, 3621], [6525, 868], [6531, 888], [6591, 942], [6618, 929], [6548, 851], [6574, 854], [6559, 874], [6566, 942], [6530, 940], [6620, 894], [6550, 903], [6584, 917], [1472, 1211], [1423, 1273], [1442, 1244], [1468, 1281], [1500, 1235], [1466, 1296], [1426, 1232], [1450, 1264], [1492, 1248], [1428, 1251], [1497, 1264], [1494, 1225], [919, 6136], [910, 6189], [925, 6186], [901, 6225], [883, 6136], [863, 6149], [879, 6219], [889, 6175], [933, 6194], [927, 6171], [866, 6199], [908, 6153], [6619, 6506], [6551, 6515], [6568, 6446], [6535, 6492], [6603, 6457], [6565, 6513], [6532, 6513], [6541, 6471], [6603, 6486], [6596, 6495], [6584, 6473], [6556, 6526]], 'distance_matrix': array([[   0.,   58.,   27., ..., 3921., 3896., 3916.],
       [  58.,    0.,   72., ..., 3897., 3872., 3893.],
       [  27.,   72.,    0., ..., 3902., 3878., 3897.],
       ...,
       [3921., 3897., 3902., ...,    0.,   25.,   51.],
       [3896., 3872., 3878., ...,   25.,    0.,   60.],
       [3916., 3893., 3897., ...,   51.,   60.,    0.]])}
2025-06-26 19:04:53,643 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:04:53,643 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:04:53,643 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:04:53,646 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 19:04:53,646 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:04:53,646 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 19:04:53,646 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:04:53,646 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:04:53,648 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:04:53,654 - EliteExpert - INFO - 精英解分析完成
2025-06-26 19:04:53,654 - __main__ - INFO - 精英专家分析报告: {'elite_count': 21, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 9260.0, 'avg_gap': 187263.89047619049}, 'structure_gap': {'unique_elite_edges': 141, 'unique_pop_edges': 452, 'common_edges': 46}}, 'elite_diversity': {'diversity_score': 0.20193704600484252}}
2025-06-26 19:04:53,654 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:04:53,654 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:04:53,654 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:04:53,654 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 33711.0, mean 211718.7, max 279411.0, std 80270.96858024076
- diversity: 0.9491525423728815
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [26245.0, 26245.0, 33711.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [863.0, 851.0, 6620.0, 6526.0]
- centroid/spread: [3883.576, 3695.881] | rms 3403.363
- density_grid(3x3): [[12, 0, 12], [0, 11, 0], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [11.0, 3466.0, 4901.0, 5640.0, 7830.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [3, 7, 8, 8, 7, 6, 12, 7]
- nn_median_dist: 20.881

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:04:53,654 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:05:14,785 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 11.0 to 7830.0, high std in cost_stats)",
    "modality": "Potentially multimodal (high diversity score, no clustering detected)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 suggests some misleading long edges)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (high diversity 0.95, density_grid shows nodes distributed in all corners)",
    "convergence": "No convergence (convergence score 0.0, recent best costs show stagnation)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges in q3-max range: 5640.0-7830.0)",
    "Low-density center cell (only 11 nodes vs. 12 in corners)"
  ],
  "opportunity_regions": [
    "High-density corner cells (12 nodes each in grid cells [0,0], [0,2], [2,0], [2,2])",
    "Short-edge clusters (edges in min-q1 range: 11.0-3466.0)"
  ],
  "evolution_phase": "Mid-phase exploration (iteration 4/5, high diversity but no elites yet)",
  "evolution_direction": [
    "Intensify exploitation in high-density corners (e.g. 2-opt local search)",
    "Add path-relinking between distant high-density cells",
    "Apply edge-recombination focusing on short edges",
    "Introduce directed mutation to break long-edge regions"
  ]
}
```
2025-06-26 19:05:14,785 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:05:14,785 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 11.0 to 7830.0, high std in cost_stats)', 'modality': 'Potentially multimodal (high diversity score, no clustering detected)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Good spatial coverage (high diversity 0.95, density_grid shows nodes distributed in all corners)', 'convergence': 'No convergence (convergence score 0.0, recent best costs show stagnation)'}, 'difficult_regions': ['Long-edge corridors (edges in q3-max range: 5640.0-7830.0)', 'Low-density center cell (only 11 nodes vs. 12 in corners)'], 'opportunity_regions': ['High-density corner cells (12 nodes each in grid cells [0,0], [0,2], [2,0], [2,2])', 'Short-edge clusters (edges in min-q1 range: 11.0-3466.0)'], 'evolution_phase': 'Mid-phase exploration (iteration 4/5, high diversity but no elites yet)', 'evolution_direction': ['Intensify exploitation in high-density corners (e.g. 2-opt local search)', 'Add path-relinking between distant high-density cells', 'Apply edge-recombination focusing on short edges', 'Introduce directed mutation to break long-edge regions']}
2025-06-26 19:05:14,785 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:05:14,785 - __main__ - INFO - 分析阶段完成
2025-06-26 19:05:14,785 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 11.0 to 7830.0, high std in cost_stats)', 'modality': 'Potentially multimodal (high diversity score, no clustering detected)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Good spatial coverage (high diversity 0.95, density_grid shows nodes distributed in all corners)', 'convergence': 'No convergence (convergence score 0.0, recent best costs show stagnation)'}, 'difficult_regions': ['Long-edge corridors (edges in q3-max range: 5640.0-7830.0)', 'Low-density center cell (only 11 nodes vs. 12 in corners)'], 'opportunity_regions': ['High-density corner cells (12 nodes each in grid cells [0,0], [0,2], [2,0], [2,2])', 'Short-edge clusters (edges in min-q1 range: 11.0-3466.0)'], 'evolution_phase': 'Mid-phase exploration (iteration 4/5, high diversity but no elites yet)', 'evolution_direction': ['Intensify exploitation in high-density corners (e.g. 2-opt local search)', 'Add path-relinking between distant high-density cells', 'Apply edge-recombination focusing on short edges', 'Introduce directed mutation to break long-edge regions']}
2025-06-26 19:05:14,785 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:05:14,785 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:05:14,785 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 33711.0
  • mean_cost: 211718.7
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 11.0 to 7830.0, high std in cost_stats), modality Potentially multimodal (high diversity score, no clustering detected), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 suggests some misleading long edges)
- Evolution phase: Mid-phase exploration (iteration 4/5, high diversity but no elites yet)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Low-density center c
- Opportunity regions (# 2): High-density corner , Short-edge clusters 
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:05:14,785 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:05:14,785 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:05:24,507 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and landscape focus on balance favor exploration. Top 60% explore to cover opportunity regions, bottom 40% exploit best-cost areas."
}
```
2025-06-26 19:05:24,507 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:05:24,513 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:05:24,513 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and landscape focus on balance favor exploration. Top 60% explore to cover opportunity regions, bottom 40% exploit best-cost areas."
}
```
2025-06-26 19:05:24,513 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:05:24,513 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:05:24,513 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and landscape focus on balance favor exploration. Top 60% explore to cover opportunity regions, bottom 40% exploit best-cost areas."
}
```
2025-06-26 19:05:24,513 - __main__ - INFO - 开始进化阶段
2025-06-26 19:05:24,515 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:05:24,515 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:05:24,515 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 256225.0 (rank 60.0%)
- Population diversity: 0.7931034482758621
- Evolution phase: Mid-phase exploration (iteration 4/5, high diversity but no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density co, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density cen
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:05:24,515 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:05:24,515 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:05:38,405 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 41, 15, 50, 29, 4, 52, 18, 43, 10, 31, 6, 48, 21, 39, 14, 54, 26, 9, 33, 1, 46, 20, 58, 13, 35, 5, 49, 24, 57, 11, 38, 3, 44, 17, 51, 28, 0, 42, 16, 53, 25, 47, 22, 36, 30, 55, 27, 40, 32],
  "targeted_regions": "High-density co, Short-edge clus, adjacent low-density",
  "strategy_comment": "Prioritize short edges in dense clusters, explore nearby sparse cells"
}
```
2025-06-26 19:05:38,405 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:05:38,405 - ExplorationExpert - INFO - 探索路径生成完成，成本: 326289.0, 路径: [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 41, 15, 50, 29, 4, 52, 18, 43, 10, 31, 6, 48, 21, 39, 14, 54, 26, 9, 33, 1, 46, 20, 58, 13, 35, 5, 49, 24, 57, 11, 38, 3, 44, 17, 51, 28, 0, 42, 16, 53, 25, 47, 22, 36, 30, 55, 27, 40, 32]
2025-06-26 19:05:38,411 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 41, 15, 50, 29, 4, 52, 18, 43, 10, 31, 6, 48, 21, 39, 14, 54, 26, 9, 33, 1, 46, 20, 58, 13, 35, 5, 49, 24, 57, 11, 38, 3, 44, 17, 51, 28, 0, 42, 16, 53, 25, 47, 22, 36, 30, 55, 27, 40, 32], 'cur_cost': 326289.0}
2025-06-26 19:05:38,411 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:05:38,411 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:05:38,411 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 165452.0 (rank 20.0%)
- Population diversity: 0.7982758620689655
- Evolution phase: Mid-phase exploration (iteration 4/5, high diversity but no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density co, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density cen
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:05:38,413 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:05:38,413 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:05:52,056 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47],
  "targeted_regions": "High-density co, Short-edge clus",
  "strategy_comment": "Sequential leapfrog to explore sparse cells between clusters"
}
```
2025-06-26 19:05:52,056 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:05:52,056 - ExplorationExpert - INFO - 探索路径生成完成，成本: 279411.0, 路径: [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47]
2025-06-26 19:05:52,056 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}
2025-06-26 19:05:52,056 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:05:52,056 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:05:52,056 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 242223.0 (rank 20.0%)
- Population diversity: 0.7241379310344828
- Evolution phase: Mid-phase exploration (iteration 4/5, high diversity but no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density co, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density cen
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:05:52,056 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:05:52,056 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:06:05,292 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "High-density co, Short-edge clus",
  "strategy_comment": "Alternating high-low node traversal to explore sparse cells"
}
```
2025-06-26 19:06:05,292 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:06:05,292 - ExplorationExpert - INFO - 探索路径生成完成，成本: 41975.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 19:06:05,292 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 41975.0}
2025-06-26 19:06:05,292 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:06:05,292 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:06:05,292 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 279411.0 (rank 60.0%)
- Population diversity: 0.7327586206896551
- Evolution phase: Mid-phase exploration (iteration 4/5, high diversity but no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density co, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density cen
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:06:05,292 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:06:05,292 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:06:18,671 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "High-density co, Short-edge clus",
  "strategy_comment": "Prioritize clustering in high-density areas with short edges"
}
```
2025-06-26 19:06:18,671 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:06:18,671 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 19:06:18,671 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 19:06:18,671 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:06:18,671 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:06:18,671 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 264755.0 (rank 60.0%)
- Population diversity: 0.7362068965517241
- Evolution phase: Mid-phase exploration (iteration 4/5, high diversity but no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density co, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density cen
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:06:18,679 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:06:18,679 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:06:31,942 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54],
  "targeted_regions": "High-density co, Short-edge clus",
  "strategy_comment": "Grid traversal prioritizing dense clusters while avoiding long edges"
}
```
2025-06-26 19:06:31,942 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:06:31,942 - ExplorationExpert - INFO - 探索路径生成完成，成本: 118896.0, 路径: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54]
2025-06-26 19:06:31,942 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}
2025-06-26 19:06:31,942 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:06:31,942 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:06:31,942 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 33711.0 (rank 10.0%)
- Population diversity: 0.743103448275862
- Evolution phase: Mid-phase exploration (iteration 4/5, high diversity but no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density co, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density cen
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 58]  
• Path length == 59 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:06:31,942 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:06:31,942 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:06:44,945 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58],
  "targeted_regions": "High-density co, Short-edge clus",
  "strategy_comment": "Prioritize adjacency in high-density clusters"
}
```
2025-06-26 19:06:44,951 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:06:44,951 - ExplorationExpert - INFO - 探索路径生成完成，成本: 26245.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]
2025-06-26 19:06:44,951 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}
2025-06-26 19:06:44,951 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 19:06:44,951 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:06:44,951 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:06:44,951 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 241948.0
2025-06-26 19:06:45,452 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:06:45,452 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:06:45,453 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:06:45,463 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:06:45,463 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 41, 15, 50, 29, 4, 52, 18, 43, 10, 31, 6, 48, 21, 39, 14, 54, 26, 9, 33, 1, 46, 20, 58, 13, 35, 5, 49, 24, 57, 11, 38, 3, 44, 17, 51, 28, 0, 42, 16, 53, 25, 47, 22, 36, 30, 55, 27, 40, 32], 'cur_cost': 326289.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 41975.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': array([34, 17, 40, 27, 12, 15, 11, 36, 23, 24,  8, 41, 35, 21, 22, 31, 10,
       13, 47, 44,  7, 58,  6,  0, 43, 45, 19,  9, 42, 16, 49, 28, 51, 18,
       26, 46,  1, 55, 50, 39, 32, 33, 30, 37, 48, 29,  4, 52, 25, 57,  5,
       54, 38,  3, 53, 56,  2, 14, 20]), 'cur_cost': 241948.0}, {'tour': array([ 0, 20, 23,  2, 53, 56, 13, 33, 17,  1, 14, 25, 37, 52, 11, 38, 55,
        8, 48, 51, 27, 46, 31, 16, 19, 49, 21, 44, 35, 22, 42,  5, 28, 24,
       18, 39, 32, 47, 12, 50, 30, 43,  9, 36, 41, 40, 54, 26, 15, 58, 57,
       10, 45, 29,  3,  4,  7, 34,  6]), 'cur_cost': 250287.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': array([42, 12, 38, 16, 45, 48,  7,  8, 13, 57,  4, 51, 40, 39, 56, 32,  9,
       23, 26, 30, 29,  6, 21,  3, 53, 33, 35, 58, 50, 43, 11, 55, 49, 25,
        5, 37, 15, 28,  2, 54, 44, 52, 17, 46, 27,  1, 19, 14, 22, 41, 36,
       10, 47, 31, 34,  0, 20, 18, 24]), 'cur_cost': 247723.0}]
2025-06-26 19:06:45,465 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:06:45,465 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 11, 'total_searches': 295, 'skip_rate': 0.03728813559322034, 'estimated_time_saved': 55, 'path_optimizer': {'path_count': 284, 'cache_hits': 184, 'similarity_calculations': 4195, 'cache_hit_rate': 0.043861740166865316, 'cache_size': 4011}}
2025-06-26 19:06:45,466 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 19:06:45,466 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:06:45,466 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:06:45,466 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:06:45,466 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 211946.0
2025-06-26 19:06:45,970 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:06:45,970 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:06:45,970 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:06:45,981 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:06:45,981 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 41, 15, 50, 29, 4, 52, 18, 43, 10, 31, 6, 48, 21, 39, 14, 54, 26, 9, 33, 1, 46, 20, 58, 13, 35, 5, 49, 24, 57, 11, 38, 3, 44, 17, 51, 28, 0, 42, 16, 53, 25, 47, 22, 36, 30, 55, 27, 40, 32], 'cur_cost': 326289.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 41975.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': array([34, 17, 40, 27, 12, 15, 11, 36, 23, 24,  8, 41, 35, 21, 22, 31, 10,
       13, 47, 44,  7, 58,  6,  0, 43, 45, 19,  9, 42, 16, 49, 28, 51, 18,
       26, 46,  1, 55, 50, 39, 32, 33, 30, 37, 48, 29,  4, 52, 25, 57,  5,
       54, 38,  3, 53, 56,  2, 14, 20]), 'cur_cost': 241948.0}, {'tour': array([ 1, 18,  9, 49, 52, 56,  0, 20,  6, 11, 13, 51, 47, 26, 32, 24, 46,
       38, 33, 31, 28, 40, 45, 48,  7, 37, 58, 41, 12, 17, 16, 53, 15, 14,
        4, 42,  2, 25, 57, 55, 36, 39, 19, 10, 35, 34, 22, 50, 44, 54, 30,
       21,  3, 23,  5, 43, 29,  8, 27]), 'cur_cost': 211946.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': array([42, 12, 38, 16, 45, 48,  7,  8, 13, 57,  4, 51, 40, 39, 56, 32,  9,
       23, 26, 30, 29,  6, 21,  3, 53, 33, 35, 58, 50, 43, 11, 55, 49, 25,
        5, 37, 15, 28,  2, 54, 44, 52, 17, 46, 27,  1, 19, 14, 22, 41, 36,
       10, 47, 31, 34,  0, 20, 18, 24]), 'cur_cost': 247723.0}]
2025-06-26 19:06:45,984 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:06:45,984 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 11, 'total_searches': 296, 'skip_rate': 0.037162162162162164, 'estimated_time_saved': 55, 'path_optimizer': {'path_count': 285, 'cache_hits': 184, 'similarity_calculations': 4207, 'cache_hit_rate': 0.043736629427145235, 'cache_size': 4023}}
2025-06-26 19:06:45,984 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:06:45,984 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:06:45,984 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:06:45,984 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:06:45,985 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 225488.0
2025-06-26 19:06:45,986 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：274,跳过局部搜索
2025-06-26 19:06:45,986 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 19:06:45,986 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([41, 55, 10,  4, 34,  9, 43, 53,  7, 11, 58, 31, 16,  1, 15, 56, 47,
       22, 20, 19, 27, 29, 18, 37, 49,  6,  3, 32, 21, 51,  2,  0, 13, 42,
       52, 48, 50, 14, 57,  8, 28, 38, 17, 30, 12, 25, 46, 23,  5, 40, 36,
       44, 26, 39, 45, 35, 54, 33, 24]), 'cur_cost': 225488.0}
2025-06-26 19:06:45,986 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:06:45,986 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:06:45,986 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:06:45,988 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 237603.0
2025-06-26 19:06:46,489 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:06:46,489 - ExploitationExpert - INFO - res_population_costs: [24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24451, 24456, 24456, 24458, 24475, 24490]
2025-06-26 19:06:46,489 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  3,  5,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15,
       11, 12, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 57, 51, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       22, 13, 18, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  2, 35, 46, 44, 37, 43, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20,
       14, 13, 18, 19, 49, 51, 57, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0, 10,  9,  4,  7,  1,  6, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16,
       15, 11, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35, 46, 39,
       40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52, 56, 47,
       55, 51, 57, 49,  8,  5,  3,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 18, 22, 13, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 21, 12, 11, 15, 17, 16, 20, 14,
       13, 22, 18, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 51, 57, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  2,  3,  5,  8, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,
       43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26, 30, 24, 32,
       29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22, 20, 14, 13,
       18, 19,  6,  1,  7,  4,  9, 10], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  2, 35, 46, 44, 43, 37, 36, 38, 41, 45, 42, 40, 39, 28, 26, 30,
       24, 32, 29, 25, 23, 34, 27, 31, 33, 11, 15, 16, 17, 12, 21, 22, 20,
       14, 13, 18, 19, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54,  8,
        7,  1,  6, 10,  9,  4,  5,  3], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 19, 18, 13, 14, 20, 22, 21, 12, 17, 16, 15, 11,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 51, 57, 49, 19, 18, 13, 14, 20, 22, 21, 17, 16, 15, 11, 12,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 43, 37, 44, 46, 35,  2], dtype=int64), array([ 0,  3,  5,  4,  9, 10,  6,  1,  7,  8, 54, 50, 53, 48, 58, 52, 56,
       47, 55, 57, 51, 49, 18, 13, 22, 14, 20, 16, 17, 15, 11, 12, 21, 19,
       33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 39, 40, 42, 45, 41,
       38, 36, 37, 43, 44, 46, 35,  2], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 35,
       46, 39, 40, 42, 45, 41, 38, 36, 44, 37, 43, 54, 50, 53, 48, 58, 52,
       56, 47, 55, 57, 51, 49, 13, 14, 20, 16, 17, 15, 11, 12, 21, 22, 18,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64), array([ 0,  2,  3,  5,  4,  1,  7,  8, 49, 51, 57, 55, 47, 56, 52, 58, 48,
       53, 50, 54, 43, 37, 44, 36, 38, 41, 45, 42, 40, 39, 46, 35, 28, 26,
       30, 24, 32, 29, 25, 23, 34, 27, 31, 33, 12, 11, 15, 16, 17, 21, 22,
       20, 14, 13, 18, 19,  6, 10,  9], dtype=int64), array([ 0,  2,  3,  5,  8,  7,  1,  6, 19, 18, 22, 21, 12, 11, 15, 17, 16,
       20, 14, 13, 49, 57, 51, 55, 47, 56, 52, 58, 48, 53, 50, 54, 43, 37,
       44, 35, 46, 42, 36, 38, 41, 45, 40, 39, 28, 26, 30, 24, 32, 29, 25,
       23, 34, 27, 31, 33, 10,  9,  4], dtype=int64), array([ 0,  4,  9, 10, 33, 31, 27, 34, 23, 25, 29, 32, 24, 30, 26, 28, 40,
       39, 35, 46, 42, 45, 41, 38, 36, 44, 37, 43, 53, 48, 58, 52, 50, 54,
       49, 57, 56, 47, 55, 51, 18, 13, 14, 20, 22, 17, 16, 15, 11, 12, 21,
       19,  6,  1,  7,  8,  5,  3,  2], dtype=int64)]
2025-06-26 19:06:46,499 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:06:46,499 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 41, 15, 50, 29, 4, 52, 18, 43, 10, 31, 6, 48, 21, 39, 14, 54, 26, 9, 33, 1, 46, 20, 58, 13, 35, 5, 49, 24, 57, 11, 38, 3, 44, 17, 51, 28, 0, 42, 16, 53, 25, 47, 22, 36, 30, 55, 27, 40, 32], 'cur_cost': 326289.0}, {'tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 41975.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}, {'tour': array([34, 17, 40, 27, 12, 15, 11, 36, 23, 24,  8, 41, 35, 21, 22, 31, 10,
       13, 47, 44,  7, 58,  6,  0, 43, 45, 19,  9, 42, 16, 49, 28, 51, 18,
       26, 46,  1, 55, 50, 39, 32, 33, 30, 37, 48, 29,  4, 52, 25, 57,  5,
       54, 38,  3, 53, 56,  2, 14, 20]), 'cur_cost': 241948.0}, {'tour': array([ 1, 18,  9, 49, 52, 56,  0, 20,  6, 11, 13, 51, 47, 26, 32, 24, 46,
       38, 33, 31, 28, 40, 45, 48,  7, 37, 58, 41, 12, 17, 16, 53, 15, 14,
        4, 42,  2, 25, 57, 55, 36, 39, 19, 10, 35, 34, 22, 50, 44, 54, 30,
       21,  3, 23,  5, 43, 29,  8, 27]), 'cur_cost': 211946.0}, {'tour': array([41, 55, 10,  4, 34,  9, 43, 53,  7, 11, 58, 31, 16,  1, 15, 56, 47,
       22, 20, 19, 27, 29, 18, 37, 49,  6,  3, 32, 21, 51,  2,  0, 13, 42,
       52, 48, 50, 14, 57,  8, 28, 38, 17, 30, 12, 25, 46, 23,  5, 40, 36,
       44, 26, 39, 45, 35, 54, 33, 24]), 'cur_cost': 225488.0}, {'tour': array([29, 32, 41, 52, 43, 14, 58, 23,  4, 20, 51, 19, 25,  9, 22, 57, 50,
       13, 26, 18,  8, 16, 33, 30, 44,  7, 10, 34, 15, 36, 24, 42, 40, 35,
        5, 28,  1, 11,  3, 46, 45, 37, 38,  2, 31, 53, 49, 21, 48, 55,  6,
       17, 47, 39,  0, 56, 12, 27, 54]), 'cur_cost': 237603.0}]
2025-06-26 19:06:46,500 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:06:46,500 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 12, 'total_searches': 298, 'skip_rate': 0.040268456375838924, 'estimated_time_saved': 60, 'path_optimizer': {'path_count': 286, 'cache_hits': 186, 'similarity_calculations': 4223, 'cache_hit_rate': 0.044044518115084065, 'cache_size': 4037}}
2025-06-26 19:06:46,500 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:06:46,502 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 7, 45, 23, 56, 8, 19, 37, 2, 41, 15, 50, 29, 4, 52, 18, 43, 10, 31, 6, 48, 21, 39, 14, 54, 26, 9, 33, 1, 46, 20, 58, 13, 35, 5, 49, 24, 57, 11, 38, 3, 44, 17, 51, 28, 0, 42, 16, 53, 25, 47, 22, 36, 30, 55, 27, 40, 32], 'cur_cost': 326289.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 1, 13, 25, 37, 49, 2, 14, 26, 38, 50, 3, 15, 27, 39, 51, 4, 16, 28, 40, 52, 5, 17, 29, 41, 53, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47], 'cur_cost': 279411.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 58, 56, 54, 52, 50, 48, 46, 44, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 41975.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 1, 6, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 2, 7, 12, 17, 22, 27, 32, 37, 42, 47, 52, 57, 3, 8, 13, 18, 23, 28, 33, 38, 43, 48, 53, 58, 4, 9, 14, 19, 24, 29, 34, 39, 44, 49, 54], 'cur_cost': 118896.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58], 'cur_cost': 26245.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([41, 55, 10,  4, 34,  9, 43, 53,  7, 11, 58, 31, 16,  1, 15, 56, 47,
       22, 20, 19, 27, 29, 18, 37, 49,  6,  3, 32, 21, 51,  2,  0, 13, 42,
       52, 48, 50, 14, 57,  8, 28, 38, 17, 30, 12, 25, 46, 23,  5, 40, 36,
       44, 26, 39, 45, 35, 54, 33, 24]), 'cur_cost': 225488.0}}]
2025-06-26 19:06:46,502 - __main__ - INFO - 进化阶段完成
2025-06-26 19:06:46,503 - __main__ - INFO - 开始评估阶段
2025-06-26 19:06:46,503 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:06:46,504 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:06:46,504 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 33711.0 → 26245.0 (Δ 7466.0)
- Mean cost: 211718.7 → 173604.6
- Diversity: 0.793 → 0.810 (Δ 0.0172)

#### Elite metrics
- Elite count: 21 → 21
- Best elite cost: 24451 → 24451 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 66.7% , avg Δ 70452.67
- Exploit: 4 inds, success 75.0% , avg Δ -10393.75

#### Other indicators
- No-change individuals: 0
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [26245.0, 26245.0, 33711.0], diversity [0.5092278719397363, 0.8873822975517892, 0.9491525423728815]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:06:46,505 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:06:58,775 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": 70452.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": -10393.75,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "slightly imbalanced towards explore",
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing small mutations to exploit individuals to enhance diversity without losing quality."
  }
}
```
2025-06-26 19:06:58,798 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:06:58,798 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": 70452.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": -10393.75,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "slightly imbalanced towards explore",
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing small mutations to exploit individuals to enhance diversity without losing quality."
  }
}
```
2025-06-26 19:06:58,798 - __main__ - INFO - 评估阶段完成
2025-06-26 19:06:58,798 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": 70452.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": -10393.75,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "slightly imbalanced towards explore",
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing small mutations to exploit individuals to enhance diversity without losing quality."
  }
}
```
2025-06-26 19:06:58,798 - __main__ - INFO - 当前最佳适应度: 26245.0
2025-06-26 19:06:58,798 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite11_59_route_4.pkl
2025-06-26 19:06:58,809 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite11_59_solution.json
2025-06-26 19:06:58,810 - __main__ - INFO - 实例 composite11_59 处理完成
