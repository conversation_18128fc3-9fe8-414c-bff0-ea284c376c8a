import os
import glob
import re
import time
from enhanced_tsp_visualization import read_tsp_file, read_solution_file, plot_enhanced_tsp_path

def plot_all_instances(benchmark_folder, output_folder, dpi=300):
    """
    为benchmark_MMTSP文件夹中所有实例的所有解绘制TSP路径图像
    
    :param benchmark_folder: 包含TSP实例和解决方案的文件夹路径
    :param output_folder: 输出图像的文件夹路径
    :param dpi: 图像分辨率
    :return: 绘制的图像数量
    """
    # 确保输出文件夹存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建输出文件夹: {output_folder}")
    
    # 获取所有TSP文件
    tsp_files = glob.glob(os.path.join(benchmark_folder, "*.tsp"))
    print(f"找到{len(tsp_files)}个TSP实例文件")
    
    # 统计信息
    total_images = 0
    processed_instances = 0
    start_time = time.time()
    
    # 遍历所有TSP文件
    for tsp_file in tsp_files:
        # 从文件名中提取实例名称
        instance_name = os.path.basename(tsp_file).split('.')[0]
        print(f"\n处理实例: {instance_name}")
        
        # 构建对应的solution文件路径
        solution_file = os.path.join(benchmark_folder, f"{instance_name}.solution")
        
        # 检查solution文件是否存在
        if not os.path.exists(solution_file):
            print(f"警告: 未找到{instance_name}的解决方案文件，跳过此实例")
            continue
        
        # 读取城市坐标和解决方案
        cities = read_tsp_file(tsp_file)
        solutions = read_solution_file(solution_file)
        
        if not solutions:
            print(f"警告: {instance_name}的解决方案文件为空，跳过此实例")
            continue
        
        print(f"实例{instance_name}有{len(cities)}个城市和{len(solutions)}个解决方案")
        
        # 为每个解决方案绘制图像
        instance_images = 0
        for i, (cost, path) in enumerate(solutions):
            print(f"  绘制解决方案 {i+1}/{len(solutions)}, 成本: {cost}")
            
            # 绘制并保存增强的TSP路径图
            output_path = plot_enhanced_tsp_path(cities, path, instance_name, cost, i, output_folder, dpi=dpi)
            
            if output_path:
                instance_images += 1
                total_images += 1
                print(f"  图像已保存至: {output_path}")
            else:
                print(f"  警告: 解决方案 {i+1} 绘制失败")
        
        processed_instances += 1
        print(f"实例{instance_name}处理完成，生成了{instance_images}张图像")
        
        # 显示进度
        elapsed_time = time.time() - start_time
        avg_time_per_instance = elapsed_time / processed_instances
        remaining_instances = len(tsp_files) - processed_instances
        estimated_remaining_time = avg_time_per_instance * remaining_instances
        
        print(f"进度: {processed_instances}/{len(tsp_files)} 实例")
        print(f"已用时间: {elapsed_time:.2f}秒, 预计剩余时间: {estimated_remaining_time:.2f}秒")
    
    # 总结
    total_time = time.time() - start_time
    print(f"\n所有实例处理完成!")
    print(f"总共处理了{processed_instances}个实例，生成了{total_images}张图像")
    print(f"总用时: {total_time:.2f}秒, 平均每个实例用时: {total_time/processed_instances:.2f}秒")
    
    return total_images

def main():
    # 设置路径
    benchmark_folder = os.path.join(os.getcwd(), "benchmark_MMTSP")
    output_folder = os.path.join(os.getcwd(), "results", "all_instance_plots")
    
    # 绘制所有实例的图像
    total_images = plot_all_instances(benchmark_folder, output_folder, dpi=300)
    
    print(f"\n程序执行完毕，共生成{total_images}张图像，保存在{output_folder}文件夹中")

if __name__ == "__main__":
    main()