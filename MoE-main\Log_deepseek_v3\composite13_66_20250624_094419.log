2025-06-24 09:44:19,625 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-24 09:44:19,628 - __main__ - INFO - 开始分析阶段
2025-06-24 09:44:19,628 - StatsExpert - INFO - 开始统计分析
2025-06-24 09:44:19,649 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9971.0, 'max': 116634.0, 'mean': 79244.0, 'std': 45404.95914545018}, 'diversity': 0.9114478114478115, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-24 09:44:19,649 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9971.0, 'max': 116634.0, 'mean': 79244.0, 'std': 45404.95914545018}, 'diversity_level': 0.9114478114478115, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-24 09:44:19,650 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-24 09:44:19,650 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-24 09:44:19,650 - PathExpert - INFO - 开始路径结构分析
2025-06-24 09:44:19,654 - PathExpert - INFO - 路径结构分析完成
2025-06-24 09:44:19,654 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (26, 36, 35), 'frequency': 0.3}, {'subpath': (36, 35, 28), 'frequency': 0.3}, {'subpath': (35, 28, 30), 'frequency': 0.3}, {'subpath': (28, 30, 34), 'frequency': 0.3}, {'subpath': (30, 34, 33), 'frequency': 0.3}, {'subpath': (34, 33, 31), 'frequency': 0.3}, {'subpath': (33, 31, 24), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(16, 18)', 'frequency': 0.4}, {'edge': '(13, 20)', 'frequency': 0.4}, {'edge': '(20, 21)', 'frequency': 0.4}, {'edge': '(27, 37)', 'frequency': 0.4}, {'edge': '(26, 36)', 'frequency': 0.4}, {'edge': '(28, 35)', 'frequency': 0.4}, {'edge': '(33, 34)', 'frequency': 0.4}, {'edge': '(4, 5)', 'frequency': 0.4}, {'edge': '(0, 10)', 'frequency': 0.4}, {'edge': '(57, 64)', 'frequency': 0.4}, {'edge': '(39, 44)', 'frequency': 0.4}, {'edge': '(47, 49)', 'frequency': 0.4}, {'edge': '(46, 48)', 'frequency': 0.4}, {'edge': '(38, 56)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(22, 23)', 'frequency': 0.2}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(14, 15)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(3, 32)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 7)', 'frequency': 0.2}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(5, 9)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.3}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(0, 55)', 'frequency': 0.2}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(39, 63)', 'frequency': 0.2}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.2}, {'edge': '(15, 22)', 'frequency': 0.2}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(19, 27)', 'frequency': 0.2}, {'edge': '(39, 43)', 'frequency': 0.2}, {'edge': '(41, 42)', 'frequency': 0.2}, {'edge': '(14, 17)', 'frequency': 0.3}, {'edge': '(43, 63)', 'frequency': 0.2}, {'edge': '(13, 15)', 'frequency': 0.2}, {'edge': '(41, 58)', 'frequency': 0.2}, {'edge': '(58, 65)', 'frequency': 0.2}, {'edge': '(24, 27)', 'frequency': 0.2}, {'edge': '(11, 37)', 'frequency': 0.2}, {'edge': '(11, 63)', 'frequency': 0.2}, {'edge': '(31, 49)', 'frequency': 0.2}, {'edge': '(5, 38)', 'frequency': 0.2}, {'edge': '(2, 48)', 'frequency': 0.2}, {'edge': '(42, 64)', 'frequency': 0.2}, {'edge': '(32, 59)', 'frequency': 0.2}, {'edge': '(19, 60)', 'frequency': 0.2}, {'edge': '(14, 44)', 'frequency': 0.3}, {'edge': '(12, 23)', 'frequency': 0.2}, {'edge': '(26, 56)', 'frequency': 0.2}, {'edge': '(36, 54)', 'frequency': 0.2}, {'edge': '(16, 24)', 'frequency': 0.2}, {'edge': '(15, 59)', 'frequency': 0.2}, {'edge': '(8, 9)', 'frequency': 0.2}, {'edge': '(9, 53)', 'frequency': 0.2}, {'edge': '(5, 63)', 'frequency': 0.2}, {'edge': '(34, 62)', 'frequency': 0.3}, {'edge': '(3, 21)', 'frequency': 0.2}, {'edge': '(0, 4)', 'frequency': 0.2}, {'edge': '(18, 61)', 'frequency': 0.2}, {'edge': '(20, 52)', 'frequency': 0.2}, {'edge': '(20, 59)', 'frequency': 0.2}, {'edge': '(6, 43)', 'frequency': 0.2}, {'edge': '(7, 51)', 'frequency': 0.2}, {'edge': '(4, 50)', 'frequency': 0.2}, {'edge': '(42, 46)', 'frequency': 0.2}, {'edge': '(3, 16)', 'frequency': 0.2}, {'edge': '(5, 22)', 'frequency': 0.2}, {'edge': '(2, 23)', 'frequency': 0.2}, {'edge': '(49, 53)', 'frequency': 0.3}, {'edge': '(1, 13)', 'frequency': 0.2}, {'edge': '(28, 43)', 'frequency': 0.2}, {'edge': '(18, 45)', 'frequency': 0.2}, {'edge': '(5, 50)', 'frequency': 0.2}, {'edge': '(19, 62)', 'frequency': 0.2}, {'edge': '(1, 64)', 'frequency': 0.2}, {'edge': '(19, 35)', 'frequency': 0.2}, {'edge': '(33, 58)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [5, 38, 56, 40, 2, 48, 64, 42], 'cost': 18733.0, 'size': 8}, {'region': [31, 56, 38, 33, 60, 37], 'cost': 13750.0, 'size': 6}, {'region': [47, 4, 50, 60, 41, 56], 'cost': 13356.0, 'size': 6}, {'region': [49, 53, 31, 63, 47], 'cost': 11527.0, 'size': 5}, {'region': [50, 57, 45, 10, 48], 'cost': 10787.0, 'size': 5}]}
2025-06-24 09:44:19,657 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-24 09:44:19,657 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-24 09:44:19,657 - EliteExpert - INFO - 开始精英解分析
2025-06-24 09:44:19,657 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-24 09:44:19,658 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-24 09:44:19,658 - __main__ - INFO - 更新共享数据: elite_features
2025-06-24 09:44:19,659 - LandscapeExpert - INFO - 开始景观分析
2025-06-24 09:44:19,659 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-24 09:44:19,659 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9971.0, mean 79244.0, max 116634.0, std 45404.95914545018
- diversity: 0.9114478114478115
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: [{'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [5, 38, 56, 40, 2, 48, 64, 42], 'cost': 18733.0, 'size': 8}, {'region': [31, 56, 38, 33, 60, 37], 'cost': 13750.0, 'size': 6}, {'region': [47, 4, 50, 60, 41, 56], 'cost': 13356.0, 'size': 6}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness.
2. Evaluate population coverage vs. convergence.
3. List difficult regions to avoid and opportunity regions to target (edge/node sequences).
4. State current evolution phase and recommend next-step actions with concrete operator suggestions.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-24 09:44:25,478 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "The absence of any detailed spatial summary and edge statistics makes it impossible to accurately assess ruggedness. However, the high standard deviation of the cost (45404.959) suggests potentially high variance in fitness landscape values, hinting at some degree of ruggedness. The presence of identified difficult regions further supports this.",
    "modality": "The presence of 'difficult regions' indicates the possibility of multiple local optima, suggesting a multimodal landscape. Further analysis, especially spatial information and edge statistics (which are currently unavailable), would provide a better determination of modality.",
    "deceptiveness": "The identification of 'difficult regions' is indicative of potential deception, where initial exploration might lead the search towards suboptimal areas.  The lack of 'opportunity regions' makes it harder to assess how easily good solutions might be found."
  },
  "population_state": {
    "coverage": "The diversity (0.911) indicates relatively good coverage of the search space at the beginning. However, this is an early iteration. Needs further tracking of diversity metrics in further generations.",
    "convergence": "Convergence is 0.0, as expected in the initial stage. It is essential to monitor it in future iterations to see how the population evolves."
  },
  "difficult_regions": [
    {"region": [5, 38, 56, 40, 2, 48, 64, 42], "cost": 18733.0, "size": 8, "reason": "Costly sequences"},
    {"region": [31, 56, 38, 33, 60, 37], "cost": 13750.0, "size": 6, "reason": "Costly sequences"},
    {"region": [47, 4, 50, 60, 41, 56], "cost": 13356.0, "size": 6, "reason": "Costly sequences"}
  ],
  "opportunity_regions": [],
  "evolution_phase": "Early Exploration/Initialization",
  "evolution_direction": {
    "recommendations": [
      {
        "action": "Continue exploration of the search space. Focus on diversification to prevent premature convergence.",
        "operator_suggestions": [
          "Apply a high mutation rate to introduce substantial changes in solution structures.",
          "Utilize a large population size to maintain diversity."
        ]
      },
      {
        "action": "Analyze the influence of the difficult regions to develop strategies to avoid or circumvent them.",
        "operator_suggestions": [
          "Use a repair operator to reroute paths through the difficult regions.",
          "Implement specialized operators (e.g., local search) to improve path segments after mutation."
        ]
      },
      {
        "action": "Monitor the rate of convergence and diversity to assess the evolutionary progress.",
        "operator_suggestions": [
          "Plot the cost distribution over time.",
          "Track the diversity over time."
        ]
      }
    ]
  }
}
```
2025-06-24 09:44:25,478 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-24 09:44:25,478 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'The absence of any detailed spatial summary and edge statistics makes it impossible to accurately assess ruggedness. However, the high standard deviation of the cost (45404.959) suggests potentially high variance in fitness landscape values, hinting at some degree of ruggedness. The presence of identified difficult regions further supports this.', 'modality': "The presence of 'difficult regions' indicates the possibility of multiple local optima, suggesting a multimodal landscape. Further analysis, especially spatial information and edge statistics (which are currently unavailable), would provide a better determination of modality.", 'deceptiveness': "The identification of 'difficult regions' is indicative of potential deception, where initial exploration might lead the search towards suboptimal areas.  The lack of 'opportunity regions' makes it harder to assess how easily good solutions might be found."}, 'population_state': {'coverage': 'The diversity (0.911) indicates relatively good coverage of the search space at the beginning. However, this is an early iteration. Needs further tracking of diversity metrics in further generations.', 'convergence': 'Convergence is 0.0, as expected in the initial stage. It is essential to monitor it in future iterations to see how the population evolves.'}, 'difficult_regions': [{'region': [5, 38, 56, 40, 2, 48, 64, 42], 'cost': 18733.0, 'size': 8, 'reason': 'Costly sequences'}, {'region': [31, 56, 38, 33, 60, 37], 'cost': 13750.0, 'size': 6, 'reason': 'Costly sequences'}, {'region': [47, 4, 50, 60, 41, 56], 'cost': 13356.0, 'size': 6, 'reason': 'Costly sequences'}], 'opportunity_regions': [], 'evolution_phase': 'Early Exploration/Initialization', 'evolution_direction': {'recommendations': [{'action': 'Continue exploration of the search space. Focus on diversification to prevent premature convergence.', 'operator_suggestions': ['Apply a high mutation rate to introduce substantial changes in solution structures.', 'Utilize a large population size to maintain diversity.']}, {'action': 'Analyze the influence of the difficult regions to develop strategies to avoid or circumvent them.', 'operator_suggestions': ['Use a repair operator to reroute paths through the difficult regions.', 'Implement specialized operators (e.g., local search) to improve path segments after mutation.']}, {'action': 'Monitor the rate of convergence and diversity to assess the evolutionary progress.', 'operator_suggestions': ['Plot the cost distribution over time.', 'Track the diversity over time.']}]}}
2025-06-24 09:44:25,478 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-24 09:44:25,478 - __main__ - INFO - 分析阶段完成
2025-06-24 09:44:25,478 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'The absence of any detailed spatial summary and edge statistics makes it impossible to accurately assess ruggedness. However, the high standard deviation of the cost (45404.959) suggests potentially high variance in fitness landscape values, hinting at some degree of ruggedness. The presence of identified difficult regions further supports this.', 'modality': "The presence of 'difficult regions' indicates the possibility of multiple local optima, suggesting a multimodal landscape. Further analysis, especially spatial information and edge statistics (which are currently unavailable), would provide a better determination of modality.", 'deceptiveness': "The identification of 'difficult regions' is indicative of potential deception, where initial exploration might lead the search towards suboptimal areas.  The lack of 'opportunity regions' makes it harder to assess how easily good solutions might be found."}, 'population_state': {'coverage': 'The diversity (0.911) indicates relatively good coverage of the search space at the beginning. However, this is an early iteration. Needs further tracking of diversity metrics in further generations.', 'convergence': 'Convergence is 0.0, as expected in the initial stage. It is essential to monitor it in future iterations to see how the population evolves.'}, 'difficult_regions': [{'region': [5, 38, 56, 40, 2, 48, 64, 42], 'cost': 18733.0, 'size': 8, 'reason': 'Costly sequences'}, {'region': [31, 56, 38, 33, 60, 37], 'cost': 13750.0, 'size': 6, 'reason': 'Costly sequences'}, {'region': [47, 4, 50, 60, 41, 56], 'cost': 13356.0, 'size': 6, 'reason': 'Costly sequences'}], 'opportunity_regions': [], 'evolution_phase': 'Early Exploration/Initialization', 'evolution_direction': {'recommendations': [{'action': 'Continue exploration of the search space. Focus on diversification to prevent premature convergence.', 'operator_suggestions': ['Apply a high mutation rate to introduce substantial changes in solution structures.', 'Utilize a large population size to maintain diversity.']}, {'action': 'Analyze the influence of the difficult regions to develop strategies to avoid or circumvent them.', 'operator_suggestions': ['Use a repair operator to reroute paths through the difficult regions.', 'Implement specialized operators (e.g., local search) to improve path segments after mutation.']}, {'action': 'Monitor the rate of convergence and diversity to assess the evolutionary progress.', 'operator_suggestions': ['Plot the cost distribution over time.', 'Track the diversity over time.']}]}}
2025-06-24 09:44:25,478 - __main__ - INFO - 开始策略分配阶段
2025-06-24 09:44:25,478 - StrategyExpert - INFO - 开始策略分配分析
2025-06-24 09:44:25,478 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 0
- Population: 10 individuals, diversity=0.7261538461538461
- Best cost: 9971.0, Mean cost: 79244.0
- Search space: Ruggedness: The absence of any detailed spatial summary and edge statistics makes it impossible to accurately assess ruggedness. However, the high standard deviation of the cost (45404.959) suggests potentially high variance in fitness landscape values, hinting at some degree of ruggedness. The presence of identified difficult regions further supports this., Modality: The presence of 'difficult regions' indicates the possibility of multiple local optima, suggesting a multimodal landscape. Further analysis, especially spatial information and edge statistics (which are currently unavailable), would provide a better determination of modality.
- Difficult regions: 3 regions
- Opportunity regions: None identified

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: No previous feedback

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-24 09:44:25,478 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-24 09:44:29,844 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.1,
      "restart_probability": 0.1
    },
    "exploit": {
      "local_search_radius": 0.05,
      "improvement_threshold": 0.01,
      "max_iterations": 20
    }
  }
}
```

2025-06-24 09:44:29,845 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-06-24 09:44:29,845 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-06-24 09:44:29,845 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.1,
      "restart_probability": 0.1
    },
    "exploit": {
      "local_search_radius": 0.05,
      "improvement_threshold": 0.01,
      "max_iterations": 20
    }
  }
}
```

2025-06-24 09:44:29,845 - __main__ - INFO - 策略分配阶段完成
2025-06-24 09:44:29,845 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-06-24 09:44:29,845 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.1,
      "restart_probability": 0.1
    },
    "exploit": {
      "local_search_radius": 0.05,
      "improvement_threshold": 0.01,
      "max_iterations": 20
    }
  }
}
```

2025-06-24 09:44:29,845 - __main__ - INFO - 开始进化阶段
2025-06-24 09:44:29,845 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-24 09:44:29,845 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 09:44:29,845 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [17, 12, 22] and ending with [43, 48, 42]
- Cost: 10053.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7261538461538461
- Unexplored regions: Areas with low edge frequency
- Opportunity regions: None identified
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 09:44:29,845 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 09:44:33,889 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-24 09:44:33,889 - ExplorationExpert - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-24 09:44:33,889 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-24 09:44:33,889 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 09:44:33,889 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [8, 2, 6] and ending with [50, 41, 42]
- Cost: 10213.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7261538461538461
- Unexplored regions: Areas with low edge frequency
- Opportunity regions: None identified
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 09:44:33,889 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 09:44:39,792 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-24 09:44:39,792 - ExplorationExpert - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-24 09:44:39,792 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-24 09:44:39,792 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 09:44:39,792 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [43, 40, 49] and ending with [65, 52, 63]
- Cost: 9971.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7261538461538461
- Unexplored regions: Areas with low edge frequency
- Opportunity regions: None identified
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 09:44:39,792 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 09:44:43,869 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-24 09:44:43,870 - ExplorationExpert - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-24 09:44:43,870 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-24 09:44:43,870 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 09:44:43,881 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 09:44:43,883 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 104088.0
2025-06-24 09:44:46,832 - ExploitationExpert - INFO - res_population_num: 1
2025-06-24 09:44:46,832 - ExploitationExpert - INFO - res_population_costs: [96714]
2025-06-24 09:44:46,832 - ExploitationExpert - INFO - res_populations: [array([ 0, 44, 14, 19, 60, 61, 20, 32, 33, 30, 42, 64, 48,  2, 40, 56, 38,
        5,  4, 49, 31, 51, 54, 62, 55, 47, 22, 10, 24, 27, 17, 46, 37, 11,
       63, 43, 29, 28, 65, 58, 41,  6, 57, 59,  8,  3, 52,  7, 18, 16, 53,
       21, 36, 25, 50, 34,  9, 35, 13, 15,  1, 12, 23, 45, 39, 26],
      dtype=int64)]
2025-06-24 09:44:46,847 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 09:44:46,847 - ExploitationExpert - INFO - populations: [{'tour': [17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10053.0}, {'tour': [8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10213.0}, {'tour': [43, 40, 49, 47, 46, 48, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63], 'cur_cost': 9971.0}, {'tour': array([51, 18, 40, 22, 53, 35, 34, 37, 46, 49,  1, 52, 54, 11, 28,  2, 63,
       32, 56, 24, 27, 38, 12, 45, 36, 10, 50, 61, 33, 23, 60, 62, 19, 16,
       25, 20, 30,  3,  6, 14,  9,  8, 26, 48, 43, 55, 17, 13, 47, 42, 58,
       29,  7, 15,  4, 39, 41, 57, 21,  5, 31, 59, 44, 65, 64,  0]), 'cur_cost': 104088.0}, {'tour': [49, 31, 65, 58, 57, 40, 7, 28, 11, 29, 14, 50, 1, 45, 56, 26, 36, 54, 24, 16, 41, 61, 15, 59, 18, 44, 10, 8, 9, 53, 63, 5, 13, 38, 47, 33, 34, 62, 22, 25, 39, 43, 19, 37, 17, 6, 48, 46, 2, 51, 35, 12, 23, 60, 20, 21, 3, 64, 27, 30, 55, 32, 52, 0, 4, 42], 'cur_cost': 104035.0}, {'tour': [54, 56, 26, 61, 18, 38, 65, 41, 52, 20, 59, 6, 43, 57, 64, 15, 8, 9, 25, 63, 51, 7, 62, 50, 4, 0, 10, 42, 46, 60, 32, 24, 12, 36, 19, 55, 35, 30, 16, 3, 5, 22, 23, 2, 39, 44, 14, 17, 48, 34, 49, 53, 33, 27, 37, 11, 13, 1, 47, 31, 58, 45, 29, 21, 40, 28], 'cur_cost': 110039.0}, {'tour': [47, 7, 29, 59, 36, 15, 43, 28, 39, 57, 41, 1, 56, 38, 12, 32, 4, 9, 65, 22, 34, 26, 23, 14, 45, 18, 64, 44, 54, 6, 5, 50, 35, 48, 58, 10, 13, 24, 21, 11, 25, 62, 19, 60, 17, 30, 8, 46, 42, 55, 40, 51, 52, 20, 16, 3, 61, 33, 37, 0, 2, 27, 49, 53, 31, 63], 'cur_cost': 116634.0}, {'tour': [12, 2, 48, 15, 30, 51, 8, 26, 52, 10, 57, 7, 38, 40, 18, 11, 49, 53, 65, 36, 9, 14, 0, 28, 43, 31, 22, 3, 21, 62, 34, 17, 24, 39, 63, 5, 23, 54, 27, 55, 6, 64, 1, 37, 35, 19, 20, 59, 32, 42, 47, 4, 50, 60, 41, 56, 16, 46, 44, 29, 25, 45, 13, 61, 58, 33], 'cur_cost': 111290.0}, {'tour': [43, 6, 31, 56, 38, 33, 60, 37, 30, 2, 58, 41, 39, 36, 34, 62, 4, 3, 63, 11, 53, 9, 7, 25, 27, 12, 55, 21, 32, 51, 42, 64, 49, 20, 23, 1, 13, 15, 59, 8, 29, 19, 35, 61, 18, 52, 28, 47, 26, 17, 14, 40, 24, 16, 65, 0, 5, 22, 44, 50, 57, 45, 10, 48, 46, 54], 'cur_cost': 104286.0}, {'tour': [19, 52, 16, 25, 21, 43, 60, 26, 30, 47, 49, 46, 24, 27, 57, 32, 63, 10, 17, 58, 33, 23, 2, 29, 55, 31, 41, 37, 34, 39, 12, 13, 20, 65, 15, 11, 4, 8, 64, 1, 14, 44, 51, 7, 6, 53, 42, 22, 48, 18, 45, 59, 61, 36, 54, 3, 50, 5, 38, 56, 0, 40, 9, 28, 35, 62], 'cur_cost': 107442.0}]
2025-06-24 09:44:46,849 - ExploitationExpert - INFO - 局部搜索耗时: 2.97秒
2025-06-24 09:44:46,849 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-24 09:44:46,850 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-24 09:44:46,850 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-24 09:44:46,850 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 09:44:46,850 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [49, 31, 65] and ending with [0, 4, 42]
- Cost: 104035.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7215384615384616
- Unexplored regions: Areas with low edge frequency
- Opportunity regions: None identified
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 09:44:46,852 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 09:44:53,154 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-24 09:44:53,154 - ExplorationExpert - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-24 09:44:53,154 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-24 09:44:53,154 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 09:44:53,154 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 09:44:53,154 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 109387.0
2025-06-24 09:44:54,238 - ExploitationExpert - INFO - res_population_num: 2
2025-06-24 09:44:54,238 - ExploitationExpert - INFO - res_population_costs: [96714, 9610]
2025-06-24 09:44:54,238 - ExploitationExpert - INFO - res_populations: [array([ 0, 44, 14, 19, 60, 61, 20, 32, 33, 30, 42, 64, 48,  2, 40, 56, 38,
        5,  4, 49, 31, 51, 54, 62, 55, 47, 22, 10, 24, 27, 17, 46, 37, 11,
       63, 43, 29, 28, 65, 58, 41,  6, 57, 59,  8,  3, 52,  7, 18, 16, 53,
       21, 36, 25, 50, 34,  9, 35, 13, 15,  1, 12, 23, 45, 39, 26],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-24 09:44:54,238 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 09:44:54,238 - ExploitationExpert - INFO - populations: [{'tour': [17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10053.0}, {'tour': [8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10213.0}, {'tour': [43, 40, 49, 47, 46, 48, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63], 'cur_cost': 9971.0}, {'tour': array([51, 18, 40, 22, 53, 35, 34, 37, 46, 49,  1, 52, 54, 11, 28,  2, 63,
       32, 56, 24, 27, 38, 12, 45, 36, 10, 50, 61, 33, 23, 60, 62, 19, 16,
       25, 20, 30,  3,  6, 14,  9,  8, 26, 48, 43, 55, 17, 13, 47, 42, 58,
       29,  7, 15,  4, 39, 41, 57, 21,  5, 31, 59, 44, 65, 64,  0]), 'cur_cost': 104088.0}, {'tour': [49, 31, 65, 58, 57, 40, 7, 28, 11, 29, 14, 50, 1, 45, 56, 26, 36, 54, 24, 16, 41, 61, 15, 59, 18, 44, 10, 8, 9, 53, 63, 5, 13, 38, 47, 33, 34, 62, 22, 25, 39, 43, 19, 37, 17, 6, 48, 46, 2, 51, 35, 12, 23, 60, 20, 21, 3, 64, 27, 30, 55, 32, 52, 0, 4, 42], 'cur_cost': 104035.0}, {'tour': array([ 9, 54, 10, 56, 13,  8, 47,  4,  0, 24,  2, 11, 32, 38, 17, 44, 12,
       41, 50, 52, 29, 16, 51, 35, 28, 34, 58, 53, 43,  3, 30, 45, 26, 61,
       55, 15,  1, 57, 40,  5, 48, 42, 36, 62, 59, 19, 21, 65, 14, 60, 22,
       23, 27, 64, 37, 46, 20, 39,  7, 49, 63, 25, 31, 18,  6, 33]), 'cur_cost': 109387.0}, {'tour': [47, 7, 29, 59, 36, 15, 43, 28, 39, 57, 41, 1, 56, 38, 12, 32, 4, 9, 65, 22, 34, 26, 23, 14, 45, 18, 64, 44, 54, 6, 5, 50, 35, 48, 58, 10, 13, 24, 21, 11, 25, 62, 19, 60, 17, 30, 8, 46, 42, 55, 40, 51, 52, 20, 16, 3, 61, 33, 37, 0, 2, 27, 49, 53, 31, 63], 'cur_cost': 116634.0}, {'tour': [12, 2, 48, 15, 30, 51, 8, 26, 52, 10, 57, 7, 38, 40, 18, 11, 49, 53, 65, 36, 9, 14, 0, 28, 43, 31, 22, 3, 21, 62, 34, 17, 24, 39, 63, 5, 23, 54, 27, 55, 6, 64, 1, 37, 35, 19, 20, 59, 32, 42, 47, 4, 50, 60, 41, 56, 16, 46, 44, 29, 25, 45, 13, 61, 58, 33], 'cur_cost': 111290.0}, {'tour': [43, 6, 31, 56, 38, 33, 60, 37, 30, 2, 58, 41, 39, 36, 34, 62, 4, 3, 63, 11, 53, 9, 7, 25, 27, 12, 55, 21, 32, 51, 42, 64, 49, 20, 23, 1, 13, 15, 59, 8, 29, 19, 35, 61, 18, 52, 28, 47, 26, 17, 14, 40, 24, 16, 65, 0, 5, 22, 44, 50, 57, 45, 10, 48, 46, 54], 'cur_cost': 104286.0}, {'tour': [19, 52, 16, 25, 21, 43, 60, 26, 30, 47, 49, 46, 24, 27, 57, 32, 63, 10, 17, 58, 33, 23, 2, 29, 55, 31, 41, 37, 34, 39, 12, 13, 20, 65, 15, 11, 4, 8, 64, 1, 14, 44, 51, 7, 6, 53, 42, 22, 48, 18, 45, 59, 61, 36, 54, 3, 50, 5, 38, 56, 0, 40, 9, 28, 35, 62], 'cur_cost': 107442.0}]
2025-06-24 09:44:54,238 - ExploitationExpert - INFO - 局部搜索耗时: 1.08秒
2025-06-24 09:44:54,243 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-24 09:44:54,243 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-24 09:44:54,244 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-24 09:44:54,244 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 09:44:54,244 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 09:44:54,244 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 100356.0
2025-06-24 09:44:54,746 - ExploitationExpert - INFO - res_population_num: 9
2025-06-24 09:44:54,746 - ExploitationExpert - INFO - res_population_costs: [96714, 9610, 9587, 9541, 9533, 9526, 9521, 9521, 9521]
2025-06-24 09:44:54,746 - ExploitationExpert - INFO - res_populations: [array([ 0, 44, 14, 19, 60, 61, 20, 32, 33, 30, 42, 64, 48,  2, 40, 56, 38,
        5,  4, 49, 31, 51, 54, 62, 55, 47, 22, 10, 24, 27, 17, 46, 37, 11,
       63, 43, 29, 28, 65, 58, 41,  6, 57, 59,  8,  3, 52,  7, 18, 16, 53,
       21, 36, 25, 50, 34,  9, 35, 13, 15,  1, 12, 23, 45, 39, 26],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 23, 18, 16, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43,
       42, 46, 48, 49, 47, 39, 44, 45, 50, 51, 38, 41, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-24 09:44:54,751 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 09:44:54,751 - ExploitationExpert - INFO - populations: [{'tour': [17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10053.0}, {'tour': [8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10213.0}, {'tour': [43, 40, 49, 47, 46, 48, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63], 'cur_cost': 9971.0}, {'tour': array([51, 18, 40, 22, 53, 35, 34, 37, 46, 49,  1, 52, 54, 11, 28,  2, 63,
       32, 56, 24, 27, 38, 12, 45, 36, 10, 50, 61, 33, 23, 60, 62, 19, 16,
       25, 20, 30,  3,  6, 14,  9,  8, 26, 48, 43, 55, 17, 13, 47, 42, 58,
       29,  7, 15,  4, 39, 41, 57, 21,  5, 31, 59, 44, 65, 64,  0]), 'cur_cost': 104088.0}, {'tour': [49, 31, 65, 58, 57, 40, 7, 28, 11, 29, 14, 50, 1, 45, 56, 26, 36, 54, 24, 16, 41, 61, 15, 59, 18, 44, 10, 8, 9, 53, 63, 5, 13, 38, 47, 33, 34, 62, 22, 25, 39, 43, 19, 37, 17, 6, 48, 46, 2, 51, 35, 12, 23, 60, 20, 21, 3, 64, 27, 30, 55, 32, 52, 0, 4, 42], 'cur_cost': 104035.0}, {'tour': array([ 9, 54, 10, 56, 13,  8, 47,  4,  0, 24,  2, 11, 32, 38, 17, 44, 12,
       41, 50, 52, 29, 16, 51, 35, 28, 34, 58, 53, 43,  3, 30, 45, 26, 61,
       55, 15,  1, 57, 40,  5, 48, 42, 36, 62, 59, 19, 21, 65, 14, 60, 22,
       23, 27, 64, 37, 46, 20, 39,  7, 49, 63, 25, 31, 18,  6, 33]), 'cur_cost': 109387.0}, {'tour': array([27, 44,  7, 17, 42, 40, 21, 61, 63, 48, 50, 33, 45,  6, 41, 28, 18,
       47,  5, 16, 54, 56, 13, 64, 23,  1,  9, 15, 24, 30, 19,  0, 14, 46,
       37,  4, 12,  3, 35, 58, 10, 31, 34, 26,  8, 51, 36, 57, 11, 39, 52,
       22, 25, 32, 60, 62, 43,  2, 59, 49, 38, 55, 53, 65, 20, 29]), 'cur_cost': 100356.0}, {'tour': [12, 2, 48, 15, 30, 51, 8, 26, 52, 10, 57, 7, 38, 40, 18, 11, 49, 53, 65, 36, 9, 14, 0, 28, 43, 31, 22, 3, 21, 62, 34, 17, 24, 39, 63, 5, 23, 54, 27, 55, 6, 64, 1, 37, 35, 19, 20, 59, 32, 42, 47, 4, 50, 60, 41, 56, 16, 46, 44, 29, 25, 45, 13, 61, 58, 33], 'cur_cost': 111290.0}, {'tour': [43, 6, 31, 56, 38, 33, 60, 37, 30, 2, 58, 41, 39, 36, 34, 62, 4, 3, 63, 11, 53, 9, 7, 25, 27, 12, 55, 21, 32, 51, 42, 64, 49, 20, 23, 1, 13, 15, 59, 8, 29, 19, 35, 61, 18, 52, 28, 47, 26, 17, 14, 40, 24, 16, 65, 0, 5, 22, 44, 50, 57, 45, 10, 48, 46, 54], 'cur_cost': 104286.0}, {'tour': [19, 52, 16, 25, 21, 43, 60, 26, 30, 47, 49, 46, 24, 27, 57, 32, 63, 10, 17, 58, 33, 23, 2, 29, 55, 31, 41, 37, 34, 39, 12, 13, 20, 65, 15, 11, 4, 8, 64, 1, 14, 44, 51, 7, 6, 53, 42, 22, 48, 18, 45, 59, 61, 36, 54, 3, 50, 5, 38, 56, 0, 40, 9, 28, 35, 62], 'cur_cost': 107442.0}]
2025-06-24 09:44:54,752 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 09:44:54,752 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-24 09:44:54,752 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-24 09:44:54,752 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-24 09:44:54,752 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 09:44:54,754 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [12, 2, 48] and ending with [61, 58, 33]
- Cost: 111290.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7338461538461538
- Unexplored regions: Areas with low edge frequency
- Opportunity regions: None identified
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 09:44:54,754 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 09:44:58,662 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-24 09:44:58,662 - ExplorationExpert - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-24 09:44:58,662 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-24 09:44:58,662 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 09:44:58,662 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 09:44:58,662 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 94037.0
2025-06-24 09:44:59,164 - ExploitationExpert - INFO - res_population_num: 12
2025-06-24 09:44:59,164 - ExploitationExpert - INFO - res_population_costs: [96714, 9610, 9587, 9541, 9533, 9526, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-24 09:44:59,164 - ExploitationExpert - INFO - res_populations: [array([ 0, 44, 14, 19, 60, 61, 20, 32, 33, 30, 42, 64, 48,  2, 40, 56, 38,
        5,  4, 49, 31, 51, 54, 62, 55, 47, 22, 10, 24, 27, 17, 46, 37, 11,
       63, 43, 29, 28, 65, 58, 41,  6, 57, 59,  8,  3, 52,  7, 18, 16, 53,
       21, 36, 25, 50, 34,  9, 35, 13, 15,  1, 12, 23, 45, 39, 26],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 23, 18, 16, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43,
       42, 46, 48, 49, 47, 39, 44, 45, 50, 51, 38, 41, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-24 09:44:59,167 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 09:44:59,167 - ExploitationExpert - INFO - populations: [{'tour': [17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10053.0}, {'tour': [8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10213.0}, {'tour': [43, 40, 49, 47, 46, 48, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63], 'cur_cost': 9971.0}, {'tour': array([51, 18, 40, 22, 53, 35, 34, 37, 46, 49,  1, 52, 54, 11, 28,  2, 63,
       32, 56, 24, 27, 38, 12, 45, 36, 10, 50, 61, 33, 23, 60, 62, 19, 16,
       25, 20, 30,  3,  6, 14,  9,  8, 26, 48, 43, 55, 17, 13, 47, 42, 58,
       29,  7, 15,  4, 39, 41, 57, 21,  5, 31, 59, 44, 65, 64,  0]), 'cur_cost': 104088.0}, {'tour': [49, 31, 65, 58, 57, 40, 7, 28, 11, 29, 14, 50, 1, 45, 56, 26, 36, 54, 24, 16, 41, 61, 15, 59, 18, 44, 10, 8, 9, 53, 63, 5, 13, 38, 47, 33, 34, 62, 22, 25, 39, 43, 19, 37, 17, 6, 48, 46, 2, 51, 35, 12, 23, 60, 20, 21, 3, 64, 27, 30, 55, 32, 52, 0, 4, 42], 'cur_cost': 104035.0}, {'tour': array([ 9, 54, 10, 56, 13,  8, 47,  4,  0, 24,  2, 11, 32, 38, 17, 44, 12,
       41, 50, 52, 29, 16, 51, 35, 28, 34, 58, 53, 43,  3, 30, 45, 26, 61,
       55, 15,  1, 57, 40,  5, 48, 42, 36, 62, 59, 19, 21, 65, 14, 60, 22,
       23, 27, 64, 37, 46, 20, 39,  7, 49, 63, 25, 31, 18,  6, 33]), 'cur_cost': 109387.0}, {'tour': array([27, 44,  7, 17, 42, 40, 21, 61, 63, 48, 50, 33, 45,  6, 41, 28, 18,
       47,  5, 16, 54, 56, 13, 64, 23,  1,  9, 15, 24, 30, 19,  0, 14, 46,
       37,  4, 12,  3, 35, 58, 10, 31, 34, 26,  8, 51, 36, 57, 11, 39, 52,
       22, 25, 32, 60, 62, 43,  2, 59, 49, 38, 55, 53, 65, 20, 29]), 'cur_cost': 100356.0}, {'tour': [12, 2, 48, 15, 30, 51, 8, 26, 52, 10, 57, 7, 38, 40, 18, 11, 49, 53, 65, 36, 9, 14, 0, 28, 43, 31, 22, 3, 21, 62, 34, 17, 24, 39, 63, 5, 23, 54, 27, 55, 6, 64, 1, 37, 35, 19, 20, 59, 32, 42, 47, 4, 50, 60, 41, 56, 16, 46, 44, 29, 25, 45, 13, 61, 58, 33], 'cur_cost': 111290.0}, {'tour': array([32, 40,  4, 13, 51, 29, 35, 53, 61, 65,  6, 45, 60,  2, 27, 25, 24,
       20, 10, 33, 64, 31, 28, 26, 30, 50,  5, 21, 49, 37,  7, 47, 19, 43,
       58, 59, 63, 52, 23, 12, 48, 39, 42, 17, 14, 38,  9, 15, 11, 54,  3,
        0, 46, 62,  8,  1, 18, 57, 34, 16, 22, 36, 44, 56, 41, 55]), 'cur_cost': 94037.0}, {'tour': [19, 52, 16, 25, 21, 43, 60, 26, 30, 47, 49, 46, 24, 27, 57, 32, 63, 10, 17, 58, 33, 23, 2, 29, 55, 31, 41, 37, 34, 39, 12, 13, 20, 65, 15, 11, 4, 8, 64, 1, 14, 44, 51, 7, 6, 53, 42, 22, 48, 18, 45, 59, 61, 36, 54, 3, 50, 5, 38, 56, 0, 40, 9, 28, 35, 62], 'cur_cost': 107442.0}]
2025-06-24 09:44:59,170 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 09:44:59,170 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-24 09:44:59,170 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-24 09:44:59,171 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-24 09:44:59,171 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 09:44:59,171 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 09:44:59,171 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 109062.0
2025-06-24 09:44:59,673 - ExploitationExpert - INFO - res_population_num: 14
2025-06-24 09:44:59,673 - ExploitationExpert - INFO - res_population_costs: [96714, 9610, 9587, 9541, 9533, 9526, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-24 09:44:59,674 - ExploitationExpert - INFO - res_populations: [array([ 0, 44, 14, 19, 60, 61, 20, 32, 33, 30, 42, 64, 48,  2, 40, 56, 38,
        5,  4, 49, 31, 51, 54, 62, 55, 47, 22, 10, 24, 27, 17, 46, 37, 11,
       63, 43, 29, 28, 65, 58, 41,  6, 57, 59,  8,  3, 52,  7, 18, 16, 53,
       21, 36, 25, 50, 34,  9, 35, 13, 15,  1, 12, 23, 45, 39, 26],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 46, 47, 49, 48, 43, 40, 19, 13,
       21, 20, 14, 15, 22, 23, 16, 18, 12, 17, 56, 58, 60, 59, 62, 53, 64,
       57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 17, 12, 22, 15, 14, 23, 18, 16, 13, 20, 21,
       19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43,
       42, 46, 48, 49, 47, 39, 44, 45, 50, 51, 38, 41, 58, 56, 59, 62, 60,
       54, 57, 64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-24 09:44:59,679 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 09:44:59,679 - ExploitationExpert - INFO - populations: [{'tour': [17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 14, 15, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 10053.0}, {'tour': [8, 2, 6, 4, 5, 9, 11, 7, 3, 1, 0, 10, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10213.0}, {'tour': [43, 40, 49, 47, 46, 48, 39, 44, 45, 38, 51, 50, 41, 42, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63], 'cur_cost': 9971.0}, {'tour': array([51, 18, 40, 22, 53, 35, 34, 37, 46, 49,  1, 52, 54, 11, 28,  2, 63,
       32, 56, 24, 27, 38, 12, 45, 36, 10, 50, 61, 33, 23, 60, 62, 19, 16,
       25, 20, 30,  3,  6, 14,  9,  8, 26, 48, 43, 55, 17, 13, 47, 42, 58,
       29,  7, 15,  4, 39, 41, 57, 21,  5, 31, 59, 44, 65, 64,  0]), 'cur_cost': 104088.0}, {'tour': [49, 31, 65, 58, 57, 40, 7, 28, 11, 29, 14, 50, 1, 45, 56, 26, 36, 54, 24, 16, 41, 61, 15, 59, 18, 44, 10, 8, 9, 53, 63, 5, 13, 38, 47, 33, 34, 62, 22, 25, 39, 43, 19, 37, 17, 6, 48, 46, 2, 51, 35, 12, 23, 60, 20, 21, 3, 64, 27, 30, 55, 32, 52, 0, 4, 42], 'cur_cost': 104035.0}, {'tour': array([ 9, 54, 10, 56, 13,  8, 47,  4,  0, 24,  2, 11, 32, 38, 17, 44, 12,
       41, 50, 52, 29, 16, 51, 35, 28, 34, 58, 53, 43,  3, 30, 45, 26, 61,
       55, 15,  1, 57, 40,  5, 48, 42, 36, 62, 59, 19, 21, 65, 14, 60, 22,
       23, 27, 64, 37, 46, 20, 39,  7, 49, 63, 25, 31, 18,  6, 33]), 'cur_cost': 109387.0}, {'tour': array([27, 44,  7, 17, 42, 40, 21, 61, 63, 48, 50, 33, 45,  6, 41, 28, 18,
       47,  5, 16, 54, 56, 13, 64, 23,  1,  9, 15, 24, 30, 19,  0, 14, 46,
       37,  4, 12,  3, 35, 58, 10, 31, 34, 26,  8, 51, 36, 57, 11, 39, 52,
       22, 25, 32, 60, 62, 43,  2, 59, 49, 38, 55, 53, 65, 20, 29]), 'cur_cost': 100356.0}, {'tour': [12, 2, 48, 15, 30, 51, 8, 26, 52, 10, 57, 7, 38, 40, 18, 11, 49, 53, 65, 36, 9, 14, 0, 28, 43, 31, 22, 3, 21, 62, 34, 17, 24, 39, 63, 5, 23, 54, 27, 55, 6, 64, 1, 37, 35, 19, 20, 59, 32, 42, 47, 4, 50, 60, 41, 56, 16, 46, 44, 29, 25, 45, 13, 61, 58, 33], 'cur_cost': 111290.0}, {'tour': array([32, 40,  4, 13, 51, 29, 35, 53, 61, 65,  6, 45, 60,  2, 27, 25, 24,
       20, 10, 33, 64, 31, 28, 26, 30, 50,  5, 21, 49, 37,  7, 47, 19, 43,
       58, 59, 63, 52, 23, 12, 48, 39, 42, 17, 14, 38,  9, 15, 11, 54,  3,
        0, 46, 62,  8,  1, 18, 57, 34, 16, 22, 36, 44, 56, 41, 55]), 'cur_cost': 94037.0}, {'tour': array([ 9, 20, 55, 16, 65, 28, 11, 61,  5, 58, 33, 59, 57, 34, 39, 10,  2,
       54, 40, 43, 30,  7, 53,  3, 60, 32,  0, 35, 36, 27, 38, 26, 46, 15,
       29, 19,  1, 18, 44, 31, 17, 21, 62,  6, 24, 25, 63, 48, 52, 13, 64,
       23,  4, 42, 22, 45, 47, 12,  8, 50, 49, 56, 51, 37, 14, 41]), 'cur_cost': 109062.0}]
2025-06-24 09:44:59,681 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 09:44:59,681 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-24 09:44:59,681 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-24 09:44:59,682 - __main__ - INFO - 进化阶段报告汇总: []
2025-06-24 09:44:59,682 - __main__ - INFO - 进化阶段完成
2025-06-24 09:44:59,682 - __main__ - INFO - 开始评估阶段
2025-06-24 09:44:59,682 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-24 09:44:59,683 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-24 09:44:59,683 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 0/5
- Population changes:
  * Cost: min 9971.0->9971.0, mean 79244.0->76249.2
  * Diversity: 0.7261538461538461->0.7246153846153847
  * Elite solutions: 3->3

## Strategy Performance:
- Explore: 5 individuals, success rate 0.0%, avg improvement 0.0
- Exploit: 5 individuals, success rate 80.0%, avg improvement 5989.6
- Overall improvement: 0.0

## Historical Trends:
No historical data available

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-24 09:45:03,812 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: None
2025-06-24 09:45:03,841 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-24 09:45:03,843 - __main__ - INFO - 评估阶段完整报告: None
2025-06-24 09:45:03,843 - __main__ - INFO - 评估阶段完成
2025-06-24 09:45:03,843 - __main__ - INFO - 评估完整报告: None
2025-06-24 09:45:03,843 - __main__ - INFO - 当前最佳适应度: 9971.0
2025-06-24 09:45:03,843 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-24 09:45:03,843 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-24 09:45:03,843 - __main__ - INFO - 开始分析阶段
2025-06-24 09:45:03,843 - StatsExpert - INFO - 开始统计分析
2025-06-24 09:45:03,862 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9971.0, 'max': 111290.0, 'mean': 76249.2, 'std': 43569.21022878427}, 'diversity': 0.9138047138047137, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-24 09:45:03,862 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9971.0, 'max': 111290.0, 'mean': 76249.2, 'std': 43569.21022878427}, 'diversity_level': 0.9138047138047137, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-24 09:45:03,863 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-24 09:45:03,863 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-24 09:45:03,863 - PathExpert - INFO - 开始路径结构分析
2025-06-24 09:45:03,866 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-24 09:45:03,866 - PathExpert - INFO - 路径结构分析完成
2025-06-24 09:45:03,866 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-24 09:45:03,866 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-24 09:45:03,866 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-24 09:45:03,866 - EliteExpert - INFO - 开始精英解分析
2025-06-24 09:45:03,870 - EliteExpert - INFO - 精英解分析完成
2025-06-24 09:45:03,871 - __main__ - INFO - 精英专家分析报告: {'elite_count': 14, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 450.0, 'avg_gap': 60486.41428571429}, 'structure_gap': {'unique_elite_edges': 174, 'unique_pop_edges': 437, 'common_edges': 97}}, 'elite_diversity': {'diversity_score': 0.3261738261738262}}
2025-06-24 09:45:03,871 - __main__ - INFO - 更新共享数据: elite_features
2025-06-24 09:45:03,871 - LandscapeExpert - INFO - 开始景观分析
2025-06-24 09:45:03,872 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-24 09:45:03,872 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 9971.0, mean 76249.2, max 111290.0, std 43569.21022878427
- diversity: 0.9138047138047137
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [9971.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: N/A
- centroid/spread: N/A | rms N/A
- density_grid(3x3): N/A
- edge_len_stats(min,q1,med,q3,max): N/A
- long_edge_ratio: N/A
- direction_hist(8 sectors): N/A
- nn_median_dist: N/A

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness.
2. Evaluate population coverage vs. convergence.
3. List difficult regions to avoid and opportunity regions to target (edge/node sequences).
4. State current evolution phase and recommend next-step actions with concrete operator suggestions.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-24 09:45:08,084 - LandscapeExpert - INFO - LLM返回的分析结果: None
