2025-06-26 20:36:12,879 - __main__ - INFO - kroA100 开始进化第 1 代
2025-06-26 20:36:12,880 - __main__ - INFO - 开始分析阶段
2025-06-26 20:36:12,880 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:36:12,915 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 26309.0, 'max': 180049.0, 'mean': 126406.8, 'std': 65680.55201473265}, 'diversity': 0.9313333333333335, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:36:12,915 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 26309.0, 'max': 180049.0, 'mean': 126406.8, 'std': 65680.55201473265}, 'diversity_level': 0.9313333333333335, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[1380, 939], [2848, 96], [3510, 1671], [457, 334], [3888, 666], [984, 965], [2721, 1482], [1286, 525], [2716, 1432], [738, 1325], [1251, 1832], [2728, 1698], [3815, 169], [3683, 1533], [1247, 1945], [123, 862], [1234, 1946], [252, 1240], [611, 673], [2576, 1676], [928, 1700], [53, 857], [1807, 1711], [274, 1420], [2574, 946], [178, 24], [2678, 1825], [1795, 962], [3384, 1498], [3520, 1079], [1256, 61], [1424, 1728], [3913, 192], [3085, 1528], [2573, 1969], [463, 1670], [3875, 598], [298, 1513], [3479, 821], [2542, 236], [3955, 1743], [1323, 280], [3447, 1830], [2936, 337], [1621, 1830], [3373, 1646], [1393, 1368], [3874, 1318], [938, 955], [3022, 474], [2482, 1183], [3854, 923], [376, 825], [2519, 135], [2945, 1622], [953, 268], [2628, 1479], [2097, 981], [890, 1846], [2139, 1806], [2421, 1007], [2290, 1810], [1115, 1052], [2588, 302], [327, 265], [241, 341], [1917, 687], [2991, 792], [2573, 599], [19, 674], [3911, 1673], [872, 1559], [2863, 558], [929, 1766], [839, 620], [3893, 102], [2178, 1619], [3822, 899], [378, 1048], [1178, 100], [2599, 901], [3416, 143], [2961, 1605], [611, 1384], [3113, 885], [2597, 1830], [2586, 1286], [161, 906], [1429, 134], [742, 1025], [1625, 1651], [1187, 706], [1787, 1009], [22, 987], [3640, 43], [3756, 882], [776, 392], [1724, 1642], [198, 1810], [3950, 1558]], 'distance_matrix': array([[   0., 1693., 2252., ...,  783., 1468., 2643.],
       [1693.,    0., 1708., ..., 1911., 3156., 1831.],
       [2252., 1708.,    0., ..., 1786., 3315.,  454.],
       ...,
       [ 783., 1911., 1786., ...,    0., 1535., 2228.],
       [1468., 3156., 3315., ..., 1535.,    0., 3760.],
       [2643., 1831.,  454., ..., 2228., 3760.,    0.]])}
2025-06-26 20:36:12,915 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:36:12,915 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:36:12,915 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:36:12,923 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:36:12,923 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (90, 97), 'frequency': 0.5, 'avg_cost': 99.0}], 'common_subpaths': [{'subpath': (11, 26, 85), 'frequency': 0.3}, {'subpath': (26, 85, 34), 'frequency': 0.3}, {'subpath': (67, 84, 38), 'frequency': 0.3}, {'subpath': (84, 38, 29), 'frequency': 0.3}, {'subpath': (38, 29, 95), 'frequency': 0.3}, {'subpath': (29, 95, 77), 'frequency': 0.3}, {'subpath': (95, 77, 51), 'frequency': 0.3}, {'subpath': (77, 51, 4), 'frequency': 0.3}, {'subpath': (51, 4, 36), 'frequency': 0.3}, {'subpath': (4, 36, 32), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(90, 97)', 'frequency': 0.5}, {'edge': '(6, 8)', 'frequency': 0.4}, {'edge': '(8, 86)', 'frequency': 0.4}, {'edge': '(1, 53)', 'frequency': 0.4}, {'edge': '(38, 84)', 'frequency': 0.4}, {'edge': '(48, 89)', 'frequency': 0.4}, {'edge': '(10, 31)', 'frequency': 0.4}, {'edge': '(17, 23)', 'frequency': 0.4}, {'edge': '(15, 87)', 'frequency': 0.4}, {'edge': '(3, 64)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(44, 90)', 'frequency': 0.2}, {'edge': '(22, 97)', 'frequency': 0.3}, {'edge': '(59, 61)', 'frequency': 0.3}, {'edge': '(11, 19)', 'frequency': 0.2}, {'edge': '(11, 26)', 'frequency': 0.3}, {'edge': '(26, 85)', 'frequency': 0.3}, {'edge': '(34, 85)', 'frequency': 0.3}, {'edge': '(6, 56)', 'frequency': 0.3}, {'edge': '(50, 86)', 'frequency': 0.3}, {'edge': '(50, 60)', 'frequency': 0.2}, {'edge': '(24, 60)', 'frequency': 0.3}, {'edge': '(24, 80)', 'frequency': 0.3}, {'edge': '(68, 80)', 'frequency': 0.3}, {'edge': '(68, 72)', 'frequency': 0.2}, {'edge': '(49, 72)', 'frequency': 0.3}, {'edge': '(43, 49)', 'frequency': 0.3}, {'edge': '(1, 43)', 'frequency': 0.3}, {'edge': '(39, 53)', 'frequency': 0.3}, {'edge': '(39, 63)', 'frequency': 0.3}, {'edge': '(63, 67)', 'frequency': 0.2}, {'edge': '(67, 84)', 'frequency': 0.3}, {'edge': '(29, 38)', 'frequency': 0.3}, {'edge': '(29, 95)', 'frequency': 0.3}, {'edge': '(77, 95)', 'frequency': 0.3}, {'edge': '(51, 77)', 'frequency': 0.3}, {'edge': '(4, 51)', 'frequency': 0.3}, {'edge': '(4, 36)', 'frequency': 0.3}, {'edge': '(32, 36)', 'frequency': 0.3}, {'edge': '(32, 75)', 'frequency': 0.3}, {'edge': '(12, 75)', 'frequency': 0.3}, {'edge': '(12, 94)', 'frequency': 0.3}, {'edge': '(81, 94)', 'frequency': 0.3}, {'edge': '(47, 81)', 'frequency': 0.2}, {'edge': '(47, 99)', 'frequency': 0.3}, {'edge': '(70, 99)', 'frequency': 0.2}, {'edge': '(40, 70)', 'frequency': 0.3}, {'edge': '(13, 40)', 'frequency': 0.2}, {'edge': '(2, 13)', 'frequency': 0.2}, {'edge': '(2, 45)', 'frequency': 0.3}, {'edge': '(28, 45)', 'frequency': 0.3}, {'edge': '(28, 33)', 'frequency': 0.3}, {'edge': '(33, 82)', 'frequency': 0.3}, {'edge': '(54, 82)', 'frequency': 0.3}, {'edge': '(27, 92)', 'frequency': 0.3}, {'edge': '(0, 62)', 'frequency': 0.2}, {'edge': '(5, 62)', 'frequency': 0.3}, {'edge': '(5, 48)', 'frequency': 0.3}, {'edge': '(9, 83)', 'frequency': 0.3}, {'edge': '(20, 71)', 'frequency': 0.2}, {'edge': '(20, 73)', 'frequency': 0.3}, {'edge': '(58, 73)', 'frequency': 0.2}, {'edge': '(16, 58)', 'frequency': 0.2}, {'edge': '(14, 16)', 'frequency': 0.3}, {'edge': '(10, 14)', 'frequency': 0.3}, {'edge': '(7, 91)', 'frequency': 0.3}, {'edge': '(7, 41)', 'frequency': 0.3}, {'edge': '(41, 88)', 'frequency': 0.3}, {'edge': '(30, 88)', 'frequency': 0.3}, {'edge': '(30, 79)', 'frequency': 0.3}, {'edge': '(55, 79)', 'frequency': 0.3}, {'edge': '(55, 96)', 'frequency': 0.3}, {'edge': '(74, 96)', 'frequency': 0.2}, {'edge': '(18, 74)', 'frequency': 0.3}, {'edge': '(18, 52)', 'frequency': 0.2}, {'edge': '(52, 78)', 'frequency': 0.3}, {'edge': '(17, 78)', 'frequency': 0.2}, {'edge': '(23, 37)', 'frequency': 0.3}, {'edge': '(35, 37)', 'frequency': 0.3}, {'edge': '(35, 98)', 'frequency': 0.3}, {'edge': '(21, 93)', 'frequency': 0.2}, {'edge': '(15, 21)', 'frequency': 0.3}, {'edge': '(65, 69)', 'frequency': 0.2}, {'edge': '(64, 65)', 'frequency': 0.3}, {'edge': '(19, 56)', 'frequency': 0.3}, {'edge': '(34, 61)', 'frequency': 0.2}, {'edge': '(59, 76)', 'frequency': 0.2}, {'edge': '(9, 71)', 'frequency': 0.2}, {'edge': '(52, 87)', 'frequency': 0.2}, {'edge': '(0, 91)', 'frequency': 0.2}, {'edge': '(18, 89)', 'frequency': 0.2}, {'edge': '(27, 66)', 'frequency': 0.2}, {'edge': '(57, 66)', 'frequency': 0.2}, {'edge': '(44, 46)', 'frequency': 0.2}, {'edge': '(57, 60)', 'frequency': 0.2}, {'edge': '(11, 54)', 'frequency': 0.2}, {'edge': '(7, 74)', 'frequency': 0.2}, {'edge': '(67, 94)', 'frequency': 0.2}, {'edge': '(16, 27)', 'frequency': 0.2}, {'edge': '(23, 28)', 'frequency': 0.2}, {'edge': '(11, 59)', 'frequency': 0.2}, {'edge': '(46, 85)', 'frequency': 0.2}, {'edge': '(0, 81)', 'frequency': 0.3}, {'edge': '(15, 51)', 'frequency': 0.2}, {'edge': '(87, 91)', 'frequency': 0.2}, {'edge': '(61, 78)', 'frequency': 0.3}, {'edge': '(58, 78)', 'frequency': 0.2}, {'edge': '(65, 88)', 'frequency': 0.2}, {'edge': '(20, 72)', 'frequency': 0.2}, {'edge': '(17, 31)', 'frequency': 0.2}, {'edge': '(64, 96)', 'frequency': 0.2}, {'edge': '(25, 53)', 'frequency': 0.2}, {'edge': '(30, 76)', 'frequency': 0.2}, {'edge': '(77, 84)', 'frequency': 0.2}, {'edge': '(51, 86)', 'frequency': 0.2}, {'edge': '(19, 87)', 'frequency': 0.2}, {'edge': '(92, 93)', 'frequency': 0.2}, {'edge': '(0, 50)', 'frequency': 0.2}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(13, 58)', 'frequency': 0.2}, {'edge': '(3, 45)', 'frequency': 0.2}, {'edge': '(6, 48)', 'frequency': 0.2}, {'edge': '(25, 89)', 'frequency': 0.2}, {'edge': '(21, 28)', 'frequency': 0.2}, {'edge': '(32, 50)', 'frequency': 0.2}, {'edge': '(38, 80)', 'frequency': 0.2}, {'edge': '(28, 55)', 'frequency': 0.2}, {'edge': '(34, 36)', 'frequency': 0.2}, {'edge': '(24, 71)', 'frequency': 0.2}, {'edge': '(57, 71)', 'frequency': 0.2}, {'edge': '(57, 61)', 'frequency': 0.2}, {'edge': '(78, 79)', 'frequency': 0.2}, {'edge': '(73, 99)', 'frequency': 0.2}, {'edge': '(21, 97)', 'frequency': 0.2}, {'edge': '(25, 46)', 'frequency': 0.2}, {'edge': '(41, 43)', 'frequency': 0.2}, {'edge': '(62, 83)', 'frequency': 0.2}, {'edge': '(18, 27)', 'frequency': 0.2}, {'edge': '(33, 48)', 'frequency': 0.2}, {'edge': '(22, 50)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [11, 21, 84, 23, 29, 37, 39], 'cost': 17850.0, 'size': 7}, {'region': [12, 98, 85, 87, 40], 'cost': 12861.0, 'size': 5}, {'region': [79, 4, 96, 99, 73], 'cost': 12301.0, 'size': 5}, {'region': [28, 9, 2, 15, 45], 'cost': 12271.0, 'size': 5}, {'region': [71, 99, 88, 36, 5], 'cost': 11377.0, 'size': 5}]}
2025-06-26 20:36:12,927 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:36:12,927 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:36:12,927 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:36:12,927 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 20:36:12,928 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 20:36:12,928 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:36:12,928 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:36:12,931 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:36:12,931 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 26309.0, mean 126406.8, max 180049.0, std 65680.55201473265
- diversity: 0.9313333333333335
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [476135.0, 150781.0, 150781.0] (Δ 162677.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (90, 97), 'frequency': 0.5, 'avg_cost': 99.0}]
- common_subpaths_sample: [{'subpath': (11, 26, 85), 'frequency': 0.3}, {'subpath': (26, 85, 34), 'frequency': 0.3}, {'subpath': (67, 84, 38), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [11, 21, 84, 23, 29, 37, 39], 'cost': 17850.0, 'size': 7}, {'region': [12, 98, 85, 87, 40], 'cost': 12861.0, 'size': 5}, {'region': [79, 4, 96, 99, 73], 'cost': 12301.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [19.0, 24.0, 3955.0, 1969.0]
- centroid/spread: [2011.37, 1064.48] | rms 1365.283
- density_grid(3x3): [[11, 5, 11], [14, 10, 8], [13, 13, 15]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 985.0, 1586.0, 2370.5, 4150.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [26, 4, 5, 21, 17, 6, 8, 12]
- nn_median_dist: 140.036

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:36:12,932 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:36:43,176 - InterfaceAPI.deepseek - ERROR - DeepSeek API请求网络错误 (尝试 1/3): HTTPSConnectionPool(host='api.deepseek.com', port=443): Read timed out.
2025-06-26 20:36:43,176 - InterfaceAPI.deepseek - INFO - 等待 2 秒后重试...
2025-06-26 20:36:45,178 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 2/3)
2025-06-26 20:37:15,281 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High (wide edge length range: 13.0 to 4150.0, std 65680.55)",
    "modality": "Potentially multimodal (high diversity 0.93, no clustering info)",
    "deceptiveness": "Moderate (long_edge_ratio 0.25 suggests some deceptive long edges)"
  },
  "population_state": {
    "coverage": "Good (high diversity 0.93, density_grid shows balanced distribution)",
    "convergence": "Early stage (convergence 0.0, no stagnation detected)"
  },
  "difficult_regions": [
    {"region": [11, 21, 84, 23, 29, 37, 39], "reason": "High cost (17850.0) and spatial spread"},
    {"region": [12, 98, 85, 87, 40], "reason": "High cost (12861.0) and density_grid low coverage"},
    {"region": [79, 4, 96, 99, 73], "reason": "High cost (12301.0) and long-edge corridor"}
  ],
  "opportunity_regions": [
    {"region": [90, 97], "reason": "High-quality edge (freq 0.5, avg_cost 99.0)"},
    {"region": [11, 26, 85], "reason": "Common subpath (freq 0.3) in dense cell"},
    {"region": [67, 84, 38], "reason": "Common subpath (freq 0.3) with moderate density"}
  ],
  "evolution_phase": "Exploration (iter 0/5, high diversity, no elites)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Exploit high-quality edges (e.g. (90,97)) with edge-preserving crossover",
    "2. Intensify search in dense cells (e.g. bottom-right grid cell with 15 nodes)",
    "3. Apply local optimization to common subpaths (e.g. (11,26,85))",
    "4. Use long-edge reduction heuristics for difficult regions"
  ]
}
```
2025-06-26 20:37:15,281 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:37:15,281 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High (wide edge length range: 13.0 to 4150.0, std 65680.55)', 'modality': 'Potentially multimodal (high diversity 0.93, no clustering info)', 'deceptiveness': 'Moderate (long_edge_ratio 0.25 suggests some deceptive long edges)'}, 'population_state': {'coverage': 'Good (high diversity 0.93, density_grid shows balanced distribution)', 'convergence': 'Early stage (convergence 0.0, no stagnation detected)'}, 'difficult_regions': [{'region': [11, 21, 84, 23, 29, 37, 39], 'reason': 'High cost (17850.0) and spatial spread'}, {'region': [12, 98, 85, 87, 40], 'reason': 'High cost (12861.0) and density_grid low coverage'}, {'region': [79, 4, 96, 99, 73], 'reason': 'High cost (12301.0) and long-edge corridor'}], 'opportunity_regions': [{'region': [90, 97], 'reason': 'High-quality edge (freq 0.5, avg_cost 99.0)'}, {'region': [11, 26, 85], 'reason': 'Common subpath (freq 0.3) in dense cell'}, {'region': [67, 84, 38], 'reason': 'Common subpath (freq 0.3) with moderate density'}], 'evolution_phase': 'Exploration (iter 0/5, high diversity, no elites)', 'evolution_direction': ['Operator suggestions:', '1. Exploit high-quality edges (e.g. (90,97)) with edge-preserving crossover', '2. Intensify search in dense cells (e.g. bottom-right grid cell with 15 nodes)', '3. Apply local optimization to common subpaths (e.g. (11,26,85))', '4. Use long-edge reduction heuristics for difficult regions']}
2025-06-26 20:37:15,281 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:37:15,281 - __main__ - INFO - 分析阶段完成
2025-06-26 20:37:15,281 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High (wide edge length range: 13.0 to 4150.0, std 65680.55)', 'modality': 'Potentially multimodal (high diversity 0.93, no clustering info)', 'deceptiveness': 'Moderate (long_edge_ratio 0.25 suggests some deceptive long edges)'}, 'population_state': {'coverage': 'Good (high diversity 0.93, density_grid shows balanced distribution)', 'convergence': 'Early stage (convergence 0.0, no stagnation detected)'}, 'difficult_regions': [{'region': [11, 21, 84, 23, 29, 37, 39], 'reason': 'High cost (17850.0) and spatial spread'}, {'region': [12, 98, 85, 87, 40], 'reason': 'High cost (12861.0) and density_grid low coverage'}, {'region': [79, 4, 96, 99, 73], 'reason': 'High cost (12301.0) and long-edge corridor'}], 'opportunity_regions': [{'region': [90, 97], 'reason': 'High-quality edge (freq 0.5, avg_cost 99.0)'}, {'region': [11, 26, 85], 'reason': 'Common subpath (freq 0.3) in dense cell'}, {'region': [67, 84, 38], 'reason': 'Common subpath (freq 0.3) with moderate density'}], 'evolution_phase': 'Exploration (iter 0/5, high diversity, no elites)', 'evolution_direction': ['Operator suggestions:', '1. Exploit high-quality edges (e.g. (90,97)) with edge-preserving crossover', '2. Intensify search in dense cells (e.g. bottom-right grid cell with 15 nodes)', '3. Apply local optimization to common subpaths (e.g. (11,26,85))', '4. Use long-edge reduction heuristics for difficult regions']}
2025-06-26 20:37:15,281 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:37:15,281 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:37:15,281 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 26309.0
  • mean_cost: 126406.8
- Search-space: ruggedness High (wide edge length range: 13.0 to 4150.0, std 65680.55), modality Potentially multimodal (high diversity 0.93, no clustering info), deceptiveness Moderate (long_edge_ratio 0.25 suggests some deceptive long edges)
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [11, 21, , {'region': [12, 98, ...
- Opportunity regions (# 3): {'region': [90, 97],, {'region': [11, 26, ...
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:37:15,281 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:37:15,281 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:37:26,791 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and no elites favors exploration. Top 80% explore to sample space, bottom 20% exploit best current solutions."
}
```
2025-06-26 20:37:26,791 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 20:37:26,791 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 20:37:26,791 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and no elites favors exploration. Top 80% explore to sample space, bottom 20% exploit best current solutions."
}
```
2025-06-26 20:37:26,791 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:37:26,791 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 20:37:26,791 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and no elites favors exploration. Top 80% explore to sample space, bottom 20% exploit best current solutions."
}
```
2025-06-26 20:37:26,793 - __main__ - INFO - 开始进化阶段
2025-06-26 20:37:26,793 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:37:26,793 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:37:26,794 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26484.0 (rank 20.0%)
- Population diversity: 0.7828282828282829
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [90,, {'region': [11,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:37:26,794 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:37:26,794 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:37:47,806 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99],
  "targeted_regions": "Regions 90 and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions while avoiding difficult areas"
}
```
2025-06-26 20:37:47,806 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:37:47,806 - ExplorationExpert - INFO - 探索路径生成完成，成本: 190340.0, 路径: [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99]
2025-06-26 20:37:47,806 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}
2025-06-26 20:37:47,806 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:37:47,806 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:37:47,806 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26309.0 (rank 0.0%)
- Population diversity: 0.8545454545454545
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [90,, {'region': [11,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:37:47,810 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:37:47,810 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:38:08,575 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99],
  "targeted_regions": "90 and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions while avoiding difficult areas"
}
```
2025-06-26 20:38:08,575 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:38:08,575 - ExplorationExpert - INFO - 探索路径生成完成，成本: 190340.0, 路径: [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99]
2025-06-26 20:38:08,575 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}
2025-06-26 20:38:08,575 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:38:08,575 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:38:08,575 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 26478.0 (rank 0.0%)
- Population diversity: 0.8272727272727273
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [90,, {'region': [11,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:38:08,575 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:38:08,575 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:38:29,030 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [90, 11, 12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99],
  "targeted_regions": "90, 11, 12",
  "strategy_comment": "Prioritize opportunity regions, avoid difficult areas"
}
```
2025-06-26 20:38:29,030 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:38:29,030 - ExplorationExpert - INFO - 探索路径生成完成，成本: 192750.0, 路径: [90, 11, 12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99]
2025-06-26 20:38:29,030 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [90, 11, 12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 192750.0}
2025-06-26 20:38:29,030 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:38:29,030 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:38:29,030 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 174713.0 (rank 50.0%)
- Population diversity: 0.7474747474747475
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [90,, {'region': [11,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:38:29,030 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:38:29,030 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:38:50,938 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
  "targeted_regions": "90 and adjacent low-density cells",
  "strategy_comment": "Prioritize opportunity regions while avoiding difficult areas"
}
```
2025-06-26 20:38:50,938 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:38:50,938 - ExplorationExpert - INFO - 探索路径生成完成，成本: 189847.0, 路径: [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
2025-06-26 20:38:50,938 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'cur_cost': 189847.0}
2025-06-26 20:38:50,938 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:38:50,938 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:38:50,938 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 161554.0 (rank 0.0%)
- Population diversity: 0.6606060606060606
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [90,, {'region': [11,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:38:50,938 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:38:50,938 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:39:12,009 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 90, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 1, 11, 12, 91, 92, 93, 94, 95, 96, 97, 98, 99],
  "targeted_regions": "Prioritized region 90, avoided 11-12 early",
  "strategy_comment": "Shift opportunity regions forward, delay difficult areas"
}
```
2025-06-26 20:39:12,010 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:39:12,010 - ExplorationExpert - INFO - 探索路径生成完成，成本: 194687.0, 路径: [0, 90, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 1, 11, 12, 91, 92, 93, 94, 95, 96, 97, 98, 99]
2025-06-26 20:39:12,010 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 90, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 1, 11, 12, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 194687.0}
2025-06-26 20:39:12,010 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:39:12,010 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:39:12,010 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 162642.0 (rank 0.0%)
- Population diversity: 0.5797979797979798
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [90,, {'region': [11,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:39:12,013 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:39:12,013 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:39:33,093 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
  "targeted_regions": "90, adjacent low-density cells",
  "strategy_comment": "Start with opportunity region, spiral outward"
}
```
2025-06-26 20:39:33,093 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:39:33,093 - ExplorationExpert - INFO - 探索路径生成完成，成本: 189847.0, 路径: [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
2025-06-26 20:39:33,093 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'cur_cost': 189847.0}
2025-06-26 20:39:33,093 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:39:33,093 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:39:33,109 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 163290.0 (rank 0.0%)
- Population diversity: 0.4868686868686869
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [90,, {'region': [11,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:39:33,109 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:39:33,109 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:39:55,296 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99],
  "targeted_regions": "90, 11, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid difficult areas, balance exploration"
}
```
2025-06-26 20:39:55,296 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:39:55,296 - ExplorationExpert - INFO - 探索路径生成完成，成本: 190340.0, 路径: [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99]
2025-06-26 20:39:55,296 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}
2025-06-26 20:39:55,296 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 20:39:55,296 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:39:55,296 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 169939.0 (rank 0.0%)
- Population diversity: 0.397979797979798
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [90,, {'region': [11,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:39:55,296 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:39:55,296 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:40:18,095 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99],
  "targeted_regions": "90, 11, adjacent low-density areas",
  "strategy_comment": "Prioritize opportunity regions, avoid difficult areas, balance diversity"
}
```
2025-06-26 20:40:18,095 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:40:18,095 - ExplorationExpert - INFO - 探索路径生成完成，成本: 190340.0, 路径: [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99]
2025-06-26 20:40:18,095 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}
2025-06-26 20:40:18,095 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:40:18,095 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:40:18,095 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:40:18,095 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 173147.0
2025-06-26 20:40:18,597 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:40:18,598 - ExploitationExpert - INFO - res_population_costs: [21709, 21343, 21282]
2025-06-26 20:40:18,598 - ExploitationExpert - INFO - res_populations: [array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64)]
2025-06-26 20:40:18,600 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:40:18,600 - ExploitationExpert - INFO - populations: [{'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}, {'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}, {'tour': [90, 11, 12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 192750.0}, {'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'cur_cost': 189847.0}, {'tour': [0, 90, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 1, 11, 12, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 194687.0}, {'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'cur_cost': 189847.0}, {'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}, {'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}, {'tour': array([69,  2, 87, 60, 63, 14, 27, 76, 15, 78, 51, 80, 12, 65, 55, 34, 45,
       99, 54, 67, 70, 38, 22, 91, 74, 68, 37, 13, 84, 43, 66, 61, 96, 18,
        8,  1, 48, 40, 30, 35,  4, 39, 33, 53, 46, 93, 11, 58, 25, 92, 47,
       49, 31, 26, 89, 41, 50, 57, 98, 75, 94, 83, 72,  0, 62, 19, 29,  9,
       85,  7, 28,  5, 44, 42, 77, 23, 64, 79,  3, 36, 56, 20, 81,  6, 90,
       71, 21, 82, 59, 10, 88, 95, 86, 24, 17, 97, 32, 16, 52, 73]), 'cur_cost': 173147.0}, {'tour': [32, 99, 12, 98, 85, 87, 40, 67, 50, 22, 56, 30, 18, 69, 10, 94, 70, 49, 79, 14, 59, 86, 8, 53, 26, 55, 27, 28, 9, 2, 15, 45, 51, 31, 17, 33, 3, 48, 6, 68, 46, 44, 11, 21, 84, 23, 29, 37, 39, 72, 43, 35, 65, 38, 76, 16, 42, 89, 60, 13, 88, 97, 25, 19, 77, 75, 24, 64, 58, 78, 5, 1, 93, 74, 66, 95, 73, 82, 91, 63, 36, 34, 80, 96, 71, 81, 0, 61, 57, 92, 4, 52, 7, 62, 83, 90, 47, 41, 20, 54], 'cur_cost': 180049.0}]
2025-06-26 20:40:18,601 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:40:18,601 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 55, 'skip_rate': 0.05454545454545454, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 52, 'cache_hits': 37, 'similarity_calculations': 448, 'cache_hit_rate': 0.08258928571428571, 'cache_size': 411}}
2025-06-26 20:40:18,601 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:40:18,602 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:40:18,602 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:40:18,602 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:40:18,602 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 177970.0
2025-06-26 20:40:19,103 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:40:19,103 - ExploitationExpert - INFO - res_population_costs: [21709, 21343, 21282]
2025-06-26 20:40:19,103 - ExploitationExpert - INFO - res_populations: [array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64)]
2025-06-26 20:40:19,105 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:40:19,105 - ExploitationExpert - INFO - populations: [{'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}, {'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}, {'tour': [90, 11, 12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 192750.0}, {'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'cur_cost': 189847.0}, {'tour': [0, 90, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 1, 11, 12, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 194687.0}, {'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'cur_cost': 189847.0}, {'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}, {'tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}, {'tour': array([69,  2, 87, 60, 63, 14, 27, 76, 15, 78, 51, 80, 12, 65, 55, 34, 45,
       99, 54, 67, 70, 38, 22, 91, 74, 68, 37, 13, 84, 43, 66, 61, 96, 18,
        8,  1, 48, 40, 30, 35,  4, 39, 33, 53, 46, 93, 11, 58, 25, 92, 47,
       49, 31, 26, 89, 41, 50, 57, 98, 75, 94, 83, 72,  0, 62, 19, 29,  9,
       85,  7, 28,  5, 44, 42, 77, 23, 64, 79,  3, 36, 56, 20, 81,  6, 90,
       71, 21, 82, 59, 10, 88, 95, 86, 24, 17, 97, 32, 16, 52, 73]), 'cur_cost': 173147.0}, {'tour': array([ 4, 63, 74, 59, 84, 88, 81, 55, 51,  0, 64, 61, 92,  9, 85, 66,  6,
       78, 70, 37, 20, 42, 26, 38, 99, 69, 12, 18, 94, 58, 49, 48, 76, 53,
       29, 19, 72, 56, 93, 21, 23, 75,  8, 27, 89, 83, 46, 22, 65, 45, 77,
       86, 14, 40, 98, 91, 11, 17, 34,  7, 24, 41, 33, 79, 87, 35,  3, 31,
       30, 96, 50, 82, 68, 36, 90, 57, 15,  1, 43, 97, 13, 54,  2, 28, 39,
       62, 67,  5, 10, 16, 80, 73, 47, 25, 60, 71, 52, 32, 44, 95]), 'cur_cost': 177970.0}]
2025-06-26 20:40:19,107 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:40:19,107 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 56, 'skip_rate': 0.05357142857142857, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 53, 'cache_hits': 37, 'similarity_calculations': 449, 'cache_hit_rate': 0.08240534521158129, 'cache_size': 412}}
2025-06-26 20:40:19,107 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:40:19,107 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [90, 11, 12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 192750.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'cur_cost': 189847.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 90, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 1, 11, 12, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 194687.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], 'cur_cost': 189847.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [90, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99], 'cur_cost': 190340.0}}]
2025-06-26 20:40:19,108 - __main__ - INFO - 进化阶段完成
2025-06-26 20:40:19,108 - __main__ - INFO - 开始评估阶段
2025-06-26 20:40:19,108 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:40:19,109 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:40:19,109 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 26309.0 → 173147.0 (Δ -146838.0)
- Mean cost: 126406.8 → 187960.8
- Diversity: 0.783 → 0.305 (Δ -0.4778)

#### Elite metrics
- Elite count: 0 → 3
- Best elite cost: 26309.0 → 21282 (Δ 5027.0)

#### Strategy performance
- Explore: 8 inds, success 0.0% , avg Δ -77135.25
- Exploit: 2 inds, success 50.0% , avg Δ 771.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 9
- Historical trends: Last 3 iterations: costs [476135.0, 150781.0, 150781.0], diversity [0.7824561403508773, 0.7657894736842108, 0.8926900584795323]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:40:19,109 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:40:32,701 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "average_delta": -77135.25,
      "contribution_to_elite": "negative"
    },
    "exploit": {
      "success_rate": 0.5,
      "average_delta": 771.0,
      "contribution_to_elite": "positive"
    }
  },
  "balance_state": "unbalanced_explore_too_high",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_suggestions": "Consider reducing the number of explore individuals and increasing diversity weight to prevent premature convergence."
  }
}
```
2025-06-26 20:40:32,761 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:40:32,761 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "average_delta": -77135.25,
      "contribution_to_elite": "negative"
    },
    "exploit": {
      "success_rate": 0.5,
      "average_delta": 771.0,
      "contribution_to_elite": "positive"
    }
  },
  "balance_state": "unbalanced_explore_too_high",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_suggestions": "Consider reducing the number of explore individuals and increasing diversity weight to prevent premature convergence."
  }
}
```
2025-06-26 20:40:32,761 - __main__ - INFO - 评估阶段完成
2025-06-26 20:40:32,761 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      "average_delta": -77135.25,
      "contribution_to_elite": "negative"
    },
    "exploit": {
      "success_rate": 0.5,
      "average_delta": 771.0,
      "contribution_to_elite": "positive"
    }
  },
  "balance_state": "unbalanced_explore_too_high",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "additional_suggestions": "Consider reducing the number of explore individuals and increasing diversity weight to prevent premature convergence."
  }
}
```
2025-06-26 20:40:32,761 - __main__ - INFO - 当前最佳适应度: 173147.0
2025-06-26 20:40:32,761 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\kroA100_route_0.pkl
2025-06-26 20:40:32,761 - __main__ - INFO - kroA100 开始进化第 2 代
2025-06-26 20:40:32,761 - __main__ - INFO - 开始分析阶段
2025-06-26 20:40:32,761 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:40:32,793 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 173147.0, 'max': 194687.0, 'mean': 187960.8, 'std': 6459.108526724102}, 'diversity': 0.39333333333333326, 'clusters': {'clusters': 3, 'cluster_sizes': [8, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:40:32,793 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 173147.0, 'max': 194687.0, 'mean': 187960.8, 'std': 6459.108526724102}, 'diversity_level': 0.39333333333333326, 'convergence_level': 0.0, 'clustering_info': {'clusters': 3, 'cluster_sizes': [8, 1, 1]}, 'coordinates': [[1380, 939], [2848, 96], [3510, 1671], [457, 334], [3888, 666], [984, 965], [2721, 1482], [1286, 525], [2716, 1432], [738, 1325], [1251, 1832], [2728, 1698], [3815, 169], [3683, 1533], [1247, 1945], [123, 862], [1234, 1946], [252, 1240], [611, 673], [2576, 1676], [928, 1700], [53, 857], [1807, 1711], [274, 1420], [2574, 946], [178, 24], [2678, 1825], [1795, 962], [3384, 1498], [3520, 1079], [1256, 61], [1424, 1728], [3913, 192], [3085, 1528], [2573, 1969], [463, 1670], [3875, 598], [298, 1513], [3479, 821], [2542, 236], [3955, 1743], [1323, 280], [3447, 1830], [2936, 337], [1621, 1830], [3373, 1646], [1393, 1368], [3874, 1318], [938, 955], [3022, 474], [2482, 1183], [3854, 923], [376, 825], [2519, 135], [2945, 1622], [953, 268], [2628, 1479], [2097, 981], [890, 1846], [2139, 1806], [2421, 1007], [2290, 1810], [1115, 1052], [2588, 302], [327, 265], [241, 341], [1917, 687], [2991, 792], [2573, 599], [19, 674], [3911, 1673], [872, 1559], [2863, 558], [929, 1766], [839, 620], [3893, 102], [2178, 1619], [3822, 899], [378, 1048], [1178, 100], [2599, 901], [3416, 143], [2961, 1605], [611, 1384], [3113, 885], [2597, 1830], [2586, 1286], [161, 906], [1429, 134], [742, 1025], [1625, 1651], [1187, 706], [1787, 1009], [22, 987], [3640, 43], [3756, 882], [776, 392], [1724, 1642], [198, 1810], [3950, 1558]], 'distance_matrix': array([[   0., 1693., 2252., ...,  783., 1468., 2643.],
       [1693.,    0., 1708., ..., 1911., 3156., 1831.],
       [2252., 1708.,    0., ..., 1786., 3315.,  454.],
       ...,
       [ 783., 1911., 1786., ...,    0., 1535., 2228.],
       [1468., 3156., 3315., ..., 1535.,    0., 3760.],
       [2643., 1831.,  454., ..., 2228., 3760.,    0.]])}
2025-06-26 20:40:32,793 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:40:32,793 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:40:32,793 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:40:32,793 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:40:32,793 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:40:32,793 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:40:32,793 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:40:32,793 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:40:32,799 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:40:32,800 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:40:32,800 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {'(18, 74)': 1.0, '(84, 67)': 1.0, '(67, 72)': 1.0}, 'common_edge_ratio': 0.03}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 151865.0, 'avg_gap': 166516.13333333333}, 'structure_gap': {'unique_elite_edges': 204, 'unique_pop_edges': 304, 'common_edges': 5}}, 'elite_diversity': {'diversity_score': 0.12}}
2025-06-26 20:40:32,800 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:40:32,800 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:40:32,802 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:40:32,802 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 173147.0, mean 187960.8, max 194687.0, std 6459.108526724102
- diversity: 0.39333333333333326
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [150781.0, 150781.0, 173147.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [19.0, 24.0, 3955.0, 1969.0]
- centroid/spread: [2011.37, 1064.48] | rms 1365.283
- density_grid(3x3): [[11, 5, 11], [14, 10, 8], [13, 13, 15]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 985.0, 1586.0, 2370.5, 4150.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [26, 4, 5, 21, 17, 6, 8, 12]
- nn_median_dist: 140.036

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:40:32,804 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:40:59,175 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant standard deviation in costs)",
    "modality": "Potentially multimodal (direction histogram shows uneven distribution of edge directions, suggesting multiple local optima)",
    "deceptiveness": "Moderate (long_edge_ratio of 0.25 indicates some deceptive long edges that may mislead local search)"
  },
  "population_state": {
    "coverage": "Fair (diversity 0.39, density_grid shows uneven distribution with some high-density cells)",
    "convergence": "Early stage (0.0 convergence, no stagnation detected)",
    "elite_influence": "Minimal (0 elites, only one fixed node with no common edges)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges in max quartile ≥2370.5 length)",
    "Low-density grid cells (e.g., [0,1] with 5 nodes)",
    "Direction sectors with sparse edges (sectors 1,2,5 with ≤6 edges)"
  ],
  "opportunity_regions": [
    "High-density grid cells (e.g., [2,2] with 15 nodes)",
    "Short-edge clusters (edges ≤985.0 in q1)",
    "Direction sectors with high edge concentration (sectors 0,3 with ≥21 edges)"
  ],
  "evolution_phase": "Exploration (low convergence, no elites, recent best costs show no improvement)",
  "evolution_direction": [
    "Prioritize diversity-preserving operators (e.g., edge recombination with spatial bias toward low-density cells)",
    "Introduce local search in high-density opportunity regions (e.g., 2-opt on short edges in sector 0/3)",
    "Mitigate long-edge deception with penalization in fitness evaluation"
  ]
}
```
2025-06-26 20:40:59,175 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:40:59,175 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant standard deviation in costs)', 'modality': 'Potentially multimodal (direction histogram shows uneven distribution of edge directions, suggesting multiple local optima)', 'deceptiveness': 'Moderate (long_edge_ratio of 0.25 indicates some deceptive long edges that may mislead local search)'}, 'population_state': {'coverage': 'Fair (diversity 0.39, density_grid shows uneven distribution with some high-density cells)', 'convergence': 'Early stage (0.0 convergence, no stagnation detected)', 'elite_influence': 'Minimal (0 elites, only one fixed node with no common edges)'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile ≥2370.5 length)', 'Low-density grid cells (e.g., [0,1] with 5 nodes)', 'Direction sectors with sparse edges (sectors 1,2,5 with ≤6 edges)'], 'opportunity_regions': ['High-density grid cells (e.g., [2,2] with 15 nodes)', 'Short-edge clusters (edges ≤985.0 in q1)', 'Direction sectors with high edge concentration (sectors 0,3 with ≥21 edges)'], 'evolution_phase': 'Exploration (low convergence, no elites, recent best costs show no improvement)', 'evolution_direction': ['Prioritize diversity-preserving operators (e.g., edge recombination with spatial bias toward low-density cells)', 'Introduce local search in high-density opportunity regions (e.g., 2-opt on short edges in sector 0/3)', 'Mitigate long-edge deception with penalization in fitness evaluation']}
2025-06-26 20:40:59,175 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:40:59,175 - __main__ - INFO - 分析阶段完成
2025-06-26 20:40:59,175 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant standard deviation in costs)', 'modality': 'Potentially multimodal (direction histogram shows uneven distribution of edge directions, suggesting multiple local optima)', 'deceptiveness': 'Moderate (long_edge_ratio of 0.25 indicates some deceptive long edges that may mislead local search)'}, 'population_state': {'coverage': 'Fair (diversity 0.39, density_grid shows uneven distribution with some high-density cells)', 'convergence': 'Early stage (0.0 convergence, no stagnation detected)', 'elite_influence': 'Minimal (0 elites, only one fixed node with no common edges)'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile ≥2370.5 length)', 'Low-density grid cells (e.g., [0,1] with 5 nodes)', 'Direction sectors with sparse edges (sectors 1,2,5 with ≤6 edges)'], 'opportunity_regions': ['High-density grid cells (e.g., [2,2] with 15 nodes)', 'Short-edge clusters (edges ≤985.0 in q1)', 'Direction sectors with high edge concentration (sectors 0,3 with ≥21 edges)'], 'evolution_phase': 'Exploration (low convergence, no elites, recent best costs show no improvement)', 'evolution_direction': ['Prioritize diversity-preserving operators (e.g., edge recombination with spatial bias toward low-density cells)', 'Introduce local search in high-density opportunity regions (e.g., 2-opt on short edges in sector 0/3)', 'Mitigate long-edge deception with penalization in fitness evaluation']}
2025-06-26 20:40:59,175 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:40:59,175 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:40:59,175 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 173147.0
  • mean_cost: 187960.8
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant standard deviation in costs), modality Potentially multimodal (direction histogram shows uneven distribution of edge directions, suggesting multiple local optima), deceptiveness Moderate (long_edge_ratio of 0.25 indicates some deceptive long edges that may mislead local search)
- Evolution phase: Exploration (low convergence, no elites, recent best costs show no improvement)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): Long-edge corridors , Low-density grid cel...
- Opportunity regions (# 3): High-density grid ce, Short-edge clusters ...
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 0.0,
      ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:40:59,175 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:40:59,175 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:41:08,339 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early evolution phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 20:41:08,339 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:41:08,339 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:41:08,339 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early evolution phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 20:41:08,339 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:41:08,339 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:41:08,342 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early evolution phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 20:41:08,342 - __main__ - INFO - 开始进化阶段
2025-06-26 20:41:08,342 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:41:08,342 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:41:08,342 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 190340.0 (rank 40.0%)
- Population diversity: 0.30505050505050507
- Evolution phase: Exploration (low convergence, no elites, recent best costs show no improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:41:08,342 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:41:08,342 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:41:29,711 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 77, 30, 63, 96, 29, 62, 95, 28, 61, 94, 27, 60, 93, 26, 59, 92, 25, 58, 91],
  "targeted_regions": "High-density gr, Short-edge clus, adjacent low-density cells",
  "strategy_comment": "Cluster jumps with short edges, avoiding long corridors"
}
```
2025-06-26 20:41:29,711 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:41:29,711 - ExplorationExpert - INFO - 探索路径生成完成，成本: 190456.0, 路径: [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 30, 29, 28, 27, 26, 59, 25, 58, 81, 82, 83, 85, 0, 84, 86, 33, 31, 87, 32]
2025-06-26 20:41:29,711 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 30, 29, 28, 27, 26, 59, 25, 58, 81, 82, 83, 85, 0, 84, 86, 33, 31, 87, 32], 'cur_cost': 190456.0}
2025-06-26 20:41:29,711 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:41:29,711 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:41:29,711 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 190340.0 (rank 40.0%)
- Population diversity: 0.3929292929292929
- Evolution phase: Exploration (low convergence, no elites, recent best costs show no improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:41:29,711 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:41:29,711 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:41:50,889 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 81, 3, 24, 46, 68, 80, 2, 14, 35, 57, 79, 91, 13, 36, 58, 70, 92, 15, 37, 59, 71, 93, 16, 38, 61, 83, 5, 26, 48, 70, 94, 17, 28, 49, 72, 84, 6, 27, 50, 73, 85, 7, 29, 51, 75, 86, 8, 20, 42, 64, 97, 19, 31, 53, 77, 99, 11, 22, 44, 66, 88, 0, 33, 55, 63, 95, 41, 62],
  "targeted_regions": "High-density gr, Short-edge clus",
  "strategy_comment": "Prioritize dense clusters with short edges, avoid long corridors"
}
```
2025-06-26 20:41:50,890 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:41:50,891 - ExplorationExpert - INFO - 探索路径生成完成，成本: 161752.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 81, 3, 24, 46, 68, 80, 2, 14, 35, 57, 79, 91, 13, 36, 58, 70, 92, 15, 37, 59, 71, 93, 16, 38, 61, 83, 5, 26, 48, 94, 17, 28, 49, 72, 84, 6, 27, 50, 73, 85, 7, 29, 51, 75, 86, 8, 20, 42, 64, 97, 19, 31, 53, 77, 99, 11, 22, 44, 66, 88, 0, 33, 55, 63, 95, 41, 62, 40]
2025-06-26 20:41:50,891 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 81, 3, 24, 46, 68, 80, 2, 14, 35, 57, 79, 91, 13, 36, 58, 70, 92, 15, 37, 59, 71, 93, 16, 38, 61, 83, 5, 26, 48, 94, 17, 28, 49, 72, 84, 6, 27, 50, 73, 85, 7, 29, 51, 75, 86, 8, 20, 42, 64, 97, 19, 31, 53, 77, 99, 11, 22, 44, 66, 88, 0, 33, 55, 63, 95, 41, 62, 40], 'cur_cost': 161752.0}
2025-06-26 20:41:50,891 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:41:50,891 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:41:50,893 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 192750.0 (rank 80.0%)
- Population diversity: 0.4808080808080808
- Evolution phase: Exploration (low convergence, no elites, recent best costs show no improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:41:50,893 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:41:50,893 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:42:11,797 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 30, 42, 64, 86, 8, 20, 41, 63, 85, 7, 18, 29, 52, 74, 96, 17, 28, 51, 73, 95, 16, 27, 50, 72, 94, 15, 26, 49, 71, 93, 14, 25, 48, 70, 92, 13, 24, 47, 69, 91, 11, 22, 44, 66, 88, 0, 33, 55, 77, 99, 2, 35, 57, 79, 3, 36, 58, 80, 4, 37, 59, 81, 5, 38, 60, 82, 6, 39, 61, 83, 40, 62, 84, 46, 68],
  "targeted_regions": "High-density gr, Short-edge clus, adjacent low-density cells",
  "strategy_comment": "Cluster hops with short edges, avoiding long corridors"
}
```
2025-06-26 20:42:11,808 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:42:11,808 - ExplorationExpert - INFO - 探索路径生成完成，成本: 176797.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 30, 42, 64, 86, 8, 20, 41, 63, 85, 7, 18, 29, 52, 74, 96, 17, 28, 51, 73, 95, 16, 27, 50, 72, 94, 15, 26, 49, 71, 93, 14, 25, 48, 70, 92, 13, 24, 47, 69, 91, 11, 22, 44, 66, 88, 0, 33, 55, 77, 99, 2, 35, 57, 79, 3, 36, 58, 80, 4, 37, 59, 81, 5, 38, 60, 82, 6, 39, 61, 83, 40, 62, 84, 46, 68]
2025-06-26 20:42:11,808 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 30, 42, 64, 86, 8, 20, 41, 63, 85, 7, 18, 29, 52, 74, 96, 17, 28, 51, 73, 95, 16, 27, 50, 72, 94, 15, 26, 49, 71, 93, 14, 25, 48, 70, 92, 13, 24, 47, 69, 91, 11, 22, 44, 66, 88, 0, 33, 55, 77, 99, 2, 35, 57, 79, 3, 36, 58, 80, 4, 37, 59, 81, 5, 38, 60, 82, 6, 39, 61, 83, 40, 62, 84, 46, 68], 'cur_cost': 176797.0}
2025-06-26 20:42:11,808 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:42:11,808 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:42:11,808 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 189847.0 (rank 40.0%)
- Population diversity: 0.5242424242424243
- Evolution phase: Exploration (low convergence, no elites, recent best costs show no improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:42:11,808 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:42:11,808 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:42:32,816 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 45, 67, 23, 78, 34, 56, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 90, 11, 35, 57, 79, 0, 22, 44, 66, 88, 20, 55, 77, 99, 31, 33],
  "targeted_regions": "High-density gr, Short-edge clus, adjacent low-density cells",
  "strategy_comment": "Prioritize opportunity regions with zigzag pattern to boost diversity"
}
```
2025-06-26 20:42:32,816 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:42:32,816 - ExplorationExpert - INFO - 探索路径生成完成，成本: 162206.0, 路径: [12, 45, 67, 23, 78, 34, 56, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 90, 11, 35, 57, 79, 0, 22, 44, 66, 88, 20, 55, 77, 99, 31, 33]
2025-06-26 20:42:32,816 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [12, 45, 67, 23, 78, 34, 56, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 90, 11, 35, 57, 79, 0, 22, 44, 66, 88, 20, 55, 77, 99, 31, 33], 'cur_cost': 162206.0}
2025-06-26 20:42:32,816 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:42:32,816 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:42:32,816 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 194687.0 (rank 90.0%)
- Population diversity: 0.5535353535353535
- Evolution phase: Exploration (low convergence, no elites, recent best costs show no improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:42:32,816 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:42:32,816 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:42:56,147 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 77, 0, 33, 66, 99, 32, 65, 98, 31, 64, 97, 30, 63, 96, 29, 62, 95, 28, 61, 94, 27, 60, 93, 26, 59, 92, 25, 58, 91],
  "targeted_regions": "High-density gr, Short-edge clus, adjacent low-density cells",
  "strategy_comment": "Prioritize opportunity regions with zigzag pattern for diversity"
}
```
2025-06-26 20:42:56,147 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:42:56,147 - ExplorationExpert - INFO - 探索路径生成完成，成本: 192926.0, 路径: [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 32, 31, 30, 29, 28, 58, 26, 82, 87, 85, 84, 25, 83, 81, 86, 59, 27]
2025-06-26 20:42:56,147 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 32, 31, 30, 29, 28, 58, 26, 82, 87, 85, 84, 25, 83, 81, 86, 59, 27], 'cur_cost': 192926.0}
2025-06-26 20:42:56,147 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:42:56,147 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:42:56,147 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 189847.0 (rank 50.0%)
- Population diversity: 0.5606060606060606
- Evolution phase: Exploration (low convergence, no elites, recent best costs show no improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:42:56,147 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:42:56,147 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:43:19,164 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 29, 41, 63, 85, 7, 28, 50, 72, 94, 16, 37, 59, 81, 3, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 4, 26, 48, 60, 82, 5, 17, 39, 61, 83, 6, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 36, 58, 80, 2, 24, 46, 68, 90, 12, 34, 56, 78, 1, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9],
  "targeted_regions": "High-density gr, Short-edge clus, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of opportunity regions with sparse adjacency"
}
```
2025-06-26 20:43:19,164 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:43:19,164 - ExplorationExpert - INFO - 探索路径生成完成，成本: 177530.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 29, 41, 63, 85, 7, 28, 50, 72, 94, 16, 37, 59, 81, 3, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 4, 26, 48, 60, 82, 5, 17, 39, 61, 83, 6, 27, 49, 71, 93, 15, 38, 99, 31, 77, 95, 44, 55, 33, 53, 66, 42, 35, 40, 64, 8, 47, 88, 91, 57, 25, 19, 20, 86, 62, 22, 97, 75, 11, 79, 13, 0, 84, 69, 73, 51]
2025-06-26 20:43:19,168 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 29, 41, 63, 85, 7, 28, 50, 72, 94, 16, 37, 59, 81, 3, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 4, 26, 48, 60, 82, 5, 17, 39, 61, 83, 6, 27, 49, 71, 93, 15, 38, 99, 31, 77, 95, 44, 55, 33, 53, 66, 42, 35, 40, 64, 8, 47, 88, 91, 57, 25, 19, 20, 86, 62, 22, 97, 75, 11, 79, 13, 0, 84, 69, 73, 51], 'cur_cost': 177530.0}
2025-06-26 20:43:19,168 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:43:19,168 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:43:19,169 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 190340.0 (rank 60.0%)
- Population diversity: 0.592929292929293
- Evolution phase: Exploration (low convergence, no elites, recent best costs show no improvement)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:43:19,169 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:43:19,169 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:43:40,387 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 21, 54, 79, 22, 55, 68, 91, 35, 46, 97, 48, 99, 50, 92, 43, 94, 47, 96, 49, 98, 51, 93, 52, 95, 0],
  "targeted_regions": "High-density gr, Short-edge clus, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of dense clusters with sparse transitions"
}
```
2025-06-26 20:43:40,387 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:43:40,395 - ExplorationExpert - INFO - 探索路径生成完成，成本: 180376.0, 路径: [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 21, 54, 79, 22, 55, 68, 91, 35, 46, 97, 48, 99, 50, 92, 43, 94, 47, 96, 49, 98, 51, 93, 52, 95, 0]
2025-06-26 20:43:40,395 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 21, 54, 79, 22, 55, 68, 91, 35, 46, 97, 48, 99, 50, 92, 43, 94, 47, 96, 49, 98, 51, 93, 52, 95, 0], 'cur_cost': 180376.0}
2025-06-26 20:43:40,395 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:43:40,395 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:43:40,395 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:43:40,395 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 165421.0
2025-06-26 20:43:40,896 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:43:40,898 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:43:40,898 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:43:40,898 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:43:40,898 - ExploitationExpert - INFO - populations: [{'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 30, 29, 28, 27, 26, 59, 25, 58, 81, 82, 83, 85, 0, 84, 86, 33, 31, 87, 32], 'cur_cost': 190456.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 81, 3, 24, 46, 68, 80, 2, 14, 35, 57, 79, 91, 13, 36, 58, 70, 92, 15, 37, 59, 71, 93, 16, 38, 61, 83, 5, 26, 48, 94, 17, 28, 49, 72, 84, 6, 27, 50, 73, 85, 7, 29, 51, 75, 86, 8, 20, 42, 64, 97, 19, 31, 53, 77, 99, 11, 22, 44, 66, 88, 0, 33, 55, 63, 95, 41, 62, 40], 'cur_cost': 161752.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 30, 42, 64, 86, 8, 20, 41, 63, 85, 7, 18, 29, 52, 74, 96, 17, 28, 51, 73, 95, 16, 27, 50, 72, 94, 15, 26, 49, 71, 93, 14, 25, 48, 70, 92, 13, 24, 47, 69, 91, 11, 22, 44, 66, 88, 0, 33, 55, 77, 99, 2, 35, 57, 79, 3, 36, 58, 80, 4, 37, 59, 81, 5, 38, 60, 82, 6, 39, 61, 83, 40, 62, 84, 46, 68], 'cur_cost': 176797.0}, {'tour': [12, 45, 67, 23, 78, 34, 56, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 90, 11, 35, 57, 79, 0, 22, 44, 66, 88, 20, 55, 77, 99, 31, 33], 'cur_cost': 162206.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 32, 31, 30, 29, 28, 58, 26, 82, 87, 85, 84, 25, 83, 81, 86, 59, 27], 'cur_cost': 192926.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 29, 41, 63, 85, 7, 28, 50, 72, 94, 16, 37, 59, 81, 3, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 4, 26, 48, 60, 82, 5, 17, 39, 61, 83, 6, 27, 49, 71, 93, 15, 38, 99, 31, 77, 95, 44, 55, 33, 53, 66, 42, 35, 40, 64, 8, 47, 88, 91, 57, 25, 19, 20, 86, 62, 22, 97, 75, 11, 79, 13, 0, 84, 69, 73, 51], 'cur_cost': 177530.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 21, 54, 79, 22, 55, 68, 91, 35, 46, 97, 48, 99, 50, 92, 43, 94, 47, 96, 49, 98, 51, 93, 52, 95, 0], 'cur_cost': 180376.0}, {'tour': array([51, 95, 40, 35, 46, 56, 86, 62, 18, 77, 32, 54, 55,  8, 50, 93, 29,
       83, 16, 63, 94, 31, 47, 70, 39, 36, 23, 12, 10, 73, 87, 30, 90, 98,
        5, 85, 52, 15, 24, 78,  4, 48, 44, 19, 72, 20,  0, 74, 75, 60, 99,
       92, 58, 37, 34, 26, 97, 61, 25, 69, 82, 53, 96, 21, 67, 80, 68, 76,
       27, 11,  2, 22, 13,  7, 66, 65, 57, 28, 81, 41, 88,  6, 84,  3,  1,
       71, 49, 43, 33, 42,  9, 17, 45, 79, 64, 38, 14, 91, 89, 59]), 'cur_cost': 165421.0}, {'tour': array([69,  2, 87, 60, 63, 14, 27, 76, 15, 78, 51, 80, 12, 65, 55, 34, 45,
       99, 54, 67, 70, 38, 22, 91, 74, 68, 37, 13, 84, 43, 66, 61, 96, 18,
        8,  1, 48, 40, 30, 35,  4, 39, 33, 53, 46, 93, 11, 58, 25, 92, 47,
       49, 31, 26, 89, 41, 50, 57, 98, 75, 94, 83, 72,  0, 62, 19, 29,  9,
       85,  7, 28,  5, 44, 42, 77, 23, 64, 79,  3, 36, 56, 20, 81,  6, 90,
       71, 21, 82, 59, 10, 88, 95, 86, 24, 17, 97, 32, 16, 52, 73]), 'cur_cost': 173147.0}, {'tour': array([ 4, 63, 74, 59, 84, 88, 81, 55, 51,  0, 64, 61, 92,  9, 85, 66,  6,
       78, 70, 37, 20, 42, 26, 38, 99, 69, 12, 18, 94, 58, 49, 48, 76, 53,
       29, 19, 72, 56, 93, 21, 23, 75,  8, 27, 89, 83, 46, 22, 65, 45, 77,
       86, 14, 40, 98, 91, 11, 17, 34,  7, 24, 41, 33, 79, 87, 35,  3, 31,
       30, 96, 50, 82, 68, 36, 90, 57, 15,  1, 43, 97, 13, 54,  2, 28, 39,
       62, 67,  5, 10, 16, 80, 73, 47, 25, 60, 71, 52, 32, 44, 95]), 'cur_cost': 177970.0}]
2025-06-26 20:43:40,901 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:43:40,902 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 57, 'skip_rate': 0.05263157894736842, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 54, 'cache_hits': 37, 'similarity_calculations': 451, 'cache_hit_rate': 0.082039911308204, 'cache_size': 414}}
2025-06-26 20:43:40,902 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:43:40,903 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:43:40,903 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:43:40,903 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:43:40,903 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 157815.0
2025-06-26 20:43:41,405 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:43:41,407 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:43:41,407 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:43:41,407 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:43:41,409 - ExploitationExpert - INFO - populations: [{'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 30, 29, 28, 27, 26, 59, 25, 58, 81, 82, 83, 85, 0, 84, 86, 33, 31, 87, 32], 'cur_cost': 190456.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 81, 3, 24, 46, 68, 80, 2, 14, 35, 57, 79, 91, 13, 36, 58, 70, 92, 15, 37, 59, 71, 93, 16, 38, 61, 83, 5, 26, 48, 94, 17, 28, 49, 72, 84, 6, 27, 50, 73, 85, 7, 29, 51, 75, 86, 8, 20, 42, 64, 97, 19, 31, 53, 77, 99, 11, 22, 44, 66, 88, 0, 33, 55, 63, 95, 41, 62, 40], 'cur_cost': 161752.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 30, 42, 64, 86, 8, 20, 41, 63, 85, 7, 18, 29, 52, 74, 96, 17, 28, 51, 73, 95, 16, 27, 50, 72, 94, 15, 26, 49, 71, 93, 14, 25, 48, 70, 92, 13, 24, 47, 69, 91, 11, 22, 44, 66, 88, 0, 33, 55, 77, 99, 2, 35, 57, 79, 3, 36, 58, 80, 4, 37, 59, 81, 5, 38, 60, 82, 6, 39, 61, 83, 40, 62, 84, 46, 68], 'cur_cost': 176797.0}, {'tour': [12, 45, 67, 23, 78, 34, 56, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 90, 11, 35, 57, 79, 0, 22, 44, 66, 88, 20, 55, 77, 99, 31, 33], 'cur_cost': 162206.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 32, 31, 30, 29, 28, 58, 26, 82, 87, 85, 84, 25, 83, 81, 86, 59, 27], 'cur_cost': 192926.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 29, 41, 63, 85, 7, 28, 50, 72, 94, 16, 37, 59, 81, 3, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 4, 26, 48, 60, 82, 5, 17, 39, 61, 83, 6, 27, 49, 71, 93, 15, 38, 99, 31, 77, 95, 44, 55, 33, 53, 66, 42, 35, 40, 64, 8, 47, 88, 91, 57, 25, 19, 20, 86, 62, 22, 97, 75, 11, 79, 13, 0, 84, 69, 73, 51], 'cur_cost': 177530.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 21, 54, 79, 22, 55, 68, 91, 35, 46, 97, 48, 99, 50, 92, 43, 94, 47, 96, 49, 98, 51, 93, 52, 95, 0], 'cur_cost': 180376.0}, {'tour': array([51, 95, 40, 35, 46, 56, 86, 62, 18, 77, 32, 54, 55,  8, 50, 93, 29,
       83, 16, 63, 94, 31, 47, 70, 39, 36, 23, 12, 10, 73, 87, 30, 90, 98,
        5, 85, 52, 15, 24, 78,  4, 48, 44, 19, 72, 20,  0, 74, 75, 60, 99,
       92, 58, 37, 34, 26, 97, 61, 25, 69, 82, 53, 96, 21, 67, 80, 68, 76,
       27, 11,  2, 22, 13,  7, 66, 65, 57, 28, 81, 41, 88,  6, 84,  3,  1,
       71, 49, 43, 33, 42,  9, 17, 45, 79, 64, 38, 14, 91, 89, 59]), 'cur_cost': 165421.0}, {'tour': array([42, 51, 43, 26, 23, 50, 68, 22, 92, 99, 62, 59, 63, 33, 73,  0, 89,
       18, 10, 78, 29,  5, 52, 48, 30, 93, 60, 44, 13,  8, 47, 28, 66, 12,
        3, 35, 40, 41, 71, 98, 14, 77, 11, 38, 58, 17, 53, 54, 95, 86,  2,
       85, 90, 19, 76, 34,  1, 65, 56,  7, 81,  4, 87,  9, 83, 74, 69, 67,
       96, 31, 21, 37, 80, 91,  6, 16, 25, 70, 32, 46, 97, 57, 79, 15, 84,
       20, 64, 55, 36, 39, 49, 45, 75, 72, 61, 88, 94, 82, 24, 27]), 'cur_cost': 157815.0}, {'tour': array([ 4, 63, 74, 59, 84, 88, 81, 55, 51,  0, 64, 61, 92,  9, 85, 66,  6,
       78, 70, 37, 20, 42, 26, 38, 99, 69, 12, 18, 94, 58, 49, 48, 76, 53,
       29, 19, 72, 56, 93, 21, 23, 75,  8, 27, 89, 83, 46, 22, 65, 45, 77,
       86, 14, 40, 98, 91, 11, 17, 34,  7, 24, 41, 33, 79, 87, 35,  3, 31,
       30, 96, 50, 82, 68, 36, 90, 57, 15,  1, 43, 97, 13, 54,  2, 28, 39,
       62, 67,  5, 10, 16, 80, 73, 47, 25, 60, 71, 52, 32, 44, 95]), 'cur_cost': 177970.0}]
2025-06-26 20:43:41,411 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:43:41,411 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 58, 'skip_rate': 0.05172413793103448, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 55, 'cache_hits': 37, 'similarity_calculations': 454, 'cache_hit_rate': 0.08149779735682819, 'cache_size': 417}}
2025-06-26 20:43:41,413 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:43:41,413 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:43:41,413 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:43:41,413 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:43:41,414 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 173374.0
2025-06-26 20:43:41,914 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:43:41,916 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:43:41,916 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:43:41,916 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:43:41,916 - ExploitationExpert - INFO - populations: [{'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 30, 29, 28, 27, 26, 59, 25, 58, 81, 82, 83, 85, 0, 84, 86, 33, 31, 87, 32], 'cur_cost': 190456.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 81, 3, 24, 46, 68, 80, 2, 14, 35, 57, 79, 91, 13, 36, 58, 70, 92, 15, 37, 59, 71, 93, 16, 38, 61, 83, 5, 26, 48, 94, 17, 28, 49, 72, 84, 6, 27, 50, 73, 85, 7, 29, 51, 75, 86, 8, 20, 42, 64, 97, 19, 31, 53, 77, 99, 11, 22, 44, 66, 88, 0, 33, 55, 63, 95, 41, 62, 40], 'cur_cost': 161752.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 30, 42, 64, 86, 8, 20, 41, 63, 85, 7, 18, 29, 52, 74, 96, 17, 28, 51, 73, 95, 16, 27, 50, 72, 94, 15, 26, 49, 71, 93, 14, 25, 48, 70, 92, 13, 24, 47, 69, 91, 11, 22, 44, 66, 88, 0, 33, 55, 77, 99, 2, 35, 57, 79, 3, 36, 58, 80, 4, 37, 59, 81, 5, 38, 60, 82, 6, 39, 61, 83, 40, 62, 84, 46, 68], 'cur_cost': 176797.0}, {'tour': [12, 45, 67, 23, 78, 34, 56, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 90, 11, 35, 57, 79, 0, 22, 44, 66, 88, 20, 55, 77, 99, 31, 33], 'cur_cost': 162206.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 32, 31, 30, 29, 28, 58, 26, 82, 87, 85, 84, 25, 83, 81, 86, 59, 27], 'cur_cost': 192926.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 29, 41, 63, 85, 7, 28, 50, 72, 94, 16, 37, 59, 81, 3, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 4, 26, 48, 60, 82, 5, 17, 39, 61, 83, 6, 27, 49, 71, 93, 15, 38, 99, 31, 77, 95, 44, 55, 33, 53, 66, 42, 35, 40, 64, 8, 47, 88, 91, 57, 25, 19, 20, 86, 62, 22, 97, 75, 11, 79, 13, 0, 84, 69, 73, 51], 'cur_cost': 177530.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 21, 54, 79, 22, 55, 68, 91, 35, 46, 97, 48, 99, 50, 92, 43, 94, 47, 96, 49, 98, 51, 93, 52, 95, 0], 'cur_cost': 180376.0}, {'tour': array([51, 95, 40, 35, 46, 56, 86, 62, 18, 77, 32, 54, 55,  8, 50, 93, 29,
       83, 16, 63, 94, 31, 47, 70, 39, 36, 23, 12, 10, 73, 87, 30, 90, 98,
        5, 85, 52, 15, 24, 78,  4, 48, 44, 19, 72, 20,  0, 74, 75, 60, 99,
       92, 58, 37, 34, 26, 97, 61, 25, 69, 82, 53, 96, 21, 67, 80, 68, 76,
       27, 11,  2, 22, 13,  7, 66, 65, 57, 28, 81, 41, 88,  6, 84,  3,  1,
       71, 49, 43, 33, 42,  9, 17, 45, 79, 64, 38, 14, 91, 89, 59]), 'cur_cost': 165421.0}, {'tour': array([42, 51, 43, 26, 23, 50, 68, 22, 92, 99, 62, 59, 63, 33, 73,  0, 89,
       18, 10, 78, 29,  5, 52, 48, 30, 93, 60, 44, 13,  8, 47, 28, 66, 12,
        3, 35, 40, 41, 71, 98, 14, 77, 11, 38, 58, 17, 53, 54, 95, 86,  2,
       85, 90, 19, 76, 34,  1, 65, 56,  7, 81,  4, 87,  9, 83, 74, 69, 67,
       96, 31, 21, 37, 80, 91,  6, 16, 25, 70, 32, 46, 97, 57, 79, 15, 84,
       20, 64, 55, 36, 39, 49, 45, 75, 72, 61, 88, 94, 82, 24, 27]), 'cur_cost': 157815.0}, {'tour': array([88,  9,  6, 33, 86,  1, 56, 60, 21, 98, 19, 62, 31, 84, 32, 96, 47,
        2, 55, 76, 89,  7, 66, 24, 10, 15, 43, 52, 73, 25,  8, 91, 34, 78,
       37, 72,  0, 12, 54, 30, 36, 85, 42, 45, 63, 99, 38, 70, 14, 95, 41,
       29, 23, 53,  3, 87, 93, 58, 11, 49, 44, 35, 82, 75, 90, 61, 40, 71,
       92, 28, 39,  5, 50, 77, 57, 94, 68, 27, 16, 69, 59, 20, 18, 65,  4,
       22, 46, 80, 97, 17, 51, 26, 48, 81, 64, 67, 74, 79, 83, 13]), 'cur_cost': 173374.0}]
2025-06-26 20:43:41,916 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-26 20:43:41,916 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 59, 'skip_rate': 0.05084745762711865, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 56, 'cache_hits': 37, 'similarity_calculations': 458, 'cache_hit_rate': 0.08078602620087336, 'cache_size': 421}}
2025-06-26 20:43:41,916 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:43:41,916 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 30, 29, 28, 27, 26, 59, 25, 58, 81, 82, 83, 85, 0, 84, 86, 33, 31, 87, 32], 'cur_cost': 190456.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 81, 3, 24, 46, 68, 80, 2, 14, 35, 57, 79, 91, 13, 36, 58, 70, 92, 15, 37, 59, 71, 93, 16, 38, 61, 83, 5, 26, 48, 94, 17, 28, 49, 72, 84, 6, 27, 50, 73, 85, 7, 29, 51, 75, 86, 8, 20, 42, 64, 97, 19, 31, 53, 77, 99, 11, 22, 44, 66, 88, 0, 33, 55, 63, 95, 41, 62, 40], 'cur_cost': 161752.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 30, 42, 64, 86, 8, 20, 41, 63, 85, 7, 18, 29, 52, 74, 96, 17, 28, 51, 73, 95, 16, 27, 50, 72, 94, 15, 26, 49, 71, 93, 14, 25, 48, 70, 92, 13, 24, 47, 69, 91, 11, 22, 44, 66, 88, 0, 33, 55, 77, 99, 2, 35, 57, 79, 3, 36, 58, 80, 4, 37, 59, 81, 5, 38, 60, 82, 6, 39, 61, 83, 40, 62, 84, 46, 68], 'cur_cost': 176797.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [12, 45, 67, 23, 78, 34, 56, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 90, 11, 35, 57, 79, 0, 22, 44, 66, 88, 20, 55, 77, 99, 31, 33], 'cur_cost': 162206.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 32, 31, 30, 29, 28, 58, 26, 82, 87, 85, 84, 25, 83, 81, 86, 59, 27], 'cur_cost': 192926.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 29, 41, 63, 85, 7, 28, 50, 72, 94, 16, 37, 59, 81, 3, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 4, 26, 48, 60, 82, 5, 17, 39, 61, 83, 6, 27, 49, 71, 93, 15, 38, 99, 31, 77, 95, 44, 55, 33, 53, 66, 42, 35, 40, 64, 8, 47, 88, 91, 57, 25, 19, 20, 86, 62, 22, 97, 75, 11, 79, 13, 0, 84, 69, 73, 51], 'cur_cost': 177530.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 21, 54, 79, 22, 55, 68, 91, 35, 46, 97, 48, 99, 50, 92, 43, 94, 47, 96, 49, 98, 51, 93, 52, 95, 0], 'cur_cost': 180376.0}}]
2025-06-26 20:43:41,916 - __main__ - INFO - 进化阶段完成
2025-06-26 20:43:41,916 - __main__ - INFO - 开始评估阶段
2025-06-26 20:43:41,922 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:43:41,923 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:43:41,923 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 173147.0 → 157815.0 (Δ 15332.0)
- Mean cost: 187960.8 → 173865.3
- Diversity: 0.305 → 0.663 (Δ 0.3576)

#### Elite metrics
- Elite count: 3 → 3
- Best elite cost: 21282 → 21282 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 85.7% , avg Δ 13729.71
- Exploit: 3 inds, success 100.0% , avg Δ 14949.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 1
- Historical trends: Last 3 iterations: costs [150781.0, 150781.0, 173147.0], diversity [0.7657894736842108, 0.8926900584795323, 0.39333333333333326]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:43:41,923 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:43:57,638 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "85.7% success rate with significant average cost reduction (Δ 13729.71)"
    },
    "exploit": {
      "contribution": "moderate",
      "reason": "100% success rate but no improvement in elite cost (Δ 0)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "slightly imbalanced towards explore",
    "reason": "Explore shows higher success and impact, but exploit maintains elite stability"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider increasing mutation intensity in exploit to break elite stagnation"
  }
}
```
2025-06-26 20:43:57,698 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:43:57,704 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "85.7% success rate with significant average cost reduction (Δ 13729.71)"
    },
    "exploit": {
      "contribution": "moderate",
      "reason": "100% success rate but no improvement in elite cost (Δ 0)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "slightly imbalanced towards explore",
    "reason": "Explore shows higher success and impact, but exploit maintains elite stability"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider increasing mutation intensity in exploit to break elite stagnation"
  }
}
```
2025-06-26 20:43:57,704 - __main__ - INFO - 评估阶段完成
2025-06-26 20:43:57,704 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "85.7% success rate with significant average cost reduction (Δ 13729.71)"
    },
    "exploit": {
      "contribution": "moderate",
      "reason": "100% success rate but no improvement in elite cost (Δ 0)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore, 30% exploit",
    "assessment": "slightly imbalanced towards explore",
    "reason": "Explore shows higher success and impact, but exploit maintains elite stability"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Consider increasing mutation intensity in exploit to break elite stagnation"
  }
}
```
2025-06-26 20:43:57,704 - __main__ - INFO - 当前最佳适应度: 157815.0
2025-06-26 20:43:57,704 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\kroA100_route_1.pkl
2025-06-26 20:43:57,704 - __main__ - INFO - kroA100 开始进化第 3 代
2025-06-26 20:43:57,704 - __main__ - INFO - 开始分析阶段
2025-06-26 20:43:57,704 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:43:57,746 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 157815.0, 'max': 192926.0, 'mean': 173865.3, 'std': 11466.502248288272}, 'diversity': 0.8900000000000001, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:43:57,746 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 157815.0, 'max': 192926.0, 'mean': 173865.3, 'std': 11466.502248288272}, 'diversity_level': 0.8900000000000001, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[1380, 939], [2848, 96], [3510, 1671], [457, 334], [3888, 666], [984, 965], [2721, 1482], [1286, 525], [2716, 1432], [738, 1325], [1251, 1832], [2728, 1698], [3815, 169], [3683, 1533], [1247, 1945], [123, 862], [1234, 1946], [252, 1240], [611, 673], [2576, 1676], [928, 1700], [53, 857], [1807, 1711], [274, 1420], [2574, 946], [178, 24], [2678, 1825], [1795, 962], [3384, 1498], [3520, 1079], [1256, 61], [1424, 1728], [3913, 192], [3085, 1528], [2573, 1969], [463, 1670], [3875, 598], [298, 1513], [3479, 821], [2542, 236], [3955, 1743], [1323, 280], [3447, 1830], [2936, 337], [1621, 1830], [3373, 1646], [1393, 1368], [3874, 1318], [938, 955], [3022, 474], [2482, 1183], [3854, 923], [376, 825], [2519, 135], [2945, 1622], [953, 268], [2628, 1479], [2097, 981], [890, 1846], [2139, 1806], [2421, 1007], [2290, 1810], [1115, 1052], [2588, 302], [327, 265], [241, 341], [1917, 687], [2991, 792], [2573, 599], [19, 674], [3911, 1673], [872, 1559], [2863, 558], [929, 1766], [839, 620], [3893, 102], [2178, 1619], [3822, 899], [378, 1048], [1178, 100], [2599, 901], [3416, 143], [2961, 1605], [611, 1384], [3113, 885], [2597, 1830], [2586, 1286], [161, 906], [1429, 134], [742, 1025], [1625, 1651], [1187, 706], [1787, 1009], [22, 987], [3640, 43], [3756, 882], [776, 392], [1724, 1642], [198, 1810], [3950, 1558]], 'distance_matrix': array([[   0., 1693., 2252., ...,  783., 1468., 2643.],
       [1693.,    0., 1708., ..., 1911., 3156., 1831.],
       [2252., 1708.,    0., ..., 1786., 3315.,  454.],
       ...,
       [ 783., 1911., 1786., ...,    0., 1535., 2228.],
       [1468., 3156., 3315., ..., 1535.,    0., 3760.],
       [2643., 1831.,  454., ..., 2228., 3760.,    0.]])}
2025-06-26 20:43:57,746 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:43:57,746 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:43:57,746 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:43:57,756 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:43:57,756 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:43:57,756 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:43:57,757 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:43:57,757 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:43:57,757 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:43:57,758 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:43:57,758 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {'(18, 74)': 1.0, '(84, 67)': 1.0, '(67, 72)': 1.0}, 'common_edge_ratio': 0.03}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 136533.0, 'avg_gap': 152420.63333333333}, 'structure_gap': {'unique_elite_edges': 198, 'unique_pop_edges': 677, 'common_edges': 11}}, 'elite_diversity': {'diversity_score': 0.12}}
2025-06-26 20:43:57,758 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:43:57,758 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:43:57,760 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:43:57,761 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 157815.0, mean 173865.3, max 192926.0, std 11466.502248288272
- diversity: 0.8900000000000001
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [150781.0, 173147.0, 157815.0] (Δ 7666.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [19.0, 24.0, 3955.0, 1969.0]
- centroid/spread: [2011.37, 1064.48] | rms 1365.283
- density_grid(3x3): [[11, 5, 11], [14, 10, 8], [13, 13, 15]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 985.0, 1586.0, 2370.5, 4150.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [26, 4, 5, 21, 17, 6, 8, 12]
- nn_median_dist: 140.036

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:43:57,762 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:44:26,856 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "potentially multimodal",
    "deceptiveness": "low to moderate",
    "edge_length_variability": "high (min 13.0, max 4150.0)",
    "long_edge_ratio": "significant (25%)"
  },
  "population_state": {
    "coverage": "good (diversity 0.89)",
    "convergence": "none detected (0.0)",
    "density_distribution": "uneven (higher density in bottom-right grid cell)",
    "spread": "wide (rms 1365.283)"
  },
  "difficult_regions": {
    "long_edge_corridors": "present (q3 edge length 2370.5)",
    "low_density_areas": "top-center grid cell (5 nodes)",
    "directional_bias": "strong in sectors 0 and 3"
  },
  "opportunity_regions": {
    "high_density_cells": "bottom-right (15 nodes), top-left (11 nodes)",
    "short_edge_clusters": "median nn distance 140.036",
    "potential_subpaths": "none identified yet"
  },
  "evolution_phase": "early exploration (iteration 2/5)",
  "evolution_direction": {
    "suggested_operators": [
      "edge recombination emphasizing high-density regions",
      "directed local search towards low-density areas",
      "long-edge elimination heuristics",
      "directional bias correction in mutation"
    ],
    "focus_areas": [
      "exploit high-density clusters",
      "improve coverage in sparse regions",
      "reduce longest 25% edges"
    ]
  }
}
```
2025-06-26 20:44:26,856 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:44:26,856 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low to moderate', 'edge_length_variability': 'high (min 13.0, max 4150.0)', 'long_edge_ratio': 'significant (25%)'}, 'population_state': {'coverage': 'good (diversity 0.89)', 'convergence': 'none detected (0.0)', 'density_distribution': 'uneven (higher density in bottom-right grid cell)', 'spread': 'wide (rms 1365.283)'}, 'difficult_regions': {'long_edge_corridors': 'present (q3 edge length 2370.5)', 'low_density_areas': 'top-center grid cell (5 nodes)', 'directional_bias': 'strong in sectors 0 and 3'}, 'opportunity_regions': {'high_density_cells': 'bottom-right (15 nodes), top-left (11 nodes)', 'short_edge_clusters': 'median nn distance 140.036', 'potential_subpaths': 'none identified yet'}, 'evolution_phase': 'early exploration (iteration 2/5)', 'evolution_direction': {'suggested_operators': ['edge recombination emphasizing high-density regions', 'directed local search towards low-density areas', 'long-edge elimination heuristics', 'directional bias correction in mutation'], 'focus_areas': ['exploit high-density clusters', 'improve coverage in sparse regions', 'reduce longest 25% edges']}}
2025-06-26 20:44:26,856 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:44:26,856 - __main__ - INFO - 分析阶段完成
2025-06-26 20:44:26,856 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low to moderate', 'edge_length_variability': 'high (min 13.0, max 4150.0)', 'long_edge_ratio': 'significant (25%)'}, 'population_state': {'coverage': 'good (diversity 0.89)', 'convergence': 'none detected (0.0)', 'density_distribution': 'uneven (higher density in bottom-right grid cell)', 'spread': 'wide (rms 1365.283)'}, 'difficult_regions': {'long_edge_corridors': 'present (q3 edge length 2370.5)', 'low_density_areas': 'top-center grid cell (5 nodes)', 'directional_bias': 'strong in sectors 0 and 3'}, 'opportunity_regions': {'high_density_cells': 'bottom-right (15 nodes), top-left (11 nodes)', 'short_edge_clusters': 'median nn distance 140.036', 'potential_subpaths': 'none identified yet'}, 'evolution_phase': 'early exploration (iteration 2/5)', 'evolution_direction': {'suggested_operators': ['edge recombination emphasizing high-density regions', 'directed local search towards low-density areas', 'long-edge elimination heuristics', 'directional bias correction in mutation'], 'focus_areas': ['exploit high-density clusters', 'improve coverage in sparse regions', 'reduce longest 25% edges']}}
2025-06-26 20:44:26,856 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:44:26,856 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:44:26,856 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 157815.0
  • mean_cost: 173865.3
- Search-space: ruggedness moderate, modality potentially multimodal, deceptiveness low to moderate
- Evolution phase: early exploration (iteration 2/5)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): long_edge_corridors, low_density_areas...
- Opportunity regions (# 3): high_density_cells, short_edge_clusters...
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:44:26,856 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:44:26,856 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:44:37,556 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify; bottom 40% exploit current best regions."
}
```
2025-06-26 20:44:37,556 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:44:37,556 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:44:37,556 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify; bottom 40% exploit current best regions."
}
```
2025-06-26 20:44:37,556 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:44:37,556 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:44:37,556 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify; bottom 40% exploit current best regions."
}
```
2025-06-26 20:44:37,556 - __main__ - INFO - 开始进化阶段
2025-06-26 20:44:37,556 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:44:37,556 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:44:37,556 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 190456.0 (rank 80.0%)
- Population diversity: 0.6626262626262627
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:44:37,563 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:44:37,563 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:44:59,657 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 13, 35, 57, 79, 1, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99],
  "targeted_regions": "high_density_ce, short_edge_clus, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of dense clusters and sparse regions"
}
```
2025-06-26 20:44:59,657 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:44:59,657 - ExplorationExpert - INFO - 探索路径生成完成，成本: 172104.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 13, 35, 57, 79, 1, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99]
2025-06-26 20:44:59,657 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 13, 35, 57, 79, 1, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99], 'cur_cost': 172104.0}
2025-06-26 20:44:59,657 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:44:59,657 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:44:59,657 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 161752.0 (rank 10.0%)
- Population diversity: 0.6616161616161617
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:44:59,657 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:44:59,657 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:45:21,129 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0],
  "targeted_regions": "high_density_ce, short_edge_clus, adjacent low-density",
  "strategy_comment": "Cluster hops in dense regions, spiral to sparse cells"
}
```
2025-06-26 20:45:21,129 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:45:21,136 - ExplorationExpert - INFO - 探索路径生成完成，成本: 183842.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0]
2025-06-26 20:45:21,136 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}
2025-06-26 20:45:21,137 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:45:21,137 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:45:21,138 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 176797.0 (rank 50.0%)
- Population diversity: 0.7070707070707071
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:45:21,138 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:45:21,138 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:45:38,729 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0],
  "targeted_regions": "high_density_ce, short_edge_clus, adjacent low-density",
  "strategy_comment": "Cluster-first routing with sparse cell exploration"
}
```
2025-06-26 20:45:38,731 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:45:38,731 - ExplorationExpert - INFO - 探索路径生成完成，成本: 183842.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0]
2025-06-26 20:45:38,731 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}
2025-06-26 20:45:38,731 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:45:38,731 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:45:38,731 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 162206.0 (rank 10.0%)
- Population diversity: 0.6828282828282828
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:45:38,731 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:45:38,731 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:46:00,519 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99],
  "targeted_regions": "high_density_ce, short_edge_clus, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of opportunity regions with sparse adjacency"
}
```
2025-06-26 20:46:00,519 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:46:00,519 - ExplorationExpert - INFO - 探索路径生成完成，成本: 170722.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99]
2025-06-26 20:46:00,519 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}
2025-06-26 20:46:00,519 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:46:00,519 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:46:00,519 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 192926.0 (rank 90.0%)
- Population diversity: 0.6707070707070707
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:46:00,519 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:46:00,519 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:46:21,340 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 31, 53, 75, 97, 18, 40, 62, 84, 16, 47, 69, 71, 93, 25, 36, 58, 60, 82, 14, 27, 49, 51, 73, 95, 20, 42, 64, 86, 38, 55, 77, 99, 11, 33, 29],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Balanced exploration of dense clusters with sparse adjacency"
}
```
2025-06-26 20:46:21,340 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:46:21,341 - ExplorationExpert - INFO - 探索路径生成完成，成本: 170584.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 31, 53, 75, 97, 18, 40, 62, 84, 16, 47, 69, 71, 93, 25, 36, 58, 60, 82, 14, 27, 49, 51, 73, 95, 20, 42, 64, 86, 38, 55, 77, 99, 11, 33, 29]
2025-06-26 20:46:21,341 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 31, 53, 75, 97, 18, 40, 62, 84, 16, 47, 69, 71, 93, 25, 36, 58, 60, 82, 14, 27, 49, 51, 73, 95, 20, 42, 64, 86, 38, 55, 77, 99, 11, 33, 29], 'cur_cost': 170584.0}
2025-06-26 20:46:21,341 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:46:21,341 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:46:21,343 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 177530.0 (rank 60.0%)
- Population diversity: 0.6313131313131313
- Evolution phase: early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:46:21,343 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:46:21,343 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:46:47,911 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 99, 11, 33, 55, 77, 88, 0, 22, 44, 66, 79, 20, 31, 57, 35],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters with short edges, skip long corridors"
}
```
2025-06-26 20:46:47,911 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:46:47,911 - ExplorationExpert - INFO - 探索路径生成完成，成本: 165452.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 99, 11, 33, 55, 77, 88, 0, 22, 44, 66, 79, 20, 31, 57, 35]
2025-06-26 20:46:47,911 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 99, 11, 33, 55, 77, 88, 0, 22, 44, 66, 79, 20, 31, 57, 35], 'cur_cost': 165452.0}
2025-06-26 20:46:47,911 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 20:46:47,911 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:46:47,911 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:46:47,911 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 182991.0
2025-06-26 20:46:48,412 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:46:48,412 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:46:48,412 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:46:48,414 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:46:48,415 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 13, 35, 57, 79, 1, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99], 'cur_cost': 172104.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 31, 53, 75, 97, 18, 40, 62, 84, 16, 47, 69, 71, 93, 25, 36, 58, 60, 82, 14, 27, 49, 51, 73, 95, 20, 42, 64, 86, 38, 55, 77, 99, 11, 33, 29], 'cur_cost': 170584.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 99, 11, 33, 55, 77, 88, 0, 22, 44, 66, 79, 20, 31, 57, 35], 'cur_cost': 165452.0}, {'tour': array([98, 86, 44, 59,  7, 28, 78, 21, 49, 76, 48, 79, 41, 68,  0, 74,  8,
       24, 71, 15, 17, 67, 57, 80, 88, 50, 47, 64,  9, 38, 27, 58, 19, 92,
       81, 62, 30, 29, 77, 91, 61, 63, 89, 26, 96, 22,  5, 94, 69, 82, 33,
       37, 13, 84, 85, 46, 45, 53, 31,  4,  1, 72, 36, 93, 32, 40, 75, 18,
       25, 60, 73, 66, 14, 97,  2, 83, 56,  6, 55, 95, 51, 35, 11, 87, 39,
       65, 16, 34, 99, 90, 23, 10,  3, 42, 52, 12, 20, 43, 54, 70]), 'cur_cost': 182991.0}, {'tour': array([51, 95, 40, 35, 46, 56, 86, 62, 18, 77, 32, 54, 55,  8, 50, 93, 29,
       83, 16, 63, 94, 31, 47, 70, 39, 36, 23, 12, 10, 73, 87, 30, 90, 98,
        5, 85, 52, 15, 24, 78,  4, 48, 44, 19, 72, 20,  0, 74, 75, 60, 99,
       92, 58, 37, 34, 26, 97, 61, 25, 69, 82, 53, 96, 21, 67, 80, 68, 76,
       27, 11,  2, 22, 13,  7, 66, 65, 57, 28, 81, 41, 88,  6, 84,  3,  1,
       71, 49, 43, 33, 42,  9, 17, 45, 79, 64, 38, 14, 91, 89, 59]), 'cur_cost': 165421.0}, {'tour': array([42, 51, 43, 26, 23, 50, 68, 22, 92, 99, 62, 59, 63, 33, 73,  0, 89,
       18, 10, 78, 29,  5, 52, 48, 30, 93, 60, 44, 13,  8, 47, 28, 66, 12,
        3, 35, 40, 41, 71, 98, 14, 77, 11, 38, 58, 17, 53, 54, 95, 86,  2,
       85, 90, 19, 76, 34,  1, 65, 56,  7, 81,  4, 87,  9, 83, 74, 69, 67,
       96, 31, 21, 37, 80, 91,  6, 16, 25, 70, 32, 46, 97, 57, 79, 15, 84,
       20, 64, 55, 36, 39, 49, 45, 75, 72, 61, 88, 94, 82, 24, 27]), 'cur_cost': 157815.0}, {'tour': array([88,  9,  6, 33, 86,  1, 56, 60, 21, 98, 19, 62, 31, 84, 32, 96, 47,
        2, 55, 76, 89,  7, 66, 24, 10, 15, 43, 52, 73, 25,  8, 91, 34, 78,
       37, 72,  0, 12, 54, 30, 36, 85, 42, 45, 63, 99, 38, 70, 14, 95, 41,
       29, 23, 53,  3, 87, 93, 58, 11, 49, 44, 35, 82, 75, 90, 61, 40, 71,
       92, 28, 39,  5, 50, 77, 57, 94, 68, 27, 16, 69, 59, 20, 18, 65,  4,
       22, 46, 80, 97, 17, 51, 26, 48, 81, 64, 67, 74, 79, 83, 13]), 'cur_cost': 173374.0}]
2025-06-26 20:46:48,417 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:46:48,417 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 60, 'skip_rate': 0.05, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 57, 'cache_hits': 37, 'similarity_calculations': 463, 'cache_hit_rate': 0.07991360691144708, 'cache_size': 426}}
2025-06-26 20:46:48,417 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 20:46:48,418 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:46:48,418 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:46:48,419 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:46:48,419 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 164593.0
2025-06-26 20:46:48,919 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:46:48,921 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:46:48,921 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:46:48,922 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:46:48,922 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 13, 35, 57, 79, 1, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99], 'cur_cost': 172104.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 31, 53, 75, 97, 18, 40, 62, 84, 16, 47, 69, 71, 93, 25, 36, 58, 60, 82, 14, 27, 49, 51, 73, 95, 20, 42, 64, 86, 38, 55, 77, 99, 11, 33, 29], 'cur_cost': 170584.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 99, 11, 33, 55, 77, 88, 0, 22, 44, 66, 79, 20, 31, 57, 35], 'cur_cost': 165452.0}, {'tour': array([98, 86, 44, 59,  7, 28, 78, 21, 49, 76, 48, 79, 41, 68,  0, 74,  8,
       24, 71, 15, 17, 67, 57, 80, 88, 50, 47, 64,  9, 38, 27, 58, 19, 92,
       81, 62, 30, 29, 77, 91, 61, 63, 89, 26, 96, 22,  5, 94, 69, 82, 33,
       37, 13, 84, 85, 46, 45, 53, 31,  4,  1, 72, 36, 93, 32, 40, 75, 18,
       25, 60, 73, 66, 14, 97,  2, 83, 56,  6, 55, 95, 51, 35, 11, 87, 39,
       65, 16, 34, 99, 90, 23, 10,  3, 42, 52, 12, 20, 43, 54, 70]), 'cur_cost': 182991.0}, {'tour': array([59,  6, 39, 63,  0,  3, 15, 29, 73, 96, 69, 43, 31, 71, 25,  1, 85,
       87, 60,  8, 13, 94, 61, 66, 65, 19, 30, 74, 72, 40,  2, 41, 70, 89,
       21, 33, 10, 75, 97, 62, 98, 24, 49, 82, 77, 14,  9, 51, 28, 27, 92,
       95, 54, 80, 36, 84, 42, 17, 16, 91, 11,  5, 79, 64, 57, 67, 90, 88,
        4, 32, 76, 37, 34, 22, 55, 58, 78, 35,  7, 50, 52, 18, 86, 46, 23,
       56, 81, 83, 53, 68, 26, 44, 47, 48, 20, 12, 38, 93, 45, 99]), 'cur_cost': 164593.0}, {'tour': array([42, 51, 43, 26, 23, 50, 68, 22, 92, 99, 62, 59, 63, 33, 73,  0, 89,
       18, 10, 78, 29,  5, 52, 48, 30, 93, 60, 44, 13,  8, 47, 28, 66, 12,
        3, 35, 40, 41, 71, 98, 14, 77, 11, 38, 58, 17, 53, 54, 95, 86,  2,
       85, 90, 19, 76, 34,  1, 65, 56,  7, 81,  4, 87,  9, 83, 74, 69, 67,
       96, 31, 21, 37, 80, 91,  6, 16, 25, 70, 32, 46, 97, 57, 79, 15, 84,
       20, 64, 55, 36, 39, 49, 45, 75, 72, 61, 88, 94, 82, 24, 27]), 'cur_cost': 157815.0}, {'tour': array([88,  9,  6, 33, 86,  1, 56, 60, 21, 98, 19, 62, 31, 84, 32, 96, 47,
        2, 55, 76, 89,  7, 66, 24, 10, 15, 43, 52, 73, 25,  8, 91, 34, 78,
       37, 72,  0, 12, 54, 30, 36, 85, 42, 45, 63, 99, 38, 70, 14, 95, 41,
       29, 23, 53,  3, 87, 93, 58, 11, 49, 44, 35, 82, 75, 90, 61, 40, 71,
       92, 28, 39,  5, 50, 77, 57, 94, 68, 27, 16, 69, 59, 20, 18, 65,  4,
       22, 46, 80, 97, 17, 51, 26, 48, 81, 64, 67, 74, 79, 83, 13]), 'cur_cost': 173374.0}]
2025-06-26 20:46:48,924 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-26 20:46:48,924 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 61, 'skip_rate': 0.04918032786885246, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 58, 'cache_hits': 37, 'similarity_calculations': 469, 'cache_hit_rate': 0.07889125799573561, 'cache_size': 432}}
2025-06-26 20:46:48,926 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:46:48,926 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:46:48,926 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:46:48,926 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:46:48,926 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 168662.0
2025-06-26 20:46:49,428 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:46:49,428 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:46:49,430 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:46:49,430 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:46:49,430 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 13, 35, 57, 79, 1, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99], 'cur_cost': 172104.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 31, 53, 75, 97, 18, 40, 62, 84, 16, 47, 69, 71, 93, 25, 36, 58, 60, 82, 14, 27, 49, 51, 73, 95, 20, 42, 64, 86, 38, 55, 77, 99, 11, 33, 29], 'cur_cost': 170584.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 99, 11, 33, 55, 77, 88, 0, 22, 44, 66, 79, 20, 31, 57, 35], 'cur_cost': 165452.0}, {'tour': array([98, 86, 44, 59,  7, 28, 78, 21, 49, 76, 48, 79, 41, 68,  0, 74,  8,
       24, 71, 15, 17, 67, 57, 80, 88, 50, 47, 64,  9, 38, 27, 58, 19, 92,
       81, 62, 30, 29, 77, 91, 61, 63, 89, 26, 96, 22,  5, 94, 69, 82, 33,
       37, 13, 84, 85, 46, 45, 53, 31,  4,  1, 72, 36, 93, 32, 40, 75, 18,
       25, 60, 73, 66, 14, 97,  2, 83, 56,  6, 55, 95, 51, 35, 11, 87, 39,
       65, 16, 34, 99, 90, 23, 10,  3, 42, 52, 12, 20, 43, 54, 70]), 'cur_cost': 182991.0}, {'tour': array([59,  6, 39, 63,  0,  3, 15, 29, 73, 96, 69, 43, 31, 71, 25,  1, 85,
       87, 60,  8, 13, 94, 61, 66, 65, 19, 30, 74, 72, 40,  2, 41, 70, 89,
       21, 33, 10, 75, 97, 62, 98, 24, 49, 82, 77, 14,  9, 51, 28, 27, 92,
       95, 54, 80, 36, 84, 42, 17, 16, 91, 11,  5, 79, 64, 57, 67, 90, 88,
        4, 32, 76, 37, 34, 22, 55, 58, 78, 35,  7, 50, 52, 18, 86, 46, 23,
       56, 81, 83, 53, 68, 26, 44, 47, 48, 20, 12, 38, 93, 45, 99]), 'cur_cost': 164593.0}, {'tour': array([44, 93, 32, 71, 10, 49, 86, 62, 58, 36, 85, 48, 98, 56, 69, 61, 11,
       25, 53, 54, 37, 92, 60, 22, 18, 24,  4,  2, 67, 29,  6, 57, 76, 81,
        5, 30, 91, 51, 16, 17, 97, 40, 59, 33, 46, 83, 87, 42, 72, 66, 94,
       43,  1, 78, 84, 19, 75,  7, 14,  9, 96, 90, 82, 13, 28, 64, 77, 88,
       15, 70, 55,  0, 20, 73,  8,  3, 74, 31, 26, 47, 23, 99, 63, 41, 79,
       21, 65, 12, 95, 50, 38, 52, 27, 68, 45, 35, 39, 89, 80, 34]), 'cur_cost': 168662.0}, {'tour': array([88,  9,  6, 33, 86,  1, 56, 60, 21, 98, 19, 62, 31, 84, 32, 96, 47,
        2, 55, 76, 89,  7, 66, 24, 10, 15, 43, 52, 73, 25,  8, 91, 34, 78,
       37, 72,  0, 12, 54, 30, 36, 85, 42, 45, 63, 99, 38, 70, 14, 95, 41,
       29, 23, 53,  3, 87, 93, 58, 11, 49, 44, 35, 82, 75, 90, 61, 40, 71,
       92, 28, 39,  5, 50, 77, 57, 94, 68, 27, 16, 69, 59, 20, 18, 65,  4,
       22, 46, 80, 97, 17, 51, 26, 48, 81, 64, 67, 74, 79, 83, 13]), 'cur_cost': 173374.0}]
2025-06-26 20:46:49,435 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:46:49,435 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 62, 'skip_rate': 0.04838709677419355, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 59, 'cache_hits': 37, 'similarity_calculations': 476, 'cache_hit_rate': 0.07773109243697479, 'cache_size': 439}}
2025-06-26 20:46:49,435 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:46:49,435 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:46:49,435 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:46:49,435 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:46:49,435 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 165835.0
2025-06-26 20:46:49,938 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:46:49,939 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:46:49,939 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:46:49,940 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:46:49,940 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 13, 35, 57, 79, 1, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99], 'cur_cost': 172104.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 31, 53, 75, 97, 18, 40, 62, 84, 16, 47, 69, 71, 93, 25, 36, 58, 60, 82, 14, 27, 49, 51, 73, 95, 20, 42, 64, 86, 38, 55, 77, 99, 11, 33, 29], 'cur_cost': 170584.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 99, 11, 33, 55, 77, 88, 0, 22, 44, 66, 79, 20, 31, 57, 35], 'cur_cost': 165452.0}, {'tour': array([98, 86, 44, 59,  7, 28, 78, 21, 49, 76, 48, 79, 41, 68,  0, 74,  8,
       24, 71, 15, 17, 67, 57, 80, 88, 50, 47, 64,  9, 38, 27, 58, 19, 92,
       81, 62, 30, 29, 77, 91, 61, 63, 89, 26, 96, 22,  5, 94, 69, 82, 33,
       37, 13, 84, 85, 46, 45, 53, 31,  4,  1, 72, 36, 93, 32, 40, 75, 18,
       25, 60, 73, 66, 14, 97,  2, 83, 56,  6, 55, 95, 51, 35, 11, 87, 39,
       65, 16, 34, 99, 90, 23, 10,  3, 42, 52, 12, 20, 43, 54, 70]), 'cur_cost': 182991.0}, {'tour': array([59,  6, 39, 63,  0,  3, 15, 29, 73, 96, 69, 43, 31, 71, 25,  1, 85,
       87, 60,  8, 13, 94, 61, 66, 65, 19, 30, 74, 72, 40,  2, 41, 70, 89,
       21, 33, 10, 75, 97, 62, 98, 24, 49, 82, 77, 14,  9, 51, 28, 27, 92,
       95, 54, 80, 36, 84, 42, 17, 16, 91, 11,  5, 79, 64, 57, 67, 90, 88,
        4, 32, 76, 37, 34, 22, 55, 58, 78, 35,  7, 50, 52, 18, 86, 46, 23,
       56, 81, 83, 53, 68, 26, 44, 47, 48, 20, 12, 38, 93, 45, 99]), 'cur_cost': 164593.0}, {'tour': array([44, 93, 32, 71, 10, 49, 86, 62, 58, 36, 85, 48, 98, 56, 69, 61, 11,
       25, 53, 54, 37, 92, 60, 22, 18, 24,  4,  2, 67, 29,  6, 57, 76, 81,
        5, 30, 91, 51, 16, 17, 97, 40, 59, 33, 46, 83, 87, 42, 72, 66, 94,
       43,  1, 78, 84, 19, 75,  7, 14,  9, 96, 90, 82, 13, 28, 64, 77, 88,
       15, 70, 55,  0, 20, 73,  8,  3, 74, 31, 26, 47, 23, 99, 63, 41, 79,
       21, 65, 12, 95, 50, 38, 52, 27, 68, 45, 35, 39, 89, 80, 34]), 'cur_cost': 168662.0}, {'tour': array([23,  9, 59, 74, 30, 88, 84, 71, 69, 80,  0, 41, 33, 73, 75, 39, 55,
       24, 91, 94, 78, 21, 45, 97, 36, 62, 99, 83,  3, 95, 90, 43, 49, 82,
        4, 51, 26, 61, 56, 66, 48, 58, 18,  2, 87,  5, 70, 16, 67,  1, 38,
       76, 17, 57, 42,  6, 34, 15, 63, 31, 64, 40, 96, 35, 52,  7, 25, 93,
       44, 37, 47, 19, 60, 68, 12, 98, 27, 89, 29, 11, 81, 77, 54, 65, 32,
       53, 46, 86, 72, 13, 50, 85, 10, 14, 92, 20, 22, 28,  8, 79]), 'cur_cost': 165835.0}]
2025-06-26 20:46:49,942 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:46:49,942 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 63, 'skip_rate': 0.047619047619047616, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 60, 'cache_hits': 37, 'similarity_calculations': 484, 'cache_hit_rate': 0.07644628099173553, 'cache_size': 447}}
2025-06-26 20:46:49,942 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:46:49,942 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 13, 35, 57, 79, 1, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99], 'cur_cost': 172104.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 24, 35, 46, 57, 68, 79, 80, 2, 14, 25, 36, 47, 58, 69, 70, 3, 15, 26, 37, 48, 59, 60, 4, 16, 27, 38, 49, 50, 5, 17, 28, 39, 40, 6, 18, 29, 30, 7, 19, 20, 8, 21, 22, 9, 10, 11, 31, 32, 33, 41, 42, 43, 44, 51, 52, 53, 54, 55, 61, 62, 63, 64, 65, 66, 71, 72, 73, 74, 75, 76, 77, 81, 82, 83, 84, 85, 86, 87, 88, 91, 92, 93, 94, 95, 96, 97, 98, 99, 0], 'cur_cost': 183842.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 31, 53, 75, 97, 18, 40, 62, 84, 16, 47, 69, 71, 93, 25, 36, 58, 60, 82, 14, 27, 49, 51, 73, 95, 20, 42, 64, 86, 38, 55, 77, 99, 11, 33, 29], 'cur_cost': 170584.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 43, 65, 87, 9, 21, 53, 75, 97, 19, 42, 64, 86, 8, 30, 52, 74, 96, 18, 41, 63, 85, 7, 29, 51, 73, 95, 17, 40, 62, 84, 6, 28, 50, 72, 94, 16, 39, 61, 83, 5, 27, 49, 71, 93, 15, 38, 60, 82, 4, 26, 48, 70, 92, 14, 37, 59, 81, 3, 25, 47, 69, 91, 13, 36, 58, 80, 2, 24, 46, 68, 99, 11, 33, 55, 77, 88, 0, 22, 44, 66, 79, 20, 31, 57, 35], 'cur_cost': 165452.0}}]
2025-06-26 20:46:49,944 - __main__ - INFO - 进化阶段完成
2025-06-26 20:46:49,944 - __main__ - INFO - 开始评估阶段
2025-06-26 20:46:49,945 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:46:49,945 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:46:49,946 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 157815.0 → 164593.0 (Δ -6778.0)
- Mean cost: 173865.3 → 172862.7
- Diversity: 0.663 → 0.620 (Δ -0.0424)

#### Elite metrics
- Elite count: 3 → 3
- Best elite cost: 21282 → 21282 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 50.0% , avg Δ 2520.17
- Exploit: 4 inds, success 50.0% , avg Δ -1273.75

#### Other indicators
- No-change individuals: 0
- Regression individuals: 5
- Historical trends: Last 3 iterations: costs [150781.0, 173147.0, 157815.0], diversity [0.8926900584795323, 0.39333333333333326, 0.8900000000000001]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:46:49,946 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:47:07,211 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 50.0,
      "average_improvement": 2520.17,
      "contribution_to_elite": "moderate (positive avg Δ but no elite improvement)"
    },
    "Exploit": {
      "success_rate": 50.0,
      "average_improvement": -1273.75,
      "contribution_to_elite": "neutral (mixed results)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "slightly imbalanced (explore shows better potential but exploit drags performance)",
    "diversity_warning": "declining diversity (-0.042) may limit exploration effectiveness"
  },
  "recommendations": {
    "explore_ratio": 0.65,
    "diversity_weight": 0.75,
    "additional_actions": [
      "introduce 1-2 random immigrants to counter regression",
      "increase elite pressure by cloning top 1-2 elites more aggressively"
    ]
  }
}
```
2025-06-26 20:47:07,278 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:47:07,278 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 50.0,
      "average_improvement": 2520.17,
      "contribution_to_elite": "moderate (positive avg Δ but no elite improvement)"
    },
    "Exploit": {
      "success_rate": 50.0,
      "average_improvement": -1273.75,
      "contribution_to_elite": "neutral (mixed results)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "slightly imbalanced (explore shows better potential but exploit drags performance)",
    "diversity_warning": "declining diversity (-0.042) may limit exploration effectiveness"
  },
  "recommendations": {
    "explore_ratio": 0.65,
    "diversity_weight": 0.75,
    "additional_actions": [
      "introduce 1-2 random immigrants to counter regression",
      "increase elite pressure by cloning top 1-2 elites more aggressively"
    ]
  }
}
```
2025-06-26 20:47:07,278 - __main__ - INFO - 评估阶段完成
2025-06-26 20:47:07,278 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 50.0,
      "average_improvement": 2520.17,
      "contribution_to_elite": "moderate (positive avg Δ but no elite improvement)"
    },
    "Exploit": {
      "success_rate": 50.0,
      "average_improvement": -1273.75,
      "contribution_to_elite": "neutral (mixed results)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "slightly imbalanced (explore shows better potential but exploit drags performance)",
    "diversity_warning": "declining diversity (-0.042) may limit exploration effectiveness"
  },
  "recommendations": {
    "explore_ratio": 0.65,
    "diversity_weight": 0.75,
    "additional_actions": [
      "introduce 1-2 random immigrants to counter regression",
      "increase elite pressure by cloning top 1-2 elites more aggressively"
    ]
  }
}
```
2025-06-26 20:47:07,284 - __main__ - INFO - 当前最佳适应度: 164593.0
2025-06-26 20:47:07,285 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\kroA100_route_2.pkl
2025-06-26 20:47:07,285 - __main__ - INFO - kroA100 开始进化第 4 代
2025-06-26 20:47:07,285 - __main__ - INFO - 开始分析阶段
2025-06-26 20:47:07,285 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:47:07,320 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 164593.0, 'max': 183842.0, 'mean': 172862.7, 'std': 7378.103239315645}, 'diversity': 0.8533333333333332, 'clusters': {'clusters': 7, 'cluster_sizes': [3, 2, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:47:07,320 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 164593.0, 'max': 183842.0, 'mean': 172862.7, 'std': 7378.103239315645}, 'diversity_level': 0.8533333333333332, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [3, 2, 1, 1, 1, 1, 1]}, 'coordinates': [[1380, 939], [2848, 96], [3510, 1671], [457, 334], [3888, 666], [984, 965], [2721, 1482], [1286, 525], [2716, 1432], [738, 1325], [1251, 1832], [2728, 1698], [3815, 169], [3683, 1533], [1247, 1945], [123, 862], [1234, 1946], [252, 1240], [611, 673], [2576, 1676], [928, 1700], [53, 857], [1807, 1711], [274, 1420], [2574, 946], [178, 24], [2678, 1825], [1795, 962], [3384, 1498], [3520, 1079], [1256, 61], [1424, 1728], [3913, 192], [3085, 1528], [2573, 1969], [463, 1670], [3875, 598], [298, 1513], [3479, 821], [2542, 236], [3955, 1743], [1323, 280], [3447, 1830], [2936, 337], [1621, 1830], [3373, 1646], [1393, 1368], [3874, 1318], [938, 955], [3022, 474], [2482, 1183], [3854, 923], [376, 825], [2519, 135], [2945, 1622], [953, 268], [2628, 1479], [2097, 981], [890, 1846], [2139, 1806], [2421, 1007], [2290, 1810], [1115, 1052], [2588, 302], [327, 265], [241, 341], [1917, 687], [2991, 792], [2573, 599], [19, 674], [3911, 1673], [872, 1559], [2863, 558], [929, 1766], [839, 620], [3893, 102], [2178, 1619], [3822, 899], [378, 1048], [1178, 100], [2599, 901], [3416, 143], [2961, 1605], [611, 1384], [3113, 885], [2597, 1830], [2586, 1286], [161, 906], [1429, 134], [742, 1025], [1625, 1651], [1187, 706], [1787, 1009], [22, 987], [3640, 43], [3756, 882], [776, 392], [1724, 1642], [198, 1810], [3950, 1558]], 'distance_matrix': array([[   0., 1693., 2252., ...,  783., 1468., 2643.],
       [1693.,    0., 1708., ..., 1911., 3156., 1831.],
       [2252., 1708.,    0., ..., 1786., 3315.,  454.],
       ...,
       [ 783., 1911., 1786., ...,    0., 1535., 2228.],
       [1468., 3156., 3315., ..., 1535.,    0., 3760.],
       [2643., 1831.,  454., ..., 2228., 3760.,    0.]])}
2025-06-26 20:47:07,320 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:47:07,320 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:47:07,320 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:47:07,330 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:47:07,330 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:47:07,330 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:47:07,330 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:47:07,331 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:47:07,331 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:47:07,332 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:47:07,332 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {'(18, 74)': 1.0, '(84, 67)': 1.0, '(67, 72)': 1.0}, 'common_edge_ratio': 0.03}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 143311.0, 'avg_gap': 151418.03333333335}, 'structure_gap': {'unique_elite_edges': 196, 'unique_pop_edges': 628, 'common_edges': 13}}, 'elite_diversity': {'diversity_score': 0.12}}
2025-06-26 20:47:07,332 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:47:07,332 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:47:07,335 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:47:07,335 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 164593.0, mean 172862.7, max 183842.0, std 7378.103239315645
- diversity: 0.8533333333333332
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [173147.0, 157815.0, 164593.0] (Δ 7666.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [19.0, 24.0, 3955.0, 1969.0]
- centroid/spread: [2011.37, 1064.48] | rms 1365.283
- density_grid(3x3): [[11, 5, 11], [14, 10, 8], [13, 13, 15]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 985.0, 1586.0, 2370.5, 4150.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [26, 4, 5, 21, 17, 6, 8, 12]
- nn_median_dist: 140.036

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:47:07,335 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:47:33,018 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant quartile spreads)",
    "modality": "Potentially multimodal (diverse direction histogram with 8 sectors showing varied frequencies)",
    "deceptiveness": "Possible presence (long_edge_ratio of 0.25 suggests some deceptive long edges that may mislead local search)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (high diversity score of 0.85, density_grid shows nodes distributed across all cells)",
    "convergence": "Early stage (0.0 convergence score, recent best costs show improvement Δ7666/iteration)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges in max quartile ≥2370.5 length)",
    "Low-density grid cells (e.g. [1,1] with only 5 nodes)",
    "Direction sectors with low frequency (sectors 1,2,5 with ≤6 counts)"
  ],
  "opportunity_regions": [
    "High-density cells (e.g. [2,2] with 15 nodes, [0,0] and [0,2] with 11 nodes)",
    "Short-edge clusters (edges ≤985.0 in Q1)",
    "Frequent direction sectors (sectors 0,3 with ≥21 counts)"
  ],
  "evolution_phase": "Exploration-dominant (no elites yet, improving costs, high diversity)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Edge-exchange mutations in high-density cells",
    "2. Direction-biased crossovers (favoring sectors 0/3)",
    "3. Long-edge removal heuristics",
    "4. Spatial niching to maintain coverage of low-density regions"
  ]
}
```
2025-06-26 20:47:33,024 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:47:33,024 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant quartile spreads)', 'modality': 'Potentially multimodal (diverse direction histogram with 8 sectors showing varied frequencies)', 'deceptiveness': 'Possible presence (long_edge_ratio of 0.25 suggests some deceptive long edges that may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (high diversity score of 0.85, density_grid shows nodes distributed across all cells)', 'convergence': 'Early stage (0.0 convergence score, recent best costs show improvement Δ7666/iteration)'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile ≥2370.5 length)', 'Low-density grid cells (e.g. [1,1] with only 5 nodes)', 'Direction sectors with low frequency (sectors 1,2,5 with ≤6 counts)'], 'opportunity_regions': ['High-density cells (e.g. [2,2] with 15 nodes, [0,0] and [0,2] with 11 nodes)', 'Short-edge clusters (edges ≤985.0 in Q1)', 'Frequent direction sectors (sectors 0,3 with ≥21 counts)'], 'evolution_phase': 'Exploration-dominant (no elites yet, improving costs, high diversity)', 'evolution_direction': ['Operator suggestions:', '1. Edge-exchange mutations in high-density cells', '2. Direction-biased crossovers (favoring sectors 0/3)', '3. Long-edge removal heuristics', '4. Spatial niching to maintain coverage of low-density regions']}
2025-06-26 20:47:33,024 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:47:33,026 - __main__ - INFO - 分析阶段完成
2025-06-26 20:47:33,026 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant quartile spreads)', 'modality': 'Potentially multimodal (diverse direction histogram with 8 sectors showing varied frequencies)', 'deceptiveness': 'Possible presence (long_edge_ratio of 0.25 suggests some deceptive long edges that may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (high diversity score of 0.85, density_grid shows nodes distributed across all cells)', 'convergence': 'Early stage (0.0 convergence score, recent best costs show improvement Δ7666/iteration)'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile ≥2370.5 length)', 'Low-density grid cells (e.g. [1,1] with only 5 nodes)', 'Direction sectors with low frequency (sectors 1,2,5 with ≤6 counts)'], 'opportunity_regions': ['High-density cells (e.g. [2,2] with 15 nodes, [0,0] and [0,2] with 11 nodes)', 'Short-edge clusters (edges ≤985.0 in Q1)', 'Frequent direction sectors (sectors 0,3 with ≥21 counts)'], 'evolution_phase': 'Exploration-dominant (no elites yet, improving costs, high diversity)', 'evolution_direction': ['Operator suggestions:', '1. Edge-exchange mutations in high-density cells', '2. Direction-biased crossovers (favoring sectors 0/3)', '3. Long-edge removal heuristics', '4. Spatial niching to maintain coverage of low-density regions']}
2025-06-26 20:47:33,026 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:47:33,026 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:47:33,026 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 164593.0
  • mean_cost: 172862.7
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant quartile spreads), modality Potentially multimodal (diverse direction histogram with 8 sectors showing varied frequencies), deceptiveness Possible presence (long_edge_ratio of 0.25 suggests some deceptive long edges that may mislead local search)
- Evolution phase: Exploration-dominant (no elites yet, improving costs, high diversity)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): Long-edge corridors , Low-density grid cel...
- Opportunity regions (# 3): High-density cells (, Short-edge clusters ...
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:47:33,026 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:47:33,026 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:47:43,602 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration (70%). Top 60% explore to diversify; bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 20:47:43,602 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:47:43,602 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:47:43,602 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration (70%). Top 60% explore to diversify; bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 20:47:43,606 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:47:43,606 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:47:43,606 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration (70%). Top 60% explore to diversify; bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 20:47:43,607 - __main__ - INFO - 开始进化阶段
2025-06-26 20:47:43,608 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:47:43,608 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:47:43,608 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 172104.0 (rank 60.0%)
- Population diversity: 0.6202020202020202
- Evolution phase: Exploration-dominant (no elites yet, improving costs, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:47:43,609 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:47:43,609 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:48:05,348 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 97, 31, 53, 75, 97, 38, 60, 82, 36, 58, 84, 40],
  "targeted_regions": "High-density ce, Short-edge clusters, adjacent low-density cells",
  "strategy_comment": "Prioritize opportunity regions with zigzag pattern to boost diversity"
}
```
2025-06-26 20:48:05,356 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:48:05,356 - ExplorationExpert - INFO - 探索路径生成完成，成本: 172783.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 97, 31, 53, 75, 38, 60, 82, 36, 58, 84, 40, 62]
2025-06-26 20:48:05,356 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 97, 31, 53, 75, 38, 60, 82, 36, 58, 84, 40, 62], 'cur_cost': 172783.0}
2025-06-26 20:48:05,356 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:48:05,356 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:48:05,356 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 183842.0 (rank 80.0%)
- Population diversity: 0.6262626262626263
- Evolution phase: Exploration-dominant (no elites yet, improving costs, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:48:05,360 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:48:05,360 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:48:26,804 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 99, 22, 55, 68, 21, 54, 87, 10, 33, 66, 99, 22, 55, 68, 21, 54, 87, 10, 33, 66, 99, 22, 55, 68, 21],
  "targeted_regions": "High-density ce, Short-edge clusters, adjacent low-density cells",
  "strategy_comment": "Leverage opportunity regions while avoiding long edges"
}
```
2025-06-26 20:48:26,804 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:48:26,804 - ExplorationExpert - INFO - 探索路径生成完成，成本: 179636.0, 路径: [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 99, 22, 55, 68, 21, 54, 95, 0, 51, 52, 97, 79, 50, 49, 35, 96, 48, 46, 91, 98, 47, 93, 43, 92, 94]
2025-06-26 20:48:26,804 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 99, 22, 55, 68, 21, 54, 95, 0, 51, 52, 97, 79, 50, 49, 35, 96, 48, 46, 91, 98, 47, 93, 43, 92, 94], 'cur_cost': 179636.0}
2025-06-26 20:48:26,804 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:48:26,804 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:48:26,804 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 183842.0 (rank 90.0%)
- Population diversity: 0.704040404040404
- Evolution phase: Exploration-dominant (no elites yet, improving costs, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:48:26,804 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:48:26,804 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:48:49,204 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 2, 24, 46, 68, 80, 3, 14, 36, 58, 70, 4, 25, 47, 69, 81, 5, 15, 37, 59, 71, 6, 26, 48, 60, 82, 7, 16, 38, 50, 72, 8, 27, 49, 61, 83, 9, 17, 39, 51, 73, 10, 28, 40, 62, 84, 11, 18, 30, 52, 74, 19, 29, 41, 63, 85, 20, 21, 31, 53, 75, 22, 32, 42, 64, 86, 33, 43, 54, 76, 87, 44, 55, 65, 77, 88, 66, 0, 99, 98, 97, 96, 95, 94, 93, 92, 91],
  "targeted_regions": "High-density ce, Short-edge clusters, adjacent low-density cells",
  "strategy_comment": "Cluster hops in dense regions, sparse exploration at edges"
}
```
2025-06-26 20:48:49,204 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:48:49,204 - ExplorationExpert - INFO - 探索路径生成完成，成本: 178692.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 2, 24, 46, 68, 80, 3, 14, 36, 58, 70, 4, 25, 47, 69, 81, 5, 15, 37, 59, 71, 6, 26, 48, 60, 82, 7, 16, 38, 50, 72, 8, 27, 49, 61, 83, 9, 17, 39, 51, 73, 10, 28, 40, 62, 84, 11, 18, 30, 52, 74, 19, 29, 41, 63, 85, 20, 21, 31, 53, 75, 22, 32, 42, 64, 86, 33, 43, 54, 76, 87, 44, 55, 65, 77, 88, 66, 0, 99, 98, 97, 96, 95, 94, 93, 92, 91]
2025-06-26 20:48:49,204 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 2, 24, 46, 68, 80, 3, 14, 36, 58, 70, 4, 25, 47, 69, 81, 5, 15, 37, 59, 71, 6, 26, 48, 60, 82, 7, 16, 38, 50, 72, 8, 27, 49, 61, 83, 9, 17, 39, 51, 73, 10, 28, 40, 62, 84, 11, 18, 30, 52, 74, 19, 29, 41, 63, 85, 20, 21, 31, 53, 75, 22, 32, 42, 64, 86, 33, 43, 54, 76, 87, 44, 55, 65, 77, 88, 66, 0, 99, 98, 97, 96, 95, 94, 93, 92, 91], 'cur_cost': 178692.0}
2025-06-26 20:48:49,206 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:48:49,206 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:48:49,207 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 170722.0 (rank 50.0%)
- Population diversity: 0.6818181818181818
- Evolution phase: Exploration-dominant (no elites yet, improving costs, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:48:49,207 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:48:49,207 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:49:10,708 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 73, 16, 59, 92, 25, 58, 81, 4, 27, 60, 93, 26, 59, 82, 15, 48, 71],
  "targeted_regions": "High-density ce, Short-edge clusters, adjacent low-density cells",
  "strategy_comment": "Balanced exploration with focus on opportunity regions"
}
```
2025-06-26 20:49:10,708 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:49:10,708 - ExplorationExpert - INFO - 探索路径生成完成，成本: 187542.0, 路径: [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 27, 26, 82, 0, 31, 84, 83, 33, 85, 29, 28, 86, 32]
2025-06-26 20:49:10,708 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 27, 26, 82, 0, 31, 84, 83, 33, 85, 29, 28, 86, 32], 'cur_cost': 187542.0}
2025-06-26 20:49:10,708 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:49:10,708 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:49:10,708 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 170584.0 (rank 40.0%)
- Population diversity: 0.7383838383838384
- Evolution phase: Exploration-dominant (no elites yet, improving costs, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:49:10,714 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:49:10,714 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:49:32,386 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 80, 2, 14, 36, 58, 70, 92, 13, 35, 57, 79, 91, 3, 24, 46, 68, 81, 5, 16, 38, 50, 72, 94, 15, 37, 59, 71, 93, 6, 27, 49, 61, 83, 7, 28, 40, 62, 84, 17, 48, 75, 97, 19, 31, 53, 85, 8, 20, 42, 64, 86, 11, 33, 55, 77, 99, 22, 44, 66, 88, 0, 26, 41, 63, 95, 29, 51, 73],
  "targeted_regions": "High-density ce, Short-edge clusters, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of opportunity regions with sparse cell adjacency"
}
```
2025-06-26 20:49:32,386 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:49:32,391 - ExplorationExpert - INFO - 探索路径生成完成，成本: 170005.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 80, 2, 14, 36, 58, 70, 92, 13, 35, 57, 79, 91, 3, 24, 46, 68, 81, 5, 16, 38, 50, 72, 94, 15, 37, 59, 71, 93, 6, 27, 49, 61, 83, 7, 28, 40, 62, 84, 17, 48, 75, 97, 19, 31, 53, 85, 8, 20, 42, 64, 86, 11, 33, 55, 77, 99, 22, 44, 66, 88, 0, 26, 41, 63, 95, 29, 51, 73]
2025-06-26 20:49:32,391 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 80, 2, 14, 36, 58, 70, 92, 13, 35, 57, 79, 91, 3, 24, 46, 68, 81, 5, 16, 38, 50, 72, 94, 15, 37, 59, 71, 93, 6, 27, 49, 61, 83, 7, 28, 40, 62, 84, 17, 48, 75, 97, 19, 31, 53, 85, 8, 20, 42, 64, 86, 11, 33, 55, 77, 99, 22, 44, 66, 88, 0, 26, 41, 63, 95, 29, 51, 73], 'cur_cost': 170005.0}
2025-06-26 20:49:32,391 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:49:32,391 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:49:32,392 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 165452.0 (rank 10.0%)
- Population diversity: 0.7474747474747475
- Evolution phase: Exploration-dominant (no elites yet, improving costs, high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:49:32,393 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:49:32,393 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:49:54,449 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 2, 24, 46, 68, 80, 3, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 70, 92, 14, 36, 58, 71, 93, 15, 37, 49, 72, 94, 16, 28, 50, 63, 85, 7, 19, 41, 53, 75, 97, 27, 48, 61, 83, 5, 26, 40, 62, 84, 6, 31, 4, 82],
  "targeted_regions": "High-density ce, Short-edge clusters, adjacent low-density cells",
  "strategy_comment": "Prioritize opportunity regions with short edges, avoid long corridors"
}
```
2025-06-26 20:49:54,449 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:49:54,449 - ExplorationExpert - INFO - 探索路径生成完成，成本: 163098.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 2, 24, 46, 68, 80, 3, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 70, 92, 14, 36, 58, 71, 93, 15, 37, 49, 72, 94, 16, 28, 50, 63, 85, 7, 19, 41, 53, 75, 97, 27, 48, 61, 83, 5, 26, 40, 62, 84, 6, 31, 4, 82]
2025-06-26 20:49:54,451 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 2, 24, 46, 68, 80, 3, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 70, 92, 14, 36, 58, 71, 93, 15, 37, 49, 72, 94, 16, 28, 50, 63, 85, 7, 19, 41, 53, 75, 97, 27, 48, 61, 83, 5, 26, 40, 62, 84, 6, 31, 4, 82], 'cur_cost': 163098.0}
2025-06-26 20:49:54,451 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 20:49:54,451 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:49:54,451 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:49:54,452 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 183450.0
2025-06-26 20:49:54,953 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:49:54,953 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:49:54,953 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:49:54,956 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:49:54,956 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 97, 31, 53, 75, 38, 60, 82, 36, 58, 84, 40, 62], 'cur_cost': 172783.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 99, 22, 55, 68, 21, 54, 95, 0, 51, 52, 97, 79, 50, 49, 35, 96, 48, 46, 91, 98, 47, 93, 43, 92, 94], 'cur_cost': 179636.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 2, 24, 46, 68, 80, 3, 14, 36, 58, 70, 4, 25, 47, 69, 81, 5, 15, 37, 59, 71, 6, 26, 48, 60, 82, 7, 16, 38, 50, 72, 8, 27, 49, 61, 83, 9, 17, 39, 51, 73, 10, 28, 40, 62, 84, 11, 18, 30, 52, 74, 19, 29, 41, 63, 85, 20, 21, 31, 53, 75, 22, 32, 42, 64, 86, 33, 43, 54, 76, 87, 44, 55, 65, 77, 88, 66, 0, 99, 98, 97, 96, 95, 94, 93, 92, 91], 'cur_cost': 178692.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 27, 26, 82, 0, 31, 84, 83, 33, 85, 29, 28, 86, 32], 'cur_cost': 187542.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 80, 2, 14, 36, 58, 70, 92, 13, 35, 57, 79, 91, 3, 24, 46, 68, 81, 5, 16, 38, 50, 72, 94, 15, 37, 59, 71, 93, 6, 27, 49, 61, 83, 7, 28, 40, 62, 84, 17, 48, 75, 97, 19, 31, 53, 85, 8, 20, 42, 64, 86, 11, 33, 55, 77, 99, 22, 44, 66, 88, 0, 26, 41, 63, 95, 29, 51, 73], 'cur_cost': 170005.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 2, 24, 46, 68, 80, 3, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 70, 92, 14, 36, 58, 71, 93, 15, 37, 49, 72, 94, 16, 28, 50, 63, 85, 7, 19, 41, 53, 75, 97, 27, 48, 61, 83, 5, 26, 40, 62, 84, 6, 31, 4, 82], 'cur_cost': 163098.0}, {'tour': array([67,  1, 24, 40, 37, 43, 61, 75, 31, 76, 16,  2, 48, 68, 71, 74, 72,
       55, 30, 34, 36, 42, 64, 98,  6, 69,  8, 15, 44, 81, 96, 25, 12,  5,
       29, 65, 66, 88,  3, 59, 91, 10, 89,  7, 85, 54, 22, 45, 57,  0, 46,
       60, 62, 50, 52, 27, 77, 13,  9, 87, 97,  4, 73, 83, 56, 63, 38, 26,
       90, 86, 58, 92, 84, 28, 20, 17, 39, 93, 51, 79, 41, 94, 82, 32, 18,
       19, 99, 14, 80, 95, 78, 70, 53, 35, 23, 47, 33, 21, 49, 11]), 'cur_cost': 183450.0}, {'tour': array([59,  6, 39, 63,  0,  3, 15, 29, 73, 96, 69, 43, 31, 71, 25,  1, 85,
       87, 60,  8, 13, 94, 61, 66, 65, 19, 30, 74, 72, 40,  2, 41, 70, 89,
       21, 33, 10, 75, 97, 62, 98, 24, 49, 82, 77, 14,  9, 51, 28, 27, 92,
       95, 54, 80, 36, 84, 42, 17, 16, 91, 11,  5, 79, 64, 57, 67, 90, 88,
        4, 32, 76, 37, 34, 22, 55, 58, 78, 35,  7, 50, 52, 18, 86, 46, 23,
       56, 81, 83, 53, 68, 26, 44, 47, 48, 20, 12, 38, 93, 45, 99]), 'cur_cost': 164593.0}, {'tour': array([44, 93, 32, 71, 10, 49, 86, 62, 58, 36, 85, 48, 98, 56, 69, 61, 11,
       25, 53, 54, 37, 92, 60, 22, 18, 24,  4,  2, 67, 29,  6, 57, 76, 81,
        5, 30, 91, 51, 16, 17, 97, 40, 59, 33, 46, 83, 87, 42, 72, 66, 94,
       43,  1, 78, 84, 19, 75,  7, 14,  9, 96, 90, 82, 13, 28, 64, 77, 88,
       15, 70, 55,  0, 20, 73,  8,  3, 74, 31, 26, 47, 23, 99, 63, 41, 79,
       21, 65, 12, 95, 50, 38, 52, 27, 68, 45, 35, 39, 89, 80, 34]), 'cur_cost': 168662.0}, {'tour': array([23,  9, 59, 74, 30, 88, 84, 71, 69, 80,  0, 41, 33, 73, 75, 39, 55,
       24, 91, 94, 78, 21, 45, 97, 36, 62, 99, 83,  3, 95, 90, 43, 49, 82,
        4, 51, 26, 61, 56, 66, 48, 58, 18,  2, 87,  5, 70, 16, 67,  1, 38,
       76, 17, 57, 42,  6, 34, 15, 63, 31, 64, 40, 96, 35, 52,  7, 25, 93,
       44, 37, 47, 19, 60, 68, 12, 98, 27, 89, 29, 11, 81, 77, 54, 65, 32,
       53, 46, 86, 72, 13, 50, 85, 10, 14, 92, 20, 22, 28,  8, 79]), 'cur_cost': 165835.0}]
2025-06-26 20:49:54,956 - ExploitationExpert - INFO - 局部搜索耗时: 0.50秒
2025-06-26 20:49:54,956 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 64, 'skip_rate': 0.046875, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 61, 'cache_hits': 37, 'similarity_calculations': 493, 'cache_hit_rate': 0.07505070993914807, 'cache_size': 456}}
2025-06-26 20:49:54,959 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 20:49:54,960 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:49:54,960 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:49:54,960 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:49:54,960 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 165675.0
2025-06-26 20:49:55,464 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:49:55,464 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:49:55,464 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:49:55,466 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:49:55,466 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 97, 31, 53, 75, 38, 60, 82, 36, 58, 84, 40, 62], 'cur_cost': 172783.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 99, 22, 55, 68, 21, 54, 95, 0, 51, 52, 97, 79, 50, 49, 35, 96, 48, 46, 91, 98, 47, 93, 43, 92, 94], 'cur_cost': 179636.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 2, 24, 46, 68, 80, 3, 14, 36, 58, 70, 4, 25, 47, 69, 81, 5, 15, 37, 59, 71, 6, 26, 48, 60, 82, 7, 16, 38, 50, 72, 8, 27, 49, 61, 83, 9, 17, 39, 51, 73, 10, 28, 40, 62, 84, 11, 18, 30, 52, 74, 19, 29, 41, 63, 85, 20, 21, 31, 53, 75, 22, 32, 42, 64, 86, 33, 43, 54, 76, 87, 44, 55, 65, 77, 88, 66, 0, 99, 98, 97, 96, 95, 94, 93, 92, 91], 'cur_cost': 178692.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 27, 26, 82, 0, 31, 84, 83, 33, 85, 29, 28, 86, 32], 'cur_cost': 187542.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 80, 2, 14, 36, 58, 70, 92, 13, 35, 57, 79, 91, 3, 24, 46, 68, 81, 5, 16, 38, 50, 72, 94, 15, 37, 59, 71, 93, 6, 27, 49, 61, 83, 7, 28, 40, 62, 84, 17, 48, 75, 97, 19, 31, 53, 85, 8, 20, 42, 64, 86, 11, 33, 55, 77, 99, 22, 44, 66, 88, 0, 26, 41, 63, 95, 29, 51, 73], 'cur_cost': 170005.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 2, 24, 46, 68, 80, 3, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 70, 92, 14, 36, 58, 71, 93, 15, 37, 49, 72, 94, 16, 28, 50, 63, 85, 7, 19, 41, 53, 75, 97, 27, 48, 61, 83, 5, 26, 40, 62, 84, 6, 31, 4, 82], 'cur_cost': 163098.0}, {'tour': array([67,  1, 24, 40, 37, 43, 61, 75, 31, 76, 16,  2, 48, 68, 71, 74, 72,
       55, 30, 34, 36, 42, 64, 98,  6, 69,  8, 15, 44, 81, 96, 25, 12,  5,
       29, 65, 66, 88,  3, 59, 91, 10, 89,  7, 85, 54, 22, 45, 57,  0, 46,
       60, 62, 50, 52, 27, 77, 13,  9, 87, 97,  4, 73, 83, 56, 63, 38, 26,
       90, 86, 58, 92, 84, 28, 20, 17, 39, 93, 51, 79, 41, 94, 82, 32, 18,
       19, 99, 14, 80, 95, 78, 70, 53, 35, 23, 47, 33, 21, 49, 11]), 'cur_cost': 183450.0}, {'tour': array([60, 20, 17, 80, 19, 89, 15, 69, 37, 22, 36, 75, 26, 61, 92, 78, 71,
       98, 77, 45, 27, 44, 34, 90, 97, 63, 42, 91, 59, 14,  2, 56,  0, 54,
       62, 55, 40, 13, 18, 76, 21, 88, 11, 29,  3, 87, 46, 64,  9, 30,  5,
       94, 52, 33, 72,  4, 28, 86, 41, 67, 12, 48, 50, 79, 83, 38, 49, 95,
       93,  1, 35, 43, 81, 10, 74, 47, 53, 84, 66, 96, 99, 25, 39, 51, 65,
       85, 68,  6,  8, 70, 58, 57, 31, 73, 82, 16,  7, 23, 32, 24]), 'cur_cost': 165675.0}, {'tour': array([44, 93, 32, 71, 10, 49, 86, 62, 58, 36, 85, 48, 98, 56, 69, 61, 11,
       25, 53, 54, 37, 92, 60, 22, 18, 24,  4,  2, 67, 29,  6, 57, 76, 81,
        5, 30, 91, 51, 16, 17, 97, 40, 59, 33, 46, 83, 87, 42, 72, 66, 94,
       43,  1, 78, 84, 19, 75,  7, 14,  9, 96, 90, 82, 13, 28, 64, 77, 88,
       15, 70, 55,  0, 20, 73,  8,  3, 74, 31, 26, 47, 23, 99, 63, 41, 79,
       21, 65, 12, 95, 50, 38, 52, 27, 68, 45, 35, 39, 89, 80, 34]), 'cur_cost': 168662.0}, {'tour': array([23,  9, 59, 74, 30, 88, 84, 71, 69, 80,  0, 41, 33, 73, 75, 39, 55,
       24, 91, 94, 78, 21, 45, 97, 36, 62, 99, 83,  3, 95, 90, 43, 49, 82,
        4, 51, 26, 61, 56, 66, 48, 58, 18,  2, 87,  5, 70, 16, 67,  1, 38,
       76, 17, 57, 42,  6, 34, 15, 63, 31, 64, 40, 96, 35, 52,  7, 25, 93,
       44, 37, 47, 19, 60, 68, 12, 98, 27, 89, 29, 11, 81, 77, 54, 65, 32,
       53, 46, 86, 72, 13, 50, 85, 10, 14, 92, 20, 22, 28,  8, 79]), 'cur_cost': 165835.0}]
2025-06-26 20:49:55,469 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:49:55,469 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 65, 'skip_rate': 0.046153846153846156, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 62, 'cache_hits': 37, 'similarity_calculations': 503, 'cache_hit_rate': 0.073558648111332, 'cache_size': 466}}
2025-06-26 20:49:55,469 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:49:55,470 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:49:55,470 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:49:55,470 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:49:55,471 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 172341.0
2025-06-26 20:49:55,975 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:49:55,975 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:49:55,976 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:49:55,979 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:49:55,979 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 97, 31, 53, 75, 38, 60, 82, 36, 58, 84, 40, 62], 'cur_cost': 172783.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 99, 22, 55, 68, 21, 54, 95, 0, 51, 52, 97, 79, 50, 49, 35, 96, 48, 46, 91, 98, 47, 93, 43, 92, 94], 'cur_cost': 179636.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 2, 24, 46, 68, 80, 3, 14, 36, 58, 70, 4, 25, 47, 69, 81, 5, 15, 37, 59, 71, 6, 26, 48, 60, 82, 7, 16, 38, 50, 72, 8, 27, 49, 61, 83, 9, 17, 39, 51, 73, 10, 28, 40, 62, 84, 11, 18, 30, 52, 74, 19, 29, 41, 63, 85, 20, 21, 31, 53, 75, 22, 32, 42, 64, 86, 33, 43, 54, 76, 87, 44, 55, 65, 77, 88, 66, 0, 99, 98, 97, 96, 95, 94, 93, 92, 91], 'cur_cost': 178692.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 27, 26, 82, 0, 31, 84, 83, 33, 85, 29, 28, 86, 32], 'cur_cost': 187542.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 80, 2, 14, 36, 58, 70, 92, 13, 35, 57, 79, 91, 3, 24, 46, 68, 81, 5, 16, 38, 50, 72, 94, 15, 37, 59, 71, 93, 6, 27, 49, 61, 83, 7, 28, 40, 62, 84, 17, 48, 75, 97, 19, 31, 53, 85, 8, 20, 42, 64, 86, 11, 33, 55, 77, 99, 22, 44, 66, 88, 0, 26, 41, 63, 95, 29, 51, 73], 'cur_cost': 170005.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 2, 24, 46, 68, 80, 3, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 70, 92, 14, 36, 58, 71, 93, 15, 37, 49, 72, 94, 16, 28, 50, 63, 85, 7, 19, 41, 53, 75, 97, 27, 48, 61, 83, 5, 26, 40, 62, 84, 6, 31, 4, 82], 'cur_cost': 163098.0}, {'tour': array([67,  1, 24, 40, 37, 43, 61, 75, 31, 76, 16,  2, 48, 68, 71, 74, 72,
       55, 30, 34, 36, 42, 64, 98,  6, 69,  8, 15, 44, 81, 96, 25, 12,  5,
       29, 65, 66, 88,  3, 59, 91, 10, 89,  7, 85, 54, 22, 45, 57,  0, 46,
       60, 62, 50, 52, 27, 77, 13,  9, 87, 97,  4, 73, 83, 56, 63, 38, 26,
       90, 86, 58, 92, 84, 28, 20, 17, 39, 93, 51, 79, 41, 94, 82, 32, 18,
       19, 99, 14, 80, 95, 78, 70, 53, 35, 23, 47, 33, 21, 49, 11]), 'cur_cost': 183450.0}, {'tour': array([60, 20, 17, 80, 19, 89, 15, 69, 37, 22, 36, 75, 26, 61, 92, 78, 71,
       98, 77, 45, 27, 44, 34, 90, 97, 63, 42, 91, 59, 14,  2, 56,  0, 54,
       62, 55, 40, 13, 18, 76, 21, 88, 11, 29,  3, 87, 46, 64,  9, 30,  5,
       94, 52, 33, 72,  4, 28, 86, 41, 67, 12, 48, 50, 79, 83, 38, 49, 95,
       93,  1, 35, 43, 81, 10, 74, 47, 53, 84, 66, 96, 99, 25, 39, 51, 65,
       85, 68,  6,  8, 70, 58, 57, 31, 73, 82, 16,  7, 23, 32, 24]), 'cur_cost': 165675.0}, {'tour': array([82,  6, 30, 16, 50, 46, 38, 37,  5, 84, 12, 68,  4, 88, 19, 27, 29,
       36, 23, 92, 10, 65, 87, 76, 63, 67, 96, 70, 14, 86, 62, 35, 32, 42,
       56, 41, 43, 13,  8, 89, 61, 99, 72, 22, 77, 11, 71, 81, 57, 69, 15,
       17, 64,  3, 93,  2, 20, 28, 39, 26, 79, 49, 80,  7, 74,  1, 31, 97,
       55, 44, 24, 78, 47, 73, 54, 25, 33, 51, 75, 21, 59,  9, 48, 95, 85,
       60, 45, 18, 66, 53, 40, 91,  0, 90, 98, 58, 52, 34, 94, 83]), 'cur_cost': 172341.0}, {'tour': array([23,  9, 59, 74, 30, 88, 84, 71, 69, 80,  0, 41, 33, 73, 75, 39, 55,
       24, 91, 94, 78, 21, 45, 97, 36, 62, 99, 83,  3, 95, 90, 43, 49, 82,
        4, 51, 26, 61, 56, 66, 48, 58, 18,  2, 87,  5, 70, 16, 67,  1, 38,
       76, 17, 57, 42,  6, 34, 15, 63, 31, 64, 40, 96, 35, 52,  7, 25, 93,
       44, 37, 47, 19, 60, 68, 12, 98, 27, 89, 29, 11, 81, 77, 54, 65, 32,
       53, 46, 86, 72, 13, 50, 85, 10, 14, 92, 20, 22, 28,  8, 79]), 'cur_cost': 165835.0}]
2025-06-26 20:49:55,981 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:49:55,982 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 66, 'skip_rate': 0.045454545454545456, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 63, 'cache_hits': 37, 'similarity_calculations': 514, 'cache_hit_rate': 0.07198443579766536, 'cache_size': 477}}
2025-06-26 20:49:55,982 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:49:55,982 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:49:55,982 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:49:55,983 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:49:55,983 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 164316.0
2025-06-26 20:49:56,487 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:49:56,488 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:49:56,488 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:49:56,494 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:49:56,497 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 97, 31, 53, 75, 38, 60, 82, 36, 58, 84, 40, 62], 'cur_cost': 172783.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 99, 22, 55, 68, 21, 54, 95, 0, 51, 52, 97, 79, 50, 49, 35, 96, 48, 46, 91, 98, 47, 93, 43, 92, 94], 'cur_cost': 179636.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 2, 24, 46, 68, 80, 3, 14, 36, 58, 70, 4, 25, 47, 69, 81, 5, 15, 37, 59, 71, 6, 26, 48, 60, 82, 7, 16, 38, 50, 72, 8, 27, 49, 61, 83, 9, 17, 39, 51, 73, 10, 28, 40, 62, 84, 11, 18, 30, 52, 74, 19, 29, 41, 63, 85, 20, 21, 31, 53, 75, 22, 32, 42, 64, 86, 33, 43, 54, 76, 87, 44, 55, 65, 77, 88, 66, 0, 99, 98, 97, 96, 95, 94, 93, 92, 91], 'cur_cost': 178692.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 27, 26, 82, 0, 31, 84, 83, 33, 85, 29, 28, 86, 32], 'cur_cost': 187542.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 80, 2, 14, 36, 58, 70, 92, 13, 35, 57, 79, 91, 3, 24, 46, 68, 81, 5, 16, 38, 50, 72, 94, 15, 37, 59, 71, 93, 6, 27, 49, 61, 83, 7, 28, 40, 62, 84, 17, 48, 75, 97, 19, 31, 53, 85, 8, 20, 42, 64, 86, 11, 33, 55, 77, 99, 22, 44, 66, 88, 0, 26, 41, 63, 95, 29, 51, 73], 'cur_cost': 170005.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 2, 24, 46, 68, 80, 3, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 70, 92, 14, 36, 58, 71, 93, 15, 37, 49, 72, 94, 16, 28, 50, 63, 85, 7, 19, 41, 53, 75, 97, 27, 48, 61, 83, 5, 26, 40, 62, 84, 6, 31, 4, 82], 'cur_cost': 163098.0}, {'tour': array([67,  1, 24, 40, 37, 43, 61, 75, 31, 76, 16,  2, 48, 68, 71, 74, 72,
       55, 30, 34, 36, 42, 64, 98,  6, 69,  8, 15, 44, 81, 96, 25, 12,  5,
       29, 65, 66, 88,  3, 59, 91, 10, 89,  7, 85, 54, 22, 45, 57,  0, 46,
       60, 62, 50, 52, 27, 77, 13,  9, 87, 97,  4, 73, 83, 56, 63, 38, 26,
       90, 86, 58, 92, 84, 28, 20, 17, 39, 93, 51, 79, 41, 94, 82, 32, 18,
       19, 99, 14, 80, 95, 78, 70, 53, 35, 23, 47, 33, 21, 49, 11]), 'cur_cost': 183450.0}, {'tour': array([60, 20, 17, 80, 19, 89, 15, 69, 37, 22, 36, 75, 26, 61, 92, 78, 71,
       98, 77, 45, 27, 44, 34, 90, 97, 63, 42, 91, 59, 14,  2, 56,  0, 54,
       62, 55, 40, 13, 18, 76, 21, 88, 11, 29,  3, 87, 46, 64,  9, 30,  5,
       94, 52, 33, 72,  4, 28, 86, 41, 67, 12, 48, 50, 79, 83, 38, 49, 95,
       93,  1, 35, 43, 81, 10, 74, 47, 53, 84, 66, 96, 99, 25, 39, 51, 65,
       85, 68,  6,  8, 70, 58, 57, 31, 73, 82, 16,  7, 23, 32, 24]), 'cur_cost': 165675.0}, {'tour': array([82,  6, 30, 16, 50, 46, 38, 37,  5, 84, 12, 68,  4, 88, 19, 27, 29,
       36, 23, 92, 10, 65, 87, 76, 63, 67, 96, 70, 14, 86, 62, 35, 32, 42,
       56, 41, 43, 13,  8, 89, 61, 99, 72, 22, 77, 11, 71, 81, 57, 69, 15,
       17, 64,  3, 93,  2, 20, 28, 39, 26, 79, 49, 80,  7, 74,  1, 31, 97,
       55, 44, 24, 78, 47, 73, 54, 25, 33, 51, 75, 21, 59,  9, 48, 95, 85,
       60, 45, 18, 66, 53, 40, 91,  0, 90, 98, 58, 52, 34, 94, 83]), 'cur_cost': 172341.0}, {'tour': array([29, 84, 25, 77,  4, 38, 53, 76, 67, 49, 40, 98,  3, 83, 21, 59, 91,
        5, 74, 36, 73, 47, 14, 58, 15, 16, 92, 28, 60, 30, 66, 48, 33, 13,
       55, 62, 45, 22, 32, 23,  9, 42, 46, 10, 97, 24, 44, 19, 11, 81, 90,
       93, 71, 79, 27, 75, 34,  7, 18, 72, 50, 85, 70,  0, 78, 12, 95, 56,
       86, 82, 41, 68, 37, 35,  8, 43, 57,  2, 64, 31, 61, 99, 65, 80, 89,
       63,  1,  6, 87, 39, 20, 69, 17, 26, 54, 94, 52, 51, 88, 96]), 'cur_cost': 164316.0}]
2025-06-26 20:49:56,500 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 20:49:56,501 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 67, 'skip_rate': 0.04477611940298507, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 64, 'cache_hits': 37, 'similarity_calculations': 526, 'cache_hit_rate': 0.07034220532319392, 'cache_size': 489}}
2025-06-26 20:49:56,501 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:49:56,501 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 97, 31, 53, 75, 38, 60, 82, 36, 58, 84, 40, 62], 'cur_cost': 172783.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 3, 26, 59, 82, 5, 28, 61, 84, 7, 30, 63, 86, 9, 32, 65, 88, 11, 44, 77, 20, 53, 76, 19, 42, 75, 18, 41, 74, 17, 40, 73, 16, 39, 72, 15, 38, 71, 14, 37, 70, 13, 36, 69, 2, 25, 58, 81, 4, 27, 60, 83, 6, 29, 62, 85, 8, 31, 64, 87, 10, 33, 66, 99, 22, 55, 68, 21, 54, 95, 0, 51, 52, 97, 79, 50, 49, 35, 96, 48, 46, 91, 98, 47, 93, 43, 92, 94], 'cur_cost': 179636.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 2, 24, 46, 68, 80, 3, 14, 36, 58, 70, 4, 25, 47, 69, 81, 5, 15, 37, 59, 71, 6, 26, 48, 60, 82, 7, 16, 38, 50, 72, 8, 27, 49, 61, 83, 9, 17, 39, 51, 73, 10, 28, 40, 62, 84, 11, 18, 30, 52, 74, 19, 29, 41, 63, 85, 20, 21, 31, 53, 75, 22, 32, 42, 64, 86, 33, 43, 54, 76, 87, 44, 55, 65, 77, 88, 66, 0, 99, 98, 97, 96, 95, 94, 93, 92, 91], 'cur_cost': 178692.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 27, 26, 82, 0, 31, 84, 83, 33, 85, 29, 28, 86, 32], 'cur_cost': 187542.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 82, 4, 25, 47, 69, 80, 2, 14, 36, 58, 70, 92, 13, 35, 57, 79, 91, 3, 24, 46, 68, 81, 5, 16, 38, 50, 72, 94, 15, 37, 59, 71, 93, 6, 27, 49, 61, 83, 7, 28, 40, 62, 84, 17, 48, 75, 97, 19, 31, 53, 85, 8, 20, 42, 64, 86, 11, 33, 55, 77, 99, 22, 44, 66, 88, 0, 26, 41, 63, 95, 29, 51, 73], 'cur_cost': 170005.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 2, 24, 46, 68, 80, 3, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 70, 92, 14, 36, 58, 71, 93, 15, 37, 49, 72, 94, 16, 28, 50, 63, 85, 7, 19, 41, 53, 75, 97, 27, 48, 61, 83, 5, 26, 40, 62, 84, 6, 31, 4, 82], 'cur_cost': 163098.0}}]
2025-06-26 20:49:56,502 - __main__ - INFO - 进化阶段完成
2025-06-26 20:49:56,502 - __main__ - INFO - 开始评估阶段
2025-06-26 20:49:56,502 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:49:56,503 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:49:56,503 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 164593.0 → 163098.0 (Δ 1495.0)
- Mean cost: 172862.7 → 173753.8
- Diversity: 0.620 → 0.751 (Δ 0.1303)

#### Elite metrics
- Elite count: 3 → 3
- Best elite cost: 21282 → 21282 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 66.7% , avg Δ -868.33
- Exploit: 4 inds, success 25.0% , avg Δ -925.25

#### Other indicators
- No-change individuals: 0
- Regression individuals: 5
- Historical trends: Last 3 iterations: costs [173147.0, 157815.0, 164593.0], diversity [0.39333333333333326, 0.8900000000000001, 0.8533333333333332]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:49:56,503 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:50:09,308 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": -868.33,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -925.25,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore_effective_but_exploit_underperforming",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the number of exploit attempts or refining the exploit strategy to improve success rates."
  }
}
```
2025-06-26 20:50:09,383 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:50:09,383 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": -868.33,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -925.25,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore_effective_but_exploit_underperforming",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the number of exploit attempts or refining the exploit strategy to improve success rates."
  }
}
```
2025-06-26 20:50:09,383 - __main__ - INFO - 评估阶段完成
2025-06-26 20:50:09,383 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": -868.33,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_delta": -925.25,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore_effective_but_exploit_underperforming",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the number of exploit attempts or refining the exploit strategy to improve success rates."
  }
}
```
2025-06-26 20:50:09,383 - __main__ - INFO - 当前最佳适应度: 163098.0
2025-06-26 20:50:09,383 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\kroA100_route_3.pkl
2025-06-26 20:50:09,383 - __main__ - INFO - kroA100 开始进化第 5 代
2025-06-26 20:50:09,383 - __main__ - INFO - 开始分析阶段
2025-06-26 20:50:09,383 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:50:09,426 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 163098.0, 'max': 187542.0, 'mean': 173753.8, 'std': 7924.606990886046}, 'diversity': 0.9137777777777777, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:50:09,430 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 163098.0, 'max': 187542.0, 'mean': 173753.8, 'std': 7924.606990886046}, 'diversity_level': 0.9137777777777777, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[1380, 939], [2848, 96], [3510, 1671], [457, 334], [3888, 666], [984, 965], [2721, 1482], [1286, 525], [2716, 1432], [738, 1325], [1251, 1832], [2728, 1698], [3815, 169], [3683, 1533], [1247, 1945], [123, 862], [1234, 1946], [252, 1240], [611, 673], [2576, 1676], [928, 1700], [53, 857], [1807, 1711], [274, 1420], [2574, 946], [178, 24], [2678, 1825], [1795, 962], [3384, 1498], [3520, 1079], [1256, 61], [1424, 1728], [3913, 192], [3085, 1528], [2573, 1969], [463, 1670], [3875, 598], [298, 1513], [3479, 821], [2542, 236], [3955, 1743], [1323, 280], [3447, 1830], [2936, 337], [1621, 1830], [3373, 1646], [1393, 1368], [3874, 1318], [938, 955], [3022, 474], [2482, 1183], [3854, 923], [376, 825], [2519, 135], [2945, 1622], [953, 268], [2628, 1479], [2097, 981], [890, 1846], [2139, 1806], [2421, 1007], [2290, 1810], [1115, 1052], [2588, 302], [327, 265], [241, 341], [1917, 687], [2991, 792], [2573, 599], [19, 674], [3911, 1673], [872, 1559], [2863, 558], [929, 1766], [839, 620], [3893, 102], [2178, 1619], [3822, 899], [378, 1048], [1178, 100], [2599, 901], [3416, 143], [2961, 1605], [611, 1384], [3113, 885], [2597, 1830], [2586, 1286], [161, 906], [1429, 134], [742, 1025], [1625, 1651], [1187, 706], [1787, 1009], [22, 987], [3640, 43], [3756, 882], [776, 392], [1724, 1642], [198, 1810], [3950, 1558]], 'distance_matrix': array([[   0., 1693., 2252., ...,  783., 1468., 2643.],
       [1693.,    0., 1708., ..., 1911., 3156., 1831.],
       [2252., 1708.,    0., ..., 1786., 3315.,  454.],
       ...,
       [ 783., 1911., 1786., ...,    0., 1535., 2228.],
       [1468., 3156., 3315., ..., 1535.,    0., 3760.],
       [2643., 1831.,  454., ..., 2228., 3760.,    0.]])}
2025-06-26 20:50:09,430 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:50:09,431 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:50:09,431 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:50:09,436 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:50:09,436 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:50:09,439 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:50:09,439 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:50:09,439 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:50:09,440 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:50:09,441 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:50:09,441 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {'(18, 74)': 1.0, '(84, 67)': 1.0, '(67, 72)': 1.0}, 'common_edge_ratio': 0.03}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 141816.0, 'avg_gap': 152309.13333333333}, 'structure_gap': {'unique_elite_edges': 197, 'unique_pop_edges': 767, 'common_edges': 12}}, 'elite_diversity': {'diversity_score': 0.12}}
2025-06-26 20:50:09,441 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:50:09,442 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:50:09,444 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:50:09,444 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 163098.0, mean 173753.8, max 187542.0, std 7924.606990886046
- diversity: 0.9137777777777777
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [157815.0, 164593.0, 163098.0] (Δ 747.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [19.0, 24.0, 3955.0, 1969.0]
- centroid/spread: [2011.37, 1064.48] | rms 1365.283
- density_grid(3x3): [[11, 5, 11], [14, 10, 8], [13, 13, 15]]
- edge_len_stats(min,q1,med,q3,max): [13.0, 985.0, 1586.0, 2370.5, 4150.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [26, 4, 5, 21, 17, 6, 8, 12]
- nn_median_dist: 140.036

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:50:09,444 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:50:34,442 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant quartile spreads)",
    "modality": "Potentially multimodal (direction histogram shows varied edge orientations, no single dominant pattern)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 suggests some solutions may be misled by long edges)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (diversity 0.91, density_grid shows nodes distributed across all cells)",
    "convergence": "Early exploration phase (convergence 0.0, recent best costs still improving by ~747.5 per iteration)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges in max quartile >2370.5 length)",
    "Low-density grid cells (e.g., [1,1] with only 5 nodes)"
  ],
  "opportunity_regions": [
    "High-density cells (e.g., [2,2] with 15 nodes, [0,0] and [0,2] with 11 nodes)",
    "Centroid-proximal regions (near [2011.37, 1064.48] with RMS spread 1365.283)"
  ],
  "evolution_phase": "Mid-exploration (iter 4/5, improving solutions but no stagnation)",
  "evolution_direction": [
    "Prioritize 2-opt near long edges to reduce tour length",
    "Enforce diversity preservation in low-density grid cells",
    "Focus crossover on high-density cell subpaths",
    "Introduce directed mutation toward centroid-proximal nodes"
  ]
}
```
2025-06-26 20:50:34,443 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:50:34,443 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant quartile spreads)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations, no single dominant pattern)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some solutions may be misled by long edges)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.91, density_grid shows nodes distributed across all cells)', 'convergence': 'Early exploration phase (convergence 0.0, recent best costs still improving by ~747.5 per iteration)'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile >2370.5 length)', 'Low-density grid cells (e.g., [1,1] with only 5 nodes)'], 'opportunity_regions': ['High-density cells (e.g., [2,2] with 15 nodes, [0,0] and [0,2] with 11 nodes)', 'Centroid-proximal regions (near [2011.37, 1064.48] with RMS spread 1365.283)'], 'evolution_phase': 'Mid-exploration (iter 4/5, improving solutions but no stagnation)', 'evolution_direction': ['Prioritize 2-opt near long edges to reduce tour length', 'Enforce diversity preservation in low-density grid cells', 'Focus crossover on high-density cell subpaths', 'Introduce directed mutation toward centroid-proximal nodes']}
2025-06-26 20:50:34,444 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:50:34,444 - __main__ - INFO - 分析阶段完成
2025-06-26 20:50:34,444 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant quartile spreads)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations, no single dominant pattern)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some solutions may be misled by long edges)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.91, density_grid shows nodes distributed across all cells)', 'convergence': 'Early exploration phase (convergence 0.0, recent best costs still improving by ~747.5 per iteration)'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile >2370.5 length)', 'Low-density grid cells (e.g., [1,1] with only 5 nodes)'], 'opportunity_regions': ['High-density cells (e.g., [2,2] with 15 nodes, [0,0] and [0,2] with 11 nodes)', 'Centroid-proximal regions (near [2011.37, 1064.48] with RMS spread 1365.283)'], 'evolution_phase': 'Mid-exploration (iter 4/5, improving solutions but no stagnation)', 'evolution_direction': ['Prioritize 2-opt near long edges to reduce tour length', 'Enforce diversity preservation in low-density grid cells', 'Focus crossover on high-density cell subpaths', 'Introduce directed mutation toward centroid-proximal nodes']}
2025-06-26 20:50:34,444 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:50:34,444 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:50:34,445 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 163098.0
  • mean_cost: 173753.8
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 13.0 to 4150.0, with significant quartile spreads), modality Potentially multimodal (direction histogram shows varied edge orientations, no single dominant pattern), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 suggests some solutions may be misled by long edges)
- Evolution phase: Mid-exploration (iter 4/5, improving solutions but no stagnation)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Low-density grid cel
- Opportunity regions (# 2): High-density cells (, Centroid-proximal re
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:50:34,445 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:50:34,445 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:50:45,367 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and mid-exploration phase suggest prioritizing exploration (70%). Top 60% individuals explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 20:50:45,367 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:50:45,369 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:50:45,369 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and mid-exploration phase suggest prioritizing exploration (70%). Top 60% individuals explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 20:50:45,369 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:50:45,369 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:50:45,369 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and mid-exploration phase suggest prioritizing exploration (70%). Top 60% individuals explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 20:50:45,371 - __main__ - INFO - 开始进化阶段
2025-06-26 20:50:45,371 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:50:45,371 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:50:45,371 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 172783.0 (rank 50.0%)
- Population diversity: 0.7505050505050505
- Evolution phase: Mid-exploration (iter 4/5, improving solutions but no stagnation)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid-proxim
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:50:45,372 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:50:45,372 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:51:06,938 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99],
  "targeted_regions": "High-density ce, Centroid-proxim, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of dense/sparse regions with spiral pattern"
}
```
2025-06-26 20:51:06,938 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:51:06,940 - ExplorationExpert - INFO - 探索路径生成完成，成本: 170722.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99]
2025-06-26 20:51:06,940 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}
2025-06-26 20:51:06,941 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:51:06,941 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:51:06,942 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 179636.0 (rank 70.0%)
- Population diversity: 0.7404040404040404
- Evolution phase: Mid-exploration (iter 4/5, improving solutions but no stagnation)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid-proxim
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:51:06,942 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:51:06,942 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:51:28,104 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 82, 31, 53, 75, 97, 38, 60, 84, 36, 58, 62],
  "targeted_regions": "High-density ce, Centroid-proxim, adjacent low-density",
  "strategy_comment": "Balanced exploration of dense/sparse cells with centroid focus"
}
```
2025-06-26 20:51:28,104 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:51:28,104 - ExplorationExpert - INFO - 探索路径生成完成，成本: 174197.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 82, 31, 53, 75, 97, 38, 60, 84, 36, 58, 62, 40]
2025-06-26 20:51:28,104 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 82, 31, 53, 75, 97, 38, 60, 84, 36, 58, 62, 40], 'cur_cost': 174197.0}
2025-06-26 20:51:28,104 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:51:28,112 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:51:28,113 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 178692.0 (rank 70.0%)
- Population diversity: 0.6898989898989899
- Evolution phase: Mid-exploration (iter 4/5, improving solutions but no stagnation)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid-proxim
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:51:28,113 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:51:28,113 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:51:49,499 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 42, 64, 86, 8, 20, 32, 54, 76, 98, 11, 33, 55, 77, 99, 10, 22, 44, 66, 88, 0, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 41, 63, 85, 7, 29, 52, 74, 96, 18, 40, 62, 84, 6, 30],
  "targeted_regions": "High-density ce, Centroid-proxim, adjacent low-density",
  "strategy_comment": "Balanced exploration of dense/sparse regions with centroid focus"
}
```
2025-06-26 20:51:49,499 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:51:49,501 - ExplorationExpert - INFO - 探索路径生成完成，成本: 174988.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 42, 64, 86, 8, 20, 32, 54, 76, 98, 11, 33, 55, 77, 99, 10, 22, 44, 66, 88, 0, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 41, 63, 85, 7, 29, 52, 74, 96, 18, 40, 62, 84, 6, 30]
2025-06-26 20:51:49,501 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 42, 64, 86, 8, 20, 32, 54, 76, 98, 11, 33, 55, 77, 99, 10, 22, 44, 66, 88, 0, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 41, 63, 85, 7, 29, 52, 74, 96, 18, 40, 62, 84, 6, 30], 'cur_cost': 174988.0}
2025-06-26 20:51:49,501 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:51:49,501 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:51:49,503 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 187542.0 (rank 90.0%)
- Population diversity: 0.6666666666666666
- Evolution phase: Mid-exploration (iter 4/5, improving solutions but no stagnation)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid-proxim
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:51:49,503 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:51:49,503 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:52:12,228 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 3, 24, 46, 68, 80, 2, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 82, 4, 26, 48, 70, 92, 14, 36, 58, 71, 93, 15, 37, 50, 72, 94, 16, 28, 41, 63, 85, 7, 19, 40, 62, 84, 6, 27, 49, 61, 83, 5, 31, 53, 75, 97],
  "targeted_regions": "High-density ce, Centroid-proxim, adjacent low-density",
  "strategy_comment": "Balanced exploration of dense/sparse regions, avoiding long edges"
}
```
2025-06-26 20:52:12,228 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:52:12,229 - ExplorationExpert - INFO - 探索路径生成完成，成本: 170822.0, 路径: [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 3, 24, 46, 68, 80, 2, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 82, 4, 26, 48, 70, 92, 14, 36, 58, 71, 93, 15, 37, 50, 72, 94, 16, 28, 41, 63, 85, 7, 19, 40, 62, 84, 6, 27, 49, 61, 83, 5, 31, 53, 75, 97]
2025-06-26 20:52:12,230 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 3, 24, 46, 68, 80, 2, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 82, 4, 26, 48, 70, 92, 14, 36, 58, 71, 93, 15, 37, 50, 72, 94, 16, 28, 41, 63, 85, 7, 19, 40, 62, 84, 6, 27, 49, 61, 83, 5, 31, 53, 75, 97], 'cur_cost': 170822.0}
2025-06-26 20:52:12,230 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:52:12,230 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:52:12,230 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 170005.0 (rank 30.0%)
- Population diversity: 0.5898989898989899
- Evolution phase: Mid-exploration (iter 4/5, improving solutions but no stagnation)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid-proxim
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:52:12,230 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:52:12,230 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:52:33,948 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 77, 0, 33, 66, 99, 22, 55, 88, 11, 44, 77, 0, 33, 66, 99, 22, 55, 88, 11, 44, 77, 0, 33, 66, 99],
  "targeted_regions": "High-density ce, Centroid-proxim, adjacent low-density",
  "strategy_comment": "Balanced exploration of opportunity regions with sparse adjacency"
}
```
2025-06-26 20:52:33,949 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:52:33,949 - ExplorationExpert - INFO - 探索路径生成完成，成本: 193732.0, 路径: [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 84, 31, 32, 25, 59, 26, 27, 87, 86, 83, 30, 82, 81, 29, 85, 28, 58]
2025-06-26 20:52:33,950 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 84, 31, 32, 25, 59, 26, 27, 87, 86, 83, 30, 82, 81, 29, 85, 28, 58], 'cur_cost': 193732.0}
2025-06-26 20:52:33,950 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:52:33,950 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:52:33,952 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 163098.0 (rank 0.0%)
- Population diversity: 0.6515151515151515
- Evolution phase: Mid-exploration (iter 4/5, improving solutions but no stagnation)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid-proxim
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 99]  
• Path length == 100 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:52:33,952 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:52:33,952 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:52:56,003 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 73, 16, 59, 92, 25, 58, 81, 14, 47, 80, 13, 46, 79, 12, 45, 78, 23],
  "targeted_regions": "High-density ce, Centroid-proxim, adjacent low-density",
  "strategy_comment": "Balanced exploration of opportunity regions with spiral-like diversity"
}
```
2025-06-26 20:52:56,003 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:52:56,003 - ExplorationExpert - INFO - 探索路径生成完成，成本: 187419.0, 路径: [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 86, 85, 26, 33, 28, 84, 0, 29, 27, 83, 31, 32, 82]
2025-06-26 20:52:56,003 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 86, 85, 26, 33, 28, 84, 0, 29, 27, 83, 31, 32, 82], 'cur_cost': 187419.0}
2025-06-26 20:52:56,003 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 20:52:56,003 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:52:56,003 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:52:56,003 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 161350.0
2025-06-26 20:52:56,504 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:52:56,505 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:52:56,505 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:52:56,506 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:52:56,507 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 82, 31, 53, 75, 97, 38, 60, 84, 36, 58, 62, 40], 'cur_cost': 174197.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 42, 64, 86, 8, 20, 32, 54, 76, 98, 11, 33, 55, 77, 99, 10, 22, 44, 66, 88, 0, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 41, 63, 85, 7, 29, 52, 74, 96, 18, 40, 62, 84, 6, 30], 'cur_cost': 174988.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 3, 24, 46, 68, 80, 2, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 82, 4, 26, 48, 70, 92, 14, 36, 58, 71, 93, 15, 37, 50, 72, 94, 16, 28, 41, 63, 85, 7, 19, 40, 62, 84, 6, 27, 49, 61, 83, 5, 31, 53, 75, 97], 'cur_cost': 170822.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 84, 31, 32, 25, 59, 26, 27, 87, 86, 83, 30, 82, 81, 29, 85, 28, 58], 'cur_cost': 193732.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 86, 85, 26, 33, 28, 84, 0, 29, 27, 83, 31, 32, 82], 'cur_cost': 187419.0}, {'tour': array([49, 94, 42, 96, 89, 61, 74, 88, 51,  9,  3, 62, 41, 95, 33, 15, 93,
       54, 97, 32, 29,  1, 86, 81, 10, 71, 37, 66, 91, 12, 25, 76, 92, 52,
       24, 31, 90, 16,  2, 27, 19, 85, 82, 56, 46, 59, 34, 65, 30, 63, 98,
       14, 58, 21, 77, 28, 45, 43,  6,  5, 69, 60, 44,  0, 83,  4, 68, 17,
       70, 72, 80, 73, 57, 40, 26, 48, 13, 64, 87, 67, 55, 18, 84, 36, 35,
       75, 78, 39, 79, 22,  7, 11, 53, 23, 20,  8, 99, 47, 38, 50]), 'cur_cost': 161350.0}, {'tour': array([60, 20, 17, 80, 19, 89, 15, 69, 37, 22, 36, 75, 26, 61, 92, 78, 71,
       98, 77, 45, 27, 44, 34, 90, 97, 63, 42, 91, 59, 14,  2, 56,  0, 54,
       62, 55, 40, 13, 18, 76, 21, 88, 11, 29,  3, 87, 46, 64,  9, 30,  5,
       94, 52, 33, 72,  4, 28, 86, 41, 67, 12, 48, 50, 79, 83, 38, 49, 95,
       93,  1, 35, 43, 81, 10, 74, 47, 53, 84, 66, 96, 99, 25, 39, 51, 65,
       85, 68,  6,  8, 70, 58, 57, 31, 73, 82, 16,  7, 23, 32, 24]), 'cur_cost': 165675.0}, {'tour': array([82,  6, 30, 16, 50, 46, 38, 37,  5, 84, 12, 68,  4, 88, 19, 27, 29,
       36, 23, 92, 10, 65, 87, 76, 63, 67, 96, 70, 14, 86, 62, 35, 32, 42,
       56, 41, 43, 13,  8, 89, 61, 99, 72, 22, 77, 11, 71, 81, 57, 69, 15,
       17, 64,  3, 93,  2, 20, 28, 39, 26, 79, 49, 80,  7, 74,  1, 31, 97,
       55, 44, 24, 78, 47, 73, 54, 25, 33, 51, 75, 21, 59,  9, 48, 95, 85,
       60, 45, 18, 66, 53, 40, 91,  0, 90, 98, 58, 52, 34, 94, 83]), 'cur_cost': 172341.0}, {'tour': array([29, 84, 25, 77,  4, 38, 53, 76, 67, 49, 40, 98,  3, 83, 21, 59, 91,
        5, 74, 36, 73, 47, 14, 58, 15, 16, 92, 28, 60, 30, 66, 48, 33, 13,
       55, 62, 45, 22, 32, 23,  9, 42, 46, 10, 97, 24, 44, 19, 11, 81, 90,
       93, 71, 79, 27, 75, 34,  7, 18, 72, 50, 85, 70,  0, 78, 12, 95, 56,
       86, 82, 41, 68, 37, 35,  8, 43, 57,  2, 64, 31, 61, 99, 65, 80, 89,
       63,  1,  6, 87, 39, 20, 69, 17, 26, 54, 94, 52, 51, 88, 96]), 'cur_cost': 164316.0}]
2025-06-26 20:52:56,508 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:52:56,509 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 68, 'skip_rate': 0.04411764705882353, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 65, 'cache_hits': 37, 'similarity_calculations': 539, 'cache_hit_rate': 0.0686456400742115, 'cache_size': 502}}
2025-06-26 20:52:56,509 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 20:52:56,509 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:52:56,509 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:52:56,509 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:52:56,509 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 173153.0
2025-06-26 20:52:57,012 - root - WARNING - 无法找到足够的不重叠段 (找到 2/4)，使用退化策略
2025-06-26 20:52:57,013 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:52:57,014 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:52:57,014 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:52:57,016 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:52:57,016 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 82, 31, 53, 75, 97, 38, 60, 84, 36, 58, 62, 40], 'cur_cost': 174197.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 42, 64, 86, 8, 20, 32, 54, 76, 98, 11, 33, 55, 77, 99, 10, 22, 44, 66, 88, 0, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 41, 63, 85, 7, 29, 52, 74, 96, 18, 40, 62, 84, 6, 30], 'cur_cost': 174988.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 3, 24, 46, 68, 80, 2, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 82, 4, 26, 48, 70, 92, 14, 36, 58, 71, 93, 15, 37, 50, 72, 94, 16, 28, 41, 63, 85, 7, 19, 40, 62, 84, 6, 27, 49, 61, 83, 5, 31, 53, 75, 97], 'cur_cost': 170822.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 84, 31, 32, 25, 59, 26, 27, 87, 86, 83, 30, 82, 81, 29, 85, 28, 58], 'cur_cost': 193732.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 86, 85, 26, 33, 28, 84, 0, 29, 27, 83, 31, 32, 82], 'cur_cost': 187419.0}, {'tour': array([49, 94, 42, 96, 89, 61, 74, 88, 51,  9,  3, 62, 41, 95, 33, 15, 93,
       54, 97, 32, 29,  1, 86, 81, 10, 71, 37, 66, 91, 12, 25, 76, 92, 52,
       24, 31, 90, 16,  2, 27, 19, 85, 82, 56, 46, 59, 34, 65, 30, 63, 98,
       14, 58, 21, 77, 28, 45, 43,  6,  5, 69, 60, 44,  0, 83,  4, 68, 17,
       70, 72, 80, 73, 57, 40, 26, 48, 13, 64, 87, 67, 55, 18, 84, 36, 35,
       75, 78, 39, 79, 22,  7, 11, 53, 23, 20,  8, 99, 47, 38, 50]), 'cur_cost': 161350.0}, {'tour': array([60, 42, 44, 49, 52, 65, 81,  6,  7, 92, 17, 15, 63,  8, 86, 26, 27,
       67, 78, 18, 76, 99, 19, 54, 20, 22, 36, 74, 47, 32, 95, 98, 88, 25,
       75, 64, 59, 97, 93, 43, 24, 82, 31, 73, 66, 87, 41, 79, 56, 90, 14,
       53, 71, 84,  5, 70, 83, 38, 80, 48, 77,  0, 30, 69, 29, 72, 58, 94,
       68, 89,  3, 61, 16, 21, 10, 37, 28, 85, 33, 57, 13, 46, 50,  9, 39,
       35, 12, 51, 62,  2, 11, 45, 40,  4, 55,  1, 91, 96, 34, 23]), 'cur_cost': 173153.0}, {'tour': array([82,  6, 30, 16, 50, 46, 38, 37,  5, 84, 12, 68,  4, 88, 19, 27, 29,
       36, 23, 92, 10, 65, 87, 76, 63, 67, 96, 70, 14, 86, 62, 35, 32, 42,
       56, 41, 43, 13,  8, 89, 61, 99, 72, 22, 77, 11, 71, 81, 57, 69, 15,
       17, 64,  3, 93,  2, 20, 28, 39, 26, 79, 49, 80,  7, 74,  1, 31, 97,
       55, 44, 24, 78, 47, 73, 54, 25, 33, 51, 75, 21, 59,  9, 48, 95, 85,
       60, 45, 18, 66, 53, 40, 91,  0, 90, 98, 58, 52, 34, 94, 83]), 'cur_cost': 172341.0}, {'tour': array([29, 84, 25, 77,  4, 38, 53, 76, 67, 49, 40, 98,  3, 83, 21, 59, 91,
        5, 74, 36, 73, 47, 14, 58, 15, 16, 92, 28, 60, 30, 66, 48, 33, 13,
       55, 62, 45, 22, 32, 23,  9, 42, 46, 10, 97, 24, 44, 19, 11, 81, 90,
       93, 71, 79, 27, 75, 34,  7, 18, 72, 50, 85, 70,  0, 78, 12, 95, 56,
       86, 82, 41, 68, 37, 35,  8, 43, 57,  2, 64, 31, 61, 99, 65, 80, 89,
       63,  1,  6, 87, 39, 20, 69, 17, 26, 54, 94, 52, 51, 88, 96]), 'cur_cost': 164316.0}]
2025-06-26 20:52:57,017 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:52:57,018 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 69, 'skip_rate': 0.043478260869565216, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 66, 'cache_hits': 37, 'similarity_calculations': 553, 'cache_hit_rate': 0.06690777576853527, 'cache_size': 516}}
2025-06-26 20:52:57,018 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:52:57,018 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:52:57,018 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:52:57,018 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:52:57,019 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 168116.0
2025-06-26 20:52:57,522 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:52:57,522 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:52:57,523 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:52:57,525 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:52:57,525 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 82, 31, 53, 75, 97, 38, 60, 84, 36, 58, 62, 40], 'cur_cost': 174197.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 42, 64, 86, 8, 20, 32, 54, 76, 98, 11, 33, 55, 77, 99, 10, 22, 44, 66, 88, 0, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 41, 63, 85, 7, 29, 52, 74, 96, 18, 40, 62, 84, 6, 30], 'cur_cost': 174988.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 3, 24, 46, 68, 80, 2, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 82, 4, 26, 48, 70, 92, 14, 36, 58, 71, 93, 15, 37, 50, 72, 94, 16, 28, 41, 63, 85, 7, 19, 40, 62, 84, 6, 27, 49, 61, 83, 5, 31, 53, 75, 97], 'cur_cost': 170822.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 84, 31, 32, 25, 59, 26, 27, 87, 86, 83, 30, 82, 81, 29, 85, 28, 58], 'cur_cost': 193732.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 86, 85, 26, 33, 28, 84, 0, 29, 27, 83, 31, 32, 82], 'cur_cost': 187419.0}, {'tour': array([49, 94, 42, 96, 89, 61, 74, 88, 51,  9,  3, 62, 41, 95, 33, 15, 93,
       54, 97, 32, 29,  1, 86, 81, 10, 71, 37, 66, 91, 12, 25, 76, 92, 52,
       24, 31, 90, 16,  2, 27, 19, 85, 82, 56, 46, 59, 34, 65, 30, 63, 98,
       14, 58, 21, 77, 28, 45, 43,  6,  5, 69, 60, 44,  0, 83,  4, 68, 17,
       70, 72, 80, 73, 57, 40, 26, 48, 13, 64, 87, 67, 55, 18, 84, 36, 35,
       75, 78, 39, 79, 22,  7, 11, 53, 23, 20,  8, 99, 47, 38, 50]), 'cur_cost': 161350.0}, {'tour': array([60, 42, 44, 49, 52, 65, 81,  6,  7, 92, 17, 15, 63,  8, 86, 26, 27,
       67, 78, 18, 76, 99, 19, 54, 20, 22, 36, 74, 47, 32, 95, 98, 88, 25,
       75, 64, 59, 97, 93, 43, 24, 82, 31, 73, 66, 87, 41, 79, 56, 90, 14,
       53, 71, 84,  5, 70, 83, 38, 80, 48, 77,  0, 30, 69, 29, 72, 58, 94,
       68, 89,  3, 61, 16, 21, 10, 37, 28, 85, 33, 57, 13, 46, 50,  9, 39,
       35, 12, 51, 62,  2, 11, 45, 40,  4, 55,  1, 91, 96, 34, 23]), 'cur_cost': 173153.0}, {'tour': array([83,  7, 78, 46, 18, 44, 91, 86, 10, 38, 39, 15,  9, 65, 66, 76, 16,
       79, 49, 47, 74, 28, 56, 11, 37, 75, 80, 92,  1, 26,  6, 12, 13, 99,
       63, 48, 19, 97, 34, 29,  8, 60, 54, 70, 30, 67, 84, 90, 36, 89, 45,
       57,  3, 96, 31, 95, 62, 73,  4,  5, 93, 33, 77, 22, 21, 59, 81, 82,
        0, 68, 42, 27, 72, 43, 52, 40, 41, 14, 32, 88,  2, 64, 69, 23, 71,
       98, 25, 94, 51, 24, 17, 50, 55, 61, 87, 35, 20, 85, 58, 53]), 'cur_cost': 168116.0}, {'tour': array([29, 84, 25, 77,  4, 38, 53, 76, 67, 49, 40, 98,  3, 83, 21, 59, 91,
        5, 74, 36, 73, 47, 14, 58, 15, 16, 92, 28, 60, 30, 66, 48, 33, 13,
       55, 62, 45, 22, 32, 23,  9, 42, 46, 10, 97, 24, 44, 19, 11, 81, 90,
       93, 71, 79, 27, 75, 34,  7, 18, 72, 50, 85, 70,  0, 78, 12, 95, 56,
       86, 82, 41, 68, 37, 35,  8, 43, 57,  2, 64, 31, 61, 99, 65, 80, 89,
       63,  1,  6, 87, 39, 20, 69, 17, 26, 54, 94, 52, 51, 88, 96]), 'cur_cost': 164316.0}]
2025-06-26 20:52:57,527 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:52:57,527 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 70, 'skip_rate': 0.04285714285714286, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 67, 'cache_hits': 37, 'similarity_calculations': 568, 'cache_hit_rate': 0.06514084507042253, 'cache_size': 531}}
2025-06-26 20:52:57,527 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:52:57,527 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:52:57,527 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:52:57,527 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:52:57,528 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 158735.0
2025-06-26 20:52:58,031 - ExploitationExpert - INFO - res_population_num: 3
2025-06-26 20:52:58,031 - ExploitationExpert - INFO - res_population_costs: [21282, 21343, 21709]
2025-06-26 20:52:58,031 - ExploitationExpert - INFO - res_populations: [array([ 0, 62,  5, 48, 89, 18, 74, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64,
       25, 65, 69, 21, 93, 15, 87, 52, 78, 17, 23, 37, 98, 35, 83,  9, 71,
       20, 73, 58, 16, 14, 10, 31, 44, 90, 97, 22, 76, 59, 61, 34, 85, 26,
       11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99, 47,
       29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81, 84, 67, 72, 49, 43,
        1, 53, 39, 63, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92, 46],
      dtype=int64), array([ 0, 92, 27, 66, 57, 60, 50, 86, 24, 80, 84, 67, 72, 68, 63, 39, 53,
        1, 43, 49, 81, 94, 12, 75, 32, 36,  4, 51, 77, 95, 38, 29, 47, 99,
       40, 70, 13,  2, 42, 45, 28, 33, 82, 54,  6,  8, 56, 19, 11, 26, 85,
       34, 61, 59, 76, 22, 97, 90, 44, 31, 10, 14, 16, 58, 73, 20, 71, 46,
       62,  5, 48, 89,  9, 83, 35, 98, 37, 23, 17, 78, 52, 87, 15, 93, 21,
       69, 65, 25, 64,  3, 18, 74, 96, 55, 79, 30, 88, 41,  7, 91],
      dtype=int64), array([ 0, 91,  7, 41, 88, 30, 79, 55, 96,  3, 64, 25, 65, 69, 21, 93, 15,
       87, 52, 78, 17, 23, 37, 98, 35, 58, 73, 20, 71, 83,  9, 89, 18, 74,
       48,  5, 62, 46, 31, 10, 16, 14, 44, 90, 97, 22, 76, 59, 61, 34, 85,
       26, 11, 19, 56,  8,  6, 54, 82, 33, 28, 45, 42,  2, 13, 70, 40, 99,
       47, 29, 38, 95, 77, 51,  4, 36, 32, 75, 12, 94, 81,  1, 53, 39, 63,
       43, 49, 84, 67, 72, 68, 80, 24, 86, 50, 60, 57, 66, 27, 92],
      dtype=int64)]
2025-06-26 20:52:58,034 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:52:58,034 - ExploitationExpert - INFO - populations: [{'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 82, 31, 53, 75, 97, 38, 60, 84, 36, 58, 62, 40], 'cur_cost': 174197.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 42, 64, 86, 8, 20, 32, 54, 76, 98, 11, 33, 55, 77, 99, 10, 22, 44, 66, 88, 0, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 41, 63, 85, 7, 29, 52, 74, 96, 18, 40, 62, 84, 6, 30], 'cur_cost': 174988.0}, {'tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 3, 24, 46, 68, 80, 2, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 82, 4, 26, 48, 70, 92, 14, 36, 58, 71, 93, 15, 37, 50, 72, 94, 16, 28, 41, 63, 85, 7, 19, 40, 62, 84, 6, 27, 49, 61, 83, 5, 31, 53, 75, 97], 'cur_cost': 170822.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 84, 31, 32, 25, 59, 26, 27, 87, 86, 83, 30, 82, 81, 29, 85, 28, 58], 'cur_cost': 193732.0}, {'tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 86, 85, 26, 33, 28, 84, 0, 29, 27, 83, 31, 32, 82], 'cur_cost': 187419.0}, {'tour': array([49, 94, 42, 96, 89, 61, 74, 88, 51,  9,  3, 62, 41, 95, 33, 15, 93,
       54, 97, 32, 29,  1, 86, 81, 10, 71, 37, 66, 91, 12, 25, 76, 92, 52,
       24, 31, 90, 16,  2, 27, 19, 85, 82, 56, 46, 59, 34, 65, 30, 63, 98,
       14, 58, 21, 77, 28, 45, 43,  6,  5, 69, 60, 44,  0, 83,  4, 68, 17,
       70, 72, 80, 73, 57, 40, 26, 48, 13, 64, 87, 67, 55, 18, 84, 36, 35,
       75, 78, 39, 79, 22,  7, 11, 53, 23, 20,  8, 99, 47, 38, 50]), 'cur_cost': 161350.0}, {'tour': array([60, 42, 44, 49, 52, 65, 81,  6,  7, 92, 17, 15, 63,  8, 86, 26, 27,
       67, 78, 18, 76, 99, 19, 54, 20, 22, 36, 74, 47, 32, 95, 98, 88, 25,
       75, 64, 59, 97, 93, 43, 24, 82, 31, 73, 66, 87, 41, 79, 56, 90, 14,
       53, 71, 84,  5, 70, 83, 38, 80, 48, 77,  0, 30, 69, 29, 72, 58, 94,
       68, 89,  3, 61, 16, 21, 10, 37, 28, 85, 33, 57, 13, 46, 50,  9, 39,
       35, 12, 51, 62,  2, 11, 45, 40,  4, 55,  1, 91, 96, 34, 23]), 'cur_cost': 173153.0}, {'tour': array([83,  7, 78, 46, 18, 44, 91, 86, 10, 38, 39, 15,  9, 65, 66, 76, 16,
       79, 49, 47, 74, 28, 56, 11, 37, 75, 80, 92,  1, 26,  6, 12, 13, 99,
       63, 48, 19, 97, 34, 29,  8, 60, 54, 70, 30, 67, 84, 90, 36, 89, 45,
       57,  3, 96, 31, 95, 62, 73,  4,  5, 93, 33, 77, 22, 21, 59, 81, 82,
        0, 68, 42, 27, 72, 43, 52, 40, 41, 14, 32, 88,  2, 64, 69, 23, 71,
       98, 25, 94, 51, 24, 17, 50, 55, 61, 87, 35, 20, 85, 58, 53]), 'cur_cost': 168116.0}, {'tour': array([31, 92,  8, 72, 77, 33, 62, 96, 80, 90, 58, 27, 44, 98, 61, 48,  0,
       20, 78, 45, 43, 70, 40, 51, 30,  9, 99, 74, 67, 86, 81,  5, 66, 42,
       65, 15, 52, 18, 36, 39, 85, 84, 63, 91, 97,  1, 34, 16, 53, 29, 17,
       12, 25, 37,  2, 75, 68, 49, 47, 94, 26, 22,  4, 10, 71, 32, 21, 35,
       93, 28, 83, 11, 60, 14, 23,  3, 54, 50, 59, 73,  6, 38, 19, 46, 69,
       79, 41, 55, 88, 76, 95, 87, 56, 24, 13, 57, 64,  7, 89, 82]), 'cur_cost': 158735.0}]
2025-06-26 20:52:58,036 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:52:58,036 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 71, 'skip_rate': 0.04225352112676056, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 68, 'cache_hits': 37, 'similarity_calculations': 584, 'cache_hit_rate': 0.06335616438356165, 'cache_size': 547}}
2025-06-26 20:52:58,036 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:52:58,037 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 10, 32, 54, 76, 98, 21, 43, 65, 87, 9, 31, 53, 75, 97, 20, 42, 64, 86, 8, 30, 52, 74, 96, 19, 41, 63, 85, 7, 29, 51, 73, 95, 18, 40, 62, 84, 6, 28, 50, 72, 94, 17, 39, 61, 83, 5, 27, 49, 71, 93, 16, 38, 60, 82, 4, 26, 48, 70, 92, 15, 37, 59, 81, 3, 25, 47, 69, 91, 14, 36, 58, 80, 2, 24, 46, 68, 1, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99], 'cur_cost': 170722.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 8, 19, 41, 63, 85, 7, 28, 50, 72, 94, 6, 17, 39, 61, 83, 5, 26, 48, 70, 92, 4, 15, 37, 59, 81, 3, 24, 46, 68, 80, 2, 13, 35, 57, 79, 91, 22, 44, 66, 88, 0, 11, 33, 55, 77, 99, 20, 42, 64, 86, 18, 29, 51, 73, 95, 16, 27, 49, 71, 93, 14, 25, 47, 69, 82, 31, 53, 75, 97, 38, 60, 84, 36, 58, 62, 40], 'cur_cost': 174197.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 13, 35, 57, 79, 91, 24, 46, 68, 80, 2, 14, 36, 58, 70, 92, 25, 47, 69, 81, 3, 15, 37, 59, 71, 93, 26, 48, 60, 82, 4, 16, 38, 50, 72, 94, 27, 49, 61, 83, 5, 17, 39, 51, 73, 95, 28, 42, 64, 86, 8, 20, 32, 54, 76, 98, 11, 33, 55, 77, 99, 10, 22, 44, 66, 88, 0, 21, 43, 65, 87, 9, 31, 53, 75, 97, 19, 41, 63, 85, 7, 29, 52, 74, 96, 18, 40, 62, 84, 6, 30], 'cur_cost': 174988.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [12, 34, 56, 78, 90, 23, 45, 67, 89, 1, 32, 54, 76, 98, 10, 21, 43, 65, 87, 9, 30, 52, 74, 96, 18, 39, 60, 81, 3, 24, 46, 68, 80, 2, 25, 47, 69, 91, 13, 35, 57, 79, 0, 22, 44, 66, 88, 11, 33, 55, 77, 99, 20, 42, 64, 86, 8, 29, 51, 73, 95, 17, 38, 59, 82, 4, 26, 48, 70, 92, 14, 36, 58, 71, 93, 15, 37, 50, 72, 94, 16, 28, 41, 63, 85, 7, 19, 40, 62, 84, 6, 27, 49, 61, 83, 5, 31, 53, 75, 97], 'cur_cost': 170822.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 0, 33, 84, 31, 32, 25, 59, 26, 27, 87, 86, 83, 30, 82, 81, 29, 85, 28, 58], 'cur_cost': 193732.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [12, 45, 78, 23, 56, 89, 34, 67, 90, 1, 24, 57, 80, 13, 46, 79, 2, 35, 68, 91, 14, 47, 70, 3, 36, 69, 92, 15, 48, 71, 4, 37, 60, 93, 16, 49, 72, 5, 38, 61, 94, 17, 50, 73, 6, 39, 62, 95, 18, 51, 74, 7, 40, 63, 96, 19, 52, 75, 8, 41, 64, 97, 20, 53, 76, 9, 42, 65, 98, 21, 54, 77, 10, 43, 66, 99, 22, 55, 88, 11, 44, 87, 30, 59, 25, 58, 81, 86, 85, 26, 33, 28, 84, 0, 29, 27, 83, 31, 32, 82], 'cur_cost': 187419.0}}]
2025-06-26 20:52:58,037 - __main__ - INFO - 进化阶段完成
2025-06-26 20:52:58,037 - __main__ - INFO - 开始评估阶段
2025-06-26 20:52:58,037 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:52:58,037 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:52:58,037 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 163098.0 → 158735.0 (Δ 4363.0)
- Mean cost: 173753.8 → 173323.4
- Diversity: 0.751 → 0.657 (Δ -0.0939)

#### Elite metrics
- Elite count: 3 → 3
- Best elite cost: 21282 → 21282 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 66.7% , avg Δ -3354.0
- Exploit: 4 inds, success 75.0% , avg Δ 6107.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [157815.0, 164593.0, 163098.0], diversity [0.8900000000000001, 0.8533333333333332, 0.9137777777777777]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:52:58,037 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:53:14,915 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": -3354.0,
      "contribution_to_elite": "moderate (reduced diversity but some cost improvement)"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 6107.0,
      "contribution_to_elite": "limited (higher cost deltas but no elite improvement)"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (explore:exploit)",
    "assessment": "exploit-heavy given population size; explore success is offset by exploit regressions"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_actions": [
      "introduce 1-2 new random individuals to boost diversity",
      "penalize exploit mutations that increase cost beyond 5% of current min"
    ]
  }
}
```
2025-06-26 20:53:14,983 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:53:14,983 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": -3354.0,
      "contribution_to_elite": "moderate (reduced diversity but some cost improvement)"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 6107.0,
      "contribution_to_elite": "limited (higher cost deltas but no elite improvement)"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (explore:exploit)",
    "assessment": "exploit-heavy given population size; explore success is offset by exploit regressions"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_actions": [
      "introduce 1-2 new random individuals to boost diversity",
      "penalize exploit mutations that increase cost beyond 5% of current min"
    ]
  }
}
```
2025-06-26 20:53:14,983 - __main__ - INFO - 评估阶段完成
2025-06-26 20:53:14,983 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_delta": -3354.0,
      "contribution_to_elite": "moderate (reduced diversity but some cost improvement)"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 6107.0,
      "contribution_to_elite": "limited (higher cost deltas but no elite improvement)"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (explore:exploit)",
    "assessment": "exploit-heavy given population size; explore success is offset by exploit regressions"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_actions": [
      "introduce 1-2 new random individuals to boost diversity",
      "penalize exploit mutations that increase cost beyond 5% of current min"
    ]
  }
}
```
2025-06-26 20:53:14,983 - __main__ - INFO - 当前最佳适应度: 158735.0
2025-06-26 20:53:14,983 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\kroA100_route_4.pkl
2025-06-26 20:53:14,991 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\kroA100_solution.json
2025-06-26 20:53:14,992 - __main__ - INFO - 实例 kroA100 处理完成
