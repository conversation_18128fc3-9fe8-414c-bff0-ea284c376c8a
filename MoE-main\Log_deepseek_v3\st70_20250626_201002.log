2025-06-26 20:10:02,085 - __main__ - INFO - st70 开始进化第 1 代
2025-06-26 20:10:02,085 - __main__ - INFO - 开始分析阶段
2025-06-26 20:10:02,085 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:10:02,106 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 814.0, 'max': 3933.0, 'mean': 2816.1, 'std': 1291.428546223135}, 'diversity': 0.927936507936508, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:10:02,107 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 814.0, 'max': 3933.0, 'mean': 2816.1, 'std': 1291.428546223135}, 'diversity_level': 0.927936507936508, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[64, 96], [80, 39], [69, 23], [72, 42], [48, 67], [58, 43], [81, 34], [79, 17], [30, 23], [42, 67], [7, 76], [29, 51], [78, 92], [64, 8], [95, 57], [57, 91], [40, 35], [68, 40], [92, 34], [62, 1], [28, 43], [76, 73], [67, 88], [93, 54], [6, 8], [87, 18], [30, 9], [77, 13], [78, 94], [55, 3], [82, 88], [73, 28], [20, 55], [27, 43], [95, 86], [67, 99], [48, 83], [75, 81], [8, 19], [20, 18], [54, 38], [63, 36], [44, 33], [52, 18], [12, 13], [25, 5], [58, 85], [5, 67], [90, 9], [41, 76], [25, 76], [37, 64], [56, 63], [10, 55], [98, 7], [16, 74], [89, 60], [48, 82], [81, 76], [29, 60], [17, 22], [5, 45], [79, 70], [9, 100], [17, 82], [74, 67], [10, 68], [48, 19], [83, 86], [84, 94]], 'distance_matrix': array([[ 0., 59., 73., ..., 79., 21., 20.],
       [59.,  0., 19., ..., 38., 47., 55.],
       [73., 19.,  0., ..., 21., 65., 73.],
       ...,
       [79., 38., 21., ...,  0., 76., 83.],
       [21., 47., 65., ..., 76.,  0.,  8.],
       [20., 55., 73., ..., 83.,  8.,  0.]])}
2025-06-26 20:10:02,116 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:10:02,116 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:10:02,116 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:10:02,122 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:10:02,123 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (15, 46), 'frequency': 0.5, 'avg_cost': 6.0}], 'common_subpaths': [{'subpath': (22, 0, 35), 'frequency': 0.3}, {'subpath': (36, 57, 49), 'frequency': 0.3}, {'subpath': (57, 49, 9), 'frequency': 0.3}, {'subpath': (49, 9, 4), 'frequency': 0.3}, {'subpath': (9, 4, 52), 'frequency': 0.3}, {'subpath': (25, 48, 54), 'frequency': 0.3}, {'subpath': (48, 54, 18), 'frequency': 0.3}, {'subpath': (29, 19, 13), 'frequency': 0.3}, {'subpath': (38, 44, 24), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(0, 22)', 'frequency': 0.4}, {'edge': '(12, 30)', 'frequency': 0.4}, {'edge': '(21, 62)', 'frequency': 0.4}, {'edge': '(15, 46)', 'frequency': 0.5}, {'edge': '(4, 52)', 'frequency': 0.4}, {'edge': '(20, 33)', 'frequency': 0.4}, {'edge': '(7, 27)', 'frequency': 0.4}, {'edge': '(25, 48)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(0, 35)', 'frequency': 0.3}, {'edge': '(12, 28)', 'frequency': 0.3}, {'edge': '(30, 68)', 'frequency': 0.3}, {'edge': '(34, 69)', 'frequency': 0.3}, {'edge': '(21, 58)', 'frequency': 0.3}, {'edge': '(36, 57)', 'frequency': 0.3}, {'edge': '(49, 57)', 'frequency': 0.3}, {'edge': '(9, 49)', 'frequency': 0.3}, {'edge': '(4, 9)', 'frequency': 0.3}, {'edge': '(51, 59)', 'frequency': 0.3}, {'edge': '(11, 59)', 'frequency': 0.3}, {'edge': '(11, 20)', 'frequency': 0.3}, {'edge': '(32, 33)', 'frequency': 0.2}, {'edge': '(32, 53)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.2}, {'edge': '(47, 61)', 'frequency': 0.2}, {'edge': '(47, 66)', 'frequency': 0.3}, {'edge': '(55, 66)', 'frequency': 0.2}, {'edge': '(55, 64)', 'frequency': 0.3}, {'edge': '(50, 64)', 'frequency': 0.2}, {'edge': '(10, 50)', 'frequency': 0.2}, {'edge': '(10, 63)', 'frequency': 0.2}, {'edge': '(16, 42)', 'frequency': 0.3}, {'edge': '(40, 42)', 'frequency': 0.3}, {'edge': '(5, 40)', 'frequency': 0.3}, {'edge': '(5, 41)', 'frequency': 0.3}, {'edge': '(17, 41)', 'frequency': 0.3}, {'edge': '(3, 17)', 'frequency': 0.3}, {'edge': '(1, 3)', 'frequency': 0.2}, {'edge': '(1, 6)', 'frequency': 0.3}, {'edge': '(6, 31)', 'frequency': 0.3}, {'edge': '(2, 31)', 'frequency': 0.3}, {'edge': '(2, 7)', 'frequency': 0.2}, {'edge': '(25, 27)', 'frequency': 0.2}, {'edge': '(48, 54)', 'frequency': 0.3}, {'edge': '(18, 54)', 'frequency': 0.3}, {'edge': '(18, 23)', 'frequency': 0.2}, {'edge': '(14, 23)', 'frequency': 0.3}, {'edge': '(14, 56)', 'frequency': 0.2}, {'edge': '(43, 67)', 'frequency': 0.3}, {'edge': '(19, 29)', 'frequency': 0.3}, {'edge': '(13, 19)', 'frequency': 0.3}, {'edge': '(26, 45)', 'frequency': 0.3}, {'edge': '(39, 60)', 'frequency': 0.3}, {'edge': '(38, 60)', 'frequency': 0.2}, {'edge': '(38, 44)', 'frequency': 0.3}, {'edge': '(24, 44)', 'frequency': 0.3}, {'edge': '(8, 24)', 'frequency': 0.2}, {'edge': '(56, 62)', 'frequency': 0.3}, {'edge': '(37, 58)', 'frequency': 0.2}, {'edge': '(37, 68)', 'frequency': 0.2}, {'edge': '(28, 69)', 'frequency': 0.3}, {'edge': '(22, 34)', 'frequency': 0.2}, {'edge': '(15, 35)', 'frequency': 0.2}, {'edge': '(36, 46)', 'frequency': 0.2}, {'edge': '(52, 65)', 'frequency': 0.2}, {'edge': '(11, 33)', 'frequency': 0.2}, {'edge': '(24, 39)', 'frequency': 0.2}, {'edge': '(45, 67)', 'frequency': 0.2}, {'edge': '(29, 43)', 'frequency': 0.2}, {'edge': '(13, 27)', 'frequency': 0.2}, {'edge': '(1, 31)', 'frequency': 0.3}, {'edge': '(9, 59)', 'frequency': 0.2}, {'edge': '(28, 53)', 'frequency': 0.2}, {'edge': '(35, 44)', 'frequency': 0.2}, {'edge': '(11, 54)', 'frequency': 0.2}, {'edge': '(8, 61)', 'frequency': 0.2}, {'edge': '(34, 51)', 'frequency': 0.2}, {'edge': '(10, 58)', 'frequency': 0.2}, {'edge': '(25, 60)', 'frequency': 0.2}, {'edge': '(14, 28)', 'frequency': 0.2}, {'edge': '(52, 68)', 'frequency': 0.2}, {'edge': '(36, 64)', 'frequency': 0.2}, {'edge': '(2, 16)', 'frequency': 0.2}, {'edge': '(7, 12)', 'frequency': 0.2}, {'edge': '(5, 8)', 'frequency': 0.2}, {'edge': '(34, 66)', 'frequency': 0.2}, {'edge': '(5, 42)', 'frequency': 0.3}, {'edge': '(37, 38)', 'frequency': 0.2}, {'edge': '(4, 23)', 'frequency': 0.2}, {'edge': '(53, 57)', 'frequency': 0.3}, {'edge': '(11, 24)', 'frequency': 0.2}, {'edge': '(10, 66)', 'frequency': 0.2}, {'edge': '(26, 41)', 'frequency': 0.2}, {'edge': '(33, 55)', 'frequency': 0.2}, {'edge': '(17, 25)', 'frequency': 0.2}, {'edge': '(32, 38)', 'frequency': 0.2}, {'edge': '(43, 46)', 'frequency': 0.2}, {'edge': '(61, 67)', 'frequency': 0.2}, {'edge': '(48, 65)', 'frequency': 0.3}, {'edge': '(31, 57)', 'frequency': 0.2}, {'edge': '(13, 62)', 'frequency': 0.2}, {'edge': '(4, 7)', 'frequency': 0.2}, {'edge': '(14, 26)', 'frequency': 0.2}, {'edge': '(31, 50)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [65, 63, 19, 68], 'cost': 273.0, 'size': 4}, {'region': [19, 47, 25, 55], 'cost': 273.0, 'size': 4}, {'region': [69, 47, 23, 45], 'cost': 256.0, 'size': 4}, {'region': [22, 54, 34, 66], 'cost': 253.0, 'size': 4}, {'region': [61, 14, 26, 21], 'cost': 251.0, 'size': 4}]}
2025-06-26 20:10:02,123 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:10:02,124 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:10:02,124 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:10:02,124 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 20:10:02,124 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 20:10:02,124 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:10:02,124 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:10:02,126 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:10:02,126 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 814.0, mean 2816.1, max 3933.0, std 1291.428546223135
- diversity: 0.927936507936508
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [29238.0, 27336.0, 23824.0] (Δ 2707.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (15, 46), 'frequency': 0.5, 'avg_cost': 6.0}]
- common_subpaths_sample: [{'subpath': (22, 0, 35), 'frequency': 0.3}, {'subpath': (36, 57, 49), 'frequency': 0.3}, {'subpath': (57, 49, 9), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [65, 63, 19, 68], 'cost': 273.0, 'size': 4}, {'region': [19, 47, 25, 55], 'cost': 273.0, 'size': 4}, {'region': [69, 47, 23, 45], 'cost': 256.0, 'size': 4}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [5.0, 1.0, 98.0, 100.0]
- centroid/spread: [52.657, 51.271] | rms 40.489
- density_grid(3x3): [[8, 6, 7], [7, 6, 8], [7, 8, 13]]
- edge_len_stats(min,q1,med,q3,max): [1.0, 33.0, 52.0, 70.0, 129.0]
- long_edge_ratio: 0.246
- direction_hist(8 sectors): [10, 8, 8, 13, 8, 8, 5, 9]
- nn_median_dist: 6.083

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:10:02,127 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:10:30,244 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 1.0 to 129.0, with 24.6% long edges)",
    "modality": "Potentially multimodal (high diversity score of 0.928, no clustering detected yet)",
    "deceptiveness": "Possible deceptive regions (presence of difficult regions with high cost despite small size)"
  },
  "population_state": {
    "coverage": "Good initial coverage (high diversity 0.928, uniform density grid except one dense cell)",
    "convergence": "No convergence yet (Δ2707 cost improvement per iteration, 0.0 convergence metric)",
    "quality_distribution": "Wide spread (min 814 vs max 3933, stddev 1291.4)"
  },
  "difficult_regions": [
    {"region": [65, 63, 19, 68], "reason": "High cost (273) relative to size (4), located in sparse NE quadrant"},
    {"region": [19, 47, 25, 55], "reason": "High cost (273) cluster near centroid"},
    {"region": [69, 47, 23, 45], "reason": "High cost (256) with directional bias (NE/SW)"}
  ],
  "opportunity_regions": [
    {"region": "SE grid cell", "reason": "Highest density (13 nodes) with common subpaths like (36,57,49)"},
    {"region": "Centroid area", "reason": "Frequent edges like (15,46) with low avg_cost (6.0)"}
  ],
  "evolution_phase": "Early exploration (iteration 0/5, no elites yet)",
  "evolution_direction": [
    "Operator priority: Edge recombination (exploit high-frequency edges like (15,46))",
    "Focus search: Intensify in SE dense cell while maintaining diversity",
    "Adaptive strategy: Penalize long edges (>70 units) in difficult regions",
    "Next steps: Identify building blocks from common subpaths (e.g. (57,49,9))"
  ]
}
```
2025-06-26 20:10:30,244 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:10:30,244 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 1.0 to 129.0, with 24.6% long edges)', 'modality': 'Potentially multimodal (high diversity score of 0.928, no clustering detected yet)', 'deceptiveness': 'Possible deceptive regions (presence of difficult regions with high cost despite small size)'}, 'population_state': {'coverage': 'Good initial coverage (high diversity 0.928, uniform density grid except one dense cell)', 'convergence': 'No convergence yet (Δ2707 cost improvement per iteration, 0.0 convergence metric)', 'quality_distribution': 'Wide spread (min 814 vs max 3933, stddev 1291.4)'}, 'difficult_regions': [{'region': [65, 63, 19, 68], 'reason': 'High cost (273) relative to size (4), located in sparse NE quadrant'}, {'region': [19, 47, 25, 55], 'reason': 'High cost (273) cluster near centroid'}, {'region': [69, 47, 23, 45], 'reason': 'High cost (256) with directional bias (NE/SW)'}], 'opportunity_regions': [{'region': 'SE grid cell', 'reason': 'Highest density (13 nodes) with common subpaths like (36,57,49)'}, {'region': 'Centroid area', 'reason': 'Frequent edges like (15,46) with low avg_cost (6.0)'}], 'evolution_phase': 'Early exploration (iteration 0/5, no elites yet)', 'evolution_direction': ['Operator priority: Edge recombination (exploit high-frequency edges like (15,46))', 'Focus search: Intensify in SE dense cell while maintaining diversity', 'Adaptive strategy: Penalize long edges (>70 units) in difficult regions', 'Next steps: Identify building blocks from common subpaths (e.g. (57,49,9))']}
2025-06-26 20:10:30,244 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:10:30,244 - __main__ - INFO - 分析阶段完成
2025-06-26 20:10:30,244 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 1.0 to 129.0, with 24.6% long edges)', 'modality': 'Potentially multimodal (high diversity score of 0.928, no clustering detected yet)', 'deceptiveness': 'Possible deceptive regions (presence of difficult regions with high cost despite small size)'}, 'population_state': {'coverage': 'Good initial coverage (high diversity 0.928, uniform density grid except one dense cell)', 'convergence': 'No convergence yet (Δ2707 cost improvement per iteration, 0.0 convergence metric)', 'quality_distribution': 'Wide spread (min 814 vs max 3933, stddev 1291.4)'}, 'difficult_regions': [{'region': [65, 63, 19, 68], 'reason': 'High cost (273) relative to size (4), located in sparse NE quadrant'}, {'region': [19, 47, 25, 55], 'reason': 'High cost (273) cluster near centroid'}, {'region': [69, 47, 23, 45], 'reason': 'High cost (256) with directional bias (NE/SW)'}], 'opportunity_regions': [{'region': 'SE grid cell', 'reason': 'Highest density (13 nodes) with common subpaths like (36,57,49)'}, {'region': 'Centroid area', 'reason': 'Frequent edges like (15,46) with low avg_cost (6.0)'}], 'evolution_phase': 'Early exploration (iteration 0/5, no elites yet)', 'evolution_direction': ['Operator priority: Edge recombination (exploit high-frequency edges like (15,46))', 'Focus search: Intensify in SE dense cell while maintaining diversity', 'Adaptive strategy: Penalize long edges (>70 units) in difficult regions', 'Next steps: Identify building blocks from common subpaths (e.g. (57,49,9))']}
2025-06-26 20:10:30,244 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:10:30,244 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:10:30,256 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 814.0
  • mean_cost: 2816.1
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 1.0 to 129.0, with 24.6% long edges), modality Potentially multimodal (high diversity score of 0.928, no clustering detected yet), deceptiveness Possible deceptive regions (presence of difficult regions with high cost despite small size)
- Evolution phase: Early exploration (iteration 0/5, no elites yet)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [65, 63, , {'region': [19, 47, ...
- Opportunity regions (# 2): {'region': 'SE grid , {'region': 'Centroid
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:10:30,256 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:10:30,256 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:10:40,354 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with low diversity and no elites; high exploration to discover promising regions."
}
```
2025-06-26 20:10:40,354 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:10:40,354 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:10:40,354 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with low diversity and no elites; high exploration to discover promising regions."
}
```
2025-06-26 20:10:40,357 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:10:40,357 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 20:10:40,357 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase with low diversity and no elites; high exploration to discover promising regions."
}
```
2025-06-26 20:10:40,357 - __main__ - INFO - 开始进化阶段
2025-06-26 20:10:40,357 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:10:40,357 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:10:40,357 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 871.0 (rank 20.0%)
- Population diversity: 0.7695652173913043
- Evolution phase: Early exploration (iteration 0/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'SE , {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [65,, {'region': [19,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:10:40,357 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:10:40,357 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:10:56,067 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11],
  "targeted_regions": "SE, Cen",
  "strategy_comment": "Sequential sweep through opportunity regions, avoiding difficult nodes"
}
```
2025-06-26 20:10:56,067 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:10:56,067 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3480.0, 路径: [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11]
2025-06-26 20:10:56,067 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}
2025-06-26 20:10:56,067 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:10:56,067 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:10:56,067 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 867.0 (rank 10.0%)
- Population diversity: 0.8318840579710145
- Evolution phase: Early exploration (iteration 0/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'SE , {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [65,, {'region': [19,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:10:56,067 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:10:56,078 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:11:11,626 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11],
  "targeted_regions": "SE, Cen adjacent",
  "strategy_comment": "Sequential sweep through sparse cells near opportunity regions"
}
```
2025-06-26 20:11:11,633 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:11:11,633 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3480.0, 路径: [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11]
2025-06-26 20:11:11,633 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}
2025-06-26 20:11:11,633 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:11:11,633 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:11:11,633 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 814.0 (rank 0.0%)
- Population diversity: 0.8115942028985508
- Evolution phase: Early exploration (iteration 0/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'SE , {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [65,, {'region': [19,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:11:11,633 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:11:11,633 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:11:27,428 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11],
  "targeted_regions": "SE, Cen",
  "strategy_comment": "Sequential sweep through opportunity regions, avoiding difficult nodes"
}
```
2025-06-26 20:11:27,428 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:11:27,428 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3480.0, 路径: [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11]
2025-06-26 20:11:27,428 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}
2025-06-26 20:11:27,428 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:11:27,428 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:11:27,428 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3549.0 (rank 40.0%)
- Population diversity: 0.7289855072463768
- Evolution phase: Early exploration (iteration 0/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'SE , {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [65,, {'region': [19,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:11:27,428 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:11:27,428 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:11:42,923 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11],
  "targeted_regions": "SE, Cen",
  "strategy_comment": "Sequential sweep through opportunity regions, skipping difficult nodes"
}
```
2025-06-26 20:11:42,923 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:11:42,923 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3480.0, 路径: [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11]
2025-06-26 20:11:42,923 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}
2025-06-26 20:11:42,932 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:11:42,932 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:11:42,932 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3721.0 (rank 80.0%)
- Population diversity: 0.636231884057971
- Evolution phase: Early exploration (iteration 0/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'SE , {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [65,, {'region': [19,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:11:42,932 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:11:42,932 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:11:58,621 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "SE, Cen",
  "strategy_comment": "Sequential skip to balance exploration and cost"
}
```
2025-06-26 20:11:58,621 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:11:58,621 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:11:58,621 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:11:58,621 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:11:58,621 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:11:58,621 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3536.0 (rank 50.0%)
- Population diversity: 0.6260869565217392
- Evolution phase: Early exploration (iteration 0/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'SE , {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [65,, {'region': [19,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:11:58,621 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:11:58,621 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:12:13,426 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69],
  "targeted_regions": "SE, Cen",
  "strategy_comment": "Sequential sweep through opportunity regions"
}
```
2025-06-26 20:12:13,426 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:12:13,426 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3410.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]
2025-06-26 20:12:13,426 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}
2025-06-26 20:12:13,426 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:12:13,426 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:12:13,426 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3602.0 (rank 70.0%)
- Population diversity: 0.6289855072463768
- Evolution phase: Early exploration (iteration 0/5, no elites yet)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': 'SE , {'region': 'Cen
- Difficult regions to avoid (sample): {'region': [65,, {'region': [19,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:12:13,426 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:12:13,426 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:12:29,275 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69],
  "targeted_regions": "SE, Cen",
  "strategy_comment": "Sequential exploration with focus on SE and Cen regions"
}
```
2025-06-26 20:12:29,275 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:12:29,275 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3410.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]
2025-06-26 20:12:29,275 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}
2025-06-26 20:12:29,275 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:12:29,275 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:12:29,275 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:12:29,275 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 3467.0
2025-06-26 20:12:29,776 - ExploitationExpert - INFO - res_population_num: 7
2025-06-26 20:12:29,777 - ExploitationExpert - INFO - res_population_costs: [680, 676, 675, 675, 675, 675, 675]
2025-06-26 20:12:29,777 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64)]
2025-06-26 20:12:29,780 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:12:29,780 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': array([28, 69, 10, 51,  7, 25,  8, 41, 62,  2, 45, 21, 35, 57,  5, 43, 47,
       48, 44, 34, 58, 67, 20, 24, 16, 46, 60, 39, 22, 30, 54, 32,  9, 40,
       50, 56, 36, 38, 53, 29, 66, 49, 61, 31, 17, 42, 55, 11, 19, 14, 68,
       52, 63, 27, 13, 26,  4,  1, 64, 18,  0,  3, 65, 37, 12, 23,  6, 59,
       15, 33]), 'cur_cost': 3467.0}, {'tour': [9, 17, 25, 22, 55, 12, 37, 21, 23, 66, 10, 48, 65, 38, 32, 18, 50, 31, 33, 1, 44, 20, 3, 24, 11, 54, 42, 34, 52, 57, 43, 49, 0, 28, 53, 14, 58, 27, 36, 68, 60, 19, 69, 35, 39, 29, 4, 7, 13, 16, 5, 8, 59, 45, 64, 30, 47, 41, 2, 6, 67, 61, 26, 63, 62, 56, 15, 46, 40, 51], 'cur_cost': 3564.0}, {'tour': [37, 17, 67, 41, 16, 2, 11, 68, 6, 3, 52, 8, 1, 60, 0, 53, 51, 47, 48, 25, 29, 10, 27, 23, 32, 58, 15, 30, 12, 38, 24, 55, 33, 69, 18, 19, 20, 59, 35, 5, 61, 14, 26, 21, 50, 31, 36, 7, 65, 22, 54, 34, 66, 45, 57, 28, 9, 13, 62, 42, 46, 43, 40, 39, 56, 4, 63, 64, 44, 49], 'cur_cost': 3704.0}]
2025-06-26 20:12:29,781 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:12:29,781 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-06-26 20:12:29,781 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:12:29,781 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:12:29,781 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:12:29,781 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:12:29,783 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 3302.0
2025-06-26 20:12:30,285 - ExploitationExpert - INFO - res_population_num: 16
2025-06-26 20:12:30,285 - ExploitationExpert - INFO - res_population_costs: [680, 676, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675]
2025-06-26 20:12:30,285 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:12:30,290 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:12:30,290 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': array([28, 69, 10, 51,  7, 25,  8, 41, 62,  2, 45, 21, 35, 57,  5, 43, 47,
       48, 44, 34, 58, 67, 20, 24, 16, 46, 60, 39, 22, 30, 54, 32,  9, 40,
       50, 56, 36, 38, 53, 29, 66, 49, 61, 31, 17, 42, 55, 11, 19, 14, 68,
       52, 63, 27, 13, 26,  4,  1, 64, 18,  0,  3, 65, 37, 12, 23,  6, 59,
       15, 33]), 'cur_cost': 3467.0}, {'tour': array([38, 51, 52, 37, 43, 17, 34, 69, 11,  4, 35, 65,  6, 56, 60, 23, 29,
        3, 21, 10, 40, 19, 48,  2, 27, 53, 66,  0, 62, 12, 15, 59, 31,  7,
       50, 32, 63, 26, 47, 14, 42, 68, 57, 33, 36, 55, 41,  9, 64, 44, 39,
       58, 67, 45, 18,  8, 24, 13, 49, 46, 20, 22,  5, 25, 28, 54,  1, 30,
       16, 61]), 'cur_cost': 3302.0}, {'tour': [37, 17, 67, 41, 16, 2, 11, 68, 6, 3, 52, 8, 1, 60, 0, 53, 51, 47, 48, 25, 29, 10, 27, 23, 32, 58, 15, 30, 12, 38, 24, 55, 33, 69, 18, 19, 20, 59, 35, 5, 61, 14, 26, 21, 50, 31, 36, 7, 65, 22, 54, 34, 66, 45, 57, 28, 9, 13, 62, 42, 46, 43, 40, 39, 56, 4, 63, 64, 44, 49], 'cur_cost': 3704.0}]
2025-06-26 20:12:30,293 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:12:30,293 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 121, 'cache_hit_rate': 0.0, 'cache_size': 121}}
2025-06-26 20:12:30,293 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:12:30,293 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:12:30,294 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:12:30,294 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:12:30,294 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 3816.0
2025-06-26 20:12:30,795 - ExploitationExpert - INFO - res_population_num: 17
2025-06-26 20:12:30,795 - ExploitationExpert - INFO - res_population_costs: [680, 676, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675]
2025-06-26 20:12:30,795 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:12:30,801 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:12:30,801 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': array([28, 69, 10, 51,  7, 25,  8, 41, 62,  2, 45, 21, 35, 57,  5, 43, 47,
       48, 44, 34, 58, 67, 20, 24, 16, 46, 60, 39, 22, 30, 54, 32,  9, 40,
       50, 56, 36, 38, 53, 29, 66, 49, 61, 31, 17, 42, 55, 11, 19, 14, 68,
       52, 63, 27, 13, 26,  4,  1, 64, 18,  0,  3, 65, 37, 12, 23,  6, 59,
       15, 33]), 'cur_cost': 3467.0}, {'tour': array([38, 51, 52, 37, 43, 17, 34, 69, 11,  4, 35, 65,  6, 56, 60, 23, 29,
        3, 21, 10, 40, 19, 48,  2, 27, 53, 66,  0, 62, 12, 15, 59, 31,  7,
       50, 32, 63, 26, 47, 14, 42, 68, 57, 33, 36, 55, 41,  9, 64, 44, 39,
       58, 67, 45, 18,  8, 24, 13, 49, 46, 20, 22,  5, 25, 28, 54,  1, 30,
       16, 61]), 'cur_cost': 3302.0}, {'tour': array([48,  9, 28, 27, 40, 39,  1, 13, 21, 63, 46, 25,  5,  8, 17, 24, 67,
       44, 41, 62, 52,  6, 50, 16, 36, 69, 38, 54, 14, 49, 51, 47, 23, 64,
       12, 55, 26, 29, 32, 10, 30, 33, 59, 20, 65, 45,  2, 35, 19,  3, 22,
       53, 58, 68,  7, 56, 60, 61, 57, 31, 18, 66, 34, 43, 15, 37, 42,  4,
        0, 11]), 'cur_cost': 3816.0}]
2025-06-26 20:12:30,803 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:12:30,803 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 19, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 19, 'cache_hits': 0, 'similarity_calculations': 123, 'cache_hit_rate': 0.0, 'cache_size': 123}}
2025-06-26 20:12:30,803 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:12:30,804 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}}]
2025-06-26 20:12:30,804 - __main__ - INFO - 进化阶段完成
2025-06-26 20:12:30,804 - __main__ - INFO - 开始评估阶段
2025-06-26 20:12:30,805 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:12:30,805 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:12:30,805 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 814.0 → 3302.0 (Δ -2488.0)
- Mean cost: 2816.1 → 3483.3
- Diversity: 0.770 → 0.555 (Δ -0.2145)

#### Elite metrics
- Elite count: 0 → 17
- Best elite cost: 814.0 → 675 (Δ 139.0)

#### Strategy performance
- Explore: 7 inds, success 57.1% , avg Δ -1041.14
- Exploit: 3 inds, success 66.7% , avg Δ 205.33

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [29238.0, 27336.0, 23824.0], diversity [0.6260683760683761, 0.8307692307692308, 0.829059829059829]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:12:30,805 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:12:48,902 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_improvement": -1041.14,
      "contribution_to_elites": "high (drives significant cost reductions)"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 205.33,
      "contribution_to_elites": "moderate (refines existing solutions)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore-heavy but effective; exploit underutilized given its success rate",
    "diversity_warning": "diversity dropped sharply (-0.2145), risking premature convergence"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_actions": [
      "increase exploit attempts to 40% to capitalize on its success rate",
      "introduce 1-2 random immigrants per iteration to combat diversity loss",
      "monitor elite count (currently 17) for over-specialization"
    ]
  }
}
```
2025-06-26 20:12:48,937 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:12:48,937 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_improvement": -1041.14,
      "contribution_to_elites": "high (drives significant cost reductions)"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 205.33,
      "contribution_to_elites": "moderate (refines existing solutions)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore-heavy but effective; exploit underutilized given its success rate",
    "diversity_warning": "diversity dropped sharply (-0.2145), risking premature convergence"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_actions": [
      "increase exploit attempts to 40% to capitalize on its success rate",
      "introduce 1-2 random immigrants per iteration to combat diversity loss",
      "monitor elite count (currently 17) for over-specialization"
    ]
  }
}
```
2025-06-26 20:12:48,937 - __main__ - INFO - 评估阶段完成
2025-06-26 20:12:48,937 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 57.1,
      "average_improvement": -1041.14,
      "contribution_to_elites": "high (drives significant cost reductions)"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_improvement": 205.33,
      "contribution_to_elites": "moderate (refines existing solutions)"
    }
  },
  "balance_state": {
    "current_ratio": "70% explore / 30% exploit",
    "assessment": "explore-heavy but effective; exploit underutilized given its success rate",
    "diversity_warning": "diversity dropped sharply (-0.2145), risking premature convergence"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_actions": [
      "increase exploit attempts to 40% to capitalize on its success rate",
      "introduce 1-2 random immigrants per iteration to combat diversity loss",
      "monitor elite count (currently 17) for over-specialization"
    ]
  }
}
```
2025-06-26 20:12:48,937 - __main__ - INFO - 当前最佳适应度: 3302.0
2025-06-26 20:12:48,937 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\st70_route_0.pkl
2025-06-26 20:12:48,937 - __main__ - INFO - st70 开始进化第 2 代
2025-06-26 20:12:48,937 - __main__ - INFO - 开始分析阶段
2025-06-26 20:12:48,937 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:12:48,959 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 3302.0, 'max': 3816.0, 'mean': 3483.3, 'std': 124.58896419827882}, 'diversity': 0.8152380952380953, 'clusters': {'clusters': 6, 'cluster_sizes': [4, 1, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:12:48,960 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 3302.0, 'max': 3816.0, 'mean': 3483.3, 'std': 124.58896419827882}, 'diversity_level': 0.8152380952380953, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [4, 1, 2, 1, 1, 1]}, 'coordinates': [[64, 96], [80, 39], [69, 23], [72, 42], [48, 67], [58, 43], [81, 34], [79, 17], [30, 23], [42, 67], [7, 76], [29, 51], [78, 92], [64, 8], [95, 57], [57, 91], [40, 35], [68, 40], [92, 34], [62, 1], [28, 43], [76, 73], [67, 88], [93, 54], [6, 8], [87, 18], [30, 9], [77, 13], [78, 94], [55, 3], [82, 88], [73, 28], [20, 55], [27, 43], [95, 86], [67, 99], [48, 83], [75, 81], [8, 19], [20, 18], [54, 38], [63, 36], [44, 33], [52, 18], [12, 13], [25, 5], [58, 85], [5, 67], [90, 9], [41, 76], [25, 76], [37, 64], [56, 63], [10, 55], [98, 7], [16, 74], [89, 60], [48, 82], [81, 76], [29, 60], [17, 22], [5, 45], [79, 70], [9, 100], [17, 82], [74, 67], [10, 68], [48, 19], [83, 86], [84, 94]], 'distance_matrix': array([[ 0., 59., 73., ..., 79., 21., 20.],
       [59.,  0., 19., ..., 38., 47., 55.],
       [73., 19.,  0., ..., 21., 65., 73.],
       ...,
       [79., 38., 21., ...,  0., 76., 83.],
       [21., 47., 65., ..., 76.,  0.,  8.],
       [20., 55., 73., ..., 83.,  8.,  0.]])}
2025-06-26 20:12:48,960 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:12:48,961 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:12:48,961 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:12:48,962 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:12:48,962 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:12:48,962 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:12:48,962 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:12:48,962 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:12:48,962 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:12:48,970 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:12:48,970 - __main__ - INFO - 精英专家分析报告: {'elite_count': 17, 'elite_common_features': {'common_edges': {'(33, 20)': 0.7647058823529411, '(66, 47)': 0.7058823529411765}, 'common_edge_ratio': 0.02857142857142857}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 2627.0, 'avg_gap': 2807.9470588235295}, 'structure_gap': {'unique_elite_edges': 155, 'unique_pop_edges': 386, 'common_edges': 11}}, 'elite_diversity': {'diversity_score': 0.07647058823529418}}
2025-06-26 20:12:48,971 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:12:48,971 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:12:48,971 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:12:48,971 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 3302.0, mean 3483.3, max 3816.0, std 124.58896419827882
- diversity: 0.8152380952380953
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [27336.0, 23824.0, 3302.0] (Δ 12017.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [5.0, 1.0, 98.0, 100.0]
- centroid/spread: [52.657, 51.271] | rms 40.489
- density_grid(3x3): [[8, 6, 7], [7, 6, 8], [7, 8, 13]]
- edge_len_stats(min,q1,med,q3,max): [1.0, 33.0, 52.0, 70.0, 129.0]
- long_edge_ratio: 0.246
- direction_hist(8 sectors): [10, 8, 8, 13, 8, 8, 5, 9]
- nn_median_dist: 6.083

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:12:48,971 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:13:15,460 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "potentially multimodal",
    "deceptiveness": "low to moderate",
    "edge_length_variability": "high (range 1.0 to 129.0)",
    "long_edge_prevalence": "24.6% of edges are long (above Q3)"
  },
  "population_state": {
    "coverage": "good (diversity 0.815)",
    "spread": "uneven (density_grid shows concentration in bottom-right cell)",
    "convergence_status": "early stage (0.0 convergence)",
    "elite_influence": "minimal (0 elites, only 1 fixed node)"
  },
  "difficult_regions": {
    "long_edge_corridors": [
      "edges in top-left quadrant (low density cell with long median edge lengths)",
      "north-south connections between high-density clusters"
    ],
    "low_density_zones": [
      "top-center grid cell (only 6 nodes)",
      "left-center grid cell (7 nodes)"
    ]
  },
  "opportunity_regions": {
    "high_density_clusters": [
      "bottom-right grid cell (13 nodes)",
      "top-right and center-right cells (8 nodes each)"
    ],
    "short_edge_concentrations": [
      "regions with median edge length <52 units",
      "sectors with dominant directionality (sectors 4 and 8)"
    ]
  },
  "evolution_phase": "exploration-dominant",
  "evolution_direction": [
    "Prioritize 2-opt mutations in high-density clusters",
    "Test bridge-building crossovers for long-edge corridors",
    "Introduce directed local search in opportunity regions",
    "Maintain diversity through restart mechanisms in low-density zones"
  ]
}
```
2025-06-26 20:13:15,460 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:13:15,460 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low to moderate', 'edge_length_variability': 'high (range 1.0 to 129.0)', 'long_edge_prevalence': '24.6% of edges are long (above Q3)'}, 'population_state': {'coverage': 'good (diversity 0.815)', 'spread': 'uneven (density_grid shows concentration in bottom-right cell)', 'convergence_status': 'early stage (0.0 convergence)', 'elite_influence': 'minimal (0 elites, only 1 fixed node)'}, 'difficult_regions': {'long_edge_corridors': ['edges in top-left quadrant (low density cell with long median edge lengths)', 'north-south connections between high-density clusters'], 'low_density_zones': ['top-center grid cell (only 6 nodes)', 'left-center grid cell (7 nodes)']}, 'opportunity_regions': {'high_density_clusters': ['bottom-right grid cell (13 nodes)', 'top-right and center-right cells (8 nodes each)'], 'short_edge_concentrations': ['regions with median edge length <52 units', 'sectors with dominant directionality (sectors 4 and 8)']}, 'evolution_phase': 'exploration-dominant', 'evolution_direction': ['Prioritize 2-opt mutations in high-density clusters', 'Test bridge-building crossovers for long-edge corridors', 'Introduce directed local search in opportunity regions', 'Maintain diversity through restart mechanisms in low-density zones']}
2025-06-26 20:13:15,460 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:13:15,460 - __main__ - INFO - 分析阶段完成
2025-06-26 20:13:15,460 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low to moderate', 'edge_length_variability': 'high (range 1.0 to 129.0)', 'long_edge_prevalence': '24.6% of edges are long (above Q3)'}, 'population_state': {'coverage': 'good (diversity 0.815)', 'spread': 'uneven (density_grid shows concentration in bottom-right cell)', 'convergence_status': 'early stage (0.0 convergence)', 'elite_influence': 'minimal (0 elites, only 1 fixed node)'}, 'difficult_regions': {'long_edge_corridors': ['edges in top-left quadrant (low density cell with long median edge lengths)', 'north-south connections between high-density clusters'], 'low_density_zones': ['top-center grid cell (only 6 nodes)', 'left-center grid cell (7 nodes)']}, 'opportunity_regions': {'high_density_clusters': ['bottom-right grid cell (13 nodes)', 'top-right and center-right cells (8 nodes each)'], 'short_edge_concentrations': ['regions with median edge length <52 units', 'sectors with dominant directionality (sectors 4 and 8)']}, 'evolution_phase': 'exploration-dominant', 'evolution_direction': ['Prioritize 2-opt mutations in high-density clusters', 'Test bridge-building crossovers for long-edge corridors', 'Introduce directed local search in opportunity regions', 'Maintain diversity through restart mechanisms in low-density zones']}
2025-06-26 20:13:15,460 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:13:15,460 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:13:15,460 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 3302.0
  • mean_cost: 3483.3
- Search-space: ruggedness moderate, modality potentially multimodal, deceptiveness low to moderate
- Evolution phase: exploration-dominant
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_zones
- Opportunity regions (# 2): high_density_cluster, short_edge_concentra
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:13:15,460 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:13:15,460 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:13:25,804 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration, with top 60% exploring and bottom 40% exploiting."
}
```
2025-06-26 20:13:25,804 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:13:25,804 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:13:25,804 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration, with top 60% exploring and bottom 40% exploiting."
}
```
2025-06-26 20:13:25,804 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:13:25,804 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:13:25,804 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration, with top 60% exploring and bottom 40% exploiting."
}
```
2025-06-26 20:13:25,804 - __main__ - INFO - 开始进化阶段
2025-06-26 20:13:25,804 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:13:25,804 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:13:25,807 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3480.0 (rank 40.0%)
- Population diversity: 0.5550724637681159
- Evolution phase: exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:13:25,807 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:13:25,807 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:13:42,265 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Leverage modular jumps to explore sparse cells near opportunities"
}
```
2025-06-26 20:13:42,274 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:13:42,274 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:13:42,274 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:13:42,275 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:13:42,275 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:13:42,275 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3480.0 (rank 40.0%)
- Population diversity: 0.5550724637681159
- Evolution phase: exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:13:42,275 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:13:42,275 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:13:57,481 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Sequential traversal with clustered jumps to balance exploration"
}
```
2025-06-26 20:13:57,481 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:13:57,481 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3480.0, 路径: [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11]
2025-06-26 20:13:57,483 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}
2025-06-26 20:13:57,483 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:13:57,483 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:13:57,483 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3480.0 (rank 40.0%)
- Population diversity: 0.5550724637681159
- Evolution phase: exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:13:57,483 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:13:57,483 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:14:13,789 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Sequential clustering to explore sparse cells near opportunities"
}
```
2025-06-26 20:14:13,789 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:14:13,803 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:14:13,803 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:14:13,803 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:14:13,803 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:14:13,804 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3480.0 (rank 40.0%)
- Population diversity: 0.5550724637681159
- Evolution phase: exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:14:13,804 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:14:13,804 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:14:29,628 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Sequential jumps to balance exploration and cost"
}
```
2025-06-26 20:14:29,635 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:14:29,636 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3480.0, 路径: [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11]
2025-06-26 20:14:29,636 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}
2025-06-26 20:14:29,637 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:14:29,638 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:14:29,638 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3508.0 (rank 60.0%)
- Population diversity: 0.5550724637681159
- Evolution phase: exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:14:29,638 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:14:29,638 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:14:45,596 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Reverse initial segment to explore sparse cells near opportunities"
}
```
2025-06-26 20:14:45,596 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:14:45,596 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3316.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]
2025-06-26 20:14:45,596 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3316.0}
2025-06-26 20:14:45,596 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:14:45,596 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:14:45,596 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3410.0 (rank 20.0%)
- Population diversity: 0.5550724637681159
- Evolution phase: exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_conc
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:14:45,596 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:14:45,596 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:15:01,656 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "high_density_cl, short_edge_conc",
  "strategy_comment": "Leverage modular jumps to explore sparse cells near opportunities"
}
```
2025-06-26 20:15:01,656 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:15:01,656 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:15:01,656 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:15:01,656 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 20:15:01,656 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:15:01,656 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:15:01,668 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 3272.0
2025-06-26 20:15:02,169 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:15:02,170 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680, 675]
2025-06-26 20:15:02,170 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64)]
2025-06-26 20:15:02,176 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:15:02,176 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3316.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([35, 53, 36, 37,  3, 27, 45, 17, 59, 42, 67, 32, 69,  2, 25, 29, 47,
        9,  7,  8, 39, 26, 14, 54, 28, 65, 63, 50, 18, 48, 41, 22, 30, 33,
       21, 44, 23,  1, 34, 51,  0, 52, 13,  6, 40, 43, 24, 38,  5, 20, 55,
       15, 58, 12, 16, 46, 62, 19, 57, 60, 68, 61, 31, 56, 11, 66,  4, 49,
       10, 64]), 'cur_cost': 3272.0}, {'tour': array([28, 69, 10, 51,  7, 25,  8, 41, 62,  2, 45, 21, 35, 57,  5, 43, 47,
       48, 44, 34, 58, 67, 20, 24, 16, 46, 60, 39, 22, 30, 54, 32,  9, 40,
       50, 56, 36, 38, 53, 29, 66, 49, 61, 31, 17, 42, 55, 11, 19, 14, 68,
       52, 63, 27, 13, 26,  4,  1, 64, 18,  0,  3, 65, 37, 12, 23,  6, 59,
       15, 33]), 'cur_cost': 3467.0}, {'tour': array([38, 51, 52, 37, 43, 17, 34, 69, 11,  4, 35, 65,  6, 56, 60, 23, 29,
        3, 21, 10, 40, 19, 48,  2, 27, 53, 66,  0, 62, 12, 15, 59, 31,  7,
       50, 32, 63, 26, 47, 14, 42, 68, 57, 33, 36, 55, 41,  9, 64, 44, 39,
       58, 67, 45, 18,  8, 24, 13, 49, 46, 20, 22,  5, 25, 28, 54,  1, 30,
       16, 61]), 'cur_cost': 3302.0}, {'tour': array([48,  9, 28, 27, 40, 39,  1, 13, 21, 63, 46, 25,  5,  8, 17, 24, 67,
       44, 41, 62, 52,  6, 50, 16, 36, 69, 38, 54, 14, 49, 51, 47, 23, 64,
       12, 55, 26, 29, 32, 10, 30, 33, 59, 20, 65, 45,  2, 35, 19,  3, 22,
       53, 58, 68,  7, 56, 60, 61, 57, 31, 18, 66, 34, 43, 15, 37, 42,  4,
        0, 11]), 'cur_cost': 3816.0}]
2025-06-26 20:15:02,178 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:15:02,178 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 20, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 20, 'cache_hits': 0, 'similarity_calculations': 126, 'cache_hit_rate': 0.0, 'cache_size': 126}}
2025-06-26 20:15:02,178 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 20:15:02,178 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:15:02,179 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:15:02,179 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:15:02,179 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 3409.0
2025-06-26 20:15:02,680 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:15:02,680 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680, 675]
2025-06-26 20:15:02,680 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64)]
2025-06-26 20:15:02,690 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:15:02,690 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3316.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([35, 53, 36, 37,  3, 27, 45, 17, 59, 42, 67, 32, 69,  2, 25, 29, 47,
        9,  7,  8, 39, 26, 14, 54, 28, 65, 63, 50, 18, 48, 41, 22, 30, 33,
       21, 44, 23,  1, 34, 51,  0, 52, 13,  6, 40, 43, 24, 38,  5, 20, 55,
       15, 58, 12, 16, 46, 62, 19, 57, 60, 68, 61, 31, 56, 11, 66,  4, 49,
       10, 64]), 'cur_cost': 3272.0}, {'tour': array([37, 65, 64,  3,  5, 41, 31, 48, 22, 40, 55, 47, 61, 68, 30, 24, 19,
       39, 66, 44,  1,  4, 54, 28, 14, 51, 32, 46, 49,  7, 43, 67, 27, 11,
       45,  9, 57, 26, 10, 21, 23, 52, 12,  2, 15, 56, 17, 29, 18, 69, 50,
        0, 63, 33, 36,  6, 42, 20, 25, 53, 34,  8, 38, 62, 16, 60, 59, 13,
       58, 35]), 'cur_cost': 3409.0}, {'tour': array([38, 51, 52, 37, 43, 17, 34, 69, 11,  4, 35, 65,  6, 56, 60, 23, 29,
        3, 21, 10, 40, 19, 48,  2, 27, 53, 66,  0, 62, 12, 15, 59, 31,  7,
       50, 32, 63, 26, 47, 14, 42, 68, 57, 33, 36, 55, 41,  9, 64, 44, 39,
       58, 67, 45, 18,  8, 24, 13, 49, 46, 20, 22,  5, 25, 28, 54,  1, 30,
       16, 61]), 'cur_cost': 3302.0}, {'tour': array([48,  9, 28, 27, 40, 39,  1, 13, 21, 63, 46, 25,  5,  8, 17, 24, 67,
       44, 41, 62, 52,  6, 50, 16, 36, 69, 38, 54, 14, 49, 51, 47, 23, 64,
       12, 55, 26, 29, 32, 10, 30, 33, 59, 20, 65, 45,  2, 35, 19,  3, 22,
       53, 58, 68,  7, 56, 60, 61, 57, 31, 18, 66, 34, 43, 15, 37, 42,  4,
        0, 11]), 'cur_cost': 3816.0}]
2025-06-26 20:15:02,693 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:15:02,693 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 21, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 21, 'cache_hits': 0, 'similarity_calculations': 130, 'cache_hit_rate': 0.0, 'cache_size': 130}}
2025-06-26 20:15:02,693 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:15:02,693 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:15:02,693 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:15:02,693 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:15:02,693 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 3460.0
2025-06-26 20:15:03,198 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:15:03,198 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680, 675]
2025-06-26 20:15:03,198 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64)]
2025-06-26 20:15:03,207 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:15:03,207 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3316.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([35, 53, 36, 37,  3, 27, 45, 17, 59, 42, 67, 32, 69,  2, 25, 29, 47,
        9,  7,  8, 39, 26, 14, 54, 28, 65, 63, 50, 18, 48, 41, 22, 30, 33,
       21, 44, 23,  1, 34, 51,  0, 52, 13,  6, 40, 43, 24, 38,  5, 20, 55,
       15, 58, 12, 16, 46, 62, 19, 57, 60, 68, 61, 31, 56, 11, 66,  4, 49,
       10, 64]), 'cur_cost': 3272.0}, {'tour': array([37, 65, 64,  3,  5, 41, 31, 48, 22, 40, 55, 47, 61, 68, 30, 24, 19,
       39, 66, 44,  1,  4, 54, 28, 14, 51, 32, 46, 49,  7, 43, 67, 27, 11,
       45,  9, 57, 26, 10, 21, 23, 52, 12,  2, 15, 56, 17, 29, 18, 69, 50,
        0, 63, 33, 36,  6, 42, 20, 25, 53, 34,  8, 38, 62, 16, 60, 59, 13,
       58, 35]), 'cur_cost': 3409.0}, {'tour': array([ 1, 13, 63,  6, 67, 10, 64, 36,  9, 69, 21, 37, 17, 65, 18, 55, 14,
        8, 24, 43, 29, 42, 40, 45, 38, 56, 44, 47, 66, 16, 30,  7, 51, 35,
       34, 52, 25,  5, 22,  2, 50, 28, 19, 32, 59, 26, 68, 60,  4, 20, 31,
       62, 27,  0, 11, 58, 33, 61, 48, 54, 49,  3, 23, 12, 15, 57, 39, 53,
       46, 41]), 'cur_cost': 3460.0}, {'tour': array([48,  9, 28, 27, 40, 39,  1, 13, 21, 63, 46, 25,  5,  8, 17, 24, 67,
       44, 41, 62, 52,  6, 50, 16, 36, 69, 38, 54, 14, 49, 51, 47, 23, 64,
       12, 55, 26, 29, 32, 10, 30, 33, 59, 20, 65, 45,  2, 35, 19,  3, 22,
       53, 58, 68,  7, 56, 60, 61, 57, 31, 18, 66, 34, 43, 15, 37, 42,  4,
        0, 11]), 'cur_cost': 3816.0}]
2025-06-26 20:15:03,208 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 20:15:03,209 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 22, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 22, 'cache_hits': 0, 'similarity_calculations': 135, 'cache_hit_rate': 0.0, 'cache_size': 135}}
2025-06-26 20:15:03,209 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:15:03,209 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:15:03,209 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:15:03,209 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:15:03,209 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 3700.0
2025-06-26 20:15:03,711 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:15:03,711 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680, 675]
2025-06-26 20:15:03,711 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64)]
2025-06-26 20:15:03,718 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:15:03,718 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3316.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([35, 53, 36, 37,  3, 27, 45, 17, 59, 42, 67, 32, 69,  2, 25, 29, 47,
        9,  7,  8, 39, 26, 14, 54, 28, 65, 63, 50, 18, 48, 41, 22, 30, 33,
       21, 44, 23,  1, 34, 51,  0, 52, 13,  6, 40, 43, 24, 38,  5, 20, 55,
       15, 58, 12, 16, 46, 62, 19, 57, 60, 68, 61, 31, 56, 11, 66,  4, 49,
       10, 64]), 'cur_cost': 3272.0}, {'tour': array([37, 65, 64,  3,  5, 41, 31, 48, 22, 40, 55, 47, 61, 68, 30, 24, 19,
       39, 66, 44,  1,  4, 54, 28, 14, 51, 32, 46, 49,  7, 43, 67, 27, 11,
       45,  9, 57, 26, 10, 21, 23, 52, 12,  2, 15, 56, 17, 29, 18, 69, 50,
        0, 63, 33, 36,  6, 42, 20, 25, 53, 34,  8, 38, 62, 16, 60, 59, 13,
       58, 35]), 'cur_cost': 3409.0}, {'tour': array([ 1, 13, 63,  6, 67, 10, 64, 36,  9, 69, 21, 37, 17, 65, 18, 55, 14,
        8, 24, 43, 29, 42, 40, 45, 38, 56, 44, 47, 66, 16, 30,  7, 51, 35,
       34, 52, 25,  5, 22,  2, 50, 28, 19, 32, 59, 26, 68, 60,  4, 20, 31,
       62, 27,  0, 11, 58, 33, 61, 48, 54, 49,  3, 23, 12, 15, 57, 39, 53,
       46, 41]), 'cur_cost': 3460.0}, {'tour': array([ 5, 61, 67, 20, 11, 49, 51,  2, 29, 66, 38, 68, 10, 54, 33, 15, 57,
       63, 28, 34, 39, 62,  7, 32, 26, 42, 60, 27, 23, 50, 53, 44, 41,  0,
       36, 43, 21, 40, 46,  4, 19,  1, 56, 58,  9, 69, 47, 13, 59, 25, 64,
       35, 48, 52, 31, 37, 14,  6, 12, 55, 17, 30,  8, 45, 18, 24,  3, 16,
       65, 22]), 'cur_cost': 3700.0}]
2025-06-26 20:15:03,719 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:15:03,719 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 23, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 23, 'cache_hits': 0, 'similarity_calculations': 141, 'cache_hit_rate': 0.0, 'cache_size': 141}}
2025-06-26 20:15:03,719 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:15:03,719 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3316.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}]
2025-06-26 20:15:03,721 - __main__ - INFO - 进化阶段完成
2025-06-26 20:15:03,721 - __main__ - INFO - 开始评估阶段
2025-06-26 20:15:03,721 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:15:03,721 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:15:03,721 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 3302.0 → 3272.0 (Δ 30.0)
- Mean cost: 3483.3 → 3464.1
- Diversity: 0.555 → 0.646 (Δ 0.0913)

#### Elite metrics
- Elite count: 17 → 18
- Best elite cost: 675 → 675 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ 6.33
- Exploit: 4 inds, success 75.0% , avg Δ 38.5

#### Other indicators
- No-change individuals: 2
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [27336.0, 23824.0, 3302.0], diversity [0.8307692307692308, 0.829059829059829, 0.8152380952380953]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:15:03,721 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:15:15,903 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": 6.33,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 38.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy (effective but risks premature convergence)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_intensity": "consider 10-15% increase for explore individuals"
  }
}
```
2025-06-26 20:15:15,933 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:15:15,933 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": 6.33,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 38.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy (effective but risks premature convergence)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_intensity": "consider 10-15% increase for explore individuals"
  }
}
```
2025-06-26 20:15:15,933 - __main__ - INFO - 评估阶段完成
2025-06-26 20:15:15,933 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": 6.33,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 38.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy (effective but risks premature convergence)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_intensity": "consider 10-15% increase for explore individuals"
  }
}
```
2025-06-26 20:15:15,933 - __main__ - INFO - 当前最佳适应度: 3272.0
2025-06-26 20:15:15,939 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\st70_route_1.pkl
2025-06-26 20:15:15,939 - __main__ - INFO - st70 开始进化第 3 代
2025-06-26 20:15:15,939 - __main__ - INFO - 开始分析阶段
2025-06-26 20:15:15,939 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:15:15,958 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 3272.0, 'max': 3700.0, 'mean': 3464.1, 'std': 111.276637260478}, 'diversity': 0.8717460317460317, 'clusters': {'clusters': 7, 'cluster_sizes': [3, 2, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:15:15,959 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 3272.0, 'max': 3700.0, 'mean': 3464.1, 'std': 111.276637260478}, 'diversity_level': 0.8717460317460317, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [3, 2, 1, 1, 1, 1, 1]}, 'coordinates': [[64, 96], [80, 39], [69, 23], [72, 42], [48, 67], [58, 43], [81, 34], [79, 17], [30, 23], [42, 67], [7, 76], [29, 51], [78, 92], [64, 8], [95, 57], [57, 91], [40, 35], [68, 40], [92, 34], [62, 1], [28, 43], [76, 73], [67, 88], [93, 54], [6, 8], [87, 18], [30, 9], [77, 13], [78, 94], [55, 3], [82, 88], [73, 28], [20, 55], [27, 43], [95, 86], [67, 99], [48, 83], [75, 81], [8, 19], [20, 18], [54, 38], [63, 36], [44, 33], [52, 18], [12, 13], [25, 5], [58, 85], [5, 67], [90, 9], [41, 76], [25, 76], [37, 64], [56, 63], [10, 55], [98, 7], [16, 74], [89, 60], [48, 82], [81, 76], [29, 60], [17, 22], [5, 45], [79, 70], [9, 100], [17, 82], [74, 67], [10, 68], [48, 19], [83, 86], [84, 94]], 'distance_matrix': array([[ 0., 59., 73., ..., 79., 21., 20.],
       [59.,  0., 19., ..., 38., 47., 55.],
       [73., 19.,  0., ..., 21., 65., 73.],
       ...,
       [79., 38., 21., ...,  0., 76., 83.],
       [21., 47., 65., ..., 76.,  0.,  8.],
       [20., 55., 73., ..., 83.,  8.,  0.]])}
2025-06-26 20:15:15,959 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:15:15,959 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:15:15,959 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:15:15,961 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:15:15,962 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:15:15,962 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:15:15,963 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:15:15,963 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:15:15,963 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:15:15,969 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:15:15,969 - __main__ - INFO - 精英专家分析报告: {'elite_count': 18, 'elite_common_features': {'common_edges': {'(33, 20)': 0.7222222222222222, '(66, 47)': 0.7222222222222222}, 'common_edge_ratio': 0.02857142857142857}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 2597.0, 'avg_gap': 2788.7666666666664}, 'structure_gap': {'unique_elite_edges': 150, 'unique_pop_edges': 451, 'common_edges': 16}}, 'elite_diversity': {'diversity_score': 0.0756302521008404}}
2025-06-26 20:15:15,970 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:15:15,970 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:15:15,971 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:15:15,971 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 3272.0, mean 3464.1, max 3700.0, std 111.276637260478
- diversity: 0.8717460317460317
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [23824.0, 3302.0, 3272.0] (Δ 10276.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [5.0, 1.0, 98.0, 100.0]
- centroid/spread: [52.657, 51.271] | rms 40.489
- density_grid(3x3): [[8, 6, 7], [7, 6, 8], [7, 8, 13]]
- edge_len_stats(min,q1,med,q3,max): [1.0, 33.0, 52.0, 70.0, 129.0]
- long_edge_ratio: 0.246
- direction_hist(8 sectors): [10, 8, 8, 13, 8, 8, 5, 9]
- nn_median_dist: 6.083

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:15:15,973 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:15:45,971 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate (wide range of edge lengths from 1.0 to 129.0, median 52.0)",
    "modality": "Potential multi-modality (direction histogram shows uneven distribution across sectors)",
    "deceptiveness": "Possible mild deceptiveness (long_edge_ratio 0.246 suggests some misleading long edges)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (diversity 0.87, density_grid shows reasonable spread)",
    "convergence": "Early exploration phase (convergence 0.0, recent best costs still improving)",
    "quality_spread": "Wide cost range (min 3272 to max 3700, std 111.28)"
  },
  "difficult_regions": [
    {
      "type": "Long-edge corridor",
      "evidence": "Edge lengths in Q3 (70.0) to max (129.0) range",
      "nodes": "Nodes connected by edges >100 units (check NE/SW directions from direction_hist)"
    },
    {
      "type": "Low-density zone",
      "evidence": "Sparse cells in density_grid (e.g. [1][1] with count 6)",
      "nodes": "Nodes near centroid (52.657,51.271) with RMS spread 40.489"
    }
  ],
  "opportunity_regions": [
    {
      "type": "High-density cluster",
      "evidence": "Dense cell [2][2] with count 13",
      "nodes": "Nodes in SE quadrant (direction_hist sector 4 has 13 edges)"
    },
    {
      "type": "Short-edge neighborhood",
      "evidence": "Q1 edge length 33.0, min 1.0",
      "nodes": "Nodes with median NN distance 6.083 (potential local optima)"
    }
  ],
  "evolution_phase": "Early exploration (iteration 2/5)",
  "evolution_direction": [
    "Prioritize exploitation in high-density SE quadrant (sector 4)",
    "Apply edge-recombination operators for short-edge neighborhoods",
    "Use mutation operators targeting long-edge reduction",
    "Maintain diversity through spatial niching in low-density zones"
  ]
}
```
2025-06-26 20:15:45,982 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:15:45,982 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate (wide range of edge lengths from 1.0 to 129.0, median 52.0)', 'modality': 'Potential multi-modality (direction histogram shows uneven distribution across sectors)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio 0.246 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.87, density_grid shows reasonable spread)', 'convergence': 'Early exploration phase (convergence 0.0, recent best costs still improving)', 'quality_spread': 'Wide cost range (min 3272 to max 3700, std 111.28)'}, 'difficult_regions': [{'type': 'Long-edge corridor', 'evidence': 'Edge lengths in Q3 (70.0) to max (129.0) range', 'nodes': 'Nodes connected by edges >100 units (check NE/SW directions from direction_hist)'}, {'type': 'Low-density zone', 'evidence': 'Sparse cells in density_grid (e.g. [1][1] with count 6)', 'nodes': 'Nodes near centroid (52.657,51.271) with RMS spread 40.489'}], 'opportunity_regions': [{'type': 'High-density cluster', 'evidence': 'Dense cell [2][2] with count 13', 'nodes': 'Nodes in SE quadrant (direction_hist sector 4 has 13 edges)'}, {'type': 'Short-edge neighborhood', 'evidence': 'Q1 edge length 33.0, min 1.0', 'nodes': 'Nodes with median NN distance 6.083 (potential local optima)'}], 'evolution_phase': 'Early exploration (iteration 2/5)', 'evolution_direction': ['Prioritize exploitation in high-density SE quadrant (sector 4)', 'Apply edge-recombination operators for short-edge neighborhoods', 'Use mutation operators targeting long-edge reduction', 'Maintain diversity through spatial niching in low-density zones']}
2025-06-26 20:15:45,982 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:15:45,982 - __main__ - INFO - 分析阶段完成
2025-06-26 20:15:45,982 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate (wide range of edge lengths from 1.0 to 129.0, median 52.0)', 'modality': 'Potential multi-modality (direction histogram shows uneven distribution across sectors)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio 0.246 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.87, density_grid shows reasonable spread)', 'convergence': 'Early exploration phase (convergence 0.0, recent best costs still improving)', 'quality_spread': 'Wide cost range (min 3272 to max 3700, std 111.28)'}, 'difficult_regions': [{'type': 'Long-edge corridor', 'evidence': 'Edge lengths in Q3 (70.0) to max (129.0) range', 'nodes': 'Nodes connected by edges >100 units (check NE/SW directions from direction_hist)'}, {'type': 'Low-density zone', 'evidence': 'Sparse cells in density_grid (e.g. [1][1] with count 6)', 'nodes': 'Nodes near centroid (52.657,51.271) with RMS spread 40.489'}], 'opportunity_regions': [{'type': 'High-density cluster', 'evidence': 'Dense cell [2][2] with count 13', 'nodes': 'Nodes in SE quadrant (direction_hist sector 4 has 13 edges)'}, {'type': 'Short-edge neighborhood', 'evidence': 'Q1 edge length 33.0, min 1.0', 'nodes': 'Nodes with median NN distance 6.083 (potential local optima)'}], 'evolution_phase': 'Early exploration (iteration 2/5)', 'evolution_direction': ['Prioritize exploitation in high-density SE quadrant (sector 4)', 'Apply edge-recombination operators for short-edge neighborhoods', 'Use mutation operators targeting long-edge reduction', 'Maintain diversity through spatial niching in low-density zones']}
2025-06-26 20:15:45,984 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:15:45,984 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:15:45,984 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 3272.0
  • mean_cost: 3464.1
- Search-space: ruggedness Moderate (wide range of edge lengths from 1.0 to 129.0, median 52.0), modality Potential multi-modality (direction histogram shows uneven distribution across sectors), deceptiveness Possible mild deceptiveness (long_edge_ratio 0.246 suggests some misleading long edges)
- Evolution phase: Early exploration (iteration 2/5)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'Long-edge , {'type': 'Low-densit
- Opportunity regions (# 2): {'type': 'High-densi, {'type': 'Short-edge
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:15:45,985 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:15:45,985 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:15:56,054 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify; bottom 40% exploit current best."
}
```
2025-06-26 20:15:56,054 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:15:56,054 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:15:56,056 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify; bottom 40% exploit current best."
}
```
2025-06-26 20:15:56,056 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:15:56,056 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:15:56,056 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify; bottom 40% exploit current best."
}
```
2025-06-26 20:15:56,056 - __main__ - INFO - 开始进化阶段
2025-06-26 20:15:56,056 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:15:56,056 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:15:56,056 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3508.0 (rank 60.0%)
- Population diversity: 0.6463768115942029
- Evolution phase: Early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:15:56,058 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:15:56,058 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:16:12,859 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-opportunity and low-density regions",
  "strategy_comment": "Structured leapfrog pattern to explore sparse cells"
}
```
2025-06-26 20:16:12,859 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:16:12,859 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:16:12,860 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:16:12,860 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:16:12,861 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:16:12,861 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3480.0 (rank 40.0%)
- Population diversity: 0.6463768115942029
- Evolution phase: Early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:16:12,861 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:16:12,862 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:16:28,883 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-opportunity and sparse cells",
  "strategy_comment": "Structured leapfrog to explore sparse regions"
}
```
2025-06-26 20:16:28,883 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:16:28,883 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:16:28,883 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:16:28,883 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:16:28,883 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:16:28,883 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3508.0 (rank 50.0%)
- Population diversity: 0.6463768115942029
- Evolution phase: Early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:16:28,883 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:16:28,883 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:16:44,427 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-opportunity and low-density regions",
  "strategy_comment": "Structured leapfrog pattern to explore sparse cells"
}
```
2025-06-26 20:16:44,427 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:16:44,427 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:16:44,427 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:16:44,427 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:16:44,427 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:16:44,427 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3480.0 (rank 40.0%)
- Population diversity: 0.6463768115942029
- Evolution phase: Early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:16:44,427 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:16:44,427 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:17:01,374 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "Low-density adjacent to high-opportunity",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 20:17:01,387 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:17:01,388 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:17:01,388 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:17:01,389 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:17:01,389 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:17:01,389 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3316.0 (rank 10.0%)
- Population diversity: 0.5695652173913044
- Evolution phase: Early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:17:01,389 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:17:01,389 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:17:17,070 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69],
  "targeted_regions": "High-opportunity and low-density adjacent cells",
  "strategy_comment": "Balanced exploration with cost-efficient routing"
}
```
2025-06-26 20:17:17,070 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:17:17,070 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3410.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]
2025-06-26 20:17:17,070 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}
2025-06-26 20:17:17,070 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:17:17,070 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:17:17,070 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3508.0 (rank 40.0%)
- Population diversity: 0.572463768115942
- Evolution phase: Early exploration (iteration 2/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Short
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:17:17,070 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:17:17,070 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:17:33,344 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-opportunity and low-density regions",
  "strategy_comment": "Balanced exploration with modular jumps"
}
```
2025-06-26 20:17:33,344 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:17:33,344 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:17:33,347 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:17:33,347 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 20:17:33,347 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:17:33,347 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:17:33,347 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 3786.0
2025-06-26 20:17:33,851 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:17:33,851 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680]
2025-06-26 20:17:33,851 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:17:33,857 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:17:33,857 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([63, 37, 46,  0, 27, 55,  3, 69, 20, 64, 62,  8, 36,  4, 59, 32, 15,
       14, 50, 41, 45, 53, 68, 57,  7, 66, 25, 44, 38, 33,  2, 12, 39, 34,
       54, 23,  9, 11, 58, 21, 24, 51, 49, 28, 47, 48, 67, 17, 61, 65, 30,
       31, 18, 13, 22,  6, 26, 35,  5, 16, 52, 19, 43, 42, 10, 60,  1, 40,
       56, 29]), 'cur_cost': 3786.0}, {'tour': array([37, 65, 64,  3,  5, 41, 31, 48, 22, 40, 55, 47, 61, 68, 30, 24, 19,
       39, 66, 44,  1,  4, 54, 28, 14, 51, 32, 46, 49,  7, 43, 67, 27, 11,
       45,  9, 57, 26, 10, 21, 23, 52, 12,  2, 15, 56, 17, 29, 18, 69, 50,
        0, 63, 33, 36,  6, 42, 20, 25, 53, 34,  8, 38, 62, 16, 60, 59, 13,
       58, 35]), 'cur_cost': 3409.0}, {'tour': array([ 1, 13, 63,  6, 67, 10, 64, 36,  9, 69, 21, 37, 17, 65, 18, 55, 14,
        8, 24, 43, 29, 42, 40, 45, 38, 56, 44, 47, 66, 16, 30,  7, 51, 35,
       34, 52, 25,  5, 22,  2, 50, 28, 19, 32, 59, 26, 68, 60,  4, 20, 31,
       62, 27,  0, 11, 58, 33, 61, 48, 54, 49,  3, 23, 12, 15, 57, 39, 53,
       46, 41]), 'cur_cost': 3460.0}, {'tour': array([ 5, 61, 67, 20, 11, 49, 51,  2, 29, 66, 38, 68, 10, 54, 33, 15, 57,
       63, 28, 34, 39, 62,  7, 32, 26, 42, 60, 27, 23, 50, 53, 44, 41,  0,
       36, 43, 21, 40, 46,  4, 19,  1, 56, 58,  9, 69, 47, 13, 59, 25, 64,
       35, 48, 52, 31, 37, 14,  6, 12, 55, 17, 30,  8, 45, 18, 24,  3, 16,
       65, 22]), 'cur_cost': 3700.0}]
2025-06-26 20:17:33,859 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:17:33,859 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 24, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 24, 'cache_hits': 0, 'similarity_calculations': 148, 'cache_hit_rate': 0.0, 'cache_size': 148}}
2025-06-26 20:17:33,860 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 20:17:33,861 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:17:33,861 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:17:33,861 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:17:33,861 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 3746.0
2025-06-26 20:17:34,363 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:17:34,363 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680]
2025-06-26 20:17:34,364 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:17:34,370 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:17:34,370 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([63, 37, 46,  0, 27, 55,  3, 69, 20, 64, 62,  8, 36,  4, 59, 32, 15,
       14, 50, 41, 45, 53, 68, 57,  7, 66, 25, 44, 38, 33,  2, 12, 39, 34,
       54, 23,  9, 11, 58, 21, 24, 51, 49, 28, 47, 48, 67, 17, 61, 65, 30,
       31, 18, 13, 22,  6, 26, 35,  5, 16, 52, 19, 43, 42, 10, 60,  1, 40,
       56, 29]), 'cur_cost': 3786.0}, {'tour': array([53, 67, 50, 52, 47, 57, 15, 49, 17, 43, 35, 42, 23, 21,  7, 19, 28,
       45, 27, 33, 39, 61,  1,  9,  8, 38, 58, 12, 60, 29, 31, 51,  5, 18,
        4, 30, 62, 41, 13, 10, 44, 25, 56, 16, 69, 63, 66,  2, 68, 64, 34,
       36, 54,  3, 32, 22, 59, 14, 40, 24,  0,  6, 46, 65, 11, 26, 55, 20,
       37, 48]), 'cur_cost': 3746.0}, {'tour': array([ 1, 13, 63,  6, 67, 10, 64, 36,  9, 69, 21, 37, 17, 65, 18, 55, 14,
        8, 24, 43, 29, 42, 40, 45, 38, 56, 44, 47, 66, 16, 30,  7, 51, 35,
       34, 52, 25,  5, 22,  2, 50, 28, 19, 32, 59, 26, 68, 60,  4, 20, 31,
       62, 27,  0, 11, 58, 33, 61, 48, 54, 49,  3, 23, 12, 15, 57, 39, 53,
       46, 41]), 'cur_cost': 3460.0}, {'tour': array([ 5, 61, 67, 20, 11, 49, 51,  2, 29, 66, 38, 68, 10, 54, 33, 15, 57,
       63, 28, 34, 39, 62,  7, 32, 26, 42, 60, 27, 23, 50, 53, 44, 41,  0,
       36, 43, 21, 40, 46,  4, 19,  1, 56, 58,  9, 69, 47, 13, 59, 25, 64,
       35, 48, 52, 31, 37, 14,  6, 12, 55, 17, 30,  8, 45, 18, 24,  3, 16,
       65, 22]), 'cur_cost': 3700.0}]
2025-06-26 20:17:34,372 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:17:34,372 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 25, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 25, 'cache_hits': 0, 'similarity_calculations': 156, 'cache_hit_rate': 0.0, 'cache_size': 156}}
2025-06-26 20:17:34,372 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:17:34,372 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:17:34,372 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:17:34,372 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:17:34,373 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 3697.0
2025-06-26 20:17:34,875 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:17:34,876 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680]
2025-06-26 20:17:34,876 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:17:34,881 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:17:34,881 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([63, 37, 46,  0, 27, 55,  3, 69, 20, 64, 62,  8, 36,  4, 59, 32, 15,
       14, 50, 41, 45, 53, 68, 57,  7, 66, 25, 44, 38, 33,  2, 12, 39, 34,
       54, 23,  9, 11, 58, 21, 24, 51, 49, 28, 47, 48, 67, 17, 61, 65, 30,
       31, 18, 13, 22,  6, 26, 35,  5, 16, 52, 19, 43, 42, 10, 60,  1, 40,
       56, 29]), 'cur_cost': 3786.0}, {'tour': array([53, 67, 50, 52, 47, 57, 15, 49, 17, 43, 35, 42, 23, 21,  7, 19, 28,
       45, 27, 33, 39, 61,  1,  9,  8, 38, 58, 12, 60, 29, 31, 51,  5, 18,
        4, 30, 62, 41, 13, 10, 44, 25, 56, 16, 69, 63, 66,  2, 68, 64, 34,
       36, 54,  3, 32, 22, 59, 14, 40, 24,  0,  6, 46, 65, 11, 26, 55, 20,
       37, 48]), 'cur_cost': 3746.0}, {'tour': array([67, 38, 42,  3, 69, 17, 10, 36, 44, 12, 31, 23, 60, 58, 26, 40, 39,
       27, 43, 50, 49, 46,  2, 34, 22, 62, 29,  4, 51, 11, 63, 55, 45,  7,
       68, 59, 32, 65, 33, 35, 53, 54, 19, 41,  1, 13, 52,  5, 30, 56, 61,
       25, 24, 64, 18, 14,  8, 37, 20, 15, 16, 21,  6,  9, 28,  0, 48, 66,
       47, 57]), 'cur_cost': 3697.0}, {'tour': array([ 5, 61, 67, 20, 11, 49, 51,  2, 29, 66, 38, 68, 10, 54, 33, 15, 57,
       63, 28, 34, 39, 62,  7, 32, 26, 42, 60, 27, 23, 50, 53, 44, 41,  0,
       36, 43, 21, 40, 46,  4, 19,  1, 56, 58,  9, 69, 47, 13, 59, 25, 64,
       35, 48, 52, 31, 37, 14,  6, 12, 55, 17, 30,  8, 45, 18, 24,  3, 16,
       65, 22]), 'cur_cost': 3700.0}]
2025-06-26 20:17:34,883 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:17:34,884 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 26, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 26, 'cache_hits': 0, 'similarity_calculations': 165, 'cache_hit_rate': 0.0, 'cache_size': 165}}
2025-06-26 20:17:34,884 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 20:17:34,884 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:17:34,884 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:17:34,884 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:17:34,885 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 3459.0
2025-06-26 20:17:35,386 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:17:35,386 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680]
2025-06-26 20:17:35,386 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:17:35,392 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:17:35,392 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([63, 37, 46,  0, 27, 55,  3, 69, 20, 64, 62,  8, 36,  4, 59, 32, 15,
       14, 50, 41, 45, 53, 68, 57,  7, 66, 25, 44, 38, 33,  2, 12, 39, 34,
       54, 23,  9, 11, 58, 21, 24, 51, 49, 28, 47, 48, 67, 17, 61, 65, 30,
       31, 18, 13, 22,  6, 26, 35,  5, 16, 52, 19, 43, 42, 10, 60,  1, 40,
       56, 29]), 'cur_cost': 3786.0}, {'tour': array([53, 67, 50, 52, 47, 57, 15, 49, 17, 43, 35, 42, 23, 21,  7, 19, 28,
       45, 27, 33, 39, 61,  1,  9,  8, 38, 58, 12, 60, 29, 31, 51,  5, 18,
        4, 30, 62, 41, 13, 10, 44, 25, 56, 16, 69, 63, 66,  2, 68, 64, 34,
       36, 54,  3, 32, 22, 59, 14, 40, 24,  0,  6, 46, 65, 11, 26, 55, 20,
       37, 48]), 'cur_cost': 3746.0}, {'tour': array([67, 38, 42,  3, 69, 17, 10, 36, 44, 12, 31, 23, 60, 58, 26, 40, 39,
       27, 43, 50, 49, 46,  2, 34, 22, 62, 29,  4, 51, 11, 63, 55, 45,  7,
       68, 59, 32, 65, 33, 35, 53, 54, 19, 41,  1, 13, 52,  5, 30, 56, 61,
       25, 24, 64, 18, 14,  8, 37, 20, 15, 16, 21,  6,  9, 28,  0, 48, 66,
       47, 57]), 'cur_cost': 3697.0}, {'tour': array([ 8, 42, 69, 29, 43, 51, 50, 22, 18, 32, 57, 38, 40, 48, 13, 37, 26,
       45, 21, 59, 68, 14,  9, 36, 63,  1, 19, 44,  7, 46, 49, 66, 34, 55,
       60, 35,  0, 33, 47, 24, 27, 41, 61, 31, 23, 52,  6, 28, 25, 39, 67,
       16,  2, 15, 56,  3, 54, 17, 11, 53, 20, 58,  5, 30, 65,  4, 62, 10,
       12, 64]), 'cur_cost': 3459.0}]
2025-06-26 20:17:35,393 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:17:35,393 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 27, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 27, 'cache_hits': 0, 'similarity_calculations': 175, 'cache_hit_rate': 0.0, 'cache_size': 175}}
2025-06-26 20:17:35,393 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:17:35,393 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}]
2025-06-26 20:17:35,395 - __main__ - INFO - 进化阶段完成
2025-06-26 20:17:35,395 - __main__ - INFO - 开始评估阶段
2025-06-26 20:17:35,395 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:17:35,396 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:17:35,396 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 3272.0 → 3410.0 (Δ -138.0)
- Mean cost: 3464.1 → 3563.8
- Diversity: 0.646 → 0.558 (Δ -0.0884)

#### Elite metrics
- Elite count: 18 → 18
- Best elite cost: 675 → 675 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 0.0% , avg Δ -25.0
- Exploit: 4 inds, success 25.0% , avg Δ -211.75

#### Other indicators
- No-change individuals: 3
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [23824.0, 3302.0, 3272.0], diversity [0.829059829059829, 0.8152380952380953, 0.8717460317460317]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:17:35,396 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:17:47,550 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success, negative avg delta)",
    "exploit": "moderately effective (25% success, significant negative avg delta)"
  },
  "balance_state": "explore ratio too low (current success rate doesn't justify 6:4 ratio)",
  "recommendations": {
    "explore_ratio": 0.4,
    "exploit_ratio": 0.6,
    "diversity_weight": 0.7,
    "mutation_rate": "increase by 15%",
    "elite_preservation": "maintain current count (18)"
  }
}
```
2025-06-26 20:17:47,583 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:17:47,583 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success, negative avg delta)",
    "exploit": "moderately effective (25% success, significant negative avg delta)"
  },
  "balance_state": "explore ratio too low (current success rate doesn't justify 6:4 ratio)",
  "recommendations": {
    "explore_ratio": 0.4,
    "exploit_ratio": 0.6,
    "diversity_weight": 0.7,
    "mutation_rate": "increase by 15%",
    "elite_preservation": "maintain current count (18)"
  }
}
```
2025-06-26 20:17:47,583 - __main__ - INFO - 评估阶段完成
2025-06-26 20:17:47,583 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineffective (0% success, negative avg delta)",
    "exploit": "moderately effective (25% success, significant negative avg delta)"
  },
  "balance_state": "explore ratio too low (current success rate doesn't justify 6:4 ratio)",
  "recommendations": {
    "explore_ratio": 0.4,
    "exploit_ratio": 0.6,
    "diversity_weight": 0.7,
    "mutation_rate": "increase by 15%",
    "elite_preservation": "maintain current count (18)"
  }
}
```
2025-06-26 20:17:47,583 - __main__ - INFO - 当前最佳适应度: 3410.0
2025-06-26 20:17:47,583 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\st70_route_2.pkl
2025-06-26 20:17:47,583 - __main__ - INFO - st70 开始进化第 4 代
2025-06-26 20:17:47,583 - __main__ - INFO - 开始分析阶段
2025-06-26 20:17:47,583 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:17:47,605 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 3410.0, 'max': 3786.0, 'mean': 3563.8, 'std': 122.68561447863397}, 'diversity': 0.7596825396825395, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:17:47,606 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 3410.0, 'max': 3786.0, 'mean': 3563.8, 'std': 122.68561447863397}, 'diversity_level': 0.7596825396825395, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'coordinates': [[64, 96], [80, 39], [69, 23], [72, 42], [48, 67], [58, 43], [81, 34], [79, 17], [30, 23], [42, 67], [7, 76], [29, 51], [78, 92], [64, 8], [95, 57], [57, 91], [40, 35], [68, 40], [92, 34], [62, 1], [28, 43], [76, 73], [67, 88], [93, 54], [6, 8], [87, 18], [30, 9], [77, 13], [78, 94], [55, 3], [82, 88], [73, 28], [20, 55], [27, 43], [95, 86], [67, 99], [48, 83], [75, 81], [8, 19], [20, 18], [54, 38], [63, 36], [44, 33], [52, 18], [12, 13], [25, 5], [58, 85], [5, 67], [90, 9], [41, 76], [25, 76], [37, 64], [56, 63], [10, 55], [98, 7], [16, 74], [89, 60], [48, 82], [81, 76], [29, 60], [17, 22], [5, 45], [79, 70], [9, 100], [17, 82], [74, 67], [10, 68], [48, 19], [83, 86], [84, 94]], 'distance_matrix': array([[ 0., 59., 73., ..., 79., 21., 20.],
       [59.,  0., 19., ..., 38., 47., 55.],
       [73., 19.,  0., ..., 21., 65., 73.],
       ...,
       [79., 38., 21., ...,  0., 76., 83.],
       [21., 47., 65., ..., 76.,  0.,  8.],
       [20., 55., 73., ..., 83.,  8.,  0.]])}
2025-06-26 20:17:47,606 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:17:47,607 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:17:47,607 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:17:47,610 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:17:47,610 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:17:47,610 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:17:47,610 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:17:47,610 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:17:47,610 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:17:47,617 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:17:47,617 - __main__ - INFO - 精英专家分析报告: {'elite_count': 18, 'elite_common_features': {'common_edges': {'(33, 20)': 0.7222222222222222, '(66, 47)': 0.7222222222222222}, 'common_edge_ratio': 0.02857142857142857}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 2735.0, 'avg_gap': 2888.4666666666667}, 'structure_gap': {'unique_elite_edges': 152, 'unique_pop_edges': 394, 'common_edges': 14}}, 'elite_diversity': {'diversity_score': 0.0756302521008404}}
2025-06-26 20:17:47,617 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:17:47,617 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:17:47,619 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:17:47,619 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 3410.0, mean 3563.8, max 3786.0, std 122.68561447863397
- diversity: 0.7596825396825395
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [3302.0, 3272.0, 3410.0] (Δ 15.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [5.0, 1.0, 98.0, 100.0]
- centroid/spread: [52.657, 51.271] | rms 40.489
- density_grid(3x3): [[8, 6, 7], [7, 6, 8], [7, 8, 13]]
- edge_len_stats(min,q1,med,q3,max): [1.0, 33.0, 52.0, 70.0, 129.0]
- long_edge_ratio: 0.246
- direction_hist(8 sectors): [10, 8, 8, 13, 8, 8, 5, 9]
- nn_median_dist: 6.083

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:17:47,619 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:18:17,441 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate",
    "modality": "Potential multimodal due to varied edge lengths and directions",
    "deceptiveness": "Low to moderate, with some long edges that may mislead",
    "edge_len_stats": {
      "min": 1.0,
      "q1": 33.0,
      "med": 52.0,
      "q3": 70.0,
      "max": 129.0
    },
    "long_edge_ratio": 0.246,
    "direction_hist": [10, 8, 8, 13, 8, 8, 5, 9]
  },
  "population_state": {
    "coverage": "Good, as indicated by high diversity (0.76) and density grid",
    "convergence": "None detected (0.0)",
    "density_grid": [[8, 6, 7], [7, 6, 8], [7, 8, 13]],
    "recent_best_costs": [3302.0, 3272.0, 3410.0],
    "cost_stats": {
      "min": 3410.0,
      "mean": 3563.8,
      "max": 3786.0,
      "std": 122.68561447863397
    }
  },
  "difficult_regions": {
    "low_density_cells": "Cells with lower counts in density_grid (e.g., [6, 6, 6])",
    "long_edge_corridors": "Edges with lengths in the upper quartile (q3=70.0, max=129.0)"
  },
  "opportunity_regions": {
    "high_density_cells": "Cell with highest count in density_grid (13)",
    "short_edge_clusters": "Edges with lengths in the lower quartile (min=1.0, q1=33.0)"
  },
  "evolution_phase": "Exploration",
  "evolution_direction": {
    "suggestions": [
      "Focus on exploiting high-density regions with local search operators",
      "Use mutation operators to break long edges in difficult regions",
      "Encourage crossover operations that preserve short-edge clusters",
      "Maintain diversity by avoiding premature convergence to any single region"
    ]
  }
}
```
2025-06-26 20:18:17,441 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:18:17,443 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate', 'modality': 'Potential multimodal due to varied edge lengths and directions', 'deceptiveness': 'Low to moderate, with some long edges that may mislead', 'edge_len_stats': {'min': 1.0, 'q1': 33.0, 'med': 52.0, 'q3': 70.0, 'max': 129.0}, 'long_edge_ratio': 0.246, 'direction_hist': [10, 8, 8, 13, 8, 8, 5, 9]}, 'population_state': {'coverage': 'Good, as indicated by high diversity (0.76) and density grid', 'convergence': 'None detected (0.0)', 'density_grid': [[8, 6, 7], [7, 6, 8], [7, 8, 13]], 'recent_best_costs': [3302.0, 3272.0, 3410.0], 'cost_stats': {'min': 3410.0, 'mean': 3563.8, 'max': 3786.0, 'std': 122.68561447863397}}, 'difficult_regions': {'low_density_cells': 'Cells with lower counts in density_grid (e.g., [6, 6, 6])', 'long_edge_corridors': 'Edges with lengths in the upper quartile (q3=70.0, max=129.0)'}, 'opportunity_regions': {'high_density_cells': 'Cell with highest count in density_grid (13)', 'short_edge_clusters': 'Edges with lengths in the lower quartile (min=1.0, q1=33.0)'}, 'evolution_phase': 'Exploration', 'evolution_direction': {'suggestions': ['Focus on exploiting high-density regions with local search operators', 'Use mutation operators to break long edges in difficult regions', 'Encourage crossover operations that preserve short-edge clusters', 'Maintain diversity by avoiding premature convergence to any single region']}}
2025-06-26 20:18:17,443 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:18:17,443 - __main__ - INFO - 分析阶段完成
2025-06-26 20:18:17,443 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate', 'modality': 'Potential multimodal due to varied edge lengths and directions', 'deceptiveness': 'Low to moderate, with some long edges that may mislead', 'edge_len_stats': {'min': 1.0, 'q1': 33.0, 'med': 52.0, 'q3': 70.0, 'max': 129.0}, 'long_edge_ratio': 0.246, 'direction_hist': [10, 8, 8, 13, 8, 8, 5, 9]}, 'population_state': {'coverage': 'Good, as indicated by high diversity (0.76) and density grid', 'convergence': 'None detected (0.0)', 'density_grid': [[8, 6, 7], [7, 6, 8], [7, 8, 13]], 'recent_best_costs': [3302.0, 3272.0, 3410.0], 'cost_stats': {'min': 3410.0, 'mean': 3563.8, 'max': 3786.0, 'std': 122.68561447863397}}, 'difficult_regions': {'low_density_cells': 'Cells with lower counts in density_grid (e.g., [6, 6, 6])', 'long_edge_corridors': 'Edges with lengths in the upper quartile (q3=70.0, max=129.0)'}, 'opportunity_regions': {'high_density_cells': 'Cell with highest count in density_grid (13)', 'short_edge_clusters': 'Edges with lengths in the lower quartile (min=1.0, q1=33.0)'}, 'evolution_phase': 'Exploration', 'evolution_direction': {'suggestions': ['Focus on exploiting high-density regions with local search operators', 'Use mutation operators to break long edges in difficult regions', 'Encourage crossover operations that preserve short-edge clusters', 'Maintain diversity by avoiding premature convergence to any single region']}}
2025-06-26 20:18:17,443 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:18:17,443 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:18:17,443 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 3410.0
  • mean_cost: 3563.8
- Search-space: ruggedness Moderate, modality Potential multimodal due to varied edge lengths and directions, deceptiveness Low to moderate, with some long edges that may mislead
- Evolution phase: Exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): low_density_cells, long_edge_corridors
- Opportunity regions (# 2): high_density_cells, short_edge_clusters
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "ineff...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:18:17,443 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:18:17,445 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:18:27,762 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest prioritizing exploration. Top performers exploit; others explore to diversify."
}
```
2025-06-26 20:18:27,762 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 20:18:27,762 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 20:18:27,762 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest prioritizing exploration. Top performers exploit; others explore to diversify."
}
```
2025-06-26 20:18:27,762 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:18:27,762 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 20:18:27,762 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and stagnation suggest prioritizing exploration. Top performers exploit; others explore to diversify."
}
```
2025-06-26 20:18:27,762 - __main__ - INFO - 开始进化阶段
2025-06-26 20:18:27,762 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:18:27,762 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:18:27,765 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3508.0 (rank 20.0%)
- Population diversity: 0.5579710144927537
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:18:27,765 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:18:27,765 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:18:44,111 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters with short edges, spiral outward"
}
```
2025-06-26 20:18:44,111 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:18:44,114 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3316.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]
2025-06-26 20:18:44,114 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3316.0}
2025-06-26 20:18:44,115 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:18:44,115 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:18:44,116 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3508.0 (rank 30.0%)
- Population diversity: 0.5579710144927537
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:18:44,116 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:18:44,116 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:19:00,616 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Sequential path targeting opportunity regions, avoiding difficult areas"
}
```
2025-06-26 20:19:00,616 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:19:00,616 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3410.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]
2025-06-26 20:19:00,616 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}
2025-06-26 20:19:00,616 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-26 20:19:00,616 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:19:00,616 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:19:00,616 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 3840.0
2025-06-26 20:19:01,118 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:19:01,121 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680]
2025-06-26 20:19:01,121 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:19:01,127 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:19:01,127 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3316.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': array([23, 52, 20, 12, 60, 47, 42, 21, 67, 45, 46, 13,  0, 49, 16,  7, 54,
       38, 56, 58, 10, 57, 39, 61, 69,  2, 34, 30,  6, 41,  1, 19, 22, 64,
       65,  8, 18,  9, 68, 26, 62, 53,  4, 40, 48, 29,  3, 25, 32, 31, 43,
       63, 15, 36, 14, 33, 11, 51, 17, 37, 66,  5, 50, 35, 27, 59, 44, 55,
       28, 24]), 'cur_cost': 3840.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([63, 37, 46,  0, 27, 55,  3, 69, 20, 64, 62,  8, 36,  4, 59, 32, 15,
       14, 50, 41, 45, 53, 68, 57,  7, 66, 25, 44, 38, 33,  2, 12, 39, 34,
       54, 23,  9, 11, 58, 21, 24, 51, 49, 28, 47, 48, 67, 17, 61, 65, 30,
       31, 18, 13, 22,  6, 26, 35,  5, 16, 52, 19, 43, 42, 10, 60,  1, 40,
       56, 29]), 'cur_cost': 3786.0}, {'tour': array([53, 67, 50, 52, 47, 57, 15, 49, 17, 43, 35, 42, 23, 21,  7, 19, 28,
       45, 27, 33, 39, 61,  1,  9,  8, 38, 58, 12, 60, 29, 31, 51,  5, 18,
        4, 30, 62, 41, 13, 10, 44, 25, 56, 16, 69, 63, 66,  2, 68, 64, 34,
       36, 54,  3, 32, 22, 59, 14, 40, 24,  0,  6, 46, 65, 11, 26, 55, 20,
       37, 48]), 'cur_cost': 3746.0}, {'tour': array([67, 38, 42,  3, 69, 17, 10, 36, 44, 12, 31, 23, 60, 58, 26, 40, 39,
       27, 43, 50, 49, 46,  2, 34, 22, 62, 29,  4, 51, 11, 63, 55, 45,  7,
       68, 59, 32, 65, 33, 35, 53, 54, 19, 41,  1, 13, 52,  5, 30, 56, 61,
       25, 24, 64, 18, 14,  8, 37, 20, 15, 16, 21,  6,  9, 28,  0, 48, 66,
       47, 57]), 'cur_cost': 3697.0}, {'tour': array([ 8, 42, 69, 29, 43, 51, 50, 22, 18, 32, 57, 38, 40, 48, 13, 37, 26,
       45, 21, 59, 68, 14,  9, 36, 63,  1, 19, 44,  7, 46, 49, 66, 34, 55,
       60, 35,  0, 33, 47, 24, 27, 41, 61, 31, 23, 52,  6, 28, 25, 39, 67,
       16,  2, 15, 56,  3, 54, 17, 11, 53, 20, 58,  5, 30, 65,  4, 62, 10,
       12, 64]), 'cur_cost': 3459.0}]
2025-06-26 20:19:01,130 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:19:01,130 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 28, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 28, 'cache_hits': 0, 'similarity_calculations': 186, 'cache_hit_rate': 0.0, 'cache_size': 186}}
2025-06-26 20:19:01,130 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-06-26 20:19:01,131 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:19:01,131 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:19:01,131 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3508.0 (rank 40.0%)
- Population diversity: 0.6391304347826087
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:19:01,132 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:19:01,132 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:19:17,271 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize opportunity regions while avoiding difficult areas"
}
```
2025-06-26 20:19:17,272 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:19:17,273 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3410.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]
2025-06-26 20:19:17,273 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}
2025-06-26 20:19:17,273 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:19:17,273 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:19:17,273 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3410.0 (rank 10.0%)
- Population diversity: 0.6391304347826087
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:19:17,273 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:19:17,273 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:19:33,614 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Sequential traversal with clustered jumps to balance exploration"
}
```
2025-06-26 20:19:33,614 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:19:33,614 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3480.0, 路径: [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11]
2025-06-26 20:19:33,616 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}
2025-06-26 20:19:33,616 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-26 20:19:33,616 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:19:33,617 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:19:33,617 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 3527.0
2025-06-26 20:19:33,617 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：27,跳过局部搜索
2025-06-26 20:19:33,617 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 20:19:33,617 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([16, 45, 32,  7, 11, 27, 41, 26, 67, 13, 44, 46, 33, 55, 31, 52, 47,
       64, 48, 23, 59, 42,  0,  8, 60,  6,  5, 28, 39, 14, 24,  3, 18, 61,
       29, 68, 66, 54, 21, 30, 35, 69, 15, 51,  9, 19, 10, 57, 50,  4, 37,
        2, 34, 38, 43,  1, 36, 20, 25, 58, 12, 65, 56, 40, 17, 22, 62, 53,
       63, 49]), 'cur_cost': 3527.0}
2025-06-26 20:19:33,617 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 20:19:33,617 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:19:33,617 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3786.0 (rank 80.0%)
- Population diversity: 0.7246376811594203
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:19:33,617 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:19:33,617 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:19:49,077 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Cluster traversal in opportunity regions, then sparse cells"
}
```
2025-06-26 20:19:49,077 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:19:49,077 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3394.0, 路径: [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
2025-06-26 20:19:49,077 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 3394.0}
2025-06-26 20:19:49,077 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:19:49,077 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:19:49,077 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:19:49,077 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 3678.0
2025-06-26 20:19:49,579 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:19:49,579 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680]
2025-06-26 20:19:49,579 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:19:49,586 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:19:49,586 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3316.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': array([23, 52, 20, 12, 60, 47, 42, 21, 67, 45, 46, 13,  0, 49, 16,  7, 54,
       38, 56, 58, 10, 57, 39, 61, 69,  2, 34, 30,  6, 41,  1, 19, 22, 64,
       65,  8, 18,  9, 68, 26, 62, 53,  4, 40, 48, 29,  3, 25, 32, 31, 43,
       63, 15, 36, 14, 33, 11, 51, 17, 37, 66,  5, 50, 35, 27, 59, 44, 55,
       28, 24]), 'cur_cost': 3840.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': array([16, 45, 32,  7, 11, 27, 41, 26, 67, 13, 44, 46, 33, 55, 31, 52, 47,
       64, 48, 23, 59, 42,  0,  8, 60,  6,  5, 28, 39, 14, 24,  3, 18, 61,
       29, 68, 66, 54, 21, 30, 35, 69, 15, 51,  9, 19, 10, 57, 50,  4, 37,
        2, 34, 38, 43,  1, 36, 20, 25, 58, 12, 65, 56, 40, 17, 22, 62, 53,
       63, 49]), 'cur_cost': 3527.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 3394.0}, {'tour': array([ 8, 17, 26, 61,  0, 38, 65, 69, 53, 13, 33, 54, 50, 44, 25, 11, 56,
       18, 64, 28, 30, 15,  6, 43, 58, 31, 23, 60, 34, 48, 24, 47, 14,  3,
       32,  9, 12, 51,  2, 67, 37,  4, 59, 45, 39, 19,  1, 49, 22, 55, 62,
       52, 68,  5, 20, 66, 63, 16, 46, 36, 21, 40,  7, 41, 42, 29, 57, 27,
       10, 35]), 'cur_cost': 3678.0}, {'tour': array([67, 38, 42,  3, 69, 17, 10, 36, 44, 12, 31, 23, 60, 58, 26, 40, 39,
       27, 43, 50, 49, 46,  2, 34, 22, 62, 29,  4, 51, 11, 63, 55, 45,  7,
       68, 59, 32, 65, 33, 35, 53, 54, 19, 41,  1, 13, 52,  5, 30, 56, 61,
       25, 24, 64, 18, 14,  8, 37, 20, 15, 16, 21,  6,  9, 28,  0, 48, 66,
       47, 57]), 'cur_cost': 3697.0}, {'tour': array([ 8, 42, 69, 29, 43, 51, 50, 22, 18, 32, 57, 38, 40, 48, 13, 37, 26,
       45, 21, 59, 68, 14,  9, 36, 63,  1, 19, 44,  7, 46, 49, 66, 34, 55,
       60, 35,  0, 33, 47, 24, 27, 41, 61, 31, 23, 52,  6, 28, 25, 39, 67,
       16,  2, 15, 56,  3, 54, 17, 11, 53, 20, 58,  5, 30, 65,  4, 62, 10,
       12, 64]), 'cur_cost': 3459.0}]
2025-06-26 20:19:49,588 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:19:49,588 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 30, 'skip_rate': 0.03333333333333333, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 29, 'cache_hits': 11, 'similarity_calculations': 210, 'cache_hit_rate': 0.05238095238095238, 'cache_size': 199}}
2025-06-26 20:19:49,588 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:19:49,588 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-26 20:19:49,588 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:19:49,588 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3697.0 (rank 80.0%)
- Population diversity: 0.6463768115942029
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:19:49,590 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:19:49,590 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:20:05,727 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Sequential jumps to balance exploration and cost"
}
```
2025-06-26 20:20:05,727 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:20:05,727 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:20:05,727 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:20:05,727 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:20:05,727 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:20:05,727 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:20:05,736 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 3712.0
2025-06-26 20:20:06,239 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:20:06,239 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680]
2025-06-26 20:20:06,239 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:20:06,245 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:20:06,245 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3316.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': array([23, 52, 20, 12, 60, 47, 42, 21, 67, 45, 46, 13,  0, 49, 16,  7, 54,
       38, 56, 58, 10, 57, 39, 61, 69,  2, 34, 30,  6, 41,  1, 19, 22, 64,
       65,  8, 18,  9, 68, 26, 62, 53,  4, 40, 48, 29,  3, 25, 32, 31, 43,
       63, 15, 36, 14, 33, 11, 51, 17, 37, 66,  5, 50, 35, 27, 59, 44, 55,
       28, 24]), 'cur_cost': 3840.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': array([16, 45, 32,  7, 11, 27, 41, 26, 67, 13, 44, 46, 33, 55, 31, 52, 47,
       64, 48, 23, 59, 42,  0,  8, 60,  6,  5, 28, 39, 14, 24,  3, 18, 61,
       29, 68, 66, 54, 21, 30, 35, 69, 15, 51,  9, 19, 10, 57, 50,  4, 37,
        2, 34, 38, 43,  1, 36, 20, 25, 58, 12, 65, 56, 40, 17, 22, 62, 53,
       63, 49]), 'cur_cost': 3527.0}, {'tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 3394.0}, {'tour': array([ 8, 17, 26, 61,  0, 38, 65, 69, 53, 13, 33, 54, 50, 44, 25, 11, 56,
       18, 64, 28, 30, 15,  6, 43, 58, 31, 23, 60, 34, 48, 24, 47, 14,  3,
       32,  9, 12, 51,  2, 67, 37,  4, 59, 45, 39, 19,  1, 49, 22, 55, 62,
       52, 68,  5, 20, 66, 63, 16, 46, 36, 21, 40,  7, 41, 42, 29, 57, 27,
       10, 35]), 'cur_cost': 3678.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([69, 59, 16, 36, 30, 29, 68, 11, 20, 31,  0, 66, 53, 61,  7, 28, 15,
       32, 24, 19, 12,  8, 14, 48, 67,  5, 35, 47, 65, 43, 40, 56, 27,  4,
        6, 46, 57, 62, 41, 52, 38, 44, 17, 49, 60, 58, 55, 54, 51, 45, 33,
       18, 64, 63,  3, 22, 26, 37, 25, 23,  9, 10,  2,  1, 42, 39, 13, 21,
       50, 34]), 'cur_cost': 3712.0}]
2025-06-26 20:20:06,248 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 20:20:06,248 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 31, 'skip_rate': 0.03225806451612903, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 30, 'cache_hits': 11, 'similarity_calculations': 223, 'cache_hit_rate': 0.04932735426008968, 'cache_size': 212}}
2025-06-26 20:20:06,248 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:20:06,248 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3316.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([16, 45, 32,  7, 11, 27, 41, 26, 67, 13, 44, 46, 33, 55, 31, 52, 47,
       64, 48, 23, 59, 42,  0,  8, 60,  6,  5, 28, 39, 14, 24,  3, 18, 61,
       29, 68, 66, 54, 21, 30, 35, 69, 15, 51,  9, 19, 10, 57, 50,  4, 37,
        2, 34, 38, 43,  1, 36, 20, 25, 58, 12, 65, 56, 40, 17, 22, 62, 53,
       63, 49]), 'cur_cost': 3527.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], 'cur_cost': 3394.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}]
2025-06-26 20:20:06,249 - __main__ - INFO - 进化阶段完成
2025-06-26 20:20:06,249 - __main__ - INFO - 开始评估阶段
2025-06-26 20:20:06,249 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:20:06,251 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:20:06,251 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 3410.0 → 3316.0 (Δ 94.0)
- Mean cost: 3563.8 → 3527.5
- Diversity: 0.558 → 0.639 (Δ 0.0812)

#### Elite metrics
- Elite count: 18 → 18
- Best elite cost: 675 → 675 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 83.3% , avg Δ 149.83
- Exploit: 4 inds, success 25.0% , avg Δ -134.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [3302.0, 3272.0, 3410.0], diversity [0.8152380952380953, 0.8717460317460317, 0.7596825396825395]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:20:06,251 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:20:18,747 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 83.3,
      "average_improvement": 149.83,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -134.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy with ineffective exploitation",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "adjust_exploit_strategy": "consider refining exploit parameters or reducing exploit attempts"
  }
}
```
2025-06-26 20:20:18,789 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:20:18,789 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 83.3,
      "average_improvement": 149.83,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -134.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy with ineffective exploitation",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "adjust_exploit_strategy": "consider refining exploit parameters or reducing exploit attempts"
  }
}
```
2025-06-26 20:20:18,789 - __main__ - INFO - 评估阶段完成
2025-06-26 20:20:18,789 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 83.3,
      "average_improvement": 149.83,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -134.0,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy with ineffective exploitation",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "adjust_exploit_strategy": "consider refining exploit parameters or reducing exploit attempts"
  }
}
```
2025-06-26 20:20:18,789 - __main__ - INFO - 当前最佳适应度: 3316.0
2025-06-26 20:20:18,789 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\st70_route_3.pkl
2025-06-26 20:20:18,789 - __main__ - INFO - st70 开始进化第 5 代
2025-06-26 20:20:18,789 - __main__ - INFO - 开始分析阶段
2025-06-26 20:20:18,789 - StatsExpert - INFO - 开始统计分析
2025-06-26 20:20:18,815 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 3316.0, 'max': 3840.0, 'mean': 3527.5, 'std': 157.2102095921254}, 'diversity': 0.8438095238095239, 'clusters': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 20:20:18,823 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 3316.0, 'max': 3840.0, 'mean': 3527.5, 'std': 157.2102095921254}, 'diversity_level': 0.8438095238095239, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [4, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[64, 96], [80, 39], [69, 23], [72, 42], [48, 67], [58, 43], [81, 34], [79, 17], [30, 23], [42, 67], [7, 76], [29, 51], [78, 92], [64, 8], [95, 57], [57, 91], [40, 35], [68, 40], [92, 34], [62, 1], [28, 43], [76, 73], [67, 88], [93, 54], [6, 8], [87, 18], [30, 9], [77, 13], [78, 94], [55, 3], [82, 88], [73, 28], [20, 55], [27, 43], [95, 86], [67, 99], [48, 83], [75, 81], [8, 19], [20, 18], [54, 38], [63, 36], [44, 33], [52, 18], [12, 13], [25, 5], [58, 85], [5, 67], [90, 9], [41, 76], [25, 76], [37, 64], [56, 63], [10, 55], [98, 7], [16, 74], [89, 60], [48, 82], [81, 76], [29, 60], [17, 22], [5, 45], [79, 70], [9, 100], [17, 82], [74, 67], [10, 68], [48, 19], [83, 86], [84, 94]], 'distance_matrix': array([[ 0., 59., 73., ..., 79., 21., 20.],
       [59.,  0., 19., ..., 38., 47., 55.],
       [73., 19.,  0., ..., 21., 65., 73.],
       ...,
       [79., 38., 21., ...,  0., 76., 83.],
       [21., 47., 65., ..., 76.,  0.,  8.],
       [20., 55., 73., ..., 83.,  8.,  0.]])}
2025-06-26 20:20:18,824 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 20:20:18,824 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 20:20:18,824 - PathExpert - INFO - 开始路径结构分析
2025-06-26 20:20:18,828 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 20:20:18,828 - PathExpert - INFO - 路径结构分析完成
2025-06-26 20:20:18,828 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 20:20:18,828 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 20:20:18,828 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 20:20:18,828 - EliteExpert - INFO - 开始精英解分析
2025-06-26 20:20:18,834 - EliteExpert - INFO - 精英解分析完成
2025-06-26 20:20:18,835 - __main__ - INFO - 精英专家分析报告: {'elite_count': 18, 'elite_common_features': {'common_edges': {'(33, 20)': 0.7222222222222222, '(66, 47)': 0.7222222222222222}, 'common_edge_ratio': 0.02857142857142857}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 2641.0, 'avg_gap': 2852.1666666666665}, 'structure_gap': {'unique_elite_edges': 149, 'unique_pop_edges': 453, 'common_edges': 17}}, 'elite_diversity': {'diversity_score': 0.0756302521008404}}
2025-06-26 20:20:18,835 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 20:20:18,836 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 20:20:18,837 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 20:20:18,837 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 3316.0, mean 3527.5, max 3840.0, std 157.2102095921254
- diversity: 0.8438095238095239
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [3272.0, 3410.0, 3316.0] (Δ 47.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [5.0, 1.0, 98.0, 100.0]
- centroid/spread: [52.657, 51.271] | rms 40.489
- density_grid(3x3): [[8, 6, 7], [7, 6, 8], [7, 8, 13]]
- edge_len_stats(min,q1,med,q3,max): [1.0, 33.0, 52.0, 70.0, 129.0]
- long_edge_ratio: 0.246
- direction_hist(8 sectors): [10, 8, 8, 13, 8, 8, 5, 9]
- nn_median_dist: 6.083

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 20:20:18,838 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:20:44,102 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate (edge length stats show wide range from 1.0 to 129.0, median 52.0)",
    "modality": "Potential multi-modality (diversity 0.84 with no clustering info, direction histogram shows some sector imbalance)",
    "deceptiveness": "Possible mild deceptiveness (long_edge_ratio 0.246 suggests ~25% edges may mislead local search)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (density_grid shows nodes distributed across all cells, though unevenly)",
    "convergence": "Early-stage (convergence 0.0, recent best costs still improving by ~47 per iteration)",
    "diversity": "High (0.84) with no elite solutions yet"
  },
  "difficult_regions": [
    "Long-edge corridors (edges in max quartile ≥70.0 length)",
    "Low-density grid cells (particularly [0,1] and [1,1] with only 6 nodes each)",
    "Direction sectors with low counts (sectors 6 and 2 with only 5-8 edges)"
  ],
  "opportunity_regions": [
    "High-density cell [2,2] (13 nodes) - potential cluster to exploit",
    "Frequently occurring mid-length edges (Q1-Q3 range 33.0-70.0)",
    "Direction sectors 3 and 7 (higher edge counts)"
  ],
  "evolution_phase": "Early exploration",
  "evolution_direction": [
    "Prioritize 2-opt operators for long-edge reduction",
    "Apply directed mutation favoring high-density regions",
    "Introduce path consolidation around centroid (52.657,51.271)",
    "Maintain diversity via restart triggers if Δbest < 20 for 3 iterations"
  ]
}
```
2025-06-26 20:20:44,102 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 20:20:44,102 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate (edge length stats show wide range from 1.0 to 129.0, median 52.0)', 'modality': 'Potential multi-modality (diversity 0.84 with no clustering info, direction histogram shows some sector imbalance)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio 0.246 suggests ~25% edges may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (density_grid shows nodes distributed across all cells, though unevenly)', 'convergence': 'Early-stage (convergence 0.0, recent best costs still improving by ~47 per iteration)', 'diversity': 'High (0.84) with no elite solutions yet'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile ≥70.0 length)', 'Low-density grid cells (particularly [0,1] and [1,1] with only 6 nodes each)', 'Direction sectors with low counts (sectors 6 and 2 with only 5-8 edges)'], 'opportunity_regions': ['High-density cell [2,2] (13 nodes) - potential cluster to exploit', 'Frequently occurring mid-length edges (Q1-Q3 range 33.0-70.0)', 'Direction sectors 3 and 7 (higher edge counts)'], 'evolution_phase': 'Early exploration', 'evolution_direction': ['Prioritize 2-opt operators for long-edge reduction', 'Apply directed mutation favoring high-density regions', 'Introduce path consolidation around centroid (52.657,51.271)', 'Maintain diversity via restart triggers if Δbest < 20 for 3 iterations']}
2025-06-26 20:20:44,103 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 20:20:44,103 - __main__ - INFO - 分析阶段完成
2025-06-26 20:20:44,103 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate (edge length stats show wide range from 1.0 to 129.0, median 52.0)', 'modality': 'Potential multi-modality (diversity 0.84 with no clustering info, direction histogram shows some sector imbalance)', 'deceptiveness': 'Possible mild deceptiveness (long_edge_ratio 0.246 suggests ~25% edges may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (density_grid shows nodes distributed across all cells, though unevenly)', 'convergence': 'Early-stage (convergence 0.0, recent best costs still improving by ~47 per iteration)', 'diversity': 'High (0.84) with no elite solutions yet'}, 'difficult_regions': ['Long-edge corridors (edges in max quartile ≥70.0 length)', 'Low-density grid cells (particularly [0,1] and [1,1] with only 6 nodes each)', 'Direction sectors with low counts (sectors 6 and 2 with only 5-8 edges)'], 'opportunity_regions': ['High-density cell [2,2] (13 nodes) - potential cluster to exploit', 'Frequently occurring mid-length edges (Q1-Q3 range 33.0-70.0)', 'Direction sectors 3 and 7 (higher edge counts)'], 'evolution_phase': 'Early exploration', 'evolution_direction': ['Prioritize 2-opt operators for long-edge reduction', 'Apply directed mutation favoring high-density regions', 'Introduce path consolidation around centroid (52.657,51.271)', 'Maintain diversity via restart triggers if Δbest < 20 for 3 iterations']}
2025-06-26 20:20:44,104 - __main__ - INFO - 开始策略分配阶段
2025-06-26 20:20:44,104 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 20:20:44,104 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 3316.0
  • mean_cost: 3527.5
- Search-space: ruggedness Moderate (edge length stats show wide range from 1.0 to 129.0, median 52.0), modality Potential multi-modality (diversity 0.84 with no clustering info, direction histogram shows some sector imbalance), deceptiveness Possible mild deceptiveness (long_edge_ratio 0.246 suggests ~25% edges may mislead local search)
- Evolution phase: Early exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): Long-edge corridors , Low-density grid cel...
- Opportunity regions (# 3): High-density cell [2, Frequently occurring...
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 20:20:44,104 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 20:20:44,104 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:20:54,560 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify, bottom 40% exploit current bests."
}
```
2025-06-26 20:20:54,560 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:20:54,565 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:20:54,565 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify, bottom 40% exploit current bests."
}
```
2025-06-26 20:20:54,565 - __main__ - INFO - 策略分配阶段完成
2025-06-26 20:20:54,566 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 20:20:54,566 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 60% explore to diversify, bottom 40% exploit current bests."
}
```
2025-06-26 20:20:54,566 - __main__ - INFO - 开始进化阶段
2025-06-26 20:20:54,567 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 20:20:54,567 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:20:54,568 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3316.0 (rank 0.0%)
- Population diversity: 0.6391304347826087
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Frequently occu...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:20:54,568 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:20:54,568 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:21:10,516 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Sequential exploration with opportunistic density jumps"
}
```
2025-06-26 20:21:10,516 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:21:10,516 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3410.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]
2025-06-26 20:21:10,516 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}
2025-06-26 20:21:10,516 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 20:21:10,516 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:21:10,516 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3410.0 (rank 10.0%)
- Population diversity: 0.6391304347826087
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Frequently occu...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:21:10,516 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:21:10,516 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:21:26,581 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Layered spiral through opportunity regions, avoiding long edges"
}
```
2025-06-26 20:21:26,581 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:21:26,581 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3480.0, 路径: [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11]
2025-06-26 20:21:26,581 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}
2025-06-26 20:21:26,593 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 20:21:26,593 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:21:26,593 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3840.0 (rank 90.0%)
- Population diversity: 0.6391304347826087
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Frequently occu...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:21:26,593 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:21:26,593 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:21:42,032 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Structured sweep through opportunity regions with modular jumps"
}
```
2025-06-26 20:21:42,032 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:21:42,032 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:21:42,032 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:21:42,032 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 20:21:42,032 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:21:42,032 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3410.0 (rank 10.0%)
- Population diversity: 0.553623188405797
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Frequently occu...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:21:42,032 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:21:42,032 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:21:58,493 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Structured leapfrog pattern to explore clustered regions"
}
```
2025-06-26 20:21:58,507 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:21:58,507 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:21:58,507 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:21:58,507 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 20:21:58,507 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:21:58,507 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3480.0 (rank 20.0%)
- Population diversity: 0.553623188405797
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Frequently occu...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:21:58,509 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:21:58,509 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:22:14,918 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, adjacent low-density",
  "strategy_comment": "Structured leapfrog to explore sparse cells near opportunities"
}
```
2025-06-26 20:22:14,918 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:22:14,918 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3508.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 20:22:14,923 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}
2025-06-26 20:22:14,923 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 20:22:14,923 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 20:22:14,924 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3527.0 (rank 70.0%)
- Population diversity: 0.553623188405797
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Frequently occu...
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 69]  
• Path length == 70 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 20:22:14,924 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 20:22:14,924 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:22:31,224 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11],
  "targeted_regions": "High-density ce, adjacent low-density cells",
  "strategy_comment": "Leverage opportunity regions with adjacent exploration, maintaining balance"
}
```
2025-06-26 20:22:31,224 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 20:22:31,224 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3480.0, 路径: [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11]
2025-06-26 20:22:31,224 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}
2025-06-26 20:22:31,224 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 20:22:31,224 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:22:31,224 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:22:31,224 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 3715.0
2025-06-26 20:22:31,730 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:22:31,730 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680]
2025-06-26 20:22:31,730 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:22:31,737 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:22:31,738 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': array([15, 52, 26, 21, 50, 24, 40,  9, 14, 12, 33,  8,  5, 28,  0, 25, 59,
       47, 49, 69, 29, 48, 38, 31, 54, 61, 64,  1, 30, 19, 66, 41, 68, 18,
       17, 58, 39,  2, 23, 22, 37, 53, 67, 44, 35, 60, 51,  4, 62, 43, 42,
        3, 27, 63, 46, 11, 57, 32, 65,  7, 16,  6, 20, 56, 13, 34, 36, 55,
       45, 10]), 'cur_cost': 3715.0}, {'tour': array([ 8, 17, 26, 61,  0, 38, 65, 69, 53, 13, 33, 54, 50, 44, 25, 11, 56,
       18, 64, 28, 30, 15,  6, 43, 58, 31, 23, 60, 34, 48, 24, 47, 14,  3,
       32,  9, 12, 51,  2, 67, 37,  4, 59, 45, 39, 19,  1, 49, 22, 55, 62,
       52, 68,  5, 20, 66, 63, 16, 46, 36, 21, 40,  7, 41, 42, 29, 57, 27,
       10, 35]), 'cur_cost': 3678.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([69, 59, 16, 36, 30, 29, 68, 11, 20, 31,  0, 66, 53, 61,  7, 28, 15,
       32, 24, 19, 12,  8, 14, 48, 67,  5, 35, 47, 65, 43, 40, 56, 27,  4,
        6, 46, 57, 62, 41, 52, 38, 44, 17, 49, 60, 58, 55, 54, 51, 45, 33,
       18, 64, 63,  3, 22, 26, 37, 25, 23,  9, 10,  2,  1, 42, 39, 13, 21,
       50, 34]), 'cur_cost': 3712.0}]
2025-06-26 20:22:31,739 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 20:22:31,740 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 32, 'skip_rate': 0.03125, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 31, 'cache_hits': 11, 'similarity_calculations': 237, 'cache_hit_rate': 0.046413502109704644, 'cache_size': 226}}
2025-06-26 20:22:31,740 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 20:22:31,740 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 20:22:31,740 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:22:31,740 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:22:31,741 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 3643.0
2025-06-26 20:22:32,243 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:22:32,243 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680]
2025-06-26 20:22:32,243 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:22:32,250 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:22:32,250 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': array([15, 52, 26, 21, 50, 24, 40,  9, 14, 12, 33,  8,  5, 28,  0, 25, 59,
       47, 49, 69, 29, 48, 38, 31, 54, 61, 64,  1, 30, 19, 66, 41, 68, 18,
       17, 58, 39,  2, 23, 22, 37, 53, 67, 44, 35, 60, 51,  4, 62, 43, 42,
        3, 27, 63, 46, 11, 57, 32, 65,  7, 16,  6, 20, 56, 13, 34, 36, 55,
       45, 10]), 'cur_cost': 3715.0}, {'tour': array([ 0, 69, 49, 42, 55, 26, 36, 21, 62, 23, 60, 32, 30, 65, 38, 50, 64,
       16,  9, 12, 39, 52, 51, 45, 13, 68, 53, 61, 27,  3, 66,  1, 47, 37,
       29, 10, 31, 67,  7, 22, 28, 46, 63, 48, 24, 14, 44, 11, 58, 25,  8,
       17, 18, 54, 40, 20,  4, 19, 15, 56,  6, 33, 43, 35, 34, 59, 57,  5,
        2, 41]), 'cur_cost': 3643.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': array([69, 59, 16, 36, 30, 29, 68, 11, 20, 31,  0, 66, 53, 61,  7, 28, 15,
       32, 24, 19, 12,  8, 14, 48, 67,  5, 35, 47, 65, 43, 40, 56, 27,  4,
        6, 46, 57, 62, 41, 52, 38, 44, 17, 49, 60, 58, 55, 54, 51, 45, 33,
       18, 64, 63,  3, 22, 26, 37, 25, 23,  9, 10,  2,  1, 42, 39, 13, 21,
       50, 34]), 'cur_cost': 3712.0}]
2025-06-26 20:22:32,251 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:22:32,252 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 33, 'skip_rate': 0.030303030303030304, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 32, 'cache_hits': 11, 'similarity_calculations': 252, 'cache_hit_rate': 0.04365079365079365, 'cache_size': 241}}
2025-06-26 20:22:32,252 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 20:22:32,252 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 20:22:32,252 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:22:32,252 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:22:32,253 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 3541.0
2025-06-26 20:22:32,253 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：27,跳过局部搜索
2025-06-26 20:22:32,253 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 20:22:32,253 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([63, 53, 58, 37, 69, 27, 64, 19, 50,  2, 17, 38, 10,  0,  1, 52, 20,
       68,  6, 61, 33, 51, 47, 15, 21, 13, 59,  3, 12, 26, 22, 41, 67, 16,
       49,  5, 48, 24, 55, 35,  4, 18, 25, 62, 28, 45,  8, 29, 60, 44, 14,
        9,  7, 40, 43, 36, 42, 56, 65, 31, 46, 30, 32, 39, 54, 11, 66, 23,
       34, 57]), 'cur_cost': 3541.0}
2025-06-26 20:22:32,254 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 20:22:32,254 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 20:22:32,254 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 20:22:32,254 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 3659.0
2025-06-26 20:22:32,755 - ExploitationExpert - INFO - res_population_num: 18
2025-06-26 20:22:32,756 - ExploitationExpert - INFO - res_population_costs: [675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 675, 676, 680]
2025-06-26 20:22:32,756 - ExploitationExpert - INFO - res_populations: [array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 68, 30, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 44, 24, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 33, 20, 32, 61, 53, 47, 66, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       20, 33, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 66, 47, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 44,
       24, 45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,
        3,  1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 68, 30, 34, 69, 12,
       28, 35], dtype=int64), array([ 0, 35, 28, 12, 69, 34, 30, 68, 37, 58, 21, 65, 62, 56, 14, 23, 18,
        6,  1,  3, 17, 41, 31,  2,  7, 25, 54, 48, 27, 13, 19, 29, 43, 67,
       26, 45, 24, 44, 38, 60, 39,  8, 16, 42, 40,  5, 52,  4,  9, 51, 59,
       11, 20, 33, 32, 61, 53, 66, 47, 10, 63, 64, 55, 50, 49, 57, 36, 46,
       15, 22], dtype=int64), array([ 0, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32, 33,
       20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24, 44,
       45, 26, 67, 43, 29, 19, 13, 27, 48, 54, 25,  7,  2, 31, 41, 17,  3,
        1,  6, 18, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 28, 12,
       22, 35], dtype=int64), array([ 0, 22, 15, 46, 36, 57, 49, 50, 55, 64, 63, 10, 47, 66, 53, 61, 32,
       33, 20, 11, 59, 51,  9,  4, 52,  5, 40, 42, 16,  8, 39, 60, 38, 24,
       44, 45, 26, 67, 43, 29, 19, 13, 27,  7, 25, 48, 54, 18,  6, 31,  2,
       41, 17,  3,  1, 23, 14, 56, 62, 65, 21, 58, 37, 30, 68, 34, 69, 12,
       28, 35], dtype=int64)]
2025-06-26 20:22:32,761 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 20:22:32,762 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}, {'tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}, {'tour': array([15, 52, 26, 21, 50, 24, 40,  9, 14, 12, 33,  8,  5, 28,  0, 25, 59,
       47, 49, 69, 29, 48, 38, 31, 54, 61, 64,  1, 30, 19, 66, 41, 68, 18,
       17, 58, 39,  2, 23, 22, 37, 53, 67, 44, 35, 60, 51,  4, 62, 43, 42,
        3, 27, 63, 46, 11, 57, 32, 65,  7, 16,  6, 20, 56, 13, 34, 36, 55,
       45, 10]), 'cur_cost': 3715.0}, {'tour': array([ 0, 69, 49, 42, 55, 26, 36, 21, 62, 23, 60, 32, 30, 65, 38, 50, 64,
       16,  9, 12, 39, 52, 51, 45, 13, 68, 53, 61, 27,  3, 66,  1, 47, 37,
       29, 10, 31, 67,  7, 22, 28, 46, 63, 48, 24, 14, 44, 11, 58, 25,  8,
       17, 18, 54, 40, 20,  4, 19, 15, 56,  6, 33, 43, 35, 34, 59, 57,  5,
        2, 41]), 'cur_cost': 3643.0}, {'tour': array([63, 53, 58, 37, 69, 27, 64, 19, 50,  2, 17, 38, 10,  0,  1, 52, 20,
       68,  6, 61, 33, 51, 47, 15, 21, 13, 59,  3, 12, 26, 22, 41, 67, 16,
       49,  5, 48, 24, 55, 35,  4, 18, 25, 62, 28, 45,  8, 29, 60, 44, 14,
        9,  7, 40, 43, 36, 42, 56, 65, 31, 46, 30, 32, 39, 54, 11, 66, 23,
       34, 57]), 'cur_cost': 3541.0}, {'tour': array([28, 29, 26, 53, 63, 69,  1, 55, 68, 27,  6, 60,  3, 61, 64, 11, 15,
       24, 20,  0, 46, 19, 22, 59, 62, 36,  8, 21, 44, 42, 40, 30, 38, 13,
       65, 16, 52, 67, 18, 43, 56, 33, 57,  2, 25, 58, 49, 50, 66, 14, 35,
       34, 39, 54, 48, 12, 31, 32,  7, 37, 41, 45, 10, 47, 51, 23, 17,  9,
        4,  5]), 'cur_cost': 3659.0}]
2025-06-26 20:22:32,764 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 20:22:32,764 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 35, 'skip_rate': 0.05714285714285714, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 33, 'cache_hits': 23, 'similarity_calculations': 280, 'cache_hit_rate': 0.08214285714285714, 'cache_size': 257}}
2025-06-26 20:22:32,764 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 20:22:32,765 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69], 'cur_cost': 3410.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 66, 7, 19, 31, 43, 55, 67, 8, 20, 32, 44, 56, 68, 9, 21, 33, 45, 57, 69, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 3508.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 67, 1, 13, 24, 35, 46, 57, 68, 2, 14, 25, 36, 47, 58, 69, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 66, 11], 'cur_cost': 3480.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([63, 53, 58, 37, 69, 27, 64, 19, 50,  2, 17, 38, 10,  0,  1, 52, 20,
       68,  6, 61, 33, 51, 47, 15, 21, 13, 59,  3, 12, 26, 22, 41, 67, 16,
       49,  5, 48, 24, 55, 35,  4, 18, 25, 62, 28, 45,  8, 29, 60, 44, 14,
        9,  7, 40, 43, 36, 42, 56, 65, 31, 46, 30, 32, 39, 54, 11, 66, 23,
       34, 57]), 'cur_cost': 3541.0}}]
2025-06-26 20:22:32,765 - __main__ - INFO - 进化阶段完成
2025-06-26 20:22:32,765 - __main__ - INFO - 开始评估阶段
2025-06-26 20:22:32,766 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 20:22:32,766 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 20:22:32,766 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 3316.0 → 3410.0 (Δ -94.0)
- Mean cost: 3527.5 → 3545.2
- Diversity: 0.639 → 0.638 (Δ -0.0014)

#### Elite metrics
- Elite count: 18 → 18
- Best elite cost: 675 → 675 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 33.3% , avg Δ 14.83
- Exploit: 4 inds, success 50.0% , avg Δ -66.5

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [3272.0, 3410.0, 3316.0], diversity [0.8717460317460317, 0.7596825396825395, 0.8438095238095239]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 20:22:32,766 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 20:22:43,032 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "plateau",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": 14.83
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -66.5
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8
  }
}
```
2025-06-26 20:22:43,076 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 20:22:43,076 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "plateau",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": 14.83
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -66.5
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8
  }
}
```
2025-06-26 20:22:43,076 - __main__ - INFO - 评估阶段完成
2025-06-26 20:22:43,076 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "plateau",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": 14.83
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": -66.5
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8
  }
}
```
2025-06-26 20:22:43,076 - __main__ - INFO - 当前最佳适应度: 3410.0
2025-06-26 20:22:43,076 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\st70_route_4.pkl
2025-06-26 20:22:43,103 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\st70_solution.json
2025-06-26 20:22:43,103 - __main__ - INFO - 实例 st70 处理完成
