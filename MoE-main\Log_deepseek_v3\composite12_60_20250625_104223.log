2025-06-25 10:42:23,183 - __main__ - INFO - composite12_60 开始进化第 1 代
2025-06-25 10:42:23,184 - __main__ - INFO - 开始分析阶段
2025-06-25 10:42:23,184 - StatsExpert - INFO - 开始统计分析
2025-06-25 10:42:23,201 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9948.0, 'max': 112557.0, 'mean': 71264.3, 'std': 40606.26905060351}, 'diversity': 0.912962962962963, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-25 10:42:23,202 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9948.0, 'max': 112557.0, 'mean': 71264.3, 'std': 40606.26905060351}, 'diversity_level': 0.912962962962963, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[1380, 1590], [1386, 1610], [1446, 1664], [1473, 1651], [1403, 1573], [1429, 1576], [1414, 1596], [1421, 1664], [1385, 1662], [1475, 1616], [1405, 1625], [1439, 1639], [3242, 2428], [3193, 2490], [3212, 2461], [3238, 2498], [3270, 2452], [3236, 2513], [3196, 2449], [3220, 2481], [3262, 2465], [3198, 2468], [3267, 2481], [3264, 2442], [426, 2488], [478, 2496], [499, 2467], [445, 2489], [451, 2481], [441, 2477], [461, 2463], [511, 2487], [499, 2478], [431, 2471], [461, 2456], [476, 2486], [832, 400], [885, 348], [838, 364], [921, 379], [900, 346], [918, 411], [855, 361], [866, 388], [878, 327], [841, 380], [902, 374], [871, 361], [2763, 1531], [2817, 1507], [2789, 1453], [2739, 1464], [2773, 1490], [2783, 1514], [2808, 1535], [2797, 1510], [2807, 1517], [2793, 1495], [2828, 1490], [2736, 1497]], 'distance_matrix': array([[   0.,   21.,   99., ..., 1416., 1451., 1359.],
       [  21.,    0.,   81., ..., 1412., 1447., 1355.],
       [  99.,   81.,    0., ..., 1358., 1393., 1301.],
       ...,
       [1416., 1412., 1358., ...,    0.,   35.,   57.],
       [1451., 1447., 1393., ...,   35.,    0.,   92.],
       [1359., 1355., 1301., ...,   57.,   92.,    0.]])}
2025-06-25 10:42:23,211 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-25 10:42:23,211 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-25 10:42:23,211 - PathExpert - INFO - 开始路径结构分析
2025-06-25 10:42:23,217 - PathExpert - INFO - 路径结构分析完成
2025-06-25 10:42:23,217 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (12, 23, 16), 'frequency': 0.3}, {'subpath': (23, 16, 20), 'frequency': 0.3}, {'subpath': (16, 20, 22), 'frequency': 0.3}, {'subpath': (20, 22, 15), 'frequency': 0.3}, {'subpath': (22, 15, 17), 'frequency': 0.3}, {'subpath': (15, 17, 19), 'frequency': 0.3}, {'subpath': (17, 19, 14), 'frequency': 0.3}, {'subpath': (19, 14, 21), 'frequency': 0.3}, {'subpath': (14, 21, 18), 'frequency': 0.3}, {'subpath': (21, 18, 13), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(49, 58)', 'frequency': 0.4}, {'edge': '(12, 23)', 'frequency': 0.4}, {'edge': '(16, 20)', 'frequency': 0.4}, {'edge': '(2, 3)', 'frequency': 0.4}, {'edge': '(10, 11)', 'frequency': 0.4}, {'edge': '(0, 1)', 'frequency': 0.4}, {'edge': '(26, 32)', 'frequency': 0.4}, {'edge': '(25, 31)', 'frequency': 0.4}, {'edge': '(38, 45)', 'frequency': 0.4}, {'edge': '(38, 42)', 'frequency': 0.4}, {'edge': '(39, 41)', 'frequency': 0.4}, {'edge': '(41, 43)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(55, 56)', 'frequency': 0.3}, {'edge': '(49, 56)', 'frequency': 0.2}, {'edge': '(52, 57)', 'frequency': 0.3}, {'edge': '(48, 53)', 'frequency': 0.3}, {'edge': '(48, 59)', 'frequency': 0.2}, {'edge': '(51, 59)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.2}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(20, 22)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.3}, {'edge': '(15, 17)', 'frequency': 0.3}, {'edge': '(17, 19)', 'frequency': 0.3}, {'edge': '(14, 19)', 'frequency': 0.3}, {'edge': '(14, 21)', 'frequency': 0.3}, {'edge': '(18, 21)', 'frequency': 0.3}, {'edge': '(13, 18)', 'frequency': 0.3}, {'edge': '(2, 7)', 'frequency': 0.3}, {'edge': '(7, 11)', 'frequency': 0.3}, {'edge': '(1, 10)', 'frequency': 0.3}, {'edge': '(0, 4)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.3}, {'edge': '(5, 6)', 'frequency': 0.3}, {'edge': '(8, 26)', 'frequency': 0.3}, {'edge': '(31, 32)', 'frequency': 0.3}, {'edge': '(25, 35)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(27, 28)', 'frequency': 0.3}, {'edge': '(27, 29)', 'frequency': 0.3}, {'edge': '(29, 33)', 'frequency': 0.3}, {'edge': '(24, 33)', 'frequency': 0.3}, {'edge': '(24, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(34, 36)', 'frequency': 0.3}, {'edge': '(36, 45)', 'frequency': 0.3}, {'edge': '(42, 47)', 'frequency': 0.3}, {'edge': '(37, 47)', 'frequency': 0.3}, {'edge': '(37, 40)', 'frequency': 0.3}, {'edge': '(40, 46)', 'frequency': 0.3}, {'edge': '(39, 46)', 'frequency': 0.3}, {'edge': '(43, 44)', 'frequency': 0.3}, {'edge': '(44, 55)', 'frequency': 0.2}, {'edge': '(3, 9)', 'frequency': 0.3}, {'edge': '(5, 8)', 'frequency': 0.3}, {'edge': '(52, 59)', 'frequency': 0.2}, {'edge': '(55, 57)', 'frequency': 0.2}, {'edge': '(54, 58)', 'frequency': 0.2}, {'edge': '(9, 13)', 'frequency': 0.2}, {'edge': '(54, 56)', 'frequency': 0.2}, {'edge': '(12, 44)', 'frequency': 0.2}, {'edge': '(0, 8)', 'frequency': 0.2}, {'edge': '(13, 40)', 'frequency': 0.2}, {'edge': '(14, 50)', 'frequency': 0.2}, {'edge': '(9, 22)', 'frequency': 0.2}, {'edge': '(39, 45)', 'frequency': 0.2}, {'edge': '(29, 39)', 'frequency': 0.2}, {'edge': '(38, 47)', 'frequency': 0.2}, {'edge': '(3, 52)', 'frequency': 0.2}, {'edge': '(17, 53)', 'frequency': 0.2}, {'edge': '(5, 16)', 'frequency': 0.2}, {'edge': '(1, 14)', 'frequency': 0.2}, {'edge': '(27, 35)', 'frequency': 0.2}, {'edge': '(11, 36)', 'frequency': 0.2}, {'edge': '(15, 56)', 'frequency': 0.2}, {'edge': '(5, 44)', 'frequency': 0.2}, {'edge': '(50, 52)', 'frequency': 0.2}, {'edge': '(38, 50)', 'frequency': 0.2}, {'edge': '(22, 45)', 'frequency': 0.2}, {'edge': '(4, 20)', 'frequency': 0.2}, {'edge': '(46, 49)', 'frequency': 0.2}, {'edge': '(8, 29)', 'frequency': 0.2}, {'edge': '(16, 28)', 'frequency': 0.2}, {'edge': '(50, 55)', 'frequency': 0.2}, {'edge': '(7, 57)', 'frequency': 0.2}, {'edge': '(25, 43)', 'frequency': 0.2}, {'edge': '(2, 49)', 'frequency': 0.2}, {'edge': '(32, 56)', 'frequency': 0.2}, {'edge': '(36, 51)', 'frequency': 0.2}, {'edge': '(8, 48)', 'frequency': 0.2}, {'edge': '(23, 40)', 'frequency': 0.2}, {'edge': '(41, 54)', 'frequency': 0.2}, {'edge': '(51, 58)', 'frequency': 0.2}, {'edge': '(40, 59)', 'frequency': 0.2}, {'edge': '(19, 20)', 'frequency': 0.2}, {'edge': '(14, 18)', 'frequency': 0.2}, {'edge': '(15, 49)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [37, 23, 26, 16, 25, 22], 'cost': 14286.0, 'size': 6}, {'region': [12, 24, 18, 40, 13], 'cost': 11840.0, 'size': 5}, {'region': [20, 38, 50, 33], 'cost': 8010.0, 'size': 4}, {'region': [21, 46, 54, 24], 'cost': 7905.0, 'size': 4}, {'region': [27, 57, 33, 52], 'cost': 7645.0, 'size': 4}]}
2025-06-25 10:42:23,218 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-25 10:42:23,218 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-25 10:42:23,218 - EliteExpert - INFO - 开始精英解分析
2025-06-25 10:42:23,218 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-25 10:42:23,218 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-25 10:42:23,218 - __main__ - INFO - 更新共享数据: elite_features
2025-06-25 10:42:23,218 - LandscapeExpert - INFO - 开始景观分析
2025-06-25 10:42:24,019 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-25 10:42:24,019 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9948.0, mean 71264.3, max 112557.0, std 40606.26905060351
- diversity: 0.912962962962963
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: [{'subpath': (12, 23, 16), 'frequency': 0.3}, {'subpath': (23, 16, 20), 'frequency': 0.3}, {'subpath': (16, 20, 22), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [37, 23, 26, 16, 25, 22], 'cost': 14286.0, 'size': 6}, {'region': [12, 24, 18, 40, 13], 'cost': 11840.0, 'size': 5}, {'region': [20, 38, 50, 33], 'cost': 8010.0, 'size': 4}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [426.0, 327.0, 3270.0, 2513.0]
- centroid/spread: [1756.217, 1687.917] | rms 1327.809
- density_grid(3x3): [[12, 0, 0], [0, 12, 12], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [7.0, 1094.25, 1427.5, 2273.75, 3222.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 7, 6, 9, 12, 8, 4, 7]
- nn_median_dist: 17.242

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-25 10:42:32,652 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "The high range in edge lengths (7.0 to 3222.0) and the large interquartile range (1094.25 to 2273.75) suggest a rugged search space. The large standard deviation in cost (40606.269) further confirms this.",
    "modality": "The edge length statistics and the direction histogram suggest a potentially multimodal search space.  The histogram isn't strongly biased towards any particular direction, hinting at multiple potentially good directions to explore.",
    "deceptiveness": "The long_edge_ratio of 0.25 implies that long edges are present, potentially creating deceptive landscapes where short-term improvements might lead away from the global optimum.  The wide cost range within the population also hints at potential deceptiveness."
  },
  "population_state": {
    "coverage": "The diversity is relatively high (0.913) at this early stage, indicating good population coverage of the search space. The density grid shows node distribution across different areas, with some clusters appearing. However, the convergence is 0.0, meaning the population hasn't focused on any single solution yet.",
    "convergence": "The convergence is 0.0, indicating no focused convergence towards specific solutions."
  },
  "difficult_regions": [
    {
      "region": [
        37,
        23,
        26,
        16,
        25,
        22
      ],
      "cost": 14286.0,
      "size": 6,
      "spatial_context": "This region might be difficult due to the relatively high cost combined with its size. No specific spatial correlation evident, but needs more investigation."
    },
    {
      "region": [
        12,
        24,
        18,
        40,
        13
      ],
      "cost": 11840.0,
      "size": 5,
      "spatial_context": "Similar to the previous one, this region has a high cost for its size and needs more analysis regarding spatial distribution of the nodes in this region."
    },
    {
      "region": [
        20,
        38,
        50,
        33
      ],
      "cost": 8010.0,
      "size": 4,
      "spatial_context": "This smaller region with a substantial cost could indicate a locally suboptimal configuration. More investigation of its spatial context is required."
    },
    {
      "region_type": "Low-Density Corridor",
      "description": "Regions where the density grid indicates sparse node distribution (e.g., cells with value 0) potentially represent difficult areas to traverse efficiently."
    }
  ],
  "opportunity_regions": [
    {
      "region_type": "High-Density Cell",
      "description": "Cells with high density values (e.g., cells with value 12 in the density grid) suggest regions where exploiting local connections could lead to improvements."
    },
    {
      "region_type": "Common Subpaths",
      "description": "The common subpaths, specifically (12, 23, 16), (23, 16, 20), and (16, 20, 22), might represent promising edges or routes to exploit further."
    }
  ],
  "evolution_phase": "Exploration",
  "evolution_direction": {
    "phase": "Early Exploration",
    "strategy": "Focus on diversifying the population and sampling across the entire search space.",
    "operator_suggestions": [
      "Apply crossover operators that encourage diversity, such as uniform crossover.",
      "Use mutation operators with a relatively high mutation rate to explore different regions.",
      "Implement a local search operator that focuses on improving solutions within high-density regions of the spatial summary."
    ]
  }
}
```
2025-06-25 10:42:32,652 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-25 10:42:32,652 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'The high range in edge lengths (7.0 to 3222.0) and the large interquartile range (1094.25 to 2273.75) suggest a rugged search space. The large standard deviation in cost (40606.269) further confirms this.', 'modality': "The edge length statistics and the direction histogram suggest a potentially multimodal search space.  The histogram isn't strongly biased towards any particular direction, hinting at multiple potentially good directions to explore.", 'deceptiveness': 'The long_edge_ratio of 0.25 implies that long edges are present, potentially creating deceptive landscapes where short-term improvements might lead away from the global optimum.  The wide cost range within the population also hints at potential deceptiveness.'}, 'population_state': {'coverage': "The diversity is relatively high (0.913) at this early stage, indicating good population coverage of the search space. The density grid shows node distribution across different areas, with some clusters appearing. However, the convergence is 0.0, meaning the population hasn't focused on any single solution yet.", 'convergence': 'The convergence is 0.0, indicating no focused convergence towards specific solutions.'}, 'difficult_regions': [{'region': [37, 23, 26, 16, 25, 22], 'cost': 14286.0, 'size': 6, 'spatial_context': 'This region might be difficult due to the relatively high cost combined with its size. No specific spatial correlation evident, but needs more investigation.'}, {'region': [12, 24, 18, 40, 13], 'cost': 11840.0, 'size': 5, 'spatial_context': 'Similar to the previous one, this region has a high cost for its size and needs more analysis regarding spatial distribution of the nodes in this region.'}, {'region': [20, 38, 50, 33], 'cost': 8010.0, 'size': 4, 'spatial_context': 'This smaller region with a substantial cost could indicate a locally suboptimal configuration. More investigation of its spatial context is required.'}, {'region_type': 'Low-Density Corridor', 'description': 'Regions where the density grid indicates sparse node distribution (e.g., cells with value 0) potentially represent difficult areas to traverse efficiently.'}], 'opportunity_regions': [{'region_type': 'High-Density Cell', 'description': 'Cells with high density values (e.g., cells with value 12 in the density grid) suggest regions where exploiting local connections could lead to improvements.'}, {'region_type': 'Common Subpaths', 'description': 'The common subpaths, specifically (12, 23, 16), (23, 16, 20), and (16, 20, 22), might represent promising edges or routes to exploit further.'}], 'evolution_phase': 'Exploration', 'evolution_direction': {'phase': 'Early Exploration', 'strategy': 'Focus on diversifying the population and sampling across the entire search space.', 'operator_suggestions': ['Apply crossover operators that encourage diversity, such as uniform crossover.', 'Use mutation operators with a relatively high mutation rate to explore different regions.', 'Implement a local search operator that focuses on improving solutions within high-density regions of the spatial summary.']}}
2025-06-25 10:42:32,652 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-25 10:42:32,652 - __main__ - INFO - 分析阶段完成
2025-06-25 10:42:32,652 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'The high range in edge lengths (7.0 to 3222.0) and the large interquartile range (1094.25 to 2273.75) suggest a rugged search space. The large standard deviation in cost (40606.269) further confirms this.', 'modality': "The edge length statistics and the direction histogram suggest a potentially multimodal search space.  The histogram isn't strongly biased towards any particular direction, hinting at multiple potentially good directions to explore.", 'deceptiveness': 'The long_edge_ratio of 0.25 implies that long edges are present, potentially creating deceptive landscapes where short-term improvements might lead away from the global optimum.  The wide cost range within the population also hints at potential deceptiveness.'}, 'population_state': {'coverage': "The diversity is relatively high (0.913) at this early stage, indicating good population coverage of the search space. The density grid shows node distribution across different areas, with some clusters appearing. However, the convergence is 0.0, meaning the population hasn't focused on any single solution yet.", 'convergence': 'The convergence is 0.0, indicating no focused convergence towards specific solutions.'}, 'difficult_regions': [{'region': [37, 23, 26, 16, 25, 22], 'cost': 14286.0, 'size': 6, 'spatial_context': 'This region might be difficult due to the relatively high cost combined with its size. No specific spatial correlation evident, but needs more investigation.'}, {'region': [12, 24, 18, 40, 13], 'cost': 11840.0, 'size': 5, 'spatial_context': 'Similar to the previous one, this region has a high cost for its size and needs more analysis regarding spatial distribution of the nodes in this region.'}, {'region': [20, 38, 50, 33], 'cost': 8010.0, 'size': 4, 'spatial_context': 'This smaller region with a substantial cost could indicate a locally suboptimal configuration. More investigation of its spatial context is required.'}, {'region_type': 'Low-Density Corridor', 'description': 'Regions where the density grid indicates sparse node distribution (e.g., cells with value 0) potentially represent difficult areas to traverse efficiently.'}], 'opportunity_regions': [{'region_type': 'High-Density Cell', 'description': 'Cells with high density values (e.g., cells with value 12 in the density grid) suggest regions where exploiting local connections could lead to improvements.'}, {'region_type': 'Common Subpaths', 'description': 'The common subpaths, specifically (12, 23, 16), (23, 16, 20), and (16, 20, 22), might represent promising edges or routes to exploit further.'}], 'evolution_phase': 'Exploration', 'evolution_direction': {'phase': 'Early Exploration', 'strategy': 'Focus on diversifying the population and sampling across the entire search space.', 'operator_suggestions': ['Apply crossover operators that encourage diversity, such as uniform crossover.', 'Use mutation operators with a relatively high mutation rate to explore different regions.', 'Implement a local search operator that focuses on improving solutions within high-density regions of the spatial summary.']}}
2025-06-25 10:42:32,652 - __main__ - INFO - 开始策略分配阶段
2025-06-25 10:42:32,652 - StrategyExpert - INFO - 开始策略分配分析
2025-06-25 10:42:32,652 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 9948.0
  • mean_cost: 71264.3
- Search-space: ruggedness The high range in edge lengths (7.0 to 3222.0) and the large interquartile range (1094.25 to 2273.75) suggest a rugged search space. The large standard deviation in cost (40606.269) further confirms this., modality The edge length statistics and the direction histogram suggest a potentially multimodal search space.  The histogram isn't strongly biased towards any particular direction, hinting at multiple potentially good directions to explore., deceptiveness The long_edge_ratio of 0.25 implies that long edges are present, potentially creating deceptive landscapes where short-term improvements might lead away from the global optimum.  The wide cost range within the population also hints at potential deceptiveness.
- Evolution phase: Exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 4): {'region': [37, 23, , {'region': [12, 24, ...
- Opportunity regions (# 2): {'region_type': 'Hig, {'region_type': 'Com
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-25 10:42:32,652 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-25 10:42:35,373 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High ruggedness, low diversity, and exploration phase necessitate a high exploration ratio. Best individuals (8 and 9) are assigned to exploit to leverage their potential, while the rest explore."
}
```
2025-06-25 10:42:35,373 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-25 10:42:35,373 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-25 10:42:35,373 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High ruggedness, low diversity, and exploration phase necessitate a high exploration ratio. Best individuals (8 and 9) are assigned to exploit to leverage their potential, while the rest explore."
}
```
2025-06-25 10:42:35,373 - __main__ - INFO - 策略分配阶段完成
2025-06-25 10:42:35,373 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-25 10:42:35,375 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High ruggedness, low diversity, and exploration phase necessitate a high exploration ratio. Best individuals (8 and 9) are assigned to exploit to leverage their potential, while the rest explore."
}
```
2025-06-25 10:42:35,375 - __main__ - INFO - 开始进化阶段
2025-06-25 10:42:35,375 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-25 10:42:35,375 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:42:35,375 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9976.0 (rank 10.0%)
- Population diversity: 0.735593220338983
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region_type':, {'region_type':
- Difficult regions to avoid (sample): {'region': [37,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:42:35,375 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:42:38,707 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 56, 18, 29, 55, 42, 46, 17, 24, 36, 5, 10, 30, 4, 52, 47, 59, 35, 23, 8, 16, 51, 33, 15, 6, 26, 28, 38, 50, 22, 31, 1, 3, 43, 41, 9, 19, 14, 58, 11, 21, 44, 34, 27, 57, 20, 49, 13, 7, 40, 0, 45, 54, 39, 48, 53, 37, 12, 25, 32],
  "targeted_regions": "Leverage sparse cells and opportunity region {'region_type':}. Avoid difficult region [37,",
  "strategy_comment": "Explore low density regions while steering clear of known problem areas."
}
```
2025-06-25 10:42:38,707 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:42:38,707 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98906.0, 路径: [2, 56, 18, 29, 55, 42, 46, 17, 24, 36, 5, 10, 30, 4, 52, 47, 59, 35, 23, 8, 16, 51, 33, 15, 6, 26, 28, 38, 50, 22, 31, 1, 3, 43, 41, 9, 19, 14, 58, 11, 21, 44, 34, 27, 57, 20, 49, 13, 7, 40, 0, 45, 54, 39, 48, 53, 37, 12, 25, 32]
2025-06-25 10:42:38,709 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [2, 56, 18, 29, 55, 42, 46, 17, 24, 36, 5, 10, 30, 4, 52, 47, 59, 35, 23, 8, 16, 51, 33, 15, 6, 26, 28, 38, 50, 22, 31, 1, 3, 43, 41, 9, 19, 14, 58, 11, 21, 44, 34, 27, 57, 20, 49, 13, 7, 40, 0, 45, 54, 39, 48, 53, 37, 12, 25, 32], 'cur_cost': 98906.0}
2025-06-25 10:42:38,709 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-25 10:42:38,709 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:42:38,709 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9948.0 (rank 0.0%)
- Population diversity: 0.7847457627118644
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region_type':, {'region_type':
- Difficult regions to avoid (sample): {'region': [37,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:42:38,709 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:42:43,563 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [45, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 37],
  "targeted_regions": "Regions near nodes 37, 12 (avoidance) and 45-59 (opportunity).",
  "strategy_comment": "Shifted segment to explore adjacent space near an opportunity region and end with avoiding hard area."
}
```
2025-06-25 10:42:43,563 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:42:43,563 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13173.0, 路径: [45, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 37]
2025-06-25 10:42:43,565 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [45, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 37], 'cur_cost': 13173.0}
2025-06-25 10:42:43,565 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-25 10:42:43,565 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:42:43,565 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10246.0 (rank 0.0%)
- Population diversity: 0.847457627118644
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region_type':, {'region_type':
- Difficult regions to avoid (sample): {'region': [37,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:42:43,565 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:42:47,066 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [16, 56, 55, 23, 27, 52, 26, 4, 5, 25, 51, 30, 18, 35, 58, 54, 41, 24, 47, 46, 19, 14, 59, 36, 49, 3, 22, 34, 50, 45, 42, 8, 2, 1, 48, 33, 7, 11, 15, 13, 38, 6, 21, 20, 32, 31, 40, 9, 10, 39, 44, 43, 57, 0, 17, 53, 28, 29, 12, 37],
  "targeted_regions": "Explore near nodes 3, 22, 34 and avoids cluster around node 12.",
  "strategy_comment": "Diversify: Moved path sections to sparse areas, avoid dense areas."
}
```
2025-06-25 10:42:47,066 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:42:47,066 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93457.0, 路径: [16, 56, 55, 23, 27, 52, 26, 4, 5, 25, 51, 30, 18, 35, 58, 54, 41, 24, 47, 46, 19, 14, 59, 36, 49, 3, 22, 34, 50, 45, 42, 8, 2, 1, 48, 33, 7, 11, 15, 13, 38, 6, 21, 20, 32, 31, 40, 9, 10, 39, 44, 43, 57, 0, 17, 53, 28, 29, 12, 37]
2025-06-25 10:42:47,066 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [16, 56, 55, 23, 27, 52, 26, 4, 5, 25, 51, 30, 18, 35, 58, 54, 41, 24, 47, 46, 19, 14, 59, 36, 49, 3, 22, 34, 50, 45, 42, 8, 2, 1, 48, 33, 7, 11, 15, 13, 38, 6, 21, 20, 32, 31, 40, 9, 10, 39, 44, 43, 57, 0, 17, 53, 28, 29, 12, 37], 'cur_cost': 93457.0}
2025-06-25 10:42:47,066 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-25 10:42:47,066 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:42:47,066 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101904.0 (rank 80.0%)
- Population diversity: 0.8423728813559322
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region_type':, {'region_type':
- Difficult regions to avoid (sample): {'region': [37,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:42:47,066 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:42:51,367 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [15, 26, 56, 40, 47, 11, 32, 53, 17, 25, 29, 34, 46, 4, 54, 55, 2, 1, 51, 57, 43, 48, 24, 39, 58, 36, 8, 22, 30, 31, 49, 42, 21, 14, 52, 10, 6, 28, 19, 33, 5, 35, 41, 0, 18, 20, 16, 44, 27, 13, 3, 7, 9, 23, 37, 12, 59, 50, 45, 38],
  "targeted_regions": "Leveraged opportunity region around node 29 and explored sparse cells near 57.",
  "strategy_comment": "Shifted segment to prioritize opportunity region; diversified route in sparse area."
}
```
2025-06-25 10:42:51,367 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:42:51,367 - ExplorationExpert - INFO - 探索路径生成完成，成本: 94672.0, 路径: [15, 26, 56, 40, 47, 11, 32, 53, 17, 25, 29, 34, 46, 4, 54, 55, 2, 1, 51, 57, 43, 48, 24, 39, 58, 36, 8, 22, 30, 31, 49, 42, 21, 14, 52, 10, 6, 28, 19, 33, 5, 35, 41, 0, 18, 20, 16, 44, 27, 13, 3, 7, 9, 23, 37, 12, 59, 50, 45, 38]
2025-06-25 10:42:51,367 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [15, 26, 56, 40, 47, 11, 32, 53, 17, 25, 29, 34, 46, 4, 54, 55, 2, 1, 51, 57, 43, 48, 24, 39, 58, 36, 8, 22, 30, 31, 49, 42, 21, 14, 52, 10, 6, 28, 19, 33, 5, 35, 41, 0, 18, 20, 16, 44, 27, 13, 3, 7, 9, 23, 37, 12, 59, 50, 45, 38], 'cur_cost': 94672.0}
2025-06-25 10:42:51,367 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-25 10:42:51,367 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:42:51,367 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 95734.0 (rank 50.0%)
- Population diversity: 0.8338983050847457
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region_type':, {'region_type':
- Difficult regions to avoid (sample): {'region': [37,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:42:51,367 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:42:54,972 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    52,
    11,
    33,
    4,
    58,
    45,
    26,
    5,
    1,
    14,
    30,
    41,
    24,
    10,
    6,
    22,
    54,
    15,
    35,
    43,
    39,
    34,
    25,
    9,
    55,
    48,
    27,
    17,
    19,
    40,
    57,
    3,
    56,
    44,
    8,
    29,
    36,
    51,
    20,
    47,
    16,
    23,
    49,
    38,
    21,
    0,
    18,
    42,
    13,
    50,
    32,
    2,
    46,
    7,
    31,
    12,
    37,
    53,
    28,
    59
  ],
  "targeted_regions": "Explored low-density cells and leveraged initial segments, avoided region [12,...",
  "strategy_comment": "Minor perturbations, exploring sparse areas while avoiding difficult regions."
}
```
2025-06-25 10:42:54,972 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:42:54,972 - ExplorationExpert - INFO - 探索路径生成完成，成本: 104419.0, 路径: [52, 11, 33, 4, 58, 45, 26, 5, 1, 14, 30, 41, 24, 10, 6, 22, 54, 15, 35, 43, 39, 34, 25, 9, 55, 48, 27, 17, 19, 40, 57, 3, 56, 44, 8, 29, 36, 51, 20, 47, 16, 23, 49, 38, 21, 0, 18, 42, 13, 50, 32, 2, 46, 7, 31, 12, 37, 53, 28, 59]
2025-06-25 10:42:54,972 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [52, 11, 33, 4, 58, 45, 26, 5, 1, 14, 30, 41, 24, 10, 6, 22, 54, 15, 35, 43, 39, 34, 25, 9, 55, 48, 27, 17, 19, 40, 57, 3, 56, 44, 8, 29, 36, 51, 20, 47, 16, 23, 49, 38, 21, 0, 18, 42, 13, 50, 32, 2, 46, 7, 31, 12, 37, 53, 28, 59], 'cur_cost': 104419.0}
2025-06-25 10:42:54,972 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-25 10:42:54,972 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:42:54,972 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 91478.0 (rank 20.0%)
- Population diversity: 0.8406779661016949
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region_type':, {'region_type':
- Difficult regions to avoid (sample): {'region': [37,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:42:54,976 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:42:58,470 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [41, 18, 51, 52, 49, 50, 30, 2, 54, 47, 15, 29, 58, 5, 43, 14, 20, 26, 53, 27, 11, 17, 3, 46, 24, 38, 28, 9, 48, 40, 31, 16, 34, 35, 59, 25, 32, 42, 4, 19, 57, 39, 8, 23, 33, 6, 13, 21, 10, 45, 7, 36, 12, 0, 56, 1, 55, 22, 44, 37],
  "targeted_regions": "Leveraged sparse cells and adjacent opportunity regions; avoided nodes 37 and 12.",
  "strategy_comment": "Introduced larger perturbations for diversity, while steering clear of difficult areas."
}
```
2025-06-25 10:42:58,470 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:42:58,470 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103024.0, 路径: [41, 18, 51, 52, 49, 50, 30, 2, 54, 47, 15, 29, 58, 5, 43, 14, 20, 26, 53, 27, 11, 17, 3, 46, 24, 38, 28, 9, 48, 40, 31, 16, 34, 35, 59, 25, 32, 42, 4, 19, 57, 39, 8, 23, 33, 6, 13, 21, 10, 45, 7, 36, 12, 0, 56, 1, 55, 22, 44, 37]
2025-06-25 10:42:58,470 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [41, 18, 51, 52, 49, 50, 30, 2, 54, 47, 15, 29, 58, 5, 43, 14, 20, 26, 53, 27, 11, 17, 3, 46, 24, 38, 28, 9, 48, 40, 31, 16, 34, 35, 59, 25, 32, 42, 4, 19, 57, 39, 8, 23, 33, 6, 13, 21, 10, 45, 7, 36, 12, 0, 56, 1, 55, 22, 44, 37], 'cur_cost': 103024.0}
2025-06-25 10:42:58,470 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-25 10:42:58,470 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:42:58,470 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 112557.0 (rank 90.0%)
- Population diversity: 0.8322033898305085
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region_type':, {'region_type':
- Difficult regions to avoid (sample): {'region': [37,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:42:58,470 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:43:01,714 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [17, 42, 5, 47, 29, 22, 51, 4, 31, 15, 1, 57, 58, 43, 3, 46, 24, 33, 13, 20, 50, 41, 35, 2, 8, 19, 39, 56, 25, 53, 10, 6, 30, 28, 40, 48, 55, 9, 27, 38, 26, 36, 23, 59, 52, 16, 34, 49, 11, 18, 45, 0, 14, 32, 21, 54, 7, 12, 37, 44],
  "targeted_regions": "Leverage opportunity regions [37, ...] by re-ordering neighbors and explore cells near opportunity regions",
  "strategy_comment": "Introduced swaps and small local changes near opportunity regions. Avoided known difficult segments."
}
```
2025-06-25 10:43:01,714 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:43:01,714 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102928.0, 路径: [17, 42, 5, 47, 29, 22, 51, 4, 31, 15, 1, 57, 58, 43, 3, 46, 24, 33, 13, 20, 50, 41, 35, 2, 8, 19, 39, 56, 25, 53, 10, 6, 30, 28, 40, 48, 55, 9, 27, 38, 26, 36, 23, 59, 52, 16, 34, 49, 11, 18, 45, 0, 14, 32, 21, 54, 7, 12, 37, 44]
2025-06-25 10:43:01,714 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [17, 42, 5, 47, 29, 22, 51, 4, 31, 15, 1, 57, 58, 43, 3, 46, 24, 33, 13, 20, 50, 41, 35, 2, 8, 19, 39, 56, 25, 53, 10, 6, 30, 28, 40, 48, 55, 9, 27, 38, 26, 36, 23, 59, 52, 16, 34, 49, 11, 18, 45, 0, 14, 32, 21, 54, 7, 12, 37, 44], 'cur_cost': 102928.0}
2025-06-25 10:43:01,714 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-25 10:43:01,714 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:43:01,714 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 97556.0 (rank 40.0%)
- Population diversity: 0.8389830508474576
- Evolution phase: Exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region_type':, {'region_type':
- Difficult regions to avoid (sample): {'region': [37,, {'region': [12,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:43:01,714 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:43:04,946 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    21,
    23,
    54,
    42,
    31,
    40,
    10,
    50,
    39,
    17,
    38,
    26,
    1,
    48,
    57,
    22,
    35,
    49,
    20,
    43,
    34,
    15,
    53,
    56,
    51,
    47,
    59,
    8,
    11,
    16,
    6,
    25,
    29,
    41,
    33,
    28,
    5,
    27,
    36,
    46,
    0,
    18,
    44,
    30,
    58,
    14,
    7,
    32,
    3,
    2,
    19,
    45,
    9,
    52,
    55,
    4,
    24,
    13,
    12,
    37
  ],
  "targeted_regions": "Explored cells near opportunity regions while avoiding difficult blocks.",
  "strategy_comment": "Shifted nodes near sparse cells, maintained TSP validity. Higher diversity focus."
}
```
2025-06-25 10:43:04,946 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:43:04,946 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97247.0, 路径: [21, 23, 54, 42, 31, 40, 10, 50, 39, 17, 38, 26, 1, 48, 57, 22, 35, 49, 20, 43, 34, 15, 53, 56, 51, 47, 59, 8, 11, 16, 6, 25, 29, 41, 33, 28, 5, 27, 36, 46, 0, 18, 44, 30, 58, 14, 7, 32, 3, 2, 19, 45, 9, 52, 55, 4, 24, 13, 12, 37]
2025-06-25 10:43:04,946 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [21, 23, 54, 42, 31, 40, 10, 50, 39, 17, 38, 26, 1, 48, 57, 22, 35, 49, 20, 43, 34, 15, 53, 56, 51, 47, 59, 8, 11, 16, 6, 25, 29, 41, 33, 28, 5, 27, 36, 46, 0, 18, 44, 30, 58, 14, 7, 32, 3, 2, 19, 45, 9, 52, 55, 4, 24, 13, 12, 37], 'cur_cost': 97247.0}
2025-06-25 10:43:04,946 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-25 10:43:04,950 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-25 10:43:04,951 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-25 10:43:04,951 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 82634.0
2025-06-25 10:43:07,865 - ExploitationExpert - INFO - res_population_num: 1
2025-06-25 10:43:07,865 - ExploitationExpert - INFO - res_population_costs: [87470]
2025-06-25 10:43:07,865 - ExploitationExpert - INFO - res_populations: [array([ 0, 55, 46, 29,  1, 21, 58, 51, 36, 24, 56, 23, 16, 15, 49, 45, 31,
        2, 52, 38, 27,  7,  9,  3, 28, 34, 44, 11, 35, 14, 18, 19, 20, 48,
        8, 47, 39, 50, 32, 13, 57, 22, 30, 59, 40, 41, 54,  5, 10, 37, 53,
       33, 25, 43, 17, 26, 42,  4, 12,  6], dtype=int64)]
2025-06-25 10:43:07,865 - ExploitationExpert - INFO - populations_num: 10
2025-06-25 10:43:07,865 - ExploitationExpert - INFO - populations: [{'tour': [2, 56, 18, 29, 55, 42, 46, 17, 24, 36, 5, 10, 30, 4, 52, 47, 59, 35, 23, 8, 16, 51, 33, 15, 6, 26, 28, 38, 50, 22, 31, 1, 3, 43, 41, 9, 19, 14, 58, 11, 21, 44, 34, 27, 57, 20, 49, 13, 7, 40, 0, 45, 54, 39, 48, 53, 37, 12, 25, 32], 'cur_cost': 98906.0}, {'tour': [45, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 37], 'cur_cost': 13173.0}, {'tour': [16, 56, 55, 23, 27, 52, 26, 4, 5, 25, 51, 30, 18, 35, 58, 54, 41, 24, 47, 46, 19, 14, 59, 36, 49, 3, 22, 34, 50, 45, 42, 8, 2, 1, 48, 33, 7, 11, 15, 13, 38, 6, 21, 20, 32, 31, 40, 9, 10, 39, 44, 43, 57, 0, 17, 53, 28, 29, 12, 37], 'cur_cost': 93457.0}, {'tour': [15, 26, 56, 40, 47, 11, 32, 53, 17, 25, 29, 34, 46, 4, 54, 55, 2, 1, 51, 57, 43, 48, 24, 39, 58, 36, 8, 22, 30, 31, 49, 42, 21, 14, 52, 10, 6, 28, 19, 33, 5, 35, 41, 0, 18, 20, 16, 44, 27, 13, 3, 7, 9, 23, 37, 12, 59, 50, 45, 38], 'cur_cost': 94672.0}, {'tour': [52, 11, 33, 4, 58, 45, 26, 5, 1, 14, 30, 41, 24, 10, 6, 22, 54, 15, 35, 43, 39, 34, 25, 9, 55, 48, 27, 17, 19, 40, 57, 3, 56, 44, 8, 29, 36, 51, 20, 47, 16, 23, 49, 38, 21, 0, 18, 42, 13, 50, 32, 2, 46, 7, 31, 12, 37, 53, 28, 59], 'cur_cost': 104419.0}, {'tour': [41, 18, 51, 52, 49, 50, 30, 2, 54, 47, 15, 29, 58, 5, 43, 14, 20, 26, 53, 27, 11, 17, 3, 46, 24, 38, 28, 9, 48, 40, 31, 16, 34, 35, 59, 25, 32, 42, 4, 19, 57, 39, 8, 23, 33, 6, 13, 21, 10, 45, 7, 36, 12, 0, 56, 1, 55, 22, 44, 37], 'cur_cost': 103024.0}, {'tour': [17, 42, 5, 47, 29, 22, 51, 4, 31, 15, 1, 57, 58, 43, 3, 46, 24, 33, 13, 20, 50, 41, 35, 2, 8, 19, 39, 56, 25, 53, 10, 6, 30, 28, 40, 48, 55, 9, 27, 38, 26, 36, 23, 59, 52, 16, 34, 49, 11, 18, 45, 0, 14, 32, 21, 54, 7, 12, 37, 44], 'cur_cost': 102928.0}, {'tour': [21, 23, 54, 42, 31, 40, 10, 50, 39, 17, 38, 26, 1, 48, 57, 22, 35, 49, 20, 43, 34, 15, 53, 56, 51, 47, 59, 8, 11, 16, 6, 25, 29, 41, 33, 28, 5, 27, 36, 46, 0, 18, 44, 30, 58, 14, 7, 32, 3, 2, 19, 45, 9, 52, 55, 4, 24, 13, 12, 37], 'cur_cost': 97247.0}, {'tour': array([ 2, 54, 12, 16, 23, 47, 34,  7, 24,  9, 56, 18,  4, 26, 45, 58, 14,
       20, 17, 40, 44,  5, 46, 13, 32, 30, 33, 28, 52, 35,  8, 15, 42, 37,
       39, 22, 21,  3, 19, 38, 29, 25, 51, 49, 50, 55, 11, 10, 27, 41,  1,
       43, 53, 59, 31, 48,  6, 36,  0, 57]), 'cur_cost': 82634.0}, {'tour': [6, 37, 23, 26, 16, 25, 22, 2, 9, 1, 53, 31, 27, 57, 33, 52, 50, 55, 39, 45, 10, 20, 19, 34, 24, 14, 18, 12, 44, 59, 40, 13, 17, 11, 32, 28, 54, 56, 3, 21, 48, 35, 51, 58, 5, 42, 46, 49, 15, 36, 7, 30, 4, 29, 8, 0, 41, 43, 38, 47], 'cur_cost': 85371.0}]
2025-06-25 10:43:07,865 - ExploitationExpert - INFO - 局部搜索耗时: 2.91秒
2025-06-25 10:43:07,865 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-25 10:43:07,865 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-25 10:43:07,871 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-25 10:43:07,871 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-25 10:43:07,871 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-25 10:43:07,871 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 98229.0
2025-06-25 10:43:08,889 - ExploitationExpert - INFO - res_population_num: 2
2025-06-25 10:43:08,892 - ExploitationExpert - INFO - res_population_costs: [87470, 9644]
2025-06-25 10:43:08,892 - ExploitationExpert - INFO - res_populations: [array([ 0, 55, 46, 29,  1, 21, 58, 51, 36, 24, 56, 23, 16, 15, 49, 45, 31,
        2, 52, 38, 27,  7,  9,  3, 28, 34, 44, 11, 35, 14, 18, 19, 20, 48,
        8, 47, 39, 50, 32, 13, 57, 22, 30, 59, 40, 41, 54,  5, 10, 37, 53,
       33, 25, 43, 17, 26, 42,  4, 12,  6], dtype=int64), array([ 0,  1,  6,  5,  9,  3,  2,  7, 11, 10,  8, 26, 34, 30, 29, 33, 24,
       27, 28, 35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64)]
2025-06-25 10:43:08,893 - ExploitationExpert - INFO - populations_num: 10
2025-06-25 10:43:08,893 - ExploitationExpert - INFO - populations: [{'tour': [2, 56, 18, 29, 55, 42, 46, 17, 24, 36, 5, 10, 30, 4, 52, 47, 59, 35, 23, 8, 16, 51, 33, 15, 6, 26, 28, 38, 50, 22, 31, 1, 3, 43, 41, 9, 19, 14, 58, 11, 21, 44, 34, 27, 57, 20, 49, 13, 7, 40, 0, 45, 54, 39, 48, 53, 37, 12, 25, 32], 'cur_cost': 98906.0}, {'tour': [45, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 37], 'cur_cost': 13173.0}, {'tour': [16, 56, 55, 23, 27, 52, 26, 4, 5, 25, 51, 30, 18, 35, 58, 54, 41, 24, 47, 46, 19, 14, 59, 36, 49, 3, 22, 34, 50, 45, 42, 8, 2, 1, 48, 33, 7, 11, 15, 13, 38, 6, 21, 20, 32, 31, 40, 9, 10, 39, 44, 43, 57, 0, 17, 53, 28, 29, 12, 37], 'cur_cost': 93457.0}, {'tour': [15, 26, 56, 40, 47, 11, 32, 53, 17, 25, 29, 34, 46, 4, 54, 55, 2, 1, 51, 57, 43, 48, 24, 39, 58, 36, 8, 22, 30, 31, 49, 42, 21, 14, 52, 10, 6, 28, 19, 33, 5, 35, 41, 0, 18, 20, 16, 44, 27, 13, 3, 7, 9, 23, 37, 12, 59, 50, 45, 38], 'cur_cost': 94672.0}, {'tour': [52, 11, 33, 4, 58, 45, 26, 5, 1, 14, 30, 41, 24, 10, 6, 22, 54, 15, 35, 43, 39, 34, 25, 9, 55, 48, 27, 17, 19, 40, 57, 3, 56, 44, 8, 29, 36, 51, 20, 47, 16, 23, 49, 38, 21, 0, 18, 42, 13, 50, 32, 2, 46, 7, 31, 12, 37, 53, 28, 59], 'cur_cost': 104419.0}, {'tour': [41, 18, 51, 52, 49, 50, 30, 2, 54, 47, 15, 29, 58, 5, 43, 14, 20, 26, 53, 27, 11, 17, 3, 46, 24, 38, 28, 9, 48, 40, 31, 16, 34, 35, 59, 25, 32, 42, 4, 19, 57, 39, 8, 23, 33, 6, 13, 21, 10, 45, 7, 36, 12, 0, 56, 1, 55, 22, 44, 37], 'cur_cost': 103024.0}, {'tour': [17, 42, 5, 47, 29, 22, 51, 4, 31, 15, 1, 57, 58, 43, 3, 46, 24, 33, 13, 20, 50, 41, 35, 2, 8, 19, 39, 56, 25, 53, 10, 6, 30, 28, 40, 48, 55, 9, 27, 38, 26, 36, 23, 59, 52, 16, 34, 49, 11, 18, 45, 0, 14, 32, 21, 54, 7, 12, 37, 44], 'cur_cost': 102928.0}, {'tour': [21, 23, 54, 42, 31, 40, 10, 50, 39, 17, 38, 26, 1, 48, 57, 22, 35, 49, 20, 43, 34, 15, 53, 56, 51, 47, 59, 8, 11, 16, 6, 25, 29, 41, 33, 28, 5, 27, 36, 46, 0, 18, 44, 30, 58, 14, 7, 32, 3, 2, 19, 45, 9, 52, 55, 4, 24, 13, 12, 37], 'cur_cost': 97247.0}, {'tour': array([ 2, 54, 12, 16, 23, 47, 34,  7, 24,  9, 56, 18,  4, 26, 45, 58, 14,
       20, 17, 40, 44,  5, 46, 13, 32, 30, 33, 28, 52, 35,  8, 15, 42, 37,
       39, 22, 21,  3, 19, 38, 29, 25, 51, 49, 50, 55, 11, 10, 27, 41,  1,
       43, 53, 59, 31, 48,  6, 36,  0, 57]), 'cur_cost': 82634.0}, {'tour': array([33,  2, 14, 23, 11, 48, 34, 12, 53, 13, 37,  7, 52, 58, 51, 20,  8,
       35,  5, 17, 55,  1, 50, 43, 54,  6, 40, 26, 25, 44,  0, 56, 41, 19,
        4, 15, 57, 46, 29, 27, 59, 30, 18,  3, 28, 45, 38, 49,  9, 24, 31,
       47, 10, 16, 21, 36, 32, 39, 42, 22]), 'cur_cost': 98229.0}]
2025-06-25 10:43:08,893 - ExploitationExpert - INFO - 局部搜索耗时: 1.02秒
2025-06-25 10:43:08,893 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-25 10:43:08,893 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-25 10:43:08,893 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [2, 56, 18, 29, 55, 42, 46, 17, 24, 36, 5, 10, 30, 4, 52, 47, 59, 35, 23, 8, 16, 51, 33, 15, 6, 26, 28, 38, 50, 22, 31, 1, 3, 43, 41, 9, 19, 14, 58, 11, 21, 44, 34, 27, 57, 20, 49, 13, 7, 40, 0, 45, 54, 39, 48, 53, 37, 12, 25, 32], 'cur_cost': 98906.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [45, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 43, 37], 'cur_cost': 13173.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [16, 56, 55, 23, 27, 52, 26, 4, 5, 25, 51, 30, 18, 35, 58, 54, 41, 24, 47, 46, 19, 14, 59, 36, 49, 3, 22, 34, 50, 45, 42, 8, 2, 1, 48, 33, 7, 11, 15, 13, 38, 6, 21, 20, 32, 31, 40, 9, 10, 39, 44, 43, 57, 0, 17, 53, 28, 29, 12, 37], 'cur_cost': 93457.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [15, 26, 56, 40, 47, 11, 32, 53, 17, 25, 29, 34, 46, 4, 54, 55, 2, 1, 51, 57, 43, 48, 24, 39, 58, 36, 8, 22, 30, 31, 49, 42, 21, 14, 52, 10, 6, 28, 19, 33, 5, 35, 41, 0, 18, 20, 16, 44, 27, 13, 3, 7, 9, 23, 37, 12, 59, 50, 45, 38], 'cur_cost': 94672.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [52, 11, 33, 4, 58, 45, 26, 5, 1, 14, 30, 41, 24, 10, 6, 22, 54, 15, 35, 43, 39, 34, 25, 9, 55, 48, 27, 17, 19, 40, 57, 3, 56, 44, 8, 29, 36, 51, 20, 47, 16, 23, 49, 38, 21, 0, 18, 42, 13, 50, 32, 2, 46, 7, 31, 12, 37, 53, 28, 59], 'cur_cost': 104419.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [41, 18, 51, 52, 49, 50, 30, 2, 54, 47, 15, 29, 58, 5, 43, 14, 20, 26, 53, 27, 11, 17, 3, 46, 24, 38, 28, 9, 48, 40, 31, 16, 34, 35, 59, 25, 32, 42, 4, 19, 57, 39, 8, 23, 33, 6, 13, 21, 10, 45, 7, 36, 12, 0, 56, 1, 55, 22, 44, 37], 'cur_cost': 103024.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [17, 42, 5, 47, 29, 22, 51, 4, 31, 15, 1, 57, 58, 43, 3, 46, 24, 33, 13, 20, 50, 41, 35, 2, 8, 19, 39, 56, 25, 53, 10, 6, 30, 28, 40, 48, 55, 9, 27, 38, 26, 36, 23, 59, 52, 16, 34, 49, 11, 18, 45, 0, 14, 32, 21, 54, 7, 12, 37, 44], 'cur_cost': 102928.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [21, 23, 54, 42, 31, 40, 10, 50, 39, 17, 38, 26, 1, 48, 57, 22, 35, 49, 20, 43, 34, 15, 53, 56, 51, 47, 59, 8, 11, 16, 6, 25, 29, 41, 33, 28, 5, 27, 36, 46, 0, 18, 44, 30, 58, 14, 7, 32, 3, 2, 19, 45, 9, 52, 55, 4, 24, 13, 12, 37], 'cur_cost': 97247.0}}]
2025-06-25 10:43:08,893 - __main__ - INFO - 进化阶段完成
2025-06-25 10:43:08,893 - __main__ - INFO - 开始评估阶段
2025-06-25 10:43:08,893 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-25 10:43:08,893 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-25 10:43:08,896 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9948.0 → 13173.0 (Δ -3225.0)
- Mean cost: 71264.3 → 88868.9
- Diversity: 0.736 → 0.861 (Δ 0.1254)

#### Elite metrics
- Elite count: 0 → 2
- Best elite cost: 9948.0 → 9644 (Δ 304.0)

#### Strategy performance
- Explore: 8 inds, success 37.5% , avg Δ -22303.38
- Exploit: 2 inds, success 50.0% , avg Δ 1190.5

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-25 10:43:11,672 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "Early Stage - Initial Progress",
  "strategy_effectiveness": "Explore strategy shows higher potential for larger negative cost changes (improvements), despite a lower success rate. Exploit is more reliable but provides marginal gains. Elite improvement is primarily driven by some form of directed search, given the existence of elite individuals and their declining cost.",
  "balance_state": "Explore strategy is underutilized. Increasing the proportion of explore operations could lead to faster overall improvement, despite the higher risk of regression.",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.7,
    "exploit_mutation_rate": 0.05,
    "explore_mutation_rate": 0.2
  }
}
```
2025-06-25 10:43:11,697 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-25 10:43:11,697 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "Early Stage - Initial Progress",
  "strategy_effectiveness": "Explore strategy shows higher potential for larger negative cost changes (improvements), despite a lower success rate. Exploit is more reliable but provides marginal gains. Elite improvement is primarily driven by some form of directed search, given the existence of elite individuals and their declining cost.",
  "balance_state": "Explore strategy is underutilized. Increasing the proportion of explore operations could lead to faster overall improvement, despite the higher risk of regression.",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.7,
    "exploit_mutation_rate": 0.05,
    "explore_mutation_rate": 0.2
  }
}
```
2025-06-25 10:43:11,698 - __main__ - INFO - 评估阶段完成
2025-06-25 10:43:11,698 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "Early Stage - Initial Progress",
  "strategy_effectiveness": "Explore strategy shows higher potential for larger negative cost changes (improvements), despite a lower success rate. Exploit is more reliable but provides marginal gains. Elite improvement is primarily driven by some form of directed search, given the existence of elite individuals and their declining cost.",
  "balance_state": "Explore strategy is underutilized. Increasing the proportion of explore operations could lead to faster overall improvement, despite the higher risk of regression.",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.7,
    "exploit_mutation_rate": 0.05,
    "explore_mutation_rate": 0.2
  }
}
```
2025-06-25 10:43:11,698 - __main__ - INFO - 当前最佳适应度: 13173.0
2025-06-25 10:43:11,700 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite12_60_route_0.pkl
2025-06-25 10:43:11,700 - __main__ - INFO - composite12_60 开始进化第 2 代
2025-06-25 10:43:11,700 - __main__ - INFO - 开始分析阶段
2025-06-25 10:43:11,700 - StatsExpert - INFO - 开始统计分析
2025-06-25 10:43:11,713 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 13173.0, 'max': 104419.0, 'mean': 88868.9, 'std': 25926.527058015312}, 'diversity': 0.9625925925925926, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-25 10:43:11,713 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 13173.0, 'max': 104419.0, 'mean': 88868.9, 'std': 25926.527058015312}, 'diversity_level': 0.9625925925925926, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[1380, 1590], [1386, 1610], [1446, 1664], [1473, 1651], [1403, 1573], [1429, 1576], [1414, 1596], [1421, 1664], [1385, 1662], [1475, 1616], [1405, 1625], [1439, 1639], [3242, 2428], [3193, 2490], [3212, 2461], [3238, 2498], [3270, 2452], [3236, 2513], [3196, 2449], [3220, 2481], [3262, 2465], [3198, 2468], [3267, 2481], [3264, 2442], [426, 2488], [478, 2496], [499, 2467], [445, 2489], [451, 2481], [441, 2477], [461, 2463], [511, 2487], [499, 2478], [431, 2471], [461, 2456], [476, 2486], [832, 400], [885, 348], [838, 364], [921, 379], [900, 346], [918, 411], [855, 361], [866, 388], [878, 327], [841, 380], [902, 374], [871, 361], [2763, 1531], [2817, 1507], [2789, 1453], [2739, 1464], [2773, 1490], [2783, 1514], [2808, 1535], [2797, 1510], [2807, 1517], [2793, 1495], [2828, 1490], [2736, 1497]], 'distance_matrix': array([[   0.,   21.,   99., ..., 1416., 1451., 1359.],
       [  21.,    0.,   81., ..., 1412., 1447., 1355.],
       [  99.,   81.,    0., ..., 1358., 1393., 1301.],
       ...,
       [1416., 1412., 1358., ...,    0.,   35.,   57.],
       [1451., 1447., 1393., ...,   35.,    0.,   92.],
       [1359., 1355., 1301., ...,   57.,   92.,    0.]])}
2025-06-25 10:43:11,718 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-25 10:43:11,718 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-25 10:43:11,718 - PathExpert - INFO - 开始路径结构分析
2025-06-25 10:43:11,720 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-25 10:43:11,721 - PathExpert - INFO - 路径结构分析完成
2025-06-25 10:43:11,721 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-25 10:43:11,721 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-25 10:43:11,721 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-25 10:43:11,721 - EliteExpert - INFO - 开始精英解分析
2025-06-25 10:43:11,722 - EliteExpert - INFO - 精英解分析完成
2025-06-25 10:43:11,722 - __main__ - INFO - 精英专家分析报告: {'elite_count': 2, 'elite_common_features': {'common_edges': {'(9, 3)': 1.0}, 'common_edge_ratio': 0.016666666666666666}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 3529.0, 'avg_gap': 40311.899999999994}, 'structure_gap': {'unique_elite_edges': 93, 'unique_pop_edges': 529, 'common_edges': 26}}, 'elite_diversity': {'diversity_score': 0.95}}
2025-06-25 10:43:11,722 - __main__ - INFO - 更新共享数据: elite_features
2025-06-25 10:43:11,723 - LandscapeExpert - INFO - 开始景观分析
2025-06-25 10:43:11,724 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-25 10:43:11,724 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 13173.0, mean 88868.9, max 104419.0, std 25926.527058015312
- diversity: 0.9625925925925926
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [13173.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [426.0, 327.0, 3270.0, 2513.0]
- centroid/spread: [1756.217, 1687.917] | rms 1327.809
- density_grid(3x3): [[12, 0, 0], [0, 12, 12], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [7.0, 1094.25, 1427.5, 2273.75, 3222.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 7, 6, 9, 12, 8, 4, 7]
- nn_median_dist: 17.242

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-25 10:43:18,741 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "The large range in edge lengths (7.0 to 3222.0), combined with a relatively high long_edge_ratio (0.25), suggests a rugged search space. Many possible solutions are significantly different from each other, and small changes in a tour can lead to drastic cost changes.",
    "modality": "The lack of strong structural signals (empty high_quality_edges_sample and common_subpaths_sample) indicates a potentially multi-modal landscape. The cost statistics also support this, with a wide range between the min and max and a sizable standard deviation.",
    "deceptiveness": "The discrepancy between short and long edges suggests the presence of potentially deceptive local optima. Short edges may lead to small improvements, but longer edges might be necessary to escape local traps and reach the global optimum."
  },
  "population_state": {
    "coverage": "The diversity is high (0.9626), indicating good population coverage across the search space. The density grid reveals uneven distribution with some cells heavily populated and others completely empty.",
    "convergence": "The convergence is currently at 0.0, indicating that the population hasn't converged to any specific part of the search space yet. Given high diversity and initial iteration, this is expected."
  },
  "difficult_regions": {
    "description": "Difficult regions are inferred based on low-density areas and the presence of long edges.",
    "spatial_indicators": [
      "Cells with density 0 in the density grid are likely to be difficult regions. Specifically, the top middle, top right, middle left, bottom middle cells are likely difficult.",
      "Areas connected by the longest edges (edge lengths approaching 3222.0) are potentially difficult regions to traverse."
    ],
    "structural_indicators": "Lack of identified high-quality edges or common subpaths reinforces the idea that certain connections/routes are inherently difficult."
  },
  "opportunity_regions": {
    "description": "Opportunity regions are inferred from high-density grid cells, suggesting potentially beneficial node clusters or connections.",
    "spatial_indicators": [
      "The high-density cells in the density grid suggest potential opportunity regions. Specifically, the top left, middle middle, middle right, and bottom right cells warrant further exploration. These areas may contain clusters of nodes that can be connected efficiently.",
      "Further investigation into node arrangements within these high-density cells is needed to identify specific opportunity edges."
    ],
    "structural_indicators": "Need more structural information to identify concrete opportunity regions. The 'elite_common_features_sample' being empty and the lack of high quality edges identified suggests there's no clear structure identified so far."
  },
  "evolution_phase": "Exploration phase. The high diversity and lack of convergence indicate that the algorithm is still exploring the search space.",
  "evolution_direction": {
    "suggested_operators": [
      "Enhance exploration: Utilize operators that promote diversity and explore new regions of the search space. Examples include random swaps, inversions, or more aggressive crossover operators.",
      "Target high-density cells: Focus search efforts on the high-density grid cells to discover better connections within these promising regions. Local search operators could be applied within these regions.",
      "Address difficult regions: Implement strategies to overcome long edges that potentially act as barriers. Edge recombination operators that can break and rearrange long connections might be useful.",
      "Monitor elite solutions closely: Continue to track any elite solutions. As the search progresses, analyzing edges and patterns present in these solutions can identify more concrete opportunity regions."
    ]
  }
}
```
2025-06-25 10:43:18,741 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-25 10:43:18,741 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'The large range in edge lengths (7.0 to 3222.0), combined with a relatively high long_edge_ratio (0.25), suggests a rugged search space. Many possible solutions are significantly different from each other, and small changes in a tour can lead to drastic cost changes.', 'modality': 'The lack of strong structural signals (empty high_quality_edges_sample and common_subpaths_sample) indicates a potentially multi-modal landscape. The cost statistics also support this, with a wide range between the min and max and a sizable standard deviation.', 'deceptiveness': 'The discrepancy between short and long edges suggests the presence of potentially deceptive local optima. Short edges may lead to small improvements, but longer edges might be necessary to escape local traps and reach the global optimum.'}, 'population_state': {'coverage': 'The diversity is high (0.9626), indicating good population coverage across the search space. The density grid reveals uneven distribution with some cells heavily populated and others completely empty.', 'convergence': "The convergence is currently at 0.0, indicating that the population hasn't converged to any specific part of the search space yet. Given high diversity and initial iteration, this is expected."}, 'difficult_regions': {'description': 'Difficult regions are inferred based on low-density areas and the presence of long edges.', 'spatial_indicators': ['Cells with density 0 in the density grid are likely to be difficult regions. Specifically, the top middle, top right, middle left, bottom middle cells are likely difficult.', 'Areas connected by the longest edges (edge lengths approaching 3222.0) are potentially difficult regions to traverse.'], 'structural_indicators': 'Lack of identified high-quality edges or common subpaths reinforces the idea that certain connections/routes are inherently difficult.'}, 'opportunity_regions': {'description': 'Opportunity regions are inferred from high-density grid cells, suggesting potentially beneficial node clusters or connections.', 'spatial_indicators': ['The high-density cells in the density grid suggest potential opportunity regions. Specifically, the top left, middle middle, middle right, and bottom right cells warrant further exploration. These areas may contain clusters of nodes that can be connected efficiently.', 'Further investigation into node arrangements within these high-density cells is needed to identify specific opportunity edges.'], 'structural_indicators': "Need more structural information to identify concrete opportunity regions. The 'elite_common_features_sample' being empty and the lack of high quality edges identified suggests there's no clear structure identified so far."}, 'evolution_phase': 'Exploration phase. The high diversity and lack of convergence indicate that the algorithm is still exploring the search space.', 'evolution_direction': {'suggested_operators': ['Enhance exploration: Utilize operators that promote diversity and explore new regions of the search space. Examples include random swaps, inversions, or more aggressive crossover operators.', 'Target high-density cells: Focus search efforts on the high-density grid cells to discover better connections within these promising regions. Local search operators could be applied within these regions.', 'Address difficult regions: Implement strategies to overcome long edges that potentially act as barriers. Edge recombination operators that can break and rearrange long connections might be useful.', 'Monitor elite solutions closely: Continue to track any elite solutions. As the search progresses, analyzing edges and patterns present in these solutions can identify more concrete opportunity regions.']}}
2025-06-25 10:43:18,741 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-25 10:43:18,741 - __main__ - INFO - 分析阶段完成
2025-06-25 10:43:18,741 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'The large range in edge lengths (7.0 to 3222.0), combined with a relatively high long_edge_ratio (0.25), suggests a rugged search space. Many possible solutions are significantly different from each other, and small changes in a tour can lead to drastic cost changes.', 'modality': 'The lack of strong structural signals (empty high_quality_edges_sample and common_subpaths_sample) indicates a potentially multi-modal landscape. The cost statistics also support this, with a wide range between the min and max and a sizable standard deviation.', 'deceptiveness': 'The discrepancy between short and long edges suggests the presence of potentially deceptive local optima. Short edges may lead to small improvements, but longer edges might be necessary to escape local traps and reach the global optimum.'}, 'population_state': {'coverage': 'The diversity is high (0.9626), indicating good population coverage across the search space. The density grid reveals uneven distribution with some cells heavily populated and others completely empty.', 'convergence': "The convergence is currently at 0.0, indicating that the population hasn't converged to any specific part of the search space yet. Given high diversity and initial iteration, this is expected."}, 'difficult_regions': {'description': 'Difficult regions are inferred based on low-density areas and the presence of long edges.', 'spatial_indicators': ['Cells with density 0 in the density grid are likely to be difficult regions. Specifically, the top middle, top right, middle left, bottom middle cells are likely difficult.', 'Areas connected by the longest edges (edge lengths approaching 3222.0) are potentially difficult regions to traverse.'], 'structural_indicators': 'Lack of identified high-quality edges or common subpaths reinforces the idea that certain connections/routes are inherently difficult.'}, 'opportunity_regions': {'description': 'Opportunity regions are inferred from high-density grid cells, suggesting potentially beneficial node clusters or connections.', 'spatial_indicators': ['The high-density cells in the density grid suggest potential opportunity regions. Specifically, the top left, middle middle, middle right, and bottom right cells warrant further exploration. These areas may contain clusters of nodes that can be connected efficiently.', 'Further investigation into node arrangements within these high-density cells is needed to identify specific opportunity edges.'], 'structural_indicators': "Need more structural information to identify concrete opportunity regions. The 'elite_common_features_sample' being empty and the lack of high quality edges identified suggests there's no clear structure identified so far."}, 'evolution_phase': 'Exploration phase. The high diversity and lack of convergence indicate that the algorithm is still exploring the search space.', 'evolution_direction': {'suggested_operators': ['Enhance exploration: Utilize operators that promote diversity and explore new regions of the search space. Examples include random swaps, inversions, or more aggressive crossover operators.', 'Target high-density cells: Focus search efforts on the high-density grid cells to discover better connections within these promising regions. Local search operators could be applied within these regions.', 'Address difficult regions: Implement strategies to overcome long edges that potentially act as barriers. Edge recombination operators that can break and rearrange long connections might be useful.', 'Monitor elite solutions closely: Continue to track any elite solutions. As the search progresses, analyzing edges and patterns present in these solutions can identify more concrete opportunity regions.']}}
2025-06-25 10:43:18,745 - __main__ - INFO - 开始策略分配阶段
2025-06-25 10:43:18,746 - StrategyExpert - INFO - 开始策略分配分析
2025-06-25 10:43:18,746 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 13173.0
  • mean_cost: 88868.9
- Search-space: ruggedness The large range in edge lengths (7.0 to 3222.0), combined with a relatively high long_edge_ratio (0.25), suggests a rugged search space. Many possible solutions are significantly different from each other, and small changes in a tour can lead to drastic cost changes., modality The lack of strong structural signals (empty high_quality_edges_sample and common_subpaths_sample) indicates a potentially multi-modal landscape. The cost statistics also support this, with a wide range between the min and max and a sizable standard deviation., deceptiveness The discrepancy between short and long edges suggests the presence of potentially deceptive local optima. Short edges may lead to small improvements, but longer edges might be necessary to escape local traps and reach the global optimum.
- Evolution phase: Exploration phase. The high diversity and lack of convergence indicate that the algorithm is still exploring the search space.
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): description, spatial_indicators...
- Opportunity regions (# 3): description, spatial_indicators...
- Last-iteration feedback: ```json
{
  "evolution_stage": "Early Stage - Initial Progress",
  "strategy_effectiveness": "Explore strategy shows hig...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-25 10:43:18,746 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-25 10:43:23,832 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High initial diversity, rugged search space, and the need to escape deceptive local optima favor exploration. The 3 best individuals are assigned to exploitation."
}
```
2025-06-25 10:43:23,832 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-25 10:43:23,832 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-25 10:43:23,832 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High initial diversity, rugged search space, and the need to escape deceptive local optima favor exploration. The 3 best individuals are assigned to exploitation."
}
```
2025-06-25 10:43:23,832 - __main__ - INFO - 策略分配阶段完成
2025-06-25 10:43:23,832 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-25 10:43:23,832 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High initial diversity, rugged search space, and the need to escape deceptive local optima favor exploration. The 3 best individuals are assigned to exploitation."
}
```
2025-06-25 10:43:23,835 - __main__ - INFO - 开始进化阶段
2025-06-25 10:43:23,835 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-25 10:43:23,835 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:43:23,835 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98906.0 (rank 60.0%)
- Population diversity: 0.8610169491525423
- Evolution phase: Exploration phase. The high diversity and lack of convergence indicate that the algorithm is still exploring the search space.
- Landscape recommends focus: balance
- Opportunity regions (sample): description, spatial_indicat...
- Difficult regions to avoid (sample): description, spatial_indicat...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:43:23,836 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:43:27,225 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [52, 41, 59, 10, 30, 56, 47, 16, 1, 39, 33, 31, 28, 20, 21, 4, 48, 24, 29, 13, 51, 27, 55, 17, 9, 54, 12, 44, 32, 34, 37, 6, 46, 26, 0, 42, 36, 23, 50, 49, 25, 40, 53, 3, 18, 35, 22, 19, 8, 14, 45, 58, 15, 2, 11, 38, 57, 7, 43, 5],
  "targeted_regions": "Prioritize sparse cells near opportunity regions, and minimize transitions through difficult regions.",
  "strategy_comment": "Shifted the order to explore new adjacent sparse cells and circumvent difficult zones."
}
```
2025-06-25 10:43:27,225 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:43:27,225 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100280.0, 路径: [52, 41, 59, 10, 30, 56, 47, 16, 1, 39, 33, 31, 28, 20, 21, 4, 48, 24, 29, 13, 51, 27, 55, 17, 9, 54, 12, 44, 32, 34, 37, 6, 46, 26, 0, 42, 36, 23, 50, 49, 25, 40, 53, 3, 18, 35, 22, 19, 8, 14, 45, 58, 15, 2, 11, 38, 57, 7, 43, 5]
2025-06-25 10:43:27,225 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [52, 41, 59, 10, 30, 56, 47, 16, 1, 39, 33, 31, 28, 20, 21, 4, 48, 24, 29, 13, 51, 27, 55, 17, 9, 54, 12, 44, 32, 34, 37, 6, 46, 26, 0, 42, 36, 23, 50, 49, 25, 40, 53, 3, 18, 35, 22, 19, 8, 14, 45, 58, 15, 2, 11, 38, 57, 7, 43, 5], 'cur_cost': 100280.0}
2025-06-25 10:43:27,225 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-25 10:43:27,225 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:43:27,227 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13173.0 (rank 0.0%)
- Population diversity: 0.8610169491525423
- Evolution phase: Exploration phase. The high diversity and lack of convergence indicate that the algorithm is still exploring the search space.
- Landscape recommends focus: balance
- Opportunity regions (sample): description, spatial_indicat...
- Difficult regions to avoid (sample): description, spatial_indicat...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:43:27,227 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:43:30,728 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [52, 34, 38, 45, 16, 57, 4, 49, 25, 58, 10, 35, 22, 30, 23, 54, 59, 37, 50, 14, 48, 11, 32, 56, 6, 27, 13, 26, 33, 44, 2, 39, 19, 31, 17, 28, 20, 7, 40, 51, 1, 18, 43, 47, 53, 21, 36, 8, 5, 15, 0, 46, 42, 29, 3, 9, 41, 12, 55, 24],
  "targeted_regions": "Explored sparse cells near known opportunity regions by varying edges and node order.",
  "strategy_comment": "Increased diversity with path variations near opportunity cells. Avoided difficult areas."
}
```
2025-06-25 10:43:30,730 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:43:30,730 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107466.0, 路径: [52, 34, 38, 45, 16, 57, 4, 49, 25, 58, 10, 35, 22, 30, 23, 54, 59, 37, 50, 14, 48, 11, 32, 56, 6, 27, 13, 26, 33, 44, 2, 39, 19, 31, 17, 28, 20, 7, 40, 51, 1, 18, 43, 47, 53, 21, 36, 8, 5, 15, 0, 46, 42, 29, 3, 9, 41, 12, 55, 24]
2025-06-25 10:43:30,730 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [52, 34, 38, 45, 16, 57, 4, 49, 25, 58, 10, 35, 22, 30, 23, 54, 59, 37, 50, 14, 48, 11, 32, 56, 6, 27, 13, 26, 33, 44, 2, 39, 19, 31, 17, 28, 20, 7, 40, 51, 1, 18, 43, 47, 53, 21, 36, 8, 5, 15, 0, 46, 42, 29, 3, 9, 41, 12, 55, 24], 'cur_cost': 107466.0}
2025-06-25 10:43:30,730 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-25 10:43:30,730 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:43:30,732 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 93457.0 (rank 10.0%)
- Population diversity: 0.8694915254237288
- Evolution phase: Exploration phase. The high diversity and lack of convergence indicate that the algorithm is still exploring the search space.
- Landscape recommends focus: balance
- Opportunity regions (sample): description, spatial_indicat...
- Difficult regions to avoid (sample): description, spatial_indicat...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:43:30,732 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:43:34,199 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    41,
    29,
    52,
    16,
    56,
    21,
    2,
    22,
    44,
    13,
    54,
    4,
    25,
    32,
    40,
    53,
    12,
    46,
    58,
    10,
    39,
    35,
    11,
    50,
    26,
    34,
    19,
    18,
    30,
    48,
    38,
    57,
    36,
    20,
    8,
    3,
    5,
    17,
    45,
    23,
    24,
    33,
    49,
    15,
    0,
    28,
    47,
    9,
    37,
    7,
    55,
    42,
    31,
    1,
    6,
    43,
    59,
    51,
    14,
    27
  ],
  "targeted_regions": "Focused on exploring cells near previously identified opportunity regions. Deliberate deviations near nodes 42, 31, 1 to increase path diversity.",
  "strategy_comment": "Exploring sparse cells and adding deviations to boost diversity while balancing cost."
}
```
2025-06-25 10:43:34,199 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:43:34,201 - ExplorationExpert - INFO - 探索路径生成完成，成本: 104182.0, 路径: [41, 29, 52, 16, 56, 21, 2, 22, 44, 13, 54, 4, 25, 32, 40, 53, 12, 46, 58, 10, 39, 35, 11, 50, 26, 34, 19, 18, 30, 48, 38, 57, 36, 20, 8, 3, 5, 17, 45, 23, 24, 33, 49, 15, 0, 28, 47, 9, 37, 7, 55, 42, 31, 1, 6, 43, 59, 51, 14, 27]
2025-06-25 10:43:34,201 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [41, 29, 52, 16, 56, 21, 2, 22, 44, 13, 54, 4, 25, 32, 40, 53, 12, 46, 58, 10, 39, 35, 11, 50, 26, 34, 19, 18, 30, 48, 38, 57, 36, 20, 8, 3, 5, 17, 45, 23, 24, 33, 49, 15, 0, 28, 47, 9, 37, 7, 55, 42, 31, 1, 6, 43, 59, 51, 14, 27], 'cur_cost': 104182.0}
2025-06-25 10:43:34,202 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-25 10:43:34,202 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:43:34,204 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 94672.0 (rank 10.0%)
- Population diversity: 0.8694915254237288
- Evolution phase: Exploration phase. The high diversity and lack of convergence indicate that the algorithm is still exploring the search space.
- Landscape recommends focus: balance
- Opportunity regions (sample): description, spatial_indicat...
- Difficult regions to avoid (sample): description, spatial_indicat...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:43:34,204 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:43:37,353 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [51, 21, 40, 26, 32, 43, 11, 23, 55, 52, 3, 24, 56, 10, 47, 4, 14, 19, 28, 42, 5, 57, 36, 38, 39, 31, 46, 16, 0, 27, 50, 9, 58, 12, 45, 34, 6, 49, 13, 18, 41, 17, 2, 33, 7, 22, 35, 20, 48, 1, 25, 8, 59, 15, 29, 37, 44, 30, 53, 54],
  "targeted_regions": "Explore cells near nodes 51, 21, 40 (Opportunity). Slightly deviate from current paths to increase diversity.",
  "strategy_comment": "Introduce variation by changing start and reordering path leveraging opportunity hints."
}
```
2025-06-25 10:43:37,353 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:43:37,353 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96038.0, 路径: [51, 21, 40, 26, 32, 43, 11, 23, 55, 52, 3, 24, 56, 10, 47, 4, 14, 19, 28, 42, 5, 57, 36, 38, 39, 31, 46, 16, 0, 27, 50, 9, 58, 12, 45, 34, 6, 49, 13, 18, 41, 17, 2, 33, 7, 22, 35, 20, 48, 1, 25, 8, 59, 15, 29, 37, 44, 30, 53, 54]
2025-06-25 10:43:37,353 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [51, 21, 40, 26, 32, 43, 11, 23, 55, 52, 3, 24, 56, 10, 47, 4, 14, 19, 28, 42, 5, 57, 36, 38, 39, 31, 46, 16, 0, 27, 50, 9, 58, 12, 45, 34, 6, 49, 13, 18, 41, 17, 2, 33, 7, 22, 35, 20, 48, 1, 25, 8, 59, 15, 29, 37, 44, 30, 53, 54], 'cur_cost': 96038.0}
2025-06-25 10:43:37,353 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-25 10:43:37,356 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:43:37,357 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 104419.0 (rank 80.0%)
- Population diversity: 0.8694915254237288
- Evolution phase: Exploration phase. The high diversity and lack of convergence indicate that the algorithm is still exploring the search space.
- Landscape recommends focus: balance
- Opportunity regions (sample): description, spatial_indicat...
- Difficult regions to avoid (sample): description, spatial_indicat...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:43:37,357 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:43:40,831 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    49,
    15,
    40,
    56,
    28,
    13,
    46,
    52,
    41,
    9,
    38,
    37,
    2,
    10,
    5,
    33,
    11,
    1,
    58,
    20,
    47,
    53,
    31,
    18,
    30,
    21,
    54,
    24,
    48,
    16,
    3,
    42,
    14,
    23,
    59,
    26,
    27,
    8,
    55,
    39,
    22,
    19,
    4,
    29,
    43,
    34,
    51,
    6,
    12,
    17,
    36,
    35,
    0,
    50,
    7,
    45,
    25,
    32,
    57,
    44
  ],
  "targeted_regions": "Opportunity regions and adjacent sparse cells.",
  "strategy_comment": "Explored sparse areas and balanced exploitation/exploration for diversity."
}
```
2025-06-25 10:43:40,831 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:43:40,833 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103704.0, 路径: [49, 15, 40, 56, 28, 13, 46, 52, 41, 9, 38, 37, 2, 10, 5, 33, 11, 1, 58, 20, 47, 53, 31, 18, 30, 21, 54, 24, 48, 16, 3, 42, 14, 23, 59, 26, 27, 8, 55, 39, 22, 19, 4, 29, 43, 34, 51, 6, 12, 17, 36, 35, 0, 50, 7, 45, 25, 32, 57, 44]
2025-06-25 10:43:40,833 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [49, 15, 40, 56, 28, 13, 46, 52, 41, 9, 38, 37, 2, 10, 5, 33, 11, 1, 58, 20, 47, 53, 31, 18, 30, 21, 54, 24, 48, 16, 3, 42, 14, 23, 59, 26, 27, 8, 55, 39, 22, 19, 4, 29, 43, 34, 51, 6, 12, 17, 36, 35, 0, 50, 7, 45, 25, 32, 57, 44], 'cur_cost': 103704.0}
2025-06-25 10:43:40,833 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-25 10:43:40,833 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:43:40,833 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 103024.0 (rank 60.0%)
- Population diversity: 0.8610169491525423
- Evolution phase: Exploration phase. The high diversity and lack of convergence indicate that the algorithm is still exploring the search space.
- Landscape recommends focus: balance
- Opportunity regions (sample): description, spatial_indicat...
- Difficult regions to avoid (sample): description, spatial_indicat...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:43:40,833 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:43:45,089 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [39, 56, 40, 19, 20, 52, 23, 11, 34, 13, 14, 12, 41, 57, 35, 15, 27, 59, 9, 21, 42, 5, 46, 32, 17, 48, 2, 31, 8, 1, 10, 6, 54, 44, 3, 55, 28, 43, 33, 22, 16, 47, 37, 26, 50, 0, 30, 4, 25, 53, 58, 45, 18, 36, 51, 7, 24, 38, 49, 29],
  "targeted_regions": "Shifted the sequence near nodes 56, 40, and 19 (opportunity) while deviating from the original near difficult region around node 44.",
  "strategy_comment": "Introduce a large swap near key opportunity regions. Avoid difficult region. Maintain path validity, aim for exploration."
}
```
2025-06-25 10:43:45,089 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:43:45,089 - ExplorationExpert - INFO - 探索路径生成完成，成本: 104877.0, 路径: [39, 56, 40, 19, 20, 52, 23, 11, 34, 13, 14, 12, 41, 57, 35, 15, 27, 59, 9, 21, 42, 5, 46, 32, 17, 48, 2, 31, 8, 1, 10, 6, 54, 44, 3, 55, 28, 43, 33, 22, 16, 47, 37, 26, 50, 0, 30, 4, 25, 53, 58, 45, 18, 36, 51, 7, 24, 38, 49, 29]
2025-06-25 10:43:45,089 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [39, 56, 40, 19, 20, 52, 23, 11, 34, 13, 14, 12, 41, 57, 35, 15, 27, 59, 9, 21, 42, 5, 46, 32, 17, 48, 2, 31, 8, 1, 10, 6, 54, 44, 3, 55, 28, 43, 33, 22, 16, 47, 37, 26, 50, 0, 30, 4, 25, 53, 58, 45, 18, 36, 51, 7, 24, 38, 49, 29], 'cur_cost': 104877.0}
2025-06-25 10:43:45,089 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-25 10:43:45,092 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:43:45,092 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 102928.0 (rank 50.0%)
- Population diversity: 0.8610169491525423
- Evolution phase: Exploration phase. The high diversity and lack of convergence indicate that the algorithm is still exploring the search space.
- Landscape recommends focus: balance
- Opportunity regions (sample): description, spatial_indicat...
- Difficult regions to avoid (sample): description, spatial_indicat...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:43:45,093 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:43:48,069 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [4, 53, 30, 46, 19, 22, 17, 49, 31, 28, 5, 23, 8, 15, 37, 34, 10, 58, 25, 40, 51, 27, 57, 38, 14, 20, 32, 50, 13, 44, 1, 39, 12, 3, 47, 36, 21, 6, 11, 9, 16, 45, 29, 2, 41, 55, 0, 33, 56, 24, 7, 54, 48, 35, 59, 43, 42, 26, 52, 18],
  "targeted_regions": "Explored sparse areas near opportunity regions, shifted nodes.",
  "strategy_comment": "Slightly shuffled the path to explore near opportunity zones to increase population diversity."
}
```
2025-06-25 10:43:48,069 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:43:48,069 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107289.0, 路径: [4, 53, 30, 46, 19, 22, 17, 49, 31, 28, 5, 23, 8, 15, 37, 34, 10, 58, 25, 40, 51, 27, 57, 38, 14, 20, 32, 50, 13, 44, 1, 39, 12, 3, 47, 36, 21, 6, 11, 9, 16, 45, 29, 2, 41, 55, 0, 33, 56, 24, 7, 54, 48, 35, 59, 43, 42, 26, 52, 18]
2025-06-25 10:43:48,069 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [4, 53, 30, 46, 19, 22, 17, 49, 31, 28, 5, 23, 8, 15, 37, 34, 10, 58, 25, 40, 51, 27, 57, 38, 14, 20, 32, 50, 13, 44, 1, 39, 12, 3, 47, 36, 21, 6, 11, 9, 16, 45, 29, 2, 41, 55, 0, 33, 56, 24, 7, 54, 48, 35, 59, 43, 42, 26, 52, 18], 'cur_cost': 107289.0}
2025-06-25 10:43:48,069 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-25 10:43:48,069 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-25 10:43:48,069 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-25 10:43:48,072 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 83670.0
2025-06-25 10:43:48,575 - ExploitationExpert - INFO - res_population_num: 16
2025-06-25 10:43:48,575 - ExploitationExpert - INFO - res_population_costs: [9644, 87470, 9644, 9644, 9630, 9630, 9630, 9626, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-25 10:43:48,575 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  6,  5,  9,  3,  2,  7, 11, 10,  8, 26, 34, 30, 29, 33, 24,
       27, 28, 35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0, 55, 46, 29,  1, 21, 58, 51, 36, 24, 56, 23, 16, 15, 49, 45, 31,
        2, 52, 38, 27,  7,  9,  3, 28, 34, 44, 11, 35, 14, 18, 19, 20, 48,
        8, 47, 39, 50, 32, 13, 57, 22, 30, 59, 40, 41, 54,  5, 10, 37, 53,
       33, 25, 43, 17, 26, 42,  4, 12,  6], dtype=int64), array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 59, 52,
       50, 58, 49, 56, 55, 57, 53, 48, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  1, 10,  6,  5,  9,  3, 11,  2,  7,  8, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-06-25 10:43:48,579 - ExploitationExpert - INFO - populations_num: 10
2025-06-25 10:43:48,579 - ExploitationExpert - INFO - populations: [{'tour': [52, 41, 59, 10, 30, 56, 47, 16, 1, 39, 33, 31, 28, 20, 21, 4, 48, 24, 29, 13, 51, 27, 55, 17, 9, 54, 12, 44, 32, 34, 37, 6, 46, 26, 0, 42, 36, 23, 50, 49, 25, 40, 53, 3, 18, 35, 22, 19, 8, 14, 45, 58, 15, 2, 11, 38, 57, 7, 43, 5], 'cur_cost': 100280.0}, {'tour': [52, 34, 38, 45, 16, 57, 4, 49, 25, 58, 10, 35, 22, 30, 23, 54, 59, 37, 50, 14, 48, 11, 32, 56, 6, 27, 13, 26, 33, 44, 2, 39, 19, 31, 17, 28, 20, 7, 40, 51, 1, 18, 43, 47, 53, 21, 36, 8, 5, 15, 0, 46, 42, 29, 3, 9, 41, 12, 55, 24], 'cur_cost': 107466.0}, {'tour': [41, 29, 52, 16, 56, 21, 2, 22, 44, 13, 54, 4, 25, 32, 40, 53, 12, 46, 58, 10, 39, 35, 11, 50, 26, 34, 19, 18, 30, 48, 38, 57, 36, 20, 8, 3, 5, 17, 45, 23, 24, 33, 49, 15, 0, 28, 47, 9, 37, 7, 55, 42, 31, 1, 6, 43, 59, 51, 14, 27], 'cur_cost': 104182.0}, {'tour': [51, 21, 40, 26, 32, 43, 11, 23, 55, 52, 3, 24, 56, 10, 47, 4, 14, 19, 28, 42, 5, 57, 36, 38, 39, 31, 46, 16, 0, 27, 50, 9, 58, 12, 45, 34, 6, 49, 13, 18, 41, 17, 2, 33, 7, 22, 35, 20, 48, 1, 25, 8, 59, 15, 29, 37, 44, 30, 53, 54], 'cur_cost': 96038.0}, {'tour': [49, 15, 40, 56, 28, 13, 46, 52, 41, 9, 38, 37, 2, 10, 5, 33, 11, 1, 58, 20, 47, 53, 31, 18, 30, 21, 54, 24, 48, 16, 3, 42, 14, 23, 59, 26, 27, 8, 55, 39, 22, 19, 4, 29, 43, 34, 51, 6, 12, 17, 36, 35, 0, 50, 7, 45, 25, 32, 57, 44], 'cur_cost': 103704.0}, {'tour': [39, 56, 40, 19, 20, 52, 23, 11, 34, 13, 14, 12, 41, 57, 35, 15, 27, 59, 9, 21, 42, 5, 46, 32, 17, 48, 2, 31, 8, 1, 10, 6, 54, 44, 3, 55, 28, 43, 33, 22, 16, 47, 37, 26, 50, 0, 30, 4, 25, 53, 58, 45, 18, 36, 51, 7, 24, 38, 49, 29], 'cur_cost': 104877.0}, {'tour': [4, 53, 30, 46, 19, 22, 17, 49, 31, 28, 5, 23, 8, 15, 37, 34, 10, 58, 25, 40, 51, 27, 57, 38, 14, 20, 32, 50, 13, 44, 1, 39, 12, 3, 47, 36, 21, 6, 11, 9, 16, 45, 29, 2, 41, 55, 0, 33, 56, 24, 7, 54, 48, 35, 59, 43, 42, 26, 52, 18], 'cur_cost': 107289.0}, {'tour': array([ 4, 49, 42,  3, 28, 33, 27, 43, 36,  2, 10, 45, 25, 51,  1, 12, 56,
       20, 31, 35, 53, 39,  9, 58, 50,  7, 18, 19, 41, 40, 26, 37, 14, 22,
       16, 54, 17, 52, 13, 59, 21,  5, 15, 48, 32,  8, 34,  6, 57, 38, 44,
       24, 29, 23,  0, 47, 30, 46, 55, 11]), 'cur_cost': 83670.0}, {'tour': array([ 2, 54, 12, 16, 23, 47, 34,  7, 24,  9, 56, 18,  4, 26, 45, 58, 14,
       20, 17, 40, 44,  5, 46, 13, 32, 30, 33, 28, 52, 35,  8, 15, 42, 37,
       39, 22, 21,  3, 19, 38, 29, 25, 51, 49, 50, 55, 11, 10, 27, 41,  1,
       43, 53, 59, 31, 48,  6, 36,  0, 57]), 'cur_cost': 82634.0}, {'tour': array([33,  2, 14, 23, 11, 48, 34, 12, 53, 13, 37,  7, 52, 58, 51, 20,  8,
       35,  5, 17, 55,  1, 50, 43, 54,  6, 40, 26, 25, 44,  0, 56, 41, 19,
        4, 15, 57, 46, 29, 27, 59, 30, 18,  3, 28, 45, 38, 49,  9, 24, 31,
       47, 10, 16, 21, 36, 32, 39, 42, 22]), 'cur_cost': 98229.0}]
2025-06-25 10:43:48,581 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-25 10:43:48,582 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-25 10:43:48,582 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-25 10:43:48,582 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-25 10:43:48,582 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-25 10:43:48,582 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-25 10:43:48,582 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 92004.0
2025-06-25 10:43:50,149 - ExploitationExpert - INFO - res_population_num: 21
2025-06-25 10:43:50,150 - ExploitationExpert - INFO - res_population_costs: [9644, 87470, 9644, 9644, 9630, 9630, 9630, 9626, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-25 10:43:50,151 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  6,  5,  9,  3,  2,  7, 11, 10,  8, 26, 34, 30, 29, 33, 24,
       27, 28, 35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0, 55, 46, 29,  1, 21, 58, 51, 36, 24, 56, 23, 16, 15, 49, 45, 31,
        2, 52, 38, 27,  7,  9,  3, 28, 34, 44, 11, 35, 14, 18, 19, 20, 48,
        8, 47, 39, 50, 32, 13, 57, 22, 30, 59, 40, 41, 54,  5, 10, 37, 53,
       33, 25, 43, 17, 26, 42,  4, 12,  6], dtype=int64), array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 59, 52,
       50, 58, 49, 56, 55, 57, 53, 48, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  1, 10,  6,  5,  9,  3, 11,  2,  7,  8, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64)]
2025-06-25 10:43:50,159 - ExploitationExpert - INFO - populations_num: 10
2025-06-25 10:43:50,159 - ExploitationExpert - INFO - populations: [{'tour': [52, 41, 59, 10, 30, 56, 47, 16, 1, 39, 33, 31, 28, 20, 21, 4, 48, 24, 29, 13, 51, 27, 55, 17, 9, 54, 12, 44, 32, 34, 37, 6, 46, 26, 0, 42, 36, 23, 50, 49, 25, 40, 53, 3, 18, 35, 22, 19, 8, 14, 45, 58, 15, 2, 11, 38, 57, 7, 43, 5], 'cur_cost': 100280.0}, {'tour': [52, 34, 38, 45, 16, 57, 4, 49, 25, 58, 10, 35, 22, 30, 23, 54, 59, 37, 50, 14, 48, 11, 32, 56, 6, 27, 13, 26, 33, 44, 2, 39, 19, 31, 17, 28, 20, 7, 40, 51, 1, 18, 43, 47, 53, 21, 36, 8, 5, 15, 0, 46, 42, 29, 3, 9, 41, 12, 55, 24], 'cur_cost': 107466.0}, {'tour': [41, 29, 52, 16, 56, 21, 2, 22, 44, 13, 54, 4, 25, 32, 40, 53, 12, 46, 58, 10, 39, 35, 11, 50, 26, 34, 19, 18, 30, 48, 38, 57, 36, 20, 8, 3, 5, 17, 45, 23, 24, 33, 49, 15, 0, 28, 47, 9, 37, 7, 55, 42, 31, 1, 6, 43, 59, 51, 14, 27], 'cur_cost': 104182.0}, {'tour': [51, 21, 40, 26, 32, 43, 11, 23, 55, 52, 3, 24, 56, 10, 47, 4, 14, 19, 28, 42, 5, 57, 36, 38, 39, 31, 46, 16, 0, 27, 50, 9, 58, 12, 45, 34, 6, 49, 13, 18, 41, 17, 2, 33, 7, 22, 35, 20, 48, 1, 25, 8, 59, 15, 29, 37, 44, 30, 53, 54], 'cur_cost': 96038.0}, {'tour': [49, 15, 40, 56, 28, 13, 46, 52, 41, 9, 38, 37, 2, 10, 5, 33, 11, 1, 58, 20, 47, 53, 31, 18, 30, 21, 54, 24, 48, 16, 3, 42, 14, 23, 59, 26, 27, 8, 55, 39, 22, 19, 4, 29, 43, 34, 51, 6, 12, 17, 36, 35, 0, 50, 7, 45, 25, 32, 57, 44], 'cur_cost': 103704.0}, {'tour': [39, 56, 40, 19, 20, 52, 23, 11, 34, 13, 14, 12, 41, 57, 35, 15, 27, 59, 9, 21, 42, 5, 46, 32, 17, 48, 2, 31, 8, 1, 10, 6, 54, 44, 3, 55, 28, 43, 33, 22, 16, 47, 37, 26, 50, 0, 30, 4, 25, 53, 58, 45, 18, 36, 51, 7, 24, 38, 49, 29], 'cur_cost': 104877.0}, {'tour': [4, 53, 30, 46, 19, 22, 17, 49, 31, 28, 5, 23, 8, 15, 37, 34, 10, 58, 25, 40, 51, 27, 57, 38, 14, 20, 32, 50, 13, 44, 1, 39, 12, 3, 47, 36, 21, 6, 11, 9, 16, 45, 29, 2, 41, 55, 0, 33, 56, 24, 7, 54, 48, 35, 59, 43, 42, 26, 52, 18], 'cur_cost': 107289.0}, {'tour': array([ 4, 49, 42,  3, 28, 33, 27, 43, 36,  2, 10, 45, 25, 51,  1, 12, 56,
       20, 31, 35, 53, 39,  9, 58, 50,  7, 18, 19, 41, 40, 26, 37, 14, 22,
       16, 54, 17, 52, 13, 59, 21,  5, 15, 48, 32,  8, 34,  6, 57, 38, 44,
       24, 29, 23,  0, 47, 30, 46, 55, 11]), 'cur_cost': 83670.0}, {'tour': array([ 7, 59, 57,  0, 16, 50, 56, 52, 39, 42, 32, 58, 54, 14, 23,  8, 44,
        9, 21, 10, 36, 29, 53, 31, 43, 51, 22, 40,  6, 47,  5,  2,  1, 19,
       45, 48,  3, 35, 34, 25, 13, 17, 38, 18, 15, 26, 11,  4, 30, 37, 24,
       55, 33, 28, 41, 12, 49, 20, 27, 46]), 'cur_cost': 92004.0}, {'tour': array([33,  2, 14, 23, 11, 48, 34, 12, 53, 13, 37,  7, 52, 58, 51, 20,  8,
       35,  5, 17, 55,  1, 50, 43, 54,  6, 40, 26, 25, 44,  0, 56, 41, 19,
        4, 15, 57, 46, 29, 27, 59, 30, 18,  3, 28, 45, 38, 49,  9, 24, 31,
       47, 10, 16, 21, 36, 32, 39, 42, 22]), 'cur_cost': 98229.0}]
2025-06-25 10:43:50,160 - ExploitationExpert - INFO - 局部搜索耗时: 1.58秒
2025-06-25 10:43:50,160 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-25 10:43:50,161 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-25 10:43:50,161 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-25 10:43:50,161 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-25 10:43:50,162 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-25 10:43:50,162 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 99171.0
2025-06-25 10:43:50,664 - ExploitationExpert - INFO - res_population_num: 29
2025-06-25 10:43:50,664 - ExploitationExpert - INFO - res_population_costs: [9644, 87470, 9644, 9644, 9630, 9630, 9630, 9626, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-25 10:43:50,666 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  6,  5,  9,  3,  2,  7, 11, 10,  8, 26, 34, 30, 29, 33, 24,
       27, 28, 35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0, 55, 46, 29,  1, 21, 58, 51, 36, 24, 56, 23, 16, 15, 49, 45, 31,
        2, 52, 38, 27,  7,  9,  3, 28, 34, 44, 11, 35, 14, 18, 19, 20, 48,
        8, 47, 39, 50, 32, 13, 57, 22, 30, 59, 40, 41, 54,  5, 10, 37, 53,
       33, 25, 43, 17, 26, 42,  4, 12,  6], dtype=int64), array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 59, 52,
       50, 58, 49, 56, 55, 57, 53, 48, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  1, 10,  6,  5,  9,  3, 11,  2,  7,  8, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64)]
2025-06-25 10:43:50,679 - ExploitationExpert - INFO - populations_num: 10
2025-06-25 10:43:50,679 - ExploitationExpert - INFO - populations: [{'tour': [52, 41, 59, 10, 30, 56, 47, 16, 1, 39, 33, 31, 28, 20, 21, 4, 48, 24, 29, 13, 51, 27, 55, 17, 9, 54, 12, 44, 32, 34, 37, 6, 46, 26, 0, 42, 36, 23, 50, 49, 25, 40, 53, 3, 18, 35, 22, 19, 8, 14, 45, 58, 15, 2, 11, 38, 57, 7, 43, 5], 'cur_cost': 100280.0}, {'tour': [52, 34, 38, 45, 16, 57, 4, 49, 25, 58, 10, 35, 22, 30, 23, 54, 59, 37, 50, 14, 48, 11, 32, 56, 6, 27, 13, 26, 33, 44, 2, 39, 19, 31, 17, 28, 20, 7, 40, 51, 1, 18, 43, 47, 53, 21, 36, 8, 5, 15, 0, 46, 42, 29, 3, 9, 41, 12, 55, 24], 'cur_cost': 107466.0}, {'tour': [41, 29, 52, 16, 56, 21, 2, 22, 44, 13, 54, 4, 25, 32, 40, 53, 12, 46, 58, 10, 39, 35, 11, 50, 26, 34, 19, 18, 30, 48, 38, 57, 36, 20, 8, 3, 5, 17, 45, 23, 24, 33, 49, 15, 0, 28, 47, 9, 37, 7, 55, 42, 31, 1, 6, 43, 59, 51, 14, 27], 'cur_cost': 104182.0}, {'tour': [51, 21, 40, 26, 32, 43, 11, 23, 55, 52, 3, 24, 56, 10, 47, 4, 14, 19, 28, 42, 5, 57, 36, 38, 39, 31, 46, 16, 0, 27, 50, 9, 58, 12, 45, 34, 6, 49, 13, 18, 41, 17, 2, 33, 7, 22, 35, 20, 48, 1, 25, 8, 59, 15, 29, 37, 44, 30, 53, 54], 'cur_cost': 96038.0}, {'tour': [49, 15, 40, 56, 28, 13, 46, 52, 41, 9, 38, 37, 2, 10, 5, 33, 11, 1, 58, 20, 47, 53, 31, 18, 30, 21, 54, 24, 48, 16, 3, 42, 14, 23, 59, 26, 27, 8, 55, 39, 22, 19, 4, 29, 43, 34, 51, 6, 12, 17, 36, 35, 0, 50, 7, 45, 25, 32, 57, 44], 'cur_cost': 103704.0}, {'tour': [39, 56, 40, 19, 20, 52, 23, 11, 34, 13, 14, 12, 41, 57, 35, 15, 27, 59, 9, 21, 42, 5, 46, 32, 17, 48, 2, 31, 8, 1, 10, 6, 54, 44, 3, 55, 28, 43, 33, 22, 16, 47, 37, 26, 50, 0, 30, 4, 25, 53, 58, 45, 18, 36, 51, 7, 24, 38, 49, 29], 'cur_cost': 104877.0}, {'tour': [4, 53, 30, 46, 19, 22, 17, 49, 31, 28, 5, 23, 8, 15, 37, 34, 10, 58, 25, 40, 51, 27, 57, 38, 14, 20, 32, 50, 13, 44, 1, 39, 12, 3, 47, 36, 21, 6, 11, 9, 16, 45, 29, 2, 41, 55, 0, 33, 56, 24, 7, 54, 48, 35, 59, 43, 42, 26, 52, 18], 'cur_cost': 107289.0}, {'tour': array([ 4, 49, 42,  3, 28, 33, 27, 43, 36,  2, 10, 45, 25, 51,  1, 12, 56,
       20, 31, 35, 53, 39,  9, 58, 50,  7, 18, 19, 41, 40, 26, 37, 14, 22,
       16, 54, 17, 52, 13, 59, 21,  5, 15, 48, 32,  8, 34,  6, 57, 38, 44,
       24, 29, 23,  0, 47, 30, 46, 55, 11]), 'cur_cost': 83670.0}, {'tour': array([ 7, 59, 57,  0, 16, 50, 56, 52, 39, 42, 32, 58, 54, 14, 23,  8, 44,
        9, 21, 10, 36, 29, 53, 31, 43, 51, 22, 40,  6, 47,  5,  2,  1, 19,
       45, 48,  3, 35, 34, 25, 13, 17, 38, 18, 15, 26, 11,  4, 30, 37, 24,
       55, 33, 28, 41, 12, 49, 20, 27, 46]), 'cur_cost': 92004.0}, {'tour': array([11, 24, 34, 59, 37, 57, 16, 51,  4, 48, 30,  7, 18, 32, 44, 10, 47,
        1,  3, 26, 40, 14, 15,  0, 42,  5, 36, 27, 22, 28, 43, 17, 23, 39,
       25, 20, 35, 56, 38, 21, 12, 13, 54, 58,  9, 41, 49,  6, 45, 46, 29,
        2,  8, 52, 50, 19, 31, 55, 33, 53]), 'cur_cost': 99171.0}]
2025-06-25 10:43:50,681 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-25 10:43:50,681 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-25 10:43:50,682 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-25 10:43:50,682 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [52, 41, 59, 10, 30, 56, 47, 16, 1, 39, 33, 31, 28, 20, 21, 4, 48, 24, 29, 13, 51, 27, 55, 17, 9, 54, 12, 44, 32, 34, 37, 6, 46, 26, 0, 42, 36, 23, 50, 49, 25, 40, 53, 3, 18, 35, 22, 19, 8, 14, 45, 58, 15, 2, 11, 38, 57, 7, 43, 5], 'cur_cost': 100280.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [52, 34, 38, 45, 16, 57, 4, 49, 25, 58, 10, 35, 22, 30, 23, 54, 59, 37, 50, 14, 48, 11, 32, 56, 6, 27, 13, 26, 33, 44, 2, 39, 19, 31, 17, 28, 20, 7, 40, 51, 1, 18, 43, 47, 53, 21, 36, 8, 5, 15, 0, 46, 42, 29, 3, 9, 41, 12, 55, 24], 'cur_cost': 107466.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [41, 29, 52, 16, 56, 21, 2, 22, 44, 13, 54, 4, 25, 32, 40, 53, 12, 46, 58, 10, 39, 35, 11, 50, 26, 34, 19, 18, 30, 48, 38, 57, 36, 20, 8, 3, 5, 17, 45, 23, 24, 33, 49, 15, 0, 28, 47, 9, 37, 7, 55, 42, 31, 1, 6, 43, 59, 51, 14, 27], 'cur_cost': 104182.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [51, 21, 40, 26, 32, 43, 11, 23, 55, 52, 3, 24, 56, 10, 47, 4, 14, 19, 28, 42, 5, 57, 36, 38, 39, 31, 46, 16, 0, 27, 50, 9, 58, 12, 45, 34, 6, 49, 13, 18, 41, 17, 2, 33, 7, 22, 35, 20, 48, 1, 25, 8, 59, 15, 29, 37, 44, 30, 53, 54], 'cur_cost': 96038.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [49, 15, 40, 56, 28, 13, 46, 52, 41, 9, 38, 37, 2, 10, 5, 33, 11, 1, 58, 20, 47, 53, 31, 18, 30, 21, 54, 24, 48, 16, 3, 42, 14, 23, 59, 26, 27, 8, 55, 39, 22, 19, 4, 29, 43, 34, 51, 6, 12, 17, 36, 35, 0, 50, 7, 45, 25, 32, 57, 44], 'cur_cost': 103704.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [39, 56, 40, 19, 20, 52, 23, 11, 34, 13, 14, 12, 41, 57, 35, 15, 27, 59, 9, 21, 42, 5, 46, 32, 17, 48, 2, 31, 8, 1, 10, 6, 54, 44, 3, 55, 28, 43, 33, 22, 16, 47, 37, 26, 50, 0, 30, 4, 25, 53, 58, 45, 18, 36, 51, 7, 24, 38, 49, 29], 'cur_cost': 104877.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [4, 53, 30, 46, 19, 22, 17, 49, 31, 28, 5, 23, 8, 15, 37, 34, 10, 58, 25, 40, 51, 27, 57, 38, 14, 20, 32, 50, 13, 44, 1, 39, 12, 3, 47, 36, 21, 6, 11, 9, 16, 45, 29, 2, 41, 55, 0, 33, 56, 24, 7, 54, 48, 35, 59, 43, 42, 26, 52, 18], 'cur_cost': 107289.0}}]
2025-06-25 10:43:50,682 - __main__ - INFO - 进化阶段完成
2025-06-25 10:43:50,683 - __main__ - INFO - 开始评估阶段
2025-06-25 10:43:50,683 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-25 10:43:50,684 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-25 10:43:50,684 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 13173.0 → 83670.0 (Δ -70497.0)
- Mean cost: 88868.9 → 99868.1
- Diversity: 0.861 → 0.849 (Δ -0.0119)

#### Elite metrics
- Elite count: 2 → 29
- Best elite cost: 9644 → 9614 (Δ 30)

#### Strategy performance
- Explore: 7 inds, success 14.3% , avg Δ -16179.57
- Exploit: 3 inds, success 33.3% , avg Δ 1088.33

#### Other indicators
- No-change individuals: 0
- Regression individuals: 8
- Historical trends: Last 1 iterations: costs [13173.0], diversity [0.9625925925925926]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-25 10:43:54,040 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "Early Exploitation",
  "strategy_effectiveness": "Exploit strategy shows higher success rate (33.3%) and positive average delta (1088.33), suggesting it's more effective at refining existing solutions. However, the increase in 'Elite Count' suggests that even 'Explore' may be finding new promising areas despite the low success rate, indicating initial exploration of the search space.",
  "balance_state": "Exploration is currently weak. While exploit offers a small average improvement, explore has a significant negative average improvement (potentially getting stuck in local optima). The drastic increase in the minimum cost and mean cost raises concerns about exploration leading to worse solutions.",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight_adjustment": "Monitor diversity closely, consider a small increase (e.g. 0.05 if it continues dropping)."
  }
}
```
2025-06-25 10:43:54,066 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-25 10:43:54,066 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "Early Exploitation",
  "strategy_effectiveness": "Exploit strategy shows higher success rate (33.3%) and positive average delta (1088.33), suggesting it's more effective at refining existing solutions. However, the increase in 'Elite Count' suggests that even 'Explore' may be finding new promising areas despite the low success rate, indicating initial exploration of the search space.",
  "balance_state": "Exploration is currently weak. While exploit offers a small average improvement, explore has a significant negative average improvement (potentially getting stuck in local optima). The drastic increase in the minimum cost and mean cost raises concerns about exploration leading to worse solutions.",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight_adjustment": "Monitor diversity closely, consider a small increase (e.g. 0.05 if it continues dropping)."
  }
}
```
2025-06-25 10:43:54,066 - __main__ - INFO - 评估阶段完成
2025-06-25 10:43:54,067 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "Early Exploitation",
  "strategy_effectiveness": "Exploit strategy shows higher success rate (33.3%) and positive average delta (1088.33), suggesting it's more effective at refining existing solutions. However, the increase in 'Elite Count' suggests that even 'Explore' may be finding new promising areas despite the low success rate, indicating initial exploration of the search space.",
  "balance_state": "Exploration is currently weak. While exploit offers a small average improvement, explore has a significant negative average improvement (potentially getting stuck in local optima). The drastic increase in the minimum cost and mean cost raises concerns about exploration leading to worse solutions.",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight_adjustment": "Monitor diversity closely, consider a small increase (e.g. 0.05 if it continues dropping)."
  }
}
```
2025-06-25 10:43:54,067 - __main__ - INFO - 当前最佳适应度: 83670.0
2025-06-25 10:43:54,068 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite12_60_route_1.pkl
2025-06-25 10:43:54,068 - __main__ - INFO - composite12_60 开始进化第 3 代
2025-06-25 10:43:54,069 - __main__ - INFO - 开始分析阶段
2025-06-25 10:43:54,069 - StatsExpert - INFO - 开始统计分析
2025-06-25 10:43:54,083 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 83670.0, 'max': 107466.0, 'mean': 99868.1, 'std': 7146.73751371911}, 'diversity': 0.9622222222222222, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-25 10:43:54,084 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 83670.0, 'max': 107466.0, 'mean': 99868.1, 'std': 7146.73751371911}, 'diversity_level': 0.9622222222222222, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[1380, 1590], [1386, 1610], [1446, 1664], [1473, 1651], [1403, 1573], [1429, 1576], [1414, 1596], [1421, 1664], [1385, 1662], [1475, 1616], [1405, 1625], [1439, 1639], [3242, 2428], [3193, 2490], [3212, 2461], [3238, 2498], [3270, 2452], [3236, 2513], [3196, 2449], [3220, 2481], [3262, 2465], [3198, 2468], [3267, 2481], [3264, 2442], [426, 2488], [478, 2496], [499, 2467], [445, 2489], [451, 2481], [441, 2477], [461, 2463], [511, 2487], [499, 2478], [431, 2471], [461, 2456], [476, 2486], [832, 400], [885, 348], [838, 364], [921, 379], [900, 346], [918, 411], [855, 361], [866, 388], [878, 327], [841, 380], [902, 374], [871, 361], [2763, 1531], [2817, 1507], [2789, 1453], [2739, 1464], [2773, 1490], [2783, 1514], [2808, 1535], [2797, 1510], [2807, 1517], [2793, 1495], [2828, 1490], [2736, 1497]], 'distance_matrix': array([[   0.,   21.,   99., ..., 1416., 1451., 1359.],
       [  21.,    0.,   81., ..., 1412., 1447., 1355.],
       [  99.,   81.,    0., ..., 1358., 1393., 1301.],
       ...,
       [1416., 1412., 1358., ...,    0.,   35.,   57.],
       [1451., 1447., 1393., ...,   35.,    0.,   92.],
       [1359., 1355., 1301., ...,   57.,   92.,    0.]])}
2025-06-25 10:43:54,084 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-25 10:43:54,084 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-25 10:43:54,084 - PathExpert - INFO - 开始路径结构分析
2025-06-25 10:43:54,088 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-25 10:43:54,088 - PathExpert - INFO - 路径结构分析完成
2025-06-25 10:43:54,088 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-25 10:43:54,088 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-25 10:43:54,088 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-25 10:43:54,088 - EliteExpert - INFO - 开始精英解分析
2025-06-25 10:43:54,101 - EliteExpert - INFO - 精英解分析完成
2025-06-25 10:43:54,101 - __main__ - INFO - 精英专家分析报告: {'elite_count': 29, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 74056.0, 'avg_gap': 87564.23793103448}, 'structure_gap': {'unique_elite_edges': 207, 'unique_pop_edges': 514, 'common_edges': 33}}, 'elite_diversity': {'diversity_score': 0.2260673234811167}}
2025-06-25 10:43:54,101 - __main__ - INFO - 更新共享数据: elite_features
2025-06-25 10:43:54,101 - LandscapeExpert - INFO - 开始景观分析
2025-06-25 10:43:54,103 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-25 10:43:54,103 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 83670.0, mean 99868.1, max 107466.0, std 7146.73751371911
- diversity: 0.9622222222222222
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [13173.0, 83670.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [426.0, 327.0, 3270.0, 2513.0]
- centroid/spread: [1756.217, 1687.917] | rms 1327.809
- density_grid(3x3): [[12, 0, 0], [0, 12, 12], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [7.0, 1094.25, 1427.5, 2273.75, 3222.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 7, 6, 9, 12, 8, 4, 7]
- nn_median_dist: 17.242

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-25 10:44:01,579 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High. The significant difference between minimum and maximum edge lengths (edge_len_stats: min 7.0, max 3222.0) suggests a highly variable cost landscape. Solutions can be dramatically affected by small changes.",
    "modality": "Potentially multi-modal. While a nearest neighbor distance of 17.242 indicates nodes are generally close, the high edge length variation and density grid suggest several regions with distinct characteristics, possibly leading to multiple local optima.",
    "deceptiveness": "Likely present. The long_edge_ratio of 0.25 indicates a significant number of long edges are present, suggesting that greedy or local improvements might be easily trapped. The presence of long edges implies detours in current solutions, making finding optimal paths challenging."
  },
  "population_state": {
    "coverage": "Reasonable. Diversity is relatively high (0.962), suggesting good exploration across the search space. However, the density grid indicates uneven exploration, with higher concentration in specific areas. Further, the fact that `elite_count` is zero suggests that the minimum cost is still far from the global optimum.",
    "convergence": "Low. The convergence is 0.0, and the cost statistics show a relatively high standard deviation (7146.737) within the population, suggesting the algorithm has not yet converged to a good local optima. Recent best costs show minimal improvement, further indicating a lack of convergence."
  },
  "difficult_regions": [
    "Low-density regions identified in the density grid: (0,1), (0,2), (1,0), (2,1) potentially correspond to regions where the algorithm struggles to find good solutions due to sparsity of nodes.",
    "Corridors formed by long edges may represent paths the algorithm finds initially, but are difficult to improve locally. These areas need global re-optimization.",
    "Regions with higher variance in edge lengths may denote areas where adding/removing edges may have a drastic impact on the overall cost."
  ],
  "opportunity_regions": [
    "High-density cells in the density grid (e.g., cells (0,0), (1,1), (1,2), (2,0), and (2,2)) represent regions where nodes are clustered tightly, potentially creating opportunities for local optimization and reducing path lengths.",
    "Given the lack of information from structural signals (high_quality_edges_sample, common_subpaths_sample, etc.), no specific edges or subpaths are readily identified as good candidates for exploration. More advanced structural analysis is needed.",
    "Exploring edges between high-density cells could lead to efficient shortcuts and reduce the overall path length. Focusing on connecting cells that are relatively close but not yet connected is a promising direction."
  ],
  "evolution_phase": "Exploration/Diversification. The algorithm is still in the early stages. The high diversity, low convergence, and stagnation in recent best costs suggest that the population is exploring the search space but has not yet settled into a promising region.",
  "evolution_direction": {
    "focus": "Shift focus towards exploitation while still maintaining some exploration. Given the reasonable diversity and early stage of the search, it is important to exploit the potentially good regions identified while not completely abandoning exploration to avoid premature convergence. The absence of high-quality edges and common subpaths implies the algorithm needs to find building blocks for solution construction.",
    "operator_suggestions": [
      "Apply a local search operator (e.g., 2-opt, 3-opt) within the high-density cells to refine paths locally.",
      "Introduce a path relinking or crossover operator that combines features of different solutions, specifically focusing on connecting different high-density regions to facilitate the discovery of better paths.",
      "Increase the mutation rate slightly to introduce new diversity and escape local optima, particularly focusing on mutation operators that can modify longer edges or connections between dense regions.",
      "Investigate and prioritize solutions with shorter edges within the high density cells during selection."
    ]
  }
}
```
2025-06-25 10:44:01,579 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-25 10:44:01,579 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High. The significant difference between minimum and maximum edge lengths (edge_len_stats: min 7.0, max 3222.0) suggests a highly variable cost landscape. Solutions can be dramatically affected by small changes.', 'modality': 'Potentially multi-modal. While a nearest neighbor distance of 17.242 indicates nodes are generally close, the high edge length variation and density grid suggest several regions with distinct characteristics, possibly leading to multiple local optima.', 'deceptiveness': 'Likely present. The long_edge_ratio of 0.25 indicates a significant number of long edges are present, suggesting that greedy or local improvements might be easily trapped. The presence of long edges implies detours in current solutions, making finding optimal paths challenging.'}, 'population_state': {'coverage': 'Reasonable. Diversity is relatively high (0.962), suggesting good exploration across the search space. However, the density grid indicates uneven exploration, with higher concentration in specific areas. Further, the fact that `elite_count` is zero suggests that the minimum cost is still far from the global optimum.', 'convergence': 'Low. The convergence is 0.0, and the cost statistics show a relatively high standard deviation (7146.737) within the population, suggesting the algorithm has not yet converged to a good local optima. Recent best costs show minimal improvement, further indicating a lack of convergence.'}, 'difficult_regions': ['Low-density regions identified in the density grid: (0,1), (0,2), (1,0), (2,1) potentially correspond to regions where the algorithm struggles to find good solutions due to sparsity of nodes.', 'Corridors formed by long edges may represent paths the algorithm finds initially, but are difficult to improve locally. These areas need global re-optimization.', 'Regions with higher variance in edge lengths may denote areas where adding/removing edges may have a drastic impact on the overall cost.'], 'opportunity_regions': ['High-density cells in the density grid (e.g., cells (0,0), (1,1), (1,2), (2,0), and (2,2)) represent regions where nodes are clustered tightly, potentially creating opportunities for local optimization and reducing path lengths.', 'Given the lack of information from structural signals (high_quality_edges_sample, common_subpaths_sample, etc.), no specific edges or subpaths are readily identified as good candidates for exploration. More advanced structural analysis is needed.', 'Exploring edges between high-density cells could lead to efficient shortcuts and reduce the overall path length. Focusing on connecting cells that are relatively close but not yet connected is a promising direction.'], 'evolution_phase': 'Exploration/Diversification. The algorithm is still in the early stages. The high diversity, low convergence, and stagnation in recent best costs suggest that the population is exploring the search space but has not yet settled into a promising region.', 'evolution_direction': {'focus': 'Shift focus towards exploitation while still maintaining some exploration. Given the reasonable diversity and early stage of the search, it is important to exploit the potentially good regions identified while not completely abandoning exploration to avoid premature convergence. The absence of high-quality edges and common subpaths implies the algorithm needs to find building blocks for solution construction.', 'operator_suggestions': ['Apply a local search operator (e.g., 2-opt, 3-opt) within the high-density cells to refine paths locally.', 'Introduce a path relinking or crossover operator that combines features of different solutions, specifically focusing on connecting different high-density regions to facilitate the discovery of better paths.', 'Increase the mutation rate slightly to introduce new diversity and escape local optima, particularly focusing on mutation operators that can modify longer edges or connections between dense regions.', 'Investigate and prioritize solutions with shorter edges within the high density cells during selection.']}}
2025-06-25 10:44:01,579 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-25 10:44:01,579 - __main__ - INFO - 分析阶段完成
2025-06-25 10:44:01,579 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High. The significant difference between minimum and maximum edge lengths (edge_len_stats: min 7.0, max 3222.0) suggests a highly variable cost landscape. Solutions can be dramatically affected by small changes.', 'modality': 'Potentially multi-modal. While a nearest neighbor distance of 17.242 indicates nodes are generally close, the high edge length variation and density grid suggest several regions with distinct characteristics, possibly leading to multiple local optima.', 'deceptiveness': 'Likely present. The long_edge_ratio of 0.25 indicates a significant number of long edges are present, suggesting that greedy or local improvements might be easily trapped. The presence of long edges implies detours in current solutions, making finding optimal paths challenging.'}, 'population_state': {'coverage': 'Reasonable. Diversity is relatively high (0.962), suggesting good exploration across the search space. However, the density grid indicates uneven exploration, with higher concentration in specific areas. Further, the fact that `elite_count` is zero suggests that the minimum cost is still far from the global optimum.', 'convergence': 'Low. The convergence is 0.0, and the cost statistics show a relatively high standard deviation (7146.737) within the population, suggesting the algorithm has not yet converged to a good local optima. Recent best costs show minimal improvement, further indicating a lack of convergence.'}, 'difficult_regions': ['Low-density regions identified in the density grid: (0,1), (0,2), (1,0), (2,1) potentially correspond to regions where the algorithm struggles to find good solutions due to sparsity of nodes.', 'Corridors formed by long edges may represent paths the algorithm finds initially, but are difficult to improve locally. These areas need global re-optimization.', 'Regions with higher variance in edge lengths may denote areas where adding/removing edges may have a drastic impact on the overall cost.'], 'opportunity_regions': ['High-density cells in the density grid (e.g., cells (0,0), (1,1), (1,2), (2,0), and (2,2)) represent regions where nodes are clustered tightly, potentially creating opportunities for local optimization and reducing path lengths.', 'Given the lack of information from structural signals (high_quality_edges_sample, common_subpaths_sample, etc.), no specific edges or subpaths are readily identified as good candidates for exploration. More advanced structural analysis is needed.', 'Exploring edges between high-density cells could lead to efficient shortcuts and reduce the overall path length. Focusing on connecting cells that are relatively close but not yet connected is a promising direction.'], 'evolution_phase': 'Exploration/Diversification. The algorithm is still in the early stages. The high diversity, low convergence, and stagnation in recent best costs suggest that the population is exploring the search space but has not yet settled into a promising region.', 'evolution_direction': {'focus': 'Shift focus towards exploitation while still maintaining some exploration. Given the reasonable diversity and early stage of the search, it is important to exploit the potentially good regions identified while not completely abandoning exploration to avoid premature convergence. The absence of high-quality edges and common subpaths implies the algorithm needs to find building blocks for solution construction.', 'operator_suggestions': ['Apply a local search operator (e.g., 2-opt, 3-opt) within the high-density cells to refine paths locally.', 'Introduce a path relinking or crossover operator that combines features of different solutions, specifically focusing on connecting different high-density regions to facilitate the discovery of better paths.', 'Increase the mutation rate slightly to introduce new diversity and escape local optima, particularly focusing on mutation operators that can modify longer edges or connections between dense regions.', 'Investigate and prioritize solutions with shorter edges within the high density cells during selection.']}}
2025-06-25 10:44:01,579 - __main__ - INFO - 开始策略分配阶段
2025-06-25 10:44:01,579 - StrategyExpert - INFO - 开始策略分配分析
2025-06-25 10:44:01,583 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 83670.0
  • mean_cost: 99868.1
- Search-space: ruggedness High. The significant difference between minimum and maximum edge lengths (edge_len_stats: min 7.0, max 3222.0) suggests a highly variable cost landscape. Solutions can be dramatically affected by small changes., modality Potentially multi-modal. While a nearest neighbor distance of 17.242 indicates nodes are generally close, the high edge length variation and density grid suggest several regions with distinct characteristics, possibly leading to multiple local optima., deceptiveness Likely present. The long_edge_ratio of 0.25 indicates a significant number of long edges are present, suggesting that greedy or local improvements might be easily trapped. The presence of long edges implies detours in current solutions, making finding optimal paths challenging.
- Evolution phase: Exploration/Diversification. The algorithm is still in the early stages. The high diversity, low convergence, and stagnation in recent best costs suggest that the population is exploring the search space but has not yet settled into a promising region.
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): Low-density regions , Corridors formed by ...
- Opportunity regions (# 3): High-density cells i, Given the lack of in...
- Last-iteration feedback: ```json
{
  "evolution_stage": "Early Exploitation",
  "strategy_effectiveness": "Exploit strategy shows higher success ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-25 10:44:01,583 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-25 10:44:04,986 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High landscape ruggedness, low diversity, and exploration phase necessitate a strong exploratory bias. The best individuals (2, 5, 8) are assigned to exploit, while the rest explore to broaden the search."
}
```
2025-06-25 10:44:04,986 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-06-25 10:44:04,986 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-06-25 10:44:04,987 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High landscape ruggedness, low diversity, and exploration phase necessitate a strong exploratory bias. The best individuals (2, 5, 8) are assigned to exploit, while the rest explore to broaden the search."
}
```
2025-06-25 10:44:04,987 - __main__ - INFO - 策略分配阶段完成
2025-06-25 10:44:04,987 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-06-25 10:44:04,987 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High landscape ruggedness, low diversity, and exploration phase necessitate a strong exploratory bias. The best individuals (2, 5, 8) are assigned to exploit, while the rest explore to broaden the search."
}
```
2025-06-25 10:44:04,988 - __main__ - INFO - 开始进化阶段
2025-06-25 10:44:04,988 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-25 10:44:04,989 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:44:04,989 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100280.0 (rank 40.0%)
- Population diversity: 0.8491525423728814
- Evolution phase: Exploration/Diversification. The algorithm is still in the early stages. The high diversity, low convergence, and stagnation in recent best costs suggest that the population is exploring the search space but has not yet settled into a promising region.
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Given the lack ...
- Difficult regions to avoid (sample): Low-density reg, Corridors forme...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:44:04,989 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:44:09,696 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-25 10:44:09,696 - ExplorationExpert - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-25 10:44:09,696 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-25 10:44:09,696 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:44:09,696 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 107466.0 (rank 90.0%)
- Population diversity: 0.8491525423728814
- Evolution phase: Exploration/Diversification. The algorithm is still in the early stages. The high diversity, low convergence, and stagnation in recent best costs suggest that the population is exploring the search space but has not yet settled into a promising region.
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Given the lack ...
- Difficult regions to avoid (sample): Low-density reg, Corridors forme...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:44:09,696 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:44:18,585 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    58,
    3,
    29,
    12,
    50,
    40,
    48,
    2,
    43,
    44,
    26,
    37,
    5,
    54,
    18,
    56,
    38,
    45,
    35,
    17,
    14,
    57,
    51,
    23,
    24,
    9,
    22,
    13,
    46,
    39,
    19,
    16,
    36,
    41,
    15,
    47,
    59,
    20,
    49,
    32,
    0,
    28,
    31,
    30,
    25,
    8,
    4,
    53,
    6,
    1,
    27,
    55,
    7,
    42,
    10,
    33,
    52,
    11,
    34,
    21
  ],
  "targeted_regions": "Explore low-density cells near 'ce' region, shifting node sequence around node 7.",
  "strategy_comment": "Introduced randomness, explored sparse regions. Shifted sequence near node 7 to increase diversity."
}
```
2025-06-25 10:44:18,585 - ExplorationExpert - INFO - 计算路径成本
2025-06-25 10:44:18,585 - ExplorationExpert - INFO - 探索路径生成完成，成本: 86340.0, 路径: [58, 3, 29, 12, 50, 40, 48, 2, 43, 44, 26, 37, 5, 54, 18, 56, 38, 45, 35, 17, 14, 57, 51, 23, 24, 9, 22, 13, 46, 39, 19, 16, 36, 41, 15, 47, 59, 20, 49, 32, 0, 28, 31, 30, 25, 8, 4, 53, 6, 1, 27, 55, 7, 42, 10, 33, 52, 11, 34, 21]
2025-06-25 10:44:18,585 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [58, 3, 29, 12, 50, 40, 48, 2, 43, 44, 26, 37, 5, 54, 18, 56, 38, 45, 35, 17, 14, 57, 51, 23, 24, 9, 22, 13, 46, 39, 19, 16, 36, 41, 15, 47, 59, 20, 49, 32, 0, 28, 31, 30, 25, 8, 4, 53, 6, 1, 27, 55, 7, 42, 10, 33, 52, 11, 34, 21], 'cur_cost': 86340.0}
2025-06-25 10:44:18,585 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-25 10:44:18,585 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-25 10:44:18,585 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-25 10:44:18,585 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 81800.0
2025-06-25 10:44:19,087 - ExploitationExpert - INFO - res_population_num: 40
2025-06-25 10:44:19,087 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9626, 9630, 9630, 9630, 9644, 9644, 9644, 87470, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-25 10:44:19,087 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1,  6,  5,  9,  3,  2,  7, 11, 10,  8, 26, 34, 30, 29, 33, 24,
       27, 28, 35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 59, 52,
       50, 58, 49, 56, 55, 57, 53, 48, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  1, 10,  6,  5,  9,  3, 11,  2,  7,  8, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0, 55, 46, 29,  1, 21, 58, 51, 36, 24, 56, 23, 16, 15, 49, 45, 31,
        2, 52, 38, 27,  7,  9,  3, 28, 34, 44, 11, 35, 14, 18, 19, 20, 48,
        8, 47, 39, 50, 32, 13, 57, 22, 30, 59, 40, 41, 54,  5, 10, 37, 53,
       33, 25, 43, 17, 26, 42,  4, 12,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64)]
2025-06-25 10:44:19,103 - ExploitationExpert - INFO - populations_num: 10
2025-06-25 10:44:19,103 - ExploitationExpert - INFO - populations: [{'tour': [52, 41, 59, 10, 30, 56, 47, 16, 1, 39, 33, 31, 28, 20, 21, 4, 48, 24, 29, 13, 51, 27, 55, 17, 9, 54, 12, 44, 32, 34, 37, 6, 46, 26, 0, 42, 36, 23, 50, 49, 25, 40, 53, 3, 18, 35, 22, 19, 8, 14, 45, 58, 15, 2, 11, 38, 57, 7, 43, 5], 'cur_cost': 100280.0}, {'tour': [58, 3, 29, 12, 50, 40, 48, 2, 43, 44, 26, 37, 5, 54, 18, 56, 38, 45, 35, 17, 14, 57, 51, 23, 24, 9, 22, 13, 46, 39, 19, 16, 36, 41, 15, 47, 59, 20, 49, 32, 0, 28, 31, 30, 25, 8, 4, 53, 6, 1, 27, 55, 7, 42, 10, 33, 52, 11, 34, 21], 'cur_cost': 86340.0}, {'tour': array([45,  4, 26, 34, 23, 22, 14, 58, 32, 20, 17, 44, 21, 30, 29, 48, 59,
       31, 55, 53, 49, 16, 54,  6, 10, 15, 42, 36, 24, 33, 11, 27, 46,  7,
       43, 37, 35, 19, 13, 47, 12,  3, 40, 56, 25, 57, 51, 18, 52,  0,  1,
        8, 38, 39, 50,  5,  9, 28,  2, 41]), 'cur_cost': 81800.0}, {'tour': [51, 21, 40, 26, 32, 43, 11, 23, 55, 52, 3, 24, 56, 10, 47, 4, 14, 19, 28, 42, 5, 57, 36, 38, 39, 31, 46, 16, 0, 27, 50, 9, 58, 12, 45, 34, 6, 49, 13, 18, 41, 17, 2, 33, 7, 22, 35, 20, 48, 1, 25, 8, 59, 15, 29, 37, 44, 30, 53, 54], 'cur_cost': 96038.0}, {'tour': [49, 15, 40, 56, 28, 13, 46, 52, 41, 9, 38, 37, 2, 10, 5, 33, 11, 1, 58, 20, 47, 53, 31, 18, 30, 21, 54, 24, 48, 16, 3, 42, 14, 23, 59, 26, 27, 8, 55, 39, 22, 19, 4, 29, 43, 34, 51, 6, 12, 17, 36, 35, 0, 50, 7, 45, 25, 32, 57, 44], 'cur_cost': 103704.0}, {'tour': [39, 56, 40, 19, 20, 52, 23, 11, 34, 13, 14, 12, 41, 57, 35, 15, 27, 59, 9, 21, 42, 5, 46, 32, 17, 48, 2, 31, 8, 1, 10, 6, 54, 44, 3, 55, 28, 43, 33, 22, 16, 47, 37, 26, 50, 0, 30, 4, 25, 53, 58, 45, 18, 36, 51, 7, 24, 38, 49, 29], 'cur_cost': 104877.0}, {'tour': [4, 53, 30, 46, 19, 22, 17, 49, 31, 28, 5, 23, 8, 15, 37, 34, 10, 58, 25, 40, 51, 27, 57, 38, 14, 20, 32, 50, 13, 44, 1, 39, 12, 3, 47, 36, 21, 6, 11, 9, 16, 45, 29, 2, 41, 55, 0, 33, 56, 24, 7, 54, 48, 35, 59, 43, 42, 26, 52, 18], 'cur_cost': 107289.0}, {'tour': array([ 4, 49, 42,  3, 28, 33, 27, 43, 36,  2, 10, 45, 25, 51,  1, 12, 56,
       20, 31, 35, 53, 39,  9, 58, 50,  7, 18, 19, 41, 40, 26, 37, 14, 22,
       16, 54, 17, 52, 13, 59, 21,  5, 15, 48, 32,  8, 34,  6, 57, 38, 44,
       24, 29, 23,  0, 47, 30, 46, 55, 11]), 'cur_cost': 83670.0}, {'tour': array([ 7, 59, 57,  0, 16, 50, 56, 52, 39, 42, 32, 58, 54, 14, 23,  8, 44,
        9, 21, 10, 36, 29, 53, 31, 43, 51, 22, 40,  6, 47,  5,  2,  1, 19,
       45, 48,  3, 35, 34, 25, 13, 17, 38, 18, 15, 26, 11,  4, 30, 37, 24,
       55, 33, 28, 41, 12, 49, 20, 27, 46]), 'cur_cost': 92004.0}, {'tour': array([11, 24, 34, 59, 37, 57, 16, 51,  4, 48, 30,  7, 18, 32, 44, 10, 47,
        1,  3, 26, 40, 14, 15,  0, 42,  5, 36, 27, 22, 28, 43, 17, 23, 39,
       25, 20, 35, 56, 38, 21, 12, 13, 54, 58,  9, 41, 49,  6, 45, 46, 29,
        2,  8, 52, 50, 19, 31, 55, 33, 53]), 'cur_cost': 99171.0}]
2025-06-25 10:44:19,105 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-25 10:44:19,105 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-25 10:44:19,105 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-06-25 10:44:19,105 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-25 10:44:19,106 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:44:19,106 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 96038.0 (rank 40.0%)
- Population diversity: 0.8576271186440678
- Evolution phase: Exploration/Diversification. The algorithm is still in the early stages. The high diversity, low convergence, and stagnation in recent best costs suggest that the population is exploring the search space but has not yet settled into a promising region.
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Given the lack ...
- Difficult regions to avoid (sample): Low-density reg, Corridors forme...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:44:19,107 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:44:24,256 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-25 10:44:24,256 - ExplorationExpert - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-25 10:44:24,256 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-25 10:44:24,256 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:44:24,256 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 103704.0 (rank 70.0%)
- Population diversity: 0.8576271186440678
- Evolution phase: Exploration/Diversification. The algorithm is still in the early stages. The high diversity, low convergence, and stagnation in recent best costs suggest that the population is exploring the search space but has not yet settled into a promising region.
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Given the lack ...
- Difficult regions to avoid (sample): Low-density reg, Corridors forme...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:44:24,256 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:44:31,430 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-25 10:44:31,430 - ExplorationExpert - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-25 10:44:31,430 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-25 10:44:31,430 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-25 10:44:31,430 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-25 10:44:31,430 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 94290.0
2025-06-25 10:44:31,937 - ExploitationExpert - INFO - res_population_num: 41
2025-06-25 10:44:31,937 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9626, 9630, 9630, 9630, 9644, 9644, 9644, 87470, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-25 10:44:31,938 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1,  6,  5,  9,  3,  2,  7, 11, 10,  8, 26, 34, 30, 29, 33, 24,
       27, 28, 35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 59, 52,
       50, 58, 49, 56, 55, 57, 53, 48, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  1, 10,  6,  5,  9,  3, 11,  2,  7,  8, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0, 55, 46, 29,  1, 21, 58, 51, 36, 24, 56, 23, 16, 15, 49, 45, 31,
        2, 52, 38, 27,  7,  9,  3, 28, 34, 44, 11, 35, 14, 18, 19, 20, 48,
        8, 47, 39, 50, 32, 13, 57, 22, 30, 59, 40, 41, 54,  5, 10, 37, 53,
       33, 25, 43, 17, 26, 42,  4, 12,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64)]
2025-06-25 10:44:31,957 - ExploitationExpert - INFO - populations_num: 10
2025-06-25 10:44:31,957 - ExploitationExpert - INFO - populations: [{'tour': [52, 41, 59, 10, 30, 56, 47, 16, 1, 39, 33, 31, 28, 20, 21, 4, 48, 24, 29, 13, 51, 27, 55, 17, 9, 54, 12, 44, 32, 34, 37, 6, 46, 26, 0, 42, 36, 23, 50, 49, 25, 40, 53, 3, 18, 35, 22, 19, 8, 14, 45, 58, 15, 2, 11, 38, 57, 7, 43, 5], 'cur_cost': 100280.0}, {'tour': [58, 3, 29, 12, 50, 40, 48, 2, 43, 44, 26, 37, 5, 54, 18, 56, 38, 45, 35, 17, 14, 57, 51, 23, 24, 9, 22, 13, 46, 39, 19, 16, 36, 41, 15, 47, 59, 20, 49, 32, 0, 28, 31, 30, 25, 8, 4, 53, 6, 1, 27, 55, 7, 42, 10, 33, 52, 11, 34, 21], 'cur_cost': 86340.0}, {'tour': array([45,  4, 26, 34, 23, 22, 14, 58, 32, 20, 17, 44, 21, 30, 29, 48, 59,
       31, 55, 53, 49, 16, 54,  6, 10, 15, 42, 36, 24, 33, 11, 27, 46,  7,
       43, 37, 35, 19, 13, 47, 12,  3, 40, 56, 25, 57, 51, 18, 52,  0,  1,
        8, 38, 39, 50,  5,  9, 28,  2, 41]), 'cur_cost': 81800.0}, {'tour': [51, 21, 40, 26, 32, 43, 11, 23, 55, 52, 3, 24, 56, 10, 47, 4, 14, 19, 28, 42, 5, 57, 36, 38, 39, 31, 46, 16, 0, 27, 50, 9, 58, 12, 45, 34, 6, 49, 13, 18, 41, 17, 2, 33, 7, 22, 35, 20, 48, 1, 25, 8, 59, 15, 29, 37, 44, 30, 53, 54], 'cur_cost': 96038.0}, {'tour': [49, 15, 40, 56, 28, 13, 46, 52, 41, 9, 38, 37, 2, 10, 5, 33, 11, 1, 58, 20, 47, 53, 31, 18, 30, 21, 54, 24, 48, 16, 3, 42, 14, 23, 59, 26, 27, 8, 55, 39, 22, 19, 4, 29, 43, 34, 51, 6, 12, 17, 36, 35, 0, 50, 7, 45, 25, 32, 57, 44], 'cur_cost': 103704.0}, {'tour': array([26, 34,  3, 43,  1, 17, 31, 51, 18, 23, 46, 27, 49, 13, 12, 32, 24,
       37, 11, 48, 56, 15, 35,  2, 58, 54, 29, 21,  4, 45, 41, 20,  9, 33,
        5, 42, 40,  6, 25, 28, 59, 19, 53, 47, 10, 36,  0, 55, 44, 30, 16,
       38, 57, 50, 52,  7, 14, 39, 22,  8]), 'cur_cost': 94290.0}, {'tour': [4, 53, 30, 46, 19, 22, 17, 49, 31, 28, 5, 23, 8, 15, 37, 34, 10, 58, 25, 40, 51, 27, 57, 38, 14, 20, 32, 50, 13, 44, 1, 39, 12, 3, 47, 36, 21, 6, 11, 9, 16, 45, 29, 2, 41, 55, 0, 33, 56, 24, 7, 54, 48, 35, 59, 43, 42, 26, 52, 18], 'cur_cost': 107289.0}, {'tour': array([ 4, 49, 42,  3, 28, 33, 27, 43, 36,  2, 10, 45, 25, 51,  1, 12, 56,
       20, 31, 35, 53, 39,  9, 58, 50,  7, 18, 19, 41, 40, 26, 37, 14, 22,
       16, 54, 17, 52, 13, 59, 21,  5, 15, 48, 32,  8, 34,  6, 57, 38, 44,
       24, 29, 23,  0, 47, 30, 46, 55, 11]), 'cur_cost': 83670.0}, {'tour': array([ 7, 59, 57,  0, 16, 50, 56, 52, 39, 42, 32, 58, 54, 14, 23,  8, 44,
        9, 21, 10, 36, 29, 53, 31, 43, 51, 22, 40,  6, 47,  5,  2,  1, 19,
       45, 48,  3, 35, 34, 25, 13, 17, 38, 18, 15, 26, 11,  4, 30, 37, 24,
       55, 33, 28, 41, 12, 49, 20, 27, 46]), 'cur_cost': 92004.0}, {'tour': array([11, 24, 34, 59, 37, 57, 16, 51,  4, 48, 30,  7, 18, 32, 44, 10, 47,
        1,  3, 26, 40, 14, 15,  0, 42,  5, 36, 27, 22, 28, 43, 17, 23, 39,
       25, 20, 35, 56, 38, 21, 12, 13, 54, 58,  9, 41, 49,  6, 45, 46, 29,
        2,  8, 52, 50, 19, 31, 55, 33, 53]), 'cur_cost': 99171.0}]
2025-06-25 10:44:31,961 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-25 10:44:31,961 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-25 10:44:31,961 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-25 10:44:31,962 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-25 10:44:31,962 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:44:31,963 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 107289.0 (rank 90.0%)
- Population diversity: 0.8542372881355932
- Evolution phase: Exploration/Diversification. The algorithm is still in the early stages. The high diversity, low convergence, and stagnation in recent best costs suggest that the population is exploring the search space but has not yet settled into a promising region.
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Given the lack ...
- Difficult regions to avoid (sample): Low-density reg, Corridors forme...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:44:31,963 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:44:37,721 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-25 10:44:37,721 - ExplorationExpert - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-25 10:44:37,721 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-25 10:44:37,721 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:44:37,724 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 83670.0 (rank 10.0%)
- Population diversity: 0.8542372881355932
- Evolution phase: Exploration/Diversification. The algorithm is still in the early stages. The high diversity, low convergence, and stagnation in recent best costs suggest that the population is exploring the search space but has not yet settled into a promising region.
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Given the lack ...
- Difficult regions to avoid (sample): Low-density reg, Corridors forme...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:44:37,724 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:44:42,413 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-25 10:44:42,415 - ExplorationExpert - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-25 10:44:42,415 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-25 10:44:42,415 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-25 10:44:42,415 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-25 10:44:42,417 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 88269.0
2025-06-25 10:44:42,919 - ExploitationExpert - INFO - res_population_num: 43
2025-06-25 10:44:42,919 - ExploitationExpert - INFO - res_population_costs: [9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9626, 9630, 9630, 9630, 9644, 9644, 9644, 87470, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614, 9614]
2025-06-25 10:44:42,919 - ExploitationExpert - INFO - res_populations: [array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 43, 45, 38, 42, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  2,  7,  8, 26, 32, 31, 25, 35, 28, 27, 24, 33, 29,
       30, 34, 36, 45, 38, 42, 43, 47, 37, 44, 40, 46, 39, 41, 51, 59, 48,
       53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       13, 21, 14, 18,  3,  9,  5,  6,  4], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 18, 14, 21, 13, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 42, 38, 45, 43, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 26, 34, 30, 29, 33, 24, 27, 28,
       35, 25, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  4,  6,  5,  9,  3, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 41, 39, 46, 40,
       44, 37, 47, 43, 42, 38, 45, 36, 34, 30, 29, 33, 24, 27, 28, 35, 25,
       31, 32, 26,  8,  7,  2, 11, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 50, 52, 59, 48, 53, 55, 57,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1,  6,  5,  9,  3,  2,  7, 11, 10,  8, 26, 34, 30, 29, 33, 24,
       27, 28, 35, 25, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23,
       12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51, 59, 52,
       50, 58, 49, 56, 55, 57, 53, 48, 54, 12, 23, 16, 20, 22, 17, 15, 19,
       14, 18, 21, 13, 31, 32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34,  8,
        7,  2, 11,  3,  9,  5,  6, 10,  1], dtype=int64), array([ 0,  1, 10,  6,  5,  9,  3, 11,  2,  7,  8, 34, 30, 29, 33, 24, 27,
       28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23,
       12, 54, 48, 53, 57, 55, 56, 49, 58, 50, 52, 59, 51, 39, 46, 40, 44,
       37, 47, 42, 38, 45, 36, 43, 41,  4], dtype=int64), array([ 0, 55, 46, 29,  1, 21, 58, 51, 36, 24, 56, 23, 16, 15, 49, 45, 31,
        2, 52, 38, 27,  7,  9,  3, 28, 34, 44, 11, 35, 14, 18, 19, 20, 48,
        8, 47, 39, 50, 32, 13, 57, 22, 30, 59, 40, 41, 54,  5, 10, 37, 53,
       33, 25, 43, 17, 26, 42,  4, 12,  6], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 43, 45, 38, 42, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 25,
       35, 26, 32, 31, 13, 21, 18, 14, 19, 17, 15, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 42, 38, 45, 43, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 32, 26, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7,  2, 11,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 43, 36, 45, 38, 42, 47, 37, 44, 40, 46, 39, 51,
       59, 48, 53, 55, 57, 52, 50, 58, 49, 56, 54, 12, 23, 16, 20, 22, 15,
       17, 19, 14, 18, 21, 13, 31, 26, 32, 25, 35, 28, 27, 24, 33, 29, 30,
       34,  8,  7,  2,  3,  9, 11, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3,  2, 11,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 35, 25, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 25, 35, 26, 32, 31, 13, 21, 18, 14, 19, 17,
       15, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 15, 17, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       32, 26, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  6,  5,  4, 41, 39, 46, 40, 44, 37, 47, 43, 42, 38, 45, 36, 34,
       30, 29, 33, 24, 27, 28, 35, 25, 26, 32, 31, 13, 21, 18, 14, 19, 15,
       17, 22, 20, 16, 23, 12, 54, 56, 49, 58, 50, 52, 57, 55, 53, 48, 59,
       51,  9,  3, 11,  2,  7,  8, 10,  1], dtype=int64), array([ 0,  1, 10,  8,  7, 11,  2,  3,  9, 51, 59, 48, 53, 55, 57, 52, 50,
       58, 49, 56, 54, 12, 23, 16, 20, 22, 17, 15, 19, 14, 18, 21, 13, 31,
       26, 32, 25, 35, 28, 27, 24, 33, 29, 30, 34, 36, 45, 38, 42, 43, 47,
       37, 44, 40, 46, 39, 41,  4,  5,  6], dtype=int64), array([ 0,  1, 10, 11,  9,  3,  2,  7,  8, 34, 30, 29, 33, 24, 27, 28, 35,
       25, 26, 32, 31, 13, 21, 18, 14, 19, 15, 17, 22, 20, 16, 23, 12, 54,
       56, 49, 58, 50, 52, 57, 55, 53, 48, 59, 51, 39, 46, 40, 44, 37, 47,
       42, 38, 45, 36, 43, 41,  4,  5,  6], dtype=int64)]
2025-06-25 10:44:42,935 - ExploitationExpert - INFO - populations_num: 10
2025-06-25 10:44:42,935 - ExploitationExpert - INFO - populations: [{'tour': [52, 41, 59, 10, 30, 56, 47, 16, 1, 39, 33, 31, 28, 20, 21, 4, 48, 24, 29, 13, 51, 27, 55, 17, 9, 54, 12, 44, 32, 34, 37, 6, 46, 26, 0, 42, 36, 23, 50, 49, 25, 40, 53, 3, 18, 35, 22, 19, 8, 14, 45, 58, 15, 2, 11, 38, 57, 7, 43, 5], 'cur_cost': 100280.0}, {'tour': [58, 3, 29, 12, 50, 40, 48, 2, 43, 44, 26, 37, 5, 54, 18, 56, 38, 45, 35, 17, 14, 57, 51, 23, 24, 9, 22, 13, 46, 39, 19, 16, 36, 41, 15, 47, 59, 20, 49, 32, 0, 28, 31, 30, 25, 8, 4, 53, 6, 1, 27, 55, 7, 42, 10, 33, 52, 11, 34, 21], 'cur_cost': 86340.0}, {'tour': array([45,  4, 26, 34, 23, 22, 14, 58, 32, 20, 17, 44, 21, 30, 29, 48, 59,
       31, 55, 53, 49, 16, 54,  6, 10, 15, 42, 36, 24, 33, 11, 27, 46,  7,
       43, 37, 35, 19, 13, 47, 12,  3, 40, 56, 25, 57, 51, 18, 52,  0,  1,
        8, 38, 39, 50,  5,  9, 28,  2, 41]), 'cur_cost': 81800.0}, {'tour': [51, 21, 40, 26, 32, 43, 11, 23, 55, 52, 3, 24, 56, 10, 47, 4, 14, 19, 28, 42, 5, 57, 36, 38, 39, 31, 46, 16, 0, 27, 50, 9, 58, 12, 45, 34, 6, 49, 13, 18, 41, 17, 2, 33, 7, 22, 35, 20, 48, 1, 25, 8, 59, 15, 29, 37, 44, 30, 53, 54], 'cur_cost': 96038.0}, {'tour': [49, 15, 40, 56, 28, 13, 46, 52, 41, 9, 38, 37, 2, 10, 5, 33, 11, 1, 58, 20, 47, 53, 31, 18, 30, 21, 54, 24, 48, 16, 3, 42, 14, 23, 59, 26, 27, 8, 55, 39, 22, 19, 4, 29, 43, 34, 51, 6, 12, 17, 36, 35, 0, 50, 7, 45, 25, 32, 57, 44], 'cur_cost': 103704.0}, {'tour': array([26, 34,  3, 43,  1, 17, 31, 51, 18, 23, 46, 27, 49, 13, 12, 32, 24,
       37, 11, 48, 56, 15, 35,  2, 58, 54, 29, 21,  4, 45, 41, 20,  9, 33,
        5, 42, 40,  6, 25, 28, 59, 19, 53, 47, 10, 36,  0, 55, 44, 30, 16,
       38, 57, 50, 52,  7, 14, 39, 22,  8]), 'cur_cost': 94290.0}, {'tour': [4, 53, 30, 46, 19, 22, 17, 49, 31, 28, 5, 23, 8, 15, 37, 34, 10, 58, 25, 40, 51, 27, 57, 38, 14, 20, 32, 50, 13, 44, 1, 39, 12, 3, 47, 36, 21, 6, 11, 9, 16, 45, 29, 2, 41, 55, 0, 33, 56, 24, 7, 54, 48, 35, 59, 43, 42, 26, 52, 18], 'cur_cost': 107289.0}, {'tour': array([ 4, 49, 42,  3, 28, 33, 27, 43, 36,  2, 10, 45, 25, 51,  1, 12, 56,
       20, 31, 35, 53, 39,  9, 58, 50,  7, 18, 19, 41, 40, 26, 37, 14, 22,
       16, 54, 17, 52, 13, 59, 21,  5, 15, 48, 32,  8, 34,  6, 57, 38, 44,
       24, 29, 23,  0, 47, 30, 46, 55, 11]), 'cur_cost': 83670.0}, {'tour': array([55, 19,  3, 27, 47, 23, 24,  2, 48, 49, 14, 35, 50, 15, 33,  8, 21,
       17, 36, 56, 13, 37,  7,  5, 32, 12, 28, 39, 41, 29, 10, 30, 34,  9,
        6, 46, 51, 40, 45, 43, 16, 52, 26, 20, 11, 57, 58,  4, 38, 42, 44,
       31,  1, 22, 18, 53, 54, 59,  0, 25]), 'cur_cost': 88269.0}, {'tour': array([11, 24, 34, 59, 37, 57, 16, 51,  4, 48, 30,  7, 18, 32, 44, 10, 47,
        1,  3, 26, 40, 14, 15,  0, 42,  5, 36, 27, 22, 28, 43, 17, 23, 39,
       25, 20, 35, 56, 38, 21, 12, 13, 54, 58,  9, 41, 49,  6, 45, 46, 29,
        2,  8, 52, 50, 19, 31, 55, 33, 53]), 'cur_cost': 99171.0}]
2025-06-25 10:44:42,937 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-25 10:44:42,939 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-06-25 10:44:42,939 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-25 10:44:42,939 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-25 10:44:42,939 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-25 10:44:42,939 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 99171.0 (rank 60.0%)
- Population diversity: 0.8610169491525423
- Evolution phase: Exploration/Diversification. The algorithm is still in the early stages. The high diversity, low convergence, and stagnation in recent best costs suggest that the population is exploring the search space but has not yet settled into a promising region.
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Given the lack ...
- Difficult regions to avoid (sample): Low-density reg, Corridors forme...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 59]  
• Path length == 60 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-25 10:44:42,941 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-25 10:44:49,164 - ExplorationExpert - INFO - LLM生成的探索路径: None
2025-06-25 10:44:49,164 - ExplorationExpert - ERROR - 提取探索路径时出错: expected string or bytes-like object, got 'NoneType'
2025-06-25 10:44:49,164 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [58, 3, 29, 12, 50, 40, 48, 2, 43, 44, 26, 37, 5, 54, 18, 56, 38, 45, 35, 17, 14, 57, 51, 23, 24, 9, 22, 13, 46, 39, 19, 16, 36, 41, 15, 47, 59, 20, 49, 32, 0, 28, 31, 30, 25, 8, 4, 53, 6, 1, 27, 55, 7, 42, 10, 33, 52, 11, 34, 21], 'cur_cost': 86340.0}}]
2025-06-25 10:44:49,164 - __main__ - INFO - 进化阶段完成
2025-06-25 10:44:49,164 - __main__ - INFO - 开始评估阶段
2025-06-25 10:44:49,164 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-25 10:44:49,164 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-25 10:44:49,164 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 83670.0 → 81800.0 (Δ 1870.0)
- Mean cost: 99868.1 → 94085.1
- Diversity: 0.849 → 0.861 (Δ 0.0119)

#### Elite metrics
- Elite count: 29 → 43
- Best elite cost: 9614 → 9614 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 14.3% , avg Δ 3018.0
- Exploit: 3 inds, success 100.0% , avg Δ 12234.67

#### Other indicators
- No-change individuals: 6
- Regression individuals: 0
- Historical trends: Last 2 iterations: costs [13173.0, 83670.0], diversity [0.9625925925925926, 0.9622222222222222]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-25 10:44:55,590 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: None
2025-06-25 10:44:55,614 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-25 10:44:55,615 - __main__ - INFO - 评估阶段完整报告: None
2025-06-25 10:44:55,615 - __main__ - INFO - 评估阶段完成
2025-06-25 10:44:55,615 - __main__ - INFO - 评估完整报告: None
2025-06-25 10:44:55,616 - __main__ - INFO - 当前最佳适应度: 81800.0
2025-06-25 10:44:55,625 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite12_60_route_2.pkl
2025-06-25 10:44:55,626 - __main__ - INFO - composite12_60 开始进化第 4 代
2025-06-25 10:44:55,627 - __main__ - INFO - 开始分析阶段
2025-06-25 10:44:55,627 - StatsExpert - INFO - 开始统计分析
2025-06-25 10:44:55,641 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 81800.0, 'max': 107289.0, 'mean': 94085.1, 'std': 8295.64200589683}, 'diversity': 0.965925925925926, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-25 10:44:55,642 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 81800.0, 'max': 107289.0, 'mean': 94085.1, 'std': 8295.64200589683}, 'diversity_level': 0.965925925925926, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[1380, 1590], [1386, 1610], [1446, 1664], [1473, 1651], [1403, 1573], [1429, 1576], [1414, 1596], [1421, 1664], [1385, 1662], [1475, 1616], [1405, 1625], [1439, 1639], [3242, 2428], [3193, 2490], [3212, 2461], [3238, 2498], [3270, 2452], [3236, 2513], [3196, 2449], [3220, 2481], [3262, 2465], [3198, 2468], [3267, 2481], [3264, 2442], [426, 2488], [478, 2496], [499, 2467], [445, 2489], [451, 2481], [441, 2477], [461, 2463], [511, 2487], [499, 2478], [431, 2471], [461, 2456], [476, 2486], [832, 400], [885, 348], [838, 364], [921, 379], [900, 346], [918, 411], [855, 361], [866, 388], [878, 327], [841, 380], [902, 374], [871, 361], [2763, 1531], [2817, 1507], [2789, 1453], [2739, 1464], [2773, 1490], [2783, 1514], [2808, 1535], [2797, 1510], [2807, 1517], [2793, 1495], [2828, 1490], [2736, 1497]], 'distance_matrix': array([[   0.,   21.,   99., ..., 1416., 1451., 1359.],
       [  21.,    0.,   81., ..., 1412., 1447., 1355.],
       [  99.,   81.,    0., ..., 1358., 1393., 1301.],
       ...,
       [1416., 1412., 1358., ...,    0.,   35.,   57.],
       [1451., 1447., 1393., ...,   35.,    0.,   92.],
       [1359., 1355., 1301., ...,   57.,   92.,    0.]])}
2025-06-25 10:44:55,642 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-25 10:44:55,642 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-25 10:44:55,642 - PathExpert - INFO - 开始路径结构分析
2025-06-25 10:44:55,643 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-25 10:44:55,643 - PathExpert - INFO - 路径结构分析完成
2025-06-25 10:44:55,643 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-25 10:44:55,643 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-25 10:44:55,646 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-25 10:44:55,646 - EliteExpert - INFO - 开始精英解分析
2025-06-25 10:44:55,672 - EliteExpert - INFO - 精英解分析完成
2025-06-25 10:44:55,672 - __main__ - INFO - 精英专家分析报告: {'elite_count': 43, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 72186.0, 'avg_gap': 82657.00697674419}, 'structure_gap': {'unique_elite_edges': 197, 'unique_pop_edges': 507, 'common_edges': 44}}, 'elite_diversity': {'diversity_score': 0.19492432631967516}}
2025-06-25 10:44:55,672 - __main__ - INFO - 更新共享数据: elite_features
2025-06-25 10:44:55,673 - LandscapeExpert - INFO - 开始景观分析
2025-06-25 10:44:55,675 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-25 10:44:55,675 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 81800.0, mean 94085.1, max 107289.0, std 8295.64200589683
- diversity: 0.965925925925926
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [13173.0, 83670.0, 81800.0] (Δ 935.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [426.0, 327.0, 3270.0, 2513.0]
- centroid/spread: [1756.217, 1687.917] | rms 1327.809
- density_grid(3x3): [[12, 0, 0], [0, 12, 12], [12, 0, 12]]
- edge_len_stats(min,q1,med,q3,max): [7.0, 1094.25, 1427.5, 2273.75, 3222.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [6, 7, 6, 9, 12, 8, 4, 7]
- nn_median_dist: 17.242

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-25 10:45:00,572 - LandscapeExpert - INFO - LLM返回的分析结果: None
