2025-07-03 17:04:43,075 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-03 17:04:43,075 - __main__ - INFO - 开始分析阶段
2025-07-03 17:04:43,075 - StatsExpert - INFO - 开始统计分析
2025-07-03 17:04:43,095 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9958.0, 'max': 116650.0, 'mean': 79125.4, 'std': 45494.07996036408}, 'diversity': 0.9289562289562289, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 17:04:43,096 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9958.0, 'max': 116650.0, 'mean': 79125.4, 'std': 45494.07996036408}, 'diversity_level': 0.9289562289562289, 'convergence_level': 0.0, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1], 'summary': '8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1], 'summary': '8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 17:04:43,097 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 17:04:43,097 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 17:04:43,098 - PathExpert - INFO - 开始路径结构分析
2025-07-03 17:04:43,104 - PathExpert - INFO - 路径结构分析完成
2025-07-03 17:04:43,104 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (40, 49), 'frequency': 0.5, 'avg_cost': 10.0}], 'common_subpaths': [{'subpath': (8, 2, 6), 'frequency': 0.3}, {'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}, {'subpath': (53, 62, 59), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}, {'subpath': (59, 56, 58), 'frequency': 0.3}, {'subpath': (56, 58, 60), 'frequency': 0.3}, {'subpath': (58, 60, 64), 'frequency': 0.3}, {'subpath': (60, 64, 57), 'frequency': 0.3}, {'subpath': (64, 57, 54), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(2, 6)', 'frequency': 0.4}, {'edge': '(52, 65)', 'frequency': 0.4}, {'edge': '(12, 22)', 'frequency': 0.4}, {'edge': '(25, 26)', 'frequency': 0.4}, {'edge': '(28, 30)', 'frequency': 0.4}, {'edge': '(40, 49)', 'frequency': 0.5}], 'low_frequency_edges': [{'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 3)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(8, 10)', 'frequency': 0.2}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(5, 55)', 'frequency': 0.2}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(14, 15)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.3}, {'edge': '(13, 20)', 'frequency': 0.2}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(19, 27)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.2}, {'edge': '(26, 36)', 'frequency': 0.2}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.2}, {'edge': '(30, 34)', 'frequency': 0.2}, {'edge': '(33, 34)', 'frequency': 0.2}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.2}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(32, 40)', 'frequency': 0.2}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(46, 48)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(39, 44)', 'frequency': 0.2}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.2}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.2}, {'edge': '(41, 50)', 'frequency': 0.2}, {'edge': '(34, 35)', 'frequency': 0.2}, {'edge': '(18, 32)', 'frequency': 0.2}, {'edge': '(13, 19)', 'frequency': 0.2}, {'edge': '(9, 19)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.2}, {'edge': '(45, 51)', 'frequency': 0.2}, {'edge': '(41, 44)', 'frequency': 0.2}, {'edge': '(39, 41)', 'frequency': 0.2}, {'edge': '(17, 19)', 'frequency': 0.2}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(5, 9)', 'frequency': 0.2}, {'edge': '(14, 19)', 'frequency': 0.2}, {'edge': '(9, 49)', 'frequency': 0.2}, {'edge': '(24, 56)', 'frequency': 0.2}, {'edge': '(55, 58)', 'frequency': 0.2}, {'edge': '(5, 37)', 'frequency': 0.2}, {'edge': '(5, 53)', 'frequency': 0.3}, {'edge': '(7, 34)', 'frequency': 0.2}, {'edge': '(18, 60)', 'frequency': 0.2}, {'edge': '(22, 63)', 'frequency': 0.2}, {'edge': '(3, 22)', 'frequency': 0.2}, {'edge': '(15, 23)', 'frequency': 0.2}, {'edge': '(1, 31)', 'frequency': 0.3}, {'edge': '(1, 29)', 'frequency': 0.2}, {'edge': '(20, 53)', 'frequency': 0.2}, {'edge': '(0, 46)', 'frequency': 0.2}, {'edge': '(5, 31)', 'frequency': 0.2}, {'edge': '(18, 47)', 'frequency': 0.2}, {'edge': '(21, 45)', 'frequency': 0.2}, {'edge': '(21, 47)', 'frequency': 0.2}, {'edge': '(2, 18)', 'frequency': 0.2}, {'edge': '(24, 43)', 'frequency': 0.2}, {'edge': '(32, 34)', 'frequency': 0.2}, {'edge': '(25, 58)', 'frequency': 0.2}, {'edge': '(14, 28)', 'frequency': 0.2}, {'edge': '(30, 65)', 'frequency': 0.2}, {'edge': '(19, 57)', 'frequency': 0.2}, {'edge': '(17, 42)', 'frequency': 0.2}, {'edge': '(21, 38)', 'frequency': 0.2}, {'edge': '(8, 64)', 'frequency': 0.2}, {'edge': '(7, 37)', 'frequency': 0.2}, {'edge': '(45, 64)', 'frequency': 0.2}, {'edge': '(20, 26)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [2, 41, 6, 50, 61, 33, 65], 'cost': 16238.0, 'size': 7}, {'region': [57, 33, 44, 60, 36], 'cost': 10865.0, 'size': 5}, {'region': [28, 54, 30, 65], 'cost': 9048.0, 'size': 4}, {'region': [65, 37, 63, 33], 'cost': 8818.0, 'size': 4}, {'region': [49, 57, 35, 58], 'cost': 8751.0, 'size': 4}]}
2025-07-03 17:04:43,105 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 17:04:43,105 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 17:04:43,107 - EliteExpert - INFO - 开始精英解分析
2025-07-03 17:04:43,107 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-03 17:04:43,107 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-07-03 17:04:43,107 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 17:04:43,107 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 17:04:43,727 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 17:04:43,728 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9958.0, mean 79125.4, max 116650.0, std 45494.07996036408
- diversity: 0.9289562289562289
- convergence: 0.0
- clustering: 8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (40, 49), 'frequency': 0.5, 'avg_cost': 10.0}]
- common_subpaths_sample: [{'subpath': (8, 2, 6), 'frequency': 0.3}, {'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [2, 41, 6, 50, 61, 33, 65], 'cost': 16238.0, 'size': 7}, {'region': [57, 33, 44, 60, 36], 'cost': 10865.0, 'size': 5}, {'region': [28, 54, 30, 65], 'cost': 9048.0, 'size': 4}]
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 17:04:43,729 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:05:02,857 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "moderate"
  },
  "population_state": {
    "diversity": 0.9289562289562289,
    "convergence": 0.0,
    "clustering": "strong"
  },
  "difficult_regions": [
    {"region": [2, 41, 6, 50, 61, 33, 65], "cost": 16238.0, "size": 7},
    {"region": [57, 33, 44, 60, 36], "cost": 10865.0, "size": 5},
    {"region": [28, 54, 30, 65], "cost": 9048.0, "size": 4}
  ],
  "opportunity_regions": [
    {"nodes": [40, 49], "frequency": 0.5, "avg_cost": 10.0},
    {"subpath": [8, 2, 6], "frequency": 0.3},
    {"subpath": [55, 61, 53], "frequency": 0.3},
    {"subpath": [61, 53, 62], "frequency": 0.3}
  ],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["edge recombination", "local search around high-quality edges", "diversity preservation"]
  }
}
```
2025-07-03 17:05:02,857 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 17:05:02,857 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.9289562289562289, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'region': [2, 41, 6, 50, 61, 33, 65], 'cost': 16238.0, 'size': 7}, {'region': [57, 33, 44, 60, 36], 'cost': 10865.0, 'size': 5}, {'region': [28, 54, 30, 65], 'cost': 9048.0, 'size': 4}], 'opportunity_regions': [{'nodes': [40, 49], 'frequency': 0.5, 'avg_cost': 10.0}, {'subpath': [8, 2, 6], 'frequency': 0.3}, {'subpath': [55, 61, 53], 'frequency': 0.3}, {'subpath': [61, 53, 62], 'frequency': 0.3}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['edge recombination', 'local search around high-quality edges', 'diversity preservation']}}
2025-07-03 17:05:02,857 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 17:05:02,858 - __main__ - INFO - 分析阶段完成
2025-07-03 17:05:02,858 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.9289562289562289, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'region': [2, 41, 6, 50, 61, 33, 65], 'cost': 16238.0, 'size': 7}, {'region': [57, 33, 44, 60, 36], 'cost': 10865.0, 'size': 5}, {'region': [28, 54, 30, 65], 'cost': 9048.0, 'size': 4}], 'opportunity_regions': [{'nodes': [40, 49], 'frequency': 0.5, 'avg_cost': 10.0}, {'subpath': [8, 2, 6], 'frequency': 0.3}, {'subpath': [55, 61, 53], 'frequency': 0.3}, {'subpath': [61, 53, 62], 'frequency': 0.3}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['edge recombination', 'local search around high-quality edges', 'diversity preservation']}}
2025-07-03 17:05:02,858 - __main__ - INFO - 开始策略分配阶段
2025-07-03 17:05:02,858 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 17:05:02,860 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.929
  • best_cost: 9958.0
  • mean_cost: 79125.4
- Search-space: ruggedness high, modality multi, deceptiveness moderate
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 3): {'region': [2, 41, 6, {'region': [57, 33, ...
- Opportunity regions (# 4): {'nodes': [40, 49], , {'subpath': [8, 2, 6...
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 17:05:02,860 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 17:05:02,861 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:05:12,240 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "High diversity and explore focus; 80% explore to discover new regions."
}
```
2025-07-03 17:05:12,240 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-07-03 17:05:12,240 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-07-03 17:05:12,240 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "High diversity and explore focus; 80% explore to discover new regions."
}
```
2025-07-03 17:05:12,241 - __main__ - INFO - 策略分配阶段完成
2025-07-03 17:05:12,241 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'explore']
2025-07-03 17:05:12,241 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "rationale": "High diversity and explore focus; 80% explore to discover new regions."
}
```
2025-07-03 17:05:12,242 - __main__ - INFO - 开始进化阶段
2025-07-03 17:05:12,242 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 17:05:12,242 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:05:12,243 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10248.0 (rank 20.0%)
- Population diversity: 0.7661538461538462
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'nodes': [40, , {'subpath': [8,...
- Difficult regions to avoid (sample): {'region': [2, , {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"nodes": [40, 49], "frequency": 0.5, "avg_cost": 10.0}, {"subpath": [8, 2, 6], "frequency": 0.3}, {"subpath": [55, 61, 53], "frequency": 0.3}, {"subpath": [61, 53, 62], "frequency": 0.3}]
- Difficult regions (full): [{"region": [2, 41, 6, 50, 61, 33, 65], "cost": 16238.0, "size": 7}, {"region": [57, 33, 44, 60, 36], "cost": 10865.0, "size": 5}, {"region": [28, 54, 30, 65], "cost": 9048.0, "size": 4}]

2025-07-03 17:05:12,243 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:05:12,243 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:05:26,742 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
  "targeted_regions": "nodes(40,49), subpath(8,2,6)",
  "strategy_comment": "Focuses on opportunity regions while avoiding high-cost clusters."
}
```
2025-07-03 17:05:26,743 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:05:26,756 - ExplorationKnowledgeBase - INFO - 已保存1条知识条目
2025-07-03 17:05:26,756 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10248.0, 路径: [9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42]
2025-07-03 17:05:26,758 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10248.0}
2025-07-03 17:05:26,758 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 17:05:26,758 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:05:26,759 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9958.0 (rank 0.0%)
- Population diversity: 0.7661538461538462
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'nodes': [40, , {'subpath': [8,...
- Difficult regions to avoid (sample): {'region': [2, , {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 24, 29, 32, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"nodes": [40, 49], "frequency": 0.5, "avg_cost": 10.0}, {"subpath": [8, 2, 6], "frequency": 0.3}, {"subpath": [55, 61, 53], "frequency": 0.3}, {"subpath": [61, 53, 62], "frequency": 0.3}]
- Difficult regions (full): [{"region": [2, 41, 6, 50, 61, 33, 65], "cost": 16238.0, "size": 7}, {"region": [57, 33, 44, 60, 36], "cost": 10865.0, "size": 5}, {"region": [28, 54, 30, 65], "cost": 9048.0, "size": 4}]

2025-07-03 17:05:26,759 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:05:26,759 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:05:41,302 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 24, 29, 32, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42],
  "targeted_regions": "nodes [40,49], subpath [8,2,6]",
  "strategy_comment": "Leverages opportunity regions, avoids high-cost clusters"
}
```
2025-07-03 17:05:41,303 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:05:41,320 - ExplorationKnowledgeBase - INFO - 已保存2条知识条目
2025-07-03 17:05:41,321 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9958.0, 路径: [26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 24, 29, 32, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42]
2025-07-03 17:05:41,322 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 24, 29, 32, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 9958.0}
2025-07-03 17:05:41,322 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 17:05:41,322 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:05:41,323 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9961.0 (rank 10.0%)
- Population diversity: 0.7661538461538462
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'nodes': [40, , {'subpath': [8,...
- Difficult regions to avoid (sample): {'region': [2, , {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"nodes": [40, 49], "frequency": 0.5, "avg_cost": 10.0}, {"subpath": [8, 2, 6], "frequency": 0.3}, {"subpath": [55, 61, 53], "frequency": 0.3}, {"subpath": [61, 53, 62], "frequency": 0.3}]
- Difficult regions (full): [{"region": [2, 41, 6, 50, 61, 33, 65], "cost": 16238.0, "size": 7}, {"region": [57, 33, 44, 60, 36], "cost": 10865.0, "size": 5}, {"region": [28, 54, 30, 65], "cost": 9048.0, "size": 4}]

2025-07-03 17:05:41,323 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:05:41,323 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:05:55,931 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
  "targeted_regions": "nodes [40, 49], subpath [55, 61, 53]",
  "strategy_comment": "Focus on opportunity regions, avoid high-cost clusters"
}
```
2025-07-03 17:05:55,931 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:05:55,948 - ExplorationKnowledgeBase - INFO - 已保存3条知识条目
2025-07-03 17:05:55,949 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9961.0, 路径: [42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63]
2025-07-03 17:05:55,949 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63], 'cur_cost': 9961.0}
2025-07-03 17:05:55,950 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 17:05:55,950 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:05:55,950 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 113276.0 (rank 70.0%)
- Population diversity: 0.7661538461538462
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'nodes': [40, , {'subpath': [8,...
- Difficult regions to avoid (sample): {'region': [2, , {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [1, 11, 25, 62, 42, 20, 4, 54, 8, 32, 40, 51, 61, 36, 46, 33, 30, 14, 19, 9, 49, 10, 27, 13, 64, 16, 28, 45, 12, 24, 56, 26, 47, 6, 48, 58, 55, 37, 5, 53, 39, 7, 34, 29, 2, 38, 57, 21, 17, 50, 52, 44, 41, 43, 65, 18, 60, 63, 22, 3, 0, 35, 59, 15, 23, 31]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"nodes": [40, 49], "frequency": 0.5, "avg_cost": 10.0}, {"subpath": [8, 2, 6], "frequency": 0.3}, {"subpath": [55, 61, 53], "frequency": 0.3}, {"subpath": [61, 53, 62], "frequency": 0.3}]
- Difficult regions (full): [{"region": [2, 41, 6, 50, 61, 33, 65], "cost": 16238.0, "size": 7}, {"region": [57, 33, 44, 60, 36], "cost": 10865.0, "size": 5}, {"region": [28, 54, 30, 65], "cost": 9048.0, "size": 4}]

2025-07-03 17:05:55,951 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:05:55,951 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:06:10,234 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [1, 11, 25, 62, 42, 20, 4, 54, 8, 32, 40, 49, 10, 27, 13, 64, 16, 28, 45, 12, 24, 56, 26, 47, 6, 48, 58, 55, 61, 53, 39, 7, 34, 29, 2, 38, 57, 21, 17, 50, 52, 44, 41, 43, 65, 18, 60, 63, 22, 3, 0, 35, 59, 15, 23, 31, 5, 37, 36, 46, 33, 30, 14, 19, 9, 51],
  "targeted_regions": "nodes(40,49), subpath(55,61,53)",
  "strategy_comment": "Prioritizes opportunity regions while avoiding high-cost clusters."
}
```
2025-07-03 17:06:10,235 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:06:10,253 - ExplorationKnowledgeBase - INFO - 已保存4条知识条目
2025-07-03 17:06:10,254 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106245.0, 路径: [1, 11, 25, 62, 42, 20, 4, 54, 8, 32, 40, 49, 10, 27, 13, 64, 16, 28, 45, 12, 24, 56, 26, 47, 6, 48, 58, 55, 61, 53, 39, 7, 34, 29, 2, 38, 57, 21, 17, 50, 52, 44, 41, 43, 65, 18, 60, 63, 22, 3, 0, 35, 59, 15, 23, 31, 5, 37, 36, 46, 33, 30, 14, 19, 9, 51]
2025-07-03 17:06:10,254 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [1, 11, 25, 62, 42, 20, 4, 54, 8, 32, 40, 49, 10, 27, 13, 64, 16, 28, 45, 12, 24, 56, 26, 47, 6, 48, 58, 55, 61, 53, 39, 7, 34, 29, 2, 38, 57, 21, 17, 50, 52, 44, 41, 43, 65, 18, 60, 63, 22, 3, 0, 35, 59, 15, 23, 31, 5, 37, 36, 46, 33, 30, 14, 19, 9, 51], 'cur_cost': 106245.0}
2025-07-03 17:06:10,254 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 17:06:10,255 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:06:10,255 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 113743.0 (rank 80.0%)
- Population diversity: 0.7615384615384615
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'nodes': [40, , {'subpath': [8,...
- Difficult regions to avoid (sample): {'region': [2, , {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [6, 58, 42, 12, 2, 48, 59, 7, 13, 19, 32, 60, 38, 29, 1, 49, 56, 27, 39, 14, 35, 10, 11, 64, 25, 16, 22, 65, 37, 63, 33, 40, 57, 3, 54, 45, 53, 20, 9, 21, 4, 0, 46, 55, 52, 41, 24, 34, 61, 44, 50, 28, 30, 17, 8, 15, 23, 5, 31, 26, 62, 36, 47, 18, 51, 43]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"nodes": [40, 49], "frequency": 0.5, "avg_cost": 10.0}, {"subpath": [8, 2, 6], "frequency": 0.3}, {"subpath": [55, 61, 53], "frequency": 0.3}, {"subpath": [61, 53, 62], "frequency": 0.3}]
- Difficult regions (full): [{"region": [2, 41, 6, 50, 61, 33, 65], "cost": 16238.0, "size": 7}, {"region": [57, 33, 44, 60, 36], "cost": 10865.0, "size": 5}, {"region": [28, 54, 30, 65], "cost": 9048.0, "size": 4}]

2025-07-03 17:06:10,256 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:06:10,256 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:06:26,463 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [6, 58, 42, 12, 48, 59, 7, 13, 19, 32, 38, 29, 1, 49, 40, 56, 27, 39, 14, 35, 10, 11, 64, 25, 16, 22, 65, 37, 63, 33, 57, 3, 54, 45, 53, 20, 9, 21, 4, 0, 46, 55, 52, 41, 24, 34, 61, 44, 50, 28, 30, 17, 8, 2, 15, 23, 5, 31, 26, 62, 36, 47, 18, 51, 43, 60],
  "targeted_regions": "nodes [40,49], subpath [8,2,6]",
  "strategy_comment": "Prioritizes opportunity regions, avoids high-cost clusters"
}
```
2025-07-03 17:06:26,464 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:06:26,473 - ExplorationKnowledgeBase - INFO - 已保存5条知识条目
2025-07-03 17:06:26,475 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107295.0, 路径: [6, 58, 42, 12, 48, 59, 7, 13, 19, 32, 38, 29, 1, 49, 40, 56, 27, 39, 14, 35, 10, 11, 64, 25, 16, 22, 65, 37, 63, 33, 57, 3, 54, 45, 53, 20, 9, 21, 4, 0, 46, 55, 52, 41, 24, 34, 61, 44, 50, 28, 30, 17, 8, 2, 15, 23, 5, 31, 26, 62, 36, 47, 18, 51, 43, 60]
2025-07-03 17:06:26,475 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [6, 58, 42, 12, 48, 59, 7, 13, 19, 32, 38, 29, 1, 49, 40, 56, 27, 39, 14, 35, 10, 11, 64, 25, 16, 22, 65, 37, 63, 33, 57, 3, 54, 45, 53, 20, 9, 21, 4, 0, 46, 55, 52, 41, 24, 34, 61, 44, 50, 28, 30, 17, 8, 2, 15, 23, 5, 31, 26, 62, 36, 47, 18, 51, 43, 60], 'cur_cost': 107295.0}
2025-07-03 17:06:26,476 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-03 17:06:26,476 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 17:06:26,478 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 17:06:26,478 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 109361.0
2025-07-03 17:06:29,145 - ExploitationExpert - INFO - res_population_num: 1
2025-07-03 17:06:29,145 - ExploitationExpert - INFO - res_population_costs: [97968]
2025-07-03 17:06:29,146 - ExploitationExpert - INFO - res_populations: [array([ 0, 46, 58, 29, 11, 20, 23, 26, 15,  7, 27, 44, 38, 53, 55,  1, 52,
       65,  3, 39, 10, 30, 13, 59, 36, 40, 34, 28,  9, 50, 45, 21, 47, 18,
        2,  6, 56, 61, 57, 12, 43,  4,  8, 48, 31, 63, 64, 62, 32, 42,  5,
       24, 16, 60, 19, 14, 54, 41, 33, 17, 25, 35, 49, 51, 22, 37],
      dtype=int64)]
2025-07-03 17:06:29,146 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 17:06:29,146 - ExploitationExpert - INFO - populations: [{'tour': [9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10248.0}, {'tour': [26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 24, 29, 32, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 9958.0}, {'tour': [42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63], 'cur_cost': 9961.0}, {'tour': [1, 11, 25, 62, 42, 20, 4, 54, 8, 32, 40, 49, 10, 27, 13, 64, 16, 28, 45, 12, 24, 56, 26, 47, 6, 48, 58, 55, 61, 53, 39, 7, 34, 29, 2, 38, 57, 21, 17, 50, 52, 44, 41, 43, 65, 18, 60, 63, 22, 3, 0, 35, 59, 15, 23, 31, 5, 37, 36, 46, 33, 30, 14, 19, 9, 51], 'cur_cost': 106245.0}, {'tour': [6, 58, 42, 12, 48, 59, 7, 13, 19, 32, 38, 29, 1, 49, 40, 56, 27, 39, 14, 35, 10, 11, 64, 25, 16, 22, 65, 37, 63, 33, 57, 3, 54, 45, 53, 20, 9, 21, 4, 0, 46, 55, 52, 41, 24, 34, 61, 44, 50, 28, 30, 17, 8, 2, 15, 23, 5, 31, 26, 62, 36, 47, 18, 51, 43, 60], 'cur_cost': 107295.0}, {'tour': array([37, 23,  6, 29, 22, 38, 17, 39, 31, 51, 33, 57, 54,  9, 58, 60, 63,
       45, 53, 55, 24, 14, 32, 49,  0, 16, 47, 27, 48, 46, 15,  2, 50, 40,
       12,  4, 26, 59, 20,  3,  5, 19, 64, 21, 43, 18, 65,  8, 52, 35, 42,
       10, 61, 41,  7, 44,  1, 28, 34, 36, 13, 62, 25, 56, 30, 11]), 'cur_cost': 109361.0}, {'tour': [37, 1, 31, 53, 4, 23, 12, 22, 15, 6, 44, 56, 0, 49, 40, 39, 41, 62, 43, 24, 50, 48, 47, 21, 18, 61, 38, 13, 51, 55, 64, 32, 34, 8, 11, 27, 26, 25, 58, 5, 9, 14, 28, 54, 30, 65, 10, 2, 16, 7, 60, 3, 46, 29, 57, 19, 42, 17, 45, 36, 35, 63, 59, 33, 52, 20], 'cur_cost': 97773.0}, {'tour': [4, 32, 25, 61, 13, 56, 20, 6, 53, 5, 63, 34, 35, 62, 12, 46, 47, 27, 16, 57, 33, 44, 60, 36, 30, 65, 41, 26, 48, 28, 14, 0, 52, 31, 18, 10, 9, 54, 38, 21, 1, 29, 64, 8, 55, 58, 7, 37, 17, 19, 50, 15, 40, 11, 49, 39, 22, 3, 59, 51, 45, 42, 23, 2, 43, 24], 'cur_cost': 104765.0}, {'tour': [51, 29, 42, 6, 52, 65, 4, 55, 54, 15, 0, 16, 62, 44, 12, 17, 23, 45, 64, 8, 49, 9, 19, 57, 30, 11, 22, 63, 36, 48, 10, 60, 24, 2, 39, 56, 1, 31, 28, 5, 53, 20, 26, 50, 14, 25, 58, 38, 41, 35, 37, 7, 34, 32, 18, 27, 3, 61, 43, 33, 13, 46, 59, 21, 40, 47], 'cur_cost': 106047.0}, {'tour': [56, 24, 48, 39, 4, 27, 59, 8, 16, 3, 15, 43, 34, 14, 55, 30, 47, 32, 28, 60, 18, 2, 41, 6, 50, 61, 33, 65, 9, 29, 12, 25, 46, 1, 22, 19, 26, 20, 36, 54, 17, 42, 64, 45, 21, 38, 23, 13, 62, 63, 7, 44, 51, 52, 40, 49, 57, 35, 58, 0, 53, 10, 37, 5, 31, 11], 'cur_cost': 116650.0}]
2025-07-03 17:06:29,147 - ExploitationExpert - INFO - 局部搜索耗时: 2.67秒
2025-07-03 17:06:29,147 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-03 17:06:29,148 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-03 17:06:29,148 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 17:06:29,148 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 17:06:29,148 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 17:06:29,149 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 102652.0
2025-07-03 17:06:30,127 - ExploitationExpert - INFO - res_population_num: 2
2025-07-03 17:06:30,127 - ExploitationExpert - INFO - res_population_costs: [97968, 9545]
2025-07-03 17:06:30,127 - ExploitationExpert - INFO - res_populations: [array([ 0, 46, 58, 29, 11, 20, 23, 26, 15,  7, 27, 44, 38, 53, 55,  1, 52,
       65,  3, 39, 10, 30, 13, 59, 36, 40, 34, 28,  9, 50, 45, 21, 47, 18,
        2,  6, 56, 61, 57, 12, 43,  4,  8, 48, 31, 63, 64, 62, 32, 42,  5,
       24, 16, 60, 19, 14, 54, 41, 33, 17, 25, 35, 49, 51, 22, 37],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 64, 57, 54, 60, 58,
       56, 59, 62, 53, 61, 55, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-03 17:06:30,128 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 17:06:30,129 - ExploitationExpert - INFO - populations: [{'tour': [9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10248.0}, {'tour': [26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 24, 29, 32, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 9958.0}, {'tour': [42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63], 'cur_cost': 9961.0}, {'tour': [1, 11, 25, 62, 42, 20, 4, 54, 8, 32, 40, 49, 10, 27, 13, 64, 16, 28, 45, 12, 24, 56, 26, 47, 6, 48, 58, 55, 61, 53, 39, 7, 34, 29, 2, 38, 57, 21, 17, 50, 52, 44, 41, 43, 65, 18, 60, 63, 22, 3, 0, 35, 59, 15, 23, 31, 5, 37, 36, 46, 33, 30, 14, 19, 9, 51], 'cur_cost': 106245.0}, {'tour': [6, 58, 42, 12, 48, 59, 7, 13, 19, 32, 38, 29, 1, 49, 40, 56, 27, 39, 14, 35, 10, 11, 64, 25, 16, 22, 65, 37, 63, 33, 57, 3, 54, 45, 53, 20, 9, 21, 4, 0, 46, 55, 52, 41, 24, 34, 61, 44, 50, 28, 30, 17, 8, 2, 15, 23, 5, 31, 26, 62, 36, 47, 18, 51, 43, 60], 'cur_cost': 107295.0}, {'tour': array([37, 23,  6, 29, 22, 38, 17, 39, 31, 51, 33, 57, 54,  9, 58, 60, 63,
       45, 53, 55, 24, 14, 32, 49,  0, 16, 47, 27, 48, 46, 15,  2, 50, 40,
       12,  4, 26, 59, 20,  3,  5, 19, 64, 21, 43, 18, 65,  8, 52, 35, 42,
       10, 61, 41,  7, 44,  1, 28, 34, 36, 13, 62, 25, 56, 30, 11]), 'cur_cost': 109361.0}, {'tour': array([50, 11, 53, 23, 25, 52,  8, 27, 20, 64, 51, 60, 13,  4, 39, 59, 31,
       24, 37, 15, 35, 63,  6, 45,  5, 41, 57, 65, 55, 21, 54, 48, 17, 44,
       62, 56, 10, 30, 38, 28, 29, 22, 33, 49, 40, 14,  7,  0, 42, 34, 32,
       36, 47, 46, 26,  9, 12, 18,  3, 43, 61, 19, 16,  1,  2, 58]), 'cur_cost': 102652.0}, {'tour': [4, 32, 25, 61, 13, 56, 20, 6, 53, 5, 63, 34, 35, 62, 12, 46, 47, 27, 16, 57, 33, 44, 60, 36, 30, 65, 41, 26, 48, 28, 14, 0, 52, 31, 18, 10, 9, 54, 38, 21, 1, 29, 64, 8, 55, 58, 7, 37, 17, 19, 50, 15, 40, 11, 49, 39, 22, 3, 59, 51, 45, 42, 23, 2, 43, 24], 'cur_cost': 104765.0}, {'tour': [51, 29, 42, 6, 52, 65, 4, 55, 54, 15, 0, 16, 62, 44, 12, 17, 23, 45, 64, 8, 49, 9, 19, 57, 30, 11, 22, 63, 36, 48, 10, 60, 24, 2, 39, 56, 1, 31, 28, 5, 53, 20, 26, 50, 14, 25, 58, 38, 41, 35, 37, 7, 34, 32, 18, 27, 3, 61, 43, 33, 13, 46, 59, 21, 40, 47], 'cur_cost': 106047.0}, {'tour': [56, 24, 48, 39, 4, 27, 59, 8, 16, 3, 15, 43, 34, 14, 55, 30, 47, 32, 28, 60, 18, 2, 41, 6, 50, 61, 33, 65, 9, 29, 12, 25, 46, 1, 22, 19, 26, 20, 36, 54, 17, 42, 64, 45, 21, 38, 23, 13, 62, 63, 7, 44, 51, 52, 40, 49, 57, 35, 58, 0, 53, 10, 37, 5, 31, 11], 'cur_cost': 116650.0}]
2025-07-03 17:06:30,131 - ExploitationExpert - INFO - 局部搜索耗时: 0.98秒
2025-07-03 17:06:30,131 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-03 17:06:30,131 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-03 17:06:30,131 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-03 17:06:30,132 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:06:30,132 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 104765.0 (rank 40.0%)
- Population diversity: 0.7492307692307693
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'nodes': [40, , {'subpath': [8,...
- Difficult regions to avoid (sample): {'region': [2, , {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [4, 32, 25, 61, 13, 56, 20, 6, 53, 5, 63, 34, 35, 62, 12, 46, 47, 27, 16, 57, 33, 44, 60, 36, 30, 65, 41, 26, 48, 28, 14, 0, 52, 31, 18, 10, 9, 54, 38, 21, 1, 29, 64, 8, 55, 58, 7, 37, 17, 19, 50, 15, 40, 11, 49, 39, 22, 3, 59, 51, 45, 42, 23, 2, 43, 24]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"nodes": [40, 49], "frequency": 0.5, "avg_cost": 10.0}, {"subpath": [8, 2, 6], "frequency": 0.3}, {"subpath": [55, 61, 53], "frequency": 0.3}, {"subpath": [61, 53, 62], "frequency": 0.3}]
- Difficult regions (full): [{"region": [2, 41, 6, 50, 61, 33, 65], "cost": 16238.0, "size": 7}, {"region": [57, 33, 44, 60, 36], "cost": 10865.0, "size": 5}, {"region": [28, 54, 30, 65], "cost": 9048.0, "size": 4}]

2025-07-03 17:06:30,132 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:06:30,133 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:06:45,560 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [4, 32, 25, 61, 53, 62, 12, 46, 47, 27, 16, 57, 33, 44, 60, 36, 30, 65, 41, 26, 48, 28, 14, 0, 52, 31, 18, 10, 9, 54, 38, 21, 1, 29, 64, 8, 55, 58, 7, 37, 17, 19, 50, 15, 40, 49, 39, 22, 3, 59, 51, 45, 42, 23, 2, 43, 24, 6, 20, 56, 13, 5, 63, 34, 35, 11],
  "targeted_regions": "nodes [40,49], subpath [61,53,62]",
  "strategy_comment": "Leverages low-frequency nodes and subpaths while avoiding high-cost clusters"
}
```
2025-07-03 17:06:45,561 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:06:45,569 - ExplorationKnowledgeBase - INFO - 已保存6条知识条目
2025-07-03 17:06:45,572 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96244.0, 路径: [4, 32, 25, 61, 53, 62, 12, 46, 47, 27, 16, 57, 33, 44, 60, 36, 30, 65, 41, 26, 48, 28, 14, 0, 52, 31, 18, 10, 9, 54, 38, 21, 1, 29, 64, 8, 55, 58, 7, 37, 17, 19, 50, 15, 40, 49, 39, 22, 3, 59, 51, 45, 42, 23, 2, 43, 24, 6, 20, 56, 13, 5, 63, 34, 35, 11]
2025-07-03 17:06:45,572 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [4, 32, 25, 61, 53, 62, 12, 46, 47, 27, 16, 57, 33, 44, 60, 36, 30, 65, 41, 26, 48, 28, 14, 0, 52, 31, 18, 10, 9, 54, 38, 21, 1, 29, 64, 8, 55, 58, 7, 37, 17, 19, 50, 15, 40, 49, 39, 22, 3, 59, 51, 45, 42, 23, 2, 43, 24, 6, 20, 56, 13, 5, 63, 34, 35, 11], 'cur_cost': 96244.0}
2025-07-03 17:06:45,572 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-03 17:06:45,573 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:06:45,573 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106047.0 (rank 50.0%)
- Population diversity: 0.7461538461538462
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'nodes': [40, , {'subpath': [8,...
- Difficult regions to avoid (sample): {'region': [2, , {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [51, 29, 42, 6, 52, 65, 4, 55, 54, 15, 0, 16, 62, 44, 12, 17, 23, 45, 64, 8, 49, 9, 19, 57, 30, 11, 22, 63, 36, 48, 10, 60, 24, 2, 39, 56, 1, 31, 28, 5, 53, 20, 26, 50, 14, 25, 58, 38, 41, 35, 37, 7, 34, 32, 18, 27, 3, 61, 43, 33, 13, 46, 59, 21, 40, 47]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"nodes": [40, 49], "frequency": 0.5, "avg_cost": 10.0}, {"subpath": [8, 2, 6], "frequency": 0.3}, {"subpath": [55, 61, 53], "frequency": 0.3}, {"subpath": [61, 53, 62], "frequency": 0.3}]
- Difficult regions (full): [{"region": [2, 41, 6, 50, 61, 33, 65], "cost": 16238.0, "size": 7}, {"region": [57, 33, 44, 60, 36], "cost": 10865.0, "size": 5}, {"region": [28, 54, 30, 65], "cost": 9048.0, "size": 4}]

2025-07-03 17:06:45,574 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:06:45,574 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:07:00,093 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [51, 29, 42, 6, 52, 65, 4, 55, 61, 53, 62, 44, 12, 17, 23, 45, 64, 8, 2, 49, 9, 19, 57, 30, 11, 22, 63, 36, 48, 10, 60, 24, 40, 39, 56, 1, 31, 28, 5, 54, 15, 0, 16, 20, 26, 50, 14, 25, 58, 38, 41, 35, 37, 7, 34, 32, 18, 27, 3, 33, 13, 46, 59, 21, 47, 43],
  "targeted_regions": "nodes(40,49), subpath(8,2,6), subpath(55,61,53)",
  "strategy_comment": "Prioritizes opportunity regions while avoiding high-cost clusters."
}
```
2025-07-03 17:07:00,093 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:07:00,111 - ExplorationKnowledgeBase - INFO - 已保存7条知识条目
2025-07-03 17:07:00,115 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95950.0, 路径: [51, 29, 42, 6, 52, 65, 4, 55, 61, 53, 62, 44, 12, 17, 23, 45, 64, 8, 2, 49, 9, 19, 57, 30, 11, 22, 63, 36, 48, 10, 60, 24, 40, 39, 56, 1, 31, 28, 5, 54, 15, 0, 16, 20, 26, 50, 14, 25, 58, 38, 41, 35, 37, 7, 34, 32, 18, 27, 3, 33, 13, 46, 59, 21, 47, 43]
2025-07-03 17:07:00,115 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [51, 29, 42, 6, 52, 65, 4, 55, 61, 53, 62, 44, 12, 17, 23, 45, 64, 8, 2, 49, 9, 19, 57, 30, 11, 22, 63, 36, 48, 10, 60, 24, 40, 39, 56, 1, 31, 28, 5, 54, 15, 0, 16, 20, 26, 50, 14, 25, 58, 38, 41, 35, 37, 7, 34, 32, 18, 27, 3, 33, 13, 46, 59, 21, 47, 43], 'cur_cost': 95950.0}
2025-07-03 17:07:00,116 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-03 17:07:00,116 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:07:00,117 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 116650.0 (rank 90.0%)
- Population diversity: 0.7430769230769231
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'nodes': [40, , {'subpath': [8,...
- Difficult regions to avoid (sample): {'region': [2, , {'region': [57,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [56, 24, 48, 39, 4, 27, 59, 8, 16, 3, 15, 43, 34, 14, 55, 30, 47, 32, 28, 60, 18, 2, 41, 6, 50, 61, 33, 65, 9, 29, 12, 25, 46, 1, 22, 19, 26, 20, 36, 54, 17, 42, 64, 45, 21, 38, 23, 13, 62, 63, 7, 44, 51, 52, 40, 49, 57, 35, 58, 0, 53, 10, 37, 5, 31, 11]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): [{"nodes": [40, 49], "frequency": 0.5, "avg_cost": 10.0}, {"subpath": [8, 2, 6], "frequency": 0.3}, {"subpath": [55, 61, 53], "frequency": 0.3}, {"subpath": [61, 53, 62], "frequency": 0.3}]
- Difficult regions (full): [{"region": [2, 41, 6, 50, 61, 33, 65], "cost": 16238.0, "size": 7}, {"region": [57, 33, 44, 60, 36], "cost": 10865.0, "size": 5}, {"region": [28, 54, 30, 65], "cost": 9048.0, "size": 4}]

2025-07-03 17:07:00,118 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:07:00,118 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:07:14,965 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [56, 24, 48, 39, 4, 27, 59, 8, 2, 6, 40, 49, 16, 3, 15, 43, 34, 14, 55, 61, 53, 62, 30, 47, 32, 28, 60, 18, 41, 50, 33, 65, 9, 29, 12, 25, 46, 1, 22, 19, 26, 20, 36, 54, 17, 42, 64, 45, 21, 38, 23, 13, 63, 7, 44, 51, 52, 57, 35, 58, 0, 10, 37, 5, 31, 11],
  "targeted_regions": "nodes [40,49], subpath [8,2,6], [55,61,53]",
  "strategy_comment": "Prioritizes opportunity regions and avoids high-cost clusters."
}
```
2025-07-03 17:07:14,966 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:07:14,983 - ExplorationKnowledgeBase - INFO - 已保存8条知识条目
2025-07-03 17:07:14,986 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100809.0, 路径: [56, 24, 48, 39, 4, 27, 59, 8, 2, 6, 40, 49, 16, 3, 15, 43, 34, 14, 55, 61, 53, 62, 30, 47, 32, 28, 60, 18, 41, 50, 33, 65, 9, 29, 12, 25, 46, 1, 22, 19, 26, 20, 36, 54, 17, 42, 64, 45, 21, 38, 23, 13, 63, 7, 44, 51, 52, 57, 35, 58, 0, 10, 37, 5, 31, 11]
2025-07-03 17:07:14,986 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [56, 24, 48, 39, 4, 27, 59, 8, 2, 6, 40, 49, 16, 3, 15, 43, 34, 14, 55, 61, 53, 62, 30, 47, 32, 28, 60, 18, 41, 50, 33, 65, 9, 29, 12, 25, 46, 1, 22, 19, 26, 20, 36, 54, 17, 42, 64, 45, 21, 38, 23, 13, 63, 7, 44, 51, 52, 57, 35, 58, 0, 10, 37, 5, 31, 11], 'cur_cost': 100809.0}
2025-07-03 17:07:14,987 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10248.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 24, 29, 32, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42], 'cur_cost': 9958.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63], 'cur_cost': 9961.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [1, 11, 25, 62, 42, 20, 4, 54, 8, 32, 40, 49, 10, 27, 13, 64, 16, 28, 45, 12, 24, 56, 26, 47, 6, 48, 58, 55, 61, 53, 39, 7, 34, 29, 2, 38, 57, 21, 17, 50, 52, 44, 41, 43, 65, 18, 60, 63, 22, 3, 0, 35, 59, 15, 23, 31, 5, 37, 36, 46, 33, 30, 14, 19, 9, 51], 'cur_cost': 106245.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [6, 58, 42, 12, 48, 59, 7, 13, 19, 32, 38, 29, 1, 49, 40, 56, 27, 39, 14, 35, 10, 11, 64, 25, 16, 22, 65, 37, 63, 33, 57, 3, 54, 45, 53, 20, 9, 21, 4, 0, 46, 55, 52, 41, 24, 34, 61, 44, 50, 28, 30, 17, 8, 2, 15, 23, 5, 31, 26, 62, 36, 47, 18, 51, 43, 60], 'cur_cost': 107295.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [4, 32, 25, 61, 53, 62, 12, 46, 47, 27, 16, 57, 33, 44, 60, 36, 30, 65, 41, 26, 48, 28, 14, 0, 52, 31, 18, 10, 9, 54, 38, 21, 1, 29, 64, 8, 55, 58, 7, 37, 17, 19, 50, 15, 40, 49, 39, 22, 3, 59, 51, 45, 42, 23, 2, 43, 24, 6, 20, 56, 13, 5, 63, 34, 35, 11], 'cur_cost': 96244.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [51, 29, 42, 6, 52, 65, 4, 55, 61, 53, 62, 44, 12, 17, 23, 45, 64, 8, 2, 49, 9, 19, 57, 30, 11, 22, 63, 36, 48, 10, 60, 24, 40, 39, 56, 1, 31, 28, 5, 54, 15, 0, 16, 20, 26, 50, 14, 25, 58, 38, 41, 35, 37, 7, 34, 32, 18, 27, 3, 33, 13, 46, 59, 21, 47, 43], 'cur_cost': 95950.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [56, 24, 48, 39, 4, 27, 59, 8, 2, 6, 40, 49, 16, 3, 15, 43, 34, 14, 55, 61, 53, 62, 30, 47, 32, 28, 60, 18, 41, 50, 33, 65, 9, 29, 12, 25, 46, 1, 22, 19, 26, 20, 36, 54, 17, 42, 64, 45, 21, 38, 23, 13, 63, 7, 44, 51, 52, 57, 35, 58, 0, 10, 37, 5, 31, 11], 'cur_cost': 100809.0}}]
2025-07-03 17:07:14,988 - __main__ - INFO - 进化阶段完成
2025-07-03 17:07:14,988 - __main__ - INFO - 开始评估阶段
2025-07-03 17:07:14,988 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 17:07:14,989 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 17:07:14,990 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9958.0 → 9958.0 (Δ 0.0)
- Mean cost: 79125.4 → 74872.3
- Diversity: 0.766 → 0.734 (Δ -0.0323)

#### Elite metrics
- Elite count: 0 → 2
- Best elite cost: 9958.0 → 9545 (Δ 413.0)
- Elite diversity: 0.000 → 0.977 (Δ 0.9769)

#### Strategy performance
- Explore: 8 inds, success 62.5% , avg Δ 5992.25
- Exploit: 2 inds, success 0.0% , avg Δ -2703.5

#### Other indicators
- No-change individuals: 3
- Regression individuals: 2
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 17:07:14,990 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:07:23,053 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.0},
  "balance_state": "explore_heavy",
  "recommendations": ["set_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 17:07:23,083 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 17:07:23,085 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.0},
  "balance_state": "explore_heavy",
  "recommendations": ["set_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 17:07:23,085 - __main__ - INFO - 评估阶段完成
2025-07-03 17:07:23,085 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.0},
  "balance_state": "explore_heavy",
  "recommendations": ["set_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 17:07:23,086 - __main__ - INFO - 当前最佳适应度: 9958.0
2025-07-03 17:07:23,087 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_0.pkl
2025-07-03 17:07:23,087 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-03 17:07:23,087 - __main__ - INFO - 开始分析阶段
2025-07-03 17:07:23,087 - StatsExpert - INFO - 开始统计分析
2025-07-03 17:07:23,105 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9958.0, 'max': 109361.0, 'mean': 74872.3, 'std': 42631.01536217499}, 'diversity': 0.9013468013468013, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 17:07:23,106 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9958.0, 'max': 109361.0, 'mean': 74872.3, 'std': 42631.01536217499}, 'diversity_level': 0.9013468013468013, 'convergence_level': 0.0, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1], 'summary': '8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1], 'summary': '8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 17:07:23,107 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 17:07:23,107 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 17:07:23,107 - PathExpert - INFO - 开始路径结构分析
2025-07-03 17:07:23,110 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 17:07:23,111 - PathExpert - INFO - 路径结构分析完成
2025-07-03 17:07:23,111 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 17:07:23,111 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 17:07:23,111 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 17:07:23,111 - EliteExpert - INFO - 开始精英解分析
2025-07-03 17:07:23,112 - EliteExpert - INFO - 精英解分析完成
2025-07-03 17:07:23,112 - __main__ - INFO - 精英专家分析报告: {'elite_count': 2, 'elite_common_features': {'common_edges': {'(52, 65)': 1.0}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 413.0, 'avg_gap': 21115.800000000003}, 'structure_gap': {'unique_elite_edges': 94, 'unique_pop_edges': 496, 'common_edges': 37}}, 'elite_diversity': {'diversity_score': 0.9545454545454546}}
2025-07-03 17:07:23,113 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 17:07:23,113 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 17:07:23,115 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 17:07:23,115 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 9958.0, mean 74872.3, max 109361.0, std 42631.01536217499
- diversity: 0.9013468013468013
- convergence: 0.0
- clustering: 8 clusters, sizes [3, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [9958.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.9545454545454546}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 17:07:23,116 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:07:33,952 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.9013468013468013,
    "convergence": 0.0,
    "clustering": "weak"
  },
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["mutation", "crossover"]
  }
}
```
2025-07-03 17:07:33,953 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 17:07:33,953 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9013468013468013, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation', 'crossover']}}
2025-07-03 17:07:33,953 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 17:07:33,953 - __main__ - INFO - 分析阶段完成
2025-07-03 17:07:33,954 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.9013468013468013, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [], 'opportunity_regions': [], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation', 'crossover']}}
2025-07-03 17:07:33,954 - __main__ - INFO - 开始策略分配阶段
2025-07-03 17:07:33,954 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 17:07:33,954 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.901
  • best_cost: 9958.0
  • mean_cost: 74872.3
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 0): None
- Opportunity regions (# 0): None
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 1.0, "exploit": 0.0},
  "balance_state":...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 17:07:33,955 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 17:07:33,955 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:07:43,568 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.9,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase, high diversity, rugged landscape favors exploration."
}
```
2025-07-03 17:07:43,568 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-03 17:07:43,568 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-03 17:07:43,568 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.9,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase, high diversity, rugged landscape favors exploration."
}
```
2025-07-03 17:07:43,568 - __main__ - INFO - 策略分配阶段完成
2025-07-03 17:07:43,568 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-07-03 17:07:43,568 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.9,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early phase, high diversity, rugged landscape favors exploration."
}
```
2025-07-03 17:07:43,570 - __main__ - INFO - 开始进化阶段
2025-07-03 17:07:43,570 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 17:07:43,570 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:07:43,571 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10248.0 (rank 20.0%)
- Population diversity: 0.7338461538461538
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): []

2025-07-03 17:07:43,571 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:07:43,572 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:07:57,422 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 3, 7, 11, 9, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42],
  "targeted_regions": "early_segment_reorder",
  "strategy_comment": "Reordered start to explore sparse early path variations"
}
```
2025-07-03 17:07:57,422 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:07:57,440 - ExplorationKnowledgeBase - INFO - 已保存9条知识条目
2025-07-03 17:07:57,444 - ExplorationExpert - INFO - 探索路径生成完成，成本: 10334.0, 路径: [0, 1, 3, 7, 11, 9, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42]
2025-07-03 17:07:57,444 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 3, 7, 11, 9, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 14, 15, 22, 12, 17, 18, 16, 23, 13, 20, 21, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 40, 49, 47, 46, 48, 43, 39, 44, 45, 38, 51, 50, 41, 42], 'cur_cost': 10334.0}
2025-07-03 17:07:57,445 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 17:07:57,445 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:07:57,446 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9958.0 (rank 0.0%)
- Population diversity: 0.7338461538461538
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [26, 25, 31, 33, 28, 30, 35, 34, 36, 37, 27, 24, 29, 32, 18, 16, 23, 22, 12, 17, 15, 14, 20, 21, 13, 19, 9, 11, 7, 3, 1, 0, 10, 8, 2, 6, 4, 5, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63, 39, 44, 45, 38, 51, 50, 41, 46, 47, 49, 40, 43, 48, 42]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): []

2025-07-03 17:07:57,446 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:07:57,446 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:08:11,987 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 3, 7, 11, 9, 19, 13, 21, 20, 14, 15, 17, 12, 22, 23, 16, 18, 32, 29, 24, 27, 37, 36, 34, 35, 30, 28, 33, 31, 25, 26, 42, 48, 43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 5, 4, 6, 2, 8, 10],
  "targeted_regions": "unexplored_start_end",
  "strategy_comment": "Reversed start/end segments to explore sparse areas"
}
```
2025-07-03 17:08:11,987 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:08:12,006 - ExplorationKnowledgeBase - INFO - 已保存10条知识条目
2025-07-03 17:08:12,010 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9958.0, 路径: [0, 1, 3, 7, 11, 9, 19, 13, 21, 20, 14, 15, 17, 12, 22, 23, 16, 18, 32, 29, 24, 27, 37, 36, 34, 35, 30, 28, 33, 31, 25, 26, 42, 48, 43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 5, 4, 6, 2, 8, 10]
2025-07-03 17:08:12,010 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 3, 7, 11, 9, 19, 13, 21, 20, 14, 15, 17, 12, 22, 23, 16, 18, 32, 29, 24, 27, 37, 36, 34, 35, 30, 28, 33, 31, 25, 26, 42, 48, 43, 40, 49, 47, 46, 41, 50, 51, 38, 45, 44, 39, 63, 52, 65, 54, 57, 64, 60, 58, 56, 59, 62, 53, 61, 55, 5, 4, 6, 2, 8, 10], 'cur_cost': 9958.0}
2025-07-03 17:08:12,011 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 17:08:12,011 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:08:12,011 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9961.0 (rank 10.0%)
- Population diversity: 0.7353846153846154
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): []

2025-07-03 17:08:12,012 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:08:12,012 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:08:25,034 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63],
  "targeted_regions": "sparse_cells(0,1)",
  "strategy_comment": "Random shuffle in sparse regions for diversity"
}
```
2025-07-03 17:08:25,034 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:08:25,046 - ExplorationKnowledgeBase - INFO - 已保存11条知识条目
2025-07-03 17:08:25,049 - ExplorationExpert - INFO - 探索路径生成完成，成本: 9961.0, 路径: [42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63]
2025-07-03 17:08:25,050 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [42, 50, 38, 51, 45, 44, 41, 39, 47, 49, 40, 43, 48, 46, 21, 20, 13, 23, 16, 18, 12, 22, 15, 14, 17, 19, 27, 37, 25, 26, 36, 35, 28, 30, 34, 33, 31, 24, 29, 32, 3, 7, 1, 11, 9, 5, 4, 8, 2, 6, 10, 0, 55, 61, 53, 62, 59, 56, 58, 60, 64, 57, 54, 65, 52, 63], 'cur_cost': 9961.0}
2025-07-03 17:08:25,050 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 17:08:25,050 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:08:25,051 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106245.0 (rank 70.0%)
- Population diversity: 0.7353846153846154
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [1, 11, 25, 62, 42, 20, 4, 54, 8, 32, 40, 49, 10, 27, 13, 64, 16, 28, 45, 12, 24, 56, 26, 47, 6, 48, 58, 55, 61, 53, 39, 7, 34, 29, 2, 38, 57, 21, 17, 50, 52, 44, 41, 43, 65, 18, 60, 63, 22, 3, 0, 35, 59, 15, 23, 31, 5, 37, 36, 46, 33, 30, 14, 19, 9, 51]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): []

2025-07-03 17:08:25,051 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:08:25,051 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:08:38,292 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 35, 59, 15, 23, 31, 5, 37, 36, 46, 33, 30, 14, 19, 9, 51, 1, 11, 25, 62, 42, 20, 4, 54, 8, 32, 40, 49, 10, 27, 13, 64, 16, 28, 45, 12, 24, 56, 26, 47, 6, 48, 58, 55, 61, 53, 39, 7, 34, 29, 2, 38, 57, 21, 17, 50, 52, 44, 41, 43, 65, 18, 60, 63, 22, 3],
  "targeted_regions": "unexplored_start_end",
  "strategy_comment": "Reversed segment to explore sparse start/end regions"
}
```
2025-07-03 17:08:38,293 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:08:38,310 - ExplorationKnowledgeBase - INFO - 已保存12条知识条目
2025-07-03 17:08:38,313 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106245.0, 路径: [0, 35, 59, 15, 23, 31, 5, 37, 36, 46, 33, 30, 14, 19, 9, 51, 1, 11, 25, 62, 42, 20, 4, 54, 8, 32, 40, 49, 10, 27, 13, 64, 16, 28, 45, 12, 24, 56, 26, 47, 6, 48, 58, 55, 61, 53, 39, 7, 34, 29, 2, 38, 57, 21, 17, 50, 52, 44, 41, 43, 65, 18, 60, 63, 22, 3]
2025-07-03 17:08:38,314 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 35, 59, 15, 23, 31, 5, 37, 36, 46, 33, 30, 14, 19, 9, 51, 1, 11, 25, 62, 42, 20, 4, 54, 8, 32, 40, 49, 10, 27, 13, 64, 16, 28, 45, 12, 24, 56, 26, 47, 6, 48, 58, 55, 61, 53, 39, 7, 34, 29, 2, 38, 57, 21, 17, 50, 52, 44, 41, 43, 65, 18, 60, 63, 22, 3], 'cur_cost': 106245.0}
2025-07-03 17:08:38,314 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 17:08:38,314 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 17:08:38,315 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 107295.0 (rank 80.0%)
- Population diversity: 0.7353846153846154
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): None
- Difficult regions to avoid (sample): None
- Sparse cells / unexplored areas: Explore cells with zero visits in density grid

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

### Additional Context
- Full current path sequence: [6, 58, 42, 12, 48, 59, 7, 13, 19, 32, 38, 29, 1, 49, 40, 56, 27, 39, 14, 35, 10, 11, 64, 25, 16, 22, 65, 37, 63, 33, 57, 3, 54, 45, 53, 20, 9, 21, 4, 0, 46, 55, 52, 41, 24, 34, 61, 44, 50, 28, 30, 17, 8, 2, 15, 23, 5, 31, 26, 62, 36, 47, 18, 51, 43, 60]
- Strategy parameters: {"diversity_weight": 0.7, "risk_tolerance": 0.6}
- Opportunity regions (full): []
- Difficult regions (full): []

2025-07-03 17:08:38,315 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 17:08:38,315 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 17:08:51,695 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 21, 9, 20, 53, 45, 54, 3, 57, 33, 63, 37, 65, 22, 16, 25, 64, 11, 10, 35, 14, 39, 27, 56, 40, 49, 1, 29, 38, 32, 19, 13, 7, 59, 48, 12, 42, 58, 6, 60, 43, 51, 18, 47, 36, 62, 26, 31, 5, 23, 15, 2, 8, 17, 30, 28, 50, 44, 61, 34, 24, 41, 52, 55, 46],
  "targeted_regions": "reversed_path_segments",
  "strategy_comment": "Reverses segments to explore sparse cells and increase diversity"
}
```
2025-07-03 17:08:51,696 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 17:08:51,713 - ExplorationKnowledgeBase - INFO - 已保存13条知识条目
2025-07-03 17:08:51,716 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107295.0, 路径: [0, 4, 21, 9, 20, 53, 45, 54, 3, 57, 33, 63, 37, 65, 22, 16, 25, 64, 11, 10, 35, 14, 39, 27, 56, 40, 49, 1, 29, 38, 32, 19, 13, 7, 59, 48, 12, 42, 58, 6, 60, 43, 51, 18, 47, 36, 62, 26, 31, 5, 23, 15, 2, 8, 17, 30, 28, 50, 44, 61, 34, 24, 41, 52, 55, 46]
2025-07-03 17:08:51,717 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 21, 9, 20, 53, 45, 54, 3, 57, 33, 63, 37, 65, 22, 16, 25, 64, 11, 10, 35, 14, 39, 27, 56, 40, 49, 1, 29, 38, 32, 19, 13, 7, 59, 48, 12, 42, 58, 6, 60, 43, 51, 18, 47, 36, 62, 26, 31, 5, 23, 15, 2, 8, 17, 30, 28, 50, 44, 61, 34, 24, 41, 52, 55, 46], 'cur_cost': 107295.0}
2025-07-03 17:08:51,717 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 17:08:51,717 - ExplorationExpert - INFO - 开始生成探索路径
