2025-06-22 16:17:15,499 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-22 16:17:15,499 - __main__ - INFO - 开始分析阶段
2025-06-22 16:17:15,499 - StatsExpert - INFO - 开始统计分析
2025-06-22 16:17:15,521 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9948.0, 'max': 116630.0, 'mean': 78550.2, 'std': 45257.57648129205}, 'diversity': 0.9138047138047138, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 16:17:15,521 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9948.0, 'max': 116630.0, 'mean': 78550.2, 'std': 45257.57648129205}, 'diversity_level': 0.9138047138047138, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 16:17:15,530 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 16:17:15,530 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 16:17:15,530 - PathExpert - INFO - 开始路径结构分析
2025-06-22 16:17:15,536 - PathExpert - INFO - 路径结构分析完成
2025-06-22 16:17:15,537 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (40, 49), 'frequency': 0.5, 'avg_cost': 10.0}], 'common_subpaths': [{'subpath': (39, 44, 45), 'frequency': 0.3}, {'subpath': (44, 45, 38), 'frequency': 0.3}, {'subpath': (45, 38, 51), 'frequency': 0.3}, {'subpath': (38, 51, 50), 'frequency': 0.3}, {'subpath': (51, 50, 41), 'frequency': 0.3}, {'subpath': (19, 27, 37), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (26, 36, 35), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(45, 38)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(51, 50)', 'frequency': 0.3}, {'edge': '(50, 41)', 'frequency': 0.3}, {'edge': '(41, 46)', 'frequency': 0.2}, {'edge': '(46, 47)', 'frequency': 0.2}, {'edge': '(47, 49)', 'frequency': 0.2}, {'edge': '(49, 40)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.2}, {'edge': '(48, 42)', 'frequency': 0.2}, {'edge': '(42, 21)', 'frequency': 0.3}, {'edge': '(21, 20)', 'frequency': 0.2}, {'edge': '(20, 13)', 'frequency': 0.2}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(23, 16)', 'frequency': 0.2}, {'edge': '(16, 18)', 'frequency': 0.2}, {'edge': '(18, 12)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.2}, {'edge': '(22, 15)', 'frequency': 0.2}, {'edge': '(15, 14)', 'frequency': 0.2}, {'edge': '(14, 17)', 'frequency': 0.2}, {'edge': '(17, 19)', 'frequency': 0.2}, {'edge': '(19, 27)', 'frequency': 0.3}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(37, 25)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(36, 35)', 'frequency': 0.3}, {'edge': '(35, 28)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(34, 33)', 'frequency': 0.3}, {'edge': '(33, 31)', 'frequency': 0.3}, {'edge': '(31, 24)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(32, 3)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(7, 1)', 'frequency': 0.2}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(11, 9)', 'frequency': 0.2}, {'edge': '(9, 5)', 'frequency': 0.2}, {'edge': '(5, 4)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(8, 2)', 'frequency': 0.2}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(10, 0)', 'frequency': 0.2}, {'edge': '(0, 55)', 'frequency': 0.3}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(61, 53)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(62, 59)', 'frequency': 0.3}, {'edge': '(59, 56)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(64, 57)', 'frequency': 0.3}, {'edge': '(57, 54)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(65, 52)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(63, 39)', 'frequency': 0.2}, {'edge': '(11, 7)', 'frequency': 0.2}, {'edge': '(14, 15)', 'frequency': 0.2}, {'edge': '(22, 12)', 'frequency': 0.2}, {'edge': '(40, 49)', 'frequency': 0.2}, {'edge': '(48, 43)', 'frequency': 0.2}, {'edge': '(15, 61)', 'frequency': 0.2}, {'edge': '(61, 37)', 'frequency': 0.2}, {'edge': '(27, 2)', 'frequency': 0.2}, {'edge': '(62, 42)', 'frequency': 0.2}, {'edge': '(14, 34)', 'frequency': 0.2}, {'edge': '(28, 5)', 'frequency': 0.2}, {'edge': '(32, 50)', 'frequency': 0.2}, {'edge': '(9, 4)', 'frequency': 0.2}, {'edge': '(26, 0)', 'frequency': 0.2}, {'edge': '(19, 11)', 'frequency': 0.2}, {'edge': '(10, 18)', 'frequency': 0.2}, {'edge': '(5, 51)', 'frequency': 0.2}, {'edge': '(51, 13)', 'frequency': 0.2}, {'edge': '(58, 30)', 'frequency': 0.2}, {'edge': '(34, 43)', 'frequency': 0.2}, {'edge': '(1, 6)', 'frequency': 0.2}, {'edge': '(36, 34)', 'frequency': 0.2}, {'edge': '(18, 15)', 'frequency': 0.2}, {'edge': '(46, 23)', 'frequency': 0.2}, {'edge': '(23, 47)', 'frequency': 0.2}, {'edge': '(41, 17)', 'frequency': 0.2}, {'edge': '(39, 58)', 'frequency': 0.2}, {'edge': '(21, 55)', 'frequency': 0.2}, {'edge': '(8, 62)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [6, 39, 30, 45, 65, 26, 64], 'cost': 15456.0, 'size': 7}, {'region': [25, 42, 55, 43, 52, 50], 'cost': 13481.0, 'size': 6}, {'region': [27, 58, 30, 52, 46], 'cost': 11685.0, 'size': 5}, {'region': [53, 50, 54, 28, 55], 'cost': 11659.0, 'size': 5}, {'region': [27, 38, 10, 39, 32, 50], 'cost': 11553.0, 'size': 6}]}
2025-06-22 16:17:15,537 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 16:17:15,537 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 16:17:15,537 - EliteExpert - INFO - 开始精英解分析
2025-06-22 16:17:15,538 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-22 16:17:15,538 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-22 16:17:15,538 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 16:17:15,538 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 16:17:15,538 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 16:17:15,538 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=9948.0, max=116630.0, mean=78550.2, std=45257.57648129205
- Diversity: 0.9138047138047138, Convergence: 0.0
- Structure: 1 high quality edges, 10 common subpaths
- Elite: 0 solutions with 0 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 5 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 16:17:26,645 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High. The large standard deviation in cost (std=45257.576) and the cost range from min=9948.0 to max=116630.0 suggests significant cost variability.  The presence of 5 difficult regions further indicates a rugged landscape.",
    "modality": "Likely multi-modal. The presence of 1 high-quality edges and the identification of 5 difficult regions, coupled with no elite solutions and 0 fixed nodes, suggests the existence of multiple potentially good solutions (local optima) and the difficulty in converging to a single global optimum." ,
    "other_notes": "The presence of 10 common subpaths hints at some structure in the search space, potentially indicating building blocks of solutions."
  },
  "population_state": {
    "diversity": "High. A diversity score of 0.9138 is very close to the maximum (likely 1.0), indicating that the population covers a wide range of the search space and solutions are dissimilar. This is beneficial for exploring the space, but it also means convergence is very low.",
    "convergence": "Zero. The convergence score of 0.0 indicates no convergence towards a single best solution, which is expected given the high diversity and potentially rugged landscape. The absence of elite solutions supports this.",
    "other_notes": "The high diversity is counterbalanced by the low convergence.  The challenge is to maintain diversity while guiding the search toward more promising regions."
  },
  "difficult_regions": {
    "challenges": "The 5 identified difficult regions represent areas where the search is likely getting trapped or where it's difficult to find better solutions. They might be due to deceptive landscapes, local optima, or narrow valleys. Further investigation to determine the specific causes within these regions is recommended (e.g., using visualization or clustering).",
    "details": "The fact that no elite solutions were found (0 solutions with 0 fixed nodes) reinforces the notion that the search is struggling to find high-quality solutions."
  },
  "opportunity_regions": {
    "potential": "None explicitly identified. The absence of identified opportunity regions suggests that the current population hasn't yet found areas that significantly outperform others. However, the structure (1 high-quality edges and 10 common subpaths) hints at building blocks and relationships that can be leveraged to improve solutions. Focusing on combining or refining these subpaths may prove fruitful.",
    "actions": "Exploring the common subpaths and high-quality edges to identify potential building blocks or partial solutions that can be combined, refined, or extended may lead to improved solutions and identify areas for further investigation. Look at where these edges are present to focus the search."
  },
  "evolution_direction": {
    "strategy": "Exploration and Diversification with Targeted Exploitation.",
    "details": "Given the high diversity and low convergence, the immediate focus should be on exploring the search space and finding promising areas that can guide the convergence process. The presence of difficult regions and the lack of elite solutions suggest that standard convergence strategies may be premature. Here's the suggested approach:",
    "steps": [
      "Maintain high diversity in the early stages. Avoid premature convergence through aggressive selection.",
      "Focus on exploring the difficult regions to identify their characteristics (e.g., local optima, deceptive regions).  This could involve visualization techniques or further analysis of the solutions found within these regions.",
      "Explore the 10 common subpaths and the high-quality edges to identify promising building blocks or partial solutions.  Investigate their effectiveness in different contexts.  This might involve genetic operators such as crossover and mutation that emphasize these building blocks.",
      "Use techniques to encourage crossover to explore more combinations of subpaths.",
       "Gradually guide the search towards potentially better regions after identifying potential building blocks. This could involve an adaptive selection pressure (increasing the selection pressure slowly over iterations), but focus primarily on exploration in the initial stages to prevent the loss of diversity.",
      "Regularly monitor and re-evaluate the performance to understand the search progress.",
      "Consider local search or refinement techniques to explore promising regions more thoroughly after identifying potential good solutions (after some convergence).",
      "If computation is a concern, use techniques to limit the exploration of difficult regions to avoid wasted resources.",
      "Identify if the 10 common subpaths are being used by high-cost and low-cost solutions.  If so, refine the solutions with these subpaths to optimize for cost."
    ]
  }
}
```
2025-06-22 16:17:26,646 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 16:17:26,646 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High. The large standard deviation in cost (std=45257.576) and the cost range from min=9948.0 to max=116630.0 suggests significant cost variability.  The presence of 5 difficult regions further indicates a rugged landscape.', 'modality': 'Likely multi-modal. The presence of 1 high-quality edges and the identification of 5 difficult regions, coupled with no elite solutions and 0 fixed nodes, suggests the existence of multiple potentially good solutions (local optima) and the difficulty in converging to a single global optimum.', 'other_notes': 'The presence of 10 common subpaths hints at some structure in the search space, potentially indicating building blocks of solutions.'}, 'population_state': {'diversity': 'High. A diversity score of 0.9138 is very close to the maximum (likely 1.0), indicating that the population covers a wide range of the search space and solutions are dissimilar. This is beneficial for exploring the space, but it also means convergence is very low.', 'convergence': 'Zero. The convergence score of 0.0 indicates no convergence towards a single best solution, which is expected given the high diversity and potentially rugged landscape. The absence of elite solutions supports this.', 'other_notes': 'The high diversity is counterbalanced by the low convergence.  The challenge is to maintain diversity while guiding the search toward more promising regions.'}, 'difficult_regions': {'challenges': "The 5 identified difficult regions represent areas where the search is likely getting trapped or where it's difficult to find better solutions. They might be due to deceptive landscapes, local optima, or narrow valleys. Further investigation to determine the specific causes within these regions is recommended (e.g., using visualization or clustering).", 'details': 'The fact that no elite solutions were found (0 solutions with 0 fixed nodes) reinforces the notion that the search is struggling to find high-quality solutions.'}, 'opportunity_regions': {'potential': "None explicitly identified. The absence of identified opportunity regions suggests that the current population hasn't yet found areas that significantly outperform others. However, the structure (1 high-quality edges and 10 common subpaths) hints at building blocks and relationships that can be leveraged to improve solutions. Focusing on combining or refining these subpaths may prove fruitful.", 'actions': 'Exploring the common subpaths and high-quality edges to identify potential building blocks or partial solutions that can be combined, refined, or extended may lead to improved solutions and identify areas for further investigation. Look at where these edges are present to focus the search.'}, 'evolution_direction': {'strategy': 'Exploration and Diversification with Targeted Exploitation.', 'details': "Given the high diversity and low convergence, the immediate focus should be on exploring the search space and finding promising areas that can guide the convergence process. The presence of difficult regions and the lack of elite solutions suggest that standard convergence strategies may be premature. Here's the suggested approach:", 'steps': ['Maintain high diversity in the early stages. Avoid premature convergence through aggressive selection.', 'Focus on exploring the difficult regions to identify their characteristics (e.g., local optima, deceptive regions).  This could involve visualization techniques or further analysis of the solutions found within these regions.', 'Explore the 10 common subpaths and the high-quality edges to identify promising building blocks or partial solutions.  Investigate their effectiveness in different contexts.  This might involve genetic operators such as crossover and mutation that emphasize these building blocks.', 'Use techniques to encourage crossover to explore more combinations of subpaths.', 'Gradually guide the search towards potentially better regions after identifying potential building blocks. This could involve an adaptive selection pressure (increasing the selection pressure slowly over iterations), but focus primarily on exploration in the initial stages to prevent the loss of diversity.', 'Regularly monitor and re-evaluate the performance to understand the search progress.', 'Consider local search or refinement techniques to explore promising regions more thoroughly after identifying potential good solutions (after some convergence).', 'If computation is a concern, use techniques to limit the exploration of difficult regions to avoid wasted resources.', 'Identify if the 10 common subpaths are being used by high-cost and low-cost solutions.  If so, refine the solutions with these subpaths to optimize for cost.']}}
2025-06-22 16:17:26,646 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 16:17:26,648 - __main__ - INFO - 分析阶段完成
2025-06-22 16:17:26,648 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High. The large standard deviation in cost (std=45257.576) and the cost range from min=9948.0 to max=116630.0 suggests significant cost variability.  The presence of 5 difficult regions further indicates a rugged landscape.', 'modality': 'Likely multi-modal. The presence of 1 high-quality edges and the identification of 5 difficult regions, coupled with no elite solutions and 0 fixed nodes, suggests the existence of multiple potentially good solutions (local optima) and the difficulty in converging to a single global optimum.', 'other_notes': 'The presence of 10 common subpaths hints at some structure in the search space, potentially indicating building blocks of solutions.'}, 'population_state': {'diversity': 'High. A diversity score of 0.9138 is very close to the maximum (likely 1.0), indicating that the population covers a wide range of the search space and solutions are dissimilar. This is beneficial for exploring the space, but it also means convergence is very low.', 'convergence': 'Zero. The convergence score of 0.0 indicates no convergence towards a single best solution, which is expected given the high diversity and potentially rugged landscape. The absence of elite solutions supports this.', 'other_notes': 'The high diversity is counterbalanced by the low convergence.  The challenge is to maintain diversity while guiding the search toward more promising regions.'}, 'difficult_regions': {'challenges': "The 5 identified difficult regions represent areas where the search is likely getting trapped or where it's difficult to find better solutions. They might be due to deceptive landscapes, local optima, or narrow valleys. Further investigation to determine the specific causes within these regions is recommended (e.g., using visualization or clustering).", 'details': 'The fact that no elite solutions were found (0 solutions with 0 fixed nodes) reinforces the notion that the search is struggling to find high-quality solutions.'}, 'opportunity_regions': {'potential': "None explicitly identified. The absence of identified opportunity regions suggests that the current population hasn't yet found areas that significantly outperform others. However, the structure (1 high-quality edges and 10 common subpaths) hints at building blocks and relationships that can be leveraged to improve solutions. Focusing on combining or refining these subpaths may prove fruitful.", 'actions': 'Exploring the common subpaths and high-quality edges to identify potential building blocks or partial solutions that can be combined, refined, or extended may lead to improved solutions and identify areas for further investigation. Look at where these edges are present to focus the search.'}, 'evolution_direction': {'strategy': 'Exploration and Diversification with Targeted Exploitation.', 'details': "Given the high diversity and low convergence, the immediate focus should be on exploring the search space and finding promising areas that can guide the convergence process. The presence of difficult regions and the lack of elite solutions suggest that standard convergence strategies may be premature. Here's the suggested approach:", 'steps': ['Maintain high diversity in the early stages. Avoid premature convergence through aggressive selection.', 'Focus on exploring the difficult regions to identify their characteristics (e.g., local optima, deceptive regions).  This could involve visualization techniques or further analysis of the solutions found within these regions.', 'Explore the 10 common subpaths and the high-quality edges to identify promising building blocks or partial solutions.  Investigate their effectiveness in different contexts.  This might involve genetic operators such as crossover and mutation that emphasize these building blocks.', 'Use techniques to encourage crossover to explore more combinations of subpaths.', 'Gradually guide the search towards potentially better regions after identifying potential building blocks. This could involve an adaptive selection pressure (increasing the selection pressure slowly over iterations), but focus primarily on exploration in the initial stages to prevent the loss of diversity.', 'Regularly monitor and re-evaluate the performance to understand the search progress.', 'Consider local search or refinement techniques to explore promising regions more thoroughly after identifying potential good solutions (after some convergence).', 'If computation is a concern, use techniques to limit the exploration of difficult regions to avoid wasted resources.', 'Identify if the 10 common subpaths are being used by high-cost and low-cost solutions.  If so, refine the solutions with these subpaths to optimize for cost.']}}
2025-06-22 16:17:26,648 - __main__ - INFO - 开始策略分配阶段
2025-06-22 16:17:26,648 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 16:17:26,649 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 0
- Population: 10 individuals, diversity=High. A diversity score of 0.9138 is very close to the maximum (likely 1.0), indicating that the population covers a wide range of the search space and solutions are dissimilar. This is beneficial for exploring the space, but it also means convergence is very low.
- Best cost: 9948.0, Mean cost: 78550.2
- Search space: Ruggedness: High. The large standard deviation in cost (std=45257.576) and the cost range from min=9948.0 to max=116630.0 suggests significant cost variability.  The presence of 5 difficult regions further indicates a rugged landscape., Modality: Likely multi-modal. The presence of 1 high-quality edges and the identification of 5 difficult regions, coupled with no elite solutions and 0 fixed nodes, suggests the existence of multiple potentially good solutions (local optima) and the difficulty in converging to a single global optimum.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: No previous feedback

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 16:17:26,649 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 16:17:31,589 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "step_size": 0.15,
      "mutation_rate": 0.2,
      "local_search_depth": 2
    },
    "exploit": {
      "step_size": 0.05,
      "mutation_rate": 0.1,
      "local_search_depth": 5
    }
  }
}
```

2025-06-22 16:17:31,589 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 16:17:31,589 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "step_size": 0.15,
      "mutation_rate": 0.2,
      "local_search_depth": 2
    },
    "exploit": {
      "step_size": 0.05,
      "mutation_rate": 0.1,
      "local_search_depth": 5
    }
  }
}
```

2025-06-22 16:17:31,589 - __main__ - INFO - 策略分配阶段完成
2025-06-22 16:17:31,589 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 16:17:31,589 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "step_size": 0.15,
      "mutation_rate": 0.2,
      "local_search_depth": 2
    },
    "exploit": {
      "step_size": 0.05,
      "mutation_rate": 0.1,
      "local_search_depth": 5
    }
  }
}
```

2025-06-22 16:17:31,589 - __main__ - INFO - 开始进化阶段
2025-06-22 16:17:31,589 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 16:17:31,589 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:17:31,589 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [39, 44, 45] and ending with [65, 52, 63]
- Cost: 9948.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7338461538461538
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:17:31,589 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:17:36,213 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [39, 44, 48, 51, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63],
  "modification_strategy": "Prioritized exploration of unexplored regions and avoidance of difficult regions.  I used a higher diversity weight to favor paths that deviate from the existing one.  The focus was to find an alternative route, especially in the middle sections, using the information about opportunity and difficult regions.",
  "targeted_regions": [
    "Opportunity Region 1 (Region ID not specified, but indicated in search context) ",
    "Opportunity Region 2 (Region ID not specified, but indicated in search context)"
  ]
}
```

2025-06-22 16:17:36,213 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:17:36,214 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6540.0, 路径: [39, 44, 48, 51, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63]
2025-06-22 16:17:36,214 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [39, 44, 48, 51, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63], 'cur_cost': 6540.0}
2025-06-22 16:17:36,215 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 16:17:36,215 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:17:36,215 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [2, 8, 5] and ending with [50, 41, 42]
- Cost: 10244.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8129139072847682
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:17:36,216 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:17:42,726 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    2, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193
  ],
  "modification_strategy": "Prioritized exploration of unexplored regions by drastically altering the path's initial segments and then leveraging a different route that may have been initially avoided. This strategy prioritizes diversity by focusing on regions that have been less frequently visited by paths in the existing population. The path construction uses a breadth-first search approach focusing more on previously unused nodes. The diversification is achieved by swapping the central nodes to introduce a novel approach to discover unexplored areas.",
  "targeted_regions": "The path targets regions with a high 'opportunity' score, aiming to uncover promising unexplored areas and avoiding those deemed 'difficult'."
}
```
2025-06-22 16:17:42,726 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:17:42,726 - ExplorationExpert - INFO - 探索路径生成完成，成本: nan, 路径: [2, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193]
2025-06-22 16:17:42,726 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193], 'cur_cost': nan}
2025-06-22 16:17:42,726 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 16:17:42,726 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:17:42,726 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [39, 44, 45] and ending with [65, 52, 63]
- Cost: 9948.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.890728476821192
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:17:42,726 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:17:49,624 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 52, 51, 50, 49, 48, 47, 46, 45, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6
  ],
  "modification_strategy": "This path prioritizes exploration of the two opportunity regions. It deviates early and significantly from the original path to achieve higher diversity. It explores a more comprehensive set of nodes, incorporating a 'backtracking' element to revisit certain sections and ensure thorough coverage while targeting unexplored regions.  The risk tolerance is set to a moderate value for efficient search.",
  "targeted_regions": [
    "Opportunity Region 1 (unspecified node indices - assumed based on problem context)",
    "Opportunity Region 2 (unspecified node indices - assumed based on problem context)"
  ]
}
```

2025-06-22 16:17:49,624 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:17:49,624 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15563.0, 路径: [39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 52, 51, 50, 49, 48, 47, 46, 45, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6]
2025-06-22 16:17:49,624 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 52, 51, 50, 49, 48, 47, 46, 45, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6], 'cur_cost': 15563.0}
2025-06-22 16:17:49,624 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 16:17:49,624 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:17:49,624 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [11, 47, 40] and ending with [58, 13, 19]
- Cost: 116630.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8760330578512396
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:17:49,624 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:17:56,917 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    11, 33, 52, 45, 2, 15, 28, 37, 54, 4, 21, 30, 49, 6, 17, 58, 3, 40, 51, 24, 1, 19, 50, 35, 13, 47, 44, 60, 5, 43, 26, 57, 9, 34, 10, 22, 38, 16, 29, 59, 27, 12, 36, 14, 42, 48, 8, 20, 32, 55, 23, 46, 56, 7, 39, 31, 41, 25, 18, 0, 53
  ],
  "modification_strategy": "This path generation strategy prioritizes diversity by employing a multi-pronged approach:\n\n1.  **Region Diversification:** The path attempts to visit nodes within all unexplored regions. It also intentionally incorporates nodes from the 'difficult to avoid regions' to get a broader perspective.\n2.  **Randomized Node Selection (within constraints):** Nodes are selected with a degree of randomness, especially within the 'Opportunity Regions' and 'Difficult Regions', to increase the chance of discovering less-traveled paths.\n3.  **Node Order Variation:**  Instead of a simple sequential addition, the path weaves through the graph in a less predictable way, changing direction frequently to increase path diversity.  The goal is to make this path significantly different from the original path, avoiding long stretches in the same general area.\n4.  **Heuristic Guidance (Quality):** The path maintains some quality considerations by still prioritizing a general sense of 'directionality'. Avoids completely chaotic and potentially extremely long paths by trying to move towards the end node while still embracing the diversification.  Nodes are chosen more for diversity than for pure cost reduction.",
  "targeted_regions": [
    "All unexplored regions.",
    "Opportunity regions.",
    "Difficult to avoid regions."
  ]
}
```
2025-06-22 16:17:56,919 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:17:56,919 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103608.0, 路径: [11, 33, 52, 45, 2, 15, 28, 37, 54, 4, 21, 30, 49, 6, 17, 58, 3, 40, 51, 24, 1, 19, 50, 35, 13, 47, 44, 60, 5, 43, 26, 57, 9, 34, 10, 22, 38, 16, 29, 59, 27, 12, 36, 14, 42, 48, 8, 20, 32, 55, 23, 46, 56, 7, 39, 31, 41, 25, 18, 0, 53]
2025-06-22 16:17:56,919 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [11, 33, 52, 45, 2, 15, 28, 37, 54, 4, 21, 30, 49, 6, 17, 58, 3, 40, 51, 24, 1, 19, 50, 35, 13, 47, 44, 60, 5, 43, 26, 57, 9, 34, 10, 22, 38, 16, 29, 59, 27, 12, 36, 14, 42, 48, 8, 20, 32, 55, 23, 46, 56, 7, 39, 31, 41, 25, 18, 0, 53], 'cur_cost': 103608.0}
2025-06-22 16:17:56,919 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 16:17:56,919 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:17:56,919 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [63, 21, 7] and ending with [19, 17, 38]
- Cost: 115326.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8883333333333333
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:17:56,922 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:18:01,633 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    63, 21, 5, 48, 12, 2, 35, 57, 4, 28, 10, 3, 50, 60, 65, 54, 18, 32, 42, 47, 19, 17, 38
  ],
  "modification_strategy": "Prioritized exploration of unexplored regions identified in the search context. Adjusted path segments to avoid difficult regions, favoring nodes leading towards the opportunity regions based on diversity and risk parameters. Primarily changed the path early on (after the initial two nodes) and inserted more nodes to explore new areas.",
  "targeted_regions": [
    "Unexplored regions (identified during the search)",
    "Opportunity regions (identified during the search)"
  ]
}
```

2025-06-22 16:18:01,635 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:18:01,635 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35343.0, 路径: [63, 21, 5, 48, 12, 2, 35, 57, 4, 28, 10, 3, 50, 60, 65, 54, 18, 32, 42, 47, 19, 17, 38]
2025-06-22 16:18:01,635 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [63, 21, 5, 48, 12, 2, 35, 57, 4, 28, 10, 3, 50, 60, 65, 54, 18, 32, 42, 47, 19, 17, 38], 'cur_cost': 35343.0}
2025-06-22 16:18:01,635 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 16:18:01,635 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:18:01,635 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:18:01,635 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 97618.0
2025-06-22 16:18:03,274 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 16:18:03,275 - ExploitationExpert - INFO - res_population_costs: [14984]
2025-06-22 16:18:03,275 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  8,  5,  1, 26, 29, 35, 36, 37, 32, 30, 34, 27, 31, 28, 25,
       33, 24, 12, 22, 18, 19, 21, 20, 13, 17, 14, 16, 23, 15, 11,  7,  3,
       46, 41, 40, 49, 47, 44, 45, 38, 48, 39, 50, 51, 42, 43, 58, 60, 62,
       56, 64, 59,  6,  4,  9, 54, 57, 53, 55, 61, 65, 52, 63, 10],
      dtype=int64)]
2025-06-22 16:18:03,276 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:18:03,276 - ExploitationExpert - INFO - populations: [{'tour': [39, 44, 48, 51, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63], 'cur_cost': 6540.0}, {'tour': [2, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193], 'cur_cost': nan}, {'tour': [39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 52, 51, 50, 49, 48, 47, 46, 45, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6], 'cur_cost': 15563.0}, {'tour': [11, 33, 52, 45, 2, 15, 28, 37, 54, 4, 21, 30, 49, 6, 17, 58, 3, 40, 51, 24, 1, 19, 50, 35, 13, 47, 44, 60, 5, 43, 26, 57, 9, 34, 10, 22, 38, 16, 29, 59, 27, 12, 36, 14, 42, 48, 8, 20, 32, 55, 23, 46, 56, 7, 39, 31, 41, 25, 18, 0, 53], 'cur_cost': 103608.0}, {'tour': [63, 21, 5, 48, 12, 2, 35, 57, 4, 28, 10, 3, 50, 60, 65, 54, 18, 32, 42, 47, 19, 17, 38], 'cur_cost': 35343.0}, {'tour': array([ 9, 47, 54, 59, 19,  5,  2,  1, 27, 29, 31,  4, 41, 28, 38,  3, 56,
        6, 18, 20, 53, 42, 32, 44, 64, 65, 57, 62, 17, 14, 35, 55, 25, 40,
       63, 52, 61, 36, 12,  7, 21, 15, 13, 39,  0, 30, 37, 33, 43, 24, 10,
       58, 46, 45, 22, 60, 23, 26, 16, 51, 34,  8, 49, 48, 11, 50]), 'cur_cost': 97618.0}, {'tour': [62, 42, 61, 39, 49, 40, 1, 6, 44, 36, 34, 57, 64, 38, 12, 11, 56, 65, 60, 29, 16, 26, 0, 22, 30, 20, 8, 59, 4, 24, 27, 14, 43, 18, 15, 58, 37, 9, 21, 35, 31, 52, 53, 50, 54, 28, 55, 5, 48, 10, 3, 32, 46, 23, 47, 19, 51, 7, 45, 13, 63, 25, 2, 41, 17, 33], 'cur_cost': 107087.0}, {'tour': [65, 39, 58, 30, 19, 11, 52, 32, 10, 49, 21, 55, 31, 45, 42, 14, 15, 1, 6, 37, 47, 26, 8, 12, 57, 20, 64, 61, 27, 51, 13, 50, 22, 33, 4, 56, 23, 63, 3, 9, 59, 54, 29, 40, 24, 48, 43, 35, 0, 25, 60, 18, 46, 44, 2, 17, 28, 5, 53, 7, 38, 36, 34, 16, 41, 62], 'cur_cost': 106772.0}, {'tour': [19, 54, 48, 31, 43, 46, 35, 56, 6, 21, 23, 47, 25, 11, 7, 41, 17, 57, 65, 45, 29, 51, 58, 5, 49, 36, 28, 20, 53, 15, 8, 62, 12, 3, 18, 14, 52, 22, 26, 13, 1, 33, 27, 38, 10, 39, 32, 50, 42, 40, 63, 60, 0, 55, 44, 24, 59, 16, 30, 64, 2, 37, 61, 4, 9, 34], 'cur_cost': 105775.0}, {'tour': [49, 10, 18, 15, 17, 39, 58, 40, 30, 24, 25, 33, 2, 53, 14, 35, 29, 1, 45, 38, 47, 44, 65, 63, 48, 34, 43, 28, 57, 16, 36, 50, 52, 9, 4, 64, 13, 27, 0, 11, 19, 21, 55, 37, 42, 46, 23, 8, 62, 31, 3, 56, 54, 5, 6, 60, 7, 20, 61, 22, 32, 59, 26, 51, 12, 41], 'cur_cost': 92926.0}]
2025-06-22 16:18:03,276 - ExploitationExpert - INFO - 局部搜索耗时: 1.64秒
2025-06-22 16:18:03,277 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-22 16:18:03,277 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 16:18:03,277 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 16:18:03,277 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:18:03,277 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:18:03,279 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 108376.0
2025-06-22 16:18:04,817 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 16:18:04,818 - ExploitationExpert - INFO - res_population_costs: [14984, 10250]
2025-06-22 16:18:04,818 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  8,  5,  1, 26, 29, 35, 36, 37, 32, 30, 34, 27, 31, 28, 25,
       33, 24, 12, 22, 18, 19, 21, 20, 13, 17, 14, 16, 23, 15, 11,  7,  3,
       46, 41, 40, 49, 47, 44, 45, 38, 48, 39, 50, 51, 42, 43, 58, 60, 62,
       56, 64, 59,  6,  4,  9, 54, 57, 53, 55, 61, 65, 52, 63, 10],
      dtype=int64), array([ 0, 11,  5,  6,  2,  4,  8, 10, 55, 56, 59, 65, 60, 54, 53, 52, 63,
       61, 62, 57, 64, 58, 41, 38, 44, 42, 43, 48, 46, 47, 50, 51, 45, 39,
       49, 40, 34, 36, 37, 26, 28, 29, 33, 32, 30, 35, 31, 25, 24, 27, 17,
       18, 15, 13, 23, 12, 22, 16, 19, 20, 21, 14,  9,  7,  3,  1],
      dtype=int64)]
2025-06-22 16:18:04,819 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:18:04,819 - ExploitationExpert - INFO - populations: [{'tour': [39, 44, 48, 51, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63], 'cur_cost': 6540.0}, {'tour': [2, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193], 'cur_cost': nan}, {'tour': [39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 52, 51, 50, 49, 48, 47, 46, 45, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6], 'cur_cost': 15563.0}, {'tour': [11, 33, 52, 45, 2, 15, 28, 37, 54, 4, 21, 30, 49, 6, 17, 58, 3, 40, 51, 24, 1, 19, 50, 35, 13, 47, 44, 60, 5, 43, 26, 57, 9, 34, 10, 22, 38, 16, 29, 59, 27, 12, 36, 14, 42, 48, 8, 20, 32, 55, 23, 46, 56, 7, 39, 31, 41, 25, 18, 0, 53], 'cur_cost': 103608.0}, {'tour': [63, 21, 5, 48, 12, 2, 35, 57, 4, 28, 10, 3, 50, 60, 65, 54, 18, 32, 42, 47, 19, 17, 38], 'cur_cost': 35343.0}, {'tour': array([ 9, 47, 54, 59, 19,  5,  2,  1, 27, 29, 31,  4, 41, 28, 38,  3, 56,
        6, 18, 20, 53, 42, 32, 44, 64, 65, 57, 62, 17, 14, 35, 55, 25, 40,
       63, 52, 61, 36, 12,  7, 21, 15, 13, 39,  0, 30, 37, 33, 43, 24, 10,
       58, 46, 45, 22, 60, 23, 26, 16, 51, 34,  8, 49, 48, 11, 50]), 'cur_cost': 97618.0}, {'tour': array([43, 48, 33,  4, 17, 41, 51, 63, 56, 57, 22, 39, 27, 52, 19,  3, 50,
       35, 38, 59, 64, 11, 15, 10, 26, 62, 34, 55, 28, 65, 42, 29, 46, 53,
        9,  7,  2, 40, 24, 21, 32, 20, 47, 23, 14,  0, 44, 37, 36, 61, 30,
       54, 58,  8,  5, 12, 25, 45,  1, 16, 49, 13, 18, 31, 60,  6]), 'cur_cost': 108376.0}, {'tour': [65, 39, 58, 30, 19, 11, 52, 32, 10, 49, 21, 55, 31, 45, 42, 14, 15, 1, 6, 37, 47, 26, 8, 12, 57, 20, 64, 61, 27, 51, 13, 50, 22, 33, 4, 56, 23, 63, 3, 9, 59, 54, 29, 40, 24, 48, 43, 35, 0, 25, 60, 18, 46, 44, 2, 17, 28, 5, 53, 7, 38, 36, 34, 16, 41, 62], 'cur_cost': 106772.0}, {'tour': [19, 54, 48, 31, 43, 46, 35, 56, 6, 21, 23, 47, 25, 11, 7, 41, 17, 57, 65, 45, 29, 51, 58, 5, 49, 36, 28, 20, 53, 15, 8, 62, 12, 3, 18, 14, 52, 22, 26, 13, 1, 33, 27, 38, 10, 39, 32, 50, 42, 40, 63, 60, 0, 55, 44, 24, 59, 16, 30, 64, 2, 37, 61, 4, 9, 34], 'cur_cost': 105775.0}, {'tour': [49, 10, 18, 15, 17, 39, 58, 40, 30, 24, 25, 33, 2, 53, 14, 35, 29, 1, 45, 38, 47, 44, 65, 63, 48, 34, 43, 28, 57, 16, 36, 50, 52, 9, 4, 64, 13, 27, 0, 11, 19, 21, 55, 37, 42, 46, 23, 8, 62, 31, 3, 56, 54, 5, 6, 60, 7, 20, 61, 22, 32, 59, 26, 51, 12, 41], 'cur_cost': 92926.0}]
2025-06-22 16:18:04,820 - ExploitationExpert - INFO - 局部搜索耗时: 1.54秒
2025-06-22 16:18:04,820 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-22 16:18:04,821 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 16:18:04,821 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 16:18:04,821 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:18:04,822 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:18:04,822 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 106580.0
2025-06-22 16:18:05,332 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 16:18:05,332 - ExploitationExpert - INFO - res_population_costs: [14984, 10250]
2025-06-22 16:18:05,332 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  8,  5,  1, 26, 29, 35, 36, 37, 32, 30, 34, 27, 31, 28, 25,
       33, 24, 12, 22, 18, 19, 21, 20, 13, 17, 14, 16, 23, 15, 11,  7,  3,
       46, 41, 40, 49, 47, 44, 45, 38, 48, 39, 50, 51, 42, 43, 58, 60, 62,
       56, 64, 59,  6,  4,  9, 54, 57, 53, 55, 61, 65, 52, 63, 10],
      dtype=int64), array([ 0, 11,  5,  6,  2,  4,  8, 10, 55, 56, 59, 65, 60, 54, 53, 52, 63,
       61, 62, 57, 64, 58, 41, 38, 44, 42, 43, 48, 46, 47, 50, 51, 45, 39,
       49, 40, 34, 36, 37, 26, 28, 29, 33, 32, 30, 35, 31, 25, 24, 27, 17,
       18, 15, 13, 23, 12, 22, 16, 19, 20, 21, 14,  9,  7,  3,  1],
      dtype=int64)]
2025-06-22 16:18:05,333 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:18:05,334 - ExploitationExpert - INFO - populations: [{'tour': [39, 44, 48, 51, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63], 'cur_cost': 6540.0}, {'tour': [2, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193], 'cur_cost': nan}, {'tour': [39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 52, 51, 50, 49, 48, 47, 46, 45, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6], 'cur_cost': 15563.0}, {'tour': [11, 33, 52, 45, 2, 15, 28, 37, 54, 4, 21, 30, 49, 6, 17, 58, 3, 40, 51, 24, 1, 19, 50, 35, 13, 47, 44, 60, 5, 43, 26, 57, 9, 34, 10, 22, 38, 16, 29, 59, 27, 12, 36, 14, 42, 48, 8, 20, 32, 55, 23, 46, 56, 7, 39, 31, 41, 25, 18, 0, 53], 'cur_cost': 103608.0}, {'tour': [63, 21, 5, 48, 12, 2, 35, 57, 4, 28, 10, 3, 50, 60, 65, 54, 18, 32, 42, 47, 19, 17, 38], 'cur_cost': 35343.0}, {'tour': array([ 9, 47, 54, 59, 19,  5,  2,  1, 27, 29, 31,  4, 41, 28, 38,  3, 56,
        6, 18, 20, 53, 42, 32, 44, 64, 65, 57, 62, 17, 14, 35, 55, 25, 40,
       63, 52, 61, 36, 12,  7, 21, 15, 13, 39,  0, 30, 37, 33, 43, 24, 10,
       58, 46, 45, 22, 60, 23, 26, 16, 51, 34,  8, 49, 48, 11, 50]), 'cur_cost': 97618.0}, {'tour': array([43, 48, 33,  4, 17, 41, 51, 63, 56, 57, 22, 39, 27, 52, 19,  3, 50,
       35, 38, 59, 64, 11, 15, 10, 26, 62, 34, 55, 28, 65, 42, 29, 46, 53,
        9,  7,  2, 40, 24, 21, 32, 20, 47, 23, 14,  0, 44, 37, 36, 61, 30,
       54, 58,  8,  5, 12, 25, 45,  1, 16, 49, 13, 18, 31, 60,  6]), 'cur_cost': 108376.0}, {'tour': array([12, 28, 21,  4, 41, 31, 36, 22, 43, 60, 59, 38, 10, 53, 19, 57,  6,
       65,  1, 40, 29, 61, 15, 30, 13, 33,  0, 64, 20,  9, 14, 45, 55, 48,
       26,  2,  5,  8, 11, 32, 63, 51, 46, 49, 35, 54, 34, 37, 42, 27, 52,
       62, 23,  3, 58, 16, 25, 50, 17, 56, 44, 39, 24, 18,  7, 47]), 'cur_cost': 106580.0}, {'tour': [19, 54, 48, 31, 43, 46, 35, 56, 6, 21, 23, 47, 25, 11, 7, 41, 17, 57, 65, 45, 29, 51, 58, 5, 49, 36, 28, 20, 53, 15, 8, 62, 12, 3, 18, 14, 52, 22, 26, 13, 1, 33, 27, 38, 10, 39, 32, 50, 42, 40, 63, 60, 0, 55, 44, 24, 59, 16, 30, 64, 2, 37, 61, 4, 9, 34], 'cur_cost': 105775.0}, {'tour': [49, 10, 18, 15, 17, 39, 58, 40, 30, 24, 25, 33, 2, 53, 14, 35, 29, 1, 45, 38, 47, 44, 65, 63, 48, 34, 43, 28, 57, 16, 36, 50, 52, 9, 4, 64, 13, 27, 0, 11, 19, 21, 55, 37, 42, 46, 23, 8, 62, 31, 3, 56, 54, 5, 6, 60, 7, 20, 61, 22, 32, 59, 26, 51, 12, 41], 'cur_cost': 92926.0}]
2025-06-22 16:18:05,335 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 16:18:05,335 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-22 16:18:05,335 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 16:18:05,336 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 16:18:05,336 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:18:05,336 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:18:05,336 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 117665.0
2025-06-22 16:18:05,867 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 16:18:05,867 - ExploitationExpert - INFO - res_population_costs: [14984, 10250, 10196]
2025-06-22 16:18:05,867 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  8,  5,  1, 26, 29, 35, 36, 37, 32, 30, 34, 27, 31, 28, 25,
       33, 24, 12, 22, 18, 19, 21, 20, 13, 17, 14, 16, 23, 15, 11,  7,  3,
       46, 41, 40, 49, 47, 44, 45, 38, 48, 39, 50, 51, 42, 43, 58, 60, 62,
       56, 64, 59,  6,  4,  9, 54, 57, 53, 55, 61, 65, 52, 63, 10],
      dtype=int64), array([ 0, 11,  5,  6,  2,  4,  8, 10, 55, 56, 59, 65, 60, 54, 53, 52, 63,
       61, 62, 57, 64, 58, 41, 38, 44, 42, 43, 48, 46, 47, 50, 51, 45, 39,
       49, 40, 34, 36, 37, 26, 28, 29, 33, 32, 30, 35, 31, 25, 24, 27, 17,
       18, 15, 13, 23, 12, 22, 16, 19, 20, 21, 14,  9,  7,  3,  1],
      dtype=int64), array([ 0, 10,  2,  8,  5,  1,  3,  7, 11,  9,  4,  6, 61, 59, 62, 54, 57,
       64, 60, 53, 63, 52, 65, 55, 56, 58, 41, 51, 38, 45, 44, 47, 49, 46,
       43, 48, 40, 42, 50, 39, 27, 37, 31, 33, 32, 29, 24, 25, 28, 26, 36,
       35, 30, 34, 16, 19, 21, 20, 14, 15, 17, 12, 18, 22, 13, 23],
      dtype=int64)]
2025-06-22 16:18:05,868 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:18:05,869 - ExploitationExpert - INFO - populations: [{'tour': [39, 44, 48, 51, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63], 'cur_cost': 6540.0}, {'tour': [2, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193], 'cur_cost': nan}, {'tour': [39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 52, 51, 50, 49, 48, 47, 46, 45, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6], 'cur_cost': 15563.0}, {'tour': [11, 33, 52, 45, 2, 15, 28, 37, 54, 4, 21, 30, 49, 6, 17, 58, 3, 40, 51, 24, 1, 19, 50, 35, 13, 47, 44, 60, 5, 43, 26, 57, 9, 34, 10, 22, 38, 16, 29, 59, 27, 12, 36, 14, 42, 48, 8, 20, 32, 55, 23, 46, 56, 7, 39, 31, 41, 25, 18, 0, 53], 'cur_cost': 103608.0}, {'tour': [63, 21, 5, 48, 12, 2, 35, 57, 4, 28, 10, 3, 50, 60, 65, 54, 18, 32, 42, 47, 19, 17, 38], 'cur_cost': 35343.0}, {'tour': array([ 9, 47, 54, 59, 19,  5,  2,  1, 27, 29, 31,  4, 41, 28, 38,  3, 56,
        6, 18, 20, 53, 42, 32, 44, 64, 65, 57, 62, 17, 14, 35, 55, 25, 40,
       63, 52, 61, 36, 12,  7, 21, 15, 13, 39,  0, 30, 37, 33, 43, 24, 10,
       58, 46, 45, 22, 60, 23, 26, 16, 51, 34,  8, 49, 48, 11, 50]), 'cur_cost': 97618.0}, {'tour': array([43, 48, 33,  4, 17, 41, 51, 63, 56, 57, 22, 39, 27, 52, 19,  3, 50,
       35, 38, 59, 64, 11, 15, 10, 26, 62, 34, 55, 28, 65, 42, 29, 46, 53,
        9,  7,  2, 40, 24, 21, 32, 20, 47, 23, 14,  0, 44, 37, 36, 61, 30,
       54, 58,  8,  5, 12, 25, 45,  1, 16, 49, 13, 18, 31, 60,  6]), 'cur_cost': 108376.0}, {'tour': array([12, 28, 21,  4, 41, 31, 36, 22, 43, 60, 59, 38, 10, 53, 19, 57,  6,
       65,  1, 40, 29, 61, 15, 30, 13, 33,  0, 64, 20,  9, 14, 45, 55, 48,
       26,  2,  5,  8, 11, 32, 63, 51, 46, 49, 35, 54, 34, 37, 42, 27, 52,
       62, 23,  3, 58, 16, 25, 50, 17, 56, 44, 39, 24, 18,  7, 47]), 'cur_cost': 106580.0}, {'tour': array([40, 21, 45, 41,  6, 39, 35, 25, 61, 19, 16,  3, 49, 47, 50, 10, 18,
       64, 29, 13, 56, 26, 38,  0, 37, 44, 52,  7,  8, 62,  5, 20, 54,  4,
       43, 36, 30, 55, 24,  9, 63, 27, 11, 17, 60, 28, 23, 58, 34,  2, 15,
       22, 32, 14, 51, 12, 48, 59, 65, 42, 33, 53, 31, 57, 46,  1]), 'cur_cost': 117665.0}, {'tour': [49, 10, 18, 15, 17, 39, 58, 40, 30, 24, 25, 33, 2, 53, 14, 35, 29, 1, 45, 38, 47, 44, 65, 63, 48, 34, 43, 28, 57, 16, 36, 50, 52, 9, 4, 64, 13, 27, 0, 11, 19, 21, 55, 37, 42, 46, 23, 8, 62, 31, 3, 56, 54, 5, 6, 60, 7, 20, 61, 22, 32, 59, 26, 51, 12, 41], 'cur_cost': 92926.0}]
2025-06-22 16:18:05,870 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-22 16:18:05,871 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-22 16:18:05,871 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 16:18:05,871 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 16:18:05,871 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:18:05,871 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:18:05,872 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 107836.0
2025-06-22 16:18:06,376 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 16:18:06,376 - ExploitationExpert - INFO - res_population_costs: [14984, 10250, 10196]
2025-06-22 16:18:06,376 - ExploitationExpert - INFO - res_populations: [array([ 0,  2,  8,  5,  1, 26, 29, 35, 36, 37, 32, 30, 34, 27, 31, 28, 25,
       33, 24, 12, 22, 18, 19, 21, 20, 13, 17, 14, 16, 23, 15, 11,  7,  3,
       46, 41, 40, 49, 47, 44, 45, 38, 48, 39, 50, 51, 42, 43, 58, 60, 62,
       56, 64, 59,  6,  4,  9, 54, 57, 53, 55, 61, 65, 52, 63, 10],
      dtype=int64), array([ 0, 11,  5,  6,  2,  4,  8, 10, 55, 56, 59, 65, 60, 54, 53, 52, 63,
       61, 62, 57, 64, 58, 41, 38, 44, 42, 43, 48, 46, 47, 50, 51, 45, 39,
       49, 40, 34, 36, 37, 26, 28, 29, 33, 32, 30, 35, 31, 25, 24, 27, 17,
       18, 15, 13, 23, 12, 22, 16, 19, 20, 21, 14,  9,  7,  3,  1],
      dtype=int64), array([ 0, 10,  2,  8,  5,  1,  3,  7, 11,  9,  4,  6, 61, 59, 62, 54, 57,
       64, 60, 53, 63, 52, 65, 55, 56, 58, 41, 51, 38, 45, 44, 47, 49, 46,
       43, 48, 40, 42, 50, 39, 27, 37, 31, 33, 32, 29, 24, 25, 28, 26, 36,
       35, 30, 34, 16, 19, 21, 20, 14, 15, 17, 12, 18, 22, 13, 23],
      dtype=int64)]
2025-06-22 16:18:06,382 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:18:06,382 - ExploitationExpert - INFO - populations: [{'tour': [39, 44, 48, 51, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63], 'cur_cost': 6540.0}, {'tour': [2, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193], 'cur_cost': nan}, {'tour': [39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 52, 51, 50, 49, 48, 47, 46, 45, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6], 'cur_cost': 15563.0}, {'tour': [11, 33, 52, 45, 2, 15, 28, 37, 54, 4, 21, 30, 49, 6, 17, 58, 3, 40, 51, 24, 1, 19, 50, 35, 13, 47, 44, 60, 5, 43, 26, 57, 9, 34, 10, 22, 38, 16, 29, 59, 27, 12, 36, 14, 42, 48, 8, 20, 32, 55, 23, 46, 56, 7, 39, 31, 41, 25, 18, 0, 53], 'cur_cost': 103608.0}, {'tour': [63, 21, 5, 48, 12, 2, 35, 57, 4, 28, 10, 3, 50, 60, 65, 54, 18, 32, 42, 47, 19, 17, 38], 'cur_cost': 35343.0}, {'tour': array([ 9, 47, 54, 59, 19,  5,  2,  1, 27, 29, 31,  4, 41, 28, 38,  3, 56,
        6, 18, 20, 53, 42, 32, 44, 64, 65, 57, 62, 17, 14, 35, 55, 25, 40,
       63, 52, 61, 36, 12,  7, 21, 15, 13, 39,  0, 30, 37, 33, 43, 24, 10,
       58, 46, 45, 22, 60, 23, 26, 16, 51, 34,  8, 49, 48, 11, 50]), 'cur_cost': 97618.0}, {'tour': array([43, 48, 33,  4, 17, 41, 51, 63, 56, 57, 22, 39, 27, 52, 19,  3, 50,
       35, 38, 59, 64, 11, 15, 10, 26, 62, 34, 55, 28, 65, 42, 29, 46, 53,
        9,  7,  2, 40, 24, 21, 32, 20, 47, 23, 14,  0, 44, 37, 36, 61, 30,
       54, 58,  8,  5, 12, 25, 45,  1, 16, 49, 13, 18, 31, 60,  6]), 'cur_cost': 108376.0}, {'tour': array([12, 28, 21,  4, 41, 31, 36, 22, 43, 60, 59, 38, 10, 53, 19, 57,  6,
       65,  1, 40, 29, 61, 15, 30, 13, 33,  0, 64, 20,  9, 14, 45, 55, 48,
       26,  2,  5,  8, 11, 32, 63, 51, 46, 49, 35, 54, 34, 37, 42, 27, 52,
       62, 23,  3, 58, 16, 25, 50, 17, 56, 44, 39, 24, 18,  7, 47]), 'cur_cost': 106580.0}, {'tour': array([40, 21, 45, 41,  6, 39, 35, 25, 61, 19, 16,  3, 49, 47, 50, 10, 18,
       64, 29, 13, 56, 26, 38,  0, 37, 44, 52,  7,  8, 62,  5, 20, 54,  4,
       43, 36, 30, 55, 24,  9, 63, 27, 11, 17, 60, 28, 23, 58, 34,  2, 15,
       22, 32, 14, 51, 12, 48, 59, 65, 42, 33, 53, 31, 57, 46,  1]), 'cur_cost': 117665.0}, {'tour': array([45, 12,  3, 42,  1, 30, 13, 16, 57, 24, 27, 40, 56, 53, 28, 31, 64,
       25,  8, 46,  9, 44, 62, 19, 52,  6, 63,  4, 36, 22, 38, 49, 54, 32,
       60, 58,  0,  7, 55, 41, 61, 23, 35, 39, 18, 51, 37, 20, 34, 50, 59,
       65, 17, 11, 21, 47, 33, 14,  5, 48, 29, 26, 10,  2, 15, 43]), 'cur_cost': 107836.0}]
2025-06-22 16:18:06,384 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 16:18:06,384 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-22 16:18:06,385 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 16:18:06,385 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [39, 44, 48, 51, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 52, 63], 'cur_cost': 6540.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [2, 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193], 'cur_cost': nan}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 52, 51, 50, 49, 48, 47, 46, 45, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6], 'cur_cost': 15563.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [11, 33, 52, 45, 2, 15, 28, 37, 54, 4, 21, 30, 49, 6, 17, 58, 3, 40, 51, 24, 1, 19, 50, 35, 13, 47, 44, 60, 5, 43, 26, 57, 9, 34, 10, 22, 38, 16, 29, 59, 27, 12, 36, 14, 42, 48, 8, 20, 32, 55, 23, 46, 56, 7, 39, 31, 41, 25, 18, 0, 53], 'cur_cost': 103608.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [63, 21, 5, 48, 12, 2, 35, 57, 4, 28, 10, 3, 50, 60, 65, 54, 18, 32, 42, 47, 19, 17, 38], 'cur_cost': 35343.0}}]
2025-06-22 16:18:06,385 - __main__ - INFO - 进化阶段完成
2025-06-22 16:18:06,385 - __main__ - INFO - 开始评估阶段
2025-06-22 16:18:06,385 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 16:18:06,386 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 16:18:06,386 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 0/5
- Population changes:
  * Cost: min 9948.0->6540.0, mean 78550.2->nan
  * Diversity: 0.7338461538461538->0.8868940754039497
  * Elite solutions: 3->1

## Strategy Performance:
- Explore: 5 individuals, success rate 60.0%, avg improvement nan
- Exploit: 5 individuals, success rate 40.0%, avg improvement -2933.8
- Overall improvement: 3408.0

## Historical Trends:
No historical data available

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 16:18:13,865 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage - Initial Optimization & Diversity Increase",
    "features": {
      "iteration": "0/5 (very early stage)",
      "cost": "Significant initial cost reduction from 9948.0 to 6540.0, showing positive progress.",
      "diversity": "Increased diversity from 0.73 to 0.89, a positive sign that the algorithm is exploring a wider solution space.",
      "elite_solutions": "Reduced number of elite solutions from 3 to 1.  This might indicate that the initial elite solutions are being refined or that the definition of elite is strict"
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "60.0% (Good)",
      "avg_improvement": "nan (Cannot reliably assess improvement at this stage without historical data)",
      "assessment": "Explore appears to be performing reasonably well, finding new solutions that lead to higher quality (measured in cost reduction)."
    },
    "exploit": {
      "success_rate": "40.0% (Needs Improvement)",
      "avg_improvement": "-2933.8 (Negative Improvement)",
      "assessment": "Exploit shows a lower success rate and a negative average improvement, suggesting it's not refining solutions effectively in this iteration.  It might be over-aggressive or exploiting in a sub-optimal region."
    },
    "overall_improvement": {
        "value": "3408.0 (Positive)",
        "assessment": "Overall improvement is positive (cost reduction), indicating the evolutionary process is making progress despite the issues with exploitation."
    }
  },
  "balance_state": {
    "assessment": "Potentially imbalanced towards exploration. The high exploration success rate and negative exploitation improvement suggest the algorithm is finding new regions of the solution space, but it is having difficulty refining the best found so far.",
    "adjustment_needs": "Prioritize refining the best current solution(s).  The high diversity also suggests more exploitation might be needed, especially given the relatively small number of iterations."
  },
  "recommendations": {
    "general": "Focus on balancing the exploration/exploitation strategies to get the best results.  Consider adding some level of exploitation in the next iteration.",
    "specific": [
      "Increase the number of individuals used for exploitation, in the next iteration.",
      "Refine the exploitation strategy.  For example, consider using smaller mutation steps, or applying exploitation to a more diverse set of solutions (but more carefully selected)",
      "Review the definition of an 'elite' solution. Perhaps adjust its criteria to allow some more solutions to be considered elite for the exploitation phase",
      "Monitor the population's mean cost across iterations, and watch for signs of divergence or convergence. A stable, low cost is the ultimate goal.",
        "Consider increasing the total population size in later iterations, to provide the best solutions for the exploration and exploitation phases. "
    ]
  }
}
```

2025-06-22 16:18:13,885 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 16:18:13,885 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage - Initial Optimization & Diversity Increase",
    "features": {
      "iteration": "0/5 (very early stage)",
      "cost": "Significant initial cost reduction from 9948.0 to 6540.0, showing positive progress.",
      "diversity": "Increased diversity from 0.73 to 0.89, a positive sign that the algorithm is exploring a wider solution space.",
      "elite_solutions": "Reduced number of elite solutions from 3 to 1.  This might indicate that the initial elite solutions are being refined or that the definition of elite is strict"
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "60.0% (Good)",
      "avg_improvement": "nan (Cannot reliably assess improvement at this stage without historical data)",
      "assessment": "Explore appears to be performing reasonably well, finding new solutions that lead to higher quality (measured in cost reduction)."
    },
    "exploit": {
      "success_rate": "40.0% (Needs Improvement)",
      "avg_improvement": "-2933.8 (Negative Improvement)",
      "assessment": "Exploit shows a lower success rate and a negative average improvement, suggesting it's not refining solutions effectively in this iteration.  It might be over-aggressive or exploiting in a sub-optimal region."
    },
    "overall_improvement": {
        "value": "3408.0 (Positive)",
        "assessment": "Overall improvement is positive (cost reduction), indicating the evolutionary process is making progress despite the issues with exploitation."
    }
  },
  "balance_state": {
    "assessment": "Potentially imbalanced towards exploration. The high exploration success rate and negative exploitation improvement suggest the algorithm is finding new regions of the solution space, but it is having difficulty refining the best found so far.",
    "adjustment_needs": "Prioritize refining the best current solution(s).  The high diversity also suggests more exploitation might be needed, especially given the relatively small number of iterations."
  },
  "recommendations": {
    "general": "Focus on balancing the exploration/exploitation strategies to get the best results.  Consider adding some level of exploitation in the next iteration.",
    "specific": [
      "Increase the number of individuals used for exploitation, in the next iteration.",
      "Refine the exploitation strategy.  For example, consider using smaller mutation steps, or applying exploitation to a more diverse set of solutions (but more carefully selected)",
      "Review the definition of an 'elite' solution. Perhaps adjust its criteria to allow some more solutions to be considered elite for the exploitation phase",
      "Monitor the population's mean cost across iterations, and watch for signs of divergence or convergence. A stable, low cost is the ultimate goal.",
        "Consider increasing the total population size in later iterations, to provide the best solutions for the exploration and exploitation phases. "
    ]
  }
}
```

2025-06-22 16:18:13,886 - __main__ - INFO - 评估阶段完成
2025-06-22 16:18:13,887 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage - Initial Optimization & Diversity Increase",
    "features": {
      "iteration": "0/5 (very early stage)",
      "cost": "Significant initial cost reduction from 9948.0 to 6540.0, showing positive progress.",
      "diversity": "Increased diversity from 0.73 to 0.89, a positive sign that the algorithm is exploring a wider solution space.",
      "elite_solutions": "Reduced number of elite solutions from 3 to 1.  This might indicate that the initial elite solutions are being refined or that the definition of elite is strict"
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "60.0% (Good)",
      "avg_improvement": "nan (Cannot reliably assess improvement at this stage without historical data)",
      "assessment": "Explore appears to be performing reasonably well, finding new solutions that lead to higher quality (measured in cost reduction)."
    },
    "exploit": {
      "success_rate": "40.0% (Needs Improvement)",
      "avg_improvement": "-2933.8 (Negative Improvement)",
      "assessment": "Exploit shows a lower success rate and a negative average improvement, suggesting it's not refining solutions effectively in this iteration.  It might be over-aggressive or exploiting in a sub-optimal region."
    },
    "overall_improvement": {
        "value": "3408.0 (Positive)",
        "assessment": "Overall improvement is positive (cost reduction), indicating the evolutionary process is making progress despite the issues with exploitation."
    }
  },
  "balance_state": {
    "assessment": "Potentially imbalanced towards exploration. The high exploration success rate and negative exploitation improvement suggest the algorithm is finding new regions of the solution space, but it is having difficulty refining the best found so far.",
    "adjustment_needs": "Prioritize refining the best current solution(s).  The high diversity also suggests more exploitation might be needed, especially given the relatively small number of iterations."
  },
  "recommendations": {
    "general": "Focus on balancing the exploration/exploitation strategies to get the best results.  Consider adding some level of exploitation in the next iteration.",
    "specific": [
      "Increase the number of individuals used for exploitation, in the next iteration.",
      "Refine the exploitation strategy.  For example, consider using smaller mutation steps, or applying exploitation to a more diverse set of solutions (but more carefully selected)",
      "Review the definition of an 'elite' solution. Perhaps adjust its criteria to allow some more solutions to be considered elite for the exploitation phase",
      "Monitor the population's mean cost across iterations, and watch for signs of divergence or convergence. A stable, low cost is the ultimate goal.",
        "Consider increasing the total population size in later iterations, to provide the best solutions for the exploration and exploitation phases. "
    ]
  }
}
```

2025-06-22 16:18:13,887 - __main__ - INFO - 当前最佳适应度: 6540.0
2025-06-22 16:18:13,888 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-22 16:18:13,888 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-22 16:18:13,889 - __main__ - INFO - 开始分析阶段
2025-06-22 16:18:13,889 - StatsExpert - INFO - 开始统计分析
2025-06-22 16:18:13,896 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 6540.0, 'max': 117665.0, 'mean': nan, 'std': nan}, 'diversity': 0.9919191919191916, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 16:18:13,897 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 6540.0, 'max': 117665.0, 'mean': nan, 'std': nan}, 'diversity_level': 0.9919191919191916, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 16:18:13,897 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 16:18:13,897 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 16:18:13,897 - PathExpert - INFO - 开始路径结构分析
2025-06-22 16:18:13,898 - path_structure_analyzer - ERROR - 索引越界: city1=64, city2=67, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,898 - path_structure_analyzer - ERROR - 索引越界: city1=67, city2=70, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,898 - path_structure_analyzer - ERROR - 索引越界: city1=70, city2=73, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,898 - path_structure_analyzer - ERROR - 索引越界: city1=73, city2=76, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,898 - path_structure_analyzer - ERROR - 索引越界: city1=76, city2=79, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,898 - path_structure_analyzer - ERROR - 索引越界: city1=79, city2=82, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,899 - path_structure_analyzer - ERROR - 索引越界: city1=82, city2=85, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,899 - path_structure_analyzer - ERROR - 索引越界: city1=85, city2=88, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,899 - path_structure_analyzer - ERROR - 索引越界: city1=88, city2=91, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,900 - path_structure_analyzer - ERROR - 索引越界: city1=91, city2=94, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,900 - path_structure_analyzer - ERROR - 索引越界: city1=94, city2=97, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,900 - path_structure_analyzer - ERROR - 索引越界: city1=97, city2=100, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,900 - path_structure_analyzer - ERROR - 索引越界: city1=100, city2=103, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,900 - path_structure_analyzer - ERROR - 索引越界: city1=103, city2=106, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,900 - path_structure_analyzer - ERROR - 索引越界: city1=106, city2=109, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,900 - path_structure_analyzer - ERROR - 索引越界: city1=109, city2=112, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,900 - path_structure_analyzer - ERROR - 索引越界: city1=112, city2=115, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,901 - path_structure_analyzer - ERROR - 索引越界: city1=115, city2=118, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,901 - path_structure_analyzer - ERROR - 索引越界: city1=118, city2=121, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,902 - path_structure_analyzer - ERROR - 索引越界: city1=121, city2=124, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,902 - path_structure_analyzer - ERROR - 索引越界: city1=124, city2=127, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,902 - path_structure_analyzer - ERROR - 索引越界: city1=127, city2=130, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,902 - path_structure_analyzer - ERROR - 索引越界: city1=130, city2=133, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,902 - path_structure_analyzer - ERROR - 索引越界: city1=133, city2=136, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,902 - path_structure_analyzer - ERROR - 索引越界: city1=136, city2=139, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,902 - path_structure_analyzer - ERROR - 索引越界: city1=139, city2=142, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,903 - path_structure_analyzer - ERROR - 索引越界: city1=142, city2=145, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,903 - path_structure_analyzer - ERROR - 索引越界: city1=145, city2=148, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,903 - path_structure_analyzer - ERROR - 索引越界: city1=148, city2=151, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,903 - path_structure_analyzer - ERROR - 索引越界: city1=151, city2=154, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,903 - path_structure_analyzer - ERROR - 索引越界: city1=154, city2=157, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,903 - path_structure_analyzer - ERROR - 索引越界: city1=157, city2=160, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,903 - path_structure_analyzer - ERROR - 索引越界: city1=160, city2=163, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,904 - path_structure_analyzer - ERROR - 索引越界: city1=163, city2=166, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,904 - path_structure_analyzer - ERROR - 索引越界: city1=166, city2=169, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,904 - path_structure_analyzer - ERROR - 索引越界: city1=169, city2=172, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,904 - path_structure_analyzer - ERROR - 索引越界: city1=172, city2=175, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,905 - path_structure_analyzer - ERROR - 索引越界: city1=175, city2=178, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,905 - path_structure_analyzer - ERROR - 索引越界: city1=178, city2=181, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,905 - path_structure_analyzer - ERROR - 索引越界: city1=181, city2=184, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,905 - path_structure_analyzer - ERROR - 索引越界: city1=184, city2=187, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,905 - path_structure_analyzer - ERROR - 索引越界: city1=187, city2=190, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,905 - path_structure_analyzer - ERROR - 索引越界: city1=190, city2=193, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,906 - path_structure_analyzer - ERROR - 索引越界: city1=193, city2=2, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,908 - path_structure_analyzer - ERROR - 索引越界: city1=64, city2=67, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,908 - path_structure_analyzer - ERROR - 索引越界: city1=67, city2=70, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,908 - path_structure_analyzer - ERROR - 索引越界: city1=70, city2=73, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,908 - path_structure_analyzer - ERROR - 索引越界: city1=73, city2=76, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,908 - path_structure_analyzer - ERROR - 索引越界: city1=76, city2=79, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,908 - path_structure_analyzer - ERROR - 索引越界: city1=79, city2=82, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,909 - path_structure_analyzer - ERROR - 索引越界: city1=82, city2=85, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,909 - path_structure_analyzer - ERROR - 索引越界: city1=85, city2=88, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,909 - path_structure_analyzer - ERROR - 索引越界: city1=88, city2=91, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,909 - path_structure_analyzer - ERROR - 索引越界: city1=91, city2=94, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,909 - path_structure_analyzer - ERROR - 索引越界: city1=94, city2=97, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,909 - path_structure_analyzer - ERROR - 索引越界: city1=97, city2=100, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,910 - path_structure_analyzer - ERROR - 索引越界: city1=100, city2=103, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,910 - path_structure_analyzer - ERROR - 索引越界: city1=103, city2=106, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,910 - path_structure_analyzer - ERROR - 索引越界: city1=106, city2=109, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,910 - path_structure_analyzer - ERROR - 索引越界: city1=109, city2=112, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,910 - path_structure_analyzer - ERROR - 索引越界: city1=112, city2=115, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,911 - path_structure_analyzer - ERROR - 索引越界: city1=115, city2=118, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,911 - path_structure_analyzer - ERROR - 索引越界: city1=118, city2=121, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,911 - path_structure_analyzer - ERROR - 索引越界: city1=121, city2=124, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,911 - path_structure_analyzer - ERROR - 索引越界: city1=124, city2=127, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,911 - path_structure_analyzer - ERROR - 索引越界: city1=127, city2=130, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,911 - path_structure_analyzer - ERROR - 索引越界: city1=130, city2=133, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,911 - path_structure_analyzer - ERROR - 索引越界: city1=133, city2=136, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,912 - path_structure_analyzer - ERROR - 索引越界: city1=136, city2=139, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,912 - path_structure_analyzer - ERROR - 索引越界: city1=139, city2=142, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,912 - path_structure_analyzer - ERROR - 索引越界: city1=142, city2=145, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,912 - path_structure_analyzer - ERROR - 索引越界: city1=145, city2=148, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,912 - path_structure_analyzer - ERROR - 索引越界: city1=148, city2=151, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,912 - path_structure_analyzer - ERROR - 索引越界: city1=151, city2=154, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,913 - path_structure_analyzer - ERROR - 索引越界: city1=154, city2=157, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,913 - path_structure_analyzer - ERROR - 索引越界: city1=157, city2=160, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,913 - path_structure_analyzer - ERROR - 索引越界: city1=160, city2=163, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,913 - path_structure_analyzer - ERROR - 索引越界: city1=163, city2=166, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,913 - path_structure_analyzer - ERROR - 索引越界: city1=166, city2=169, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,913 - path_structure_analyzer - ERROR - 索引越界: city1=169, city2=172, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,913 - path_structure_analyzer - ERROR - 索引越界: city1=172, city2=175, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,914 - path_structure_analyzer - ERROR - 索引越界: city1=175, city2=178, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,914 - path_structure_analyzer - ERROR - 索引越界: city1=178, city2=181, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,914 - path_structure_analyzer - ERROR - 索引越界: city1=181, city2=184, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,914 - path_structure_analyzer - ERROR - 索引越界: city1=184, city2=187, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,914 - path_structure_analyzer - ERROR - 索引越界: city1=187, city2=190, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,914 - path_structure_analyzer - ERROR - 索引越界: city1=190, city2=193, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,915 - path_structure_analyzer - ERROR - 索引越界: city1=193, city2=2, distance_matrix.shape=(66, 66)
2025-06-22 16:18:13,918 - PathExpert - INFO - 路径结构分析完成
2025-06-22 16:18:13,918 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [], 'low_frequency_edges': [{'edge': '(49, 50)', 'frequency': 0.2}, {'edge': '(53, 54)', 'frequency': 0.2}, {'edge': '(54, 55)', 'frequency': 0.2}, {'edge': '(55, 56)', 'frequency': 0.2}, {'edge': '(56, 57)', 'frequency': 0.3}, {'edge': '(57, 58)', 'frequency': 0.2}, {'edge': '(58, 59)', 'frequency': 0.2}, {'edge': '(59, 60)', 'frequency': 0.2}, {'edge': '(60, 61)', 'frequency': 0.2}, {'edge': '(61, 62)', 'frequency': 0.2}, {'edge': '(64, 65)', 'frequency': 0.3}, {'edge': '(65, 52)', 'frequency': 0.2}, {'edge': '(2, 1)', 'frequency': 0.2}, {'edge': '(13, 16)', 'frequency': 0.2}, {'edge': '(28, 31)', 'frequency': 0.2}, {'edge': '(34, 37)', 'frequency': 0.2}, {'edge': '(46, 49)', 'frequency': 0.2}, {'edge': '(49, 48)', 'frequency': 0.2}, {'edge': '(46, 45)', 'frequency': 0.2}, {'edge': '(37, 36)', 'frequency': 0.2}, {'edge': '(6, 39)', 'frequency': 0.2}, {'edge': '(2, 15)', 'frequency': 0.3}, {'edge': '(54, 4)', 'frequency': 0.2}, {'edge': '(50, 35)', 'frequency': 0.2}, {'edge': '(22, 38)', 'frequency': 0.2}, {'edge': '(5, 48)', 'frequency': 0.2}, {'edge': '(3, 50)', 'frequency': 0.2}, {'edge': '(4, 41)', 'frequency': 0.2}, {'edge': '(27, 52)', 'frequency': 0.2}, {'edge': '(65, 42)', 'frequency': 0.2}, {'edge': '(36, 22)', 'frequency': 0.2}, {'edge': '(1, 40)', 'frequency': 0.2}, {'edge': '(30, 13)', 'frequency': 0.2}, {'edge': '(59, 65)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [26, 62, 34, 55, 28, 65, 42, 29, 46, 53], 'cost': 24749.0, 'size': 10}, {'region': [65, 42, 33, 53, 31, 57, 46, 1, 40], 'cost': 21743.0, 'size': 9}, {'region': [8, 46, 9, 44, 62], 'cost': 10266.0, 'size': 5}, {'region': [53, 42, 32, 44, 64], 'cost': 10023.0, 'size': 5}, {'region': [3, 50, 35, 38, 59], 'cost': 9620.0, 'size': 5}]}
2025-06-22 16:18:13,918 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 16:18:13,919 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 16:18:13,919 - EliteExpert - INFO - 开始精英解分析
2025-06-22 16:18:13,920 - EliteExpert - INFO - 精英解分析完成
2025-06-22 16:18:13,920 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': -3656.0, 'avg_gap': nan}, 'structure_gap': {'unique_elite_edges': 149, 'unique_pop_edges': 502, 'common_edges': 28}}, 'elite_diversity': {'diversity_score': 0.7727272727272727}}
2025-06-22 16:18:13,920 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 16:18:13,920 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 16:18:13,920 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 16:18:13,920 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=6540.0, max=117665.0, mean=nan, std=nan
- Diversity: 0.9919191919191916, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 5 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 16:18:23,307 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Likely very high. The min and max cost span a large range (6540.0 to 117665.0) which suggests a highly varied landscape. However, the extremely low number of high-quality edges and common subpaths suggest a highly disconnected and fragmented structure rather than classical ruggedness with readily identifiable basins. ",
    "modality": "Unknown but likely high or very high. Given the wide cost range and the identified '5 difficult regions', it suggests multiple potential local optima. The complete absence of converged elites makes definitive modality assessment difficult, however.",
    "structure": "Fragmented and sparse. The zero values for high-quality edges and common subpaths strongly indicate a lack of strong connectivity within the search space. This makes identifying and leveraging existing high-performing structures challenging.",
    "cost_distribution_implications": "The mean and standard deviation being NaN, combined with the vast cost difference, implies that the current population does not have a meaningful central tendency or cost variation, hinting at either a very small number of individuals being effective or a very poor exploration of the search space."
  },
  "population_state": {
    "diversity": "Very high (0.9919). This indicates the population covers a wide area of the search space, which is positive for exploration but can hinder convergence if the search direction is unclear.",
    "convergence": "Complete lack of convergence (0.0). No solutions are identified as elite (with fixed nodes) which is expected given the cost spread. The population is not focusing on any particular regions.",
    "status": "The population is in a state of extremely high exploration and lack of exploitation. There is no identified elite status and lack of convergence is observed. This is normal in the beginning stages of a run, but should be expected. The population is also poorly focused towards the optimal solution."
  },
  "difficult_regions": {
    "description": "5 difficult regions identified. This confirms the assumption of multiple local optima or areas of high complexity.",
    "challenges": "These regions likely represent areas where the optimization process gets stuck or takes a long time to escape. There may also be issues with the data or setup, such as badly constructed solutions. Navigating and escaping from these regions is critical for finding better solutions."
  },
  "opportunity_regions": {
    "description": "0 opportunity regions identified. This doesn't mean they don't exist, but no specific areas of promising solutions have been identified yet. This is likely related to the population's highly diverse nature, preventing the identification of any strong trends in the current population.",
    "potential": "The lack of known good solutions (lack of opportunity regions) suggests the algorithm hasn't yet found/explored such an area, or the problem itself might not have any clear patterns of success (or a proper fitness function)."
  },
  "evolution_direction": {
    "strategy": "Focus on exploitation within the identified difficult regions. Because of high diversity and lack of convergence, the current strategy should be about exploring the challenging areas more effectively, or focus on a different subset of the input data.",
    "recommendations": [
      "Implement strategies to escape difficult regions.  Consider techniques like:  (1) mutation with large jumps, (2) simulated annealing, (3) restart with diverse population.",
      "Reduce the diversity to focus on specific areas. Consider this as the second strategy only if the first one does not converge within reasonable time.",
      "Inspect individual solutions: Analyze the components of solutions in difficult regions to understand *why* they're difficult.  Look for commonalities or bottlenecks.",
      "Experiment with different mutation rates and selection pressure to balance exploration and exploitation.",
      "Adjust parameters to increase the population size, as 10 individuals may be insufficient to sufficiently explore the search space."
    ]
  }
}
```
2025-06-22 16:18:23,308 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 16:18:23,308 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Likely very high. The min and max cost span a large range (6540.0 to 117665.0) which suggests a highly varied landscape. However, the extremely low number of high-quality edges and common subpaths suggest a highly disconnected and fragmented structure rather than classical ruggedness with readily identifiable basins. ', 'modality': "Unknown but likely high or very high. Given the wide cost range and the identified '5 difficult regions', it suggests multiple potential local optima. The complete absence of converged elites makes definitive modality assessment difficult, however.", 'structure': 'Fragmented and sparse. The zero values for high-quality edges and common subpaths strongly indicate a lack of strong connectivity within the search space. This makes identifying and leveraging existing high-performing structures challenging.', 'cost_distribution_implications': 'The mean and standard deviation being NaN, combined with the vast cost difference, implies that the current population does not have a meaningful central tendency or cost variation, hinting at either a very small number of individuals being effective or a very poor exploration of the search space.'}, 'population_state': {'diversity': 'Very high (0.9919). This indicates the population covers a wide area of the search space, which is positive for exploration but can hinder convergence if the search direction is unclear.', 'convergence': 'Complete lack of convergence (0.0). No solutions are identified as elite (with fixed nodes) which is expected given the cost spread. The population is not focusing on any particular regions.', 'status': 'The population is in a state of extremely high exploration and lack of exploitation. There is no identified elite status and lack of convergence is observed. This is normal in the beginning stages of a run, but should be expected. The population is also poorly focused towards the optimal solution.'}, 'difficult_regions': {'description': '5 difficult regions identified. This confirms the assumption of multiple local optima or areas of high complexity.', 'challenges': 'These regions likely represent areas where the optimization process gets stuck or takes a long time to escape. There may also be issues with the data or setup, such as badly constructed solutions. Navigating and escaping from these regions is critical for finding better solutions.'}, 'opportunity_regions': {'description': "0 opportunity regions identified. This doesn't mean they don't exist, but no specific areas of promising solutions have been identified yet. This is likely related to the population's highly diverse nature, preventing the identification of any strong trends in the current population.", 'potential': "The lack of known good solutions (lack of opportunity regions) suggests the algorithm hasn't yet found/explored such an area, or the problem itself might not have any clear patterns of success (or a proper fitness function)."}, 'evolution_direction': {'strategy': 'Focus on exploitation within the identified difficult regions. Because of high diversity and lack of convergence, the current strategy should be about exploring the challenging areas more effectively, or focus on a different subset of the input data.', 'recommendations': ['Implement strategies to escape difficult regions.  Consider techniques like:  (1) mutation with large jumps, (2) simulated annealing, (3) restart with diverse population.', 'Reduce the diversity to focus on specific areas. Consider this as the second strategy only if the first one does not converge within reasonable time.', "Inspect individual solutions: Analyze the components of solutions in difficult regions to understand *why* they're difficult.  Look for commonalities or bottlenecks.", 'Experiment with different mutation rates and selection pressure to balance exploration and exploitation.', 'Adjust parameters to increase the population size, as 10 individuals may be insufficient to sufficiently explore the search space.']}}
2025-06-22 16:18:23,308 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 16:18:23,308 - __main__ - INFO - 分析阶段完成
2025-06-22 16:18:23,309 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Likely very high. The min and max cost span a large range (6540.0 to 117665.0) which suggests a highly varied landscape. However, the extremely low number of high-quality edges and common subpaths suggest a highly disconnected and fragmented structure rather than classical ruggedness with readily identifiable basins. ', 'modality': "Unknown but likely high or very high. Given the wide cost range and the identified '5 difficult regions', it suggests multiple potential local optima. The complete absence of converged elites makes definitive modality assessment difficult, however.", 'structure': 'Fragmented and sparse. The zero values for high-quality edges and common subpaths strongly indicate a lack of strong connectivity within the search space. This makes identifying and leveraging existing high-performing structures challenging.', 'cost_distribution_implications': 'The mean and standard deviation being NaN, combined with the vast cost difference, implies that the current population does not have a meaningful central tendency or cost variation, hinting at either a very small number of individuals being effective or a very poor exploration of the search space.'}, 'population_state': {'diversity': 'Very high (0.9919). This indicates the population covers a wide area of the search space, which is positive for exploration but can hinder convergence if the search direction is unclear.', 'convergence': 'Complete lack of convergence (0.0). No solutions are identified as elite (with fixed nodes) which is expected given the cost spread. The population is not focusing on any particular regions.', 'status': 'The population is in a state of extremely high exploration and lack of exploitation. There is no identified elite status and lack of convergence is observed. This is normal in the beginning stages of a run, but should be expected. The population is also poorly focused towards the optimal solution.'}, 'difficult_regions': {'description': '5 difficult regions identified. This confirms the assumption of multiple local optima or areas of high complexity.', 'challenges': 'These regions likely represent areas where the optimization process gets stuck or takes a long time to escape. There may also be issues with the data or setup, such as badly constructed solutions. Navigating and escaping from these regions is critical for finding better solutions.'}, 'opportunity_regions': {'description': "0 opportunity regions identified. This doesn't mean they don't exist, but no specific areas of promising solutions have been identified yet. This is likely related to the population's highly diverse nature, preventing the identification of any strong trends in the current population.", 'potential': "The lack of known good solutions (lack of opportunity regions) suggests the algorithm hasn't yet found/explored such an area, or the problem itself might not have any clear patterns of success (or a proper fitness function)."}, 'evolution_direction': {'strategy': 'Focus on exploitation within the identified difficult regions. Because of high diversity and lack of convergence, the current strategy should be about exploring the challenging areas more effectively, or focus on a different subset of the input data.', 'recommendations': ['Implement strategies to escape difficult regions.  Consider techniques like:  (1) mutation with large jumps, (2) simulated annealing, (3) restart with diverse population.', 'Reduce the diversity to focus on specific areas. Consider this as the second strategy only if the first one does not converge within reasonable time.', "Inspect individual solutions: Analyze the components of solutions in difficult regions to understand *why* they're difficult.  Look for commonalities or bottlenecks.", 'Experiment with different mutation rates and selection pressure to balance exploration and exploitation.', 'Adjust parameters to increase the population size, as 10 individuals may be insufficient to sufficiently explore the search space.']}}
2025-06-22 16:18:23,309 - __main__ - INFO - 开始策略分配阶段
2025-06-22 16:18:23,309 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 16:18:23,309 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 1
- Population: 10 individuals, diversity=Very high (0.9919). This indicates the population covers a wide area of the search space, which is positive for exploration but can hinder convergence if the search direction is unclear.
- Best cost: 6540.0, Mean cost: nan
- Search space: Ruggedness: Likely very high. The min and max cost span a large range (6540.0 to 117665.0) which suggests a highly varied landscape. However, the extremely low number of high-quality edges and common subpaths suggest a highly disconnected and fragmented structure rather than classical ruggedness with readily identifiable basins. , Modality: Unknown but likely high or very high. Given the wide cost range and the identified '5 difficult regions', it suggests multiple potential local optima. The complete absence of converged elites makes definitive modality assessment difficult, however.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early Stage - Initial Optimization & Diversity Increase",
    "features": {
      "iteration": "0/5 (very early stage)",
      "cost": "Significant initi...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 16:18:23,311 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 16:18:28,545 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.3,
      "step_size_factor": 0.1,
      "regional_focus_factor": 0.5,
        "difficulty_region_bias":0.6,
        "opportunity_region_bias": 0.8
    },
    "exploit": {
      "crossover_rate": 0.1,
      "elitism_percentage": 0.1,
      "step_size_factor": 0.05,
      "local_search_depth": 2
    }
  }
}
```

2025-06-22 16:18:28,545 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'explore']
2025-06-22 16:18:28,545 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.3,
      "step_size_factor": 0.1,
      "regional_focus_factor": 0.5,
        "difficulty_region_bias":0.6,
        "opportunity_region_bias": 0.8
    },
    "exploit": {
      "crossover_rate": 0.1,
      "elitism_percentage": 0.1,
      "step_size_factor": 0.05,
      "local_search_depth": 2
    }
  }
}
```

2025-06-22 16:18:28,546 - __main__ - INFO - 策略分配阶段完成
2025-06-22 16:18:28,546 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'explore']
2025-06-22 16:18:28,546 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.3,
      "step_size_factor": 0.1,
      "regional_focus_factor": 0.5,
        "difficulty_region_bias":0.6,
        "opportunity_region_bias": 0.8
    },
    "exploit": {
      "crossover_rate": 0.1,
      "elitism_percentage": 0.1,
      "step_size_factor": 0.05,
      "local_search_depth": 2
    }
  }
}
```

2025-06-22 16:18:28,547 - __main__ - INFO - 开始进化阶段
2025-06-22 16:18:28,547 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 16:18:28,547 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:18:28,548 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 20 nodes, starting with [39, 44, 48] and ending with [65, 52, 63]
- Cost: 6540.0
- Key features: Standard path

## Search Context:
- Population diversity: 0.8868940754039497
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:18:28,548 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:18:35,221 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    39, 45, 49, 53, 57, 61, 60, 56, 52, 48, 44, 40, 36, 32, 28, 24, 20, 16, 12, 8, 4, 0, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 65, 64, 63, 62, 58, 54, 50, 46, 42, 38, 34, 30, 26, 22, 18, 14, 10, 6, 2, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 63, 59, 55, 51, 47, 43, 39, 44, 48, 52, 63, 65, 65, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63
  ],
  "modification_strategy": "This strategy prioritizes a highly diverse path by employing a 'zigzag' pattern combined with a more thorough coverage of regions indicated by the unexplored and opportunity regions. Initially, it takes a wide sweep to cover different areas then focuses on the target regions. This path aims to maximize coverage and explore potentially valuable regions beyond the standard path, as well as returning to some of the starting points to ensure coverage. Finally it attempts to converge to the end points.",
  "targeted_regions": "Unexplored and Opportunity Regions"
}
```
2025-06-22 16:18:35,221 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:18:35,221 - ExplorationExpert - INFO - 探索路径生成完成，成本: -4.716837917368192e+303, 路径: [39, 45, 49, 53, 57, 61, 60, 56, 52, 48, 44, 40, 36, 32, 28, 24, 20, 16, 12, 8, 4, 0, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 65, 64, 63, 62, 58, 54, 50, 46, 42, 38, 34, 30, 26, 22, 18, 14, 10, 6, 2, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 63, 59, 55, 51, 47, 43, 39, 44, 48, 52, 63, 65, 65, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63]
2025-06-22 16:18:35,221 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [39, 45, 49, 53, 57, 61, 60, 56, 52, 48, 44, 40, 36, 32, 28, 24, 20, 16, 12, 8, 4, 0, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 65, 64, 63, 62, 58, 54, 50, 46, 42, 38, 34, 30, 26, 22, 18, 14, 10, 6, 2, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 63, 59, 55, 51, 47, 43, 39, 44, 48, 52, 63, 65, 65, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63], 'cur_cost': -4.716837917368192e+303}
2025-06-22 16:18:35,221 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 16:18:35,221 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:18:35,221 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [2, 1, 4] and ending with [187, 190, 193]
- Cost: nan
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8720748829953198
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:18:35,221 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:18:40,401 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [2, 1, 5, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193],
  "modification_strategy": "Utilized a simple branching strategy to explore different regions. I prioritized paths that are farther from the initial path to maximize diversity. The high diversity weight helped shape this strategy to choose more unexplored paths.",
  "targeted_regions": [ "Unexplored regions" ]
}
```
2025-06-22 16:18:40,401 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:18:40,401 - ExplorationExpert - INFO - 探索路径生成完成，成本: nan, 路径: [2, 1, 5, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193]
2025-06-22 16:18:40,401 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [2, 1, 5, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193], 'cur_cost': nan}
2025-06-22 16:18:40,401 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 16:18:40,401 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:18:40,401 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 67 nodes, starting with [39, 40, 41] and ending with [8, 7, 6]
- Cost: 15563.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8705148205928237
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:18:40,401 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:18:46,354 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [39, 40, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 42, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 2, 5, 8],
  "modification_strategy": "Prioritized exploration of opportunity regions by deviating early from the original path and then gradually shifting towards the end point, integrating risk management by balancing deviation with efficient transitions to potentially new regions and avoiding difficult regions. The diversity parameter is prioritized to generate a more different path from the original.",
  "targeted_regions": [
    "Nodes 41-65 (Opportunity Exploration): Focused on initially branching towards unexplored nodes, maximizing exploration of the two opportunity regions. ",
    "Nodes 43-10 (Path Integration): Efficiently transition from opportunity areas towards potentially new regions, ensuring some overlap with the original path (nodes 7, 8) to reach the end node, ensuring overall quality."
  ]
}
```
2025-06-22 16:18:46,354 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:18:46,355 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17697.0, 路径: [39, 40, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 42, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 2, 5, 8]
2025-06-22 16:18:46,355 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [39, 40, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 42, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 2, 5, 8], 'cur_cost': 17697.0}
2025-06-22 16:18:46,355 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 16:18:46,355 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:18:46,355 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 61 nodes, starting with [11, 33, 52] and ending with [18, 0, 53]
- Cost: 103608.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8488745980707395
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:18:46,355 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:18:51,693 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    11, 33, 2, 45, 10, 15, 5, 28, 48, 39, 13, 31, 20, 51, 8, 30, 1, 40, 4, 22, 3, 57, 26, 43, 50, 17, 36, 6, 23, 55, 16, 38, 24, 47, 9, 19, 59, 54, 7, 37, 44, 12, 27, 58, 41, 14, 21, 49, 29, 35, 56, 60, 42, 34, 53, 18, 0, 52 ],
  "modification_strategy": "This strategy prioritizes exploration of the 2 opportunity regions while maintaining a high diversity score and reasonable quality. The algorithm attempts to traverse through the nodes more dynamically than the original path. Several new nodes were inserted in earlier stages to enhance the exploratory power of the current path.",
  "targeted_regions": [
    "Opportunity Region 1",
    "Opportunity Region 2"
  ]
}
```
2025-06-22 16:18:51,694 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:18:51,694 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96950.0, 路径: [11, 33, 2, 45, 10, 15, 5, 28, 48, 39, 13, 31, 20, 51, 8, 30, 1, 40, 4, 22, 3, 57, 26, 43, 50, 17, 36, 6, 23, 55, 16, 38, 24, 47, 9, 19, 59, 54, 7, 37, 44, 12, 27, 58, 41, 14, 21, 49, 29, 35, 56, 60, 42, 34, 53, 18, 0, 52]
2025-06-22 16:18:51,694 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [11, 33, 2, 45, 10, 15, 5, 28, 48, 39, 13, 31, 20, 51, 8, 30, 1, 40, 4, 22, 3, 57, 26, 43, 50, 17, 36, 6, 23, 55, 16, 38, 24, 47, 9, 19, 59, 54, 7, 37, 44, 12, 27, 58, 41, 14, 21, 49, 29, 35, 56, 60, 42, 34, 53, 18, 0, 52], 'cur_cost': 96950.0}
2025-06-22 16:18:51,694 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-22 16:18:51,694 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:18:51,694 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:18:51,694 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 113371.0
2025-06-22 16:18:51,709 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 16:18:51,709 - ExploitationExpert - INFO - res_population_costs: [10196, 10250, 14984]
2025-06-22 16:18:51,709 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  2,  8,  5,  1,  3,  7, 11,  9,  4,  6, 61, 59, 62, 54, 57,
       64, 60, 53, 63, 52, 65, 55, 56, 58, 41, 51, 38, 45, 44, 47, 49, 46,
       43, 48, 40, 42, 50, 39, 27, 37, 31, 33, 32, 29, 24, 25, 28, 26, 36,
       35, 30, 34, 16, 19, 21, 20, 14, 15, 17, 12, 18, 22, 13, 23],
      dtype=int64), array([ 0, 11,  5,  6,  2,  4,  8, 10, 55, 56, 59, 65, 60, 54, 53, 52, 63,
       61, 62, 57, 64, 58, 41, 38, 44, 42, 43, 48, 46, 47, 50, 51, 45, 39,
       49, 40, 34, 36, 37, 26, 28, 29, 33, 32, 30, 35, 31, 25, 24, 27, 17,
       18, 15, 13, 23, 12, 22, 16, 19, 20, 21, 14,  9,  7,  3,  1],
      dtype=int64), array([ 0,  2,  8,  5,  1, 26, 29, 35, 36, 37, 32, 30, 34, 27, 31, 28, 25,
       33, 24, 12, 22, 18, 19, 21, 20, 13, 17, 14, 16, 23, 15, 11,  7,  3,
       46, 41, 40, 49, 47, 44, 45, 38, 48, 39, 50, 51, 42, 43, 58, 60, 62,
       56, 64, 59,  6,  4,  9, 54, 57, 53, 55, 61, 65, 52, 63, 10],
      dtype=int64)]
2025-06-22 16:18:51,711 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:18:51,711 - ExploitationExpert - INFO - populations: [{'tour': [39, 45, 49, 53, 57, 61, 60, 56, 52, 48, 44, 40, 36, 32, 28, 24, 20, 16, 12, 8, 4, 0, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 65, 64, 63, 62, 58, 54, 50, 46, 42, 38, 34, 30, 26, 22, 18, 14, 10, 6, 2, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 63, 59, 55, 51, 47, 43, 39, 44, 48, 52, 63, 65, 65, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63], 'cur_cost': -4.716837917368192e+303}, {'tour': [2, 1, 5, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193], 'cur_cost': nan}, {'tour': [39, 40, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 42, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 2, 5, 8], 'cur_cost': 17697.0}, {'tour': [11, 33, 2, 45, 10, 15, 5, 28, 48, 39, 13, 31, 20, 51, 8, 30, 1, 40, 4, 22, 3, 57, 26, 43, 50, 17, 36, 6, 23, 55, 16, 38, 24, 47, 9, 19, 59, 54, 7, 37, 44, 12, 27, 58, 41, 14, 21, 49, 29, 35, 56, 60, 42, 34, 53, 18, 0, 52], 'cur_cost': 96950.0}, {'tour': array([34, 44, 24, 23, 36,  6, 41,  9, 57, 46, 21, 20, 43, 54, 30, 64, 27,
       45, 58, 53,  2, 63, 33, 10, 11, 29, 38,  4, 19, 56, 42, 15,  5, 12,
       37, 28, 60, 13, 59, 31,  8, 35, 51, 14,  7, 18, 39, 50, 48, 17, 49,
       65, 55, 62, 25,  3, 22, 47, 32,  0, 16, 61, 52, 26,  1, 40]), 'cur_cost': 113371.0}, {'tour': array([ 9, 47, 54, 59, 19,  5,  2,  1, 27, 29, 31,  4, 41, 28, 38,  3, 56,
        6, 18, 20, 53, 42, 32, 44, 64, 65, 57, 62, 17, 14, 35, 55, 25, 40,
       63, 52, 61, 36, 12,  7, 21, 15, 13, 39,  0, 30, 37, 33, 43, 24, 10,
       58, 46, 45, 22, 60, 23, 26, 16, 51, 34,  8, 49, 48, 11, 50]), 'cur_cost': 97618.0}, {'tour': array([43, 48, 33,  4, 17, 41, 51, 63, 56, 57, 22, 39, 27, 52, 19,  3, 50,
       35, 38, 59, 64, 11, 15, 10, 26, 62, 34, 55, 28, 65, 42, 29, 46, 53,
        9,  7,  2, 40, 24, 21, 32, 20, 47, 23, 14,  0, 44, 37, 36, 61, 30,
       54, 58,  8,  5, 12, 25, 45,  1, 16, 49, 13, 18, 31, 60,  6]), 'cur_cost': 108376.0}, {'tour': array([12, 28, 21,  4, 41, 31, 36, 22, 43, 60, 59, 38, 10, 53, 19, 57,  6,
       65,  1, 40, 29, 61, 15, 30, 13, 33,  0, 64, 20,  9, 14, 45, 55, 48,
       26,  2,  5,  8, 11, 32, 63, 51, 46, 49, 35, 54, 34, 37, 42, 27, 52,
       62, 23,  3, 58, 16, 25, 50, 17, 56, 44, 39, 24, 18,  7, 47]), 'cur_cost': 106580.0}, {'tour': array([40, 21, 45, 41,  6, 39, 35, 25, 61, 19, 16,  3, 49, 47, 50, 10, 18,
       64, 29, 13, 56, 26, 38,  0, 37, 44, 52,  7,  8, 62,  5, 20, 54,  4,
       43, 36, 30, 55, 24,  9, 63, 27, 11, 17, 60, 28, 23, 58, 34,  2, 15,
       22, 32, 14, 51, 12, 48, 59, 65, 42, 33, 53, 31, 57, 46,  1]), 'cur_cost': 117665.0}, {'tour': array([45, 12,  3, 42,  1, 30, 13, 16, 57, 24, 27, 40, 56, 53, 28, 31, 64,
       25,  8, 46,  9, 44, 62, 19, 52,  6, 63,  4, 36, 22, 38, 49, 54, 32,
       60, 58,  0,  7, 55, 41, 61, 23, 35, 39, 18, 51, 37, 20, 34, 50, 59,
       65, 17, 11, 21, 47, 33, 14,  5, 48, 29, 26, 10,  2, 15, 43]), 'cur_cost': 107836.0}]
2025-06-22 16:18:51,713 - ExploitationExpert - INFO - 局部搜索耗时: 0.02秒
2025-06-22 16:18:51,713 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-22 16:18:51,714 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-22 16:18:51,714 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 16:18:51,714 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:18:51,714 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:18:51,715 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 116616.0
2025-06-22 16:18:52,231 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 16:18:52,233 - ExploitationExpert - INFO - res_population_costs: [10196, 10250, 14984]
2025-06-22 16:18:52,233 - ExploitationExpert - INFO - res_populations: [array([ 0, 10,  2,  8,  5,  1,  3,  7, 11,  9,  4,  6, 61, 59, 62, 54, 57,
       64, 60, 53, 63, 52, 65, 55, 56, 58, 41, 51, 38, 45, 44, 47, 49, 46,
       43, 48, 40, 42, 50, 39, 27, 37, 31, 33, 32, 29, 24, 25, 28, 26, 36,
       35, 30, 34, 16, 19, 21, 20, 14, 15, 17, 12, 18, 22, 13, 23],
      dtype=int64), array([ 0, 11,  5,  6,  2,  4,  8, 10, 55, 56, 59, 65, 60, 54, 53, 52, 63,
       61, 62, 57, 64, 58, 41, 38, 44, 42, 43, 48, 46, 47, 50, 51, 45, 39,
       49, 40, 34, 36, 37, 26, 28, 29, 33, 32, 30, 35, 31, 25, 24, 27, 17,
       18, 15, 13, 23, 12, 22, 16, 19, 20, 21, 14,  9,  7,  3,  1],
      dtype=int64), array([ 0,  2,  8,  5,  1, 26, 29, 35, 36, 37, 32, 30, 34, 27, 31, 28, 25,
       33, 24, 12, 22, 18, 19, 21, 20, 13, 17, 14, 16, 23, 15, 11,  7,  3,
       46, 41, 40, 49, 47, 44, 45, 38, 48, 39, 50, 51, 42, 43, 58, 60, 62,
       56, 64, 59,  6,  4,  9, 54, 57, 53, 55, 61, 65, 52, 63, 10],
      dtype=int64)]
2025-06-22 16:18:52,235 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:18:52,235 - ExploitationExpert - INFO - populations: [{'tour': [39, 45, 49, 53, 57, 61, 60, 56, 52, 48, 44, 40, 36, 32, 28, 24, 20, 16, 12, 8, 4, 0, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 65, 64, 63, 62, 58, 54, 50, 46, 42, 38, 34, 30, 26, 22, 18, 14, 10, 6, 2, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 63, 59, 55, 51, 47, 43, 39, 44, 48, 52, 63, 65, 65, 52, 52, 52, 52, 52, 52, 52, 52, 52, 52, 63, 63, 63, 63, 63, 63, 63, 63, 63, 63], 'cur_cost': -4.716837917368192e+303}, {'tour': [2, 1, 5, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 67, 70, 73, 76, 79, 82, 85, 88, 91, 94, 97, 100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148, 151, 154, 157, 160, 163, 166, 169, 172, 175, 178, 181, 184, 187, 190, 193], 'cur_cost': nan}, {'tour': [39, 40, 41, 44, 47, 50, 53, 56, 59, 62, 65, 64, 61, 58, 55, 52, 49, 46, 43, 42, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 37, 34, 31, 28, 25, 22, 19, 16, 13, 10, 7, 4, 1, 2, 5, 8], 'cur_cost': 17697.0}, {'tour': [11, 33, 2, 45, 10, 15, 5, 28, 48, 39, 13, 31, 20, 51, 8, 30, 1, 40, 4, 22, 3, 57, 26, 43, 50, 17, 36, 6, 23, 55, 16, 38, 24, 47, 9, 19, 59, 54, 7, 37, 44, 12, 27, 58, 41, 14, 21, 49, 29, 35, 56, 60, 42, 34, 53, 18, 0, 52], 'cur_cost': 96950.0}, {'tour': array([34, 44, 24, 23, 36,  6, 41,  9, 57, 46, 21, 20, 43, 54, 30, 64, 27,
       45, 58, 53,  2, 63, 33, 10, 11, 29, 38,  4, 19, 56, 42, 15,  5, 12,
       37, 28, 60, 13, 59, 31,  8, 35, 51, 14,  7, 18, 39, 50, 48, 17, 49,
       65, 55, 62, 25,  3, 22, 47, 32,  0, 16, 61, 52, 26,  1, 40]), 'cur_cost': 113371.0}, {'tour': array([ 4, 29, 22, 30, 21, 34,  8, 38, 20, 64, 13, 54,  1, 15, 47, 61, 10,
       65, 19, 45, 24, 26, 48, 56, 51, 59,  3,  6, 50,  9,  5, 27, 44, 11,
       40, 37, 55, 57, 49, 32, 16, 52,  2, 46, 58, 14, 17, 23, 18, 39, 62,
        0, 25, 33, 60, 31, 63, 36,  7, 42, 12, 28, 35, 41, 53, 43]), 'cur_cost': 116616.0}, {'tour': array([43, 48, 33,  4, 17, 41, 51, 63, 56, 57, 22, 39, 27, 52, 19,  3, 50,
       35, 38, 59, 64, 11, 15, 10, 26, 62, 34, 55, 28, 65, 42, 29, 46, 53,
        9,  7,  2, 40, 24, 21, 32, 20, 47, 23, 14,  0, 44, 37, 36, 61, 30,
       54, 58,  8,  5, 12, 25, 45,  1, 16, 49, 13, 18, 31, 60,  6]), 'cur_cost': 108376.0}, {'tour': array([12, 28, 21,  4, 41, 31, 36, 22, 43, 60, 59, 38, 10, 53, 19, 57,  6,
       65,  1, 40, 29, 61, 15, 30, 13, 33,  0, 64, 20,  9, 14, 45, 55, 48,
       26,  2,  5,  8, 11, 32, 63, 51, 46, 49, 35, 54, 34, 37, 42, 27, 52,
       62, 23,  3, 58, 16, 25, 50, 17, 56, 44, 39, 24, 18,  7, 47]), 'cur_cost': 106580.0}, {'tour': array([40, 21, 45, 41,  6, 39, 35, 25, 61, 19, 16,  3, 49, 47, 50, 10, 18,
       64, 29, 13, 56, 26, 38,  0, 37, 44, 52,  7,  8, 62,  5, 20, 54,  4,
       43, 36, 30, 55, 24,  9, 63, 27, 11, 17, 60, 28, 23, 58, 34,  2, 15,
       22, 32, 14, 51, 12, 48, 59, 65, 42, 33, 53, 31, 57, 46,  1]), 'cur_cost': 117665.0}, {'tour': array([45, 12,  3, 42,  1, 30, 13, 16, 57, 24, 27, 40, 56, 53, 28, 31, 64,
       25,  8, 46,  9, 44, 62, 19, 52,  6, 63,  4, 36, 22, 38, 49, 54, 32,
       60, 58,  0,  7, 55, 41, 61, 23, 35, 39, 18, 51, 37, 20, 34, 50, 59,
       65, 17, 11, 21, 47, 33, 14,  5, 48, 29, 26, 10,  2, 15, 43]), 'cur_cost': 107836.0}]
2025-06-22 16:18:52,237 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 16:18:52,237 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-22 16:18:52,237 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 16:18:52,237 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 16:18:52,237 - ExplorationExpert - INFO - 开始生成探索路径
