import json
import re
from typing import Dict, Any

# Expert Prompt Interface Module
"""
Optimized prompt templates for multi-expert evolutionary algorithm system.
Each expert has streamlined prompt templates with improved data organization.
"""

# Common utility functions for data preparation
def summarize_data(data, max_items=5):
    """Summarize complex data structures for more efficient prompts"""
    if isinstance(data, list):
        if len(data) > max_items:
            return f"{len(data)} items, first {max_items}: {data[:max_items]}"
        return str(data)
    elif isinstance(data, dict):
        if len(data) > max_items:
            keys = list(data.keys())[:max_items]
            return f"{len(data)} key-value pairs, sample: {', '.join([f'{k}: {data[k]}' for k in keys])}"
        return str(data)
    return str(data)

def format_path_summary(path):
    """Create a concise summary of a path"""
    # 检查是否为NumPy数组
    try:
        import numpy as np
        if isinstance(path, np.ndarray):
            # 对NumPy数组使用size属性检查是否为空
            if path.size == 0:
                return "Empty path"
            # 将NumPy数组转换为列表进行处理
            path_list = path.tolist()
            return f"Path with {len(path_list)} nodes, starting with {path_list[:3]} and ending with {path_list[-3:]}"
    except ImportError:
        pass  # 如果没有NumPy，继续使用标准处理
        
    # 标准列表处理
    if not path:
        return "Empty path"
    return f"Path with {len(path)} nodes, starting with {path[:3]} and ending with {path[-3:]}"

def compute_adaptive_spatial_stats(coordinates=None, distance_matrix=None, problem_size=None, grid_size=3):
    """Compute adaptive spatial statistics based on problem characteristics.
    Args:
        coordinates (list): list of (x, y) tuples or np.ndarray of shape (n, 2)
        distance_matrix (np.ndarray, optional): pre-computed pairwise distances
        problem_size (int): number of nodes in the problem
        grid_size (int): density grid resolution per dimension (default 3 -> 3×3)
    Returns:
        dict with adaptive statistics based on problem size and characteristics
    """
    if problem_size is None and coordinates is not None:
        try:
            import numpy as np
            coords = np.asarray(coordinates)
            problem_size = coords.shape[0] if coords.ndim == 2 else len(coordinates)
        except:
            problem_size = len(coordinates) if coordinates else 0

    # Adaptive analysis based on problem size
    if problem_size < 20:
        return compute_small_problem_stats(coordinates, distance_matrix)
    elif problem_size < 100:
        return compute_medium_problem_stats(coordinates, distance_matrix, grid_size)
    else:
        return compute_large_problem_stats(coordinates, distance_matrix, grid_size)

def compute_small_problem_stats(coordinates=None, distance_matrix=None):
    """Simplified statistics for small problems (< 20 nodes)"""
    stats = {
        "problem_type": "small",
        "node_count": 0,
        "avg_distance": "N/A",
        "geometry_type": "unknown"
    }

    if coordinates is None:
        return stats

    try:
        import numpy as np
        coords = np.asarray(coordinates)
        if coords.ndim != 2 or coords.shape[1] < 2:
            return stats

        stats["node_count"] = coords.shape[0]

        # Simple geometry classification
        if coords.shape[0] >= 3:
            # Check if points form a roughly circular pattern
            center = coords.mean(axis=0)
            distances_from_center = np.sqrt(((coords - center)**2).sum(axis=1))
            std_distances = distances_from_center.std()
            mean_distance = distances_from_center.mean()

            if std_distances / mean_distance < 0.3:
                stats["geometry_type"] = "clustered"
            elif std_distances / mean_distance > 0.7:
                stats["geometry_type"] = "scattered"
            else:
                stats["geometry_type"] = "mixed"

        # Average distance between consecutive points
        if distance_matrix is not None and hasattr(distance_matrix, 'shape'):
            tri = distance_matrix[np.triu_indices_from(distance_matrix, k=1)]
            if tri.size > 0:
                stats["avg_distance"] = str(round(float(tri.mean()), 2))

    except Exception:
        pass

    return stats

def compute_medium_problem_stats(coordinates=None, distance_matrix=None, grid_size=3):
    """Balanced statistics for medium problems (20-100 nodes)"""
    stats = compute_spatial_stats(coordinates, distance_matrix, grid_size)
    stats["problem_type"] = "medium"
    return stats

def compute_large_problem_stats(coordinates=None, distance_matrix=None, grid_size=5):
    """Comprehensive statistics for large problems (> 100 nodes)"""
    stats = compute_spatial_stats(coordinates, distance_matrix, grid_size)
    stats["problem_type"] = "large"

    # Additional statistics for large problems
    if coordinates is not None:
        try:
            import numpy as np
            coords = np.asarray(coordinates)
            if coords.ndim == 2 and coords.shape[1] >= 2:
                # Cluster analysis for large problems
                stats["cluster_tendency"] = analyze_clustering_tendency(coords)
                stats["outlier_nodes"] = identify_outlier_nodes(coords)
        except Exception:
            pass

    return stats

def analyze_clustering_tendency(coordinates):
    """Analyze clustering tendency for large problems"""
    try:
        import numpy as np
        from sklearn.cluster import KMeans

        # Use k-means to identify potential clusters
        n_clusters = min(5, max(2, len(coordinates) // 20))
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = kmeans.fit_predict(coordinates)

        # Calculate silhouette-like measure
        cluster_sizes = np.bincount(labels)
        size_variance = cluster_sizes.var()

        if size_variance < cluster_sizes.mean() * 0.5:
            return "strong"
        elif size_variance < cluster_sizes.mean():
            return "moderate"
        else:
            return "weak"
    except Exception:
        return "unknown"

def identify_outlier_nodes(coordinates):
    """Identify outlier nodes that might be difficult to connect"""
    try:
        import numpy as np

        center = coordinates.mean(axis=0)
        distances = np.sqrt(((coordinates - center)**2).sum(axis=1))
        threshold = distances.mean() + 2 * distances.std()

        outliers = np.where(distances > threshold)[0].tolist()
        return outliers[:5]  # Return at most 5 outliers
    except Exception:
        return []

def compute_spatial_stats(coordinates=None, distance_matrix=None, grid_size=3):
    """Original spatial statistics computation (kept for compatibility)"""
    stats = {
        "bounding_box": "N/A",
        "centroid": "N/A",
        "rms_dist": "N/A",
        "density_grid": "N/A",
        "edge_len_stats": "N/A",
        "long_edge_ratio": "N/A",
        "direction_histogram": "N/A",
        "nn_median": "N/A",
        "knn_cluster": "N/A",
    }

    if coordinates is None:
        return stats  # cannot compute

    try:
        import numpy as np
        coords = np.asarray(coordinates)
        if coords.ndim != 2 or coords.shape[1] < 2:
            return stats

        # bounding box
        xmin, ymin = coords.min(axis=0)[:2]
        xmax, ymax = coords.max(axis=0)[:2]
        stats["bounding_box"] = str([round(float(xmin), 3), round(float(ymin), 3), round(float(xmax), 3), round(float(ymax), 3)])

        # centroid & spread
        centroid = coords.mean(axis=0)[:2]
        diffs = coords[:, :2] - centroid
        rms = float(np.sqrt((diffs**2).sum(axis=1).mean()))
        stats["centroid"] = str([round(float(centroid[0]), 3), round(float(centroid[1]), 3)])
        stats["rms_dist"] = str(round(rms, 3))

        # density grid
        grid = np.zeros((grid_size, grid_size), dtype=int)
        # avoid divide-by-zero
        span_x = xmax - xmin if xmax - xmin != 0 else 1e-9
        span_y = ymax - ymin if ymax - ymin != 0 else 1e-9
        gx = np.clip(((coords[:, 0] - xmin) / span_x * grid_size).astype(int), 0, grid_size - 1)
        gy = np.clip(((coords[:, 1] - ymin) / span_y * grid_size).astype(int), 0, grid_size - 1)
        for ix, iy in zip(gx, gy):
            grid[iy, ix] += 1
        stats["density_grid"] = str(grid.tolist())

        # edge lengths from distance_matrix if available else NN distances
        if distance_matrix is not None and hasattr(distance_matrix, 'shape'):
            tri = distance_matrix[np.triu_indices_from(distance_matrix, k=1)]
            if tri.size > 0:
                q = np.percentile(tri, [0, 25, 50, 75, 100])
                stats["edge_len_stats"] = str([round(float(v), 3) for v in q])
                stats["long_edge_ratio"] = str(round(float((tri > q[3]).mean()), 3))
        else:
            # fallback using euclidean distances of first 1e4 pairs for speed
            pass

        # nearest-neighbor median distance
        try:
            from sklearn.neighbors import NearestNeighbors
            nbrs = NearestNeighbors(n_neighbors=2).fit(coords[:, :2])
            dists, _ = nbrs.kneighbors(coords[:, :2])
            nn_med = np.median(dists[:, 1])
            stats["nn_median"] = str(round(float(nn_med), 3))
        except Exception:
            pass

        # direction histogram (8 sectors)
        if coords.shape[0] >= 2:
            directions = []
            for i in range(coords.shape[0] - 1):
                dx, dy = coords[i+1, 0] - coords[i, 0], coords[i+1, 1] - coords[i, 1]
                angle = np.arctan2(dy, dx)
                sector = int(((angle + np.pi) / (2 * np.pi)) * 8) % 8
                directions.append(sector)
            if directions:
                hist = np.bincount(directions, minlength=8)
                stats["direction_histogram"] = str(hist.tolist())
        return stats
    except Exception:
        return stats

# Improved Landscape Expert Prompt Templates
LANDSCAPE_PROMPT = """
## Role: TSP Landscape Analyzer

### Current State (Iter {iteration}/{total_iterations})
- Problem: {instance_name} ({node_count} nodes)
- Population: {population_size} individuals, diversity: {diversity_level:.3f}
- Performance: best={min_cost}, mean={mean_cost:.1f}, improvement={improvement_rate}
- Convergence: {convergence_level:.3f}, stagnation: {stagnation_flag}

### Key Indicators
- Cost distribution: std={std_cost:.1f}, range=[{min_cost}, {max_cost}]
- Search progress: {recent_cost_series}
- Population clustering: {clustering_summary}
- Elite solutions: {elite_count} found, diversity={elite_diversity_summary}

### Structural Analysis
- High-quality patterns: {high_quality_edges_sample}
- Common structures: {common_subpaths_sample}
- Problem areas: {difficult_regions_sample}
- Opportunities: {opportunity_regions_sample}

### Spatial Characteristics
- Problem geometry: {sp_bounding_box}
- Node distribution: {sp_density_grid}
- Edge patterns: {sp_edge_stats}

### Task
Analyze the current search landscape and determine:
1. Search difficulty level and problem characteristics
2. Population state and search progress
3. Recommended focus (explore/exploit/balance)
4. Specific regions to target or avoid

### Output Format
Return ONLY JSON with keys: search_difficulty, population_status, recommended_action, target_regions

### Example
```json
{{
  "search_difficulty": "medium",
  "population_status": {{"diversity": 0.650, "convergence": 0.400, "phase": "exploration"}},
  "recommended_action": {{"focus": "explore", "intensity": 0.7}},
  "target_regions": {{"opportunities": [1,5,8], "avoid": [12,15]}}
}}
```
"""

# Simplified Landscape Prompt for Small Problems
LANDSCAPE_PROMPT_SIMPLE = """
## Role: TSP Landscape Analyzer (Small Problem)

### Status (Iter {iteration})
- Problem: {node_count} nodes, best cost: {min_cost}
- Population diversity: {diversity_level:.3f}
- Recent improvement: {improvement_rate}

### Analysis
Based on the current state, determine:
1. Should we explore (find new areas) or exploit (improve current solutions)?
2. Which nodes/edges show promise?

### Output
JSON with: recommended_focus, promising_areas

### Example
```json
{{
  "recommended_focus": "explore",
  "promising_areas": [2, 5, 8]
}}
```
"""

# Optimized Strategy Expert Prompt Templates
STRATEGY_PROMPT = """
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration {iteration}
- Population: {population_size} individuals
  • diversity: {population_diversity}
  • best_cost: {min_cost}
  • mean_cost: {mean_cost}
- Search-space: ruggedness {ruggedness}, modality {modality}, deceptiveness {deceptiveness}
- Evolution phase: {evolution_phase}
- Landscape focus suggestion: {recommended_focus}
- Spatial density summary: {density_summary}
- Elite diversity: {elite_diversity}
- Difficult regions (# {difficult_regions_count}): {difficult_regions_brief}
- Opportunity regions (# {opportunity_regions_count}): {opportunity_regions_brief}
- Last-iteration feedback: {previous_feedback_summary}

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {{"0": "explore", ...}},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{{
  "global_explore_ratio": 0.6,
  "individual_assignments": {{"0": "explore", "1": "exploit"}},
  "rationale": "Population diversity low; half explore."
}}
```
"""

# Optimized Exploration Expert Prompt Templates
EXPLORATION_PROMPT = """
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: {iteration}
- Individual cost: {path_cost} (rank {cost_rank_pct}%)
- Population diversity: {population_diversity}
- Evolution phase: {evolution_phase}
- Landscape recommends focus: {recommended_focus}
- Opportunity regions (sample): {opportunity_regions_brief}
- Difficult regions to avoid (sample): {difficult_regions_brief}
- Sparse cells / unexplored areas: {sparse_hint}

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, {max_node_index}]  
• Path length == {path_length} and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}}
```
"""

# Optimized Exploitation Expert Prompt Templates
EXPLOITATION_PROMPT = """
## Role: Path Optimization Expert

## Current Path:
- Path summary: {path_summary}
- Cost: {path_cost}
- Inefficient segments: {inefficient_segments}

## Optimization Context:
- High quality edges: {high_quality_edges_summary}
- Fixed nodes: {fixed_nodes_summary}
- Elite solutions: {elite_solutions_summary}
- Improvement opportunities: {improvement_opportunities}

## Strategy Parameters:
- Quality edge usage: {quality_edge_usage}
- Elite influence: {elite_influence}
- Local search depth: {local_search_depth}

## Task:
Refine the current path by preserving high-quality edges and improving inefficient segments.
Focus on local optimization and incorporating elite features.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the optimized path
- refinement_strategy: Brief description of your approach
- preserved_edges: Edges you preserved from the original path
- optimized_regions: Segments you improved

### Output Example
```json
{{
  "new_path": [0,2,4,1,3],
  "refinement_strategy": "2-opt on long edges, kept HQ pairs",
  "preserved_edges": [[0,2]],
  "optimized_regions": [[4,1,3]]
}}
```
"""

# Optimized Assessment Expert Prompt Templates
ASSESSMENT_PROMPT = """
## Role: Evolution Assessment Expert

### Iteration {iteration}/{total_iterations}

#### Population metrics
- Min cost: {old_min_cost} → {new_min_cost} (Δ {min_cost_improvement})
- Mean cost: {old_mean_cost} → {new_mean_cost}
- Diversity: {old_diversity:.3f} → {new_diversity:.3f} (Δ {diversity_delta})

#### Elite metrics
- Elite count: {old_elite_count} → {new_elite_count}
- Best elite cost: {old_best_elite_cost} → {new_best_elite_cost} (Δ {elite_cost_improvement})
- Elite diversity: {old_elite_diversity:.3f} → {new_elite_diversity:.3f} (Δ {elite_diversity_delta})

#### Strategy performance
- Explore: {explore_count} inds, success {explore_success_rate}% , avg Δ {explore_avg_improvement}
- Exploit: {exploit_count} inds, success {exploit_success_rate}% , avg Δ {exploit_avg_improvement}

#### Other indicators
- No-change individuals: {no_change_count}
- Regression individuals: {regression_count}
- Historical trends: {historical_trends_summary}

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{{
  "evolution_stage": "mid",
  "strategy_effectiveness": {{"explore": 0.5, "exploit": 0.5}},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}}
```
"""

# Expert Prompt Generation Functions with Optimized Data Organization

def generate_landscape_expert_prompt(stats_report, path_report, elite_report, iteration=0, total_iterations=10, history_data=None):
    """Generate adaptive landscape analysis expert prompt based on problem characteristics"""

    # Extract and summarize statistical data
    population_size = len(stats_report.get("population", [])) if "population" in stats_report else stats_report.get("population_size", 0)
    min_cost = stats_report.get("cost_stats", {}).get("min", 0)
    max_cost = stats_report.get("cost_stats", {}).get("max", 0)
    mean_cost = stats_report.get("cost_stats", {}).get("mean", 0)
    std_cost = stats_report.get("cost_stats", {}).get("std", 0)
    diversity_level = stats_report.get("diversity", {}).get("overall_score", 0) if isinstance(stats_report.get("diversity"), dict) else stats_report.get("diversity_level", 0)
    convergence_level = stats_report.get("convergence", {}).get("overall_score", 0) if isinstance(stats_report.get("convergence"), dict) else stats_report.get("convergence_level", 0)

    # Determine problem size for adaptive analysis
    node_count = stats_report.get("node_count", 0)
    if node_count == 0:
        # Try to estimate from path data
        if path_report and "high_quality_edges" in path_report and path_report["high_quality_edges"]:
            edges = path_report["high_quality_edges"]
            nodes = set()
            for edge in edges:
                if isinstance(edge, tuple) and len(edge) >= 2:
                    nodes.add(edge[0])
                    nodes.add(edge[1])
            node_count = len(nodes)
    
    # Extract and summarize path structure data
    high_quality_edges = path_report.get("high_quality_edges", [])
    high_quality_edges_count = len(high_quality_edges)
    common_subpaths = path_report.get("common_subpaths", [])
    common_subpaths_count = len(common_subpaths)
    difficult_regions = path_report.get("low_quality_regions", [])
    difficult_regions_count = len(difficult_regions)
    
    # Extract and summarize elite solution data
    elite_solutions = elite_report.get("elite_solutions", [])
    elite_count = len(elite_solutions)
    fixed_nodes = elite_report.get("fixed_nodes", [])
    fixed_nodes_count = len(fixed_nodes)
    elite_common_features = elite_report.get("common_features", {})
    
    # Calculate relationship metrics
    elite_common_edge_percentage = elite_report.get("elite_diversity", {}).get("common_edge_percentage", 0)
    # -------- add elite diversity summary --------
    elite_diversity_summary = summarize_data(elite_report.get("elite_diversity", {}), 3)
    opportunity_regions = path_report.get("opportunity_regions", [])
    opportunity_regions_count = len(opportunity_regions)
    
    # ---------------------------------------------
    # Compute adaptive spatial statistics if coordinates available
    coordinates = None
    distance_matrix = None
    if "coordinates" in stats_report:
        coordinates = stats_report.get("coordinates")
    elif "coordinates" in elite_report:
        coordinates = elite_report.get("coordinates")
    elif "coordinates" in path_report:
        coordinates = path_report.get("coordinates")

    if "distance_matrix" in stats_report:
        distance_matrix = stats_report.get("distance_matrix")
    elif "distance_matrix" in elite_report:
        distance_matrix = elite_report.get("distance_matrix")
    elif "distance_matrix" in path_report:
        distance_matrix = path_report.get("distance_matrix")

    # Use adaptive spatial statistics based on problem size
    spatial_stats = compute_adaptive_spatial_stats(coordinates, distance_matrix, node_count)
    # Unpack with fallback to 'N/A'
    sp_bbox = spatial_stats.get("bounding_box", "N/A")
    sp_centroid = spatial_stats.get("centroid", "N/A")
    sp_rms = spatial_stats.get("rms_dist", "N/A")
    sp_density = spatial_stats.get("density_grid", "N/A")
    sp_edge_stats = spatial_stats.get("edge_len_stats", "N/A")
    sp_long_ratio = spatial_stats.get("long_edge_ratio", "N/A")
    sp_dir_hist = spatial_stats.get("direction_histogram", "N/A")
    sp_nn_med = spatial_stats.get("nn_median", "N/A")
    
    # New metrics for enhanced template
    
    # Evolution context
    cost_trend_summary = "Not available"
    improvement_rate = "Not available"
    stagnation_periods = "None detected"
    
    if history_data and isinstance(history_data, list):
        # Extract cost trends from history data
        try:
            recent_costs = [h.get("new_stats", {}).get("min_cost", 0) for h in history_data[-3:]]
            if recent_costs:
                cost_trend_summary = f"Recent costs: {recent_costs}"
                
                # Calculate improvement rate
                if len(recent_costs) >= 2:
                    improvements = [max(0, recent_costs[i] - recent_costs[i+1]) for i in range(len(recent_costs)-1)]
                    avg_improvement = sum(improvements) / len(improvements) if improvements else 0
                    improvement_rate = f"{avg_improvement:.2f} per iteration"
                
                # Detect stagnation
                if len(recent_costs) >= 3:
                    if max(recent_costs) - min(recent_costs) < 0.001 * min(recent_costs):
                        stagnation_periods = "Recent stagnation detected"
        except Exception as e:
            cost_trend_summary = f"Error processing history: {str(e)}"
    
    # Problem characteristics
    instance_name = stats_report.get("instance_name", "Unknown")
    node_count = stats_report.get("node_count", 0)
    search_space_topology = "Unknown"
    edge_distribution = "Unknown"
    
    # Try to extract instance name from elite report
    if elite_solutions and isinstance(elite_solutions, list) and len(elite_solutions) > 0:
        instance_name = elite_solutions[0].get("func_name", "Unknown")
    
    # Estimate node count from path data
    if path_report and "high_quality_edges" in path_report and path_report["high_quality_edges"]:
        edges = path_report["high_quality_edges"]
        nodes = set()
        for edge in edges:
            if isinstance(edge, tuple) and len(edge) >= 2:
                nodes.add(edge[0])
                nodes.add(edge[1])
        node_count = len(nodes)
    
    # Estimate search space topology based on diversity and convergence
    if diversity_level > 0.7:
        search_space_topology = "Likely rugged with many local optima"
    elif diversity_level < 0.3:
        search_space_topology = "Likely smooth with few local optima"
    else:
        search_space_topology = "Moderately complex"
    
    # Estimate edge distribution
    if high_quality_edges_count > 0 and population_size > 0:
        edge_ratio = high_quality_edges_count / (population_size * 2)  # Rough estimate
        if edge_ratio > 0.5:
            edge_distribution = "Many common high-quality edges"
        elif edge_ratio < 0.2:
            edge_distribution = "Few common high-quality edges"
        else:
            edge_distribution = "Moderate distribution of high-quality edges"
    
    # Additional key relationships
    clustering_summary = stats_report.get("clustering_info", {}).get("summary", "No clustering information")
    
    # Estimate exploration/exploitation ratio
    if diversity_level > 0.7 and convergence_level < 0.3:
        exploration_exploitation_ratio = "High exploration (>70%)"
    elif diversity_level < 0.3 and convergence_level > 0.7:
        exploration_exploitation_ratio = "High exploitation (>70%)"
    else:
        exploration_exploitation_ratio = "Balanced (~50%)"
    
    # Estimate local optima density
    if difficult_regions_count > 5:
        local_optima_density = "High"
    elif difficult_regions_count < 2:
        local_optima_density = "Low"
    else:
        local_optima_density = "Moderate"
    
    # Sample lists for concise prompt
    high_quality_edges_sample = high_quality_edges[:5] if high_quality_edges else []
    common_subpaths_sample = common_subpaths[:3] if common_subpaths else []
    difficult_regions_sample = difficult_regions[:3] if difficult_regions else []
    opportunity_regions_sample = opportunity_regions[:3] if opportunity_regions else []
    fixed_nodes_sample = fixed_nodes[:5]
    elite_common_features_sample = {k: elite_common_features[k] for k in list(elite_common_features)[:3]} if isinstance(elite_common_features, dict) else elite_common_features
    recent_cost_series = cost_trend_summary
    stagnation_flag = stagnation_periods
    
    # Auto-derive regions from spatial density when not provided
    if (not opportunity_regions) and coordinates is not None and isinstance(sp_density, list):
        try:
            import numpy as np
            grid_arr = np.array(sp_density)
            if grid_arr.ndim == 2:
                # densest cell as opportunity
                max_idx = np.unravel_index(np.argmax(grid_arr), grid_arr.shape)
                min_idx = np.unravel_index(np.argmin(grid_arr), grid_arr.shape)

                # assign nodes into cells
                gs = grid_arr.shape[0]
                coords_np = np.asarray(coordinates)
                xmin, ymin = coords_np[:,0].min(), coords_np[:,1].min()
                xmax, ymax = coords_np[:,0].max(), coords_np[:,1].max()
                span_x = xmax - xmin if xmax - xmin != 0 else 1e-9
                span_y = ymax - ymin if ymax - ymin != 0 else 1e-9
                cell_indices = []
                for cx,cy in coords_np:
                    gx = int(((cx - xmin) / span_x) * gs)
                    gy = int(((cy - ymin) / span_y) * gs)
                    gx = min(gx, gs-1); gy = min(gy, gs-1)
                    cell_indices.append((gy, gx))

                # nodes in densest cell
                opp_nodes = [idx for idx,(gy,gx) in enumerate(cell_indices) if (gy,gx)==max_idx][:8]
                if opp_nodes:
                    opportunity_regions.append({"region": opp_nodes, "reason":"densest_cell"})

                # nodes in sparsest cell (but non-zero to avoid empty)
                sparse_nodes = [idx for idx,(gy,gx) in enumerate(cell_indices) if (gy,gx)==min_idx][:8]
                if sparse_nodes:
                    difficult_regions.append({"region": sparse_nodes, "reason":"sparsest_cell"})
        except Exception:
            pass
    
    # Choose appropriate prompt template based on problem size
    if node_count < 20:
        # Use simplified prompt for small problems
        prompt = LANDSCAPE_PROMPT_SIMPLE.format(
            iteration=iteration,
            node_count=node_count,
            min_cost=min_cost,
            diversity_level=diversity_level,
            improvement_rate=improvement_rate
        )
    else:
        # Use full prompt for medium and large problems
        prompt = LANDSCAPE_PROMPT.format(
            population_size=population_size,
            min_cost=min_cost,
            max_cost=max_cost,
            mean_cost=mean_cost,
            std_cost=std_cost,
            diversity_level=diversity_level,
            convergence_level=convergence_level,
            clustering_summary=clustering_summary,
            iteration=iteration,
            total_iterations=total_iterations,
            node_count=node_count,
            instance_name=instance_name,
            recent_cost_series=recent_cost_series,
            improvement_rate=improvement_rate,
            stagnation_flag=stagnation_flag,
            high_quality_edges_sample=high_quality_edges_sample,
            common_subpaths_sample=common_subpaths_sample,
            difficult_regions_sample=difficult_regions_sample,
            opportunity_regions_sample=opportunity_regions_sample,
            elite_count=elite_count,
            fixed_nodes_sample=fixed_nodes_sample,
            elite_common_edge_percentage=elite_common_edge_percentage,
            elite_common_features_sample=elite_common_features_sample,
            sp_bounding_box=sp_bbox,
            sp_centroid=sp_centroid,
            sp_rms_dist=sp_rms,
            sp_density_grid=sp_density,
            sp_edge_stats=sp_edge_stats,
            sp_long_edge_ratio=sp_long_ratio,
            sp_direction_hist=sp_dir_hist,
            sp_nn_median=sp_nn_med,
            elite_diversity_summary=elite_diversity_summary
        )

    return prompt


def generate_strategy_expert_prompt(landscape_report, population=None, previous_feedback=None, iteration=0):
    """Generate optimized strategy selection expert prompt"""
    
    # 导入需要的模块
    import re
    import json
    
    # ---------------------------------------------------------------------------------
    # Universal coercion: ensure landscape_report is dict early to avoid AttributeError
    # Accept str / None / other types gracefully.
    if not isinstance(landscape_report, dict):
        try:
            # attempt JSON parse directly
            landscape_report = json.loads(str(landscape_report))
            if not isinstance(landscape_report, dict):
                landscape_report = {"raw_text": str(landscape_report)}
        except Exception:
            landscape_report = {"raw_text": str(landscape_report)}
    
    # 处理landscape_report可能是字符串的情况
    if isinstance(landscape_report, str):
        try:
            # 尝试从字符串中提取JSON
            json_match = re.search(r'```json\s*({[\s\S]*?})\s*```', landscape_report)
            if json_match:
                landscape_report = json.loads(json_match.group(1))
            else:
                # 如果无法提取JSON，创建一个基本结构
                landscape_report = {
                    "search_space_features": {"ruggedness": 0.5, "modality": "unknown"},
                    "population_state": {"diversity": 0.5, "convergence": 0.5},
                    "difficult_regions": [],
                    "opportunity_regions": [],
                    "evolution_direction": {"recommended_focus": "balance"},
                    "raw_text": landscape_report  # 保留原始文本
                }
        except Exception:
            # 如果解析失败，创建一个基本结构
            landscape_report = {
                "search_space_features": {},
                "population_state": {},
                "difficult_regions": [],
                "opportunity_regions": [],
                "evolution_direction": {}
            }
    
    # 最后一重保障：确保 landscape_report 为 dict，防止后续属性访问报错
    if not isinstance(landscape_report, dict):
        try:
            landscape_report = json.loads(str(landscape_report)) if landscape_report else {}
        except Exception:
            landscape_report = {}
    
    # Extract and summarize population data
    population_size = 0
    lr: Dict[str, Any] = landscape_report if isinstance(landscape_report, dict) else {}
    population_diversity = lr.get("population_state", {}).get("diversity", 0)
    min_cost = 0
    mean_cost = 0
    
    if population:
        population_size = len(population)
        costs = [p.get("cur_cost", 0) for p in population]
        min_cost = min(costs) if costs else 0
        mean_cost = sum(costs) / len(costs) if costs else 0
    
    # Extract and summarize landscape data
    raw_features = lr.get("search_space_features", {})
    search_space_features = raw_features if isinstance(raw_features, dict) else {}
    ruggedness = search_space_features.get("ruggedness", "N/A")
    modality = search_space_features.get("modality", "N/A")
    deceptiveness = search_space_features.get("deceptiveness", "N/A")

    # concise summaries
    def summarize_list(lst, max_items=2):
        """Return concise string for list-like input, robust to other types"""
        if not lst:
            return "None"
        if not isinstance(lst, list):
            try:
                # if it's dict, take keys; if other iterable, cast to list
                lst = list(lst) if not isinstance(lst, dict) else list(lst.keys())
            except Exception:
                return str(lst)[:30]
        sample = lst[:max_items]
        sample_str = ", ".join(str(item)[:20] for item in sample)
        try:
            import json as _json
            sample_json = _json.dumps(sample, ensure_ascii=False)
        except Exception:
            sample_json = sample_str
        if len(lst) > max_items:
            return f"{len(lst)} items, first {max_items}: {sample_json}"
        return sample_json

    difficult_regions_brief = summarize_list(lr.get("difficult_regions", []))
    opportunity_regions_brief = summarize_list(lr.get("opportunity_regions", []))

    difficult_regions_count = len(lr.get("difficult_regions", []))
    opportunity_regions_count = len(lr.get("opportunity_regions", []))

    # Evolution & focus hints
    evolution_phase = lr.get("evolution_phase", "unknown")
    evo_raw = lr.get("evolution_direction", {})
    evo_dir = evo_raw if isinstance(evo_raw, dict) else {}
    recommended_focus = evo_dir.get("recommended_focus", "balance")

    # Spatial density summary if provided
    density_summary = lr.get("spatial_insights", "None")

    # Previous feedback brief
    previous_feedback_summary = (previous_feedback[:120] + "...") if previous_feedback else "None"

    # Elite diversity extracted from landscape report if available
    elite_diversity = lr.get("elite_diversity", lr.get("elite_common_edge_percentage", "N/A"))

    # Generate prompt with optimized data organization
    prompt = STRATEGY_PROMPT.format(
        iteration=iteration,
        population_size=population_size,
        population_diversity=round(population_diversity if isinstance(population_diversity,(int,float)) else 0, 3),
        min_cost=min_cost,
        mean_cost=round(mean_cost, 2),
        ruggedness=ruggedness,
        modality=modality,
        deceptiveness=deceptiveness,
        evolution_phase=evolution_phase,
        recommended_focus=recommended_focus,
        density_summary=density_summary,
        difficult_regions_count=difficult_regions_count,
        difficult_regions_brief=difficult_regions_brief,
        opportunity_regions_count=opportunity_regions_count,
        opportunity_regions_brief=opportunity_regions_brief,
        previous_feedback_summary=previous_feedback_summary,
        elite_diversity=elite_diversity
    )
    
    return prompt


def generate_exploration_expert_prompt(individual, population, landscape_report, strategy_params, distance_matrix=None, iteration=0):
    """Generate optimized exploration path generation expert prompt"""
    
    # 导入需要的模块
    import re
    import json
    
    # 处理landscape_report可能是字符串的情况
    if isinstance(landscape_report, str):
        try:
            # 尝试从字符串中提取JSON
            json_match = re.search(r'```json\s*({[\s\S]*?})\s*```', landscape_report)
            if json_match:
                landscape_report = json.loads(json_match.group(1))
            else:
                # 如果无法提取JSON，创建一个基本结构
                landscape_report = {
                    "search_space_features": {},
                    "difficult_regions": [],
                    "opportunity_regions": []
                }
        except Exception:
            # 如果解析失败，创建一个基本结构
            landscape_report = {
                "search_space_features": {},
                "difficult_regions": [],
                "opportunity_regions": []
            }
    
    # Extract and summarize current path data
    current_path = individual.get("tour", [])
    path_summary = format_path_summary(current_path)
    path_cost = individual.get("cur_cost", 0)
    
    # 确定路径长度和最大节点索引
    path_length = len(current_path) if current_path is not None else 0
    
    # 从距离矩阵确定最大节点索引
    max_node_index = 0
    if distance_matrix is not None and hasattr(distance_matrix, 'shape'):
        max_node_index = distance_matrix.shape[0] - 1
    else:
        # 如果没有提供距离矩阵，尝试从当前路径推断最大节点索引
        try:
            import numpy as np
            if isinstance(current_path, np.ndarray):
                if current_path.size > 0:
                    max_node_index = int(np.max(current_path))
            elif current_path:
                max_node_index = max(current_path)
        except ImportError:
            if current_path:
                max_node_index = max(current_path)
    
    # Extract key path features - 修复NumPy数组条件判断问题
    path_key_features = "Standard path"
    try:
        import numpy as np
        if isinstance(current_path, np.ndarray):
            # 对NumPy数组使用size属性检查是否为空
            if current_path.size > 0:
                # 检查路径长度和特征
                if len(current_path) > 20:
                    path_key_features = "Long path with potential for optimization"
                # 检查是否有重复节点 - 对NumPy数组使用np.unique
                elif len(np.unique(current_path)) < current_path.size * 0.9:
                    path_key_features = "Path with repeated nodes, needs correction"
        else:
            # 标准列表处理
            if current_path:
                # Identify any notable features like crossings or clusters
                if len(current_path) > 20:
                    path_key_features = "Long path with potential for optimization"
                elif len(set(current_path)) < len(current_path) * 0.9:
                    path_key_features = "Path with repeated nodes, needs correction"
    except ImportError:
        # 如果没有NumPy，使用标准处理
        if current_path:
            # Identify any notable features like crossings or clusters
            if len(current_path) > 20:
                path_key_features = "Long path with potential for optimization"
            elif len(set(current_path)) < len(current_path) * 0.9:
                path_key_features = "Path with repeated nodes, needs correction"
    
    # Calculate population diversity
    if isinstance(population, list) and population:
        unique_edges = set()
        total_edges = 0
        for p in population:
            path = p.get("tour", [])
            # 处理NumPy数组
            try:
                import numpy as np
                if isinstance(path, np.ndarray) and path.size > 0:
                    path_list = path.tolist()
                    for i in range(len(path_list) - 1):
                        edge = (min(path_list[i], path_list[i+1]), max(path_list[i], path_list[i+1]))
                        unique_edges.add(edge)
                        total_edges += 1
                elif not isinstance(path, np.ndarray) and path:
                    for i in range(len(path) - 1):
                        edge = (min(path[i], path[i+1]), max(path[i], path[i+1]))
                        unique_edges.add(edge)
                        total_edges += 1
            except ImportError:
                # 如果没有NumPy，使用标准处理
                if path:
                    for i in range(len(path) - 1):
                        edge = (min(path[i], path[i+1]), max(path[i], path[i+1]))
                        unique_edges.add(edge)
                        total_edges += 1
        population_diversity = len(unique_edges) / total_edges if total_edges > 0 else 0
    else:
        population_diversity = "Unknown"
    
    # Extract and summarize landscape data
    difficult_regions = landscape_report.get("difficult_regions", []) if isinstance(landscape_report, dict) else []
    opportunity_regions = landscape_report.get("opportunity_regions", []) if isinstance(landscape_report, dict) else []

    # concise list samples
    def brief(lst, n=2):
        """Return a short textual summary for difficult/opportunity region lists.

        This helper must cope with inputs that are *not* plain lists (e.g. dicts, sets,
        NumPy arrays). Previous implementation assumed list-slicing support which
        triggered a KeyError when a dict was passed in. We now coerce any iterable
        into a list of printable elements before slicing so that the function is
        resilient.
        """
        if not lst:
            return "None"

        # Normalise to a list for safe slicing. For dicts we use their keys; for other
        # iterables we attempt list() conversion, falling back to a single-element list
        # containing the string representation.
        if isinstance(lst, dict):
            lst_conv = list(lst.keys())
        else:
            try:
                lst_conv = list(lst)
            except Exception:
                lst_conv = [str(lst)]

        sample = lst_conv[:n]
        sample_str = ", ".join(str(it)[:15] for it in sample)
        return sample_str + ("..." if len(lst_conv) > n else "")

    difficult_regions_brief = brief(difficult_regions)
    opportunity_regions_brief = brief(opportunity_regions)

    # Evolution & focus hints
    evolution_phase = landscape_report.get("evolution_phase", "unknown")
    evo_raw = landscape_report.get("evolution_direction", {})
    evo_dir = evo_raw if isinstance(evo_raw, dict) else {}
    recommended_focus = evo_dir.get("recommended_focus", "balance")

    # Sparse cell hint – fallback heuristic based on opportunity/difficult counts
    if opportunity_regions:
        sparse_hint = "Explore additional low-density cells adjacent to opportunity regions"
    else:
        sparse_hint = "Explore cells with zero visits in density grid"

    # Extract strategy parameters (kept for future tuning but not included in prompt)
    _ = strategy_params.get("diversity_weight", 0.7)
    _ = strategy_params.get("risk_tolerance", 0.5)

    # rank percentile of individual cost
    cost_rank_pct = 50
    if population:
        costs_sorted = sorted([p.get("cur_cost", 0) for p in population])
        if costs_sorted:
            try:
                rank = costs_sorted.index(path_cost)
            except ValueError:
                rank = sum(c <= path_cost for c in costs_sorted)
            cost_rank_pct = round((rank / len(costs_sorted)) * 100, 1)

    prompt = EXPLORATION_PROMPT.format(
        iteration=iteration,
        path_summary=path_summary,
        path_cost=path_cost,
        cost_rank_pct=cost_rank_pct,
        population_diversity=population_diversity,
        evolution_phase=evolution_phase,
        recommended_focus=recommended_focus,
        opportunity_regions_brief=opportunity_regions_brief,
        difficult_regions_brief=difficult_regions_brief,
        sparse_hint=sparse_hint,
        max_node_index=max_node_index,
        path_length=path_length
    )
    
    return prompt


def generate_exploitation_expert_prompt(individual, population, landscape_report, strategy_params, elite_solutions):
    """Generate optimized exploitation path generation expert prompt"""
    
    # 导入需要的模块
    import re
    import json
    
    # 处理landscape_report可能是字符串的情况
    if isinstance(landscape_report, str):
        try:
            # 尝试从字符串中提取JSON
            json_match = re.search(r'```json\s*({[\s\S]*?})\s*```', landscape_report)
            if json_match:
                landscape_report = json.loads(json_match.group(1))
            else:
                # 如果无法提取JSON，创建一个基本结构
                landscape_report = {
                    "high_quality_edges": [],
                    "fixed_nodes": [],
                    "low_quality_regions": []
                }
        except Exception:
            # 如果解析失败，创建一个基本结构
            landscape_report = {
                "high_quality_edges": [],
                "fixed_nodes": [],
                "low_quality_regions": []
            }
    
    # Extract and summarize current path data
    current_path = individual.get("tour", [])
    path_summary = format_path_summary(current_path)
    path_cost = individual.get("cur_cost", 0)
    
    # Identify inefficient segments (simplified approach)
    inefficient_segments = "Not analyzed"
    
    # 处理NumPy数组
    try:
        import numpy as np
        if isinstance(current_path, np.ndarray):
            # 对NumPy数组使用size属性检查是否为空
            if current_path.size > 3:
                # This is a placeholder - actual implementation would use distance matrix
                # to identify segments with high relative cost
                inefficient_segments = "Middle segments potentially inefficient"
        else:
            # 标准列表处理
            if current_path and len(current_path) > 3:
                inefficient_segments = "Middle segments potentially inefficient"
    except ImportError:
        # 如果没有NumPy，使用标准处理
        if current_path and len(current_path) > 3:
            inefficient_segments = "Middle segments potentially inefficient"
    
    # Extract and summarize high quality edges
    high_quality_edges = landscape_report.get("high_quality_edges", []) if isinstance(landscape_report, dict) else []
    high_quality_edges_summary = f"{len(high_quality_edges)} edges identified" if high_quality_edges else "None identified"
    
    # Extract and summarize fixed nodes
    fixed_nodes = landscape_report.get("fixed_nodes", []) if isinstance(landscape_report, dict) else []
    fixed_nodes_summary = f"{len(fixed_nodes)} nodes identified" if fixed_nodes else "None identified"
    
    # Summarize elite solutions
    elite_solutions_summary = "No elite solutions available"
    if elite_solutions and isinstance(elite_solutions, list):
        elite_costs = [e.get("cur_cost", 0) for e in elite_solutions]
        best_elite_cost = min(elite_costs) if elite_costs else 0
        elite_solutions_summary = f"{len(elite_solutions)} solutions, best cost: {best_elite_cost}"
    
    # Identify improvement opportunities based on elite solutions
    improvement_opportunities = "General optimization needed"
    
    # 处理NumPy数组
    try:
        import numpy as np
        if isinstance(current_path, np.ndarray):
            # 对NumPy数组使用size属性检查是否为空
            if current_path.size > 0 and elite_solutions:
                improvement_opportunities = "Focus on incorporating elite solution patterns"
        else:
            # 标准列表处理
            if current_path and elite_solutions:
                improvement_opportunities = "Focus on incorporating elite solution patterns"
    except ImportError:
        # 如果没有NumPy，使用标准处理
        if current_path and elite_solutions:
            improvement_opportunities = "Focus on incorporating elite solution patterns"
    
    # Extract strategy parameters
    quality_edge_usage = strategy_params.get("quality_edge_usage", 0.7)
    elite_influence = strategy_params.get("elite_influence", 0.5)
    local_search_depth = strategy_params.get("local_search_depth", 2)
    
    # Generate prompt with optimized data organization
    prompt = EXPLOITATION_PROMPT.format(
        path_summary=path_summary,
        path_cost=path_cost,
        inefficient_segments=inefficient_segments,
        high_quality_edges_summary=high_quality_edges_summary,
        fixed_nodes_summary=fixed_nodes_summary,
        elite_solutions_summary=elite_solutions_summary,
        improvement_opportunities=improvement_opportunities,
        quality_edge_usage=quality_edge_usage,
        elite_influence=elite_influence,
        local_search_depth=local_search_depth
    )
    
    return prompt


def generate_assessment_expert_prompt(old_population, new_population, strategy_assignment, strategy_results, iteration, total_iterations, history_data=None, old_res_populations=None, new_res_populations=None):
    """Generate optimized evolution assessment expert prompt"""
    
    # 确保输入参数是正确的类型
    if not isinstance(old_population, list):
        old_population = []
    if not isinstance(new_population, list):
        new_population = []
    if not isinstance(strategy_assignment, dict):
        strategy_assignment = {"individual_assignments": {}}
    if not isinstance(strategy_results, dict):
        strategy_results = {"explore": {"success_count": 0, "improvement_sum": 0}, 
                           "exploit": {"success_count": 0, "improvement_sum": 0}}
    
    # Extract and summarize population changes
    old_costs = [p.get("cur_cost", 0) for p in old_population]
    new_costs = [p.get("cur_cost", 0) for p in new_population]
    
    old_min_cost = min(old_costs) if old_costs else 0
    new_min_cost = min(new_costs) if new_costs else 0
    old_mean_cost = sum(old_costs) / len(old_costs) if old_costs else 0
    new_mean_cost = sum(new_costs) / len(new_costs) if new_costs else 0
    
    # Calculate simple diversity metrics
    def calculate_simple_diversity(population):
        if not population:
            return 0
        unique_edges = set()
        total_edges = 0
        
        for p in population:
            path = p.get("tour", [])
            
            # 处理NumPy数组
            try:
                import numpy as np
                if isinstance(path, np.ndarray):
                    # 对NumPy数组使用size属性检查是否为空
                    if path.size > 1:  # 至少需要两个节点才能形成边
                        path_list = path.tolist()
                        for i in range(len(path_list) - 1):
                            edge = (min(path_list[i], path_list[i+1]), max(path_list[i], path_list[i+1]))
                            unique_edges.add(edge)
                            total_edges += 1
                else:
                    # 标准列表处理
                    if path and len(path) > 1:  # 至少需要两个节点才能形成边
                        for i in range(len(path) - 1):
                            edge = (min(path[i], path[i+1]), max(path[i], path[i+1]))
                            unique_edges.add(edge)
                            total_edges += 1
            except ImportError:
                # 如果没有NumPy，使用标准处理
                if path and len(path) > 1:  # 至少需要两个节点才能形成边
                    for i in range(len(path) - 1):
                        edge = (min(path[i], path[i+1]), max(path[i], path[i+1]))
                        unique_edges.add(edge)
                        total_edges += 1
        
        return len(unique_edges) / total_edges if total_edges > 0 else 0
    
    old_diversity = calculate_simple_diversity(old_population)
    new_diversity = calculate_simple_diversity(new_population)
    
    # Elite solution statistics from provided elite populations if available
    if isinstance(old_res_populations, list) and old_res_populations:
        old_elite_count = len(old_res_populations)
        old_best_elite_cost = min(e.get("cur_cost", float('inf')) for e in old_res_populations)
    else:
        old_elite_count = 0
        old_best_elite_cost = old_min_cost

    if isinstance(new_res_populations, list) and new_res_populations:
        new_elite_count = len(new_res_populations)
        new_best_elite_cost = min(e.get("cur_cost", float('inf')) for e in new_res_populations)
    else:
        new_elite_count = 0
        new_best_elite_cost = new_min_cost

    elite_cost_improvement = old_best_elite_cost - new_best_elite_cost
    
    # Extract and summarize strategy performance
    individual_assignments = strategy_assignment.get("individual_assignments", {})
    explore_count = sum(1 for s in individual_assignments.values() if s == "explore")
    exploit_count = sum(1 for s in individual_assignments.values() if s == "exploit")
    
    # Calculate strategy success rates and improvements
    explore_success = strategy_results.get("explore", {}).get("success_count", 0)
    explore_improvement = strategy_results.get("explore", {}).get("improvement_sum", 0)
    exploit_success = strategy_results.get("exploit", {}).get("success_count", 0)
    exploit_improvement = strategy_results.get("exploit", {}).get("improvement_sum", 0)
    
    explore_success_rate = (explore_success / explore_count * 100) if explore_count > 0 else 0
    exploit_success_rate = (exploit_success / exploit_count * 100) if exploit_count > 0 else 0
    explore_avg_improvement = (explore_improvement / explore_count) if explore_count > 0 else 0
    exploit_avg_improvement = (exploit_improvement / exploit_count) if exploit_count > 0 else 0
    
    overall_improvement = old_min_cost - new_min_cost

    # No-change / regression counts
    no_change_count = sum(1 for o, n in zip(old_population, new_population) if abs(o.get("cur_cost",0)-n.get("cur_cost",0))<1e-6)
    regression_count = sum(1 for o, n in zip(old_population, new_population) if n.get("cur_cost",0) > o.get("cur_cost",0)+1e-6)
    
    # Summarize historical trends
    historical_trends_summary = "N/A"
    if history_data and isinstance(history_data, list):
        trend_points = min(len(history_data), 3)
        if trend_points > 0:
            try:
                cost_trend = [h.get("new_stats", {}).get("min_cost", 0) for h in history_data[-trend_points:]]
                diversity_trend = [h.get("new_stats", {}).get("diversity", 0) for h in history_data[-trend_points:]]
                historical_trends_summary = f"Last {trend_points} iterations: costs {cost_trend}, diversity {diversity_trend}"
            except Exception:
                historical_trends_summary = "Error processing historical data"
    
    # Additional metrics
    diversity_delta = round(new_diversity - old_diversity, 4)
    min_cost_improvement = overall_improvement
    
    # --- elite diversity metrics ---
    old_elite_diversity = calculate_simple_diversity(old_res_populations) if isinstance(old_res_populations, list) and old_res_populations else 0
    new_elite_diversity = calculate_simple_diversity(new_res_populations) if isinstance(new_res_populations, list) and new_res_populations else 0
    elite_diversity_delta = round(new_elite_diversity - old_elite_diversity, 4)
    
    # Generate prompt with optimized data organization
    prompt = ASSESSMENT_PROMPT.format(
        iteration=iteration,
        total_iterations=total_iterations,
        old_min_cost=old_min_cost,
        new_min_cost=new_min_cost,
        old_mean_cost=old_mean_cost,
        new_mean_cost=new_mean_cost,
        old_diversity=old_diversity,
        new_diversity=new_diversity,
        diversity_delta=diversity_delta,
        old_elite_count=old_elite_count,
        new_elite_count=new_elite_count,
        old_best_elite_cost=old_best_elite_cost,
        new_best_elite_cost=new_best_elite_cost,
        elite_cost_improvement=elite_cost_improvement,
        min_cost_improvement=min_cost_improvement,
        explore_count=explore_count,
        exploit_count=exploit_count,
        explore_success_rate=round(explore_success_rate, 1),
        exploit_success_rate=round(exploit_success_rate, 1),
        explore_avg_improvement=round(explore_avg_improvement, 2),
        exploit_avg_improvement=round(exploit_avg_improvement, 2),
        overall_improvement=overall_improvement,
        historical_trends_summary=historical_trends_summary,
        no_change_count=no_change_count,
        regression_count=regression_count,
        old_elite_diversity=old_elite_diversity,
        new_elite_diversity=new_elite_diversity,
        elite_diversity_delta=elite_diversity_delta
    )
    
    return prompt


# Response parsing function
def parse_expert_response(response_text):
    """Parse expert response to extract JSON data"""
    # Extract JSON part
    json_match = re.search(r'```json\s*(.+?)\s*```', response_text, re.DOTALL)
    
    if not json_match:
        # Try to find any JSON-like structure
        json_match = re.search(r'({[\s\S]*})', response_text)
    
    if not json_match:
        # Try to parse the entire response
        try:
            return json.loads(response_text)
        except:
            # If all else fails, return a structured error
            return {
                "error": "Unable to extract valid JSON data from the response",
                "raw_response": response_text[:1000] + ("..." if len(response_text) > 1000 else "")
            }
    
    # Parse JSON
    try:
        json_str = json_match.group(1)
        return json.loads(json_str)
    except:
        # Return structured error with the extracted text
        return {
            "error": "Extracted JSON data is invalid",
            "extracted_text": json_str[:1000] + ("..." if len(json_str) > 1000 else ""),
            "raw_response": response_text[:1000] + ("..." if len(response_text) > 1000 else "")
        }