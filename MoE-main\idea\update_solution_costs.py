import os
import numpy as np
import sys
import os.path

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 尝试导入不同的LoadInstace实现
try:
    from loadinstance import LoadInstace
except ImportError:
    try:
        from idea.loadinstance import LoadInstace
    except ImportError:
        from idea.loadinstance_tsplib import LoadInstace

def update_solution_costs(instance_name, input_path, solution_file_path):
    """
    计算并更新指定解集文件中所有解的成本
    
    参数:
        instance_name: TSP实例名称
        input_path: TSP实例文件所在目录
        solution_file_path: 解集文件路径
    
    返回:
        bool: 更新是否成功
    """
    try:
        # 尝试使用标准LoadInstace加载TSP实例
        try:
            from loadinstance import LoadInstace as StandardLoadInstace
            instance = StandardLoadInstace(instance_name, input_path)
        except Exception as e:
            # 如果标准加载失败（可能是因为没有.solution文件），尝试使用TSPLIB加载器
            from idea.loadinstance_tsplib import LoadInstace as TSPLIBLoadInstace
            instance = TSPLIBLoadInstace(instance_name, input_path)
            print(f"使用TSPLIB加载器加载实例 {instance_name}")
        
        # 获取距离矩阵
        distance_matrix = instance.get_distance_matrix()
        
        # 读取解集文件
        with open(solution_file_path, 'r') as f:
            solutions = f.readlines()
        
        updated_solutions = []
        
        # 处理每个解
        for solution_line in solutions:
            if not solution_line.strip():
                updated_solutions.append('\n')
                continue
                
            parts = solution_line.strip().split()
            if len(parts) < 2:
                updated_solutions.append(solution_line)
                continue
                
            # 提取路径部分
            path = [int(p) for p in parts[1:]]
            
            # 计算实际成本
            cost = int(instance.calculate_path_length(distance_matrix, path))
            
            # 更新解的成本
            updated_solution = f"{cost} " + " ".join(parts[1:]) + "\n"
            updated_solutions.append(updated_solution)
        
        # 写回文件
        with open(solution_file_path, 'w') as f:
            f.writelines(updated_solutions)
        
        print(f"成功更新文件 {solution_file_path} 中的解集成本")
        return True
        
    except Exception as e:
        print(f"更新解集成本时出错: {str(e)}")
        return False

def batch_update_solution_costs(instance_names, input_path, solutions_dir):
    """
    批量更新指定目录下与实例相关的所有解集文件的成本
    
    参数:
        instance_names: TSP实例名称列表或单个实例名称
        input_path: TSP实例文件所在目录
        solutions_dir: 解集文件所在目录
    """
    # 确保instance_names是列表
    if isinstance(instance_names, str):
        instance_names = [instance_names]
    
    count = 0
    # 获取目录下所有.solution文件
    solution_files = [f for f in os.listdir(solutions_dir) if f.endswith('.solution')]
    
    for instance_name in instance_names:
        for filename in solution_files:
            # 检查文件名是否以实例名开头，并且后面跟着下划线（表示时间戳部分）
            if filename.startswith(instance_name + '_') or filename == instance_name + '.solution':
                solution_path = os.path.join(solutions_dir, filename)
                if update_solution_costs(instance_name, input_path, solution_path):
                    count += 1
    
    print(f"共更新了 {count} 个解集文件")
    return count

if __name__ == "__main__":
    # 示例用法
    input_path = r"C:\Users\<USER>\Desktop\EoH-main - idea - 0509_0bug-fa-feedback\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\benchmark_MMTSP"
    solutions_dir = r"C:\Users\<USER>\Desktop\EoH-main - idea - 0509_0bug-fa-feedback\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution"
    
    # # 示例1：更新单个解集文件
    # instance_name = "geometry5_10"
    # solution_file = r"C:\Users\<USER>\Desktop\EoH-main - idea - 0407-perturb\EoH-main - idea - 0312-feedback\EoH-main\results\solutions\geometry5_10_20250409_170219.solution"
    # update_solution_costs(instance_name, input_path, solution_file)
    
    # 示例2：批量更新单个实例的所有解集文件
    # batch_update_solution_costs("composite1_28", input_path, solutions_dir)
    
    # 示例3：批量更新多个实例的所有解集文件
    # 定义需要更新的实例列表
    instance_list = [
        "simple1_9", "simple2_10", "simple3_10", "simple4_11", "simple5_12", "simple6_12",
        "geometry1_10", "geometry2_12", "geometry3_10", "geometry4_10", "geometry5_10", "geometry6_15",
        "composite1_28", "composite2_34", "composite3_22", "composite4_33", "composite5_35", "composite6_39", 
        "composite7_42", "composite8_45", "composite9_48", "composite10_55", "composite11_59", "composite12_60", "composite13_66",
        "eil51","berlin52","st70","pr76","kroA100","lin105"
        # 可以添加更多实例名称
    ]
    batch_update_solution_costs(instance_list, input_path, solutions_dir)