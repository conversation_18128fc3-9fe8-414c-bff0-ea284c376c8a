import time
import numpy as np
import logging

class ProgressCalculator:
    """
    多维度进度计算器，用于计算算法的进度，结合多种因素：
    1. 迭代进度：当前迭代次数与最大迭代次数的比值
    2. 时间进度：已用时间与总时间限制的比值
    3. 解质量进度：当前解与初始解和历史最优解的差距
    4. 收敛状态：连续无改进次数
    5. 路径多样性：对于多解TSP问题，考虑路径结构的变化
    """
    
    def __init__(self, max_iterations=100, time_limit=60, problem_size=None, initial_cost=None, initial_path=None):
        """
        初始化进度计算器
        
        参数:
            max_iterations: 最大迭代次数
            time_limit: 时间限制（秒）
            problem_size: 问题规模（城市数量）
            initial_cost: 初始解成本
            initial_path: 初始解路径
        """
        self.max_iterations = max_iterations
        self.time_limit = time_limit
        self.problem_size = problem_size
        self.initial_cost = initial_cost
        self.best_cost = initial_cost
        self.start_time = time.time()
        self.no_improvement_count = 0
        self.last_improvement_time = time.time()
        self.history_costs = [initial_cost] if initial_cost is not None else []
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 多解TSP问题相关属性
        self.initial_path = initial_path
        self.best_paths = [initial_path] if initial_path is not None else []
        self.current_path = initial_path
        self.path_diversity = 0.0  # 路径多样性指标
        self.path_change_count = 0  # 路径结构变化次数
        self.last_path_change_time = time.time()  # 上次路径结构变化时间
    
    def update_best_cost(self, current_cost, current_path=None):
        """
        更新最佳成本记录
        
        参数:
            current_cost: 当前解的成本
            current_path: 当前解的路径
            
        返回:
            bool: 是否有改进
        """
        if current_cost is None:
            return False
            
        self.history_costs.append(current_cost)
        self.current_path = current_path
        
        # 如果是第一次设置成本
        if self.best_cost is None:
            self.best_cost = current_cost
            self.initial_cost = current_cost
            self.last_improvement_time = time.time()
            if current_path is not None:
                self.initial_path = current_path
                self.best_paths = [current_path]
            return True
        
        # 检查是否有改进
        if current_cost < self.best_cost:
            improvement = (self.best_cost - current_cost) / self.best_cost
            self.best_cost = current_cost
            self.last_improvement_time = time.time()
            self.no_improvement_count = 0
            if current_path is not None:
                self.best_paths = [current_path]  # 重置最优路径列表
                self.path_change_count += 1
                self.last_path_change_time = time.time()
            self.logger.info(f"发现更优解: {current_cost}, 改进幅度: {improvement:.4f}")
            return True
        elif current_cost == self.best_cost and current_path is not None:
            # 发现等价最优解，检查路径是否不同
            is_new_path = True
            for path in self.best_paths:
                if self.calculate_path_similarity(path, current_path) > 0.9:  # 相似度阈值
                    is_new_path = False
                    break
            
            if is_new_path:
                self.best_paths.append(current_path)
                self.path_change_count += 1
                self.last_path_change_time = time.time()
                self.no_improvement_count = 0  # 发现新的等价最优解也算是一种改进
                self.logger.info(f"发现等价最优解，路径结构不同，当前已有{len(self.best_paths)}个等价最优解")
                return True
            else:
                self.no_improvement_count += 1
                return False
        else:
            self.no_improvement_count += 1
            return False
            
    def calculate_path_similarity(self, path1, path2):
        """
        计算两条路径的相似度
        
        参数:
            path1: 路径1
            path2: 路径2
            
        返回:
            float: 相似度，范围[0,1]，1表示完全相同
        """
        if path1 is None or path2 is None:
            return 0.0
            
        # 如果路径长度不同，直接返回0
        if len(path1) != len(path2):
            return 0.0
            
        # 计算共同边的数量
        n = len(path1)
        common_edges = 0
        
        # 将路径转换为边集合
        edges1 = set()
        edges2 = set()
        
        for i in range(n):
            # 添加正向和反向边（无向图）
            edge1 = (path1[i], path1[(i+1) % n])
            edge1_rev = (path1[(i+1) % n], path1[i])
            edges1.add(edge1)
            edges1.add(edge1_rev)
            
            edge2 = (path2[i], path2[(i+1) % n])
            edge2_rev = (path2[(i+1) % n], path2[i])
            edges2.add(edge2)
            edges2.add(edge2_rev)
        
        # 计算交集大小
        common_edges = len(edges1.intersection(edges2)) // 2  # 除以2因为我们计算了正向和反向边
        
        # 计算相似度
        similarity = common_edges / n
        
        return similarity
    
    def calculate_iteration_progress(self, current_iteration):
        """
        计算迭代进度
        
        参数:
            current_iteration: 当前迭代次数
            
        返回:
            float: 迭代进度，范围[0,1]
        """
        return min(1.0, current_iteration / self.max_iterations if self.max_iterations > 0 else 0.5)
    
    def calculate_time_progress(self):
        """
        计算时间进度
        
        返回:
            float: 时间进度，范围[0,1]
        """
        elapsed_time = time.time() - self.start_time
        return min(1.0, elapsed_time / self.time_limit if self.time_limit > 0 else 0.5)
    
    def calculate_quality_progress(self, current_cost, current_path=None):
        """
        计算解质量进度，结合当前解与初始解和历史最优解的差距，以及路径多样性
        
        参数:
            current_cost: 当前解的成本
            current_path: 当前解的路径
            
        返回:
            float: 质量进度，范围[0,1]
        """
        if self.initial_cost is None or current_cost is None or self.best_cost is None:
            return 0.5
        
        # 如果初始解就是最优解，返回1.0
        if self.initial_cost <= self.best_cost:
            return 1.0
        
        # 计算当前解与初始解的差距比例
        initial_gap = (self.initial_cost - current_cost) / (self.initial_cost - self.best_cost + 1e-10)
        
        # 计算当前解与最优解的差距比例
        best_gap = (current_cost - self.best_cost) / (self.initial_cost - self.best_cost + 1e-10)
        
        # 基础质量进度
        base_quality_progress = initial_gap / (initial_gap + best_gap + 1e-10)
        
        # 如果是多解问题（已发现多个等价最优解），考虑路径多样性
        diversity_factor = 0.0
        if current_path is not None and len(self.best_paths) > 1:
            # 计算当前路径与所有最优路径的最大相似度
            max_similarity = 0.0
            for path in self.best_paths:
                similarity = self.calculate_path_similarity(current_path, path)
                max_similarity = max(max_similarity, similarity)
            
            # 多样性因子 = 1 - 最大相似度
            diversity_factor = 1.0 - max_similarity
            
            # 更新路径多样性指标
            self.path_diversity = max(self.path_diversity, diversity_factor)
            
            # 根据已发现的等价最优解数量调整权重
            diversity_weight = min(0.3, 0.1 * len(self.best_paths))  # 最多占30%
            base_weight = 1.0 - diversity_weight
            
            # 结合基础质量进度和多样性因子
            quality_progress = base_weight * base_quality_progress + diversity_weight * diversity_factor
        else:
            quality_progress = base_quality_progress
        
        return min(1.0, max(0.0, quality_progress))
    
    def calculate_convergence_progress(self):
        """
        计算收敛进度，基于连续无改进次数和路径结构变化
        
        返回:
            float: 收敛进度，范围[0,1]
        """
        # 根据问题规模动态调整收敛阈值
        if self.problem_size is None:
            convergence_threshold = 20  # 默认阈值
        else:
            # 问题规模越大，收敛阈值越大
            convergence_threshold = max(10, min(50, int(self.problem_size * 0.2)))
        
        # 计算基础收敛进度
        base_convergence_progress = min(1.0, self.no_improvement_count / convergence_threshold)
        
        # 如果长时间没有改进，增加收敛进度
        time_since_last_improvement = time.time() - self.last_improvement_time
        time_factor = min(1.0, time_since_last_improvement / (self.time_limit * 0.3))
        
        # 对于多解问题，考虑路径结构变化
        if len(self.best_paths) > 1:
            # 如果长时间没有发现新的路径结构，增加收敛进度
            time_since_last_path_change = time.time() - self.last_path_change_time
            path_change_factor = min(1.0, time_since_last_path_change / (self.time_limit * 0.2))
            
            # 根据已发现的等价最优解数量调整收敛判断
            # 发现的等价最优解越多，收敛程度越高
            path_diversity_factor = min(1.0, len(self.best_paths) / (self.problem_size * 0.1 if self.problem_size else 10))
            
            # 结合基础收敛进度、时间因素和路径变化因素
            convergence_progress = max(base_convergence_progress, time_factor, path_change_factor * path_diversity_factor)
        else:
            # 结合基础收敛进度和时间因素
            convergence_progress = max(base_convergence_progress, time_factor)
        
        return min(1.0, convergence_progress)
    
    def calculate_progress(self, current_iteration, current_cost=None, current_path=None, end_time=None):
        """
        计算综合进度，结合迭代进度、时间进度、质量进度和收敛进度
        
        参数:
            current_iteration: 当前迭代次数
            current_cost: 当前解的成本
            current_path: 当前解的路径
            end_time: 结束时间，如果提供则用于计算剩余时间
            
        返回:
            float: 综合进度，范围[0,1]
            dict: 各维度进度详情
        """
        # 更新最佳成本
        if current_cost is not None:
            self.update_best_cost(current_cost, current_path)
        
        # 计算各维度进度
        iteration_progress = self.calculate_iteration_progress(current_iteration)
        time_progress = self.calculate_time_progress()
        quality_progress = self.calculate_quality_progress(current_cost, current_path)
        convergence_progress = self.calculate_convergence_progress()
        
        # 根据问题规模动态调整各维度权重
        if self.problem_size is None or self.problem_size < 50:
            # 小规模问题：更注重迭代进度和质量进度
            weights = {
                'iteration': 0.3,
                'time': 0.2,
                'quality': 0.3,
                'convergence': 0.2
            }
        elif self.problem_size < 100:
            # 中等规模问题：平衡各维度
            weights = {
                'iteration': 0.25,
                'time': 0.25,
                'quality': 0.25,
                'convergence': 0.25
            }
        else:
            # 大规模问题：更注重时间进度和收敛进度
            weights = {
                'iteration': 0.2,
                'time': 0.3,
                'quality': 0.2,
                'convergence': 0.3
            }
        
        # 计算综合进度
        progress = (
            weights['iteration'] * iteration_progress +
            weights['time'] * time_progress +
            weights['quality'] * quality_progress +
            weights['convergence'] * convergence_progress
        )
        
        # 确保进度在[0,1]范围内
        progress = min(1.0, max(0.0, progress))
        
        # 构建详情字典
        details = {
            'iteration_progress': iteration_progress,
            'time_progress': time_progress,
            'quality_progress': quality_progress,
            'convergence_progress': convergence_progress,
            'weights': weights,
            'no_improvement_count': self.no_improvement_count,
            'best_cost': self.best_cost,
            'initial_cost': self.initial_cost,
            'best_paths_count': len(self.best_paths),
            'path_diversity': self.path_diversity,
            'path_change_count': self.path_change_count
        }
        
        return progress, details

# 全局进度计算器实例
_global_progress_calculator = None

def get_progress_calculator(max_iterations=100, time_limit=60, problem_size=None, initial_cost=None, initial_path=None):
    """
    获取全局进度计算器实例，如果不存在则创建
    
    参数:
        max_iterations: 最大迭代次数
        time_limit: 时间限制（秒）
        problem_size: 问题规模（城市数量）
        initial_cost: 初始解成本
        initial_path: 初始解路径
        
    返回:
        ProgressCalculator: 进度计算器实例
    """
    global _global_progress_calculator
    
    if _global_progress_calculator is None:
        _global_progress_calculator = ProgressCalculator(
            max_iterations=max_iterations,
            time_limit=time_limit,
            problem_size=problem_size,
            initial_cost=initial_cost,
            initial_path=initial_path
        )
    
    return _global_progress_calculator

def calculate_progress(current_iteration, max_iterations=None, current_cost=None, current_path=None, initial_cost=None, 
                      initial_path=None, problem_size=None, time_limit=None, end_time=None):
    """
    计算算法进度的便捷函数
    
    参数:
        current_iteration: 当前迭代次数
        max_iterations: 最大迭代次数
        current_cost: 当前解的成本
        current_path: 当前解的路径
        initial_cost: 初始解成本
        initial_path: 初始解路径
        problem_size: 问题规模（城市数量）
        time_limit: 时间限制（秒）
        end_time: 结束时间
        
    返回:
        float: 综合进度，范围[0,1]
    """
    # 获取全局进度计算器
    calculator = get_progress_calculator(
        max_iterations=max_iterations or 100,
        time_limit=time_limit or 60,
        problem_size=problem_size,
        initial_cost=initial_cost,
        initial_path=initial_path
    )
    
    # 计算进度
    progress, _ = calculator.calculate_progress(
        current_iteration=current_iteration,
        current_cost=current_cost,
        current_path=current_path,
        end_time=end_time
    )
    
    return progress