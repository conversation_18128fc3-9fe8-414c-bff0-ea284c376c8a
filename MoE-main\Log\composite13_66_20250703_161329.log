2025-07-03 16:13:29,414 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-03 16:13:29,415 - __main__ - INFO - 开始分析阶段
2025-07-03 16:13:29,415 - StatsExpert - INFO - 开始统计分析
2025-07-03 16:13:29,435 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9863.0, 'max': 117265.0, 'mean': 75372.2, 'std': 43378.89816212487}, 'diversity': 0.9252525252525252, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 16:13:29,436 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9863.0, 'max': 117265.0, 'mean': 75372.2, 'std': 43378.89816212487}, 'diversity_level': 0.9252525252525252, 'convergence_level': 0.0, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 16:13:29,445 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 16:13:29,445 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 16:13:29,445 - PathExpert - INFO - 开始路径结构分析
2025-07-03 16:13:29,452 - PathExpert - INFO - 路径结构分析完成
2025-07-03 16:13:29,453 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (52, 63), 'frequency': 0.5, 'avg_cost': 19.0}], 'common_subpaths': [{'subpath': (64, 57, 54), 'frequency': 0.3}, {'subpath': (65, 52, 63), 'frequency': 0.3}, {'subpath': (9, 11, 7), 'frequency': 0.3}, {'subpath': (11, 7, 3), 'frequency': 0.3}, {'subpath': (7, 3, 1), 'frequency': 0.3}, {'subpath': (3, 1, 0), 'frequency': 0.3}, {'subpath': (1, 0, 10), 'frequency': 0.3}, {'subpath': (22, 12, 17), 'frequency': 0.3}, {'subpath': (18, 16, 23), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(55, 61)', 'frequency': 0.4}, {'edge': '(56, 59)', 'frequency': 0.4}, {'edge': '(52, 65)', 'frequency': 0.4}, {'edge': '(52, 63)', 'frequency': 0.5}, {'edge': '(20, 21)', 'frequency': 0.4}, {'edge': '(47, 49)', 'frequency': 0.4}, {'edge': '(39, 44)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 60)', 'frequency': 0.2}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.3}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(7, 11)', 'frequency': 0.3}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 3)', 'frequency': 0.3}, {'edge': '(0, 1)', 'frequency': 0.3}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(14, 19)', 'frequency': 0.2}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.2}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.2}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.3}, {'edge': '(34, 35)', 'frequency': 0.2}, {'edge': '(25, 33)', 'frequency': 0.2}, {'edge': '(36, 37)', 'frequency': 0.2}, {'edge': '(31, 37)', 'frequency': 0.2}, {'edge': '(18, 27)', 'frequency': 0.2}, {'edge': '(22, 23)', 'frequency': 0.3}, {'edge': '(15, 17)', 'frequency': 0.2}, {'edge': '(14, 15)', 'frequency': 0.2}, {'edge': '(14, 20)', 'frequency': 0.2}, {'edge': '(13, 21)', 'frequency': 0.2}, {'edge': '(13, 19)', 'frequency': 0.2}, {'edge': '(9, 19)', 'frequency': 0.2}, {'edge': '(8, 10)', 'frequency': 0.2}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(5, 55)', 'frequency': 0.2}, {'edge': '(53, 61)', 'frequency': 0.2}, {'edge': '(56, 58)', 'frequency': 0.2}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.2}, {'edge': '(39, 63)', 'frequency': 0.2}, {'edge': '(41, 46)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.3}, {'edge': '(42, 48)', 'frequency': 0.2}, {'edge': '(34, 42)', 'frequency': 0.2}, {'edge': '(27, 34)', 'frequency': 0.2}, {'edge': '(7, 24)', 'frequency': 0.2}, {'edge': '(60, 65)', 'frequency': 0.2}, {'edge': '(56, 61)', 'frequency': 0.2}, {'edge': '(2, 30)', 'frequency': 0.2}, {'edge': '(16, 22)', 'frequency': 0.2}, {'edge': '(21, 30)', 'frequency': 0.2}, {'edge': '(14, 62)', 'frequency': 0.2}, {'edge': '(4, 25)', 'frequency': 0.2}, {'edge': '(2, 44)', 'frequency': 0.2}, {'edge': '(0, 37)', 'frequency': 0.2}, {'edge': '(5, 45)', 'frequency': 0.2}, {'edge': '(63, 64)', 'frequency': 0.2}, {'edge': '(9, 31)', 'frequency': 0.2}, {'edge': '(18, 42)', 'frequency': 0.2}, {'edge': '(8, 28)', 'frequency': 0.2}, {'edge': '(1, 39)', 'frequency': 0.3}, {'edge': '(15, 61)', 'frequency': 0.2}, {'edge': '(16, 59)', 'frequency': 0.2}, {'edge': '(5, 7)', 'frequency': 0.2}, {'edge': '(24, 36)', 'frequency': 0.2}, {'edge': '(13, 39)', 'frequency': 0.2}, {'edge': '(4, 29)', 'frequency': 0.2}, {'edge': '(4, 64)', 'frequency': 0.2}, {'edge': '(8, 62)', 'frequency': 0.2}, {'edge': '(6, 38)', 'frequency': 0.2}, {'edge': '(28, 33)', 'frequency': 0.3}, {'edge': '(10, 44)', 'frequency': 0.2}, {'edge': '(42, 50)', 'frequency': 0.2}, {'edge': '(4, 54)', 'frequency': 0.2}, {'edge': '(2, 46)', 'frequency': 0.2}, {'edge': '(21, 49)', 'frequency': 0.2}, {'edge': '(13, 42)', 'frequency': 0.2}, {'edge': '(11, 15)', 'frequency': 0.2}, {'edge': '(14, 34)', 'frequency': 0.2}, {'edge': '(27, 40)', 'frequency': 0.2}, {'edge': '(23, 35)', 'frequency': 0.2}, {'edge': '(35, 39)', 'frequency': 0.2}, {'edge': '(45, 65)', 'frequency': 0.2}, {'edge': '(1, 26)', 'frequency': 0.2}, {'edge': '(31, 44)', 'frequency': 0.2}, {'edge': '(9, 28)', 'frequency': 0.2}, {'edge': '(41, 63)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [11, 48, 54, 41, 63, 24, 58], 'cost': 16854.0, 'size': 7}, {'region': [28, 41, 34, 57, 45, 37], 'cost': 12272.0, 'size': 6}, {'region': [30, 2, 44, 10, 40, 27], 'cost': 11673.0, 'size': 6}, {'region': [65, 29, 53, 43, 9], 'cost': 11218.0, 'size': 5}, {'region': [43, 58, 44, 10, 50], 'cost': 10655.0, 'size': 5}]}
2025-07-03 16:13:29,453 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 16:13:29,453 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 16:13:29,454 - EliteExpert - INFO - 开始精英解分析
2025-07-03 16:13:29,454 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-03 16:13:29,454 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-07-03 16:13:29,454 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 16:13:29,454 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 16:13:30,036 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 16:13:30,037 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9863.0, mean 75372.2, max 117265.0, std 43378.89816212487
- diversity: 0.9252525252525252
- convergence: 0.0
- clustering: 9 clusters, sizes [1, 2, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (52, 63), 'frequency': 0.5, 'avg_cost': 19.0}]
- common_subpaths_sample: [{'subpath': (64, 57, 54), 'frequency': 0.3}, {'subpath': (65, 52, 63), 'frequency': 0.3}, {'subpath': (9, 11, 7), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [11, 48, 54, 41, 63, 24, 58], 'cost': 16854.0, 'size': 7}, {'region': [28, 41, 34, 57, 45, 37], 'cost': 12272.0, 'size': 6}, {'region': [30, 2, 44, 10, 40, 27], 'cost': 11673.0, 'size': 6}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-03 16:13:30,038 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:13:56,096 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "moderate"
  },
  "population_state": {
    "diversity": 0.925,
    "convergence": 0.000,
    "clustering": "strong"
  },
  "difficult_regions": [
    {"region": [11, 48, 54, 41, 63, 24, 58], "cost": 16854.0, "size": 7},
    {"region": [28, 41, 34, 57, 45, 37], "cost": 12272.0, "size": 6},
    {"region": [30, 2, 44, 10, 40, 27], "cost": 11673.0, "size": 6},
    {"region": [15, 22, 39, 50, 61], "cost": 9872.0, "size": 5},
    {"region": [5, 18, 33, 47, 59], "cost": 10543.0, "size": 5}
  ],
  "opportunity_regions": [
    {"region": [52, 63, 64, 57, 54], "cost": 5432.0, "size": 5},
    {"region": [65, 52, 63, 9, 11, 7], "cost": 6789.0, "size": 6},
    {"region": [3, 6, 12, 19, 25], "cost": 4321.0, "size": 5}
  ],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["crossover", "mutation"]
  }
}
```
2025-07-03 16:13:56,096 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 16:13:56,096 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.925, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'region': [11, 48, 54, 41, 63, 24, 58], 'cost': 16854.0, 'size': 7}, {'region': [28, 41, 34, 57, 45, 37], 'cost': 12272.0, 'size': 6}, {'region': [30, 2, 44, 10, 40, 27], 'cost': 11673.0, 'size': 6}, {'region': [15, 22, 39, 50, 61], 'cost': 9872.0, 'size': 5}, {'region': [5, 18, 33, 47, 59], 'cost': 10543.0, 'size': 5}], 'opportunity_regions': [{'region': [52, 63, 64, 57, 54], 'cost': 5432.0, 'size': 5}, {'region': [65, 52, 63, 9, 11, 7], 'cost': 6789.0, 'size': 6}, {'region': [3, 6, 12, 19, 25], 'cost': 4321.0, 'size': 5}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:13:56,097 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 16:13:56,097 - __main__ - INFO - 分析阶段完成
2025-07-03 16:13:56,097 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.925, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'region': [11, 48, 54, 41, 63, 24, 58], 'cost': 16854.0, 'size': 7}, {'region': [28, 41, 34, 57, 45, 37], 'cost': 12272.0, 'size': 6}, {'region': [30, 2, 44, 10, 40, 27], 'cost': 11673.0, 'size': 6}, {'region': [15, 22, 39, 50, 61], 'cost': 9872.0, 'size': 5}, {'region': [5, 18, 33, 47, 59], 'cost': 10543.0, 'size': 5}], 'opportunity_regions': [{'region': [52, 63, 64, 57, 54], 'cost': 5432.0, 'size': 5}, {'region': [65, 52, 63, 9, 11, 7], 'cost': 6789.0, 'size': 6}, {'region': [3, 6, 12, 19, 25], 'cost': 4321.0, 'size': 5}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:13:56,098 - __main__ - INFO - 开始策略分配阶段
2025-07-03 16:13:56,098 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 16:13:56,098 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.925
  • best_cost: 9863.0
  • mean_cost: 75372.2
- Search-space: ruggedness high, modality multi, deceptiveness moderate
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 5): 5 items, first 2: [{"region": [11, 48, 54, 41, 63, 24, 58], "cost": 16854.0, "size": 7}, {"region": [28, 41, 34, 57, 45, 37], "cost": 12272.0, "size": 6}]
- Opportunity regions (# 3): 3 items, first 2: [{"region": [52, 63, 64, 57, 54], "cost": 5432.0, "size": 5}, {"region": [65, 52, 63, 9, 11, 7], "cost": 6789.0, "size": 6}]
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 16:13:56,099 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 16:13:56,099 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:14:07,908 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early phase favor exploration; top 30% exploit opportunity regions."
}
```
2025-07-03 16:14:07,909 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 16:14:07,909 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 16:14:07,909 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early phase favor exploration; top 30% exploit opportunity regions."
}
```
2025-07-03 16:14:07,909 - __main__ - INFO - 策略分配阶段完成
2025-07-03 16:14:07,909 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 16:14:07,910 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early phase favor exploration; top 30% exploit opportunity regions."
}
```
2025-07-03 16:14:07,910 - __main__ - INFO - 开始进化阶段
2025-07-03 16:14:07,910 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 16:14:07,910 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:14:07,911 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10090.0 (rank 20.0%)
- Population diversity: 0.7569230769230769
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [52,, {'region': [65,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [28,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:14:07,911 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:14:07,911 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:14:24,227 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11],
  "targeted_regions": "region_52, region_65",
  "strategy_comment": "Prioritizes opportunity regions, skips difficult nodes."
}
```
2025-07-03 16:14:24,228 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:14:24,228 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40090.0, 路径: [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11]
2025-07-03 16:14:24,228 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}
2025-07-03 16:14:24,229 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 16:14:24,229 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:14:24,229 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9863.0 (rank 0.0%)
- Population diversity: 0.796923076923077
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [52,, {'region': [65,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [28,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:14:24,230 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:14:24,230 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:14:40,200 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 11, 28, 66],
  "targeted_regions": "region_52_65",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes."
}
```
2025-07-03 16:14:40,200 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:14:40,200 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17486.0, 路径: [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 11, 28]
2025-07-03 16:14:40,200 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 11, 28], 'cur_cost': 17486.0}
2025-07-03 16:14:40,202 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 16:14:40,202 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:14:40,202 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9890.0 (rank 0.0%)
- Population diversity: 0.8584615384615385
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [52,, {'region': [65,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [28,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:14:40,202 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:14:40,202 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:14:56,993 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 28, 11, 66],
  "targeted_regions": "region_52, region_65",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes."
}
```
2025-07-03 16:14:56,994 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:14:56,994 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17412.0, 路径: [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 28, 11]
2025-07-03 16:14:56,995 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 28, 11], 'cur_cost': 17412.0}
2025-07-03 16:14:56,995 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 16:14:56,995 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:14:56,996 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 90358.0 (rank 30.0%)
- Population diversity: 0.7938461538461539
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [52,, {'region': [65,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [28,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:14:56,996 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:14:56,996 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:15:14,959 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 52, 65, 12, 34, 47, 23, 56, 8, 19, 42, 30, 61, 5, 27, 38, 50, 14, 33, 45, 60, 7, 21, 39, 54, 16, 29, 44, 58, 10, 25, 37, 49, 63, 3, 18, 41, 55, 9, 24, 36, 48, 62, 2, 17, 40, 53, 15, 31, 46, 59, 6, 20, 35, 51, 13, 32, 43, 57, 11, 26, 64, 4, 22, 28, 1],
  "targeted_regions": "region_52, region_65, sparse_adjacent",
  "strategy_comment": "Prioritizes opportunity regions and adjacent sparse cells while avoiding difficult areas."
}
```
2025-07-03 16:15:14,960 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:15:14,960 - ExplorationExpert - INFO - 探索路径生成完成，成本: 107403.0, 路径: [0, 52, 65, 12, 34, 47, 23, 56, 8, 19, 42, 30, 61, 5, 27, 38, 50, 14, 33, 45, 60, 7, 21, 39, 54, 16, 29, 44, 58, 10, 25, 37, 49, 63, 3, 18, 41, 55, 9, 24, 36, 48, 62, 2, 17, 40, 53, 15, 31, 46, 59, 6, 20, 35, 51, 13, 32, 43, 57, 11, 26, 64, 4, 22, 28, 1]
2025-07-03 16:15:14,960 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 52, 65, 12, 34, 47, 23, 56, 8, 19, 42, 30, 61, 5, 27, 38, 50, 14, 33, 45, 60, 7, 21, 39, 54, 16, 29, 44, 58, 10, 25, 37, 49, 63, 3, 18, 41, 55, 9, 24, 36, 48, 62, 2, 17, 40, 53, 15, 31, 46, 59, 6, 20, 35, 51, 13, 32, 43, 57, 11, 26, 64, 4, 22, 28, 1], 'cur_cost': 107403.0}
2025-07-03 16:15:14,961 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 16:15:14,961 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:15:14,961 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 117265.0 (rank 90.0%)
- Population diversity: 0.7876923076923077
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [52,, {'region': [65,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [28,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:15:14,962 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:15:14,962 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:15:31,262 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11],
  "targeted_regions": "region_52, region_65",
  "strategy_comment": "Prioritizes opportunity regions while avoiding difficult nodes."
}
```
2025-07-03 16:15:31,263 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:15:31,263 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40090.0, 路径: [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11]
2025-07-03 16:15:31,264 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}
2025-07-03 16:15:31,264 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 16:15:31,264 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:15:31,265 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100948.0 (rank 50.0%)
- Population diversity: 0.7107692307692308
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [52,, {'region': [65,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [28,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:15:31,265 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:15:31,266 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:15:47,673 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 5, 9, 13, 16, 20, 24, 28, 32, 36, 40, 43, 46, 49, 53, 56, 59, 62, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 41, 44, 47, 50, 55, 58, 61, 64, 3, 8, 11, 15, 18, 22, 26, 30, 34, 38],
  "targeted_regions": "region_52, region_65",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes."
}
```
2025-07-03 16:15:47,673 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:15:47,673 - ExplorationExpert - INFO - 探索路径生成完成，成本: 36590.0, 路径: [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 5, 9, 13, 16, 20, 24, 28, 32, 36, 40, 43, 46, 49, 53, 56, 59, 62, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 41, 44, 47, 50, 55, 58, 61, 64, 3, 8, 11, 15, 18, 22, 26, 30, 34, 38]
2025-07-03 16:15:47,674 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 5, 9, 13, 16, 20, 24, 28, 32, 36, 40, 43, 46, 49, 53, 56, 59, 62, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 41, 44, 47, 50, 55, 58, 61, 64, 3, 8, 11, 15, 18, 22, 26, 30, 34, 38], 'cur_cost': 36590.0}
2025-07-03 16:15:47,674 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-03 16:15:47,674 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:15:47,676 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 96717.0 (rank 50.0%)
- Population diversity: 0.6646153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [52,, {'region': [65,...
- Difficult regions to avoid (sample): {'region': [11,, {'region': [28,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:15:47,676 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:15:47,676 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:16:04,350 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11],
  "targeted_regions": "region_52, region_65",
  "strategy_comment": "Prioritizes opportunity regions while avoiding difficult nodes."
}
```
2025-07-03 16:16:04,351 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:16:04,351 - ExplorationExpert - INFO - 探索路径生成完成，成本: 40090.0, 路径: [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11]
2025-07-03 16:16:04,351 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}
2025-07-03 16:16:04,352 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 16:16:04,352 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:16:04,354 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:16:04,355 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 112760.0
2025-07-03 16:16:07,084 - ExploitationExpert - INFO - res_population_num: 1
2025-07-03 16:16:07,085 - ExploitationExpert - INFO - res_population_costs: [92204]
2025-07-03 16:16:07,085 - ExploitationExpert - INFO - res_populations: [array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64)]
2025-07-03 16:16:07,085 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:16:07,085 - ExploitationExpert - INFO - populations: [{'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 11, 28], 'cur_cost': 17486.0}, {'tour': [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 28, 11], 'cur_cost': 17412.0}, {'tour': [0, 52, 65, 12, 34, 47, 23, 56, 8, 19, 42, 30, 61, 5, 27, 38, 50, 14, 33, 45, 60, 7, 21, 39, 54, 16, 29, 44, 58, 10, 25, 37, 49, 63, 3, 18, 41, 55, 9, 24, 36, 48, 62, 2, 17, 40, 53, 15, 31, 46, 59, 6, 20, 35, 51, 13, 32, 43, 57, 11, 26, 64, 4, 22, 28, 1], 'cur_cost': 107403.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 5, 9, 13, 16, 20, 24, 28, 32, 36, 40, 43, 46, 49, 53, 56, 59, 62, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 41, 44, 47, 50, 55, 58, 61, 64, 3, 8, 11, 15, 18, 22, 26, 30, 34, 38], 'cur_cost': 36590.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': array([61, 24, 27, 49, 36, 16, 59, 46, 54,  4,  2,  7, 13, 37, 48, 45, 28,
        1, 41, 19, 34, 53, 62, 35,  5, 42, 30, 65, 50, 22, 11, 60,  8, 47,
       64, 25,  9, 15, 17, 38,  3, 55, 10, 29, 57, 12,  0, 39, 18, 58, 56,
       31, 14, 20, 52, 44, 40, 51, 33, 23, 21, 63, 26,  6, 43, 32]), 'cur_cost': 112760.0}, {'tour': [5, 54, 60, 65, 19, 42, 3, 29, 4, 25, 38, 52, 45, 12, 36, 16, 58, 26, 23, 35, 2, 57, 53, 40, 27, 44, 31, 13, 55, 50, 6, 30, 21, 22, 56, 59, 8, 28, 9, 24, 32, 17, 64, 39, 1, 61, 15, 11, 63, 41, 46, 10, 18, 20, 37, 0, 43, 51, 48, 34, 62, 14, 47, 49, 33, 7], 'cur_cost': 103765.0}, {'tour': [59, 25, 51, 17, 44, 31, 40, 26, 1, 39, 35, 11, 48, 54, 41, 63, 24, 58, 9, 28, 33, 56, 49, 37, 43, 19, 14, 34, 27, 10, 12, 57, 47, 16, 64, 0, 21, 7, 60, 55, 8, 62, 22, 20, 6, 53, 36, 46, 2, 61, 18, 32, 4, 50, 42, 13, 30, 23, 15, 38, 29, 52, 65, 45, 5, 3], 'cur_cost': 111119.0}]
2025-07-03 16:16:07,087 - ExploitationExpert - INFO - 局部搜索耗时: 2.73秒
2025-07-03 16:16:07,087 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-03 16:16:07,087 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-03 16:16:07,088 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 16:16:07,088 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:16:07,088 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:16:07,088 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 88951.0
2025-07-03 16:16:08,088 - ExploitationExpert - INFO - res_population_num: 2
2025-07-03 16:16:08,088 - ExploitationExpert - INFO - res_population_costs: [92204, 9552]
2025-07-03 16:16:08,089 - ExploitationExpert - INFO - res_populations: [array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:16:08,090 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:16:08,090 - ExploitationExpert - INFO - populations: [{'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 11, 28], 'cur_cost': 17486.0}, {'tour': [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 28, 11], 'cur_cost': 17412.0}, {'tour': [0, 52, 65, 12, 34, 47, 23, 56, 8, 19, 42, 30, 61, 5, 27, 38, 50, 14, 33, 45, 60, 7, 21, 39, 54, 16, 29, 44, 58, 10, 25, 37, 49, 63, 3, 18, 41, 55, 9, 24, 36, 48, 62, 2, 17, 40, 53, 15, 31, 46, 59, 6, 20, 35, 51, 13, 32, 43, 57, 11, 26, 64, 4, 22, 28, 1], 'cur_cost': 107403.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 5, 9, 13, 16, 20, 24, 28, 32, 36, 40, 43, 46, 49, 53, 56, 59, 62, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 41, 44, 47, 50, 55, 58, 61, 64, 3, 8, 11, 15, 18, 22, 26, 30, 34, 38], 'cur_cost': 36590.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': array([61, 24, 27, 49, 36, 16, 59, 46, 54,  4,  2,  7, 13, 37, 48, 45, 28,
        1, 41, 19, 34, 53, 62, 35,  5, 42, 30, 65, 50, 22, 11, 60,  8, 47,
       64, 25,  9, 15, 17, 38,  3, 55, 10, 29, 57, 12,  0, 39, 18, 58, 56,
       31, 14, 20, 52, 44, 40, 51, 33, 23, 21, 63, 26,  6, 43, 32]), 'cur_cost': 112760.0}, {'tour': array([24, 43, 56, 61, 38, 16, 28, 37, 13, 50, 17, 54, 31, 27, 60, 21,  0,
       44, 30, 12, 46,  3,  2, 62, 64, 52, 19, 42, 39, 45, 36, 15, 22, 57,
       53, 18, 40, 41, 25,  6, 55,  8,  9, 48, 51, 58, 29, 34, 33, 65, 11,
        4,  1,  7, 32, 59, 20, 14, 49, 47, 10, 35, 63,  5, 26, 23]), 'cur_cost': 88951.0}, {'tour': [59, 25, 51, 17, 44, 31, 40, 26, 1, 39, 35, 11, 48, 54, 41, 63, 24, 58, 9, 28, 33, 56, 49, 37, 43, 19, 14, 34, 27, 10, 12, 57, 47, 16, 64, 0, 21, 7, 60, 55, 8, 62, 22, 20, 6, 53, 36, 46, 2, 61, 18, 32, 4, 50, 42, 13, 30, 23, 15, 38, 29, 52, 65, 45, 5, 3], 'cur_cost': 111119.0}]
2025-07-03 16:16:08,091 - ExploitationExpert - INFO - 局部搜索耗时: 1.00秒
2025-07-03 16:16:08,091 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-03 16:16:08,091 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 16:16:08,092 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 16:16:08,092 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:16:08,092 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:16:08,092 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 103659.0
2025-07-03 16:16:08,594 - ExploitationExpert - INFO - res_population_num: 12
2025-07-03 16:16:08,595 - ExploitationExpert - INFO - res_population_costs: [92204, 9552, 9545, 9545, 9541, 9534, 9533, 9526, 9526, 9521, 9521, 9521]
2025-07-03 16:16:08,595 - ExploitationExpert - INFO - res_populations: [array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:16:08,600 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:16:08,600 - ExploitationExpert - INFO - populations: [{'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 11, 28], 'cur_cost': 17486.0}, {'tour': [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 28, 11], 'cur_cost': 17412.0}, {'tour': [0, 52, 65, 12, 34, 47, 23, 56, 8, 19, 42, 30, 61, 5, 27, 38, 50, 14, 33, 45, 60, 7, 21, 39, 54, 16, 29, 44, 58, 10, 25, 37, 49, 63, 3, 18, 41, 55, 9, 24, 36, 48, 62, 2, 17, 40, 53, 15, 31, 46, 59, 6, 20, 35, 51, 13, 32, 43, 57, 11, 26, 64, 4, 22, 28, 1], 'cur_cost': 107403.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 5, 9, 13, 16, 20, 24, 28, 32, 36, 40, 43, 46, 49, 53, 56, 59, 62, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 41, 44, 47, 50, 55, 58, 61, 64, 3, 8, 11, 15, 18, 22, 26, 30, 34, 38], 'cur_cost': 36590.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': array([61, 24, 27, 49, 36, 16, 59, 46, 54,  4,  2,  7, 13, 37, 48, 45, 28,
        1, 41, 19, 34, 53, 62, 35,  5, 42, 30, 65, 50, 22, 11, 60,  8, 47,
       64, 25,  9, 15, 17, 38,  3, 55, 10, 29, 57, 12,  0, 39, 18, 58, 56,
       31, 14, 20, 52, 44, 40, 51, 33, 23, 21, 63, 26,  6, 43, 32]), 'cur_cost': 112760.0}, {'tour': array([24, 43, 56, 61, 38, 16, 28, 37, 13, 50, 17, 54, 31, 27, 60, 21,  0,
       44, 30, 12, 46,  3,  2, 62, 64, 52, 19, 42, 39, 45, 36, 15, 22, 57,
       53, 18, 40, 41, 25,  6, 55,  8,  9, 48, 51, 58, 29, 34, 33, 65, 11,
        4,  1,  7, 32, 59, 20, 14, 49, 47, 10, 35, 63,  5, 26, 23]), 'cur_cost': 88951.0}, {'tour': array([16, 65, 19,  3, 23,  7, 50, 26, 47, 42, 51, 33, 49, 21,  2,  6, 32,
        9, 44, 40, 56,  8, 30, 12, 43, 20,  4, 55, 39, 10, 63, 34,  5, 62,
        0, 25, 57, 59, 61, 28, 18, 46, 38, 14, 24, 45, 15, 41, 36, 48, 54,
       31, 35,  1, 13, 11, 22, 58, 52, 37, 17, 53, 29, 27, 64, 60]), 'cur_cost': 103659.0}]
2025-07-03 16:16:08,601 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:16:08,601 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-03 16:16:08,602 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 16:16:08,602 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 11, 28], 'cur_cost': 17486.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 52, 65, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 28, 11], 'cur_cost': 17412.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 52, 65, 12, 34, 47, 23, 56, 8, 19, 42, 30, 61, 5, 27, 38, 50, 14, 33, 45, 60, 7, 21, 39, 54, 16, 29, 44, 58, 10, 25, 37, 49, 63, 3, 18, 41, 55, 9, 24, 36, 48, 62, 2, 17, 40, 53, 15, 31, 46, 59, 6, 20, 35, 51, 13, 32, 43, 57, 11, 26, 64, 4, 22, 28, 1], 'cur_cost': 107403.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 5, 9, 13, 16, 20, 24, 28, 32, 36, 40, 43, 46, 49, 53, 56, 59, 62, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 41, 44, 47, 50, 55, 58, 61, 64, 3, 8, 11, 15, 18, 22, 26, 30, 34, 38], 'cur_cost': 36590.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}}]
2025-07-03 16:16:08,603 - __main__ - INFO - 进化阶段完成
2025-07-03 16:16:08,603 - __main__ - INFO - 开始评估阶段
2025-07-03 16:16:08,603 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 16:16:08,605 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 16:16:08,605 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9863.0 → 17412.0 (Δ -7549.0)
- Mean cost: 75372.2 → 60453.1
- Diversity: 0.757 → 0.594 (Δ -0.1631)

#### Elite metrics
- Elite count: 0 → 12
- Best elite cost: 9863.0 → 9521 (Δ 342.0)
- Elite diversity: 0.000 → 0.222 (Δ 0.2218)

#### Strategy performance
- Explore: 7 inds, success 42.9% , avg Δ 19424.29
- Exploit: 3 inds, success 66.7% , avg Δ 4407.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 5
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 16:16:08,606 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:16:17,428 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.429, "exploit": 0.667},
  "balance_state": "exploit_heavy",
  "recommendations": ["increase_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:16:17,459 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 16:16:17,459 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.429, "exploit": 0.667},
  "balance_state": "exploit_heavy",
  "recommendations": ["increase_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:16:17,459 - __main__ - INFO - 评估阶段完成
2025-07-03 16:16:17,460 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.429, "exploit": 0.667},
  "balance_state": "exploit_heavy",
  "recommendations": ["increase_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:16:17,460 - __main__ - INFO - 当前最佳适应度: 17412.0
2025-07-03 16:16:17,462 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_0.pkl
2025-07-03 16:16:17,462 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-03 16:16:17,463 - __main__ - INFO - 开始分析阶段
2025-07-03 16:16:17,463 - StatsExpert - INFO - 开始统计分析
2025-07-03 16:16:17,480 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 17412.0, 'max': 112760.0, 'mean': 60453.1, 'std': 36237.1467349459}, 'diversity': 0.8461279461279461, 'clusters': {'clusters': 7, 'cluster_sizes': [3, 2, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 16:16:17,481 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 17412.0, 'max': 112760.0, 'mean': 60453.1, 'std': 36237.1467349459}, 'diversity_level': 0.8461279461279461, 'convergence_level': 0.0, 'clusters': {'clusters': 7, 'cluster_sizes': [3, 2, 1, 1, 1, 1, 1], 'summary': '7 clusters, sizes [3, 2, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 7, 'cluster_sizes': [3, 2, 1, 1, 1, 1, 1], 'summary': '7 clusters, sizes [3, 2, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 16:16:17,481 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 16:16:17,481 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 16:16:17,481 - PathExpert - INFO - 开始路径结构分析
2025-07-03 16:16:17,485 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 16:16:17,486 - PathExpert - INFO - 路径结构分析完成
2025-07-03 16:16:17,486 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 16:16:17,486 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 16:16:17,486 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 16:16:17,486 - EliteExpert - INFO - 开始精英解分析
2025-07-03 16:16:17,490 - EliteExpert - INFO - 精英解分析完成
2025-07-03 16:16:17,490 - __main__ - INFO - 精英专家分析报告: {'elite_count': 12, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 7891.0, 'avg_gap': 44030.683333333334}, 'structure_gap': {'unique_elite_edges': 223, 'unique_pop_edges': 366, 'common_edges': 42}}, 'elite_diversity': {'diversity_score': 0.3608815426997245}}
2025-07-03 16:16:17,491 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 16:16:17,491 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 16:16:17,493 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 16:16:17,493 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 17412.0, mean 60453.1, max 112760.0, std 36237.1467349459
- diversity: 0.8461279461279461
- convergence: 0.0
- clustering: 7 clusters, sizes [3, 2, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [17412.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.3608815426997245}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-03 16:16:17,494 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:16:32,774 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.846,
    "convergence": 0.000,
    "clustering": "weak"
  },
  "difficult_regions": [
    {"node": 1, "position": 1},
    {"node": 2, "position": 2},
    {"node": 3, "position": 3}
  ],
  "opportunity_regions": [
    {"node": 4, "position": 4},
    {"node": 5, "position": 5},
    {"node": 6, "position": 6}
  ],
  "evolution_phase": "exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["crossover", "mutation"]
  }
}
```
2025-07-03 16:16:32,774 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 16:16:32,774 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.846, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:16:32,775 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 16:16:32,775 - __main__ - INFO - 分析阶段完成
2025-07-03 16:16:32,775 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.846, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:16:32,775 - __main__ - INFO - 开始策略分配阶段
2025-07-03 16:16:32,775 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 16:16:32,777 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.846
  • best_cost: 17412.0
  • mean_cost: 60453.1
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: exploration
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 3): 3 items, first 2: [{"node": 1, "position": 1}, {"node": 2, "position": 2}]
- Opportunity regions (# 3): 3 items, first 2: [{"node": 4, "position": 4}, {"node": 5, "position": 5}]
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.429, "exploit": 0.667},
  "balance_state...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 16:16:32,777 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 16:16:32,778 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:16:43,773 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.70,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High diversity and exploration focus; 70% explore to maintain diversity."
}
```
2025-07-03 16:16:43,774 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:16:43,774 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:16:43,774 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.70,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High diversity and exploration focus; 70% explore to maintain diversity."
}
```
2025-07-03 16:16:43,774 - __main__ - INFO - 策略分配阶段完成
2025-07-03 16:16:43,774 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:16:43,774 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.70,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "High diversity and exploration focus; 70% explore to maintain diversity."
}
```
2025-07-03 16:16:43,776 - __main__ - INFO - 开始进化阶段
2025-07-03 16:16:43,776 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 16:16:43,776 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:16:43,777 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 40090.0 (rank 30.0%)
- Population diversity: 0.5938461538461538
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:16:43,777 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:16:43,777 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:17:00,523 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Focuses on opportunity nodes, avoids difficult regions, maintains diversity"
}
```
2025-07-03 16:17:00,524 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:17:00,524 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:17:00,524 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:17:00,525 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 16:17:00,525 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:17:00,525 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17486.0 (rank 20.0%)
- Population diversity: 0.6092307692307692
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:17:00,526 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:17:00,526 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:17:16,755 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions, maintains diversity"
}
```
2025-07-03 16:17:16,755 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:17:16,755 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:17:16,755 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:17:16,756 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-03 16:17:16,756 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:17:16,756 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:17:16,756 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 97833.0
2025-07-03 16:17:17,259 - ExploitationExpert - INFO - res_population_num: 17
2025-07-03 16:17:17,259 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9526, 9526, 9533, 9534, 9541, 9545, 9545, 9552, 92204, 9521, 9521, 9521, 9521, 9521]
2025-07-03 16:17:17,260 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-03 16:17:17,266 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:17:17,266 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([22, 27, 39, 14, 48,  4, 37, 20, 59, 63, 61, 64, 46, 49, 10, 42, 32,
       29,  1, 56, 53,  7, 38, 44, 23, 43, 65,  9,  6,  0, 62, 50, 31, 18,
       26, 25, 58,  3,  2, 40, 35, 45, 30,  8, 16, 54, 24, 12, 15, 55, 19,
        5, 57, 34, 41, 60, 33, 21, 17, 11, 52, 28, 47, 51, 13, 36]), 'cur_cost': 97833.0}, {'tour': [0, 52, 65, 12, 34, 47, 23, 56, 8, 19, 42, 30, 61, 5, 27, 38, 50, 14, 33, 45, 60, 7, 21, 39, 54, 16, 29, 44, 58, 10, 25, 37, 49, 63, 3, 18, 41, 55, 9, 24, 36, 48, 62, 2, 17, 40, 53, 15, 31, 46, 59, 6, 20, 35, 51, 13, 32, 43, 57, 11, 26, 64, 4, 22, 28, 1], 'cur_cost': 107403.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 5, 9, 13, 16, 20, 24, 28, 32, 36, 40, 43, 46, 49, 53, 56, 59, 62, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 41, 44, 47, 50, 55, 58, 61, 64, 3, 8, 11, 15, 18, 22, 26, 30, 34, 38], 'cur_cost': 36590.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': array([61, 24, 27, 49, 36, 16, 59, 46, 54,  4,  2,  7, 13, 37, 48, 45, 28,
        1, 41, 19, 34, 53, 62, 35,  5, 42, 30, 65, 50, 22, 11, 60,  8, 47,
       64, 25,  9, 15, 17, 38,  3, 55, 10, 29, 57, 12,  0, 39, 18, 58, 56,
       31, 14, 20, 52, 44, 40, 51, 33, 23, 21, 63, 26,  6, 43, 32]), 'cur_cost': 112760.0}, {'tour': array([24, 43, 56, 61, 38, 16, 28, 37, 13, 50, 17, 54, 31, 27, 60, 21,  0,
       44, 30, 12, 46,  3,  2, 62, 64, 52, 19, 42, 39, 45, 36, 15, 22, 57,
       53, 18, 40, 41, 25,  6, 55,  8,  9, 48, 51, 58, 29, 34, 33, 65, 11,
        4,  1,  7, 32, 59, 20, 14, 49, 47, 10, 35, 63,  5, 26, 23]), 'cur_cost': 88951.0}, {'tour': array([16, 65, 19,  3, 23,  7, 50, 26, 47, 42, 51, 33, 49, 21,  2,  6, 32,
        9, 44, 40, 56,  8, 30, 12, 43, 20,  4, 55, 39, 10, 63, 34,  5, 62,
        0, 25, 57, 59, 61, 28, 18, 46, 38, 14, 24, 45, 15, 41, 36, 48, 54,
       31, 35,  1, 13, 11, 22, 58, 52, 37, 17, 53, 29, 27, 64, 60]), 'cur_cost': 103659.0}]
2025-07-03 16:17:17,268 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:17:17,268 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-03 16:17:17,268 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-07-03 16:17:17,269 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 16:17:17,269 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:17:17,269 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 107403.0 (rank 80.0%)
- Population diversity: 0.6784615384615384
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:17:17,270 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:17:17,270 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:17:33,109 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions, maintains diversity."
}
```
2025-07-03 16:17:33,110 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:17:33,110 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:17:33,111 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:17:33,111 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-03 16:17:33,111 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:17:33,111 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:17:33,112 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 103761.0
2025-07-03 16:17:33,613 - ExploitationExpert - INFO - res_population_num: 19
2025-07-03 16:17:33,614 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9526, 9526, 9533, 9534, 9541, 9545, 9545, 9552, 92204, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 16:17:33,614 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-03 16:17:33,621 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:17:33,621 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([22, 27, 39, 14, 48,  4, 37, 20, 59, 63, 61, 64, 46, 49, 10, 42, 32,
       29,  1, 56, 53,  7, 38, 44, 23, 43, 65,  9,  6,  0, 62, 50, 31, 18,
       26, 25, 58,  3,  2, 40, 35, 45, 30,  8, 16, 54, 24, 12, 15, 55, 19,
        5, 57, 34, 41, 60, 33, 21, 17, 11, 52, 28, 47, 51, 13, 36]), 'cur_cost': 97833.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([23, 63, 12, 27, 17, 30, 65, 31, 13, 21, 53,  0, 20,  9, 18, 46, 41,
       42, 33,  4,  5, 40, 59, 10, 28, 57, 44, 45, 64, 61, 62, 56, 48, 32,
       58, 24, 51, 47, 14, 39, 55, 52, 50, 34, 29, 16, 26, 19,  1, 15,  6,
       36, 35, 54, 38,  3, 22, 43,  8, 11, 37, 49, 60, 25,  2,  7]), 'cur_cost': 103761.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 1, 5, 9, 13, 16, 20, 24, 28, 32, 36, 40, 43, 46, 49, 53, 56, 59, 62, 2, 6, 10, 14, 17, 21, 25, 29, 33, 37, 41, 44, 47, 50, 55, 58, 61, 64, 3, 8, 11, 15, 18, 22, 26, 30, 34, 38], 'cur_cost': 36590.0}, {'tour': [0, 52, 65, 4, 7, 12, 19, 23, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 2, 6, 10, 14, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 3, 8, 15, 20, 24, 28, 32, 36, 40, 44, 48, 56, 60, 64, 16, 11], 'cur_cost': 40090.0}, {'tour': array([61, 24, 27, 49, 36, 16, 59, 46, 54,  4,  2,  7, 13, 37, 48, 45, 28,
        1, 41, 19, 34, 53, 62, 35,  5, 42, 30, 65, 50, 22, 11, 60,  8, 47,
       64, 25,  9, 15, 17, 38,  3, 55, 10, 29, 57, 12,  0, 39, 18, 58, 56,
       31, 14, 20, 52, 44, 40, 51, 33, 23, 21, 63, 26,  6, 43, 32]), 'cur_cost': 112760.0}, {'tour': array([24, 43, 56, 61, 38, 16, 28, 37, 13, 50, 17, 54, 31, 27, 60, 21,  0,
       44, 30, 12, 46,  3,  2, 62, 64, 52, 19, 42, 39, 45, 36, 15, 22, 57,
       53, 18, 40, 41, 25,  6, 55,  8,  9, 48, 51, 58, 29, 34, 33, 65, 11,
        4,  1,  7, 32, 59, 20, 14, 49, 47, 10, 35, 63,  5, 26, 23]), 'cur_cost': 88951.0}, {'tour': array([16, 65, 19,  3, 23,  7, 50, 26, 47, 42, 51, 33, 49, 21,  2,  6, 32,
        9, 44, 40, 56,  8, 30, 12, 43, 20,  4, 55, 39, 10, 63, 34,  5, 62,
        0, 25, 57, 59, 61, 28, 18, 46, 38, 14, 24, 45, 15, 41, 36, 48, 54,
       31, 35,  1, 13, 11, 22, 58, 52, 37, 17, 53, 29, 27, 64, 60]), 'cur_cost': 103659.0}]
2025-07-03 16:17:33,624 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:17:33,624 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-07-03 16:17:33,624 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-07-03 16:17:33,624 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 16:17:33,624 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:17:33,625 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 36590.0 (rank 30.0%)
- Population diversity: 0.6723076923076923
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:17:33,625 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:17:33,625 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:17:50,378 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions, maintains diversity"
}
```
2025-07-03 16:17:50,379 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:17:50,379 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:17:50,380 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:17:50,380 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 16:17:50,380 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:17:50,380 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:17:50,381 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 110501.0
2025-07-03 16:17:50,381 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：4,跳过局部搜索
2025-07-03 16:17:50,381 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:17:50,382 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([14,  9, 17,  8, 34, 29,  6, 45,  1, 39, 54,  5, 37, 44, 51, 32, 62,
       61, 16, 57, 41, 25,  4, 53, 42, 58, 46, 10, 18, 64, 30,  2, 43, 52,
       59,  3, 24, 50, 26, 38, 40, 11, 23, 31, 60, 28, 49, 35, 56,  7, 65,
       55, 21, 36, 33, 47, 22, 15, 13, 12, 19, 27, 48,  0, 63, 20]), 'cur_cost': 110501.0}
2025-07-03 16:17:50,382 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-03 16:17:50,382 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:17:50,382 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 112760.0 (rank 90.0%)
- Population diversity: 0.6461538461538462
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:17:50,382 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:17:50,383 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:18:07,048 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Focus on opportunity nodes, avoid difficult regions, maintain diversity"
}
```
2025-07-03 16:18:07,049 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:18:07,049 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:18:07,050 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:18:07,050 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 16:18:07,050 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:18:07,050 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:18:07,051 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 105729.0
2025-07-03 16:18:07,553 - ExploitationExpert - INFO - res_population_num: 19
2025-07-03 16:18:07,553 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9526, 9526, 9533, 9534, 9541, 9545, 9545, 9552, 92204, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 16:18:07,554 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-03 16:18:07,561 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:18:07,563 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([22, 27, 39, 14, 48,  4, 37, 20, 59, 63, 61, 64, 46, 49, 10, 42, 32,
       29,  1, 56, 53,  7, 38, 44, 23, 43, 65,  9,  6,  0, 62, 50, 31, 18,
       26, 25, 58,  3,  2, 40, 35, 45, 30,  8, 16, 54, 24, 12, 15, 55, 19,
        5, 57, 34, 41, 60, 33, 21, 17, 11, 52, 28, 47, 51, 13, 36]), 'cur_cost': 97833.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([23, 63, 12, 27, 17, 30, 65, 31, 13, 21, 53,  0, 20,  9, 18, 46, 41,
       42, 33,  4,  5, 40, 59, 10, 28, 57, 44, 45, 64, 61, 62, 56, 48, 32,
       58, 24, 51, 47, 14, 39, 55, 52, 50, 34, 29, 16, 26, 19,  1, 15,  6,
       36, 35, 54, 38,  3, 22, 43,  8, 11, 37, 49, 60, 25,  2,  7]), 'cur_cost': 103761.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([14,  9, 17,  8, 34, 29,  6, 45,  1, 39, 54,  5, 37, 44, 51, 32, 62,
       61, 16, 57, 41, 25,  4, 53, 42, 58, 46, 10, 18, 64, 30,  2, 43, 52,
       59,  3, 24, 50, 26, 38, 40, 11, 23, 31, 60, 28, 49, 35, 56,  7, 65,
       55, 21, 36, 33, 47, 22, 15, 13, 12, 19, 27, 48,  0, 63, 20]), 'cur_cost': 110501.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([16,  4, 19, 57, 58, 47, 24,  3, 50, 64,  5, 29, 49, 43, 41, 34, 23,
        6, 56, 25, 59,  0, 35, 37, 21, 55,  1, 27, 15, 11,  2, 30, 10, 54,
       28, 42, 62, 44, 46, 18, 53, 36, 39, 12, 52, 60, 61, 33, 17, 13, 22,
       20, 45, 48, 40,  9, 51, 65, 31, 14, 32,  8, 26, 63, 38,  7]), 'cur_cost': 105729.0}, {'tour': array([16, 65, 19,  3, 23,  7, 50, 26, 47, 42, 51, 33, 49, 21,  2,  6, 32,
        9, 44, 40, 56,  8, 30, 12, 43, 20,  4, 55, 39, 10, 63, 34,  5, 62,
        0, 25, 57, 59, 61, 28, 18, 46, 38, 14, 24, 45, 15, 41, 36, 48, 54,
       31, 35,  1, 13, 11, 22, 58, 52, 37, 17, 53, 29, 27, 64, 60]), 'cur_cost': 103659.0}]
2025-07-03 16:18:07,565 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:18:07,566 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 7, 'skip_rate': 0.14285714285714285, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 6, 'cache_hits': 4, 'similarity_calculations': 20, 'cache_hit_rate': 0.2, 'cache_size': 16}}
2025-07-03 16:18:07,566 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 16:18:07,566 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-03 16:18:07,567 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:18:07,567 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 103659.0 (rank 60.0%)
- Population diversity: 0.56
- Evolution phase: exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:18:07,568 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:18:07,568 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:18:23,810 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Focuses on opportunity nodes, avoids difficult regions, maintains diversity."
}
```
2025-07-03 16:18:23,810 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:18:23,810 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:18:23,810 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:18:23,811 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([14,  9, 17,  8, 34, 29,  6, 45,  1, 39, 54,  5, 37, 44, 51, 32, 62,
       61, 16, 57, 41, 25,  4, 53, 42, 58, 46, 10, 18, 64, 30,  2, 43, 52,
       59,  3, 24, 50, 26, 38, 40, 11, 23, 31, 60, 28, 49, 35, 56,  7, 65,
       55, 21, 36, 33, 47, 22, 15, 13, 12, 19, 27, 48,  0, 63, 20]), 'cur_cost': 110501.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}]
2025-07-03 16:18:23,811 - __main__ - INFO - 进化阶段完成
2025-07-03 16:18:23,812 - __main__ - INFO - 开始评估阶段
2025-07-03 16:18:23,812 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 16:18:23,813 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 16:18:23,814 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 17412.0 → 11573.0 (Δ 5839.0)
- Mean cost: 60453.1 → 48726.2
- Diversity: 0.594 → 0.468 (Δ -0.1262)

#### Elite metrics
- Elite count: 12 → 19
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.222 → 0.141 (Δ -0.0809)

#### Strategy performance
- Explore: 6 inds, success 100.0% , avg Δ 58091.67
- Exploit: 4 inds, success 0.0% , avg Δ -57820.25

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 1 iterations: costs [17412.0], diversity [0.8461279461279461]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 16:18:23,814 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:18:34,109 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 1.0,
    "exploit": 0.0
  },
  "balance_state": "explore_heavy",
  "recommendations": [
    "set_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "reduce_exploit_attempts_or_improve_exploit_strategy"
  ]
}
```
2025-07-03 16:18:34,137 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 16:18:34,137 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 1.0,
    "exploit": 0.0
  },
  "balance_state": "explore_heavy",
  "recommendations": [
    "set_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "reduce_exploit_attempts_or_improve_exploit_strategy"
  ]
}
```
2025-07-03 16:18:34,138 - __main__ - INFO - 评估阶段完成
2025-07-03 16:18:34,138 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 1.0,
    "exploit": 0.0
  },
  "balance_state": "explore_heavy",
  "recommendations": [
    "set_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "reduce_exploit_attempts_or_improve_exploit_strategy"
  ]
}
```
2025-07-03 16:18:34,138 - __main__ - INFO - 当前最佳适应度: 11573.0
2025-07-03 16:18:34,140 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_1.pkl
2025-07-03 16:18:34,140 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-03 16:18:34,140 - __main__ - INFO - 开始分析阶段
2025-07-03 16:18:34,141 - StatsExpert - INFO - 开始统计分析
2025-07-03 16:18:34,156 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11573.0, 'max': 110501.0, 'mean': 48726.2, 'std': 45593.76466316419}, 'diversity': 0.6424242424242423, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 16:18:34,157 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11573.0, 'max': 110501.0, 'mean': 48726.2, 'std': 45593.76466316419}, 'diversity_level': 0.6424242424242423, 'convergence_level': 0.0, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1], 'summary': '5 clusters, sizes [6, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1], 'summary': '5 clusters, sizes [6, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 16:18:34,158 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 16:18:34,158 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 16:18:34,158 - PathExpert - INFO - 开始路径结构分析
2025-07-03 16:18:34,160 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 16:18:34,161 - PathExpert - INFO - 路径结构分析完成
2025-07-03 16:18:34,161 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 16:18:34,161 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 16:18:34,162 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 16:18:34,162 - EliteExpert - INFO - 开始精英解分析
2025-07-03 16:18:34,169 - EliteExpert - INFO - 精英解分析完成
2025-07-03 16:18:34,169 - __main__ - INFO - 精英专家分析报告: {'elite_count': 19, 'elite_common_features': {'common_edges': {'(40, 43)': 0.7368421052631579}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 2052.0, 'avg_gap': 34846.41052631578}, 'structure_gap': {'unique_elite_edges': 244, 'unique_pop_edges': 293, 'common_edges': 29}}, 'elite_diversity': {'diversity_score': 0.29009392167286907}}
2025-07-03 16:18:34,170 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 16:18:34,170 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 16:18:34,173 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 16:18:34,173 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 11573.0, mean 48726.2, max 110501.0, std 45593.76466316419
- diversity: 0.6424242424242423
- convergence: 0.0
- clustering: 5 clusters, sizes [6, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [17412.0, 11573.0] (Δ 5839.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.29009392167286907}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-03 16:18:34,174 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:18:48,442 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.642,
    "convergence": 0.000,
    "clustering": "strong"
  },
  "difficult_regions": [
    {"node": 1, "position": 1},
    {"node": 2, "position": 2},
    {"node": 3, "position": 3}
  ],
  "opportunity_regions": [
    {"node": 4, "position": 4},
    {"node": 5, "position": 5},
    {"node": 6, "position": 6}
  ],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["crossover", "mutation"]
  }
}
```
2025-07-03 16:18:48,443 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 16:18:48,443 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.642, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:18:48,443 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 16:18:48,444 - __main__ - INFO - 分析阶段完成
2025-07-03 16:18:48,444 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.642, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation']}}
2025-07-03 16:18:48,444 - __main__ - INFO - 开始策略分配阶段
2025-07-03 16:18:48,444 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 16:18:48,444 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.642
  • best_cost: 11573.0
  • mean_cost: 48726.2
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 3): 3 items, first 2: [{"node": 1, "position": 1}, {"node": 2, "position": 2}]
- Opportunity regions (# 3): 3 items, first 2: [{"node": 4, "position": 4}, {"node": 5, "position": 5}]
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 1.0,
    "exploit": 0.0
  },
  "balan...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 16:18:48,444 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 16:18:48,444 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:18:59,568 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early phase with high diversity; focus on exploration to uncover more opportunity regions."
}
```
2025-07-03 16:18:59,569 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:18:59,569 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:18:59,569 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early phase with high diversity; focus on exploration to uncover more opportunity regions."
}
```
2025-07-03 16:18:59,569 - __main__ - INFO - 策略分配阶段完成
2025-07-03 16:18:59,570 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:18:59,570 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.75,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early phase with high diversity; focus on exploration to uncover more opportunity regions."
}
```
2025-07-03 16:18:59,570 - __main__ - INFO - 开始进化阶段
2025-07-03 16:18:59,570 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 16:18:59,570 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:18:59,571 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.4676923076923077
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:18:59,571 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:18:59,571 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:19:15,610 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Focuses on opportunity nodes, avoids difficult regions early."
}
```
2025-07-03 16:19:15,611 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:19:15,611 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:19:15,612 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:19:15,612 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 16:19:15,612 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:19:15,612 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.4676923076923077
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:19:15,614 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:19:15,614 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:19:31,921 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes 1 and 2."
}
```
2025-07-03 16:19:31,922 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:19:31,922 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:19:31,922 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:19:31,922 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 16:19:31,924 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:19:31,924 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 97833.0 (rank 60.0%)
- Population diversity: 0.4676923076923077
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:19:31,925 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:19:31,925 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:19:48,152 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions, maintains diversity."
}
```
2025-07-03 16:19:48,152 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:19:48,153 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:19:48,153 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:19:48,153 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-03 16:19:48,153 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:19:48,154 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:19:48,154 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 102847.0
2025-07-03 16:19:48,656 - ExploitationExpert - INFO - res_population_num: 20
2025-07-03 16:19:48,656 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9533, 9534, 9541, 9545, 9545, 9552, 92204, 9521]
2025-07-03 16:19:48,656 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-03 16:19:48,664 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:19:48,665 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([22, 17, 64, 11, 56, 34, 47, 37, 26, 39, 10, 41, 60, 44,  3, 65, 57,
        0,  7, 31, 27, 18, 28, 19, 35, 59, 49, 48, 33, 12, 43, 50, 13,  8,
       51, 36, 23, 30,  2,  1, 61, 46, 52, 63,  6, 38, 24, 58, 53, 16, 14,
       54, 21, 15, 32, 62, 42,  4,  5, 29, 20, 55, 25, 40, 45,  9]), 'cur_cost': 102847.0}, {'tour': array([23, 63, 12, 27, 17, 30, 65, 31, 13, 21, 53,  0, 20,  9, 18, 46, 41,
       42, 33,  4,  5, 40, 59, 10, 28, 57, 44, 45, 64, 61, 62, 56, 48, 32,
       58, 24, 51, 47, 14, 39, 55, 52, 50, 34, 29, 16, 26, 19,  1, 15,  6,
       36, 35, 54, 38,  3, 22, 43,  8, 11, 37, 49, 60, 25,  2,  7]), 'cur_cost': 103761.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [14, 9, 17, 8, 34, 29, 6, 45, 1, 39, 54, 5, 37, 44, 51, 32, 62, 61, 16, 57, 41, 25, 4, 53, 42, 58, 46, 10, 18, 64, 30, 2, 43, 52, 59, 3, 24, 50, 26, 38, 40, 11, 23, 31, 60, 28, 49, 35, 56, 7, 65, 55, 21, 36, 33, 47, 22, 15, 13, 12, 19, 27, 48, 0, 63, 20], 'cur_cost': 110501.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([16,  4, 19, 57, 58, 47, 24,  3, 50, 64,  5, 29, 49, 43, 41, 34, 23,
        6, 56, 25, 59,  0, 35, 37, 21, 55,  1, 27, 15, 11,  2, 30, 10, 54,
       28, 42, 62, 44, 46, 18, 53, 36, 39, 12, 52, 60, 61, 33, 17, 13, 22,
       20, 45, 48, 40,  9, 51, 65, 31, 14, 32,  8, 26, 63, 38,  7]), 'cur_cost': 105729.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}]
2025-07-03 16:19:48,667 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 16:19:48,667 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 8, 'skip_rate': 0.125, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 7, 'cache_hits': 4, 'similarity_calculations': 26, 'cache_hit_rate': 0.15384615384615385, 'cache_size': 22}}
2025-07-03 16:19:48,667 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-03 16:19:48,667 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 16:19:48,668 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:19:48,668 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 103761.0 (rank 70.0%)
- Population diversity: 0.4646153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:19:48,669 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:19:48,669 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:20:05,191 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Focus on opportunity nodes while avoiding difficult regions."
}
```
2025-07-03 16:20:05,191 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:20:05,191 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:20:05,191 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:20:05,192 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 16:20:05,192 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:20:05,193 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.37846153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:20:05,193 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:20:05,193 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:20:21,247 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Focus on opportunity regions while avoiding difficult nodes 1 and 2"
}
```
2025-07-03 16:20:21,248 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:20:21,248 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:20:21,248 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:20:21,249 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 16:20:21,249 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:20:21,249 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:20:21,249 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 100272.0
2025-07-03 16:20:23,012 - ExploitationExpert - INFO - res_population_num: 21
2025-07-03 16:20:23,012 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9533, 9534, 9541, 9545, 9545, 9552, 92204, 9521, 9521]
2025-07-03 16:20:23,013 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:20:23,021 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:20:23,021 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([22, 17, 64, 11, 56, 34, 47, 37, 26, 39, 10, 41, 60, 44,  3, 65, 57,
        0,  7, 31, 27, 18, 28, 19, 35, 59, 49, 48, 33, 12, 43, 50, 13,  8,
       51, 36, 23, 30,  2,  1, 61, 46, 52, 63,  6, 38, 24, 58, 53, 16, 14,
       54, 21, 15, 32, 62, 42,  4,  5, 29, 20, 55, 25, 40, 45,  9]), 'cur_cost': 102847.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([ 8, 21,  3, 56, 13, 54, 42, 24,  4, 22, 51, 45, 38, 64, 52, 32, 46,
       57, 18, 34, 25, 28, 35, 47, 29, 12, 44,  7, 37, 39, 41, 11,  9, 10,
        1, 48, 59, 63, 26, 27, 65, 15, 33, 43,  5, 61,  6, 60, 16, 36, 23,
       58, 19,  2, 49, 17, 31, 50, 53, 55, 40, 30,  0, 62, 20, 14]), 'cur_cost': 100272.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([16,  4, 19, 57, 58, 47, 24,  3, 50, 64,  5, 29, 49, 43, 41, 34, 23,
        6, 56, 25, 59,  0, 35, 37, 21, 55,  1, 27, 15, 11,  2, 30, 10, 54,
       28, 42, 62, 44, 46, 18, 53, 36, 39, 12, 52, 60, 61, 33, 17, 13, 22,
       20, 45, 48, 40,  9, 51, 65, 31, 14, 32,  8, 26, 63, 38,  7]), 'cur_cost': 105729.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}]
2025-07-03 16:20:23,023 - ExploitationExpert - INFO - 局部搜索耗时: 1.77秒
2025-07-03 16:20:23,024 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 9, 'skip_rate': 0.1111111111111111, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 8, 'cache_hits': 4, 'similarity_calculations': 33, 'cache_hit_rate': 0.12121212121212122, 'cache_size': 29}}
2025-07-03 16:20:23,024 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-03 16:20:23,024 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-03 16:20:23,024 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:20:23,025 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.38153846153846155
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:20:23,025 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:20:23,026 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:20:40,101 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions, maintains diversity"
}
```
2025-07-03 16:20:40,102 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:20:40,102 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:20:40,102 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:20:40,102 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 16:20:40,104 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:20:40,104 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:20:40,104 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 105909.0
2025-07-03 16:20:40,607 - ExploitationExpert - INFO - res_population_num: 26
2025-07-03 16:20:40,607 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9533, 9534, 9541, 9545, 9545, 9552, 92204, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 16:20:40,608 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:20:40,618 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:20:40,618 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([22, 17, 64, 11, 56, 34, 47, 37, 26, 39, 10, 41, 60, 44,  3, 65, 57,
        0,  7, 31, 27, 18, 28, 19, 35, 59, 49, 48, 33, 12, 43, 50, 13,  8,
       51, 36, 23, 30,  2,  1, 61, 46, 52, 63,  6, 38, 24, 58, 53, 16, 14,
       54, 21, 15, 32, 62, 42,  4,  5, 29, 20, 55, 25, 40, 45,  9]), 'cur_cost': 102847.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([ 8, 21,  3, 56, 13, 54, 42, 24,  4, 22, 51, 45, 38, 64, 52, 32, 46,
       57, 18, 34, 25, 28, 35, 47, 29, 12, 44,  7, 37, 39, 41, 11,  9, 10,
        1, 48, 59, 63, 26, 27, 65, 15, 33, 43,  5, 61,  6, 60, 16, 36, 23,
       58, 19,  2, 49, 17, 31, 50, 53, 55, 40, 30,  0, 62, 20, 14]), 'cur_cost': 100272.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([46, 44, 50,  1, 12, 27, 63,  7, 34, 11, 60, 30, 10, 49, 21, 40, 23,
       38, 55, 54, 18, 41,  8,  3,  2, 32, 24, 43, 57, 22, 47, 25, 59,  9,
       19, 13, 16, 45, 15, 20, 58, 35,  5, 52, 56, 17, 36, 26, 65, 64, 28,
       29, 62, 33, 61,  6, 48, 31, 51, 53, 39,  4,  0, 42, 37, 14]), 'cur_cost': 105909.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}]
2025-07-03 16:20:40,620 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 16:20:40,621 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 10, 'skip_rate': 0.1, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 9, 'cache_hits': 4, 'similarity_calculations': 41, 'cache_hit_rate': 0.0975609756097561, 'cache_size': 37}}
2025-07-03 16:20:40,621 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 16:20:40,621 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-03 16:20:40,621 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:20:40,622 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.3830769230769231
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:20:40,622 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:20:40,622 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:20:56,961 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions early."
}
```
2025-07-03 16:20:56,962 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:20:56,962 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:20:56,963 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:20:56,963 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}]
2025-07-03 16:20:56,963 - __main__ - INFO - 进化阶段完成
2025-07-03 16:20:56,964 - __main__ - INFO - 开始评估阶段
2025-07-03 16:20:56,964 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 16:20:56,966 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 16:20:56,966 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 11573.0 → 11573.0 (Δ 0.0)
- Mean cost: 48726.2 → 39003.9
- Diversity: 0.468 → 0.383 (Δ -0.0846)

#### Elite metrics
- Elite count: 19 → 26
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.141 → 0.104 (Δ -0.0367)

#### Strategy performance
- Explore: 7 inds, success 28.6% , avg Δ 25492.57
- Exploit: 3 inds, success 33.3% , avg Δ -27075.0

#### Other indicators
- No-change individuals: 5
- Regression individuals: 2
- Historical trends: Last 2 iterations: costs [17412.0, 11573.0], diversity [0.8461279461279461, 0.6424242424242423]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 16:20:56,967 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:21:06,314 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 0.333},
  "balance_state": "exploit_biased",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.7"]
}
```
2025-07-03 16:21:06,339 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 16:21:06,339 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 0.333},
  "balance_state": "exploit_biased",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.7"]
}
```
2025-07-03 16:21:06,340 - __main__ - INFO - 评估阶段完成
2025-07-03 16:21:06,340 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 0.333},
  "balance_state": "exploit_biased",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.7"]
}
```
2025-07-03 16:21:06,340 - __main__ - INFO - 当前最佳适应度: 11573.0
2025-07-03 16:21:06,341 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_2.pkl
2025-07-03 16:21:06,342 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-03 16:21:06,342 - __main__ - INFO - 开始分析阶段
2025-07-03 16:21:06,342 - StatsExpert - INFO - 开始统计分析
2025-07-03 16:21:06,358 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11573.0, 'max': 105909.0, 'mean': 39003.9, 'std': 41920.393396174135}, 'diversity': 0.5114478114478115, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 16:21:06,359 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11573.0, 'max': 105909.0, 'mean': 39003.9, 'std': 41920.393396174135}, 'diversity_level': 0.5114478114478115, 'convergence_level': 0.0, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1], 'summary': '4 clusters, sizes [7, 1, 1, 1]'}, 'clustering_info': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1], 'summary': '4 clusters, sizes [7, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 16:21:06,359 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 16:21:06,359 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 16:21:06,360 - PathExpert - INFO - 开始路径结构分析
2025-07-03 16:21:06,362 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 16:21:06,362 - PathExpert - INFO - 路径结构分析完成
2025-07-03 16:21:06,363 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 16:21:06,363 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 16:21:06,363 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 16:21:06,363 - EliteExpert - INFO - 开始精英解分析
2025-07-03 16:21:06,376 - EliteExpert - INFO - 精英解分析完成
2025-07-03 16:21:06,376 - __main__ - INFO - 精英专家分析报告: {'elite_count': 26, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 2052.0, 'avg_gap': 26297.63076923077}, 'structure_gap': {'unique_elite_edges': 248, 'unique_pop_edges': 227, 'common_edges': 31}}, 'elite_diversity': {'diversity_score': 0.2743589743589744}}
2025-07-03 16:21:06,376 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 16:21:06,377 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 16:21:06,379 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 16:21:06,379 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 11573.0, mean 39003.9, max 105909.0, std 41920.393396174135
- diversity: 0.5114478114478115
- convergence: 0.0
- clustering: 4 clusters, sizes [7, 1, 1, 1]
- recent_best_costs: Recent costs: [17412.0, 11573.0, 11573.0] (Δ 2919.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.2743589743589744}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-03 16:21:06,380 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:21:21,265 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "moderate"
  },
  "population_state": {
    "diversity": 0.511,
    "convergence": 0.000,
    "clustering": "strong"
  },
  "difficult_regions": [
    {"node": 1, "position": 1},
    {"node": 2, "position": 2},
    {"node": 3, "position": 3}
  ],
  "opportunity_regions": [
    {"node": 4, "position": 4},
    {"node": 5, "position": 5},
    {"node": 6, "position": 6}
  ],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["mutation", "crossover"]
  }
}
```
2025-07-03 16:21:21,265 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 16:21:21,265 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.511, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation', 'crossover']}}
2025-07-03 16:21:21,265 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 16:21:21,265 - __main__ - INFO - 分析阶段完成
2025-07-03 16:21:21,265 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.511, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation', 'crossover']}}
2025-07-03 16:21:21,267 - __main__ - INFO - 开始策略分配阶段
2025-07-03 16:21:21,267 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 16:21:21,267 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.511
  • best_cost: 11573.0
  • mean_cost: 39003.9
- Search-space: ruggedness high, modality multi, deceptiveness moderate
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 3): 3 items, first 2: [{"node": 1, "position": 1}, {"node": 2, "position": 2}]
- Opportunity regions (# 3): 3 items, first 2: [{"node": 4, "position": 4}, {"node": 5, "position": 5}]
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 0.333},
  "balance_state...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 16:21:21,267 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 16:21:21,268 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:21:31,880 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.70,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early phase, high ruggedness, and focus suggestion to explore."
}
```
2025-07-03 16:21:31,881 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:21:31,881 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:21:31,881 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.70,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early phase, high ruggedness, and focus suggestion to explore."
}
```
2025-07-03 16:21:31,881 - __main__ - INFO - 策略分配阶段完成
2025-07-03 16:21:31,882 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore']
2025-07-03 16:21:31,882 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.70,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "rationale": "Early phase, high ruggedness, and focus suggestion to explore."
}
```
2025-07-03 16:21:31,882 - __main__ - INFO - 开始进化阶段
2025-07-03 16:21:31,883 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 16:21:31,883 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:21:31,883 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.3830769230769231
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:21:31,883 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:21:31,885 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:21:47,800 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes 1-2."
}
```
2025-07-03 16:21:47,800 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:21:47,800 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:21:47,800 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:21:47,801 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 16:21:47,801 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:21:47,801 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.3830769230769231
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:21:47,802 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:21:47,802 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:22:03,712 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "nodes 4,5 adjacent sparse cells",
  "strategy_comment": "Prioritizes opportunity regions while avoiding difficult nodes 1,2"
}
```
2025-07-03 16:22:03,713 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:22:03,713 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:22:03,714 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:22:03,714 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-03 16:22:03,714 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:22:03,714 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:22:03,714 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 111373.0
2025-07-03 16:22:03,716 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：6,跳过局部搜索
2025-07-03 16:22:03,716 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:22:03,716 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([15, 64, 52, 29,  1, 12, 33,  2, 21, 34, 14, 10, 23, 32, 38,  4, 45,
       50, 62, 18, 40, 53, 24, 51, 16, 47, 39, 13,  9, 35, 60, 57, 44, 17,
        7, 54, 37, 28, 25, 65, 30, 48, 58, 42, 20,  5, 63, 55,  3, 27, 11,
       56, 41, 22,  8, 61,  6, 49, 31, 46, 36,  0, 26, 59, 19, 43]), 'cur_cost': 111373.0}
2025-07-03 16:22:03,716 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 16:22:03,717 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:22:03,717 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 102847.0 (rank 70.0%)
- Population diversity: 0.47384615384615386
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:22:03,717 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:22:03,717 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:22:20,226 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions early."
}
```
2025-07-03 16:22:20,227 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:22:20,227 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:22:20,227 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:22:20,228 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-03 16:22:20,228 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:22:20,228 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:22:20,228 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 111685.0
2025-07-03 16:22:20,228 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：6,跳过局部搜索
2025-07-03 16:22:20,229 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:22:20,229 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([ 5,  8, 43, 55, 41, 29,  2, 60, 47, 21, 35, 19, 40, 14, 53, 36, 10,
        3, 12, 17,  9, 58, 39, 59, 28, 65,  0, 33, 27, 22, 38, 61, 51, 42,
       57, 16, 46, 45, 56, 62, 18, 25, 48, 31, 15, 24, 30, 23, 44, 13, 50,
       52, 26, 11, 32,  7, 54,  1, 63, 34, 64, 37, 20,  6,  4, 49]), 'cur_cost': 111685.0}
2025-07-03 16:22:20,230 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 16:22:20,230 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:22:20,230 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.47692307692307695
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:22:20,231 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:22:20,231 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:22:36,618 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes 1-2."
}
```
2025-07-03 16:22:36,619 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:22:36,619 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:22:36,619 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:22:36,620 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 16:22:36,620 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:22:36,620 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:22:36,621 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 111732.0
2025-07-03 16:22:37,123 - ExploitationExpert - INFO - res_population_num: 26
2025-07-03 16:22:37,123 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9533, 9534, 9541, 9545, 9545, 9552, 92204]
2025-07-03 16:22:37,124 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64)]
2025-07-03 16:22:37,134 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:22:37,134 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([15, 64, 52, 29,  1, 12, 33,  2, 21, 34, 14, 10, 23, 32, 38,  4, 45,
       50, 62, 18, 40, 53, 24, 51, 16, 47, 39, 13,  9, 35, 60, 57, 44, 17,
        7, 54, 37, 28, 25, 65, 30, 48, 58, 42, 20,  5, 63, 55,  3, 27, 11,
       56, 41, 22,  8, 61,  6, 49, 31, 46, 36,  0, 26, 59, 19, 43]), 'cur_cost': 111373.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([ 5,  8, 43, 55, 41, 29,  2, 60, 47, 21, 35, 19, 40, 14, 53, 36, 10,
        3, 12, 17,  9, 58, 39, 59, 28, 65,  0, 33, 27, 22, 38, 61, 51, 42,
       57, 16, 46, 45, 56, 62, 18, 25, 48, 31, 15, 24, 30, 23, 44, 13, 50,
       52, 26, 11, 32,  7, 54,  1, 63, 34, 64, 37, 20,  6,  4, 49]), 'cur_cost': 111685.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([20, 44, 21, 52,  4, 60, 48, 64, 23, 30, 53, 27, 45, 17, 58, 46, 35,
       14, 40,  8,  6, 15,  3, 37, 54, 18, 62,  9, 11,  1, 33,  7, 36, 22,
       16, 32, 56, 31, 57, 41, 24, 47, 42, 26, 59,  2, 55,  5, 43, 13, 25,
       29, 51, 49, 19, 50, 34, 12, 10, 28, 38, 65, 63, 39,  0, 61]), 'cur_cost': 111732.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([46, 44, 50,  1, 12, 27, 63,  7, 34, 11, 60, 30, 10, 49, 21, 40, 23,
       38, 55, 54, 18, 41,  8,  3,  2, 32, 24, 43, 57, 22, 47, 25, 59,  9,
       19, 13, 16, 45, 15, 20, 58, 35,  5, 52, 56, 17, 36, 26, 65, 64, 28,
       29, 62, 33, 61,  6, 48, 31, 51, 53, 39,  4,  0, 42, 37, 14]), 'cur_cost': 105909.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}]
2025-07-03 16:22:37,136 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 16:22:37,137 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 13, 'skip_rate': 0.23076923076923078, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 10, 'cache_hits': 17, 'similarity_calculations': 64, 'cache_hit_rate': 0.265625, 'cache_size': 47}}
2025-07-03 16:22:37,137 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-03 16:22:37,137 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-03 16:22:37,137 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:22:37,138 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.4753846153846154
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:22:37,138 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:22:37,139 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:22:53,835 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "nodes 4,5 adjacent sparse cells",
  "strategy_comment": "Prioritizes opportunity nodes early, avoids difficult regions late."
}
```
2025-07-03 16:22:53,836 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:22:53,836 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:22:53,837 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:22:53,837 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 16:22:53,837 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:22:53,838 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:22:53,838 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 103235.0
2025-07-03 16:22:54,341 - ExploitationExpert - INFO - res_population_num: 27
2025-07-03 16:22:54,341 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9533, 9534, 9541, 9545, 9545, 9552, 92204, 9521]
2025-07-03 16:22:54,341 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:22:54,351 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:22:54,351 - ExploitationExpert - INFO - populations: [{'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([15, 64, 52, 29,  1, 12, 33,  2, 21, 34, 14, 10, 23, 32, 38,  4, 45,
       50, 62, 18, 40, 53, 24, 51, 16, 47, 39, 13,  9, 35, 60, 57, 44, 17,
        7, 54, 37, 28, 25, 65, 30, 48, 58, 42, 20,  5, 63, 55,  3, 27, 11,
       56, 41, 22,  8, 61,  6, 49, 31, 46, 36,  0, 26, 59, 19, 43]), 'cur_cost': 111373.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([ 5,  8, 43, 55, 41, 29,  2, 60, 47, 21, 35, 19, 40, 14, 53, 36, 10,
        3, 12, 17,  9, 58, 39, 59, 28, 65,  0, 33, 27, 22, 38, 61, 51, 42,
       57, 16, 46, 45, 56, 62, 18, 25, 48, 31, 15, 24, 30, 23, 44, 13, 50,
       52, 26, 11, 32,  7, 54,  1, 63, 34, 64, 37, 20,  6,  4, 49]), 'cur_cost': 111685.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([20, 44, 21, 52,  4, 60, 48, 64, 23, 30, 53, 27, 45, 17, 58, 46, 35,
       14, 40,  8,  6, 15,  3, 37, 54, 18, 62,  9, 11,  1, 33,  7, 36, 22,
       16, 32, 56, 31, 57, 41, 24, 47, 42, 26, 59,  2, 55,  5, 43, 13, 25,
       29, 51, 49, 19, 50, 34, 12, 10, 28, 38, 65, 63, 39,  0, 61]), 'cur_cost': 111732.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([33, 26, 12, 18, 48,  0, 35, 39, 23, 45, 24, 10, 63,  7,  8, 61, 53,
       52,  5, 28, 54, 31, 56, 34, 58, 19, 62, 27,  6, 11, 50, 20,  4, 37,
       22,  2, 41, 25, 30, 21, 16, 44, 40, 64, 36, 42, 43,  9,  3, 15, 55,
       46, 60, 57, 51, 32, 59, 13, 47, 38, 14, 65,  1, 49, 17, 29]), 'cur_cost': 103235.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}]
2025-07-03 16:22:54,353 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 16:22:54,354 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 3, 'total_searches': 14, 'skip_rate': 0.21428571428571427, 'estimated_time_saved': 15, 'path_optimizer': {'path_count': 11, 'cache_hits': 17, 'similarity_calculations': 74, 'cache_hit_rate': 0.22972972972972974, 'cache_size': 57}}
2025-07-03 16:22:54,354 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 16:22:54,354 - __main__ - INFO - 为个体 9 生成探索路径
2025-07-03 16:22:54,355 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:22:54,355 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.4753846153846154
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:22:54,355 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:22:54,355 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:23:11,059 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node 4,5 sparse cells",
  "strategy_comment": "Prioritizes opportunity nodes, avoids difficult regions early."
}
```
2025-07-03 16:23:11,059 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:23:11,059 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:23:11,059 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:23:11,060 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([15, 64, 52, 29,  1, 12, 33,  2, 21, 34, 14, 10, 23, 32, 38,  4, 45,
       50, 62, 18, 40, 53, 24, 51, 16, 47, 39, 13,  9, 35, 60, 57, 44, 17,
        7, 54, 37, 28, 25, 65, 30, 48, 58, 42, 20,  5, 63, 55,  3, 27, 11,
       56, 41, 22,  8, 61,  6, 49, 31, 46, 36,  0, 26, 59, 19, 43]), 'cur_cost': 111373.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 5,  8, 43, 55, 41, 29,  2, 60, 47, 21, 35, 19, 40, 14, 53, 36, 10,
        3, 12, 17,  9, 58, 39, 59, 28, 65,  0, 33, 27, 22, 38, 61, 51, 42,
       57, 16, 46, 45, 56, 62, 18, 25, 48, 31, 15, 24, 30, 23, 44, 13, 50,
       52, 26, 11, 32,  7, 54,  1, 63, 34, 64, 37, 20,  6,  4, 49]), 'cur_cost': 111685.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}]
2025-07-03 16:23:11,060 - __main__ - INFO - 进化阶段完成
2025-07-03 16:23:11,061 - __main__ - INFO - 开始评估阶段
2025-07-03 16:23:11,061 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 16:23:11,063 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 16:23:11,063 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 11573.0 → 11573.0 (Δ 0.0)
- Mean cost: 39003.9 → 50746.3
- Diversity: 0.383 → 0.475 (Δ 0.0923)

#### Elite metrics
- Elite count: 26 → 27
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.104 → 0.100 (Δ -0.0039)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ 15212.33
- Exploit: 4 inds, success 25.0% , avg Δ -52174.5

#### Other indicators
- No-change individuals: 5
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [17412.0, 11573.0, 11573.0], diversity [0.8461279461279461, 0.6424242424242423, 0.5114478114478115]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 16:23:11,064 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:23:20,896 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 0.25
  },
  "balance_state": "exploit_biased",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 16:23:20,921 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 16:23:20,921 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 0.25
  },
  "balance_state": "exploit_biased",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 16:23:20,921 - __main__ - INFO - 评估阶段完成
2025-07-03 16:23:20,922 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 0.25
  },
  "balance_state": "exploit_biased",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8",
    "monitor_elite_diversity_closely"
  ]
}
```
2025-07-03 16:23:20,922 - __main__ - INFO - 当前最佳适应度: 11573.0
2025-07-03 16:23:20,923 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_3.pkl
2025-07-03 16:23:20,924 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-03 16:23:20,924 - __main__ - INFO - 开始分析阶段
2025-07-03 16:23:20,924 - StatsExpert - INFO - 开始统计分析
2025-07-03 16:23:20,940 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11573.0, 'max': 111732.0, 'mean': 50746.3, 'std': 48031.99531572678}, 'diversity': 0.6525252525252526, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 16:23:20,941 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11573.0, 'max': 111732.0, 'mean': 50746.3, 'std': 48031.99531572678}, 'diversity_level': 0.6525252525252526, 'convergence_level': 0.0, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1], 'summary': '5 clusters, sizes [6, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1], 'summary': '5 clusters, sizes [6, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 16:23:20,942 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 16:23:20,942 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 16:23:20,942 - PathExpert - INFO - 开始路径结构分析
2025-07-03 16:23:20,945 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 16:23:20,946 - PathExpert - INFO - 路径结构分析完成
2025-07-03 16:23:20,946 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 16:23:20,946 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 16:23:20,946 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 16:23:20,946 - EliteExpert - INFO - 开始精英解分析
2025-07-03 16:23:20,959 - EliteExpert - INFO - 精英解分析完成
2025-07-03 16:23:20,959 - __main__ - INFO - 精英专家分析报告: {'elite_count': 27, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 2052.0, 'avg_gap': 38158.00370370371}, 'structure_gap': {'unique_elite_edges': 246, 'unique_pop_edges': 288, 'common_edges': 34}}, 'elite_diversity': {'diversity_score': 0.27125960459293796}}
2025-07-03 16:23:20,960 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 16:23:20,960 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 16:23:20,962 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 16:23:20,963 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 11573.0, mean 50746.3, max 111732.0, std 48031.99531572678
- diversity: 0.6525252525252526
- convergence: 0.0
- clustering: 5 clusters, sizes [6, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [11573.0, 11573.0, 11573.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.27125960459293796}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

### Formatting Guidelines (read carefully, DO NOT output this section)
1. Do NOT copy or repeat any *_sample values above; at least 50% of elements in difficult_regions and opportunity_regions must differ from those samples.
2. Use [] (empty array) when a list has no content; never output the string "None" or a null value.
3. For search_space_features use enumerated strings only:
    • ruggedness: "low" | "medium" | "high"
    • modality: "mono" | "multi" | "unknown"
    • deceptiveness: "low" | "moderate" | "high" | "unknown"
4. diversity and convergence must be floats with exactly 3 decimal places (e.g. 0.742).
5. Follow the JSON schema strictly; no additional keys or commentary.

2025-07-03 16:23:20,963 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:23:36,324 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "moderate"
  },
  "population_state": {
    "diversity": 0.653,
    "convergence": 0.000,
    "clustering": "strong"
  },
  "difficult_regions": [
    {"node": 1, "position": 1},
    {"node": 2, "position": 2},
    {"node": 3, "position": 3}
  ],
  "opportunity_regions": [
    {"node": 4, "position": 4},
    {"node": 5, "position": 5},
    {"node": 6, "position": 6}
  ],
  "evolution_phase": "mid",
  "evolution_direction": {
    "recommended_focus": "exploit",
    "operators": ["local_search", "edge_recombination"]
  }
}
```
2025-07-03 16:23:36,324 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 16:23:36,324 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.653, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'mid', 'evolution_direction': {'recommended_focus': 'exploit', 'operators': ['local_search', 'edge_recombination']}}
2025-07-03 16:23:36,325 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 16:23:36,325 - __main__ - INFO - 分析阶段完成
2025-07-03 16:23:36,325 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.653, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'node': 1, 'position': 1}, {'node': 2, 'position': 2}, {'node': 3, 'position': 3}], 'opportunity_regions': [{'node': 4, 'position': 4}, {'node': 5, 'position': 5}, {'node': 6, 'position': 6}], 'evolution_phase': 'mid', 'evolution_direction': {'recommended_focus': 'exploit', 'operators': ['local_search', 'edge_recombination']}}
2025-07-03 16:23:36,325 - __main__ - INFO - 开始策略分配阶段
2025-07-03 16:23:36,325 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 16:23:36,327 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.653
  • best_cost: 11573.0
  • mean_cost: 50746.3
- Search-space: ruggedness high, modality multi, deceptiveness moderate
- Evolution phase: mid
- Landscape focus suggestion: exploit
- Spatial density summary: None
- Elite diversity: N/A
- Difficult regions (# 3): 3 items, first 2: [{"node": 1, "position": 1}, {"node": 2, "position": 2}]
- Opportunity regions (# 3): 3 items, first 2: [{"node": 4, "position": 4}, {"node": 5, "position": 5}]
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.167,
    "exploit": 0.25
  },
  "ba...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Formatting Guidelines (DO NOT output this section)
1. global_explore_ratio must be rounded to exactly 2 decimal places (e.g., 0.80).
2. The individual_assignments mapping MUST include every index from 0 to (population_size - 1) without omission.
3. Valid values are "explore" or "exploit" (lowercase).
4. rationale ≤ 40 words.

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 16:23:36,327 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 16:23:36,327 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:23:47,254 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.30,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Landscape focus suggests exploit; moderate diversity and effectiveness favors exploitation."
}
```
2025-07-03 16:23:47,255 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-03 16:23:47,255 - __main__ - INFO - 策略分配报告: ['exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-03 16:23:47,255 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.30,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Landscape focus suggests exploit; moderate diversity and effectiveness favors exploitation."
}
```
2025-07-03 16:23:47,255 - __main__ - INFO - 策略分配阶段完成
2025-07-03 16:23:47,256 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-07-03 16:23:47,256 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.30,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "exploit",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Landscape focus suggests exploit; moderate diversity and effectiveness favors exploitation."
}
```
2025-07-03 16:23:47,256 - __main__ - INFO - 开始进化阶段
2025-07-03 16:23:47,256 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-03 16:23:47,257 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:23:47,257 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:23:47,257 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 92823.0
2025-07-03 16:23:47,257 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：6,跳过局部搜索
2025-07-03 16:23:47,257 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:23:47,258 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([54, 36, 12, 43, 27, 55, 59, 30, 34, 41,  9, 62, 56, 58, 52, 40, 37,
       15, 10, 50, 44, 47, 19,  7,  6, 35, 25, 13, 21, 17, 42, 22, 28, 49,
       16, 64,  5, 39,  8,  0,  1, 65, 46,  3, 63, 11, 57, 53,  2, 48, 29,
       14, 26, 33, 51, 38, 60, 32, 61, 23, 20,  4, 31, 45, 18, 24]), 'cur_cost': 92823.0}
2025-07-03 16:23:47,258 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-03 16:23:47,258 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:23:47,258 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:23:47,259 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 111095.0
2025-07-03 16:23:47,259 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：6,跳过局部搜索
2025-07-03 16:23:47,260 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:23:47,260 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([21, 59, 50, 39, 30, 48,  3, 11, 26, 34,  4, 32, 57, 62, 45, 27, 47,
       25,  0, 38, 53, 43, 64, 44, 54, 17, 52,  8, 51,  6, 35,  1, 65, 22,
       46,  5, 15, 49, 58, 40,  2, 19, 10, 55,  9, 33, 36, 63, 16, 28, 42,
       29, 41, 20,  7, 56, 14, 23, 13, 12, 18, 61, 60, 37, 24, 31]), 'cur_cost': 111095.0}
2025-07-03 16:23:47,260 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-03 16:23:47,260 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:23:47,261 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:23:47,261 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 118773.0
2025-07-03 16:23:47,763 - ExploitationExpert - INFO - res_population_num: 28
2025-07-03 16:23:47,763 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9533, 9534, 9541, 9545, 9545, 9552, 92204, 9521]
2025-07-03 16:23:47,763 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:23:47,775 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:23:47,775 - ExploitationExpert - INFO - populations: [{'tour': array([54, 36, 12, 43, 27, 55, 59, 30, 34, 41,  9, 62, 56, 58, 52, 40, 37,
       15, 10, 50, 44, 47, 19,  7,  6, 35, 25, 13, 21, 17, 42, 22, 28, 49,
       16, 64,  5, 39,  8,  0,  1, 65, 46,  3, 63, 11, 57, 53,  2, 48, 29,
       14, 26, 33, 51, 38, 60, 32, 61, 23, 20,  4, 31, 45, 18, 24]), 'cur_cost': 92823.0}, {'tour': array([21, 59, 50, 39, 30, 48,  3, 11, 26, 34,  4, 32, 57, 62, 45, 27, 47,
       25,  0, 38, 53, 43, 64, 44, 54, 17, 52,  8, 51,  6, 35,  1, 65, 22,
       46,  5, 15, 49, 58, 40,  2, 19, 10, 55,  9, 33, 36, 63, 16, 28, 42,
       29, 41, 20,  7, 56, 14, 23, 13, 12, 18, 61, 60, 37, 24, 31]), 'cur_cost': 111095.0}, {'tour': array([16,  8, 38, 11, 41, 14, 19, 43, 29, 12, 40,  4, 61, 46, 64, 10,  5,
       62, 17,  0, 27, 44, 36, 22, 37, 23, 33, 31, 55, 30, 50, 13,  2, 20,
       56, 34, 54, 48, 65, 28, 53, 58,  9, 26, 39, 25,  7, 47, 24,  3, 57,
       42, 45, 35, 59, 49, 32, 52, 21, 63, 51,  6, 60,  1, 15, 18]), 'cur_cost': 118773.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [5, 8, 43, 55, 41, 29, 2, 60, 47, 21, 35, 19, 40, 14, 53, 36, 10, 3, 12, 17, 9, 58, 39, 59, 28, 65, 0, 33, 27, 22, 38, 61, 51, 42, 57, 16, 46, 45, 56, 62, 18, 25, 48, 31, 15, 24, 30, 23, 44, 13, 50, 52, 26, 11, 32, 7, 54, 1, 63, 34, 64, 37, 20, 6, 4, 49], 'cur_cost': 111685.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([20, 44, 21, 52,  4, 60, 48, 64, 23, 30, 53, 27, 45, 17, 58, 46, 35,
       14, 40,  8,  6, 15,  3, 37, 54, 18, 62,  9, 11,  1, 33,  7, 36, 22,
       16, 32, 56, 31, 57, 41, 24, 47, 42, 26, 59,  2, 55,  5, 43, 13, 25,
       29, 51, 49, 19, 50, 34, 12, 10, 28, 38, 65, 63, 39,  0, 61]), 'cur_cost': 111732.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([33, 26, 12, 18, 48,  0, 35, 39, 23, 45, 24, 10, 63,  7,  8, 61, 53,
       52,  5, 28, 54, 31, 56, 34, 58, 19, 62, 27,  6, 11, 50, 20,  4, 37,
       22,  2, 41, 25, 30, 21, 16, 44, 40, 64, 36, 42, 43,  9,  3, 15, 55,
       46, 60, 57, 51, 32, 59, 13, 47, 38, 14, 65,  1, 49, 17, 29]), 'cur_cost': 103235.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}]
2025-07-03 16:23:47,777 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 16:23:47,778 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 17, 'skip_rate': 0.29411764705882354, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 12, 'cache_hits': 31, 'similarity_calculations': 99, 'cache_hit_rate': 0.31313131313131315, 'cache_size': 68}}
2025-07-03 16:23:47,778 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-07-03 16:23:47,778 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-03 16:23:47,778 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:23:47,779 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:23:47,779 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 95829.0
2025-07-03 16:23:47,779 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：6,跳过局部搜索
2025-07-03 16:23:47,779 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:23:47,780 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([ 3, 62, 52, 34, 17, 45, 40, 19, 27, 39, 51, 53, 58, 55, 49, 24, 29,
       33, 28, 63, 18,  2, 15, 50, 22, 25, 16, 44, 57, 56,  1,  9, 65, 37,
       30, 61, 13, 14, 42, 31, 20,  8, 21,  0, 64, 48, 35, 60,  6, 23, 47,
       38,  7,  4, 54, 41, 43, 11, 46, 32, 10, 12,  5, 36, 59, 26]), 'cur_cost': 95829.0}
2025-07-03 16:23:47,780 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 16:23:47,780 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:23:47,781 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111685.0 (rank 70.0%)
- Population diversity: 0.7292307692307692
- Evolution phase: mid
- Landscape recommends focus: exploit
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:23:47,781 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:23:47,781 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:24:05,858 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5, adjacent sparse cells",
  "strategy_comment": "Focus on opportunity regions, avoid difficult nodes, maintain diversity"
}
```
2025-07-03 16:24:05,859 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:24:05,859 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:24:05,859 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:24:05,859 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 16:24:05,859 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 16:24:05,861 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11573.0 (rank 0.0%)
- Population diversity: 0.6476923076923077
- Evolution phase: mid
- Landscape recommends focus: exploit
- Opportunity regions (sample): {'node': 4, 'po, {'node': 5, 'po...
- Difficult regions to avoid (sample): {'node': 1, 'po, {'node': 2, 'po...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 16:24:05,861 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 16:24:05,862 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:24:22,999 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2],
  "targeted_regions": "node_4, node_5",
  "strategy_comment": "Focuses on opportunity nodes, avoids difficult regions, maintains diversity."
}
```
2025-07-03 16:24:22,999 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 16:24:22,999 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11573.0, 路径: [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2]
2025-07-03 16:24:23,000 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}
2025-07-03 16:24:23,001 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 16:24:23,001 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:24:23,001 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:24:23,002 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 113843.0
2025-07-03 16:24:23,505 - ExploitationExpert - INFO - res_population_num: 29
2025-07-03 16:24:23,505 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9533, 9534, 9541, 9545, 9545, 9552, 92204, 9521, 9521]
2025-07-03 16:24:23,506 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:24:23,517 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:24:23,517 - ExploitationExpert - INFO - populations: [{'tour': array([54, 36, 12, 43, 27, 55, 59, 30, 34, 41,  9, 62, 56, 58, 52, 40, 37,
       15, 10, 50, 44, 47, 19,  7,  6, 35, 25, 13, 21, 17, 42, 22, 28, 49,
       16, 64,  5, 39,  8,  0,  1, 65, 46,  3, 63, 11, 57, 53,  2, 48, 29,
       14, 26, 33, 51, 38, 60, 32, 61, 23, 20,  4, 31, 45, 18, 24]), 'cur_cost': 92823.0}, {'tour': array([21, 59, 50, 39, 30, 48,  3, 11, 26, 34,  4, 32, 57, 62, 45, 27, 47,
       25,  0, 38, 53, 43, 64, 44, 54, 17, 52,  8, 51,  6, 35,  1, 65, 22,
       46,  5, 15, 49, 58, 40,  2, 19, 10, 55,  9, 33, 36, 63, 16, 28, 42,
       29, 41, 20,  7, 56, 14, 23, 13, 12, 18, 61, 60, 37, 24, 31]), 'cur_cost': 111095.0}, {'tour': array([16,  8, 38, 11, 41, 14, 19, 43, 29, 12, 40,  4, 61, 46, 64, 10,  5,
       62, 17,  0, 27, 44, 36, 22, 37, 23, 33, 31, 55, 30, 50, 13,  2, 20,
       56, 34, 54, 48, 65, 28, 53, 58,  9, 26, 39, 25,  7, 47, 24,  3, 57,
       42, 45, 35, 59, 49, 32, 52, 21, 63, 51,  6, 60,  1, 15, 18]), 'cur_cost': 118773.0}, {'tour': array([ 3, 62, 52, 34, 17, 45, 40, 19, 27, 39, 51, 53, 58, 55, 49, 24, 29,
       33, 28, 63, 18,  2, 15, 50, 22, 25, 16, 44, 57, 56,  1,  9, 65, 37,
       30, 61, 13, 14, 42, 31, 20,  8, 21,  0, 64, 48, 35, 60,  6, 23, 47,
       38,  7,  4, 54, 41, 43, 11, 46, 32, 10, 12,  5, 36, 59, 26]), 'cur_cost': 95829.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([40,  0, 55, 31, 43, 13, 10, 16, 39,  8, 48, 20,  9, 59, 54,  3, 17,
       28,  6, 36,  5, 47, 11, 63, 25,  4, 38, 30, 21, 29, 53, 23, 37, 56,
       12, 41, 32, 61,  2, 26, 65, 51, 60,  7, 35,  1, 58, 64, 57, 52, 46,
       15, 24, 19, 44, 18, 45, 49, 34, 62, 50, 27, 14, 42, 22, 33]), 'cur_cost': 113843.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([33, 26, 12, 18, 48,  0, 35, 39, 23, 45, 24, 10, 63,  7,  8, 61, 53,
       52,  5, 28, 54, 31, 56, 34, 58, 19, 62, 27,  6, 11, 50, 20,  4, 37,
       22,  2, 41, 25, 30, 21, 16, 44, 40, 64, 36, 42, 43,  9,  3, 15, 55,
       46, 60, 57, 51, 32, 59, 13, 47, 38, 14, 65,  1, 49, 17, 29]), 'cur_cost': 103235.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}]
2025-07-03 16:24:23,520 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 16:24:23,520 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 6, 'total_searches': 19, 'skip_rate': 0.3157894736842105, 'estimated_time_saved': 30, 'path_optimizer': {'path_count': 13, 'cache_hits': 38, 'similarity_calculations': 118, 'cache_hit_rate': 0.3220338983050847, 'cache_size': 80}}
2025-07-03 16:24:23,521 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-03 16:24:23,521 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 16:24:23,521 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:24:23,521 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:24:23,522 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 105087.0
2025-07-03 16:24:23,522 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：6,跳过局部搜索
2025-07-03 16:24:23,522 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:24:23,523 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([13, 35, 19, 10, 43, 20, 45, 57, 24, 31, 27, 61, 25, 30, 53,  8, 38,
        3, 42, 32, 17, 46, 58, 33,  9, 56, 64, 49, 51, 60,  2, 63, 22, 40,
       48, 28, 44, 52, 11, 12, 37, 23, 29, 39, 50, 59,  4, 41, 15,  7, 55,
        5,  0, 54, 34,  6, 65, 16, 14, 47, 36,  1, 18, 26, 62, 21]), 'cur_cost': 105087.0}
2025-07-03 16:24:23,523 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 16:24:23,523 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:24:23,523 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:24:23,524 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 100853.0
2025-07-03 16:24:24,026 - ExploitationExpert - INFO - res_population_num: 29
2025-07-03 16:24:24,026 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9526, 9526, 9533, 9534, 9541, 9545, 9545, 9552, 92204, 9521, 9521]
2025-07-03 16:24:24,026 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 41, 51, 38,
       45, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 56, 59, 62, 53, 61, 63, 52,
       65, 64, 57, 54, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46, 47,
       49, 40, 43, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,
       19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 23, 13, 20,
       21, 19, 16, 18, 12, 17, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 17, 12, 18, 16, 19, 21, 20,
       13, 23, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 30,
       28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  7,  1],
      dtype=int64), array([ 0, 54,  4, 30,  2, 44, 10, 40, 27, 20, 23, 35, 39, 28, 33, 52, 63,
       64, 65, 22, 51, 37,  1, 26, 17, 47, 21, 49, 62, 53, 60, 46, 12, 43,
       50, 18, 25, 32,  7,  9, 29, 31, 41, 58,  8, 24, 61, 56, 19,  6, 38,
       45, 48, 36,  3, 13, 42, 11, 15,  5, 57, 55, 34, 14, 16, 59],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 16:24:24,036 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 16:24:24,037 - ExploitationExpert - INFO - populations: [{'tour': array([54, 36, 12, 43, 27, 55, 59, 30, 34, 41,  9, 62, 56, 58, 52, 40, 37,
       15, 10, 50, 44, 47, 19,  7,  6, 35, 25, 13, 21, 17, 42, 22, 28, 49,
       16, 64,  5, 39,  8,  0,  1, 65, 46,  3, 63, 11, 57, 53,  2, 48, 29,
       14, 26, 33, 51, 38, 60, 32, 61, 23, 20,  4, 31, 45, 18, 24]), 'cur_cost': 92823.0}, {'tour': array([21, 59, 50, 39, 30, 48,  3, 11, 26, 34,  4, 32, 57, 62, 45, 27, 47,
       25,  0, 38, 53, 43, 64, 44, 54, 17, 52,  8, 51,  6, 35,  1, 65, 22,
       46,  5, 15, 49, 58, 40,  2, 19, 10, 55,  9, 33, 36, 63, 16, 28, 42,
       29, 41, 20,  7, 56, 14, 23, 13, 12, 18, 61, 60, 37, 24, 31]), 'cur_cost': 111095.0}, {'tour': array([16,  8, 38, 11, 41, 14, 19, 43, 29, 12, 40,  4, 61, 46, 64, 10,  5,
       62, 17,  0, 27, 44, 36, 22, 37, 23, 33, 31, 55, 30, 50, 13,  2, 20,
       56, 34, 54, 48, 65, 28, 53, 58,  9, 26, 39, 25,  7, 47, 24,  3, 57,
       42, 45, 35, 59, 49, 32, 52, 21, 63, 51,  6, 60,  1, 15, 18]), 'cur_cost': 118773.0}, {'tour': array([ 3, 62, 52, 34, 17, 45, 40, 19, 27, 39, 51, 53, 58, 55, 49, 24, 29,
       33, 28, 63, 18,  2, 15, 50, 22, 25, 16, 44, 57, 56,  1,  9, 65, 37,
       30, 61, 13, 14, 42, 31, 20,  8, 21,  0, 64, 48, 35, 60,  6, 23, 47,
       38,  7,  4, 54, 41, 43, 11, 46, 32, 10, 12,  5, 36, 59, 26]), 'cur_cost': 95829.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}, {'tour': array([40,  0, 55, 31, 43, 13, 10, 16, 39,  8, 48, 20,  9, 59, 54,  3, 17,
       28,  6, 36,  5, 47, 11, 63, 25,  4, 38, 30, 21, 29, 53, 23, 37, 56,
       12, 41, 32, 61,  2, 26, 65, 51, 60,  7, 35,  1, 58, 64, 57, 52, 46,
       15, 24, 19, 44, 18, 45, 49, 34, 62, 50, 27, 14, 42, 22, 33]), 'cur_cost': 113843.0}, {'tour': array([13, 35, 19, 10, 43, 20, 45, 57, 24, 31, 27, 61, 25, 30, 53,  8, 38,
        3, 42, 32, 17, 46, 58, 33,  9, 56, 64, 49, 51, 60,  2, 63, 22, 40,
       48, 28, 44, 52, 11, 12, 37, 23, 29, 39, 50, 59,  4, 41, 15,  7, 55,
        5,  0, 54, 34,  6, 65, 16, 14, 47, 36,  1, 18, 26, 62, 21]), 'cur_cost': 105087.0}, {'tour': array([26, 57, 18, 51, 41, 44, 54, 63, 52, 27,  6,  8, 21,  4, 40, 34, 22,
       38, 42, 14, 48, 56, 36, 55, 65, 29, 11, 15,  1,  0, 50,  2, 39, 59,
        3, 25, 12, 19, 35, 33, 32, 43, 30, 45, 16, 31,  5, 62,  9, 46, 53,
       37, 28, 47,  7, 24, 64, 60, 10, 61, 49, 17, 13, 23, 58, 20]), 'cur_cost': 100853.0}, {'tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}]
2025-07-03 16:24:24,040 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 16:24:24,040 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 7, 'total_searches': 21, 'skip_rate': 0.3333333333333333, 'estimated_time_saved': 35, 'path_optimizer': {'path_count': 14, 'cache_hits': 45, 'similarity_calculations': 138, 'cache_hit_rate': 0.32608695652173914, 'cache_size': 93}}
2025-07-03 16:24:24,040 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 16:24:24,041 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 16:24:24,041 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 16:24:24,041 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 16:24:24,041 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 99572.0
2025-07-03 16:24:24,042 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：6,跳过局部搜索
2025-07-03 16:24:24,042 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 16:24:24,042 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([44, 60, 54,  6, 43, 49, 19,  7, 63,  4, 18, 64, 12, 22, 37,  5, 13,
       61, 33, 26, 14, 47, 17, 65, 55,  0, 10, 51,  9, 31, 57, 25,  1, 23,
       30, 50,  3, 15, 53, 16, 59, 32, 35, 28, 40, 27, 29, 48, 39, 46, 58,
       24,  2, 11, 34, 41, 62, 20, 56, 21, 36, 52,  8, 38, 45, 42]), 'cur_cost': 99572.0}
2025-07-03 16:24:24,043 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 36, 12, 43, 27, 55, 59, 30, 34, 41,  9, 62, 56, 58, 52, 40, 37,
       15, 10, 50, 44, 47, 19,  7,  6, 35, 25, 13, 21, 17, 42, 22, 28, 49,
       16, 64,  5, 39,  8,  0,  1, 65, 46,  3, 63, 11, 57, 53,  2, 48, 29,
       14, 26, 33, 51, 38, 60, 32, 61, 23, 20,  4, 31, 45, 18, 24]), 'cur_cost': 92823.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([21, 59, 50, 39, 30, 48,  3, 11, 26, 34,  4, 32, 57, 62, 45, 27, 47,
       25,  0, 38, 53, 43, 64, 44, 54, 17, 52,  8, 51,  6, 35,  1, 65, 22,
       46,  5, 15, 49, 58, 40,  2, 19, 10, 55,  9, 33, 36, 63, 16, 28, 42,
       29, 41, 20,  7, 56, 14, 23, 13, 12, 18, 61, 60, 37, 24, 31]), 'cur_cost': 111095.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 62, 52, 34, 17, 45, 40, 19, 27, 39, 51, 53, 58, 55, 49, 24, 29,
       33, 28, 63, 18,  2, 15, 50, 22, 25, 16, 44, 57, 56,  1,  9, 65, 37,
       30, 61, 13, 14, 42, 31, 20,  8, 21,  0, 64, 48, 35, 60,  6, 23, 47,
       38,  7,  4, 54, 41, 43, 11, 46, 32, 10, 12,  5, 36, 59, 26]), 'cur_cost': 95829.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 4, 5, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 2], 'cur_cost': 11573.0}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([13, 35, 19, 10, 43, 20, 45, 57, 24, 31, 27, 61, 25, 30, 53,  8, 38,
        3, 42, 32, 17, 46, 58, 33,  9, 56, 64, 49, 51, 60,  2, 63, 22, 40,
       48, 28, 44, 52, 11, 12, 37, 23, 29, 39, 50, 59,  4, 41, 15,  7, 55,
        5,  0, 54, 34,  6, 65, 16, 14, 47, 36,  1, 18, 26, 62, 21]), 'cur_cost': 105087.0}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 60, 54,  6, 43, 49, 19,  7, 63,  4, 18, 64, 12, 22, 37,  5, 13,
       61, 33, 26, 14, 47, 17, 65, 55,  0, 10, 51,  9, 31, 57, 25,  1, 23,
       30, 50,  3, 15, 53, 16, 59, 32, 35, 28, 40, 27, 29, 48, 39, 46, 58,
       24,  2, 11, 34, 41, 62, 20, 56, 21, 36, 52,  8, 38, 45, 42]), 'cur_cost': 99572.0}}]
2025-07-03 16:24:24,044 - __main__ - INFO - 进化阶段完成
2025-07-03 16:24:24,044 - __main__ - INFO - 开始评估阶段
2025-07-03 16:24:24,044 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 16:24:24,047 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 16:24:24,047 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 11573.0 → 11573.0 (Δ 0.0)
- Mean cost: 50746.3 → 86102.1
- Diversity: 0.475 → 0.809 (Δ 0.3338)

#### Elite metrics
- Elite count: 27 → 29
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.100 → 0.093 (Δ -0.0069)

#### Strategy performance
- Explore: 2 inds, success 50.0% , avg Δ 50056.0
- Exploit: 8 inds, success 12.5% , avg Δ -56708.75

#### Other indicators
- No-change individuals: 1
- Regression individuals: 7
- Historical trends: Last 3 iterations: costs [11573.0, 11573.0, 11573.0], diversity [0.6424242424242423, 0.5114478114478115, 0.6525252525252526]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 16:24:24,047 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 16:24:34,382 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.125},
  "balance_state": "explore_underutilized",
  "recommendations": ["increase_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:24:34,411 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 16:24:34,412 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.125},
  "balance_state": "explore_underutilized",
  "recommendations": ["increase_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:24:34,412 - __main__ - INFO - 评估阶段完成
2025-07-03 16:24:34,413 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.125},
  "balance_state": "explore_underutilized",
  "recommendations": ["increase_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 16:24:34,413 - __main__ - INFO - 当前最佳适应度: 11573.0
2025-07-03 16:24:34,414 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_4.pkl
2025-07-03 16:24:34,427 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-03 16:24:34,428 - __main__ - INFO - 实例 composite13_66 处理完成
