2025-06-26 19:17:47,763 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-26 19:17:47,763 - __main__ - INFO - 开始分析阶段
2025-06-26 19:17:47,764 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:17:47,777 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9951.0, 'max': 104755.0, 'mean': 73374.5, 'std': 41617.15253894721}, 'diversity': 0.9168350168350167, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:17:47,777 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9951.0, 'max': 104755.0, 'mean': 73374.5, 'std': 41617.15253894721}, 'diversity_level': 0.9168350168350167, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-26 19:17:47,791 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:17:47,791 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:17:47,792 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:17:47,798 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:17:47,798 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (41, 42), 'frequency': 0.5, 'avg_cost': 90.0}], 'common_subpaths': [{'subpath': (64, 57, 54), 'frequency': 0.3}, {'subpath': (65, 52, 63), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (26, 36, 35), 'frequency': 0.3}, {'subpath': (36, 35, 28), 'frequency': 0.3}, {'subpath': (35, 28, 30), 'frequency': 0.3}, {'subpath': (28, 30, 34), 'frequency': 0.3}, {'subpath': (30, 34, 33), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(4, 5)', 'frequency': 0.4}, {'edge': '(59, 62)', 'frequency': 0.4}, {'edge': '(57, 64)', 'frequency': 0.4}, {'edge': '(52, 65)', 'frequency': 0.4}, {'edge': '(12, 22)', 'frequency': 0.4}, {'edge': '(41, 42)', 'frequency': 0.5}, {'edge': '(22, 43)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 7)', 'frequency': 0.3}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(5, 9)', 'frequency': 0.2}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(0, 55)', 'frequency': 0.3}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.2}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.2}, {'edge': '(60, 64)', 'frequency': 0.2}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.2}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(14, 63)', 'frequency': 0.2}, {'edge': '(14, 15)', 'frequency': 0.2}, {'edge': '(15, 22)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(17, 18)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.3}, {'edge': '(13, 20)', 'frequency': 0.3}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(19, 21)', 'frequency': 0.2}, {'edge': '(19, 27)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(32, 40)', 'frequency': 0.2}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.2}, {'edge': '(46, 48)', 'frequency': 0.3}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(39, 43)', 'frequency': 0.3}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.3}, {'edge': '(39, 46)', 'frequency': 0.3}, {'edge': '(21, 42)', 'frequency': 0.2}, {'edge': '(14, 17)', 'frequency': 0.2}, {'edge': '(17, 19)', 'frequency': 0.2}, {'edge': '(47, 63)', 'frequency': 0.3}, {'edge': '(61, 65)', 'frequency': 0.2}, {'edge': '(19, 61)', 'frequency': 0.2}, {'edge': '(6, 24)', 'frequency': 0.2}, {'edge': '(1, 56)', 'frequency': 0.2}, {'edge': '(26, 37)', 'frequency': 0.3}, {'edge': '(8, 39)', 'frequency': 0.2}, {'edge': '(53, 54)', 'frequency': 0.2}, {'edge': '(3, 38)', 'frequency': 0.3}, {'edge': '(55, 58)', 'frequency': 0.2}, {'edge': '(7, 10)', 'frequency': 0.2}, {'edge': '(11, 55)', 'frequency': 0.2}, {'edge': '(11, 44)', 'frequency': 0.3}, {'edge': '(12, 62)', 'frequency': 0.2}, {'edge': '(57, 65)', 'frequency': 0.2}, {'edge': '(24, 45)', 'frequency': 0.2}, {'edge': '(8, 26)', 'frequency': 0.2}, {'edge': '(40, 51)', 'frequency': 0.2}, {'edge': '(19, 25)', 'frequency': 0.2}, {'edge': '(8, 19)', 'frequency': 0.2}, {'edge': '(24, 65)', 'frequency': 0.2}, {'edge': '(15, 48)', 'frequency': 0.2}, {'edge': '(5, 34)', 'frequency': 0.2}, {'edge': '(50, 60)', 'frequency': 0.2}, {'edge': '(27, 49)', 'frequency': 0.2}, {'edge': '(7, 51)', 'frequency': 0.2}, {'edge': '(16, 44)', 'frequency': 0.2}, {'edge': '(9, 58)', 'frequency': 0.2}, {'edge': '(17, 43)', 'frequency': 0.2}, {'edge': '(18, 31)', 'frequency': 0.2}, {'edge': '(10, 29)', 'frequency': 0.2}, {'edge': '(40, 42)', 'frequency': 0.2}, {'edge': '(48, 49)', 'frequency': 0.3}, {'edge': '(21, 27)', 'frequency': 0.2}, {'edge': '(1, 5)', 'frequency': 0.2}, {'edge': '(27, 30)', 'frequency': 0.2}, {'edge': '(2, 49)', 'frequency': 0.2}, {'edge': '(16, 50)', 'frequency': 0.2}, {'edge': '(15, 26)', 'frequency': 0.2}, {'edge': '(21, 35)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [39, 63, 47, 62, 28, 60], 'cost': 14412.0, 'size': 6}, {'region': [48, 60, 37, 59, 42, 31], 'cost': 13610.0, 'size': 6}, {'region': [26, 52, 24, 65, 38], 'cost': 11685.0, 'size': 5}, {'region': [50, 60, 49, 27, 39], 'cost': 9749.0, 'size': 5}, {'region': [38, 59, 35, 53], 'cost': 8719.0, 'size': 4}]}
2025-06-26 19:17:47,799 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:17:47,799 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:17:47,799 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:17:47,799 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 19:17:47,800 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 19:17:47,800 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:17:47,801 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:17:47,803 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:17:47,803 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9951.0, mean 73374.5, max 104755.0, std 41617.15253894721
- diversity: 0.9168350168350167
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [83548.0, 13233.0, 21405.0] (Δ 35157.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (41, 42), 'frequency': 0.5, 'avg_cost': 90.0}]
- common_subpaths_sample: [{'subpath': (64, 57, 54), 'frequency': 0.3}, {'subpath': (65, 52, 63), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [39, 63, 47, 62, 28, 60], 'cost': 14412.0, 'size': 6}, {'region': [48, 60, 37, 59, 42, 31], 'cost': 13610.0, 'size': 6}, {'region': [26, 52, 24, 65, 38], 'cost': 11685.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:17:47,804 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:18:18,254 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 10.0 to 3032.0 with Q1-Q3 spread of 1075.0-2469.0)",
    "modality": "Potentially multimodal (presence of both short and long edges, with 24.9% long edges)",
    "deceptiveness": "Possible deceptive regions (high cost regions identified despite some high-quality edges)"
  },
  "population_state": {
    "coverage": "Good initial diversity (0.917) but uneven spatial distribution (density_grid shows clusters and empty cells)",
    "convergence": "Early stage (0.0 convergence, no stagnation detected)",
    "elite_influence": "None yet (0 elite solutions)"
  },
  "difficult_regions": [
    {"region": [39, 63, 47, 62, 28, 60], "reason": "High cost (14412) with clustered nodes in NW density_grid gap"},
    {"region": [48, 60, 37, 59, 42, 31], "reason": "High cost (13610) near centroid but sparse in density_grid"},
    {"region": [26, 52, 24, 65, 38], "reason": "High cost (11685) in directionally sparse sector (histogram bin 5)"}
  ],
  "opportunity_regions": [
    {"potential_nodes": [41,42], "reason": "High-quality edge (freq 0.5, avg cost 90) in dense SE grid cell"},
    {"potential_subpath": [64,57,54], "reason": "Frequent subpath (0.3) crossing medium-density regions"}
  ],
  "evolution_phase": "Exploration (initial population with high diversity)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Edge recombination focusing on high-quality edges (41-42)",
    "2. Local search intensification around dense SE grid cell",
    "3. Path relinking for common subpaths (64-57-54, 65-52-63)",
    "4. Adaptive mutation rates for long-edge corridors"
  ]
}
```
2025-06-26 19:18:18,254 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:18:18,254 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 3032.0 with Q1-Q3 spread of 1075.0-2469.0)', 'modality': 'Potentially multimodal (presence of both short and long edges, with 24.9% long edges)', 'deceptiveness': 'Possible deceptive regions (high cost regions identified despite some high-quality edges)'}, 'population_state': {'coverage': 'Good initial diversity (0.917) but uneven spatial distribution (density_grid shows clusters and empty cells)', 'convergence': 'Early stage (0.0 convergence, no stagnation detected)', 'elite_influence': 'None yet (0 elite solutions)'}, 'difficult_regions': [{'region': [39, 63, 47, 62, 28, 60], 'reason': 'High cost (14412) with clustered nodes in NW density_grid gap'}, {'region': [48, 60, 37, 59, 42, 31], 'reason': 'High cost (13610) near centroid but sparse in density_grid'}, {'region': [26, 52, 24, 65, 38], 'reason': 'High cost (11685) in directionally sparse sector (histogram bin 5)'}], 'opportunity_regions': [{'potential_nodes': [41, 42], 'reason': 'High-quality edge (freq 0.5, avg cost 90) in dense SE grid cell'}, {'potential_subpath': [64, 57, 54], 'reason': 'Frequent subpath (0.3) crossing medium-density regions'}], 'evolution_phase': 'Exploration (initial population with high diversity)', 'evolution_direction': ['Operator suggestions:', '1. Edge recombination focusing on high-quality edges (41-42)', '2. Local search intensification around dense SE grid cell', '3. Path relinking for common subpaths (64-57-54, 65-52-63)', '4. Adaptive mutation rates for long-edge corridors']}
2025-06-26 19:18:18,254 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:18:18,257 - __main__ - INFO - 分析阶段完成
2025-06-26 19:18:18,257 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 3032.0 with Q1-Q3 spread of 1075.0-2469.0)', 'modality': 'Potentially multimodal (presence of both short and long edges, with 24.9% long edges)', 'deceptiveness': 'Possible deceptive regions (high cost regions identified despite some high-quality edges)'}, 'population_state': {'coverage': 'Good initial diversity (0.917) but uneven spatial distribution (density_grid shows clusters and empty cells)', 'convergence': 'Early stage (0.0 convergence, no stagnation detected)', 'elite_influence': 'None yet (0 elite solutions)'}, 'difficult_regions': [{'region': [39, 63, 47, 62, 28, 60], 'reason': 'High cost (14412) with clustered nodes in NW density_grid gap'}, {'region': [48, 60, 37, 59, 42, 31], 'reason': 'High cost (13610) near centroid but sparse in density_grid'}, {'region': [26, 52, 24, 65, 38], 'reason': 'High cost (11685) in directionally sparse sector (histogram bin 5)'}], 'opportunity_regions': [{'potential_nodes': [41, 42], 'reason': 'High-quality edge (freq 0.5, avg cost 90) in dense SE grid cell'}, {'potential_subpath': [64, 57, 54], 'reason': 'Frequent subpath (0.3) crossing medium-density regions'}], 'evolution_phase': 'Exploration (initial population with high diversity)', 'evolution_direction': ['Operator suggestions:', '1. Edge recombination focusing on high-quality edges (41-42)', '2. Local search intensification around dense SE grid cell', '3. Path relinking for common subpaths (64-57-54, 65-52-63)', '4. Adaptive mutation rates for long-edge corridors']}
2025-06-26 19:18:18,257 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:18:18,257 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:18:18,257 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 9951.0
  • mean_cost: 73374.5
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 10.0 to 3032.0 with Q1-Q3 spread of 1075.0-2469.0), modality Potentially multimodal (presence of both short and long edges, with 24.9% long edges), deceptiveness Possible deceptive regions (high cost regions identified despite some high-quality edges)
- Evolution phase: Exploration (initial population with high diversity)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [39, 63, , {'region': [48, 60, ...
- Opportunity regions (# 2): {'potential_nodes': , {'potential_subpath'
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:18:18,257 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:18:18,257 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:18:28,152 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial population with high diversity and low convergence suggests strong exploration focus, with top 20% exploiting promising regions."
}
```
2025-06-26 19:18:28,152 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 19:18:28,152 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 19:18:28,152 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial population with high diversity and low convergence suggests strong exploration focus, with top 20% exploiting promising regions."
}
```
2025-06-26 19:18:28,152 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:18:28,152 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 19:18:28,152 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial population with high diversity and low convergence suggests strong exploration focus, with top 20% exploiting promising regions."
}
```
2025-06-26 19:18:28,152 - __main__ - INFO - 开始进化阶段
2025-06-26 19:18:28,152 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:18:28,152 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:18:28,152 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10304.0 (rank 20.0%)
- Population diversity: 0.7369230769230769
- Evolution phase: Exploration (initial population with high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [39,, {'region': [48,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:18:28,152 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:18:28,152 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:18:44,685 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "low-density cells adjacent to opportunity regions",
  "strategy_comment": "Balanced exploration with structured jumps to increase diversity"
}
```
2025-06-26 19:18:44,685 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:18:44,689 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:18:44,689 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:18:44,689 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:18:44,689 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:18:44,689 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9951.0 (rank 0.0%)
- Population diversity: 0.82
- Evolution phase: Exploration (initial population with high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [39,, {'region': [48,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:18:44,689 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:18:44,689 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:19:02,188 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 39, 48],
  "targeted_regions": "potential_nod, potential_sub",
  "strategy_comment": "Shift difficult nodes to end, explore sparse adjacency"
}
```
2025-06-26 19:19:02,188 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:19:02,190 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15787.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 39, 48]
2025-06-26 19:19:02,190 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 39, 48], 'cur_cost': 15787.0}
2025-06-26 19:19:02,190 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:19:02,190 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:19:02,190 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10027.0 (rank 0.0%)
- Population diversity: 0.8569230769230769
- Evolution phase: Exploration (initial population with high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [39,, {'region': [48,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:19:02,190 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:19:02,190 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:19:23,985 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "Sparse cells near opportunity regions",
  "strategy_comment": "Sequential jumps to explore low-density areas"
}
```
2025-06-26 19:19:23,985 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:19:23,985 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:19:23,985 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:19:23,985 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:19:23,985 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:19:23,985 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101744.0 (rank 60.0%)
- Population diversity: 0.7861538461538462
- Evolution phase: Exploration (initial population with high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [39,, {'region': [48,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:19:23,993 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:19:23,993 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:19:40,701 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11],
  "targeted_regions": "potential_nod, potential_sub adjacent cells",
  "strategy_comment": "Balanced exploration with diagonal node jumps"
}
```
2025-06-26 19:19:40,701 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:19:40,701 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93216.0, 路径: [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11]
2025-06-26 19:19:40,701 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}
2025-06-26 19:19:40,701 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:19:40,701 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:19:40,701 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 104755.0 (rank 90.0%)
- Population diversity: 0.7615384615384615
- Evolution phase: Exploration (initial population with high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [39,, {'region': [48,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:19:40,701 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:19:40,701 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:19:58,202 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "low-density cells adjacent to opportunity regions",
  "strategy_comment": "Leverage modular jumps to explore sparse regions"
}
```
2025-06-26 19:19:58,202 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:19:58,202 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:19:58,202 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:19:58,202 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:19:58,202 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:19:58,202 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 104211.0 (rank 80.0%)
- Population diversity: 0.68
- Evolution phase: Exploration (initial population with high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [39,, {'region': [48,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:19:58,202 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:19:58,202 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:20:14,668 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "low-density cells adjacent to opportunity regions",
  "strategy_comment": "Balanced exploration with modular jumps"
}
```
2025-06-26 19:20:14,668 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:20:14,672 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:20:14,672 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:20:14,672 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 19:20:14,672 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:20:14,672 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 90545.0 (rank 10.0%)
- Population diversity: 0.6138461538461538
- Evolution phase: Exploration (initial population with high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [39,, {'region': [48,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:20:14,672 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:20:14,672 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:20:34,289 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11],
  "targeted_regions": "potential_nod, potential_sub adjacent cells",
  "strategy_comment": "Sequential jumps to explore sparse regions"
}
```
2025-06-26 19:20:34,297 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:20:34,297 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93216.0, 路径: [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11]
2025-06-26 19:20:34,297 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}
2025-06-26 19:20:34,297 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 19:20:34,297 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:20:34,297 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 97598.0 (rank 30.0%)
- Population diversity: 0.5292307692307693
- Evolution phase: Exploration (initial population with high diversity)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_sub
- Difficult regions to avoid (sample): {'region': [39,, {'region': [48,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:20:34,297 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:20:34,297 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:20:53,745 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "Low-density columns in grid",
  "strategy_comment": "Column-wise traversal to explore sparse regions"
}
```
2025-06-26 19:20:53,751 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:20:53,751 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:20:53,751 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:20:53,751 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:20:53,751 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:20:53,751 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:20:53,751 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 102882.0
2025-06-26 19:20:54,252 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 19:20:54,254 - ExploitationExpert - INFO - res_population_costs: [9567, 9561, 9551, 9533, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-26 19:20:54,254 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-06-26 19:20:54,258 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:20:54,258 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 39, 48], 'cur_cost': 15787.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': array([ 9, 52, 13, 22, 57, 30, 47,  0,  1, 45, 41, 24, 46, 11, 23, 43, 31,
       44, 65, 64,  4, 40, 20, 55, 63, 10, 42, 54, 50,  5, 21, 17, 62, 37,
        6,  3, 14, 34, 36, 56, 61, 32, 12, 25, 33, 53, 15, 48, 38, 51, 26,
       29, 19,  2,  7, 28, 60, 39, 35, 49, 59,  8, 58, 18, 27, 16]), 'cur_cost': 102882.0}, {'tour': [21, 35, 46, 39, 63, 47, 62, 28, 60, 5, 4, 64, 57, 13, 24, 6, 36, 55, 34, 18, 17, 11, 44, 3, 38, 50, 16, 54, 52, 37, 42, 41, 45, 20, 40, 51, 61, 33, 8, 0, 25, 53, 32, 31, 43, 22, 59, 2, 49, 48, 15, 26, 12, 27, 30, 1, 7, 19, 56, 14, 65, 58, 9, 23, 29, 10], 'cur_cost': 100010.0}]
2025-06-26 19:20:54,259 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:20:54,259 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 318, 'skip_rate': 0.040880503144654086, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 305, 'cache_hits': 200, 'similarity_calculations': 4391, 'cache_hit_rate': 0.04554771122751082, 'cache_size': 4191}}
2025-06-26 19:20:54,259 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:20:54,259 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:20:54,260 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:20:54,260 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:20:54,260 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 96452.0
2025-06-26 19:20:54,761 - ExploitationExpert - INFO - res_population_num: 17
2025-06-26 19:20:54,763 - ExploitationExpert - INFO - res_population_costs: [9567, 9561, 9551, 9533, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-26 19:20:54,763 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-26 19:20:54,770 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:20:54,770 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 39, 48], 'cur_cost': 15787.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': array([ 9, 52, 13, 22, 57, 30, 47,  0,  1, 45, 41, 24, 46, 11, 23, 43, 31,
       44, 65, 64,  4, 40, 20, 55, 63, 10, 42, 54, 50,  5, 21, 17, 62, 37,
        6,  3, 14, 34, 36, 56, 61, 32, 12, 25, 33, 53, 15, 48, 38, 51, 26,
       29, 19,  2,  7, 28, 60, 39, 35, 49, 59,  8, 58, 18, 27, 16]), 'cur_cost': 102882.0}, {'tour': array([23, 63, 41, 30,  8,  1, 47, 18, 40, 46, 56, 17, 62, 31, 64, 24, 25,
       13, 21, 42, 34, 14,  2, 55, 38,  6, 32, 36, 57, 35, 54, 59, 11, 16,
       33,  9,  7, 51, 15,  4, 27, 50, 26, 10, 52, 53,  0, 58, 65, 37, 19,
        5, 12, 22, 43, 45, 48, 29, 61, 60,  3, 28, 44, 39, 49, 20]), 'cur_cost': 96452.0}]
2025-06-26 19:20:54,772 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:20:54,772 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 319, 'skip_rate': 0.04075235109717868, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 306, 'cache_hits': 200, 'similarity_calculations': 4392, 'cache_hit_rate': 0.04553734061930783, 'cache_size': 4192}}
2025-06-26 19:20:54,772 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:20:54,772 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 39, 48], 'cur_cost': 15787.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}]
2025-06-26 19:20:54,772 - __main__ - INFO - 进化阶段完成
2025-06-26 19:20:54,772 - __main__ - INFO - 开始评估阶段
2025-06-26 19:20:54,772 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:20:54,775 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:20:54,775 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9951.0 → 15787.0 (Δ -5836.0)
- Mean cost: 73374.5 → 90857.8
- Diversity: 0.737 → 0.462 (Δ -0.2754)

#### Elite metrics
- Elite count: 0 → 17
- Best elite cost: 9951.0 → 9521 (Δ 430.0)

#### Strategy performance
- Explore: 8 inds, success 37.5% , avg Δ -22513.62
- Exploit: 2 inds, success 100.0% , avg Δ 2638.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 5
- Historical trends: Last 3 iterations: costs [83548.0, 13233.0, 21405.0], diversity [0.8274074074074075, 0.6466666666666667, 0.8422222222222223]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:20:54,775 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:21:08,280 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 37.5,
      "avg_improvement": -22513.62,
      "contribution_to_elite": "negative"
    },
    "exploit": {
      "success_rate": 100.0,
      "avg_improvement": 2638.0,
      "contribution_to_elite": "positive"
    }
  },
  "balance_state": "exploit-heavy with insufficient exploration",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_actions": "increase mutation rate for exploration individuals"
  }
}
```
2025-06-26 19:21:08,308 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:21:08,308 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 37.5,
      "avg_improvement": -22513.62,
      "contribution_to_elite": "negative"
    },
    "exploit": {
      "success_rate": 100.0,
      "avg_improvement": 2638.0,
      "contribution_to_elite": "positive"
    }
  },
  "balance_state": "exploit-heavy with insufficient exploration",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_actions": "increase mutation rate for exploration individuals"
  }
}
```
2025-06-26 19:21:08,308 - __main__ - INFO - 评估阶段完成
2025-06-26 19:21:08,308 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 37.5,
      "avg_improvement": -22513.62,
      "contribution_to_elite": "negative"
    },
    "exploit": {
      "success_rate": 100.0,
      "avg_improvement": 2638.0,
      "contribution_to_elite": "positive"
    }
  },
  "balance_state": "exploit-heavy with insufficient exploration",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_actions": "increase mutation rate for exploration individuals"
  }
}
```
2025-06-26 19:21:08,308 - __main__ - INFO - 当前最佳适应度: 15787.0
2025-06-26 19:21:08,308 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-26 19:21:08,308 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-26 19:21:08,308 - __main__ - INFO - 开始分析阶段
2025-06-26 19:21:08,308 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:21:08,326 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 15787.0, 'max': 102882.0, 'mean': 90857.8, 'std': 25256.367168696295}, 'diversity': 0.7057239057239058, 'clusters': {'clusters': 5, 'cluster_sizes': [5, 1, 2, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:21:08,326 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 15787.0, 'max': 102882.0, 'mean': 90857.8, 'std': 25256.367168696295}, 'diversity_level': 0.7057239057239058, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [5, 1, 2, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-26 19:21:08,329 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:21:08,329 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:21:08,329 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:21:08,331 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 19:21:08,331 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:21:08,331 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 19:21:08,332 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:21:08,332 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:21:08,332 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:21:08,338 - EliteExpert - INFO - 精英解分析完成
2025-06-26 19:21:08,338 - __main__ - INFO - 精英专家分析报告: {'elite_count': 17, 'elite_common_features': {'common_edges': {'(38, 51)': 0.7647058823529411}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 6266.0, 'avg_gap': 81329.27058823529}, 'structure_gap': {'unique_elite_edges': 181, 'unique_pop_edges': 289, 'common_edges': 24}}, 'elite_diversity': {'diversity_score': 0.20409982174688057}}
2025-06-26 19:21:08,338 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:21:08,339 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:21:08,341 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:21:08,341 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 15787.0, mean 90857.8, max 102882.0, std 25256.367168696295
- diversity: 0.7057239057239058
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [13233.0, 21405.0, 15787.0] (Δ 2809.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:21:08,342 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:21:38,509 - InterfaceAPI.deepseek - ERROR - DeepSeek API请求网络错误 (尝试 1/3): HTTPSConnectionPool(host='api.deepseek.com', port=443): Read timed out.
2025-06-26 19:21:38,509 - InterfaceAPI.deepseek - INFO - 等待 2 秒后重试...
2025-06-26 19:21:40,512 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 2/3)
2025-06-26 19:22:06,793 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "potentially multimodal",
    "deceptiveness": "low to moderate",
    "edge_length_variability": "high",
    "long_edge_ratio": "moderate (24.9%)"
  },
  "population_state": {
    "coverage": "uneven (high density in some cells, empty in others)",
    "convergence": "none detected",
    "diversity": "high (0.706)",
    "spread": "wide (rms 1310.584)",
    "elite_influence": "none (elite_count: 0)"
  },
  "difficult_regions": {
    "potential_candidates": [
      "low-density cells in grid",
      "regions with long edges (q3: 2469, max: 3032)"
    ],
    "structural_signals": "none identified yet"
  },
  "opportunity_regions": {
    "potential_candidates": [
      "high-density cells in grid (e.g., [14, 0, 14] in density_grid)",
      "regions with median edge lengths (med: 1900)"
    ],
    "structural_signals": "none identified yet"
  },
  "evolution_phase": "early exploration (iteration 1/5)",
  "evolution_direction": {
    "suggested_operators": [
      "edge recombination to exploit high-density regions",
      "local search around median-length edges",
      "diversity maintenance in low-density cells",
      "long-edge avoidance or repair heuristics"
    ],
    "monitoring_priorities": [
      "emergence of elite solutions",
      "changes in density_grid patterns",
      "stagnation in recent_best_costs"
    ]
  }
}
```
2025-06-26 19:22:06,793 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:22:06,793 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low to moderate', 'edge_length_variability': 'high', 'long_edge_ratio': 'moderate (24.9%)'}, 'population_state': {'coverage': 'uneven (high density in some cells, empty in others)', 'convergence': 'none detected', 'diversity': 'high (0.706)', 'spread': 'wide (rms 1310.584)', 'elite_influence': 'none (elite_count: 0)'}, 'difficult_regions': {'potential_candidates': ['low-density cells in grid', 'regions with long edges (q3: 2469, max: 3032)'], 'structural_signals': 'none identified yet'}, 'opportunity_regions': {'potential_candidates': ['high-density cells in grid (e.g., [14, 0, 14] in density_grid)', 'regions with median edge lengths (med: 1900)'], 'structural_signals': 'none identified yet'}, 'evolution_phase': 'early exploration (iteration 1/5)', 'evolution_direction': {'suggested_operators': ['edge recombination to exploit high-density regions', 'local search around median-length edges', 'diversity maintenance in low-density cells', 'long-edge avoidance or repair heuristics'], 'monitoring_priorities': ['emergence of elite solutions', 'changes in density_grid patterns', 'stagnation in recent_best_costs']}}
2025-06-26 19:22:06,793 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:22:06,793 - __main__ - INFO - 分析阶段完成
2025-06-26 19:22:06,793 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'potentially multimodal', 'deceptiveness': 'low to moderate', 'edge_length_variability': 'high', 'long_edge_ratio': 'moderate (24.9%)'}, 'population_state': {'coverage': 'uneven (high density in some cells, empty in others)', 'convergence': 'none detected', 'diversity': 'high (0.706)', 'spread': 'wide (rms 1310.584)', 'elite_influence': 'none (elite_count: 0)'}, 'difficult_regions': {'potential_candidates': ['low-density cells in grid', 'regions with long edges (q3: 2469, max: 3032)'], 'structural_signals': 'none identified yet'}, 'opportunity_regions': {'potential_candidates': ['high-density cells in grid (e.g., [14, 0, 14] in density_grid)', 'regions with median edge lengths (med: 1900)'], 'structural_signals': 'none identified yet'}, 'evolution_phase': 'early exploration (iteration 1/5)', 'evolution_direction': {'suggested_operators': ['edge recombination to exploit high-density regions', 'local search around median-length edges', 'diversity maintenance in low-density cells', 'long-edge avoidance or repair heuristics'], 'monitoring_priorities': ['emergence of elite solutions', 'changes in density_grid patterns', 'stagnation in recent_best_costs']}}
2025-06-26 19:22:06,793 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:22:06,793 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:22:06,793 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 15787.0
  • mean_cost: 90857.8
- Search-space: ruggedness moderate, modality potentially multimodal, deceptiveness low to moderate
- Evolution phase: early exploration (iteration 1/5)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): potential_candidates, structural_signals
- Opportunity regions (# 2): potential_candidates, structural_signals
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:22:06,793 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:22:06,793 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:22:17,817 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% exploit to seed quality."
}
```
2025-06-26 19:22:17,817 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 19:22:17,817 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 19:22:17,817 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% exploit to seed quality."
}
```
2025-06-26 19:22:17,817 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:22:17,817 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 19:22:17,817 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration; top 20% exploit to seed quality."
}
```
2025-06-26 19:22:17,817 - __main__ - INFO - 开始进化阶段
2025-06-26 19:22:17,817 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:22:17,817 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:22:17,817 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 40.0%)
- Population diversity: 0.46153846153846156
- Evolution phase: early exploration (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_candi, structural_sign
- Difficult regions to avoid (sample): potential_candi, structural_sign
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:22:17,817 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:22:17,817 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:22:34,267 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "adjacent low-density cells near opportunity regions",
  "strategy_comment": "Balanced exploration with structured jumps to increase diversity"
}
```
2025-06-26 19:22:34,267 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:22:34,267 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:22:34,267 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:22:34,267 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:22:34,269 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:22:34,269 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15787.0 (rank 0.0%)
- Population diversity: 0.46153846153846156
- Evolution phase: early exploration (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_candi, structural_sign
- Difficult regions to avoid (sample): potential_candi, structural_sign
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:22:34,269 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:22:34,269 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:22:56,667 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "potential_candi, structural_sign",
  "strategy_comment": "Sequential exploration with minor shuffles near opportunity regions"
}
```
2025-06-26 19:22:56,667 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:22:56,667 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-26 19:22:56,667 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-26 19:22:56,667 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:22:56,669 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:22:56,669 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 40.0%)
- Population diversity: 0.46153846153846156
- Evolution phase: early exploration (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_candi, structural_sign
- Difficult regions to avoid (sample): potential_candi, structural_sign
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:22:56,669 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:22:56,669 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:23:23,707 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "potential_candi, structural_sign",
  "strategy_comment": "Sequential exploration to maximize diversity"
}
```
2025-06-26 19:23:23,707 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:23:23,707 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-26 19:23:23,707 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-26 19:23:23,707 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:23:23,707 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:23:23,707 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 93216.0 (rank 20.0%)
- Population diversity: 0.46153846153846156
- Evolution phase: early exploration (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_candi, structural_sign
- Difficult regions to avoid (sample): potential_candi, structural_sign
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:23:23,707 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:23:23,707 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:23:40,183 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "low-density cells near opportunity regions",
  "strategy_comment": "Sequential exploration to maximize diversity"
}
```
2025-06-26 19:23:40,183 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:23:40,183 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-26 19:23:40,183 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-26 19:23:40,183 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:23:40,183 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:23:40,183 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 50.0%)
- Population diversity: 0.46153846153846156
- Evolution phase: early exploration (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_candi, structural_sign
- Difficult regions to avoid (sample): potential_candi, structural_sign
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:23:40,183 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:23:40,183 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:23:58,044 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11],
  "targeted_regions": "potential_candi adjacent sparse cells",
  "strategy_comment": "Alternating high-low index jumps to explore sparse regions"
}
```
2025-06-26 19:23:58,044 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:23:58,044 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93216.0, 路径: [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11]
2025-06-26 19:23:58,044 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}
2025-06-26 19:23:58,044 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:23:58,044 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:23:58,044 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 60.0%)
- Population diversity: 0.46153846153846156
- Evolution phase: early exploration (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_candi, structural_sign
- Difficult regions to avoid (sample): potential_candi, structural_sign
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:23:58,044 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:23:58,044 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:24:18,741 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "low-density cells adjacent to opportunity regions",
  "strategy_comment": "Balanced exploration with modular jumps to increase diversity"
}
```
2025-06-26 19:24:18,741 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:24:18,746 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:24:18,746 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:24:18,746 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 19:24:18,746 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:24:18,746 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 93216.0 (rank 30.0%)
- Population diversity: 0.46153846153846156
- Evolution phase: early exploration (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_candi, structural_sign
- Difficult regions to avoid (sample): potential_candi, structural_sign
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:24:18,746 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:24:18,749 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:24:35,572 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "low-density cells adjacent to opportunity regions",
  "strategy_comment": "Balanced exploration with modular jumps to increase diversity"
}
```
2025-06-26 19:24:35,572 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:24:35,572 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:24:35,580 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:24:35,580 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 19:24:35,580 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:24:35,580 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 50.0%)
- Population diversity: 0.46153846153846156
- Evolution phase: early exploration (iteration 1/5)
- Landscape recommends focus: balance
- Opportunity regions (sample): potential_candi, structural_sign
- Difficult regions to avoid (sample): potential_candi, structural_sign
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:24:35,580 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:24:35,580 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:24:58,005 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "low-density adjacent cells",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 19:24:58,005 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:24:58,013 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:24:58,013 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:24:58,013 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:24:58,013 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:24:58,013 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:24:58,013 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 114645.0
2025-06-26 19:24:58,515 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 19:24:58,516 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521, 9521, 9521, 9521]
2025-06-26 19:24:58,516 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-26 19:24:58,525 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:24:58,525 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': array([41, 61, 14, 40,  3, 44, 32,  4,  7,  9, 54, 12, 52, 22, 51, 57, 43,
       63, 26, 11, 58, 27, 15, 37, 20,  5, 56, 53, 24,  6, 36, 18, 33, 55,
       64,  2, 30, 17, 31, 59, 48, 23, 35, 28, 19, 47, 13, 38, 46, 62, 10,
       29, 65, 45, 60, 39, 42, 34,  1, 49, 21,  0, 50, 25, 16,  8]), 'cur_cost': 114645.0}, {'tour': array([23, 63, 41, 30,  8,  1, 47, 18, 40, 46, 56, 17, 62, 31, 64, 24, 25,
       13, 21, 42, 34, 14,  2, 55, 38,  6, 32, 36, 57, 35, 54, 59, 11, 16,
       33,  9,  7, 51, 15,  4, 27, 50, 26, 10, 52, 53,  0, 58, 65, 37, 19,
        5, 12, 22, 43, 45, 48, 29, 61, 60,  3, 28, 44, 39, 49, 20]), 'cur_cost': 96452.0}]
2025-06-26 19:24:58,526 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 19:24:58,527 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 320, 'skip_rate': 0.040625, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 307, 'cache_hits': 200, 'similarity_calculations': 4394, 'cache_hit_rate': 0.04551661356395084, 'cache_size': 4194}}
2025-06-26 19:24:58,527 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:24:58,527 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:24:58,528 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:24:58,528 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:24:58,528 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 105931.0
2025-06-26 19:24:59,031 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 19:24:59,031 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521, 9521, 9521, 9521, 9521]
2025-06-26 19:24:59,032 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-26 19:24:59,043 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:24:59,043 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': array([41, 61, 14, 40,  3, 44, 32,  4,  7,  9, 54, 12, 52, 22, 51, 57, 43,
       63, 26, 11, 58, 27, 15, 37, 20,  5, 56, 53, 24,  6, 36, 18, 33, 55,
       64,  2, 30, 17, 31, 59, 48, 23, 35, 28, 19, 47, 13, 38, 46, 62, 10,
       29, 65, 45, 60, 39, 42, 34,  1, 49, 21,  0, 50, 25, 16,  8]), 'cur_cost': 114645.0}, {'tour': array([21, 10, 30, 54, 41, 28, 13, 42, 20,  0, 44, 35, 24, 33, 16, 23,  6,
        4, 59, 36, 48, 32, 22, 11, 45,  1, 46, 39, 29, 49, 56,  5, 26, 25,
       47,  3, 43, 12, 57, 40, 27, 60, 14,  7, 65, 34, 52, 51, 37, 31, 17,
       64, 50, 19, 18, 15, 63,  9, 61, 62, 58,  8, 55, 53, 38,  2]), 'cur_cost': 105931.0}]
2025-06-26 19:24:59,044 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:24:59,044 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 321, 'skip_rate': 0.040498442367601244, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 308, 'cache_hits': 200, 'similarity_calculations': 4397, 'cache_hit_rate': 0.04548555833522856, 'cache_size': 4197}}
2025-06-26 19:24:59,044 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:24:59,046 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}]
2025-06-26 19:24:59,046 - __main__ - INFO - 进化阶段完成
2025-06-26 19:24:59,046 - __main__ - INFO - 开始评估阶段
2025-06-26 19:24:59,046 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:24:59,048 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:24:59,049 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 15787.0 → 11479.0 (Δ 4308.0)
- Mean cost: 90857.8 → 75384.9
- Diversity: 0.462 → 0.460 (Δ -0.0015)

#### Elite metrics
- Elite count: 17 → 22
- Best elite cost: 9521 → 9521 (Δ 0)

#### Strategy performance
- Explore: 8 inds, success 50.0% , avg Δ 21996.38
- Exploit: 2 inds, success 0.0% , avg Δ -10621.0

#### Other indicators
- No-change individuals: 3
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [13233.0, 21405.0, 15787.0], diversity [0.6466666666666667, 0.8422222222222223, 0.7057239057239058]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:24:59,049 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:25:19,520 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_improvement": 21996.38,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_improvement": -10621.0,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": "explore-heavy but effective, exploit ineffective",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "additional_suggestions": "Consider refining exploit strategy parameters or mutation operators to improve success rate"
  }
}
```
2025-06-26 19:25:19,547 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:25:19,547 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_improvement": 21996.38,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_improvement": -10621.0,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": "explore-heavy but effective, exploit ineffective",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "additional_suggestions": "Consider refining exploit strategy parameters or mutation operators to improve success rate"
  }
}
```
2025-06-26 19:25:19,547 - __main__ - INFO - 评估阶段完成
2025-06-26 19:25:19,553 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_improvement": 21996.38,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 0.0,
      "average_improvement": -10621.0,
      "contribution_to_elite": "none"
    }
  },
  "balance_state": "explore-heavy but effective, exploit ineffective",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "additional_suggestions": "Consider refining exploit strategy parameters or mutation operators to improve success rate"
  }
}
```
2025-06-26 19:25:19,553 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-26 19:25:19,555 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_1.pkl
2025-06-26 19:25:19,555 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-06-26 19:25:19,555 - __main__ - INFO - 开始分析阶段
2025-06-26 19:25:19,555 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:25:19,571 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 114645.0, 'mean': 75384.9, 'std': 42133.64734377977}, 'diversity': 0.7713804713804714, 'clusters': {'clusters': 5, 'cluster_sizes': [4, 3, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:25:19,572 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 114645.0, 'mean': 75384.9, 'std': 42133.64734377977}, 'diversity_level': 0.7713804713804714, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [4, 3, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-26 19:25:19,573 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:25:19,573 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:25:19,574 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:25:19,576 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 19:25:19,576 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:25:19,576 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 19:25:19,576 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:25:19,577 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:25:19,577 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:25:19,580 - EliteExpert - INFO - 精英解分析完成
2025-06-26 19:25:19,580 - __main__ - INFO - 精英专家分析报告: {'elite_count': 22, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1958.0, 'avg_gap': 65858.08181818182}, 'structure_gap': {'unique_elite_edges': 183, 'unique_pop_edges': 289, 'common_edges': 27}}, 'elite_diversity': {'diversity_score': 0.2057588875770694}}
2025-06-26 19:25:19,580 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:25:19,586 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:25:19,588 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:25:19,588 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 11479.0, mean 75384.9, max 114645.0, std 42133.64734377977
- diversity: 0.7713804713804714
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [21405.0, 15787.0, 11479.0] (Δ 4963.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:25:19,589 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:25:41,369 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "multimodal",
    "deceptiveness": "low",
    "edge_length_variability": "high",
    "long_edge_impact": "moderate"
  },
  "population_state": {
    "coverage": "partial",
    "convergence_status": "early_stage",
    "diversity_level": "high",
    "spatial_distribution": "uneven",
    "potential_stagnation": "none"
  },
  "difficult_regions": {
    "low_density_cells": ["cell_1_2", "cell_2_1"],
    "long_edge_corridors": ["edges_with_length_above_2469"],
    "sparse_directions": ["sector_5"]
  },
  "opportunity_regions": {
    "high_density_cells": ["cell_0_0", "cell_2_0", "cell_2_2"],
    "short_edge_clusters": ["edges_with_length_below_1075"],
    "dominant_directions": ["sector_3", "sector_6"]
  },
  "evolution_phase": "exploration",
  "evolution_direction": [
    "Increase exploitation in high-density cells",
    "Prioritize mutation operators that break long edges",
    "Enhance diversity maintenance in low-density regions",
    "Consider path-relinking between elite solutions",
    "Monitor direction histogram for emerging patterns"
  ]
}
```
2025-06-26 19:25:41,369 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:25:41,369 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high', 'long_edge_impact': 'moderate'}, 'population_state': {'coverage': 'partial', 'convergence_status': 'early_stage', 'diversity_level': 'high', 'spatial_distribution': 'uneven', 'potential_stagnation': 'none'}, 'difficult_regions': {'low_density_cells': ['cell_1_2', 'cell_2_1'], 'long_edge_corridors': ['edges_with_length_above_2469'], 'sparse_directions': ['sector_5']}, 'opportunity_regions': {'high_density_cells': ['cell_0_0', 'cell_2_0', 'cell_2_2'], 'short_edge_clusters': ['edges_with_length_below_1075'], 'dominant_directions': ['sector_3', 'sector_6']}, 'evolution_phase': 'exploration', 'evolution_direction': ['Increase exploitation in high-density cells', 'Prioritize mutation operators that break long edges', 'Enhance diversity maintenance in low-density regions', 'Consider path-relinking between elite solutions', 'Monitor direction histogram for emerging patterns']}
2025-06-26 19:25:41,369 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:25:41,369 - __main__ - INFO - 分析阶段完成
2025-06-26 19:25:41,369 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high', 'long_edge_impact': 'moderate'}, 'population_state': {'coverage': 'partial', 'convergence_status': 'early_stage', 'diversity_level': 'high', 'spatial_distribution': 'uneven', 'potential_stagnation': 'none'}, 'difficult_regions': {'low_density_cells': ['cell_1_2', 'cell_2_1'], 'long_edge_corridors': ['edges_with_length_above_2469'], 'sparse_directions': ['sector_5']}, 'opportunity_regions': {'high_density_cells': ['cell_0_0', 'cell_2_0', 'cell_2_2'], 'short_edge_clusters': ['edges_with_length_below_1075'], 'dominant_directions': ['sector_3', 'sector_6']}, 'evolution_phase': 'exploration', 'evolution_direction': ['Increase exploitation in high-density cells', 'Prioritize mutation operators that break long edges', 'Enhance diversity maintenance in low-density regions', 'Consider path-relinking between elite solutions', 'Monitor direction histogram for emerging patterns']}
2025-06-26 19:25:41,369 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:25:41,369 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:25:41,369 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 11479.0
  • mean_cost: 75384.9
- Search-space: ruggedness moderate, modality multimodal, deceptiveness low
- Evolution phase: exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): low_density_cells, long_edge_corridors...
- Opportunity regions (# 3): high_density_cells, short_edge_clusters...
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:25:41,369 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:25:41,369 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:25:53,287 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration phase suggest high exploration. Top 30% individuals exploit dense quality regions."
}
```
2025-06-26 19:25:53,287 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:25:53,287 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:25:53,287 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration phase suggest high exploration. Top 30% individuals exploit dense quality regions."
}
```
2025-06-26 19:25:53,287 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:25:53,287 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 19:25:53,287 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration phase suggest high exploration. Top 30% individuals exploit dense quality regions."
}
```
2025-06-26 19:25:53,287 - __main__ - INFO - 开始进化阶段
2025-06-26 19:25:53,287 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:25:53,287 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:25:53,287 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 40.0%)
- Population diversity: 0.46
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:25:53,287 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:25:53,287 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:26:11,877 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 66, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Leverage high-density clusters with short edges, avoiding long corridors"
}
```
2025-06-26 19:26:11,877 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:26:11,877 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97515.0, 路径: [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 50, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1]
2025-06-26 19:26:11,877 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 50, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 97515.0}
2025-06-26 19:26:11,878 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:26:11,878 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:26:11,878 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.49230769230769234
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:26:11,878 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:26:11,878 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:26:35,107 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Sequential path targeting opportunity regions"
}
```
2025-06-26 19:26:35,107 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:26:35,114 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-26 19:26:35,114 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-26 19:26:35,114 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:26:35,114 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:26:35,116 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.49230769230769234
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:26:35,116 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:26:35,116 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:26:59,994 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 66, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Leverage high-density clusters with short edges"
}
```
2025-06-26 19:26:59,994 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:26:59,994 - ExplorationExpert - INFO - 探索路径生成完成，成本: 97494.0, 路径: [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 39, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1]
2025-06-26 19:26:59,994 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 39, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 97494.0}
2025-06-26 19:26:59,994 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:26:59,994 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:27:00,002 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.4969230769230769
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:27:00,002 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:27:00,002 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:27:17,436 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Leverage modular jumps to explore sparse cells near opportunities"
}
```
2025-06-26 19:27:17,436 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:27:17,436 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 19:27:17,436 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 19:27:17,436 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:27:17,436 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:27:17,436 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 93216.0 (rank 10.0%)
- Population diversity: 0.5138461538461538
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:27:17,436 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:27:17,436 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:27:36,206 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Leverage clustered nodes with short edges, skip long corridors"
}
```
2025-06-26 19:27:36,206 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:27:36,206 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93216.0, 路径: [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11]
2025-06-26 19:27:36,206 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}
2025-06-26 19:27:36,206 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:27:36,206 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:27:36,206 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 50.0%)
- Population diversity: 0.5138461538461538
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:27:36,206 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:27:36,211 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:27:52,856 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Leverage clustered jumps in opportunity regions"
}
```
2025-06-26 19:27:52,856 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:27:52,859 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 19:27:52,859 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 19:27:52,859 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 19:27:52,859 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:27:52,859 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 60.0%)
- Population diversity: 0.5138461538461538
- Evolution phase: exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus...
- Difficult regions to avoid (sample): low_density_cel, long_edge_corri...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:27:52,859 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:27:52,859 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:28:15,998 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 66, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 19, 30, 41, 52, 4, 16, 27, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Leverage clustered nodes, alternate between dense/sparse regions"
}
```
2025-06-26 19:28:15,998 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:28:15,998 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95956.0, 路径: [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 19, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 30, 41, 52, 4, 16, 27, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64]
2025-06-26 19:28:15,998 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 19, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 30, 41, 52, 4, 16, 27, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64], 'cur_cost': 95956.0}
2025-06-26 19:28:15,998 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:28:15,998 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:28:15,998 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:28:15,998 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 113680.0
2025-06-26 19:28:16,500 - ExploitationExpert - INFO - res_population_num: 23
2025-06-26 19:28:16,500 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521]
2025-06-26 19:28:16,501 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-26 19:28:16,512 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:28:16,512 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 50, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 97515.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 39, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 97494.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 19, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 30, 41, 52, 4, 16, 27, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64], 'cur_cost': 95956.0}, {'tour': array([30, 22, 63, 39, 43, 65,  8, 33,  0, 20, 57, 54, 48, 28,  7,  4, 46,
       34, 11, 36, 32, 16, 24,  9, 15, 31, 38,  1, 37, 60,  2, 23, 44, 55,
       35, 61, 21, 17, 18, 14, 47,  6, 59, 26, 52, 50, 10, 45, 27, 29, 12,
       53, 51,  5, 13, 49, 62, 56, 42, 41, 64, 40, 19,  3, 58, 25]), 'cur_cost': 113680.0}, {'tour': array([41, 61, 14, 40,  3, 44, 32,  4,  7,  9, 54, 12, 52, 22, 51, 57, 43,
       63, 26, 11, 58, 27, 15, 37, 20,  5, 56, 53, 24,  6, 36, 18, 33, 55,
       64,  2, 30, 17, 31, 59, 48, 23, 35, 28, 19, 47, 13, 38, 46, 62, 10,
       29, 65, 45, 60, 39, 42, 34,  1, 49, 21,  0, 50, 25, 16,  8]), 'cur_cost': 114645.0}, {'tour': array([21, 10, 30, 54, 41, 28, 13, 42, 20,  0, 44, 35, 24, 33, 16, 23,  6,
        4, 59, 36, 48, 32, 22, 11, 45,  1, 46, 39, 29, 49, 56,  5, 26, 25,
       47,  3, 43, 12, 57, 40, 27, 60, 14,  7, 65, 34, 52, 51, 37, 31, 17,
       64, 50, 19, 18, 15, 63,  9, 61, 62, 58,  8, 55, 53, 38,  2]), 'cur_cost': 105931.0}]
2025-06-26 19:28:16,514 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:28:16,514 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 322, 'skip_rate': 0.040372670807453416, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 309, 'cache_hits': 200, 'similarity_calculations': 4401, 'cache_hit_rate': 0.04544421722335833, 'cache_size': 4201}}
2025-06-26 19:28:16,514 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:28:16,515 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:28:16,515 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:28:16,515 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:28:16,515 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 109696.0
2025-06-26 19:28:17,019 - ExploitationExpert - INFO - res_population_num: 24
2025-06-26 19:28:17,020 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521, 9521]
2025-06-26 19:28:17,020 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-26 19:28:17,031 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:28:17,032 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 50, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 97515.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 39, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 97494.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 19, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 30, 41, 52, 4, 16, 27, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64], 'cur_cost': 95956.0}, {'tour': array([30, 22, 63, 39, 43, 65,  8, 33,  0, 20, 57, 54, 48, 28,  7,  4, 46,
       34, 11, 36, 32, 16, 24,  9, 15, 31, 38,  1, 37, 60,  2, 23, 44, 55,
       35, 61, 21, 17, 18, 14, 47,  6, 59, 26, 52, 50, 10, 45, 27, 29, 12,
       53, 51,  5, 13, 49, 62, 56, 42, 41, 64, 40, 19,  3, 58, 25]), 'cur_cost': 113680.0}, {'tour': array([ 7, 48, 18, 40,  5, 10, 20, 16,  1, 14, 28, 41, 61, 51, 60, 34, 43,
       44, 57, 17, 32, 24,  2, 22, 35, 31, 33, 47, 36,  8, 64, 38, 37, 13,
       12, 54, 21, 59,  9, 55, 25, 29,  0, 63, 26, 65, 15, 23,  4, 52, 46,
        6, 56, 30, 49, 39, 53, 58, 19, 62, 42, 11,  3, 50, 27, 45]), 'cur_cost': 109696.0}, {'tour': array([21, 10, 30, 54, 41, 28, 13, 42, 20,  0, 44, 35, 24, 33, 16, 23,  6,
        4, 59, 36, 48, 32, 22, 11, 45,  1, 46, 39, 29, 49, 56,  5, 26, 25,
       47,  3, 43, 12, 57, 40, 27, 60, 14,  7, 65, 34, 52, 51, 37, 31, 17,
       64, 50, 19, 18, 15, 63,  9, 61, 62, 58,  8, 55, 53, 38,  2]), 'cur_cost': 105931.0}]
2025-06-26 19:28:17,034 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:28:17,034 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 323, 'skip_rate': 0.04024767801857585, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 310, 'cache_hits': 200, 'similarity_calculations': 4406, 'cache_hit_rate': 0.04539264639128461, 'cache_size': 4206}}
2025-06-26 19:28:17,034 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:28:17,034 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:28:17,034 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:28:17,035 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:28:17,036 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 105786.0
2025-06-26 19:28:17,542 - ExploitationExpert - INFO - res_population_num: 24
2025-06-26 19:28:17,542 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521, 9521]
2025-06-26 19:28:17,543 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-26 19:28:17,556 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:28:17,556 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 50, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 97515.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 39, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 97494.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 19, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 30, 41, 52, 4, 16, 27, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64], 'cur_cost': 95956.0}, {'tour': array([30, 22, 63, 39, 43, 65,  8, 33,  0, 20, 57, 54, 48, 28,  7,  4, 46,
       34, 11, 36, 32, 16, 24,  9, 15, 31, 38,  1, 37, 60,  2, 23, 44, 55,
       35, 61, 21, 17, 18, 14, 47,  6, 59, 26, 52, 50, 10, 45, 27, 29, 12,
       53, 51,  5, 13, 49, 62, 56, 42, 41, 64, 40, 19,  3, 58, 25]), 'cur_cost': 113680.0}, {'tour': array([ 7, 48, 18, 40,  5, 10, 20, 16,  1, 14, 28, 41, 61, 51, 60, 34, 43,
       44, 57, 17, 32, 24,  2, 22, 35, 31, 33, 47, 36,  8, 64, 38, 37, 13,
       12, 54, 21, 59,  9, 55, 25, 29,  0, 63, 26, 65, 15, 23,  4, 52, 46,
        6, 56, 30, 49, 39, 53, 58, 19, 62, 42, 11,  3, 50, 27, 45]), 'cur_cost': 109696.0}, {'tour': array([53,  7, 39, 21, 28,  1,  0, 35, 20, 49, 51, 54, 18, 47, 44, 11, 48,
       17, 37, 14, 24, 33, 34, 60,  8, 41, 29, 32, 61,  9, 55, 52, 40, 26,
       65, 30, 27,  3, 19, 25, 12, 50, 58, 36,  5,  6, 59, 57, 46, 23, 31,
       64, 45,  2, 63, 43, 62, 42, 22, 10, 56, 15, 38,  4, 13, 16]), 'cur_cost': 105786.0}]
2025-06-26 19:28:17,558 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:28:17,558 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 324, 'skip_rate': 0.040123456790123455, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 311, 'cache_hits': 200, 'similarity_calculations': 4412, 'cache_hit_rate': 0.04533091568449683, 'cache_size': 4212}}
2025-06-26 19:28:17,558 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:28:17,558 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 50, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 97515.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 39, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 97494.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 1, 13, 24, 35, 46, 57, 2, 14, 25, 36, 47, 58, 3, 15, 26, 37, 48, 59, 4, 16, 27, 38, 49, 60, 5, 17, 28, 39, 50, 61, 6, 18, 29, 40, 51, 62, 7, 19, 30, 41, 52, 63, 8, 20, 31, 42, 53, 64, 9, 21, 32, 43, 54, 65, 10, 22, 33, 44, 55, 11], 'cur_cost': 93216.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 19, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 30, 41, 52, 4, 16, 27, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64], 'cur_cost': 95956.0}}]
2025-06-26 19:28:17,558 - __main__ - INFO - 进化阶段完成
2025-06-26 19:28:17,558 - __main__ - INFO - 开始评估阶段
2025-06-26 19:28:17,558 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:28:17,561 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:28:17,561 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 11479.0 → 11479.0 (Δ 0.0)
- Mean cost: 75384.9 → 92253.4
- Diversity: 0.460 → 0.594 (Δ 0.1338)

#### Elite metrics
- Elite count: 22 → 24
- Best elite cost: 9521 → 9521 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 42.9% , avg Δ -23072.0
- Exploit: 3 inds, success 66.7% , avg Δ -2393.67

#### Other indicators
- No-change individuals: 2
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [21405.0, 15787.0, 11479.0], diversity [0.8422222222222223, 0.7057239057239058, 0.7713804713804714]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:28:17,561 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:28:39,660 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 42.9,
      "average_delta": -23072.0,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_delta": -2393.67,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "slightly imbalanced (favoring explore)",
    "diversity_trend": "increasing (healthy)",
    "stagnation_indicator": "no stagnation (best elite cost stable, diversity increasing)"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_intensity": "consider slight increase for exploit strategy"
  }
}
```
2025-06-26 19:28:39,694 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:28:39,695 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 42.9,
      "average_delta": -23072.0,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_delta": -2393.67,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "slightly imbalanced (favoring explore)",
    "diversity_trend": "increasing (healthy)",
    "stagnation_indicator": "no stagnation (best elite cost stable, diversity increasing)"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_intensity": "consider slight increase for exploit strategy"
  }
}
```
2025-06-26 19:28:39,695 - __main__ - INFO - 评估阶段完成
2025-06-26 19:28:39,695 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 42.9,
      "average_delta": -23072.0,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 66.7,
      "average_delta": -2393.67,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "slightly imbalanced (favoring explore)",
    "diversity_trend": "increasing (healthy)",
    "stagnation_indicator": "no stagnation (best elite cost stable, diversity increasing)"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_intensity": "consider slight increase for exploit strategy"
  }
}
```
2025-06-26 19:28:39,695 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-26 19:28:39,697 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_2.pkl
2025-06-26 19:28:39,697 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-06-26 19:28:39,697 - __main__ - INFO - 开始分析阶段
2025-06-26 19:28:39,697 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:28:39,714 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 113680.0, 'mean': 92253.4, 'std': 27618.745920841517}, 'diversity': 0.8508417508417508, 'clusters': {'clusters': 8, 'cluster_sizes': [2, 1, 2, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:28:39,715 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 113680.0, 'mean': 92253.4, 'std': 27618.745920841517}, 'diversity_level': 0.8508417508417508, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [2, 1, 2, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-26 19:28:39,715 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:28:39,716 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:28:39,716 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:28:39,718 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 19:28:39,719 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:28:39,719 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 19:28:39,719 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:28:39,719 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:28:39,720 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:28:39,729 - EliteExpert - INFO - 精英解分析完成
2025-06-26 19:28:39,729 - __main__ - INFO - 精英专家分析报告: {'elite_count': 24, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1958.0, 'avg_gap': 82727.06666666667}, 'structure_gap': {'unique_elite_edges': 184, 'unique_pop_edges': 383, 'common_edges': 26}}, 'elite_diversity': {'diversity_score': 0.20630215195432589}}
2025-06-26 19:28:39,729 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:28:39,729 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:28:39,731 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:28:39,731 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 11479.0, mean 92253.4, max 113680.0, std 27618.745920841517
- diversity: 0.8508417508417508
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [15787.0, 11479.0, 11479.0] (Δ 2154.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:28:39,733 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:29:06,133 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 10.0 to 3032.0, with significant std deviation in cost stats)",
    "modality": "Potentially multimodal (high diversity score 0.85, no clustering detected yet)",
    "deceptiveness": "Possible mild deceptiveness (recent best costs show improvement but stagnation not detected yet)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (density_grid shows nodes spread across multiple cells, though some empty cells exist)",
    "convergence": "Early stage (convergence metric 0.0, recent improvements in best costs)",
    "diversity": "High (0.85 diversity score)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Edges in Q3 (2469.0) to max (3032.0) range from edge_len_stats",
    "low_density_areas": "Top-right and center-left grid cells (0 counts in density_grid)"
  },
  "opportunity_regions": {
    "high_density_cells": "Bottom-left and bottom-right grid cells (14 counts in density_grid)",
    "short_edge_clusters": "Edges in min (10.0) to Q1 (1075.0) range from edge_len_stats"
  },
  "evolution_phase": "Early exploration (iteration 3/5, high diversity, no convergence)",
  "evolution_direction": [
    "Prioritize exploitation in high-density cells (bottom grid cells)",
    "Apply local optimization to short-edge clusters",
    "Use edge-recombination operators to preserve promising subpaths",
    "Maintain diversity via niching in low-density regions"
  ]
}
```
2025-06-26 19:29:06,142 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:29:06,142 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 10.0 to 3032.0, with significant std deviation in cost stats)', 'modality': 'Potentially multimodal (high diversity score 0.85, no clustering detected yet)', 'deceptiveness': 'Possible mild deceptiveness (recent best costs show improvement but stagnation not detected yet)'}, 'population_state': {'coverage': 'Good spatial coverage (density_grid shows nodes spread across multiple cells, though some empty cells exist)', 'convergence': 'Early stage (convergence metric 0.0, recent improvements in best costs)', 'diversity': 'High (0.85 diversity score)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in Q3 (2469.0) to max (3032.0) range from edge_len_stats', 'low_density_areas': 'Top-right and center-left grid cells (0 counts in density_grid)'}, 'opportunity_regions': {'high_density_cells': 'Bottom-left and bottom-right grid cells (14 counts in density_grid)', 'short_edge_clusters': 'Edges in min (10.0) to Q1 (1075.0) range from edge_len_stats'}, 'evolution_phase': 'Early exploration (iteration 3/5, high diversity, no convergence)', 'evolution_direction': ['Prioritize exploitation in high-density cells (bottom grid cells)', 'Apply local optimization to short-edge clusters', 'Use edge-recombination operators to preserve promising subpaths', 'Maintain diversity via niching in low-density regions']}
2025-06-26 19:29:06,142 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:29:06,142 - __main__ - INFO - 分析阶段完成
2025-06-26 19:29:06,142 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 10.0 to 3032.0, with significant std deviation in cost stats)', 'modality': 'Potentially multimodal (high diversity score 0.85, no clustering detected yet)', 'deceptiveness': 'Possible mild deceptiveness (recent best costs show improvement but stagnation not detected yet)'}, 'population_state': {'coverage': 'Good spatial coverage (density_grid shows nodes spread across multiple cells, though some empty cells exist)', 'convergence': 'Early stage (convergence metric 0.0, recent improvements in best costs)', 'diversity': 'High (0.85 diversity score)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in Q3 (2469.0) to max (3032.0) range from edge_len_stats', 'low_density_areas': 'Top-right and center-left grid cells (0 counts in density_grid)'}, 'opportunity_regions': {'high_density_cells': 'Bottom-left and bottom-right grid cells (14 counts in density_grid)', 'short_edge_clusters': 'Edges in min (10.0) to Q1 (1075.0) range from edge_len_stats'}, 'evolution_phase': 'Early exploration (iteration 3/5, high diversity, no convergence)', 'evolution_direction': ['Prioritize exploitation in high-density cells (bottom grid cells)', 'Apply local optimization to short-edge clusters', 'Use edge-recombination operators to preserve promising subpaths', 'Maintain diversity via niching in low-density regions']}
2025-06-26 19:29:06,142 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:29:06,142 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:29:06,142 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 11479.0
  • mean_cost: 92253.4
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 10.0 to 3032.0, with significant std deviation in cost stats), modality Potentially multimodal (high diversity score 0.85, no clustering detected yet), deceptiveness Possible mild deceptiveness (recent best costs show improvement but stagnation not detected yet)
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_areas
- Opportunity regions (# 2): high_density_cells, short_edge_clusters
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 42.9,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:29:06,142 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:29:06,142 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:29:16,974 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity and no convergence; balance focus suggests 70% explore to target opportunity regions and difficult areas."
}
```
2025-06-26 19:29:16,974 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:29:16,974 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:29:16,974 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity and no convergence; balance focus suggests 70% explore to target opportunity regions and difficult areas."
}
```
2025-06-26 19:29:16,974 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:29:16,974 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:29:16,974 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity and no convergence; balance focus suggests 70% explore to target opportunity regions and difficult areas."
}
```
2025-06-26 19:29:16,974 - __main__ - INFO - 开始进化阶段
2025-06-26 19:29:16,974 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:29:16,974 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:29:16,974 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 97515.0 (rank 40.0%)
- Population diversity: 0.5938461538461538
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:29:16,974 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:29:16,974 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:29:32,873 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Leverage grid-like traversal for diversity"
}
```
2025-06-26 19:29:32,873 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:29:32,873 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:29:32,873 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:29:32,873 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:29:32,873 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:29:32,873 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6138461538461538
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:29:32,873 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:29:32,873 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:29:49,547 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Sequential path to explore sparse cells near opportunity regions"
}
```
2025-06-26 19:29:49,547 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:29:49,547 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-26 19:29:49,547 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-26 19:29:49,547 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:29:49,547 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:29:49,547 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 97494.0 (rank 30.0%)
- Population diversity: 0.6138461538461538
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:29:49,547 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:29:49,547 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:30:10,604 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters with short edges, reverse partial segments"
}
```
2025-06-26 19:30:10,604 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:30:10,604 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13945.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-26 19:30:10,604 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}
2025-06-26 19:30:10,604 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:30:10,604 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:30:10,604 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 40.0%)
- Population diversity: 0.5876923076923077
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:30:10,604 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:30:10,604 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:30:33,588 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Sequential traversal prioritizing dense clusters"
}
```
2025-06-26 19:30:33,592 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:30:33,592 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-26 19:30:33,592 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-26 19:30:33,592 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:30:33,592 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:30:33,592 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 93216.0 (rank 30.0%)
- Population diversity: 0.5876923076923077
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:30:33,592 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:30:33,595 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:30:50,947 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 66, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Leverage clustered nodes, avoid long edges"
}
```
2025-06-26 19:30:50,947 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:30:50,947 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95290.0, 路径: [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 30, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1]
2025-06-26 19:30:50,947 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 30, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 95290.0}
2025-06-26 19:30:50,947 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:30:50,947 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:30:50,947 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 50.0%)
- Population diversity: 0.5984615384615385
- Evolution phase: Early exploration (iteration 3/5, high diversity, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_are
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:30:50,947 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:30:50,947 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:31:10,551 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_ce, adjacent low-density cells",
  "strategy_comment": "Layered spiral through opportunity regions, avoiding long edges"
}
```
2025-06-26 19:31:10,551 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:31:10,551 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 19:31:10,553 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 19:31:10,553 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 19:31:10,553 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:31:10,553 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:31:10,553 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 91727.0
2025-06-26 19:31:11,054 - ExploitationExpert - INFO - res_population_num: 28
2025-06-26 19:31:11,055 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521, 9521, 9521, 9521]
2025-06-26 19:31:11,055 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-26 19:31:11,067 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:31:11,067 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 30, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 95290.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([50, 16,  5,  8, 12, 40, 17, 30, 25, 46, 53,  0,  1, 22, 60, 11, 65,
       55, 42, 26, 24, 64,  6, 15,  7, 52, 63, 59, 18, 49, 28, 43, 56, 34,
       13,  3, 35,  2, 33, 19, 62, 41, 48, 14, 38, 37,  9,  4, 36, 44, 21,
       23, 61, 29, 31, 27, 58, 10, 32, 54, 57, 47, 51, 20, 45, 39]), 'cur_cost': 91727.0}, {'tour': array([30, 22, 63, 39, 43, 65,  8, 33,  0, 20, 57, 54, 48, 28,  7,  4, 46,
       34, 11, 36, 32, 16, 24,  9, 15, 31, 38,  1, 37, 60,  2, 23, 44, 55,
       35, 61, 21, 17, 18, 14, 47,  6, 59, 26, 52, 50, 10, 45, 27, 29, 12,
       53, 51,  5, 13, 49, 62, 56, 42, 41, 64, 40, 19,  3, 58, 25]), 'cur_cost': 113680.0}, {'tour': array([ 7, 48, 18, 40,  5, 10, 20, 16,  1, 14, 28, 41, 61, 51, 60, 34, 43,
       44, 57, 17, 32, 24,  2, 22, 35, 31, 33, 47, 36,  8, 64, 38, 37, 13,
       12, 54, 21, 59,  9, 55, 25, 29,  0, 63, 26, 65, 15, 23,  4, 52, 46,
        6, 56, 30, 49, 39, 53, 58, 19, 62, 42, 11,  3, 50, 27, 45]), 'cur_cost': 109696.0}, {'tour': array([53,  7, 39, 21, 28,  1,  0, 35, 20, 49, 51, 54, 18, 47, 44, 11, 48,
       17, 37, 14, 24, 33, 34, 60,  8, 41, 29, 32, 61,  9, 55, 52, 40, 26,
       65, 30, 27,  3, 19, 25, 12, 50, 58, 36,  5,  6, 59, 57, 46, 23, 31,
       64, 45,  2, 63, 43, 62, 42, 22, 10, 56, 15, 38,  4, 13, 16]), 'cur_cost': 105786.0}]
2025-06-26 19:31:11,070 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:31:11,070 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 325, 'skip_rate': 0.04, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 312, 'cache_hits': 200, 'similarity_calculations': 4419, 'cache_hit_rate': 0.04525910839556461, 'cache_size': 4219}}
2025-06-26 19:31:11,070 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 19:31:11,071 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:31:11,071 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:31:11,071 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:31:11,071 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 104726.0
2025-06-26 19:31:11,575 - ExploitationExpert - INFO - res_population_num: 28
2025-06-26 19:31:11,576 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521, 9521, 9521, 9521]
2025-06-26 19:31:11,576 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-26 19:31:11,589 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:31:11,589 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 30, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 95290.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([50, 16,  5,  8, 12, 40, 17, 30, 25, 46, 53,  0,  1, 22, 60, 11, 65,
       55, 42, 26, 24, 64,  6, 15,  7, 52, 63, 59, 18, 49, 28, 43, 56, 34,
       13,  3, 35,  2, 33, 19, 62, 41, 48, 14, 38, 37,  9,  4, 36, 44, 21,
       23, 61, 29, 31, 27, 58, 10, 32, 54, 57, 47, 51, 20, 45, 39]), 'cur_cost': 91727.0}, {'tour': array([59, 44,  7,  3, 12, 13, 15, 33, 55, 52, 65, 21,  6, 49, 25, 19, 57,
       17,  0, 42, 28, 58,  2, 24, 36, 38, 41, 29, 64, 20, 45, 60, 37, 61,
       14, 10, 16, 22, 34, 40, 50, 32, 26, 46, 11, 47, 43,  5, 53, 56, 27,
       48, 63, 23, 62,  8,  4, 39, 31, 35, 54,  9,  1, 18, 30, 51]), 'cur_cost': 104726.0}, {'tour': array([ 7, 48, 18, 40,  5, 10, 20, 16,  1, 14, 28, 41, 61, 51, 60, 34, 43,
       44, 57, 17, 32, 24,  2, 22, 35, 31, 33, 47, 36,  8, 64, 38, 37, 13,
       12, 54, 21, 59,  9, 55, 25, 29,  0, 63, 26, 65, 15, 23,  4, 52, 46,
        6, 56, 30, 49, 39, 53, 58, 19, 62, 42, 11,  3, 50, 27, 45]), 'cur_cost': 109696.0}, {'tour': array([53,  7, 39, 21, 28,  1,  0, 35, 20, 49, 51, 54, 18, 47, 44, 11, 48,
       17, 37, 14, 24, 33, 34, 60,  8, 41, 29, 32, 61,  9, 55, 52, 40, 26,
       65, 30, 27,  3, 19, 25, 12, 50, 58, 36,  5,  6, 59, 57, 46, 23, 31,
       64, 45,  2, 63, 43, 62, 42, 22, 10, 56, 15, 38,  4, 13, 16]), 'cur_cost': 105786.0}]
2025-06-26 19:31:11,593 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:31:11,593 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 326, 'skip_rate': 0.03987730061349693, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 313, 'cache_hits': 200, 'similarity_calculations': 4427, 'cache_hit_rate': 0.0451773209848656, 'cache_size': 4227}}
2025-06-26 19:31:11,594 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:31:11,594 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:31:11,594 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:31:11,594 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:31:11,595 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 101672.0
2025-06-26 19:31:12,098 - ExploitationExpert - INFO - res_population_num: 29
2025-06-26 19:31:12,099 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521, 9521, 9521, 9521, 9521]
2025-06-26 19:31:12,099 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-26 19:31:12,112 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:31:12,112 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 30, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 95290.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([50, 16,  5,  8, 12, 40, 17, 30, 25, 46, 53,  0,  1, 22, 60, 11, 65,
       55, 42, 26, 24, 64,  6, 15,  7, 52, 63, 59, 18, 49, 28, 43, 56, 34,
       13,  3, 35,  2, 33, 19, 62, 41, 48, 14, 38, 37,  9,  4, 36, 44, 21,
       23, 61, 29, 31, 27, 58, 10, 32, 54, 57, 47, 51, 20, 45, 39]), 'cur_cost': 91727.0}, {'tour': array([59, 44,  7,  3, 12, 13, 15, 33, 55, 52, 65, 21,  6, 49, 25, 19, 57,
       17,  0, 42, 28, 58,  2, 24, 36, 38, 41, 29, 64, 20, 45, 60, 37, 61,
       14, 10, 16, 22, 34, 40, 50, 32, 26, 46, 11, 47, 43,  5, 53, 56, 27,
       48, 63, 23, 62,  8,  4, 39, 31, 35, 54,  9,  1, 18, 30, 51]), 'cur_cost': 104726.0}, {'tour': array([ 4, 38,  9, 58, 11, 63,  1, 26, 54, 21, 28, 34, 39, 46, 62, 17, 20,
       27, 15, 35, 24, 14, 48, 31, 56, 22, 50, 53, 61, 29, 65,  0, 52, 18,
       33, 25, 41, 64, 37, 16,  6,  8,  2, 57, 49, 43, 60,  7, 55,  5, 42,
       32, 51, 10, 44, 45, 12,  3, 47, 23, 36, 30, 13, 40, 59, 19]), 'cur_cost': 101672.0}, {'tour': array([53,  7, 39, 21, 28,  1,  0, 35, 20, 49, 51, 54, 18, 47, 44, 11, 48,
       17, 37, 14, 24, 33, 34, 60,  8, 41, 29, 32, 61,  9, 55, 52, 40, 26,
       65, 30, 27,  3, 19, 25, 12, 50, 58, 36,  5,  6, 59, 57, 46, 23, 31,
       64, 45,  2, 63, 43, 62, 42, 22, 10, 56, 15, 38,  4, 13, 16]), 'cur_cost': 105786.0}]
2025-06-26 19:31:12,114 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:31:12,116 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 327, 'skip_rate': 0.039755351681957186, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 314, 'cache_hits': 200, 'similarity_calculations': 4436, 'cache_hit_rate': 0.04508566275924256, 'cache_size': 4236}}
2025-06-26 19:31:12,116 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:31:12,116 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:31:12,116 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:31:12,117 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:31:12,117 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 103992.0
2025-06-26 19:31:12,621 - ExploitationExpert - INFO - res_population_num: 29
2025-06-26 19:31:12,621 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521, 9521, 9521, 9521, 9521]
2025-06-26 19:31:12,621 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-26 19:31:12,635 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:31:12,635 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 30, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 95290.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([50, 16,  5,  8, 12, 40, 17, 30, 25, 46, 53,  0,  1, 22, 60, 11, 65,
       55, 42, 26, 24, 64,  6, 15,  7, 52, 63, 59, 18, 49, 28, 43, 56, 34,
       13,  3, 35,  2, 33, 19, 62, 41, 48, 14, 38, 37,  9,  4, 36, 44, 21,
       23, 61, 29, 31, 27, 58, 10, 32, 54, 57, 47, 51, 20, 45, 39]), 'cur_cost': 91727.0}, {'tour': array([59, 44,  7,  3, 12, 13, 15, 33, 55, 52, 65, 21,  6, 49, 25, 19, 57,
       17,  0, 42, 28, 58,  2, 24, 36, 38, 41, 29, 64, 20, 45, 60, 37, 61,
       14, 10, 16, 22, 34, 40, 50, 32, 26, 46, 11, 47, 43,  5, 53, 56, 27,
       48, 63, 23, 62,  8,  4, 39, 31, 35, 54,  9,  1, 18, 30, 51]), 'cur_cost': 104726.0}, {'tour': array([ 4, 38,  9, 58, 11, 63,  1, 26, 54, 21, 28, 34, 39, 46, 62, 17, 20,
       27, 15, 35, 24, 14, 48, 31, 56, 22, 50, 53, 61, 29, 65,  0, 52, 18,
       33, 25, 41, 64, 37, 16,  6,  8,  2, 57, 49, 43, 60,  7, 55,  5, 42,
       32, 51, 10, 44, 45, 12,  3, 47, 23, 36, 30, 13, 40, 59, 19]), 'cur_cost': 101672.0}, {'tour': array([29, 31,  3, 61, 62, 10, 34, 49, 18, 39, 21, 54, 64, 45, 12, 52,  8,
       36, 59, 46, 40, 26,  2, 41,  9, 43, 20, 16, 24, 44, 60,  6, 50,  0,
       14, 15, 30, 47,  4,  7, 51, 33, 63, 25, 23, 53, 56, 35, 37, 17, 22,
       48, 42, 55,  5, 27, 13, 19, 58,  1, 57, 32, 28, 65, 11, 38]), 'cur_cost': 103992.0}]
2025-06-26 19:31:12,638 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:31:12,638 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 328, 'skip_rate': 0.039634146341463415, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 315, 'cache_hits': 200, 'similarity_calculations': 4446, 'cache_hit_rate': 0.0449842555105713, 'cache_size': 4246}}
2025-06-26 19:31:12,639 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:31:12,639 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 30, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 95290.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}]
2025-06-26 19:31:12,639 - __main__ - INFO - 进化阶段完成
2025-06-26 19:31:12,640 - __main__ - INFO - 开始评估阶段
2025-06-26 19:31:12,640 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:31:12,640 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:31:12,640 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 11479.0 → 11479.0 (Δ 0.0)
- Mean cost: 92253.4 → 73457.1
- Diversity: 0.594 → 0.668 (Δ 0.0738)

#### Elite metrics
- Elite count: 24 → 29
- Best elite cost: 9521 → 9521 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 33.3% , avg Δ 27493.67
- Exploit: 4 inds, success 100.0% , avg Δ 5750.25

#### Other indicators
- No-change individuals: 2
- Regression individuals: 2
- Historical trends: Last 3 iterations: costs [15787.0, 11479.0, 11479.0], diversity [0.7057239057239058, 0.7713804713804714, 0.8508417508417508]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:31:12,642 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:31:35,140 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_improvement": 27493.67,
      "contribution_to_elite": "moderate (potential for high-impact improvements but inconsistent)"
    },
    "exploit": {
      "success_rate": 100.0,
      "average_improvement": 5750.25,
      "contribution_to_elite": "consistent (reliable but incremental gains)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "slightly explore-heavy given exploit's higher success rate",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.75,
    "additional_notes": "Consider adding a small 'no-change' penalty (e.g., -0.1 fitness) to discourage stagnation"
  }
}
```
2025-06-26 19:31:35,173 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:31:35,183 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_improvement": 27493.67,
      "contribution_to_elite": "moderate (potential for high-impact improvements but inconsistent)"
    },
    "exploit": {
      "success_rate": 100.0,
      "average_improvement": 5750.25,
      "contribution_to_elite": "consistent (reliable but incremental gains)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "slightly explore-heavy given exploit's higher success rate",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.75,
    "additional_notes": "Consider adding a small 'no-change' penalty (e.g., -0.1 fitness) to discourage stagnation"
  }
}
```
2025-06-26 19:31:35,183 - __main__ - INFO - 评估阶段完成
2025-06-26 19:31:35,183 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_improvement": 27493.67,
      "contribution_to_elite": "moderate (potential for high-impact improvements but inconsistent)"
    },
    "exploit": {
      "success_rate": 100.0,
      "average_improvement": 5750.25,
      "contribution_to_elite": "consistent (reliable but incremental gains)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "assessment": "slightly explore-heavy given exploit's higher success rate",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.5,
    "diversity_weight": 0.75,
    "additional_notes": "Consider adding a small 'no-change' penalty (e.g., -0.1 fitness) to discourage stagnation"
  }
}
```
2025-06-26 19:31:35,183 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-26 19:31:35,184 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_3.pkl
2025-06-26 19:31:35,184 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-06-26 19:31:35,184 - __main__ - INFO - 开始分析阶段
2025-06-26 19:31:35,186 - StatsExpert - INFO - 开始统计分析
2025-06-26 19:31:35,203 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 104726.0, 'mean': 73457.1, 'std': 40208.35562031852}, 'diversity': 0.8919191919191919, 'clusters': {'clusters': 7, 'cluster_sizes': [2, 3, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 19:31:35,204 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 104726.0, 'mean': 73457.1, 'std': 40208.35562031852}, 'diversity_level': 0.8919191919191919, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [2, 3, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-26 19:31:35,204 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 19:31:35,205 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 19:31:35,205 - PathExpert - INFO - 开始路径结构分析
2025-06-26 19:31:35,207 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 19:31:35,208 - PathExpert - INFO - 路径结构分析完成
2025-06-26 19:31:35,208 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 19:31:35,208 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 19:31:35,208 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 19:31:35,208 - EliteExpert - INFO - 开始精英解分析
2025-06-26 19:31:35,217 - EliteExpert - INFO - 精英解分析完成
2025-06-26 19:31:35,217 - __main__ - INFO - 精英专家分析报告: {'elite_count': 29, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1958.0, 'avg_gap': 63931.68620689656}, 'structure_gap': {'unique_elite_edges': 177, 'unique_pop_edges': 430, 'common_edges': 33}}, 'elite_diversity': {'diversity_score': 0.20338856545753092}}
2025-06-26 19:31:35,223 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 19:31:35,224 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 19:31:35,226 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 19:31:35,226 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 11479.0, mean 73457.1, max 104726.0, std 40208.35562031852
- diversity: 0.8919191919191919
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [11479.0, 11479.0, 11479.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 19:31:35,227 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:32:04,087 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 10.0 to 3032.0, with significant std deviation)",
    "modality": "Potentially multimodal (diverse direction histogram and edge length distribution)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.249 suggests some misleading long edges)"
  },
  "population_state": {
    "coverage": "Patchy (uneven density_grid with empty cells adjacent to high-density cells)",
    "convergence": "Early-stage (diversity=0.89, convergence=0.0, recent stagnation detected)",
    "elite_influence": "Minimal (elite_count=0, no common edges or features)"
  },
  "difficult_regions": {
    "spatial": [
      "Low-density cells at grid positions (0,1), (0,2), (1,0), (2,1)",
      "Long-edge corridors (q3 edge length=2469.0, max=3032.0)"
    ],
    "structural": "No explicit difficult regions identified in structural signals"
  },
  "opportunity_regions": {
    "spatial": [
      "High-density cells at grid positions (0,0), (2,0), (2,2) (14 nodes each)",
      "Centroid region (1795.652, 1565.212) with moderate spread"
    ],
    "structural": "No explicit opportunity regions identified in structural signals"
  },
  "evolution_phase": "Early exploration (high diversity, no convergence, population still discovering structure)",
  "evolution_direction": [
    "Intensify local search in high-density opportunity regions",
    "Add targeted mutation to break stagnation in current best solutions",
    "Consider edge-recombination operators that favor medium-length edges (med=1900.0)",
    "Implement diversity preservation for low-density grid cells"
  ]
}
```
2025-06-26 19:32:04,087 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 19:32:04,087 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 3032.0, with significant std deviation)', 'modality': 'Potentially multimodal (diverse direction histogram and edge length distribution)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.249 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Patchy (uneven density_grid with empty cells adjacent to high-density cells)', 'convergence': 'Early-stage (diversity=0.89, convergence=0.0, recent stagnation detected)', 'elite_influence': 'Minimal (elite_count=0, no common edges or features)'}, 'difficult_regions': {'spatial': ['Low-density cells at grid positions (0,1), (0,2), (1,0), (2,1)', 'Long-edge corridors (q3 edge length=2469.0, max=3032.0)'], 'structural': 'No explicit difficult regions identified in structural signals'}, 'opportunity_regions': {'spatial': ['High-density cells at grid positions (0,0), (2,0), (2,2) (14 nodes each)', 'Centroid region (1795.652, 1565.212) with moderate spread'], 'structural': 'No explicit opportunity regions identified in structural signals'}, 'evolution_phase': 'Early exploration (high diversity, no convergence, population still discovering structure)', 'evolution_direction': ['Intensify local search in high-density opportunity regions', 'Add targeted mutation to break stagnation in current best solutions', 'Consider edge-recombination operators that favor medium-length edges (med=1900.0)', 'Implement diversity preservation for low-density grid cells']}
2025-06-26 19:32:04,087 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 19:32:04,087 - __main__ - INFO - 分析阶段完成
2025-06-26 19:32:04,087 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 3032.0, with significant std deviation)', 'modality': 'Potentially multimodal (diverse direction histogram and edge length distribution)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.249 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Patchy (uneven density_grid with empty cells adjacent to high-density cells)', 'convergence': 'Early-stage (diversity=0.89, convergence=0.0, recent stagnation detected)', 'elite_influence': 'Minimal (elite_count=0, no common edges or features)'}, 'difficult_regions': {'spatial': ['Low-density cells at grid positions (0,1), (0,2), (1,0), (2,1)', 'Long-edge corridors (q3 edge length=2469.0, max=3032.0)'], 'structural': 'No explicit difficult regions identified in structural signals'}, 'opportunity_regions': {'spatial': ['High-density cells at grid positions (0,0), (2,0), (2,2) (14 nodes each)', 'Centroid region (1795.652, 1565.212) with moderate spread'], 'structural': 'No explicit opportunity regions identified in structural signals'}, 'evolution_phase': 'Early exploration (high diversity, no convergence, population still discovering structure)', 'evolution_direction': ['Intensify local search in high-density opportunity regions', 'Add targeted mutation to break stagnation in current best solutions', 'Consider edge-recombination operators that favor medium-length edges (med=1900.0)', 'Implement diversity preservation for low-density grid cells']}
2025-06-26 19:32:04,087 - __main__ - INFO - 开始策略分配阶段
2025-06-26 19:32:04,087 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 19:32:04,091 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 11479.0
  • mean_cost: 73457.1
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 10.0 to 3032.0, with significant std deviation), modality Potentially multimodal (diverse direction histogram and edge length distribution), deceptiveness Possible deceptive regions (long_edge_ratio of 0.249 suggests some misleading long edges)
- Evolution phase: Early exploration (high diversity, no convergence, population still discovering structure)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): spatial, structural
- Opportunity regions (# 2): spatial, structural
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 19:32:04,091 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 19:32:04,091 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:32:19,881 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity and no convergence suggests prioritizing exploration. Top 60% explore to discover structure, bottom 40% exploit to refine."
}
```
2025-06-26 19:32:19,881 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:32:19,881 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:32:19,881 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity and no convergence suggests prioritizing exploration. Top 60% explore to discover structure, bottom 40% exploit to refine."
}
```
2025-06-26 19:32:19,881 - __main__ - INFO - 策略分配阶段完成
2025-06-26 19:32:19,881 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 19:32:19,881 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Early exploration phase with high diversity and no convergence suggests prioritizing exploration. Top 60% explore to discover structure, bottom 40% exploit to refine."
}
```
2025-06-26 19:32:19,881 - __main__ - INFO - 开始进化阶段
2025-06-26 19:32:19,881 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 19:32:19,886 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:32:19,886 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 60.0%)
- Population diversity: 0.6676923076923077
- Evolution phase: Early exploration (high diversity, no convergence, population still discovering structure)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial, structural
- Difficult regions to avoid (sample): spatial, structural
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:32:19,886 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:32:19,886 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:32:37,132 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1, 58, 59, 60, 61, 62, 63, 64, 57],
  "targeted_regions": "spatial clusters, low-density adjacents",
  "strategy_comment": "Alternate spatial jumps with local clusters to explore diversity"
}
```
2025-06-26 19:32:37,132 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:32:37,132 - ExplorationExpert - INFO - 探索路径生成完成，成本: 87538.0, 路径: [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1, 58, 59, 60, 61, 62, 63, 64, 57]
2025-06-26 19:32:37,132 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1, 58, 59, 60, 61, 62, 63, 64, 57], 'cur_cost': 87538.0}
2025-06-26 19:32:37,132 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 19:32:37,132 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:32:37,132 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6646153846153846
- Evolution phase: Early exploration (high diversity, no convergence, population still discovering structure)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial, structural
- Difficult regions to avoid (sample): spatial, structural
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:32:37,132 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:32:37,132 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:32:53,331 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "Sparse cells in modular progression",
  "strategy_comment": "Modular skip pattern to explore low-density regions"
}
```
2025-06-26 19:32:53,331 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:32:53,331 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 19:32:53,331 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 19:32:53,331 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 19:32:53,331 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:32:53,331 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13945.0 (rank 10.0%)
- Population diversity: 0.6646153846153846
- Evolution phase: Early exploration (high diversity, no convergence, population still discovering structure)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial, structural
- Difficult regions to avoid (sample): spatial, structural
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:32:53,331 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:32:53,331 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:33:14,613 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "spatial clusters with modular jumps",
  "strategy_comment": "Balanced modular traversal to explore sparse regions"
}
```
2025-06-26 19:33:14,620 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:33:14,620 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:33:14,620 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:33:14,620 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 19:33:14,620 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:33:14,620 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 11479.0 (rank 0.0%)
- Population diversity: 0.6861538461538461
- Evolution phase: Early exploration (high diversity, no convergence, population still discovering structure)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial, structural
- Difficult regions to avoid (sample): spatial, structural
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:33:14,623 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:33:14,623 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:33:30,886 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "spatial clusters, low-density adjacency",
  "strategy_comment": "Modular spatial hops with offset sequencing"
}
```
2025-06-26 19:33:30,886 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:33:30,886 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 19:33:30,895 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 19:33:30,895 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 19:33:30,895 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:33:30,895 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 95290.0 (rank 20.0%)
- Population diversity: 0.6046153846153847
- Evolution phase: Early exploration (high diversity, no convergence, population still discovering structure)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial, structural
- Difficult regions to avoid (sample): spatial, structural
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:33:30,895 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:33:30,895 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:33:51,041 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "spatial clusters, low-density adjacents",
  "strategy_comment": "Grid-like traversal to explore sparse regions systematically"
}
```
2025-06-26 19:33:51,045 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:33:51,045 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 19:33:51,045 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 19:33:51,045 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 19:33:51,045 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 19:33:51,045 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 20.0%)
- Population diversity: 0.5753846153846154
- Evolution phase: Early exploration (high diversity, no convergence, population still discovering structure)
- Landscape recommends focus: balance
- Opportunity regions (sample): spatial, structural
- Difficult regions to avoid (sample): spatial, structural
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 19:33:51,045 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 19:33:51,045 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:34:06,531 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "spatial clusters, low-density adjacency",
  "strategy_comment": "Interleaved spatial bands to explore sparse regions"
}
```
2025-06-26 19:34:06,531 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 19:34:06,531 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 19:34:06,531 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 19:34:06,531 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 19:34:06,531 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:34:06,531 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:34:06,531 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 110675.0
2025-06-26 19:34:07,033 - ExploitationExpert - INFO - res_population_num: 29
2025-06-26 19:34:07,033 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567]
2025-06-26 19:34:07,033 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64)]
2025-06-26 19:34:07,046 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:34:07,046 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1, 58, 59, 60, 61, 62, 63, 64, 57], 'cur_cost': 87538.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([65, 12, 18, 54, 45, 64, 34, 10, 24,  3, 15, 51,  7, 36,  1, 62, 49,
       32, 57,  5, 17, 37, 35, 58, 44, 23,  8,  9, 26, 39, 14, 33, 43, 40,
       22, 53, 59, 20, 21, 46, 60, 48, 27, 41, 11,  2, 61, 55,  4, 38, 30,
       16, 13, 29,  6, 19, 52, 47, 25, 56, 50, 42, 28,  0, 31, 63]), 'cur_cost': 110675.0}, {'tour': array([59, 44,  7,  3, 12, 13, 15, 33, 55, 52, 65, 21,  6, 49, 25, 19, 57,
       17,  0, 42, 28, 58,  2, 24, 36, 38, 41, 29, 64, 20, 45, 60, 37, 61,
       14, 10, 16, 22, 34, 40, 50, 32, 26, 46, 11, 47, 43,  5, 53, 56, 27,
       48, 63, 23, 62,  8,  4, 39, 31, 35, 54,  9,  1, 18, 30, 51]), 'cur_cost': 104726.0}, {'tour': array([ 4, 38,  9, 58, 11, 63,  1, 26, 54, 21, 28, 34, 39, 46, 62, 17, 20,
       27, 15, 35, 24, 14, 48, 31, 56, 22, 50, 53, 61, 29, 65,  0, 52, 18,
       33, 25, 41, 64, 37, 16,  6,  8,  2, 57, 49, 43, 60,  7, 55,  5, 42,
       32, 51, 10, 44, 45, 12,  3, 47, 23, 36, 30, 13, 40, 59, 19]), 'cur_cost': 101672.0}, {'tour': array([29, 31,  3, 61, 62, 10, 34, 49, 18, 39, 21, 54, 64, 45, 12, 52,  8,
       36, 59, 46, 40, 26,  2, 41,  9, 43, 20, 16, 24, 44, 60,  6, 50,  0,
       14, 15, 30, 47,  4,  7, 51, 33, 63, 25, 23, 53, 56, 35, 37, 17, 22,
       48, 42, 55,  5, 27, 13, 19, 58,  1, 57, 32, 28, 65, 11, 38]), 'cur_cost': 103992.0}]
2025-06-26 19:34:07,049 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:34:07,049 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 329, 'skip_rate': 0.03951367781155015, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 316, 'cache_hits': 200, 'similarity_calculations': 4457, 'cache_hit_rate': 0.04487323311644604, 'cache_size': 4257}}
2025-06-26 19:34:07,049 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 19:34:07,049 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 19:34:07,051 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:34:07,051 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:34:07,051 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 105446.0
2025-06-26 19:34:07,555 - ExploitationExpert - INFO - res_population_num: 30
2025-06-26 19:34:07,556 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521]
2025-06-26 19:34:07,556 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-26 19:34:07,572 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:34:07,572 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1, 58, 59, 60, 61, 62, 63, 64, 57], 'cur_cost': 87538.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([65, 12, 18, 54, 45, 64, 34, 10, 24,  3, 15, 51,  7, 36,  1, 62, 49,
       32, 57,  5, 17, 37, 35, 58, 44, 23,  8,  9, 26, 39, 14, 33, 43, 40,
       22, 53, 59, 20, 21, 46, 60, 48, 27, 41, 11,  2, 61, 55,  4, 38, 30,
       16, 13, 29,  6, 19, 52, 47, 25, 56, 50, 42, 28,  0, 31, 63]), 'cur_cost': 110675.0}, {'tour': array([31, 63, 56, 33,  5,  0, 40, 11, 54, 46, 36, 41, 29, 43, 23, 57, 10,
       55, 48, 61, 58, 34, 47, 51, 25, 39, 32,  9, 16, 38,  7, 20,  8, 35,
       12, 27, 45, 28,  3,  6,  4, 21, 14, 19, 18, 62, 13, 15, 22, 26, 24,
       64, 50, 49, 37, 59, 42, 60,  1, 44, 52, 17,  2, 65, 53, 30]), 'cur_cost': 105446.0}, {'tour': array([ 4, 38,  9, 58, 11, 63,  1, 26, 54, 21, 28, 34, 39, 46, 62, 17, 20,
       27, 15, 35, 24, 14, 48, 31, 56, 22, 50, 53, 61, 29, 65,  0, 52, 18,
       33, 25, 41, 64, 37, 16,  6,  8,  2, 57, 49, 43, 60,  7, 55,  5, 42,
       32, 51, 10, 44, 45, 12,  3, 47, 23, 36, 30, 13, 40, 59, 19]), 'cur_cost': 101672.0}, {'tour': array([29, 31,  3, 61, 62, 10, 34, 49, 18, 39, 21, 54, 64, 45, 12, 52,  8,
       36, 59, 46, 40, 26,  2, 41,  9, 43, 20, 16, 24, 44, 60,  6, 50,  0,
       14, 15, 30, 47,  4,  7, 51, 33, 63, 25, 23, 53, 56, 35, 37, 17, 22,
       48, 42, 55,  5, 27, 13, 19, 58,  1, 57, 32, 28, 65, 11, 38]), 'cur_cost': 103992.0}]
2025-06-26 19:34:07,575 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:34:07,575 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 330, 'skip_rate': 0.03939393939393939, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 317, 'cache_hits': 200, 'similarity_calculations': 4469, 'cache_hit_rate': 0.04475274110539271, 'cache_size': 4269}}
2025-06-26 19:34:07,575 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 19:34:07,575 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 19:34:07,575 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:34:07,575 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:34:07,575 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 113137.0
2025-06-26 19:34:08,081 - ExploitationExpert - INFO - res_population_num: 30
2025-06-26 19:34:08,081 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521]
2025-06-26 19:34:08,082 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-26 19:34:08,096 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:34:08,096 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1, 58, 59, 60, 61, 62, 63, 64, 57], 'cur_cost': 87538.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([65, 12, 18, 54, 45, 64, 34, 10, 24,  3, 15, 51,  7, 36,  1, 62, 49,
       32, 57,  5, 17, 37, 35, 58, 44, 23,  8,  9, 26, 39, 14, 33, 43, 40,
       22, 53, 59, 20, 21, 46, 60, 48, 27, 41, 11,  2, 61, 55,  4, 38, 30,
       16, 13, 29,  6, 19, 52, 47, 25, 56, 50, 42, 28,  0, 31, 63]), 'cur_cost': 110675.0}, {'tour': array([31, 63, 56, 33,  5,  0, 40, 11, 54, 46, 36, 41, 29, 43, 23, 57, 10,
       55, 48, 61, 58, 34, 47, 51, 25, 39, 32,  9, 16, 38,  7, 20,  8, 35,
       12, 27, 45, 28,  3,  6,  4, 21, 14, 19, 18, 62, 13, 15, 22, 26, 24,
       64, 50, 49, 37, 59, 42, 60,  1, 44, 52, 17,  2, 65, 53, 30]), 'cur_cost': 105446.0}, {'tour': array([34, 38, 52, 41, 15, 55, 26, 29, 13, 11, 46,  3, 30, 39, 19, 37, 59,
       64, 65, 45, 23,  8, 48, 40,  2, 56, 24,  7, 42, 60, 20, 25, 14, 49,
        6, 58, 12, 62, 33, 27, 36, 18, 63,  0, 47, 22,  4, 16, 10, 54, 32,
       43, 21,  5, 28,  9, 51, 50, 35, 44,  1, 53, 31, 17, 57, 61]), 'cur_cost': 113137.0}, {'tour': array([29, 31,  3, 61, 62, 10, 34, 49, 18, 39, 21, 54, 64, 45, 12, 52,  8,
       36, 59, 46, 40, 26,  2, 41,  9, 43, 20, 16, 24, 44, 60,  6, 50,  0,
       14, 15, 30, 47,  4,  7, 51, 33, 63, 25, 23, 53, 56, 35, 37, 17, 22,
       48, 42, 55,  5, 27, 13, 19, 58,  1, 57, 32, 28, 65, 11, 38]), 'cur_cost': 103992.0}]
2025-06-26 19:34:08,098 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:34:08,099 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 331, 'skip_rate': 0.03927492447129909, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 318, 'cache_hits': 200, 'similarity_calculations': 4482, 'cache_hit_rate': 0.04462293618920125, 'cache_size': 4282}}
2025-06-26 19:34:08,099 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 19:34:08,099 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 19:34:08,099 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 19:34:08,099 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 19:34:08,099 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 99809.0
2025-06-26 19:34:08,602 - ExploitationExpert - INFO - res_population_num: 30
2025-06-26 19:34:08,604 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9533, 9551, 9561, 9567, 9521]
2025-06-26 19:34:08,604 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36, 26,
       25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 22, 23, 12, 17, 18, 16, 19, 13, 20,
       21, 40, 43, 48, 49, 47, 46, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 33, 25, 26, 36, 37, 31, 24, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7, 11,  8,  2,  6,  4,  5,  9,  3, 27, 37, 36, 26, 25, 33,
       31, 24, 29, 32, 28, 30, 35, 34, 42, 50, 45, 38, 51, 41, 44, 39, 46,
       47, 49, 48, 43, 40, 21, 20, 13, 19, 16, 18, 17, 12, 23, 22, 15, 14,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 46, 47, 49, 48, 43, 40, 21, 20,
       13, 19, 16, 18, 17, 12, 23, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55,  2,  6,  4,  5,  9, 11,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-26 19:34:08,620 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 19:34:08,621 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1, 58, 59, 60, 61, 62, 63, 64, 57], 'cur_cost': 87538.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([65, 12, 18, 54, 45, 64, 34, 10, 24,  3, 15, 51,  7, 36,  1, 62, 49,
       32, 57,  5, 17, 37, 35, 58, 44, 23,  8,  9, 26, 39, 14, 33, 43, 40,
       22, 53, 59, 20, 21, 46, 60, 48, 27, 41, 11,  2, 61, 55,  4, 38, 30,
       16, 13, 29,  6, 19, 52, 47, 25, 56, 50, 42, 28,  0, 31, 63]), 'cur_cost': 110675.0}, {'tour': array([31, 63, 56, 33,  5,  0, 40, 11, 54, 46, 36, 41, 29, 43, 23, 57, 10,
       55, 48, 61, 58, 34, 47, 51, 25, 39, 32,  9, 16, 38,  7, 20,  8, 35,
       12, 27, 45, 28,  3,  6,  4, 21, 14, 19, 18, 62, 13, 15, 22, 26, 24,
       64, 50, 49, 37, 59, 42, 60,  1, 44, 52, 17,  2, 65, 53, 30]), 'cur_cost': 105446.0}, {'tour': array([34, 38, 52, 41, 15, 55, 26, 29, 13, 11, 46,  3, 30, 39, 19, 37, 59,
       64, 65, 45, 23,  8, 48, 40,  2, 56, 24,  7, 42, 60, 20, 25, 14, 49,
        6, 58, 12, 62, 33, 27, 36, 18, 63,  0, 47, 22,  4, 16, 10, 54, 32,
       43, 21,  5, 28,  9, 51, 50, 35, 44,  1, 53, 31, 17, 57, 61]), 'cur_cost': 113137.0}, {'tour': array([36, 61, 47, 34,  3, 64, 58, 56, 30, 20, 42, 24, 57, 37,  4, 14, 50,
       41,  6, 48, 32,  9, 59,  8, 12, 23, 13, 29,  2, 33, 46, 63,  1, 51,
        0, 25, 28, 45, 60, 16, 43, 44, 17, 21, 38,  5, 55,  7, 49, 52, 11,
       40, 39, 65, 15, 35, 27, 18, 10, 53, 62, 54, 31, 22, 19, 26]), 'cur_cost': 99809.0}]
2025-06-26 19:34:08,623 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 19:34:08,623 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 13, 'total_searches': 332, 'skip_rate': 0.0391566265060241, 'estimated_time_saved': 65, 'path_optimizer': {'path_count': 319, 'cache_hits': 200, 'similarity_calculations': 4496, 'cache_hit_rate': 0.04448398576512456, 'cache_size': 4296}}
2025-06-26 19:34:08,623 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 19:34:08,624 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 8, 19, 30, 41, 52, 7, 18, 29, 40, 51, 6, 17, 28, 39, 50, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 3, 14, 25, 36, 47, 2, 13, 24, 35, 46, 1, 58, 59, 60, 61, 62, 63, 64, 57], 'cur_cost': 87538.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}]
2025-06-26 19:34:08,624 - __main__ - INFO - 进化阶段完成
2025-06-26 19:34:08,625 - __main__ - INFO - 开始评估阶段
2025-06-26 19:34:08,625 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 19:34:08,625 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 19:34:08,626 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 11479.0 → 87538.0 (Δ -76059.0)
- Mean cost: 73457.1 → 101598.3
- Diversity: 0.668 → 0.574 (Δ -0.0938)

#### Elite metrics
- Elite count: 29 → 30
- Best elite cost: 9521 → 9521 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ -42410.33
- Exploit: 4 inds, success 25.0% , avg Δ -6737.5

#### Other indicators
- No-change individuals: 1
- Regression individuals: 7
- Historical trends: Last 3 iterations: costs [11479.0, 11479.0, 11479.0], diversity [0.7713804713804714, 0.8508417508417508, 0.8919191919191919]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 19:34:08,627 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 19:34:26,286 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "late-stage stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_improvement": -42410.33,
      "contribution_to_elite": "moderate (high potential but low success rate)"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -6737.5,
      "contribution_to_elite": "limited (small improvements)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "diagnosis": "explore strategy yields higher improvements but fails frequently, exploit is stable but ineffective",
    "required_adjustment": "increase explore attempts while improving its success rate"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_actions": [
      "introduce new genetic operators to improve explore success rate",
      "implement local search on exploited solutions to enhance their quality",
      "increase population size temporarily to boost diversity"
    ]
  }
}
```
2025-06-26 19:34:26,313 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 19:34:26,313 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "late-stage stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_improvement": -42410.33,
      "contribution_to_elite": "moderate (high potential but low success rate)"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -6737.5,
      "contribution_to_elite": "limited (small improvements)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "diagnosis": "explore strategy yields higher improvements but fails frequently, exploit is stable but ineffective",
    "required_adjustment": "increase explore attempts while improving its success rate"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_actions": [
      "introduce new genetic operators to improve explore success rate",
      "implement local search on exploited solutions to enhance their quality",
      "increase population size temporarily to boost diversity"
    ]
  }
}
```
2025-06-26 19:34:26,313 - __main__ - INFO - 评估阶段完成
2025-06-26 19:34:26,313 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "late-stage stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_improvement": -42410.33,
      "contribution_to_elite": "moderate (high potential but low success rate)"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -6737.5,
      "contribution_to_elite": "limited (small improvements)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore / 40% exploit",
    "diagnosis": "explore strategy yields higher improvements but fails frequently, exploit is stable but ineffective",
    "required_adjustment": "increase explore attempts while improving its success rate"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.8,
    "additional_actions": [
      "introduce new genetic operators to improve explore success rate",
      "implement local search on exploited solutions to enhance their quality",
      "increase population size temporarily to boost diversity"
    ]
  }
}
```
2025-06-26 19:34:26,313 - __main__ - INFO - 当前最佳适应度: 87538.0
2025-06-26 19:34:26,313 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_4.pkl
2025-06-26 19:34:26,333 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_solution.json
2025-06-26 19:34:26,334 - __main__ - INFO - 实例 composite13_66 处理完成
