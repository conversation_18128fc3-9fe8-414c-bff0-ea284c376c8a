2025-06-24 10:26:41,781 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-24 10:26:41,781 - __main__ - INFO - 开始分析阶段
2025-06-24 10:26:41,781 - StatsExpert - INFO - 开始统计分析
2025-06-24 10:26:41,797 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9890.0, 'max': 121665.0, 'mean': 78955.6, 'std': 45536.060392177096}, 'diversity': 0.926936026936027, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-24 10:26:41,797 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9890.0, 'max': 121665.0, 'mean': 78955.6, 'std': 45536.060392177096}, 'diversity_level': 0.926936026936027, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-24 10:26:41,812 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-24 10:26:41,813 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-24 10:26:41,813 - PathExpert - INFO - 开始路径结构分析
2025-06-24 10:26:41,818 - PathExpert - INFO - 路径结构分析完成
2025-06-24 10:26:41,819 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (20, 21), 'frequency': 0.5, 'avg_cost': 11.0}, {'edge': (41, 50), 'frequency': 0.5, 'avg_cost': 30.0}], 'common_subpaths': [{'subpath': (53, 62, 59), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}, {'subpath': (59, 56, 58), 'frequency': 0.3}, {'subpath': (56, 58, 60), 'frequency': 0.3}, {'subpath': (65, 52, 63), 'frequency': 0.3}, {'subpath': (1, 0, 10), 'frequency': 0.3}, {'subpath': (22, 12, 17), 'frequency': 0.3}, {'subpath': (18, 16, 23), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (31, 24, 29), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(54, 57)', 'frequency': 0.4}, {'edge': '(55, 61)', 'frequency': 0.4}, {'edge': '(16, 18)', 'frequency': 0.4}, {'edge': '(20, 21)', 'frequency': 0.5}, {'edge': '(24, 29)', 'frequency': 0.4}, {'edge': '(50, 51)', 'frequency': 0.4}, {'edge': '(41, 50)', 'frequency': 0.5}], 'low_frequency_edges': [{'edge': '(54, 64)', 'frequency': 0.2}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(55, 60)', 'frequency': 0.2}, {'edge': '(61, 65)', 'frequency': 0.2}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(5, 8)', 'frequency': 0.2}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.3}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 3)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.3}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.3}, {'edge': '(17, 18)', 'frequency': 0.2}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.2}, {'edge': '(13, 20)', 'frequency': 0.2}, {'edge': '(19, 21)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.2}, {'edge': '(25, 37)', 'frequency': 0.2}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.2}, {'edge': '(28, 35)', 'frequency': 0.2}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.2}, {'edge': '(33, 34)', 'frequency': 0.2}, {'edge': '(31, 33)', 'frequency': 0.2}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(32, 40)', 'frequency': 0.2}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(46, 48)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(39, 43)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(41, 42)', 'frequency': 0.3}, {'edge': '(1, 7)', 'frequency': 0.2}, {'edge': '(8, 10)', 'frequency': 0.2}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(5, 55)', 'frequency': 0.2}, {'edge': '(53, 61)', 'frequency': 0.2}, {'edge': '(60, 64)', 'frequency': 0.2}, {'edge': '(57, 64)', 'frequency': 0.2}, {'edge': '(54, 65)', 'frequency': 0.2}, {'edge': '(14, 63)', 'frequency': 0.2}, {'edge': '(14, 15)', 'frequency': 0.2}, {'edge': '(28, 33)', 'frequency': 0.2}, {'edge': '(41, 46)', 'frequency': 0.2}, {'edge': '(2, 57)', 'frequency': 0.2}, {'edge': '(19, 45)', 'frequency': 0.2}, {'edge': '(3, 19)', 'frequency': 0.2}, {'edge': '(12, 35)', 'frequency': 0.2}, {'edge': '(5, 9)', 'frequency': 0.3}, {'edge': '(40, 54)', 'frequency': 0.2}, {'edge': '(42, 64)', 'frequency': 0.2}, {'edge': '(25, 29)', 'frequency': 0.2}, {'edge': '(15, 30)', 'frequency': 0.2}, {'edge': '(33, 61)', 'frequency': 0.2}, {'edge': '(16, 56)', 'frequency': 0.2}, {'edge': '(35, 39)', 'frequency': 0.2}, {'edge': '(0, 8)', 'frequency': 0.2}, {'edge': '(0, 30)', 'frequency': 0.3}, {'edge': '(13, 47)', 'frequency': 0.2}, {'edge': '(21, 34)', 'frequency': 0.2}, {'edge': '(3, 25)', 'frequency': 0.2}, {'edge': '(7, 50)', 'frequency': 0.2}, {'edge': '(1, 17)', 'frequency': 0.3}, {'edge': '(14, 47)', 'frequency': 0.2}, {'edge': '(25, 58)', 'frequency': 0.2}, {'edge': '(28, 64)', 'frequency': 0.2}, {'edge': '(8, 44)', 'frequency': 0.2}, {'edge': '(21, 37)', 'frequency': 0.2}, {'edge': '(22, 51)', 'frequency': 0.2}, {'edge': '(6, 30)', 'frequency': 0.2}, {'edge': '(15, 16)', 'frequency': 0.2}, {'edge': '(49, 53)', 'frequency': 0.2}, {'edge': '(43, 60)', 'frequency': 0.2}, {'edge': '(10, 63)', 'frequency': 0.2}, {'edge': '(9, 26)', 'frequency': 0.2}, {'edge': '(3, 40)', 'frequency': 0.2}, {'edge': '(45, 59)', 'frequency': 0.2}, {'edge': '(18, 43)', 'frequency': 0.2}, {'edge': '(32, 42)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [43, 62, 39, 24, 42, 64, 28], 'cost': 15760.0, 'size': 7}, {'region': [59, 30, 38, 52, 48, 1], 'cost': 13352.0, 'size': 6}, {'region': [8, 51, 64, 39, 4], 'cost': 10654.0, 'size': 5}, {'region': [2, 50, 7, 46, 63], 'cost': 10423.0, 'size': 5}, {'region': [53, 25, 65, 36], 'cost': 8821.0, 'size': 4}]}
2025-06-24 10:26:41,819 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-24 10:26:41,820 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-24 10:26:41,820 - EliteExpert - INFO - 开始精英解分析
2025-06-24 10:26:41,820 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-24 10:26:41,820 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-24 10:26:41,821 - __main__ - INFO - 更新共享数据: elite_features
2025-06-24 10:26:41,821 - LandscapeExpert - INFO - 开始景观分析
2025-06-24 10:26:42,628 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-24 10:26:42,628 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9890.0, mean 78955.6, max 121665.0, std 45536.060392177096
- diversity: 0.926936026936027
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (20, 21), 'frequency': 0.5, 'avg_cost': 11.0}, {'edge': (41, 50), 'frequency': 0.5, 'avg_cost': 30.0}]
- common_subpaths_sample: [{'subpath': (53, 62, 59), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}, {'subpath': (59, 56, 58), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [43, 62, 39, 24, 42, 64, 28], 'cost': 15760.0, 'size': 7}, {'region': [59, 30, 38, 52, 48, 1], 'cost': 13352.0, 'size': 6}, {'region': [8, 51, 64, 39, 4], 'cost': 10654.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-24 10:26:50,956 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High. The wide range of edge lengths (min 10.0, max 3032.0) and a high standard deviation in cost (45536.06) suggest a rugged search space with varying levels of difficulty.",
    "modality": "Potentially multimodal. The large range in cost statistics (min 9890.0, max 121665.0) indicates multiple local optima.",
    "deceptiveness": "Potentially deceptive. The presence of long edges (long_edge_ratio 0.249) relative to the median edge length (1900.0) could mislead the search towards suboptimal solutions."
  },
  "population_state": {
    "coverage": "Good, but uneven. The diversity is relatively high (0.927), suggesting good population coverage. However, the density grid reveals uneven distribution, with clustering in some cells and sparsity in others.",
    "convergence": "Low. The convergence is 0.0, indicating the population is not converging towards a single solution. This is supported by the high diversity and high cost standard deviation."
  },
  "difficult_regions": {
    "spatial_analysis": "The low-density cells (center and top-right) and the relatively high percentage of long edges indicate potential difficult corridors or regions. Also, the edge_len_stats shows high values for 75% edges (2469.0).",
    "structural_signals": "Regions identified as 'difficult_regions_sample' likely correspond to challenging areas in the search space. These regions appear to involve nodes with high connection costs or complex interdependencies. Specifically, region [43, 62, 39, 24, 42, 64, 28] is expensive.",
    "refined_list": "[43, 62, 39, 24, 42, 64, 28], [59, 30, 38, 52, 48, 1], [8, 51, 64, 39, 4]"
  },
  "opportunity_regions": {
    "spatial_analysis": "The high-density cells (top-left, bottom-left, bottom-right) suggest areas where solutions may be concentrated, potentially representing promising regions.",
    "structural_signals": "The 'high_quality_edges_sample' and 'common_subpaths_sample' point towards specific edges and sequences of nodes that frequently appear in good solutions. These could form the basis of opportunity regions.",
    "refined_list": "Nodes 20, 21 (edge with freq 0.5), Nodes 41, 50 (edge with freq 0.5), Subpath (53, 62, 59), Subpath (62, 59, 56), Subpath (59, 56, 58). Focusing on permutations using these elements may prove fruitful."
  },
  "evolution_phase": "Exploration phase. The high diversity, low convergence, and wide range of costs indicate the algorithm is still exploring the search space.",
  "evolution_direction": {
    "operator_suggestions": "1. **Diversify Crossover:** Use crossover operators that promote exploration and introduce new edges, especially considering the high diversity and uneven spatial distribution. Consider edge recombination operators that incorporate edges from diverse parents.\n2. **Intensify Local Search in Opportunity Regions:** Apply local search operators (e.g., 2-opt, 3-opt) within the identified opportunity regions, focusing on refining solutions around the promising edges and subpaths. Apply guided local search to escape local minima around these regions.\n3. **Avoid Exploitation of Difficult Regions:** Implement mechanisms to penalize or avoid exploration of the identified difficult regions, potentially through adaptive penalty functions or restricted neighborhood searches.\n4. **Adaptive Mutation:** Adjust mutation rates based on the density of the surrounding nodes. Higher mutation rates in sparse regions, and lower rates in dense regions to avoid premature disruption. Implement subpath reordering, specifically focused in high-density areas to exploit existing successful structures."
  }
}
```
2025-06-24 10:26:50,956 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-24 10:26:50,956 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High. The wide range of edge lengths (min 10.0, max 3032.0) and a high standard deviation in cost (45536.06) suggest a rugged search space with varying levels of difficulty.', 'modality': 'Potentially multimodal. The large range in cost statistics (min 9890.0, max 121665.0) indicates multiple local optima.', 'deceptiveness': 'Potentially deceptive. The presence of long edges (long_edge_ratio 0.249) relative to the median edge length (1900.0) could mislead the search towards suboptimal solutions.'}, 'population_state': {'coverage': 'Good, but uneven. The diversity is relatively high (0.927), suggesting good population coverage. However, the density grid reveals uneven distribution, with clustering in some cells and sparsity in others.', 'convergence': 'Low. The convergence is 0.0, indicating the population is not converging towards a single solution. This is supported by the high diversity and high cost standard deviation.'}, 'difficult_regions': {'spatial_analysis': 'The low-density cells (center and top-right) and the relatively high percentage of long edges indicate potential difficult corridors or regions. Also, the edge_len_stats shows high values for 75% edges (2469.0).', 'structural_signals': "Regions identified as 'difficult_regions_sample' likely correspond to challenging areas in the search space. These regions appear to involve nodes with high connection costs or complex interdependencies. Specifically, region [43, 62, 39, 24, 42, 64, 28] is expensive.", 'refined_list': '[43, 62, 39, 24, 42, 64, 28], [59, 30, 38, 52, 48, 1], [8, 51, 64, 39, 4]'}, 'opportunity_regions': {'spatial_analysis': 'The high-density cells (top-left, bottom-left, bottom-right) suggest areas where solutions may be concentrated, potentially representing promising regions.', 'structural_signals': "The 'high_quality_edges_sample' and 'common_subpaths_sample' point towards specific edges and sequences of nodes that frequently appear in good solutions. These could form the basis of opportunity regions.", 'refined_list': 'Nodes 20, 21 (edge with freq 0.5), Nodes 41, 50 (edge with freq 0.5), Subpath (53, 62, 59), Subpath (62, 59, 56), Subpath (59, 56, 58). Focusing on permutations using these elements may prove fruitful.'}, 'evolution_phase': 'Exploration phase. The high diversity, low convergence, and wide range of costs indicate the algorithm is still exploring the search space.', 'evolution_direction': {'operator_suggestions': '1. **Diversify Crossover:** Use crossover operators that promote exploration and introduce new edges, especially considering the high diversity and uneven spatial distribution. Consider edge recombination operators that incorporate edges from diverse parents.\n2. **Intensify Local Search in Opportunity Regions:** Apply local search operators (e.g., 2-opt, 3-opt) within the identified opportunity regions, focusing on refining solutions around the promising edges and subpaths. Apply guided local search to escape local minima around these regions.\n3. **Avoid Exploitation of Difficult Regions:** Implement mechanisms to penalize or avoid exploration of the identified difficult regions, potentially through adaptive penalty functions or restricted neighborhood searches.\n4. **Adaptive Mutation:** Adjust mutation rates based on the density of the surrounding nodes. Higher mutation rates in sparse regions, and lower rates in dense regions to avoid premature disruption. Implement subpath reordering, specifically focused in high-density areas to exploit existing successful structures.'}}
2025-06-24 10:26:50,959 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-24 10:26:50,959 - __main__ - INFO - 分析阶段完成
2025-06-24 10:26:50,959 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High. The wide range of edge lengths (min 10.0, max 3032.0) and a high standard deviation in cost (45536.06) suggest a rugged search space with varying levels of difficulty.', 'modality': 'Potentially multimodal. The large range in cost statistics (min 9890.0, max 121665.0) indicates multiple local optima.', 'deceptiveness': 'Potentially deceptive. The presence of long edges (long_edge_ratio 0.249) relative to the median edge length (1900.0) could mislead the search towards suboptimal solutions.'}, 'population_state': {'coverage': 'Good, but uneven. The diversity is relatively high (0.927), suggesting good population coverage. However, the density grid reveals uneven distribution, with clustering in some cells and sparsity in others.', 'convergence': 'Low. The convergence is 0.0, indicating the population is not converging towards a single solution. This is supported by the high diversity and high cost standard deviation.'}, 'difficult_regions': {'spatial_analysis': 'The low-density cells (center and top-right) and the relatively high percentage of long edges indicate potential difficult corridors or regions. Also, the edge_len_stats shows high values for 75% edges (2469.0).', 'structural_signals': "Regions identified as 'difficult_regions_sample' likely correspond to challenging areas in the search space. These regions appear to involve nodes with high connection costs or complex interdependencies. Specifically, region [43, 62, 39, 24, 42, 64, 28] is expensive.", 'refined_list': '[43, 62, 39, 24, 42, 64, 28], [59, 30, 38, 52, 48, 1], [8, 51, 64, 39, 4]'}, 'opportunity_regions': {'spatial_analysis': 'The high-density cells (top-left, bottom-left, bottom-right) suggest areas where solutions may be concentrated, potentially representing promising regions.', 'structural_signals': "The 'high_quality_edges_sample' and 'common_subpaths_sample' point towards specific edges and sequences of nodes that frequently appear in good solutions. These could form the basis of opportunity regions.", 'refined_list': 'Nodes 20, 21 (edge with freq 0.5), Nodes 41, 50 (edge with freq 0.5), Subpath (53, 62, 59), Subpath (62, 59, 56), Subpath (59, 56, 58). Focusing on permutations using these elements may prove fruitful.'}, 'evolution_phase': 'Exploration phase. The high diversity, low convergence, and wide range of costs indicate the algorithm is still exploring the search space.', 'evolution_direction': {'operator_suggestions': '1. **Diversify Crossover:** Use crossover operators that promote exploration and introduce new edges, especially considering the high diversity and uneven spatial distribution. Consider edge recombination operators that incorporate edges from diverse parents.\n2. **Intensify Local Search in Opportunity Regions:** Apply local search operators (e.g., 2-opt, 3-opt) within the identified opportunity regions, focusing on refining solutions around the promising edges and subpaths. Apply guided local search to escape local minima around these regions.\n3. **Avoid Exploitation of Difficult Regions:** Implement mechanisms to penalize or avoid exploration of the identified difficult regions, potentially through adaptive penalty functions or restricted neighborhood searches.\n4. **Adaptive Mutation:** Adjust mutation rates based on the density of the surrounding nodes. Higher mutation rates in sparse regions, and lower rates in dense regions to avoid premature disruption. Implement subpath reordering, specifically focused in high-density areas to exploit existing successful structures.'}}
2025-06-24 10:26:50,959 - __main__ - INFO - 开始策略分配阶段
2025-06-24 10:26:50,959 - StrategyExpert - INFO - 开始策略分配分析
2025-06-24 10:26:50,961 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 0
- Population: 10 individuals, diversity=0.76
- Best cost: 9890.0, Mean cost: 78955.6
- Search space: Ruggedness: High. The wide range of edge lengths (min 10.0, max 3032.0) and a high standard deviation in cost (45536.06) suggest a rugged search space with varying levels of difficulty., Modality: Potentially multimodal. The large range in cost statistics (min 9890.0, max 121665.0) indicates multiple local optima.
- Difficult regions: 3 regions
- Opportunity regions: 3 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: No previous feedback

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-24 10:26:50,961 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-24 10:26:53,942 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "type": "random_restart",
      "mutation_rate": 0.8,
      "step_size": 0.2,
      "restart_probability": 0.3
    },
    "exploit": {
      "type": "local_search",
      "step_size": 0.05,
      "neighborhood_size": 5,
      "climb_probability": 0.9
    }
  }
}
```

2025-06-24 10:26:53,942 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore']
2025-06-24 10:26:53,942 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore']
2025-06-24 10:26:53,942 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "type": "random_restart",
      "mutation_rate": 0.8,
      "step_size": 0.2,
      "restart_probability": 0.3
    },
    "exploit": {
      "type": "local_search",
      "step_size": 0.05,
      "neighborhood_size": 5,
      "climb_probability": 0.9
    }
  }
}
```

2025-06-24 10:26:53,942 - __main__ - INFO - 策略分配阶段完成
2025-06-24 10:26:53,942 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore']
2025-06-24 10:26:53,942 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "type": "random_restart",
      "mutation_rate": 0.8,
      "step_size": 0.2,
      "restart_probability": 0.3
    },
    "exploit": {
      "type": "local_search",
      "step_size": 0.05,
      "neighborhood_size": 5,
      "climb_probability": 0.9
    }
  }
}
```

2025-06-24 10:26:53,942 - __main__ - INFO - 开始进化阶段
2025-06-24 10:26:53,942 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-24 10:26:53,942 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:26:53,942 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [57, 54, 64] and ending with [50, 41, 42]
- Cost: 10079.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.76
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:26:53,942 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:26:57,380 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2
  ],
  "modification_strategy": "Completely shuffled the original path and then performed a random swap operation to ensure the solution is drastically different from the current best path, encouraging exploration of completely new regions of the search space. This leverages the high diversity weight to prioritize exploration over exploitation.",
  "targeted_regions": "Exploration of all possible regions due to the complete shuffle."
}
```
2025-06-24 10:26:57,382 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:26:57,382 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2]
2025-06-24 10:26:57,382 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2], 'cur_cost': 11479.0}
2025-06-24 10:26:57,382 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-24 10:26:57,382 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:26:57,382 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [11, 9, 3] and ending with [50, 41, 42]
- Cost: 10255.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8138461538461539
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:26:57,382 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:27:00,852 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41
  ],
  "modification_strategy": "Reversed the second half of the original path and introduced significant disruption to force exploration. Maintained all nodes to satisfy TSP constraints.",
  "targeted_regions": "Entire path, due to widespread changes; specifically targeted exploration of the nodes 41-65 range which would be significantly different from the current best path."
}
```
2025-06-24 10:27:00,852 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:27:00,852 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15727.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41]
2025-06-24 10:27:00,852 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41], 'cur_cost': 15727.0}
2025-06-24 10:27:00,854 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-24 10:27:00,854 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:27:00,854 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [30, 28, 33] and ending with [43, 48, 42]
- Cost: 9890.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7953846153846154
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:27:00,854 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:27:04,698 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65,
    4,
    3,
    2,
    1,
    0
  ],
  "modification_strategy": "Completely randomized the path and inverted a segment to increase diversity. Aimed for a path that is structurally different from the original.",
  "targeted_regions": "Explored potentially undiscovered connections between different regions of the graph by creating a largely random path."
}
```
2025-06-24 10:27:04,698 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:27:04,701 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11493.0, 路径: [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 4, 3, 2, 1, 0]
2025-06-24 10:27:04,701 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 4, 3, 2, 1, 0], 'cur_cost': 11493.0}
2025-06-24 10:27:04,701 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-24 10:27:04,701 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:27:04,701 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [27, 62, 1] and ending with [15, 30, 39]
- Cost: 98697.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7246153846153847
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:27:04,701 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:27:08,863 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27
  ],
  "modification_strategy": "Reverse the original path entirely. This will create a very diverse path as it completely inverts the order of nodes. This is a high-risk, high-reward strategy aiming to explore a drastically different trajectory and hoping to stumble upon new connections that lead to improvements. It maximizes diversity at the expense of potentially increasing the path cost significantly.",
  "targeted_regions": "The entire problem space is targeted as the path is completely reversed, inherently exploring every area in a drastically different order than the original."
}
```
2025-06-24 10:27:08,863 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:27:08,863 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15272.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27]
2025-06-24 10:27:08,863 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}
2025-06-24 10:27:08,863 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-24 10:27:08,863 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:27:08,863 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [50, 27, 54] and ending with [23, 24, 29]
- Cost: 115812.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6461538461538462
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:27:08,863 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:27:13,642 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Generated a completely new, naive path to maximize diversity. This starts with 0 and increments by 1 until 65.  This prioritizes exploration over immediate cost improvement and acts as a blank slate for further mutations. Due to the high diversity weight, this drastic measure is taken to break away from the current path's local optima and explore vastly different routes.",
  "targeted_regions": "All unexplored regions, effectively resetting the path to allow for a fresh exploration of the search space."
}
```
2025-06-24 10:27:13,642 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:27:13,642 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-24 10:27:13,645 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-24 10:27:13,645 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-24 10:27:13,645 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:27:13,645 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [54, 23, 12] and ending with [11, 32, 9]
- Cost: 121665.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.5661538461538461
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:27:13,646 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:27:17,184 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32
  ],
  "modification_strategy": "Reverse a large portion of the path to significantly alter the node sequence and explore potentially different connections. We reversed the path from index 31 to index 65 inclusive.",
  "targeted_regions": "By reversing a significant chunk, we aim to break existing patterns and introduce a completely new connection sequence, exploring all opportunity regions simultaneously, assuming they lie within the reversed portion of the path or connect to its endpoints."
}
```
2025-06-24 10:27:17,184 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:27:17,184 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15356.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32]
2025-06-24 10:27:17,184 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32], 'cur_cost': 15356.0}
2025-06-24 10:27:17,184 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-24 10:27:17,184 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:27:17,184 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:27:17,189 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 104093.0
2025-06-24 10:27:20,104 - ExploitationExpert - INFO - res_population_num: 1
2025-06-24 10:27:20,104 - ExploitationExpert - INFO - res_population_costs: [89460]
2025-06-24 10:27:20,104 - ExploitationExpert - INFO - res_populations: [array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64)]
2025-06-24 10:27:20,104 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:27:20,104 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41], 'cur_cost': 15727.0}, {'tour': [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 4, 3, 2, 1, 0], 'cur_cost': 11493.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32], 'cur_cost': 15356.0}, {'tour': array([58,  7, 35, 61, 38, 60, 65, 12, 52, 64,  4, 56, 45, 54,  1, 62, 32,
       25, 23, 51, 36, 49, 44, 17,  2, 63, 42, 47, 11, 43, 31, 40, 26, 55,
       18, 50, 15, 57, 48, 37, 24, 10, 13,  8,  9, 27, 22, 20, 53, 34, 14,
        3, 19,  5,  6, 29, 59, 41, 30, 33, 28, 21,  0, 46, 39, 16]), 'cur_cost': 104093.0}, {'tour': [64, 28, 36, 61, 22, 31, 1, 17, 58, 20, 21, 63, 10, 54, 57, 2, 19, 41, 50, 47, 13, 26, 9, 42, 14, 51, 33, 32, 38, 25, 3, 40, 24, 5, 11, 46, 27, 35, 60, 55, 45, 59, 12, 15, 16, 56, 29, 53, 49, 37, 44, 8, 39, 23, 34, 6, 30, 0, 48, 4, 52, 7, 43, 18, 65, 62], 'cur_cost': 105729.0}, {'tour': [38, 31, 0, 57, 6, 21, 37, 9, 26, 24, 14, 33, 28, 61, 12, 53, 11, 34, 7, 1, 10, 52, 29, 40, 23, 18, 43, 46, 62, 2, 65, 19, 22, 42, 32, 16, 60, 47, 17, 49, 3, 59, 45, 13, 5, 8, 51, 64, 39, 4, 54, 25, 58, 55, 48, 35, 56, 20, 50, 41, 63, 15, 30, 27, 44, 36], 'cur_cost': 105668.0}, {'tour': [24, 35, 16, 46, 41, 33, 61, 23, 42, 32, 56, 1, 17, 45, 19, 54, 64, 38, 58, 5, 28, 47, 14, 62, 48, 9, 4, 3, 40, 37, 57, 13, 0, 30, 49, 39, 55, 2, 15, 6, 29, 59, 52, 43, 60, 8, 26, 11, 50, 7, 63, 10, 51, 22, 34, 21, 53, 25, 65, 36, 18, 20, 12, 27, 31, 44], 'cur_cost': 110832.0}]
2025-06-24 10:27:20,104 - ExploitationExpert - INFO - 局部搜索耗时: 2.91秒
2025-06-24 10:27:20,104 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-24 10:27:20,104 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-24 10:27:20,104 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-24 10:27:20,104 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:27:20,104 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:27:20,104 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 108331.0
2025-06-24 10:27:21,150 - ExploitationExpert - INFO - res_population_num: 2
2025-06-24 10:27:21,150 - ExploitationExpert - INFO - res_population_costs: [89460, 9541]
2025-06-24 10:27:21,150 - ExploitationExpert - INFO - res_populations: [array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64)]
2025-06-24 10:27:21,150 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:27:21,150 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41], 'cur_cost': 15727.0}, {'tour': [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 4, 3, 2, 1, 0], 'cur_cost': 11493.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32], 'cur_cost': 15356.0}, {'tour': array([58,  7, 35, 61, 38, 60, 65, 12, 52, 64,  4, 56, 45, 54,  1, 62, 32,
       25, 23, 51, 36, 49, 44, 17,  2, 63, 42, 47, 11, 43, 31, 40, 26, 55,
       18, 50, 15, 57, 48, 37, 24, 10, 13,  8,  9, 27, 22, 20, 53, 34, 14,
        3, 19,  5,  6, 29, 59, 41, 30, 33, 28, 21,  0, 46, 39, 16]), 'cur_cost': 104093.0}, {'tour': array([27, 61, 51, 55, 22, 25, 31, 30, 15, 26, 28, 21,  6, 34, 56, 36, 39,
       38,  2, 23, 57, 48, 50, 54, 65, 10, 13, 19, 20, 11, 44,  7, 60,  4,
       59, 14, 32, 46, 18, 52, 33, 53, 12, 41, 35,  0,  5,  3, 64, 47, 40,
        1, 45, 17, 49, 58,  9, 29,  8, 24, 62, 16, 42, 43, 37, 63]), 'cur_cost': 108331.0}, {'tour': [38, 31, 0, 57, 6, 21, 37, 9, 26, 24, 14, 33, 28, 61, 12, 53, 11, 34, 7, 1, 10, 52, 29, 40, 23, 18, 43, 46, 62, 2, 65, 19, 22, 42, 32, 16, 60, 47, 17, 49, 3, 59, 45, 13, 5, 8, 51, 64, 39, 4, 54, 25, 58, 55, 48, 35, 56, 20, 50, 41, 63, 15, 30, 27, 44, 36], 'cur_cost': 105668.0}, {'tour': [24, 35, 16, 46, 41, 33, 61, 23, 42, 32, 56, 1, 17, 45, 19, 54, 64, 38, 58, 5, 28, 47, 14, 62, 48, 9, 4, 3, 40, 37, 57, 13, 0, 30, 49, 39, 55, 2, 15, 6, 29, 59, 52, 43, 60, 8, 26, 11, 50, 7, 63, 10, 51, 22, 34, 21, 53, 25, 65, 36, 18, 20, 12, 27, 31, 44], 'cur_cost': 110832.0}]
2025-06-24 10:27:21,150 - ExploitationExpert - INFO - 局部搜索耗时: 1.05秒
2025-06-24 10:27:21,150 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-24 10:27:21,150 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-24 10:27:21,166 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-24 10:27:21,166 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:27:21,166 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:27:21,166 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 108612.0
2025-06-24 10:27:21,667 - ExploitationExpert - INFO - res_population_num: 6
2025-06-24 10:27:21,667 - ExploitationExpert - INFO - res_population_costs: [89460, 9541, 9528, 9528, 9521, 9521]
2025-06-24 10:27:21,667 - ExploitationExpert - INFO - res_populations: [array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-24 10:27:21,671 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:27:21,671 - ExploitationExpert - INFO - populations: [{'tour': [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41], 'cur_cost': 15727.0}, {'tour': [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 4, 3, 2, 1, 0], 'cur_cost': 11493.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32], 'cur_cost': 15356.0}, {'tour': array([58,  7, 35, 61, 38, 60, 65, 12, 52, 64,  4, 56, 45, 54,  1, 62, 32,
       25, 23, 51, 36, 49, 44, 17,  2, 63, 42, 47, 11, 43, 31, 40, 26, 55,
       18, 50, 15, 57, 48, 37, 24, 10, 13,  8,  9, 27, 22, 20, 53, 34, 14,
        3, 19,  5,  6, 29, 59, 41, 30, 33, 28, 21,  0, 46, 39, 16]), 'cur_cost': 104093.0}, {'tour': array([27, 61, 51, 55, 22, 25, 31, 30, 15, 26, 28, 21,  6, 34, 56, 36, 39,
       38,  2, 23, 57, 48, 50, 54, 65, 10, 13, 19, 20, 11, 44,  7, 60,  4,
       59, 14, 32, 46, 18, 52, 33, 53, 12, 41, 35,  0,  5,  3, 64, 47, 40,
        1, 45, 17, 49, 58,  9, 29,  8, 24, 62, 16, 42, 43, 37, 63]), 'cur_cost': 108331.0}, {'tour': array([ 1, 45, 40,  3, 34, 10, 16, 55, 58, 50,  8, 36, 64, 44, 62, 28,  4,
       56, 27, 18, 11, 17, 25, 32, 59, 38, 47, 24,  6,  9, 43, 14, 20, 46,
       33, 19, 52,  5, 65, 51, 63, 53, 42,  0, 35, 22, 41, 26, 30, 29, 49,
        7, 23, 21, 15, 48, 37, 13, 54, 31, 12,  2, 60, 39, 61, 57]), 'cur_cost': 108612.0}, {'tour': [24, 35, 16, 46, 41, 33, 61, 23, 42, 32, 56, 1, 17, 45, 19, 54, 64, 38, 58, 5, 28, 47, 14, 62, 48, 9, 4, 3, 40, 37, 57, 13, 0, 30, 49, 39, 55, 2, 15, 6, 29, 59, 52, 43, 60, 8, 26, 11, 50, 7, 63, 10, 51, 22, 34, 21, 53, 25, 65, 36, 18, 20, 12, 27, 31, 44], 'cur_cost': 110832.0}]
2025-06-24 10:27:21,673 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 10:27:21,673 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-24 10:27:21,673 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-24 10:27:21,673 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-24 10:27:21,673 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:27:21,674 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [24, 35, 16] and ending with [27, 31, 44]
- Cost: 110832.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.47692307692307695
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:27:21,674 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:27:25,550 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Started with an identity permutation. This maximizes diversity from the previous path while still fulfilling all problem constraints. Since cost is not specified as a hard constraint for this generation, we prioritize exploration.",
  "targeted_regions": "All regions, as the goal is to maximize diversity and explore the entire search space."
}
```
2025-06-24 10:27:25,550 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:27:25,550 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-24 10:27:25,550 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-24 10:27:25,553 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41], 'cur_cost': 15727.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 4, 3, 2, 1, 0], 'cur_cost': 11493.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32], 'cur_cost': 15356.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-06-24 10:27:25,553 - __main__ - INFO - 进化阶段完成
2025-06-24 10:27:25,553 - __main__ - INFO - 开始评估阶段
2025-06-24 10:27:25,553 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-24 10:27:25,553 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-24 10:27:25,553 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 0/5
- Population changes:
  * Cost: min 9890.0->11479.0, mean 78955.6->41332.1
  * Diversity: 0.76->0.38461538461538464
  * Elite solutions: 3->4

## Strategy Performance:
- Explore: 7 individuals, success rate 57.1%, avg improvement 54992.14
- Exploit: 3 individuals, success rate 0.0%, avg improvement -2903.33
- Overall improvement: -1589.0

## Historical Trends:
No historical data available

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-24 10:27:30,676 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Exploration",
    "features": "The evolution is in its very early stages (iteration 0/5). The population cost, while showing a decrease in the mean, indicates that the solutions are still far from optimal. The low elite count (3, now 4) and high diversity suggest the algorithm is primarily exploring the search space. The significant difference between min and mean cost indicates a wide range of solution qualities within the population."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Effective in finding improvements. High success rate (57.1%) and substantial average improvement (54992.14) suggest that exploration is discovering promising regions of the search space.",
      "assessment": "Highly effective and crucial at this stage. Focus should remain on continuing to explore effectively."
    },
    "exploit": {
      "performance": "Ineffective. Zero success rate and negative average improvement (-2903.33) indicate that exploitation is currently leading to worse solutions.",
      "assessment": "Not effective at this stage. It's likely that the algorithm is trying to refine solutions in areas that are not yet optimal, leading to regression."
    }
  },
  "balance_state": {
    "assessment": "Imbalanced towards exploration, which is appropriate for the early stage. Exploitation is currently detrimental.",
    "adjustment_needs": "Reduce or temporarily disable the exploitation strategy. Focus on amplifying the exploration strategy to find better initial solutions before attempting refinement."
  },
  "recommendations": {
    "exploration": "Continue and possibly increase the emphasis on the explore strategy. Consider adjusting the parameters of the exploration strategy (e.g., mutation rates, crossover types) to further enhance its effectiveness. Analyze the successful exploration individuals to identify beneficial traits and patterns.",
    "exploitation": "Reduce the number of individuals assigned to the exploit strategy, or temporarily disable it altogether. If retaining, reduce its intensity (e.g., smaller mutations).",
    "population_management": "Monitor the diversity metric. If diversity drops too rapidly in subsequent iterations, consider mechanisms to re-introduce diversity (e.g., random immigrants).",
    "iteration_strategy": "For iteration 1, focus almost exclusively on exploration. Re-evaluate the exploitation strategy in later iterations when the overall population quality has improved significantly and diversity has decreased substantially.",
    "parameter_tuning": "Analyze the parameters of the explore strategy for top-performing individuals to identify beneficial parameter settings for the next iteration. Consider adaptive parameter control mechanisms that adjust parameters based on the performance of individuals."
  }
}
```
2025-06-24 10:27:30,703 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-24 10:27:30,703 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Exploration",
    "features": "The evolution is in its very early stages (iteration 0/5). The population cost, while showing a decrease in the mean, indicates that the solutions are still far from optimal. The low elite count (3, now 4) and high diversity suggest the algorithm is primarily exploring the search space. The significant difference between min and mean cost indicates a wide range of solution qualities within the population."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Effective in finding improvements. High success rate (57.1%) and substantial average improvement (54992.14) suggest that exploration is discovering promising regions of the search space.",
      "assessment": "Highly effective and crucial at this stage. Focus should remain on continuing to explore effectively."
    },
    "exploit": {
      "performance": "Ineffective. Zero success rate and negative average improvement (-2903.33) indicate that exploitation is currently leading to worse solutions.",
      "assessment": "Not effective at this stage. It's likely that the algorithm is trying to refine solutions in areas that are not yet optimal, leading to regression."
    }
  },
  "balance_state": {
    "assessment": "Imbalanced towards exploration, which is appropriate for the early stage. Exploitation is currently detrimental.",
    "adjustment_needs": "Reduce or temporarily disable the exploitation strategy. Focus on amplifying the exploration strategy to find better initial solutions before attempting refinement."
  },
  "recommendations": {
    "exploration": "Continue and possibly increase the emphasis on the explore strategy. Consider adjusting the parameters of the exploration strategy (e.g., mutation rates, crossover types) to further enhance its effectiveness. Analyze the successful exploration individuals to identify beneficial traits and patterns.",
    "exploitation": "Reduce the number of individuals assigned to the exploit strategy, or temporarily disable it altogether. If retaining, reduce its intensity (e.g., smaller mutations).",
    "population_management": "Monitor the diversity metric. If diversity drops too rapidly in subsequent iterations, consider mechanisms to re-introduce diversity (e.g., random immigrants).",
    "iteration_strategy": "For iteration 1, focus almost exclusively on exploration. Re-evaluate the exploitation strategy in later iterations when the overall population quality has improved significantly and diversity has decreased substantially.",
    "parameter_tuning": "Analyze the parameters of the explore strategy for top-performing individuals to identify beneficial parameter settings for the next iteration. Consider adaptive parameter control mechanisms that adjust parameters based on the performance of individuals."
  }
}
```
2025-06-24 10:27:30,703 - __main__ - INFO - 评估阶段完成
2025-06-24 10:27:30,703 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Exploration",
    "features": "The evolution is in its very early stages (iteration 0/5). The population cost, while showing a decrease in the mean, indicates that the solutions are still far from optimal. The low elite count (3, now 4) and high diversity suggest the algorithm is primarily exploring the search space. The significant difference between min and mean cost indicates a wide range of solution qualities within the population."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Effective in finding improvements. High success rate (57.1%) and substantial average improvement (54992.14) suggest that exploration is discovering promising regions of the search space.",
      "assessment": "Highly effective and crucial at this stage. Focus should remain on continuing to explore effectively."
    },
    "exploit": {
      "performance": "Ineffective. Zero success rate and negative average improvement (-2903.33) indicate that exploitation is currently leading to worse solutions.",
      "assessment": "Not effective at this stage. It's likely that the algorithm is trying to refine solutions in areas that are not yet optimal, leading to regression."
    }
  },
  "balance_state": {
    "assessment": "Imbalanced towards exploration, which is appropriate for the early stage. Exploitation is currently detrimental.",
    "adjustment_needs": "Reduce or temporarily disable the exploitation strategy. Focus on amplifying the exploration strategy to find better initial solutions before attempting refinement."
  },
  "recommendations": {
    "exploration": "Continue and possibly increase the emphasis on the explore strategy. Consider adjusting the parameters of the exploration strategy (e.g., mutation rates, crossover types) to further enhance its effectiveness. Analyze the successful exploration individuals to identify beneficial traits and patterns.",
    "exploitation": "Reduce the number of individuals assigned to the exploit strategy, or temporarily disable it altogether. If retaining, reduce its intensity (e.g., smaller mutations).",
    "population_management": "Monitor the diversity metric. If diversity drops too rapidly in subsequent iterations, consider mechanisms to re-introduce diversity (e.g., random immigrants).",
    "iteration_strategy": "For iteration 1, focus almost exclusively on exploration. Re-evaluate the exploitation strategy in later iterations when the overall population quality has improved significantly and diversity has decreased substantially.",
    "parameter_tuning": "Analyze the parameters of the explore strategy for top-performing individuals to identify beneficial parameter settings for the next iteration. Consider adaptive parameter control mechanisms that adjust parameters based on the performance of individuals."
  }
}
```
2025-06-24 10:27:30,703 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-24 10:27:30,703 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-24 10:27:30,703 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-24 10:27:30,703 - __main__ - INFO - 开始分析阶段
2025-06-24 10:27:30,703 - StatsExpert - INFO - 开始统计分析
2025-06-24 10:27:30,710 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 108612.0, 'mean': 41332.1, 'std': 43044.01290644263}, 'diversity': 0.5279461279461279, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0}
2025-06-24 10:27:30,710 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 108612.0, 'mean': 41332.1, 'std': 43044.01290644263}, 'diversity_level': 0.5279461279461279, 'convergence_level': 0.0, 'clustering_info': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-24 10:27:30,710 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-24 10:27:30,710 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-24 10:27:30,725 - PathExpert - INFO - 开始路径结构分析
2025-06-24 10:27:30,727 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-24 10:27:30,727 - PathExpert - INFO - 路径结构分析完成
2025-06-24 10:27:30,727 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-24 10:27:30,728 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-24 10:27:30,728 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-24 10:27:30,728 - EliteExpert - INFO - 开始精英解分析
2025-06-24 10:27:30,729 - EliteExpert - INFO - 精英解分析完成
2025-06-24 10:27:30,729 - __main__ - INFO - 精英专家分析报告: {'elite_count': 6, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1958.0, 'avg_gap': 18482.266666666666}, 'structure_gap': {'unique_elite_edges': 195, 'unique_pop_edges': 265, 'common_edges': 35}}, 'elite_diversity': {'diversity_score': 0.4878787878787879}}
2025-06-24 10:27:30,729 - __main__ - INFO - 更新共享数据: elite_features
2025-06-24 10:27:30,730 - LandscapeExpert - INFO - 开始景观分析
2025-06-24 10:27:30,732 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-24 10:27:30,732 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 11479.0, mean 41332.1, max 108612.0, std 43044.01290644263
- diversity: 0.5279461279461279
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [11479.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-24 10:27:36,111 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "The wide range in edge lengths (edge_len_stats: [10.0, 1075.0, 1900.0, 2469.0, 3032.0]) and a significant long_edge_ratio (0.249) suggest a moderately rugged search space. The presence of both very short and very long edges indicates potential for significant cost changes with small changes in the tour.",
    "modality": "Without more evolution information, it is hard to assess modality precisely, but the high standard deviation of cost (std 43044.01290644263) implies multiple local optima of varying quality. The initial population may be exploring a multi-modal landscape.",
    "deceptiveness": "The presence of long edges may be deceptive if simply removing them doesn't necessarily lead to good solutions, as re-connecting the tour might create new longer edges or other issues. Conversely, very short edges clustered together might appear beneficial locally but detrimental globally."
  },
  "population_state": {
    "coverage": "The diversity is moderate (0.5279461279461279). Given the density grid showing concentrations in specific cells, the population is not exploring all areas of the search space evenly. It appears concentrated in the corners and possibly a central band.",
    "convergence": "The convergence is very low (0.0), indicating that the population is still exploring and hasn't started to coalesce around a specific region of the search space significantly."
  },
  "difficult_regions": [
    "Potential difficult regions include corridors between the high-density cells in the density grid (e.g., between [14,0,0] and [0,12,12], or between [0,12,12] and [14,0,14]).",
    "Regions containing the longest edges (determined using spatial coordinates would be necessary for exact location)"
  ],
  "opportunity_regions": [
    "The high-density cells in the density grid ([14, 0, 0], [0, 12, 12], [14, 0, 14]) suggest potential opportunity regions. These areas might contain clusters of nodes that can be connected in advantageous ways.",
    "Regions with short edges, especially if they connect to the high-density regions.",
    "As the elite summary suggests node '0' is fixed, the connected nodes to it may be opportunistic"
  ],
  "evolution_phase": "Early exploration phase.",
  "evolution_direction": "The algorithm is currently in the exploration phase. The wide range of costs and moderate diversity suggest it's crucial to maintain exploration while gently encouraging convergence towards potentially promising regions. Considering the absence of any clustering information, more thorough clustering could benefit from the overall understanding of the solution space."
}
```
2025-06-24 10:27:36,111 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-24 10:27:36,111 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'The wide range in edge lengths (edge_len_stats: [10.0, 1075.0, 1900.0, 2469.0, 3032.0]) and a significant long_edge_ratio (0.249) suggest a moderately rugged search space. The presence of both very short and very long edges indicates potential for significant cost changes with small changes in the tour.', 'modality': 'Without more evolution information, it is hard to assess modality precisely, but the high standard deviation of cost (std 43044.01290644263) implies multiple local optima of varying quality. The initial population may be exploring a multi-modal landscape.', 'deceptiveness': "The presence of long edges may be deceptive if simply removing them doesn't necessarily lead to good solutions, as re-connecting the tour might create new longer edges or other issues. Conversely, very short edges clustered together might appear beneficial locally but detrimental globally."}, 'population_state': {'coverage': 'The diversity is moderate (0.5279461279461279). Given the density grid showing concentrations in specific cells, the population is not exploring all areas of the search space evenly. It appears concentrated in the corners and possibly a central band.', 'convergence': "The convergence is very low (0.0), indicating that the population is still exploring and hasn't started to coalesce around a specific region of the search space significantly."}, 'difficult_regions': ['Potential difficult regions include corridors between the high-density cells in the density grid (e.g., between [14,0,0] and [0,12,12], or between [0,12,12] and [14,0,14]).', 'Regions containing the longest edges (determined using spatial coordinates would be necessary for exact location)'], 'opportunity_regions': ['The high-density cells in the density grid ([14, 0, 0], [0, 12, 12], [14, 0, 14]) suggest potential opportunity regions. These areas might contain clusters of nodes that can be connected in advantageous ways.', 'Regions with short edges, especially if they connect to the high-density regions.', "As the elite summary suggests node '0' is fixed, the connected nodes to it may be opportunistic"], 'evolution_phase': 'Early exploration phase.', 'evolution_direction': "The algorithm is currently in the exploration phase. The wide range of costs and moderate diversity suggest it's crucial to maintain exploration while gently encouraging convergence towards potentially promising regions. Considering the absence of any clustering information, more thorough clustering could benefit from the overall understanding of the solution space."}
2025-06-24 10:27:36,111 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-24 10:27:36,111 - __main__ - INFO - 分析阶段完成
2025-06-24 10:27:36,111 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'The wide range in edge lengths (edge_len_stats: [10.0, 1075.0, 1900.0, 2469.0, 3032.0]) and a significant long_edge_ratio (0.249) suggest a moderately rugged search space. The presence of both very short and very long edges indicates potential for significant cost changes with small changes in the tour.', 'modality': 'Without more evolution information, it is hard to assess modality precisely, but the high standard deviation of cost (std 43044.01290644263) implies multiple local optima of varying quality. The initial population may be exploring a multi-modal landscape.', 'deceptiveness': "The presence of long edges may be deceptive if simply removing them doesn't necessarily lead to good solutions, as re-connecting the tour might create new longer edges or other issues. Conversely, very short edges clustered together might appear beneficial locally but detrimental globally."}, 'population_state': {'coverage': 'The diversity is moderate (0.5279461279461279). Given the density grid showing concentrations in specific cells, the population is not exploring all areas of the search space evenly. It appears concentrated in the corners and possibly a central band.', 'convergence': "The convergence is very low (0.0), indicating that the population is still exploring and hasn't started to coalesce around a specific region of the search space significantly."}, 'difficult_regions': ['Potential difficult regions include corridors between the high-density cells in the density grid (e.g., between [14,0,0] and [0,12,12], or between [0,12,12] and [14,0,14]).', 'Regions containing the longest edges (determined using spatial coordinates would be necessary for exact location)'], 'opportunity_regions': ['The high-density cells in the density grid ([14, 0, 0], [0, 12, 12], [14, 0, 14]) suggest potential opportunity regions. These areas might contain clusters of nodes that can be connected in advantageous ways.', 'Regions with short edges, especially if they connect to the high-density regions.', "As the elite summary suggests node '0' is fixed, the connected nodes to it may be opportunistic"], 'evolution_phase': 'Early exploration phase.', 'evolution_direction': "The algorithm is currently in the exploration phase. The wide range of costs and moderate diversity suggest it's crucial to maintain exploration while gently encouraging convergence towards potentially promising regions. Considering the absence of any clustering information, more thorough clustering could benefit from the overall understanding of the solution space."}
2025-06-24 10:27:36,111 - __main__ - INFO - 开始策略分配阶段
2025-06-24 10:27:36,111 - StrategyExpert - INFO - 开始策略分配分析
2025-06-24 10:27:36,111 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 1
- Population: 10 individuals, diversity=0.38461538461538464
- Best cost: 11479.0, Mean cost: 41332.1
- Search space: Ruggedness: The wide range in edge lengths (edge_len_stats: [10.0, 1075.0, 1900.0, 2469.0, 3032.0]) and a significant long_edge_ratio (0.249) suggest a moderately rugged search space. The presence of both very short and very long edges indicates potential for significant cost changes with small changes in the tour., Modality: Without more evolution information, it is hard to assess modality precisely, but the high standard deviation of cost (std 43044.01290644263) implies multiple local optima of varying quality. The initial population may be exploring a multi-modal landscape.
- Difficult regions: 2 regions
- Opportunity regions: 3 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early Exploration",
    "features": "The evolution is in its very early stages (iteration 0/5). The population cost, while showing a decrease in the mean...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-24 10:27:36,111 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-24 10:27:39,335 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "type": "guided_mutation",
      "mutation_rate": 0.3,
      "guide_weight": 0.4,
      "local_search_intensity": 0.1
    },
    "exploit": {
      "type": "local_search",
      "neighborhood_size": 5,
      "iterations": 10,
      "use_2_opt": true
    }
  }
}
```
2025-06-24 10:27:39,335 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-06-24 10:27:39,337 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-06-24 10:27:39,337 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "type": "guided_mutation",
      "mutation_rate": 0.3,
      "guide_weight": 0.4,
      "local_search_intensity": 0.1
    },
    "exploit": {
      "type": "local_search",
      "neighborhood_size": 5,
      "iterations": 10,
      "use_2_opt": true
    }
  }
}
```
2025-06-24 10:27:39,337 - __main__ - INFO - 策略分配阶段完成
2025-06-24 10:27:39,337 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-06-24 10:27:39,337 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "type": "guided_mutation",
      "mutation_rate": 0.3,
      "guide_weight": 0.4,
      "local_search_intensity": 0.1
    },
    "exploit": {
      "type": "local_search",
      "neighborhood_size": 5,
      "iterations": 10,
      "use_2_opt": true
    }
  }
}
```
2025-06-24 10:27:39,337 - __main__ - INFO - 开始进化阶段
2025-06-24 10:27:39,338 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-24 10:27:39,338 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:27:39,338 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [3, 4, 5] and ending with [0, 1, 2]
- Cost: 11479.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.38461538461538464
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:27:39,338 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:27:43,443 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    33, 28, 61, 52, 20, 1, 47, 14, 31, 50, 63, 6, 12, 45, 49, 38, 19, 57, 23, 41, 8, 55, 2, 10, 35, 54, 17, 43, 26, 0, 65, 36, 22, 40, 59, 4, 16, 30, 48, 64, 13, 46, 27, 5, 32, 11, 29, 44, 62, 18, 37, 24, 42, 60, 3, 9, 56, 21, 39, 58, 15, 34, 53, 7, 25, 51
  ],
  "modification_strategy": "Completely randomized the path to maximize diversity, while ensuring all TSP constraints are met (all nodes included exactly once and within valid range). This prioritizes exploration of unexplored regions at the potential expense of path cost, leveraging a high diversity weight.",
  "targeted_regions": "All unexplored regions, aiming for a globally diverse path."
}
```
2025-06-24 10:27:43,443 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:27:43,443 - ExplorationExpert - INFO - 探索路径生成完成，成本: 109846.0, 路径: [33, 28, 61, 52, 20, 1, 47, 14, 31, 50, 63, 6, 12, 45, 49, 38, 19, 57, 23, 41, 8, 55, 2, 10, 35, 54, 17, 43, 26, 0, 65, 36, 22, 40, 59, 4, 16, 30, 48, 64, 13, 46, 27, 5, 32, 11, 29, 44, 62, 18, 37, 24, 42, 60, 3, 9, 56, 21, 39, 58, 15, 34, 53, 7, 25, 51]
2025-06-24 10:27:43,443 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [33, 28, 61, 52, 20, 1, 47, 14, 31, 50, 63, 6, 12, 45, 49, 38, 19, 57, 23, 41, 8, 55, 2, 10, 35, 54, 17, 43, 26, 0, 65, 36, 22, 40, 59, 4, 16, 30, 48, 64, 13, 46, 27, 5, 32, 11, 29, 44, 62, 18, 37, 24, 42, 60, 3, 9, 56, 21, 39, 58, 15, 34, 53, 7, 25, 51], 'cur_cost': 109846.0}
2025-06-24 10:27:43,443 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-24 10:27:43,443 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:27:43,443 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 1, 2] and ending with [43, 42, 41]
- Cost: 15727.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.47384615384615386
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:27:43,447 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:27:47,573 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  "modification_strategy": "Reversed the order of nodes after starting with node 0 to introduce maximum diversity. Since a completely random shuffle might lead to highly suboptimal paths, this approach provides a structured but significant deviation from the original path, promoting exploration of entirely different connections between nodes.",
  "targeted_regions": "Primarily targeting regions unexplored by paths similar to the current one. The reversal aims to explore a broad range of new edge combinations rather than focusing on specific 'opportunity regions' which might be locally optimal but globally less effective given the diversity objective."
}
```
2025-06-24 10:27:47,573 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:27:47,575 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
2025-06-24 10:27:47,575 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 11479.0}
2025-06-24 10:27:47,575 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-24 10:27:47,575 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:27:47,576 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [5, 6, 7] and ending with [2, 1, 0]
- Cost: 11493.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.4723076923076923
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:27:47,576 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:27:52,410 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31
  ],
  "modification_strategy": "Generated a new path with a large block swap and reversal. Divided the original path into two halves and swapped their positions. This promotes a significant change in node sequence and enhances exploration of different areas within the TSP space.",
  "targeted_regions": "Focuses on significantly altering the overall path structure by shifting and reversing node order. This aims at exploring different clusters and connections within the TSP graph, potentially uncovering shorter paths by changing the traversal sequence of opportunity regions, targeting mainly unexplored regions."
}
```
2025-06-24 10:27:52,410 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:27:52,411 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]
2025-06-24 10:27:52,412 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31], 'cur_cost': 11479.0}
2025-06-24 10:27:52,412 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-24 10:27:52,412 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:27:52,412 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:27:52,412 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 109865.0
2025-06-24 10:27:52,913 - ExploitationExpert - INFO - res_population_num: 9
2025-06-24 10:27:52,913 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9528, 9528, 9541, 89460, 9521, 9521, 9521]
2025-06-24 10:27:52,913 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-24 10:27:52,916 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:27:52,917 - ExploitationExpert - INFO - populations: [{'tour': [33, 28, 61, 52, 20, 1, 47, 14, 31, 50, 63, 6, 12, 45, 49, 38, 19, 57, 23, 41, 8, 55, 2, 10, 35, 54, 17, 43, 26, 0, 65, 36, 22, 40, 59, 4, 16, 30, 48, 64, 13, 46, 27, 5, 32, 11, 29, 44, 62, 18, 37, 24, 42, 60, 3, 9, 56, 21, 39, 58, 15, 34, 53, 7, 25, 51], 'cur_cost': 109846.0}, {'tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 11479.0}, {'tour': [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31], 'cur_cost': 11479.0}, {'tour': array([56, 58, 52, 10,  7,  3, 28, 20, 32, 48, 22, 12, 44, 17, 39, 43, 51,
        5, 46, 61, 29, 33,  1, 63, 26, 60, 42, 62, 21, 50, 37, 47, 65, 41,
       11, 45,  0, 25, 16, 30, 57, 38, 40, 31, 36,  2,  4, 27,  6, 15, 14,
       64, 34, 59, 49, 35, 19, 54, 13, 18,  8, 23, 53,  9, 55, 24]), 'cur_cost': 109865.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32], 'cur_cost': 15356.0}, {'tour': array([58,  7, 35, 61, 38, 60, 65, 12, 52, 64,  4, 56, 45, 54,  1, 62, 32,
       25, 23, 51, 36, 49, 44, 17,  2, 63, 42, 47, 11, 43, 31, 40, 26, 55,
       18, 50, 15, 57, 48, 37, 24, 10, 13,  8,  9, 27, 22, 20, 53, 34, 14,
        3, 19,  5,  6, 29, 59, 41, 30, 33, 28, 21,  0, 46, 39, 16]), 'cur_cost': 104093.0}, {'tour': array([27, 61, 51, 55, 22, 25, 31, 30, 15, 26, 28, 21,  6, 34, 56, 36, 39,
       38,  2, 23, 57, 48, 50, 54, 65, 10, 13, 19, 20, 11, 44,  7, 60,  4,
       59, 14, 32, 46, 18, 52, 33, 53, 12, 41, 35,  0,  5,  3, 64, 47, 40,
        1, 45, 17, 49, 58,  9, 29,  8, 24, 62, 16, 42, 43, 37, 63]), 'cur_cost': 108331.0}, {'tour': array([ 1, 45, 40,  3, 34, 10, 16, 55, 58, 50,  8, 36, 64, 44, 62, 28,  4,
       56, 27, 18, 11, 17, 25, 32, 59, 38, 47, 24,  6,  9, 43, 14, 20, 46,
       33, 19, 52,  5, 65, 51, 63, 53, 42,  0, 35, 22, 41, 26, 30, 29, 49,
        7, 23, 21, 15, 48, 37, 13, 54, 31, 12,  2, 60, 39, 61, 57]), 'cur_cost': 108612.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}]
2025-06-24 10:27:52,918 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 10:27:52,918 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-24 10:27:52,919 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-24 10:27:52,919 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-24 10:27:52,919 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:27:52,919 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 1, 2] and ending with [63, 64, 65]
- Cost: 11479.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.56
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:27:52,920 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:27:56,421 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 33, 1, 34, 2, 35, 3, 36, 4, 37, 5, 38, 6, 39, 7, 40, 8, 41, 9, 42, 10, 43, 11, 44, 12, 45, 13, 46, 14, 47, 15, 48, 16, 49, 17, 50, 18, 51, 19, 52, 20, 53, 21, 54, 22, 55, 23, 56, 24, 57, 25, 58, 26, 59, 27, 60, 28, 61, 29, 62, 30, 63, 31, 64, 32, 65
  ],
  "modification_strategy": "Created a path with high diversity by interleaving nodes from the first half and second half of the node range. This introduces significant changes to the adjacencies compared to the original path, promoting exploration of new regions.",
  "targeted_regions": "Exploration of adjacencies between lower-numbered and higher-numbered nodes to assess potential shortcuts or alternative routes. Prioritizes diversity over immediate cost reduction."
}
```
2025-06-24 10:27:56,421 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:27:56,423 - ExplorationExpert - INFO - 探索路径生成完成，成本: 145254.0, 路径: [0, 33, 1, 34, 2, 35, 3, 36, 4, 37, 5, 38, 6, 39, 7, 40, 8, 41, 9, 42, 10, 43, 11, 44, 12, 45, 13, 46, 14, 47, 15, 48, 16, 49, 17, 50, 18, 51, 19, 52, 20, 53, 21, 54, 22, 55, 23, 56, 24, 57, 25, 58, 26, 59, 27, 60, 28, 61, 29, 62, 30, 63, 31, 64, 32, 65]
2025-06-24 10:27:56,423 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 33, 1, 34, 2, 35, 3, 36, 4, 37, 5, 38, 6, 39, 7, 40, 8, 41, 9, 42, 10, 43, 11, 44, 12, 45, 13, 46, 14, 47, 15, 48, 16, 49, 17, 50, 18, 51, 19, 52, 20, 53, 21, 54, 22, 55, 23, 56, 24, 57, 25, 58, 26, 59, 27, 60, 28, 61, 29, 62, 30, 63, 31, 64, 32, 65], 'cur_cost': 145254.0}
2025-06-24 10:27:56,423 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-24 10:27:56,423 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:27:56,424 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 1, 2] and ending with [34, 33, 32]
- Cost: 15356.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6338461538461538
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:27:56,424 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:28:00,325 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  "modification_strategy": "Reversed the initial path and swapped the original start with the original end to drastically change the path's direction and hopefully enter a new region. Since the diversity weight is high, a radical change is justified to explore more efficiently.",
  "targeted_regions": "All regions, aiming for global exploration rather than local refinement due to the high diversity weight."
}
```
2025-06-24 10:28:00,325 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:28:00,325 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1]
2025-06-24 10:28:00,325 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 11479.0}
2025-06-24 10:28:00,325 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-24 10:28:00,325 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:28:00,325 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:28:00,325 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 93773.0
2025-06-24 10:28:00,827 - ExploitationExpert - INFO - res_population_num: 12
2025-06-24 10:28:00,827 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9528, 9528, 9541, 89460, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-24 10:28:00,827 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-24 10:28:00,830 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:28:00,830 - ExploitationExpert - INFO - populations: [{'tour': [33, 28, 61, 52, 20, 1, 47, 14, 31, 50, 63, 6, 12, 45, 49, 38, 19, 57, 23, 41, 8, 55, 2, 10, 35, 54, 17, 43, 26, 0, 65, 36, 22, 40, 59, 4, 16, 30, 48, 64, 13, 46, 27, 5, 32, 11, 29, 44, 62, 18, 37, 24, 42, 60, 3, 9, 56, 21, 39, 58, 15, 34, 53, 7, 25, 51], 'cur_cost': 109846.0}, {'tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 11479.0}, {'tour': [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31], 'cur_cost': 11479.0}, {'tour': array([56, 58, 52, 10,  7,  3, 28, 20, 32, 48, 22, 12, 44, 17, 39, 43, 51,
        5, 46, 61, 29, 33,  1, 63, 26, 60, 42, 62, 21, 50, 37, 47, 65, 41,
       11, 45,  0, 25, 16, 30, 57, 38, 40, 31, 36,  2,  4, 27,  6, 15, 14,
       64, 34, 59, 49, 35, 19, 54, 13, 18,  8, 23, 53,  9, 55, 24]), 'cur_cost': 109865.0}, {'tour': [0, 33, 1, 34, 2, 35, 3, 36, 4, 37, 5, 38, 6, 39, 7, 40, 8, 41, 9, 42, 10, 43, 11, 44, 12, 45, 13, 46, 14, 47, 15, 48, 16, 49, 17, 50, 18, 51, 19, 52, 20, 53, 21, 54, 22, 55, 23, 56, 24, 57, 25, 58, 26, 59, 27, 60, 28, 61, 29, 62, 30, 63, 31, 64, 32, 65], 'cur_cost': 145254.0}, {'tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 11479.0}, {'tour': array([ 7, 30, 24, 44, 34,  2, 28,  1, 37, 39, 31, 52,  6,  8, 64, 29, 40,
        4, 22, 41, 19, 42, 16, 20, 21, 13, 33, 62,  3, 56, 55, 61, 48,  0,
       36, 45, 46, 25, 32, 50, 47, 58, 54, 18, 60, 15, 38, 26, 65, 53, 57,
       23,  5, 10, 27, 35,  9, 11, 17, 12, 49, 43, 14, 59, 51, 63]), 'cur_cost': 93773.0}, {'tour': array([27, 61, 51, 55, 22, 25, 31, 30, 15, 26, 28, 21,  6, 34, 56, 36, 39,
       38,  2, 23, 57, 48, 50, 54, 65, 10, 13, 19, 20, 11, 44,  7, 60,  4,
       59, 14, 32, 46, 18, 52, 33, 53, 12, 41, 35,  0,  5,  3, 64, 47, 40,
        1, 45, 17, 49, 58,  9, 29,  8, 24, 62, 16, 42, 43, 37, 63]), 'cur_cost': 108331.0}, {'tour': array([ 1, 45, 40,  3, 34, 10, 16, 55, 58, 50,  8, 36, 64, 44, 62, 28,  4,
       56, 27, 18, 11, 17, 25, 32, 59, 38, 47, 24,  6,  9, 43, 14, 20, 46,
       33, 19, 52,  5, 65, 51, 63, 53, 42,  0, 35, 22, 41, 26, 30, 29, 49,
        7, 23, 21, 15, 48, 37, 13, 54, 31, 12,  2, 60, 39, 61, 57]), 'cur_cost': 108612.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}]
2025-06-24 10:28:00,833 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 10:28:00,833 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-24 10:28:00,834 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-24 10:28:00,834 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-24 10:28:00,834 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:28:00,834 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [27, 61, 51] and ending with [43, 37, 63]
- Cost: 108331.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6384615384615384
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:28:00,835 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:28:04,536 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 43, 37, 63, 27, 61, 51, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 64, 65
  ],
  "modification_strategy": "Introduced a large block swap, moving the original starting sequence [27, 61, 51] to later in the path, and replacing it with a simpler initial sequence. This aims to explore a completely different path structure, leveraging the unexplored regions. Additionally, the original ending sequence [43, 37, 63] was moved to an earlier position to further diversify the path.",
  "targeted_regions": "Focused on exploring alternative starting and ending regions of the TSP tour, aiming to break away from the potentially locally optimal structure of the current path."
}
```
2025-06-24 10:28:04,536 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:28:04,536 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28909.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 43, 37, 63, 27, 61, 51, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 64, 65]
2025-06-24 10:28:04,536 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 43, 37, 63, 27, 61, 51, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 64, 65], 'cur_cost': 28909.0}
2025-06-24 10:28:04,536 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-24 10:28:04,536 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:28:04,536 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [1, 45, 40] and ending with [39, 61, 57]
- Cost: 108612.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.5753846153846154
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:28:04,539 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:28:08,268 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 1
  ],
  "modification_strategy": "Completely reversed the order of a simple initial path (0-65) to maximize diversity and explore a drastically different sequence. The path now starts at 0 and ends at 1, hopefully traversing new areas based on the problem's structure.",
  "targeted_regions": "Focused on exploring the initial and final segments of the original path in reverse to hopefully uncover new connections and potentially lower cost paths."
}
```
2025-06-24 10:28:08,268 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:28:08,269 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17673.0, 路径: [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 1]
2025-06-24 10:28:08,269 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 1], 'cur_cost': 17673.0}
2025-06-24 10:28:08,269 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-24 10:28:08,269 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:28:08,269 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:28:08,271 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 113340.0
2025-06-24 10:28:08,771 - ExploitationExpert - INFO - res_population_num: 12
2025-06-24 10:28:08,773 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9528, 9528, 9541, 89460, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-24 10:28:08,773 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-24 10:28:08,777 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:28:08,777 - ExploitationExpert - INFO - populations: [{'tour': [33, 28, 61, 52, 20, 1, 47, 14, 31, 50, 63, 6, 12, 45, 49, 38, 19, 57, 23, 41, 8, 55, 2, 10, 35, 54, 17, 43, 26, 0, 65, 36, 22, 40, 59, 4, 16, 30, 48, 64, 13, 46, 27, 5, 32, 11, 29, 44, 62, 18, 37, 24, 42, 60, 3, 9, 56, 21, 39, 58, 15, 34, 53, 7, 25, 51], 'cur_cost': 109846.0}, {'tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 11479.0}, {'tour': [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31], 'cur_cost': 11479.0}, {'tour': array([56, 58, 52, 10,  7,  3, 28, 20, 32, 48, 22, 12, 44, 17, 39, 43, 51,
        5, 46, 61, 29, 33,  1, 63, 26, 60, 42, 62, 21, 50, 37, 47, 65, 41,
       11, 45,  0, 25, 16, 30, 57, 38, 40, 31, 36,  2,  4, 27,  6, 15, 14,
       64, 34, 59, 49, 35, 19, 54, 13, 18,  8, 23, 53,  9, 55, 24]), 'cur_cost': 109865.0}, {'tour': [0, 33, 1, 34, 2, 35, 3, 36, 4, 37, 5, 38, 6, 39, 7, 40, 8, 41, 9, 42, 10, 43, 11, 44, 12, 45, 13, 46, 14, 47, 15, 48, 16, 49, 17, 50, 18, 51, 19, 52, 20, 53, 21, 54, 22, 55, 23, 56, 24, 57, 25, 58, 26, 59, 27, 60, 28, 61, 29, 62, 30, 63, 31, 64, 32, 65], 'cur_cost': 145254.0}, {'tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 11479.0}, {'tour': array([ 7, 30, 24, 44, 34,  2, 28,  1, 37, 39, 31, 52,  6,  8, 64, 29, 40,
        4, 22, 41, 19, 42, 16, 20, 21, 13, 33, 62,  3, 56, 55, 61, 48,  0,
       36, 45, 46, 25, 32, 50, 47, 58, 54, 18, 60, 15, 38, 26, 65, 53, 57,
       23,  5, 10, 27, 35,  9, 11, 17, 12, 49, 43, 14, 59, 51, 63]), 'cur_cost': 93773.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 43, 37, 63, 27, 61, 51, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 64, 65], 'cur_cost': 28909.0}, {'tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 1], 'cur_cost': 17673.0}, {'tour': array([27, 52, 51, 16, 34, 65, 40, 31, 10, 63, 26,  1, 24, 39, 49, 61, 30,
        6, 41, 42, 58, 59, 46, 36, 13, 32, 38,  0, 64, 18, 17, 22, 50, 25,
        4, 48,  3, 12, 35, 45, 62,  5, 47, 15, 43, 11, 57,  8, 19, 53, 28,
       33, 14, 37, 56, 23,  7, 21, 44, 60,  9, 20, 54, 55,  2, 29]), 'cur_cost': 113340.0}]
2025-06-24 10:28:08,778 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 10:28:08,779 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-24 10:28:08,779 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-24 10:28:08,779 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [33, 28, 61, 52, 20, 1, 47, 14, 31, 50, 63, 6, 12, 45, 49, 38, 19, 57, 23, 41, 8, 55, 2, 10, 35, 54, 17, 43, 26, 0, 65, 36, 22, 40, 59, 4, 16, 30, 48, 64, 13, 46, 27, 5, 32, 11, 29, 44, 62, 18, 37, 24, 42, 60, 3, 9, 56, 21, 39, 58, 15, 34, 53, 7, 25, 51], 'cur_cost': 109846.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31], 'cur_cost': 11479.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 33, 1, 34, 2, 35, 3, 36, 4, 37, 5, 38, 6, 39, 7, 40, 8, 41, 9, 42, 10, 43, 11, 44, 12, 45, 13, 46, 14, 47, 15, 48, 16, 49, 17, 50, 18, 51, 19, 52, 20, 53, 21, 54, 22, 55, 23, 56, 24, 57, 25, 58, 26, 59, 27, 60, 28, 61, 29, 62, 30, 63, 31, 64, 32, 65], 'cur_cost': 145254.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], 'cur_cost': 11479.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 43, 37, 63, 27, 61, 51, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 64, 65], 'cur_cost': 28909.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 1], 'cur_cost': 17673.0}}]
2025-06-24 10:28:08,780 - __main__ - INFO - 进化阶段完成
2025-06-24 10:28:08,780 - __main__ - INFO - 开始评估阶段
2025-06-24 10:28:08,780 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-24 10:28:08,782 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-24 10:28:08,782 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 1/5
- Population changes:
  * Cost: min 11479.0->11479.0, mean 41332.1->65309.7
  * Diversity: 0.38461538461538464->0.6538461538461539
  * Elite solutions: 4->3

## Strategy Performance:
- Explore: 7 individuals, success rate 71.4%, avg improvement -7663.14
- Exploit: 3 individuals, success rate 33.3%, avg improvement -62044.67
- Overall improvement: 0.0

## Historical Trends:
Last 1 iterations: costs [11479.0], diversity [0.5279461279461279]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-24 10:28:14,820 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "Initial Stage - High Exploration",
  "evolution_stage_features": [
    "Iteration 1/5 suggests the early stages of evolution.",
    "Significant increase in population cost (mean) implies exploration of a wider range of solutions, including less optimal ones.",
    "Increased diversity indicates the algorithm is actively searching for new areas in the solution space.",
    "Decrease in elite solutions suggests a disruption or exploration that temporarily displaced the best solutions.",
    "Cost remains stable at the minimum value."
  ],
  "strategy_effectiveness": {
    "explore": {
      "performance": "Effective",
      "success_rate": "71.4%",
      "avg_improvement": "-7663.14",
      "assessment": "Explore strategy is performing well with a high success rate, indicating it is finding new and potentially promising solutions, despite an average cost increase. This is acceptable during initial exploration phases."
    },
    "exploit": {
      "performance": "Ineffective",
      "success_rate": "33.3%",
      "avg_improvement": "-62044.67",
      "assessment": "Exploit strategy is underperforming significantly, suggesting that the algorithm is struggling to refine existing solutions. The extremely negative average improvement signifies that exploitation is often leading to worse solutions, possibly due to being stuck in local optima, or current best solution being sensitive to small changes."
    }
  },
  "balance_state": "Exploration-heavy, needs more exploitation focus",
  "balance_adjustment_needs": [
    "The significant cost increase with a minor decrease in elite solutions shows that the balance is heavily skewed towards exploration. The current exploit strategy is not efficient. While exploration is important initially, the algorithm needs to refine promising solutions.",
    "The substantial difference in 'avg improvement' between the exploration and exploitation suggests that there is a need to fine-tune the exploitation strategy to be more effective.",
    "The diversity increase might eventually lead to finding a superior path, but at the moment, it is essential to improve local search around the best solutions found so far."
  ],
  "recommendations": {
    "strategy_adjustment": "Increase the number of individuals dedicated to the 'exploit' strategy in the next iteration. Experiment with different exploitation methods - consider adaptive mutation rates or varying the intensity of local search to avoid getting stuck in local optima. Consider reducing the 'explore' count to balance them",
    "parameter_tuning": "Review the parameters used in the exploitation stage, such as mutation rates, step sizes, or selection pressure. Consider reducing mutation rates for exploitation to encourage refinement, and increasing it for exploration to discover new area.",
    "performance_monitoring": "Closely monitor the performance of the 'exploit' strategy after adjustments to ensure effectiveness. Pay attention to the diversity within the exploited population; if it collapses too quickly, re-introduce exploration elements.",
    "consider_hybrid_approaches": "Consider a hybrid approach where some individuals from the 'explore' strategy are used as starting points for the 'exploit' strategy.",
    "explore_new_techniques": "Investigate other optimization algorithms (e.g. Simulated Annealing, Tabu Search) as alternatives or additions to the exploit phase. These algorithms are good at escaping local optima."
  }
}
```
2025-06-24 10:28:14,845 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-24 10:28:14,845 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "Initial Stage - High Exploration",
  "evolution_stage_features": [
    "Iteration 1/5 suggests the early stages of evolution.",
    "Significant increase in population cost (mean) implies exploration of a wider range of solutions, including less optimal ones.",
    "Increased diversity indicates the algorithm is actively searching for new areas in the solution space.",
    "Decrease in elite solutions suggests a disruption or exploration that temporarily displaced the best solutions.",
    "Cost remains stable at the minimum value."
  ],
  "strategy_effectiveness": {
    "explore": {
      "performance": "Effective",
      "success_rate": "71.4%",
      "avg_improvement": "-7663.14",
      "assessment": "Explore strategy is performing well with a high success rate, indicating it is finding new and potentially promising solutions, despite an average cost increase. This is acceptable during initial exploration phases."
    },
    "exploit": {
      "performance": "Ineffective",
      "success_rate": "33.3%",
      "avg_improvement": "-62044.67",
      "assessment": "Exploit strategy is underperforming significantly, suggesting that the algorithm is struggling to refine existing solutions. The extremely negative average improvement signifies that exploitation is often leading to worse solutions, possibly due to being stuck in local optima, or current best solution being sensitive to small changes."
    }
  },
  "balance_state": "Exploration-heavy, needs more exploitation focus",
  "balance_adjustment_needs": [
    "The significant cost increase with a minor decrease in elite solutions shows that the balance is heavily skewed towards exploration. The current exploit strategy is not efficient. While exploration is important initially, the algorithm needs to refine promising solutions.",
    "The substantial difference in 'avg improvement' between the exploration and exploitation suggests that there is a need to fine-tune the exploitation strategy to be more effective.",
    "The diversity increase might eventually lead to finding a superior path, but at the moment, it is essential to improve local search around the best solutions found so far."
  ],
  "recommendations": {
    "strategy_adjustment": "Increase the number of individuals dedicated to the 'exploit' strategy in the next iteration. Experiment with different exploitation methods - consider adaptive mutation rates or varying the intensity of local search to avoid getting stuck in local optima. Consider reducing the 'explore' count to balance them",
    "parameter_tuning": "Review the parameters used in the exploitation stage, such as mutation rates, step sizes, or selection pressure. Consider reducing mutation rates for exploitation to encourage refinement, and increasing it for exploration to discover new area.",
    "performance_monitoring": "Closely monitor the performance of the 'exploit' strategy after adjustments to ensure effectiveness. Pay attention to the diversity within the exploited population; if it collapses too quickly, re-introduce exploration elements.",
    "consider_hybrid_approaches": "Consider a hybrid approach where some individuals from the 'explore' strategy are used as starting points for the 'exploit' strategy.",
    "explore_new_techniques": "Investigate other optimization algorithms (e.g. Simulated Annealing, Tabu Search) as alternatives or additions to the exploit phase. These algorithms are good at escaping local optima."
  }
}
```
2025-06-24 10:28:14,846 - __main__ - INFO - 评估阶段完成
2025-06-24 10:28:14,846 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "Initial Stage - High Exploration",
  "evolution_stage_features": [
    "Iteration 1/5 suggests the early stages of evolution.",
    "Significant increase in population cost (mean) implies exploration of a wider range of solutions, including less optimal ones.",
    "Increased diversity indicates the algorithm is actively searching for new areas in the solution space.",
    "Decrease in elite solutions suggests a disruption or exploration that temporarily displaced the best solutions.",
    "Cost remains stable at the minimum value."
  ],
  "strategy_effectiveness": {
    "explore": {
      "performance": "Effective",
      "success_rate": "71.4%",
      "avg_improvement": "-7663.14",
      "assessment": "Explore strategy is performing well with a high success rate, indicating it is finding new and potentially promising solutions, despite an average cost increase. This is acceptable during initial exploration phases."
    },
    "exploit": {
      "performance": "Ineffective",
      "success_rate": "33.3%",
      "avg_improvement": "-62044.67",
      "assessment": "Exploit strategy is underperforming significantly, suggesting that the algorithm is struggling to refine existing solutions. The extremely negative average improvement signifies that exploitation is often leading to worse solutions, possibly due to being stuck in local optima, or current best solution being sensitive to small changes."
    }
  },
  "balance_state": "Exploration-heavy, needs more exploitation focus",
  "balance_adjustment_needs": [
    "The significant cost increase with a minor decrease in elite solutions shows that the balance is heavily skewed towards exploration. The current exploit strategy is not efficient. While exploration is important initially, the algorithm needs to refine promising solutions.",
    "The substantial difference in 'avg improvement' between the exploration and exploitation suggests that there is a need to fine-tune the exploitation strategy to be more effective.",
    "The diversity increase might eventually lead to finding a superior path, but at the moment, it is essential to improve local search around the best solutions found so far."
  ],
  "recommendations": {
    "strategy_adjustment": "Increase the number of individuals dedicated to the 'exploit' strategy in the next iteration. Experiment with different exploitation methods - consider adaptive mutation rates or varying the intensity of local search to avoid getting stuck in local optima. Consider reducing the 'explore' count to balance them",
    "parameter_tuning": "Review the parameters used in the exploitation stage, such as mutation rates, step sizes, or selection pressure. Consider reducing mutation rates for exploitation to encourage refinement, and increasing it for exploration to discover new area.",
    "performance_monitoring": "Closely monitor the performance of the 'exploit' strategy after adjustments to ensure effectiveness. Pay attention to the diversity within the exploited population; if it collapses too quickly, re-introduce exploration elements.",
    "consider_hybrid_approaches": "Consider a hybrid approach where some individuals from the 'explore' strategy are used as starting points for the 'exploit' strategy.",
    "explore_new_techniques": "Investigate other optimization algorithms (e.g. Simulated Annealing, Tabu Search) as alternatives or additions to the exploit phase. These algorithms are good at escaping local optima."
  }
}
```
2025-06-24 10:28:14,846 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-24 10:28:14,848 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_1.pkl
2025-06-24 10:28:14,848 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-06-24 10:28:14,848 - __main__ - INFO - 开始分析阶段
2025-06-24 10:28:14,848 - StatsExpert - INFO - 开始统计分析
2025-06-24 10:28:14,865 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 145254.0, 'mean': 65309.7, 'std': 50758.56181778598}, 'diversity': 0.8498316498316498, 'clusters': {'clusters': 7, 'cluster_sizes': [1, 4, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-24 10:28:14,867 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 145254.0, 'mean': 65309.7, 'std': 50758.56181778598}, 'diversity_level': 0.8498316498316498, 'convergence_level': 0.0, 'clustering_info': {'clusters': 7, 'cluster_sizes': [1, 4, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-24 10:28:14,867 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-24 10:28:14,867 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-24 10:28:14,868 - PathExpert - INFO - 开始路径结构分析
2025-06-24 10:28:14,869 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-24 10:28:14,870 - PathExpert - INFO - 路径结构分析完成
2025-06-24 10:28:14,870 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-24 10:28:14,870 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-24 10:28:14,870 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-24 10:28:14,870 - EliteExpert - INFO - 开始精英解分析
2025-06-24 10:28:14,874 - EliteExpert - INFO - 精英解分析完成
2025-06-24 10:28:14,874 - __main__ - INFO - 精英专家分析报告: {'elite_count': 12, 'elite_common_features': {'common_edges': {'(0, 1)': 0.75, '(1, 7)': 0.75, '(7, 3)': 0.75, '(27, 37)': 0.75, '(37, 36)': 0.75, '(36, 26)': 0.75, '(26, 25)': 0.75, '(31, 24)': 0.75, '(24, 29)': 0.75, '(29, 32)': 0.75, '(28, 30)': 0.75, '(30, 35)': 0.75, '(35, 34)': 0.75, '(42, 50)': 0.75, '(44, 39)': 0.75, '(47, 46)': 0.75, '(23, 22)': 0.75, '(15, 14)': 0.75, '(60, 54)': 0.75, '(54, 57)': 0.75, '(57, 64)': 0.75, '(65, 52)': 0.75, '(52, 63)': 0.75, '(63, 61)': 0.75, '(55, 10)': 0.75, '(8, 2)': 0.75, '(2, 6)': 0.75, '(6, 4)': 0.75, '(5, 9)': 0.75, '(9, 11)': 0.75}, 'common_edge_ratio': 0.45454545454545453}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1958.0, 'avg_gap': 49124.28333333333}, 'structure_gap': {'unique_elite_edges': 173, 'unique_pop_edges': 444, 'common_edges': 59}}, 'elite_diversity': {'diversity_score': 0.3009641873278237}}
2025-06-24 10:28:14,874 - __main__ - INFO - 更新共享数据: elite_features
2025-06-24 10:28:14,874 - LandscapeExpert - INFO - 开始景观分析
2025-06-24 10:28:14,876 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-24 10:28:14,877 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 11479.0, mean 65309.7, max 145254.0, std 50758.56181778598
- diversity: 0.8498316498316498
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [11479.0, 11479.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-24 10:28:23,545 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "The search space appears rugged due to the wide range in `edge_len_stats` (10.0 to 3032.0) indicating highly variable edge lengths. A large difference between q1 and q3 (1075.0 and 2469.0 respectively) supports this.",
    "modality": "The `edge_len_stats` and `long_edge_ratio` of 0.249 suggest the presence of both short and long edges. This implies some degree of modality; solutions can vary significantly based on how these edges are combined.",
    "deceptiveness": "The `long_edge_ratio` suggests some potential for deceptiveness. Longer edges may lead to solutions that appear promising locally but are ultimately suboptimal globally."
  },
  "population_state": {
    "coverage": "The diversity of 0.849 suggests reasonably good population coverage, but could decrease soon given stagnation.",
    "convergence": "Convergence is low at 0.0. This implies that the population is not yet focusing on a particular region of the search space, though the best cost is unchanged for two iterations, so convergence *could* be starting."
  },
  "difficult_regions": [
    "Corridors corresponding to low-density cells in the density grid (e.g., cells with value 0). These areas may represent regions where improvements are hard to find due to the lack of node concentration. Specifically, the areas between cells [1, 0] and [1, 1] and [1, 2], and between [0,1], [0,2], and [2,1] and [2,2] may contain difficult paths.",
    "Regions containing long edges, as identified by `long_edge_ratio`. These could indicate areas where shortcuts or alternative connections are needed to improve the tour length but are not immediately obvious.",
    "Regions without any samples in `high_quality_edges_sample` or `common_subpaths_sample`. In the absence of those signals, we default to the above based on edge length and distribution."
  ],
  "opportunity_regions": [
    "High-density cells in the density grid, particularly those with a high node count (14). These regions may represent areas where nodes are clustered, creating opportunities to find short and efficient paths. Clusters around grid cells [0, 0], [2, 0] and [2, 2] might represent opportunities.",
    "Regions suggested by the `nn_median_dist` of 17.455. Short edges might lead to improvements. Prioritize nodes that are not currently included in high-quality edges.",
    "Regions that combine features of high-density areas and short distances. Since none are defined yet, we select based on density only"
  ],
  "evolution_phase": "Early Exploitation/Late Exploration. The low convergence but stagnant minimum cost suggest that the algorithm is struggling to improve beyond the initial solution found. High diversity suggests there's still ample space to search, but stagnation in min cost implies a need to either better explore, or slightly improve exploitation.",
  "evolution_direction": {
    "strategy": "Balance exploration and exploitation. The algorithm might be stuck in a local optimum.",
    "operator_suggestions": [
      "**Intensify Local Search:** Apply more aggressive local search operators (e.g., 2-opt, 3-opt) within the identified opportunity regions, particularly around high-density cells. Focus on improving the connections between nodes in these clusters.",
      "**Enhance Crossover:** Increase the crossover rate or use more disruptive crossover operators to introduce new genetic material into the population and escape the local optimum. Use a path relinking operator focused on the clusters.",
      "**Promote Exploration:** Introduce a small percentage of random restarts or mutations to encourage exploration of unexplored regions of the search space, especially in low-density areas. Focus on edge swapping to create longer routes.",
      "**Focus Edge Recombination:** Given the lack of edge information, focus crossover operators on combining short edges and avoiding disruptive long edges. Path relinking could be helpful here"
    ]
  }
}
```
2025-06-24 10:28:23,545 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-24 10:28:23,545 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'The search space appears rugged due to the wide range in `edge_len_stats` (10.0 to 3032.0) indicating highly variable edge lengths. A large difference between q1 and q3 (1075.0 and 2469.0 respectively) supports this.', 'modality': 'The `edge_len_stats` and `long_edge_ratio` of 0.249 suggest the presence of both short and long edges. This implies some degree of modality; solutions can vary significantly based on how these edges are combined.', 'deceptiveness': 'The `long_edge_ratio` suggests some potential for deceptiveness. Longer edges may lead to solutions that appear promising locally but are ultimately suboptimal globally.'}, 'population_state': {'coverage': 'The diversity of 0.849 suggests reasonably good population coverage, but could decrease soon given stagnation.', 'convergence': 'Convergence is low at 0.0. This implies that the population is not yet focusing on a particular region of the search space, though the best cost is unchanged for two iterations, so convergence *could* be starting.'}, 'difficult_regions': ['Corridors corresponding to low-density cells in the density grid (e.g., cells with value 0). These areas may represent regions where improvements are hard to find due to the lack of node concentration. Specifically, the areas between cells [1, 0] and [1, 1] and [1, 2], and between [0,1], [0,2], and [2,1] and [2,2] may contain difficult paths.', 'Regions containing long edges, as identified by `long_edge_ratio`. These could indicate areas where shortcuts or alternative connections are needed to improve the tour length but are not immediately obvious.', 'Regions without any samples in `high_quality_edges_sample` or `common_subpaths_sample`. In the absence of those signals, we default to the above based on edge length and distribution.'], 'opportunity_regions': ['High-density cells in the density grid, particularly those with a high node count (14). These regions may represent areas where nodes are clustered, creating opportunities to find short and efficient paths. Clusters around grid cells [0, 0], [2, 0] and [2, 2] might represent opportunities.', 'Regions suggested by the `nn_median_dist` of 17.455. Short edges might lead to improvements. Prioritize nodes that are not currently included in high-quality edges.', 'Regions that combine features of high-density areas and short distances. Since none are defined yet, we select based on density only'], 'evolution_phase': "Early Exploitation/Late Exploration. The low convergence but stagnant minimum cost suggest that the algorithm is struggling to improve beyond the initial solution found. High diversity suggests there's still ample space to search, but stagnation in min cost implies a need to either better explore, or slightly improve exploitation.", 'evolution_direction': {'strategy': 'Balance exploration and exploitation. The algorithm might be stuck in a local optimum.', 'operator_suggestions': ['**Intensify Local Search:** Apply more aggressive local search operators (e.g., 2-opt, 3-opt) within the identified opportunity regions, particularly around high-density cells. Focus on improving the connections between nodes in these clusters.', '**Enhance Crossover:** Increase the crossover rate or use more disruptive crossover operators to introduce new genetic material into the population and escape the local optimum. Use a path relinking operator focused on the clusters.', '**Promote Exploration:** Introduce a small percentage of random restarts or mutations to encourage exploration of unexplored regions of the search space, especially in low-density areas. Focus on edge swapping to create longer routes.', '**Focus Edge Recombination:** Given the lack of edge information, focus crossover operators on combining short edges and avoiding disruptive long edges. Path relinking could be helpful here']}}
2025-06-24 10:28:23,545 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-24 10:28:23,545 - __main__ - INFO - 分析阶段完成
2025-06-24 10:28:23,545 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'The search space appears rugged due to the wide range in `edge_len_stats` (10.0 to 3032.0) indicating highly variable edge lengths. A large difference between q1 and q3 (1075.0 and 2469.0 respectively) supports this.', 'modality': 'The `edge_len_stats` and `long_edge_ratio` of 0.249 suggest the presence of both short and long edges. This implies some degree of modality; solutions can vary significantly based on how these edges are combined.', 'deceptiveness': 'The `long_edge_ratio` suggests some potential for deceptiveness. Longer edges may lead to solutions that appear promising locally but are ultimately suboptimal globally.'}, 'population_state': {'coverage': 'The diversity of 0.849 suggests reasonably good population coverage, but could decrease soon given stagnation.', 'convergence': 'Convergence is low at 0.0. This implies that the population is not yet focusing on a particular region of the search space, though the best cost is unchanged for two iterations, so convergence *could* be starting.'}, 'difficult_regions': ['Corridors corresponding to low-density cells in the density grid (e.g., cells with value 0). These areas may represent regions where improvements are hard to find due to the lack of node concentration. Specifically, the areas between cells [1, 0] and [1, 1] and [1, 2], and between [0,1], [0,2], and [2,1] and [2,2] may contain difficult paths.', 'Regions containing long edges, as identified by `long_edge_ratio`. These could indicate areas where shortcuts or alternative connections are needed to improve the tour length but are not immediately obvious.', 'Regions without any samples in `high_quality_edges_sample` or `common_subpaths_sample`. In the absence of those signals, we default to the above based on edge length and distribution.'], 'opportunity_regions': ['High-density cells in the density grid, particularly those with a high node count (14). These regions may represent areas where nodes are clustered, creating opportunities to find short and efficient paths. Clusters around grid cells [0, 0], [2, 0] and [2, 2] might represent opportunities.', 'Regions suggested by the `nn_median_dist` of 17.455. Short edges might lead to improvements. Prioritize nodes that are not currently included in high-quality edges.', 'Regions that combine features of high-density areas and short distances. Since none are defined yet, we select based on density only'], 'evolution_phase': "Early Exploitation/Late Exploration. The low convergence but stagnant minimum cost suggest that the algorithm is struggling to improve beyond the initial solution found. High diversity suggests there's still ample space to search, but stagnation in min cost implies a need to either better explore, or slightly improve exploitation.", 'evolution_direction': {'strategy': 'Balance exploration and exploitation. The algorithm might be stuck in a local optimum.', 'operator_suggestions': ['**Intensify Local Search:** Apply more aggressive local search operators (e.g., 2-opt, 3-opt) within the identified opportunity regions, particularly around high-density cells. Focus on improving the connections between nodes in these clusters.', '**Enhance Crossover:** Increase the crossover rate or use more disruptive crossover operators to introduce new genetic material into the population and escape the local optimum. Use a path relinking operator focused on the clusters.', '**Promote Exploration:** Introduce a small percentage of random restarts or mutations to encourage exploration of unexplored regions of the search space, especially in low-density areas. Focus on edge swapping to create longer routes.', '**Focus Edge Recombination:** Given the lack of edge information, focus crossover operators on combining short edges and avoiding disruptive long edges. Path relinking could be helpful here']}}
2025-06-24 10:28:23,549 - __main__ - INFO - 开始策略分配阶段
2025-06-24 10:28:23,549 - StrategyExpert - INFO - 开始策略分配分析
2025-06-24 10:28:23,550 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 2
- Population: 10 individuals, diversity=0.6538461538461539
- Best cost: 11479.0, Mean cost: 65309.7
- Search space: Ruggedness: The search space appears rugged due to the wide range in `edge_len_stats` (10.0 to 3032.0) indicating highly variable edge lengths. A large difference between q1 and q3 (1075.0 and 2469.0 respectively) supports this., Modality: The `edge_len_stats` and `long_edge_ratio` of 0.249 suggest the presence of both short and long edges. This implies some degree of modality; solutions can vary significantly based on how these edges are combined.
- Difficult regions: 3 regions
- Opportunity regions: 3 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": "Initial Stage - High Exploration",
  "evolution_stage_features": [
    "Iteration 1/5 suggests the early stages of evolution.",
    "Significant increase in population ...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-24 10:28:23,550 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-24 10:28:26,438 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.8,
      "crossover_rate": 0.9,
      "perturbation_strength": 0.6
    },
    "exploit": {
      "mutation_rate": 0.2,
      "crossover_rate": 0.6,
      "local_search_radius": 0.3
    }
  }
}
```
2025-06-24 10:28:26,438 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-06-24 10:28:26,438 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-06-24 10:28:26,438 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.8,
      "crossover_rate": 0.9,
      "perturbation_strength": 0.6
    },
    "exploit": {
      "mutation_rate": 0.2,
      "crossover_rate": 0.6,
      "local_search_radius": 0.3
    }
  }
}
```
2025-06-24 10:28:26,438 - __main__ - INFO - 策略分配阶段完成
2025-06-24 10:28:26,438 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore']
2025-06-24 10:28:26,438 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.8,
      "crossover_rate": 0.9,
      "perturbation_strength": 0.6
    },
    "exploit": {
      "mutation_rate": 0.2,
      "crossover_rate": 0.6,
      "local_search_radius": 0.3
    }
  }
}
```
2025-06-24 10:28:26,438 - __main__ - INFO - 开始进化阶段
2025-06-24 10:28:26,438 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-24 10:28:26,438 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:28:26,438 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [33, 28, 61] and ending with [7, 25, 51]
- Cost: 109846.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6538461538461539
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:28:26,438 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:28:29,967 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Completely randomized path to maximize diversity, which increases the chance of discovering unexplored territories. This approach prioritizes path variation and potentially opens new sections for exploration at the expense of a higher initial cost.",
  "targeted_regions": "Exploration of the entire solution space by creating a random and likely novel path."
}
```
2025-06-24 10:28:29,967 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:28:29,967 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-24 10:28:29,967 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-24 10:28:29,967 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-24 10:28:29,970 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:28:29,970 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 65, 64] and ending with [3, 2, 1]
- Cost: 11479.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.5738461538461539
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:28:29,970 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:28:33,324 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39
  ],
  "modification_strategy": "Completely reverse and shift a large segment of the original path to maximize diversity. This prioritizes exploration of different node sequences, given the high diversity weight and ample unexplored regions.",
  "targeted_regions": "Primarily explores regions with nodes 40-65 first, then 0-39. Shifts the focus entirely from the start of the original path to a different region."
}
```
2025-06-24 10:28:33,324 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:28:33,324 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]
2025-06-24 10:28:33,324 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39], 'cur_cost': 11479.0}
2025-06-24 10:28:33,324 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-24 10:28:33,324 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:28:33,324 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:28:33,324 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 99215.0
2025-06-24 10:28:33,328 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：5,跳过局部搜索
2025-06-24 10:28:33,328 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-24 10:28:33,328 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([53, 62,  4,  9, 59, 58, 45,  1, 44, 18,  5, 23, 49, 28, 26, 35, 12,
       24, 57, 13, 65, 33, 34, 40, 21, 43, 14,  8, 52, 50, 25, 20, 10, 19,
       51,  7, 47, 37, 46, 39, 36, 64, 29, 38, 41, 31, 16, 11, 48, 30, 42,
        0,  3, 63, 54, 32,  6, 15, 27, 56, 61, 22, 17, 60,  2, 55]), 'cur_cost': 99215.0}
2025-06-24 10:28:33,328 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-24 10:28:33,328 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:28:33,328 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [56, 58, 52] and ending with [9, 55, 24]
- Cost: 109865.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6507692307692308
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:28:33,330 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:28:37,286 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    3, 63, 17, 5, 29, 60, 40, 46, 2, 57, 19, 36, 48, 13, 27, 23, 38, 32, 62, 11, 41, 37, 45, 65, 49, 4, 51, 61, 15, 8, 25, 30, 20, 54, 10, 47, 59, 28, 50, 22, 1, 34, 14, 39, 56, 58, 52, 0, 7, 33, 26, 43, 16, 9, 55, 24, 6, 12, 18, 44, 35, 53, 21, 31, 42, 64],
  "modification_strategy": "Implemented a partially randomized path generation approach. The path is constructed by randomly selecting nodes while ensuring that each node is visited exactly once. The initial segment was seeded with a few random nodes to promote exploration in previously unexplored regions. Subsequent node selections were biased towards nodes that are far away from the current path to increase diversity and to hit the opportunity regions.",
  "targeted_regions": "Focused on exploring regions hinted at by the 'opportunity regions' context. Specifically, the randomization aims to generate segments which will traverse these regions."
}
```
2025-06-24 10:28:37,286 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:28:37,287 - ExplorationExpert - INFO - 探索路径生成完成，成本: 113162.0, 路径: [3, 63, 17, 5, 29, 60, 40, 46, 2, 57, 19, 36, 48, 13, 27, 23, 38, 32, 62, 11, 41, 37, 45, 65, 49, 4, 51, 61, 15, 8, 25, 30, 20, 54, 10, 47, 59, 28, 50, 22, 1, 34, 14, 39, 56, 58, 52, 0, 7, 33, 26, 43, 16, 9, 55, 24, 6, 12, 18, 44, 35, 53, 21, 31, 42, 64]
2025-06-24 10:28:37,287 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [3, 63, 17, 5, 29, 60, 40, 46, 2, 57, 19, 36, 48, 13, 27, 23, 38, 32, 62, 11, 41, 37, 45, 65, 49, 4, 51, 61, 15, 8, 25, 30, 20, 54, 10, 47, 59, 28, 50, 22, 1, 34, 14, 39, 56, 58, 52, 0, 7, 33, 26, 43, 16, 9, 55, 24, 6, 12, 18, 44, 35, 53, 21, 31, 42, 64], 'cur_cost': 113162.0}
2025-06-24 10:28:37,287 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-24 10:28:37,287 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:28:37,287 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 33, 1] and ending with [64, 32, 65]
- Cost: 145254.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6523076923076923
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:28:37,289 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:28:40,598 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
    65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32
  ],
  "modification_strategy": "Reversed the path from node 31 to node 65 to drastically alter the traversal order and explore a different region of the solution space. This creates a high degree of diversity compared to the original path.",
  "targeted_regions": "The region connecting node 31 and node 65, in reverse order, effectively inverting a significant portion of the original path's trajectory."
}
```
2025-06-24 10:28:40,599 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:28:40,600 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15356.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32]
2025-06-24 10:28:40,600 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32], 'cur_cost': 15356.0}
2025-06-24 10:28:40,600 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-24 10:28:40,600 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:28:40,601 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:28:40,601 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 99318.0
2025-06-24 10:28:40,601 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：5,跳过局部搜索
2025-06-24 10:28:40,601 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-24 10:28:40,601 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([30, 44, 56, 53, 18, 63, 28, 27, 62,  8, 39,  6, 57, 23, 17, 40, 49,
       54, 59, 10, 58, 11,  5, 35, 43, 22, 12, 60, 34, 14, 25, 33,  1, 50,
        3, 48, 41, 20, 46, 65, 24, 36, 29,  7, 52, 45, 19, 32, 21, 37, 47,
       15,  2, 31, 64, 61, 38, 42, 26, 55,  4,  9, 16, 13,  0, 51]), 'cur_cost': 99318.0}
2025-06-24 10:28:40,601 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-24 10:28:40,601 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:28:40,601 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [7, 30, 24] and ending with [59, 51, 63]
- Cost: 93773.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6538461538461539
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:28:40,603 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:28:44,651 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    6,
    15,
    2,
    56,
    42,
    61,
    3,
    38,
    19,
    4,
    47,
    21,
    5,
    28,
    10,
    35,
    1,
    20,
    50,
    11,
    40,
    26,
    65,
    33,
    8,
    54,
    17,
    45,
    63,
    23,
    57,
    36,
    13,
    60,
    48,
    39,
    0,
    52,
    29,
    14,
    41,
    27,
    58,
    37,
    9,
    55,
    16,
    44,
    34,
    12,
    49,
    25,
    53,
    22,
    43,
    62,
    31,
    7,
    30,
    24,
    18,
    46,
    64,
    51,
    32,
    59
  ],
  "modification_strategy": "Implemented a combination of inversion and random swaps to introduce diversity. The path was segmented into three sections. The middle section (nodes 10-55) was inverted. Then, 5 random swaps were performed on the whole path to further diversify the route. This balances exploration with maintaining some structure for reasonable quality.",
  "targeted_regions": "Focused on exploring regions previously less visited by introducing inversions to the original path and then applying random swaps to explore adjacent node relations in those inverted regions. Also, the random swaps helped explore connection options across the entire graph, including those in the opportunity regions."
}
```
2025-06-24 10:28:44,652 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:28:44,652 - ExplorationExpert - INFO - 探索路径生成完成，成本: 127136.0, 路径: [6, 15, 2, 56, 42, 61, 3, 38, 19, 4, 47, 21, 5, 28, 10, 35, 1, 20, 50, 11, 40, 26, 65, 33, 8, 54, 17, 45, 63, 23, 57, 36, 13, 60, 48, 39, 0, 52, 29, 14, 41, 27, 58, 37, 9, 55, 16, 44, 34, 12, 49, 25, 53, 22, 43, 62, 31, 7, 30, 24, 18, 46, 64, 51, 32, 59]
2025-06-24 10:28:44,652 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [6, 15, 2, 56, 42, 61, 3, 38, 19, 4, 47, 21, 5, 28, 10, 35, 1, 20, 50, 11, 40, 26, 65, 33, 8, 54, 17, 45, 63, 23, 57, 36, 13, 60, 48, 39, 0, 52, 29, 14, 41, 27, 58, 37, 9, 55, 16, 44, 34, 12, 49, 25, 53, 22, 43, 62, 31, 7, 30, 24, 18, 46, 64, 51, 32, 59], 'cur_cost': 127136.0}
2025-06-24 10:28:44,652 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-24 10:28:44,652 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:28:44,652 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 1, 2] and ending with [62, 64, 65]
- Cost: 28909.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6569230769230769
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:28:44,652 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:28:48,611 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    33,
    22,
    11,
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65
  ],
  "modification_strategy": "Reversed the order of the initial sequence of nodes (0-11), moved node 33, 22, and 11 to the beginning of the path to explore a new starting area. This significantly alters the path's initial direction, promoting diversity. The rest of the path maintains a sequential ordering to ensure completeness and validity.",
  "targeted_regions": "Early portion of the path (nodes 0-11) and the starting area defined by nodes 33, 22, and 11. We aim to explore different connections from this new starting configuration."
}
```
2025-06-24 10:28:48,611 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:28:48,612 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15820.0, 路径: [33, 22, 11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-24 10:28:48,613 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [33, 22, 11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15820.0}
2025-06-24 10:28:48,613 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-24 10:28:48,613 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:28:48,613 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:28:48,613 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 106595.0
2025-06-24 10:28:49,114 - ExploitationExpert - INFO - res_population_num: 13
2025-06-24 10:28:49,114 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9528, 9528, 9541, 89460, 9521]
2025-06-24 10:28:49,115 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-24 10:28:49,120 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:28:49,121 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39], 'cur_cost': 11479.0}, {'tour': array([53, 62,  4,  9, 59, 58, 45,  1, 44, 18,  5, 23, 49, 28, 26, 35, 12,
       24, 57, 13, 65, 33, 34, 40, 21, 43, 14,  8, 52, 50, 25, 20, 10, 19,
       51,  7, 47, 37, 46, 39, 36, 64, 29, 38, 41, 31, 16, 11, 48, 30, 42,
        0,  3, 63, 54, 32,  6, 15, 27, 56, 61, 22, 17, 60,  2, 55]), 'cur_cost': 99215.0}, {'tour': [3, 63, 17, 5, 29, 60, 40, 46, 2, 57, 19, 36, 48, 13, 27, 23, 38, 32, 62, 11, 41, 37, 45, 65, 49, 4, 51, 61, 15, 8, 25, 30, 20, 54, 10, 47, 59, 28, 50, 22, 1, 34, 14, 39, 56, 58, 52, 0, 7, 33, 26, 43, 16, 9, 55, 24, 6, 12, 18, 44, 35, 53, 21, 31, 42, 64], 'cur_cost': 113162.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32], 'cur_cost': 15356.0}, {'tour': array([30, 44, 56, 53, 18, 63, 28, 27, 62,  8, 39,  6, 57, 23, 17, 40, 49,
       54, 59, 10, 58, 11,  5, 35, 43, 22, 12, 60, 34, 14, 25, 33,  1, 50,
        3, 48, 41, 20, 46, 65, 24, 36, 29,  7, 52, 45, 19, 32, 21, 37, 47,
       15,  2, 31, 64, 61, 38, 42, 26, 55,  4,  9, 16, 13,  0, 51]), 'cur_cost': 99318.0}, {'tour': [6, 15, 2, 56, 42, 61, 3, 38, 19, 4, 47, 21, 5, 28, 10, 35, 1, 20, 50, 11, 40, 26, 65, 33, 8, 54, 17, 45, 63, 23, 57, 36, 13, 60, 48, 39, 0, 52, 29, 14, 41, 27, 58, 37, 9, 55, 16, 44, 34, 12, 49, 25, 53, 22, 43, 62, 31, 7, 30, 24, 18, 46, 64, 51, 32, 59], 'cur_cost': 127136.0}, {'tour': [33, 22, 11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15820.0}, {'tour': array([39, 18, 59, 12,  1, 30,  4, 22, 64, 10,  0, 44, 42, 25, 19, 57, 48,
        3, 65, 16, 29,  8, 34, 37,  6, 14, 35, 61, 47, 13, 23, 53, 20, 11,
        9, 41, 55, 45, 40, 21, 28, 38, 54, 27, 36, 52, 56, 46, 15, 58, 63,
       32, 33, 51, 49,  7, 60, 62, 31, 43, 26, 17, 24, 50,  2,  5]), 'cur_cost': 106595.0}, {'tour': array([27, 52, 51, 16, 34, 65, 40, 31, 10, 63, 26,  1, 24, 39, 49, 61, 30,
        6, 41, 42, 58, 59, 46, 36, 13, 32, 38,  0, 64, 18, 17, 22, 50, 25,
        4, 48,  3, 12, 35, 45, 62,  5, 47, 15, 43, 11, 57,  8, 19, 53, 28,
       33, 14, 37, 56, 23,  7, 21, 44, 60,  9, 20, 54, 55,  2, 29]), 'cur_cost': 113340.0}]
2025-06-24 10:28:49,122 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 10:28:49,122 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 9, 'skip_rate': 0.2222222222222222, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 33, 'cache_hit_rate': 0.0, 'cache_size': 33}}
2025-06-24 10:28:49,122 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-24 10:28:49,124 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-24 10:28:49,124 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:28:49,124 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [27, 52, 51] and ending with [55, 2, 29]
- Cost: 113340.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6553846153846153
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:28:49,127 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:28:52,821 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27],
  "modification_strategy": "Reversed a large section of the original path in order to introduce significant diversity. This effectively inverts a large portion, exploring a different traversal order, while still visiting all nodes.",
  "targeted_regions": "The entire path, by reversing a large chunk we effectively revisit all regions but from different angles."
}
```
2025-06-24 10:28:52,822 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:28:52,823 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15272.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27]
2025-06-24 10:28:52,823 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}
2025-06-24 10:28:52,823 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([53, 62,  4,  9, 59, 58, 45,  1, 44, 18,  5, 23, 49, 28, 26, 35, 12,
       24, 57, 13, 65, 33, 34, 40, 21, 43, 14,  8, 52, 50, 25, 20, 10, 19,
       51,  7, 47, 37, 46, 39, 36, 64, 29, 38, 41, 31, 16, 11, 48, 30, 42,
        0,  3, 63, 54, 32,  6, 15, 27, 56, 61, 22, 17, 60,  2, 55]), 'cur_cost': 99215.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [3, 63, 17, 5, 29, 60, 40, 46, 2, 57, 19, 36, 48, 13, 27, 23, 38, 32, 62, 11, 41, 37, 45, 65, 49, 4, 51, 61, 15, 8, 25, 30, 20, 54, 10, 47, 59, 28, 50, 22, 1, 34, 14, 39, 56, 58, 52, 0, 7, 33, 26, 43, 16, 9, 55, 24, 6, 12, 18, 44, 35, 53, 21, 31, 42, 64], 'cur_cost': 113162.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32], 'cur_cost': 15356.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([30, 44, 56, 53, 18, 63, 28, 27, 62,  8, 39,  6, 57, 23, 17, 40, 49,
       54, 59, 10, 58, 11,  5, 35, 43, 22, 12, 60, 34, 14, 25, 33,  1, 50,
        3, 48, 41, 20, 46, 65, 24, 36, 29,  7, 52, 45, 19, 32, 21, 37, 47,
       15,  2, 31, 64, 61, 38, 42, 26, 55,  4,  9, 16, 13,  0, 51]), 'cur_cost': 99318.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [6, 15, 2, 56, 42, 61, 3, 38, 19, 4, 47, 21, 5, 28, 10, 35, 1, 20, 50, 11, 40, 26, 65, 33, 8, 54, 17, 45, 63, 23, 57, 36, 13, 60, 48, 39, 0, 52, 29, 14, 41, 27, 58, 37, 9, 55, 16, 44, 34, 12, 49, 25, 53, 22, 43, 62, 31, 7, 30, 24, 18, 46, 64, 51, 32, 59], 'cur_cost': 127136.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [33, 22, 11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15820.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}}]
2025-06-24 10:28:52,824 - __main__ - INFO - 进化阶段完成
2025-06-24 10:28:52,824 - __main__ - INFO - 开始评估阶段
2025-06-24 10:28:52,824 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-24 10:28:52,825 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-24 10:28:52,825 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 2/5
- Population changes:
  * Cost: min 11479.0->11479.0, mean 65309.7->61483.2
  * Diversity: 0.6538461538461539->0.5784615384615385
  * Elite solutions: 3->2

## Strategy Performance:
- Explore: 7 individuals, success rate 57.1%, avg improvement 43251.71
- Exploit: 3 individuals, success rate 0.0%, avg improvement -88165.67
- Overall improvement: 0.0

## Historical Trends:
Last 2 iterations: costs [11479.0, 11479.0], diversity [0.5279461279461279, 0.8498316498316498]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-24 10:28:58,592 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Stagnation/Exploitation",
    "features": "The minimum cost has plateaued at 11479.0 for two consecutive iterations. The diversity has decreased, suggesting the population is converging. The number of elite solutions has slightly decreased, which could be a warning sign that the population is over-specializing and losing potentially valuable genetic material. The historical diversity trend shows fluctuation, indicating the algorithm has previously experienced shifts in population characteristics, but the current decrease after a higher peak suggests a potential loss of variability."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "57.1%",
      "avg_improvement": "43251.71",
      "assessment": "Exploration is showing moderate success and contributing positively to finding better solutions. The improvement suggests exploring new regions of the search space is still beneficial."
    },
    "exploit": {
      "success_rate": "0.0%",
      "avg_improvement": "-88165.67",
      "assessment": "Exploitation is currently failing, with a 0% success rate and significant negative average improvement. This indicates that intensifying search around existing solutions is leading to worse outcomes. This strongly suggests the algorithm is stuck in local optima and further intensification is detrimental."
    }
  },
  "balance_state": {
    "assessment": "The current balance is heavily skewed towards exploration, which is the only strategy providing positive contributions. However, the overall improvement is zero, suggesting that while exploration finds potentially better regions, these regions aren't resulting in overall superior solutions. The failure of exploitation suggests premature convergence or the selection process is not effective in refining the solutions discovered by exploration.",
    "adjustment_needs": "A shift back toward exploration is needed, but focusing on maintaining diversity during exploration, or adding a diversity-preserving mechanism."
  },
  "recommendations": {
    "exploration_focus": "Increase the number of individuals assigned to the Explore strategy to further investigate new regions of the search space. Consider using a different exploration technique (e.g., a larger mutation rate or a completely random solution) to escape the current local optima.",
    "exploitation_reassessment": "Re-evaluate the Exploitation strategy. If the current technique is consistently failing, try a different approach. Alternatively, temporarily reduce the number of individuals assigned to this strategy or disable it entirely to allow more resources for exploration.",
    "diversity_enhancement": "Implement a diversity-preserving mechanism, such as a niching method or a diversity-based selection criterion. The decreasing diversity is a concerning trend. Prioritize selecting individuals that are different from the rest of the population even if their initial cost is slightly higher, potentially helping to escape local optima.",
    "parameter_tuning": "Carefully review and tune the parameters used in both exploration and exploitation. Specifically, analyze the mutation rates, crossover probabilities, and the degree of exploitation to ensure they are appropriate for the current stage of the evolution. Monitor diversity closely after any parameter adjustment.",
     "elite_management": "Investigate the solutions deemed 'elite' and ensure they are indeed valuable. Review the criteria for elite selection to ensure it doesn't inadvertently promote solutions that lead to local optima."
  }
}
```
2025-06-24 10:28:58,615 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-24 10:28:58,621 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Stagnation/Exploitation",
    "features": "The minimum cost has plateaued at 11479.0 for two consecutive iterations. The diversity has decreased, suggesting the population is converging. The number of elite solutions has slightly decreased, which could be a warning sign that the population is over-specializing and losing potentially valuable genetic material. The historical diversity trend shows fluctuation, indicating the algorithm has previously experienced shifts in population characteristics, but the current decrease after a higher peak suggests a potential loss of variability."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "57.1%",
      "avg_improvement": "43251.71",
      "assessment": "Exploration is showing moderate success and contributing positively to finding better solutions. The improvement suggests exploring new regions of the search space is still beneficial."
    },
    "exploit": {
      "success_rate": "0.0%",
      "avg_improvement": "-88165.67",
      "assessment": "Exploitation is currently failing, with a 0% success rate and significant negative average improvement. This indicates that intensifying search around existing solutions is leading to worse outcomes. This strongly suggests the algorithm is stuck in local optima and further intensification is detrimental."
    }
  },
  "balance_state": {
    "assessment": "The current balance is heavily skewed towards exploration, which is the only strategy providing positive contributions. However, the overall improvement is zero, suggesting that while exploration finds potentially better regions, these regions aren't resulting in overall superior solutions. The failure of exploitation suggests premature convergence or the selection process is not effective in refining the solutions discovered by exploration.",
    "adjustment_needs": "A shift back toward exploration is needed, but focusing on maintaining diversity during exploration, or adding a diversity-preserving mechanism."
  },
  "recommendations": {
    "exploration_focus": "Increase the number of individuals assigned to the Explore strategy to further investigate new regions of the search space. Consider using a different exploration technique (e.g., a larger mutation rate or a completely random solution) to escape the current local optima.",
    "exploitation_reassessment": "Re-evaluate the Exploitation strategy. If the current technique is consistently failing, try a different approach. Alternatively, temporarily reduce the number of individuals assigned to this strategy or disable it entirely to allow more resources for exploration.",
    "diversity_enhancement": "Implement a diversity-preserving mechanism, such as a niching method or a diversity-based selection criterion. The decreasing diversity is a concerning trend. Prioritize selecting individuals that are different from the rest of the population even if their initial cost is slightly higher, potentially helping to escape local optima.",
    "parameter_tuning": "Carefully review and tune the parameters used in both exploration and exploitation. Specifically, analyze the mutation rates, crossover probabilities, and the degree of exploitation to ensure they are appropriate for the current stage of the evolution. Monitor diversity closely after any parameter adjustment.",
     "elite_management": "Investigate the solutions deemed 'elite' and ensure they are indeed valuable. Review the criteria for elite selection to ensure it doesn't inadvertently promote solutions that lead to local optima."
  }
}
```
2025-06-24 10:28:58,622 - __main__ - INFO - 评估阶段完成
2025-06-24 10:28:58,622 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Stagnation/Exploitation",
    "features": "The minimum cost has plateaued at 11479.0 for two consecutive iterations. The diversity has decreased, suggesting the population is converging. The number of elite solutions has slightly decreased, which could be a warning sign that the population is over-specializing and losing potentially valuable genetic material. The historical diversity trend shows fluctuation, indicating the algorithm has previously experienced shifts in population characteristics, but the current decrease after a higher peak suggests a potential loss of variability."
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "57.1%",
      "avg_improvement": "43251.71",
      "assessment": "Exploration is showing moderate success and contributing positively to finding better solutions. The improvement suggests exploring new regions of the search space is still beneficial."
    },
    "exploit": {
      "success_rate": "0.0%",
      "avg_improvement": "-88165.67",
      "assessment": "Exploitation is currently failing, with a 0% success rate and significant negative average improvement. This indicates that intensifying search around existing solutions is leading to worse outcomes. This strongly suggests the algorithm is stuck in local optima and further intensification is detrimental."
    }
  },
  "balance_state": {
    "assessment": "The current balance is heavily skewed towards exploration, which is the only strategy providing positive contributions. However, the overall improvement is zero, suggesting that while exploration finds potentially better regions, these regions aren't resulting in overall superior solutions. The failure of exploitation suggests premature convergence or the selection process is not effective in refining the solutions discovered by exploration.",
    "adjustment_needs": "A shift back toward exploration is needed, but focusing on maintaining diversity during exploration, or adding a diversity-preserving mechanism."
  },
  "recommendations": {
    "exploration_focus": "Increase the number of individuals assigned to the Explore strategy to further investigate new regions of the search space. Consider using a different exploration technique (e.g., a larger mutation rate or a completely random solution) to escape the current local optima.",
    "exploitation_reassessment": "Re-evaluate the Exploitation strategy. If the current technique is consistently failing, try a different approach. Alternatively, temporarily reduce the number of individuals assigned to this strategy or disable it entirely to allow more resources for exploration.",
    "diversity_enhancement": "Implement a diversity-preserving mechanism, such as a niching method or a diversity-based selection criterion. The decreasing diversity is a concerning trend. Prioritize selecting individuals that are different from the rest of the population even if their initial cost is slightly higher, potentially helping to escape local optima.",
    "parameter_tuning": "Carefully review and tune the parameters used in both exploration and exploitation. Specifically, analyze the mutation rates, crossover probabilities, and the degree of exploitation to ensure they are appropriate for the current stage of the evolution. Monitor diversity closely after any parameter adjustment.",
     "elite_management": "Investigate the solutions deemed 'elite' and ensure they are indeed valuable. Review the criteria for elite selection to ensure it doesn't inadvertently promote solutions that lead to local optima."
  }
}
```
2025-06-24 10:28:58,622 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-24 10:28:58,624 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_2.pkl
2025-06-24 10:28:58,624 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-06-24 10:28:58,624 - __main__ - INFO - 开始分析阶段
2025-06-24 10:28:58,624 - StatsExpert - INFO - 开始统计分析
2025-06-24 10:28:58,628 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 127136.0, 'mean': 61483.2, 'std': 48187.534999831645}, 'diversity': 0.7784511784511785, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-24 10:28:58,644 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 127136.0, 'mean': 61483.2, 'std': 48187.534999831645}, 'diversity_level': 0.7784511784511785, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-24 10:28:58,644 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-24 10:28:58,644 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-24 10:28:58,644 - PathExpert - INFO - 开始路径结构分析
2025-06-24 10:28:58,646 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-24 10:28:58,646 - PathExpert - INFO - 路径结构分析完成
2025-06-24 10:28:58,647 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-24 10:28:58,648 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-24 10:28:58,648 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-24 10:28:58,648 - EliteExpert - INFO - 开始精英解分析
2025-06-24 10:28:58,650 - EliteExpert - INFO - 精英解分析完成
2025-06-24 10:28:58,650 - __main__ - INFO - 精英专家分析报告: {'elite_count': 13, 'elite_common_features': {'common_edges': {'(15, 14)': 0.7692307692307693}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1958.0, 'avg_gap': 45810.43076923076}, 'structure_gap': {'unique_elite_edges': 201, 'unique_pop_edges': 384, 'common_edges': 46}}, 'elite_diversity': {'diversity_score': 0.30400155400155404}}
2025-06-24 10:28:58,653 - __main__ - INFO - 更新共享数据: elite_features
2025-06-24 10:28:58,653 - LandscapeExpert - INFO - 开始景观分析
2025-06-24 10:28:58,654 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-24 10:28:58,655 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 11479.0, mean 61483.2, max 127136.0, std 48187.534999831645
- diversity: 0.7784511784511785
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [11479.0, 11479.0, 11479.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-24 10:29:05,752 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high. The large range in edge lengths (10 to 3032) indicates varying costs associated with different moves, suggesting a rugged search space. The significant difference between the median and the max edge length indicates presence of outliers which are detrimental to solution quality.",
    "modality": "Likely multimodal. The presence of a few very long edges (long_edge_ratio of 0.249) could indicate the existence of local optima separated by large jumps in the solution space. However, without knowledge of optimal tour length, it's tough to say",
    "deceptiveness": "Potentially deceptive. The presence of long edges and the variation in edge lengths, in combination with stagnation, suggest that greedy approaches might lead to suboptimal solutions due to long edges blocking shorter path."
  },
  "population_state": {
    "coverage": "Poor. The diversity is relatively high (0.778), but the convergence is 0.0, indicating the population is scattered and not effectively exploring or exploiting the search space, especially as there are not high-quality_edges or common_subpaths to exploit.",
    "convergence": "Stagnated. The recent_best_costs show stagnation, with the best cost remaining unchanged for the last three iterations."
  },
  "difficult_regions": {
    "identification": "Based on the spatial summary, low-density cells (0 in the density grid) are likely difficult regions. Also, the presence of long edges makes those connections a difficult decision when constructing a solution.",
    "regions": [
      "Regions corresponding to the empty cells in the density grid: (0,1), (0,2), (1,0), (2,1)",
      "Edges with lengths significantly exceeding the median (e.g., >2469)"
    ]
  },
  "opportunity_regions": {
    "identification": "High-density cells in the density grid represent areas where cities are clustered, making them likely opportunity regions for finding shorter paths. Although, if these opportunities were easily exploitable the algorithm would likely converge more than it is.",
    "regions": [
      "Regions corresponding to high-density cells in the density grid: (0,0), (1,1), (1,2), (2,0), (2,2)"
    ]
  },
  "evolution_phase": "Exploitation Stagnation. The algorithm has found a best solution, but diversity remains high and is not converging. The evolution has stagnated after finding a poor minimum value.",
  "evolution_direction": {
    "phase": "Stuck in local optimum, requiring a more aggressive exploration phase to escape, before returning to exploitation.",
    "operator_suggestions": [
      "Increase mutation rate to introduce more diversity.",
      "Consider a restart mechanism to introduce new individuals into the population.",
      "Apply crossover operators that significantly disrupt existing solutions (e.g., order crossover with a large fragment size).",
      "Explore more intense local search strategies within opportunity regions."
    ]
  }
}
```
2025-06-24 10:29:05,752 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-24 10:29:05,752 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high. The large range in edge lengths (10 to 3032) indicates varying costs associated with different moves, suggesting a rugged search space. The significant difference between the median and the max edge length indicates presence of outliers which are detrimental to solution quality.', 'modality': "Likely multimodal. The presence of a few very long edges (long_edge_ratio of 0.249) could indicate the existence of local optima separated by large jumps in the solution space. However, without knowledge of optimal tour length, it's tough to say", 'deceptiveness': 'Potentially deceptive. The presence of long edges and the variation in edge lengths, in combination with stagnation, suggest that greedy approaches might lead to suboptimal solutions due to long edges blocking shorter path.'}, 'population_state': {'coverage': 'Poor. The diversity is relatively high (0.778), but the convergence is 0.0, indicating the population is scattered and not effectively exploring or exploiting the search space, especially as there are not high-quality_edges or common_subpaths to exploit.', 'convergence': 'Stagnated. The recent_best_costs show stagnation, with the best cost remaining unchanged for the last three iterations.'}, 'difficult_regions': {'identification': 'Based on the spatial summary, low-density cells (0 in the density grid) are likely difficult regions. Also, the presence of long edges makes those connections a difficult decision when constructing a solution.', 'regions': ['Regions corresponding to the empty cells in the density grid: (0,1), (0,2), (1,0), (2,1)', 'Edges with lengths significantly exceeding the median (e.g., >2469)']}, 'opportunity_regions': {'identification': 'High-density cells in the density grid represent areas where cities are clustered, making them likely opportunity regions for finding shorter paths. Although, if these opportunities were easily exploitable the algorithm would likely converge more than it is.', 'regions': ['Regions corresponding to high-density cells in the density grid: (0,0), (1,1), (1,2), (2,0), (2,2)']}, 'evolution_phase': 'Exploitation Stagnation. The algorithm has found a best solution, but diversity remains high and is not converging. The evolution has stagnated after finding a poor minimum value.', 'evolution_direction': {'phase': 'Stuck in local optimum, requiring a more aggressive exploration phase to escape, before returning to exploitation.', 'operator_suggestions': ['Increase mutation rate to introduce more diversity.', 'Consider a restart mechanism to introduce new individuals into the population.', 'Apply crossover operators that significantly disrupt existing solutions (e.g., order crossover with a large fragment size).', 'Explore more intense local search strategies within opportunity regions.']}}
2025-06-24 10:29:05,752 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-24 10:29:05,752 - __main__ - INFO - 分析阶段完成
2025-06-24 10:29:05,752 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high. The large range in edge lengths (10 to 3032) indicates varying costs associated with different moves, suggesting a rugged search space. The significant difference between the median and the max edge length indicates presence of outliers which are detrimental to solution quality.', 'modality': "Likely multimodal. The presence of a few very long edges (long_edge_ratio of 0.249) could indicate the existence of local optima separated by large jumps in the solution space. However, without knowledge of optimal tour length, it's tough to say", 'deceptiveness': 'Potentially deceptive. The presence of long edges and the variation in edge lengths, in combination with stagnation, suggest that greedy approaches might lead to suboptimal solutions due to long edges blocking shorter path.'}, 'population_state': {'coverage': 'Poor. The diversity is relatively high (0.778), but the convergence is 0.0, indicating the population is scattered and not effectively exploring or exploiting the search space, especially as there are not high-quality_edges or common_subpaths to exploit.', 'convergence': 'Stagnated. The recent_best_costs show stagnation, with the best cost remaining unchanged for the last three iterations.'}, 'difficult_regions': {'identification': 'Based on the spatial summary, low-density cells (0 in the density grid) are likely difficult regions. Also, the presence of long edges makes those connections a difficult decision when constructing a solution.', 'regions': ['Regions corresponding to the empty cells in the density grid: (0,1), (0,2), (1,0), (2,1)', 'Edges with lengths significantly exceeding the median (e.g., >2469)']}, 'opportunity_regions': {'identification': 'High-density cells in the density grid represent areas where cities are clustered, making them likely opportunity regions for finding shorter paths. Although, if these opportunities were easily exploitable the algorithm would likely converge more than it is.', 'regions': ['Regions corresponding to high-density cells in the density grid: (0,0), (1,1), (1,2), (2,0), (2,2)']}, 'evolution_phase': 'Exploitation Stagnation. The algorithm has found a best solution, but diversity remains high and is not converging. The evolution has stagnated after finding a poor minimum value.', 'evolution_direction': {'phase': 'Stuck in local optimum, requiring a more aggressive exploration phase to escape, before returning to exploitation.', 'operator_suggestions': ['Increase mutation rate to introduce more diversity.', 'Consider a restart mechanism to introduce new individuals into the population.', 'Apply crossover operators that significantly disrupt existing solutions (e.g., order crossover with a large fragment size).', 'Explore more intense local search strategies within opportunity regions.']}}
2025-06-24 10:29:05,752 - __main__ - INFO - 开始策略分配阶段
2025-06-24 10:29:05,752 - StrategyExpert - INFO - 开始策略分配分析
2025-06-24 10:29:05,752 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 3
- Population: 10 individuals, diversity=0.5784615384615385
- Best cost: 11479.0, Mean cost: 61483.2
- Search space: Ruggedness: Moderate to high. The large range in edge lengths (10 to 3032) indicates varying costs associated with different moves, suggesting a rugged search space. The significant difference between the median and the max edge length indicates presence of outliers which are detrimental to solution quality., Modality: Likely multimodal. The presence of a few very long edges (long_edge_ratio of 0.249) could indicate the existence of local optima separated by large jumps in the solution space. However, without knowledge of optimal tour length, it's tough to say
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Stagnation/Exploitation",
    "features": "The minimum cost has plateaued at 11479.0 for two consecutive iterations. The diversity has decreased, suggest...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-24 10:29:05,752 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-24 10:29:08,267 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "perturbation_intensity": 0.7,
      "search_radius": 0.3
    },
    "exploit": {
      "local_search_intensity": 0.5,
      "neighborhood_size": 0.1
    }
  }
}
```
2025-06-24 10:29:08,267 - StrategyExpert - INFO - 策略分配完成: ['exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'explore']
2025-06-24 10:29:08,267 - __main__ - INFO - 策略分配报告: ['exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'explore']
2025-06-24 10:29:08,267 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "perturbation_intensity": 0.7,
      "search_radius": 0.3
    },
    "exploit": {
      "local_search_intensity": 0.5,
      "neighborhood_size": 0.1
    }
  }
}
```
2025-06-24 10:29:08,267 - __main__ - INFO - 策略分配阶段完成
2025-06-24 10:29:08,267 - __main__ - INFO - 策略分配: ['exploit', 'exploit', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit', 'explore']
2025-06-24 10:29:08,267 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.3,
  "individual_assignments": {
    "0": "exploit",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "perturbation_intensity": 0.7,
      "search_radius": 0.3
    },
    "exploit": {
      "local_search_intensity": 0.5,
      "neighborhood_size": 0.1
    }
  }
}
```
2025-06-24 10:29:08,267 - __main__ - INFO - 开始进化阶段
2025-06-24 10:29:08,267 - __main__ - INFO - 为个体 0 生成利用路径
2025-06-24 10:29:08,267 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:29:08,267 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:29:08,267 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 0 处的路径，新成本: 119161.0
2025-06-24 10:29:08,267 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：5,跳过局部搜索
2025-06-24 10:29:08,267 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-24 10:29:08,267 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([32, 62, 14, 26,  8, 16, 38, 60, 51, 58, 61, 19, 54, 10, 43, 18, 22,
       30, 17, 42, 15, 37,  3, 35, 53, 21, 47,  6, 31, 65, 46, 29, 59, 23,
       55,  7,  9, 34, 39, 20, 64, 49, 52, 11, 48,  0, 40, 12,  1, 28, 33,
       36, 44, 27, 50,  4, 56, 57,  5, 24, 45, 13, 63, 41, 25,  2]), 'cur_cost': 119161.0}
2025-06-24 10:29:08,267 - __main__ - INFO - 为个体 1 生成利用路径
2025-06-24 10:29:08,267 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:29:08,267 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:29:08,267 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 100675.0
2025-06-24 10:29:08,274 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：5,跳过局部搜索
2025-06-24 10:29:08,274 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-24 10:29:08,274 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([56, 36, 31, 25, 37,  8, 43, 51, 15, 24, 23, 30, 49, 44, 13, 61,  5,
        4,  9, 62, 59, 64, 19, 58, 34, 48, 65, 50, 42,  0, 53, 47, 22,  2,
       27, 55, 33, 12, 32,  6, 21, 11,  7, 45, 18,  3, 28, 46, 57, 17, 16,
       35, 29, 63, 52, 39, 26, 60, 41, 40,  1, 20, 54, 14, 38, 10]), 'cur_cost': 100675.0}
2025-06-24 10:29:08,275 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-24 10:29:08,275 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:29:08,275 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [53, 62, 4] and ending with [60, 2, 55]
- Cost: 99215.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7476923076923077
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:29:08,275 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:29:11,888 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Initialized a new path with a simple sequential ordering to drastically increase diversity and potentially uncover new promising regions. This effectively resets the exploration to allow for a fresh perspective, acknowledging the high risk and potential low immediate quality.  The rationale is that a high diversity path might lead to better solutions via crossover or mutation in subsequent generations.",
  "targeted_regions": "The entire solution space, aiming to avoid premature convergence by drastically altering the current path."
}
```
2025-06-24 10:29:11,888 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:29:11,888 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-24 10:29:11,888 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-24 10:29:11,888 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-24 10:29:11,888 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:29:11,888 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:29:11,888 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 98976.0
2025-06-24 10:29:12,393 - ExploitationExpert - INFO - res_population_num: 14
2025-06-24 10:29:12,393 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9528, 9528, 9541, 89460, 9521]
2025-06-24 10:29:12,394 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-24 10:29:12,399 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:29:12,399 - ExploitationExpert - INFO - populations: [{'tour': array([32, 62, 14, 26,  8, 16, 38, 60, 51, 58, 61, 19, 54, 10, 43, 18, 22,
       30, 17, 42, 15, 37,  3, 35, 53, 21, 47,  6, 31, 65, 46, 29, 59, 23,
       55,  7,  9, 34, 39, 20, 64, 49, 52, 11, 48,  0, 40, 12,  1, 28, 33,
       36, 44, 27, 50,  4, 56, 57,  5, 24, 45, 13, 63, 41, 25,  2]), 'cur_cost': 119161.0}, {'tour': array([56, 36, 31, 25, 37,  8, 43, 51, 15, 24, 23, 30, 49, 44, 13, 61,  5,
        4,  9, 62, 59, 64, 19, 58, 34, 48, 65, 50, 42,  0, 53, 47, 22,  2,
       27, 55, 33, 12, 32,  6, 21, 11,  7, 45, 18,  3, 28, 46, 57, 17, 16,
       35, 29, 63, 52, 39, 26, 60, 41, 40,  1, 20, 54, 14, 38, 10]), 'cur_cost': 100675.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 12, 65, 34, 52, 55, 51, 29, 63, 42, 64, 31, 48,  0, 53, 32, 45,
       23,  3, 20, 19, 62,  5, 28, 37, 41, 26, 25, 33, 24, 46, 44,  8,  9,
       61, 14, 40, 10, 11, 47,  4, 58, 59, 56,  1, 16, 43, 54, 30,  7, 60,
       35, 17, 27,  6,  2, 22, 50, 39, 38, 57, 13, 18, 49, 36, 15]), 'cur_cost': 98976.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32], 'cur_cost': 15356.0}, {'tour': [30, 44, 56, 53, 18, 63, 28, 27, 62, 8, 39, 6, 57, 23, 17, 40, 49, 54, 59, 10, 58, 11, 5, 35, 43, 22, 12, 60, 34, 14, 25, 33, 1, 50, 3, 48, 41, 20, 46, 65, 24, 36, 29, 7, 52, 45, 19, 32, 21, 37, 47, 15, 2, 31, 64, 61, 38, 42, 26, 55, 4, 9, 16, 13, 0, 51], 'cur_cost': 99318.0}, {'tour': [6, 15, 2, 56, 42, 61, 3, 38, 19, 4, 47, 21, 5, 28, 10, 35, 1, 20, 50, 11, 40, 26, 65, 33, 8, 54, 17, 45, 63, 23, 57, 36, 13, 60, 48, 39, 0, 52, 29, 14, 41, 27, 58, 37, 9, 55, 16, 44, 34, 12, 49, 25, 53, 22, 43, 62, 31, 7, 30, 24, 18, 46, 64, 51, 32, 59], 'cur_cost': 127136.0}, {'tour': [33, 22, 11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15820.0}, {'tour': array([39, 18, 59, 12,  1, 30,  4, 22, 64, 10,  0, 44, 42, 25, 19, 57, 48,
        3, 65, 16, 29,  8, 34, 37,  6, 14, 35, 61, 47, 13, 23, 53, 20, 11,
        9, 41, 55, 45, 40, 21, 28, 38, 54, 27, 36, 52, 56, 46, 15, 58, 63,
       32, 33, 51, 49,  7, 60, 62, 31, 43, 26, 17, 24, 50,  2,  5]), 'cur_cost': 106595.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}]
2025-06-24 10:29:12,400 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 10:29:12,401 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 12, 'skip_rate': 0.3333333333333333, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 8, 'cache_hits': 5, 'similarity_calculations': 52, 'cache_hit_rate': 0.09615384615384616, 'cache_size': 47}}
2025-06-24 10:29:12,402 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-24 10:29:12,402 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-24 10:29:12,402 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:29:12,402 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:29:12,403 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 104059.0
2025-06-24 10:29:12,905 - ExploitationExpert - INFO - res_population_num: 14
2025-06-24 10:29:12,906 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9528, 9528, 9541, 89460, 9521]
2025-06-24 10:29:12,906 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-24 10:29:12,910 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:29:12,911 - ExploitationExpert - INFO - populations: [{'tour': array([32, 62, 14, 26,  8, 16, 38, 60, 51, 58, 61, 19, 54, 10, 43, 18, 22,
       30, 17, 42, 15, 37,  3, 35, 53, 21, 47,  6, 31, 65, 46, 29, 59, 23,
       55,  7,  9, 34, 39, 20, 64, 49, 52, 11, 48,  0, 40, 12,  1, 28, 33,
       36, 44, 27, 50,  4, 56, 57,  5, 24, 45, 13, 63, 41, 25,  2]), 'cur_cost': 119161.0}, {'tour': array([56, 36, 31, 25, 37,  8, 43, 51, 15, 24, 23, 30, 49, 44, 13, 61,  5,
        4,  9, 62, 59, 64, 19, 58, 34, 48, 65, 50, 42,  0, 53, 47, 22,  2,
       27, 55, 33, 12, 32,  6, 21, 11,  7, 45, 18,  3, 28, 46, 57, 17, 16,
       35, 29, 63, 52, 39, 26, 60, 41, 40,  1, 20, 54, 14, 38, 10]), 'cur_cost': 100675.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 12, 65, 34, 52, 55, 51, 29, 63, 42, 64, 31, 48,  0, 53, 32, 45,
       23,  3, 20, 19, 62,  5, 28, 37, 41, 26, 25, 33, 24, 46, 44,  8,  9,
       61, 14, 40, 10, 11, 47,  4, 58, 59, 56,  1, 16, 43, 54, 30,  7, 60,
       35, 17, 27,  6,  2, 22, 50, 39, 38, 57, 13, 18, 49, 36, 15]), 'cur_cost': 98976.0}, {'tour': array([ 8,  1,  7, 37, 19, 25, 17,  2, 38, 64, 18, 13, 16, 63, 23, 65, 31,
       27, 33, 12, 58, 15, 10,  5, 41, 42,  6, 60, 62,  4, 14, 49, 51, 39,
       32, 53, 46, 59, 21,  0, 52, 48, 36, 61, 28, 56, 24, 40, 45, 50, 11,
       30, 35, 29, 55,  9, 57, 47,  3, 43, 22, 44, 34, 54, 26, 20]), 'cur_cost': 104059.0}, {'tour': [30, 44, 56, 53, 18, 63, 28, 27, 62, 8, 39, 6, 57, 23, 17, 40, 49, 54, 59, 10, 58, 11, 5, 35, 43, 22, 12, 60, 34, 14, 25, 33, 1, 50, 3, 48, 41, 20, 46, 65, 24, 36, 29, 7, 52, 45, 19, 32, 21, 37, 47, 15, 2, 31, 64, 61, 38, 42, 26, 55, 4, 9, 16, 13, 0, 51], 'cur_cost': 99318.0}, {'tour': [6, 15, 2, 56, 42, 61, 3, 38, 19, 4, 47, 21, 5, 28, 10, 35, 1, 20, 50, 11, 40, 26, 65, 33, 8, 54, 17, 45, 63, 23, 57, 36, 13, 60, 48, 39, 0, 52, 29, 14, 41, 27, 58, 37, 9, 55, 16, 44, 34, 12, 49, 25, 53, 22, 43, 62, 31, 7, 30, 24, 18, 46, 64, 51, 32, 59], 'cur_cost': 127136.0}, {'tour': [33, 22, 11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15820.0}, {'tour': array([39, 18, 59, 12,  1, 30,  4, 22, 64, 10,  0, 44, 42, 25, 19, 57, 48,
        3, 65, 16, 29,  8, 34, 37,  6, 14, 35, 61, 47, 13, 23, 53, 20, 11,
        9, 41, 55, 45, 40, 21, 28, 38, 54, 27, 36, 52, 56, 46, 15, 58, 63,
       32, 33, 51, 49,  7, 60, 62, 31, 43, 26, 17, 24, 50,  2,  5]), 'cur_cost': 106595.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}]
2025-06-24 10:29:12,913 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 10:29:12,913 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 13, 'skip_rate': 0.3076923076923077, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 9, 'cache_hits': 5, 'similarity_calculations': 60, 'cache_hit_rate': 0.08333333333333333, 'cache_size': 55}}
2025-06-24 10:29:12,913 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-24 10:29:12,913 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-24 10:29:12,913 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:29:12,914 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:29:12,914 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 105441.0
2025-06-24 10:29:13,417 - ExploitationExpert - INFO - res_population_num: 15
2025-06-24 10:29:13,417 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9528, 9528, 9541, 89460, 9521, 9521]
2025-06-24 10:29:13,418 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-24 10:29:13,423 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:29:13,423 - ExploitationExpert - INFO - populations: [{'tour': array([32, 62, 14, 26,  8, 16, 38, 60, 51, 58, 61, 19, 54, 10, 43, 18, 22,
       30, 17, 42, 15, 37,  3, 35, 53, 21, 47,  6, 31, 65, 46, 29, 59, 23,
       55,  7,  9, 34, 39, 20, 64, 49, 52, 11, 48,  0, 40, 12,  1, 28, 33,
       36, 44, 27, 50,  4, 56, 57,  5, 24, 45, 13, 63, 41, 25,  2]), 'cur_cost': 119161.0}, {'tour': array([56, 36, 31, 25, 37,  8, 43, 51, 15, 24, 23, 30, 49, 44, 13, 61,  5,
        4,  9, 62, 59, 64, 19, 58, 34, 48, 65, 50, 42,  0, 53, 47, 22,  2,
       27, 55, 33, 12, 32,  6, 21, 11,  7, 45, 18,  3, 28, 46, 57, 17, 16,
       35, 29, 63, 52, 39, 26, 60, 41, 40,  1, 20, 54, 14, 38, 10]), 'cur_cost': 100675.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 12, 65, 34, 52, 55, 51, 29, 63, 42, 64, 31, 48,  0, 53, 32, 45,
       23,  3, 20, 19, 62,  5, 28, 37, 41, 26, 25, 33, 24, 46, 44,  8,  9,
       61, 14, 40, 10, 11, 47,  4, 58, 59, 56,  1, 16, 43, 54, 30,  7, 60,
       35, 17, 27,  6,  2, 22, 50, 39, 38, 57, 13, 18, 49, 36, 15]), 'cur_cost': 98976.0}, {'tour': array([ 8,  1,  7, 37, 19, 25, 17,  2, 38, 64, 18, 13, 16, 63, 23, 65, 31,
       27, 33, 12, 58, 15, 10,  5, 41, 42,  6, 60, 62,  4, 14, 49, 51, 39,
       32, 53, 46, 59, 21,  0, 52, 48, 36, 61, 28, 56, 24, 40, 45, 50, 11,
       30, 35, 29, 55,  9, 57, 47,  3, 43, 22, 44, 34, 54, 26, 20]), 'cur_cost': 104059.0}, {'tour': array([ 1, 14, 26, 19,  9, 40, 22, 34, 10,  3, 11,  8, 20, 54, 38, 47, 50,
        7, 28, 62, 58, 61, 56, 44, 24, 43, 18, 51,  4, 29,  6, 36, 57, 27,
       65, 46, 53, 52, 59, 21, 60, 23, 30, 55,  5, 16, 45, 63, 15, 42, 25,
       64, 17, 13, 35, 37, 39,  2, 31, 49, 12, 41, 48,  0, 32, 33]), 'cur_cost': 105441.0}, {'tour': [6, 15, 2, 56, 42, 61, 3, 38, 19, 4, 47, 21, 5, 28, 10, 35, 1, 20, 50, 11, 40, 26, 65, 33, 8, 54, 17, 45, 63, 23, 57, 36, 13, 60, 48, 39, 0, 52, 29, 14, 41, 27, 58, 37, 9, 55, 16, 44, 34, 12, 49, 25, 53, 22, 43, 62, 31, 7, 30, 24, 18, 46, 64, 51, 32, 59], 'cur_cost': 127136.0}, {'tour': [33, 22, 11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 15820.0}, {'tour': array([39, 18, 59, 12,  1, 30,  4, 22, 64, 10,  0, 44, 42, 25, 19, 57, 48,
        3, 65, 16, 29,  8, 34, 37,  6, 14, 35, 61, 47, 13, 23, 53, 20, 11,
        9, 41, 55, 45, 40, 21, 28, 38, 54, 27, 36, 52, 56, 46, 15, 58, 63,
       32, 33, 51, 49,  7, 60, 62, 31, 43, 26, 17, 24, 50,  2,  5]), 'cur_cost': 106595.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}]
2025-06-24 10:29:13,425 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 10:29:13,425 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 14, 'skip_rate': 0.2857142857142857, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 10, 'cache_hits': 5, 'similarity_calculations': 69, 'cache_hit_rate': 0.07246376811594203, 'cache_size': 64}}
2025-06-24 10:29:13,426 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-24 10:29:13,426 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-24 10:29:13,426 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:29:13,426 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [6, 15, 2] and ending with [51, 32, 59]
- Cost: 127136.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.72
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:29:13,428 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:29:17,023 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Generated a sequential path as a maximally diverse starting point, given the TSP constraints of needing to include all nodes exactly once. This avoids any bias from the previous path and fully explores the solution space from a completely new angle. This is then considered for evolutionary changes.",
  "targeted_regions": "Exploration of the entire solution space due to the completely different starting path.  Future iterations can use the opportunity and difficult region data to refine this initial diverse path."
}
```
2025-06-24 10:29:17,023 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:29:17,025 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-24 10:29:17,025 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-24 10:29:17,025 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-24 10:29:17,025 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:29:17,025 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:29:17,025 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 100938.0
2025-06-24 10:29:17,530 - ExploitationExpert - INFO - res_population_num: 17
2025-06-24 10:29:17,530 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9528, 9528, 9541, 89460, 9521, 9521, 9521, 9521]
2025-06-24 10:29:17,530 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-06-24 10:29:17,537 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:29:17,537 - ExploitationExpert - INFO - populations: [{'tour': array([32, 62, 14, 26,  8, 16, 38, 60, 51, 58, 61, 19, 54, 10, 43, 18, 22,
       30, 17, 42, 15, 37,  3, 35, 53, 21, 47,  6, 31, 65, 46, 29, 59, 23,
       55,  7,  9, 34, 39, 20, 64, 49, 52, 11, 48,  0, 40, 12,  1, 28, 33,
       36, 44, 27, 50,  4, 56, 57,  5, 24, 45, 13, 63, 41, 25,  2]), 'cur_cost': 119161.0}, {'tour': array([56, 36, 31, 25, 37,  8, 43, 51, 15, 24, 23, 30, 49, 44, 13, 61,  5,
        4,  9, 62, 59, 64, 19, 58, 34, 48, 65, 50, 42,  0, 53, 47, 22,  2,
       27, 55, 33, 12, 32,  6, 21, 11,  7, 45, 18,  3, 28, 46, 57, 17, 16,
       35, 29, 63, 52, 39, 26, 60, 41, 40,  1, 20, 54, 14, 38, 10]), 'cur_cost': 100675.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 12, 65, 34, 52, 55, 51, 29, 63, 42, 64, 31, 48,  0, 53, 32, 45,
       23,  3, 20, 19, 62,  5, 28, 37, 41, 26, 25, 33, 24, 46, 44,  8,  9,
       61, 14, 40, 10, 11, 47,  4, 58, 59, 56,  1, 16, 43, 54, 30,  7, 60,
       35, 17, 27,  6,  2, 22, 50, 39, 38, 57, 13, 18, 49, 36, 15]), 'cur_cost': 98976.0}, {'tour': array([ 8,  1,  7, 37, 19, 25, 17,  2, 38, 64, 18, 13, 16, 63, 23, 65, 31,
       27, 33, 12, 58, 15, 10,  5, 41, 42,  6, 60, 62,  4, 14, 49, 51, 39,
       32, 53, 46, 59, 21,  0, 52, 48, 36, 61, 28, 56, 24, 40, 45, 50, 11,
       30, 35, 29, 55,  9, 57, 47,  3, 43, 22, 44, 34, 54, 26, 20]), 'cur_cost': 104059.0}, {'tour': array([ 1, 14, 26, 19,  9, 40, 22, 34, 10,  3, 11,  8, 20, 54, 38, 47, 50,
        7, 28, 62, 58, 61, 56, 44, 24, 43, 18, 51,  4, 29,  6, 36, 57, 27,
       65, 46, 53, 52, 59, 21, 60, 23, 30, 55,  5, 16, 45, 63, 15, 42, 25,
       64, 17, 13, 35, 37, 39,  2, 31, 49, 12, 41, 48,  0, 32, 33]), 'cur_cost': 105441.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([23, 54,  0, 19, 26, 20, 34, 14,  2, 60, 51, 31, 48,  7,  5, 39, 16,
       27, 13, 63, 52, 40, 36, 30, 32, 44, 41, 46, 21, 57, 12, 47, 35, 37,
       33, 55,  6, 10, 42,  3, 22,  8, 29,  1, 64, 62, 11, 17, 53, 58,  9,
       45, 38, 56, 15, 43, 65, 50, 24, 61, 49, 25, 59, 18, 28,  4]), 'cur_cost': 100938.0}, {'tour': array([39, 18, 59, 12,  1, 30,  4, 22, 64, 10,  0, 44, 42, 25, 19, 57, 48,
        3, 65, 16, 29,  8, 34, 37,  6, 14, 35, 61, 47, 13, 23, 53, 20, 11,
        9, 41, 55, 45, 40, 21, 28, 38, 54, 27, 36, 52, 56, 46, 15, 58, 63,
       32, 33, 51, 49,  7, 60, 62, 31, 43, 26, 17, 24, 50,  2,  5]), 'cur_cost': 106595.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}]
2025-06-24 10:29:17,540 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-24 10:29:17,540 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 15, 'skip_rate': 0.26666666666666666, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 11, 'cache_hits': 5, 'similarity_calculations': 79, 'cache_hit_rate': 0.06329113924050633, 'cache_size': 74}}
2025-06-24 10:29:17,540 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-24 10:29:17,540 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-24 10:29:17,540 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:29:17,540 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:29:17,541 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 97777.0
2025-06-24 10:29:19,454 - ExploitationExpert - INFO - res_population_num: 21
2025-06-24 10:29:19,456 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9528, 9528, 9541, 89460, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-24 10:29:19,456 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-24 10:29:19,465 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:29:19,465 - ExploitationExpert - INFO - populations: [{'tour': array([32, 62, 14, 26,  8, 16, 38, 60, 51, 58, 61, 19, 54, 10, 43, 18, 22,
       30, 17, 42, 15, 37,  3, 35, 53, 21, 47,  6, 31, 65, 46, 29, 59, 23,
       55,  7,  9, 34, 39, 20, 64, 49, 52, 11, 48,  0, 40, 12,  1, 28, 33,
       36, 44, 27, 50,  4, 56, 57,  5, 24, 45, 13, 63, 41, 25,  2]), 'cur_cost': 119161.0}, {'tour': array([56, 36, 31, 25, 37,  8, 43, 51, 15, 24, 23, 30, 49, 44, 13, 61,  5,
        4,  9, 62, 59, 64, 19, 58, 34, 48, 65, 50, 42,  0, 53, 47, 22,  2,
       27, 55, 33, 12, 32,  6, 21, 11,  7, 45, 18,  3, 28, 46, 57, 17, 16,
       35, 29, 63, 52, 39, 26, 60, 41, 40,  1, 20, 54, 14, 38, 10]), 'cur_cost': 100675.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([21, 12, 65, 34, 52, 55, 51, 29, 63, 42, 64, 31, 48,  0, 53, 32, 45,
       23,  3, 20, 19, 62,  5, 28, 37, 41, 26, 25, 33, 24, 46, 44,  8,  9,
       61, 14, 40, 10, 11, 47,  4, 58, 59, 56,  1, 16, 43, 54, 30,  7, 60,
       35, 17, 27,  6,  2, 22, 50, 39, 38, 57, 13, 18, 49, 36, 15]), 'cur_cost': 98976.0}, {'tour': array([ 8,  1,  7, 37, 19, 25, 17,  2, 38, 64, 18, 13, 16, 63, 23, 65, 31,
       27, 33, 12, 58, 15, 10,  5, 41, 42,  6, 60, 62,  4, 14, 49, 51, 39,
       32, 53, 46, 59, 21,  0, 52, 48, 36, 61, 28, 56, 24, 40, 45, 50, 11,
       30, 35, 29, 55,  9, 57, 47,  3, 43, 22, 44, 34, 54, 26, 20]), 'cur_cost': 104059.0}, {'tour': array([ 1, 14, 26, 19,  9, 40, 22, 34, 10,  3, 11,  8, 20, 54, 38, 47, 50,
        7, 28, 62, 58, 61, 56, 44, 24, 43, 18, 51,  4, 29,  6, 36, 57, 27,
       65, 46, 53, 52, 59, 21, 60, 23, 30, 55,  5, 16, 45, 63, 15, 42, 25,
       64, 17, 13, 35, 37, 39,  2, 31, 49, 12, 41, 48,  0, 32, 33]), 'cur_cost': 105441.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([23, 54,  0, 19, 26, 20, 34, 14,  2, 60, 51, 31, 48,  7,  5, 39, 16,
       27, 13, 63, 52, 40, 36, 30, 32, 44, 41, 46, 21, 57, 12, 47, 35, 37,
       33, 55,  6, 10, 42,  3, 22,  8, 29,  1, 64, 62, 11, 17, 53, 58,  9,
       45, 38, 56, 15, 43, 65, 50, 24, 61, 49, 25, 59, 18, 28,  4]), 'cur_cost': 100938.0}, {'tour': array([41,  4, 42, 23,  7, 62, 43, 47, 35,  5,  6, 33, 30, 10, 53, 63,  0,
        2, 65,  8, 38, 37, 28, 13, 58,  1, 52, 14, 19, 20, 54, 17, 44, 26,
       22, 24, 15, 56, 51, 29, 64, 21, 12, 27, 45, 59,  3, 55, 25, 16, 32,
       48, 50, 61, 36, 49, 57, 60, 31, 18, 46, 40, 39, 11,  9, 34]), 'cur_cost': 97777.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27], 'cur_cost': 15272.0}]
2025-06-24 10:29:19,468 - ExploitationExpert - INFO - 局部搜索耗时: 1.93秒
2025-06-24 10:29:19,468 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 16, 'skip_rate': 0.25, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 12, 'cache_hits': 5, 'similarity_calculations': 90, 'cache_hit_rate': 0.05555555555555555, 'cache_size': 85}}
2025-06-24 10:29:19,469 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-24 10:29:19,469 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-24 10:29:19,469 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:29:19,470 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 1, 2] and ending with [29, 28, 27]
- Cost: 15272.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7153846153846154
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:29:19,470 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:29:23,329 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29],
  "modification_strategy": "Reversed the path and swapped the starting positions of the first segment with the second segment of original path to dramatically increase diversity while still maintaining a full tour of all cities",
  "targeted_regions": "Focused on exploring the unexplored regions by reversing significant portions of the original path, aiming for areas the previous solution avoided. By reversing the start of the path, hopefully the areas will become optimized after this reversal"
}
```
2025-06-24 10:29:23,329 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:29:23,329 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15419.0, 路径: [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]
2025-06-24 10:29:23,329 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29], 'cur_cost': 15419.0}
2025-06-24 10:29:23,329 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([32, 62, 14, 26,  8, 16, 38, 60, 51, 58, 61, 19, 54, 10, 43, 18, 22,
       30, 17, 42, 15, 37,  3, 35, 53, 21, 47,  6, 31, 65, 46, 29, 59, 23,
       55,  7,  9, 34, 39, 20, 64, 49, 52, 11, 48,  0, 40, 12,  1, 28, 33,
       36, 44, 27, 50,  4, 56, 57,  5, 24, 45, 13, 63, 41, 25,  2]), 'cur_cost': 119161.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 36, 31, 25, 37,  8, 43, 51, 15, 24, 23, 30, 49, 44, 13, 61,  5,
        4,  9, 62, 59, 64, 19, 58, 34, 48, 65, 50, 42,  0, 53, 47, 22,  2,
       27, 55, 33, 12, 32,  6, 21, 11,  7, 45, 18,  3, 28, 46, 57, 17, 16,
       35, 29, 63, 52, 39, 26, 60, 41, 40,  1, 20, 54, 14, 38, 10]), 'cur_cost': 100675.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29], 'cur_cost': 15419.0}}]
2025-06-24 10:29:23,329 - __main__ - INFO - 进化阶段完成
2025-06-24 10:29:23,329 - __main__ - INFO - 开始评估阶段
2025-06-24 10:29:23,332 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-24 10:29:23,332 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-24 10:29:23,332 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 3/5
- Population changes:
  * Cost: min 11479.0->11479.0, mean 61483.2->76540.4
  * Diversity: 0.5784615384615385->0.7169230769230769
  * Elite solutions: 2->2

## Strategy Performance:
- Explore: 3 individuals, success rate 66.7%, avg improvement 67748.67
- Exploit: 7 individuals, success rate 28.6%, avg improvement -50545.43
- Overall improvement: 0.0

## Historical Trends:
Last 3 iterations: costs [11479.0, 11479.0, 11479.0], diversity [0.5279461279461279, 0.8498316498316498, 0.7784511784511785]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-24 10:29:27,998 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "Stagnation",
  "evolution_stage_features": [
    "Minimum cost has plateaued at 11479.0 for the last 3 iterations.",
    "Mean cost is increasing, indicating a potential drift away from optimal solutions.",
    "Diversity is fluctuating but hasn't consistently decreased, suggesting the population hasn't fully converged.",
    "The number of elite solutions remains constant at 2, confirming the lack of improvement in the best performing individuals."
  ],
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "66.7%",
      "avg_improvement": "67748.67",
      "assessment": "Explore strategy is proving effective at discovering new, potentially better solutions. High success rate and significant average improvement suggest it is a valuable approach."
    },
    "exploit": {
      "success_rate": "28.6%",
      "avg_improvement": "-50545.43",
      "assessment": "Exploit strategy is currently detrimental, resulting in a negative average improvement. This indicates that the exploitation is leading to worse solutions, likely due to getting stuck in local optima or overfitting existing good solutions."
    }
  },
  "balance_state": "Exploration-biased, but needs adjustment",
  "balance_state_assessment": "The current balance is heavily skewed towards exploration, which is beneficial given the stagnation. However, relying solely on exploration might not refine the best solutions effectively. The exploit strategy needs re-evaluation and adjustment to become useful.",
  "recommendations": [
    "Reduce the number of individuals dedicated to the exploit strategy significantly. Consider reducing the number to 3-4.",
    "Adjust the exploit strategy parameters or algorithms to prevent the current negative trend. Consider using different exploitation techniques, such as local search with smaller step sizes, or introducing a penalty for solutions that are worse than their parents.",
    "Continue to monitor the explore strategy's performance and maintain its current level. If the improvement starts to diminish, consider diversifying the exploration techniques used.",
    "Increase the number of iterations dedicated to finding the optimal solution, given that we have reached iteration 3/5.",
    "Investigate why the mean cost is increasing while the minimum cost remains constant. This could indicate that the selection pressure is not strong enough, or that the mutation operators are too aggressive.",
    "Visualize the solutions and their characteristics to gain a better understanding of the landscape and identify potential areas for improvement."
  ]
}
```
2025-06-24 10:29:28,027 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-24 10:29:28,027 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "Stagnation",
  "evolution_stage_features": [
    "Minimum cost has plateaued at 11479.0 for the last 3 iterations.",
    "Mean cost is increasing, indicating a potential drift away from optimal solutions.",
    "Diversity is fluctuating but hasn't consistently decreased, suggesting the population hasn't fully converged.",
    "The number of elite solutions remains constant at 2, confirming the lack of improvement in the best performing individuals."
  ],
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "66.7%",
      "avg_improvement": "67748.67",
      "assessment": "Explore strategy is proving effective at discovering new, potentially better solutions. High success rate and significant average improvement suggest it is a valuable approach."
    },
    "exploit": {
      "success_rate": "28.6%",
      "avg_improvement": "-50545.43",
      "assessment": "Exploit strategy is currently detrimental, resulting in a negative average improvement. This indicates that the exploitation is leading to worse solutions, likely due to getting stuck in local optima or overfitting existing good solutions."
    }
  },
  "balance_state": "Exploration-biased, but needs adjustment",
  "balance_state_assessment": "The current balance is heavily skewed towards exploration, which is beneficial given the stagnation. However, relying solely on exploration might not refine the best solutions effectively. The exploit strategy needs re-evaluation and adjustment to become useful.",
  "recommendations": [
    "Reduce the number of individuals dedicated to the exploit strategy significantly. Consider reducing the number to 3-4.",
    "Adjust the exploit strategy parameters or algorithms to prevent the current negative trend. Consider using different exploitation techniques, such as local search with smaller step sizes, or introducing a penalty for solutions that are worse than their parents.",
    "Continue to monitor the explore strategy's performance and maintain its current level. If the improvement starts to diminish, consider diversifying the exploration techniques used.",
    "Increase the number of iterations dedicated to finding the optimal solution, given that we have reached iteration 3/5.",
    "Investigate why the mean cost is increasing while the minimum cost remains constant. This could indicate that the selection pressure is not strong enough, or that the mutation operators are too aggressive.",
    "Visualize the solutions and their characteristics to gain a better understanding of the landscape and identify potential areas for improvement."
  ]
}
```
2025-06-24 10:29:28,028 - __main__ - INFO - 评估阶段完成
2025-06-24 10:29:28,028 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "Stagnation",
  "evolution_stage_features": [
    "Minimum cost has plateaued at 11479.0 for the last 3 iterations.",
    "Mean cost is increasing, indicating a potential drift away from optimal solutions.",
    "Diversity is fluctuating but hasn't consistently decreased, suggesting the population hasn't fully converged.",
    "The number of elite solutions remains constant at 2, confirming the lack of improvement in the best performing individuals."
  ],
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "66.7%",
      "avg_improvement": "67748.67",
      "assessment": "Explore strategy is proving effective at discovering new, potentially better solutions. High success rate and significant average improvement suggest it is a valuable approach."
    },
    "exploit": {
      "success_rate": "28.6%",
      "avg_improvement": "-50545.43",
      "assessment": "Exploit strategy is currently detrimental, resulting in a negative average improvement. This indicates that the exploitation is leading to worse solutions, likely due to getting stuck in local optima or overfitting existing good solutions."
    }
  },
  "balance_state": "Exploration-biased, but needs adjustment",
  "balance_state_assessment": "The current balance is heavily skewed towards exploration, which is beneficial given the stagnation. However, relying solely on exploration might not refine the best solutions effectively. The exploit strategy needs re-evaluation and adjustment to become useful.",
  "recommendations": [
    "Reduce the number of individuals dedicated to the exploit strategy significantly. Consider reducing the number to 3-4.",
    "Adjust the exploit strategy parameters or algorithms to prevent the current negative trend. Consider using different exploitation techniques, such as local search with smaller step sizes, or introducing a penalty for solutions that are worse than their parents.",
    "Continue to monitor the explore strategy's performance and maintain its current level. If the improvement starts to diminish, consider diversifying the exploration techniques used.",
    "Increase the number of iterations dedicated to finding the optimal solution, given that we have reached iteration 3/5.",
    "Investigate why the mean cost is increasing while the minimum cost remains constant. This could indicate that the selection pressure is not strong enough, or that the mutation operators are too aggressive.",
    "Visualize the solutions and their characteristics to gain a better understanding of the landscape and identify potential areas for improvement."
  ]
}
```
2025-06-24 10:29:28,028 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-24 10:29:28,030 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_3.pkl
2025-06-24 10:29:28,030 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-06-24 10:29:28,030 - __main__ - INFO - 开始分析阶段
2025-06-24 10:29:28,031 - StatsExpert - INFO - 开始统计分析
2025-06-24 10:29:28,049 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 119161.0, 'mean': 76540.4, 'std': 42122.323984319766}, 'diversity': 0.9023569023569022, 'clusters': {'clusters': 8, 'cluster_sizes': [1, 1, 3, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-24 10:29:28,051 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 11479.0, 'max': 119161.0, 'mean': 76540.4, 'std': 42122.323984319766}, 'diversity_level': 0.9023569023569022, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [1, 1, 3, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-24 10:29:28,052 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-24 10:29:28,052 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-24 10:29:28,052 - PathExpert - INFO - 开始路径结构分析
2025-06-24 10:29:28,054 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-24 10:29:28,055 - PathExpert - INFO - 路径结构分析完成
2025-06-24 10:29:28,055 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-24 10:29:28,055 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-24 10:29:28,055 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-24 10:29:28,055 - EliteExpert - INFO - 开始精英解分析
2025-06-24 10:29:28,062 - EliteExpert - INFO - 精英解分析完成
2025-06-24 10:29:28,062 - __main__ - INFO - 精英专家分析报告: {'elite_count': 21, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 1958.0, 'avg_gap': 63211.1619047619}, 'structure_gap': {'unique_elite_edges': 224, 'unique_pop_edges': 480, 'common_edges': 48}}, 'elite_diversity': {'diversity_score': 0.28528138528138525}}
2025-06-24 10:29:28,062 - __main__ - INFO - 更新共享数据: elite_features
2025-06-24 10:29:28,064 - LandscapeExpert - INFO - 开始景观分析
2025-06-24 10:29:28,066 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-24 10:29:28,067 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 11479.0, mean 76540.4, max 119161.0, std 42122.323984319766
- diversity: 0.9023569023569022
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [11479.0, 11479.0, 11479.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-24 10:29:35,375 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "The edge length statistics show a wide range (10.0 to 3032.0), suggesting a rugged search space. The large difference between Q1 (1075.0) and Q3 (2469.0) reinforces this, as does the high long_edge_ratio (0.249), indicating the presence of many long, potentially disruptive edges in solutions. This may imply that small changes in a solution can lead to large changes in cost.",
    "modality": "Difficult to assess modality definitively without more information, but the high standard deviation of cost_stats (42122.32) suggests the presence of multiple local optima.",
    "deceptiveness": "The presence of long edges (high long_edge_ratio) indicates potential deceptiveness.  Solutions might appear locally good based on shorter edges, but including a long edge could lead to a significantly worse overall cost.  This can mislead local search operators."
  },
  "population_state": {
    "coverage": "The diversity is high (0.902), indicating good population coverage. The density grid shows that nodes are concentrated in a few cells, meaning that while the population is diverse, it isn't spread evenly across the entire space.",
    "convergence": "The convergence is 0.0, indicating that the population has not converged significantly towards a single solution or region, even given the high diversity.  The stagnation in recent_best_costs further suggests that the population is struggling to improve.",
     "elite_properties": "There are no elite solutions detected."
  },
  "difficult_regions": [
    "Regions with low node density as indicated by the density_grid. Specifically, the central and top-right/center-right/bottom-center areas are sparsely populated [0, 0, 0] and [0, 12, 12].",
    "Corridors formed by long edges may represent regions where it's difficult to find efficient routes.",
    "Areas where the direction histogram shows a concentration of edges in particular sectors might suggest regions where optimizing edge orientation is crucial and difficult.",
    "Without high_quality_edges_sample and common_subpaths_sample, we can't directly pinpoint problematic path segments. Further analysis is required after more evolutionary steps."
  ],
  "opportunity_regions": [
    "High-density cells in the density grid represent potential opportunity regions. Cells [14, 0, 0], [14, 0, 14] indicate concentrated areas of nodes which could be combined to construct shorted/optimized routes.",
    "Regions where exploring alternative connections within the high-density regions might lead to significant improvements.",
    "Regions where the direction histogram shows a concentration of edges in particular sectors might provide a good basis for exploring and further refining the edge configurations within that sector."
  ],
  "evolution_phase": "Stagnation. The algorithm seems to be stuck in a local optimum. The recent best costs have not improved, and while diversity is high, convergence is not happening.",
  "evolution_direction": {
    "description": "The algorithm has high diversity but isn't converging, suggesting that it's exploring a wide range of solutions but not effectively exploiting promising regions or improving upon existing good solutions.  The stagnation further confirms this.",
    "operator_suggestions": [
      "Increase selection pressure to focus on the best solutions found so far, but be careful not to eliminate diversity completely.",
      "Implement a local search operator that focuses on optimizing edge connections within high-density regions.  Explore 2-opt or 3-opt moves specifically within these regions.",
      "Introduce a perturbation operator to escape the local optimum. For example, randomly swap a subset of nodes or reverse segments of paths. Ensure that perturbations are moderate enough to not destroy potentially useful partial solutions.",
      "Explore using a more aggressive crossover strategy to combine different solutions while avoiding overly disruptive changes. Edge recombination methods might be suitable."
    ]
  }
}
```
2025-06-24 10:29:35,375 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-24 10:29:35,375 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'The edge length statistics show a wide range (10.0 to 3032.0), suggesting a rugged search space. The large difference between Q1 (1075.0) and Q3 (2469.0) reinforces this, as does the high long_edge_ratio (0.249), indicating the presence of many long, potentially disruptive edges in solutions. This may imply that small changes in a solution can lead to large changes in cost.', 'modality': 'Difficult to assess modality definitively without more information, but the high standard deviation of cost_stats (42122.32) suggests the presence of multiple local optima.', 'deceptiveness': 'The presence of long edges (high long_edge_ratio) indicates potential deceptiveness.  Solutions might appear locally good based on shorter edges, but including a long edge could lead to a significantly worse overall cost.  This can mislead local search operators.'}, 'population_state': {'coverage': "The diversity is high (0.902), indicating good population coverage. The density grid shows that nodes are concentrated in a few cells, meaning that while the population is diverse, it isn't spread evenly across the entire space.", 'convergence': 'The convergence is 0.0, indicating that the population has not converged significantly towards a single solution or region, even given the high diversity.  The stagnation in recent_best_costs further suggests that the population is struggling to improve.', 'elite_properties': 'There are no elite solutions detected.'}, 'difficult_regions': ['Regions with low node density as indicated by the density_grid. Specifically, the central and top-right/center-right/bottom-center areas are sparsely populated [0, 0, 0] and [0, 12, 12].', "Corridors formed by long edges may represent regions where it's difficult to find efficient routes.", 'Areas where the direction histogram shows a concentration of edges in particular sectors might suggest regions where optimizing edge orientation is crucial and difficult.', "Without high_quality_edges_sample and common_subpaths_sample, we can't directly pinpoint problematic path segments. Further analysis is required after more evolutionary steps."], 'opportunity_regions': ['High-density cells in the density grid represent potential opportunity regions. Cells [14, 0, 0], [14, 0, 14] indicate concentrated areas of nodes which could be combined to construct shorted/optimized routes.', 'Regions where exploring alternative connections within the high-density regions might lead to significant improvements.', 'Regions where the direction histogram shows a concentration of edges in particular sectors might provide a good basis for exploring and further refining the edge configurations within that sector.'], 'evolution_phase': 'Stagnation. The algorithm seems to be stuck in a local optimum. The recent best costs have not improved, and while diversity is high, convergence is not happening.', 'evolution_direction': {'description': "The algorithm has high diversity but isn't converging, suggesting that it's exploring a wide range of solutions but not effectively exploiting promising regions or improving upon existing good solutions.  The stagnation further confirms this.", 'operator_suggestions': ['Increase selection pressure to focus on the best solutions found so far, but be careful not to eliminate diversity completely.', 'Implement a local search operator that focuses on optimizing edge connections within high-density regions.  Explore 2-opt or 3-opt moves specifically within these regions.', 'Introduce a perturbation operator to escape the local optimum. For example, randomly swap a subset of nodes or reverse segments of paths. Ensure that perturbations are moderate enough to not destroy potentially useful partial solutions.', 'Explore using a more aggressive crossover strategy to combine different solutions while avoiding overly disruptive changes. Edge recombination methods might be suitable.']}}
2025-06-24 10:29:35,375 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-24 10:29:35,375 - __main__ - INFO - 分析阶段完成
2025-06-24 10:29:35,375 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'The edge length statistics show a wide range (10.0 to 3032.0), suggesting a rugged search space. The large difference between Q1 (1075.0) and Q3 (2469.0) reinforces this, as does the high long_edge_ratio (0.249), indicating the presence of many long, potentially disruptive edges in solutions. This may imply that small changes in a solution can lead to large changes in cost.', 'modality': 'Difficult to assess modality definitively without more information, but the high standard deviation of cost_stats (42122.32) suggests the presence of multiple local optima.', 'deceptiveness': 'The presence of long edges (high long_edge_ratio) indicates potential deceptiveness.  Solutions might appear locally good based on shorter edges, but including a long edge could lead to a significantly worse overall cost.  This can mislead local search operators.'}, 'population_state': {'coverage': "The diversity is high (0.902), indicating good population coverage. The density grid shows that nodes are concentrated in a few cells, meaning that while the population is diverse, it isn't spread evenly across the entire space.", 'convergence': 'The convergence is 0.0, indicating that the population has not converged significantly towards a single solution or region, even given the high diversity.  The stagnation in recent_best_costs further suggests that the population is struggling to improve.', 'elite_properties': 'There are no elite solutions detected.'}, 'difficult_regions': ['Regions with low node density as indicated by the density_grid. Specifically, the central and top-right/center-right/bottom-center areas are sparsely populated [0, 0, 0] and [0, 12, 12].', "Corridors formed by long edges may represent regions where it's difficult to find efficient routes.", 'Areas where the direction histogram shows a concentration of edges in particular sectors might suggest regions where optimizing edge orientation is crucial and difficult.', "Without high_quality_edges_sample and common_subpaths_sample, we can't directly pinpoint problematic path segments. Further analysis is required after more evolutionary steps."], 'opportunity_regions': ['High-density cells in the density grid represent potential opportunity regions. Cells [14, 0, 0], [14, 0, 14] indicate concentrated areas of nodes which could be combined to construct shorted/optimized routes.', 'Regions where exploring alternative connections within the high-density regions might lead to significant improvements.', 'Regions where the direction histogram shows a concentration of edges in particular sectors might provide a good basis for exploring and further refining the edge configurations within that sector.'], 'evolution_phase': 'Stagnation. The algorithm seems to be stuck in a local optimum. The recent best costs have not improved, and while diversity is high, convergence is not happening.', 'evolution_direction': {'description': "The algorithm has high diversity but isn't converging, suggesting that it's exploring a wide range of solutions but not effectively exploiting promising regions or improving upon existing good solutions.  The stagnation further confirms this.", 'operator_suggestions': ['Increase selection pressure to focus on the best solutions found so far, but be careful not to eliminate diversity completely.', 'Implement a local search operator that focuses on optimizing edge connections within high-density regions.  Explore 2-opt or 3-opt moves specifically within these regions.', 'Introduce a perturbation operator to escape the local optimum. For example, randomly swap a subset of nodes or reverse segments of paths. Ensure that perturbations are moderate enough to not destroy potentially useful partial solutions.', 'Explore using a more aggressive crossover strategy to combine different solutions while avoiding overly disruptive changes. Edge recombination methods might be suitable.']}}
2025-06-24 10:29:35,375 - __main__ - INFO - 开始策略分配阶段
2025-06-24 10:29:35,375 - StrategyExpert - INFO - 开始策略分配分析
2025-06-24 10:29:35,380 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 4
- Population: 10 individuals, diversity=0.7169230769230769
- Best cost: 11479.0, Mean cost: 76540.4
- Search space: Ruggedness: The edge length statistics show a wide range (10.0 to 3032.0), suggesting a rugged search space. The large difference between Q1 (1075.0) and Q3 (2469.0) reinforces this, as does the high long_edge_ratio (0.249), indicating the presence of many long, potentially disruptive edges in solutions. This may imply that small changes in a solution can lead to large changes in cost., Modality: Difficult to assess modality definitively without more information, but the high standard deviation of cost_stats (42122.32) suggests the presence of multiple local optima.
- Difficult regions: 4 regions
- Opportunity regions: 3 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": "Stagnation",
  "evolution_stage_features": [
    "Minimum cost has plateaued at 11479.0 for the last 3 iterations.",
    "Mean cost is increasing, indicating a potentia...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-24 10:29:35,380 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-24 10:29:38,703 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "type": "guided_mutation",
      "mutation_rate": 0.6,
      "guide_weight": 0.3,
      "difficult_region_avoidance": true,
      "opportunity_region_focus": true
    },
    "exploit": {
      "type": "local_search",
      "neighborhood_size": 0.2,
      "intensification_factor": 1.2,
      "tabu_list_length": 5
    }
  }
}
```
2025-06-24 10:29:38,704 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-06-24 10:29:38,704 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-06-24 10:29:38,704 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "type": "guided_mutation",
      "mutation_rate": 0.6,
      "guide_weight": 0.3,
      "difficult_region_avoidance": true,
      "opportunity_region_focus": true
    },
    "exploit": {
      "type": "local_search",
      "neighborhood_size": 0.2,
      "intensification_factor": 1.2,
      "tabu_list_length": 5
    }
  }
}
```
2025-06-24 10:29:38,704 - __main__ - INFO - 策略分配阶段完成
2025-06-24 10:29:38,704 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-06-24 10:29:38,704 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "type": "guided_mutation",
      "mutation_rate": 0.6,
      "guide_weight": 0.3,
      "difficult_region_avoidance": true,
      "opportunity_region_focus": true
    },
    "exploit": {
      "type": "local_search",
      "neighborhood_size": 0.2,
      "intensification_factor": 1.2,
      "tabu_list_length": 5
    }
  }
}
```
2025-06-24 10:29:38,704 - __main__ - INFO - 开始进化阶段
2025-06-24 10:29:38,704 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-24 10:29:38,704 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:29:38,704 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [32, 62, 14] and ending with [41, 25, 2]
- Cost: 119161.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7169230769230769
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 4 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:29:38,708 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:29:42,614 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    6,
    16,
    50,
    18,
    61,
    22,
    34,
    37,
    52,
    43,
    12,
    44,
    28,
    11,
    55,
    20,
    46,
    1,
    24,
    59,
    64,
    47,
    39,
    35,
    8,
    62,
    14,
    41,
    25,
    2,
    56,
    33,
    19,
    40,
    58,
    26,
    0,
    54,
    30,
    10,
    4,
    27,
    49,
    15,
    57,
    63,
    31,
    36,
    5,
    21,
    13,
    9,
    42,
    60,
    38,
    65,
    51,
    23,
    48,
    3,
    7,
    17,
    29,
    32,
    45,
    53
  ],
  "modification_strategy": "Significantly shuffled the original path, focusing on inverting several sections and swapping nodes across the entire path to increase diversity and explore new regions. Started with some random nodes and then randomly inserted the rest.",
  "targeted_regions": "Explored all 3 opportunity regions by randomizing the path, while attempting to avoid the 4 difficult regions, although complete avoidance is difficult without specific region data."
}
```
2025-06-24 10:29:42,614 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:29:42,614 - ExplorationExpert - INFO - 探索路径生成完成，成本: 112156.0, 路径: [6, 16, 50, 18, 61, 22, 34, 37, 52, 43, 12, 44, 28, 11, 55, 20, 46, 1, 24, 59, 64, 47, 39, 35, 8, 62, 14, 41, 25, 2, 56, 33, 19, 40, 58, 26, 0, 54, 30, 10, 4, 27, 49, 15, 57, 63, 31, 36, 5, 21, 13, 9, 42, 60, 38, 65, 51, 23, 48, 3, 7, 17, 29, 32, 45, 53]
2025-06-24 10:29:42,614 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 16, 50, 18, 61, 22, 34, 37, 52, 43, 12, 44, 28, 11, 55, 20, 46, 1, 24, 59, 64, 47, 39, 35, 8, 62, 14, 41, 25, 2, 56, 33, 19, 40, 58, 26, 0, 54, 30, 10, 4, 27, 49, 15, 57, 63, 31, 36, 5, 21, 13, 9, 42, 60, 38, 65, 51, 23, 48, 3, 7, 17, 29, 32, 45, 53], 'cur_cost': 112156.0}
2025-06-24 10:29:42,614 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-24 10:29:42,614 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:29:42,614 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [56, 36, 31] and ending with [14, 38, 10]
- Cost: 100675.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7230769230769231
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 4 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:29:42,614 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:29:46,884 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Initialized a completely linear path (0 to 65), which should maximize diversity from the current path. This explores a completely new region of the search space.",
  "targeted_regions": "The entire solution space, aiming to provide maximal diversity. Given the starting path, any deviation provides substantial diversity."
}
```
2025-06-24 10:29:46,884 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:29:46,884 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-24 10:29:46,884 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-24 10:29:46,884 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-24 10:29:46,884 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:29:46,884 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [0, 1, 2] and ending with [63, 64, 65]
- Cost: 11479.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6492307692307693
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 4 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:29:46,884 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:29:50,427 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 65, 1, 64, 2, 63, 3, 62, 4, 61, 5, 60, 6, 59, 7, 58, 8, 57, 9, 56, 10, 55, 11, 54, 12, 53, 13, 52, 14, 51, 15, 50, 16, 49, 17, 48, 18, 47, 19, 46, 20, 45, 21, 44, 22, 43, 23, 42, 24, 41, 25, 40, 26, 39, 27, 38, 28, 37, 29, 36, 30, 35, 31, 34, 32, 33],
  "modification_strategy": "Reversed alternating order to drastically alter the path. This creates a high degree of diversity while ensuring a valid path according to the TSP constraints. Prioritizes exploring connections between opposite ends of the node index range.",
  "targeted_regions": "Connections between lower-indexed and higher-indexed nodes, seeking to explore edges that were likely not present in the original path."
}
```
2025-06-24 10:29:50,427 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:29:50,427 - ExplorationExpert - INFO - 探索路径生成完成，成本: 81823.0, 路径: [0, 65, 1, 64, 2, 63, 3, 62, 4, 61, 5, 60, 6, 59, 7, 58, 8, 57, 9, 56, 10, 55, 11, 54, 12, 53, 13, 52, 14, 51, 15, 50, 16, 49, 17, 48, 18, 47, 19, 46, 20, 45, 21, 44, 22, 43, 23, 42, 24, 41, 25, 40, 26, 39, 27, 38, 28, 37, 29, 36, 30, 35, 31, 34, 32, 33]
2025-06-24 10:29:50,427 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 65, 1, 64, 2, 63, 3, 62, 4, 61, 5, 60, 6, 59, 7, 58, 8, 57, 9, 56, 10, 55, 11, 54, 12, 53, 13, 52, 14, 51, 15, 50, 16, 49, 17, 48, 18, 47, 19, 46, 20, 45, 21, 44, 22, 43, 23, 42, 24, 41, 25, 40, 26, 39, 27, 38, 28, 37, 29, 36, 30, 35, 31, 34, 32, 33], 'cur_cost': 81823.0}
2025-06-24 10:29:50,427 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-24 10:29:50,427 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:29:50,427 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:29:50,427 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 98143.0
2025-06-24 10:29:50,929 - ExploitationExpert - INFO - res_population_num: 23
2025-06-24 10:29:50,930 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9528, 9528, 9541, 89460, 9521, 9521]
2025-06-24 10:29:50,930 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-06-24 10:29:50,943 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:29:50,943 - ExploitationExpert - INFO - populations: [{'tour': [6, 16, 50, 18, 61, 22, 34, 37, 52, 43, 12, 44, 28, 11, 55, 20, 46, 1, 24, 59, 64, 47, 39, 35, 8, 62, 14, 41, 25, 2, 56, 33, 19, 40, 58, 26, 0, 54, 30, 10, 4, 27, 49, 15, 57, 63, 31, 36, 5, 21, 13, 9, 42, 60, 38, 65, 51, 23, 48, 3, 7, 17, 29, 32, 45, 53], 'cur_cost': 112156.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 64, 2, 63, 3, 62, 4, 61, 5, 60, 6, 59, 7, 58, 8, 57, 9, 56, 10, 55, 11, 54, 12, 53, 13, 52, 14, 51, 15, 50, 16, 49, 17, 48, 18, 47, 19, 46, 20, 45, 21, 44, 22, 43, 23, 42, 24, 41, 25, 40, 26, 39, 27, 38, 28, 37, 29, 36, 30, 35, 31, 34, 32, 33], 'cur_cost': 81823.0}, {'tour': array([57, 24, 63, 18,  6, 14, 12,  2, 22,  7, 10,  3, 32, 51, 59, 65,  4,
        9, 54, 41, 39, 46, 27,  1, 30, 21, 16, 19, 52, 55, 45,  0, 53, 23,
       11, 20, 42, 35, 33, 47, 26, 62,  8, 48, 36, 34, 15, 25, 17, 64, 37,
       29, 43, 58, 13, 28, 49, 61, 38, 50, 60, 56,  5, 31, 40, 44]), 'cur_cost': 98143.0}, {'tour': array([ 8,  1,  7, 37, 19, 25, 17,  2, 38, 64, 18, 13, 16, 63, 23, 65, 31,
       27, 33, 12, 58, 15, 10,  5, 41, 42,  6, 60, 62,  4, 14, 49, 51, 39,
       32, 53, 46, 59, 21,  0, 52, 48, 36, 61, 28, 56, 24, 40, 45, 50, 11,
       30, 35, 29, 55,  9, 57, 47,  3, 43, 22, 44, 34, 54, 26, 20]), 'cur_cost': 104059.0}, {'tour': array([ 1, 14, 26, 19,  9, 40, 22, 34, 10,  3, 11,  8, 20, 54, 38, 47, 50,
        7, 28, 62, 58, 61, 56, 44, 24, 43, 18, 51,  4, 29,  6, 36, 57, 27,
       65, 46, 53, 52, 59, 21, 60, 23, 30, 55,  5, 16, 45, 63, 15, 42, 25,
       64, 17, 13, 35, 37, 39,  2, 31, 49, 12, 41, 48,  0, 32, 33]), 'cur_cost': 105441.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([23, 54,  0, 19, 26, 20, 34, 14,  2, 60, 51, 31, 48,  7,  5, 39, 16,
       27, 13, 63, 52, 40, 36, 30, 32, 44, 41, 46, 21, 57, 12, 47, 35, 37,
       33, 55,  6, 10, 42,  3, 22,  8, 29,  1, 64, 62, 11, 17, 53, 58,  9,
       45, 38, 56, 15, 43, 65, 50, 24, 61, 49, 25, 59, 18, 28,  4]), 'cur_cost': 100938.0}, {'tour': array([41,  4, 42, 23,  7, 62, 43, 47, 35,  5,  6, 33, 30, 10, 53, 63,  0,
        2, 65,  8, 38, 37, 28, 13, 58,  1, 52, 14, 19, 20, 54, 17, 44, 26,
       22, 24, 15, 56, 51, 29, 64, 21, 12, 27, 45, 59,  3, 55, 25, 16, 32,
       48, 50, 61, 36, 49, 57, 60, 31, 18, 46, 40, 39, 11,  9, 34]), 'cur_cost': 97777.0}, {'tour': [0, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29], 'cur_cost': 15419.0}]
2025-06-24 10:29:50,946 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-24 10:29:50,947 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 17, 'skip_rate': 0.23529411764705882, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 13, 'cache_hits': 5, 'similarity_calculations': 102, 'cache_hit_rate': 0.049019607843137254, 'cache_size': 97}}
2025-06-24 10:29:50,947 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-24 10:29:50,947 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-24 10:29:50,947 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:29:50,948 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [8, 1, 7] and ending with [54, 26, 20]
- Cost: 104059.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7323076923076923
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 4 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:29:50,949 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:29:54,871 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 64, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 1, 65
  ],
  "modification_strategy": "Significantly modified the path by swapping the second node (1) with node 64 and then moved node 1 to the end of the path and 65 to the last position to enhance diversity and explore a different initial edge while ensuring all nodes are visited once. This also allows for a completely new 'tail' end of the path potentially leading to lower cost.",
  "targeted_regions": "Exploration of regions connected to node 64 at the beginning of the path and node 65 at the end of the path."
}
```
2025-06-24 10:29:54,871 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:29:54,873 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15500.0, 路径: [0, 64, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 1, 65]
2025-06-24 10:29:54,873 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 64, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 1, 65], 'cur_cost': 15500.0}
2025-06-24 10:29:54,873 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-24 10:29:54,873 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:29:54,874 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [1, 14, 26] and ending with [0, 32, 33]
- Cost: 105441.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6553846153846153
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 4 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:29:54,874 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:29:58,673 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0
  ],
  "modification_strategy": "Reversed the initial path to maximize diversity and explore a completely different traversal order. This creates a path with high dissimilarity from the current one and inherently explores previously unexplored regions.",
  "targeted_regions": "All regions, aiming for maximum diversity by reversing the node order."
}
```
2025-06-24 10:29:58,673 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:29:58,674 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0]
2025-06-24 10:29:58,674 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 11479.0}
2025-06-24 10:29:58,674 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-24 10:29:58,674 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:29:58,674 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:29:58,674 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 91898.0
2025-06-24 10:29:58,674 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：5,跳过局部搜索
2025-06-24 10:29:58,674 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-24 10:29:58,674 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([60, 22, 16, 17, 33, 32, 37, 59, 38, 30, 39, 62, 14, 21,  5,  0,  9,
        8, 43, 20, 23, 41,  1,  3, 51,  6, 58, 49, 64, 34, 25, 26, 27, 40,
       54, 13, 29, 28, 15, 57, 61, 45, 47, 10, 11, 31, 50, 12, 65, 52, 24,
       18,  4,  2, 42, 44, 19,  7, 36, 48, 63, 53, 55, 56, 35, 46]), 'cur_cost': 91898.0}
2025-06-24 10:29:58,674 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-24 10:29:58,674 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:29:58,674 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [23, 54, 0] and ending with [18, 28, 4]
- Cost: 100938.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.6476923076923077
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 4 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:29:58,674 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:30:02,553 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    30, 15, 5, 45, 20, 60, 35, 10, 50, 25, 65, 40, 1, 16, 6, 46, 21, 61, 36, 11, 51, 26, 56, 41, 2, 17, 7, 47, 22, 62, 37, 12, 52, 27, 57, 42, 3, 18, 8, 48, 23, 63, 38, 13, 53, 28, 58, 43, 4, 19, 9, 49, 24, 64, 39, 14, 54, 29, 59, 44, 0, 31, 32, 33, 34],
  "modification_strategy": "Large-scale path disruption and re-ordering to increase diversity. The original path's structure was significantly altered by inverting multiple segments and shifting node sequences, aiming for exploration of unexplored regions. The new path starts with indices quite different from the original.",
  "targeted_regions": "All 3 opportunity regions were implicitly targeted by radically altering the node ordering. No specific region was favored, but the disruption aimed to traverse all regions differently."
}
```
2025-06-24 10:30:02,553 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:30:02,553 - ExplorationExpert - INFO - 探索路径生成完成，成本: 138994.0, 路径: [30, 15, 5, 45, 20, 60, 35, 10, 50, 25, 65, 40, 1, 16, 6, 46, 21, 61, 36, 11, 51, 26, 56, 41, 2, 17, 7, 47, 22, 62, 37, 12, 52, 27, 57, 42, 3, 18, 8, 48, 23, 63, 38, 13, 53, 28, 58, 43, 4, 19, 9, 49, 24, 64, 39, 14, 54, 29, 59, 44, 0, 31, 32, 33, 34, 55]
2025-06-24 10:30:02,553 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [30, 15, 5, 45, 20, 60, 35, 10, 50, 25, 65, 40, 1, 16, 6, 46, 21, 61, 36, 11, 51, 26, 56, 41, 2, 17, 7, 47, 22, 62, 37, 12, 52, 27, 57, 42, 3, 18, 8, 48, 23, 63, 38, 13, 53, 28, 58, 43, 4, 19, 9, 49, 24, 64, 39, 14, 54, 29, 59, 44, 0, 31, 32, 33, 34, 55], 'cur_cost': 138994.0}
2025-06-24 10:30:02,553 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-24 10:30:02,553 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-24 10:30:02,553 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [41, 4, 42] and ending with [11, 9, 34]
- Cost: 97777.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.64
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 4 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## TSP Problem Constraints:
- IMPORTANT: This is a Traveling Salesman Problem (TSP).
- Valid node indices MUST be between 0 and 65 inclusive.
- Your path MUST contain EXACTLY 66 nodes.
- Each node MUST appear EXACTLY ONCE in the path.
- DO NOT generate indices greater than 65.
- The path represents a permutation of nodes, not a sequence of increasing indices.

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.
ENSURE all node indices are within the valid range (0 to 65).

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path. Must contain exactly 66 unique integers between 0 and 65.
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-24 10:30:02,553 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-24 10:30:05,586 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65
  ],
  "modification_strategy": "Generated a completely new path by simply ordering nodes from 0 to 65. This maximizes diversity from the initial path and provides a baseline for exploring all regions.",
  "targeted_regions": "All unexplored regions, aiming for maximum path diversity."
}
```
2025-06-24 10:30:05,586 - ExplorationExpert - INFO - 计算路径成本
2025-06-24 10:30:05,586 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11479.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-24 10:30:05,586 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}
2025-06-24 10:30:05,586 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-24 10:30:05,586 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-24 10:30:05,586 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-24 10:30:05,586 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 105348.0
2025-06-24 10:30:06,094 - ExploitationExpert - INFO - res_population_num: 25
2025-06-24 10:30:06,096 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9528, 9528, 9541, 89460, 9521, 9521, 9521, 9521]
2025-06-24 10:30:06,096 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 15, 14, 20, 21, 13, 23, 22, 12, 17, 18, 16, 19, 27,
       37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 40, 49, 47,
       46, 48, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 17, 12, 18, 16, 23, 22,
       15, 14, 20, 21, 13, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 40, 43, 48, 49, 47, 46, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 60, 54, 57, 64, 65, 52, 63, 61, 53, 62, 59, 56, 55, 10],
      dtype=int64), array([ 0,  8, 11,  3, 13, 65, 45, 42, 37, 21, 17, 55, 62, 52, 56, 58, 19,
       24,  1, 28, 29, 25, 43, 60, 36, 46, 33, 20, 23, 10, 53, 49, 27, 61,
        2, 26, 15, 16, 12, 35, 39, 18, 50, 40, 63, 30,  6, 38,  4, 31,  7,
       54,  5,  9, 57, 48, 22, 51, 59, 64, 14, 34, 32, 47, 44, 41],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-24 10:30:06,115 - ExploitationExpert - INFO - populations_num: 10
2025-06-24 10:30:06,115 - ExploitationExpert - INFO - populations: [{'tour': [6, 16, 50, 18, 61, 22, 34, 37, 52, 43, 12, 44, 28, 11, 55, 20, 46, 1, 24, 59, 64, 47, 39, 35, 8, 62, 14, 41, 25, 2, 56, 33, 19, 40, 58, 26, 0, 54, 30, 10, 4, 27, 49, 15, 57, 63, 31, 36, 5, 21, 13, 9, 42, 60, 38, 65, 51, 23, 48, 3, 7, 17, 29, 32, 45, 53], 'cur_cost': 112156.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': [0, 65, 1, 64, 2, 63, 3, 62, 4, 61, 5, 60, 6, 59, 7, 58, 8, 57, 9, 56, 10, 55, 11, 54, 12, 53, 13, 52, 14, 51, 15, 50, 16, 49, 17, 48, 18, 47, 19, 46, 20, 45, 21, 44, 22, 43, 23, 42, 24, 41, 25, 40, 26, 39, 27, 38, 28, 37, 29, 36, 30, 35, 31, 34, 32, 33], 'cur_cost': 81823.0}, {'tour': array([57, 24, 63, 18,  6, 14, 12,  2, 22,  7, 10,  3, 32, 51, 59, 65,  4,
        9, 54, 41, 39, 46, 27,  1, 30, 21, 16, 19, 52, 55, 45,  0, 53, 23,
       11, 20, 42, 35, 33, 47, 26, 62,  8, 48, 36, 34, 15, 25, 17, 64, 37,
       29, 43, 58, 13, 28, 49, 61, 38, 50, 60, 56,  5, 31, 40, 44]), 'cur_cost': 98143.0}, {'tour': [0, 64, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 1, 65], 'cur_cost': 15500.0}, {'tour': [65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 11479.0}, {'tour': array([60, 22, 16, 17, 33, 32, 37, 59, 38, 30, 39, 62, 14, 21,  5,  0,  9,
        8, 43, 20, 23, 41,  1,  3, 51,  6, 58, 49, 64, 34, 25, 26, 27, 40,
       54, 13, 29, 28, 15, 57, 61, 45, 47, 10, 11, 31, 50, 12, 65, 52, 24,
       18,  4,  2, 42, 44, 19,  7, 36, 48, 63, 53, 55, 56, 35, 46]), 'cur_cost': 91898.0}, {'tour': [30, 15, 5, 45, 20, 60, 35, 10, 50, 25, 65, 40, 1, 16, 6, 46, 21, 61, 36, 11, 51, 26, 56, 41, 2, 17, 7, 47, 22, 62, 37, 12, 52, 27, 57, 42, 3, 18, 8, 48, 23, 63, 38, 13, 53, 28, 58, 43, 4, 19, 9, 49, 24, 64, 39, 14, 54, 29, 59, 44, 0, 31, 32, 33, 34, 55], 'cur_cost': 138994.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}, {'tour': array([61, 52, 47,  0, 60, 25, 38, 18, 65, 53, 37, 56,  8,  1,  6, 10, 16,
       55, 48, 14, 35, 62, 12, 43, 19, 63, 49, 40, 58, 51,  9, 21, 41, 31,
       34, 39,  7, 50, 57,  5, 23, 45, 54, 64, 27, 24, 33, 42,  2, 28, 13,
       59, 15, 30, 11, 20, 29, 26,  4, 32, 36, 46, 44, 17,  3, 22]), 'cur_cost': 105348.0}]
2025-06-24 10:30:06,117 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-24 10:30:06,117 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 5, 'total_searches': 19, 'skip_rate': 0.2631578947368421, 'estimated_time_saved': 25, 'path_optimizer': {'path_count': 14, 'cache_hits': 11, 'similarity_calculations': 121, 'cache_hit_rate': 0.09090909090909091, 'cache_size': 110}}
2025-06-24 10:30:06,118 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-24 10:30:06,120 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 16, 50, 18, 61, 22, 34, 37, 52, 43, 12, 44, 28, 11, 55, 20, 46, 1, 24, 59, 64, 47, 39, 35, 8, 62, 14, 41, 25, 2, 56, 33, 19, 40, 58, 26, 0, 54, 30, 10, 4, 27, 49, 15, 57, 63, 31, 36, 5, 21, 13, 9, 42, 60, 38, 65, 51, 23, 48, 3, 7, 17, 29, 32, 45, 53], 'cur_cost': 112156.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 65, 1, 64, 2, 63, 3, 62, 4, 61, 5, 60, 6, 59, 7, 58, 8, 57, 9, 56, 10, 55, 11, 54, 12, 53, 13, 52, 14, 51, 15, 50, 16, 49, 17, 48, 18, 47, 19, 46, 20, 45, 21, 44, 22, 43, 23, 42, 24, 41, 25, 40, 26, 39, 27, 38, 28, 37, 29, 36, 30, 35, 31, 34, 32, 33], 'cur_cost': 81823.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 64, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 1, 65], 'cur_cost': 15500.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 11479.0}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([60, 22, 16, 17, 33, 32, 37, 59, 38, 30, 39, 62, 14, 21,  5,  0,  9,
        8, 43, 20, 23, 41,  1,  3, 51,  6, 58, 49, 64, 34, 25, 26, 27, 40,
       54, 13, 29, 28, 15, 57, 61, 45, 47, 10, 11, 31, 50, 12, 65, 52, 24,
       18,  4,  2, 42, 44, 19,  7, 36, 48, 63, 53, 55, 56, 35, 46]), 'cur_cost': 91898.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [30, 15, 5, 45, 20, 60, 35, 10, 50, 25, 65, 40, 1, 16, 6, 46, 21, 61, 36, 11, 51, 26, 56, 41, 2, 17, 7, 47, 22, 62, 37, 12, 52, 27, 57, 42, 3, 18, 8, 48, 23, 63, 38, 13, 53, 28, 58, 43, 4, 19, 9, 49, 24, 64, 39, 14, 54, 29, 59, 44, 0, 31, 32, 33, 34, 55], 'cur_cost': 138994.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 11479.0}}]
2025-06-24 10:30:06,120 - __main__ - INFO - 进化阶段完成
2025-06-24 10:30:06,121 - __main__ - INFO - 开始评估阶段
2025-06-24 10:30:06,121 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-24 10:30:06,125 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-24 10:30:06,125 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 4/5
- Population changes:
  * Cost: min 11479.0->11479.0, mean 76540.4->67829.9
  * Diversity: 0.7169230769230769->0.6476923076923077
  * Elite solutions: 2->3

## Strategy Performance:
- Explore: 7 individuals, success rate 71.4%, avg improvement 36660.0
- Exploit: 3 individuals, success rate 33.3%, avg improvement -56505.0
- Overall improvement: 0.0

## Historical Trends:
Last 3 iterations: costs [11479.0, 11479.0, 11479.0], diversity [0.8498316498316498, 0.7784511784511785, 0.9023569023569022]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-24 10:30:11,226 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "Stagnation - The minimum cost has remained constant for the last three iterations, indicating a potential plateau. The diversity has fluctuated but is currently lower than at the beginning of the historical trend, suggesting a narrowing of the search space and a possible loss of potentially valuable genetic material. The number of elite solutions has slightly increased, which is a positive sign, but it hasn't translated into a cost reduction.",
  "strategy_effectiveness": {
    "explore": {
      "performance": "Explore exhibits a high success rate (71.4%) and a significant average improvement (36660.0). This indicates that exploring new areas of the search space is still yielding valuable solutions and contributing to the overall population improvement.",
      "concerns": "None apparent. Explore is performing well."
    },
    "exploit": {
      "performance": "Exploit shows a low success rate (33.3%) and a substantial negative average improvement (-56505.0). This strongly suggests that the current exploitation strategy is not effectively refining existing solutions and is, in fact, leading to regression. This could be due to overfitting or insufficient diversity within the exploited solutions.",
      "concerns": "Exploitation is detrimental to performance."
    }
  },
  "balance_state": "Unbalanced - The current balance is heavily skewed towards exploration. The negative improvement from exploitation indicates that it is actively hindering progress. The significant positive improvement from exploration suggests that it should be favored, but complete elimination of exploitation might be premature. A more nuanced approach to exploitation is needed.",
  "recommendations": {
    "evolution_strategy": "Reduce the number of individuals dedicated to exploitation and increase the number dedicated to exploration. Consider modifying the exploitation strategy to incorporate elements of exploration, such as introducing mutations during the exploitation process or exploring alternative solutions near the current best.",
    "exploitation_strategy_modification": "Investigate the reason for the negative improvement from exploitation. Potential modifications include: 1) Lowering the intensity of exploitation (smaller changes). 2) Introducing a diversity check to prevent premature convergence during exploitation. 3) Selecting individuals for exploitation based on different criteria (e.g., those with higher diversity).",
    "exploration_strategy_maintenance": "Maintain the current exploration strategy as it is currently successful. Consider slightly increasing the range of exploration to potentially escape the current local optima.",
    "diversity_management": "Implement a diversity maintenance mechanism (e.g., crowding, fitness sharing) to prevent premature convergence and encourage the exploration of diverse solutions. This is crucial given the reduced diversity.",
    "iteration_control": "Monitor the cost and diversity closely in the next iteration. If the cost continues to stagnate and diversity continues to decrease, consider a more drastic intervention such as restarting the evolution process with a different set of parameters or a completely new population."
  }
}
```
2025-06-24 10:30:11,254 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-24 10:30:11,254 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "Stagnation - The minimum cost has remained constant for the last three iterations, indicating a potential plateau. The diversity has fluctuated but is currently lower than at the beginning of the historical trend, suggesting a narrowing of the search space and a possible loss of potentially valuable genetic material. The number of elite solutions has slightly increased, which is a positive sign, but it hasn't translated into a cost reduction.",
  "strategy_effectiveness": {
    "explore": {
      "performance": "Explore exhibits a high success rate (71.4%) and a significant average improvement (36660.0). This indicates that exploring new areas of the search space is still yielding valuable solutions and contributing to the overall population improvement.",
      "concerns": "None apparent. Explore is performing well."
    },
    "exploit": {
      "performance": "Exploit shows a low success rate (33.3%) and a substantial negative average improvement (-56505.0). This strongly suggests that the current exploitation strategy is not effectively refining existing solutions and is, in fact, leading to regression. This could be due to overfitting or insufficient diversity within the exploited solutions.",
      "concerns": "Exploitation is detrimental to performance."
    }
  },
  "balance_state": "Unbalanced - The current balance is heavily skewed towards exploration. The negative improvement from exploitation indicates that it is actively hindering progress. The significant positive improvement from exploration suggests that it should be favored, but complete elimination of exploitation might be premature. A more nuanced approach to exploitation is needed.",
  "recommendations": {
    "evolution_strategy": "Reduce the number of individuals dedicated to exploitation and increase the number dedicated to exploration. Consider modifying the exploitation strategy to incorporate elements of exploration, such as introducing mutations during the exploitation process or exploring alternative solutions near the current best.",
    "exploitation_strategy_modification": "Investigate the reason for the negative improvement from exploitation. Potential modifications include: 1) Lowering the intensity of exploitation (smaller changes). 2) Introducing a diversity check to prevent premature convergence during exploitation. 3) Selecting individuals for exploitation based on different criteria (e.g., those with higher diversity).",
    "exploration_strategy_maintenance": "Maintain the current exploration strategy as it is currently successful. Consider slightly increasing the range of exploration to potentially escape the current local optima.",
    "diversity_management": "Implement a diversity maintenance mechanism (e.g., crowding, fitness sharing) to prevent premature convergence and encourage the exploration of diverse solutions. This is crucial given the reduced diversity.",
    "iteration_control": "Monitor the cost and diversity closely in the next iteration. If the cost continues to stagnate and diversity continues to decrease, consider a more drastic intervention such as restarting the evolution process with a different set of parameters or a completely new population."
  }
}
```
2025-06-24 10:30:11,256 - __main__ - INFO - 评估阶段完成
2025-06-24 10:30:11,256 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "Stagnation - The minimum cost has remained constant for the last three iterations, indicating a potential plateau. The diversity has fluctuated but is currently lower than at the beginning of the historical trend, suggesting a narrowing of the search space and a possible loss of potentially valuable genetic material. The number of elite solutions has slightly increased, which is a positive sign, but it hasn't translated into a cost reduction.",
  "strategy_effectiveness": {
    "explore": {
      "performance": "Explore exhibits a high success rate (71.4%) and a significant average improvement (36660.0). This indicates that exploring new areas of the search space is still yielding valuable solutions and contributing to the overall population improvement.",
      "concerns": "None apparent. Explore is performing well."
    },
    "exploit": {
      "performance": "Exploit shows a low success rate (33.3%) and a substantial negative average improvement (-56505.0). This strongly suggests that the current exploitation strategy is not effectively refining existing solutions and is, in fact, leading to regression. This could be due to overfitting or insufficient diversity within the exploited solutions.",
      "concerns": "Exploitation is detrimental to performance."
    }
  },
  "balance_state": "Unbalanced - The current balance is heavily skewed towards exploration. The negative improvement from exploitation indicates that it is actively hindering progress. The significant positive improvement from exploration suggests that it should be favored, but complete elimination of exploitation might be premature. A more nuanced approach to exploitation is needed.",
  "recommendations": {
    "evolution_strategy": "Reduce the number of individuals dedicated to exploitation and increase the number dedicated to exploration. Consider modifying the exploitation strategy to incorporate elements of exploration, such as introducing mutations during the exploitation process or exploring alternative solutions near the current best.",
    "exploitation_strategy_modification": "Investigate the reason for the negative improvement from exploitation. Potential modifications include: 1) Lowering the intensity of exploitation (smaller changes). 2) Introducing a diversity check to prevent premature convergence during exploitation. 3) Selecting individuals for exploitation based on different criteria (e.g., those with higher diversity).",
    "exploration_strategy_maintenance": "Maintain the current exploration strategy as it is currently successful. Consider slightly increasing the range of exploration to potentially escape the current local optima.",
    "diversity_management": "Implement a diversity maintenance mechanism (e.g., crowding, fitness sharing) to prevent premature convergence and encourage the exploration of diverse solutions. This is crucial given the reduced diversity.",
    "iteration_control": "Monitor the cost and diversity closely in the next iteration. If the cost continues to stagnate and diversity continues to decrease, consider a more drastic intervention such as restarting the evolution process with a different set of parameters or a completely new population."
  }
}
```
2025-06-24 10:30:11,257 - __main__ - INFO - 当前最佳适应度: 11479.0
2025-06-24 10:30:11,258 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_4.pkl
2025-06-24 10:30:11,270 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_solution.json
2025-06-24 10:30:11,270 - __main__ - INFO - 实例 composite13_66 处理完成
