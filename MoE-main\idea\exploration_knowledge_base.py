import os
import json
import time
import uuid
import copy
import logging
import numpy as np
from knowledge_base import KnowledgeBase

class ExplorationKnowledgeBase(KnowledgeBase):
    """探索路径生成专家知识库，用于存储和检索探索路径生成的经验"""
    
    def __init__(self, config=None):
        """初始化探索路径生成专家知识库
        
        Args:
            config (dict): 知识库配置
        """
        # 设置默认存储路径
        config = config or {}
        if "storage_path" not in config:
            config["storage_path"] = os.path.join("knowledge_base", "exploration_expert")
        
        super().__init__(config)
        self.knowledge_usage_records = {}  # 记录知识使用情况
    
    def extract_keywords(self, individual, landscape_analysis):
        """从个体和景观分析中提取关键词
        
        Args:
            individual (dict): 个体信息
            landscape_analysis (dict): 景观分析结果
            
        Returns:
            list: 关键词列表
        """
        keywords = []
        
        # 1. 从路径长度提取关键词
        path_length = len(individual.get("tour", []))
        if path_length < 30:
            keywords.append("small_instance")
        elif path_length < 100:
            keywords.append("medium_instance")
        else:
            keywords.append("large_instance")
        
        # 2. 从成本提取关键词
        cost = individual.get("cur_cost", 0)
        if cost > 0:
            # 根据成本与最优解的差距添加关键词
            optimal_cost = landscape_analysis.get("optimal_cost", cost * 0.7)  # 假设最优成本
            gap_ratio = (cost - optimal_cost) / optimal_cost if optimal_cost > 0 else 0
            
            if gap_ratio < 0.05:
                keywords.append("near_optimal")
            elif gap_ratio < 0.2:
                keywords.append("good_solution")
            else:
                keywords.append("suboptimal")
        
        # 3. 从收敛阶段提取关键词
        convergence_stage = landscape_analysis.get("convergence_stage", "unknown")
        keywords.append(f"stage_{convergence_stage}")
        
        # 4. 从多样性水平提取关键词
        diversity = landscape_analysis.get("diversity", 0.5)
        if diversity < 0.3:
            keywords.append("low_diversity")
        elif diversity < 0.7:
            keywords.append("medium_diversity")
        else:
            keywords.append("high_diversity")
        
        # 5. 从路径结构特征提取关键词
        path_structure = landscape_analysis.get("path_structure", {})
        if path_structure.get("has_crossings", False):
            keywords.append("path_crossings")
        if path_structure.get("has_clusters", False):
            keywords.append("clustered_path")
        
        return keywords
    
    def record_knowledge_usage(self, knowledge_ids, individual, new_path):
        """记录知识使用情况，以便后续更新
        
        Args:
            knowledge_ids (list): 使用的知识ID列表
            individual (dict): 个体信息
            new_path (list): 新生成的路径
            
        Returns:
            str: 记录ID
        """
        if not knowledge_ids:
            return None
        
        # 记录使用的知识ID、原始路径和新路径
        usage_record = {
            "knowledge_ids": knowledge_ids,
            "original_path": individual.get("tour", []).copy(),
            "original_cost": individual.get("cur_cost", 0),
            "new_path": new_path,
            "new_cost": None,  # 将在评估后更新
            "timestamp": time.time(),
            "success": None  # 将在评估后更新
        }
        
        # 使用原始路径的哈希值作为记录ID
        path_hash = str(hash(tuple(individual.get("tour", []))))
        self.knowledge_usage_records[path_hash] = usage_record
        
        return path_hash
    
    def update_knowledge_after_evaluation(self, record_id, new_cost, success=None):
        """评估后更新知识库
        
        Args:
            record_id (str): 记录ID
            new_cost (float): 新路径的成本
            success (bool): 是否成功，如果为None则根据成本变化判断
            
        Returns:
            bool: 是否成功更新
        """
        # 查找使用记录
        if record_id not in self.knowledge_usage_records:
            return False
        
        usage_record = self.knowledge_usage_records[record_id]
        
        # 更新记录
        usage_record["new_cost"] = new_cost
        
        # 判断是否成功
        if success is None:
            # 如果未指定，则根据成本变化判断
            original_cost = usage_record.get("original_cost", float("inf"))
            success = new_cost < original_cost
        
        usage_record["success"] = success
        
        # 计算改进幅度
        improvement = 0
        if success and usage_record["original_cost"] > 0:
            improvement = (usage_record["original_cost"] - new_cost) / usage_record["original_cost"] * 100  # 百分比
        
        # 更新使用的知识条目
        for knowledge_id in usage_record["knowledge_ids"]:
            self.update_entry(
                entry_id=knowledge_id,
                update_data={"improvement": improvement},
                success=success
            )
        
        # 如果是成功的修改，且改进幅度显著，添加为新知识
        min_improvement = self.config.get("min_improvement_to_record", 1.0)
        if success and improvement > min_improvement:
            # 提取修改策略
            strategy_content = self._extract_strategy_from_paths(
                usage_record["original_path"],
                usage_record["new_path"]
            )
            
            # 构建新知识条目
            new_entry = {
                "keywords": self._extract_keywords_from_paths(
                    usage_record["original_path"],
                    usage_record["new_path"]
                ),
                "strategy_content": strategy_content,
                "problem_features": self._extract_problem_features_from_record(usage_record),
                "usage_count": 1,
                "success_count": 1,
                "improvement_stats": {
                    "avg_improvement": improvement,
                    "max_improvement": improvement,
                    "min_improvement": improvement,
                    "improvement_variance": 0,
                    "success_rate": 1.0
                },
                "timestamp": time.time(),
                "source": "auto_generated",
                "tags": self._generate_tags_for_strategy(strategy_content, improvement)
            }
            
            # 添加到知识库
            self.add_entry(new_entry)
        
        # 清理使用记录
        del self.knowledge_usage_records[record_id]
        
        return True
    
    def _extract_strategy_from_paths(self, original_path, new_path):
        """从原始路径和新路径中提取策略描述
        
        Args:
            original_path (list): 原始路径
            new_path (list): 新路径
            
        Returns:
            str: 策略描述
        """
        if not original_path or not new_path:
            return "未能提取策略描述"
        
        # 分析路径变化
        changes = self._analyze_path_changes(original_path, new_path)
        
        # 生成策略描述
        strategy_description = []
        
        # 1. 2-opt类型的变化
        if changes.get("2opt_count", 0) > 0:
            strategy_description.append(
                f"应用了{changes['2opt_count']}次2-opt操作，"  
                f"移除了交叉路径。"
            )
        
        # 2. 节点重排序
        if changes.get("reordered_segments", []):
            segments = changes["reordered_segments"]
            strategy_description.append(
                f"重新排序了{len(segments)}个路径段，"  
                f"最长的段包含{max(len(s) for s in segments)}个节点。"
            )
        
        # 3. 节点插入
        if changes.get("node_insertions", []):
            insertions = changes["node_insertions"]
            strategy_description.append(
                f"将{len(insertions)}个节点插入到新位置，"  
                f"平均移动距离为{changes.get('avg_insertion_distance', 0):.1f}。"
            )
        
        # 4. 路径反转
        if changes.get("reversed_segments", []):
            segments = changes["reversed_segments"]
            strategy_description.append(
                f"反转了{len(segments)}个路径段，"  
                f"最长的反转段包含{max(len(s) for s in segments)}个节点。"
            )
        
        # 如果没有识别出具体策略，提供一个通用描述
        if not strategy_description:
            return "对路径进行了复杂的重组，无法用简单操作描述。"
        
        return "\n".join(strategy_description)
    
    def _analyze_path_changes(self, original_path, new_path):
        """分析两条路径之间的变化
        
        Args:
            original_path (list): 原始路径
            new_path (list): 新路径
            
        Returns:
            dict: 变化分析结果
        """
        changes = {}
        
        # 1. 检测2-opt操作（交叉消除）
        changes["2opt_count"] = self._count_2opt_operations(original_path, new_path)
        
        # 2. 检测节点重排序
        reordered_segments = self._identify_reordered_segments(original_path, new_path)
        changes["reordered_segments"] = reordered_segments
        
        # 3. 检测节点插入
        node_insertions, avg_distance = self._identify_node_insertions(original_path, new_path)
        changes["node_insertions"] = node_insertions
        changes["avg_insertion_distance"] = avg_distance
        
        # 4. 检测路径段反转
        reversed_segments = self._identify_reversed_segments(original_path, new_path)
        changes["reversed_segments"] = reversed_segments
        
        return changes
    
    def _count_2opt_operations(self, original_path, new_path):
        """计算2-opt操作的数量
        
        Args:
            original_path (list): 原始路径
            new_path (list): 新路径
            
        Returns:
            int: 2-opt操作数量
        """
        # 简化实现：通过边的变化估计2-opt操作数量
        original_edges = set()
        for i in range(len(original_path)):
            edge = (original_path[i], original_path[(i+1) % len(original_path)])
            original_edges.add(edge)
            original_edges.add((edge[1], edge[0]))  # 添加反向边
        
        new_edges = set()
        for i in range(len(new_path)):
            edge = (new_path[i], new_path[(i+1) % len(new_path)])
            new_edges.add(edge)
            new_edges.add((edge[1], edge[0]))  # 添加反向边
        
        # 计算不同的边数量
        different_edges = len(original_edges.symmetric_difference(new_edges)) // 2
        
        # 每次2-opt操作会改变2条边，因此估计2-opt操作数量
        return different_edges // 4
    
    def _identify_reordered_segments(self, original_path, new_path):
        """识别重排序的路径段
        
        Args:
            original_path (list): 原始路径
            new_path (list): 新路径
            
        Returns:
            list: 重排序的路径段列表
        """
        # 简化实现：查找在原始路径中连续但在新路径中不连续的节点段
        reordered_segments = []
        
        # 构建新路径中节点的索引映射
        new_indices = {node: i for i, node in enumerate(new_path)}
        
        current_segment = []
        for i in range(len(original_path)):
            current_node = original_path[i]
            next_node = original_path[(i+1) % len(original_path)]
            
            current_segment.append(current_node)
            
            # 检查在新路径中是否连续
            if current_node in new_indices and next_node in new_indices:
                current_idx = new_indices[current_node]
                next_idx = new_indices[next_node]
                
                # 如果在新路径中不连续（考虑循环路径）
                if abs(next_idx - current_idx) != 1 and abs(next_idx - current_idx) != len(new_path) - 1:
                    # 当前段结束，添加到重排序段列表
                    if len(current_segment) > 1:
                        reordered_segments.append(current_segment)
                    current_segment = []
        
        # 处理最后一个段
        if len(current_segment) > 1:
            reordered_segments.append(current_segment)
        
        return reordered_segments
    
    def _identify_node_insertions(self, original_path, new_path):
        """识别节点插入
        
        Args:
            original_path (list): 原始路径
            new_path (list): 新路径
            
        Returns:
            tuple: (插入节点列表, 平均插入距离)
        """
        # 构建原始路径中节点的索引映射
        original_indices = {node: i for i, node in enumerate(original_path)}
        
        insertions = []
        total_distance = 0
        
        for i in range(len(new_path)):
            current_node = new_path[i]
            prev_node = new_path[(i-1) % len(new_path)]
            next_node = new_path[(i+1) % len(new_path)]
            
            # 检查当前节点在原始路径中的位置
            if current_node in original_indices:
                original_idx = original_indices[current_node]
                original_prev = original_path[(original_idx-1) % len(original_path)]
                original_next = original_path[(original_idx+1) % len(original_path)]
                
                # 如果前后节点发生变化，认为是插入操作
                if prev_node != original_prev and next_node != original_next:
                    insertions.append(current_node)
                    
                    # 计算插入距离（简化为索引差异）
                    if prev_node in original_indices and next_node in original_indices:
                        prev_idx = original_indices[prev_node]
                        next_idx = original_indices[next_node]
                        distance = min(
                            abs(original_idx - prev_idx),
                            abs(original_idx - next_idx)
                        )
                        total_distance += distance
        
        avg_distance = total_distance / len(insertions) if insertions else 0
        return insertions, avg_distance
    
    def _identify_reversed_segments(self, original_path, new_path):
        """识别反转的路径段
        
        Args:
            original_path (list): 原始路径
            new_path (list): 新路径
            
        Returns:
            list: 反转的路径段列表
        """
        reversed_segments = []
        
        # 构建新路径中节点的索引映射
        new_indices = {node: i for i, node in enumerate(new_path)}
        
        # 在原始路径中查找连续的节点，在新路径中顺序相反的段
        i = 0
        while i < len(original_path):
            # 尝试找到最长的反转段
            for j in range(i+1, len(original_path)):
                segment = original_path[i:j+1]
                
                # 检查该段在新路径中是否以相反顺序出现
                if len(segment) >= 3:  # 至少3个节点才有意义
                    reversed_segment = segment[::-1]
                    
                    # 检查反转段是否在新路径中连续出现
                    is_reversed = True
                    for k in range(len(reversed_segment)-1):
                        node1 = reversed_segment[k]
                        node2 = reversed_segment[k+1]
                        
                        if node1 not in new_indices or node2 not in new_indices:
                            is_reversed = False
                            break
                        
                        idx1 = new_indices[node1]
                        idx2 = new_indices[node2]
                        
                        # 检查是否相邻（考虑循环路径）
                        if abs(idx1 - idx2) != 1 and abs(idx1 - idx2) != len(new_path) - 1:
                            is_reversed = False
                            break
                    
                    if is_reversed:
                        reversed_segments.append(segment)
                        i = j  # 跳过已识别的段
                        break
            
            i += 1
        
        return reversed_segments
    
    def _extract_keywords_from_paths(self, original_path, new_path):
        """从路径变化中提取关键词
        
        Args:
            original_path (list): 原始路径
            new_path (list): 新路径
            
        Returns:
            list: 关键词列表
        """
        keywords = []
        
        # 分析路径变化
        changes = self._analyze_path_changes(original_path, new_path)
        
        # 根据变化类型添加关键词
        if changes.get("2opt_count", 0) > 0:
            keywords.append("2opt")
            keywords.append("crossing_removal")
        
        if changes.get("reordered_segments", []):
            keywords.append("segment_reordering")
        
        if changes.get("node_insertions", []):
            keywords.append("node_insertion")
        
        if changes.get("reversed_segments", []):
            keywords.append("segment_reversal")
        
        # 添加路径长度相关关键词
        path_length = len(original_path)
        if path_length < 30:
            keywords.append("small_instance")
        elif path_length < 100:
            keywords.append("medium_instance")
        else:
            keywords.append("large_instance")
        
        return keywords
    
    def _extract_problem_features_from_record(self, usage_record):
        """从使用记录中提取问题特征
        
        Args:
            usage_record (dict): 使用记录
            
        Returns:
            dict: 问题特征
        """
        original_path = usage_record.get("original_path", [])
        
        return {
            "instance_size": len(original_path),
            "original_cost": usage_record.get("original_cost", 0),
            "timestamp": usage_record.get("timestamp", time.time())
        }
    
    def _generate_tags_for_strategy(self, strategy_content, improvement):
        """为策略生成标签
        
        Args:
            strategy_content (str): 策略内容
            improvement (float): 改进幅度
            
        Returns:
            list: 标签列表
        """
        tags = []
        
        # 根据策略内容添加标签
        if "2-opt" in strategy_content or "交叉路径" in strategy_content:
            tags.append("2opt")
        
        if "重新排序" in strategy_content:
            tags.append("reordering")
        
        if "插入" in strategy_content:
            tags.append("insertion")
        
        if "反转" in strategy_content:
            tags.append("reversal")
        
        # 根据改进幅度添加标签
        if improvement > 10:
            tags.append("high_improvement")
        elif improvement > 5:
            tags.append("medium_improvement")
        else:
            tags.append("low_improvement")
        
        return tags
    
    def build_knowledge_query(self, individual, population_info, landscape_analysis):
        """构建知识查询
        
        Args:
            individual (dict): 个体信息
            population_info (dict): 种群信息
            landscape_analysis (dict): 景观分析结果
            
        Returns:
            dict: 查询条件
        """
        # 提取关键特征作为查询条件
        query = {
            "keywords": self.extract_keywords(individual, landscape_analysis),
            "problem_features": {
                "instance_size": len(individual.get("tour", [])),
                "diversity_level": population_info.get("diversity", 0.5),
                "convergence_stage": landscape_analysis.get("convergence_stage", "unknown")
            }
        }
        
        return query
    
    def format_knowledge_for_prompt(self, retrieved_knowledge):
        """将检索到的知识格式化为提示词的一部分
        
        Args:
            retrieved_knowledge (list): 检索到的知识条目列表
            
        Returns:
            str: 格式化后的知识文本
        """
        if not retrieved_knowledge:
            return "没有找到相关的历史经验。"
        
        knowledge_text = "以下是一些可能相关的历史经验：\n\n"
        
        for i, entry in enumerate(retrieved_knowledge, 1):
            success_rate = entry.get("success_count", 0) / entry.get("usage_count", 1) 
            avg_improvement = entry.get("improvement_stats", {}).get("avg_improvement", 0)
            
            knowledge_text += f"经验 {i}:\n"
            knowledge_text += f"- 策略: {entry.get('strategy_content', '无策略描述')}\n"
            knowledge_text += f"- 成功率: {success_rate:.1%}\n"
            knowledge_text += f"- 平均改进: {avg_improvement:.2f}%\n"
            knowledge_text += "\n"
        
        return knowledge_text