"""
多目标评估专家
实现多目标评估框架、阶段感知评估和改进反馈机制
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Any
from collections import defaultdict, deque
import json


class MultiObjectiveAssessmentExpert:
    """多目标评估专家，提供全面的进化过程评估"""
    
    def __init__(self, memory_size=50):
        self.logger = logging.getLogger(__name__)
        self.memory_size = memory_size
        
        # 评估权重（可动态调整）
        self.evaluation_weights = {
            'cost_improvement': 0.4,
            'diversity_maintenance': 0.3,
            'convergence_speed': 0.2,
            'exploration_efficiency': 0.1
        }
        
        # 历史评估记录
        self.evaluation_history = deque(maxlen=memory_size)
        
        # 阶段特定的评估策略
        self.stage_strategies = {
            'early': self._early_stage_evaluation,
            'middle': self._middle_stage_evaluation,
            'late': self._late_stage_evaluation
        }
        
        # 性能趋势分析
        self.trend_analyzer = TrendAnalyzer()
        
    def evaluate(self, old_population, new_population, strategy_assignment, strategy_results, 
                iteration, total_iterations, old_res_populations=None, new_res_populations=None):
        """多目标综合评估"""
        
        self.logger.info(f"开始多目标评估 - 迭代 {iteration}/{total_iterations}")
        
        # 确定当前进化阶段
        current_stage = self._determine_evolution_stage(iteration, total_iterations)
        
        # 基础指标计算
        basic_metrics = self._calculate_basic_metrics(
            old_population, new_population, old_res_populations, new_res_populations
        )
        
        # 多目标评估
        multi_objective_scores = self._multi_objective_evaluation(
            old_population, new_population, strategy_results
        )
        
        # 阶段感知评估
        stage_specific_assessment = self.stage_strategies[current_stage](
            basic_metrics, multi_objective_scores, iteration, total_iterations
        )
        
        # 策略效果评估
        strategy_effectiveness = self._evaluate_strategy_effectiveness(
            strategy_assignment, strategy_results, basic_metrics
        )
        
        # 趋势分析
        trend_analysis = self.trend_analyzer.analyze_trends(
            self.evaluation_history, basic_metrics
        )
        
        # 生成综合评估报告
        assessment_report = self._generate_assessment_report(
            current_stage, basic_metrics, multi_objective_scores,
            stage_specific_assessment, strategy_effectiveness, trend_analysis
        )
        
        # 更新历史记录
        self._update_evaluation_history(iteration, basic_metrics, multi_objective_scores)
        
        # 生成改进建议
        recommendations = self._generate_recommendations(assessment_report)
        
        final_report = {
            **assessment_report,
            'recommendations': recommendations,
            'evaluation_summary': self._create_evaluation_summary(assessment_report)
        }
        
        self.logger.info(f"多目标评估完成，总体评分: {final_report.get('overall_score', 0):.3f}")
        
        return final_report
    
    def _determine_evolution_stage(self, iteration, total_iterations):
        """确定当前进化阶段"""
        progress = iteration / total_iterations
        
        if progress < 0.3:
            return 'early'
        elif progress < 0.7:
            return 'middle'
        else:
            return 'late'
    
    def _calculate_basic_metrics(self, old_population, new_population, old_res_populations, new_res_populations):
        """计算基础评估指标"""
        metrics = {}
        
        # 成本改进指标
        old_costs = [ind.get("cur_cost", float('inf')) for ind in old_population]
        new_costs = [ind.get("cur_cost", float('inf')) for ind in new_population]
        
        metrics['cost_improvement'] = {
            'absolute': min(old_costs) - min(new_costs) if old_costs and new_costs else 0,
            'relative': (min(old_costs) - min(new_costs)) / min(old_costs) if old_costs and min(old_costs) > 0 else 0,
            'mean_improvement': np.mean(old_costs) - np.mean(new_costs) if old_costs and new_costs else 0
        }
        
        # 多样性指标
        metrics['diversity'] = {
            'old': self._calculate_diversity(old_population),
            'new': self._calculate_diversity(new_population),
            'change': self._calculate_diversity(new_population) - self._calculate_diversity(old_population)
        }
        
        # 收敛性指标
        metrics['convergence'] = {
            'old': self._calculate_convergence(old_population),
            'new': self._calculate_convergence(new_population),
            'speed': self._calculate_convergence_speed(old_population, new_population)
        }
        
        # 精英解指标
        if old_res_populations is not None and new_res_populations is not None:
            metrics['elite'] = {
                'count_change': len(new_res_populations) - len(old_res_populations),
                'quality_improvement': self._calculate_elite_quality_improvement(old_res_populations, new_res_populations),
                'diversity': self._calculate_diversity(new_res_populations)
            }
        
        return metrics
    
    def _multi_objective_evaluation(self, old_population, new_population, strategy_results):
        """多目标评估"""
        scores = {}
        
        # 成本改进评分
        old_costs = [ind.get("cur_cost", float('inf')) for ind in old_population]
        new_costs = [ind.get("cur_cost", float('inf')) for ind in new_population]
        
        if old_costs and new_costs:
            cost_improvement = (min(old_costs) - min(new_costs)) / min(old_costs) if min(old_costs) > 0 else 0
            scores['cost_improvement'] = max(0, min(1, cost_improvement * 10))  # 归一化到[0,1]
        else:
            scores['cost_improvement'] = 0
        
        # 多样性维持评分
        old_diversity = self._calculate_diversity(old_population)
        new_diversity = self._calculate_diversity(new_population)
        diversity_maintenance = 1 - abs(old_diversity - new_diversity) / max(old_diversity, 0.1)
        scores['diversity_maintenance'] = max(0, min(1, diversity_maintenance))
        
        # 收敛速度评分
        convergence_speed = self._calculate_convergence_speed(old_population, new_population)
        scores['convergence_speed'] = max(0, min(1, convergence_speed))
        
        # 探索效率评分
        exploration_efficiency = self._calculate_exploration_efficiency(strategy_results)
        scores['exploration_efficiency'] = max(0, min(1, exploration_efficiency))
        
        # 加权综合评分
        overall_score = sum(scores[metric] * self.evaluation_weights[metric] 
                          for metric in scores)
        scores['overall'] = overall_score
        
        return scores
    
    def _early_stage_evaluation(self, basic_metrics, multi_objective_scores, iteration, total_iterations):
        """早期阶段评估"""
        assessment = {
            'stage': 'early',
            'focus': 'exploration_and_diversity',
            'key_metrics': ['diversity_maintenance', 'exploration_efficiency'],
            'performance': {}
        }
        
        # 早期阶段重点关注多样性和探索
        diversity_score = multi_objective_scores.get('diversity_maintenance', 0)
        exploration_score = multi_objective_scores.get('exploration_efficiency', 0)
        
        assessment['performance']['diversity_adequacy'] = diversity_score
        assessment['performance']['exploration_effectiveness'] = exploration_score
        assessment['performance']['stage_score'] = (diversity_score * 0.6 + exploration_score * 0.4)
        
        # 早期阶段建议
        if diversity_score < 0.5:
            assessment['stage_recommendations'] = ['increase_exploration_ratio', 'diversify_initialization']
        elif exploration_score < 0.5:
            assessment['stage_recommendations'] = ['improve_exploration_strategies', 'reduce_exploitation']
        else:
            assessment['stage_recommendations'] = ['maintain_current_balance']
        
        return assessment
    
    def _middle_stage_evaluation(self, basic_metrics, multi_objective_scores, iteration, total_iterations):
        """中期阶段评估"""
        assessment = {
            'stage': 'middle',
            'focus': 'balanced_search',
            'key_metrics': ['cost_improvement', 'diversity_maintenance', 'convergence_speed'],
            'performance': {}
        }
        
        # 中期阶段需要平衡各个目标
        cost_score = multi_objective_scores.get('cost_improvement', 0)
        diversity_score = multi_objective_scores.get('diversity_maintenance', 0)
        convergence_score = multi_objective_scores.get('convergence_speed', 0)
        
        assessment['performance']['cost_improvement'] = cost_score
        assessment['performance']['diversity_balance'] = diversity_score
        assessment['performance']['convergence_progress'] = convergence_score
        assessment['performance']['stage_score'] = (cost_score * 0.4 + diversity_score * 0.3 + convergence_score * 0.3)
        
        # 中期阶段建议
        if cost_score < 0.3:
            assessment['stage_recommendations'] = ['increase_exploitation', 'improve_local_search']
        elif diversity_score < 0.3:
            assessment['stage_recommendations'] = ['maintain_diversity', 'prevent_premature_convergence']
        else:
            assessment['stage_recommendations'] = ['fine_tune_balance', 'monitor_progress']
        
        return assessment
    
    def _late_stage_evaluation(self, basic_metrics, multi_objective_scores, iteration, total_iterations):
        """后期阶段评估"""
        assessment = {
            'stage': 'late',
            'focus': 'intensive_exploitation',
            'key_metrics': ['cost_improvement', 'convergence_speed'],
            'performance': {}
        }
        
        # 后期阶段重点关注成本改进和收敛
        cost_score = multi_objective_scores.get('cost_improvement', 0)
        convergence_score = multi_objective_scores.get('convergence_speed', 0)
        
        assessment['performance']['final_optimization'] = cost_score
        assessment['performance']['convergence_quality'] = convergence_score
        assessment['performance']['stage_score'] = (cost_score * 0.7 + convergence_score * 0.3)
        
        # 后期阶段建议
        if cost_score < 0.2:
            assessment['stage_recommendations'] = ['intensive_local_search', 'elite_focused_search']
        elif convergence_score < 0.3:
            assessment['stage_recommendations'] = ['accelerate_convergence', 'reduce_exploration']
        else:
            assessment['stage_recommendations'] = ['maintain_exploitation', 'prepare_termination']
        
        return assessment
    
    def _evaluate_strategy_effectiveness(self, strategy_assignment, strategy_results, basic_metrics):
        """评估策略效果"""
        effectiveness = {
            'explore': {'usage': 0, 'success_rate': 0, 'avg_improvement': 0},
            'exploit': {'usage': 0, 'success_rate': 0, 'avg_improvement': 0}
        }
        
        # 统计策略使用情况
        if isinstance(strategy_assignment, list):
            for strategy in strategy_assignment:
                if strategy in effectiveness:
                    effectiveness[strategy]['usage'] += 1
        
        # 分析策略结果
        if isinstance(strategy_results, dict):
            for strategy, results in strategy_results.items():
                if strategy in effectiveness:
                    effectiveness[strategy]['success_rate'] = results.get('success_rate', 0)
                    effectiveness[strategy]['avg_improvement'] = results.get('avg_improvement', 0)
        
        return effectiveness
    
    def _calculate_diversity(self, population):
        """计算种群多样性"""
        if not population or len(population) < 2:
            return 0
        
        # 简化的多样性计算：基于成本分布
        costs = [ind.get("cur_cost", 0) for ind in population]
        if not costs:
            return 0
        
        cost_std = np.std(costs)
        cost_mean = np.mean(costs)
        
        # 归一化多样性指标
        diversity = cost_std / cost_mean if cost_mean > 0 else 0
        return min(1.0, diversity)
    
    def _calculate_convergence(self, population):
        """计算收敛性"""
        if not population:
            return 0
        
        costs = [ind.get("cur_cost", float('inf')) for ind in population]
        if not costs:
            return 0
        
        # 收敛性 = 1 - (标准差/均值)
        cost_std = np.std(costs)
        cost_mean = np.mean(costs)
        
        convergence = 1 - (cost_std / cost_mean) if cost_mean > 0 else 0
        return max(0, min(1, convergence))
    
    def _calculate_convergence_speed(self, old_population, new_population):
        """计算收敛速度"""
        old_convergence = self._calculate_convergence(old_population)
        new_convergence = self._calculate_convergence(new_population)
        
        speed = new_convergence - old_convergence
        return max(0, min(1, speed + 0.5))  # 归一化到[0,1]
    
    def _calculate_exploration_efficiency(self, strategy_results):
        """计算探索效率"""
        if not isinstance(strategy_results, dict):
            return 0.5
        
        explore_results = strategy_results.get('explore', {})
        success_rate = explore_results.get('success_rate', 0)
        avg_improvement = explore_results.get('avg_improvement', 0)
        
        # 综合成功率和改进幅度
        efficiency = (success_rate + min(1, avg_improvement / 10)) / 2
        return max(0, min(1, efficiency))
    
    def _calculate_elite_quality_improvement(self, old_elite, new_elite):
        """计算精英解质量改进"""
        if not old_elite or not new_elite:
            return 0
        
        old_best = min(ind.get("cur_cost", float('inf')) for ind in old_elite)
        new_best = min(ind.get("cur_cost", float('inf')) for ind in new_elite)
        
        if old_best == float('inf') or new_best == float('inf'):
            return 0
        
        improvement = (old_best - new_best) / old_best if old_best > 0 else 0
        return max(0, improvement)
    
    def _generate_assessment_report(self, stage, basic_metrics, multi_objective_scores, 
                                  stage_assessment, strategy_effectiveness, trend_analysis):
        """生成评估报告"""
        return {
            'evolution_stage': stage,
            'basic_metrics': basic_metrics,
            'multi_objective_scores': multi_objective_scores,
            'stage_assessment': stage_assessment,
            'strategy_effectiveness': strategy_effectiveness,
            'trend_analysis': trend_analysis,
            'overall_score': multi_objective_scores.get('overall', 0)
        }
    
    def _generate_recommendations(self, assessment_report):
        """生成改进建议"""
        recommendations = []
        
        overall_score = assessment_report.get('overall_score', 0)
        stage_assessment = assessment_report.get('stage_assessment', {})
        
        # 基于总体评分的建议
        if overall_score < 0.3:
            recommendations.append("overall_performance_low")
        elif overall_score > 0.8:
            recommendations.append("performance_excellent")
        
        # 基于阶段评估的建议
        stage_recommendations = stage_assessment.get('stage_recommendations', [])
        recommendations.extend(stage_recommendations)
        
        # 基于策略效果的建议
        strategy_effectiveness = assessment_report.get('strategy_effectiveness', {})
        for strategy, metrics in strategy_effectiveness.items():
            if metrics.get('success_rate', 0) < 0.3:
                recommendations.append(f"improve_{strategy}_strategy")
        
        return list(set(recommendations))  # 去重
    
    def _create_evaluation_summary(self, assessment_report):
        """创建评估摘要"""
        return {
            'stage': assessment_report.get('evolution_stage', 'unknown'),
            'overall_score': assessment_report.get('overall_score', 0),
            'key_strengths': self._identify_strengths(assessment_report),
            'key_weaknesses': self._identify_weaknesses(assessment_report),
            'priority_actions': assessment_report.get('recommendations', [])[:3]
        }
    
    def _identify_strengths(self, assessment_report):
        """识别优势"""
        strengths = []
        scores = assessment_report.get('multi_objective_scores', {})
        
        for metric, score in scores.items():
            if metric != 'overall' and score > 0.7:
                strengths.append(metric)
        
        return strengths
    
    def _identify_weaknesses(self, assessment_report):
        """识别弱点"""
        weaknesses = []
        scores = assessment_report.get('multi_objective_scores', {})
        
        for metric, score in scores.items():
            if metric != 'overall' and score < 0.3:
                weaknesses.append(metric)
        
        return weaknesses
    
    def _update_evaluation_history(self, iteration, basic_metrics, multi_objective_scores):
        """更新评估历史"""
        record = {
            'iteration': iteration,
            'basic_metrics': basic_metrics,
            'scores': multi_objective_scores,
            'timestamp': iteration
        }
        self.evaluation_history.append(record)
    
    def get_evaluation_statistics(self):
        """获取评估统计信息"""
        if not self.evaluation_history:
            return {}
        
        recent_scores = [record['scores']['overall'] for record in list(self.evaluation_history)[-10:]]
        
        return {
            'evaluation_count': len(self.evaluation_history),
            'average_score': np.mean(recent_scores) if recent_scores else 0,
            'score_trend': 'improving' if len(recent_scores) > 1 and recent_scores[-1] > recent_scores[0] else 'stable',
            'best_score': max(recent_scores) if recent_scores else 0,
            'current_weights': self.evaluation_weights.copy()
        }


class TrendAnalyzer:
    """趋势分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_trends(self, evaluation_history, current_metrics):
        """分析评估趋势"""
        if len(evaluation_history) < 3:
            return {'trend': 'insufficient_data', 'confidence': 0}
        
        # 提取最近的评分
        recent_scores = [record['scores']['overall'] for record in list(evaluation_history)[-5:]]
        
        # 计算趋势
        if len(recent_scores) >= 3:
            trend_direction = self._calculate_trend_direction(recent_scores)
            trend_strength = self._calculate_trend_strength(recent_scores)
            
            return {
                'trend': trend_direction,
                'strength': trend_strength,
                'confidence': min(1.0, len(recent_scores) / 5),
                'recent_scores': recent_scores
            }
        
        return {'trend': 'stable', 'strength': 0, 'confidence': 0.5}
    
    def _calculate_trend_direction(self, scores):
        """计算趋势方向"""
        if len(scores) < 2:
            return 'stable'
        
        improvements = sum(1 for i in range(1, len(scores)) if scores[i] > scores[i-1])
        deteriorations = sum(1 for i in range(1, len(scores)) if scores[i] < scores[i-1])
        
        if improvements > deteriorations:
            return 'improving'
        elif deteriorations > improvements:
            return 'declining'
        else:
            return 'stable'
    
    def _calculate_trend_strength(self, scores):
        """计算趋势强度"""
        if len(scores) < 2:
            return 0
        
        changes = [abs(scores[i] - scores[i-1]) for i in range(1, len(scores))]
        return np.mean(changes) if changes else 0
