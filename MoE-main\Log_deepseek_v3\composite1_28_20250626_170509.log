2025-06-26 17:05:09,819 - __main__ - INFO - composite1_28 开始进化第 1 代
2025-06-26 17:05:09,820 - __main__ - INFO - 开始分析阶段
2025-06-26 17:05:09,820 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:05:09,825 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 3159.0, 'max': 19650.0, 'mean': 13217.2, 'std': 6701.0085628955885}, 'diversity': 0.8992063492063492, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:05:09,829 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 3159.0, 'max': 19650.0, 'mean': 13217.2, 'std': 6701.0085628955885}, 'diversity_level': 0.8992063492063492, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 2, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[438, 1213], [479, 1236], [466, 1259], [438, 1259], [425, 1236], [447, 1243], [464, 1235], [449, 1224], [466, 1213], [110, 246], [151, 223], [138, 246], [110, 200], [138, 200], [132, 229], [116, 229], [116, 217], [132, 217], [97, 223], [693, 219], [665, 219], [652, 196], [665, 173], [693, 173], [679, 203], [687, 189], [671, 189], [706, 196]], 'distance_matrix': array([[   0.,   47.,   54.,   46.,   26.,   31.,   34.,   16.,   28.,
        1021., 1031., 1012., 1065., 1056., 1030., 1035., 1047., 1042.,
        1047., 1026., 1020., 1039., 1064., 1071., 1038., 1054., 1050.,
        1052.],
       [  47.,    0.,   26.,   47.,   54.,   33.,   15.,   32.,   26.,
        1057., 1065., 1047., 1100., 1091., 1065., 1070., 1082., 1076.,
        1083., 1039., 1034., 1054., 1079., 1084., 1052., 1067., 1064.,
        1064.],
       [  54.,   26.,    0.,   28.,   47.,   25.,   24.,   39.,   46.,
        1074., 1083., 1065., 1117., 1109., 1083., 1088., 1099., 1094.,
        1100., 1064., 1059., 1079., 1104., 1109., 1077., 1093., 1089.,
        1090.],
       [  46.,   47.,   28.,    0.,   26.,   18.,   35.,   37.,   54.,
        1065., 1075., 1056., 1109., 1101., 1074., 1079., 1091., 1086.,
        1091., 1071., 1064., 1084., 1109., 1116., 1083., 1099., 1095.,
        1096.],
       [  26.,   54.,   47.,   26.,    0.,   23.,   39.,   27.,   47.,
        1039., 1049., 1031., 1083., 1075., 1049., 1053., 1065., 1060.,
        1065., 1052., 1045., 1064., 1090., 1096., 1064., 1079., 1076.,
        1077.],
       [  31.,   33.,   25.,   18.,   23.,    0.,   19.,   19.,   36.,
        1052., 1062., 1044., 1096., 1088., 1062., 1067., 1078., 1073.,
        1078., 1053., 1047., 1067., 1092., 1098., 1066., 1081., 1078.,
        1079.],
       [  34.,   15.,   24.,   35.,   39.,   19.,    0.,   19.,   22.,
        1050., 1059., 1041., 1094., 1085., 1059., 1064., 1076., 1071.,
        1076., 1041., 1036., 1056., 1081., 1086., 1054., 1070., 1066.,
        1067.],
       [  16.,   32.,   39.,   37.,   27.,   19.,   19.,    0.,   20.,
        1035., 1044., 1026., 1079., 1070., 1044., 1049., 1061., 1056.,
        1061., 1034., 1028., 1048., 1073., 1079., 1047., 1062., 1059.,
        1060.],
       [  28.,   26.,   46.,   54.,   47.,   36.,   22.,   20.,    0.,
        1030., 1039., 1021., 1074., 1065., 1039., 1044., 1056., 1051.,
        1057., 1020., 1014., 1034., 1059., 1064., 1032., 1048., 1044.,
        1045.],
       [1021., 1057., 1074., 1065., 1039., 1052., 1050., 1035., 1030.,
           0.,   47.,   28.,   46.,   54.,   28.,   18.,   30.,   36.,
          26.,  584.,  556.,  544.,  560.,  588.,  571.,  580.,  564.,
         598.],
       [1031., 1065., 1083., 1075., 1049., 1062., 1059., 1044., 1039.,
          47.,    0.,   26.,   47.,   26.,   20.,   36.,   36.,   20.,
          54.,  542.,  514.,  502.,  516.,  544.,  528.,  537.,  521.,
         556.],
       [1012., 1047., 1065., 1056., 1031., 1044., 1041., 1026., 1021.,
          28.,   26.,    0.,   54.,   46.,   18.,   28.,   36.,   30.,
          47.,  556.,  528.,  516.,  532.,  560.,  543.,  552.,  536.,
         570.],
       [1065., 1100., 1117., 1109., 1083., 1096., 1094., 1079., 1074.,
          46.,   47.,   54.,    0.,   28.,   36.,   30.,   18.,   28.,
          26.,  583.,  555.,  542.,  556.,  584.,  569.,  577.,  561.,
         596.],
       [1056., 1091., 1109., 1101., 1075., 1088., 1085., 1070., 1065.,
          54.,   26.,   46.,   28.,    0.,   30.,   36.,   28.,   18.,
          47.,  555.,  527.,  514.,  528.,  556.,  541.,  549.,  533.,
         568.],
       [1030., 1065., 1083., 1074., 1049., 1062., 1059., 1044., 1039.,
          28.,   20.,   18.,   36.,   30.,    0.,   16.,   20.,   12.,
          36.,  561.,  533.,  521.,  536.,  564.,  548.,  556.,  540.,
         575.],
       [1035., 1070., 1088., 1079., 1053., 1067., 1064., 1049., 1044.,
          18.,   36.,   28.,   30.,   36.,   16.,    0.,   12.,   20.,
          20.,  577.,  549.,  537.,  552.,  580.,  564.,  572.,  556.,
         591.],
       [1047., 1082., 1099., 1091., 1065., 1078., 1076., 1061., 1056.,
          30.,   36.,   36.,   18.,   28.,   20.,   12.,    0.,   16.,
          20.,  577.,  549.,  536.,  551.,  579.,  563.,  572.,  556.,
         590.],
       [1042., 1076., 1094., 1086., 1060., 1073., 1071., 1056., 1051.,
          36.,   20.,   30.,   28.,   18.,   12.,   20.,   16.,    0.,
          36.,  561.,  533.,  520.,  535.,  563.,  547.,  556.,  540.,
         574.],
       [1047., 1083., 1100., 1091., 1065., 1078., 1076., 1061., 1057.,
          26.,   54.,   47.,   26.,   47.,   36.,   20.,   20.,   36.,
           0.,  596.,  568.,  556.,  570.,  598.,  582.,  591.,  575.,
         610.],
       [1026., 1039., 1064., 1071., 1052., 1053., 1041., 1034., 1020.,
         584.,  542.,  556.,  583.,  555.,  561.,  577.,  577.,  561.,
         596.,    0.,   28.,   47.,   54.,   46.,   21.,   31.,   37.,
          26.],
       [1020., 1034., 1059., 1064., 1045., 1047., 1036., 1028., 1014.,
         556.,  514.,  528.,  555.,  527.,  533.,  549.,  549.,  533.,
         568.,   28.,    0.,   26.,   46.,   54.,   21.,   37.,   31.,
          47.],
       [1039., 1054., 1079., 1084., 1064., 1067., 1056., 1048., 1034.,
         544.,  502.,  516.,  542.,  514.,  521.,  537.,  536.,  520.,
         556.,   47.,   26.,    0.,   26.,   47.,   28.,   36.,   20.,
          54.],
       [1064., 1079., 1104., 1109., 1090., 1092., 1081., 1073., 1059.,
         560.,  516.,  532.,  556.,  528.,  536.,  552.,  551.,  535.,
         570.,   54.,   46.,   26.,    0.,   28.,   33.,   27.,   17.,
          47.],
       [1071., 1084., 1109., 1116., 1096., 1098., 1086., 1079., 1064.,
         588.,  544.,  560.,  584.,  556.,  564.,  580.,  579.,  563.,
         598.,   46.,   54.,   47.,   28.,    0.,   33.,   17.,   27.,
          26.],
       [1038., 1052., 1077., 1083., 1064., 1066., 1054., 1047., 1032.,
         571.,  528.,  543.,  569.,  541.,  548.,  564.,  563.,  547.,
         582.,   21.,   21.,   28.,   33.,   33.,    0.,   16.,   16.,
          28.],
       [1054., 1067., 1093., 1099., 1079., 1081., 1070., 1062., 1048.,
         580.,  537.,  552.,  577.,  549.,  556.,  572.,  572.,  556.,
         591.,   31.,   37.,   36.,   27.,   17.,   16.,    0.,   16.,
          20.],
       [1050., 1064., 1089., 1095., 1076., 1078., 1066., 1059., 1044.,
         564.,  521.,  536.,  561.,  533.,  540.,  556.,  556.,  540.,
         575.,   37.,   31.,   20.,   17.,   27.,   16.,   16.,    0.,
          36.],
       [1052., 1064., 1090., 1096., 1077., 1079., 1067., 1060., 1045.,
         598.,  556.,  570.,  596.,  568.,  575.,  591.,  590.,  574.,
         610.,   26.,   47.,   54.,   47.,   26.,   28.,   20.,   36.,
           0.]])}
2025-06-26 17:05:09,839 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:05:09,840 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:05:09,840 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:05:09,842 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:05:09,842 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (26, 22, 21), 'frequency': 0.3}, {'subpath': (10, 14, 17), 'frequency': 0.3}, {'subpath': (14, 17, 16), 'frequency': 0.3}, {'subpath': (17, 16, 15), 'frequency': 0.3}, {'subpath': (16, 15, 9), 'frequency': 0.3}, {'subpath': (15, 9, 18), 'frequency': 0.3}, {'subpath': (9, 18, 12), 'frequency': 0.3}, {'subpath': (18, 12, 13), 'frequency': 0.3}, {'subpath': (12, 13, 11), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(10, 14)', 'frequency': 0.4}, {'edge': '(14, 17)', 'frequency': 0.4}, {'edge': '(9, 15)', 'frequency': 0.4}, {'edge': '(0, 7)', 'frequency': 0.4}, {'edge': '(3, 5)', 'frequency': 0.4}, {'edge': '(1, 6)', 'frequency': 0.4}, {'edge': '(23, 27)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(24, 25)', 'frequency': 0.3}, {'edge': '(22, 26)', 'frequency': 0.3}, {'edge': '(21, 22)', 'frequency': 0.3}, {'edge': '(19, 20)', 'frequency': 0.2}, {'edge': '(19, 27)', 'frequency': 0.3}, {'edge': '(16, 17)', 'frequency': 0.3}, {'edge': '(15, 16)', 'frequency': 0.3}, {'edge': '(9, 18)', 'frequency': 0.3}, {'edge': '(12, 18)', 'frequency': 0.3}, {'edge': '(12, 13)', 'frequency': 0.3}, {'edge': '(11, 13)', 'frequency': 0.3}, {'edge': '(5, 7)', 'frequency': 0.3}, {'edge': '(3, 4)', 'frequency': 0.2}, {'edge': '(2, 8)', 'frequency': 0.2}, {'edge': '(0, 4)', 'frequency': 0.3}, {'edge': '(7, 8)', 'frequency': 0.2}, {'edge': '(2, 20)', 'frequency': 0.2}, {'edge': '(20, 24)', 'frequency': 0.2}, {'edge': '(25, 26)', 'frequency': 0.2}, {'edge': '(19, 21)', 'frequency': 0.2}, {'edge': '(10, 23)', 'frequency': 0.2}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(2, 6)', 'frequency': 0.2}, {'edge': '(5, 20)', 'frequency': 0.2}, {'edge': '(7, 25)', 'frequency': 0.2}, {'edge': '(10, 25)', 'frequency': 0.2}, {'edge': '(10, 26)', 'frequency': 0.2}, {'edge': '(8, 16)', 'frequency': 0.2}, {'edge': '(2, 16)', 'frequency': 0.2}, {'edge': '(2, 18)', 'frequency': 0.2}, {'edge': '(4, 18)', 'frequency': 0.2}, {'edge': '(11, 24)', 'frequency': 0.2}, {'edge': '(17, 22)', 'frequency': 0.2}, {'edge': '(13, 17)', 'frequency': 0.2}, {'edge': '(13, 15)', 'frequency': 0.2}, {'edge': '(1, 21)', 'frequency': 0.2}, {'edge': '(1, 10)', 'frequency': 0.2}, {'edge': '(20, 26)', 'frequency': 0.2}, {'edge': '(18, 26)', 'frequency': 0.2}, {'edge': '(8, 9)', 'frequency': 0.2}, {'edge': '(9, 12)', 'frequency': 0.2}, {'edge': '(11, 12)', 'frequency': 0.2}, {'edge': '(3, 14)', 'frequency': 0.2}, {'edge': '(2, 25)', 'frequency': 0.2}, {'edge': '(19, 22)', 'frequency': 0.2}, {'edge': '(19, 23)', 'frequency': 0.3}, {'edge': '(5, 26)', 'frequency': 0.2}, {'edge': '(0, 21)', 'frequency': 0.2}, {'edge': '(0, 2)', 'frequency': 0.2}, {'edge': '(0, 15)', 'frequency': 0.2}, {'edge': '(15, 27)', 'frequency': 0.2}, {'edge': '(9, 20)', 'frequency': 0.2}, {'edge': '(13, 20)', 'frequency': 0.2}, {'edge': '(4, 21)', 'frequency': 0.2}, {'edge': '(22, 24)', 'frequency': 0.2}, {'edge': '(5, 12)', 'frequency': 0.2}, {'edge': '(11, 23)', 'frequency': 0.2}, {'edge': '(7, 26)', 'frequency': 0.2}, {'edge': '(1, 16)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [26, 8, 16, 2, 18, 4, 12], 'cost': 6447.0, 'size': 7}, {'region': [16, 3, 12, 5, 20], 'cost': 4343.0, 'size': 5}, {'region': [25, 4, 18, 3, 27], 'cost': 4331.0, 'size': 5}, {'region': [18, 2, 20, 3, 25], 'cost': 4322.0, 'size': 5}, {'region': [19, 5, 16, 1, 22], 'cost': 4292.0, 'size': 5}]}
2025-06-26 17:05:09,843 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:05:09,843 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:05:09,843 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:05:09,843 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 17:05:09,843 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 17:05:09,844 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:05:09,844 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:05:09,846 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:05:09,846 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 3159.0, mean 13217.2, max 19650.0, std 6701.0085628955885
- diversity: 0.8992063492063492
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [177.0, 176.0, 166.0] (Δ 5.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: [{'subpath': (26, 22, 21), 'frequency': 0.3}, {'subpath': (10, 14, 17), 'frequency': 0.3}, {'subpath': (14, 17, 16), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [26, 8, 16, 2, 18, 4, 12], 'cost': 6447.0, 'size': 7}, {'region': [16, 3, 12, 5, 20], 'cost': 4343.0, 'size': 5}, {'region': [25, 4, 18, 3, 27], 'cost': 4331.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [97.0, 173.0, 706.0, 1259.0]
- centroid/spread: [407.964, 539.464] | rms 532.029
- density_grid(3x3): [[10, 0, 9], [0, 0, 0], [0, 9, 0]]
- edge_len_stats(min,q1,med,q3,max): [12.0, 46.0, 577.0, 1062.0, 1117.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [5, 4, 3, 4, 6, 0, 4, 1]
- nn_median_dist: 18.028

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:05:09,846 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:05:38,701 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High (wide edge length range from 12.0 to 1117.0 with Q3 at 1062.0)",
    "modality": "Potentially multimodal (high diversity 0.899, no clustering info)",
    "deceptiveness": "Moderate (25% long edges, but recent best costs show improvement)"
  },
  "population_state": {
    "coverage": "Patchy (density_grid shows concentrated nodes in 3 cells with empty regions)",
    "convergence": "Early stage (0.0 convergence, high diversity)",
    "quality_spread": "Wide (min 3159.0 vs max 19650.0 cost)"
  },
  "difficult_regions": [
    {"region": [26, 8, 16, 2, 18, 4, 12], "reason": "High cost (6447.0) with spatial discontinuity (long edges)"},
    {"region": [16, 3, 12, 5, 20], "reason": "High subpath frequency but elevated cost (4343.0)"},
    {"region": [25, 4, 18, 3, 27], "reason": "Costly (4331.0) despite common subpath patterns"}
  ],
  "opportunity_regions": [
    {"region": [26, 22, 21], "reason": "Frequent subpath (0.3 freq) in dense NE quadrant"},
    {"region": [10, 14, 17], "reason": "Recurring pattern near centroid (407.964, 539.464)"}
  ],
  "evolution_phase": "Exploration (iter 0/5, high diversity, no elites)",
  "evolution_direction": [
    "Operator priority: Edge recombination (exploit common subpaths)",
    "Focus: Intensify search around centroid region (407.964, 539.464)",
    "Mitigation: Path relinking for difficult regions [26,8,16...]",
    "Adaptive strategy: Maintain diversity via restricted tournament selection"
  ]
}
```
2025-06-26 17:05:38,705 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:05:38,705 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High (wide edge length range from 12.0 to 1117.0 with Q3 at 1062.0)', 'modality': 'Potentially multimodal (high diversity 0.899, no clustering info)', 'deceptiveness': 'Moderate (25% long edges, but recent best costs show improvement)'}, 'population_state': {'coverage': 'Patchy (density_grid shows concentrated nodes in 3 cells with empty regions)', 'convergence': 'Early stage (0.0 convergence, high diversity)', 'quality_spread': 'Wide (min 3159.0 vs max 19650.0 cost)'}, 'difficult_regions': [{'region': [26, 8, 16, 2, 18, 4, 12], 'reason': 'High cost (6447.0) with spatial discontinuity (long edges)'}, {'region': [16, 3, 12, 5, 20], 'reason': 'High subpath frequency but elevated cost (4343.0)'}, {'region': [25, 4, 18, 3, 27], 'reason': 'Costly (4331.0) despite common subpath patterns'}], 'opportunity_regions': [{'region': [26, 22, 21], 'reason': 'Frequent subpath (0.3 freq) in dense NE quadrant'}, {'region': [10, 14, 17], 'reason': 'Recurring pattern near centroid (407.964, 539.464)'}], 'evolution_phase': 'Exploration (iter 0/5, high diversity, no elites)', 'evolution_direction': ['Operator priority: Edge recombination (exploit common subpaths)', 'Focus: Intensify search around centroid region (407.964, 539.464)', 'Mitigation: Path relinking for difficult regions [26,8,16...]', 'Adaptive strategy: Maintain diversity via restricted tournament selection']}
2025-06-26 17:05:38,705 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:05:38,705 - __main__ - INFO - 分析阶段完成
2025-06-26 17:05:38,705 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High (wide edge length range from 12.0 to 1117.0 with Q3 at 1062.0)', 'modality': 'Potentially multimodal (high diversity 0.899, no clustering info)', 'deceptiveness': 'Moderate (25% long edges, but recent best costs show improvement)'}, 'population_state': {'coverage': 'Patchy (density_grid shows concentrated nodes in 3 cells with empty regions)', 'convergence': 'Early stage (0.0 convergence, high diversity)', 'quality_spread': 'Wide (min 3159.0 vs max 19650.0 cost)'}, 'difficult_regions': [{'region': [26, 8, 16, 2, 18, 4, 12], 'reason': 'High cost (6447.0) with spatial discontinuity (long edges)'}, {'region': [16, 3, 12, 5, 20], 'reason': 'High subpath frequency but elevated cost (4343.0)'}, {'region': [25, 4, 18, 3, 27], 'reason': 'Costly (4331.0) despite common subpath patterns'}], 'opportunity_regions': [{'region': [26, 22, 21], 'reason': 'Frequent subpath (0.3 freq) in dense NE quadrant'}, {'region': [10, 14, 17], 'reason': 'Recurring pattern near centroid (407.964, 539.464)'}], 'evolution_phase': 'Exploration (iter 0/5, high diversity, no elites)', 'evolution_direction': ['Operator priority: Edge recombination (exploit common subpaths)', 'Focus: Intensify search around centroid region (407.964, 539.464)', 'Mitigation: Path relinking for difficult regions [26,8,16...]', 'Adaptive strategy: Maintain diversity via restricted tournament selection']}
2025-06-26 17:05:38,705 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:05:38,707 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:05:38,707 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 3159.0
  • mean_cost: 13217.2
- Search-space: ruggedness High (wide edge length range from 12.0 to 1117.0 with Q3 at 1062.0), modality Potentially multimodal (high diversity 0.899, no clustering info), deceptiveness Moderate (25% long edges, but recent best costs show improvement)
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [26, 8, 1, {'region': [16, 3, 1...
- Opportunity regions (# 2): {'region': [26, 22, , {'region': [10, 14, 
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:05:38,707 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:05:38,707 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:05:48,697 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and no elites suggests strong exploration bias. Top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 17:05:48,700 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 17:05:48,700 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 17:05:48,700 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and no elites suggests strong exploration bias. Top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 17:05:48,700 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:05:48,700 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit']
2025-06-26 17:05:48,700 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Initial phase with high diversity and no elites suggests strong exploration bias. Top 20% individuals exploit to refine promising solutions."
}
```
2025-06-26 17:05:48,700 - __main__ - INFO - 开始进化阶段
2025-06-26 17:05:48,700 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:05:48,700 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:05:48,700 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3213.0 (rank 10.0%)
- Population diversity: 0.6703703703703704
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [26,, {'region': [10,
- Difficult regions to avoid (sample): {'region': [26,, {'region': [16,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:05:48,700 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:05:48,700 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:05:59,592 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27],
  "targeted_regions": "Prioritized regions 10 and adjacent sparse cells",
  "strategy_comment": "Balanced exploration near opportunity regions, avoiding dense clusters"
}
```
2025-06-26 17:05:59,592 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:05:59,592 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15923.0, 路径: [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27]
2025-06-26 17:05:59,592 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}
2025-06-26 17:05:59,592 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:05:59,592 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:05:59,592 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3235.0 (rank 10.0%)
- Population diversity: 0.7
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [26,, {'region': [10,
- Difficult regions to avoid (sample): {'region': [26,, {'region': [16,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:05:59,592 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:05:59,592 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:06:10,821 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27],
  "targeted_regions": "10, 26, adjacent sparse cells",
  "strategy_comment": "Balance opportunity regions with sparse exploration, avoid dense clusters"
}
```
2025-06-26 17:06:10,821 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:06:10,826 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15923.0, 路径: [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27]
2025-06-26 17:06:10,826 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}
2025-06-26 17:06:10,826 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:06:10,826 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:06:10,826 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3159.0 (rank 0.0%)
- Population diversity: 0.6888888888888889
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [26,, {'region': [10,
- Difficult regions to avoid (sample): {'region': [26,, {'region': [16,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:06:10,826 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:06:10,826 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:06:21,765 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 12, 14, 16, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8],
  "targeted_regions": "Focus on regions 10 and 26, adjacent sparse cells",
  "strategy_comment": "Alternate between opportunity regions and sparse cells"
}
```
2025-06-26 17:06:21,766 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:06:21,766 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6048.0, 路径: [0, 10, 12, 14, 16, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8]
2025-06-26 17:06:21,766 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 10, 12, 14, 16, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8], 'cur_cost': 6048.0}
2025-06-26 17:06:21,766 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:06:21,766 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:06:21,766 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19002.0 (rank 80.0%)
- Population diversity: 0.6851851851851852
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [26,, {'region': [10,
- Difficult regions to avoid (sample): {'region': [26,, {'region': [16,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:06:21,766 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:06:21,768 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:06:33,130 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 12, 14, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8, 16],
  "targeted_regions": "Prioritized regions 10, 26; avoided 16",
  "strategy_comment": "Balanced exploration of sparse/opportunity regions with alternating clusters"
}
```
2025-06-26 17:06:33,130 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:06:33,130 - ExplorationExpert - INFO - 探索路径生成完成，成本: 8119.0, 路径: [0, 10, 12, 14, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8, 16]
2025-06-26 17:06:33,138 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 10, 12, 14, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8, 16], 'cur_cost': 8119.0}
2025-06-26 17:06:33,138 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:06:33,138 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:06:33,138 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15004.0 (rank 20.0%)
- Population diversity: 0.6370370370370371
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [26,, {'region': [10,
- Difficult regions to avoid (sample): {'region': [26,, {'region': [16,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:06:33,138 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:06:33,138 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:06:44,141 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 5, 15, 20, 25, 7, 12, 17, 22, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 27],
  "targeted_regions": "Focus on regions 10, 20, sparse cells near 5,15,25",
  "strategy_comment": "Balance opportunity regions with sparse adjacency, skip difficult nodes"
}
```
2025-06-26 17:06:44,141 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:06:44,141 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17999.0, 路径: [0, 10, 5, 15, 20, 25, 7, 12, 17, 22, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 27]
2025-06-26 17:06:44,141 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 10, 5, 15, 20, 25, 7, 12, 17, 22, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 27], 'cur_cost': 17999.0}
2025-06-26 17:06:44,141 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:06:44,141 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:06:44,141 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 16862.0 (rank 50.0%)
- Population diversity: 0.5888888888888889
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [26,, {'region': [10,
- Difficult regions to avoid (sample): {'region': [26,, {'region': [16,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:06:44,141 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:06:44,141 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:06:54,786 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27],
  "targeted_regions": "Regions 10, 5, 15, 20, 25",
  "strategy_comment": "Focus on opportunity regions, avoid dense clusters, balance exploration"
}
```
2025-06-26 17:06:54,786 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:06:54,786 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15923.0, 路径: [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27]
2025-06-26 17:06:54,786 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}
2025-06-26 17:06:54,786 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 17:06:54,786 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:06:54,786 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 19650.0 (rank 90.0%)
- Population diversity: 0.5185185185185185
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [26,, {'region': [10,
- Difficult regions to avoid (sample): {'region': [26,, {'region': [16,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:06:54,786 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:06:54,786 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:07:05,252 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 26, 9, 8, 7, 6, 5, 4, 3, 2, 1, 16],
  "targeted_regions": "Region 10 and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity region 10, avoid 16/26, balance diversity"
}
```
2025-06-26 17:07:05,255 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:07:05,255 - ExplorationExpert - INFO - 探索路径生成完成，成本: 5934.0, 路径: [0, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 26, 9, 8, 7, 6, 5, 4, 3, 2, 1, 16]
2025-06-26 17:07:05,255 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 26, 9, 8, 7, 6, 5, 4, 3, 2, 1, 16], 'cur_cost': 5934.0}
2025-06-26 17:07:05,255 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-26 17:07:05,255 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:07:05,255 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 18617.0 (rank 90.0%)
- Population diversity: 0.5333333333333333
- Evolution phase: Exploration (iter 0/5, high diversity, no elites)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [26,, {'region': [10,
- Difficult regions to avoid (sample): {'region': [26,, {'region': [16,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:07:05,255 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:07:05,255 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:07:16,010 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 10, 5, 15, 20, 25, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24],
  "targeted_regions": "Prioritized regions 10, 20, 25; avoided 16, 26",
  "strategy_comment": "Balanced exploration of sparse cells near opportunities"
}
```
2025-06-26 17:07:16,010 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:07:16,010 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15922.0, 路径: [0, 10, 5, 15, 20, 25, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24]
2025-06-26 17:07:16,010 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 10, 5, 15, 20, 25, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24], 'cur_cost': 15922.0}
2025-06-26 17:07:16,010 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:07:16,010 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:07:16,010 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:07:16,018 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 15878.0
2025-06-26 17:07:16,524 - ExploitationExpert - INFO - res_population_num: 40
2025-06-26 17:07:16,525 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:07:16,525 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64)]
2025-06-26 17:07:16,541 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:07:16,542 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}, {'tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}, {'tour': [0, 10, 12, 14, 16, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8], 'cur_cost': 6048.0}, {'tour': [0, 10, 12, 14, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8, 16], 'cur_cost': 8119.0}, {'tour': [0, 10, 5, 15, 20, 25, 7, 12, 17, 22, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 27], 'cur_cost': 17999.0}, {'tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 26, 9, 8, 7, 6, 5, 4, 3, 2, 1, 16], 'cur_cost': 5934.0}, {'tour': [0, 10, 5, 15, 20, 25, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24], 'cur_cost': 15922.0}, {'tour': array([ 7,  4,  9, 19, 13,  8, 17, 14, 18, 23,  0, 12, 15,  6,  2, 16,  1,
       26, 25, 20,  5,  3, 27, 24, 11, 10, 21, 22]), 'cur_cost': 15878.0}, {'tour': [23, 19, 20, 13, 10, 25, 4, 18, 3, 27, 21, 14, 22, 17, 24, 9, 12, 15, 1, 16, 2, 0, 8, 7, 5, 26, 6, 11], 'cur_cost': 15452.0}]
2025-06-26 17:07:16,543 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 17:07:16,543 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 6, 'total_searches': 113, 'skip_rate': 0.05309734513274336, 'estimated_time_saved': 30, 'path_optimizer': {'path_count': 107, 'cache_hits': 155, 'similarity_calculations': 2782, 'cache_hit_rate': 0.05571531272465852, 'cache_size': 2627}}
2025-06-26 17:07:16,543 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:07:16,545 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:07:16,545 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:07:16,546 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:07:16,546 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 14808.0
2025-06-26 17:07:17,050 - ExploitationExpert - INFO - res_population_num: 57
2025-06-26 17:07:17,050 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:07:17,050 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64)]
2025-06-26 17:07:17,071 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:07:17,072 - ExploitationExpert - INFO - populations: [{'tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}, {'tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}, {'tour': [0, 10, 12, 14, 16, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8], 'cur_cost': 6048.0}, {'tour': [0, 10, 12, 14, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8, 16], 'cur_cost': 8119.0}, {'tour': [0, 10, 5, 15, 20, 25, 7, 12, 17, 22, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 27], 'cur_cost': 17999.0}, {'tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}, {'tour': [0, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 26, 9, 8, 7, 6, 5, 4, 3, 2, 1, 16], 'cur_cost': 5934.0}, {'tour': [0, 10, 5, 15, 20, 25, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24], 'cur_cost': 15922.0}, {'tour': array([ 7,  4,  9, 19, 13,  8, 17, 14, 18, 23,  0, 12, 15,  6,  2, 16,  1,
       26, 25, 20,  5,  3, 27, 24, 11, 10, 21, 22]), 'cur_cost': 15878.0}, {'tour': array([15, 19, 16, 12, 21, 11, 22,  9,  8,  4,  5,  2, 13,  7, 25, 24,  1,
       27, 10, 17, 14,  6,  0, 20, 23, 26,  3, 18]), 'cur_cost': 14808.0}]
2025-06-26 17:07:17,072 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 17:07:17,074 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 6, 'total_searches': 114, 'skip_rate': 0.05263157894736842, 'estimated_time_saved': 30, 'path_optimizer': {'path_count': 108, 'cache_hits': 155, 'similarity_calculations': 2783, 'cache_hit_rate': 0.055695292849443044, 'cache_size': 2628}}
2025-06-26 17:07:17,074 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:07:17,074 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 12, 14, 16, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8], 'cur_cost': 6048.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 12, 14, 18, 20, 22, 24, 26, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 2, 4, 6, 8, 16], 'cur_cost': 8119.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 5, 15, 20, 25, 7, 12, 17, 22, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 27], 'cur_cost': 17999.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 5, 15, 20, 25, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24, 2, 7, 12, 17, 22, 27], 'cur_cost': 15923.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 26, 9, 8, 7, 6, 5, 4, 3, 2, 1, 16], 'cur_cost': 5934.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 10, 5, 15, 20, 25, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 1, 6, 11, 16, 21, 26, 4, 9, 14, 19, 24], 'cur_cost': 15922.0}}]
2025-06-26 17:07:17,074 - __main__ - INFO - 进化阶段完成
2025-06-26 17:07:17,074 - __main__ - INFO - 开始评估阶段
2025-06-26 17:07:17,074 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:07:17,076 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:07:17,076 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 3159.0 → 5934.0 (Δ -2775.0)
- Mean cost: 13217.2 → 13247.7
- Diversity: 0.670 → 0.448 (Δ -0.2222)

#### Elite metrics
- Elite count: 0 → 57
- Best elite cost: 3159.0 → 3055 (Δ 104.0)

#### Strategy performance
- Explore: 8 inds, success 50.0% , avg Δ -381.12
- Exploit: 2 inds, success 100.0% , avg Δ 1372.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [177.0, 176.0, 166.0], diversity [0.7451851851851852, 0.7792592592592592, 0.842962962962963]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:07:17,076 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:07:34,204 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_improvement": -381.12,
      "contribution_to_elite": "moderate (negative impact on average but some successful improvements)"
    },
    "exploit": {
      "success_rate": 100.0,
      "average_improvement": 1372.0,
      "contribution_to_elite": "high (consistent positive improvements)"
    }
  },
  "balance_state": {
    "current_ratio": "80% explore, 20% exploit",
    "assessment": "exploit is more effective but underutilized; explore has high failure rate",
    "diversity": "declining sharply (Δ -0.222), risking premature convergence"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "increase exploit attempts slightly (e.g., 30-40% of population) to capitalize on its high success rate while maintaining exploration diversity"
  }
}
```
2025-06-26 17:07:34,210 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:07:34,210 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_improvement": -381.12,
      "contribution_to_elite": "moderate (negative impact on average but some successful improvements)"
    },
    "exploit": {
      "success_rate": 100.0,
      "average_improvement": 1372.0,
      "contribution_to_elite": "high (consistent positive improvements)"
    }
  },
  "balance_state": {
    "current_ratio": "80% explore, 20% exploit",
    "assessment": "exploit is more effective but underutilized; explore has high failure rate",
    "diversity": "declining sharply (Δ -0.222), risking premature convergence"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "increase exploit attempts slightly (e.g., 30-40% of population) to capitalize on its high success rate while maintaining exploration diversity"
  }
}
```
2025-06-26 17:07:34,210 - __main__ - INFO - 评估阶段完成
2025-06-26 17:07:34,212 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 50.0,
      "average_improvement": -381.12,
      "contribution_to_elite": "moderate (negative impact on average but some successful improvements)"
    },
    "exploit": {
      "success_rate": 100.0,
      "average_improvement": 1372.0,
      "contribution_to_elite": "high (consistent positive improvements)"
    }
  },
  "balance_state": {
    "current_ratio": "80% explore, 20% exploit",
    "assessment": "exploit is more effective but underutilized; explore has high failure rate",
    "diversity": "declining sharply (Δ -0.222), risking premature convergence"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "increase exploit attempts slightly (e.g., 30-40% of population) to capitalize on its high success rate while maintaining exploration diversity"
  }
}
```
2025-06-26 17:07:34,213 - __main__ - INFO - 当前最佳适应度: 5934.0
2025-06-26 17:07:34,214 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite1_28_route_0.pkl
2025-06-26 17:07:34,214 - __main__ - INFO - composite1_28 开始进化第 2 代
2025-06-26 17:07:34,215 - __main__ - INFO - 开始分析阶段
2025-06-26 17:07:34,215 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:07:34,218 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 5934.0, 'max': 17999.0, 'mean': 13247.7, 'std': 4383.844067710438}, 'diversity': 0.7301587301587301, 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:07:34,222 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 5934.0, 'max': 17999.0, 'mean': 13247.7, 'std': 4383.844067710438}, 'diversity_level': 0.7301587301587301, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'coordinates': [[438, 1213], [479, 1236], [466, 1259], [438, 1259], [425, 1236], [447, 1243], [464, 1235], [449, 1224], [466, 1213], [110, 246], [151, 223], [138, 246], [110, 200], [138, 200], [132, 229], [116, 229], [116, 217], [132, 217], [97, 223], [693, 219], [665, 219], [652, 196], [665, 173], [693, 173], [679, 203], [687, 189], [671, 189], [706, 196]], 'distance_matrix': array([[   0.,   47.,   54.,   46.,   26.,   31.,   34.,   16.,   28.,
        1021., 1031., 1012., 1065., 1056., 1030., 1035., 1047., 1042.,
        1047., 1026., 1020., 1039., 1064., 1071., 1038., 1054., 1050.,
        1052.],
       [  47.,    0.,   26.,   47.,   54.,   33.,   15.,   32.,   26.,
        1057., 1065., 1047., 1100., 1091., 1065., 1070., 1082., 1076.,
        1083., 1039., 1034., 1054., 1079., 1084., 1052., 1067., 1064.,
        1064.],
       [  54.,   26.,    0.,   28.,   47.,   25.,   24.,   39.,   46.,
        1074., 1083., 1065., 1117., 1109., 1083., 1088., 1099., 1094.,
        1100., 1064., 1059., 1079., 1104., 1109., 1077., 1093., 1089.,
        1090.],
       [  46.,   47.,   28.,    0.,   26.,   18.,   35.,   37.,   54.,
        1065., 1075., 1056., 1109., 1101., 1074., 1079., 1091., 1086.,
        1091., 1071., 1064., 1084., 1109., 1116., 1083., 1099., 1095.,
        1096.],
       [  26.,   54.,   47.,   26.,    0.,   23.,   39.,   27.,   47.,
        1039., 1049., 1031., 1083., 1075., 1049., 1053., 1065., 1060.,
        1065., 1052., 1045., 1064., 1090., 1096., 1064., 1079., 1076.,
        1077.],
       [  31.,   33.,   25.,   18.,   23.,    0.,   19.,   19.,   36.,
        1052., 1062., 1044., 1096., 1088., 1062., 1067., 1078., 1073.,
        1078., 1053., 1047., 1067., 1092., 1098., 1066., 1081., 1078.,
        1079.],
       [  34.,   15.,   24.,   35.,   39.,   19.,    0.,   19.,   22.,
        1050., 1059., 1041., 1094., 1085., 1059., 1064., 1076., 1071.,
        1076., 1041., 1036., 1056., 1081., 1086., 1054., 1070., 1066.,
        1067.],
       [  16.,   32.,   39.,   37.,   27.,   19.,   19.,    0.,   20.,
        1035., 1044., 1026., 1079., 1070., 1044., 1049., 1061., 1056.,
        1061., 1034., 1028., 1048., 1073., 1079., 1047., 1062., 1059.,
        1060.],
       [  28.,   26.,   46.,   54.,   47.,   36.,   22.,   20.,    0.,
        1030., 1039., 1021., 1074., 1065., 1039., 1044., 1056., 1051.,
        1057., 1020., 1014., 1034., 1059., 1064., 1032., 1048., 1044.,
        1045.],
       [1021., 1057., 1074., 1065., 1039., 1052., 1050., 1035., 1030.,
           0.,   47.,   28.,   46.,   54.,   28.,   18.,   30.,   36.,
          26.,  584.,  556.,  544.,  560.,  588.,  571.,  580.,  564.,
         598.],
       [1031., 1065., 1083., 1075., 1049., 1062., 1059., 1044., 1039.,
          47.,    0.,   26.,   47.,   26.,   20.,   36.,   36.,   20.,
          54.,  542.,  514.,  502.,  516.,  544.,  528.,  537.,  521.,
         556.],
       [1012., 1047., 1065., 1056., 1031., 1044., 1041., 1026., 1021.,
          28.,   26.,    0.,   54.,   46.,   18.,   28.,   36.,   30.,
          47.,  556.,  528.,  516.,  532.,  560.,  543.,  552.,  536.,
         570.],
       [1065., 1100., 1117., 1109., 1083., 1096., 1094., 1079., 1074.,
          46.,   47.,   54.,    0.,   28.,   36.,   30.,   18.,   28.,
          26.,  583.,  555.,  542.,  556.,  584.,  569.,  577.,  561.,
         596.],
       [1056., 1091., 1109., 1101., 1075., 1088., 1085., 1070., 1065.,
          54.,   26.,   46.,   28.,    0.,   30.,   36.,   28.,   18.,
          47.,  555.,  527.,  514.,  528.,  556.,  541.,  549.,  533.,
         568.],
       [1030., 1065., 1083., 1074., 1049., 1062., 1059., 1044., 1039.,
          28.,   20.,   18.,   36.,   30.,    0.,   16.,   20.,   12.,
          36.,  561.,  533.,  521.,  536.,  564.,  548.,  556.,  540.,
         575.],
       [1035., 1070., 1088., 1079., 1053., 1067., 1064., 1049., 1044.,
          18.,   36.,   28.,   30.,   36.,   16.,    0.,   12.,   20.,
          20.,  577.,  549.,  537.,  552.,  580.,  564.,  572.,  556.,
         591.],
       [1047., 1082., 1099., 1091., 1065., 1078., 1076., 1061., 1056.,
          30.,   36.,   36.,   18.,   28.,   20.,   12.,    0.,   16.,
          20.,  577.,  549.,  536.,  551.,  579.,  563.,  572.,  556.,
         590.],
       [1042., 1076., 1094., 1086., 1060., 1073., 1071., 1056., 1051.,
          36.,   20.,   30.,   28.,   18.,   12.,   20.,   16.,    0.,
          36.,  561.,  533.,  520.,  535.,  563.,  547.,  556.,  540.,
         574.],
       [1047., 1083., 1100., 1091., 1065., 1078., 1076., 1061., 1057.,
          26.,   54.,   47.,   26.,   47.,   36.,   20.,   20.,   36.,
           0.,  596.,  568.,  556.,  570.,  598.,  582.,  591.,  575.,
         610.],
       [1026., 1039., 1064., 1071., 1052., 1053., 1041., 1034., 1020.,
         584.,  542.,  556.,  583.,  555.,  561.,  577.,  577.,  561.,
         596.,    0.,   28.,   47.,   54.,   46.,   21.,   31.,   37.,
          26.],
       [1020., 1034., 1059., 1064., 1045., 1047., 1036., 1028., 1014.,
         556.,  514.,  528.,  555.,  527.,  533.,  549.,  549.,  533.,
         568.,   28.,    0.,   26.,   46.,   54.,   21.,   37.,   31.,
          47.],
       [1039., 1054., 1079., 1084., 1064., 1067., 1056., 1048., 1034.,
         544.,  502.,  516.,  542.,  514.,  521.,  537.,  536.,  520.,
         556.,   47.,   26.,    0.,   26.,   47.,   28.,   36.,   20.,
          54.],
       [1064., 1079., 1104., 1109., 1090., 1092., 1081., 1073., 1059.,
         560.,  516.,  532.,  556.,  528.,  536.,  552.,  551.,  535.,
         570.,   54.,   46.,   26.,    0.,   28.,   33.,   27.,   17.,
          47.],
       [1071., 1084., 1109., 1116., 1096., 1098., 1086., 1079., 1064.,
         588.,  544.,  560.,  584.,  556.,  564.,  580.,  579.,  563.,
         598.,   46.,   54.,   47.,   28.,    0.,   33.,   17.,   27.,
          26.],
       [1038., 1052., 1077., 1083., 1064., 1066., 1054., 1047., 1032.,
         571.,  528.,  543.,  569.,  541.,  548.,  564.,  563.,  547.,
         582.,   21.,   21.,   28.,   33.,   33.,    0.,   16.,   16.,
          28.],
       [1054., 1067., 1093., 1099., 1079., 1081., 1070., 1062., 1048.,
         580.,  537.,  552.,  577.,  549.,  556.,  572.,  572.,  556.,
         591.,   31.,   37.,   36.,   27.,   17.,   16.,    0.,   16.,
          20.],
       [1050., 1064., 1089., 1095., 1076., 1078., 1066., 1059., 1044.,
         564.,  521.,  536.,  561.,  533.,  540.,  556.,  556.,  540.,
         575.,   37.,   31.,   20.,   17.,   27.,   16.,   16.,    0.,
          36.],
       [1052., 1064., 1090., 1096., 1077., 1079., 1067., 1060., 1045.,
         598.,  556.,  570.,  596.,  568.,  575.,  591.,  590.,  574.,
         610.,   26.,   47.,   54.,   47.,   26.,   28.,   20.,   36.,
           0.]])}
2025-06-26 17:07:34,223 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:07:34,223 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:07:34,223 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:07:34,223 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:07:34,223 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:07:34,223 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:07:34,223 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:07:34,223 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:07:34,223 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:07:34,246 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:07:34,247 - __main__ - INFO - 精英专家分析报告: {'elite_count': 57, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 2879.0, 'avg_gap': 10192.7}, 'structure_gap': {'unique_elite_edges': 60, 'unique_pop_edges': 108, 'common_edges': 30}}, 'elite_diversity': {'diversity_score': 0.2836108127461511}}
2025-06-26 17:07:34,247 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:07:34,247 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:07:34,247 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:07:34,247 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 5934.0, mean 13247.7, max 17999.0, std 4383.844067710438
- diversity: 0.7301587301587301
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [176.0, 166.0, 5934.0] (Δ 5.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [97.0, 173.0, 706.0, 1259.0]
- centroid/spread: [407.964, 539.464] | rms 532.029
- density_grid(3x3): [[10, 0, 9], [0, 0, 0], [0, 9, 0]]
- edge_len_stats(min,q1,med,q3,max): [12.0, 46.0, 577.0, 1062.0, 1117.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [5, 4, 3, 4, 6, 0, 4, 1]
- nn_median_dist: 18.028

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:07:34,247 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:07:59,386 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 12.0 to 1117.0, high std deviation in cost_stats)",
    "modality": "Potentially multimodal (diverse direction histogram, no clear clustering yet)",
    "deceptiveness": "Possible (long_edge_ratio of 0.249 suggests some deceptive long edges, recent_best_costs shows sudden improvement)"
  },
  "population_state": {
    "coverage": "Fragmented (density_grid shows clusters in corners with empty center, diversity 0.73)",
    "convergence": "Early stage (convergence 0.0, no stagnation detected)"
  },
  "difficult_regions": [
    "Central grid cells (empty in density_grid)",
    "Edges longer than Q3 (1062.0+) from edge_len_stats",
    "Direction sectors with low counts (sectors 5,7 in direction_hist)"
  ],
  "opportunity_regions": [
    "High-density grid cells (top-left and bottom-right in density_grid)",
    "Short edges (< Q1 46.0) from edge_len_stats",
    "Direction sectors with high counts (sectors 0,4 in direction_hist)"
  ],
  "evolution_phase": "Exploration (high diversity, no elites, early iteration)",
  "evolution_direction": [
    "Prioritize exploitation in high-density cells (e.g., local search around nodes in top-left/bottom-right clusters)",
    "Add targeted exploration in central regions (e.g., forced insertion of central nodes)",
    "Balance edge lengths (penalize very long edges in selection)",
    "Monitor direction histogram for emerging patterns"
  ]
}
```
2025-06-26 17:07:59,387 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:07:59,387 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 12.0 to 1117.0, high std deviation in cost_stats)', 'modality': 'Potentially multimodal (diverse direction histogram, no clear clustering yet)', 'deceptiveness': 'Possible (long_edge_ratio of 0.249 suggests some deceptive long edges, recent_best_costs shows sudden improvement)'}, 'population_state': {'coverage': 'Fragmented (density_grid shows clusters in corners with empty center, diversity 0.73)', 'convergence': 'Early stage (convergence 0.0, no stagnation detected)'}, 'difficult_regions': ['Central grid cells (empty in density_grid)', 'Edges longer than Q3 (1062.0+) from edge_len_stats', 'Direction sectors with low counts (sectors 5,7 in direction_hist)'], 'opportunity_regions': ['High-density grid cells (top-left and bottom-right in density_grid)', 'Short edges (< Q1 46.0) from edge_len_stats', 'Direction sectors with high counts (sectors 0,4 in direction_hist)'], 'evolution_phase': 'Exploration (high diversity, no elites, early iteration)', 'evolution_direction': ['Prioritize exploitation in high-density cells (e.g., local search around nodes in top-left/bottom-right clusters)', 'Add targeted exploration in central regions (e.g., forced insertion of central nodes)', 'Balance edge lengths (penalize very long edges in selection)', 'Monitor direction histogram for emerging patterns']}
2025-06-26 17:07:59,387 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:07:59,387 - __main__ - INFO - 分析阶段完成
2025-06-26 17:07:59,388 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 12.0 to 1117.0, high std deviation in cost_stats)', 'modality': 'Potentially multimodal (diverse direction histogram, no clear clustering yet)', 'deceptiveness': 'Possible (long_edge_ratio of 0.249 suggests some deceptive long edges, recent_best_costs shows sudden improvement)'}, 'population_state': {'coverage': 'Fragmented (density_grid shows clusters in corners with empty center, diversity 0.73)', 'convergence': 'Early stage (convergence 0.0, no stagnation detected)'}, 'difficult_regions': ['Central grid cells (empty in density_grid)', 'Edges longer than Q3 (1062.0+) from edge_len_stats', 'Direction sectors with low counts (sectors 5,7 in direction_hist)'], 'opportunity_regions': ['High-density grid cells (top-left and bottom-right in density_grid)', 'Short edges (< Q1 46.0) from edge_len_stats', 'Direction sectors with high counts (sectors 0,4 in direction_hist)'], 'evolution_phase': 'Exploration (high diversity, no elites, early iteration)', 'evolution_direction': ['Prioritize exploitation in high-density cells (e.g., local search around nodes in top-left/bottom-right clusters)', 'Add targeted exploration in central regions (e.g., forced insertion of central nodes)', 'Balance edge lengths (penalize very long edges in selection)', 'Monitor direction histogram for emerging patterns']}
2025-06-26 17:07:59,388 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:07:59,388 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:07:59,388 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 5934.0
  • mean_cost: 13247.7
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 12.0 to 1117.0, high std deviation in cost_stats), modality Potentially multimodal (diverse direction histogram, no clear clustering yet), deceptiveness Possible (long_edge_ratio of 0.249 suggests some deceptive long edges, recent_best_costs shows sudden improvement)
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): Central grid cells (, Edges longer than Q3...
- Opportunity regions (# 3): High-density grid ce, Short edges (< Q1 46...
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:07:59,388 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:07:59,389 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:08:10,011 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 17:08:10,014 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:08:10,016 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:08:10,016 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 17:08:10,016 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:08:10,016 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:08:10,016 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 17:08:10,017 - __main__ - INFO - 开始进化阶段
2025-06-26 17:08:10,017 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:08:10,018 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:08:10,018 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15923.0 (rank 60.0%)
- Population diversity: 0.44814814814814813
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (< ...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:08:10,018 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:08:10,018 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:08:20,948 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24],
  "targeted_regions": "High-density grid, short edges",
  "strategy_comment": "Leverage grid structure for short edges, avoid central congestion"
}
```
2025-06-26 17:08:20,950 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:08:20,951 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13890.0, 路径: [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24]
2025-06-26 17:08:20,951 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}
2025-06-26 17:08:20,951 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:08:20,951 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:08:20,951 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15923.0 (rank 70.0%)
- Population diversity: 0.4666666666666667
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (< ...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:08:20,951 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:08:20,951 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:08:31,783 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1],
  "targeted_regions": "High-density grid, short edges",
  "strategy_comment": "Alternating high-low node traversal to balance density"
}
```
2025-06-26 17:08:31,784 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:08:31,784 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3982.0, 路径: [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1]
2025-06-26 17:08:31,784 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 3982.0}
2025-06-26 17:08:31,784 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:08:31,784 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:08:31,784 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 6048.0 (rank 20.0%)
- Population diversity: 0.4740740740740741
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (< ...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:08:31,784 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:08:31,784 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:08:42,124 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density gr, Short edges",
  "strategy_comment": "Prioritize short edges in high-density regions"
}
```
2025-06-26 17:08:42,124 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:08:42,124 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:08:42,124 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:08:42,124 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:08:42,124 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:08:42,124 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 8119.0 (rank 30.0%)
- Population diversity: 0.4888888888888889
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (< ...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:08:42,124 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:08:42,129 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:08:52,313 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density gr, Short edges",
  "strategy_comment": "Sequential traversal to explore adjacent low-density cells"
}
```
2025-06-26 17:08:52,314 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:08:52,314 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:08:52,314 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:08:52,316 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:08:52,316 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:08:52,316 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17999.0 (rank 90.0%)
- Population diversity: 0.48148148148148145
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (< ...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:08:52,316 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:08:52,316 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:09:02,555 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24],
  "targeted_regions": "High-density grid, short edges",
  "strategy_comment": "Leverage grid structure for short edges, avoid central congestion"
}
```
2025-06-26 17:09:02,555 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:09:02,555 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13890.0, 路径: [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24]
2025-06-26 17:09:02,555 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}
2025-06-26 17:09:02,556 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:09:02,556 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:09:02,556 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15923.0 (rank 90.0%)
- Population diversity: 0.4740740740740741
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (< ...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:09:02,556 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:09:02,556 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:09:13,738 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density gr, Short edges",
  "strategy_comment": "Prioritize short edges in high-density regions"
}
```
2025-06-26 17:09:13,738 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:09:13,738 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:09:13,738 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:09:13,738 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 17:09:13,738 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:09:13,738 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 5934.0 (rank 40.0%)
- Population diversity: 0.4666666666666667
- Evolution phase: Exploration (high diversity, no elites, early iteration)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (< ...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:09:13,738 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:09:13,738 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:09:23,518 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density grid, short edges",
  "strategy_comment": "Sequential path to explore adjacent low-density cells"
}
```
2025-06-26 17:09:23,521 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:09:23,521 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:09:23,521 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:09:23,521 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:09:23,521 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:09:23,521 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:09:23,521 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 22658.0
2025-06-26 17:09:24,022 - ExploitationExpert - INFO - res_population_num: 62
2025-06-26 17:09:24,023 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:09:24,023 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64)]
2025-06-26 17:09:24,039 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:09:24,039 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 3982.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 7,  5, 15,  4, 11,  3, 14,  2, 22, 18, 25,  9, 23, 21,  1, 13, 27,
       17, 20,  6, 24, 10,  0, 16,  8, 19, 12, 26]), 'cur_cost': 22658.0}, {'tour': array([ 7,  4,  9, 19, 13,  8, 17, 14, 18, 23,  0, 12, 15,  6,  2, 16,  1,
       26, 25, 20,  5,  3, 27, 24, 11, 10, 21, 22]), 'cur_cost': 15878.0}, {'tour': array([15, 19, 16, 12, 21, 11, 22,  9,  8,  4,  5,  2, 13,  7, 25, 24,  1,
       27, 10, 17, 14,  6,  0, 20, 23, 26,  3, 18]), 'cur_cost': 14808.0}]
2025-06-26 17:09:24,041 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:09:24,041 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 6, 'total_searches': 115, 'skip_rate': 0.05217391304347826, 'estimated_time_saved': 30, 'path_optimizer': {'path_count': 109, 'cache_hits': 155, 'similarity_calculations': 2785, 'cache_hit_rate': 0.05565529622980251, 'cache_size': 2630}}
2025-06-26 17:09:24,041 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:09:24,041 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:09:24,042 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:09:24,042 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:09:24,042 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 19016.0
2025-06-26 17:09:24,545 - ExploitationExpert - INFO - res_population_num: 68
2025-06-26 17:09:24,545 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:09:24,546 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64)]
2025-06-26 17:09:24,564 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:09:24,564 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 3982.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 7,  5, 15,  4, 11,  3, 14,  2, 22, 18, 25,  9, 23, 21,  1, 13, 27,
       17, 20,  6, 24, 10,  0, 16,  8, 19, 12, 26]), 'cur_cost': 22658.0}, {'tour': array([ 2, 24,  6, 21, 25, 26,  4, 18,  3, 13,  9, 19,  0,  7, 10, 16, 12,
       22,  1, 14, 15,  8, 23,  5, 11, 17, 20, 27]), 'cur_cost': 19016.0}, {'tour': array([15, 19, 16, 12, 21, 11, 22,  9,  8,  4,  5,  2, 13,  7, 25, 24,  1,
       27, 10, 17, 14,  6,  0, 20, 23, 26,  3, 18]), 'cur_cost': 14808.0}]
2025-06-26 17:09:24,565 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:09:24,565 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 6, 'total_searches': 116, 'skip_rate': 0.05172413793103448, 'estimated_time_saved': 30, 'path_optimizer': {'path_count': 110, 'cache_hits': 155, 'similarity_calculations': 2788, 'cache_hit_rate': 0.05559540889526542, 'cache_size': 2633}}
2025-06-26 17:09:24,567 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:09:24,567 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:09:24,567 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:09:24,567 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:09:24,567 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 17003.0
2025-06-26 17:09:25,069 - ExploitationExpert - INFO - res_population_num: 69
2025-06-26 17:09:25,070 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:09:25,070 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64)]
2025-06-26 17:09:25,087 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:09:25,088 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 3982.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 7,  5, 15,  4, 11,  3, 14,  2, 22, 18, 25,  9, 23, 21,  1, 13, 27,
       17, 20,  6, 24, 10,  0, 16,  8, 19, 12, 26]), 'cur_cost': 22658.0}, {'tour': array([ 2, 24,  6, 21, 25, 26,  4, 18,  3, 13,  9, 19,  0,  7, 10, 16, 12,
       22,  1, 14, 15,  8, 23,  5, 11, 17, 20, 27]), 'cur_cost': 19016.0}, {'tour': array([11,  0, 27, 19, 14, 16, 25, 13, 10, 18, 21, 15,  5, 23,  4,  2,  7,
       26, 20, 24, 12,  1,  9,  3,  8, 17,  6, 22]), 'cur_cost': 17003.0}]
2025-06-26 17:09:25,089 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:09:25,089 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 6, 'total_searches': 117, 'skip_rate': 0.05128205128205128, 'estimated_time_saved': 30, 'path_optimizer': {'path_count': 111, 'cache_hits': 155, 'similarity_calculations': 2792, 'cache_hit_rate': 0.05551575931232092, 'cache_size': 2637}}
2025-06-26 17:09:25,090 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:09:25,090 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 2, 1], 'cur_cost': 3982.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}]
2025-06-26 17:09:25,090 - __main__ - INFO - 进化阶段完成
2025-06-26 17:09:25,091 - __main__ - INFO - 开始评估阶段
2025-06-26 17:09:25,091 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:09:25,092 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:09:25,092 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 5934.0 → 3360.0 (Δ 2574.0)
- Mean cost: 13247.7 → 10387.9
- Diversity: 0.448 → 0.541 (Δ 0.0926)

#### Elite metrics
- Elite count: 57 → 69
- Best elite cost: 3055 → 3055 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 100.0% , avg Δ 5809.57
- Exploit: 3 inds, success 0.0% , avg Δ -4023.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 3
- Historical trends: Last 3 iterations: costs [176.0, 166.0, 5934.0], diversity [0.7792592592592592, 0.842962962962963, 0.7301587301587301]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:09:25,093 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:09:38,274 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution_to_elite": "high",
      "success_rate": 100.0,
      "average_improvement": 5809.57
    },
    "exploit": {
      "contribution_to_elite": "none",
      "success_rate": 0.0,
      "average_improvement": -4023.0
    }
  },
  "balance_state": "explore-heavy (effective), exploit-ineffective",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "mutation_intensity": "increase slightly",
    "exploit_retention": "reduce temporarily"
  }
}
```
2025-06-26 17:09:38,283 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:09:38,283 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution_to_elite": "high",
      "success_rate": 100.0,
      "average_improvement": 5809.57
    },
    "exploit": {
      "contribution_to_elite": "none",
      "success_rate": 0.0,
      "average_improvement": -4023.0
    }
  },
  "balance_state": "explore-heavy (effective), exploit-ineffective",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "mutation_intensity": "increase slightly",
    "exploit_retention": "reduce temporarily"
  }
}
```
2025-06-26 17:09:38,283 - __main__ - INFO - 评估阶段完成
2025-06-26 17:09:38,286 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution_to_elite": "high",
      "success_rate": 100.0,
      "average_improvement": 5809.57
    },
    "exploit": {
      "contribution_to_elite": "none",
      "success_rate": 0.0,
      "average_improvement": -4023.0
    }
  },
  "balance_state": "explore-heavy (effective), exploit-ineffective",
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "mutation_intensity": "increase slightly",
    "exploit_retention": "reduce temporarily"
  }
}
```
2025-06-26 17:09:38,286 - __main__ - INFO - 当前最佳适应度: 3360.0
2025-06-26 17:09:38,287 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite1_28_route_1.pkl
2025-06-26 17:09:38,289 - __main__ - INFO - composite1_28 开始进化第 3 代
2025-06-26 17:09:38,289 - __main__ - INFO - 开始分析阶段
2025-06-26 17:09:38,289 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:09:38,294 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 3360.0, 'max': 22658.0, 'mean': 10387.9, 'std': 7293.07540684998}, 'diversity': 0.8119047619047619, 'clusters': {'clusters': 6, 'cluster_sizes': [2, 1, 4, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:09:38,301 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 3360.0, 'max': 22658.0, 'mean': 10387.9, 'std': 7293.07540684998}, 'diversity_level': 0.8119047619047619, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [2, 1, 4, 1, 1, 1]}, 'coordinates': [[438, 1213], [479, 1236], [466, 1259], [438, 1259], [425, 1236], [447, 1243], [464, 1235], [449, 1224], [466, 1213], [110, 246], [151, 223], [138, 246], [110, 200], [138, 200], [132, 229], [116, 229], [116, 217], [132, 217], [97, 223], [693, 219], [665, 219], [652, 196], [665, 173], [693, 173], [679, 203], [687, 189], [671, 189], [706, 196]], 'distance_matrix': array([[   0.,   47.,   54.,   46.,   26.,   31.,   34.,   16.,   28.,
        1021., 1031., 1012., 1065., 1056., 1030., 1035., 1047., 1042.,
        1047., 1026., 1020., 1039., 1064., 1071., 1038., 1054., 1050.,
        1052.],
       [  47.,    0.,   26.,   47.,   54.,   33.,   15.,   32.,   26.,
        1057., 1065., 1047., 1100., 1091., 1065., 1070., 1082., 1076.,
        1083., 1039., 1034., 1054., 1079., 1084., 1052., 1067., 1064.,
        1064.],
       [  54.,   26.,    0.,   28.,   47.,   25.,   24.,   39.,   46.,
        1074., 1083., 1065., 1117., 1109., 1083., 1088., 1099., 1094.,
        1100., 1064., 1059., 1079., 1104., 1109., 1077., 1093., 1089.,
        1090.],
       [  46.,   47.,   28.,    0.,   26.,   18.,   35.,   37.,   54.,
        1065., 1075., 1056., 1109., 1101., 1074., 1079., 1091., 1086.,
        1091., 1071., 1064., 1084., 1109., 1116., 1083., 1099., 1095.,
        1096.],
       [  26.,   54.,   47.,   26.,    0.,   23.,   39.,   27.,   47.,
        1039., 1049., 1031., 1083., 1075., 1049., 1053., 1065., 1060.,
        1065., 1052., 1045., 1064., 1090., 1096., 1064., 1079., 1076.,
        1077.],
       [  31.,   33.,   25.,   18.,   23.,    0.,   19.,   19.,   36.,
        1052., 1062., 1044., 1096., 1088., 1062., 1067., 1078., 1073.,
        1078., 1053., 1047., 1067., 1092., 1098., 1066., 1081., 1078.,
        1079.],
       [  34.,   15.,   24.,   35.,   39.,   19.,    0.,   19.,   22.,
        1050., 1059., 1041., 1094., 1085., 1059., 1064., 1076., 1071.,
        1076., 1041., 1036., 1056., 1081., 1086., 1054., 1070., 1066.,
        1067.],
       [  16.,   32.,   39.,   37.,   27.,   19.,   19.,    0.,   20.,
        1035., 1044., 1026., 1079., 1070., 1044., 1049., 1061., 1056.,
        1061., 1034., 1028., 1048., 1073., 1079., 1047., 1062., 1059.,
        1060.],
       [  28.,   26.,   46.,   54.,   47.,   36.,   22.,   20.,    0.,
        1030., 1039., 1021., 1074., 1065., 1039., 1044., 1056., 1051.,
        1057., 1020., 1014., 1034., 1059., 1064., 1032., 1048., 1044.,
        1045.],
       [1021., 1057., 1074., 1065., 1039., 1052., 1050., 1035., 1030.,
           0.,   47.,   28.,   46.,   54.,   28.,   18.,   30.,   36.,
          26.,  584.,  556.,  544.,  560.,  588.,  571.,  580.,  564.,
         598.],
       [1031., 1065., 1083., 1075., 1049., 1062., 1059., 1044., 1039.,
          47.,    0.,   26.,   47.,   26.,   20.,   36.,   36.,   20.,
          54.,  542.,  514.,  502.,  516.,  544.,  528.,  537.,  521.,
         556.],
       [1012., 1047., 1065., 1056., 1031., 1044., 1041., 1026., 1021.,
          28.,   26.,    0.,   54.,   46.,   18.,   28.,   36.,   30.,
          47.,  556.,  528.,  516.,  532.,  560.,  543.,  552.,  536.,
         570.],
       [1065., 1100., 1117., 1109., 1083., 1096., 1094., 1079., 1074.,
          46.,   47.,   54.,    0.,   28.,   36.,   30.,   18.,   28.,
          26.,  583.,  555.,  542.,  556.,  584.,  569.,  577.,  561.,
         596.],
       [1056., 1091., 1109., 1101., 1075., 1088., 1085., 1070., 1065.,
          54.,   26.,   46.,   28.,    0.,   30.,   36.,   28.,   18.,
          47.,  555.,  527.,  514.,  528.,  556.,  541.,  549.,  533.,
         568.],
       [1030., 1065., 1083., 1074., 1049., 1062., 1059., 1044., 1039.,
          28.,   20.,   18.,   36.,   30.,    0.,   16.,   20.,   12.,
          36.,  561.,  533.,  521.,  536.,  564.,  548.,  556.,  540.,
         575.],
       [1035., 1070., 1088., 1079., 1053., 1067., 1064., 1049., 1044.,
          18.,   36.,   28.,   30.,   36.,   16.,    0.,   12.,   20.,
          20.,  577.,  549.,  537.,  552.,  580.,  564.,  572.,  556.,
         591.],
       [1047., 1082., 1099., 1091., 1065., 1078., 1076., 1061., 1056.,
          30.,   36.,   36.,   18.,   28.,   20.,   12.,    0.,   16.,
          20.,  577.,  549.,  536.,  551.,  579.,  563.,  572.,  556.,
         590.],
       [1042., 1076., 1094., 1086., 1060., 1073., 1071., 1056., 1051.,
          36.,   20.,   30.,   28.,   18.,   12.,   20.,   16.,    0.,
          36.,  561.,  533.,  520.,  535.,  563.,  547.,  556.,  540.,
         574.],
       [1047., 1083., 1100., 1091., 1065., 1078., 1076., 1061., 1057.,
          26.,   54.,   47.,   26.,   47.,   36.,   20.,   20.,   36.,
           0.,  596.,  568.,  556.,  570.,  598.,  582.,  591.,  575.,
         610.],
       [1026., 1039., 1064., 1071., 1052., 1053., 1041., 1034., 1020.,
         584.,  542.,  556.,  583.,  555.,  561.,  577.,  577.,  561.,
         596.,    0.,   28.,   47.,   54.,   46.,   21.,   31.,   37.,
          26.],
       [1020., 1034., 1059., 1064., 1045., 1047., 1036., 1028., 1014.,
         556.,  514.,  528.,  555.,  527.,  533.,  549.,  549.,  533.,
         568.,   28.,    0.,   26.,   46.,   54.,   21.,   37.,   31.,
          47.],
       [1039., 1054., 1079., 1084., 1064., 1067., 1056., 1048., 1034.,
         544.,  502.,  516.,  542.,  514.,  521.,  537.,  536.,  520.,
         556.,   47.,   26.,    0.,   26.,   47.,   28.,   36.,   20.,
          54.],
       [1064., 1079., 1104., 1109., 1090., 1092., 1081., 1073., 1059.,
         560.,  516.,  532.,  556.,  528.,  536.,  552.,  551.,  535.,
         570.,   54.,   46.,   26.,    0.,   28.,   33.,   27.,   17.,
          47.],
       [1071., 1084., 1109., 1116., 1096., 1098., 1086., 1079., 1064.,
         588.,  544.,  560.,  584.,  556.,  564.,  580.,  579.,  563.,
         598.,   46.,   54.,   47.,   28.,    0.,   33.,   17.,   27.,
          26.],
       [1038., 1052., 1077., 1083., 1064., 1066., 1054., 1047., 1032.,
         571.,  528.,  543.,  569.,  541.,  548.,  564.,  563.,  547.,
         582.,   21.,   21.,   28.,   33.,   33.,    0.,   16.,   16.,
          28.],
       [1054., 1067., 1093., 1099., 1079., 1081., 1070., 1062., 1048.,
         580.,  537.,  552.,  577.,  549.,  556.,  572.,  572.,  556.,
         591.,   31.,   37.,   36.,   27.,   17.,   16.,    0.,   16.,
          20.],
       [1050., 1064., 1089., 1095., 1076., 1078., 1066., 1059., 1044.,
         564.,  521.,  536.,  561.,  533.,  540.,  556.,  556.,  540.,
         575.,   37.,   31.,   20.,   17.,   27.,   16.,   16.,    0.,
          36.],
       [1052., 1064., 1090., 1096., 1077., 1079., 1067., 1060., 1045.,
         598.,  556.,  570.,  596.,  568.,  575.,  591.,  590.,  574.,
         610.,   26.,   47.,   54.,   47.,   26.,   28.,   20.,   36.,
           0.]])}
2025-06-26 17:09:38,301 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:09:38,302 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:09:38,302 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:09:38,303 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:09:38,303 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:09:38,303 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:09:38,304 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:09:38,304 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:09:38,304 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:09:38,340 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:09:38,341 - __main__ - INFO - 精英专家分析报告: {'elite_count': 69, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 305.0, 'avg_gap': 7332.9}, 'structure_gap': {'unique_elite_edges': 59, 'unique_pop_edges': 130, 'common_edges': 31}}, 'elite_diversity': {'diversity_score': 0.2850748995250274}}
2025-06-26 17:09:38,341 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:09:38,341 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:09:38,341 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:09:38,341 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 3360.0, mean 10387.9, max 22658.0, std 7293.07540684998
- diversity: 0.8119047619047619
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [166.0, 5934.0, 3360.0] (Δ 1287.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [97.0, 173.0, 706.0, 1259.0]
- centroid/spread: [407.964, 539.464] | rms 532.029
- density_grid(3x3): [[10, 0, 9], [0, 0, 0], [0, 9, 0]]
- edge_len_stats(min,q1,med,q3,max): [12.0, 46.0, 577.0, 1062.0, 1117.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [5, 4, 3, 4, 6, 0, 4, 1]
- nn_median_dist: 18.028

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:09:38,341 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:10:03,102 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 12.0 to 1117.0, high std deviation in cost_stats)",
    "modality": "Potentially multimodal (diverse direction_hist sectors, no clear clustering yet)",
    "deceptiveness": "Possible due to long_edge_ratio (24.9% long edges) and uneven density_grid distribution"
  },
  "population_state": {
    "coverage": "Fragmented (density_grid shows clusters at corners with empty center, diversity=0.81)",
    "convergence": "Early-stage (convergence=0.0, recent best costs show improvement Δ1287/iter)"
  },
  "difficult_regions": [
    "Central grid cells (empty in density_grid)",
    "Edges longer than Q3 (1062.0) in edge_len_stats",
    "Direction sectors with low counts (sectors 5,7 in direction_hist)"
  ],
  "opportunity_regions": [
    "High-density grid cells (top-left and bottom-right in density_grid)",
    "Short edges (Q1=46.0 in edge_len_stats)",
    "Direction sectors with high counts (sectors 0,4 in direction_hist)"
  ],
  "evolution_phase": "Exploration (high diversity, no elites, improving but unstable best costs)",
  "evolution_direction": [
    "Prioritize edge-exchange mutations targeting long edges (>1062.0)",
    "Introduce path-guided crossover focusing on high-density regions",
    "Maintain diversity through restart mechanisms for empty grid cells",
    "Exploit direction_hist biases (reinforce sectors 0,4 patterns)"
  ]
}
```
2025-06-26 17:10:03,102 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:10:03,104 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 12.0 to 1117.0, high std deviation in cost_stats)', 'modality': 'Potentially multimodal (diverse direction_hist sectors, no clear clustering yet)', 'deceptiveness': 'Possible due to long_edge_ratio (24.9% long edges) and uneven density_grid distribution'}, 'population_state': {'coverage': 'Fragmented (density_grid shows clusters at corners with empty center, diversity=0.81)', 'convergence': 'Early-stage (convergence=0.0, recent best costs show improvement Δ1287/iter)'}, 'difficult_regions': ['Central grid cells (empty in density_grid)', 'Edges longer than Q3 (1062.0) in edge_len_stats', 'Direction sectors with low counts (sectors 5,7 in direction_hist)'], 'opportunity_regions': ['High-density grid cells (top-left and bottom-right in density_grid)', 'Short edges (Q1=46.0 in edge_len_stats)', 'Direction sectors with high counts (sectors 0,4 in direction_hist)'], 'evolution_phase': 'Exploration (high diversity, no elites, improving but unstable best costs)', 'evolution_direction': ['Prioritize edge-exchange mutations targeting long edges (>1062.0)', 'Introduce path-guided crossover focusing on high-density regions', 'Maintain diversity through restart mechanisms for empty grid cells', 'Exploit direction_hist biases (reinforce sectors 0,4 patterns)']}
2025-06-26 17:10:03,104 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:10:03,104 - __main__ - INFO - 分析阶段完成
2025-06-26 17:10:03,104 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 12.0 to 1117.0, high std deviation in cost_stats)', 'modality': 'Potentially multimodal (diverse direction_hist sectors, no clear clustering yet)', 'deceptiveness': 'Possible due to long_edge_ratio (24.9% long edges) and uneven density_grid distribution'}, 'population_state': {'coverage': 'Fragmented (density_grid shows clusters at corners with empty center, diversity=0.81)', 'convergence': 'Early-stage (convergence=0.0, recent best costs show improvement Δ1287/iter)'}, 'difficult_regions': ['Central grid cells (empty in density_grid)', 'Edges longer than Q3 (1062.0) in edge_len_stats', 'Direction sectors with low counts (sectors 5,7 in direction_hist)'], 'opportunity_regions': ['High-density grid cells (top-left and bottom-right in density_grid)', 'Short edges (Q1=46.0 in edge_len_stats)', 'Direction sectors with high counts (sectors 0,4 in direction_hist)'], 'evolution_phase': 'Exploration (high diversity, no elites, improving but unstable best costs)', 'evolution_direction': ['Prioritize edge-exchange mutations targeting long edges (>1062.0)', 'Introduce path-guided crossover focusing on high-density regions', 'Maintain diversity through restart mechanisms for empty grid cells', 'Exploit direction_hist biases (reinforce sectors 0,4 patterns)']}
2025-06-26 17:10:03,104 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:10:03,104 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:10:03,104 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 3360.0
  • mean_cost: 10387.9
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 12.0 to 1117.0, high std deviation in cost_stats), modality Potentially multimodal (diverse direction_hist sectors, no clear clustering yet), deceptiveness Possible due to long_edge_ratio (24.9% long edges) and uneven density_grid distribution
- Evolution phase: Exploration (high diversity, no elites, improving but unstable best costs)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): Central grid cells (, Edges longer than Q3...
- Opportunity regions (# 3): High-density grid ce, Short edges (Q1=46.0...
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribut...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:10:03,104 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:10:03,104 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:10:16,787 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 60% explore to discover new regions, bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 17:10:16,788 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:10:16,788 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:10:16,789 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 60% explore to discover new regions, bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 17:10:16,789 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:10:16,789 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 17:10:16,789 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration. Top 60% explore to discover new regions, bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 17:10:16,789 - __main__ - INFO - 开始进化阶段
2025-06-26 17:10:16,789 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:10:16,789 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:10:16,789 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13890.0 (rank 50.0%)
- Population diversity: 0.5407407407407407
- Evolution phase: Exploration (high diversity, no elites, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (Q1...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:10:16,789 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:10:16,789 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:10:27,021 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density gr, Short edges (Q1)",
  "strategy_comment": "Prioritize short edges in high-density quadrant"
}
```
2025-06-26 17:10:27,021 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:10:27,026 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:10:27,026 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:10:27,026 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:10:27,027 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:10:27,028 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3982.0 (rank 50.0%)
- Population diversity: 0.5407407407407407
- Evolution phase: Exploration (high diversity, no elites, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (Q1...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:10:27,028 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:10:27,028 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:10:37,353 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24],
  "targeted_regions": "High-density grid, short edges (Q1)",
  "strategy_comment": "Leverage grid structure for low-cost transitions"
}
```
2025-06-26 17:10:37,354 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:10:37,354 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13890.0, 路径: [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24]
2025-06-26 17:10:37,354 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}
2025-06-26 17:10:37,354 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:10:37,354 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:10:37,354 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3360.0 (rank 0.0%)
- Population diversity: 0.46296296296296297
- Evolution phase: Exploration (high diversity, no elites, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (Q1...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:10:37,354 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:10:37,354 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:10:49,796 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density gr, Short edges (Q1)",
  "strategy_comment": "Prioritize short edges in high-density regions"
}
```
2025-06-26 17:10:49,796 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:10:49,796 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:10:49,796 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:10:49,796 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:10:49,796 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:10:49,796 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3360.0 (rank 0.0%)
- Population diversity: 0.46296296296296297
- Evolution phase: Exploration (high diversity, no elites, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (Q1...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:10:49,796 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:10:49,796 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:11:05,712 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density gr, Short edges (Q1)",
  "strategy_comment": "Sequential path to explore adjacent low-density cells"
}
```
2025-06-26 17:11:05,712 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:11:05,712 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:11:05,712 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:11:05,715 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:11:05,715 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:11:05,715 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13890.0 (rank 50.0%)
- Population diversity: 0.46296296296296297
- Evolution phase: Exploration (high diversity, no elites, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (Q1...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:11:05,715 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:11:05,715 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:11:17,051 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density gr, Short edges (Q1)",
  "strategy_comment": "Sequential path to explore low-density cells"
}
```
2025-06-26 17:11:17,052 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:11:17,052 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:11:17,052 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:11:17,053 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:11:17,053 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:11:17,054 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3360.0 (rank 0.0%)
- Population diversity: 0.46296296296296297
- Evolution phase: Exploration (high diversity, no elites, improving but unstable best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density gr, Short edges (Q1...
- Difficult regions to avoid (sample): Central grid ce, Edges longer th...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:11:17,054 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:11:17,054 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:11:31,163 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density gr, Short edges (Q1)",
  "strategy_comment": "Prioritize short edges in high-density regions"
}
```
2025-06-26 17:11:31,164 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:11:31,165 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:11:31,165 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:11:31,166 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 17:11:31,166 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:11:31,166 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:11:31,167 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 15393.0
2025-06-26 17:11:31,668 - root - WARNING - 无法找到足够的不重叠段 (找到 2/3)，使用退化策略
2025-06-26 17:11:31,669 - ExploitationExpert - INFO - res_population_num: 69
2025-06-26 17:11:31,669 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:11:31,670 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64)]
2025-06-26 17:11:31,686 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:11:31,687 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 2, 23,  3,  7, 19, 27, 15, 17, 13, 20, 25,  6, 22,  8,  5, 26,  9,
       18, 21, 24,  0,  1, 10, 12, 16, 14,  4, 11]), 'cur_cost': 15393.0}, {'tour': array([ 7,  5, 15,  4, 11,  3, 14,  2, 22, 18, 25,  9, 23, 21,  1, 13, 27,
       17, 20,  6, 24, 10,  0, 16,  8, 19, 12, 26]), 'cur_cost': 22658.0}, {'tour': array([ 2, 24,  6, 21, 25, 26,  4, 18,  3, 13,  9, 19,  0,  7, 10, 16, 12,
       22,  1, 14, 15,  8, 23,  5, 11, 17, 20, 27]), 'cur_cost': 19016.0}, {'tour': array([11,  0, 27, 19, 14, 16, 25, 13, 10, 18, 21, 15,  5, 23,  4,  2,  7,
       26, 20, 24, 12,  1,  9,  3,  8, 17,  6, 22]), 'cur_cost': 17003.0}]
2025-06-26 17:11:31,689 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:11:31,689 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 6, 'total_searches': 118, 'skip_rate': 0.05084745762711865, 'estimated_time_saved': 30, 'path_optimizer': {'path_count': 112, 'cache_hits': 155, 'similarity_calculations': 2797, 'cache_hit_rate': 0.05541651769753307, 'cache_size': 2642}}
2025-06-26 17:11:31,689 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 17:11:31,690 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:11:31,690 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:11:31,690 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:11:31,691 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 14940.0
2025-06-26 17:11:32,192 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:11:32,192 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:11:32,192 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64)]
2025-06-26 17:11:32,211 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:11:32,212 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 2, 23,  3,  7, 19, 27, 15, 17, 13, 20, 25,  6, 22,  8,  5, 26,  9,
       18, 21, 24,  0,  1, 10, 12, 16, 14,  4, 11]), 'cur_cost': 15393.0}, {'tour': array([27,  7,  5, 24, 20, 17,  0, 16,  9, 10, 25,  6, 26, 14, 11,  4,  3,
        2, 22, 15, 19, 21, 23, 18, 13,  8,  1, 12]), 'cur_cost': 14940.0}, {'tour': array([ 2, 24,  6, 21, 25, 26,  4, 18,  3, 13,  9, 19,  0,  7, 10, 16, 12,
       22,  1, 14, 15,  8, 23,  5, 11, 17, 20, 27]), 'cur_cost': 19016.0}, {'tour': array([11,  0, 27, 19, 14, 16, 25, 13, 10, 18, 21, 15,  5, 23,  4,  2,  7,
       26, 20, 24, 12,  1,  9,  3,  8, 17,  6, 22]), 'cur_cost': 17003.0}]
2025-06-26 17:11:32,213 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:11:32,213 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 6, 'total_searches': 119, 'skip_rate': 0.05042016806722689, 'estimated_time_saved': 30, 'path_optimizer': {'path_count': 113, 'cache_hits': 155, 'similarity_calculations': 2803, 'cache_hit_rate': 0.05529789511237959, 'cache_size': 2648}}
2025-06-26 17:11:32,214 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:11:32,215 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:11:32,215 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:11:32,215 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:11:32,215 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 15940.0
2025-06-26 17:11:32,717 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:11:32,719 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:11:32,719 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64)]
2025-06-26 17:11:32,737 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:11:32,737 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 2, 23,  3,  7, 19, 27, 15, 17, 13, 20, 25,  6, 22,  8,  5, 26,  9,
       18, 21, 24,  0,  1, 10, 12, 16, 14,  4, 11]), 'cur_cost': 15393.0}, {'tour': array([27,  7,  5, 24, 20, 17,  0, 16,  9, 10, 25,  6, 26, 14, 11,  4,  3,
        2, 22, 15, 19, 21, 23, 18, 13,  8,  1, 12]), 'cur_cost': 14940.0}, {'tour': array([ 2, 25,  9, 18, 10,  5, 11, 15,  8,  4,  1, 24, 13, 19,  6,  7, 22,
       26,  0, 17,  3, 12, 23, 27, 20, 21, 14, 16]), 'cur_cost': 15940.0}, {'tour': array([11,  0, 27, 19, 14, 16, 25, 13, 10, 18, 21, 15,  5, 23,  4,  2,  7,
       26, 20, 24, 12,  1,  9,  3,  8, 17,  6, 22]), 'cur_cost': 17003.0}]
2025-06-26 17:11:32,738 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:11:32,738 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 6, 'total_searches': 120, 'skip_rate': 0.05, 'estimated_time_saved': 30, 'path_optimizer': {'path_count': 114, 'cache_hits': 155, 'similarity_calculations': 2810, 'cache_hit_rate': 0.05516014234875445, 'cache_size': 2655}}
2025-06-26 17:11:32,739 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 17:11:32,739 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:11:32,739 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:11:32,740 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:11:32,740 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 13269.0
2025-06-26 17:11:33,241 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:11:33,241 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:11:33,241 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64)]
2025-06-26 17:11:33,260 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:11:33,261 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 2, 23,  3,  7, 19, 27, 15, 17, 13, 20, 25,  6, 22,  8,  5, 26,  9,
       18, 21, 24,  0,  1, 10, 12, 16, 14,  4, 11]), 'cur_cost': 15393.0}, {'tour': array([27,  7,  5, 24, 20, 17,  0, 16,  9, 10, 25,  6, 26, 14, 11,  4,  3,
        2, 22, 15, 19, 21, 23, 18, 13,  8,  1, 12]), 'cur_cost': 14940.0}, {'tour': array([ 2, 25,  9, 18, 10,  5, 11, 15,  8,  4,  1, 24, 13, 19,  6,  7, 22,
       26,  0, 17,  3, 12, 23, 27, 20, 21, 14, 16]), 'cur_cost': 15940.0}, {'tour': array([17, 23, 24, 27, 13, 12, 22,  9,  8,  1, 18, 20, 26, 15,  4, 16, 11,
        3,  5,  0,  6,  2, 10, 14, 25,  7, 19, 21]), 'cur_cost': 13269.0}]
2025-06-26 17:11:33,263 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:11:33,263 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 6, 'total_searches': 121, 'skip_rate': 0.049586776859504134, 'estimated_time_saved': 30, 'path_optimizer': {'path_count': 115, 'cache_hits': 155, 'similarity_calculations': 2818, 'cache_hit_rate': 0.055003548616039746, 'cache_size': 2663}}
2025-06-26 17:11:33,263 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:11:33,263 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}]
2025-06-26 17:11:33,263 - __main__ - INFO - 进化阶段完成
2025-06-26 17:11:33,263 - __main__ - INFO - 开始评估阶段
2025-06-26 17:11:33,263 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:11:33,265 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:11:33,265 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 3360.0 → 3360.0 (Δ 0.0)
- Mean cost: 10387.9 → 9023.2
- Diversity: 0.541 → 0.511 (Δ -0.0296)

#### Elite metrics
- Elite count: 69 → 70
- Best elite cost: 3055 → 3055 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 33.3% , avg Δ 1858.67
- Exploit: 4 inds, success 75.0% , avg Δ 623.75

#### Other indicators
- No-change individuals: 3
- Regression individuals: 2
- Historical trends: Last 3 iterations: costs [166.0, 5934.0, 3360.0], diversity [0.842962962962963, 0.7301587301587301, 0.8119047619047619]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:11:33,266 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:11:47,689 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": 1858.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 623.75,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the number of explore individuals to balance the success rates and improve diversity."
  }
}
```
2025-06-26 17:11:47,695 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:11:47,696 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": 1858.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 623.75,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the number of explore individuals to balance the success rates and improve diversity."
  }
}
```
2025-06-26 17:11:47,696 - __main__ - INFO - 评估阶段完成
2025-06-26 17:11:47,697 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": 1858.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 623.75,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider increasing the number of explore individuals to balance the success rates and improve diversity."
  }
}
```
2025-06-26 17:11:47,697 - __main__ - INFO - 当前最佳适应度: 3360.0
2025-06-26 17:11:47,699 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite1_28_route_2.pkl
2025-06-26 17:11:47,700 - __main__ - INFO - composite1_28 开始进化第 4 代
2025-06-26 17:11:47,700 - __main__ - INFO - 开始分析阶段
2025-06-26 17:11:47,700 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:11:47,705 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 3360.0, 'max': 15940.0, 'mean': 9023.2, 'std': 5705.232051371793}, 'diversity': 0.7293650793650794, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:11:47,709 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 3360.0, 'max': 15940.0, 'mean': 9023.2, 'std': 5705.232051371793}, 'diversity_level': 0.7293650793650794, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'coordinates': [[438, 1213], [479, 1236], [466, 1259], [438, 1259], [425, 1236], [447, 1243], [464, 1235], [449, 1224], [466, 1213], [110, 246], [151, 223], [138, 246], [110, 200], [138, 200], [132, 229], [116, 229], [116, 217], [132, 217], [97, 223], [693, 219], [665, 219], [652, 196], [665, 173], [693, 173], [679, 203], [687, 189], [671, 189], [706, 196]], 'distance_matrix': array([[   0.,   47.,   54.,   46.,   26.,   31.,   34.,   16.,   28.,
        1021., 1031., 1012., 1065., 1056., 1030., 1035., 1047., 1042.,
        1047., 1026., 1020., 1039., 1064., 1071., 1038., 1054., 1050.,
        1052.],
       [  47.,    0.,   26.,   47.,   54.,   33.,   15.,   32.,   26.,
        1057., 1065., 1047., 1100., 1091., 1065., 1070., 1082., 1076.,
        1083., 1039., 1034., 1054., 1079., 1084., 1052., 1067., 1064.,
        1064.],
       [  54.,   26.,    0.,   28.,   47.,   25.,   24.,   39.,   46.,
        1074., 1083., 1065., 1117., 1109., 1083., 1088., 1099., 1094.,
        1100., 1064., 1059., 1079., 1104., 1109., 1077., 1093., 1089.,
        1090.],
       [  46.,   47.,   28.,    0.,   26.,   18.,   35.,   37.,   54.,
        1065., 1075., 1056., 1109., 1101., 1074., 1079., 1091., 1086.,
        1091., 1071., 1064., 1084., 1109., 1116., 1083., 1099., 1095.,
        1096.],
       [  26.,   54.,   47.,   26.,    0.,   23.,   39.,   27.,   47.,
        1039., 1049., 1031., 1083., 1075., 1049., 1053., 1065., 1060.,
        1065., 1052., 1045., 1064., 1090., 1096., 1064., 1079., 1076.,
        1077.],
       [  31.,   33.,   25.,   18.,   23.,    0.,   19.,   19.,   36.,
        1052., 1062., 1044., 1096., 1088., 1062., 1067., 1078., 1073.,
        1078., 1053., 1047., 1067., 1092., 1098., 1066., 1081., 1078.,
        1079.],
       [  34.,   15.,   24.,   35.,   39.,   19.,    0.,   19.,   22.,
        1050., 1059., 1041., 1094., 1085., 1059., 1064., 1076., 1071.,
        1076., 1041., 1036., 1056., 1081., 1086., 1054., 1070., 1066.,
        1067.],
       [  16.,   32.,   39.,   37.,   27.,   19.,   19.,    0.,   20.,
        1035., 1044., 1026., 1079., 1070., 1044., 1049., 1061., 1056.,
        1061., 1034., 1028., 1048., 1073., 1079., 1047., 1062., 1059.,
        1060.],
       [  28.,   26.,   46.,   54.,   47.,   36.,   22.,   20.,    0.,
        1030., 1039., 1021., 1074., 1065., 1039., 1044., 1056., 1051.,
        1057., 1020., 1014., 1034., 1059., 1064., 1032., 1048., 1044.,
        1045.],
       [1021., 1057., 1074., 1065., 1039., 1052., 1050., 1035., 1030.,
           0.,   47.,   28.,   46.,   54.,   28.,   18.,   30.,   36.,
          26.,  584.,  556.,  544.,  560.,  588.,  571.,  580.,  564.,
         598.],
       [1031., 1065., 1083., 1075., 1049., 1062., 1059., 1044., 1039.,
          47.,    0.,   26.,   47.,   26.,   20.,   36.,   36.,   20.,
          54.,  542.,  514.,  502.,  516.,  544.,  528.,  537.,  521.,
         556.],
       [1012., 1047., 1065., 1056., 1031., 1044., 1041., 1026., 1021.,
          28.,   26.,    0.,   54.,   46.,   18.,   28.,   36.,   30.,
          47.,  556.,  528.,  516.,  532.,  560.,  543.,  552.,  536.,
         570.],
       [1065., 1100., 1117., 1109., 1083., 1096., 1094., 1079., 1074.,
          46.,   47.,   54.,    0.,   28.,   36.,   30.,   18.,   28.,
          26.,  583.,  555.,  542.,  556.,  584.,  569.,  577.,  561.,
         596.],
       [1056., 1091., 1109., 1101., 1075., 1088., 1085., 1070., 1065.,
          54.,   26.,   46.,   28.,    0.,   30.,   36.,   28.,   18.,
          47.,  555.,  527.,  514.,  528.,  556.,  541.,  549.,  533.,
         568.],
       [1030., 1065., 1083., 1074., 1049., 1062., 1059., 1044., 1039.,
          28.,   20.,   18.,   36.,   30.,    0.,   16.,   20.,   12.,
          36.,  561.,  533.,  521.,  536.,  564.,  548.,  556.,  540.,
         575.],
       [1035., 1070., 1088., 1079., 1053., 1067., 1064., 1049., 1044.,
          18.,   36.,   28.,   30.,   36.,   16.,    0.,   12.,   20.,
          20.,  577.,  549.,  537.,  552.,  580.,  564.,  572.,  556.,
         591.],
       [1047., 1082., 1099., 1091., 1065., 1078., 1076., 1061., 1056.,
          30.,   36.,   36.,   18.,   28.,   20.,   12.,    0.,   16.,
          20.,  577.,  549.,  536.,  551.,  579.,  563.,  572.,  556.,
         590.],
       [1042., 1076., 1094., 1086., 1060., 1073., 1071., 1056., 1051.,
          36.,   20.,   30.,   28.,   18.,   12.,   20.,   16.,    0.,
          36.,  561.,  533.,  520.,  535.,  563.,  547.,  556.,  540.,
         574.],
       [1047., 1083., 1100., 1091., 1065., 1078., 1076., 1061., 1057.,
          26.,   54.,   47.,   26.,   47.,   36.,   20.,   20.,   36.,
           0.,  596.,  568.,  556.,  570.,  598.,  582.,  591.,  575.,
         610.],
       [1026., 1039., 1064., 1071., 1052., 1053., 1041., 1034., 1020.,
         584.,  542.,  556.,  583.,  555.,  561.,  577.,  577.,  561.,
         596.,    0.,   28.,   47.,   54.,   46.,   21.,   31.,   37.,
          26.],
       [1020., 1034., 1059., 1064., 1045., 1047., 1036., 1028., 1014.,
         556.,  514.,  528.,  555.,  527.,  533.,  549.,  549.,  533.,
         568.,   28.,    0.,   26.,   46.,   54.,   21.,   37.,   31.,
          47.],
       [1039., 1054., 1079., 1084., 1064., 1067., 1056., 1048., 1034.,
         544.,  502.,  516.,  542.,  514.,  521.,  537.,  536.,  520.,
         556.,   47.,   26.,    0.,   26.,   47.,   28.,   36.,   20.,
          54.],
       [1064., 1079., 1104., 1109., 1090., 1092., 1081., 1073., 1059.,
         560.,  516.,  532.,  556.,  528.,  536.,  552.,  551.,  535.,
         570.,   54.,   46.,   26.,    0.,   28.,   33.,   27.,   17.,
          47.],
       [1071., 1084., 1109., 1116., 1096., 1098., 1086., 1079., 1064.,
         588.,  544.,  560.,  584.,  556.,  564.,  580.,  579.,  563.,
         598.,   46.,   54.,   47.,   28.,    0.,   33.,   17.,   27.,
          26.],
       [1038., 1052., 1077., 1083., 1064., 1066., 1054., 1047., 1032.,
         571.,  528.,  543.,  569.,  541.,  548.,  564.,  563.,  547.,
         582.,   21.,   21.,   28.,   33.,   33.,    0.,   16.,   16.,
          28.],
       [1054., 1067., 1093., 1099., 1079., 1081., 1070., 1062., 1048.,
         580.,  537.,  552.,  577.,  549.,  556.,  572.,  572.,  556.,
         591.,   31.,   37.,   36.,   27.,   17.,   16.,    0.,   16.,
          20.],
       [1050., 1064., 1089., 1095., 1076., 1078., 1066., 1059., 1044.,
         564.,  521.,  536.,  561.,  533.,  540.,  556.,  556.,  540.,
         575.,   37.,   31.,   20.,   17.,   27.,   16.,   16.,    0.,
          36.],
       [1052., 1064., 1090., 1096., 1077., 1079., 1067., 1060., 1045.,
         598.,  556.,  570.,  596.,  568.,  575.,  591.,  590.,  574.,
         610.,   26.,   47.,   54.,   47.,   26.,   28.,   20.,   36.,
           0.]])}
2025-06-26 17:11:47,710 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:11:47,710 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:11:47,711 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:11:47,712 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:11:47,712 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:11:47,713 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:11:47,713 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:11:47,713 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:11:47,714 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:11:47,749 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:11:47,749 - __main__ - INFO - 精英专家分析报告: {'elite_count': 70, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 305.0, 'avg_gap': 5968.200000000001}, 'structure_gap': {'unique_elite_edges': 60, 'unique_pop_edges': 126, 'common_edges': 30}}, 'elite_diversity': {'diversity_score': 0.28451641526175686}}
2025-06-26 17:11:47,749 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:11:47,749 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:11:47,749 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:11:47,749 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 3360.0, mean 9023.2, max 15940.0, std 5705.232051371793
- diversity: 0.7293650793650794
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [5934.0, 3360.0, 3360.0] (Δ 1287.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [97.0, 173.0, 706.0, 1259.0]
- centroid/spread: [407.964, 539.464] | rms 532.029
- density_grid(3x3): [[10, 0, 9], [0, 0, 0], [0, 9, 0]]
- edge_len_stats(min,q1,med,q3,max): [12.0, 46.0, 577.0, 1062.0, 1117.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [5, 4, 3, 4, 6, 0, 4, 1]
- nn_median_dist: 18.028

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:11:47,749 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:12:14,288 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 12.0 to 1117.0, with median 577.0 and long_edge_ratio 0.249)",
    "modality": "Potentially multimodal (direction_hist shows varied edge directions, no clear dominance in any sector)",
    "deceptiveness": "Possible deceptive regions (high std in cost_stats (5705.23) despite recent best costs showing improvement)"
  },
  "population_state": {
    "coverage": "Patchy (density_grid shows clusters in top-left and bottom-right with empty central regions)",
    "convergence": "Early-stage (diversity 0.729, convergence 0.0, recent best costs improving by 1287.0 per iteration)"
  },
  "difficult_regions": [
    "Central corridor (empty in density_grid, potentially requiring long edges to cross)",
    "Regions with edge lengths in q3-max range (1062.0-1117.0)"
  ],
  "opportunity_regions": [
    "Top-left and bottom-right grid cells (high node density in density_grid)",
    "Edges with lengths near median (577.0) or nn_median_dist (18.028)"
  ],
  "evolution_phase": "Exploration-dominant (high diversity, no elites, improving but non-stagnant best costs)",
  "evolution_direction": [
    "Prioritize crossover operators that exploit dense regions (e.g. edge-assembly crossover on top-left/bottom-right clusters)",
    "Apply local search (2-opt) selectively on long edges (>q3 1062.0)",
    "Maintain diversity via restart or mutation in empty central regions",
    "Monitor direction_hist to avoid premature direction bias"
  ]
}
```
2025-06-26 17:12:14,288 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:12:14,288 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 12.0 to 1117.0, with median 577.0 and long_edge_ratio 0.249)', 'modality': 'Potentially multimodal (direction_hist shows varied edge directions, no clear dominance in any sector)', 'deceptiveness': 'Possible deceptive regions (high std in cost_stats (5705.23) despite recent best costs showing improvement)'}, 'population_state': {'coverage': 'Patchy (density_grid shows clusters in top-left and bottom-right with empty central regions)', 'convergence': 'Early-stage (diversity 0.729, convergence 0.0, recent best costs improving by 1287.0 per iteration)'}, 'difficult_regions': ['Central corridor (empty in density_grid, potentially requiring long edges to cross)', 'Regions with edge lengths in q3-max range (1062.0-1117.0)'], 'opportunity_regions': ['Top-left and bottom-right grid cells (high node density in density_grid)', 'Edges with lengths near median (577.0) or nn_median_dist (18.028)'], 'evolution_phase': 'Exploration-dominant (high diversity, no elites, improving but non-stagnant best costs)', 'evolution_direction': ['Prioritize crossover operators that exploit dense regions (e.g. edge-assembly crossover on top-left/bottom-right clusters)', 'Apply local search (2-opt) selectively on long edges (>q3 1062.0)', 'Maintain diversity via restart or mutation in empty central regions', 'Monitor direction_hist to avoid premature direction bias']}
2025-06-26 17:12:14,288 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:12:14,288 - __main__ - INFO - 分析阶段完成
2025-06-26 17:12:14,288 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 12.0 to 1117.0, with median 577.0 and long_edge_ratio 0.249)', 'modality': 'Potentially multimodal (direction_hist shows varied edge directions, no clear dominance in any sector)', 'deceptiveness': 'Possible deceptive regions (high std in cost_stats (5705.23) despite recent best costs showing improvement)'}, 'population_state': {'coverage': 'Patchy (density_grid shows clusters in top-left and bottom-right with empty central regions)', 'convergence': 'Early-stage (diversity 0.729, convergence 0.0, recent best costs improving by 1287.0 per iteration)'}, 'difficult_regions': ['Central corridor (empty in density_grid, potentially requiring long edges to cross)', 'Regions with edge lengths in q3-max range (1062.0-1117.0)'], 'opportunity_regions': ['Top-left and bottom-right grid cells (high node density in density_grid)', 'Edges with lengths near median (577.0) or nn_median_dist (18.028)'], 'evolution_phase': 'Exploration-dominant (high diversity, no elites, improving but non-stagnant best costs)', 'evolution_direction': ['Prioritize crossover operators that exploit dense regions (e.g. edge-assembly crossover on top-left/bottom-right clusters)', 'Apply local search (2-opt) selectively on long edges (>q3 1062.0)', 'Maintain diversity via restart or mutation in empty central regions', 'Monitor direction_hist to avoid premature direction bias']}
2025-06-26 17:12:14,288 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:12:14,288 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:12:14,288 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 3360.0
  • mean_cost: 9023.2
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 12.0 to 1117.0, with median 577.0 and long_edge_ratio 0.249), modality Potentially multimodal (direction_hist shows varied edge directions, no clear dominance in any sector), deceptiveness Possible deceptive regions (high std in cost_stats (5705.23) despite recent best costs showing improvement)
- Evolution phase: Exploration-dominant (high diversity, no elites, improving but non-stagnant best costs)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Central corridor (em, Regions with edge le
- Opportunity regions (# 2): Top-left and bottom-, Edges with lengths n
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:12:14,288 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:12:14,288 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:12:24,434 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and rugged landscape favor exploration; top performers exploit while others explore to balance search."
}
```
2025-06-26 17:12:24,442 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 17:12:24,442 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 17:12:24,442 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and rugged landscape favor exploration; top performers exploit while others explore to balance search."
}
```
2025-06-26 17:12:24,442 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:12:24,442 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 17:12:24,442 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Low diversity and rugged landscape favor exploration; top performers exploit while others explore to balance search."
}
```
2025-06-26 17:12:24,442 - __main__ - INFO - 开始进化阶段
2025-06-26 17:12:24,442 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:12:24,442 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:12:24,442 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3360.0 (rank 0.0%)
- Population diversity: 0.5111111111111111
- Evolution phase: Exploration-dominant (high diversity, no elites, improving but non-stagnant best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): Top-left and bo, Edges with leng
- Difficult regions to avoid (sample): Central corrido, Regions with ed
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:12:24,442 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:12:24,442 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:12:35,554 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "Top-left and edges",
  "strategy_comment": "Prioritize perimeter traversal to avoid central corridor"
}
```
2025-06-26 17:12:35,554 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:12:35,554 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:12:35,554 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:12:35,554 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:12:35,554 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:12:35,554 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13890.0 (rank 60.0%)
- Population diversity: 0.5111111111111111
- Evolution phase: Exploration-dominant (high diversity, no elites, improving but non-stagnant best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): Top-left and bo, Edges with leng
- Difficult regions to avoid (sample): Central corrido, Regions with ed
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:12:35,554 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:12:35,554 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:12:47,318 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "Top-left and edges",
  "strategy_comment": "Prioritize edges and sparse areas while avoiding central corridor"
}
```
2025-06-26 17:12:47,318 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:12:47,318 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:12:47,318 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:12:47,318 - __main__ - INFO - 为个体 2 生成利用路径
2025-06-26 17:12:47,318 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:12:47,318 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:12:47,318 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 13329.0
2025-06-26 17:12:47,318 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：111,跳过局部搜索
2025-06-26 17:12:47,318 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 17:12:47,318 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([ 1, 25, 26, 20, 13,  6,  7, 12, 23, 24, 10, 21, 11, 17, 16,  0,  5,
        2, 18, 27, 22,  9, 14,  3, 19, 15,  4,  8]), 'cur_cost': 13329.0}
2025-06-26 17:12:47,318 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:12:47,318 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:12:47,318 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3360.0 (rank 0.0%)
- Population diversity: 0.4888888888888889
- Evolution phase: Exploration-dominant (high diversity, no elites, improving but non-stagnant best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): Top-left and bo, Edges with leng
- Difficult regions to avoid (sample): Central corrido, Regions with ed
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:12:47,318 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:12:47,318 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:12:57,637 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "Top-left and bottom edges",
  "strategy_comment": "Sequential sweep focusing on edges to explore sparse regions"
}
```
2025-06-26 17:12:57,641 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:12:57,641 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:12:57,641 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:12:57,641 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-26 17:12:57,641 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:12:57,641 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:12:57,641 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 17950.0
2025-06-26 17:12:57,641 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：111,跳过局部搜索
2025-06-26 17:12:57,641 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 17:12:57,641 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([19, 17, 14,  9,  5,  0, 22,  7, 23,  6,  2, 24, 26, 27, 21, 13, 10,
        3, 16, 25,  8, 18, 15,  4, 12,  1, 20, 11]), 'cur_cost': 17950.0}
2025-06-26 17:12:57,641 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:12:57,641 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:12:57,641 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3360.0 (rank 0.0%)
- Population diversity: 0.562962962962963
- Evolution phase: Exploration-dominant (high diversity, no elites, improving but non-stagnant best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): Top-left and bo, Edges with leng
- Difficult regions to avoid (sample): Central corrido, Regions with ed
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:12:57,641 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:12:57,641 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:13:07,817 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "Top-left and edges",
  "strategy_comment": "Prioritize perimeter traversal to avoid central corridor"
}
```
2025-06-26 17:13:07,823 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:13:07,823 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:13:07,823 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:13:07,823 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 17:13:07,823 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:13:07,823 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15393.0 (rank 70.0%)
- Population diversity: 0.562962962962963
- Evolution phase: Exploration-dominant (high diversity, no elites, improving but non-stagnant best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): Top-left and bo, Edges with leng
- Difficult regions to avoid (sample): Central corrido, Regions with ed
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:13:07,823 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:13:07,823 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:13:17,797 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "Top-left and edges",
  "strategy_comment": "Prioritize perimeter traversal to explore edges"
}
```
2025-06-26 17:13:17,797 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:13:17,799 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:13:17,799 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:13:17,799 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:13:17,799 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:13:17,799 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:13:17,799 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 22701.0
2025-06-26 17:13:18,299 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:13:18,301 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:13:18,301 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64)]
2025-06-26 17:13:18,319 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:13:18,319 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 1, 25, 26, 20, 13,  6,  7, 12, 23, 24, 10, 21, 11, 17, 16,  0,  5,
        2, 18, 27, 22,  9, 14,  3, 19, 15,  4,  8]), 'cur_cost': 13329.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([19, 17, 14,  9,  5,  0, 22,  7, 23,  6,  2, 24, 26, 27, 21, 13, 10,
        3, 16, 25,  8, 18, 15,  4, 12,  1, 20, 11]), 'cur_cost': 17950.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 4, 20,  8, 24,  7, 25, 15, 21, 12, 26, 11,  2, 13,  5, 10,  0, 23,
       18, 14,  3, 22,  9, 27, 16, 19,  1,  6, 17]), 'cur_cost': 22701.0}, {'tour': array([ 2, 25,  9, 18, 10,  5, 11, 15,  8,  4,  1, 24, 13, 19,  6,  7, 22,
       26,  0, 17,  3, 12, 23, 27, 20, 21, 14, 16]), 'cur_cost': 15940.0}, {'tour': array([17, 23, 24, 27, 13, 12, 22,  9,  8,  1, 18, 20, 26, 15,  4, 16, 11,
        3,  5,  0,  6,  2, 10, 14, 25,  7, 19, 21]), 'cur_cost': 13269.0}]
2025-06-26 17:13:18,320 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:13:18,321 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 8, 'total_searches': 124, 'skip_rate': 0.06451612903225806, 'estimated_time_saved': 40, 'path_optimizer': {'path_count': 116, 'cache_hits': 166, 'similarity_calculations': 2839, 'cache_hit_rate': 0.058471292708700245, 'cache_size': 2673}}
2025-06-26 17:13:18,322 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:13:18,322 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-26 17:13:18,323 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:13:18,323 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 15940.0 (rank 70.0%)
- Population diversity: 0.5074074074074074
- Evolution phase: Exploration-dominant (high diversity, no elites, improving but non-stagnant best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): Top-left and bo, Edges with leng
- Difficult regions to avoid (sample): Central corrido, Regions with ed
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:13:18,323 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:13:18,324 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:13:28,865 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "Top-left and bo, Edges with leng",
  "strategy_comment": "Sequential exploration of opportunity regions"
}
```
2025-06-26 17:13:28,865 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:13:28,865 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:13:28,865 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:13:28,865 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:13:28,865 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:13:28,865 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:13:28,865 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 13792.0
2025-06-26 17:13:29,371 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:13:29,371 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:13:29,373 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64)]
2025-06-26 17:13:29,389 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:13:29,390 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 1, 25, 26, 20, 13,  6,  7, 12, 23, 24, 10, 21, 11, 17, 16,  0,  5,
        2, 18, 27, 22,  9, 14,  3, 19, 15,  4,  8]), 'cur_cost': 13329.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([19, 17, 14,  9,  5,  0, 22,  7, 23,  6,  2, 24, 26, 27, 21, 13, 10,
        3, 16, 25,  8, 18, 15,  4, 12,  1, 20, 11]), 'cur_cost': 17950.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 4, 20,  8, 24,  7, 25, 15, 21, 12, 26, 11,  2, 13,  5, 10,  0, 23,
       18, 14,  3, 22,  9, 27, 16, 19,  1,  6, 17]), 'cur_cost': 22701.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 3, 23, 21, 24, 20,  4, 22, 14, 17,  1, 15, 11, 26, 18,  6, 27, 10,
       13, 12, 16,  0, 19, 25,  9,  8,  2,  5,  7]), 'cur_cost': 13792.0}]
2025-06-26 17:13:29,391 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 17:13:29,391 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 8, 'total_searches': 125, 'skip_rate': 0.064, 'estimated_time_saved': 40, 'path_optimizer': {'path_count': 117, 'cache_hits': 166, 'similarity_calculations': 2849, 'cache_hit_rate': 0.05826605826605827, 'cache_size': 2683}}
2025-06-26 17:13:29,392 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:13:29,392 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 1, 25, 26, 20, 13,  6,  7, 12, 23, 24, 10, 21, 11, 17, 16,  0,  5,
        2, 18, 27, 22,  9, 14,  3, 19, 15,  4,  8]), 'cur_cost': 13329.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([19, 17, 14,  9,  5,  0, 22,  7, 23,  6,  2, 24, 26, 27, 21, 13, 10,
        3, 16, 25,  8, 18, 15,  4, 12,  1, 20, 11]), 'cur_cost': 17950.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}]
2025-06-26 17:13:29,393 - __main__ - INFO - 进化阶段完成
2025-06-26 17:13:29,393 - __main__ - INFO - 开始评估阶段
2025-06-26 17:13:29,393 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:13:29,394 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:13:29,394 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 3360.0 → 3360.0 (Δ 0.0)
- Mean cost: 9023.2 → 8793.2
- Diversity: 0.511 → 0.433 (Δ -0.0778)

#### Elite metrics
- Elite count: 70 → 70
- Best elite cost: 3055 → 3055 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 50.0% , avg Δ 5857.17
- Exploit: 4 inds, success 0.0% , avg Δ -8210.75

#### Other indicators
- No-change individuals: 3
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [5934.0, 3360.0, 3360.0], diversity [0.7301587301587301, 0.8119047619047619, 0.7293650793650794]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:13:29,394 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:13:42,483 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 50.0,
      "average_delta": 5857.17,
      "contribution": "positive but inconsistent"
    },
    "Exploit": {
      "success_rate": 0.0,
      "average_delta": -8210.75,
      "contribution": "negative, potentially harmful"
    }
  },
  "balance_state": "Explore is outperforming Exploit, but overall strategy balance may need adjustment",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators or increasing mutation rates to boost diversity"
  }
}
```
2025-06-26 17:13:42,491 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:13:42,491 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 50.0,
      "average_delta": 5857.17,
      "contribution": "positive but inconsistent"
    },
    "Exploit": {
      "success_rate": 0.0,
      "average_delta": -8210.75,
      "contribution": "negative, potentially harmful"
    }
  },
  "balance_state": "Explore is outperforming Exploit, but overall strategy balance may need adjustment",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators or increasing mutation rates to boost diversity"
  }
}
```
2025-06-26 17:13:42,491 - __main__ - INFO - 评估阶段完成
2025-06-26 17:13:42,491 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 50.0,
      "average_delta": 5857.17,
      "contribution": "positive but inconsistent"
    },
    "Exploit": {
      "success_rate": 0.0,
      "average_delta": -8210.75,
      "contribution": "negative, potentially harmful"
    }
  },
  "balance_state": "Explore is outperforming Exploit, but overall strategy balance may need adjustment",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators or increasing mutation rates to boost diversity"
  }
}
```
2025-06-26 17:13:42,491 - __main__ - INFO - 当前最佳适应度: 3360.0
2025-06-26 17:13:42,495 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite1_28_route_3.pkl
2025-06-26 17:13:42,495 - __main__ - INFO - composite1_28 开始进化第 5 代
2025-06-26 17:13:42,496 - __main__ - INFO - 开始分析阶段
2025-06-26 17:13:42,496 - StatsExpert - INFO - 开始统计分析
2025-06-26 17:13:42,501 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 3360.0, 'max': 22701.0, 'mean': 8793.2, 'std': 7070.737893600639}, 'diversity': 0.6246031746031747, 'clusters': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 17:13:42,502 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 3360.0, 'max': 22701.0, 'mean': 8793.2, 'std': 7070.737893600639}, 'diversity_level': 0.6246031746031747, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [6, 1, 1, 1, 1]}, 'coordinates': [[438, 1213], [479, 1236], [466, 1259], [438, 1259], [425, 1236], [447, 1243], [464, 1235], [449, 1224], [466, 1213], [110, 246], [151, 223], [138, 246], [110, 200], [138, 200], [132, 229], [116, 229], [116, 217], [132, 217], [97, 223], [693, 219], [665, 219], [652, 196], [665, 173], [693, 173], [679, 203], [687, 189], [671, 189], [706, 196]], 'distance_matrix': array([[   0.,   47.,   54.,   46.,   26.,   31.,   34.,   16.,   28.,
        1021., 1031., 1012., 1065., 1056., 1030., 1035., 1047., 1042.,
        1047., 1026., 1020., 1039., 1064., 1071., 1038., 1054., 1050.,
        1052.],
       [  47.,    0.,   26.,   47.,   54.,   33.,   15.,   32.,   26.,
        1057., 1065., 1047., 1100., 1091., 1065., 1070., 1082., 1076.,
        1083., 1039., 1034., 1054., 1079., 1084., 1052., 1067., 1064.,
        1064.],
       [  54.,   26.,    0.,   28.,   47.,   25.,   24.,   39.,   46.,
        1074., 1083., 1065., 1117., 1109., 1083., 1088., 1099., 1094.,
        1100., 1064., 1059., 1079., 1104., 1109., 1077., 1093., 1089.,
        1090.],
       [  46.,   47.,   28.,    0.,   26.,   18.,   35.,   37.,   54.,
        1065., 1075., 1056., 1109., 1101., 1074., 1079., 1091., 1086.,
        1091., 1071., 1064., 1084., 1109., 1116., 1083., 1099., 1095.,
        1096.],
       [  26.,   54.,   47.,   26.,    0.,   23.,   39.,   27.,   47.,
        1039., 1049., 1031., 1083., 1075., 1049., 1053., 1065., 1060.,
        1065., 1052., 1045., 1064., 1090., 1096., 1064., 1079., 1076.,
        1077.],
       [  31.,   33.,   25.,   18.,   23.,    0.,   19.,   19.,   36.,
        1052., 1062., 1044., 1096., 1088., 1062., 1067., 1078., 1073.,
        1078., 1053., 1047., 1067., 1092., 1098., 1066., 1081., 1078.,
        1079.],
       [  34.,   15.,   24.,   35.,   39.,   19.,    0.,   19.,   22.,
        1050., 1059., 1041., 1094., 1085., 1059., 1064., 1076., 1071.,
        1076., 1041., 1036., 1056., 1081., 1086., 1054., 1070., 1066.,
        1067.],
       [  16.,   32.,   39.,   37.,   27.,   19.,   19.,    0.,   20.,
        1035., 1044., 1026., 1079., 1070., 1044., 1049., 1061., 1056.,
        1061., 1034., 1028., 1048., 1073., 1079., 1047., 1062., 1059.,
        1060.],
       [  28.,   26.,   46.,   54.,   47.,   36.,   22.,   20.,    0.,
        1030., 1039., 1021., 1074., 1065., 1039., 1044., 1056., 1051.,
        1057., 1020., 1014., 1034., 1059., 1064., 1032., 1048., 1044.,
        1045.],
       [1021., 1057., 1074., 1065., 1039., 1052., 1050., 1035., 1030.,
           0.,   47.,   28.,   46.,   54.,   28.,   18.,   30.,   36.,
          26.,  584.,  556.,  544.,  560.,  588.,  571.,  580.,  564.,
         598.],
       [1031., 1065., 1083., 1075., 1049., 1062., 1059., 1044., 1039.,
          47.,    0.,   26.,   47.,   26.,   20.,   36.,   36.,   20.,
          54.,  542.,  514.,  502.,  516.,  544.,  528.,  537.,  521.,
         556.],
       [1012., 1047., 1065., 1056., 1031., 1044., 1041., 1026., 1021.,
          28.,   26.,    0.,   54.,   46.,   18.,   28.,   36.,   30.,
          47.,  556.,  528.,  516.,  532.,  560.,  543.,  552.,  536.,
         570.],
       [1065., 1100., 1117., 1109., 1083., 1096., 1094., 1079., 1074.,
          46.,   47.,   54.,    0.,   28.,   36.,   30.,   18.,   28.,
          26.,  583.,  555.,  542.,  556.,  584.,  569.,  577.,  561.,
         596.],
       [1056., 1091., 1109., 1101., 1075., 1088., 1085., 1070., 1065.,
          54.,   26.,   46.,   28.,    0.,   30.,   36.,   28.,   18.,
          47.,  555.,  527.,  514.,  528.,  556.,  541.,  549.,  533.,
         568.],
       [1030., 1065., 1083., 1074., 1049., 1062., 1059., 1044., 1039.,
          28.,   20.,   18.,   36.,   30.,    0.,   16.,   20.,   12.,
          36.,  561.,  533.,  521.,  536.,  564.,  548.,  556.,  540.,
         575.],
       [1035., 1070., 1088., 1079., 1053., 1067., 1064., 1049., 1044.,
          18.,   36.,   28.,   30.,   36.,   16.,    0.,   12.,   20.,
          20.,  577.,  549.,  537.,  552.,  580.,  564.,  572.,  556.,
         591.],
       [1047., 1082., 1099., 1091., 1065., 1078., 1076., 1061., 1056.,
          30.,   36.,   36.,   18.,   28.,   20.,   12.,    0.,   16.,
          20.,  577.,  549.,  536.,  551.,  579.,  563.,  572.,  556.,
         590.],
       [1042., 1076., 1094., 1086., 1060., 1073., 1071., 1056., 1051.,
          36.,   20.,   30.,   28.,   18.,   12.,   20.,   16.,    0.,
          36.,  561.,  533.,  520.,  535.,  563.,  547.,  556.,  540.,
         574.],
       [1047., 1083., 1100., 1091., 1065., 1078., 1076., 1061., 1057.,
          26.,   54.,   47.,   26.,   47.,   36.,   20.,   20.,   36.,
           0.,  596.,  568.,  556.,  570.,  598.,  582.,  591.,  575.,
         610.],
       [1026., 1039., 1064., 1071., 1052., 1053., 1041., 1034., 1020.,
         584.,  542.,  556.,  583.,  555.,  561.,  577.,  577.,  561.,
         596.,    0.,   28.,   47.,   54.,   46.,   21.,   31.,   37.,
          26.],
       [1020., 1034., 1059., 1064., 1045., 1047., 1036., 1028., 1014.,
         556.,  514.,  528.,  555.,  527.,  533.,  549.,  549.,  533.,
         568.,   28.,    0.,   26.,   46.,   54.,   21.,   37.,   31.,
          47.],
       [1039., 1054., 1079., 1084., 1064., 1067., 1056., 1048., 1034.,
         544.,  502.,  516.,  542.,  514.,  521.,  537.,  536.,  520.,
         556.,   47.,   26.,    0.,   26.,   47.,   28.,   36.,   20.,
          54.],
       [1064., 1079., 1104., 1109., 1090., 1092., 1081., 1073., 1059.,
         560.,  516.,  532.,  556.,  528.,  536.,  552.,  551.,  535.,
         570.,   54.,   46.,   26.,    0.,   28.,   33.,   27.,   17.,
          47.],
       [1071., 1084., 1109., 1116., 1096., 1098., 1086., 1079., 1064.,
         588.,  544.,  560.,  584.,  556.,  564.,  580.,  579.,  563.,
         598.,   46.,   54.,   47.,   28.,    0.,   33.,   17.,   27.,
          26.],
       [1038., 1052., 1077., 1083., 1064., 1066., 1054., 1047., 1032.,
         571.,  528.,  543.,  569.,  541.,  548.,  564.,  563.,  547.,
         582.,   21.,   21.,   28.,   33.,   33.,    0.,   16.,   16.,
          28.],
       [1054., 1067., 1093., 1099., 1079., 1081., 1070., 1062., 1048.,
         580.,  537.,  552.,  577.,  549.,  556.,  572.,  572.,  556.,
         591.,   31.,   37.,   36.,   27.,   17.,   16.,    0.,   16.,
          20.],
       [1050., 1064., 1089., 1095., 1076., 1078., 1066., 1059., 1044.,
         564.,  521.,  536.,  561.,  533.,  540.,  556.,  556.,  540.,
         575.,   37.,   31.,   20.,   17.,   27.,   16.,   16.,    0.,
          36.],
       [1052., 1064., 1090., 1096., 1077., 1079., 1067., 1060., 1045.,
         598.,  556.,  570.,  596.,  568.,  575.,  591.,  590.,  574.,
         610.,   26.,   47.,   54.,   47.,   26.,   28.,   20.,   36.,
           0.]])}
2025-06-26 17:13:42,502 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 17:13:42,502 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 17:13:42,502 - PathExpert - INFO - 开始路径结构分析
2025-06-26 17:13:42,509 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 17:13:42,509 - PathExpert - INFO - 路径结构分析完成
2025-06-26 17:13:42,509 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 17:13:42,509 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 17:13:42,510 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 17:13:42,510 - EliteExpert - INFO - 开始精英解分析
2025-06-26 17:13:42,542 - EliteExpert - INFO - 精英解分析完成
2025-06-26 17:13:42,542 - __main__ - INFO - 精英专家分析报告: {'elite_count': 70, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 305.0, 'avg_gap': 5738.200000000001}, 'structure_gap': {'unique_elite_edges': 62, 'unique_pop_edges': 104, 'common_edges': 28}}, 'elite_diversity': {'diversity_score': 0.28451641526175686}}
2025-06-26 17:13:42,542 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 17:13:42,542 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 17:13:42,542 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 17:13:42,542 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 3360.0, mean 8793.2, max 22701.0, std 7070.737893600639
- diversity: 0.6246031746031747
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [3360.0, 3360.0, 3360.0] (Δ 0.00 per iteration, stagnation: Recent stagnation detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [97.0, 173.0, 706.0, 1259.0]
- centroid/spread: [407.964, 539.464] | rms 532.029
- density_grid(3x3): [[10, 0, 9], [0, 0, 0], [0, 9, 0]]
- edge_len_stats(min,q1,med,q3,max): [12.0, 46.0, 577.0, 1062.0, 1117.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [5, 4, 3, 4, 6, 0, 4, 1]
- nn_median_dist: 18.028

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 17:13:42,542 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:14:06,310 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 12.0 to 1117.0, high std deviation in cost_stats)",
    "modality": "Potentially multimodal (diverse direction_hist, no clear dominant edge direction)",
    "deceptiveness": "Possible deceptive basins (stagnation in recent_best_costs despite high diversity)"
  },
  "population_state": {
    "coverage": "Patchy (uneven density_grid with empty cells, high diversity 0.624)",
    "convergence": "Early-stage (0.0 convergence, recent stagnation detected)"
  },
  "difficult_regions": [
    "Long-edge corridors (q3 edge_len 1062.0, max 1117.0, long_edge_ratio 0.249)",
    "Low-density grid cells (3 empty cells in density_grid)"
  ],
  "opportunity_regions": [
    "High-density cells (top-left and top-right grid cells with counts 10 and 9)",
    "Short-edge clusters (min edge_len 12.0, q1 46.0, nn_median_dist 18.028)"
  ],
  "evolution_phase": "Early exploration (high diversity, no elites, uneven coverage)",
  "evolution_direction": [
    "Intensify local search in high-density cells (e.g. 2-opt around nodes in top grid cells)",
    "Path relinking between distant clusters (bridge empty grid cells)",
    "Adaptive mutation favoring short edges in sparse regions",
    "Diversity preservation for empty grid cells (restricted candidate list)"
  ]
}
```
2025-06-26 17:14:06,310 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 17:14:06,310 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 12.0 to 1117.0, high std deviation in cost_stats)', 'modality': 'Potentially multimodal (diverse direction_hist, no clear dominant edge direction)', 'deceptiveness': 'Possible deceptive basins (stagnation in recent_best_costs despite high diversity)'}, 'population_state': {'coverage': 'Patchy (uneven density_grid with empty cells, high diversity 0.624)', 'convergence': 'Early-stage (0.0 convergence, recent stagnation detected)'}, 'difficult_regions': ['Long-edge corridors (q3 edge_len 1062.0, max 1117.0, long_edge_ratio 0.249)', 'Low-density grid cells (3 empty cells in density_grid)'], 'opportunity_regions': ['High-density cells (top-left and top-right grid cells with counts 10 and 9)', 'Short-edge clusters (min edge_len 12.0, q1 46.0, nn_median_dist 18.028)'], 'evolution_phase': 'Early exploration (high diversity, no elites, uneven coverage)', 'evolution_direction': ['Intensify local search in high-density cells (e.g. 2-opt around nodes in top grid cells)', 'Path relinking between distant clusters (bridge empty grid cells)', 'Adaptive mutation favoring short edges in sparse regions', 'Diversity preservation for empty grid cells (restricted candidate list)']}
2025-06-26 17:14:06,310 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 17:14:06,310 - __main__ - INFO - 分析阶段完成
2025-06-26 17:14:06,310 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 12.0 to 1117.0, high std deviation in cost_stats)', 'modality': 'Potentially multimodal (diverse direction_hist, no clear dominant edge direction)', 'deceptiveness': 'Possible deceptive basins (stagnation in recent_best_costs despite high diversity)'}, 'population_state': {'coverage': 'Patchy (uneven density_grid with empty cells, high diversity 0.624)', 'convergence': 'Early-stage (0.0 convergence, recent stagnation detected)'}, 'difficult_regions': ['Long-edge corridors (q3 edge_len 1062.0, max 1117.0, long_edge_ratio 0.249)', 'Low-density grid cells (3 empty cells in density_grid)'], 'opportunity_regions': ['High-density cells (top-left and top-right grid cells with counts 10 and 9)', 'Short-edge clusters (min edge_len 12.0, q1 46.0, nn_median_dist 18.028)'], 'evolution_phase': 'Early exploration (high diversity, no elites, uneven coverage)', 'evolution_direction': ['Intensify local search in high-density cells (e.g. 2-opt around nodes in top grid cells)', 'Path relinking between distant clusters (bridge empty grid cells)', 'Adaptive mutation favoring short edges in sparse regions', 'Diversity preservation for empty grid cells (restricted candidate list)']}
2025-06-26 17:14:06,310 - __main__ - INFO - 开始策略分配阶段
2025-06-26 17:14:06,310 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 17:14:06,310 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 3360.0
  • mean_cost: 8793.2
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 12.0 to 1117.0, high std deviation in cost_stats), modality Potentially multimodal (diverse direction_hist, no clear dominant edge direction), deceptiveness Possible deceptive basins (stagnation in recent_best_costs despite high diversity)
- Evolution phase: Early exploration (high diversity, no elites, uneven coverage)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Low-density grid cel
- Opportunity regions (# 2): High-density cells (, Short-edge clusters 
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 17:14:06,310 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 17:14:06,317 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:14:16,408 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration focus. Top 30% exploit to refine promising solutions."
}
```
2025-06-26 17:14:16,408 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:14:16,408 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:14:16,408 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration focus. Top 30% exploit to refine promising solutions."
}
```
2025-06-26 17:14:16,408 - __main__ - INFO - 策略分配阶段完成
2025-06-26 17:14:16,408 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 17:14:16,408 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and stagnation suggest exploration focus. Top 30% exploit to refine promising solutions."
}
```
2025-06-26 17:14:16,408 - __main__ - INFO - 开始进化阶段
2025-06-26 17:14:16,416 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 17:14:16,416 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:14:16,416 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3360.0 (rank 0.0%)
- Population diversity: 0.43333333333333335
- Evolution phase: Early exploration (high diversity, no elites, uneven coverage)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:14:16,416 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:14:16,416 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:14:26,289 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Sequential traversal focusing on opportunity regions"
}
```
2025-06-26 17:14:26,298 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:14:26,298 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:14:26,298 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:14:26,298 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 17:14:26,298 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:14:26,300 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3360.0 (rank 0.0%)
- Population diversity: 0.43333333333333335
- Evolution phase: Early exploration (high diversity, no elites, uneven coverage)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:14:26,300 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:14:26,300 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:14:37,548 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Sequential exploration of opportunity regions"
}
```
2025-06-26 17:14:37,548 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:14:37,551 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:14:37,551 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:14:37,551 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 17:14:37,551 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:14:37,551 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13329.0 (rank 60.0%)
- Population diversity: 0.43333333333333335
- Evolution phase: Early exploration (high diversity, no elites, uneven coverage)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:14:37,551 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:14:37,551 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:14:48,305 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Prioritize dense clusters with short edges"
}
```
2025-06-26 17:14:48,305 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:14:48,310 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:14:48,310 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:14:48,310 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 17:14:48,310 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:14:48,310 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3360.0 (rank 0.0%)
- Population diversity: 0.37407407407407406
- Evolution phase: Early exploration (high diversity, no elites, uneven coverage)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:14:48,310 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:14:48,310 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:15:00,856 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Prioritize dense clusters while avoiding long edges"
}
```
2025-06-26 17:15:00,856 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:15:00,857 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:15:00,857 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:15:00,858 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 17:15:00,858 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:15:00,858 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17950.0 (rank 80.0%)
- Population diversity: 0.37407407407407406
- Evolution phase: Early exploration (high diversity, no elites, uneven coverage)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:15:00,859 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:15:00,859 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:15:12,981 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Grid traversal prioritizing dense clusters, skipping long edges"
}
```
2025-06-26 17:15:12,982 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:15:12,983 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13890.0, 路径: [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24]
2025-06-26 17:15:12,983 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}
2025-06-26 17:15:12,983 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 17:15:12,984 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:15:12,984 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3360.0 (rank 0.0%)
- Population diversity: 0.37407407407407406
- Evolution phase: Early exploration (high diversity, no elites, uneven coverage)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:15:12,984 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:15:12,986 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:15:30,262 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Sequential exploration of opportunity regions"
}
```
2025-06-26 17:15:30,262 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:15:30,262 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:15:30,262 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:15:30,265 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 17:15:30,265 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 17:15:30,265 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 3360.0 (rank 0.0%)
- Population diversity: 0.37407407407407406
- Evolution phase: Early exploration (high diversity, no elites, uneven coverage)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Short-edge clus
- Difficult regions to avoid (sample): Long-edge corri, Low-density gri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 27]  
• Path length == 28 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 17:15:30,265 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 17:15:30,265 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:15:40,353 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],
  "targeted_regions": "High-density ce, Short-edge clus",
  "strategy_comment": "Sequential exploration of opportunity regions"
}
```
2025-06-26 17:15:40,355 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 17:15:40,355 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3360.0, 路径: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
2025-06-26 17:15:40,355 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}
2025-06-26 17:15:40,355 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 17:15:40,355 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:15:40,355 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:15:40,355 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 20012.0
2025-06-26 17:15:40,858 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:15:40,858 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:15:40,858 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64)]
2025-06-26 17:15:40,879 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:15:40,879 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([23, 18,  0,  1, 17, 11, 21, 13,  6, 27,  5, 14,  8,  2, 19,  7, 26,
        3, 12, 24, 16, 15, 25, 22, 10, 20,  4,  9]), 'cur_cost': 20012.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([ 3, 23, 21, 24, 20,  4, 22, 14, 17,  1, 15, 11, 26, 18,  6, 27, 10,
       13, 12, 16,  0, 19, 25,  9,  8,  2,  5,  7]), 'cur_cost': 13792.0}]
2025-06-26 17:15:40,881 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 17:15:40,881 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 8, 'total_searches': 126, 'skip_rate': 0.06349206349206349, 'estimated_time_saved': 40, 'path_optimizer': {'path_count': 118, 'cache_hits': 166, 'similarity_calculations': 2860, 'cache_hit_rate': 0.05804195804195804, 'cache_size': 2694}}
2025-06-26 17:15:40,881 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 17:15:40,882 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 17:15:40,882 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:15:40,882 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:15:40,883 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 17449.0
2025-06-26 17:15:40,883 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：111,跳过局部搜索
2025-06-26 17:15:40,883 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-06-26 17:15:40,883 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 3, 17, 16, 21,  5, 15,  9, 10,  2, 25, 14,  7, 13, 23, 27, 24, 19,
       20,  4,  6, 22, 26, 11,  0,  8, 18,  1, 12]), 'cur_cost': 17449.0}
2025-06-26 17:15:40,883 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 17:15:40,883 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 17:15:40,883 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 17:15:40,885 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 17035.0
2025-06-26 17:15:41,387 - ExploitationExpert - INFO - res_population_num: 70
2025-06-26 17:15:41,388 - ExploitationExpert - INFO - res_population_costs: [3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055, 3055]
2025-06-26 17:15:41,388 - ExploitationExpert - INFO - res_populations: [array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 14, 15, 16, 12, 18,  9, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 18, 16, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 16, 12, 13, 17, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  7,  4,  5,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  4,  5,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 15,  9, 18, 12, 16, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 12, 18,  9, 15, 16, 17, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 13, 17, 16, 12, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  7,  4,  3,  5,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  5,  3,  4,  7], dtype=int64), array([ 0,  7,  5,  4,  3,  2,  1,  6,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 18, 15,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11, 14, 17, 16, 15,  9, 18, 12, 13, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0,  4,  3,  5,  2,  1,  6,  7,  8, 20, 24, 19, 27, 23, 25, 26, 22,
       21, 10, 17, 13, 12, 16, 18,  9, 15, 14, 11], dtype=int64), array([ 0, 11,  9, 18, 12, 16, 15, 14, 17, 13, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0,  4,  5,  3,  2,  1,  6,  7,  8, 20, 24, 19, 27, 25, 23, 22, 26,
       21, 10, 14, 17, 13, 12, 16, 15, 18,  9, 11], dtype=int64), array([ 0, 11,  9, 15, 18, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  3,  5,  4], dtype=int64), array([ 0, 11,  9, 18, 15, 16, 12, 13, 17, 14, 10, 21, 26, 22, 23, 25, 27,
       19, 24, 20,  8,  7,  6,  1,  2,  5,  3,  4], dtype=int64), array([ 0, 11,  9, 15, 16, 18, 12, 13, 17, 14, 10, 21, 22, 26, 25, 23, 27,
       19, 24, 20,  8,  6,  1,  2,  3,  5,  4,  7], dtype=int64)]
2025-06-26 17:15:41,406 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 17:15:41,406 - ExploitationExpert - INFO - populations: [{'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}, {'tour': array([23, 18,  0,  1, 17, 11, 21, 13,  6, 27,  5, 14,  8,  2, 19,  7, 26,
        3, 12, 24, 16, 15, 25, 22, 10, 20,  4,  9]), 'cur_cost': 20012.0}, {'tour': array([ 3, 17, 16, 21,  5, 15,  9, 10,  2, 25, 14,  7, 13, 23, 27, 24, 19,
       20,  4,  6, 22, 26, 11,  0,  8, 18,  1, 12]), 'cur_cost': 17449.0}, {'tour': array([ 7, 12,  4,  0, 24, 22, 21,  3, 27, 11, 17, 15, 10, 20,  9,  5, 23,
        1,  6, 19, 13, 14,  2,  8, 16, 26, 18, 25]), 'cur_cost': 17035.0}]
2025-06-26 17:15:41,406 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 17:15:41,408 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 128, 'skip_rate': 0.0703125, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 119, 'cache_hits': 172, 'similarity_calculations': 2878, 'cache_hit_rate': 0.059763724808895066, 'cache_size': 2706}}
2025-06-26 17:15:41,408 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 17:15:41,408 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 1, 6, 11, 16, 21, 26, 2, 7, 12, 17, 22, 27, 3, 8, 13, 18, 23, 4, 9, 14, 19, 24], 'cur_cost': 13890.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], 'cur_cost': 3360.0}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 3, 17, 16, 21,  5, 15,  9, 10,  2, 25, 14,  7, 13, 23, 27, 24, 19,
       20,  4,  6, 22, 26, 11,  0,  8, 18,  1, 12]), 'cur_cost': 17449.0}}]
2025-06-26 17:15:41,409 - __main__ - INFO - 进化阶段完成
2025-06-26 17:15:41,409 - __main__ - INFO - 开始评估阶段
2025-06-26 17:15:41,409 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 17:15:41,410 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 17:15:41,410 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 3360.0 → 3360.0 (Δ 0.0)
- Mean cost: 8793.2 → 8854.6
- Diversity: 0.433 → 0.430 (Δ -0.0037)

#### Elite metrics
- Elite count: 70 → 70
- Best elite cost: 3055 → 3055 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 28.6% , avg Δ 2004.14
- Exploit: 3 inds, success 33.3% , avg Δ -4881.0

#### Other indicators
- No-change individuals: 5
- Regression individuals: 2
- Historical trends: Last 3 iterations: costs [3360.0, 3360.0, 3360.0], diversity [0.8119047619047619, 0.7293650793650794, 0.6246031746031747]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 17:15:41,410 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 17:15:53,071 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "late-stage convergence",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_delta": 2004.14,
      "contribution": "low"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -4881.0,
      "contribution": "high"
    }
  },
  "balance_state": "exploit-heavy but effective",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "mutation_rate": "increase slightly",
    "elite_preservation": "maintain current level"
  }
}
```
2025-06-26 17:15:53,079 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 17:15:53,079 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "late-stage convergence",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_delta": 2004.14,
      "contribution": "low"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -4881.0,
      "contribution": "high"
    }
  },
  "balance_state": "exploit-heavy but effective",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "mutation_rate": "increase slightly",
    "elite_preservation": "maintain current level"
  }
}
```
2025-06-26 17:15:53,079 - __main__ - INFO - 评估阶段完成
2025-06-26 17:15:53,081 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "late-stage convergence",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "average_delta": 2004.14,
      "contribution": "low"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -4881.0,
      "contribution": "high"
    }
  },
  "balance_state": "exploit-heavy but effective",
  "recommendations": {
    "explore_ratio": 0.4,
    "diversity_weight": 0.7,
    "mutation_rate": "increase slightly",
    "elite_preservation": "maintain current level"
  }
}
```
2025-06-26 17:15:53,081 - __main__ - INFO - 当前最佳适应度: 3360.0
2025-06-26 17:15:53,083 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite1_28_route_4.pkl
2025-06-26 17:15:53,098 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite1_28_solution.json
2025-06-26 17:15:53,099 - __main__ - INFO - 实例 composite1_28 处理完成
