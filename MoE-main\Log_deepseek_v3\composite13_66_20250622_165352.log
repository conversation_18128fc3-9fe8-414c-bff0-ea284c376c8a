2025-06-22 16:53:52,327 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-22 16:53:52,332 - __main__ - INFO - 开始分析阶段
2025-06-22 16:53:52,332 - StatsExpert - INFO - 开始统计分析
2025-06-22 16:53:52,350 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 10053.0, 'max': 116123.0, 'mean': 77442.4, 'std': 44425.330725161744}, 'diversity': 0.9154882154882155, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 16:53:52,351 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 10053.0, 'max': 116123.0, 'mean': 77442.4, 'std': 44425.330725161744}, 'diversity_level': 0.9154882154882155, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 16:53:52,361 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 16:53:52,361 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 16:53:52,361 - PathExpert - INFO - 开始路径结构分析
2025-06-22 16:53:52,366 - PathExpert - INFO - 路径结构分析完成
2025-06-22 16:53:52,366 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (53, 62), 'frequency': 0.5, 'avg_cost': 15.0}, {'edge': (33, 34), 'frequency': 0.5, 'avg_cost': 54.0}], 'common_subpaths': [{'subpath': (64, 57, 54), 'frequency': 0.3}, {'subpath': (65, 52, 63), 'frequency': 0.3}, {'subpath': (13, 20, 21), 'frequency': 0.3}, {'subpath': (27, 37, 25), 'frequency': 0.3}, {'subpath': (37, 25, 26), 'frequency': 0.3}, {'subpath': (25, 26, 36), 'frequency': 0.3}, {'subpath': (26, 36, 35), 'frequency': 0.3}, {'subpath': (36, 35, 28), 'frequency': 0.3}, {'subpath': (35, 28, 30), 'frequency': 0.3}, {'subpath': (28, 30, 34), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(53, 62)', 'frequency': 0.5}, {'edge': '(57, 64)', 'frequency': 0.4}, {'edge': '(54, 57)', 'frequency': 0.4}, {'edge': '(52, 63)', 'frequency': 0.4}, {'edge': '(12, 17)', 'frequency': 0.4}, {'edge': '(35, 36)', 'frequency': 0.4}, {'edge': '(33, 34)', 'frequency': 0.5}, {'edge': '(24, 31)', 'frequency': 0.4}, {'edge': '(24, 29)', 'frequency': 0.4}, {'edge': '(41, 50)', 'frequency': 0.4}, {'edge': '(41, 42)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(55, 56)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(53, 64)', 'frequency': 0.2}, {'edge': '(54, 60)', 'frequency': 0.2}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(58, 65)', 'frequency': 0.2}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(2, 63)', 'frequency': 0.2}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(5, 8)', 'frequency': 0.3}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(4, 6)', 'frequency': 0.2}, {'edge': '(6, 9)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 3)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(10, 15)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(17, 18)', 'frequency': 0.2}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(13, 23)', 'frequency': 0.3}, {'edge': '(13, 20)', 'frequency': 0.3}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(19, 21)', 'frequency': 0.2}, {'edge': '(14, 19)', 'frequency': 0.2}, {'edge': '(14, 27)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(32, 40)', 'frequency': 0.2}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(46, 48)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(39, 43)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(42, 61)', 'frequency': 0.2}, {'edge': '(15, 27)', 'frequency': 0.2}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(60, 64)', 'frequency': 0.2}, {'edge': '(40, 43)', 'frequency': 0.2}, {'edge': '(42, 48)', 'frequency': 0.2}, {'edge': '(8, 22)', 'frequency': 0.2}, {'edge': '(27, 48)', 'frequency': 0.2}, {'edge': '(11, 31)', 'frequency': 0.3}, {'edge': '(7, 31)', 'frequency': 0.2}, {'edge': '(1, 51)', 'frequency': 0.2}, {'edge': '(58, 61)', 'frequency': 0.2}, {'edge': '(37, 47)', 'frequency': 0.2}, {'edge': '(38, 55)', 'frequency': 0.2}, {'edge': '(5, 26)', 'frequency': 0.2}, {'edge': '(5, 15)', 'frequency': 0.2}, {'edge': '(14, 45)', 'frequency': 0.2}, {'edge': '(13, 37)', 'frequency': 0.2}, {'edge': '(33, 57)', 'frequency': 0.2}, {'edge': '(31, 53)', 'frequency': 0.2}, {'edge': '(43, 47)', 'frequency': 0.2}, {'edge': '(35, 46)', 'frequency': 0.2}, {'edge': '(36, 64)', 'frequency': 0.2}, {'edge': '(20, 32)', 'frequency': 0.2}, {'edge': '(19, 49)', 'frequency': 0.2}, {'edge': '(12, 55)', 'frequency': 0.2}, {'edge': '(30, 39)', 'frequency': 0.3}, {'edge': '(17, 63)', 'frequency': 0.2}, {'edge': '(24, 44)', 'frequency': 0.2}, {'edge': '(6, 25)', 'frequency': 0.2}, {'edge': '(6, 54)', 'frequency': 0.2}, {'edge': '(43, 58)', 'frequency': 0.2}, {'edge': '(1, 48)', 'frequency': 0.2}, {'edge': '(20, 48)', 'frequency': 0.2}, {'edge': '(20, 23)', 'frequency': 0.2}, {'edge': '(16, 62)', 'frequency': 0.2}, {'edge': '(3, 51)', 'frequency': 0.2}, {'edge': '(31, 58)', 'frequency': 0.2}, {'edge': '(16, 53)', 'frequency': 0.2}, {'edge': '(46, 55)', 'frequency': 0.2}, {'edge': '(0, 12)', 'frequency': 0.2}, {'edge': '(49, 62)', 'frequency': 0.2}, {'edge': '(7, 47)', 'frequency': 0.2}, {'edge': '(22, 35)', 'frequency': 0.2}, {'edge': '(25, 48)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [52, 44, 59, 51, 1, 48], 'cost': 13536.0, 'size': 6}, {'region': [9, 50, 52, 40, 24, 60], 'cost': 13266.0, 'size': 6}, {'region': [37, 41, 59, 26, 64], 'cost': 10825.0, 'size': 5}, {'region': [4, 45, 29, 63, 27], 'cost': 10541.0, 'size': 5}, {'region': [51, 1, 50, 35, 64], 'cost': 10222.0, 'size': 5}]}
2025-06-22 16:53:52,367 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 16:53:52,367 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 16:53:52,367 - EliteExpert - INFO - 开始精英解分析
2025-06-22 16:53:52,367 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-22 16:53:52,368 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-22 16:53:52,368 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 16:53:52,368 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 16:53:52,368 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 16:53:52,368 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=10053.0, max=116123.0, mean=77442.4, std=44425.330725161744
- Diversity: 0.9154882154882155, Convergence: 0.0
- Structure: 2 high quality edges, 10 common subpaths
- Elite: 0 solutions with 0 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 5 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 16:54:00,516 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High. The wide cost range (min 10053.0, max 116123.0, with a high standard deviation of 44425.33) and the identification of 5 difficult regions suggest a highly rugged search space. The presence of high-quality edges, although only two, also contributes to this assessment indicating strong local connections in the landscape. The diversity being 0.915 is very good for a population of only 10 individuals",
    "modality": "Multi-modal. The existence of both difficult regions and the relatively high diversity of the population suggests multiple local optima.  The absence of identified opportunity regions, however, might indicate a global optimum is difficult to find."
  },
  "population_state": {
    "diversity": "High (0.915). This is positive and suggests the population is exploring a wide range of solutions.  Given the small population size (10), such high diversity is encouraging.",
    "convergence": "Zero (0.0). This is expected, and likely desired. Given the absence of convergence in the current context of the search, the search hasn't locked on to any specific solutions and is exploring.  "
  },
  "difficult_regions": {
    "identified_challenges": "5 identified difficult regions strongly suggest the search space is complex. These regions likely present local optima or areas where solutions are trapped.  The high cost variance also suggests these regions can have very high and low costs.",
    "impact": "The difficult regions will likely hinder the convergence and efficiency of the optimization process. The algorithm may get trapped in sub-optimal areas. Efforts should focus on methods to escape these regions or avoid them completely.  "
  },
  "opportunity_regions": {
    "identified_opportunities": "None identified. The lack of opportunity regions suggests the algorithm hasn't found any areas with significant promise or potential. This suggests that further exploration is needed. The high diversity makes finding a good area potentially difficult. This could also imply that the entire search space holds promise.",
    "strategies": "Explore the search space more aggressively. Increase the mutation rate or use more diverse solutions in the initial population. Use methods that allow escaping local optima, like using genetic diversity."
  },
  "evolution_direction": {
    "recommendation": "Focus on Exploration & Diversity. Given the ruggedness, high diversity, lack of convergence, and difficult regions, the primary focus should be on exploring the search space to escape from local optima and uncover potential opportunity regions. Techniques that promote exploration and maintain diversity are crucial. Using a mutation strategy that moves from solutions with low cost to those with high cost is recommended. ",
    "specific_strategies": [
      "Increase the mutation rate to explore the search space more broadly. Avoid strategies that encourage local search prematurely.",
      "Employ techniques to escape local optima (e.g., random restarts, simulated annealing, or adaptive mutation strategies).",
      "Consider elitism, if not already used, and implement it strategically to preserve promising solutions while maintaining diversity. However, given the low number of high quality edges, elitism could prematurely focus the search",
      "If applicable, consider a larger initial population size, and if the algorithm allows it, increasing population size to help balance exploration and exploitation as the search progresses."
    ]
  }
}
```
2025-06-22 16:54:00,518 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 16:54:00,519 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High. The wide cost range (min 10053.0, max 116123.0, with a high standard deviation of 44425.33) and the identification of 5 difficult regions suggest a highly rugged search space. The presence of high-quality edges, although only two, also contributes to this assessment indicating strong local connections in the landscape. The diversity being 0.915 is very good for a population of only 10 individuals', 'modality': 'Multi-modal. The existence of both difficult regions and the relatively high diversity of the population suggests multiple local optima.  The absence of identified opportunity regions, however, might indicate a global optimum is difficult to find.'}, 'population_state': {'diversity': 'High (0.915). This is positive and suggests the population is exploring a wide range of solutions.  Given the small population size (10), such high diversity is encouraging.', 'convergence': "Zero (0.0). This is expected, and likely desired. Given the absence of convergence in the current context of the search, the search hasn't locked on to any specific solutions and is exploring.  "}, 'difficult_regions': {'identified_challenges': '5 identified difficult regions strongly suggest the search space is complex. These regions likely present local optima or areas where solutions are trapped.  The high cost variance also suggests these regions can have very high and low costs.', 'impact': 'The difficult regions will likely hinder the convergence and efficiency of the optimization process. The algorithm may get trapped in sub-optimal areas. Efforts should focus on methods to escape these regions or avoid them completely.  '}, 'opportunity_regions': {'identified_opportunities': "None identified. The lack of opportunity regions suggests the algorithm hasn't found any areas with significant promise or potential. This suggests that further exploration is needed. The high diversity makes finding a good area potentially difficult. This could also imply that the entire search space holds promise.", 'strategies': 'Explore the search space more aggressively. Increase the mutation rate or use more diverse solutions in the initial population. Use methods that allow escaping local optima, like using genetic diversity.'}, 'evolution_direction': {'recommendation': 'Focus on Exploration & Diversity. Given the ruggedness, high diversity, lack of convergence, and difficult regions, the primary focus should be on exploring the search space to escape from local optima and uncover potential opportunity regions. Techniques that promote exploration and maintain diversity are crucial. Using a mutation strategy that moves from solutions with low cost to those with high cost is recommended. ', 'specific_strategies': ['Increase the mutation rate to explore the search space more broadly. Avoid strategies that encourage local search prematurely.', 'Employ techniques to escape local optima (e.g., random restarts, simulated annealing, or adaptive mutation strategies).', 'Consider elitism, if not already used, and implement it strategically to preserve promising solutions while maintaining diversity. However, given the low number of high quality edges, elitism could prematurely focus the search', 'If applicable, consider a larger initial population size, and if the algorithm allows it, increasing population size to help balance exploration and exploitation as the search progresses.']}}
2025-06-22 16:54:00,519 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 16:54:00,519 - __main__ - INFO - 分析阶段完成
2025-06-22 16:54:00,519 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High. The wide cost range (min 10053.0, max 116123.0, with a high standard deviation of 44425.33) and the identification of 5 difficult regions suggest a highly rugged search space. The presence of high-quality edges, although only two, also contributes to this assessment indicating strong local connections in the landscape. The diversity being 0.915 is very good for a population of only 10 individuals', 'modality': 'Multi-modal. The existence of both difficult regions and the relatively high diversity of the population suggests multiple local optima.  The absence of identified opportunity regions, however, might indicate a global optimum is difficult to find.'}, 'population_state': {'diversity': 'High (0.915). This is positive and suggests the population is exploring a wide range of solutions.  Given the small population size (10), such high diversity is encouraging.', 'convergence': "Zero (0.0). This is expected, and likely desired. Given the absence of convergence in the current context of the search, the search hasn't locked on to any specific solutions and is exploring.  "}, 'difficult_regions': {'identified_challenges': '5 identified difficult regions strongly suggest the search space is complex. These regions likely present local optima or areas where solutions are trapped.  The high cost variance also suggests these regions can have very high and low costs.', 'impact': 'The difficult regions will likely hinder the convergence and efficiency of the optimization process. The algorithm may get trapped in sub-optimal areas. Efforts should focus on methods to escape these regions or avoid them completely.  '}, 'opportunity_regions': {'identified_opportunities': "None identified. The lack of opportunity regions suggests the algorithm hasn't found any areas with significant promise or potential. This suggests that further exploration is needed. The high diversity makes finding a good area potentially difficult. This could also imply that the entire search space holds promise.", 'strategies': 'Explore the search space more aggressively. Increase the mutation rate or use more diverse solutions in the initial population. Use methods that allow escaping local optima, like using genetic diversity.'}, 'evolution_direction': {'recommendation': 'Focus on Exploration & Diversity. Given the ruggedness, high diversity, lack of convergence, and difficult regions, the primary focus should be on exploring the search space to escape from local optima and uncover potential opportunity regions. Techniques that promote exploration and maintain diversity are crucial. Using a mutation strategy that moves from solutions with low cost to those with high cost is recommended. ', 'specific_strategies': ['Increase the mutation rate to explore the search space more broadly. Avoid strategies that encourage local search prematurely.', 'Employ techniques to escape local optima (e.g., random restarts, simulated annealing, or adaptive mutation strategies).', 'Consider elitism, if not already used, and implement it strategically to preserve promising solutions while maintaining diversity. However, given the low number of high quality edges, elitism could prematurely focus the search', 'If applicable, consider a larger initial population size, and if the algorithm allows it, increasing population size to help balance exploration and exploitation as the search progresses.']}}
2025-06-22 16:54:00,520 - __main__ - INFO - 开始策略分配阶段
2025-06-22 16:54:00,520 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 16:54:00,520 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 0
- Population: 10 individuals, diversity=High (0.915). This is positive and suggests the population is exploring a wide range of solutions.  Given the small population size (10), such high diversity is encouraging.
- Best cost: 10053.0, Mean cost: 77442.4
- Search space: Ruggedness: High. The wide cost range (min 10053.0, max 116123.0, with a high standard deviation of 44425.33) and the identification of 5 difficult regions suggest a highly rugged search space. The presence of high-quality edges, although only two, also contributes to this assessment indicating strong local connections in the landscape. The diversity being 0.915 is very good for a population of only 10 individuals, Modality: Multi-modal. The existence of both difficult regions and the relatively high diversity of the population suggests multiple local optima.  The absence of identified opportunity regions, however, might indicate a global optimum is difficult to find.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: No previous feedback

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 16:54:00,521 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 16:54:05,408 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.1,
      "search_radius_multiplier": 1.5
    },
    "exploit": {
      "mutation_rate": 0.05,
      "step_size": 0.05,
      "search_radius_multiplier": 0.7
    }
  }
}
```

2025-06-22 16:54:05,409 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-06-22 16:54:05,410 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.1,
      "search_radius_multiplier": 1.5
    },
    "exploit": {
      "mutation_rate": 0.05,
      "step_size": 0.05,
      "search_radius_multiplier": 0.7
    }
  }
}
```

2025-06-22 16:54:05,410 - __main__ - INFO - 策略分配阶段完成
2025-06-22 16:54:05,410 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'explore', 'exploit', 'exploit']
2025-06-22 16:54:05,410 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "exploit",
    "5": "exploit",
    "6": "exploit",
    "7": "explore",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.15,
      "step_size": 0.1,
      "search_radius_multiplier": 1.5
    },
    "exploit": {
      "mutation_rate": 0.05,
      "step_size": 0.05,
      "search_radius_multiplier": 0.7
    }
  }
}
```

2025-06-22 16:54:05,410 - __main__ - INFO - 开始进化阶段
2025-06-22 16:54:05,410 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 16:54:05,410 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:54:05,412 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [61, 55, 56] and ending with [50, 41, 42]
- Cost: 10090.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7369230769230769
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:54:05,412 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:54:11,816 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [61, 62, 63, 64, 65, 59, 57, 58, 52, 51, 53, 54, 50, 49, 47, 46, 44, 45, 43, 41, 42, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 55, 56],
  "modification_strategy": "Prioritized diversification by: 1) Identifying promising opportunity regions and weaving the path through these areas; 2) Employing a breadth-first approach to explore diverse branches; 3) Avoiding difficult regions based on the provided constraints; 4) Re-routing the path strategically based on its initial position; 5) Prioritizing a more random walk through the entire network. This strategy balances high diversity with a reasonable level of quality to generate a good path.",
  "targeted_regions": ["Opportunity regions", "Regions that diverge significantly from the original path's trajectory"]
}
```
2025-06-22 16:54:11,816 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:54:11,816 - ExplorationExpert - INFO - 探索路径生成完成，成本: 16976.0, 路径: [61, 62, 63, 64, 65, 59, 57, 58, 52, 51, 53, 54, 50, 49, 47, 46, 44, 45, 43, 41, 42, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 55, 56]
2025-06-22 16:54:11,816 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [61, 62, 63, 64, 65, 59, 57, 58, 52, 51, 53, 54, 50, 49, 47, 46, 44, 45, 43, 41, 42, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 55, 56], 'cur_cost': 16976.0}
2025-06-22 16:54:11,816 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 16:54:11,816 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:54:11,819 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [61, 55, 56] and ending with [50, 41, 42]
- Cost: 10090.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8018575851393189
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:54:11,819 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:54:16,837 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    61, 54, 48, 39, 33, 28, 22, 16, 10, 4, 3, 9, 15, 21, 27, 32, 38, 44, 49, 50, 41, 42
  ],
  "modification_strategy": "Prioritized exploration of unexplored and opportunity regions identified in the search context.  This path aims to diverge from the original by immediately targeting different initial nodes (e.g. 54 vs 55 in the original path) and then prioritizing movement through opportunity regions to maximize the chance of encountering new, diverse segments. The path still focuses on a path towards the end point [50, 41, 42]. This strategy balances diversity with some degree of path completion.",
  "targeted_regions": [
    "Unexplored regions",
    "Opportunity regions"
  ]
}
```
2025-06-22 16:54:16,837 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:54:16,837 - ExplorationExpert - INFO - 探索路径生成完成，成本: 15719.0, 路径: [61, 54, 48, 39, 33, 28, 22, 16, 10, 4, 3, 9, 15, 21, 27, 32, 38, 44, 49, 50, 41, 42]
2025-06-22 16:54:16,837 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [61, 54, 48, 39, 33, 28, 22, 16, 10, 4, 3, 9, 15, 21, 27, 32, 38, 44, 49, 50, 41, 42], 'cur_cost': 15719.0}
2025-06-22 16:54:16,837 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 16:54:16,837 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:54:16,837 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [17, 12, 22] and ending with [43, 48, 42]
- Cost: 10053.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8588039867109635
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:54:16,837 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:54:22,114 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    17, 15, 20, 25, 30, 31, 36, 37, 40, 45, 50, 51, 56, 61, 60, 55, 54, 49, 44, 39, 34, 29, 24, 19, 14, 9, 4, 5, 10, 11, 16, 21, 26, 27, 32, 33, 38, 43, 48, 42, 47, 46, 41, 35, 28, 23, 18, 13, 8, 3, 2, 7, 1, 0, 6, 12, 22, 52, 57, 62, 63, 64, 65, 59, 58, 53
  ],
  "modification_strategy": "Heuristic path generation favoring exploration of unexplored regions and minimizing overlap with the original path. Prioritized nodes within the opportunity regions and utilized a randomized approach to diversify node selection and break away from the initial path structure. While keeping within the initial start and end constraints",
  "targeted_regions": [
    "Unexplored regions",
    "Opportunity regions"
  ]
}
```
2025-06-22 16:54:22,114 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:54:22,114 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30312.0, 路径: [17, 15, 20, 25, 30, 31, 36, 37, 40, 45, 50, 51, 56, 61, 60, 55, 54, 49, 44, 39, 34, 29, 24, 19, 14, 9, 4, 5, 10, 11, 16, 21, 26, 27, 32, 33, 38, 43, 48, 42, 47, 46, 41, 35, 28, 23, 18, 13, 8, 3, 2, 7, 1, 0, 6, 12, 22, 52, 57, 62, 63, 64, 65, 59, 58, 53]
2025-06-22 16:54:22,114 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [17, 15, 20, 25, 30, 31, 36, 37, 40, 45, 50, 51, 56, 61, 60, 55, 54, 49, 44, 39, 34, 29, 24, 19, 14, 9, 4, 5, 10, 11, 16, 21, 26, 27, 32, 33, 38, 43, 48, 42, 47, 46, 41, 35, 28, 23, 18, 13, 8, 3, 2, 7, 1, 0, 6, 12, 22, 52, 57, 62, 63, 64, 65, 59, 58, 53], 'cur_cost': 30312.0}
2025-06-22 16:54:22,114 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 16:54:22,114 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:54:22,114 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [14, 34, 0] and ending with [60, 40, 17]
- Cost: 105971.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8588039867109635
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:54:22,114 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:54:27,659 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    14, 34, 1, 45, 10, 22, 38, 5, 18, 55, 31, 64, 2, 48, 12, 27, 4, 40, 17, 30, 59, 3, 50, 15, 46, 20, 33, 62, 8, 25, 41, 57, 19, 36, 65, 6, 52, 39, 11, 24, 54, 28, 49, 60, 7, 37, 32, 63, 21, 56, 13, 44, 51, 35, 9, 29, 58, 42, 16, 53, 47, 26, 61, 43, 0
  ],
  "modification_strategy": "This strategy prioritizes high diversity by altering the traversal order significantly. It reorders the path to prioritize exploration of unexplored regions by using a Breadth First Search with a bias to explore opportunity regions, while maintaining a risk tolerance of 0.6. This approach attempts to find a path that has a cost which is acceptable compared to the current path.",
  "targeted_regions": [
    "Opportunity Regions",
    "Unexplored Regions"
  ]
}
```
2025-06-22 16:54:27,659 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:54:27,659 - ExplorationExpert - INFO - 探索路径生成完成，成本: 127850.0, 路径: [14, 34, 1, 45, 10, 22, 38, 5, 18, 55, 31, 64, 2, 48, 12, 27, 4, 40, 17, 30, 59, 3, 50, 15, 46, 20, 33, 62, 8, 25, 41, 57, 19, 36, 65, 6, 52, 39, 11, 24, 54, 28, 49, 60, 7, 37, 32, 63, 21, 56, 13, 44, 51, 35, 9, 29, 58, 42, 16, 53, 47, 26, 61, 43, 0]
2025-06-22 16:54:27,659 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [14, 34, 1, 45, 10, 22, 38, 5, 18, 55, 31, 64, 2, 48, 12, 27, 4, 40, 17, 30, 59, 3, 50, 15, 46, 20, 33, 62, 8, 25, 41, 57, 19, 36, 65, 6, 52, 39, 11, 24, 54, 28, 49, 60, 7, 37, 32, 63, 21, 56, 13, 44, 51, 35, 9, 29, 58, 42, 16, 53, 47, 26, 61, 43, 0], 'cur_cost': 127850.0}
2025-06-22 16:54:27,659 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-22 16:54:27,659 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:54:27,659 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:54:27,659 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 103433.0
2025-06-22 16:54:29,221 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 16:54:29,221 - ExploitationExpert - INFO - res_population_costs: [9582]
2025-06-22 16:54:29,222 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 21, 20, 13, 16, 18, 23, 22, 12, 17, 15, 14,  3,  7,  1],
      dtype=int64)]
2025-06-22 16:54:29,222 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:54:29,223 - ExploitationExpert - INFO - populations: [{'tour': [61, 62, 63, 64, 65, 59, 57, 58, 52, 51, 53, 54, 50, 49, 47, 46, 44, 45, 43, 41, 42, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 55, 56], 'cur_cost': 16976.0}, {'tour': [61, 54, 48, 39, 33, 28, 22, 16, 10, 4, 3, 9, 15, 21, 27, 32, 38, 44, 49, 50, 41, 42], 'cur_cost': 15719.0}, {'tour': [17, 15, 20, 25, 30, 31, 36, 37, 40, 45, 50, 51, 56, 61, 60, 55, 54, 49, 44, 39, 34, 29, 24, 19, 14, 9, 4, 5, 10, 11, 16, 21, 26, 27, 32, 33, 38, 43, 48, 42, 47, 46, 41, 35, 28, 23, 18, 13, 8, 3, 2, 7, 1, 0, 6, 12, 22, 52, 57, 62, 63, 64, 65, 59, 58, 53], 'cur_cost': 30312.0}, {'tour': [14, 34, 1, 45, 10, 22, 38, 5, 18, 55, 31, 64, 2, 48, 12, 27, 4, 40, 17, 30, 59, 3, 50, 15, 46, 20, 33, 62, 8, 25, 41, 57, 19, 36, 65, 6, 52, 39, 11, 24, 54, 28, 49, 60, 7, 37, 32, 63, 21, 56, 13, 44, 51, 35, 9, 29, 58, 42, 16, 53, 47, 26, 61, 43, 0], 'cur_cost': 127850.0}, {'tour': array([33, 28, 43,  0, 32, 20, 52, 24, 42, 48, 25, 27, 53, 49, 55, 26, 11,
       39, 46, 40, 57,  4, 56, 41, 30,  6,  5, 65, 47, 64, 23, 51, 22, 35,
       10, 59, 45, 50,  1,  7,  8, 17, 60, 16, 37, 58, 14, 62, 63,  3, 61,
       19, 31, 13, 38, 12, 54, 44,  2, 21,  9, 18, 29, 34, 15, 36]), 'cur_cost': 103433.0}, {'tour': [37, 13, 10, 32, 35, 19, 49, 55, 12, 2, 46, 57, 61, 36, 45, 14, 38, 29, 7, 30, 39, 52, 63, 17, 0, 42, 27, 22, 11, 31, 44, 24, 65, 25, 6, 54, 50, 41, 33, 34, 53, 43, 58, 21, 18, 9, 8, 1, 48, 20, 23, 64, 62, 16, 40, 28, 4, 59, 60, 51, 3, 56, 26, 5, 15, 47], 'cur_cost': 110267.0}, {'tour': [64, 15, 58, 31, 20, 19, 1, 56, 53, 16, 44, 24, 9, 52, 6, 21, 50, 8, 4, 45, 29, 63, 27, 48, 11, 33, 42, 46, 55, 12, 0, 62, 49, 13, 18, 5, 38, 23, 60, 47, 7, 28, 43, 40, 65, 2, 30, 39, 17, 3, 51, 61, 10, 25, 57, 54, 14, 35, 22, 34, 36, 32, 37, 41, 59, 26], 'cur_cost': 110699.0}, {'tour': [9, 50, 52, 40, 24, 60, 12, 0, 4, 46, 11, 65, 32, 38, 64, 13, 5, 8, 14, 28, 54, 6, 3, 39, 10, 35, 22, 21, 37, 42, 48, 26, 55, 47, 43, 58, 31, 27, 2, 56, 41, 53, 16, 62, 49, 19, 36, 51, 34, 59, 61, 25, 30, 1, 29, 18, 57, 33, 17, 63, 7, 44, 23, 20, 15, 45], 'cur_cost': 116123.0}, {'tour': [50, 34, 32, 20, 48, 25, 24, 39, 15, 2, 21, 4, 47, 17, 5, 61, 58, 52, 0, 49, 54, 51, 23, 56, 22, 8, 3, 26, 1, 10, 65, 41, 42, 63, 14, 7, 31, 53, 45, 33, 13, 6, 37, 40, 44, 30, 11, 36, 35, 27, 28, 19, 43, 16, 64, 60, 62, 18, 46, 55, 59, 12, 29, 57, 9, 38], 'cur_cost': 94942.0}, {'tour': [38, 11, 18, 14, 53, 40, 8, 63, 64, 36, 10, 15, 33, 34, 57, 2, 49, 37, 0, 5, 62, 55, 56, 21, 47, 7, 26, 46, 35, 30, 39, 50, 42, 41, 4, 31, 24, 54, 27, 12, 17, 32, 45, 20, 52, 44, 59, 51, 1, 48, 25, 6, 22, 29, 60, 61, 28, 3, 58, 16, 19, 9, 43, 13, 23, 65], 'cur_cost': 104863.0}]
2025-06-22 16:54:29,223 - ExploitationExpert - INFO - 局部搜索耗时: 1.56秒
2025-06-22 16:54:29,224 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-22 16:54:29,224 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-22 16:54:29,224 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 16:54:29,224 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:54:29,225 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:54:29,225 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 103184.0
2025-06-22 16:54:30,664 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 16:54:30,664 - ExploitationExpert - INFO - res_population_costs: [9582, 9565]
2025-06-22 16:54:30,664 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 21, 20, 13, 16, 18, 23, 22, 12, 17, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 16:54:30,664 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:54:30,664 - ExploitationExpert - INFO - populations: [{'tour': [61, 62, 63, 64, 65, 59, 57, 58, 52, 51, 53, 54, 50, 49, 47, 46, 44, 45, 43, 41, 42, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 55, 56], 'cur_cost': 16976.0}, {'tour': [61, 54, 48, 39, 33, 28, 22, 16, 10, 4, 3, 9, 15, 21, 27, 32, 38, 44, 49, 50, 41, 42], 'cur_cost': 15719.0}, {'tour': [17, 15, 20, 25, 30, 31, 36, 37, 40, 45, 50, 51, 56, 61, 60, 55, 54, 49, 44, 39, 34, 29, 24, 19, 14, 9, 4, 5, 10, 11, 16, 21, 26, 27, 32, 33, 38, 43, 48, 42, 47, 46, 41, 35, 28, 23, 18, 13, 8, 3, 2, 7, 1, 0, 6, 12, 22, 52, 57, 62, 63, 64, 65, 59, 58, 53], 'cur_cost': 30312.0}, {'tour': [14, 34, 1, 45, 10, 22, 38, 5, 18, 55, 31, 64, 2, 48, 12, 27, 4, 40, 17, 30, 59, 3, 50, 15, 46, 20, 33, 62, 8, 25, 41, 57, 19, 36, 65, 6, 52, 39, 11, 24, 54, 28, 49, 60, 7, 37, 32, 63, 21, 56, 13, 44, 51, 35, 9, 29, 58, 42, 16, 53, 47, 26, 61, 43, 0], 'cur_cost': 127850.0}, {'tour': array([33, 28, 43,  0, 32, 20, 52, 24, 42, 48, 25, 27, 53, 49, 55, 26, 11,
       39, 46, 40, 57,  4, 56, 41, 30,  6,  5, 65, 47, 64, 23, 51, 22, 35,
       10, 59, 45, 50,  1,  7,  8, 17, 60, 16, 37, 58, 14, 62, 63,  3, 61,
       19, 31, 13, 38, 12, 54, 44,  2, 21,  9, 18, 29, 34, 15, 36]), 'cur_cost': 103433.0}, {'tour': array([63,  2, 15, 26, 49, 40, 52, 23, 14, 53, 42, 28, 33, 47,  7, 13, 18,
       20,  6,  5,  4, 61, 43, 45, 55, 60, 64, 51, 21, 50, 27, 56,  9, 29,
       58, 65, 35,  8, 17,  3, 32, 10, 37, 31,  0, 44, 12, 19, 39, 36, 22,
       11, 34, 59, 25, 16, 38,  1, 62, 41, 30, 24, 48, 57, 54, 46]), 'cur_cost': 103184.0}, {'tour': [64, 15, 58, 31, 20, 19, 1, 56, 53, 16, 44, 24, 9, 52, 6, 21, 50, 8, 4, 45, 29, 63, 27, 48, 11, 33, 42, 46, 55, 12, 0, 62, 49, 13, 18, 5, 38, 23, 60, 47, 7, 28, 43, 40, 65, 2, 30, 39, 17, 3, 51, 61, 10, 25, 57, 54, 14, 35, 22, 34, 36, 32, 37, 41, 59, 26], 'cur_cost': 110699.0}, {'tour': [9, 50, 52, 40, 24, 60, 12, 0, 4, 46, 11, 65, 32, 38, 64, 13, 5, 8, 14, 28, 54, 6, 3, 39, 10, 35, 22, 21, 37, 42, 48, 26, 55, 47, 43, 58, 31, 27, 2, 56, 41, 53, 16, 62, 49, 19, 36, 51, 34, 59, 61, 25, 30, 1, 29, 18, 57, 33, 17, 63, 7, 44, 23, 20, 15, 45], 'cur_cost': 116123.0}, {'tour': [50, 34, 32, 20, 48, 25, 24, 39, 15, 2, 21, 4, 47, 17, 5, 61, 58, 52, 0, 49, 54, 51, 23, 56, 22, 8, 3, 26, 1, 10, 65, 41, 42, 63, 14, 7, 31, 53, 45, 33, 13, 6, 37, 40, 44, 30, 11, 36, 35, 27, 28, 19, 43, 16, 64, 60, 62, 18, 46, 55, 59, 12, 29, 57, 9, 38], 'cur_cost': 94942.0}, {'tour': [38, 11, 18, 14, 53, 40, 8, 63, 64, 36, 10, 15, 33, 34, 57, 2, 49, 37, 0, 5, 62, 55, 56, 21, 47, 7, 26, 46, 35, 30, 39, 50, 42, 41, 4, 31, 24, 54, 27, 12, 17, 32, 45, 20, 52, 44, 59, 51, 1, 48, 25, 6, 22, 29, 60, 61, 28, 3, 58, 16, 19, 9, 43, 13, 23, 65], 'cur_cost': 104863.0}]
2025-06-22 16:54:30,664 - ExploitationExpert - INFO - 局部搜索耗时: 1.44秒
2025-06-22 16:54:30,664 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-22 16:54:30,664 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 16:54:30,664 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 16:54:30,664 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:54:30,664 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:54:30,672 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 116796.0
2025-06-22 16:54:31,198 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 16:54:31,198 - ExploitationExpert - INFO - res_population_costs: [9582, 9565, 9527]
2025-06-22 16:54:31,198 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 21, 20, 13, 16, 18, 23, 22, 12, 17, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 16:54:31,198 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:54:31,200 - ExploitationExpert - INFO - populations: [{'tour': [61, 62, 63, 64, 65, 59, 57, 58, 52, 51, 53, 54, 50, 49, 47, 46, 44, 45, 43, 41, 42, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 55, 56], 'cur_cost': 16976.0}, {'tour': [61, 54, 48, 39, 33, 28, 22, 16, 10, 4, 3, 9, 15, 21, 27, 32, 38, 44, 49, 50, 41, 42], 'cur_cost': 15719.0}, {'tour': [17, 15, 20, 25, 30, 31, 36, 37, 40, 45, 50, 51, 56, 61, 60, 55, 54, 49, 44, 39, 34, 29, 24, 19, 14, 9, 4, 5, 10, 11, 16, 21, 26, 27, 32, 33, 38, 43, 48, 42, 47, 46, 41, 35, 28, 23, 18, 13, 8, 3, 2, 7, 1, 0, 6, 12, 22, 52, 57, 62, 63, 64, 65, 59, 58, 53], 'cur_cost': 30312.0}, {'tour': [14, 34, 1, 45, 10, 22, 38, 5, 18, 55, 31, 64, 2, 48, 12, 27, 4, 40, 17, 30, 59, 3, 50, 15, 46, 20, 33, 62, 8, 25, 41, 57, 19, 36, 65, 6, 52, 39, 11, 24, 54, 28, 49, 60, 7, 37, 32, 63, 21, 56, 13, 44, 51, 35, 9, 29, 58, 42, 16, 53, 47, 26, 61, 43, 0], 'cur_cost': 127850.0}, {'tour': array([33, 28, 43,  0, 32, 20, 52, 24, 42, 48, 25, 27, 53, 49, 55, 26, 11,
       39, 46, 40, 57,  4, 56, 41, 30,  6,  5, 65, 47, 64, 23, 51, 22, 35,
       10, 59, 45, 50,  1,  7,  8, 17, 60, 16, 37, 58, 14, 62, 63,  3, 61,
       19, 31, 13, 38, 12, 54, 44,  2, 21,  9, 18, 29, 34, 15, 36]), 'cur_cost': 103433.0}, {'tour': array([63,  2, 15, 26, 49, 40, 52, 23, 14, 53, 42, 28, 33, 47,  7, 13, 18,
       20,  6,  5,  4, 61, 43, 45, 55, 60, 64, 51, 21, 50, 27, 56,  9, 29,
       58, 65, 35,  8, 17,  3, 32, 10, 37, 31,  0, 44, 12, 19, 39, 36, 22,
       11, 34, 59, 25, 16, 38,  1, 62, 41, 30, 24, 48, 57, 54, 46]), 'cur_cost': 103184.0}, {'tour': array([47,  2, 65, 38, 33, 37, 18, 64, 13, 11, 25, 62, 32, 14, 27, 19, 34,
       30, 58, 45, 16, 26, 61, 36, 10,  1, 12, 42, 21, 41, 44,  7, 23,  6,
        3, 53, 43, 56, 57, 15, 46, 59, 20, 40,  0, 51, 63, 28,  5, 50, 35,
       60, 49, 48, 22, 54, 17,  4, 31, 29, 39, 55, 24, 52,  9,  8]), 'cur_cost': 116796.0}, {'tour': [9, 50, 52, 40, 24, 60, 12, 0, 4, 46, 11, 65, 32, 38, 64, 13, 5, 8, 14, 28, 54, 6, 3, 39, 10, 35, 22, 21, 37, 42, 48, 26, 55, 47, 43, 58, 31, 27, 2, 56, 41, 53, 16, 62, 49, 19, 36, 51, 34, 59, 61, 25, 30, 1, 29, 18, 57, 33, 17, 63, 7, 44, 23, 20, 15, 45], 'cur_cost': 116123.0}, {'tour': [50, 34, 32, 20, 48, 25, 24, 39, 15, 2, 21, 4, 47, 17, 5, 61, 58, 52, 0, 49, 54, 51, 23, 56, 22, 8, 3, 26, 1, 10, 65, 41, 42, 63, 14, 7, 31, 53, 45, 33, 13, 6, 37, 40, 44, 30, 11, 36, 35, 27, 28, 19, 43, 16, 64, 60, 62, 18, 46, 55, 59, 12, 29, 57, 9, 38], 'cur_cost': 94942.0}, {'tour': [38, 11, 18, 14, 53, 40, 8, 63, 64, 36, 10, 15, 33, 34, 57, 2, 49, 37, 0, 5, 62, 55, 56, 21, 47, 7, 26, 46, 35, 30, 39, 50, 42, 41, 4, 31, 24, 54, 27, 12, 17, 32, 45, 20, 52, 44, 59, 51, 1, 48, 25, 6, 22, 29, 60, 61, 28, 3, 58, 16, 19, 9, 43, 13, 23, 65], 'cur_cost': 104863.0}]
2025-06-22 16:54:31,202 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-22 16:54:31,202 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-22 16:54:31,202 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 16:54:31,202 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-22 16:54:31,202 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 16:54:31,203 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [9, 50, 52] and ending with [20, 15, 45]
- Cost: 116123.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.848585690515807
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 16:54:31,203 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 16:54:36,506 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    9, 27, 40, 6, 38, 12, 21, 30, 58, 60, 54, 47, 5, 32, 1, 42, 18, 28, 53, 63, 61, 3, 48, 57, 33, 49, 36, 23, 16, 51, 41, 39, 2, 55, 64, 25, 17, 43, 8, 29, 35, 10, 44, 37, 4, 20, 15, 45],
  "modification_strategy": "Utilized a breadth-first exploration approach, prioritizing exploration of the two opportunity regions. The starting point remained the same as the provided path, but then diverged into areas unexplored by the original path, with a focus on diverse node connections and avoiding the specified difficult regions. Focused on maximizing diversity weight to create a highly contrasting path.",
  "targeted_regions": "The two opportunity regions were directly incorporated into the exploration. Regions with higher population diversity were prioritized as well to create a new, optimized path."
}
```
2025-06-22 16:54:36,506 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 16:54:36,506 - ExplorationExpert - INFO - 探索路径生成完成，成本: 76100.0, 路径: [9, 27, 40, 6, 38, 12, 21, 30, 58, 60, 54, 47, 5, 32, 1, 42, 18, 28, 53, 63, 61, 3, 48, 57, 33, 49, 36, 23, 16, 51, 41, 39, 2, 55, 64, 25, 17, 43, 8, 29, 35, 10, 44, 37, 4, 20, 15, 45]
2025-06-22 16:54:36,506 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [9, 27, 40, 6, 38, 12, 21, 30, 58, 60, 54, 47, 5, 32, 1, 42, 18, 28, 53, 63, 61, 3, 48, 57, 33, 49, 36, 23, 16, 51, 41, 39, 2, 55, 64, 25, 17, 43, 8, 29, 35, 10, 44, 37, 4, 20, 15, 45], 'cur_cost': 76100.0}
2025-06-22 16:54:36,506 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 16:54:36,506 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:54:36,506 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:54:36,506 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 101371.0
2025-06-22 16:54:37,008 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 16:54:37,008 - ExploitationExpert - INFO - res_population_costs: [9582, 9565, 9527]
2025-06-22 16:54:37,008 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 21, 20, 13, 16, 18, 23, 22, 12, 17, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 16:54:37,008 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:54:37,008 - ExploitationExpert - INFO - populations: [{'tour': [61, 62, 63, 64, 65, 59, 57, 58, 52, 51, 53, 54, 50, 49, 47, 46, 44, 45, 43, 41, 42, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 55, 56], 'cur_cost': 16976.0}, {'tour': [61, 54, 48, 39, 33, 28, 22, 16, 10, 4, 3, 9, 15, 21, 27, 32, 38, 44, 49, 50, 41, 42], 'cur_cost': 15719.0}, {'tour': [17, 15, 20, 25, 30, 31, 36, 37, 40, 45, 50, 51, 56, 61, 60, 55, 54, 49, 44, 39, 34, 29, 24, 19, 14, 9, 4, 5, 10, 11, 16, 21, 26, 27, 32, 33, 38, 43, 48, 42, 47, 46, 41, 35, 28, 23, 18, 13, 8, 3, 2, 7, 1, 0, 6, 12, 22, 52, 57, 62, 63, 64, 65, 59, 58, 53], 'cur_cost': 30312.0}, {'tour': [14, 34, 1, 45, 10, 22, 38, 5, 18, 55, 31, 64, 2, 48, 12, 27, 4, 40, 17, 30, 59, 3, 50, 15, 46, 20, 33, 62, 8, 25, 41, 57, 19, 36, 65, 6, 52, 39, 11, 24, 54, 28, 49, 60, 7, 37, 32, 63, 21, 56, 13, 44, 51, 35, 9, 29, 58, 42, 16, 53, 47, 26, 61, 43, 0], 'cur_cost': 127850.0}, {'tour': array([33, 28, 43,  0, 32, 20, 52, 24, 42, 48, 25, 27, 53, 49, 55, 26, 11,
       39, 46, 40, 57,  4, 56, 41, 30,  6,  5, 65, 47, 64, 23, 51, 22, 35,
       10, 59, 45, 50,  1,  7,  8, 17, 60, 16, 37, 58, 14, 62, 63,  3, 61,
       19, 31, 13, 38, 12, 54, 44,  2, 21,  9, 18, 29, 34, 15, 36]), 'cur_cost': 103433.0}, {'tour': array([63,  2, 15, 26, 49, 40, 52, 23, 14, 53, 42, 28, 33, 47,  7, 13, 18,
       20,  6,  5,  4, 61, 43, 45, 55, 60, 64, 51, 21, 50, 27, 56,  9, 29,
       58, 65, 35,  8, 17,  3, 32, 10, 37, 31,  0, 44, 12, 19, 39, 36, 22,
       11, 34, 59, 25, 16, 38,  1, 62, 41, 30, 24, 48, 57, 54, 46]), 'cur_cost': 103184.0}, {'tour': array([47,  2, 65, 38, 33, 37, 18, 64, 13, 11, 25, 62, 32, 14, 27, 19, 34,
       30, 58, 45, 16, 26, 61, 36, 10,  1, 12, 42, 21, 41, 44,  7, 23,  6,
        3, 53, 43, 56, 57, 15, 46, 59, 20, 40,  0, 51, 63, 28,  5, 50, 35,
       60, 49, 48, 22, 54, 17,  4, 31, 29, 39, 55, 24, 52,  9,  8]), 'cur_cost': 116796.0}, {'tour': [9, 27, 40, 6, 38, 12, 21, 30, 58, 60, 54, 47, 5, 32, 1, 42, 18, 28, 53, 63, 61, 3, 48, 57, 33, 49, 36, 23, 16, 51, 41, 39, 2, 55, 64, 25, 17, 43, 8, 29, 35, 10, 44, 37, 4, 20, 15, 45], 'cur_cost': 76100.0}, {'tour': array([65, 51, 15, 24, 25, 21, 18, 11, 16, 56, 28,  3, 60,  5, 10, 53,  6,
       40,  1, 32,  8, 42, 34, 41, 31, 13,  0,  2, 46, 59,  9, 37, 45, 30,
       63, 17, 27, 29, 39, 33, 35, 47, 62, 23, 20, 44, 49, 48, 61, 14, 36,
       26, 12,  4, 55, 43, 58, 52, 57,  7, 19, 64, 50, 38, 54, 22]), 'cur_cost': 101371.0}, {'tour': [38, 11, 18, 14, 53, 40, 8, 63, 64, 36, 10, 15, 33, 34, 57, 2, 49, 37, 0, 5, 62, 55, 56, 21, 47, 7, 26, 46, 35, 30, 39, 50, 42, 41, 4, 31, 24, 54, 27, 12, 17, 32, 45, 20, 52, 44, 59, 51, 1, 48, 25, 6, 22, 29, 60, 61, 28, 3, 58, 16, 19, 9, 43, 13, 23, 65], 'cur_cost': 104863.0}]
2025-06-22 16:54:37,015 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 16:54:37,016 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-22 16:54:37,016 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 16:54:37,017 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 16:54:37,017 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 16:54:37,017 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 16:54:37,018 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 103229.0
2025-06-22 16:54:37,524 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 16:54:37,524 - ExploitationExpert - INFO - res_population_costs: [9582, 9565, 9527]
2025-06-22 16:54:37,524 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 61, 55, 56, 59, 62,
       53, 64, 57, 54, 60, 58, 39, 44, 45, 41, 38, 51, 50, 42, 48, 46, 47,
       49, 40, 43, 36, 26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,
       19, 21, 20, 13, 16, 18, 23, 22, 12, 17, 15, 14,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-22 16:54:37,526 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 16:54:37,526 - ExploitationExpert - INFO - populations: [{'tour': [61, 62, 63, 64, 65, 59, 57, 58, 52, 51, 53, 54, 50, 49, 47, 46, 44, 45, 43, 41, 42, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 55, 56], 'cur_cost': 16976.0}, {'tour': [61, 54, 48, 39, 33, 28, 22, 16, 10, 4, 3, 9, 15, 21, 27, 32, 38, 44, 49, 50, 41, 42], 'cur_cost': 15719.0}, {'tour': [17, 15, 20, 25, 30, 31, 36, 37, 40, 45, 50, 51, 56, 61, 60, 55, 54, 49, 44, 39, 34, 29, 24, 19, 14, 9, 4, 5, 10, 11, 16, 21, 26, 27, 32, 33, 38, 43, 48, 42, 47, 46, 41, 35, 28, 23, 18, 13, 8, 3, 2, 7, 1, 0, 6, 12, 22, 52, 57, 62, 63, 64, 65, 59, 58, 53], 'cur_cost': 30312.0}, {'tour': [14, 34, 1, 45, 10, 22, 38, 5, 18, 55, 31, 64, 2, 48, 12, 27, 4, 40, 17, 30, 59, 3, 50, 15, 46, 20, 33, 62, 8, 25, 41, 57, 19, 36, 65, 6, 52, 39, 11, 24, 54, 28, 49, 60, 7, 37, 32, 63, 21, 56, 13, 44, 51, 35, 9, 29, 58, 42, 16, 53, 47, 26, 61, 43, 0], 'cur_cost': 127850.0}, {'tour': array([33, 28, 43,  0, 32, 20, 52, 24, 42, 48, 25, 27, 53, 49, 55, 26, 11,
       39, 46, 40, 57,  4, 56, 41, 30,  6,  5, 65, 47, 64, 23, 51, 22, 35,
       10, 59, 45, 50,  1,  7,  8, 17, 60, 16, 37, 58, 14, 62, 63,  3, 61,
       19, 31, 13, 38, 12, 54, 44,  2, 21,  9, 18, 29, 34, 15, 36]), 'cur_cost': 103433.0}, {'tour': array([63,  2, 15, 26, 49, 40, 52, 23, 14, 53, 42, 28, 33, 47,  7, 13, 18,
       20,  6,  5,  4, 61, 43, 45, 55, 60, 64, 51, 21, 50, 27, 56,  9, 29,
       58, 65, 35,  8, 17,  3, 32, 10, 37, 31,  0, 44, 12, 19, 39, 36, 22,
       11, 34, 59, 25, 16, 38,  1, 62, 41, 30, 24, 48, 57, 54, 46]), 'cur_cost': 103184.0}, {'tour': array([47,  2, 65, 38, 33, 37, 18, 64, 13, 11, 25, 62, 32, 14, 27, 19, 34,
       30, 58, 45, 16, 26, 61, 36, 10,  1, 12, 42, 21, 41, 44,  7, 23,  6,
        3, 53, 43, 56, 57, 15, 46, 59, 20, 40,  0, 51, 63, 28,  5, 50, 35,
       60, 49, 48, 22, 54, 17,  4, 31, 29, 39, 55, 24, 52,  9,  8]), 'cur_cost': 116796.0}, {'tour': [9, 27, 40, 6, 38, 12, 21, 30, 58, 60, 54, 47, 5, 32, 1, 42, 18, 28, 53, 63, 61, 3, 48, 57, 33, 49, 36, 23, 16, 51, 41, 39, 2, 55, 64, 25, 17, 43, 8, 29, 35, 10, 44, 37, 4, 20, 15, 45], 'cur_cost': 76100.0}, {'tour': array([65, 51, 15, 24, 25, 21, 18, 11, 16, 56, 28,  3, 60,  5, 10, 53,  6,
       40,  1, 32,  8, 42, 34, 41, 31, 13,  0,  2, 46, 59,  9, 37, 45, 30,
       63, 17, 27, 29, 39, 33, 35, 47, 62, 23, 20, 44, 49, 48, 61, 14, 36,
       26, 12,  4, 55, 43, 58, 52, 57,  7, 19, 64, 50, 38, 54, 22]), 'cur_cost': 101371.0}, {'tour': array([43, 18, 47, 34, 16,  2, 37, 54, 32, 51, 64, 42, 28, 12, 55, 62, 61,
        8,  0, 33, 35, 11, 53,  4, 14, 56, 39, 13, 52, 60, 23, 41, 40, 49,
       31, 15, 26, 22, 59, 10, 58, 25, 27,  5, 48, 65, 45, 38, 57,  9, 46,
       17, 36, 19, 63,  1, 20, 30, 44,  7, 50,  6,  3, 29, 21, 24]), 'cur_cost': 103229.0}]
2025-06-22 16:54:37,526 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 16:54:37,526 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-22 16:54:37,526 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 16:54:37,529 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [61, 62, 63, 64, 65, 59, 57, 58, 52, 51, 53, 54, 50, 49, 47, 46, 44, 45, 43, 41, 42, 39, 38, 37, 36, 35, 34, 33, 32, 31, 30, 29, 28, 27, 26, 25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 55, 56], 'cur_cost': 16976.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [61, 54, 48, 39, 33, 28, 22, 16, 10, 4, 3, 9, 15, 21, 27, 32, 38, 44, 49, 50, 41, 42], 'cur_cost': 15719.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [17, 15, 20, 25, 30, 31, 36, 37, 40, 45, 50, 51, 56, 61, 60, 55, 54, 49, 44, 39, 34, 29, 24, 19, 14, 9, 4, 5, 10, 11, 16, 21, 26, 27, 32, 33, 38, 43, 48, 42, 47, 46, 41, 35, 28, 23, 18, 13, 8, 3, 2, 7, 1, 0, 6, 12, 22, 52, 57, 62, 63, 64, 65, 59, 58, 53], 'cur_cost': 30312.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [14, 34, 1, 45, 10, 22, 38, 5, 18, 55, 31, 64, 2, 48, 12, 27, 4, 40, 17, 30, 59, 3, 50, 15, 46, 20, 33, 62, 8, 25, 41, 57, 19, 36, 65, 6, 52, 39, 11, 24, 54, 28, 49, 60, 7, 37, 32, 63, 21, 56, 13, 44, 51, 35, 9, 29, 58, 42, 16, 53, 47, 26, 61, 43, 0], 'cur_cost': 127850.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [9, 27, 40, 6, 38, 12, 21, 30, 58, 60, 54, 47, 5, 32, 1, 42, 18, 28, 53, 63, 61, 3, 48, 57, 33, 49, 36, 23, 16, 51, 41, 39, 2, 55, 64, 25, 17, 43, 8, 29, 35, 10, 44, 37, 4, 20, 15, 45], 'cur_cost': 76100.0}}]
2025-06-22 16:54:37,529 - __main__ - INFO - 进化阶段完成
2025-06-22 16:54:37,529 - __main__ - INFO - 开始评估阶段
2025-06-22 16:54:37,529 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 16:54:37,530 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 16:54:37,530 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 0/5
- Population changes:
  * Cost: min 10053.0->15719.0, mean 77442.4->79497.0
  * Diversity: 0.7369230769230769->0.8713550600343053
  * Elite solutions: 3->1

## Strategy Performance:
- Explore: 5 individuals, success rate 20.0%, avg improvement -2926.0
- Exploit: 5 individuals, success rate 40.0%, avg improvement -1183.2
- Overall improvement: -5666.0

## Historical Trends:
No historical data available

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 16:54:45,312 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage - Initial Search & Refinement",
    "features": {
      "description": "The iteration is in the initial phase of evolution. Cost is increasing, suggesting a possible early stage exploration or a difficult landscape. Diversity has improved, indicating the population is spreading out. The number of elite solutions is decreasing which requires attention.",
      "population_dynamics": "Cost is increasing, diversity is improving, elite solutions are decreasing. This indicates a possible exploration phase, or that the landscape is rugged, or there's a detrimental search bias",
      "key_considerations": "Need to manage the increasing cost and the decreasing number of elite solutions while maintaining the improved diversity."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Poor. Success rate of 20% with an average worsening of -2926.0 indicates exploration is not finding useful areas of the search space.",
      "assessment": "Ineffective. This is making cost significantly worse."
    },
    "exploit": {
      "performance": "Acceptable. Success rate of 40% with a -1183.2 average worsening suggests some exploitation attempts found moderately better solutions, but are still detrimental.",
      "assessment": "Ineffective. This is making cost worse, although less dramatically than explore."
    },
    "overall_improvement": "Negative. Overall improvement of -5666.0 highlights the need to re-evaluate the strategy."
  },
  "balance_state": {
    "assessment": "Unbalanced. Neither exploration or exploitation is producing positive results, indicating a need for strategy refinement. The current balance has more of a detrimental effect that is likely not balanced.",
    "adjustment_needs": "The exploration strategy needs to be improved. Both are doing poorly, thus strategy re-evaluation is needed. The proportion of exploration attempts might be excessively high without effective results."
  },
  "recommendations": {
    "general": "The current approach appears to be inefficient. The increase in cost, the decreasing elite solutions, and the negative overall improvement indicate the need for significant adjustments.",
    "specific": [
      "**Revise Exploration Strategy:** Carefully examine the exploration strategy. Investigate its mechanisms and parameter settings, as it is failing to find better areas of the search space. Consider reducing exploration efforts until a more fruitful direction has been identified.",
      "**Review Exploitation Strategy:** Review the exploitation strategy. It is also detrimental, despite less so than explore. Examine the mutation or crossover parameters and whether they fit the landscape or the data at hand. ",
      "**Parameter Tuning:** Carefully check and tune the parameters associated with explore and exploit, making sure they're sensible for the current state of the run.",
      "**Increase Population Size (Optional):** Consider increasing the population size in the next iteration to facilitate more search if the resources are available.",
      "**Consider a different Search Paradigm:** Given the early stage and failure, explore and consider using a different evolutionary algorithm altogether, or try a hybrid approach."
    ]
  }
}
```
2025-06-22 16:54:45,331 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 16:54:45,332 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage - Initial Search & Refinement",
    "features": {
      "description": "The iteration is in the initial phase of evolution. Cost is increasing, suggesting a possible early stage exploration or a difficult landscape. Diversity has improved, indicating the population is spreading out. The number of elite solutions is decreasing which requires attention.",
      "population_dynamics": "Cost is increasing, diversity is improving, elite solutions are decreasing. This indicates a possible exploration phase, or that the landscape is rugged, or there's a detrimental search bias",
      "key_considerations": "Need to manage the increasing cost and the decreasing number of elite solutions while maintaining the improved diversity."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Poor. Success rate of 20% with an average worsening of -2926.0 indicates exploration is not finding useful areas of the search space.",
      "assessment": "Ineffective. This is making cost significantly worse."
    },
    "exploit": {
      "performance": "Acceptable. Success rate of 40% with a -1183.2 average worsening suggests some exploitation attempts found moderately better solutions, but are still detrimental.",
      "assessment": "Ineffective. This is making cost worse, although less dramatically than explore."
    },
    "overall_improvement": "Negative. Overall improvement of -5666.0 highlights the need to re-evaluate the strategy."
  },
  "balance_state": {
    "assessment": "Unbalanced. Neither exploration or exploitation is producing positive results, indicating a need for strategy refinement. The current balance has more of a detrimental effect that is likely not balanced.",
    "adjustment_needs": "The exploration strategy needs to be improved. Both are doing poorly, thus strategy re-evaluation is needed. The proportion of exploration attempts might be excessively high without effective results."
  },
  "recommendations": {
    "general": "The current approach appears to be inefficient. The increase in cost, the decreasing elite solutions, and the negative overall improvement indicate the need for significant adjustments.",
    "specific": [
      "**Revise Exploration Strategy:** Carefully examine the exploration strategy. Investigate its mechanisms and parameter settings, as it is failing to find better areas of the search space. Consider reducing exploration efforts until a more fruitful direction has been identified.",
      "**Review Exploitation Strategy:** Review the exploitation strategy. It is also detrimental, despite less so than explore. Examine the mutation or crossover parameters and whether they fit the landscape or the data at hand. ",
      "**Parameter Tuning:** Carefully check and tune the parameters associated with explore and exploit, making sure they're sensible for the current state of the run.",
      "**Increase Population Size (Optional):** Consider increasing the population size in the next iteration to facilitate more search if the resources are available.",
      "**Consider a different Search Paradigm:** Given the early stage and failure, explore and consider using a different evolutionary algorithm altogether, or try a hybrid approach."
    ]
  }
}
```
2025-06-22 16:54:45,332 - __main__ - INFO - 评估阶段完成
2025-06-22 16:54:45,333 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage - Initial Search & Refinement",
    "features": {
      "description": "The iteration is in the initial phase of evolution. Cost is increasing, suggesting a possible early stage exploration or a difficult landscape. Diversity has improved, indicating the population is spreading out. The number of elite solutions is decreasing which requires attention.",
      "population_dynamics": "Cost is increasing, diversity is improving, elite solutions are decreasing. This indicates a possible exploration phase, or that the landscape is rugged, or there's a detrimental search bias",
      "key_considerations": "Need to manage the increasing cost and the decreasing number of elite solutions while maintaining the improved diversity."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Poor. Success rate of 20% with an average worsening of -2926.0 indicates exploration is not finding useful areas of the search space.",
      "assessment": "Ineffective. This is making cost significantly worse."
    },
    "exploit": {
      "performance": "Acceptable. Success rate of 40% with a -1183.2 average worsening suggests some exploitation attempts found moderately better solutions, but are still detrimental.",
      "assessment": "Ineffective. This is making cost worse, although less dramatically than explore."
    },
    "overall_improvement": "Negative. Overall improvement of -5666.0 highlights the need to re-evaluate the strategy."
  },
  "balance_state": {
    "assessment": "Unbalanced. Neither exploration or exploitation is producing positive results, indicating a need for strategy refinement. The current balance has more of a detrimental effect that is likely not balanced.",
    "adjustment_needs": "The exploration strategy needs to be improved. Both are doing poorly, thus strategy re-evaluation is needed. The proportion of exploration attempts might be excessively high without effective results."
  },
  "recommendations": {
    "general": "The current approach appears to be inefficient. The increase in cost, the decreasing elite solutions, and the negative overall improvement indicate the need for significant adjustments.",
    "specific": [
      "**Revise Exploration Strategy:** Carefully examine the exploration strategy. Investigate its mechanisms and parameter settings, as it is failing to find better areas of the search space. Consider reducing exploration efforts until a more fruitful direction has been identified.",
      "**Review Exploitation Strategy:** Review the exploitation strategy. It is also detrimental, despite less so than explore. Examine the mutation or crossover parameters and whether they fit the landscape or the data at hand. ",
      "**Parameter Tuning:** Carefully check and tune the parameters associated with explore and exploit, making sure they're sensible for the current state of the run.",
      "**Increase Population Size (Optional):** Consider increasing the population size in the next iteration to facilitate more search if the resources are available.",
      "**Consider a different Search Paradigm:** Given the early stage and failure, explore and consider using a different evolutionary algorithm altogether, or try a hybrid approach."
    ]
  }
}
```
2025-06-22 16:54:45,333 - __main__ - INFO - 当前最佳适应度: 15719.0
2025-06-22 16:54:45,335 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-22 16:54:45,336 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-22 16:54:45,336 - __main__ - INFO - 开始分析阶段
2025-06-22 16:54:45,336 - StatsExpert - INFO - 开始统计分析
2025-06-22 16:54:45,344 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 15719.0, 'max': 127850.0, 'mean': 79497.0, 'std': 40388.27346396476}, 'diversity': 0.9892255892255892, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 16:54:45,344 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 15719.0, 'max': 127850.0, 'mean': 79497.0, 'std': 40388.27346396476}, 'diversity_level': 0.9892255892255892, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 16:54:45,344 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 16:54:45,344 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 16:54:45,344 - PathExpert - INFO - 开始路径结构分析
