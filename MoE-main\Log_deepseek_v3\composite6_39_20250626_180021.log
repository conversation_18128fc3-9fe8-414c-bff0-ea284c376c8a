2025-06-26 18:00:21,883 - __main__ - INFO - composite6_39 开始进化第 1 代
2025-06-26 18:00:21,883 - __main__ - INFO - 开始分析阶段
2025-06-26 18:00:21,883 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:00:21,887 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 23968.0, 'max': 149862.0, 'mean': 105920.5, 'std': 52561.10241471349}, 'diversity': 0.8871794871794872, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:00:21,887 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 23968.0, 'max': 149862.0, 'mean': 105920.5, 'std': 52561.10241471349}, 'diversity_level': 0.8871794871794872, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[3156, 1356], [3163, 1390], [3179, 1354], [3088, 1415], [3102, 1355], [5793, 2546], [5722, 2537], [5782, 2504], [5772, 2546], [5772, 2556], [7573, 838], [7596, 888], [7564, 889], [7543, 806], [7601, 818], [2523, 6168], [2536, 6136], [2509, 6132], [2562, 6118], [2569, 6184], [4199, 3605], [4160, 3676], [4171, 3694], [4189, 3693], [4214, 3700], [4165, 3649], [1085, 953], [1060, 995], [1017, 984], [991, 941], [1019, 1006], [1065, 979], [6688, 4970], [6614, 5024], [6615, 5042], [6684, 5006], [6654, 5018], [6682, 5047], [6659, 5001]], 'distance_matrix': array([[   0.,   35.,   23., ..., 5064., 5105., 5055.],
       [  35.,    0.,   39., ..., 5035., 5075., 5026.],
       [  23.,   39.,    0., ..., 5050., 5090., 5041.],
       ...,
       [5064., 5035., 5050., ...,    0.,   40.,   18.],
       [5105., 5075., 5090., ...,   40.,    0.,   51.],
       [5055., 5026., 5041., ...,   18.,   51.,    0.]])}
2025-06-26 18:00:21,892 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:00:21,892 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:00:21,893 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:00:21,895 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:00:21,895 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [{'edge': (0, 1), 'frequency': 0.5, 'avg_cost': 35.0}, {'edge': (13, 14), 'frequency': 0.5, 'avg_cost': 59.0}], 'common_subpaths': [{'subpath': (24, 1, 0), 'frequency': 0.4}, {'subpath': (36, 38, 35), 'frequency': 0.3}, {'subpath': (38, 35, 32), 'frequency': 0.3}, {'subpath': (6, 20, 25), 'frequency': 0.3}, {'subpath': (20, 25, 21), 'frequency': 0.3}, {'subpath': (25, 21, 22), 'frequency': 0.3}, {'subpath': (21, 22, 23), 'frequency': 0.3}, {'subpath': (22, 23, 24), 'frequency': 0.3}, {'subpath': (23, 24, 1), 'frequency': 0.3}, {'subpath': (1, 0, 2), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(36, 38)', 'frequency': 0.4}, {'edge': '(35, 38)', 'frequency': 0.4}, {'edge': '(8, 9)', 'frequency': 0.4}, {'edge': '(22, 23)', 'frequency': 0.4}, {'edge': '(1, 24)', 'frequency': 0.4}, {'edge': '(0, 1)', 'frequency': 0.5}, {'edge': '(3, 4)', 'frequency': 0.4}, {'edge': '(27, 30)', 'frequency': 0.4}, {'edge': '(28, 30)', 'frequency': 0.4}, {'edge': '(13, 14)', 'frequency': 0.5}, {'edge': '(12, 14)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(36, 37)', 'frequency': 0.2}, {'edge': '(32, 35)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(5, 8)', 'frequency': 0.3}, {'edge': '(5, 7)', 'frequency': 0.3}, {'edge': '(6, 7)', 'frequency': 0.2}, {'edge': '(6, 20)', 'frequency': 0.3}, {'edge': '(20, 25)', 'frequency': 0.3}, {'edge': '(21, 25)', 'frequency': 0.3}, {'edge': '(21, 22)', 'frequency': 0.3}, {'edge': '(23, 24)', 'frequency': 0.3}, {'edge': '(0, 2)', 'frequency': 0.3}, {'edge': '(2, 4)', 'frequency': 0.3}, {'edge': '(3, 26)', 'frequency': 0.3}, {'edge': '(26, 31)', 'frequency': 0.3}, {'edge': '(27, 31)', 'frequency': 0.3}, {'edge': '(28, 29)', 'frequency': 0.3}, {'edge': '(17, 29)', 'frequency': 0.3}, {'edge': '(16, 17)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(15, 18)', 'frequency': 0.3}, {'edge': '(15, 19)', 'frequency': 0.3}, {'edge': '(12, 19)', 'frequency': 0.2}, {'edge': '(11, 12)', 'frequency': 0.3}, {'edge': '(10, 11)', 'frequency': 0.3}, {'edge': '(10, 14)', 'frequency': 0.3}, {'edge': '(10, 12)', 'frequency': 0.2}, {'edge': '(32, 37)', 'frequency': 0.3}, {'edge': '(34, 37)', 'frequency': 0.2}, {'edge': '(9, 33)', 'frequency': 0.3}, {'edge': '(29, 35)', 'frequency': 0.2}, {'edge': '(16, 31)', 'frequency': 0.2}, {'edge': '(34, 36)', 'frequency': 0.2}, {'edge': '(3, 25)', 'frequency': 0.2}, {'edge': '(3, 11)', 'frequency': 0.2}, {'edge': '(11, 19)', 'frequency': 0.2}, {'edge': '(14, 19)', 'frequency': 0.2}, {'edge': '(4, 7)', 'frequency': 0.2}, {'edge': '(7, 22)', 'frequency': 0.2}, {'edge': '(17, 23)', 'frequency': 0.2}, {'edge': '(15, 28)', 'frequency': 0.2}, {'edge': '(28, 32)', 'frequency': 0.2}, {'edge': '(22, 37)', 'frequency': 0.2}, {'edge': '(7, 21)', 'frequency': 0.2}, {'edge': '(9, 20)', 'frequency': 0.2}, {'edge': '(10, 34)', 'frequency': 0.2}, {'edge': '(6, 31)', 'frequency': 0.3}, {'edge': '(23, 29)', 'frequency': 0.2}, {'edge': '(23, 38)', 'frequency': 0.2}, {'edge': '(5, 30)', 'frequency': 0.2}, {'edge': '(15, 35)', 'frequency': 0.2}, {'edge': '(24, 27)', 'frequency': 0.2}, {'edge': '(7, 38)', 'frequency': 0.2}, {'edge': '(16, 25)', 'frequency': 0.2}, {'edge': '(26, 36)', 'frequency': 0.2}, {'edge': '(8, 27)', 'frequency': 0.2}, {'edge': '(8, 18)', 'frequency': 0.2}, {'edge': '(4, 34)', 'frequency': 0.2}, {'edge': '(2, 28)', 'frequency': 0.2}, {'edge': '(0, 9)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.2}, {'edge': '(12, 32)', 'frequency': 0.2}, {'edge': '(25, 37)', 'frequency': 0.3}, {'edge': '(16, 29)', 'frequency': 0.2}, {'edge': '(2, 26)', 'frequency': 0.2}, {'edge': '(17, 21)', 'frequency': 0.2}, {'edge': '(22, 28)', 'frequency': 0.2}, {'edge': '(4, 18)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [31, 16, 13, 18, 27, 14], 'cost': 31840.0, 'size': 6}, {'region': [30, 15, 28, 32, 0], 'cost': 22760.0, 'size': 5}, {'region': [11, 19, 14, 15], 'cost': 22034.0, 'size': 4}, {'region': [31, 7, 30, 35, 0], 'cost': 21961.0, 'size': 5}, {'region': [14, 31, 34, 27], 'cost': 20289.0, 'size': 4}]}
2025-06-26 18:00:21,896 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:00:21,896 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:00:21,896 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:00:21,896 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 18:00:21,897 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 18:00:21,897 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:00:21,897 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:00:21,899 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:00:21,899 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 23968.0, mean 105920.5, max 149862.0, std 52561.10241471349
- diversity: 0.8871794871794872
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [13029.0, 48793.0, 25626.0] (Δ 11583.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: [{'edge': (0, 1), 'frequency': 0.5, 'avg_cost': 35.0}, {'edge': (13, 14), 'frequency': 0.5, 'avg_cost': 59.0}]
- common_subpaths_sample: [{'subpath': (24, 1, 0), 'frequency': 0.4}, {'subpath': (36, 38, 35), 'frequency': 0.3}, {'subpath': (38, 35, 32), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [31, 16, 13, 18, 27, 14], 'cost': 31840.0, 'size': 6}, {'region': [30, 15, 28, 32, 0], 'cost': 22760.0, 'size': 5}, {'region': [11, 19, 14, 15], 'cost': 22034.0, 'size': 4}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [991.0, 806.0, 7601.0, 6184.0]
- centroid/spread: [4436.821, 3013.308] | rms 2915.063
- density_grid(3x3): [[11, 0, 10], [0, 6, 0], [5, 0, 7]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 2516.0, 4182.0, 4954.0, 7376.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [4, 4, 4, 6, 4, 7, 5, 4]
- nn_median_dist: 27.295

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:00:21,900 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:00:50,703 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with Q1-Q3 spread of 2516.0-4954.0)",
    "modality": "Potentially multimodal (high diversity 0.887, no clustering detected yet)",
    "deceptiveness": "Possible (long_edge_ratio 0.25 suggests some misleading long edges)"
  },
  "population_state": {
    "coverage": "Irregular (density_grid shows sparse coverage with clusters in corners and center)",
    "convergence": "Early stage (0.0 convergence, recent best costs show improvement Δ11583.5/iter)"
  },
  "difficult_regions": [
    {"region": [31, 16, 13, 18, 27, 14], "reason": "High cost (31840) + sparse density in NW grid cell"},
    {"region": [30, 15, 28, 32, 0], "reason": "High cost (22760) + long edges to NE quadrant"},
    {"region": [11, 19, 14, 15], "reason": "High cost (22034) + directional bias in histogram"}
  ],
  "opportunity_regions": [
    {"potential_nodes": [0,1,24], "reason": "High-quality edge (0,1) + frequent subpath (24,1,0) in dense SE cell"},
    {"potential_nodes": [35,36,38], "reason": "Recurrent subpaths in medium-density SW cell"}
  ],
  "evolution_phase": "Exploration (high diversity, no elites, improving costs)",
  "evolution_direction": {
    "operators": [
      "Edge recombination (exploit high-quality edges like (0,1))",
      "Subpath preservation (for (24,1,0) and (36,38,35) patterns)",
      "Density-guided mutation (target under-explored grid cells)",
      "Directional bias adjustment (rebalance N/NE sectors)"
    ],
    "focus": "Balance exploitation of promising edges with coverage of sparse regions"
  }
}
```
2025-06-26 18:00:50,703 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:00:50,703 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with Q1-Q3 spread of 2516.0-4954.0)', 'modality': 'Potentially multimodal (high diversity 0.887, no clustering detected yet)', 'deceptiveness': 'Possible (long_edge_ratio 0.25 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Irregular (density_grid shows sparse coverage with clusters in corners and center)', 'convergence': 'Early stage (0.0 convergence, recent best costs show improvement Δ11583.5/iter)'}, 'difficult_regions': [{'region': [31, 16, 13, 18, 27, 14], 'reason': 'High cost (31840) + sparse density in NW grid cell'}, {'region': [30, 15, 28, 32, 0], 'reason': 'High cost (22760) + long edges to NE quadrant'}, {'region': [11, 19, 14, 15], 'reason': 'High cost (22034) + directional bias in histogram'}], 'opportunity_regions': [{'potential_nodes': [0, 1, 24], 'reason': 'High-quality edge (0,1) + frequent subpath (24,1,0) in dense SE cell'}, {'potential_nodes': [35, 36, 38], 'reason': 'Recurrent subpaths in medium-density SW cell'}], 'evolution_phase': 'Exploration (high diversity, no elites, improving costs)', 'evolution_direction': {'operators': ['Edge recombination (exploit high-quality edges like (0,1))', 'Subpath preservation (for (24,1,0) and (36,38,35) patterns)', 'Density-guided mutation (target under-explored grid cells)', 'Directional bias adjustment (rebalance N/NE sectors)'], 'focus': 'Balance exploitation of promising edges with coverage of sparse regions'}}
2025-06-26 18:00:50,709 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:00:50,709 - __main__ - INFO - 分析阶段完成
2025-06-26 18:00:50,709 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with Q1-Q3 spread of 2516.0-4954.0)', 'modality': 'Potentially multimodal (high diversity 0.887, no clustering detected yet)', 'deceptiveness': 'Possible (long_edge_ratio 0.25 suggests some misleading long edges)'}, 'population_state': {'coverage': 'Irregular (density_grid shows sparse coverage with clusters in corners and center)', 'convergence': 'Early stage (0.0 convergence, recent best costs show improvement Δ11583.5/iter)'}, 'difficult_regions': [{'region': [31, 16, 13, 18, 27, 14], 'reason': 'High cost (31840) + sparse density in NW grid cell'}, {'region': [30, 15, 28, 32, 0], 'reason': 'High cost (22760) + long edges to NE quadrant'}, {'region': [11, 19, 14, 15], 'reason': 'High cost (22034) + directional bias in histogram'}], 'opportunity_regions': [{'potential_nodes': [0, 1, 24], 'reason': 'High-quality edge (0,1) + frequent subpath (24,1,0) in dense SE cell'}, {'potential_nodes': [35, 36, 38], 'reason': 'Recurrent subpaths in medium-density SW cell'}], 'evolution_phase': 'Exploration (high diversity, no elites, improving costs)', 'evolution_direction': {'operators': ['Edge recombination (exploit high-quality edges like (0,1))', 'Subpath preservation (for (24,1,0) and (36,38,35) patterns)', 'Density-guided mutation (target under-explored grid cells)', 'Directional bias adjustment (rebalance N/NE sectors)'], 'focus': 'Balance exploitation of promising edges with coverage of sparse regions'}}
2025-06-26 18:00:50,709 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:00:50,709 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:00:50,709 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 23968.0
  • mean_cost: 105920.5
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with Q1-Q3 spread of 2516.0-4954.0), modality Potentially multimodal (high diversity 0.887, no clustering detected yet), deceptiveness Possible (long_edge_ratio 0.25 suggests some misleading long edges)
- Evolution phase: Exploration (high diversity, no elites, improving costs)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [31, 16, , {'region': [30, 15, ...
- Opportunity regions (# 2): {'potential_nodes': , {'potential_nodes': 
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:00:50,709 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:00:50,709 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:01:01,327 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High initial diversity and no elites favor exploration. Top 60% explore to diversify; bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 18:01:01,327 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:01:01,333 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:01:01,333 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High initial diversity and no elites favor exploration. Top 60% explore to diversify; bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 18:01:01,333 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:01:01,333 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:01:01,333 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High initial diversity and no elites favor exploration. Top 60% explore to diversify; bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 18:01:01,333 - __main__ - INFO - 开始进化阶段
2025-06-26 18:01:01,333 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:01:01,333 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:01:01,333 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 27325.0 (rank 20.0%)
- Population diversity: 0.6631578947368421
- Evolution phase: Exploration (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_nod
- Difficult regions to avoid (sample): {'region': [31,, {'region': [30,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:01:01,335 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:01:01,335 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:01:12,644 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Balanced spiral pattern to explore sparse areas"
}
```
2025-06-26 18:01:12,648 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:01:12,648 - ExplorationExpert - INFO - 探索路径生成完成，成本: 169603.0, 路径: [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35]
2025-06-26 18:01:12,648 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}
2025-06-26 18:01:12,648 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:01:12,649 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:01:12,649 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 23968.0 (rank 0.0%)
- Population diversity: 0.7315789473684211
- Evolution phase: Exploration (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_nod
- Difficult regions to avoid (sample): {'region': [31,, {'region': [30,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:01:12,649 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:01:12,649 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:01:24,161 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 17, 23, 8, 29, 14, 35, 2, 19, 26, 7, 32, 11, 38, 4, 21, 9, 33, 16, 28, 1, 24, 13, 36, 3, 20, 10, 34, 15, 30, 6, 22, 18, 31, 25, 37, 27],
  "targeted_regions": "potential_nod and adjacent sparse cells",
  "strategy_comment": "Balance opportunity regions with unexplored adjacency"
}
```
2025-06-26 18:01:24,161 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:01:24,161 - ExplorationExpert - INFO - 探索路径生成完成，成本: 156163.0, 路径: [0, 12, 5, 17, 23, 8, 29, 14, 35, 2, 19, 26, 7, 32, 11, 38, 4, 21, 9, 33, 16, 28, 1, 24, 13, 36, 3, 20, 10, 34, 15, 30, 6, 22, 18, 31, 25, 37, 27]
2025-06-26 18:01:24,161 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 5, 17, 23, 8, 29, 14, 35, 2, 19, 26, 7, 32, 11, 38, 4, 21, 9, 33, 16, 28, 1, 24, 13, 36, 3, 20, 10, 34, 15, 30, 6, 22, 18, 31, 25, 37, 27], 'cur_cost': 156163.0}
2025-06-26 18:01:24,161 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:01:24,161 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:01:24,161 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 27297.0 (rank 0.0%)
- Population diversity: 0.7868421052631579
- Evolution phase: Exploration (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_nod
- Difficult regions to avoid (sample): {'region': [31,, {'region': [30,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:01:24,161 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:01:24,161 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:01:35,653 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 17, 23, 8, 29, 34, 2, 14, 21, 7, 36, 11, 25, 38, 3, 16, 27, 9, 32, 1, 18, 30, 6, 22, 13, 35, 4, 19, 28, 10, 33, 15, 24, 37, 20, 31, 26],
  "targeted_regions": "potential_nod and adjacent sparse cells",
  "strategy_comment": "Balanced exploration of opportunity regions while avoiding high-cost clusters"
}
```
2025-06-26 18:01:35,653 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:01:35,653 - ExplorationExpert - INFO - 探索路径生成完成，成本: 157602.0, 路径: [0, 12, 5, 17, 23, 8, 29, 34, 2, 14, 21, 7, 36, 11, 25, 38, 3, 16, 27, 9, 32, 1, 18, 30, 6, 22, 13, 35, 4, 19, 28, 10, 33, 15, 24, 37, 20, 31, 26]
2025-06-26 18:01:35,653 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 5, 17, 23, 8, 29, 34, 2, 14, 21, 7, 36, 11, 25, 38, 3, 16, 27, 9, 32, 1, 18, 30, 6, 22, 13, 35, 4, 19, 28, 10, 33, 15, 24, 37, 20, 31, 26], 'cur_cost': 157602.0}
2025-06-26 18:01:35,653 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:01:35,653 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:01:35,658 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 134689.0 (rank 10.0%)
- Population diversity: 0.781578947368421
- Evolution phase: Exploration (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_nod
- Difficult regions to avoid (sample): {'region': [31,, {'region': [30,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:01:35,658 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:01:35,658 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:01:48,318 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 17, 23, 8, 29, 3, 14, 21, 7, 36, 11, 25, 2, 19, 34, 9, 27, 15, 32, 6, 22, 38, 1, 13, 26, 4, 18, 30, 10, 24, 35, 16, 31, 20, 37, 28, 33],
  "targeted_regions": "Adjacent to opportunity nodes 5,17,23, sparse cells near 29,36",
  "strategy_comment": "Balanced exploration of low-density regions while avoiding high-cost clusters"
}
```
2025-06-26 18:01:48,318 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:01:48,318 - ExplorationExpert - INFO - 探索路径生成完成，成本: 159786.0, 路径: [0, 12, 5, 17, 23, 8, 29, 3, 14, 21, 7, 36, 11, 25, 2, 19, 34, 9, 27, 15, 32, 6, 22, 38, 1, 13, 26, 4, 18, 30, 10, 24, 35, 16, 31, 20, 37, 28, 33]
2025-06-26 18:01:48,318 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 5, 17, 23, 8, 29, 3, 14, 21, 7, 36, 11, 25, 2, 19, 34, 9, 27, 15, 32, 6, 22, 38, 1, 13, 26, 4, 18, 30, 10, 24, 35, 16, 31, 20, 37, 28, 33], 'cur_cost': 159786.0}
2025-06-26 18:01:48,318 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:01:48,318 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:01:48,322 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 149862.0 (rank 50.0%)
- Population diversity: 0.75
- Evolution phase: Exploration (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_nod
- Difficult regions to avoid (sample): {'region': [31,, {'region': [30,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:01:48,322 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:01:48,322 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:01:59,514 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 10, 15, 20, 25, 30, 35, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 12, 17, 22, 27, 32, 37, 3, 8, 13, 18, 23, 28, 33, 38, 4, 9, 14, 19, 24, 29, 34],
  "targeted_regions": "opportunity regions and sparse cells",
  "strategy_comment": "Balanced exploration of low-density regions"
}
```
2025-06-26 18:01:59,514 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:01:59,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 159077.0, 路径: [0, 5, 10, 15, 20, 25, 30, 35, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 12, 17, 22, 27, 32, 37, 3, 8, 13, 18, 23, 28, 33, 38, 4, 9, 14, 19, 24, 29, 34]
2025-06-26 18:01:59,514 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 12, 17, 22, 27, 32, 37, 3, 8, 13, 18, 23, 28, 33, 38, 4, 9, 14, 19, 24, 29, 34], 'cur_cost': 159077.0}
2025-06-26 18:01:59,521 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:01:59,521 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:01:59,521 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 127422.0 (rank 0.0%)
- Population diversity: 0.7605263157894737
- Evolution phase: Exploration (high diversity, no elites, improving costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): {'potential_nod, {'potential_nod
- Difficult regions to avoid (sample): {'region': [31,, {'region': [30,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:01:59,522 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:01:59,522 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:02:10,841 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 18, 22, 25, 29, 33, 36, 38, 35, 32, 28, 24, 21, 17, 14, 10, 7, 3, 1, 4, 8, 11, 15, 19, 23, 26, 30, 27, 31, 34, 37, 20, 16, 13, 9, 6, 2],
  "targeted_regions": "Adjacent low-density nodes near opportunity regions",
  "strategy_comment": "Balanced exploration of sparse cells while avoiding high-cost clusters"
}
```
2025-06-26 18:02:10,843 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:02:10,843 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99331.0, 路径: [0, 5, 12, 18, 22, 25, 29, 33, 36, 38, 35, 32, 28, 24, 21, 17, 14, 10, 7, 3, 1, 4, 8, 11, 15, 19, 23, 26, 30, 27, 31, 34, 37, 20, 16, 13, 9, 6, 2]
2025-06-26 18:02:10,843 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 5, 12, 18, 22, 25, 29, 33, 36, 38, 35, 32, 28, 24, 21, 17, 14, 10, 7, 3, 1, 4, 8, 11, 15, 19, 23, 26, 30, 27, 31, 34, 37, 20, 16, 13, 9, 6, 2], 'cur_cost': 99331.0}
2025-06-26 18:02:10,843 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 18:02:10,843 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:02:10,843 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:02:10,843 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 134735.0
2025-06-26 18:02:11,345 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:02:11,345 - ExploitationExpert - INFO - res_population_costs: [23794, 23782, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763]
2025-06-26 18:02:11,345 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-06-26 18:02:11,351 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:02:11,353 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 14, 35, 2, 19, 26, 7, 32, 11, 38, 4, 21, 9, 33, 16, 28, 1, 24, 13, 36, 3, 20, 10, 34, 15, 30, 6, 22, 18, 31, 25, 37, 27], 'cur_cost': 156163.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 34, 2, 14, 21, 7, 36, 11, 25, 38, 3, 16, 27, 9, 32, 1, 18, 30, 6, 22, 13, 35, 4, 19, 28, 10, 33, 15, 24, 37, 20, 31, 26], 'cur_cost': 157602.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 3, 14, 21, 7, 36, 11, 25, 2, 19, 34, 9, 27, 15, 32, 6, 22, 38, 1, 13, 26, 4, 18, 30, 10, 24, 35, 16, 31, 20, 37, 28, 33], 'cur_cost': 159786.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 12, 17, 22, 27, 32, 37, 3, 8, 13, 18, 23, 28, 33, 38, 4, 9, 14, 19, 24, 29, 34], 'cur_cost': 159077.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 36, 38, 35, 32, 28, 24, 21, 17, 14, 10, 7, 3, 1, 4, 8, 11, 15, 19, 23, 26, 30, 27, 31, 34, 37, 20, 16, 13, 9, 6, 2], 'cur_cost': 99331.0}, {'tour': array([14, 13, 32, 23, 30, 10,  2,  3, 27, 31, 24, 11, 34, 16, 19,  1, 17,
        6,  9, 26, 21, 33, 37,  5, 38,  0, 25, 22, 29, 36,  8, 28,  7, 20,
       18, 12, 35, 15,  4]), 'cur_cost': 134735.0}, {'tour': [12, 14, 11, 3, 19, 10, 6, 23, 29, 16, 2, 26, 25, 17, 21, 38, 36, 32, 15, 28, 22, 33, 37, 1, 8, 18, 4, 34, 20, 31, 7, 30, 35, 0, 9, 27, 24, 5, 13], 'cur_cost': 135511.0}, {'tour': [17, 24, 0, 23, 30, 34, 1, 16, 29, 35, 14, 12, 32, 9, 20, 4, 26, 2, 28, 22, 7, 3, 6, 25, 37, 15, 5, 36, 8, 27, 11, 10, 33, 31, 19, 13, 38, 18, 21], 'cur_cost': 144300.0}, {'tour': [35, 15, 17, 23, 22, 29, 3, 33, 9, 0, 30, 37, 25, 12, 14, 19, 11, 13, 20, 27, 10, 16, 8, 34, 36, 31, 6, 26, 28, 32, 1, 38, 7, 21, 24, 4, 18, 2, 5], 'cur_cost': 141377.0}]
2025-06-26 18:02:11,354 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:02:11,354 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 199, 'skip_rate': 0.04522613065326633, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 190, 'cache_hits': 172, 'similarity_calculations': 3461, 'cache_hit_rate': 0.04969661947414042, 'cache_size': 3289}}
2025-06-26 18:02:11,354 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 18:02:11,354 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:02:11,355 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:02:11,355 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:02:11,355 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 149467.0
2025-06-26 18:02:11,857 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:02:11,857 - ExploitationExpert - INFO - res_population_costs: [23794, 23782, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763]
2025-06-26 18:02:11,857 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-06-26 18:02:11,865 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:02:11,865 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 14, 35, 2, 19, 26, 7, 32, 11, 38, 4, 21, 9, 33, 16, 28, 1, 24, 13, 36, 3, 20, 10, 34, 15, 30, 6, 22, 18, 31, 25, 37, 27], 'cur_cost': 156163.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 34, 2, 14, 21, 7, 36, 11, 25, 38, 3, 16, 27, 9, 32, 1, 18, 30, 6, 22, 13, 35, 4, 19, 28, 10, 33, 15, 24, 37, 20, 31, 26], 'cur_cost': 157602.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 3, 14, 21, 7, 36, 11, 25, 2, 19, 34, 9, 27, 15, 32, 6, 22, 38, 1, 13, 26, 4, 18, 30, 10, 24, 35, 16, 31, 20, 37, 28, 33], 'cur_cost': 159786.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 12, 17, 22, 27, 32, 37, 3, 8, 13, 18, 23, 28, 33, 38, 4, 9, 14, 19, 24, 29, 34], 'cur_cost': 159077.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 36, 38, 35, 32, 28, 24, 21, 17, 14, 10, 7, 3, 1, 4, 8, 11, 15, 19, 23, 26, 30, 27, 31, 34, 37, 20, 16, 13, 9, 6, 2], 'cur_cost': 99331.0}, {'tour': array([14, 13, 32, 23, 30, 10,  2,  3, 27, 31, 24, 11, 34, 16, 19,  1, 17,
        6,  9, 26, 21, 33, 37,  5, 38,  0, 25, 22, 29, 36,  8, 28,  7, 20,
       18, 12, 35, 15,  4]), 'cur_cost': 134735.0}, {'tour': array([ 4, 28, 15,  2,  9, 36, 16, 19, 21, 32, 29, 23, 10, 37, 24, 35, 33,
       27, 22,  6, 30, 12, 11, 31,  7, 26, 34,  0, 25, 18, 20,  3,  5, 14,
        1, 38,  8, 13, 17]), 'cur_cost': 149467.0}, {'tour': [17, 24, 0, 23, 30, 34, 1, 16, 29, 35, 14, 12, 32, 9, 20, 4, 26, 2, 28, 22, 7, 3, 6, 25, 37, 15, 5, 36, 8, 27, 11, 10, 33, 31, 19, 13, 38, 18, 21], 'cur_cost': 144300.0}, {'tour': [35, 15, 17, 23, 22, 29, 3, 33, 9, 0, 30, 37, 25, 12, 14, 19, 11, 13, 20, 27, 10, 16, 8, 34, 36, 31, 6, 26, 28, 32, 1, 38, 7, 21, 24, 4, 18, 2, 5], 'cur_cost': 141377.0}]
2025-06-26 18:02:11,868 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:02:11,868 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 200, 'skip_rate': 0.045, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 191, 'cache_hits': 172, 'similarity_calculations': 3462, 'cache_hit_rate': 0.04968226458694396, 'cache_size': 3290}}
2025-06-26 18:02:11,868 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:02:11,868 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:02:11,869 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:02:11,869 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:02:11,869 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 119877.0
2025-06-26 18:02:12,371 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:02:12,371 - ExploitationExpert - INFO - res_population_costs: [23794, 23782, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763]
2025-06-26 18:02:12,371 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-06-26 18:02:12,379 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:02:12,379 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 14, 35, 2, 19, 26, 7, 32, 11, 38, 4, 21, 9, 33, 16, 28, 1, 24, 13, 36, 3, 20, 10, 34, 15, 30, 6, 22, 18, 31, 25, 37, 27], 'cur_cost': 156163.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 34, 2, 14, 21, 7, 36, 11, 25, 38, 3, 16, 27, 9, 32, 1, 18, 30, 6, 22, 13, 35, 4, 19, 28, 10, 33, 15, 24, 37, 20, 31, 26], 'cur_cost': 157602.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 3, 14, 21, 7, 36, 11, 25, 2, 19, 34, 9, 27, 15, 32, 6, 22, 38, 1, 13, 26, 4, 18, 30, 10, 24, 35, 16, 31, 20, 37, 28, 33], 'cur_cost': 159786.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 12, 17, 22, 27, 32, 37, 3, 8, 13, 18, 23, 28, 33, 38, 4, 9, 14, 19, 24, 29, 34], 'cur_cost': 159077.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 36, 38, 35, 32, 28, 24, 21, 17, 14, 10, 7, 3, 1, 4, 8, 11, 15, 19, 23, 26, 30, 27, 31, 34, 37, 20, 16, 13, 9, 6, 2], 'cur_cost': 99331.0}, {'tour': array([14, 13, 32, 23, 30, 10,  2,  3, 27, 31, 24, 11, 34, 16, 19,  1, 17,
        6,  9, 26, 21, 33, 37,  5, 38,  0, 25, 22, 29, 36,  8, 28,  7, 20,
       18, 12, 35, 15,  4]), 'cur_cost': 134735.0}, {'tour': array([ 4, 28, 15,  2,  9, 36, 16, 19, 21, 32, 29, 23, 10, 37, 24, 35, 33,
       27, 22,  6, 30, 12, 11, 31,  7, 26, 34,  0, 25, 18, 20,  3,  5, 14,
        1, 38,  8, 13, 17]), 'cur_cost': 149467.0}, {'tour': array([29, 18, 17,  7, 22, 19, 11, 23, 20, 36,  9, 14, 10, 12, 38, 16,  2,
       25, 33, 27,  4,  5, 26, 21,  6,  8, 32, 37, 24, 15, 35,  3,  0, 30,
       13,  1, 34, 28, 31]), 'cur_cost': 119877.0}, {'tour': [35, 15, 17, 23, 22, 29, 3, 33, 9, 0, 30, 37, 25, 12, 14, 19, 11, 13, 20, 27, 10, 16, 8, 34, 36, 31, 6, 26, 28, 32, 1, 38, 7, 21, 24, 4, 18, 2, 5], 'cur_cost': 141377.0}]
2025-06-26 18:02:12,380 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:02:12,380 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 201, 'skip_rate': 0.04477611940298507, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 192, 'cache_hits': 172, 'similarity_calculations': 3464, 'cache_hit_rate': 0.049653579676674366, 'cache_size': 3292}}
2025-06-26 18:02:12,380 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:02:12,382 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:02:12,382 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:02:12,382 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:02:12,383 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 123377.0
2025-06-26 18:02:12,884 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:02:12,886 - ExploitationExpert - INFO - res_population_costs: [23794, 23782, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763]
2025-06-26 18:02:12,886 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64)]
2025-06-26 18:02:12,894 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:02:12,894 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 14, 35, 2, 19, 26, 7, 32, 11, 38, 4, 21, 9, 33, 16, 28, 1, 24, 13, 36, 3, 20, 10, 34, 15, 30, 6, 22, 18, 31, 25, 37, 27], 'cur_cost': 156163.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 34, 2, 14, 21, 7, 36, 11, 25, 38, 3, 16, 27, 9, 32, 1, 18, 30, 6, 22, 13, 35, 4, 19, 28, 10, 33, 15, 24, 37, 20, 31, 26], 'cur_cost': 157602.0}, {'tour': [0, 12, 5, 17, 23, 8, 29, 3, 14, 21, 7, 36, 11, 25, 2, 19, 34, 9, 27, 15, 32, 6, 22, 38, 1, 13, 26, 4, 18, 30, 10, 24, 35, 16, 31, 20, 37, 28, 33], 'cur_cost': 159786.0}, {'tour': [0, 5, 10, 15, 20, 25, 30, 35, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 12, 17, 22, 27, 32, 37, 3, 8, 13, 18, 23, 28, 33, 38, 4, 9, 14, 19, 24, 29, 34], 'cur_cost': 159077.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 36, 38, 35, 32, 28, 24, 21, 17, 14, 10, 7, 3, 1, 4, 8, 11, 15, 19, 23, 26, 30, 27, 31, 34, 37, 20, 16, 13, 9, 6, 2], 'cur_cost': 99331.0}, {'tour': array([14, 13, 32, 23, 30, 10,  2,  3, 27, 31, 24, 11, 34, 16, 19,  1, 17,
        6,  9, 26, 21, 33, 37,  5, 38,  0, 25, 22, 29, 36,  8, 28,  7, 20,
       18, 12, 35, 15,  4]), 'cur_cost': 134735.0}, {'tour': array([ 4, 28, 15,  2,  9, 36, 16, 19, 21, 32, 29, 23, 10, 37, 24, 35, 33,
       27, 22,  6, 30, 12, 11, 31,  7, 26, 34,  0, 25, 18, 20,  3,  5, 14,
        1, 38,  8, 13, 17]), 'cur_cost': 149467.0}, {'tour': array([29, 18, 17,  7, 22, 19, 11, 23, 20, 36,  9, 14, 10, 12, 38, 16,  2,
       25, 33, 27,  4,  5, 26, 21,  6,  8, 32, 37, 24, 15, 35,  3,  0, 30,
       13,  1, 34, 28, 31]), 'cur_cost': 119877.0}, {'tour': array([20, 32,  5, 10,  7,  2, 38, 23, 18, 19, 22,  3, 16, 33, 30, 24, 25,
        8, 13,  4, 12, 17, 15, 36, 37, 29, 34,  6,  0, 31, 28,  1, 27, 35,
        9, 11, 14, 26, 21]), 'cur_cost': 123377.0}]
2025-06-26 18:02:12,896 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:02:12,898 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 202, 'skip_rate': 0.04455445544554455, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 193, 'cache_hits': 172, 'similarity_calculations': 3467, 'cache_hit_rate': 0.049610614364003464, 'cache_size': 3295}}
2025-06-26 18:02:12,898 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:02:12,898 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 17, 23, 8, 29, 14, 35, 2, 19, 26, 7, 32, 11, 38, 4, 21, 9, 33, 16, 28, 1, 24, 13, 36, 3, 20, 10, 34, 15, 30, 6, 22, 18, 31, 25, 37, 27], 'cur_cost': 156163.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 17, 23, 8, 29, 34, 2, 14, 21, 7, 36, 11, 25, 38, 3, 16, 27, 9, 32, 1, 18, 30, 6, 22, 13, 35, 4, 19, 28, 10, 33, 15, 24, 37, 20, 31, 26], 'cur_cost': 157602.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 17, 23, 8, 29, 3, 14, 21, 7, 36, 11, 25, 2, 19, 34, 9, 27, 15, 32, 6, 22, 38, 1, 13, 26, 4, 18, 30, 10, 24, 35, 16, 31, 20, 37, 28, 33], 'cur_cost': 159786.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 10, 15, 20, 25, 30, 35, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 12, 17, 22, 27, 32, 37, 3, 8, 13, 18, 23, 28, 33, 38, 4, 9, 14, 19, 24, 29, 34], 'cur_cost': 159077.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 18, 22, 25, 29, 33, 36, 38, 35, 32, 28, 24, 21, 17, 14, 10, 7, 3, 1, 4, 8, 11, 15, 19, 23, 26, 30, 27, 31, 34, 37, 20, 16, 13, 9, 6, 2], 'cur_cost': 99331.0}}]
2025-06-26 18:02:12,898 - __main__ - INFO - 进化阶段完成
2025-06-26 18:02:12,900 - __main__ - INFO - 开始评估阶段
2025-06-26 18:02:12,900 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:02:12,901 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:02:12,901 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 23968.0 → 99331.0 (Δ -75363.0)
- Mean cost: 105920.5 → 142901.8
- Diversity: 0.663 → 0.755 (Δ 0.0921)

#### Elite metrics
- Elite count: 0 → 22
- Best elite cost: 23968.0 → 23763 (Δ 205.0)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ -68499.83
- Exploit: 4 inds, success 75.0% , avg Δ 10296.5

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [13029.0, 48793.0, 25626.0], diversity [0.8895238095238095, 0.904761904761905, 0.8939682539682541]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:02:12,901 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:02:25,436 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -68499.83,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 10296.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy, explore underperforming",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new exploration operators or increasing mutation rates for explore individuals"
  }
}
```
2025-06-26 18:02:25,449 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:02:25,449 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -68499.83,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 10296.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy, explore underperforming",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new exploration operators or increasing mutation rates for explore individuals"
  }
}
```
2025-06-26 18:02:25,449 - __main__ - INFO - 评估阶段完成
2025-06-26 18:02:25,450 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_delta": -68499.83,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 75.0,
      "average_delta": 10296.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy, explore underperforming",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new exploration operators or increasing mutation rates for explore individuals"
  }
}
```
2025-06-26 18:02:25,450 - __main__ - INFO - 当前最佳适应度: 99331.0
2025-06-26 18:02:25,452 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite6_39_route_0.pkl
2025-06-26 18:02:25,452 - __main__ - INFO - composite6_39 开始进化第 2 代
2025-06-26 18:02:25,452 - __main__ - INFO - 开始分析阶段
2025-06-26 18:02:25,452 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:02:25,454 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 99331.0, 'max': 169603.0, 'mean': 142901.8, 'std': 21380.353148626895}, 'diversity': 0.9321937321937324, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:02:25,460 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 99331.0, 'max': 169603.0, 'mean': 142901.8, 'std': 21380.353148626895}, 'diversity_level': 0.9321937321937324, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[3156, 1356], [3163, 1390], [3179, 1354], [3088, 1415], [3102, 1355], [5793, 2546], [5722, 2537], [5782, 2504], [5772, 2546], [5772, 2556], [7573, 838], [7596, 888], [7564, 889], [7543, 806], [7601, 818], [2523, 6168], [2536, 6136], [2509, 6132], [2562, 6118], [2569, 6184], [4199, 3605], [4160, 3676], [4171, 3694], [4189, 3693], [4214, 3700], [4165, 3649], [1085, 953], [1060, 995], [1017, 984], [991, 941], [1019, 1006], [1065, 979], [6688, 4970], [6614, 5024], [6615, 5042], [6684, 5006], [6654, 5018], [6682, 5047], [6659, 5001]], 'distance_matrix': array([[   0.,   35.,   23., ..., 5064., 5105., 5055.],
       [  35.,    0.,   39., ..., 5035., 5075., 5026.],
       [  23.,   39.,    0., ..., 5050., 5090., 5041.],
       ...,
       [5064., 5035., 5050., ...,    0.,   40.,   18.],
       [5105., 5075., 5090., ...,   40.,    0.,   51.],
       [5055., 5026., 5041., ...,   18.,   51.,    0.]])}
2025-06-26 18:02:25,460 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:02:25,460 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:02:25,460 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:02:25,462 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:02:25,463 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:02:25,463 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:02:25,463 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:02:25,463 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:02:25,464 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:02:25,469 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:02:25,470 - __main__ - INFO - 精英专家分析报告: {'elite_count': 22, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 75568.0, 'avg_gap': 119136.52727272725}, 'structure_gap': {'unique_elite_edges': 93, 'unique_pop_edges': 296, 'common_edges': 33}}, 'elite_diversity': {'diversity_score': 0.2579642579642579}}
2025-06-26 18:02:25,470 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:02:25,470 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:02:25,473 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:02:25,473 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 99331.0, mean 142901.8, max 169603.0, std 21380.353148626895
- diversity: 0.9321937321937324
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [48793.0, 25626.0, 99331.0] (Δ 11583.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [991.0, 806.0, 7601.0, 6184.0]
- centroid/spread: [4436.821, 3013.308] | rms 2915.063
- density_grid(3x3): [[11, 0, 10], [0, 6, 0], [5, 0, 7]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 2516.0, 4182.0, 4954.0, 7376.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [4, 4, 4, 6, 4, 7, 5, 4]
- nn_median_dist: 27.295

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:02:25,473 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:02:50,973 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide edge length range [10.0, 7376.0] with Q1-Q3 spread 2516-4954)",
    "modality": "Potentially multimodal (direction histogram shows balanced distribution across sectors)",
    "deceptiveness": "Possible due to long-edge ratio (25% edges >4954 units) and high std deviation in costs"
  },
  "population_state": {
    "coverage": "Good spatial diversity (diversity=0.93) but uneven distribution (density_grid shows empty cells)",
    "convergence": "Early stage (convergence=0.0, recent best costs show improvement Δ11583.5/iter)"
  },
  "difficult_regions": [
    {
      "type": "Long-edge corridor",
      "evidence": "25% edges >4954 units, empty density_grid cells suggest traversal challenges"
    },
    {
      "type": "Low-density zone",
      "evidence": "3 empty cells in 3x3 density_grid (particularly center-right region)"
    }
  ],
  "opportunity_regions": [
    {
      "type": "High-density cluster",
      "evidence": "Top-left (11 nodes) and bottom-right (7 nodes) grid cells",
      "suggested_action": "Prioritize local optimization in these regions"
    },
    {
      "type": "Directional consistency",
      "evidence": "Sector 5 (7 edges) shows potential pathing patterns",
      "suggested_action": "Bias crossover towards dominant directions"
    }
  ],
  "evolution_phase": "Exploration-dominant",
  "evolution_direction": [
    "Introduce directed mutation (e.g. favor edges <2516 units in sparse regions)",
    "Implement niching in high-density cells to prevent premature convergence",
    "Add path-relinking between elite solutions when they emerge",
    "Consider spatial crossover that respects density_grid clusters"
  ]
}
```
2025-06-26 18:02:50,973 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:02:50,973 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide edge length range [10.0, 7376.0] with Q1-Q3 spread 2516-4954)', 'modality': 'Potentially multimodal (direction histogram shows balanced distribution across sectors)', 'deceptiveness': 'Possible due to long-edge ratio (25% edges >4954 units) and high std deviation in costs'}, 'population_state': {'coverage': 'Good spatial diversity (diversity=0.93) but uneven distribution (density_grid shows empty cells)', 'convergence': 'Early stage (convergence=0.0, recent best costs show improvement Δ11583.5/iter)'}, 'difficult_regions': [{'type': 'Long-edge corridor', 'evidence': '25% edges >4954 units, empty density_grid cells suggest traversal challenges'}, {'type': 'Low-density zone', 'evidence': '3 empty cells in 3x3 density_grid (particularly center-right region)'}], 'opportunity_regions': [{'type': 'High-density cluster', 'evidence': 'Top-left (11 nodes) and bottom-right (7 nodes) grid cells', 'suggested_action': 'Prioritize local optimization in these regions'}, {'type': 'Directional consistency', 'evidence': 'Sector 5 (7 edges) shows potential pathing patterns', 'suggested_action': 'Bias crossover towards dominant directions'}], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Introduce directed mutation (e.g. favor edges <2516 units in sparse regions)', 'Implement niching in high-density cells to prevent premature convergence', 'Add path-relinking between elite solutions when they emerge', 'Consider spatial crossover that respects density_grid clusters']}
2025-06-26 18:02:50,979 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:02:50,979 - __main__ - INFO - 分析阶段完成
2025-06-26 18:02:50,979 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide edge length range [10.0, 7376.0] with Q1-Q3 spread 2516-4954)', 'modality': 'Potentially multimodal (direction histogram shows balanced distribution across sectors)', 'deceptiveness': 'Possible due to long-edge ratio (25% edges >4954 units) and high std deviation in costs'}, 'population_state': {'coverage': 'Good spatial diversity (diversity=0.93) but uneven distribution (density_grid shows empty cells)', 'convergence': 'Early stage (convergence=0.0, recent best costs show improvement Δ11583.5/iter)'}, 'difficult_regions': [{'type': 'Long-edge corridor', 'evidence': '25% edges >4954 units, empty density_grid cells suggest traversal challenges'}, {'type': 'Low-density zone', 'evidence': '3 empty cells in 3x3 density_grid (particularly center-right region)'}], 'opportunity_regions': [{'type': 'High-density cluster', 'evidence': 'Top-left (11 nodes) and bottom-right (7 nodes) grid cells', 'suggested_action': 'Prioritize local optimization in these regions'}, {'type': 'Directional consistency', 'evidence': 'Sector 5 (7 edges) shows potential pathing patterns', 'suggested_action': 'Bias crossover towards dominant directions'}], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Introduce directed mutation (e.g. favor edges <2516 units in sparse regions)', 'Implement niching in high-density cells to prevent premature convergence', 'Add path-relinking between elite solutions when they emerge', 'Consider spatial crossover that respects density_grid clusters']}
2025-06-26 18:02:50,979 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:02:50,980 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:02:50,980 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 99331.0
  • mean_cost: 142901.8
- Search-space: ruggedness Moderate to high (wide edge length range [10.0, 7376.0] with Q1-Q3 spread 2516-4954), modality Potentially multimodal (direction histogram shows balanced distribution across sectors), deceptiveness Possible due to long-edge ratio (25% edges >4954 units) and high std deviation in costs
- Evolution phase: Exploration-dominant
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'Long-edge , {'type': 'Low-densit
- Opportunity regions (# 2): {'type': 'High-densi, {'type': 'Directiona
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:02:50,980 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:02:50,980 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:03:01,163 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify; bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 18:03:01,163 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:03:01,163 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:03:01,163 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify; bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 18:03:01,163 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:03:01,171 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:03:01,171 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify; bottom 40% exploit to refine promising solutions."
}
```
2025-06-26 18:03:01,172 - __main__ - INFO - 开始进化阶段
2025-06-26 18:03:01,172 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:03:01,172 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:03:01,183 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 169603.0 (rank 90.0%)
- Population diversity: 0.7552631578947369
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:03:01,183 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:03:01,184 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:03:12,497 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 14, 37, 3, 26, 10, 31, 19, 33, 1, 15, 28, 6, 24, 38, 11, 25, 4, 20, 32, 9, 27, 13, 35, 2, 18, 30, 16, 22, 36],
  "targeted_regions": "High-density opportunity and adjacent sparse cells",
  "strategy_comment": "Balanced exploration of high-opportunity and low-density regions"
}
```
2025-06-26 18:03:12,505 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:03:12,505 - ExplorationExpert - INFO - 探索路径生成完成，成本: 162951.0, 路径: [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 14, 37, 3, 26, 10, 31, 19, 33, 1, 15, 28, 6, 24, 38, 11, 25, 4, 20, 32, 9, 27, 13, 35, 2, 18, 30, 16, 22, 36]
2025-06-26 18:03:12,505 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 14, 37, 3, 26, 10, 31, 19, 33, 1, 15, 28, 6, 24, 38, 11, 25, 4, 20, 32, 9, 27, 13, 35, 2, 18, 30, 16, 22, 36], 'cur_cost': 162951.0}
2025-06-26 18:03:12,505 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:03:12,505 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:03:12,508 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 156163.0 (rank 50.0%)
- Population diversity: 0.7578947368421053
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:03:12,508 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:03:12,508 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:03:23,188 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 25, 30, 35, 38, 37, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 23, 16, 9, 3, 10, 17],
  "targeted_regions": "High-density opportunity regions and adjacent low-density cells",
  "strategy_comment": "Balanced exploration of high-opportunity and sparse regions"
}
```
2025-06-26 18:03:23,188 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:03:23,188 - ExplorationExpert - INFO - 探索路径生成完成，成本: 146181.0, 路径: [0, 5, 12, 19, 25, 30, 35, 38, 37, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 23, 16, 9, 3, 10, 17]
2025-06-26 18:03:23,188 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 25, 30, 35, 38, 37, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 23, 16, 9, 3, 10, 17], 'cur_cost': 146181.0}
2025-06-26 18:03:23,193 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:03:23,193 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:03:23,193 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 157602.0 (rank 60.0%)
- Population diversity: 0.7605263157894737
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:03:23,193 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:03:23,193 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:03:34,822 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 38, 14, 3, 25, 19, 10, 31, 36, 2, 15, 27, 9, 32, 6, 24, 18, 11, 30, 35, 4, 22, 37, 13, 1, 16, 28, 20, 33, 26],
  "targeted_regions": "High-density opportunity, adjacent low-density",
  "strategy_comment": "Balanced exploration of high-opportunity and sparse regions"
}
```
2025-06-26 18:03:34,828 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:03:34,828 - ExplorationExpert - INFO - 探索路径生成完成，成本: 167584.0, 路径: [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 38, 14, 3, 25, 19, 10, 31, 36, 2, 15, 27, 9, 32, 6, 24, 18, 11, 30, 35, 4, 22, 37, 13, 1, 16, 28, 20, 33, 26]
2025-06-26 18:03:34,828 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 38, 14, 3, 25, 19, 10, 31, 36, 2, 15, 27, 9, 32, 6, 24, 18, 11, 30, 35, 4, 22, 37, 13, 1, 16, 28, 20, 33, 26], 'cur_cost': 167584.0}
2025-06-26 18:03:34,829 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:03:34,829 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:03:34,830 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 159786.0 (rank 70.0%)
- Population diversity: 0.7605263157894737
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:03:34,830 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:03:34,830 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:03:45,663 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 18, 22, 25, 29, 33, 37, 1, 6, 10, 14, 19, 23, 27, 31, 35, 38, 2, 7, 11, 15, 20, 24, 28, 32, 36, 3, 8, 13, 16, 21, 26, 30, 34, 4, 9, 17],
  "targeted_regions": "High-density opportunity, adjacent sparse cells",
  "strategy_comment": "Cluster opportunity regions, skip long edges, balance exploration"
}
```
2025-06-26 18:03:45,663 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:03:45,663 - ExplorationExpert - INFO - 探索路径生成完成，成本: 140023.0, 路径: [0, 5, 12, 18, 22, 25, 29, 33, 37, 1, 6, 10, 14, 19, 23, 27, 31, 35, 38, 2, 7, 11, 15, 20, 24, 28, 32, 36, 3, 8, 13, 16, 21, 26, 30, 34, 4, 9, 17]
2025-06-26 18:03:45,663 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 1, 6, 10, 14, 19, 23, 27, 31, 35, 38, 2, 7, 11, 15, 20, 24, 28, 32, 36, 3, 8, 13, 16, 21, 26, 30, 34, 4, 9, 17], 'cur_cost': 140023.0}
2025-06-26 18:03:45,663 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:03:45,663 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:03:45,663 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 159077.0 (rank 70.0%)
- Population diversity: 0.7394736842105263
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:03:45,663 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:03:45,666 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:03:56,880 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35],
  "targeted_regions": "High-density and adjacent low-density cells",
  "strategy_comment": "Balanced exploration with modular jumps to increase diversity"
}
```
2025-06-26 18:03:56,881 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:03:56,881 - ExplorationExpert - INFO - 探索路径生成完成，成本: 169603.0, 路径: [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35]
2025-06-26 18:03:56,881 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}
2025-06-26 18:03:56,883 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:03:56,883 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:03:56,883 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 99331.0 (rank 0.0%)
- Population diversity: 0.7736842105263158
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Direc
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:03:56,883 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:03:56,883 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:04:08,146 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35],
  "targeted_regions": "High-density opportunity regions and adjacent sparse cells",
  "strategy_comment": "Leverage modular jumps to explore sparse regions while maintaining structure"
}
```
2025-06-26 18:04:08,148 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:04:08,148 - ExplorationExpert - INFO - 探索路径生成完成，成本: 169603.0, 路径: [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35]
2025-06-26 18:04:08,148 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}
2025-06-26 18:04:08,148 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 18:04:08,148 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:04:08,148 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:04:08,151 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 133873.0
2025-06-26 18:04:08,653 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:04:08,653 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:04:08,653 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:04:08,660 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:04:08,661 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 14, 37, 3, 26, 10, 31, 19, 33, 1, 15, 28, 6, 24, 38, 11, 25, 4, 20, 32, 9, 27, 13, 35, 2, 18, 30, 16, 22, 36], 'cur_cost': 162951.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 38, 37, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 23, 16, 9, 3, 10, 17], 'cur_cost': 146181.0}, {'tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 38, 14, 3, 25, 19, 10, 31, 36, 2, 15, 27, 9, 32, 6, 24, 18, 11, 30, 35, 4, 22, 37, 13, 1, 16, 28, 20, 33, 26], 'cur_cost': 167584.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 1, 6, 10, 14, 19, 23, 27, 31, 35, 38, 2, 7, 11, 15, 20, 24, 28, 32, 36, 3, 8, 13, 16, 21, 26, 30, 34, 4, 9, 17], 'cur_cost': 140023.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([15, 19, 37,  7, 33, 14, 22, 12,  9,  0, 26,  3, 31, 16, 38, 28, 30,
       36, 17,  6,  4, 32, 27, 20,  1,  2, 10, 21, 11,  8, 13, 35, 34, 25,
       29,  5, 18, 24, 23]), 'cur_cost': 133873.0}, {'tour': array([ 4, 28, 15,  2,  9, 36, 16, 19, 21, 32, 29, 23, 10, 37, 24, 35, 33,
       27, 22,  6, 30, 12, 11, 31,  7, 26, 34,  0, 25, 18, 20,  3,  5, 14,
        1, 38,  8, 13, 17]), 'cur_cost': 149467.0}, {'tour': array([29, 18, 17,  7, 22, 19, 11, 23, 20, 36,  9, 14, 10, 12, 38, 16,  2,
       25, 33, 27,  4,  5, 26, 21,  6,  8, 32, 37, 24, 15, 35,  3,  0, 30,
       13,  1, 34, 28, 31]), 'cur_cost': 119877.0}, {'tour': array([20, 32,  5, 10,  7,  2, 38, 23, 18, 19, 22,  3, 16, 33, 30, 24, 25,
        8, 13,  4, 12, 17, 15, 36, 37, 29, 34,  6,  0, 31, 28,  1, 27, 35,
        9, 11, 14, 26, 21]), 'cur_cost': 123377.0}]
2025-06-26 18:04:08,663 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:04:08,663 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 203, 'skip_rate': 0.04433497536945813, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 194, 'cache_hits': 172, 'similarity_calculations': 3471, 'cache_hit_rate': 0.049553442811869776, 'cache_size': 3299}}
2025-06-26 18:04:08,663 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 18:04:08,663 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:04:08,664 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:04:08,664 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:04:08,664 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 133643.0
2025-06-26 18:04:09,168 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:04:09,168 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:04:09,168 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:04:09,176 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:04:09,176 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 14, 37, 3, 26, 10, 31, 19, 33, 1, 15, 28, 6, 24, 38, 11, 25, 4, 20, 32, 9, 27, 13, 35, 2, 18, 30, 16, 22, 36], 'cur_cost': 162951.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 38, 37, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 23, 16, 9, 3, 10, 17], 'cur_cost': 146181.0}, {'tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 38, 14, 3, 25, 19, 10, 31, 36, 2, 15, 27, 9, 32, 6, 24, 18, 11, 30, 35, 4, 22, 37, 13, 1, 16, 28, 20, 33, 26], 'cur_cost': 167584.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 1, 6, 10, 14, 19, 23, 27, 31, 35, 38, 2, 7, 11, 15, 20, 24, 28, 32, 36, 3, 8, 13, 16, 21, 26, 30, 34, 4, 9, 17], 'cur_cost': 140023.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([15, 19, 37,  7, 33, 14, 22, 12,  9,  0, 26,  3, 31, 16, 38, 28, 30,
       36, 17,  6,  4, 32, 27, 20,  1,  2, 10, 21, 11,  8, 13, 35, 34, 25,
       29,  5, 18, 24, 23]), 'cur_cost': 133873.0}, {'tour': array([35, 19, 31, 32, 12,  3, 26, 13, 33, 18,  8, 38, 36,  7, 20, 22,  5,
       17, 29, 27, 24, 25, 21,  1, 14, 30, 28,  6, 23,  4, 15,  9,  2, 16,
        0, 11, 34, 10, 37]), 'cur_cost': 133643.0}, {'tour': array([29, 18, 17,  7, 22, 19, 11, 23, 20, 36,  9, 14, 10, 12, 38, 16,  2,
       25, 33, 27,  4,  5, 26, 21,  6,  8, 32, 37, 24, 15, 35,  3,  0, 30,
       13,  1, 34, 28, 31]), 'cur_cost': 119877.0}, {'tour': array([20, 32,  5, 10,  7,  2, 38, 23, 18, 19, 22,  3, 16, 33, 30, 24, 25,
        8, 13,  4, 12, 17, 15, 36, 37, 29, 34,  6,  0, 31, 28,  1, 27, 35,
        9, 11, 14, 26, 21]), 'cur_cost': 123377.0}]
2025-06-26 18:04:09,178 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:04:09,178 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 204, 'skip_rate': 0.04411764705882353, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 195, 'cache_hits': 172, 'similarity_calculations': 3476, 'cache_hit_rate': 0.04948216340621404, 'cache_size': 3304}}
2025-06-26 18:04:09,178 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:04:09,179 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:04:09,179 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:04:09,179 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:04:09,179 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 147199.0
2025-06-26 18:04:09,682 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:04:09,682 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:04:09,682 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:04:09,690 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:04:09,690 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 14, 37, 3, 26, 10, 31, 19, 33, 1, 15, 28, 6, 24, 38, 11, 25, 4, 20, 32, 9, 27, 13, 35, 2, 18, 30, 16, 22, 36], 'cur_cost': 162951.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 38, 37, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 23, 16, 9, 3, 10, 17], 'cur_cost': 146181.0}, {'tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 38, 14, 3, 25, 19, 10, 31, 36, 2, 15, 27, 9, 32, 6, 24, 18, 11, 30, 35, 4, 22, 37, 13, 1, 16, 28, 20, 33, 26], 'cur_cost': 167584.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 1, 6, 10, 14, 19, 23, 27, 31, 35, 38, 2, 7, 11, 15, 20, 24, 28, 32, 36, 3, 8, 13, 16, 21, 26, 30, 34, 4, 9, 17], 'cur_cost': 140023.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([15, 19, 37,  7, 33, 14, 22, 12,  9,  0, 26,  3, 31, 16, 38, 28, 30,
       36, 17,  6,  4, 32, 27, 20,  1,  2, 10, 21, 11,  8, 13, 35, 34, 25,
       29,  5, 18, 24, 23]), 'cur_cost': 133873.0}, {'tour': array([35, 19, 31, 32, 12,  3, 26, 13, 33, 18,  8, 38, 36,  7, 20, 22,  5,
       17, 29, 27, 24, 25, 21,  1, 14, 30, 28,  6, 23,  4, 15,  9,  2, 16,
        0, 11, 34, 10, 37]), 'cur_cost': 133643.0}, {'tour': array([18, 32, 13, 36, 23, 19, 22, 37,  5,  8, 15, 12,  4, 35, 28, 29, 14,
       20, 17,  9, 34, 16,  0, 30, 10, 25, 21,  6, 26, 33, 27,  1, 31, 11,
       38,  7, 24,  2,  3]), 'cur_cost': 147199.0}, {'tour': array([20, 32,  5, 10,  7,  2, 38, 23, 18, 19, 22,  3, 16, 33, 30, 24, 25,
        8, 13,  4, 12, 17, 15, 36, 37, 29, 34,  6,  0, 31, 28,  1, 27, 35,
        9, 11, 14, 26, 21]), 'cur_cost': 123377.0}]
2025-06-26 18:04:09,692 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:04:09,692 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 205, 'skip_rate': 0.04390243902439024, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 196, 'cache_hits': 172, 'similarity_calculations': 3482, 'cache_hit_rate': 0.04939689833429064, 'cache_size': 3310}}
2025-06-26 18:04:09,692 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:04:09,693 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:04:09,693 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:04:09,693 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:04:09,694 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 133568.0
2025-06-26 18:04:10,197 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:04:10,197 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:04:10,198 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:04:10,205 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:04:10,205 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 14, 37, 3, 26, 10, 31, 19, 33, 1, 15, 28, 6, 24, 38, 11, 25, 4, 20, 32, 9, 27, 13, 35, 2, 18, 30, 16, 22, 36], 'cur_cost': 162951.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 38, 37, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 23, 16, 9, 3, 10, 17], 'cur_cost': 146181.0}, {'tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 38, 14, 3, 25, 19, 10, 31, 36, 2, 15, 27, 9, 32, 6, 24, 18, 11, 30, 35, 4, 22, 37, 13, 1, 16, 28, 20, 33, 26], 'cur_cost': 167584.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 1, 6, 10, 14, 19, 23, 27, 31, 35, 38, 2, 7, 11, 15, 20, 24, 28, 32, 36, 3, 8, 13, 16, 21, 26, 30, 34, 4, 9, 17], 'cur_cost': 140023.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([15, 19, 37,  7, 33, 14, 22, 12,  9,  0, 26,  3, 31, 16, 38, 28, 30,
       36, 17,  6,  4, 32, 27, 20,  1,  2, 10, 21, 11,  8, 13, 35, 34, 25,
       29,  5, 18, 24, 23]), 'cur_cost': 133873.0}, {'tour': array([35, 19, 31, 32, 12,  3, 26, 13, 33, 18,  8, 38, 36,  7, 20, 22,  5,
       17, 29, 27, 24, 25, 21,  1, 14, 30, 28,  6, 23,  4, 15,  9,  2, 16,
        0, 11, 34, 10, 37]), 'cur_cost': 133643.0}, {'tour': array([18, 32, 13, 36, 23, 19, 22, 37,  5,  8, 15, 12,  4, 35, 28, 29, 14,
       20, 17,  9, 34, 16,  0, 30, 10, 25, 21,  6, 26, 33, 27,  1, 31, 11,
       38,  7, 24,  2,  3]), 'cur_cost': 147199.0}, {'tour': array([34, 17, 13,  7, 23, 20, 32, 15, 25, 33, 36,  2, 28, 12, 35,  5, 26,
        0, 24,  3,  6, 14, 37, 29,  1, 16, 19, 18, 31, 21, 38, 22, 10, 27,
        8, 30,  4, 11,  9]), 'cur_cost': 133568.0}]
2025-06-26 18:04:10,207 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:04:10,207 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 206, 'skip_rate': 0.043689320388349516, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 197, 'cache_hits': 172, 'similarity_calculations': 3489, 'cache_hit_rate': 0.049297793063915164, 'cache_size': 3317}}
2025-06-26 18:04:10,207 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:04:10,207 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 14, 37, 3, 26, 10, 31, 19, 33, 1, 15, 28, 6, 24, 38, 11, 25, 4, 20, 32, 9, 27, 13, 35, 2, 18, 30, 16, 22, 36], 'cur_cost': 162951.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 25, 30, 35, 38, 37, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 23, 16, 9, 3, 10, 17], 'cur_cost': 146181.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 23, 17, 8, 29, 34, 7, 21, 38, 14, 3, 25, 19, 10, 31, 36, 2, 15, 27, 9, 32, 6, 24, 18, 11, 30, 35, 4, 22, 37, 13, 1, 16, 28, 20, 33, 26], 'cur_cost': 167584.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 1, 6, 10, 14, 19, 23, 27, 31, 35, 38, 2, 7, 11, 15, 20, 24, 28, 32, 36, 3, 8, 13, 16, 21, 26, 30, 34, 4, 9, 17], 'cur_cost': 140023.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}}]
2025-06-26 18:04:10,208 - __main__ - INFO - 进化阶段完成
2025-06-26 18:04:10,208 - __main__ - INFO - 开始评估阶段
2025-06-26 18:04:10,208 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:04:10,210 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:04:10,210 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 99331.0 → 133568.0 (Δ -34237.0)
- Mean cost: 142901.8 → 150422.8
- Diversity: 0.755 → 0.729 (Δ -0.0263)

#### Elite metrics
- Elite count: 22 → 22
- Best elite cost: 23763 → 23763 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 50.0% , avg Δ -9063.83
- Exploit: 4 inds, success 50.0% , avg Δ -5206.75

#### Other indicators
- No-change individuals: 0
- Regression individuals: 5
- Historical trends: Last 3 iterations: costs [48793.0, 25626.0, 99331.0], diversity [0.904761904761905, 0.8939682539682541, 0.9321937321937324]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:04:10,210 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:04:24,180 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 50.0,
      "average_improvement": -9063.83,
      "contribution_to_elite": "moderate"
    },
    "Exploit": {
      "success_rate": 50.0,
      "average_improvement": -5206.75,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (Explore:Exploit)",
    "assessment": "slightly imbalanced towards explore",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators or increasing mutation rates to enhance diversity."
  }
}
```
2025-06-26 18:04:24,195 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:04:24,195 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 50.0,
      "average_improvement": -9063.83,
      "contribution_to_elite": "moderate"
    },
    "Exploit": {
      "success_rate": 50.0,
      "average_improvement": -5206.75,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (Explore:Exploit)",
    "assessment": "slightly imbalanced towards explore",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators or increasing mutation rates to enhance diversity."
  }
}
```
2025-06-26 18:04:24,195 - __main__ - INFO - 评估阶段完成
2025-06-26 18:04:24,196 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
      "success_rate": 50.0,
      "average_improvement": -9063.83,
      "contribution_to_elite": "moderate"
    },
    "Exploit": {
      "success_rate": 50.0,
      "average_improvement": -5206.75,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "current_ratio": "6:4 (Explore:Exploit)",
    "assessment": "slightly imbalanced towards explore",
    "needs_adjustment": true
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new genetic operators or increasing mutation rates to enhance diversity."
  }
}
```
2025-06-26 18:04:24,196 - __main__ - INFO - 当前最佳适应度: 133568.0
2025-06-26 18:04:24,197 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite6_39_route_1.pkl
2025-06-26 18:04:24,198 - __main__ - INFO - composite6_39 开始进化第 3 代
2025-06-26 18:04:24,198 - __main__ - INFO - 开始分析阶段
2025-06-26 18:04:24,199 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:04:24,206 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 133568.0, 'max': 169603.0, 'mean': 150422.8, 'std': 14708.181973309956}, 'diversity': 0.9236467236467237, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 1, 1, 1, 2, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:04:24,206 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 133568.0, 'max': 169603.0, 'mean': 150422.8, 'std': 14708.181973309956}, 'diversity_level': 0.9236467236467237, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 1, 1, 1, 2, 1, 1, 1, 1]}, 'coordinates': [[3156, 1356], [3163, 1390], [3179, 1354], [3088, 1415], [3102, 1355], [5793, 2546], [5722, 2537], [5782, 2504], [5772, 2546], [5772, 2556], [7573, 838], [7596, 888], [7564, 889], [7543, 806], [7601, 818], [2523, 6168], [2536, 6136], [2509, 6132], [2562, 6118], [2569, 6184], [4199, 3605], [4160, 3676], [4171, 3694], [4189, 3693], [4214, 3700], [4165, 3649], [1085, 953], [1060, 995], [1017, 984], [991, 941], [1019, 1006], [1065, 979], [6688, 4970], [6614, 5024], [6615, 5042], [6684, 5006], [6654, 5018], [6682, 5047], [6659, 5001]], 'distance_matrix': array([[   0.,   35.,   23., ..., 5064., 5105., 5055.],
       [  35.,    0.,   39., ..., 5035., 5075., 5026.],
       [  23.,   39.,    0., ..., 5050., 5090., 5041.],
       ...,
       [5064., 5035., 5050., ...,    0.,   40.,   18.],
       [5105., 5075., 5090., ...,   40.,    0.,   51.],
       [5055., 5026., 5041., ...,   18.,   51.,    0.]])}
2025-06-26 18:04:24,207 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:04:24,207 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:04:24,207 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:04:24,208 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:04:24,208 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:04:24,208 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:04:24,209 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:04:24,209 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:04:24,209 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:04:24,215 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:04:24,215 - __main__ - INFO - 精英专家分析报告: {'elite_count': 22, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 109805.0, 'avg_gap': 126657.52727272725}, 'structure_gap': {'unique_elite_edges': 96, 'unique_pop_edges': 274, 'common_edges': 30}}, 'elite_diversity': {'diversity_score': 0.2579642579642579}}
2025-06-26 18:04:24,215 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:04:24,215 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:04:24,215 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:04:24,215 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 133568.0, mean 150422.8, max 169603.0, std 14708.181973309956
- diversity: 0.9236467236467237
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [25626.0, 99331.0, 133568.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [991.0, 806.0, 7601.0, 6184.0]
- centroid/spread: [4436.821, 3013.308] | rms 2915.063
- density_grid(3x3): [[11, 0, 10], [0, 6, 0], [5, 0, 7]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 2516.0, 4182.0, 4954.0, 7376.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [4, 4, 4, 6, 4, 7, 5, 4]
- nn_median_dist: 27.295

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:04:24,215 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:04:52,673 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation)",
    "modality": "Potentially multimodal (direction histogram shows varied edge directions, no single dominant pattern)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 suggests some long edges that may mislead local search)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (diversity 0.92, density_grid shows activity in most cells)",
    "convergence": "Early stage (convergence 0.0, no stagnation detected)",
    "elite_influence": "Minimal (elite_count 0, no common edges observed)"
  },
  "difficult_regions": [
    {
      "type": "Long-edge corridor",
      "evidence": "Edge lengths in q3 (4954.0) to max (7376.0) range",
      "nodes": "Potential nodes connected by edges in top 25% length percentile"
    },
    {
      "type": "Low-density zone",
      "evidence": "Empty cells in density_grid (positions [0,1], [1,0], [1,2])",
      "nodes": "Nodes located in bounding box regions corresponding to grid cells with 0 counts"
    }
  ],
  "opportunity_regions": [
    {
      "type": "High-density cluster",
      "evidence": "Dense cells in grid (positions [0,0] with 11 nodes, [0,2] with 10)",
      "nodes": "Nodes within these grid regions likely form promising subpaths"
    },
    {
      "type": "Central tendency",
      "evidence": "Centroid at [4436.821, 3013.308] with RMS 2915.063",
      "nodes": "Nodes near centroid may serve as good connection points"
    }
  ],
  "evolution_phase": "Exploration-dominant",
  "evolution_direction": [
    "Introduce path-relinking between high-density clusters",
    "Apply local search intensification in opportunity regions",
    "Test mutation operators that target long-edge replacement",
    "Maintain diversity through spatial niching (reward solutions covering empty grid cells)"
  ]
}
```
2025-06-26 18:04:52,677 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:04:52,677 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation)', 'modality': 'Potentially multimodal (direction histogram shows varied edge directions, no single dominant pattern)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some long edges that may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.92, density_grid shows activity in most cells)', 'convergence': 'Early stage (convergence 0.0, no stagnation detected)', 'elite_influence': 'Minimal (elite_count 0, no common edges observed)'}, 'difficult_regions': [{'type': 'Long-edge corridor', 'evidence': 'Edge lengths in q3 (4954.0) to max (7376.0) range', 'nodes': 'Potential nodes connected by edges in top 25% length percentile'}, {'type': 'Low-density zone', 'evidence': 'Empty cells in density_grid (positions [0,1], [1,0], [1,2])', 'nodes': 'Nodes located in bounding box regions corresponding to grid cells with 0 counts'}], 'opportunity_regions': [{'type': 'High-density cluster', 'evidence': 'Dense cells in grid (positions [0,0] with 11 nodes, [0,2] with 10)', 'nodes': 'Nodes within these grid regions likely form promising subpaths'}, {'type': 'Central tendency', 'evidence': 'Centroid at [4436.821, 3013.308] with RMS 2915.063', 'nodes': 'Nodes near centroid may serve as good connection points'}], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Introduce path-relinking between high-density clusters', 'Apply local search intensification in opportunity regions', 'Test mutation operators that target long-edge replacement', 'Maintain diversity through spatial niching (reward solutions covering empty grid cells)']}
2025-06-26 18:04:52,677 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:04:52,677 - __main__ - INFO - 分析阶段完成
2025-06-26 18:04:52,677 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation)', 'modality': 'Potentially multimodal (direction histogram shows varied edge directions, no single dominant pattern)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some long edges that may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.92, density_grid shows activity in most cells)', 'convergence': 'Early stage (convergence 0.0, no stagnation detected)', 'elite_influence': 'Minimal (elite_count 0, no common edges observed)'}, 'difficult_regions': [{'type': 'Long-edge corridor', 'evidence': 'Edge lengths in q3 (4954.0) to max (7376.0) range', 'nodes': 'Potential nodes connected by edges in top 25% length percentile'}, {'type': 'Low-density zone', 'evidence': 'Empty cells in density_grid (positions [0,1], [1,0], [1,2])', 'nodes': 'Nodes located in bounding box regions corresponding to grid cells with 0 counts'}], 'opportunity_regions': [{'type': 'High-density cluster', 'evidence': 'Dense cells in grid (positions [0,0] with 11 nodes, [0,2] with 10)', 'nodes': 'Nodes within these grid regions likely form promising subpaths'}, {'type': 'Central tendency', 'evidence': 'Centroid at [4436.821, 3013.308] with RMS 2915.063', 'nodes': 'Nodes near centroid may serve as good connection points'}], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Introduce path-relinking between high-density clusters', 'Apply local search intensification in opportunity regions', 'Test mutation operators that target long-edge replacement', 'Maintain diversity through spatial niching (reward solutions covering empty grid cells)']}
2025-06-26 18:04:52,677 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:04:52,677 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:04:52,677 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 133568.0
  • mean_cost: 150422.8
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation), modality Potentially multimodal (direction histogram shows varied edge directions, no single dominant pattern), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 suggests some long edges that may mislead local search)
- Evolution phase: Exploration-dominant
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): {'type': 'Long-edge , {'type': 'Low-densit
- Opportunity regions (# 2): {'type': 'High-densi, {'type': 'Central te
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "Explore": {
    ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:04:52,677 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:04:52,677 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:05:02,471 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration (70%). Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 18:05:02,471 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:05:02,471 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:05:02,471 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration (70%). Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 18:05:02,471 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:05:02,471 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 18:05:02,471 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration (70%). Top 60% explore to diversify; bottom 40% exploit to refine."
}
```
2025-06-26 18:05:02,471 - __main__ - INFO - 开始进化阶段
2025-06-26 18:05:02,471 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:05:02,471 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:05:02,471 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 162951.0 (rank 60.0%)
- Population diversity: 0.7289473684210527
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Centr
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:05:02,479 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:05:02,479 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:05:13,638 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35],
  "targeted_regions": "Central and high-opportunity regions",
  "strategy_comment": "Spiral pattern to cover central nodes while maintaining diversity"
}
```
2025-06-26 18:05:13,638 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:05:13,638 - ExplorationExpert - INFO - 探索路径生成完成，成本: 169603.0, 路径: [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35]
2025-06-26 18:05:13,638 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}
2025-06-26 18:05:13,638 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:05:13,638 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:05:13,638 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 146181.0 (rank 40.0%)
- Population diversity: 0.6868421052631579
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Centr
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:05:13,638 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:05:13,638 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:05:24,011 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 23, 17, 8, 29, 34, 21, 14, 7, 38, 25, 19, 10, 3, 31, 26, 15, 6, 37, 22, 13, 4, 35, 28, 18, 9, 2, 32, 27, 16, 1, 30, 24, 20, 11, 33, 36],
  "targeted_regions": "High-density central nodes, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of central opportunities with sparse adjacency"
}
```
2025-06-26 18:05:24,016 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:05:24,016 - ExplorationExpert - INFO - 探索路径生成完成，成本: 152836.0, 路径: [0, 12, 5, 23, 17, 8, 29, 34, 21, 14, 7, 38, 25, 19, 10, 3, 31, 26, 15, 6, 37, 22, 13, 4, 35, 28, 18, 9, 2, 32, 27, 16, 1, 30, 24, 20, 11, 33, 36]
2025-06-26 18:05:24,016 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 5, 23, 17, 8, 29, 34, 21, 14, 7, 38, 25, 19, 10, 3, 31, 26, 15, 6, 37, 22, 13, 4, 35, 28, 18, 9, 2, 32, 27, 16, 1, 30, 24, 20, 11, 33, 36], 'cur_cost': 152836.0}
2025-06-26 18:05:24,016 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:05:24,016 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:05:24,016 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 167584.0 (rank 60.0%)
- Population diversity: 0.6763157894736842
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Centr
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:05:24,016 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:05:24,016 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:05:34,986 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35],
  "targeted_regions": "Central and high-density cells",
  "strategy_comment": "Leverage central nodes and alternate high-density regions"
}
```
2025-06-26 18:05:34,986 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:05:34,986 - ExplorationExpert - INFO - 探索路径生成完成，成本: 169603.0, 路径: [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35]
2025-06-26 18:05:34,986 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}
2025-06-26 18:05:34,990 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:05:34,990 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:05:34,990 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 140023.0 (rank 30.0%)
- Population diversity: 0.6289473684210526
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Centr
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:05:34,990 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:05:34,990 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:05:46,263 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 39, 11, 23, 35, 7, 19, 31],
  "targeted_regions": "Central and low-density adjacent cells",
  "strategy_comment": "Spiral pattern to balance exploration and cost"
}
```
2025-06-26 18:05:46,263 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:05:46,263 - ExplorationExpert - INFO - 探索路径生成完成，成本: 153894.0, 路径: [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 11, 23, 35, 7, 19, 31]
2025-06-26 18:05:46,263 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 11, 23, 35, 7, 19, 31], 'cur_cost': 153894.0}
2025-06-26 18:05:46,263 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:05:46,263 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:05:46,263 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 169603.0 (rank 60.0%)
- Population diversity: 0.5710526315789474
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Centr
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:05:46,263 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:05:46,263 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:05:57,527 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 18, 22, 25, 29, 33, 37, 38, 34, 30, 26, 21, 17, 13, 9, 4, 1, 6, 10, 14, 19, 23, 27, 31, 35, 32, 28, 24, 20, 16, 11, 7, 2, 8, 15, 3, 36],
  "targeted_regions": "Central and high-density opportunity regions",
  "strategy_comment": "Balanced exploration of central nodes with sparse adjacency"
}
```
2025-06-26 18:05:57,527 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:05:57,527 - ExplorationExpert - INFO - 探索路径生成完成，成本: 129911.0, 路径: [0, 5, 12, 18, 22, 25, 29, 33, 37, 38, 34, 30, 26, 21, 17, 13, 9, 4, 1, 6, 10, 14, 19, 23, 27, 31, 35, 32, 28, 24, 20, 16, 11, 7, 2, 8, 15, 3, 36]
2025-06-26 18:05:57,527 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 38, 34, 30, 26, 21, 17, 13, 9, 4, 1, 6, 10, 14, 19, 23, 27, 31, 35, 32, 28, 24, 20, 16, 11, 7, 2, 8, 15, 3, 36], 'cur_cost': 129911.0}
2025-06-26 18:05:57,527 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:05:57,527 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:05:57,527 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 169603.0 (rank 70.0%)
- Population diversity: 0.6526315789473685
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): {'type': 'High-, {'type': 'Centr
- Difficult regions to avoid (sample): {'type': 'Long-, {'type': 'Low-d
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:05:57,527 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:05:57,527 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:06:07,384 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35],
  "targeted_regions": "Central and low-density cells",
  "strategy_comment": "Spiral pattern to cover sparse regions while avoiding long edges"
}
```
2025-06-26 18:06:07,384 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:06:07,391 - ExplorationExpert - INFO - 探索路径生成完成，成本: 169603.0, 路径: [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35]
2025-06-26 18:06:07,391 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}
2025-06-26 18:06:07,392 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 18:06:07,392 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:06:07,392 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:06:07,392 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 125620.0
2025-06-26 18:06:07,894 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:06:07,895 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:06:07,895 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:06:07,903 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:06:07,903 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 5, 23, 17, 8, 29, 34, 21, 14, 7, 38, 25, 19, 10, 3, 31, 26, 15, 6, 37, 22, 13, 4, 35, 28, 18, 9, 2, 32, 27, 16, 1, 30, 24, 20, 11, 33, 36], 'cur_cost': 152836.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 11, 23, 35, 7, 19, 31], 'cur_cost': 153894.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 38, 34, 30, 26, 21, 17, 13, 9, 4, 1, 6, 10, 14, 19, 23, 27, 31, 35, 32, 28, 24, 20, 16, 11, 7, 2, 8, 15, 3, 36], 'cur_cost': 129911.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([13, 12, 30, 16, 37, 26,  9, 23, 24, 35,  6, 19, 15, 34, 18,  0,  4,
       36, 10, 11,  1,  5, 32, 33, 31, 29,  3,  8, 14,  7, 22, 28, 25, 27,
       21, 17, 38, 20,  2]), 'cur_cost': 125620.0}, {'tour': array([35, 19, 31, 32, 12,  3, 26, 13, 33, 18,  8, 38, 36,  7, 20, 22,  5,
       17, 29, 27, 24, 25, 21,  1, 14, 30, 28,  6, 23,  4, 15,  9,  2, 16,
        0, 11, 34, 10, 37]), 'cur_cost': 133643.0}, {'tour': array([18, 32, 13, 36, 23, 19, 22, 37,  5,  8, 15, 12,  4, 35, 28, 29, 14,
       20, 17,  9, 34, 16,  0, 30, 10, 25, 21,  6, 26, 33, 27,  1, 31, 11,
       38,  7, 24,  2,  3]), 'cur_cost': 147199.0}, {'tour': array([34, 17, 13,  7, 23, 20, 32, 15, 25, 33, 36,  2, 28, 12, 35,  5, 26,
        0, 24,  3,  6, 14, 37, 29,  1, 16, 19, 18, 31, 21, 38, 22, 10, 27,
        8, 30,  4, 11,  9]), 'cur_cost': 133568.0}]
2025-06-26 18:06:07,904 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:06:07,904 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 207, 'skip_rate': 0.043478260869565216, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 198, 'cache_hits': 172, 'similarity_calculations': 3497, 'cache_hit_rate': 0.04918501572776666, 'cache_size': 3325}}
2025-06-26 18:06:07,905 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 18:06:07,905 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:06:07,905 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:06:07,905 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:06:07,906 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 125688.0
2025-06-26 18:06:08,408 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:06:08,408 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:06:08,408 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:06:08,416 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:06:08,416 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 5, 23, 17, 8, 29, 34, 21, 14, 7, 38, 25, 19, 10, 3, 31, 26, 15, 6, 37, 22, 13, 4, 35, 28, 18, 9, 2, 32, 27, 16, 1, 30, 24, 20, 11, 33, 36], 'cur_cost': 152836.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 11, 23, 35, 7, 19, 31], 'cur_cost': 153894.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 38, 34, 30, 26, 21, 17, 13, 9, 4, 1, 6, 10, 14, 19, 23, 27, 31, 35, 32, 28, 24, 20, 16, 11, 7, 2, 8, 15, 3, 36], 'cur_cost': 129911.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([13, 12, 30, 16, 37, 26,  9, 23, 24, 35,  6, 19, 15, 34, 18,  0,  4,
       36, 10, 11,  1,  5, 32, 33, 31, 29,  3,  8, 14,  7, 22, 28, 25, 27,
       21, 17, 38, 20,  2]), 'cur_cost': 125620.0}, {'tour': array([30, 14, 26,  1, 33, 38, 16, 24, 15, 19, 13, 22,  6, 10, 23,  7,  5,
       36,  8, 20, 29, 27, 31, 12, 17,  4,  2, 35,  0, 18, 32,  9, 25, 21,
       37, 11, 34,  3, 28]), 'cur_cost': 125688.0}, {'tour': array([18, 32, 13, 36, 23, 19, 22, 37,  5,  8, 15, 12,  4, 35, 28, 29, 14,
       20, 17,  9, 34, 16,  0, 30, 10, 25, 21,  6, 26, 33, 27,  1, 31, 11,
       38,  7, 24,  2,  3]), 'cur_cost': 147199.0}, {'tour': array([34, 17, 13,  7, 23, 20, 32, 15, 25, 33, 36,  2, 28, 12, 35,  5, 26,
        0, 24,  3,  6, 14, 37, 29,  1, 16, 19, 18, 31, 21, 38, 22, 10, 27,
        8, 30,  4, 11,  9]), 'cur_cost': 133568.0}]
2025-06-26 18:06:08,418 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:06:08,418 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 208, 'skip_rate': 0.04326923076923077, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 199, 'cache_hits': 172, 'similarity_calculations': 3506, 'cache_hit_rate': 0.04905875641756988, 'cache_size': 3334}}
2025-06-26 18:06:08,418 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:06:08,418 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:06:08,418 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:06:08,418 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:06:08,420 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 148837.0
2025-06-26 18:06:08,922 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:06:08,922 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:06:08,922 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:06:08,929 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:06:08,930 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 5, 23, 17, 8, 29, 34, 21, 14, 7, 38, 25, 19, 10, 3, 31, 26, 15, 6, 37, 22, 13, 4, 35, 28, 18, 9, 2, 32, 27, 16, 1, 30, 24, 20, 11, 33, 36], 'cur_cost': 152836.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 11, 23, 35, 7, 19, 31], 'cur_cost': 153894.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 38, 34, 30, 26, 21, 17, 13, 9, 4, 1, 6, 10, 14, 19, 23, 27, 31, 35, 32, 28, 24, 20, 16, 11, 7, 2, 8, 15, 3, 36], 'cur_cost': 129911.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([13, 12, 30, 16, 37, 26,  9, 23, 24, 35,  6, 19, 15, 34, 18,  0,  4,
       36, 10, 11,  1,  5, 32, 33, 31, 29,  3,  8, 14,  7, 22, 28, 25, 27,
       21, 17, 38, 20,  2]), 'cur_cost': 125620.0}, {'tour': array([30, 14, 26,  1, 33, 38, 16, 24, 15, 19, 13, 22,  6, 10, 23,  7,  5,
       36,  8, 20, 29, 27, 31, 12, 17,  4,  2, 35,  0, 18, 32,  9, 25, 21,
       37, 11, 34,  3, 28]), 'cur_cost': 125688.0}, {'tour': array([18, 31, 14,  4, 27,  6, 19, 29, 21,  2, 38,  9, 13, 28,  8, 30,  0,
       25, 20, 16, 26,  5, 22, 10, 15, 11, 24, 12,  7, 37, 23, 32,  1,  3,
       35, 33, 34, 17, 36]), 'cur_cost': 148837.0}, {'tour': array([34, 17, 13,  7, 23, 20, 32, 15, 25, 33, 36,  2, 28, 12, 35,  5, 26,
        0, 24,  3,  6, 14, 37, 29,  1, 16, 19, 18, 31, 21, 38, 22, 10, 27,
        8, 30,  4, 11,  9]), 'cur_cost': 133568.0}]
2025-06-26 18:06:08,931 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:06:08,931 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 209, 'skip_rate': 0.0430622009569378, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 200, 'cache_hits': 172, 'similarity_calculations': 3516, 'cache_hit_rate': 0.048919226393629126, 'cache_size': 3344}}
2025-06-26 18:06:08,931 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:06:08,931 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:06:08,933 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:06:08,933 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:06:08,933 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 136494.0
2025-06-26 18:06:09,437 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:06:09,437 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:06:09,437 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:06:09,445 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:06:09,445 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 5, 23, 17, 8, 29, 34, 21, 14, 7, 38, 25, 19, 10, 3, 31, 26, 15, 6, 37, 22, 13, 4, 35, 28, 18, 9, 2, 32, 27, 16, 1, 30, 24, 20, 11, 33, 36], 'cur_cost': 152836.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 11, 23, 35, 7, 19, 31], 'cur_cost': 153894.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 38, 34, 30, 26, 21, 17, 13, 9, 4, 1, 6, 10, 14, 19, 23, 27, 31, 35, 32, 28, 24, 20, 16, 11, 7, 2, 8, 15, 3, 36], 'cur_cost': 129911.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([13, 12, 30, 16, 37, 26,  9, 23, 24, 35,  6, 19, 15, 34, 18,  0,  4,
       36, 10, 11,  1,  5, 32, 33, 31, 29,  3,  8, 14,  7, 22, 28, 25, 27,
       21, 17, 38, 20,  2]), 'cur_cost': 125620.0}, {'tour': array([30, 14, 26,  1, 33, 38, 16, 24, 15, 19, 13, 22,  6, 10, 23,  7,  5,
       36,  8, 20, 29, 27, 31, 12, 17,  4,  2, 35,  0, 18, 32,  9, 25, 21,
       37, 11, 34,  3, 28]), 'cur_cost': 125688.0}, {'tour': array([18, 31, 14,  4, 27,  6, 19, 29, 21,  2, 38,  9, 13, 28,  8, 30,  0,
       25, 20, 16, 26,  5, 22, 10, 15, 11, 24, 12,  7, 37, 23, 32,  1,  3,
       35, 33, 34, 17, 36]), 'cur_cost': 148837.0}, {'tour': array([17, 18, 14, 12, 24, 35,  2, 29, 27,  1, 37, 10, 30, 28, 19,  3, 32,
        0, 13, 23, 16,  4, 34, 38,  9,  8, 36, 20, 31,  6, 15, 25, 33, 11,
       22,  7, 26, 21,  5]), 'cur_cost': 136494.0}]
2025-06-26 18:06:09,446 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:06:09,447 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 210, 'skip_rate': 0.04285714285714286, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 201, 'cache_hits': 172, 'similarity_calculations': 3527, 'cache_hit_rate': 0.048766657215764106, 'cache_size': 3355}}
2025-06-26 18:06:09,447 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:06:09,447 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 23, 17, 8, 29, 34, 21, 14, 7, 38, 25, 19, 10, 3, 31, 26, 15, 6, 37, 22, 13, 4, 35, 28, 18, 9, 2, 32, 27, 16, 1, 30, 24, 20, 11, 33, 36], 'cur_cost': 152836.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 8, 20, 32, 4, 16, 28, 1, 13, 25, 37, 9, 21, 33, 5, 17, 29, 2, 14, 26, 38, 10, 22, 34, 6, 18, 30, 3, 15, 27, 11, 23, 35, 7, 19, 31], 'cur_cost': 153894.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 38, 34, 30, 26, 21, 17, 13, 9, 4, 1, 6, 10, 14, 19, 23, 27, 31, 35, 32, 28, 24, 20, 16, 11, 7, 2, 8, 15, 3, 36], 'cur_cost': 129911.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}}]
2025-06-26 18:06:09,447 - __main__ - INFO - 进化阶段完成
2025-06-26 18:06:09,448 - __main__ - INFO - 开始评估阶段
2025-06-26 18:06:09,448 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:06:09,449 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:06:09,449 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 133568.0 → 125620.0 (Δ 7948.0)
- Mean cost: 150422.8 → 148208.9
- Diversity: 0.729 → 0.637 (Δ -0.0921)

#### Elite metrics
- Elite count: 22 → 22
- Best elite cost: 23763 → 23763 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ 1749.17
- Exploit: 4 inds, success 50.0% , avg Δ 2911.0

#### Other indicators
- No-change individuals: 1
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [25626.0, 99331.0, 133568.0], diversity [0.8939682539682541, 0.9321937321937324, 0.9236467236467237]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:06:09,449 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:06:22,823 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_improvement": 1749.17,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_improvement": 2911.0,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "exploit-heavy",
    "diversity_trend": "declining",
    "stagnation_risk": "moderate"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators to boost diversity"
  }
}
```
2025-06-26 18:06:22,837 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:06:22,837 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_improvement": 1749.17,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_improvement": 2911.0,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "exploit-heavy",
    "diversity_trend": "declining",
    "stagnation_risk": "moderate"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators to boost diversity"
  }
}
```
2025-06-26 18:06:22,837 - __main__ - INFO - 评估阶段完成
2025-06-26 18:06:22,837 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 16.7,
      "average_improvement": 1749.17,
      "contribution_to_elite": "low"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_improvement": 2911.0,
      "contribution_to_elite": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "exploit-heavy",
    "diversity_trend": "declining",
    "stagnation_risk": "moderate"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators to boost diversity"
  }
}
```
2025-06-26 18:06:22,840 - __main__ - INFO - 当前最佳适应度: 125620.0
2025-06-26 18:06:22,841 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite6_39_route_2.pkl
2025-06-26 18:06:22,842 - __main__ - INFO - composite6_39 开始进化第 4 代
2025-06-26 18:06:22,842 - __main__ - INFO - 开始分析阶段
2025-06-26 18:06:22,842 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:06:22,849 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 125620.0, 'max': 169603.0, 'mean': 148208.9, 'std': 17078.491844715096}, 'diversity': 0.8438746438746438, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:06:22,850 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 125620.0, 'max': 169603.0, 'mean': 148208.9, 'std': 17078.491844715096}, 'diversity_level': 0.8438746438746438, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[3156, 1356], [3163, 1390], [3179, 1354], [3088, 1415], [3102, 1355], [5793, 2546], [5722, 2537], [5782, 2504], [5772, 2546], [5772, 2556], [7573, 838], [7596, 888], [7564, 889], [7543, 806], [7601, 818], [2523, 6168], [2536, 6136], [2509, 6132], [2562, 6118], [2569, 6184], [4199, 3605], [4160, 3676], [4171, 3694], [4189, 3693], [4214, 3700], [4165, 3649], [1085, 953], [1060, 995], [1017, 984], [991, 941], [1019, 1006], [1065, 979], [6688, 4970], [6614, 5024], [6615, 5042], [6684, 5006], [6654, 5018], [6682, 5047], [6659, 5001]], 'distance_matrix': array([[   0.,   35.,   23., ..., 5064., 5105., 5055.],
       [  35.,    0.,   39., ..., 5035., 5075., 5026.],
       [  23.,   39.,    0., ..., 5050., 5090., 5041.],
       ...,
       [5064., 5035., 5050., ...,    0.,   40.,   18.],
       [5105., 5075., 5090., ...,   40.,    0.,   51.],
       [5055., 5026., 5041., ...,   18.,   51.,    0.]])}
2025-06-26 18:06:22,850 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:06:22,850 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:06:22,850 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:06:22,852 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:06:22,852 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:06:22,852 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:06:22,852 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:06:22,852 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:06:22,853 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:06:22,858 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:06:22,858 - __main__ - INFO - 精英专家分析报告: {'elite_count': 22, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 101857.0, 'avg_gap': 124443.62727272726}, 'structure_gap': {'unique_elite_edges': 98, 'unique_pop_edges': 237, 'common_edges': 28}}, 'elite_diversity': {'diversity_score': 0.2579642579642579}}
2025-06-26 18:06:22,858 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:06:22,858 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:06:22,858 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:06:22,861 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 125620.0, mean 148208.9, max 169603.0, std 17078.491844715096
- diversity: 0.8438746438746438
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [99331.0, 133568.0, 125620.0] (Δ 3974.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [991.0, 806.0, 7601.0, 6184.0]
- centroid/spread: [4436.821, 3013.308] | rms 2915.063
- density_grid(3x3): [[11, 0, 10], [0, 6, 0], [5, 0, 7]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 2516.0, 4182.0, 4954.0, 7376.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [4, 4, 4, 6, 4, 7, 5, 4]
- nn_median_dist: 27.295

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:06:22,861 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:06:46,408 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation)",
    "modality": "Potentially multimodal (direction histogram shows varied edge directions, no single dominant pattern)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 indicates some long edges that may mislead local search)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (diversity 0.84, density_grid shows nodes spread across most cells)",
    "convergence": "Early stage (convergence 0.0, recent best costs show improvement Δ3974 per iteration)",
    "elite_influence": "Minimal (elite_count 0, no common edges or features detected)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Edges in max length quartile (4954.0-7376.0)",
    "low_density_cells": "Cells with 0 nodes in density_grid (positions [0,1], [1,0], [1,2])"
  },
  "opportunity_regions": {
    "high_density_cells": "Cells with >5 nodes (positions [0,0], [0,2], [2,0], [2,2])",
    "short_edge_clusters": "Edges in min length quartile (10.0-2516.0)"
  },
  "evolution_phase": "Exploration (high diversity, improving costs, no convergence)",
  "evolution_direction": [
    "Prioritize 2-opt/local search in high-density cells to exploit promising regions",
    "Apply controlled mutation to escape low-density cells",
    "Introduce path-relinking between distant clusters to address long edges",
    "Maintain diversity through restart mechanisms if stagnation occurs"
  ]
}
```
2025-06-26 18:06:46,408 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:06:46,408 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation)', 'modality': 'Potentially multimodal (direction histogram shows varied edge directions, no single dominant pattern)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 indicates some long edges that may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.84, density_grid shows nodes spread across most cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs show improvement Δ3974 per iteration)', 'elite_influence': 'Minimal (elite_count 0, no common edges or features detected)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max length quartile (4954.0-7376.0)', 'low_density_cells': 'Cells with 0 nodes in density_grid (positions [0,1], [1,0], [1,2])'}, 'opportunity_regions': {'high_density_cells': 'Cells with >5 nodes (positions [0,0], [0,2], [2,0], [2,2])', 'short_edge_clusters': 'Edges in min length quartile (10.0-2516.0)'}, 'evolution_phase': 'Exploration (high diversity, improving costs, no convergence)', 'evolution_direction': ['Prioritize 2-opt/local search in high-density cells to exploit promising regions', 'Apply controlled mutation to escape low-density cells', 'Introduce path-relinking between distant clusters to address long edges', 'Maintain diversity through restart mechanisms if stagnation occurs']}
2025-06-26 18:06:46,408 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:06:46,408 - __main__ - INFO - 分析阶段完成
2025-06-26 18:06:46,408 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation)', 'modality': 'Potentially multimodal (direction histogram shows varied edge directions, no single dominant pattern)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 indicates some long edges that may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (diversity 0.84, density_grid shows nodes spread across most cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs show improvement Δ3974 per iteration)', 'elite_influence': 'Minimal (elite_count 0, no common edges or features detected)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max length quartile (4954.0-7376.0)', 'low_density_cells': 'Cells with 0 nodes in density_grid (positions [0,1], [1,0], [1,2])'}, 'opportunity_regions': {'high_density_cells': 'Cells with >5 nodes (positions [0,0], [0,2], [2,0], [2,2])', 'short_edge_clusters': 'Edges in min length quartile (10.0-2516.0)'}, 'evolution_phase': 'Exploration (high diversity, improving costs, no convergence)', 'evolution_direction': ['Prioritize 2-opt/local search in high-density cells to exploit promising regions', 'Apply controlled mutation to escape low-density cells', 'Introduce path-relinking between distant clusters to address long edges', 'Maintain diversity through restart mechanisms if stagnation occurs']}
2025-06-26 18:06:46,408 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:06:46,408 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:06:46,408 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 125620.0
  • mean_cost: 148208.9
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation), modality Potentially multimodal (direction histogram shows varied edge directions, no single dominant pattern), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 indicates some long edges that may mislead local search)
- Evolution phase: Exploration (high diversity, improving costs, no convergence)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_cells
- Opportunity regions (# 2): high_density_cells, short_edge_clusters
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:06:46,408 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:06:46,408 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:06:55,814 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration (70%). Top performers exploit opportunity regions, others explore difficult areas."
}
```
2025-06-26 18:06:55,814 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 18:06:55,814 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 18:06:55,822 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration (70%). Top performers exploit opportunity regions, others explore difficult areas."
}
```
2025-06-26 18:06:55,822 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:06:55,822 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-06-26 18:06:55,822 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration phase suggest prioritizing exploration (70%). Top performers exploit opportunity regions, others explore difficult areas."
}
```
2025-06-26 18:06:55,822 - __main__ - INFO - 开始进化阶段
2025-06-26 18:06:55,822 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:06:55,822 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:06:55,822 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 169603.0 (rank 70.0%)
- Population diversity: 0.6368421052631579
- Evolution phase: Exploration (high diversity, improving costs, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:06:55,822 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:06:55,822 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:07:07,022 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 17, 23, 29, 8, 34, 21, 15, 3, 27, 38, 11, 25, 7, 31, 14, 37, 20, 2, 16, 30, 9, 33, 18, 6, 24, 10, 35, 22, 4, 28, 13, 36, 19, 1, 32, 26],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters with short edges, skip long corridors"
}
```
2025-06-26 18:07:07,024 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:07:07,024 - ExplorationExpert - INFO - 探索路径生成完成，成本: 156680.0, 路径: [0, 12, 5, 17, 23, 29, 8, 34, 21, 15, 3, 27, 38, 11, 25, 7, 31, 14, 37, 20, 2, 16, 30, 9, 33, 18, 6, 24, 10, 35, 22, 4, 28, 13, 36, 19, 1, 32, 26]
2025-06-26 18:07:07,024 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 5, 17, 23, 29, 8, 34, 21, 15, 3, 27, 38, 11, 25, 7, 31, 14, 37, 20, 2, 16, 30, 9, 33, 18, 6, 24, 10, 35, 22, 4, 28, 13, 36, 19, 1, 32, 26], 'cur_cost': 156680.0}
2025-06-26 18:07:07,026 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:07:07,026 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:07:07,026 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 152836.0 (rank 50.0%)
- Population diversity: 0.7026315789473684
- Evolution phase: Exploration (high diversity, improving costs, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:07:07,026 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:07:07,027 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:07:17,905 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 22, 7, 29, 15, 3, 10, 24, 8, 17, 31, 13, 26, 9, 20, 34, 1, 14, 27, 6, 21, 35, 2, 16, 30, 4, 19, 33, 11, 25, 38, 23, 37, 28, 36, 32],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters with short edges, skip long corridors"
}
```
2025-06-26 18:07:17,910 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:07:17,910 - ExplorationExpert - INFO - 探索路径生成完成，成本: 163821.0, 路径: [0, 12, 5, 18, 22, 7, 29, 15, 3, 10, 24, 8, 17, 31, 13, 26, 9, 20, 34, 1, 14, 27, 6, 21, 35, 2, 16, 30, 4, 19, 33, 11, 25, 38, 23, 37, 28, 36, 32]
2025-06-26 18:07:17,912 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 22, 7, 29, 15, 3, 10, 24, 8, 17, 31, 13, 26, 9, 20, 34, 1, 14, 27, 6, 21, 35, 2, 16, 30, 4, 19, 33, 11, 25, 38, 23, 37, 28, 36, 32], 'cur_cost': 163821.0}
2025-06-26 18:07:17,912 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:07:17,912 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:07:17,913 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 169603.0 (rank 80.0%)
- Population diversity: 0.6921052631578948
- Evolution phase: Exploration (high diversity, improving costs, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:07:17,913 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:07:17,913 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:07:29,530 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 31, 10, 21, 35, 8, 23, 36, 1, 13, 27, 6, 19, 32, 11, 25, 3, 16, 30, 20, 34, 28],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters with short edges, skip long corridors"
}
```
2025-06-26 18:07:29,530 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:07:29,530 - ExplorationExpert - INFO - 探索路径生成完成，成本: 165200.0, 路径: [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 31, 10, 21, 35, 8, 23, 36, 1, 13, 27, 6, 19, 32, 11, 25, 3, 16, 30, 20, 34, 28]
2025-06-26 18:07:29,530 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 31, 10, 21, 35, 8, 23, 36, 1, 13, 27, 6, 19, 32, 11, 25, 3, 16, 30, 20, 34, 28], 'cur_cost': 165200.0}
2025-06-26 18:07:29,530 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-26 18:07:29,538 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:07:29,538 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:07:29,538 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 149755.0
2025-06-26 18:07:30,040 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:07:30,040 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:07:30,041 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:07:30,048 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:07:30,048 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 17, 23, 29, 8, 34, 21, 15, 3, 27, 38, 11, 25, 7, 31, 14, 37, 20, 2, 16, 30, 9, 33, 18, 6, 24, 10, 35, 22, 4, 28, 13, 36, 19, 1, 32, 26], 'cur_cost': 156680.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 15, 3, 10, 24, 8, 17, 31, 13, 26, 9, 20, 34, 1, 14, 27, 6, 21, 35, 2, 16, 30, 4, 19, 33, 11, 25, 38, 23, 37, 28, 36, 32], 'cur_cost': 163821.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 31, 10, 21, 35, 8, 23, 36, 1, 13, 27, 6, 19, 32, 11, 25, 3, 16, 30, 20, 34, 28], 'cur_cost': 165200.0}, {'tour': array([10, 30, 28,  6, 22, 17, 13, 31,  9, 29, 27, 38,  8,  2, 14, 34, 19,
       26, 35, 32,  1,  5, 18, 11,  4, 25, 20, 23, 15,  0, 33, 16, 37, 12,
       36, 24,  3,  7, 21]), 'cur_cost': 149755.0}, {'tour': [0, 5, 12, 18, 22, 25, 29, 33, 37, 38, 34, 30, 26, 21, 17, 13, 9, 4, 1, 6, 10, 14, 19, 23, 27, 31, 35, 32, 28, 24, 20, 16, 11, 7, 2, 8, 15, 3, 36], 'cur_cost': 129911.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([13, 12, 30, 16, 37, 26,  9, 23, 24, 35,  6, 19, 15, 34, 18,  0,  4,
       36, 10, 11,  1,  5, 32, 33, 31, 29,  3,  8, 14,  7, 22, 28, 25, 27,
       21, 17, 38, 20,  2]), 'cur_cost': 125620.0}, {'tour': array([30, 14, 26,  1, 33, 38, 16, 24, 15, 19, 13, 22,  6, 10, 23,  7,  5,
       36,  8, 20, 29, 27, 31, 12, 17,  4,  2, 35,  0, 18, 32,  9, 25, 21,
       37, 11, 34,  3, 28]), 'cur_cost': 125688.0}, {'tour': array([18, 31, 14,  4, 27,  6, 19, 29, 21,  2, 38,  9, 13, 28,  8, 30,  0,
       25, 20, 16, 26,  5, 22, 10, 15, 11, 24, 12,  7, 37, 23, 32,  1,  3,
       35, 33, 34, 17, 36]), 'cur_cost': 148837.0}, {'tour': array([17, 18, 14, 12, 24, 35,  2, 29, 27,  1, 37, 10, 30, 28, 19,  3, 32,
        0, 13, 23, 16,  4, 34, 38,  9,  8, 36, 20, 31,  6, 15, 25, 33, 11,
       22,  7, 26, 21,  5]), 'cur_cost': 136494.0}]
2025-06-26 18:07:30,051 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:07:30,051 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 211, 'skip_rate': 0.04265402843601896, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 202, 'cache_hits': 172, 'similarity_calculations': 3539, 'cache_hit_rate': 0.04860129980220401, 'cache_size': 3367}}
2025-06-26 18:07:30,051 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-26 18:07:30,051 - __main__ - INFO - 为个体 4 生成利用路径
2025-06-26 18:07:30,051 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:07:30,051 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:07:30,051 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 131567.0
2025-06-26 18:07:30,554 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:07:30,554 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:07:30,554 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:07:30,562 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:07:30,562 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 17, 23, 29, 8, 34, 21, 15, 3, 27, 38, 11, 25, 7, 31, 14, 37, 20, 2, 16, 30, 9, 33, 18, 6, 24, 10, 35, 22, 4, 28, 13, 36, 19, 1, 32, 26], 'cur_cost': 156680.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 15, 3, 10, 24, 8, 17, 31, 13, 26, 9, 20, 34, 1, 14, 27, 6, 21, 35, 2, 16, 30, 4, 19, 33, 11, 25, 38, 23, 37, 28, 36, 32], 'cur_cost': 163821.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 31, 10, 21, 35, 8, 23, 36, 1, 13, 27, 6, 19, 32, 11, 25, 3, 16, 30, 20, 34, 28], 'cur_cost': 165200.0}, {'tour': array([10, 30, 28,  6, 22, 17, 13, 31,  9, 29, 27, 38,  8,  2, 14, 34, 19,
       26, 35, 32,  1,  5, 18, 11,  4, 25, 20, 23, 15,  0, 33, 16, 37, 12,
       36, 24,  3,  7, 21]), 'cur_cost': 149755.0}, {'tour': array([32, 30,  3, 35, 25, 27, 29, 16, 15, 11,  8, 33, 13,  9, 10, 22, 24,
       17, 20,  1, 21, 18, 12, 14, 37, 38,  6, 34, 28, 23,  5,  2, 19, 26,
        7, 36,  0,  4, 31]), 'cur_cost': 131567.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([13, 12, 30, 16, 37, 26,  9, 23, 24, 35,  6, 19, 15, 34, 18,  0,  4,
       36, 10, 11,  1,  5, 32, 33, 31, 29,  3,  8, 14,  7, 22, 28, 25, 27,
       21, 17, 38, 20,  2]), 'cur_cost': 125620.0}, {'tour': array([30, 14, 26,  1, 33, 38, 16, 24, 15, 19, 13, 22,  6, 10, 23,  7,  5,
       36,  8, 20, 29, 27, 31, 12, 17,  4,  2, 35,  0, 18, 32,  9, 25, 21,
       37, 11, 34,  3, 28]), 'cur_cost': 125688.0}, {'tour': array([18, 31, 14,  4, 27,  6, 19, 29, 21,  2, 38,  9, 13, 28,  8, 30,  0,
       25, 20, 16, 26,  5, 22, 10, 15, 11, 24, 12,  7, 37, 23, 32,  1,  3,
       35, 33, 34, 17, 36]), 'cur_cost': 148837.0}, {'tour': array([17, 18, 14, 12, 24, 35,  2, 29, 27,  1, 37, 10, 30, 28, 19,  3, 32,
        0, 13, 23, 16,  4, 34, 38,  9,  8, 36, 20, 31,  6, 15, 25, 33, 11,
       22,  7, 26, 21,  5]), 'cur_cost': 136494.0}]
2025-06-26 18:07:30,565 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:07:30,565 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 212, 'skip_rate': 0.04245283018867924, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 203, 'cache_hits': 172, 'similarity_calculations': 3552, 'cache_hit_rate': 0.04842342342342342, 'cache_size': 3380}}
2025-06-26 18:07:30,565 - __main__ - WARNING - 个体 4 的利用路径生成失败，保留原个体
2025-06-26 18:07:30,565 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:07:30,565 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:07:30,565 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 169603.0 (rank 90.0%)
- Population diversity: 0.7710526315789473
- Evolution phase: Exploration (high diversity, improving costs, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:07:30,566 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:07:30,567 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:07:42,451 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 8, 17, 21, 25, 29, 32, 35, 38, 36, 33, 30, 27, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 31, 34, 37, 28, 23, 19, 15, 11, 7, 3],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters, minimize long edges, explore adjacent sparse cells"
}
```
2025-06-26 18:07:42,451 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:07:42,451 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106620.0, 路径: [0, 12, 5, 8, 17, 21, 25, 29, 32, 35, 38, 36, 33, 30, 27, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 31, 34, 37, 28, 23, 19, 15, 11, 7, 3]
2025-06-26 18:07:42,451 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 5, 8, 17, 21, 25, 29, 32, 35, 38, 36, 33, 30, 27, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 31, 34, 37, 28, 23, 19, 15, 11, 7, 3], 'cur_cost': 106620.0}
2025-06-26 18:07:42,451 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:07:42,451 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:07:42,451 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 125620.0 (rank 10.0%)
- Population diversity: 0.7763157894736842
- Evolution phase: Exploration (high diversity, improving costs, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:07:42,451 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:07:42,451 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:07:52,654 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 8, 17, 21, 25, 29, 33, 37, 1, 6, 13, 9, 18, 22, 26, 30, 34, 38, 2, 7, 14, 10, 19, 23, 27, 31, 35, 3, 11, 15, 20, 24, 28, 32, 36, 4, 16],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters with short edges, skip long corridors"
}
```
2025-06-26 18:07:52,654 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:07:52,654 - ExplorationExpert - INFO - 探索路径生成完成，成本: 136324.0, 路径: [0, 5, 12, 8, 17, 21, 25, 29, 33, 37, 1, 6, 13, 9, 18, 22, 26, 30, 34, 38, 2, 7, 14, 10, 19, 23, 27, 31, 35, 3, 11, 15, 20, 24, 28, 32, 36, 4, 16]
2025-06-26 18:07:52,654 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 5, 12, 8, 17, 21, 25, 29, 33, 37, 1, 6, 13, 9, 18, 22, 26, 30, 34, 38, 2, 7, 14, 10, 19, 23, 27, 31, 35, 3, 11, 15, 20, 24, 28, 32, 36, 4, 16], 'cur_cost': 136324.0}
2025-06-26 18:07:52,654 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:07:52,654 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:07:52,654 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:07:52,654 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 142199.0
2025-06-26 18:07:53,158 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:07:53,158 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:07:53,160 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:07:53,167 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:07:53,167 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 17, 23, 29, 8, 34, 21, 15, 3, 27, 38, 11, 25, 7, 31, 14, 37, 20, 2, 16, 30, 9, 33, 18, 6, 24, 10, 35, 22, 4, 28, 13, 36, 19, 1, 32, 26], 'cur_cost': 156680.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 15, 3, 10, 24, 8, 17, 31, 13, 26, 9, 20, 34, 1, 14, 27, 6, 21, 35, 2, 16, 30, 4, 19, 33, 11, 25, 38, 23, 37, 28, 36, 32], 'cur_cost': 163821.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 31, 10, 21, 35, 8, 23, 36, 1, 13, 27, 6, 19, 32, 11, 25, 3, 16, 30, 20, 34, 28], 'cur_cost': 165200.0}, {'tour': array([10, 30, 28,  6, 22, 17, 13, 31,  9, 29, 27, 38,  8,  2, 14, 34, 19,
       26, 35, 32,  1,  5, 18, 11,  4, 25, 20, 23, 15,  0, 33, 16, 37, 12,
       36, 24,  3,  7, 21]), 'cur_cost': 149755.0}, {'tour': array([32, 30,  3, 35, 25, 27, 29, 16, 15, 11,  8, 33, 13,  9, 10, 22, 24,
       17, 20,  1, 21, 18, 12, 14, 37, 38,  6, 34, 28, 23,  5,  2, 19, 26,
        7, 36,  0,  4, 31]), 'cur_cost': 131567.0}, {'tour': [0, 12, 5, 8, 17, 21, 25, 29, 32, 35, 38, 36, 33, 30, 27, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 31, 34, 37, 28, 23, 19, 15, 11, 7, 3], 'cur_cost': 106620.0}, {'tour': [0, 5, 12, 8, 17, 21, 25, 29, 33, 37, 1, 6, 13, 9, 18, 22, 26, 30, 34, 38, 2, 7, 14, 10, 19, 23, 27, 31, 35, 3, 11, 15, 20, 24, 28, 32, 36, 4, 16], 'cur_cost': 136324.0}, {'tour': array([19, 26, 33, 38, 11, 13,  2, 16, 22, 34,  0,  7,  5, 37, 15,  8, 12,
       20, 27, 23, 35,  3, 17,  4, 32,  6, 31, 24, 30, 18, 36,  9, 10, 25,
       21, 29, 28,  1, 14]), 'cur_cost': 142199.0}, {'tour': array([18, 31, 14,  4, 27,  6, 19, 29, 21,  2, 38,  9, 13, 28,  8, 30,  0,
       25, 20, 16, 26,  5, 22, 10, 15, 11, 24, 12,  7, 37, 23, 32,  1,  3,
       35, 33, 34, 17, 36]), 'cur_cost': 148837.0}, {'tour': array([17, 18, 14, 12, 24, 35,  2, 29, 27,  1, 37, 10, 30, 28, 19,  3, 32,
        0, 13, 23, 16,  4, 34, 38,  9,  8, 36, 20, 31,  6, 15, 25, 33, 11,
       22,  7, 26, 21,  5]), 'cur_cost': 136494.0}]
2025-06-26 18:07:53,169 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 18:07:53,169 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 213, 'skip_rate': 0.04225352112676056, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 204, 'cache_hits': 172, 'similarity_calculations': 3566, 'cache_hit_rate': 0.04823331463825014, 'cache_size': 3394}}
2025-06-26 18:07:53,169 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:07:53,169 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-26 18:07:53,170 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:07:53,170 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 148837.0 (rank 50.0%)
- Population diversity: 0.7552631578947369
- Evolution phase: Exploration (high diversity, improving costs, no convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, short_edge_clus
- Difficult regions to avoid (sample): long_edge_corri, low_density_cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:07:53,171 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:07:53,171 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:08:04,345 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 25, 30, 35, 38, 37, 36, 34, 33, 31, 29, 28, 27, 26, 24, 23, 22, 21, 20, 18, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 6, 4, 3, 2, 1, 32],
  "targeted_regions": "high_density_ce, short_edge_clus",
  "strategy_comment": "Prioritize dense clusters with short edges, spiral outward"
}
```
2025-06-26 18:08:04,346 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:08:04,346 - ExplorationExpert - INFO - 探索路径生成完成，成本: 64696.0, 路径: [0, 5, 12, 19, 25, 30, 35, 38, 37, 36, 34, 33, 31, 29, 28, 27, 26, 24, 23, 22, 21, 20, 18, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 6, 4, 3, 2, 1, 32]
2025-06-26 18:08:04,346 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 25, 30, 35, 38, 37, 36, 34, 33, 31, 29, 28, 27, 26, 24, 23, 22, 21, 20, 18, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 6, 4, 3, 2, 1, 32], 'cur_cost': 64696.0}
2025-06-26 18:08:04,346 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:08:04,346 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:08:04,346 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:08:04,346 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 161563.0
2025-06-26 18:08:04,850 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:08:04,850 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:08:04,850 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:08:04,858 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:08:04,858 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 5, 17, 23, 29, 8, 34, 21, 15, 3, 27, 38, 11, 25, 7, 31, 14, 37, 20, 2, 16, 30, 9, 33, 18, 6, 24, 10, 35, 22, 4, 28, 13, 36, 19, 1, 32, 26], 'cur_cost': 156680.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 15, 3, 10, 24, 8, 17, 31, 13, 26, 9, 20, 34, 1, 14, 27, 6, 21, 35, 2, 16, 30, 4, 19, 33, 11, 25, 38, 23, 37, 28, 36, 32], 'cur_cost': 163821.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 31, 10, 21, 35, 8, 23, 36, 1, 13, 27, 6, 19, 32, 11, 25, 3, 16, 30, 20, 34, 28], 'cur_cost': 165200.0}, {'tour': array([10, 30, 28,  6, 22, 17, 13, 31,  9, 29, 27, 38,  8,  2, 14, 34, 19,
       26, 35, 32,  1,  5, 18, 11,  4, 25, 20, 23, 15,  0, 33, 16, 37, 12,
       36, 24,  3,  7, 21]), 'cur_cost': 149755.0}, {'tour': array([32, 30,  3, 35, 25, 27, 29, 16, 15, 11,  8, 33, 13,  9, 10, 22, 24,
       17, 20,  1, 21, 18, 12, 14, 37, 38,  6, 34, 28, 23,  5,  2, 19, 26,
        7, 36,  0,  4, 31]), 'cur_cost': 131567.0}, {'tour': [0, 12, 5, 8, 17, 21, 25, 29, 32, 35, 38, 36, 33, 30, 27, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 31, 34, 37, 28, 23, 19, 15, 11, 7, 3], 'cur_cost': 106620.0}, {'tour': [0, 5, 12, 8, 17, 21, 25, 29, 33, 37, 1, 6, 13, 9, 18, 22, 26, 30, 34, 38, 2, 7, 14, 10, 19, 23, 27, 31, 35, 3, 11, 15, 20, 24, 28, 32, 36, 4, 16], 'cur_cost': 136324.0}, {'tour': array([19, 26, 33, 38, 11, 13,  2, 16, 22, 34,  0,  7,  5, 37, 15,  8, 12,
       20, 27, 23, 35,  3, 17,  4, 32,  6, 31, 24, 30, 18, 36,  9, 10, 25,
       21, 29, 28,  1, 14]), 'cur_cost': 142199.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 38, 37, 36, 34, 33, 31, 29, 28, 27, 26, 24, 23, 22, 21, 20, 18, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 6, 4, 3, 2, 1, 32], 'cur_cost': 64696.0}, {'tour': array([28,  7, 38, 23, 27, 33, 15, 19,  2, 36, 26, 21,  3, 16, 10,  5, 24,
        9, 17, 35, 30, 14, 25, 22, 12, 20, 31, 13,  6, 18,  1,  8, 32,  0,
       29, 37, 11, 34,  4]), 'cur_cost': 161563.0}]
2025-06-26 18:08:04,860 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:08:04,860 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 214, 'skip_rate': 0.04205607476635514, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 205, 'cache_hits': 172, 'similarity_calculations': 3581, 'cache_hit_rate': 0.04803127617983803, 'cache_size': 3409}}
2025-06-26 18:08:04,860 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:08:04,860 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 17, 23, 29, 8, 34, 21, 15, 3, 27, 38, 11, 25, 7, 31, 14, 37, 20, 2, 16, 30, 9, 33, 18, 6, 24, 10, 35, 22, 4, 28, 13, 36, 19, 1, 32, 26], 'cur_cost': 156680.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 22, 7, 29, 15, 3, 10, 24, 8, 17, 31, 13, 26, 9, 20, 34, 1, 14, 27, 6, 21, 35, 2, 16, 30, 4, 19, 33, 11, 25, 38, 23, 37, 28, 36, 32], 'cur_cost': 163821.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 31, 10, 21, 35, 8, 23, 36, 1, 13, 27, 6, 19, 32, 11, 25, 3, 16, 30, 20, 34, 28], 'cur_cost': 165200.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 8, 17, 21, 25, 29, 32, 35, 38, 36, 33, 30, 27, 24, 20, 16, 13, 9, 4, 1, 2, 6, 10, 14, 18, 22, 26, 31, 34, 37, 28, 23, 19, 15, 11, 7, 3], 'cur_cost': 106620.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 8, 17, 21, 25, 29, 33, 37, 1, 6, 13, 9, 18, 22, 26, 30, 34, 38, 2, 7, 14, 10, 19, 23, 27, 31, 35, 3, 11, 15, 20, 24, 28, 32, 36, 4, 16], 'cur_cost': 136324.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 25, 30, 35, 38, 37, 36, 34, 33, 31, 29, 28, 27, 26, 24, 23, 22, 21, 20, 18, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 6, 4, 3, 2, 1, 32], 'cur_cost': 64696.0}}]
2025-06-26 18:08:04,860 - __main__ - INFO - 进化阶段完成
2025-06-26 18:08:04,862 - __main__ - INFO - 开始评估阶段
2025-06-26 18:08:04,862 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:08:04,862 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:08:04,862 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 125620.0 → 64696.0 (Δ 60924.0)
- Mean cost: 148208.9 → 137842.5
- Diversity: 0.637 → 0.774 (Δ 0.1368)

#### Elite metrics
- Elite count: 22 → 22
- Best elite cost: 23763 → 23763 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 66.7% , avg Δ 23793.5
- Exploit: 4 inds, success 25.0% , avg Δ -9774.25

#### Other indicators
- No-change individuals: 0
- Regression individuals: 5
- Historical trends: Last 3 iterations: costs [99331.0, 133568.0, 125620.0], diversity [0.9321937321937324, 0.9236467236467237, 0.8438746438746438]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:08:04,863 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:08:18,075 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_improvement": 23793.5,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -9774.25,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy (effective but could benefit from more exploitation if quality improves)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_quality_control": "add cost threshold for exploitation attempts (e.g., only exploit solutions within 20% of current elite)",
    "mutation_intensity": "consider increasing for exploitation to escape local minima"
  }
}
```
2025-06-26 18:08:18,081 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:08:18,081 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_improvement": 23793.5,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -9774.25,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy (effective but could benefit from more exploitation if quality improves)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_quality_control": "add cost threshold for exploitation attempts (e.g., only exploit solutions within 20% of current elite)",
    "mutation_intensity": "consider increasing for exploitation to escape local minima"
  }
}
```
2025-06-26 18:08:18,087 - __main__ - INFO - 评估阶段完成
2025-06-26 18:08:18,087 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 66.7,
      "average_improvement": 23793.5,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 25.0,
      "average_improvement": -9774.25,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore-heavy (effective but could benefit from more exploitation if quality improves)",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "exploit_quality_control": "add cost threshold for exploitation attempts (e.g., only exploit solutions within 20% of current elite)",
    "mutation_intensity": "consider increasing for exploitation to escape local minima"
  }
}
```
2025-06-26 18:08:18,088 - __main__ - INFO - 当前最佳适应度: 64696.0
2025-06-26 18:08:18,089 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite6_39_route_3.pkl
2025-06-26 18:08:18,090 - __main__ - INFO - composite6_39 开始进化第 5 代
2025-06-26 18:08:18,090 - __main__ - INFO - 开始分析阶段
2025-06-26 18:08:18,090 - StatsExpert - INFO - 开始统计分析
2025-06-26 18:08:18,097 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 64696.0, 'max': 165200.0, 'mean': 137842.5, 'std': 29780.292568240497}, 'diversity': 0.9310541310541309, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 18:08:18,098 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 64696.0, 'max': 165200.0, 'mean': 137842.5, 'std': 29780.292568240497}, 'diversity_level': 0.9310541310541309, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[3156, 1356], [3163, 1390], [3179, 1354], [3088, 1415], [3102, 1355], [5793, 2546], [5722, 2537], [5782, 2504], [5772, 2546], [5772, 2556], [7573, 838], [7596, 888], [7564, 889], [7543, 806], [7601, 818], [2523, 6168], [2536, 6136], [2509, 6132], [2562, 6118], [2569, 6184], [4199, 3605], [4160, 3676], [4171, 3694], [4189, 3693], [4214, 3700], [4165, 3649], [1085, 953], [1060, 995], [1017, 984], [991, 941], [1019, 1006], [1065, 979], [6688, 4970], [6614, 5024], [6615, 5042], [6684, 5006], [6654, 5018], [6682, 5047], [6659, 5001]], 'distance_matrix': array([[   0.,   35.,   23., ..., 5064., 5105., 5055.],
       [  35.,    0.,   39., ..., 5035., 5075., 5026.],
       [  23.,   39.,    0., ..., 5050., 5090., 5041.],
       ...,
       [5064., 5035., 5050., ...,    0.,   40.,   18.],
       [5105., 5075., 5090., ...,   40.,    0.,   51.],
       [5055., 5026., 5041., ...,   18.,   51.,    0.]])}
2025-06-26 18:08:18,098 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 18:08:18,098 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 18:08:18,099 - PathExpert - INFO - 开始路径结构分析
2025-06-26 18:08:18,101 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 18:08:18,101 - PathExpert - INFO - 路径结构分析完成
2025-06-26 18:08:18,101 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 18:08:18,102 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 18:08:18,102 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 18:08:18,102 - EliteExpert - INFO - 开始精英解分析
2025-06-26 18:08:18,107 - EliteExpert - INFO - 精英解分析完成
2025-06-26 18:08:18,107 - __main__ - INFO - 精英专家分析报告: {'elite_count': 22, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 40933.0, 'avg_gap': 114077.22727272726}, 'structure_gap': {'unique_elite_edges': 77, 'unique_pop_edges': 281, 'common_edges': 49}}, 'elite_diversity': {'diversity_score': 0.2579642579642579}}
2025-06-26 18:08:18,107 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 18:08:18,107 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 18:08:18,109 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 18:08:18,109 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 64696.0, mean 137842.5, max 165200.0, std 29780.292568240497
- diversity: 0.9310541310541309
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [133568.0, 125620.0, 64696.0] (Δ 34436.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [991.0, 806.0, 7601.0, 6184.0]
- centroid/spread: [4436.821, 3013.308] | rms 2915.063
- density_grid(3x3): [[11, 0, 10], [0, 6, 0], [5, 0, 7]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 2516.0, 4182.0, 4954.0, 7376.0]
- long_edge_ratio: 0.25
- direction_hist(8 sectors): [4, 4, 4, 6, 4, 7, 5, 4]
- nn_median_dist: 27.295

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 18:08:18,110 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:08:43,252 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation in costs)",
    "modality": "Potentially multimodal (diverse direction histogram and edge length distribution, no clear single peak in cost_stats)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio of 0.25 suggests some long edges may mislead local search)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (high diversity 0.93, density_grid shows spread across multiple cells)",
    "convergence": "Early stage (convergence 0.0, recent best costs show large Δ 34436.00 per iteration)",
    "elite_influence": "Minimal (elite_count 0, no common edges or features detected)"
  },
  "difficult_regions": [
    "Low-density grid cells (particularly [1,0], [1,2], [2,1])",
    "Long-edge corridors (edges in q3-max range 4954.0-7376.0)"
  ],
  "opportunity_regions": [
    "High-density cells ([0,0] with 11 nodes, [0,2] with 10 nodes, [2,2] with 7 nodes)",
    "Medium-length edges (q1-med range 2516.0-4182.0) showing directional consistency (sectors 3-5 in histogram)"
  ],
  "evolution_phase": "Exploration-dominant (high diversity, no convergence, improving best costs)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Targeted 2-opt around high-density cells to exploit potential clusters",
    "2. Edge recombination favoring medium-length edges (2516-4182 range)",
    "3. Penalize solutions with multiple edges in long-edge corridors",
    "4. Diversity preservation in low-density grid cells through restricted mating"
  ]
}
```
2025-06-26 18:08:43,254 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 18:08:43,254 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation in costs)', 'modality': 'Potentially multimodal (diverse direction histogram and edge length distribution, no clear single peak in cost_stats)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some long edges may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (high diversity 0.93, density_grid shows spread across multiple cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs show large Δ 34436.00 per iteration)', 'elite_influence': 'Minimal (elite_count 0, no common edges or features detected)'}, 'difficult_regions': ['Low-density grid cells (particularly [1,0], [1,2], [2,1])', 'Long-edge corridors (edges in q3-max range 4954.0-7376.0)'], 'opportunity_regions': ['High-density cells ([0,0] with 11 nodes, [0,2] with 10 nodes, [2,2] with 7 nodes)', 'Medium-length edges (q1-med range 2516.0-4182.0) showing directional consistency (sectors 3-5 in histogram)'], 'evolution_phase': 'Exploration-dominant (high diversity, no convergence, improving best costs)', 'evolution_direction': ['Operator suggestions:', '1. Targeted 2-opt around high-density cells to exploit potential clusters', '2. Edge recombination favoring medium-length edges (2516-4182 range)', '3. Penalize solutions with multiple edges in long-edge corridors', '4. Diversity preservation in low-density grid cells through restricted mating']}
2025-06-26 18:08:43,254 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 18:08:43,254 - __main__ - INFO - 分析阶段完成
2025-06-26 18:08:43,255 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation in costs)', 'modality': 'Potentially multimodal (diverse direction histogram and edge length distribution, no clear single peak in cost_stats)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio of 0.25 suggests some long edges may mislead local search)'}, 'population_state': {'coverage': 'Good spatial coverage (high diversity 0.93, density_grid shows spread across multiple cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs show large Δ 34436.00 per iteration)', 'elite_influence': 'Minimal (elite_count 0, no common edges or features detected)'}, 'difficult_regions': ['Low-density grid cells (particularly [1,0], [1,2], [2,1])', 'Long-edge corridors (edges in q3-max range 4954.0-7376.0)'], 'opportunity_regions': ['High-density cells ([0,0] with 11 nodes, [0,2] with 10 nodes, [2,2] with 7 nodes)', 'Medium-length edges (q1-med range 2516.0-4182.0) showing directional consistency (sectors 3-5 in histogram)'], 'evolution_phase': 'Exploration-dominant (high diversity, no convergence, improving best costs)', 'evolution_direction': ['Operator suggestions:', '1. Targeted 2-opt around high-density cells to exploit potential clusters', '2. Edge recombination favoring medium-length edges (2516-4182 range)', '3. Penalize solutions with multiple edges in long-edge corridors', '4. Diversity preservation in low-density grid cells through restricted mating']}
2025-06-26 18:08:43,255 - __main__ - INFO - 开始策略分配阶段
2025-06-26 18:08:43,255 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 18:08:43,255 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 64696.0
  • mean_cost: 137842.5
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 10.0 to 7376.0, with significant standard deviation in costs), modality Potentially multimodal (diverse direction histogram and edge length distribution, no clear single peak in cost_stats), deceptiveness Possible deceptive regions (long_edge_ratio of 0.25 suggests some long edges may mislead local search)
- Evolution phase: Exploration-dominant (high diversity, no convergence, improving best costs)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Low-density grid cel, Long-edge corridors 
- Opportunity regions (# 2): High-density cells (, Medium-length edges 
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "success_r...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 18:08:43,255 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 18:08:43,255 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:08:52,905 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration-dominant phase suggest prioritizing exploration. Top 30% individuals exploit to refine solutions."
}
```
2025-06-26 18:08:52,908 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:08:52,908 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:08:52,908 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration-dominant phase suggest prioritizing exploration. Top 30% individuals exploit to refine solutions."
}
```
2025-06-26 18:08:52,909 - __main__ - INFO - 策略分配阶段完成
2025-06-26 18:08:52,909 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 18:08:52,909 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration-dominant phase suggest prioritizing exploration. Top 30% individuals exploit to refine solutions."
}
```
2025-06-26 18:08:52,909 - __main__ - INFO - 开始进化阶段
2025-06-26 18:08:52,909 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 18:08:52,909 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:08:52,909 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 156680.0 (rank 60.0%)
- Population diversity: 0.7736842105263158
- Evolution phase: Exploration-dominant (high diversity, no convergence, improving best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Medium-length e
- Difficult regions to avoid (sample): Low-density gri, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:08:52,909 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:08:52,909 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:09:04,038 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 17, 22, 27, 32, 37, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 13, 18, 23, 28, 33, 38, 3, 8, 14, 19, 24, 29, 34, 4, 9, 15, 20, 25, 30, 35, 10],
  "targeted_regions": "High-density ce, Medium-length e, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of opportunity regions with spiral pattern"
}
```
2025-06-26 18:09:04,038 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:09:04,038 - ExplorationExpert - INFO - 探索路径生成完成，成本: 157734.0, 路径: [0, 5, 12, 17, 22, 27, 32, 37, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 13, 18, 23, 28, 33, 38, 3, 8, 14, 19, 24, 29, 34, 4, 9, 15, 20, 25, 30, 35, 10]
2025-06-26 18:09:04,038 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 12, 17, 22, 27, 32, 37, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 13, 18, 23, 28, 33, 38, 3, 8, 14, 19, 24, 29, 34, 4, 9, 15, 20, 25, 30, 35, 10], 'cur_cost': 157734.0}
2025-06-26 18:09:04,038 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 18:09:04,038 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:09:04,038 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 163821.0 (rank 80.0%)
- Population diversity: 0.7631578947368421
- Evolution phase: Exploration-dominant (high diversity, no convergence, improving best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Medium-length e
- Difficult regions to avoid (sample): Low-density gri, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:09:04,038 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:09:04,038 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:09:15,449 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 30, 10, 23, 35, 8, 20, 32, 1, 13, 27, 36, 6, 19, 31, 11, 25, 34, 3, 16, 28, 21],
  "targeted_regions": "High-density ce, Medium-length e, adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions while threading sparse cells"
}
```
2025-06-26 18:09:15,452 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:09:15,452 - ExplorationExpert - INFO - 探索路径生成完成，成本: 169342.0, 路径: [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 30, 10, 23, 35, 8, 20, 32, 1, 13, 27, 36, 6, 19, 31, 11, 25, 34, 3, 16, 28, 21]
2025-06-26 18:09:15,452 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 30, 10, 23, 35, 8, 20, 32, 1, 13, 27, 36, 6, 19, 31, 11, 25, 34, 3, 16, 28, 21], 'cur_cost': 169342.0}
2025-06-26 18:09:15,452 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 18:09:15,452 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:09:15,452 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 165200.0 (rank 80.0%)
- Population diversity: 0.7473684210526316
- Evolution phase: Exploration-dominant (high diversity, no convergence, improving best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Medium-length e
- Difficult regions to avoid (sample): Low-density gri, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:09:15,452 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:09:15,452 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:09:26,358 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1],
  "targeted_regions": "High-density ce, Medium-length e, adjacent low-density",
  "strategy_comment": "Spiral pattern through opportunity regions, skipping long edges"
}
```
2025-06-26 18:09:26,358 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:09:26,358 - ExplorationExpert - INFO - 探索路径生成完成，成本: 155028.0, 路径: [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1]
2025-06-26 18:09:26,358 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1], 'cur_cost': 155028.0}
2025-06-26 18:09:26,358 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 18:09:26,358 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:09:26,358 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 149755.0 (rank 50.0%)
- Population diversity: 0.7684210526315789
- Evolution phase: Exploration-dominant (high diversity, no convergence, improving best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Medium-length e
- Difficult regions to avoid (sample): Low-density gri, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:09:26,358 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:09:26,358 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:09:36,512 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1],
  "targeted_regions": "High-density ce, Medium-length e, adjacent sparse cells",
  "strategy_comment": "Balanced spiral pattern targeting dense clusters while avoiding long edges"
}
```
2025-06-26 18:09:36,512 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:09:36,512 - ExplorationExpert - INFO - 探索路径生成完成，成本: 155028.0, 路径: [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1]
2025-06-26 18:09:36,512 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1], 'cur_cost': 155028.0}
2025-06-26 18:09:36,520 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 18:09:36,520 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:09:36,520 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 131567.0 (rank 20.0%)
- Population diversity: 0.7
- Evolution phase: Exploration-dominant (high diversity, no convergence, improving best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Medium-length e
- Difficult regions to avoid (sample): Low-density gri, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:09:36,520 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:09:36,520 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:09:47,503 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 25, 30, 35, 38, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 37, 23, 16, 9, 3, 10, 17],
  "targeted_regions": "High-density ce, Medium-length e, adjacent low-density",
  "strategy_comment": "Leverage opportunity regions while exploring adjacent sparse cells"
}
```
2025-06-26 18:09:47,503 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:09:47,503 - ExplorationExpert - INFO - 探索路径生成完成，成本: 146266.0, 路径: [0, 5, 12, 19, 25, 30, 35, 38, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 37, 23, 16, 9, 3, 10, 17]
2025-06-26 18:09:47,503 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 25, 30, 35, 38, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 37, 23, 16, 9, 3, 10, 17], 'cur_cost': 146266.0}
2025-06-26 18:09:47,503 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 18:09:47,503 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:09:47,503 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106620.0 (rank 10.0%)
- Population diversity: 0.6526315789473685
- Evolution phase: Exploration-dominant (high diversity, no convergence, improving best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Medium-length e
- Difficult regions to avoid (sample): Low-density gri, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:09:47,503 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:09:47,503 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:09:59,072 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 5, 18, 23, 7, 29, 14, 33, 9, 21, 36, 3, 16, 25, 38, 11, 27, 8, 31, 15, 34, 6, 20, 37, 2, 13, 26, 10, 30, 17, 35, 4, 19, 32, 1, 24, 22, 28],
  "targeted_regions": "High-density ce, Medium-length e, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of opportunity regions with sparse adjacency"
}
```
2025-06-26 18:09:59,073 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:09:59,073 - ExplorationExpert - INFO - 探索路径生成完成，成本: 160145.0, 路径: [0, 12, 5, 18, 23, 7, 29, 14, 33, 9, 21, 36, 3, 16, 25, 38, 11, 27, 8, 31, 15, 34, 6, 20, 37, 2, 13, 26, 10, 30, 17, 35, 4, 19, 32, 1, 24, 22, 28]
2025-06-26 18:09:59,073 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 5, 18, 23, 7, 29, 14, 33, 9, 21, 36, 3, 16, 25, 38, 11, 27, 8, 31, 15, 34, 6, 20, 37, 2, 13, 26, 10, 30, 17, 35, 4, 19, 32, 1, 24, 22, 28], 'cur_cost': 160145.0}
2025-06-26 18:09:59,074 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 18:09:59,074 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 18:09:59,074 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 136324.0 (rank 10.0%)
- Population diversity: 0.6657894736842105
- Evolution phase: Exploration-dominant (high diversity, no convergence, improving best costs)
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Medium-length e
- Difficult regions to avoid (sample): Low-density gri, Long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 38]  
• Path length == 39 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 18:09:59,074 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 18:09:59,076 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:10:09,863 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35],
  "targeted_regions": "High-density ce, Medium-length e",
  "strategy_comment": "Leverage modular jumps to explore sparse cells near opportunities"
}
```
2025-06-26 18:10:09,863 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 18:10:09,863 - ExplorationExpert - INFO - 探索路径生成完成，成本: 169603.0, 路径: [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35]
2025-06-26 18:10:09,863 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}
2025-06-26 18:10:09,863 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 18:10:09,863 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:10:09,863 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:10:09,863 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 145982.0
2025-06-26 18:10:10,371 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:10:10,371 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:10:10,371 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:10:10,378 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:10:10,378 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 17, 22, 27, 32, 37, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 13, 18, 23, 28, 33, 38, 3, 8, 14, 19, 24, 29, 34, 4, 9, 15, 20, 25, 30, 35, 10], 'cur_cost': 157734.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 30, 10, 23, 35, 8, 20, 32, 1, 13, 27, 36, 6, 19, 31, 11, 25, 34, 3, 16, 28, 21], 'cur_cost': 169342.0}, {'tour': [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1], 'cur_cost': 155028.0}, {'tour': [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1], 'cur_cost': 155028.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 38, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 37, 23, 16, 9, 3, 10, 17], 'cur_cost': 146266.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 14, 33, 9, 21, 36, 3, 16, 25, 38, 11, 27, 8, 31, 15, 34, 6, 20, 37, 2, 13, 26, 10, 30, 17, 35, 4, 19, 32, 1, 24, 22, 28], 'cur_cost': 160145.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([ 0,  6,  1,  9,  2, 21, 32, 12, 35, 29, 27, 10,  8, 22, 30, 25, 20,
       18,  5,  7, 24, 13, 38, 19, 31, 17,  3, 11, 26, 15, 36, 33, 28, 16,
       34, 14, 37,  4, 23]), 'cur_cost': 145982.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 38, 37, 36, 34, 33, 31, 29, 28, 27, 26, 24, 23, 22, 21, 20, 18, 17, 16, 15, 14, 13, 11, 10, 9, 8, 7, 6, 4, 3, 2, 1, 32], 'cur_cost': 64696.0}, {'tour': array([28,  7, 38, 23, 27, 33, 15, 19,  2, 36, 26, 21,  3, 16, 10,  5, 24,
        9, 17, 35, 30, 14, 25, 22, 12, 20, 31, 13,  6, 18,  1,  8, 32,  0,
       29, 37, 11, 34,  4]), 'cur_cost': 161563.0}]
2025-06-26 18:10:10,380 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 18:10:10,380 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 215, 'skip_rate': 0.04186046511627907, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 206, 'cache_hits': 172, 'similarity_calculations': 3597, 'cache_hit_rate': 0.047817625799277175, 'cache_size': 3425}}
2025-06-26 18:10:10,380 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 18:10:10,380 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 18:10:10,380 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:10:10,381 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:10:10,381 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 168924.0
2025-06-26 18:10:10,884 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:10:10,884 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:10:10,884 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:10:10,892 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:10:10,893 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 17, 22, 27, 32, 37, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 13, 18, 23, 28, 33, 38, 3, 8, 14, 19, 24, 29, 34, 4, 9, 15, 20, 25, 30, 35, 10], 'cur_cost': 157734.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 30, 10, 23, 35, 8, 20, 32, 1, 13, 27, 36, 6, 19, 31, 11, 25, 34, 3, 16, 28, 21], 'cur_cost': 169342.0}, {'tour': [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1], 'cur_cost': 155028.0}, {'tour': [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1], 'cur_cost': 155028.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 38, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 37, 23, 16, 9, 3, 10, 17], 'cur_cost': 146266.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 14, 33, 9, 21, 36, 3, 16, 25, 38, 11, 27, 8, 31, 15, 34, 6, 20, 37, 2, 13, 26, 10, 30, 17, 35, 4, 19, 32, 1, 24, 22, 28], 'cur_cost': 160145.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([ 0,  6,  1,  9,  2, 21, 32, 12, 35, 29, 27, 10,  8, 22, 30, 25, 20,
       18,  5,  7, 24, 13, 38, 19, 31, 17,  3, 11, 26, 15, 36, 33, 28, 16,
       34, 14, 37,  4, 23]), 'cur_cost': 145982.0}, {'tour': array([38, 22, 17, 13, 23,  1, 35, 26, 19,  8, 31, 11, 15, 10, 18,  2,  0,
        3, 14, 25, 32, 29, 33, 37, 30, 16, 21,  7,  4, 24, 12, 28, 36,  6,
       20, 34,  5,  9, 27]), 'cur_cost': 168924.0}, {'tour': array([28,  7, 38, 23, 27, 33, 15, 19,  2, 36, 26, 21,  3, 16, 10,  5, 24,
        9, 17, 35, 30, 14, 25, 22, 12, 20, 31, 13,  6, 18,  1,  8, 32,  0,
       29, 37, 11, 34,  4]), 'cur_cost': 161563.0}]
2025-06-26 18:10:10,894 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 18:10:10,894 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 216, 'skip_rate': 0.041666666666666664, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 207, 'cache_hits': 172, 'similarity_calculations': 3614, 'cache_hit_rate': 0.047592695074709465, 'cache_size': 3442}}
2025-06-26 18:10:10,894 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 18:10:10,894 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 18:10:10,894 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 18:10:10,894 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 18:10:10,896 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 159638.0
2025-06-26 18:10:11,399 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 18:10:11,399 - ExploitationExpert - INFO - res_population_costs: [23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23763, 23782, 23794]
2025-06-26 18:10:11,399 - ExploitationExpert - INFO - res_populations: [array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 38,
       36, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  8,  9,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  8,  9,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12, 11,
       10, 14, 13,  2,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 10, 14, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 14, 10, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  9,  8,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32, 12, 11, 10, 14, 13,  7,  5,
        9,  8,  6,  1,  2], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 36, 38, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2, 13, 14, 10, 11, 12,  7,  6,  9,  8,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12,
       11, 10, 14, 13,  2], dtype=int64), array([ 0,  1,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21,
       25, 20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  8,  9,  6,  7, 12,
       11, 14, 10, 13,  2], dtype=int64), array([ 0,  2, 13, 10, 14, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36, 37,
       34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29, 27,
       31, 26,  3,  4,  1], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 22, 21, 25,
       20, 23, 24, 33, 34, 37, 36, 38, 35, 32,  5,  9,  8,  6,  7, 12, 11,
       14, 10, 13,  2,  1], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 14, 10,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 35, 37, 38, 36, 33, 34, 19, 15, 17, 16, 18, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 18, 16, 17, 15, 19, 34, 33, 36,
       38, 37, 35, 32, 12, 11, 14, 10, 13,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64), array([ 0,  1,  2, 13, 14, 10, 11, 12,  7,  6,  8,  9,  5, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1,  6,  8,  9,  5,  7, 13, 10, 14, 11, 12, 32, 35, 38, 36,
       37, 34, 33, 24, 23, 20, 25, 21, 22, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  2,  1, 20, 25, 21, 22, 23, 24,  6,  9,  8,  5,  7, 13, 10, 14,
       11, 12, 32, 38, 35, 37, 36, 33, 34, 18, 19, 15, 16, 17, 30, 28, 29,
       27, 31, 26,  3,  4], dtype=int64), array([ 0,  4,  3, 26, 31, 27, 29, 28, 30, 17, 16, 15, 19, 18, 34, 33, 36,
       37, 35, 38, 32, 11, 14, 13, 10, 12,  7,  5,  9,  8,  6, 24, 23, 22,
       21, 25, 20,  1,  2], dtype=int64)]
2025-06-26 18:10:11,408 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 18:10:11,410 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 17, 22, 27, 32, 37, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 13, 18, 23, 28, 33, 38, 3, 8, 14, 19, 24, 29, 34, 4, 9, 15, 20, 25, 30, 35, 10], 'cur_cost': 157734.0}, {'tour': [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 30, 10, 23, 35, 8, 20, 32, 1, 13, 27, 36, 6, 19, 31, 11, 25, 34, 3, 16, 28, 21], 'cur_cost': 169342.0}, {'tour': [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1], 'cur_cost': 155028.0}, {'tour': [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1], 'cur_cost': 155028.0}, {'tour': [0, 5, 12, 19, 25, 30, 35, 38, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 37, 23, 16, 9, 3, 10, 17], 'cur_cost': 146266.0}, {'tour': [0, 12, 5, 18, 23, 7, 29, 14, 33, 9, 21, 36, 3, 16, 25, 38, 11, 27, 8, 31, 15, 34, 6, 20, 37, 2, 13, 26, 10, 30, 17, 35, 4, 19, 32, 1, 24, 22, 28], 'cur_cost': 160145.0}, {'tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}, {'tour': array([ 0,  6,  1,  9,  2, 21, 32, 12, 35, 29, 27, 10,  8, 22, 30, 25, 20,
       18,  5,  7, 24, 13, 38, 19, 31, 17,  3, 11, 26, 15, 36, 33, 28, 16,
       34, 14, 37,  4, 23]), 'cur_cost': 145982.0}, {'tour': array([38, 22, 17, 13, 23,  1, 35, 26, 19,  8, 31, 11, 15, 10, 18,  2,  0,
        3, 14, 25, 32, 29, 33, 37, 30, 16, 21,  7,  4, 24, 12, 28, 36,  6,
       20, 34,  5,  9, 27]), 'cur_cost': 168924.0}, {'tour': array([26, 35, 19, 12,  3, 13, 38, 32, 27, 28, 11,  0,  7,  4, 21, 17,  9,
       20, 18, 34,  2, 36,  1, 30, 22, 14, 29, 33,  5, 24, 15, 31, 25, 37,
       10, 23,  6, 16,  8]), 'cur_cost': 159638.0}]
2025-06-26 18:10:11,412 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 18:10:11,413 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 9, 'total_searches': 217, 'skip_rate': 0.041474654377880185, 'estimated_time_saved': 45, 'path_optimizer': {'path_count': 208, 'cache_hits': 172, 'similarity_calculations': 3632, 'cache_hit_rate': 0.0473568281938326, 'cache_size': 3460}}
2025-06-26 18:10:11,414 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 18:10:11,414 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 17, 22, 27, 32, 37, 1, 6, 11, 16, 21, 26, 31, 36, 2, 7, 13, 18, 23, 28, 33, 38, 3, 8, 14, 19, 24, 29, 34, 4, 9, 15, 20, 25, 30, 35, 10], 'cur_cost': 157734.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 22, 7, 29, 15, 33, 9, 24, 37, 2, 14, 26, 38, 4, 17, 30, 10, 23, 35, 8, 20, 32, 1, 13, 27, 36, 6, 19, 31, 11, 25, 34, 3, 16, 28, 21], 'cur_cost': 169342.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1], 'cur_cost': 155028.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 17, 23, 29, 35, 8, 14, 20, 26, 32, 38, 11, 4, 16, 22, 28, 34, 7, 13, 19, 25, 31, 37, 10, 3, 15, 21, 27, 33, 6, 18, 24, 30, 36, 9, 2, 1], 'cur_cost': 155028.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 25, 30, 35, 38, 34, 29, 24, 18, 11, 4, 1, 6, 13, 20, 26, 31, 36, 32, 27, 21, 14, 7, 2, 8, 15, 22, 28, 33, 37, 23, 16, 9, 3, 10, 17], 'cur_cost': 146266.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 5, 18, 23, 7, 29, 14, 33, 9, 21, 36, 3, 16, 25, 38, 11, 27, 8, 31, 15, 34, 6, 20, 37, 2, 13, 26, 10, 30, 17, 35, 4, 19, 32, 1, 24, 22, 28], 'cur_cost': 160145.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 1, 13, 25, 37, 2, 14, 26, 38, 3, 15, 27, 4, 16, 28, 5, 17, 29, 6, 18, 30, 7, 19, 31, 8, 20, 32, 9, 21, 33, 10, 22, 34, 11, 23, 35], 'cur_cost': 169603.0}}]
2025-06-26 18:10:11,414 - __main__ - INFO - 进化阶段完成
2025-06-26 18:10:11,414 - __main__ - INFO - 开始评估阶段
2025-06-26 18:10:11,414 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 18:10:11,416 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 18:10:11,416 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 64696.0 → 145982.0 (Δ -81286.0)
- Mean cost: 137842.5 → 158769.0
- Diversity: 0.774 → 0.663 (Δ -0.1105)

#### Elite metrics
- Elite count: 22 → 22
- Best elite cost: 23763 → 23763 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 14.3% , avg Δ -14739.86
- Exploit: 3 inds, success 33.3% , avg Δ -35362.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 8
- Historical trends: Last 3 iterations: costs [133568.0, 125620.0, 64696.0], diversity [0.9236467236467237, 0.8438746438746438, 0.9310541310541309]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 18:10:11,416 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 18:10:24,141 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 14.3,
      "average_delta": -14739.86,
      "contribution": "low"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -35362.0,
      "contribution": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "needs_adjustment",
    "diversity": "decreasing",
    "cost_trend": "increasing"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators or increasing population size to combat stagnation."
  }
}
```
2025-06-26 18:10:24,148 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 18:10:24,148 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 14.3,
      "average_delta": -14739.86,
      "contribution": "low"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -35362.0,
      "contribution": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "needs_adjustment",
    "diversity": "decreasing",
    "cost_trend": "increasing"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators or increasing population size to combat stagnation."
  }
}
```
2025-06-26 18:10:24,148 - __main__ - INFO - 评估阶段完成
2025-06-26 18:10:24,157 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "stagnation",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 14.3,
      "average_delta": -14739.86,
      "contribution": "low"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -35362.0,
      "contribution": "moderate"
    }
  },
  "balance_state": {
    "explore_exploit_ratio": "needs_adjustment",
    "diversity": "decreasing",
    "cost_trend": "increasing"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_suggestions": "Consider introducing new mutation operators or increasing population size to combat stagnation."
  }
}
```
2025-06-26 18:10:24,157 - __main__ - INFO - 当前最佳适应度: 145982.0
2025-06-26 18:10:24,159 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite6_39_route_4.pkl
2025-06-26 18:10:24,166 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite6_39_solution.json
2025-06-26 18:10:24,166 - __main__ - INFO - 实例 composite6_39 处理完成
