2025-07-09 20:33:07,011 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-09 20:33:07,011 - __main__ - INFO - --- Running Analysis Phase (Iteration 0) ---
2025-07-09 20:33:07,011 - improved_collaboration_manager - INFO - 开始协作决策 - 迭代 0/5
2025-07-09 20:33:07,011 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:33:07,035 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9946.0, 'max': 107726.0, 'mean': 77202.9, 'std': np.float64(44045.002759564)}, 'diversity': np.float64(0.9057239057239057), 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:33:07,036 - improved_collaboration_manager - ERROR - 景观分析失败: PathExpert.analyze() missing 1 required positional argument: 'distance_matrix'
2025-07-09 20:33:07,036 - improved_collaboration_manager - INFO - 应用自适应调整: {'landscape': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 0}, 'strategy': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 0}}
2025-07-09 20:33:07,036 - __main__ - WARNING - 改进协作模式未返回景观分析，回退到传统模式
2025-07-09 20:33:07,036 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:33:07,054 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9946.0, 'max': 107726.0, 'mean': 77202.9, 'std': np.float64(44045.002759564)}, 'diversity': np.float64(0.9057239057239057), 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:33:07,054 - PathExpert - INFO - 开始路径结构分析
2025-07-09 20:33:07,063 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-09 20:33:07,063 - PathExpert - INFO - 路径结构分析完成
2025-07-09 20:33:07,063 - EliteExpert - INFO - 开始精英解分析
2025-07-09 20:33:07,063 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-09 20:33:07,069 - LandscapeExpert - INFO - 开始景观分析
2025-07-09 20:33:07,070 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-09 20:33:07,070 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: TSP Landscape Analyzer (Small Problem)

### Status (Iter 0)
- Problem: 0 nodes, best cost: 9946.0
- Population diversity: 0.906
- Recent improvement: Not available

### Analysis
Based on the current state, determine:
1. Should we explore (find new areas) or exploit (improve current solutions)?
2. Which nodes/edges show promise?

### Output
JSON with: recommended_focus, promising_areas

### Example
```json
{
  "recommended_focus": "explore",
  "promising_areas": [2, 5, 8]
}
```

2025-07-09 20:33:07,070 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-09 20:33:09,057 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "recommended_focus": "explore",
  "promising_areas": []
}
```

2025-07-09 20:33:09,057 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-09 20:33:09,057 - __main__ - INFO - 景观分析完整报告: {'recommended_focus': 'explore', 'promising_areas': []}
2025-07-09 20:33:09,057 - __main__ - INFO - 开始策略分配阶段
2025-07-09 20:33:09,058 - StrategyExpert - INFO - 开始策略分配分析
2025-07-09 20:33:09,058 - adaptive_strategy_expert - INFO - 开始自适应策略分析
2025-07-09 20:33:09,058 - adaptive_strategy_expert - INFO - 全局策略决策: balance, 评分: {'explore': 0.5, 'exploit': 0.5, 'balance': 0.7}
2025-07-09 20:33:09,058 - adaptive_strategy_expert - INFO - 策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:09,058 - StrategyExpert - INFO - 自适应策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:09,059 - __main__ - INFO - 策略分配报告: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:09,059 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:33:09,059 - __main__ - INFO - 策略分配阶段完成
2025-07-09 20:33:09,059 - __main__ - INFO - 策略分配: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:09,059 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:33:09,060 - __main__ - INFO - 开始进化阶段
2025-07-09 20:33:09,060 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-09 20:33:09,060 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:09,060 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:12,732 - ExploitationExpert - INFO - 自适应开发成功，改进: 381.00
2025-07-09 20:33:12,742 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(381.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:12,742 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-09 20:33:12,742 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:12,743 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:12,777 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 9958.0 -> 9944.0
2025-07-09 20:33:15,567 - ExploitationExpert - INFO - 自适应开发成功，改进: 413.00
2025-07-09 20:33:15,568 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(413.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:15,568 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-09 20:33:15,569 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:15,569 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:19,807 - ExploitationExpert - INFO - 自适应开发成功，改进: 401.00
2025-07-09 20:33:19,808 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(401.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:19,809 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-09 20:33:19,809 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:19,809 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:19,965 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 105691.0 -> 99911.0
2025-07-09 20:33:24,424 - ExploitationExpert - INFO - 自适应开发成功，改进: 67377.00
2025-07-09 20:33:24,425 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36, 37, 12,  5, 54, 52,  1,  0, 63, 53, 57, 64, 62, 22, 25, 20, 16,
       58, 56, 61, 21, 15, 46, 41, 50, 34, 30, 33, 14,  4,  3, 60, 47, 40,
       49, 43, 42, 51, 44, 55, 65, 59,  6, 31, 24, 29, 26, 28, 32, 35, 18,
       17, 23, 13, 48, 39, 38, 45, 19, 11,  7,  8, 10,  2,  9, 27]), 'cur_cost': np.float64(38314.0), 'improvement': np.float64(67377.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:24,425 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-09 20:33:24,425 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:24,425 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:24,581 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 105180.0 -> 99303.0
2025-07-09 20:33:28,882 - ExploitationExpert - INFO - 自适应开发成功，改进: 95651.00
2025-07-09 20:33:28,883 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(95651.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:28,883 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-09 20:33:28,883 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:28,883 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:29,029 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 107141.0 -> 101312.0
2025-07-09 20:33:31,693 - ExploitationExpert - INFO - 自适应开发成功，改进: 67794.00
2025-07-09 20:33:31,693 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([25, 29, 28, 24, 34, 23, 27, 26, 31, 21,  6,  3, 11, 59, 64, 57, 52,
       60,  5, 10,  9, 51, 45, 20, 49, 48, 40, 39, 15,  2, 17, 53, 65, 63,
       61,  1,  7, 22, 14, 38, 43, 42, 50, 47, 44, 12, 30, 13, 16, 19, 18,
       33, 37, 32, 41, 46,  8, 54, 62, 55, 58,  0,  4, 56, 35, 36]), 'cur_cost': np.float64(39347.0), 'improvement': np.float64(67794.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:31,694 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-09 20:33:31,694 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:31,694 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:31,839 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 102874.0 -> 97148.0
2025-07-09 20:33:37,560 - ExploitationExpert - INFO - 自适应开发成功，改进: 93295.00
2025-07-09 20:33:37,561 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([61, 55, 56, 59, 62, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46,
       47, 49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36,
       26, 25, 35, 34, 30, 28, 33, 32, 29, 24, 31, 37, 27,  3,  7,  1,  0,
       11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 54, 57, 64, 53]), 'cur_cost': np.float64(9579.0), 'improvement': np.float64(93295.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:37,561 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-09 20:33:37,562 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:37,562 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:37,702 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 107726.0 -> 101904.0
2025-07-09 20:33:43,223 - ExploitationExpert - INFO - 自适应开发成功，改进: 98179.00
2025-07-09 20:33:43,223 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(98179.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:43,224 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-09 20:33:43,224 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:43,224 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:43,364 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 106299.0 -> 100391.0
2025-07-09 20:33:46,363 - ExploitationExpert - INFO - 自适应开发成功，改进: 96728.00
2025-07-09 20:33:46,364 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(96728.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:46,364 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-09 20:33:46,365 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:46,365 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:46,501 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 107263.0 -> 101386.0
2025-07-09 20:33:49,927 - ExploitationExpert - INFO - 自适应开发成功，改进: 64030.00
2025-07-09 20:33:49,928 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35, 32, 34,  3, 17, 15, 50, 38, 10,  6, 20, 23, 18, 43, 49, 40, 48,
       42, 51, 14, 28, 25,  4, 58, 56, 60, 64, 44, 41, 45, 16, 21, 13, 61,
       65,  8,  2,  0,  7, 24, 29, 27, 37, 31, 33, 26, 22,  9, 11,  5, 53,
       63, 52, 54, 62, 59, 55, 12, 39, 47, 46, 57, 36, 30,  1, 19]), 'cur_cost': np.float64(43233.0), 'improvement': np.float64(64030.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:49,930 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(381.0), 'method': 'adaptive_exploitation'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(413.0), 'method': 'adaptive_exploitation'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(401.0), 'method': 'adaptive_exploitation'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 37, 12,  5, 54, 52,  1,  0, 63, 53, 57, 64, 62, 22, 25, 20, 16,
       58, 56, 61, 21, 15, 46, 41, 50, 34, 30, 33, 14,  4,  3, 60, 47, 40,
       49, 43, 42, 51, 44, 55, 65, 59,  6, 31, 24, 29, 26, 28, 32, 35, 18,
       17, 23, 13, 48, 39, 38, 45, 19, 11,  7,  8, 10,  2,  9, 27]), 'cur_cost': np.float64(38314.0), 'improvement': np.float64(67377.0), 'method': 'adaptive_exploitation'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(95651.0), 'method': 'adaptive_exploitation'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 29, 28, 24, 34, 23, 27, 26, 31, 21,  6,  3, 11, 59, 64, 57, 52,
       60,  5, 10,  9, 51, 45, 20, 49, 48, 40, 39, 15,  2, 17, 53, 65, 63,
       61,  1,  7, 22, 14, 38, 43, 42, 50, 47, 44, 12, 30, 13, 16, 19, 18,
       33, 37, 32, 41, 46,  8, 54, 62, 55, 58,  0,  4, 56, 35, 36]), 'cur_cost': np.float64(39347.0), 'improvement': np.float64(67794.0), 'method': 'adaptive_exploitation'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 55, 56, 59, 62, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46,
       47, 49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36,
       26, 25, 35, 34, 30, 28, 33, 32, 29, 24, 31, 37, 27,  3,  7,  1,  0,
       11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 54, 57, 64, 53]), 'cur_cost': np.float64(9579.0), 'improvement': np.float64(93295.0), 'method': 'adaptive_exploitation'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(98179.0), 'method': 'adaptive_exploitation'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(96728.0), 'method': 'adaptive_exploitation'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 32, 34,  3, 17, 15, 50, 38, 10,  6, 20, 23, 18, 43, 49, 40, 48,
       42, 51, 14, 28, 25,  4, 58, 56, 60, 64, 44, 41, 45, 16, 21, 13, 61,
       65,  8,  2,  0,  7, 24, 29, 27, 37, 31, 33, 26, 22,  9, 11,  5, 53,
       63, 52, 54, 62, 59, 55, 12, 39, 47, 46, 57, 36, 30,  1, 19]), 'cur_cost': np.float64(43233.0), 'improvement': np.float64(64030.0), 'method': 'adaptive_exploitation'}}]
2025-07-09 20:33:49,931 - __main__ - INFO - 进化阶段完成
2025-07-09 20:33:49,931 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:33:49,945 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 43233.0, 'mean': 18778.0, 'std': np.float64(14135.825890269021)}, 'diversity': np.float64(0.6010101010101011), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:33:49,946 - __main__ - INFO - --- Running Assessment Phase (Iteration 0) ---
2025-07-09 20:33:49,946 - EvolutionAssessmentExpert - INFO - --- Iteration 0 Assessment ---
2025-07-09 20:33:49,946 - multi_objective_assessment_expert - INFO - 开始多目标评估 - 迭代 0/5
2025-07-09 20:33:49,947 - multi_objective_assessment_expert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:33:49,947 - EvolutionAssessmentExpert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:33:49,947 - __main__ - INFO - --- Finished Evolution Iteration 1 ---
2025-07-09 20:33:49,948 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-09 20:33:49,948 - __main__ - INFO - --- Running Analysis Phase (Iteration 1) ---
2025-07-09 20:33:49,948 - improved_collaboration_manager - INFO - 开始协作决策 - 迭代 1/5
2025-07-09 20:33:49,948 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:33:49,963 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 43233.0, 'mean': 18778.0, 'std': np.float64(14135.825890269021)}, 'diversity': np.float64(0.6010101010101011), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:33:49,963 - improved_collaboration_manager - ERROR - 景观分析失败: PathExpert.analyze() missing 1 required positional argument: 'distance_matrix'
2025-07-09 20:33:49,963 - improved_collaboration_manager - INFO - 应用自适应调整: {'landscape': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 1}, 'strategy': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 1}}
2025-07-09 20:33:49,963 - __main__ - WARNING - 改进协作模式未返回景观分析，回退到传统模式
2025-07-09 20:33:49,964 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:33:49,978 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 43233.0, 'mean': 18778.0, 'std': np.float64(14135.825890269021)}, 'diversity': np.float64(0.6010101010101011), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:33:49,978 - PathExpert - INFO - 开始路径结构分析
2025-07-09 20:33:49,982 - PathExpert - INFO - 路径结构分析完成
2025-07-09 20:33:49,982 - EliteExpert - INFO - 开始精英解分析
2025-07-09 20:33:49,982 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-09 20:33:49,984 - LandscapeExpert - INFO - 开始景观分析
2025-07-09 20:33:49,984 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-09 20:33:49,984 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: TSP Landscape Analyzer (Small Problem)

### Status (Iter 1)
- Problem: 0 nodes, best cost: 9529.0
- Population diversity: 0.601
- Recent improvement: Not available

### Analysis
Based on the current state, determine:
1. Should we explore (find new areas) or exploit (improve current solutions)?
2. Which nodes/edges show promise?

### Output
JSON with: recommended_focus, promising_areas

### Example
```json
{
  "recommended_focus": "explore",
  "promising_areas": [2, 5, 8]
}
```

2025-07-09 20:33:49,985 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-09 20:33:52,003 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "recommended_focus": "explore",
  "promising_areas": []
}
```

2025-07-09 20:33:52,003 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-09 20:33:52,003 - __main__ - INFO - 景观分析完整报告: {'recommended_focus': 'explore', 'promising_areas': []}
2025-07-09 20:33:52,004 - __main__ - INFO - 开始策略分配阶段
2025-07-09 20:33:52,004 - StrategyExpert - INFO - 开始策略分配分析
2025-07-09 20:33:52,004 - adaptive_strategy_expert - INFO - 开始自适应策略分析
2025-07-09 20:33:52,004 - adaptive_strategy_expert - INFO - 全局策略决策: balance, 评分: {'explore': 0.5, 'exploit': 0.5, 'balance': 0.7}
2025-07-09 20:33:52,004 - adaptive_strategy_expert - INFO - 策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:52,004 - StrategyExpert - INFO - 自适应策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:52,005 - __main__ - INFO - 策略分配报告: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:52,005 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:33:52,005 - __main__ - INFO - 策略分配阶段完成
2025-07-09 20:33:52,005 - __main__ - INFO - 策略分配: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:33:52,005 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:33:52,006 - __main__ - INFO - 开始进化阶段
2025-07-09 20:33:52,006 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-09 20:33:52,006 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:52,006 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:53,457 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:33:53,457 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:53,457 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-09 20:33:53,457 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:53,458 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:54,581 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:33:54,582 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:54,582 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-09 20:33:54,583 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:54,583 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:55,081 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:33:55,081 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:55,082 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-09 20:33:55,082 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:55,082 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:55,198 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 38314.0 -> 34535.0
2025-07-09 20:33:57,098 - ExploitationExpert - INFO - 自适应开发成功，改进: 19291.00
2025-07-09 20:33:57,099 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36, 27, 11,  7,  9,  3,  1,  0, 63, 53, 57, 64, 62, 22, 16, 21, 20,
       14, 15, 34, 30, 33, 25, 47, 40, 49, 43, 42, 51, 44, 58, 56, 61, 55,
       60, 54, 52, 65, 59,  6,  4,  2, 10,  8,  5, 12, 19, 46, 41, 50, 45,
       38, 39, 48, 13, 23, 17, 18, 35, 32, 28, 26, 29, 24, 31, 37]), 'cur_cost': np.float64(19023.0), 'improvement': np.float64(19291.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:57,099 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-09 20:33:57,100 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:57,100 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:57,512 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:33:57,512 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:33:57,512 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-09 20:33:57,513 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:33:57,513 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:33:57,654 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 39347.0 -> 36556.0
2025-07-09 20:34:04,974 - ExploitationExpert - INFO - 自适应开发成功，改进: 28356.00
2025-07-09 20:34:04,975 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([25, 29, 28, 34, 24, 27, 26, 31, 21, 20, 13, 16, 19, 18, 23, 12, 15,
       17, 14, 22,  7,  1,  9,  5, 10,  0,  4,  6,  3, 11,  8,  2, 59, 64,
       57, 52, 60, 56, 58, 55, 62, 54, 53, 65, 63, 61, 49, 48, 40, 39, 44,
       47, 50, 42, 43, 38, 45, 51, 46, 41, 32, 37, 33, 30, 35, 36]), 'cur_cost': np.float64(10991.0), 'improvement': np.float64(28356.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:04,975 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-09 20:34:04,976 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:04,976 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:05,612 - ExploitationExpert - INFO - 自适应开发成功，改进: 1.00
2025-07-09 20:34:05,613 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([61, 55, 56, 59, 62, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46,
       47, 49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36,
       26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1,  0,
       11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 54, 57, 64, 53]), 'cur_cost': np.float64(9578.0), 'improvement': np.float64(1.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:05,614 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-09 20:34:05,614 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:05,614 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:06,816 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:06,817 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:06,817 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-09 20:34:06,817 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:06,818 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:07,086 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:07,088 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:07,088 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-09 20:34:07,088 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:07,088 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:07,210 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 43233.0 -> 38926.0
2025-07-09 20:34:11,420 - ExploitationExpert - INFO - 自适应开发成功，改进: 28092.00
2025-07-09 20:34:11,421 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35, 32, 34,  3,  4, 58, 56, 60, 64, 55, 59, 62, 54, 52, 63, 53, 57,
       65, 61, 10,  6,  5, 11,  9,  1, 26, 33, 31, 37, 27, 29, 24,  7,  0,
        2,  8, 14, 13, 21, 20, 23, 16, 43, 49, 40, 48, 42, 51, 45, 41, 44,
       50, 38, 46, 47, 39, 19, 22, 12, 15, 17, 18, 28, 25, 36, 30]), 'cur_cost': np.float64(15141.0), 'improvement': np.float64(28092.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:11,423 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 27, 11,  7,  9,  3,  1,  0, 63, 53, 57, 64, 62, 22, 16, 21, 20,
       14, 15, 34, 30, 33, 25, 47, 40, 49, 43, 42, 51, 44, 58, 56, 61, 55,
       60, 54, 52, 65, 59,  6,  4,  2, 10,  8,  5, 12, 19, 46, 41, 50, 45,
       38, 39, 48, 13, 23, 17, 18, 35, 32, 28, 26, 29, 24, 31, 37]), 'cur_cost': np.float64(19023.0), 'improvement': np.float64(19291.0), 'method': 'adaptive_exploitation'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 29, 28, 34, 24, 27, 26, 31, 21, 20, 13, 16, 19, 18, 23, 12, 15,
       17, 14, 22,  7,  1,  9,  5, 10,  0,  4,  6,  3, 11,  8,  2, 59, 64,
       57, 52, 60, 56, 58, 55, 62, 54, 53, 65, 63, 61, 49, 48, 40, 39, 44,
       47, 50, 42, 43, 38, 45, 51, 46, 41, 32, 37, 33, 30, 35, 36]), 'cur_cost': np.float64(10991.0), 'improvement': np.float64(28356.0), 'method': 'adaptive_exploitation'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 55, 56, 59, 62, 60, 58, 39, 44, 41, 51, 38, 45, 50, 42, 48, 46,
       47, 49, 40, 43, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 36,
       26, 25, 33, 35, 34, 30, 28, 32, 29, 24, 31, 37, 27,  3,  7,  1,  0,
       11,  9,  5,  4,  6,  2,  8, 10, 63, 52, 65, 54, 57, 64, 53]), 'cur_cost': np.float64(9578.0), 'improvement': np.float64(1.0), 'method': 'adaptive_exploitation'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 32, 34,  3,  4, 58, 56, 60, 64, 55, 59, 62, 54, 52, 63, 53, 57,
       65, 61, 10,  6,  5, 11,  9,  1, 26, 33, 31, 37, 27, 29, 24,  7,  0,
        2,  8, 14, 13, 21, 20, 23, 16, 43, 49, 40, 48, 42, 51, 45, 41, 44,
       50, 38, 46, 47, 39, 19, 22, 12, 15, 17, 18, 28, 25, 36, 30]), 'cur_cost': np.float64(15141.0), 'improvement': np.float64(28092.0), 'method': 'adaptive_exploitation'}}]
2025-07-09 20:34:11,423 - __main__ - INFO - 进化阶段完成
2025-07-09 20:34:11,424 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:11,438 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 19023.0, 'mean': 11204.0, 'std': np.float64(3093.8399441470788)}, 'diversity': np.float64(0.5501683501683503), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:11,438 - __main__ - INFO - --- Running Assessment Phase (Iteration 1) ---
2025-07-09 20:34:11,439 - EvolutionAssessmentExpert - INFO - --- Iteration 1 Assessment ---
2025-07-09 20:34:11,439 - multi_objective_assessment_expert - INFO - 开始多目标评估 - 迭代 1/5
2025-07-09 20:34:11,439 - multi_objective_assessment_expert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:34:11,439 - EvolutionAssessmentExpert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:34:11,440 - __main__ - INFO - --- Finished Evolution Iteration 2 ---
2025-07-09 20:34:11,440 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-09 20:34:11,440 - __main__ - INFO - --- Running Analysis Phase (Iteration 2) ---
2025-07-09 20:34:11,440 - improved_collaboration_manager - INFO - 开始协作决策 - 迭代 2/5
2025-07-09 20:34:11,440 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:11,454 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 19023.0, 'mean': 11204.0, 'std': np.float64(3093.8399441470788)}, 'diversity': np.float64(0.5501683501683503), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:11,455 - improved_collaboration_manager - ERROR - 景观分析失败: PathExpert.analyze() missing 1 required positional argument: 'distance_matrix'
2025-07-09 20:34:11,455 - improved_collaboration_manager - INFO - 应用自适应调整: {'landscape': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 2}, 'strategy': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 2}}
2025-07-09 20:34:11,455 - __main__ - WARNING - 改进协作模式未返回景观分析，回退到传统模式
2025-07-09 20:34:11,456 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:11,470 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 19023.0, 'mean': 11204.0, 'std': np.float64(3093.8399441470788)}, 'diversity': np.float64(0.5501683501683503), 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:11,471 - PathExpert - INFO - 开始路径结构分析
2025-07-09 20:34:11,474 - PathExpert - INFO - 路径结构分析完成
2025-07-09 20:34:11,474 - EliteExpert - INFO - 开始精英解分析
2025-07-09 20:34:11,475 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-09 20:34:11,476 - LandscapeExpert - INFO - 开始景观分析
2025-07-09 20:34:11,477 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-09 20:34:11,477 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: TSP Landscape Analyzer (Small Problem)

### Status (Iter 2)
- Problem: 0 nodes, best cost: 9529.0
- Population diversity: 0.550
- Recent improvement: Not available

### Analysis
Based on the current state, determine:
1. Should we explore (find new areas) or exploit (improve current solutions)?
2. Which nodes/edges show promise?

### Output
JSON with: recommended_focus, promising_areas

### Example
```json
{
  "recommended_focus": "explore",
  "promising_areas": [2, 5, 8]
}
```

2025-07-09 20:34:11,477 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-09 20:34:13,994 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "recommended_focus": "explore",
  "promising_areas": []
}
```

2025-07-09 20:34:13,995 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-09 20:34:13,995 - __main__ - INFO - 景观分析完整报告: {'recommended_focus': 'explore', 'promising_areas': []}
2025-07-09 20:34:13,995 - __main__ - INFO - 开始策略分配阶段
2025-07-09 20:34:13,995 - StrategyExpert - INFO - 开始策略分配分析
2025-07-09 20:34:13,995 - adaptive_strategy_expert - INFO - 开始自适应策略分析
2025-07-09 20:34:13,996 - adaptive_strategy_expert - INFO - 全局策略决策: balance, 评分: {'explore': 0.5, 'exploit': 0.5, 'balance': 0.7}
2025-07-09 20:34:13,996 - adaptive_strategy_expert - INFO - 策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:13,996 - StrategyExpert - INFO - 自适应策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:13,996 - __main__ - INFO - 策略分配报告: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:13,997 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:34:13,997 - __main__ - INFO - 策略分配阶段完成
2025-07-09 20:34:13,997 - __main__ - INFO - 策略分配: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:13,997 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:34:13,998 - __main__ - INFO - 开始进化阶段
2025-07-09 20:34:13,998 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-09 20:34:13,998 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:13,998 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:16,299 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:16,300 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:16,301 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-09 20:34:16,301 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:16,301 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:16,636 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:16,637 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:16,637 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-09 20:34:16,638 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:16,638 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:17,278 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:17,278 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:17,279 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-09 20:34:17,279 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:17,279 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:17,379 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 19023.0 -> 16711.0
2025-07-09 20:34:22,459 - ExploitationExpert - INFO - 自适应开发成功，改进: 9423.00
2025-07-09 20:34:22,460 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36, 37, 27,  3,  7,  9, 11,  1,  0, 10,  2,  8,  5,  4,  6, 55, 61,
       56, 59, 62, 53, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 47, 48, 46, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 30, 28, 35, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9600.0), 'improvement': np.float64(9423.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:22,460 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-09 20:34:22,460 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:22,460 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:23,191 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:23,192 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:23,193 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-09 20:34:23,193 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:23,193 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:23,309 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 10991.0 -> 10890.0
2025-07-09 20:34:28,624 - ExploitationExpert - INFO - 自适应开发成功，改进: 1362.00
2025-07-09 20:34:28,625 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([25, 31, 24, 29, 32, 33, 35, 28, 30, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 56, 59, 58, 60, 54, 57, 64, 62, 53, 65,
       52, 63, 61, 55,  2,  8,  6,  4,  5,  9, 11, 10,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 36, 37, 26]), 'cur_cost': np.float64(9629.0), 'improvement': np.float64(1362.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:28,625 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-09 20:34:28,626 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:28,626 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:33,506 - ExploitationExpert - INFO - 自适应开发成功，改进: 40.00
2025-07-09 20:34:33,507 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(40.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:33,507 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-09 20:34:33,508 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:33,508 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:33,992 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:33,993 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:33,993 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-09 20:34:33,994 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:33,994 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:34,413 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:34:34,414 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:34,415 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-09 20:34:34,415 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:34,415 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:34:34,523 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 15141.0 -> 11348.0
2025-07-09 20:34:44,324 - ExploitationExpert - INFO - 自适应开发成功，改进: 5217.00
2025-07-09 20:34:44,325 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35, 32, 29, 24, 18, 17, 15, 12, 22, 14, 23, 16, 19, 13, 21, 20, 43,
       49, 40, 48, 42, 51, 45, 41, 44, 50, 38, 46, 47, 39, 58, 56, 60, 53,
       55, 59, 62, 64, 57, 54, 52, 63, 65, 61, 10,  8,  4,  6,  2,  5, 11,
        9,  1,  0,  7,  3, 27, 37, 31, 33, 26, 34, 36, 25, 28, 30]), 'cur_cost': np.float64(9924.0), 'improvement': np.float64(5217.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:34:44,326 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 37, 27,  3,  7,  9, 11,  1,  0, 10,  2,  8,  5,  4,  6, 55, 61,
       56, 59, 62, 53, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 47, 48, 46, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 30, 28, 35, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9600.0), 'improvement': np.float64(9423.0), 'method': 'adaptive_exploitation'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 31, 24, 29, 32, 33, 35, 28, 30, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 56, 59, 58, 60, 54, 57, 64, 62, 53, 65,
       52, 63, 61, 55,  2,  8,  6,  4,  5,  9, 11, 10,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 36, 37, 26]), 'cur_cost': np.float64(9629.0), 'improvement': np.float64(1362.0), 'method': 'adaptive_exploitation'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(40.0), 'method': 'adaptive_exploitation'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 32, 29, 24, 18, 17, 15, 12, 22, 14, 23, 16, 19, 13, 21, 20, 43,
       49, 40, 48, 42, 51, 45, 41, 44, 50, 38, 46, 47, 39, 58, 56, 60, 53,
       55, 59, 62, 64, 57, 54, 52, 63, 65, 61, 10,  8,  4,  6,  2,  5, 11,
        9,  1,  0,  7,  3, 27, 37, 31, 33, 26, 34, 36, 25, 28, 30]), 'cur_cost': np.float64(9924.0), 'improvement': np.float64(5217.0), 'method': 'adaptive_exploitation'}}]
2025-07-09 20:34:44,327 - __main__ - INFO - 进化阶段完成
2025-07-09 20:34:44,327 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:44,341 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9924.0, 'mean': 9599.8, 'std': np.float64(111.93819723400945)}, 'diversity': np.float64(0.39191919191919194), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:44,341 - __main__ - INFO - --- Running Assessment Phase (Iteration 2) ---
2025-07-09 20:34:44,341 - EvolutionAssessmentExpert - INFO - --- Iteration 2 Assessment ---
2025-07-09 20:34:44,341 - multi_objective_assessment_expert - INFO - 开始多目标评估 - 迭代 2/5
2025-07-09 20:34:44,341 - multi_objective_assessment_expert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:34:44,342 - EvolutionAssessmentExpert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:34:44,342 - __main__ - INFO - --- Finished Evolution Iteration 3 ---
2025-07-09 20:34:44,342 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-09 20:34:44,343 - __main__ - INFO - --- Running Analysis Phase (Iteration 3) ---
2025-07-09 20:34:44,343 - improved_collaboration_manager - INFO - 开始协作决策 - 迭代 3/5
2025-07-09 20:34:44,343 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:44,356 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9924.0, 'mean': 9599.8, 'std': np.float64(111.93819723400945)}, 'diversity': np.float64(0.39191919191919194), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:44,356 - improved_collaboration_manager - ERROR - 景观分析失败: PathExpert.analyze() missing 1 required positional argument: 'distance_matrix'
2025-07-09 20:34:44,356 - improved_collaboration_manager - INFO - 应用自适应调整: {'landscape': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 3}, 'strategy': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 3}}
2025-07-09 20:34:44,357 - __main__ - WARNING - 改进协作模式未返回景观分析，回退到传统模式
2025-07-09 20:34:44,357 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:34:44,370 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9924.0, 'mean': 9599.8, 'std': np.float64(111.93819723400945)}, 'diversity': np.float64(0.39191919191919194), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:34:44,370 - PathExpert - INFO - 开始路径结构分析
2025-07-09 20:34:44,373 - PathExpert - INFO - 路径结构分析完成
2025-07-09 20:34:44,373 - EliteExpert - INFO - 开始精英解分析
2025-07-09 20:34:44,374 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-09 20:34:44,375 - LandscapeExpert - INFO - 开始景观分析
2025-07-09 20:34:44,376 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-09 20:34:44,376 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: TSP Landscape Analyzer (Small Problem)

### Status (Iter 3)
- Problem: 0 nodes, best cost: 9529.0
- Population diversity: 0.392
- Recent improvement: Not available

### Analysis
Based on the current state, determine:
1. Should we explore (find new areas) or exploit (improve current solutions)?
2. Which nodes/edges show promise?

### Output
JSON with: recommended_focus, promising_areas

### Example
```json
{
  "recommended_focus": "explore",
  "promising_areas": [2, 5, 8]
}
```

2025-07-09 20:34:44,376 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-09 20:34:54,415 - InterfaceAPI.gemini - ERROR - Gemini API请求网络错误: HTTPSConnectionPool(host='generativelanguage.googleapis.com', port=443): Read timed out. (read timeout=10)
2025-07-09 20:34:56,416 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 2/3)
2025-07-09 20:34:58,566 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "recommended_focus": "explore",
  "promising_areas": []
}
```

2025-07-09 20:34:58,567 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-09 20:34:58,567 - __main__ - INFO - 景观分析完整报告: {'recommended_focus': 'explore', 'promising_areas': []}
2025-07-09 20:34:58,567 - __main__ - INFO - 开始策略分配阶段
2025-07-09 20:34:58,567 - StrategyExpert - INFO - 开始策略分配分析
2025-07-09 20:34:58,568 - adaptive_strategy_expert - INFO - 开始自适应策略分析
2025-07-09 20:34:58,568 - adaptive_strategy_expert - INFO - 全局策略决策: balance, 评分: {'explore': 0.5, 'exploit': 0.5, 'balance': 0.7}
2025-07-09 20:34:58,568 - adaptive_strategy_expert - INFO - 策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:58,568 - StrategyExpert - INFO - 自适应策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:58,568 - __main__ - INFO - 策略分配报告: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:58,568 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:34:58,569 - __main__ - INFO - 策略分配阶段完成
2025-07-09 20:34:58,569 - __main__ - INFO - 策略分配: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:34:58,569 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:34:58,569 - __main__ - INFO - 开始进化阶段
2025-07-09 20:34:58,570 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-09 20:34:58,570 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:34:58,570 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:01,489 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:01,489 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:01,490 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-09 20:35:01,490 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:01,490 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:02,828 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:02,830 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:02,831 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-09 20:35:02,831 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:02,831 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:04,017 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:04,018 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:04,019 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-09 20:35:04,019 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:04,019 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:06,607 - ExploitationExpert - INFO - 自适应开发成功，改进: 51.00
2025-07-09 20:35:06,607 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36, 37, 27,  3,  9, 11,  7,  1,  0, 10,  8,  5,  4,  6,  2, 55, 56,
       59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9549.0), 'improvement': np.float64(51.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:06,608 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-09 20:35:06,608 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:06,608 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:06,861 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:06,862 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:06,863 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-09 20:35:06,863 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:06,863 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:06,910 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 9629.0 -> 9622.0
2025-07-09 20:35:07,135 - ExploitationExpert - INFO - 自适应开发成功，改进: 21.00
2025-07-09 20:35:07,136 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 56, 59, 58, 60, 54, 57, 64, 62, 53, 65,
       52, 63, 61, 55,  2,  8,  6,  4,  5,  9, 11, 10,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 37, 36, 26]), 'cur_cost': np.float64(9608.0), 'improvement': np.float64(21.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:07,136 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-09 20:35:07,137 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:07,137 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:08,544 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:08,545 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:08,546 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-09 20:35:08,546 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:08,546 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:09,634 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:09,635 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:09,644 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-09 20:35:09,644 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:09,645 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:11,859 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:11,860 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:11,860 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-09 20:35:11,860 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:11,861 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:11,944 - adaptive_exploitation_expert - INFO - 记忆增强搜索改进: 9924.0 -> 9895.0
2025-07-09 20:35:14,580 - ExploitationExpert - INFO - 自适应开发成功，改进: 362.00
2025-07-09 20:35:14,581 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35, 34, 30, 28, 32, 29, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,
        8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       45, 44, 41, 38, 51, 50, 42, 47, 46, 48, 49, 40, 43, 21, 20, 13, 19,
       16, 23, 22, 14, 15, 17, 12, 18, 27, 36, 37, 31, 33, 25, 26]), 'cur_cost': np.float64(9562.0), 'improvement': np.float64(362.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:14,583 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 37, 27,  3,  9, 11,  7,  1,  0, 10,  8,  5,  4,  6,  2, 55, 56,
       59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9549.0), 'improvement': np.float64(51.0), 'method': 'adaptive_exploitation'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 56, 59, 58, 60, 54, 57, 64, 62, 53, 65,
       52, 63, 61, 55,  2,  8,  6,  4,  5,  9, 11, 10,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 37, 36, 26]), 'cur_cost': np.float64(9608.0), 'improvement': np.float64(21.0), 'method': 'adaptive_exploitation'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 34, 30, 28, 32, 29, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,
        8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       45, 44, 41, 38, 51, 50, 42, 47, 46, 48, 49, 40, 43, 21, 20, 13, 19,
       16, 23, 22, 14, 15, 17, 12, 18, 27, 36, 37, 31, 33, 25, 26]), 'cur_cost': np.float64(9562.0), 'improvement': np.float64(362.0), 'method': 'adaptive_exploitation'}}]
2025-07-09 20:35:14,583 - __main__ - INFO - 进化阶段完成
2025-07-09 20:35:14,584 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:35:14,596 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9608.0, 'mean': 9556.4, 'std': np.float64(21.411212016137714)}, 'diversity': np.float64(0.31818181818181823), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:35:14,597 - __main__ - INFO - --- Running Assessment Phase (Iteration 3) ---
2025-07-09 20:35:14,597 - EvolutionAssessmentExpert - INFO - --- Iteration 3 Assessment ---
2025-07-09 20:35:14,597 - multi_objective_assessment_expert - INFO - 开始多目标评估 - 迭代 3/5
2025-07-09 20:35:14,597 - multi_objective_assessment_expert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:35:14,597 - EvolutionAssessmentExpert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:35:14,598 - __main__ - INFO - --- Finished Evolution Iteration 4 ---
2025-07-09 20:35:14,598 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-09 20:35:14,598 - __main__ - INFO - --- Running Analysis Phase (Iteration 4) ---
2025-07-09 20:35:14,598 - improved_collaboration_manager - INFO - 开始协作决策 - 迭代 4/5
2025-07-09 20:35:14,599 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:35:14,611 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9608.0, 'mean': 9556.4, 'std': np.float64(21.411212016137714)}, 'diversity': np.float64(0.31818181818181823), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:35:14,611 - improved_collaboration_manager - ERROR - 景观分析失败: PathExpert.analyze() missing 1 required positional argument: 'distance_matrix'
2025-07-09 20:35:14,611 - improved_collaboration_manager - INFO - 应用自适应调整: {'landscape': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 4}, 'strategy': {'type': 'reduce_weight', 'reason': 'execution_failure', 'iteration': 4}}
2025-07-09 20:35:14,612 - __main__ - WARNING - 改进协作模式未返回景观分析，回退到传统模式
2025-07-09 20:35:14,612 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:35:14,624 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9529.0, 'max': 9608.0, 'mean': 9556.4, 'std': np.float64(21.411212016137714)}, 'diversity': np.float64(0.31818181818181823), 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-07-09 20:35:14,625 - PathExpert - INFO - 开始路径结构分析
2025-07-09 20:35:14,627 - PathExpert - INFO - 路径结构分析完成
2025-07-09 20:35:14,627 - EliteExpert - INFO - 开始精英解分析
2025-07-09 20:35:14,627 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-09 20:35:14,629 - LandscapeExpert - INFO - 开始景观分析
2025-07-09 20:35:14,629 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-09 20:35:14,630 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: TSP Landscape Analyzer (Small Problem)

### Status (Iter 4)
- Problem: 0 nodes, best cost: 9529.0
- Population diversity: 0.318
- Recent improvement: Not available

### Analysis
Based on the current state, determine:
1. Should we explore (find new areas) or exploit (improve current solutions)?
2. Which nodes/edges show promise?

### Output
JSON with: recommended_focus, promising_areas

### Example
```json
{
  "recommended_focus": "explore",
  "promising_areas": [2, 5, 8]
}
```

2025-07-09 20:35:14,630 - InterfaceAPI.gemini - INFO - 发送请求到Gemini API (尝试 1/3)
2025-07-09 20:35:16,509 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "recommended_focus": "explore",
  "promising_areas": []
}
```

2025-07-09 20:35:16,509 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-09 20:35:16,509 - __main__ - INFO - 景观分析完整报告: {'recommended_focus': 'explore', 'promising_areas': []}
2025-07-09 20:35:16,510 - __main__ - INFO - 开始策略分配阶段
2025-07-09 20:35:16,510 - StrategyExpert - INFO - 开始策略分配分析
2025-07-09 20:35:16,510 - adaptive_strategy_expert - INFO - 开始自适应策略分析
2025-07-09 20:35:16,510 - adaptive_strategy_expert - INFO - 全局策略决策: balance, 评分: {'explore': 0.5, 'exploit': 0.5, 'balance': 0.7}
2025-07-09 20:35:16,510 - adaptive_strategy_expert - INFO - 策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:35:16,511 - StrategyExpert - INFO - 自适应策略分配完成: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:35:16,511 - __main__ - INFO - 策略分配报告: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:35:16,511 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:35:16,511 - __main__ - INFO - 策略分配阶段完成
2025-07-09 20:35:16,511 - __main__ - INFO - 策略分配: ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance']
2025-07-09 20:35:16,511 - __main__ - INFO - 策略分配完整报告: {'global_strategy_values': {'explore': 0.5, 'exploit': 0.5, 'balance': 0.5}, 'strategy_assignment': ['balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance', 'balance'], 'strategy_distribution': {'balance': 10}, 'strategy_ratios': {'balance': 1.0}, 'landscape_context': {'recommended_focus': 'explore', 'promising_areas': []}, 'assignment_rationale': '平衡探索开发以维持搜索效率'}
2025-07-09 20:35:16,512 - __main__ - INFO - 开始进化阶段
2025-07-09 20:35:16,512 - __main__ - INFO - 为个体 0 生成利用路径
2025-07-09 20:35:16,512 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:16,512 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:17,402 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:17,403 - __main__ - INFO - 个体 0 利用路径生成报告: {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:17,403 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-09 20:35:17,403 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:17,403 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:18,801 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:18,802 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:18,802 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-09 20:35:18,802 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:18,803 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:18,979 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:18,980 - __main__ - INFO - 个体 2 利用路径生成报告: {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:18,981 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-09 20:35:18,981 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:18,981 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:19,166 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:19,166 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([36, 37, 27,  3,  9, 11,  7,  1,  0, 10,  8,  5,  4,  6,  2, 55, 56,
       59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9549.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:19,167 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-09 20:35:19,167 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:19,167 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:20,337 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:20,338 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:20,338 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-09 20:35:20,338 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:20,339 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:22,329 - ExploitationExpert - INFO - 自适应开发成功，改进: 81.00
2025-07-09 20:35:22,330 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65,
       52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 37, 36, 26]), 'cur_cost': np.float64(9527.0), 'improvement': np.float64(81.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:22,330 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-09 20:35:22,331 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:22,331 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:23,208 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:23,209 - __main__ - INFO - 个体 6 利用路径生成报告: {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:23,209 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-09 20:35:23,209 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:23,209 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:23,387 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:23,389 - __main__ - INFO - 个体 7 利用路径生成报告: {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:23,389 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-09 20:35:23,389 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:23,390 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:24,577 - ExploitationExpert - INFO - 自适应开发成功，改进: 0.00
2025-07-09 20:35:24,578 - __main__ - INFO - 个体 8 利用路径生成报告: {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:24,578 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-09 20:35:24,579 - ExploitationExpert - INFO - 开始生成利用路径
2025-07-09 20:35:24,579 - adaptive_exploitation_expert - INFO - 开始自适应开发路径生成
2025-07-09 20:35:24,880 - ExploitationExpert - INFO - 自适应开发成功，改进: 14.00
2025-07-09 20:35:24,881 - __main__ - INFO - 个体 9 利用路径生成报告: {'new_tour': array([35, 34, 30, 28, 32, 29, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,
        8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 48, 49, 40, 43, 21, 20, 13, 19,
       16, 23, 22, 14, 15, 17, 12, 18, 27, 36, 37, 31, 33, 25, 26]), 'cur_cost': np.float64(9548.0), 'improvement': np.float64(14.0), 'method': 'adaptive_exploitation'}
2025-07-09 20:35:24,883 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'exploit', 'path_data': {'new_tour': array([39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 21, 20, 13,
       23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37, 36, 26, 25, 35, 34, 30,
       28, 32, 29, 33, 31, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8,
       10, 63, 52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55]), 'cur_cost': np.float64(9570.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58,
       60, 56, 59, 62, 53, 64, 57, 54, 65, 52, 63, 61, 55, 10,  8,  2,  6,
        4,  5,  9, 11,  0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35,
       25, 26, 36, 37, 27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 2, 'strategy': 'exploit', 'path_data': {'new_tour': array([44, 41, 51, 38, 45, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25,
       26, 36, 37, 27,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,  8, 10, 63,
       52, 65, 64, 57, 54, 60, 58, 56, 59, 62, 53, 61, 55, 14, 15, 17, 12,
       22, 23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39]), 'cur_cost': np.float64(9545.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([36, 37, 27,  3,  9, 11,  7,  1,  0, 10,  8,  5,  4,  6,  2, 55, 56,
       59, 62, 53, 61, 63, 52, 65, 64, 57, 54, 60, 58, 14, 15, 17, 12, 22,
       23, 18, 16, 19, 13, 20, 21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41,
       38, 51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26]), 'cur_cost': np.float64(9549.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49, 48, 43, 40, 34,
       35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 16, 18, 17,
       12, 22, 23, 13, 21, 20, 14, 15,  3,  7,  1,  0, 11,  9,  5,  4,  6,
        2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59]), 'cur_cost': np.float64(9529.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 40, 43, 48, 49, 47, 46, 42,
       50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65,
       52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 17,
       12, 18, 16, 23, 22, 15, 14, 20, 21, 13, 19, 27, 37, 36, 26]), 'cur_cost': np.float64(9527.0), 'improvement': np.float64(81.0), 'method': 'adaptive_exploitation'}}, {'individual': 6, 'strategy': 'exploit', 'path_data': {'new_tour': array([61, 55, 10,  8,  2,  6,  4,  5,  9, 11,  0,  1,  7,  3, 27, 37, 31,
       24, 29, 32, 28, 30, 34, 35, 33, 25, 26, 36, 19, 16, 18, 17, 12, 22,
       15, 14, 23, 13, 20, 21, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51,
       41, 44, 39, 58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63]), 'cur_cost': np.float64(9538.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 7, 'strategy': 'exploit', 'path_data': {'new_tour': array([54, 57, 64, 53, 62, 59, 56, 60, 58, 49, 40, 43, 48, 46, 47, 39, 44,
       45, 41, 38, 51, 50, 42, 34, 35, 30, 28, 32, 29, 24, 31, 33, 25, 26,
       36, 37, 27, 19, 21, 20, 13, 23, 16, 18, 17, 12, 22, 15, 14,  6,  2,
        8,  4,  5,  9, 11,  3,  7,  1,  0, 10, 55, 61, 63, 52, 65]), 'cur_cost': np.float64(9547.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 8, 'strategy': 'exploit', 'path_data': {'new_tour': array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 43, 48, 46, 42, 50, 51, 38, 41, 45, 44, 39, 47, 49, 40, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11]), 'cur_cost': np.float64(9571.0), 'improvement': np.float64(0.0), 'method': 'adaptive_exploitation'}}, {'individual': 9, 'strategy': 'exploit', 'path_data': {'new_tour': array([35, 34, 30, 28, 32, 29, 24,  3,  7,  1,  0, 11,  9,  5,  4,  6,  2,
        8, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 46, 47, 48, 49, 40, 43, 21, 20, 13, 19,
       16, 23, 22, 14, 15, 17, 12, 18, 27, 36, 37, 31, 33, 25, 26]), 'cur_cost': np.float64(9548.0), 'improvement': np.float64(14.0), 'method': 'adaptive_exploitation'}}]
2025-07-09 20:35:24,883 - __main__ - INFO - 进化阶段完成
2025-07-09 20:35:24,884 - StatsExpert - INFO - 开始统计分析
2025-07-09 20:35:24,896 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9527.0, 'max': 9571.0, 'mean': 9546.9, 'std': np.float64(13.866866985732573)}, 'diversity': np.float64(0.3016835016835017), 'clusters': {'clusters': 4, 'cluster_sizes': [5, 2, 2, 1]}, 'convergence': 0.0}
2025-07-09 20:35:24,896 - __main__ - INFO - --- Running Assessment Phase (Iteration 4) ---
2025-07-09 20:35:24,896 - EvolutionAssessmentExpert - INFO - --- Iteration 4 Assessment ---
2025-07-09 20:35:24,897 - multi_objective_assessment_expert - INFO - 开始多目标评估 - 迭代 4/5
2025-07-09 20:35:24,897 - multi_objective_assessment_expert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:35:24,897 - EvolutionAssessmentExpert - INFO - 多目标评估完成，总体评分: 0.400
2025-07-09 20:35:24,898 - __main__ - INFO - --- Finished Evolution Iteration 5 ---
2025-07-09 20:35:24,899 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-09 20:35:24,900 - __main__ - INFO - 实例 composite13_66 处理完成
