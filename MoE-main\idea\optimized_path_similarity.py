# 优化版路径相似度计算模块
# 使用numba加速相似度计算，提高种群更新效率

import numpy as np
import time
from collections import defaultdict
import logging
from numba import jit, prange

# 获取日志记录器
logger = logging.getLogger(__name__)

# 使用numba加速边集合相似度计算
@jit(nopython=True)
def calculate_edge_similarity(path1, path2):
    """
    使用numba加速计算两个路径的边集合相似度
    
    参数:
        path1, path2: 两个路径的numpy数组
        
    返回:
        float: 相似度，范围[0,1]，值越大表示越相似
    """
    # 检查路径长度是否相同
    if len(path1) != len(path2):
        return 0.0
    
    path_length = len(path1)
    
    # 创建边集合（使用numba兼容的方式）
    edges1 = set()
    edges2 = set()
    
    # 提取边集合
    for i in range(path_length):
        # 获取当前边的两个节点
        node1_1 = path1[i]
        node1_2 = path1[(i + 1) % path_length]
        # 确保边的表示是有序的（无向边）
        if node1_1 < node1_2:
            edge1 = (node1_1, node1_2)
        else:
            edge1 = (node1_2, node1_1)
        edges1.add(edge1)
        
        node2_1 = path2[i]
        node2_2 = path2[(i + 1) % path_length]
        # 确保边的表示是有序的（无向边）
        if node2_1 < node2_2:
            edge2 = (node2_1, node2_2)
        else:
            edge2 = (node2_2, node2_1)
        edges2.add(edge2)
    
    # 计算共享边数量
    shared_edges = 0
    for edge in edges1:
        if edge in edges2:
            shared_edges += 1
    
    # 计算相似度：共享边数量除以路径长度
    similarity = shared_edges / path_length
    
    return similarity

# 使用numba加速批量相似度计算
@jit(nopython=True, parallel=True)
def batch_calculate_similarity(path, paths):
    """
    批量计算一个路径与多个路径的相似度
    
    参数:
        path: 待比较的路径
        paths: 路径列表
        
    返回:
        similarities: 相似度列表
    """
    n_paths = len(paths)
    similarities = np.zeros(n_paths)
    
    # 并行计算相似度
    for i in prange(n_paths):
        similarities[i] = calculate_edge_similarity(path, paths[i])
    
    return similarities


class OptimizedPathSimilarityOptimizer:
    """优化版路径相似度优化器，提供高效的路径存储和相似度比较机制"""
    
    def __init__(self, similarity_threshold=0.9, max_cache_size=300):
        """
        初始化路径相似度优化器
        
        参数:
            similarity_threshold: 相似度阈值，当路径相似度超过此阈值时，认为路径相似
            max_cache_size: 最大缓存大小
        """
        self.similarity_threshold = similarity_threshold
        # 路径存储列表，保存原始路径
        self.paths = []
        # 路径数量计数
        self.path_count = 0
        # 缓存命中计数
        self.cache_hits = 0
        # 相似度计算次数
        self.similarity_calculations = 0
        # 相似度计算缓存
        self.similarity_cache = {}
        # 最大缓存大小
        self.max_cache_size = max_cache_size
        # 路径长度索引，用于快速筛选
        self.path_length_index = defaultdict(list)
        # 批量计算阈值，当路径数量超过此值时使用批量计算
        self.batch_threshold = 20
    
    def _calculate_similarity_cached(self, path1, path2):
        """
        带缓存的相似度计算
        
        参数:
            path1, path2: 两个路径
            
        返回:
            float: 相似度，范围[0,1]，值越大表示越相似
        """
        # 增加计算次数计数
        self.similarity_calculations += 1
        
        # 确保路径是numpy数组
        if not isinstance(path1, np.ndarray):
            path1 = np.array(path1, dtype=np.int64)
        else:
            # Cast in-place if necessary (copy=False keeps original when already int64)
            path1 = path1.astype(np.int64, copy=False)
        if not isinstance(path2, np.ndarray):
            path2 = np.array(path2, dtype=np.int64)
        else:
            path2 = path2.astype(np.int64, copy=False)
        
        # 检查路径长度是否相同
        if len(path1) != len(path2):
            return 0.0
        
        # 生成缓存键（使用路径的哈希值）
        path1_hash = hash(path1.tobytes())
        path2_hash = hash(path2.tobytes())
        cache_key = (min(path1_hash, path2_hash), max(path1_hash, path2_hash))
        
        # 检查缓存
        if cache_key in self.similarity_cache:
            self.cache_hits += 1
            return self.similarity_cache[cache_key]
        
        # 计算相似度
        similarity = calculate_edge_similarity(path1, path2)
        
        # 更新缓存
        self.similarity_cache[cache_key] = similarity
        
        # 限制缓存大小
        if len(self.similarity_cache) > self.max_cache_size:
            # 删除最早添加的缓存项（简单实现）
            keys_to_remove = list(self.similarity_cache.keys())[:len(self.similarity_cache) // 2]
            for key in keys_to_remove:
                del self.similarity_cache[key]
        
        return similarity
    
    def add_path(self, path):
        """
        添加路径到存储集合
        
        参数:
            path: 待添加的路径
            
        返回:
            int: 路径ID
        """
        # 确保路径是numpy数组
        if not isinstance(path, np.ndarray):
            path = np.array(path, dtype=np.int64)
        else:
            path = path.astype(np.int64, copy=False)
        
        # 生成路径ID
        path_id = self.path_count
        
        # 存储原始路径
        self.paths.append(path)
        
        # 更新路径长度索引
        self.path_length_index[len(path)].append(path_id)
        
        # 增加路径计数
        self.path_count += 1
        
        return path_id
    
    def check_similarity(self, path):
        """
        检查路径与已存储路径的相似度
        
        参数:
            path: 待检查的路径
            
        返回:
            tuple: (是否相似, 最相似路径ID, 最大相似度)
        """
        if not self.paths:
            return False, -1, 0.0
        
        # 确保路径是numpy数组
        if not isinstance(path, np.ndarray):
            path = np.array(path, dtype=np.int64)
        else:
            path = path.astype(np.int64, copy=False)
        
        # 使用路径长度进行初步筛选
        path_length = len(path)
        candidate_ids = self.path_length_index.get(path_length, [])
        
        # 如果没有候选路径，返回False
        if not candidate_ids:
            return False, -1, 0.0
        
        # 获取候选路径
        candidate_paths = [self.paths[path_id] for path_id in candidate_ids]
        
        # 根据候选路径数量选择计算方式
        if len(candidate_paths) > self.batch_threshold:
            # 批量计算相似度
            # 转换为numpy数组
            np_candidate_paths = []
            for p in candidate_paths:
                if not isinstance(p, np.ndarray):
                    np_candidate_paths.append(np.array(p, dtype=np.int64))
                else:
                    if p.dtype != np.int64:
                        p = p.astype(np.int64, copy=False)
                    np_candidate_paths.append(p)
            
            # 批量计算相似度
            similarities = batch_calculate_similarity(path, np_candidate_paths)
            
            # 更新计算次数
            self.similarity_calculations += len(candidate_paths)
            
            # 找到最大相似度及其索引
            max_similarity = np.max(similarities)
            max_index = np.argmax(similarities)
            max_similar_id = candidate_ids[max_index]
        else:
            # 逐个计算相似度
            max_similarity = 0.0
            max_similar_id = -1
            
            for path_id in candidate_ids:
                path_to_compare = self.paths[path_id]
                similarity = self._calculate_similarity_cached(path, path_to_compare)
                
                # 更新最大相似度
                if similarity > max_similarity:
                    max_similarity = similarity
                    max_similar_id = path_id
                    
                    # 如果相似度超过阈值，提前返回
                    if similarity >= self.similarity_threshold:
                        return True, max_similar_id, max_similarity
        
        # 判断是否相似
        is_similar = max_similarity >= self.similarity_threshold
        
        return is_similar, max_similar_id, max_similarity
    
    def calculate_similarity(self, path1, path2):
        """
        计算两条路径之间的相似度
        
        参数:
            path1, path2: 两个路径
            
        返回:
            float: 相似度，范围[0,1]，值越大表示越相似
        """
        return self._calculate_similarity_cached(path1, path2)
    
    def get_statistics(self):
        """
        获取统计信息
        
        返回:
            dict: 统计信息字典
        """
        return {
            'path_count': self.path_count,
            'cache_hits': self.cache_hits,
            'similarity_calculations': self.similarity_calculations,
            'cache_hit_rate': self.cache_hits / max(1, self.similarity_calculations),
            'cache_size': len(self.similarity_cache)
        }
    
    def clear_cache(self):
        """清除缓存"""
        self.similarity_cache.clear()
        self.cache_hits = 0
        self.similarity_calculations = 0


# 兼容原有PathSimilarityOptimizer的接口
class PathSimilarityOptimizer(OptimizedPathSimilarityOptimizer):
    """兼容原有PathSimilarityOptimizer的接口"""
    pass


# 示例用法
def example_usage():
    # 创建路径相似度优化器
    optimizer = OptimizedPathSimilarityOptimizer(similarity_threshold=0.7)
    
    # 添加一些路径
    path1 = [0, 1, 2, 3, 4, 5, 0]
    path2 = [0, 1, 2, 4, 3, 5, 0]  # 与path1相似
    path3 = [0, 5, 4, 3, 2, 1, 0]  # 与path1完全相反
    path4 = [0, 2, 4, 5, 3, 1, 0]  # 与path1不太相似
    
    # 添加路径
    id1 = optimizer.add_path(path1)
    
    # 检查相似度
    is_similar, similar_id, similarity = optimizer.check_similarity(path2)
    print(f"Path2 similar to stored paths: {is_similar}, similar to path {similar_id}, similarity: {similarity:.4f}")
    
    is_similar, similar_id, similarity = optimizer.check_similarity(path3)
    print(f"Path3 similar to stored paths: {is_similar}, similar to path {similar_id}, similarity: {similarity:.4f}")
    
    is_similar, similar_id, similarity = optimizer.check_similarity(path4)
    print(f"Path4 similar to stored paths: {is_similar}, similar to path {similar_id}, similarity: {similarity:.4f}")
    
    # 添加更多路径
    id2 = optimizer.add_path(path2)
    id3 = optimizer.add_path(path3)
    id4 = optimizer.add_path(path4)
    
    # 计算路径之间的相似度
    similarity = optimizer.calculate_similarity(path1, path2)
    print(f"Similarity between path1 and path2: {similarity:.4f}")
    
    similarity = optimizer.calculate_similarity(path1, path3)
    print(f"Similarity between path1 and path3: {similarity:.4f}")
    
    similarity = optimizer.calculate_similarity(path1, path4)
    print(f"Similarity between path1 and path4: {similarity:.4f}")
    
    # 获取统计信息
    stats = optimizer.get_statistics()
    print(f"Statistics: {stats}")


if __name__ == "__main__":
    example_usage()



# 路径相似度优化模块
# 提供高效的路径存储和相似度比较机制
# 使用Numba JIT编译加速计算密集型函数

# import numpy as np
# import time
# from collections import defaultdict
# import logging
# from numba import jit


# # 获取日志记录器
# logger = logging.getLogger(__name__)


# # 使用numba加速边集合相似度计算
# @jit(nopython=True)
# def calculate_path_similarity(path1, path2):
#     """
#     使用numba加速计算两个路径的边集合相似度
    
#     参数:
#         path1, path2: 两个路径的numpy数组
        
#     返回:
#         float: 相似度，范围[0,1]，值越大表示越相似
#     """
#     # 检查路径长度是否相同
#     if len(path1) != len(path2):
#         return 0.0
    
#     path_length = len(path1)
    
#     # 创建边集合（使用numba兼容的方式）
#     edges1 = set()
#     edges2 = set()
    
#     # 提取边集合
#     for i in range(path_length):
#         # 获取当前边的两个节点
#         node1_1 = path1[i]
#         node1_2 = path1[(i + 1) % path_length]
#         # 确保边的表示是有序的（无向边）
#         if node1_1 < node1_2:
#             edge1 = (node1_1, node1_2)
#         else:
#             edge1 = (node1_2, node1_1)
#         edges1.add(edge1)
        
#         node2_1 = path2[i]
#         node2_2 = path2[(i + 1) % path_length]
#         # 确保边的表示是有序的（无向边）
#         if node2_1 < node2_2:
#             edge2 = (node2_1, node2_2)
#         else:
#             edge2 = (node2_2, node2_1)
#         edges2.add(edge2)
    
#     # 计算共享边数量
#     shared_edges = 0
#     for edge in edges1:
#         if edge in edges2:
#             shared_edges += 1
    
#     # 计算相似度：共享边数量除以路径长度
#     similarity = shared_edges / path_length
    
#     return similarity


# # 使用numba加速提取路径特征中的边集合
# @jit(nopython=True)
# def extract_path_edges(path):
#     """
#     使用numba加速提取路径的边集合
    
#     参数:
#         path: 路径的numpy数组
        
#     返回:
#         edges: 边集合
#         edge_count: 边数量
#     """
#     path_length = len(path)
#     edges = set()
    
#     # 提取边集合
#     for i in range(path_length):
#         # 获取当前边的两个节点
#         node1 = path[i]
#         node2 = path[(i + 1) % path_length]
#         # 确保边的表示是有序的（无向边）
#         if node1 < node2:
#             edge = (node1, node2)
#         else:
#             edge = (node2, node1)
#         edges.add(edge)
    
#     return edges, len(edges)


# # 使用numba加速批量相似度计算
# @jit(nopython=True, parallel=True)
# def batch_calculate_similarity(path, paths):
#     """
#     批量计算一个路径与多个路径的相似度
    
#     参数:
#         path: 待比较的路径
#         paths: 路径列表
        
#     返回:
#         similarities: 相似度列表
#     """
#     n_paths = len(paths)
#     similarities = np.zeros(n_paths)
    
#     # 并行计算相似度
#     for i in range(n_paths):
#         similarities[i] = calculate_path_similarity(path, paths[i])
    
#     return similarities


# class PathSimilarityOptimizer:
#     """路径相似度优化器，提供高效的路径存储和相似度比较机制"""
    
#     def __init__(self, similarity_threshold=0.9, max_cache_size=10000, batch_threshold=20):
#         """初始化路径相似度优化器
        
#         参数:
#             similarity_threshold: 相似度阈值，当路径相似度超过此阈值时，认为路径相似
#             max_cache_size: 最大缓存大小
#             batch_threshold: 批量计算阈值，当候选路径数量超过此值时使用批量计算
#         """
#         self.similarity_threshold = similarity_threshold
#         # 路径存储集合，使用哈希表存储路径特征
#         self.path_features = {}
#         # 路径存储列表，保存原始路径
#         self.paths = []
#         # 边频率统计，用于快速计算相似度
#         self.edge_frequency = defaultdict(int)
#         # 路径数量计数
#         self.path_count = 0
#         # 缓存命中计数
#         self.cache_hits = 0
#         # 相似度计算次数
#         self.similarity_calculations = 0
#         # 相似度计算缓存
#         self.similarity_cache = {}
#         # 最大缓存大小
#         self.max_cache_size = max_cache_size
#         # 批量计算阈值
#         self.batch_threshold = batch_threshold
    
#     def _extract_path_features(self, path):
#         """提取路径特征，生成路径特征向量或指纹
        
#         参数:
#             path: 路径
            
#         返回:
#             dict: 路径特征字典，包含边集合、节点频率等特征
#         """
#         # 确保路径是列表类型
#         if isinstance(path, np.ndarray):
#             path = path.tolist()
        
#         # 提取边集合（无向边）
#         edges = set()
#         for i in range(len(path)):
#             # 获取当前边的两个节点
#             node1 = path[i]
#             node2 = path[(i + 1) % len(path)]
#             # 确保边的表示是有序的（无向边）
#             edge = (min(node1, node2), max(node1, node2))
#             edges.add(edge)
        
#         # 计算节点频率
#         node_frequency = defaultdict(int)
#         for node in path:
#             node_frequency[node] += 1
        
#         # 计算路径长度
#         path_length = len(path)
        
#         # 生成路径特征字典
#         features = {
#             'edges': edges,
#             'node_frequency': dict(node_frequency),
#             'path_length': path_length,
#             'edge_count': len(edges)
#         }
        
#         return features
    
#     def _calculate_similarity_fast(self, features1, features2):
#         """快速计算两个路径特征之间的相似度
        
#         参数:
#             features1, features2: 两个路径特征字典
            
#         返回:
#             float: 相似度，范围[0,1]，值越大表示越相似
#         """
#         # 检查路径长度是否相同
#         if features1['path_length'] != features2['path_length']:
#             return 0.0
        
#         # 计算共享边数量
#         shared_edges = len(features1['edges'].intersection(features2['edges']))
        
#         # 计算相似度：共享边数量除以路径长度
#         similarity = shared_edges / features1['path_length']
        
#         return similarity
    
#     def _calculate_similarity_cached(self, path1, path2):
#         """带缓存的相似度计算
        
#         参数:
#             path1, path2: 两个路径
            
#         返回:
#             float: 相似度，范围[0,1]，值越大表示越相似
#         """
#         # 增加计算次数计数
#         self.similarity_calculations += 1
        
#         # 生成缓存键所需的可哈希类型
#         if isinstance(path1, np.ndarray):
#             path1_hash = hash(path1.tobytes())
#         elif isinstance(path1, list):
#             path1_hash = hash(tuple(path1))
#         else:
#             path1_hash = hash(path1)
            
#         if isinstance(path2, np.ndarray):
#             path2_hash = hash(path2.tobytes())
#         elif isinstance(path2, list):
#             path2_hash = hash(tuple(path2))
#         else:
#             path2_hash = hash(path2)
        
#         # 生成缓存键（确保顺序一致）
#         cache_key = (min(path1_hash, path2_hash), max(path1_hash, path2_hash))
        
#         # 检查缓存
#         if cache_key in self.similarity_cache:
#             self.cache_hits += 1
#             return self.similarity_cache[cache_key]
        
#         # 确保路径是numpy数组以便使用JIT优化函数
#         if not isinstance(path1, np.ndarray):
#             path1_np = np.array(path1)
#         else:
#             path1_np = path1
            
#         if not isinstance(path2, np.ndarray):
#             path2_np = np.array(path2)
#         else:
#             path2_np = path2
        
#         # 使用JIT优化的函数计算相似度
#         similarity = calculate_path_similarity(path1_np, path2_np)
        
#         # 更新缓存
#         self.similarity_cache[cache_key] = similarity
        
#         # 限制缓存大小
#         if len(self.similarity_cache) > self.max_cache_size:
#             # 删除最早添加的缓存项（简单实现）
#             keys_to_remove = list(self.similarity_cache.keys())[:len(self.similarity_cache) // 2]
#             for key in keys_to_remove:
#                 del self.similarity_cache[key]
        
#         return similarity
    
#     def add_path(self, path):
#         """添加路径到存储集合
        
#         参数:
#             path: 待添加的路径
            
#         返回:
#             int: 路径ID
#         """
#         # 确保路径是numpy数组，以便使用JIT优化函数
#         if not isinstance(path, np.ndarray):
#             path_np = np.array(path)
#         else:
#             path_np = path
            
#         # 存储原始路径（保持为numpy数组以便后续计算）
#         self.paths.append(path_np)
        
#         # 提取路径特征（同时使用Python原生特征字典和JIT优化）
#         features = self._extract_path_features(path_np)
        
#         # 生成路径ID
#         path_id = self.path_count
        
#         # 存储路径特征
#         self.path_features[path_id] = features
        
#         # 更新边频率统计
#         for edge in features['edges']:
#             self.edge_frequency[edge] += 1
        
#         # 增加路径计数
#         self.path_count += 1
        
#         return path_id
    
#     def check_similarity(self, path):
#         """检查路径与已存储路径的相似度
        
#         参数:
#             path: 待检查的路径
            
#         返回:
#             tuple: (是否相似, 最相似路径ID, 最大相似度)
#         """
#         if not self.paths:
#             return False, -1, 0.0
        
#         # 确保路径是numpy数组
#         if not isinstance(path, np.ndarray):
#             path = np.array(path)
        
#         # 使用路径长度进行初步筛选
#         path_length = len(path)
#         candidate_ids = []
        
#         # 筛选相同长度的路径
#         for path_id, stored_path in enumerate(self.paths):
#             if len(stored_path) == path_length:
#                 candidate_ids.append(path_id)
        
#         # 如果没有候选路径，返回False
#         if not candidate_ids:
#             return False, -1, 0.0
        
#         # 根据候选路径数量选择计算方式
#         if len(candidate_ids) > self.batch_threshold:
#             # 批量计算相似度
#             # 准备候选路径数组
#             candidate_paths = []
#             for path_id in candidate_ids:
#                 path_to_compare = self.paths[path_id]
#                 if not isinstance(path_to_compare, np.ndarray):
#                     candidate_paths.append(np.array(path_to_compare))
#                 else:
#                     candidate_paths.append(path_to_compare)
            
#             # 批量计算相似度
#             similarities = batch_calculate_similarity(path, candidate_paths)
            
#             # 更新计算次数
#             self.similarity_calculations += len(candidate_ids)
            
#             # 找到最大相似度及其索引
#             max_similarity = np.max(similarities)
#             max_index = np.argmax(similarities)
#             max_similar_id = candidate_ids[max_index]
#         else:
#             # 逐个计算相似度
#             max_similarity = 0.0
#             max_similar_id = -1
            
#             for path_id in candidate_ids:
#                 # 使用带缓存的相似度计算函数，确保正确统计缓存命中率
#                 path_to_compare = self.paths[path_id]
#                 similarity = self._calculate_similarity_cached(path, path_to_compare)
                
#                 # 更新最大相似度
#                 if similarity > max_similarity:
#                     max_similarity = similarity
#                     max_similar_id = path_id
                    
#                     # 如果相似度超过阈值，提前返回
#                     if similarity >= self.similarity_threshold:
#                         return True, max_similar_id, max_similarity
        
#         # 判断是否相似
#         is_similar = max_similarity >= self.similarity_threshold
        
#         return is_similar, max_similar_id, max_similarity
    
#     def _filter_candidates(self, features):
#         """使用路径特征进行初步筛选，减少需要详细比较的路径数量
        
#         参数:
#             features: 路径特征
            
#         返回:
#             list: 候选路径ID列表
#         """
#         # 如果路径数量较少，返回所有路径ID
#         if self.path_count < 10:
#             return list(self.path_features.keys())
        
#         # 使用路径长度进行初步筛选
#         candidates = []
#         for path_id, path_features in self.path_features.items():
#             # 路径长度必须相同
#             if path_features['path_length'] != features['path_length']:
#                 continue
                
#             # 边数量应该接近
#             if abs(path_features['edge_count'] - features['edge_count']) > 5:
#                 continue
                
#             # 添加到候选列表
#             candidates.append(path_id)
        
#         return candidates
    
#     def calculate_similarity(self, path1, path2):
#         """计算两条路径之间的相似度
        
#         参数:
#             path1, path2: 两个路径
            
#         返回:
#             float: 相似度，范围[0,1]，值越大表示越相似
#         """
#         return self._calculate_similarity_cached(path1, path2)
    
#     def get_statistics(self):
#         """获取统计信息
        
#         返回:
#             dict: 统计信息字典
#         """
#         return {
#             'path_count': self.path_count,
#             'cache_hits': self.cache_hits,
#             'similarity_calculations': self.similarity_calculations,
#             'cache_hit_rate': self.cache_hits / max(1, self.similarity_calculations),
#             'cache_size': len(self.similarity_cache)
#         }
    
#     def clear_cache(self):
#         """清除缓存"""
#         self.similarity_cache.clear()
#         self.cache_hits = 0
#         self.similarity_calculations = 0


# # 示例用法
# def example_usage():
#     # 创建路径相似度优化器
#     optimizer = PathSimilarityOptimizer(similarity_threshold=0.7)
    
#     # 添加一些路径
#     path1 = [0, 1, 2, 3, 4, 5, 0]
#     path2 = [0, 1, 2, 4, 3, 5, 0]  # 与path1相似
#     path3 = [0, 5, 4, 3, 2, 1, 0]  # 与path1完全相反
#     path4 = [0, 2, 4, 5, 3, 1, 0]  # 与path1不太相似
    
#     # 添加路径
#     id1 = optimizer.add_path(path1)
    
#     # 检查相似度
#     is_similar, similar_id, similarity = optimizer.check_similarity(path2)
#     print(f"Path2 similar to stored paths: {is_similar}, similar to path {similar_id}, similarity: {similarity:.4f}")
    
#     is_similar, similar_id, similarity = optimizer.check_similarity(path3)
#     print(f"Path3 similar to stored paths: {is_similar}, similar to path {similar_id}, similarity: {similarity:.4f}")
    
#     is_similar, similar_id, similarity = optimizer.check_similarity(path4)
#     print(f"Path4 similar to stored paths: {is_similar}, similar to path {similar_id}, similarity: {similarity:.4f}")
    
#     # 添加更多路径
#     id2 = optimizer.add_path(path2)
#     id3 = optimizer.add_path(path3)
#     id4 = optimizer.add_path(path4)
    
#     # 计算路径之间的相似度
#     similarity = optimizer.calculate_similarity(path1, path2)
#     print(f"Similarity between path1 and path2: {similarity:.4f}")
    
#     similarity = optimizer.calculate_similarity(path1, path3)
#     print(f"Similarity between path1 and path3: {similarity:.4f}")
    
#     similarity = optimizer.calculate_similarity(path1, path4)
#     print(f"Similarity between path1 and path4: {similarity:.4f}")
    
#     # 获取统计信息
#     stats = optimizer.get_statistics()
#     print(f"Statistics: {stats}")


# if __name__ == "__main__":
#     example_usage()