2025-06-26 10:51:55,807 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-26 10:51:55,807 - __main__ - INFO - 开始分析阶段
2025-06-26 10:51:55,807 - StatsExpert - INFO - 开始统计分析
2025-06-26 10:51:55,827 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9972.0, 'max': 120857.0, 'mean': 78388.9, 'std': 45212.398201046584}, 'diversity': 0.9245791245791245, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 10:51:55,828 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9972.0, 'max': 120857.0, 'mean': 78388.9, 'std': 45212.398201046584}, 'diversity_level': 0.9245791245791245, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-26 10:51:55,829 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 10:51:55,829 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 10:51:55,829 - PathExpert - INFO - 开始路径结构分析
2025-06-26 10:51:55,834 - PathExpert - INFO - 路径结构分析完成
2025-06-26 10:51:55,834 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (8, 2, 6), 'frequency': 0.3}, {'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}, {'subpath': (53, 62, 59), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}, {'subpath': (59, 56, 58), 'frequency': 0.3}, {'subpath': (56, 58, 60), 'frequency': 0.3}, {'subpath': (58, 60, 64), 'frequency': 0.3}, {'subpath': (60, 64, 57), 'frequency': 0.3}, {'subpath': (64, 57, 54), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(14, 15)', 'frequency': 0.4}, {'edge': '(25, 26)', 'frequency': 0.4}, {'edge': '(3, 7)', 'frequency': 0.4}, {'edge': '(53, 62)', 'frequency': 0.4}, {'edge': '(59, 62)', 'frequency': 0.4}, {'edge': '(52, 65)', 'frequency': 0.4}, {'edge': '(41, 50)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(12, 18)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(14, 20)', 'frequency': 0.2}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(13, 21)', 'frequency': 0.3}, {'edge': '(13, 19)', 'frequency': 0.2}, {'edge': '(17, 27)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.2}, {'edge': '(25, 37)', 'frequency': 0.2}, {'edge': '(26, 36)', 'frequency': 0.2}, {'edge': '(35, 36)', 'frequency': 0.2}, {'edge': '(28, 35)', 'frequency': 0.2}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.2}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.2}, {'edge': '(24, 29)', 'frequency': 0.2}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(3, 32)', 'frequency': 0.2}, {'edge': '(1, 7)', 'frequency': 0.3}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(9, 11)', 'frequency': 0.3}, {'edge': '(5, 9)', 'frequency': 0.2}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.3}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(0, 55)', 'frequency': 0.2}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(39, 63)', 'frequency': 0.3}, {'edge': '(39, 44)', 'frequency': 0.3}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(38, 51)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(41, 46)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.3}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(42, 48)', 'frequency': 0.3}, {'edge': '(28, 32)', 'frequency': 0.2}, {'edge': '(22, 23)', 'frequency': 0.2}, {'edge': '(15, 17)', 'frequency': 0.2}, {'edge': '(13, 20)', 'frequency': 0.3}, {'edge': '(18, 63)', 'frequency': 0.2}, {'edge': '(17, 18)', 'frequency': 0.2}, {'edge': '(30, 51)', 'frequency': 0.2}, {'edge': '(3, 35)', 'frequency': 0.3}, {'edge': '(40, 42)', 'frequency': 0.2}, {'edge': '(16, 26)', 'frequency': 0.2}, {'edge': '(39, 61)', 'frequency': 0.2}, {'edge': '(14, 43)', 'frequency': 0.2}, {'edge': '(20, 32)', 'frequency': 0.2}, {'edge': '(36, 61)', 'frequency': 0.2}, {'edge': '(11, 52)', 'frequency': 0.2}, {'edge': '(17, 31)', 'frequency': 0.2}, {'edge': '(21, 65)', 'frequency': 0.2}, {'edge': '(41, 53)', 'frequency': 0.2}, {'edge': '(5, 51)', 'frequency': 0.2}, {'edge': '(19, 32)', 'frequency': 0.2}, {'edge': '(24, 64)', 'frequency': 0.2}, {'edge': '(22, 56)', 'frequency': 0.2}, {'edge': '(16, 43)', 'frequency': 0.2}, {'edge': '(3, 10)', 'frequency': 0.2}, {'edge': '(26, 57)', 'frequency': 0.2}, {'edge': '(4, 45)', 'frequency': 0.2}, {'edge': '(23, 34)', 'frequency': 0.2}, {'edge': '(9, 12)', 'frequency': 0.2}, {'edge': '(58, 59)', 'frequency': 0.2}, {'edge': '(22, 65)', 'frequency': 0.2}, {'edge': '(4, 40)', 'frequency': 0.2}, {'edge': '(57, 63)', 'frequency': 0.2}, {'edge': '(20, 63)', 'frequency': 0.2}, {'edge': '(15, 18)', 'frequency': 0.2}, {'edge': '(40, 64)', 'frequency': 0.2}, {'edge': '(0, 53)', 'frequency': 0.2}, {'edge': '(24, 55)', 'frequency': 0.2}, {'edge': '(49, 51)', 'frequency': 0.2}, {'edge': '(1, 47)', 'frequency': 0.2}, {'edge': '(38, 54)', 'frequency': 0.2}, {'edge': '(19, 33)', 'frequency': 0.2}, {'edge': '(33, 57)', 'frequency': 0.2}, {'edge': '(4, 62)', 'frequency': 0.2}, {'edge': '(10, 27)', 'frequency': 0.2}, {'edge': '(6, 29)', 'frequency': 0.2}, {'edge': '(34, 46)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [28, 52, 44, 60, 29, 50, 64, 24], 'cost': 19583.0, 'size': 8}, {'region': [63, 25, 45, 58, 38, 65], 'cost': 13509.0, 'size': 6}, {'region': [7, 51, 30, 41, 8], 'cost': 9386.0, 'size': 5}, {'region': [55, 24, 60, 39], 'cost': 8582.0, 'size': 4}, {'region': [31, 62, 38, 55], 'cost': 8560.0, 'size': 4}]}
2025-06-26 10:51:55,835 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 10:51:55,835 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 10:51:55,835 - EliteExpert - INFO - 开始精英解分析
2025-06-26 10:51:55,835 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-26 10:51:55,836 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-26 10:51:55,836 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 10:51:55,836 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 10:51:56,678 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 10:51:56,678 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9972.0, mean 78388.9, max 120857.0, std 45212.398201046584
- diversity: 0.9245791245791245
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: [{'subpath': (8, 2, 6), 'frequency': 0.3}, {'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [28, 52, 44, 60, 29, 50, 64, 24], 'cost': 19583.0, 'size': 8}, {'region': [63, 25, 45, 58, 38, 65], 'cost': 13509.0, 'size': 6}, {'region': [7, 51, 30, 41, 8], 'cost': 9386.0, 'size': 5}]
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 10:51:56,678 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:52:26,944 - InterfaceAPI.deepseek - ERROR - DeepSeek API请求网络错误 (尝试 1/3): HTTPSConnectionPool(host='api.deepseek.com', port=443): Read timed out.
2025-06-26 10:52:26,944 - InterfaceAPI.deepseek - INFO - 等待 2 秒后重试...
2025-06-26 10:52:28,947 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 2/3)
2025-06-26 10:52:58,870 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range of edge lengths from 10.0 to 3032.0, with significant standard deviation)",
    "modality": "Potentially multimodal (presence of common subpaths suggests multiple local optima)",
    "deceptiveness": "Moderate (long_edge_ratio of 0.249 indicates some deceptive long edges that may mislead search)"
  },
  "population_state": {
    "coverage": "Good initial diversity (0.925) but uneven spatial distribution (density_grid shows sparse and dense regions)",
    "convergence": "Early stage (0.0 convergence, no stagnation detected)",
    "quality": "Large cost variance (min 9972.0 vs max 120857.0) suggests exploration phase"
  },
  "difficult_regions": [
    {
      "region": [28, 52, 44, 60, 29, 50, 64, 24],
      "reason": "High cost (19583.0) and located in sparse grid cells"
    },
    {
      "region": [63, 25, 45, 58, 38, 65],
      "reason": "High cost (13509.0) with long edges to adjacent nodes"
    },
    {
      "region": [7, 51, 30, 41, 8],
      "reason": "Contains common subpath (8,2,6) but high local cost (9386.0)"
    }
  ],
  "opportunity_regions": [
    {
      "region": [55, 61, 53, 62],
      "reason": "Frequent subpaths in population (frequency=0.3) and medium edge lengths"
    },
    {
      "region": "Top-left grid cell (14 nodes)",
      "reason": "High node density but currently underutilized in solutions"
    },
    {
      "region": "Bottom-right grid cell (14 nodes)",
      "reason": "High node density with directional consistency (sector 4 dominance)"
    }
  ],
  "evolution_phase": "Early exploration",
  "evolution_direction": [
    "Prioritize exploitation of high-density opportunity regions through 2-opt local search",
    "Use edge recombination on common subpaths (55,61,53) and (61,53,62)",
    "Apply guided mutation to break difficult regions (e.g. swap nodes between sparse cells)",
    "Maintain diversity through restart mechanism if convergence stagnates"
  ]
}
```
2025-06-26 10:52:58,870 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 10:52:58,870 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 3032.0, with significant standard deviation)', 'modality': 'Potentially multimodal (presence of common subpaths suggests multiple local optima)', 'deceptiveness': 'Moderate (long_edge_ratio of 0.249 indicates some deceptive long edges that may mislead search)'}, 'population_state': {'coverage': 'Good initial diversity (0.925) but uneven spatial distribution (density_grid shows sparse and dense regions)', 'convergence': 'Early stage (0.0 convergence, no stagnation detected)', 'quality': 'Large cost variance (min 9972.0 vs max 120857.0) suggests exploration phase'}, 'difficult_regions': [{'region': [28, 52, 44, 60, 29, 50, 64, 24], 'reason': 'High cost (19583.0) and located in sparse grid cells'}, {'region': [63, 25, 45, 58, 38, 65], 'reason': 'High cost (13509.0) with long edges to adjacent nodes'}, {'region': [7, 51, 30, 41, 8], 'reason': 'Contains common subpath (8,2,6) but high local cost (9386.0)'}], 'opportunity_regions': [{'region': [55, 61, 53, 62], 'reason': 'Frequent subpaths in population (frequency=0.3) and medium edge lengths'}, {'region': 'Top-left grid cell (14 nodes)', 'reason': 'High node density but currently underutilized in solutions'}, {'region': 'Bottom-right grid cell (14 nodes)', 'reason': 'High node density with directional consistency (sector 4 dominance)'}], 'evolution_phase': 'Early exploration', 'evolution_direction': ['Prioritize exploitation of high-density opportunity regions through 2-opt local search', 'Use edge recombination on common subpaths (55,61,53) and (61,53,62)', 'Apply guided mutation to break difficult regions (e.g. swap nodes between sparse cells)', 'Maintain diversity through restart mechanism if convergence stagnates']}
2025-06-26 10:52:58,870 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 10:52:58,870 - __main__ - INFO - 分析阶段完成
2025-06-26 10:52:58,870 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range of edge lengths from 10.0 to 3032.0, with significant standard deviation)', 'modality': 'Potentially multimodal (presence of common subpaths suggests multiple local optima)', 'deceptiveness': 'Moderate (long_edge_ratio of 0.249 indicates some deceptive long edges that may mislead search)'}, 'population_state': {'coverage': 'Good initial diversity (0.925) but uneven spatial distribution (density_grid shows sparse and dense regions)', 'convergence': 'Early stage (0.0 convergence, no stagnation detected)', 'quality': 'Large cost variance (min 9972.0 vs max 120857.0) suggests exploration phase'}, 'difficult_regions': [{'region': [28, 52, 44, 60, 29, 50, 64, 24], 'reason': 'High cost (19583.0) and located in sparse grid cells'}, {'region': [63, 25, 45, 58, 38, 65], 'reason': 'High cost (13509.0) with long edges to adjacent nodes'}, {'region': [7, 51, 30, 41, 8], 'reason': 'Contains common subpath (8,2,6) but high local cost (9386.0)'}], 'opportunity_regions': [{'region': [55, 61, 53, 62], 'reason': 'Frequent subpaths in population (frequency=0.3) and medium edge lengths'}, {'region': 'Top-left grid cell (14 nodes)', 'reason': 'High node density but currently underutilized in solutions'}, {'region': 'Bottom-right grid cell (14 nodes)', 'reason': 'High node density with directional consistency (sector 4 dominance)'}], 'evolution_phase': 'Early exploration', 'evolution_direction': ['Prioritize exploitation of high-density opportunity regions through 2-opt local search', 'Use edge recombination on common subpaths (55,61,53) and (61,53,62)', 'Apply guided mutation to break difficult regions (e.g. swap nodes between sparse cells)', 'Maintain diversity through restart mechanism if convergence stagnates']}
2025-06-26 10:52:58,875 - __main__ - INFO - 开始策略分配阶段
2025-06-26 10:52:58,875 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 10:52:58,876 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0
  • best_cost: 9972.0
  • mean_cost: 78388.9
- Search-space: ruggedness Moderate to high (wide range of edge lengths from 10.0 to 3032.0, with significant standard deviation), modality Potentially multimodal (presence of common subpaths suggests multiple local optima), deceptiveness Moderate (long_edge_ratio of 0.249 indicates some deceptive long edges that may mislead search)
- Evolution phase: Early exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): {'region': [28, 52, , {'region': [63, 25, ...
- Opportunity regions (# 3): {'region': [55, 61, , {'region': 'Top-left...
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 10:52:58,876 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 10:52:58,877 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:53:08,117 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 10:53:08,117 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 10:53:08,117 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 10:53:08,122 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 10:53:08,122 - __main__ - INFO - 策略分配阶段完成
2025-06-26 10:53:08,122 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 10:53:08,123 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 10:53:08,123 - __main__ - INFO - 开始进化阶段
2025-06-26 10:53:08,123 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 10:53:08,123 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:53:08,124 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 10014.0 (rank 20.0%)
- Population diversity: 0.7538461538461538
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [55,, {'region': 'Top...
- Difficult regions to avoid (sample): {'region': [28,, {'region': [63,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:53:08,124 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:53:08,125 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:53:32,308 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 55, 12, 34, 7, 45, 23, 56, 8, 39, 14, 60, 3, 47, 19, 62, 5, 51, 16, 64, 9, 42, 21, 58, 2, 49, 17, 65, 10, 43, 24, 59, 4, 48, 20, 61, 6, 52, 15, 63, 11, 44, 22, 57, 1, 50, 18, 66, 13, 41, 25, 54, 26, 37, 27, 36, 28, 35, 29, 38, 30, 40, 31, 33, 32, 46, 53],
  "targeted_regions": "Focus on regions 55, 12, 34, 7, 45, 23, 56, 8, 39, 14, 60, 3, 47, 19, 62, 5, 51, 16, 64, 9, 42, 21, 58, 2, 49, 17, 65, 10, 43, 24, 59, 4, 48, 20, 61, 6, 52, 15, 63, 11, 44, 22, 57, 1, 50, 18, 66, 13, 41, 25, 54, 26, 37, 27, 36, 28, 35, 29, 38, 30, 40, 31, 33, 32, 46, 53",
  "strategy_comment": "Balanced exploration of opportunity regions while avoiding difficult areas"
}
```
2025-06-26 10:53:32,309 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:53:32,309 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106181.0, 路径: [0, 55, 12, 34, 7, 45, 23, 56, 8, 39, 14, 60, 3, 47, 19, 62, 5, 51, 16, 64, 9, 42, 21, 58, 2, 49, 17, 65, 10, 43, 24, 59, 4, 48, 20, 61, 6, 52, 15, 63, 11, 44, 22, 57, 1, 50, 18, 36, 13, 41, 25, 54, 26, 37, 27, 28, 35, 29, 38, 30, 40, 31, 33, 32, 46, 53]
2025-06-26 10:53:32,310 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 55, 12, 34, 7, 45, 23, 56, 8, 39, 14, 60, 3, 47, 19, 62, 5, 51, 16, 64, 9, 42, 21, 58, 2, 49, 17, 65, 10, 43, 24, 59, 4, 48, 20, 61, 6, 52, 15, 63, 11, 44, 22, 57, 1, 50, 18, 36, 13, 41, 25, 54, 26, 37, 27, 28, 35, 29, 38, 30, 40, 31, 33, 32, 46, 53], 'cur_cost': 106181.0}
2025-06-26 10:53:32,311 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 10:53:32,311 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:53:32,311 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9972.0 (rank 0.0%)
- Population diversity: 0.8215384615384616
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [55,, {'region': 'Top...
- Difficult regions to avoid (sample): {'region': [28,, {'region': [63,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:53:32,311 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:53:32,311 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:53:47,216 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 55, 12, 34, 7, 19, 41, 23, 5, 47, 29, 10, 52, 14, 36, 8, 50, 22, 4, 46, 28, 1, 53, 15, 37, 9, 51, 21, 3, 45, 27, 0, 54, 16, 38, 11, 49, 25, 6, 48, 30, 13, 35, 17, 39, 2, 44, 26, 59, 20, 42, 24, 57, 18, 40, 33, 56, 31, 58, 32, 60, 43, 61, 62, 63, 64, 65],
  "targeted_regions": "Top opportunity regions and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, avoid difficult nodes, maintain diversity"
}
```
2025-06-26 10:53:47,216 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:53:47,216 - ExplorationExpert - INFO - 探索路径生成完成，成本: 122458.0, 路径: [0, 55, 12, 34, 7, 19, 41, 23, 5, 47, 29, 10, 52, 14, 36, 8, 50, 22, 4, 46, 28, 1, 53, 15, 37, 9, 51, 21, 3, 45, 27, 54, 16, 38, 11, 49, 25, 6, 48, 30, 13, 35, 17, 39, 2, 44, 26, 59, 20, 42, 24, 57, 18, 40, 33, 56, 31, 58, 32, 60, 43, 61, 62, 63, 64, 65]
2025-06-26 10:53:47,216 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 55, 12, 34, 7, 19, 41, 23, 5, 47, 29, 10, 52, 14, 36, 8, 50, 22, 4, 46, 28, 1, 53, 15, 37, 9, 51, 21, 3, 45, 27, 54, 16, 38, 11, 49, 25, 6, 48, 30, 13, 35, 17, 39, 2, 44, 26, 59, 20, 42, 24, 57, 18, 40, 33, 56, 31, 58, 32, 60, 43, 61, 62, 63, 64, 65], 'cur_cost': 122458.0}
2025-06-26 10:53:47,216 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 10:53:47,216 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:53:47,221 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9984.0 (rank 0.0%)
- Population diversity: 0.8661538461538462
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [55,, {'region': 'Top...
- Difficult regions to avoid (sample): {'region': [28,, {'region': [63,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:53:47,221 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:53:47,221 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:54:00,887 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28],
  "targeted_regions": "Top opportunity region 55 and adjacent sparse cells",
  "strategy_comment": "Prioritize region 55, skip difficult nodes 28 & 63"
}
```
2025-06-26 10:54:00,888 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:54:00,888 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17289.0, 路径: [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28]
2025-06-26 10:54:00,888 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28], 'cur_cost': 17289.0}
2025-06-26 10:54:00,890 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 10:54:00,890 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:54:00,890 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101947.0 (rank 30.0%)
- Population diversity: 0.8646153846153846
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [55,, {'region': 'Top...
- Difficult regions to avoid (sample): {'region': [28,, {'region': [63,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:54:00,891 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:54:00,891 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:54:15,508 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 47, 21, 43, 11, 33, 1, 25, 57, 13, 35, 59, 8, 30, 52, 16, 38, 4, 46, 22, 44, 10, 32, 54, 18, 40, 6, 48, 24, 56, 14, 36, 58, 2, 26, 50, 20, 42, 62, 28, 60, 64, 29, 61, 63, 65, 51, 66],
  "targeted_regions": "Top opportunity regions and adjacent sparse cells",
  "strategy_comment": "Prioritize opportunity regions, skip difficult nodes, maintain diversity"
}
```
2025-06-26 10:54:15,513 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:54:15,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 128360.0, 路径: [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 47, 21, 43, 11, 33, 1, 25, 57, 13, 35, 59, 8, 30, 52, 16, 38, 4, 46, 22, 44, 10, 32, 54, 18, 40, 6, 48, 24, 56, 14, 36, 58, 2, 26, 50, 20, 42, 62, 28, 60, 64, 29, 61, 63, 65, 51]
2025-06-26 10:54:15,514 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 47, 21, 43, 11, 33, 1, 25, 57, 13, 35, 59, 8, 30, 52, 16, 38, 4, 46, 22, 44, 10, 32, 54, 18, 40, 6, 48, 24, 56, 14, 36, 58, 2, 26, 50, 20, 42, 62, 28, 60, 64, 29, 61, 63, 65, 51], 'cur_cost': 128360.0}
2025-06-26 10:54:15,514 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 10:54:15,515 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:54:15,515 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 112770.0 (rank 60.0%)
- Population diversity: 0.8476923076923077
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [55,, {'region': 'Top...
- Difficult regions to avoid (sample): {'region': [28,, {'region': [63,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:54:15,516 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:54:15,516 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:54:30,321 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 29, 51, 13, 35, 1, 24, 46, 8, 30, 52, 14, 36, 2, 25, 47, 10, 32, 54, 16, 38, 4, 28, 50, 22, 44, 6, 20, 42, 26, 48, 18, 40, 11, 33, 21, 43, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56],
  "targeted_regions": "55, sparse adjacent cells",
  "strategy_comment": "Prioritize opportunity regions, avoid high-cost clusters, balance exploration"
}
```
2025-06-26 10:54:30,321 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:54:30,321 - ExplorationExpert - INFO - 探索路径生成完成，成本: 106851.0, 路径: [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 29, 51, 13, 35, 1, 24, 46, 8, 30, 52, 14, 36, 2, 25, 47, 10, 32, 54, 16, 38, 4, 28, 50, 22, 44, 6, 20, 42, 26, 48, 18, 40, 11, 33, 21, 43, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56]
2025-06-26 10:54:30,321 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 29, 51, 13, 35, 1, 24, 46, 8, 30, 52, 14, 36, 2, 25, 47, 10, 32, 54, 16, 38, 4, 28, 50, 22, 44, 6, 20, 42, 26, 48, 18, 40, 11, 33, 21, 43, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56], 'cur_cost': 106851.0}
2025-06-26 10:54:30,326 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 10:54:30,326 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:54:30,327 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 107406.0 (rank 50.0%)
- Population diversity: 0.8107692307692308
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [55,, {'region': 'Top...
- Difficult regions to avoid (sample): {'region': [28,, {'region': [63,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:54:30,327 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:54:30,327 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:54:45,261 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28],
  "targeted_regions": "Prioritized region 55, avoided 28 and 63",
  "strategy_comment": "Focus on opportunity regions, skip difficult nodes"
}
```
2025-06-26 10:54:45,261 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:54:45,261 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17289.0, 路径: [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28]
2025-06-26 10:54:45,261 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28], 'cur_cost': 17289.0}
2025-06-26 10:54:45,261 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 10:54:45,261 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:54:45,261 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101104.0 (rank 30.0%)
- Population diversity: 0.7292307692307692
- Evolution phase: Early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): {'region': [55,, {'region': 'Top...
- Difficult regions to avoid (sample): {'region': [28,, {'region': [63,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:54:45,261 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:54:45,261 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:54:59,657 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 55, 12, 34, 7, 19, 41, 23, 45, 5, 27, 49, 10, 32, 54, 16, 38, 60, 2, 24, 46, 8, 30, 52, 14, 36, 58, 20, 42, 64, 6, 28, 50, 11, 33, 55, 17, 39, 61, 3, 25, 47, 9, 31, 53, 15, 37, 59, 21, 43, 65, 1, 23, 45, 7, 29, 51, 13, 35, 57, 18, 40, 62, 4, 26, 48],
  "targeted_regions": "55, adjacent low-density cells",
  "strategy_comment": "Prioritize opportunity region 55 and nearby sparse cells"
}
```
2025-06-26 10:54:59,659 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:54:59,660 - ExplorationExpert - INFO - 探索路径生成完成，成本: 130381.0, 路径: [0, 55, 12, 34, 7, 19, 41, 23, 45, 5, 27, 49, 10, 32, 54, 16, 38, 60, 2, 24, 46, 8, 30, 52, 14, 36, 58, 20, 42, 64, 6, 28, 50, 11, 33, 17, 39, 61, 3, 25, 47, 9, 31, 53, 15, 37, 59, 21, 43, 65, 1, 29, 51, 13, 35, 57, 18, 40, 62, 4, 26, 48, 63, 44, 56, 22]
2025-06-26 10:54:59,660 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 5, 27, 49, 10, 32, 54, 16, 38, 60, 2, 24, 46, 8, 30, 52, 14, 36, 58, 20, 42, 64, 6, 28, 50, 11, 33, 17, 39, 61, 3, 25, 47, 9, 31, 53, 15, 37, 59, 21, 43, 65, 1, 29, 51, 13, 35, 57, 18, 40, 62, 4, 26, 48, 63, 44, 56, 22], 'cur_cost': 130381.0}
2025-06-26 10:54:59,660 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 10:54:59,660 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 10:54:59,662 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 10:54:59,664 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 100115.0
2025-06-26 10:55:02,547 - ExploitationExpert - INFO - res_population_num: 1
2025-06-26 10:55:02,547 - ExploitationExpert - INFO - res_population_costs: [109070]
2025-06-26 10:55:02,547 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64)]
2025-06-26 10:55:02,547 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 10:55:02,547 - ExploitationExpert - INFO - populations: [{'tour': [0, 55, 12, 34, 7, 45, 23, 56, 8, 39, 14, 60, 3, 47, 19, 62, 5, 51, 16, 64, 9, 42, 21, 58, 2, 49, 17, 65, 10, 43, 24, 59, 4, 48, 20, 61, 6, 52, 15, 63, 11, 44, 22, 57, 1, 50, 18, 36, 13, 41, 25, 54, 26, 37, 27, 28, 35, 29, 38, 30, 40, 31, 33, 32, 46, 53], 'cur_cost': 106181.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 5, 47, 29, 10, 52, 14, 36, 8, 50, 22, 4, 46, 28, 1, 53, 15, 37, 9, 51, 21, 3, 45, 27, 54, 16, 38, 11, 49, 25, 6, 48, 30, 13, 35, 17, 39, 2, 44, 26, 59, 20, 42, 24, 57, 18, 40, 33, 56, 31, 58, 32, 60, 43, 61, 62, 63, 64, 65], 'cur_cost': 122458.0}, {'tour': [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28], 'cur_cost': 17289.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 47, 21, 43, 11, 33, 1, 25, 57, 13, 35, 59, 8, 30, 52, 16, 38, 4, 46, 22, 44, 10, 32, 54, 18, 40, 6, 48, 24, 56, 14, 36, 58, 2, 26, 50, 20, 42, 62, 28, 60, 64, 29, 61, 63, 65, 51], 'cur_cost': 128360.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 29, 51, 13, 35, 1, 24, 46, 8, 30, 52, 14, 36, 2, 25, 47, 10, 32, 54, 16, 38, 4, 28, 50, 22, 44, 6, 20, 42, 26, 48, 18, 40, 11, 33, 21, 43, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56], 'cur_cost': 106851.0}, {'tour': [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28], 'cur_cost': 17289.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 5, 27, 49, 10, 32, 54, 16, 38, 60, 2, 24, 46, 8, 30, 52, 14, 36, 58, 20, 42, 64, 6, 28, 50, 11, 33, 17, 39, 61, 3, 25, 47, 9, 31, 53, 15, 37, 59, 21, 43, 65, 1, 29, 51, 13, 35, 57, 18, 40, 62, 4, 26, 48, 63, 44, 56, 22], 'cur_cost': 130381.0}, {'tour': array([ 0, 48, 11, 55, 25, 52, 62, 22, 58,  4, 37, 45, 49, 33, 19, 13,  7,
       53, 15, 18, 43,  5, 16, 63, 34, 40, 23, 47, 14, 39, 51, 32,  8, 64,
        1, 10, 29, 57, 56, 59,  3, 21,  6, 36, 42, 20, 50, 27, 26, 60, 65,
       12, 61, 24, 46, 38, 35, 44, 31, 17,  9, 54, 30, 28, 41,  2]), 'cur_cost': 100115.0}, {'tour': [21, 13, 20, 44, 56, 42, 39, 61, 9, 60, 0, 53, 27, 15, 18, 36, 23, 24, 10, 31, 17, 5, 50, 43, 16, 11, 54, 38, 35, 22, 65, 48, 49, 29, 6, 59, 58, 63, 57, 26, 14, 19, 33, 55, 34, 46, 45, 52, 3, 7, 51, 30, 41, 8, 12, 64, 40, 37, 28, 25, 2, 62, 4, 32, 47, 1], 'cur_cost': 97836.0}, {'tour': [9, 43, 5, 21, 17, 18, 63, 25, 45, 58, 38, 65, 8, 54, 62, 6, 29, 19, 3, 35, 64, 55, 24, 48, 16, 47, 60, 30, 22, 56, 53, 41, 7, 15, 61, 14, 37, 52, 11, 2, 27, 10, 4, 42, 31, 40, 28, 46, 34, 23, 33, 57, 39, 51, 0, 50, 59, 20, 13, 49, 44, 32, 36, 1, 26, 12], 'cur_cost': 111999.0}]
2025-06-26 10:55:02,549 - ExploitationExpert - INFO - 局部搜索耗时: 2.89秒
2025-06-26 10:55:02,549 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-26 10:55:02,549 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 10:55:02,550 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 10:55:02,550 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 10:55:02,550 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 10:55:02,550 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 105672.0
2025-06-26 10:55:03,565 - ExploitationExpert - INFO - res_population_num: 2
2025-06-26 10:55:03,565 - ExploitationExpert - INFO - res_population_costs: [109070, 9572]
2025-06-26 10:55:03,565 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-06-26 10:55:03,565 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 10:55:03,567 - ExploitationExpert - INFO - populations: [{'tour': [0, 55, 12, 34, 7, 45, 23, 56, 8, 39, 14, 60, 3, 47, 19, 62, 5, 51, 16, 64, 9, 42, 21, 58, 2, 49, 17, 65, 10, 43, 24, 59, 4, 48, 20, 61, 6, 52, 15, 63, 11, 44, 22, 57, 1, 50, 18, 36, 13, 41, 25, 54, 26, 37, 27, 28, 35, 29, 38, 30, 40, 31, 33, 32, 46, 53], 'cur_cost': 106181.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 5, 47, 29, 10, 52, 14, 36, 8, 50, 22, 4, 46, 28, 1, 53, 15, 37, 9, 51, 21, 3, 45, 27, 54, 16, 38, 11, 49, 25, 6, 48, 30, 13, 35, 17, 39, 2, 44, 26, 59, 20, 42, 24, 57, 18, 40, 33, 56, 31, 58, 32, 60, 43, 61, 62, 63, 64, 65], 'cur_cost': 122458.0}, {'tour': [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28], 'cur_cost': 17289.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 47, 21, 43, 11, 33, 1, 25, 57, 13, 35, 59, 8, 30, 52, 16, 38, 4, 46, 22, 44, 10, 32, 54, 18, 40, 6, 48, 24, 56, 14, 36, 58, 2, 26, 50, 20, 42, 62, 28, 60, 64, 29, 61, 63, 65, 51], 'cur_cost': 128360.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 29, 51, 13, 35, 1, 24, 46, 8, 30, 52, 14, 36, 2, 25, 47, 10, 32, 54, 16, 38, 4, 28, 50, 22, 44, 6, 20, 42, 26, 48, 18, 40, 11, 33, 21, 43, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56], 'cur_cost': 106851.0}, {'tour': [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28], 'cur_cost': 17289.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 5, 27, 49, 10, 32, 54, 16, 38, 60, 2, 24, 46, 8, 30, 52, 14, 36, 58, 20, 42, 64, 6, 28, 50, 11, 33, 17, 39, 61, 3, 25, 47, 9, 31, 53, 15, 37, 59, 21, 43, 65, 1, 29, 51, 13, 35, 57, 18, 40, 62, 4, 26, 48, 63, 44, 56, 22], 'cur_cost': 130381.0}, {'tour': array([ 0, 48, 11, 55, 25, 52, 62, 22, 58,  4, 37, 45, 49, 33, 19, 13,  7,
       53, 15, 18, 43,  5, 16, 63, 34, 40, 23, 47, 14, 39, 51, 32,  8, 64,
        1, 10, 29, 57, 56, 59,  3, 21,  6, 36, 42, 20, 50, 27, 26, 60, 65,
       12, 61, 24, 46, 38, 35, 44, 31, 17,  9, 54, 30, 28, 41,  2]), 'cur_cost': 100115.0}, {'tour': array([ 2, 30, 39, 64, 40, 55, 51, 60, 23, 38, 62, 37,  7, 29, 42,  0, 50,
       44, 49, 14, 41, 34, 24, 45, 52,  1, 63, 18, 57, 48, 11,  3, 65,  4,
       19,  5,  9, 16, 56, 10, 61, 25, 33, 31, 28, 32, 43, 36, 13,  6, 15,
       47, 17, 26, 21, 27,  8, 58, 12, 53, 46, 22, 59, 54, 35, 20]), 'cur_cost': 105672.0}, {'tour': [9, 43, 5, 21, 17, 18, 63, 25, 45, 58, 38, 65, 8, 54, 62, 6, 29, 19, 3, 35, 64, 55, 24, 48, 16, 47, 60, 30, 22, 56, 53, 41, 7, 15, 61, 14, 37, 52, 11, 2, 27, 10, 4, 42, 31, 40, 28, 46, 34, 23, 33, 57, 39, 51, 0, 50, 59, 20, 13, 49, 44, 32, 36, 1, 26, 12], 'cur_cost': 111999.0}]
2025-06-26 10:55:03,568 - ExploitationExpert - INFO - 局部搜索耗时: 1.02秒
2025-06-26 10:55:03,568 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-26 10:55:03,568 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 10:55:03,569 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 10:55:03,569 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 10:55:03,569 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 10:55:03,569 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110994.0
2025-06-26 10:55:04,072 - ExploitationExpert - INFO - res_population_num: 8
2025-06-26 10:55:04,072 - ExploitationExpert - INFO - res_population_costs: [109070, 9572, 9552, 9533, 9527, 9526, 9522, 9521]
2025-06-26 10:55:04,072 - ExploitationExpert - INFO - res_populations: [array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-26 10:55:04,075 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 10:55:04,075 - ExploitationExpert - INFO - populations: [{'tour': [0, 55, 12, 34, 7, 45, 23, 56, 8, 39, 14, 60, 3, 47, 19, 62, 5, 51, 16, 64, 9, 42, 21, 58, 2, 49, 17, 65, 10, 43, 24, 59, 4, 48, 20, 61, 6, 52, 15, 63, 11, 44, 22, 57, 1, 50, 18, 36, 13, 41, 25, 54, 26, 37, 27, 28, 35, 29, 38, 30, 40, 31, 33, 32, 46, 53], 'cur_cost': 106181.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 5, 47, 29, 10, 52, 14, 36, 8, 50, 22, 4, 46, 28, 1, 53, 15, 37, 9, 51, 21, 3, 45, 27, 54, 16, 38, 11, 49, 25, 6, 48, 30, 13, 35, 17, 39, 2, 44, 26, 59, 20, 42, 24, 57, 18, 40, 33, 56, 31, 58, 32, 60, 43, 61, 62, 63, 64, 65], 'cur_cost': 122458.0}, {'tour': [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28], 'cur_cost': 17289.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 47, 21, 43, 11, 33, 1, 25, 57, 13, 35, 59, 8, 30, 52, 16, 38, 4, 46, 22, 44, 10, 32, 54, 18, 40, 6, 48, 24, 56, 14, 36, 58, 2, 26, 50, 20, 42, 62, 28, 60, 64, 29, 61, 63, 65, 51], 'cur_cost': 128360.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 29, 51, 13, 35, 1, 24, 46, 8, 30, 52, 14, 36, 2, 25, 47, 10, 32, 54, 16, 38, 4, 28, 50, 22, 44, 6, 20, 42, 26, 48, 18, 40, 11, 33, 21, 43, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56], 'cur_cost': 106851.0}, {'tour': [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28], 'cur_cost': 17289.0}, {'tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 5, 27, 49, 10, 32, 54, 16, 38, 60, 2, 24, 46, 8, 30, 52, 14, 36, 58, 20, 42, 64, 6, 28, 50, 11, 33, 17, 39, 61, 3, 25, 47, 9, 31, 53, 15, 37, 59, 21, 43, 65, 1, 29, 51, 13, 35, 57, 18, 40, 62, 4, 26, 48, 63, 44, 56, 22], 'cur_cost': 130381.0}, {'tour': array([ 0, 48, 11, 55, 25, 52, 62, 22, 58,  4, 37, 45, 49, 33, 19, 13,  7,
       53, 15, 18, 43,  5, 16, 63, 34, 40, 23, 47, 14, 39, 51, 32,  8, 64,
        1, 10, 29, 57, 56, 59,  3, 21,  6, 36, 42, 20, 50, 27, 26, 60, 65,
       12, 61, 24, 46, 38, 35, 44, 31, 17,  9, 54, 30, 28, 41,  2]), 'cur_cost': 100115.0}, {'tour': array([ 2, 30, 39, 64, 40, 55, 51, 60, 23, 38, 62, 37,  7, 29, 42,  0, 50,
       44, 49, 14, 41, 34, 24, 45, 52,  1, 63, 18, 57, 48, 11,  3, 65,  4,
       19,  5,  9, 16, 56, 10, 61, 25, 33, 31, 28, 32, 43, 36, 13,  6, 15,
       47, 17, 26, 21, 27,  8, 58, 12, 53, 46, 22, 59, 54, 35, 20]), 'cur_cost': 105672.0}, {'tour': array([17, 33, 54, 32, 42,  5, 51, 14, 11, 23,  2, 55, 56, 16,  7, 47, 13,
       36, 40, 18, 60, 53, 62, 48, 50, 65, 61, 45, 30, 12,  1, 59, 29, 52,
       27,  6, 63, 38,  4,  0, 49, 15, 19, 34, 43,  8, 26, 44, 25, 64, 57,
       10, 28,  3, 46,  9, 37, 22, 58, 31, 35, 41, 20, 24, 39, 21]), 'cur_cost': 110994.0}]
2025-06-26 10:55:04,077 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 10:55:04,077 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-26 10:55:04,077 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 10:55:04,077 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 55, 12, 34, 7, 45, 23, 56, 8, 39, 14, 60, 3, 47, 19, 62, 5, 51, 16, 64, 9, 42, 21, 58, 2, 49, 17, 65, 10, 43, 24, 59, 4, 48, 20, 61, 6, 52, 15, 63, 11, 44, 22, 57, 1, 50, 18, 36, 13, 41, 25, 54, 26, 37, 27, 28, 35, 29, 38, 30, 40, 31, 33, 32, 46, 53], 'cur_cost': 106181.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 55, 12, 34, 7, 19, 41, 23, 5, 47, 29, 10, 52, 14, 36, 8, 50, 22, 4, 46, 28, 1, 53, 15, 37, 9, 51, 21, 3, 45, 27, 54, 16, 38, 11, 49, 25, 6, 48, 30, 13, 35, 17, 39, 2, 44, 26, 59, 20, 42, 24, 57, 18, 40, 33, 56, 31, 58, 32, 60, 43, 61, 62, 63, 64, 65], 'cur_cost': 122458.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28], 'cur_cost': 17289.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 47, 21, 43, 11, 33, 1, 25, 57, 13, 35, 59, 8, 30, 52, 16, 38, 4, 46, 22, 44, 10, 32, 54, 18, 40, 6, 48, 24, 56, 14, 36, 58, 2, 26, 50, 20, 42, 62, 28, 60, 64, 29, 61, 63, 65, 51], 'cur_cost': 128360.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 3, 27, 49, 15, 37, 9, 31, 53, 17, 39, 5, 29, 51, 13, 35, 1, 24, 46, 8, 30, 52, 14, 36, 2, 25, 47, 10, 32, 54, 16, 38, 4, 28, 50, 22, 44, 6, 20, 42, 26, 48, 18, 40, 11, 33, 21, 43, 57, 59, 61, 63, 65, 64, 62, 60, 58, 56], 'cur_cost': 106851.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 55, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 64, 65, 63, 28], 'cur_cost': 17289.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 55, 12, 34, 7, 19, 41, 23, 45, 5, 27, 49, 10, 32, 54, 16, 38, 60, 2, 24, 46, 8, 30, 52, 14, 36, 58, 20, 42, 64, 6, 28, 50, 11, 33, 17, 39, 61, 3, 25, 47, 9, 31, 53, 15, 37, 59, 21, 43, 65, 1, 29, 51, 13, 35, 57, 18, 40, 62, 4, 26, 48, 63, 44, 56, 22], 'cur_cost': 130381.0}}]
2025-06-26 10:55:04,078 - __main__ - INFO - 进化阶段完成
2025-06-26 10:55:04,078 - __main__ - INFO - 开始评估阶段
2025-06-26 10:55:04,078 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 10:55:04,079 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 10:55:04,079 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9972.0 → 17289.0 (Δ -7317.0)
- Mean cost: 78388.9 → 94559.0
- Diversity: 0.754 → 0.695 (Δ -0.0585)

#### Elite metrics
- Elite count: 0 → 8
- Best elite cost: 9972.0 → 9521 (Δ 451.0)

#### Strategy performance
- Explore: 7 inds, success 28.6% , avg Δ -25087.43
- Exploit: 3 inds, success 66.7% , avg Δ 4637.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 10:55:04,081 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:55:14,904 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "avg_delta": -25087.43,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 66.7,
      "avg_delta": 4637.0,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "elite_preservation": "increase"
  }
}
```
2025-06-26 10:55:14,935 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 10:55:14,935 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "avg_delta": -25087.43,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 66.7,
      "avg_delta": 4637.0,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "elite_preservation": "increase"
  }
}
```
2025-06-26 10:55:14,935 - __main__ - INFO - 评估阶段完成
2025-06-26 10:55:14,936 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
      "avg_delta": -25087.43,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 66.7,
      "avg_delta": 4637.0,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": "exploit-heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "elite_preservation": "increase"
  }
}
```
2025-06-26 10:55:14,936 - __main__ - INFO - 当前最佳适应度: 17289.0
2025-06-26 10:55:14,946 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-26 10:55:14,946 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-26 10:55:14,946 - __main__ - INFO - 开始分析阶段
2025-06-26 10:55:14,948 - StatsExpert - INFO - 开始统计分析
2025-06-26 10:55:14,964 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 17289.0, 'max': 130381.0, 'mean': 94559.0, 'std': 39819.62401128368}, 'diversity': 0.8969696969696969, 'clusters': {'clusters': 9, 'cluster_sizes': [1, 1, 2, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 10:55:14,965 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 17289.0, 'max': 130381.0, 'mean': 94559.0, 'std': 39819.62401128368}, 'diversity_level': 0.8969696969696969, 'convergence_level': 0.0, 'clustering_info': {'clusters': 9, 'cluster_sizes': [1, 1, 2, 1, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-26 10:55:14,966 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 10:55:14,966 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 10:55:14,966 - PathExpert - INFO - 开始路径结构分析
2025-06-26 10:55:14,968 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 10:55:14,969 - PathExpert - INFO - 路径结构分析完成
2025-06-26 10:55:14,969 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 10:55:14,969 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 10:55:14,969 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 10:55:14,969 - EliteExpert - INFO - 开始精英解分析
2025-06-26 10:55:14,971 - EliteExpert - INFO - 精英解分析完成
2025-06-26 10:55:14,972 - __main__ - INFO - 精英专家分析报告: {'elite_count': 8, 'elite_common_features': {'common_edges': {'(24, 31)': 0.75}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 7768.0, 'avg_gap': 72581.125}, 'structure_gap': {'unique_elite_edges': 211, 'unique_pop_edges': 449, 'common_edges': 37}}, 'elite_diversity': {'diversity_score': 0.4285714285714286}}
2025-06-26 10:55:14,972 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 10:55:14,972 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 10:55:14,974 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 10:55:14,974 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 17289.0, mean 94559.0, max 130381.0, std 39819.62401128368
- diversity: 0.8969696969696969
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [17289.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 10:55:14,975 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:55:34,800 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "moderate",
    "modality": "multimodal",
    "deceptiveness": "low",
    "edge_length_variability": "high",
    "long_edge_ratio": "moderate"
  },
  "population_state": {
    "coverage": "sparse",
    "convergence": "none",
    "diversity": "high",
    "spatial_distribution": "uneven",
    "potential_clusters": "none detected"
  },
  "difficult_regions": {
    "long_edge_corridors": "present",
    "low_density_areas": "present in grid cells (0,1), (0,2), (1,0), (2,1)",
    "identified_edges": []
  },
  "opportunity_regions": {
    "high_density_cells": "present in grid cells (0,0), (2,0), (2,2)",
    "potential_subpaths": [],
    "identified_nodes": []
  },
  "evolution_phase": "early exploration",
  "evolution_direction": {
    "suggested_operators": [
      "edge recombination to preserve high-quality segments",
      "directed local search in high-density regions",
      "long-edge reduction heuristics",
      "diversity maintenance in sparse regions",
      "spatial niching to cover unexplored areas"
    ],
    "focus_areas": [
      "exploit high-density clusters",
      "address long-edge connectivity",
      "improve coverage in sparse regions"
    ]
  }
}
```
2025-06-26 10:55:34,803 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 10:55:34,803 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high', 'long_edge_ratio': 'moderate'}, 'population_state': {'coverage': 'sparse', 'convergence': 'none', 'diversity': 'high', 'spatial_distribution': 'uneven', 'potential_clusters': 'none detected'}, 'difficult_regions': {'long_edge_corridors': 'present', 'low_density_areas': 'present in grid cells (0,1), (0,2), (1,0), (2,1)', 'identified_edges': []}, 'opportunity_regions': {'high_density_cells': 'present in grid cells (0,0), (2,0), (2,2)', 'potential_subpaths': [], 'identified_nodes': []}, 'evolution_phase': 'early exploration', 'evolution_direction': {'suggested_operators': ['edge recombination to preserve high-quality segments', 'directed local search in high-density regions', 'long-edge reduction heuristics', 'diversity maintenance in sparse regions', 'spatial niching to cover unexplored areas'], 'focus_areas': ['exploit high-density clusters', 'address long-edge connectivity', 'improve coverage in sparse regions']}}
2025-06-26 10:55:34,803 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 10:55:34,803 - __main__ - INFO - 分析阶段完成
2025-06-26 10:55:34,803 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'moderate', 'modality': 'multimodal', 'deceptiveness': 'low', 'edge_length_variability': 'high', 'long_edge_ratio': 'moderate'}, 'population_state': {'coverage': 'sparse', 'convergence': 'none', 'diversity': 'high', 'spatial_distribution': 'uneven', 'potential_clusters': 'none detected'}, 'difficult_regions': {'long_edge_corridors': 'present', 'low_density_areas': 'present in grid cells (0,1), (0,2), (1,0), (2,1)', 'identified_edges': []}, 'opportunity_regions': {'high_density_cells': 'present in grid cells (0,0), (2,0), (2,2)', 'potential_subpaths': [], 'identified_nodes': []}, 'evolution_phase': 'early exploration', 'evolution_direction': {'suggested_operators': ['edge recombination to preserve high-quality segments', 'directed local search in high-density regions', 'long-edge reduction heuristics', 'diversity maintenance in sparse regions', 'spatial niching to cover unexplored areas'], 'focus_areas': ['exploit high-density clusters', 'address long-edge connectivity', 'improve coverage in sparse regions']}}
2025-06-26 10:55:34,805 - __main__ - INFO - 开始策略分配阶段
2025-06-26 10:55:34,805 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 10:55:34,805 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0
  • best_cost: 17289.0
  • mean_cost: 94559.0
- Search-space: ruggedness moderate, modality multimodal, deceptiveness low
- Evolution phase: early exploration
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 3): long_edge_corridors, low_density_areas...
- Opportunity regions (# 3): high_density_cells, potential_subpaths...
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 28.6,
     ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 10:55:34,806 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 10:55:34,806 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:55:43,994 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 10:55:43,995 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 10:55:43,995 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 10:55:43,995 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 10:55:43,996 - __main__ - INFO - 策略分配阶段完成
2025-06-26 10:55:43,996 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-06-26 10:55:43,996 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and early phase favor exploration. Top 30% individuals exploit to refine promising solutions."
}
```
2025-06-26 10:55:43,996 - __main__ - INFO - 开始进化阶段
2025-06-26 10:55:43,997 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 10:55:43,997 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:55:43,997 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106181.0 (rank 40.0%)
- Population diversity: 0.6953846153846154
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:55:43,997 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:55:43,997 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:55:58,436 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Balanced exploration with modular jumps"
}
```
2025-06-26 10:55:58,436 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:55:58,436 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 10:55:58,436 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 10:55:58,436 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 10:55:58,436 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:55:58,436 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 122458.0 (rank 70.0%)
- Population diversity: 0.7061538461538461
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:55:58,436 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:55:58,436 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:56:12,514 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Leverage modular jumps to explore sparse cells"
}
```
2025-06-26 10:56:12,514 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:56:12,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 10:56:12,514 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 10:56:12,514 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 10:56:12,514 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:56:12,514 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17289.0 (rank 0.0%)
- Population diversity: 0.6723076923076923
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:56:12,519 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:56:12,519 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:56:27,218 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_ce, adjacent low-density cells",
  "strategy_comment": "Layered spiral targeting opportunity regions"
}
```
2025-06-26 10:56:27,219 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:56:27,219 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 10:56:27,220 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 10:56:27,220 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 10:56:27,220 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:56:27,220 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 128360.0 (rank 80.0%)
- Population diversity: 0.6723076923076923
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:56:27,220 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:56:27,220 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:56:40,706 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 66, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Leverage diagonal traversal for diversity"
}
```
2025-06-26 10:56:40,706 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:56:40,706 - ExplorationExpert - INFO - 探索路径生成完成，成本: 95242.0, 路径: [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 29, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1]
2025-06-26 10:56:40,706 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 29, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 95242.0}
2025-06-26 10:56:40,706 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 10:56:40,706 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:56:40,706 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 106851.0 (rank 70.0%)
- Population diversity: 0.7184615384615385
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:56:40,706 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:56:40,706 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:56:55,269 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Structured leapfrog pattern to explore sparse cells"
}
```
2025-06-26 10:56:55,275 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:56:55,275 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 10:56:55,275 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 10:56:55,276 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 10:56:55,276 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:56:55,276 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 17289.0 (rank 0.0%)
- Population diversity: 0.68
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:56:55,277 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:56:55,277 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:57:09,134 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 66, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Leverage opportunity regions with spiral pattern"
}
```
2025-06-26 10:57:09,136 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:57:09,136 - ExplorationExpert - INFO - 探索路径生成完成，成本: 91924.0, 路径: [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 60, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1]
2025-06-26 10:57:09,137 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 60, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 91924.0}
2025-06-26 10:57:09,137 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-26 10:57:09,137 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:57:09,138 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 130381.0 (rank 90.0%)
- Population diversity: 0.5953846153846154
- Evolution phase: early exploration
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_ce, potential_subpa...
- Difficult regions to avoid (sample): long_edge_corri, low_density_are...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:57:09,138 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:57:09,138 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:57:23,703 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "high_density_ce, potential_subpa",
  "strategy_comment": "Structured leapfrog to explore sparse cells"
}
```
2025-06-26 10:57:23,703 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:57:23,703 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 10:57:23,703 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 10:57:23,703 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 10:57:23,703 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 10:57:23,703 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 10:57:23,703 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 104328.0
2025-06-26 10:57:24,205 - ExploitationExpert - INFO - res_population_num: 12
2025-06-26 10:57:24,205 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521, 9521, 9521, 9521]
2025-06-26 10:57:24,205 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64)]
2025-06-26 10:57:24,209 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 10:57:24,210 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 29, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 95242.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 60, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 91924.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': array([33,  9,  8, 62, 39,  1, 11,  3, 30,  7, 32, 57, 19, 43, 47, 13, 50,
       49, 48, 36, 22,  4, 35, 16, 55, 51, 31, 41, 21, 25, 27, 38, 54,  5,
       12, 28, 42, 46, 40, 34, 17, 20,  6, 60,  2, 53,  0, 63, 26, 65, 24,
       64, 14, 10, 37, 15, 58, 18, 29, 52, 44, 59, 45, 23, 61, 56]), 'cur_cost': 104328.0}, {'tour': array([ 2, 30, 39, 64, 40, 55, 51, 60, 23, 38, 62, 37,  7, 29, 42,  0, 50,
       44, 49, 14, 41, 34, 24, 45, 52,  1, 63, 18, 57, 48, 11,  3, 65,  4,
       19,  5,  9, 16, 56, 10, 61, 25, 33, 31, 28, 32, 43, 36, 13,  6, 15,
       47, 17, 26, 21, 27,  8, 58, 12, 53, 46, 22, 59, 54, 35, 20]), 'cur_cost': 105672.0}, {'tour': array([17, 33, 54, 32, 42,  5, 51, 14, 11, 23,  2, 55, 56, 16,  7, 47, 13,
       36, 40, 18, 60, 53, 62, 48, 50, 65, 61, 45, 30, 12,  1, 59, 29, 52,
       27,  6, 63, 38,  4,  0, 49, 15, 19, 34, 43,  8, 26, 44, 25, 64, 57,
       10, 28,  3, 46,  9, 37, 22, 58, 31, 35, 41, 20, 24, 39, 21]), 'cur_cost': 110994.0}]
2025-06-26 10:57:24,211 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 10:57:24,212 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-26 10:57:24,212 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 10:57:24,212 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 10:57:24,212 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 10:57:24,212 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 10:57:24,213 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 104638.0
2025-06-26 10:57:24,715 - ExploitationExpert - INFO - res_population_num: 15
2025-06-26 10:57:24,715 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-26 10:57:24,715 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-26 10:57:24,720 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 10:57:24,720 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 29, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 95242.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 60, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 91924.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': array([33,  9,  8, 62, 39,  1, 11,  3, 30,  7, 32, 57, 19, 43, 47, 13, 50,
       49, 48, 36, 22,  4, 35, 16, 55, 51, 31, 41, 21, 25, 27, 38, 54,  5,
       12, 28, 42, 46, 40, 34, 17, 20,  6, 60,  2, 53,  0, 63, 26, 65, 24,
       64, 14, 10, 37, 15, 58, 18, 29, 52, 44, 59, 45, 23, 61, 56]), 'cur_cost': 104328.0}, {'tour': array([42, 45, 50, 16, 38, 34, 23, 27, 60, 58, 19, 55,  6, 15, 61,  7,  2,
       22, 53, 30, 63, 52,  8, 46, 43, 65, 39, 41, 49,  3, 37,  1, 21, 28,
       20, 10, 33, 14, 24, 18, 26, 44, 54,  0, 32, 35, 64, 51, 29,  9, 57,
       47, 13, 12, 36, 59,  4, 48,  5, 40, 62, 17, 25, 31, 56, 11]), 'cur_cost': 104638.0}, {'tour': array([17, 33, 54, 32, 42,  5, 51, 14, 11, 23,  2, 55, 56, 16,  7, 47, 13,
       36, 40, 18, 60, 53, 62, 48, 50, 65, 61, 45, 30, 12,  1, 59, 29, 52,
       27,  6, 63, 38,  4,  0, 49, 15, 19, 34, 43,  8, 26, 44, 25, 64, 57,
       10, 28,  3, 46,  9, 37, 22, 58, 31, 35, 41, 20, 24, 39, 21]), 'cur_cost': 110994.0}]
2025-06-26 10:57:24,721 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 10:57:24,722 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-26 10:57:24,722 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 10:57:24,722 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 10:57:24,722 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 10:57:24,722 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 10:57:24,722 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 113871.0
2025-06-26 10:57:25,225 - ExploitationExpert - INFO - res_population_num: 16
2025-06-26 10:57:25,225 - ExploitationExpert - INFO - res_population_costs: [9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-06-26 10:57:25,225 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-26 10:57:25,230 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 10:57:25,230 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 29, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 95242.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 60, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 91924.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': array([33,  9,  8, 62, 39,  1, 11,  3, 30,  7, 32, 57, 19, 43, 47, 13, 50,
       49, 48, 36, 22,  4, 35, 16, 55, 51, 31, 41, 21, 25, 27, 38, 54,  5,
       12, 28, 42, 46, 40, 34, 17, 20,  6, 60,  2, 53,  0, 63, 26, 65, 24,
       64, 14, 10, 37, 15, 58, 18, 29, 52, 44, 59, 45, 23, 61, 56]), 'cur_cost': 104328.0}, {'tour': array([42, 45, 50, 16, 38, 34, 23, 27, 60, 58, 19, 55,  6, 15, 61,  7,  2,
       22, 53, 30, 63, 52,  8, 46, 43, 65, 39, 41, 49,  3, 37,  1, 21, 28,
       20, 10, 33, 14, 24, 18, 26, 44, 54,  0, 32, 35, 64, 51, 29,  9, 57,
       47, 13, 12, 36, 59,  4, 48,  5, 40, 62, 17, 25, 31, 56, 11]), 'cur_cost': 104638.0}, {'tour': array([ 2, 39, 19, 34, 40, 52, 16, 21, 32,  1, 38,  7, 37, 25, 62, 33,  9,
       56, 41, 50,  5, 43, 44, 10,  0, 28, 48, 60, 11, 64, 58, 36, 65, 12,
        8, 51, 53, 61, 13, 22,  4, 54, 45, 14, 42, 63, 24, 17, 29, 49, 18,
        3, 55, 30, 35, 20, 27, 15, 31, 57, 47, 26, 59,  6, 46, 23]), 'cur_cost': 113871.0}]
2025-06-26 10:57:25,233 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 10:57:25,233 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-26 10:57:25,233 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 10:57:25,233 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 29, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 95242.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 60, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 91924.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}]
2025-06-26 10:57:25,234 - __main__ - INFO - 进化阶段完成
2025-06-26 10:57:25,234 - __main__ - INFO - 开始评估阶段
2025-06-26 10:57:25,234 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 10:57:25,235 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 10:57:25,235 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 17289.0 → 91924.0 (Δ -74635.0)
- Mean cost: 94559.0 → 101193.0
- Diversity: 0.695 → 0.497 (Δ -0.1985)

#### Elite metrics
- Elite count: 8 → 16
- Best elite cost: 9521 → 9521 (Δ 0)

#### Strategy performance
- Explore: 7 inds, success 71.4% , avg Δ -8612.0
- Exploit: 3 inds, success 33.3% , avg Δ -2018.67

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 1 iterations: costs [17289.0], diversity [0.8969696969696969]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 10:57:25,235 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:57:36,669 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 71.4,
      "average_delta": -8612.0,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -2018.67,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore_heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "elite_count": 8
  }
}
```
2025-06-26 10:57:36,696 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 10:57:36,696 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 71.4,
      "average_delta": -8612.0,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -2018.67,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore_heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "elite_count": 8
  }
}
```
2025-06-26 10:57:36,696 - __main__ - INFO - 评估阶段完成
2025-06-26 10:57:36,696 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 71.4,
      "average_delta": -8612.0,
      "contribution_to_elite": "high"
    },
    "exploit": {
      "success_rate": 33.3,
      "average_delta": -2018.67,
      "contribution_to_elite": "low"
    }
  },
  "balance_state": "explore_heavy",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "elite_count": 8
  }
}
```
2025-06-26 10:57:36,696 - __main__ - INFO - 当前最佳适应度: 91924.0
2025-06-26 10:57:36,696 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_1.pkl
2025-06-26 10:57:36,696 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-06-26 10:57:36,703 - __main__ - INFO - 开始分析阶段
2025-06-26 10:57:36,703 - StatsExpert - INFO - 开始统计分析
2025-06-26 10:57:36,718 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 91924.0, 'max': 113871.0, 'mean': 101193.0, 'std': 5610.834750730055}, 'diversity': 0.7676767676767677, 'clusters': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 10:57:36,720 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 91924.0, 'max': 113871.0, 'mean': 101193.0, 'std': 5610.834750730055}, 'diversity_level': 0.7676767676767677, 'convergence_level': 0.0, 'clustering_info': {'clusters': 5, 'cluster_sizes': [5, 2, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-26 10:57:36,720 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 10:57:36,720 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 10:57:36,721 - PathExpert - INFO - 开始路径结构分析
2025-06-26 10:57:36,722 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 10:57:36,722 - PathExpert - INFO - 路径结构分析完成
2025-06-26 10:57:36,723 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 10:57:36,723 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 10:57:36,724 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 10:57:36,724 - EliteExpert - INFO - 开始精英解分析
2025-06-26 10:57:36,728 - EliteExpert - INFO - 精英解分析完成
2025-06-26 10:57:36,729 - __main__ - INFO - 精英专家分析报告: {'elite_count': 16, 'elite_common_features': {'common_edges': {}, 'common_edge_ratio': 0.0}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 82403.0, 'avg_gap': 85443.5625}, 'structure_gap': {'unique_elite_edges': 243, 'unique_pop_edges': 320, 'common_edges': 16}}, 'elite_diversity': {'diversity_score': 0.2941919191919191}}
2025-06-26 10:57:36,729 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 10:57:36,729 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 10:57:36,732 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 10:57:36,732 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 91924.0, mean 101193.0, max 113871.0, std 5610.834750730055
- diversity: 0.7676767676767677
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [17289.0, 91924.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 10:57:36,732 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:58:00,646 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high ruggedness indicated by a wide range of edge lengths (10.0 to 3032.0) and a long_edge_ratio of 0.249, suggesting uneven terrain with both short and long edges.",
    "modality": "Potential multimodality suggested by the diversity (0.768) and the spread of edge lengths, but no clear clustering information is available.",
    "deceptiveness": "Possible deceptiveness due to the presence of long edges and uneven edge length distribution, which might mislead local search heuristics."
  },
  "population_state": {
    "coverage": "Good coverage indicated by high diversity (0.768) and a density_grid showing nodes spread across multiple cells, though some cells are empty.",
    "convergence": "No convergence detected (convergence: 0.0), suggesting the population is still exploring the search space."
  },
  "difficult_regions": {
    "regions": "Low-density cells (e.g., [0,1], [1,0]) and long-edge corridors (edges in the upper quartile, >2469.0) are potential difficult regions.",
    "suggestions": "Focus on refining local search operators or introducing repair mechanisms for long edges."
  },
  "opportunity_regions": {
    "regions": "High-density cells (e.g., [0,0], [2,0], [2,2]) with 14 nodes each are potential opportunity regions.",
    "suggestions": "Exploit these regions by intensifying search around these nodes or using crossover operators that preserve common subpaths."
  },
  "evolution_phase": "Exploration phase, as indicated by high diversity and no convergence.",
  "evolution_direction": {
    "suggestions": [
      "Introduce edge recombination operators to exploit high-density regions.",
      "Use mutation operators that target long edges to improve difficult regions.",
      "Maintain diversity with niching or crowding techniques to prevent premature convergence."
    ]
  }
}
```
2025-06-26 10:58:00,646 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 10:58:00,646 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high ruggedness indicated by a wide range of edge lengths (10.0 to 3032.0) and a long_edge_ratio of 0.249, suggesting uneven terrain with both short and long edges.', 'modality': 'Potential multimodality suggested by the diversity (0.768) and the spread of edge lengths, but no clear clustering information is available.', 'deceptiveness': 'Possible deceptiveness due to the presence of long edges and uneven edge length distribution, which might mislead local search heuristics.'}, 'population_state': {'coverage': 'Good coverage indicated by high diversity (0.768) and a density_grid showing nodes spread across multiple cells, though some cells are empty.', 'convergence': 'No convergence detected (convergence: 0.0), suggesting the population is still exploring the search space.'}, 'difficult_regions': {'regions': 'Low-density cells (e.g., [0,1], [1,0]) and long-edge corridors (edges in the upper quartile, >2469.0) are potential difficult regions.', 'suggestions': 'Focus on refining local search operators or introducing repair mechanisms for long edges.'}, 'opportunity_regions': {'regions': 'High-density cells (e.g., [0,0], [2,0], [2,2]) with 14 nodes each are potential opportunity regions.', 'suggestions': 'Exploit these regions by intensifying search around these nodes or using crossover operators that preserve common subpaths.'}, 'evolution_phase': 'Exploration phase, as indicated by high diversity and no convergence.', 'evolution_direction': {'suggestions': ['Introduce edge recombination operators to exploit high-density regions.', 'Use mutation operators that target long edges to improve difficult regions.', 'Maintain diversity with niching or crowding techniques to prevent premature convergence.']}}
2025-06-26 10:58:00,646 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 10:58:00,646 - __main__ - INFO - 分析阶段完成
2025-06-26 10:58:00,646 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high ruggedness indicated by a wide range of edge lengths (10.0 to 3032.0) and a long_edge_ratio of 0.249, suggesting uneven terrain with both short and long edges.', 'modality': 'Potential multimodality suggested by the diversity (0.768) and the spread of edge lengths, but no clear clustering information is available.', 'deceptiveness': 'Possible deceptiveness due to the presence of long edges and uneven edge length distribution, which might mislead local search heuristics.'}, 'population_state': {'coverage': 'Good coverage indicated by high diversity (0.768) and a density_grid showing nodes spread across multiple cells, though some cells are empty.', 'convergence': 'No convergence detected (convergence: 0.0), suggesting the population is still exploring the search space.'}, 'difficult_regions': {'regions': 'Low-density cells (e.g., [0,1], [1,0]) and long-edge corridors (edges in the upper quartile, >2469.0) are potential difficult regions.', 'suggestions': 'Focus on refining local search operators or introducing repair mechanisms for long edges.'}, 'opportunity_regions': {'regions': 'High-density cells (e.g., [0,0], [2,0], [2,2]) with 14 nodes each are potential opportunity regions.', 'suggestions': 'Exploit these regions by intensifying search around these nodes or using crossover operators that preserve common subpaths.'}, 'evolution_phase': 'Exploration phase, as indicated by high diversity and no convergence.', 'evolution_direction': {'suggestions': ['Introduce edge recombination operators to exploit high-density regions.', 'Use mutation operators that target long edges to improve difficult regions.', 'Maintain diversity with niching or crowding techniques to prevent premature convergence.']}}
2025-06-26 10:58:00,646 - __main__ - INFO - 开始策略分配阶段
2025-06-26 10:58:00,646 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 10:58:00,651 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0
  • best_cost: 91924.0
  • mean_cost: 101193.0
- Search-space: ruggedness Moderate to high ruggedness indicated by a wide range of edge lengths (10.0 to 3032.0) and a long_edge_ratio of 0.249, suggesting uneven terrain with both short and long edges., modality Potential multimodality suggested by the diversity (0.768) and the spread of edge lengths, but no clear clustering information is available., deceptiveness Possible deceptiveness due to the presence of long edges and uneven edge length distribution, which might mislead local search heuristics.
- Evolution phase: Exploration phase, as indicated by high diversity and no convergence.
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): regions, suggestions
- Opportunity regions (# 2): regions, suggestions
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 71.4,
     ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 10:58:00,651 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 10:58:00,651 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:58:11,071 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early evolution phase favor exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 10:58:11,071 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 10:58:11,071 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 10:58:11,071 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early evolution phase favor exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 10:58:11,075 - __main__ - INFO - 策略分配阶段完成
2025-06-26 10:58:11,075 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 10:58:11,075 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and early evolution phase favor exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 10:58:11,075 - __main__ - INFO - 开始进化阶段
2025-06-26 10:58:11,075 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 10:58:11,075 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:58:11,076 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 40.0%)
- Population diversity: 0.4969230769230769
- Evolution phase: Exploration phase, as indicated by high diversity and no convergence.
- Landscape recommends focus: balance
- Opportunity regions (sample): regions, suggestions
- Difficult regions to avoid (sample): regions, suggestions
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:58:11,076 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:58:11,076 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:58:25,685 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 63, 64, 65],
  "targeted_regions": "Low-density cells near opportunity regions",
  "strategy_comment": "Balanced exploration with modular jumps to increase diversity"
}
```
2025-06-26 10:58:25,685 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:58:25,685 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100137.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 63, 64, 65]
2025-06-26 10:58:25,688 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 63, 64, 65], 'cur_cost': 100137.0}
2025-06-26 10:58:25,688 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 10:58:25,688 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:58:25,688 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 20.0%)
- Population diversity: 0.5
- Evolution phase: Exploration phase, as indicated by high diversity and no convergence.
- Landscape recommends focus: balance
- Opportunity regions (sample): regions, suggestions
- Difficult regions to avoid (sample): regions, suggestions
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:58:25,688 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:58:25,688 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:58:40,038 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "low-density cells adjacent to opportunity regions",
  "strategy_comment": "Balanced exploration with modular jumps"
}
```
2025-06-26 10:58:40,038 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:58:40,044 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 10:58:40,044 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 10:58:40,044 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 10:58:40,044 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:58:40,044 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 20.0%)
- Population diversity: 0.5
- Evolution phase: Exploration phase, as indicated by high diversity and no convergence.
- Landscape recommends focus: balance
- Opportunity regions (sample): regions, suggestions
- Difficult regions to avoid (sample): regions, suggestions
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:58:40,046 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:58:40,047 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:58:54,455 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 66, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 19, 30, 41, 52, 4, 16, 27, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64],
  "targeted_regions": "Adjacent low-density cells near opportunity regions",
  "strategy_comment": "Balanced exploration with spiral-like diversification"
}
```
2025-06-26 10:58:54,455 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:58:54,455 - ExplorationExpert - INFO - 探索路径生成完成，成本: 96125.0, 路径: [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 27, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 19, 30, 41, 52, 4, 16, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64]
2025-06-26 10:58:54,455 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 27, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 19, 30, 41, 52, 4, 16, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64], 'cur_cost': 96125.0}
2025-06-26 10:58:54,455 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 10:58:54,455 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:58:54,455 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 95242.0 (rank 10.0%)
- Population diversity: 0.5153846153846153
- Evolution phase: Exploration phase, as indicated by high diversity and no convergence.
- Landscape recommends focus: balance
- Opportunity regions (sample): regions, suggestions
- Difficult regions to avoid (sample): regions, suggestions
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:58:54,455 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:58:54,455 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:59:09,607 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "Low-density cells near opportunity regions",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 10:59:09,607 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:59:09,607 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 10:59:09,607 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 10:59:09,607 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 10:59:09,607 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:59:09,607 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 30.0%)
- Population diversity: 0.5092307692307693
- Evolution phase: Exploration phase, as indicated by high diversity and no convergence.
- Landscape recommends focus: balance
- Opportunity regions (sample): regions, suggestions
- Difficult regions to avoid (sample): regions, suggestions
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:59:09,607 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:59:09,607 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:59:24,298 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "Low-density cells near opportunity regions",
  "strategy_comment": "Structured leapfrog pattern to explore sparse regions"
}
```
2025-06-26 10:59:24,298 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:59:24,298 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 10:59:24,298 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 10:59:24,298 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 10:59:24,298 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 10:59:24,298 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 91924.0 (rank 0.0%)
- Population diversity: 0.5092307692307693
- Evolution phase: Exploration phase, as indicated by high diversity and no convergence.
- Landscape recommends focus: balance
- Opportunity regions (sample): regions, suggestions
- Difficult regions to avoid (sample): regions, suggestions
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 10:59:24,298 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 10:59:24,298 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:59:38,667 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "Low-density cells in modular sequence",
  "strategy_comment": "Structured modular traversal to explore sparse regions"
}
```
2025-06-26 10:59:38,667 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 10:59:38,667 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 10:59:38,667 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 10:59:38,667 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 10:59:38,667 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 10:59:38,667 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 10:59:38,667 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 112612.0
2025-06-26 10:59:39,168 - ExploitationExpert - INFO - res_population_num: 17
2025-06-26 10:59:39,169 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521]
2025-06-26 10:59:39,169 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-26 10:59:39,174 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 10:59:39,174 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 63, 64, 65], 'cur_cost': 100137.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 27, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 19, 30, 41, 52, 4, 16, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64], 'cur_cost': 96125.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': array([21, 63, 37,  6, 44, 25, 50, 38, 54, 41, 27,  7, 39, 16,  4,  1, 22,
       10, 19, 33, 47,  5, 29, 65, 52, 60, 42, 64, 57, 13, 24, 26, 17, 15,
        0, 58, 14, 28, 62, 23, 55, 46, 49, 32,  9, 40, 36, 61, 18,  3, 12,
       35, 45, 30, 31, 56, 51, 20, 34,  8,  2, 43, 11, 48, 53, 59]), 'cur_cost': 112612.0}, {'tour': array([33,  9,  8, 62, 39,  1, 11,  3, 30,  7, 32, 57, 19, 43, 47, 13, 50,
       49, 48, 36, 22,  4, 35, 16, 55, 51, 31, 41, 21, 25, 27, 38, 54,  5,
       12, 28, 42, 46, 40, 34, 17, 20,  6, 60,  2, 53,  0, 63, 26, 65, 24,
       64, 14, 10, 37, 15, 58, 18, 29, 52, 44, 59, 45, 23, 61, 56]), 'cur_cost': 104328.0}, {'tour': array([42, 45, 50, 16, 38, 34, 23, 27, 60, 58, 19, 55,  6, 15, 61,  7,  2,
       22, 53, 30, 63, 52,  8, 46, 43, 65, 39, 41, 49,  3, 37,  1, 21, 28,
       20, 10, 33, 14, 24, 18, 26, 44, 54,  0, 32, 35, 64, 51, 29,  9, 57,
       47, 13, 12, 36, 59,  4, 48,  5, 40, 62, 17, 25, 31, 56, 11]), 'cur_cost': 104638.0}, {'tour': array([ 2, 39, 19, 34, 40, 52, 16, 21, 32,  1, 38,  7, 37, 25, 62, 33,  9,
       56, 41, 50,  5, 43, 44, 10,  0, 28, 48, 60, 11, 64, 58, 36, 65, 12,
        8, 51, 53, 61, 13, 22,  4, 54, 45, 14, 42, 63, 24, 17, 29, 49, 18,
        3, 55, 30, 35, 20, 27, 15, 31, 57, 47, 26, 59,  6, 46, 23]), 'cur_cost': 113871.0}]
2025-06-26 10:59:39,176 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 10:59:39,176 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-26 10:59:39,176 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 10:59:39,177 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 10:59:39,177 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 10:59:39,177 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 10:59:39,177 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 102532.0
2025-06-26 10:59:39,681 - ExploitationExpert - INFO - res_population_num: 17
2025-06-26 10:59:39,681 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521]
2025-06-26 10:59:39,681 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-26 10:59:39,685 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 10:59:39,688 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 63, 64, 65], 'cur_cost': 100137.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 27, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 19, 30, 41, 52, 4, 16, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64], 'cur_cost': 96125.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': array([21, 63, 37,  6, 44, 25, 50, 38, 54, 41, 27,  7, 39, 16,  4,  1, 22,
       10, 19, 33, 47,  5, 29, 65, 52, 60, 42, 64, 57, 13, 24, 26, 17, 15,
        0, 58, 14, 28, 62, 23, 55, 46, 49, 32,  9, 40, 36, 61, 18,  3, 12,
       35, 45, 30, 31, 56, 51, 20, 34,  8,  2, 43, 11, 48, 53, 59]), 'cur_cost': 112612.0}, {'tour': array([ 1, 23, 46, 35, 20, 65,  5, 59, 61, 44,  2, 11, 56,  9, 19, 53, 63,
       38, 50,  4, 25, 45, 34, 21, 29,  7, 39, 55, 43, 26, 22,  0, 57,  3,
       36, 42, 64, 15, 31, 54, 49, 30, 40, 13, 62,  6, 60, 18, 58, 33, 28,
       27, 32, 37, 16, 41, 51, 17, 12, 47,  8, 10, 24, 52, 48, 14]), 'cur_cost': 102532.0}, {'tour': array([42, 45, 50, 16, 38, 34, 23, 27, 60, 58, 19, 55,  6, 15, 61,  7,  2,
       22, 53, 30, 63, 52,  8, 46, 43, 65, 39, 41, 49,  3, 37,  1, 21, 28,
       20, 10, 33, 14, 24, 18, 26, 44, 54,  0, 32, 35, 64, 51, 29,  9, 57,
       47, 13, 12, 36, 59,  4, 48,  5, 40, 62, 17, 25, 31, 56, 11]), 'cur_cost': 104638.0}, {'tour': array([ 2, 39, 19, 34, 40, 52, 16, 21, 32,  1, 38,  7, 37, 25, 62, 33,  9,
       56, 41, 50,  5, 43, 44, 10,  0, 28, 48, 60, 11, 64, 58, 36, 65, 12,
        8, 51, 53, 61, 13, 22,  4, 54, 45, 14, 42, 63, 24, 17, 29, 49, 18,
        3, 55, 30, 35, 20, 27, 15, 31, 57, 47, 26, 59,  6, 46, 23]), 'cur_cost': 113871.0}]
2025-06-26 10:59:39,689 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 10:59:39,689 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-06-26 10:59:39,689 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 10:59:39,689 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 10:59:39,690 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 10:59:39,690 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 10:59:39,690 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 110701.0
2025-06-26 10:59:40,194 - ExploitationExpert - INFO - res_population_num: 17
2025-06-26 10:59:40,194 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521]
2025-06-26 10:59:40,194 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64)]
2025-06-26 10:59:40,199 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 10:59:40,199 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 63, 64, 65], 'cur_cost': 100137.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 27, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 19, 30, 41, 52, 4, 16, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64], 'cur_cost': 96125.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': array([21, 63, 37,  6, 44, 25, 50, 38, 54, 41, 27,  7, 39, 16,  4,  1, 22,
       10, 19, 33, 47,  5, 29, 65, 52, 60, 42, 64, 57, 13, 24, 26, 17, 15,
        0, 58, 14, 28, 62, 23, 55, 46, 49, 32,  9, 40, 36, 61, 18,  3, 12,
       35, 45, 30, 31, 56, 51, 20, 34,  8,  2, 43, 11, 48, 53, 59]), 'cur_cost': 112612.0}, {'tour': array([ 1, 23, 46, 35, 20, 65,  5, 59, 61, 44,  2, 11, 56,  9, 19, 53, 63,
       38, 50,  4, 25, 45, 34, 21, 29,  7, 39, 55, 43, 26, 22,  0, 57,  3,
       36, 42, 64, 15, 31, 54, 49, 30, 40, 13, 62,  6, 60, 18, 58, 33, 28,
       27, 32, 37, 16, 41, 51, 17, 12, 47,  8, 10, 24, 52, 48, 14]), 'cur_cost': 102532.0}, {'tour': array([47, 51, 44, 20, 10,  4, 50, 49, 53, 15, 45, 62, 28, 61, 32, 29,  0,
       57, 58, 35, 22, 54, 30, 38, 25,  3, 33, 40,  9,  7, 37,  8, 41, 19,
       55, 63, 65, 21, 14,  5, 52, 48,  2, 17, 12, 18, 36, 59, 27, 24,  1,
       13, 39, 60, 34, 16, 56, 31, 64, 23, 26, 42,  6, 46, 11, 43]), 'cur_cost': 110701.0}, {'tour': array([ 2, 39, 19, 34, 40, 52, 16, 21, 32,  1, 38,  7, 37, 25, 62, 33,  9,
       56, 41, 50,  5, 43, 44, 10,  0, 28, 48, 60, 11, 64, 58, 36, 65, 12,
        8, 51, 53, 61, 13, 22,  4, 54, 45, 14, 42, 63, 24, 17, 29, 49, 18,
        3, 55, 30, 35, 20, 27, 15, 31, 57, 47, 26, 59,  6, 46, 23]), 'cur_cost': 113871.0}]
2025-06-26 10:59:40,201 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-26 10:59:40,202 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-06-26 10:59:40,202 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 10:59:40,203 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 10:59:40,203 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 10:59:40,203 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 10:59:40,203 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 91131.0
2025-06-26 10:59:40,710 - ExploitationExpert - INFO - res_population_num: 20
2025-06-26 10:59:40,710 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521, 9521, 9521, 9521]
2025-06-26 10:59:40,711 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-26 10:59:40,716 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 10:59:40,716 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 63, 64, 65], 'cur_cost': 100137.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 27, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 19, 30, 41, 52, 4, 16, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64], 'cur_cost': 96125.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': array([21, 63, 37,  6, 44, 25, 50, 38, 54, 41, 27,  7, 39, 16,  4,  1, 22,
       10, 19, 33, 47,  5, 29, 65, 52, 60, 42, 64, 57, 13, 24, 26, 17, 15,
        0, 58, 14, 28, 62, 23, 55, 46, 49, 32,  9, 40, 36, 61, 18,  3, 12,
       35, 45, 30, 31, 56, 51, 20, 34,  8,  2, 43, 11, 48, 53, 59]), 'cur_cost': 112612.0}, {'tour': array([ 1, 23, 46, 35, 20, 65,  5, 59, 61, 44,  2, 11, 56,  9, 19, 53, 63,
       38, 50,  4, 25, 45, 34, 21, 29,  7, 39, 55, 43, 26, 22,  0, 57,  3,
       36, 42, 64, 15, 31, 54, 49, 30, 40, 13, 62,  6, 60, 18, 58, 33, 28,
       27, 32, 37, 16, 41, 51, 17, 12, 47,  8, 10, 24, 52, 48, 14]), 'cur_cost': 102532.0}, {'tour': array([47, 51, 44, 20, 10,  4, 50, 49, 53, 15, 45, 62, 28, 61, 32, 29,  0,
       57, 58, 35, 22, 54, 30, 38, 25,  3, 33, 40,  9,  7, 37,  8, 41, 19,
       55, 63, 65, 21, 14,  5, 52, 48,  2, 17, 12, 18, 36, 59, 27, 24,  1,
       13, 39, 60, 34, 16, 56, 31, 64, 23, 26, 42,  6, 46, 11, 43]), 'cur_cost': 110701.0}, {'tour': array([ 4,  2, 28, 34, 63, 52, 39, 27, 26, 19, 41, 56, 37, 30, 36, 46, 24,
       23, 29, 60,  0,  6, 11, 54,  9,  3, 62, 50, 47, 16, 58,  5, 55, 65,
       13, 40, 53, 51, 31, 43, 44,  8, 32, 25,  7, 42, 38,  1, 21, 15, 57,
       17, 14, 18, 59, 33, 35, 20, 12, 64, 48, 45, 61, 22, 49, 10]), 'cur_cost': 91131.0}]
2025-06-26 10:59:40,719 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 10:59:40,719 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-06-26 10:59:40,719 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 10:59:40,719 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 62, 1, 13, 25, 37, 49, 61, 63, 64, 65], 'cur_cost': 100137.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 65, 11, 22, 33, 44, 55, 1, 13, 24, 35, 46, 57, 27, 10, 21, 32, 43, 54, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 3, 15, 26, 37, 48, 59, 8, 19, 30, 41, 52, 4, 16, 38, 49, 60, 7, 18, 29, 40, 51, 5, 17, 28, 39, 50, 61, 6, 62, 63, 64], 'cur_cost': 96125.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}]
2025-06-26 10:59:40,720 - __main__ - INFO - 进化阶段完成
2025-06-26 10:59:40,720 - __main__ - INFO - 开始评估阶段
2025-06-26 10:59:40,720 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 10:59:40,720 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 10:59:40,720 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 91924.0 → 91131.0 (Δ 793.0)
- Mean cost: 101193.0 → 101885.8
- Diversity: 0.497 → 0.566 (Δ 0.0692)

#### Elite metrics
- Elite count: 16 → 20
- Best elite cost: 9521 → 9521 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 33.3% , avg Δ -2365.67
- Exploit: 4 inds, success 50.0% , avg Δ 1816.5

#### Other indicators
- No-change individuals: 1
- Regression individuals: 5
- Historical trends: Last 2 iterations: costs [17289.0, 91924.0], diversity [0.8969696969696969, 0.7676767676767677]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 10:59:40,720 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 10:59:55,667 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": -2365.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": 1816.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": {
    "current_ratio": "explore: 60%, exploit: 40%",
    "assessment": "exploit is more effective but explore is needed for diversity",
    "needs_adjustment": "yes"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Increase explore ratio slightly to leverage its moderate success rate and negative delta, which can help in finding better solutions. Increase diversity weight to maintain population diversity and prevent premature convergence."
  }
}
```
2025-06-26 10:59:55,698 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 10:59:55,698 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": -2365.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": 1816.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": {
    "current_ratio": "explore: 60%, exploit: 40%",
    "assessment": "exploit is more effective but explore is needed for diversity",
    "needs_adjustment": "yes"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Increase explore ratio slightly to leverage its moderate success rate and negative delta, which can help in finding better solutions. Increase diversity weight to maintain population diversity and prevent premature convergence."
  }
}
```
2025-06-26 10:59:55,698 - __main__ - INFO - 评估阶段完成
2025-06-26 10:59:55,698 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
      "average_delta": -2365.67,
      "contribution_to_elite": "moderate"
    },
    "exploit": {
      "success_rate": 50.0,
      "average_delta": 1816.5,
      "contribution_to_elite": "high"
    }
  },
  "balance_state": {
    "current_ratio": "explore: 60%, exploit: 40%",
    "assessment": "exploit is more effective but explore is needed for diversity",
    "needs_adjustment": "yes"
  },
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "additional_notes": "Increase explore ratio slightly to leverage its moderate success rate and negative delta, which can help in finding better solutions. Increase diversity weight to maintain population diversity and prevent premature convergence."
  }
}
```
2025-06-26 10:59:55,698 - __main__ - INFO - 当前最佳适应度: 91131.0
2025-06-26 10:59:55,702 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_2.pkl
2025-06-26 10:59:55,702 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-06-26 10:59:55,702 - __main__ - INFO - 开始分析阶段
2025-06-26 10:59:55,702 - StatsExpert - INFO - 开始统计分析
2025-06-26 10:59:55,718 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 91131.0, 'max': 112612.0, 'mean': 101885.8, 'std': 5882.280234738907}, 'diversity': 0.7622895622895624, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 10:59:55,718 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 91131.0, 'max': 112612.0, 'mean': 101885.8, 'std': 5882.280234738907}, 'diversity_level': 0.7622895622895624, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-26 10:59:55,720 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 10:59:55,720 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 10:59:55,720 - PathExpert - INFO - 开始路径结构分析
2025-06-26 10:59:55,723 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 10:59:55,723 - PathExpert - INFO - 路径结构分析完成
2025-06-26 10:59:55,723 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 10:59:55,723 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 10:59:55,724 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 10:59:55,724 - EliteExpert - INFO - 开始精英解分析
2025-06-26 10:59:55,730 - EliteExpert - INFO - 精英解分析完成
2025-06-26 10:59:55,731 - __main__ - INFO - 精英专家分析报告: {'elite_count': 20, 'elite_common_features': {'common_edges': {'(24, 31)': 0.7}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 81610.0, 'avg_gap': 87382.05}, 'structure_gap': {'unique_elite_edges': 247, 'unique_pop_edges': 370, 'common_edges': 21}}, 'elite_diversity': {'diversity_score': 0.29011164274322165}}
2025-06-26 10:59:55,731 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 10:59:55,731 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 10:59:55,733 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 10:59:55,733 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 91131.0, mean 101885.8, max 112612.0, std 5882.280234738907
- diversity: 0.7622895622895624
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [17289.0, 91924.0, 91131.0] (Δ 396.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 10:59:55,734 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:00:17,194 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 10.0 to 3032.0, std 5882.28)",
    "modality": "Potentially multimodal (direction histogram shows varied edge orientations)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio 0.249 suggests some long edges may mislead)"
  },
  "population_state": {
    "coverage": "Good spatial diversity (diversity 0.76, density_grid shows multiple active cells)",
    "convergence": "Early stage (convergence 0.0, recent best costs still improving)"
  },
  "difficult_regions": {
    "long_edge_corridors": "Edges in max quartile (2469.0-3032.0 length)",
    "low_density_zones": "Top-right grid cell (0 density), possibly sparse node distribution"
  },
  "opportunity_regions": {
    "high_density_clusters": [
      "Bottom-left (14 nodes) and bottom-right (14 nodes) grid cells",
      "Central-right cell (12 nodes)"
    ],
    "short_edge_zones": "Edges in min quartile (10.0-1075.0 length)"
  },
  "evolution_phase": "Exploration-dominant (high diversity, no elite convergence)",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Focused 2-opt around high-density clusters",
    "2. Directed mutation preserving short-edge sequences",
    "3. Restart mechanism for long-edge corridors",
    "4. Diversity maintenance in sparse regions"
  ]
}
```
2025-06-26 11:00:17,194 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 11:00:17,194 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 10.0 to 3032.0, std 5882.28)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio 0.249 suggests some long edges may mislead)'}, 'population_state': {'coverage': 'Good spatial diversity (diversity 0.76, density_grid shows multiple active cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs still improving)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max quartile (2469.0-3032.0 length)', 'low_density_zones': 'Top-right grid cell (0 density), possibly sparse node distribution'}, 'opportunity_regions': {'high_density_clusters': ['Bottom-left (14 nodes) and bottom-right (14 nodes) grid cells', 'Central-right cell (12 nodes)'], 'short_edge_zones': 'Edges in min quartile (10.0-1075.0 length)'}, 'evolution_phase': 'Exploration-dominant (high diversity, no elite convergence)', 'evolution_direction': ['Operator suggestions:', '1. Focused 2-opt around high-density clusters', '2. Directed mutation preserving short-edge sequences', '3. Restart mechanism for long-edge corridors', '4. Diversity maintenance in sparse regions']}
2025-06-26 11:00:17,199 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 11:00:17,199 - __main__ - INFO - 分析阶段完成
2025-06-26 11:00:17,199 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 10.0 to 3032.0, std 5882.28)', 'modality': 'Potentially multimodal (direction histogram shows varied edge orientations)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio 0.249 suggests some long edges may mislead)'}, 'population_state': {'coverage': 'Good spatial diversity (diversity 0.76, density_grid shows multiple active cells)', 'convergence': 'Early stage (convergence 0.0, recent best costs still improving)'}, 'difficult_regions': {'long_edge_corridors': 'Edges in max quartile (2469.0-3032.0 length)', 'low_density_zones': 'Top-right grid cell (0 density), possibly sparse node distribution'}, 'opportunity_regions': {'high_density_clusters': ['Bottom-left (14 nodes) and bottom-right (14 nodes) grid cells', 'Central-right cell (12 nodes)'], 'short_edge_zones': 'Edges in min quartile (10.0-1075.0 length)'}, 'evolution_phase': 'Exploration-dominant (high diversity, no elite convergence)', 'evolution_direction': ['Operator suggestions:', '1. Focused 2-opt around high-density clusters', '2. Directed mutation preserving short-edge sequences', '3. Restart mechanism for long-edge corridors', '4. Diversity maintenance in sparse regions']}
2025-06-26 11:00:17,199 - __main__ - INFO - 开始策略分配阶段
2025-06-26 11:00:17,200 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 11:00:17,200 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0
  • best_cost: 91131.0
  • mean_cost: 101885.8
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 10.0 to 3032.0, std 5882.28), modality Potentially multimodal (direction histogram shows varied edge orientations), deceptiveness Possible deceptive regions (long_edge_ratio 0.249 suggests some long edges may mislead)
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): long_edge_corridors, low_density_zones
- Opportunity regions (# 2): high_density_cluster, short_edge_zones
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage",
  "strategy_effectiveness": {
    "explore": {
      "success_rate": 33.3,
 ...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 11:00:17,200 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 11:00:17,200 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:00:27,589 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration-dominant phase suggest prioritizing exploration (70%). Top 60% explore to cover opportunity regions, bottom 40% exploit best solutions."
}
```
2025-06-26 11:00:27,590 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 11:00:27,590 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 11:00:27,591 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration-dominant phase suggest prioritizing exploration (70%). Top 60% explore to cover opportunity regions, bottom 40% exploit best solutions."
}
```
2025-06-26 11:00:27,591 - __main__ - INFO - 策略分配阶段完成
2025-06-26 11:00:27,591 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 11:00:27,591 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and exploration-dominant phase suggest prioritizing exploration (70%). Top 60% explore to cover opportunity regions, bottom 40% exploit best solutions."
}
```
2025-06-26 11:00:27,592 - __main__ - INFO - 开始进化阶段
2025-06-26 11:00:27,592 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 11:00:27,593 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:00:27,593 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100137.0 (rank 20.0%)
- Population diversity: 0.5661538461538461
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:00:27,593 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:00:27,594 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:00:42,080 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Reverse initial segment to explore sparse cells near start"
}
```
2025-06-26 11:00:42,080 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:00:42,080 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13945.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-26 11:00:42,083 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}
2025-06-26 11:00:42,083 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 11:00:42,083 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:00:42,084 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 30.0%)
- Population diversity: 0.6307692307692307
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:00:42,084 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:00:42,084 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:00:56,153 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_cl, adjacent low-density cells",
  "strategy_comment": "Layered traversal favoring clusters, skipping long edges"
}
```
2025-06-26 11:00:56,153 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:00:56,153 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 11:00:56,156 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 11:00:56,156 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 11:00:56,156 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:00:56,157 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 96125.0 (rank 20.0%)
- Population diversity: 0.6461538461538462
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:00:56,157 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:00:56,158 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:01:10,355 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Prioritize dense clusters with short edges, reverse subpaths for diversity"
}
```
2025-06-26 11:01:10,355 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:01:10,355 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13945.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-26 11:01:10,355 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}
2025-06-26 11:01:10,355 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 11:01:10,355 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:01:10,355 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 40.0%)
- Population diversity: 0.5661538461538461
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:01:10,355 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:01:10,355 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:01:23,957 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Prioritize dense clusters with short edges, avoid sparse zones"
}
```
2025-06-26 11:01:23,959 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:01:23,959 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13945.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-26 11:01:23,959 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}
2025-06-26 11:01:23,959 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 11:01:23,959 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:01:23,959 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 50.0%)
- Population diversity: 0.5661538461538461
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:01:23,959 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:01:23,959 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:01:38,336 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_cl, short_edge_zone",
  "strategy_comment": "Reverse initial segment to explore sparse cells near opportunities"
}
```
2025-06-26 11:01:38,337 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:01:38,337 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13945.0, 路径: [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65]
2025-06-26 11:01:38,338 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}
2025-06-26 11:01:38,338 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 11:01:38,338 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:01:38,338 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 60.0%)
- Population diversity: 0.5661538461538461
- Evolution phase: Exploration-dominant (high diversity, no elite convergence)
- Landscape recommends focus: balance
- Opportunity regions (sample): high_density_cl, short_edge_zone
- Difficult regions to avoid (sample): long_edge_corri, low_density_zon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:01:38,338 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:01:38,338 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:01:52,977 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high_density_cl, adjacent low-density cells",
  "strategy_comment": "Layered spiral through opportunity zones, skipping long edges"
}
```
2025-06-26 11:01:52,977 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:01:52,981 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 11:01:52,981 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 11:01:52,981 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 11:01:52,981 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 11:01:52,981 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 11:01:52,982 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 120618.0
2025-06-26 11:01:54,849 - ExploitationExpert - INFO - res_population_num: 21
2025-06-26 11:01:54,849 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521]
2025-06-26 11:01:54,849 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-06-26 11:01:54,860 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 11:01:54,860 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([29, 41, 19, 49, 22, 15,  8, 26,  1, 17, 31, 32, 10, 43, 13, 55, 35,
       18, 37,  6, 46, 54, 50, 52, 25, 47,  2, 58, 63,  0, 65, 44, 30, 42,
       57, 59, 16, 48, 56, 27,  4, 40, 62, 12, 24,  9, 51, 61, 38, 14, 39,
       21,  3, 23, 64, 34, 11,  7, 33, 53,  5, 45, 60, 28, 36, 20]), 'cur_cost': 120618.0}, {'tour': array([ 1, 23, 46, 35, 20, 65,  5, 59, 61, 44,  2, 11, 56,  9, 19, 53, 63,
       38, 50,  4, 25, 45, 34, 21, 29,  7, 39, 55, 43, 26, 22,  0, 57,  3,
       36, 42, 64, 15, 31, 54, 49, 30, 40, 13, 62,  6, 60, 18, 58, 33, 28,
       27, 32, 37, 16, 41, 51, 17, 12, 47,  8, 10, 24, 52, 48, 14]), 'cur_cost': 102532.0}, {'tour': array([47, 51, 44, 20, 10,  4, 50, 49, 53, 15, 45, 62, 28, 61, 32, 29,  0,
       57, 58, 35, 22, 54, 30, 38, 25,  3, 33, 40,  9,  7, 37,  8, 41, 19,
       55, 63, 65, 21, 14,  5, 52, 48,  2, 17, 12, 18, 36, 59, 27, 24,  1,
       13, 39, 60, 34, 16, 56, 31, 64, 23, 26, 42,  6, 46, 11, 43]), 'cur_cost': 110701.0}, {'tour': array([ 4,  2, 28, 34, 63, 52, 39, 27, 26, 19, 41, 56, 37, 30, 36, 46, 24,
       23, 29, 60,  0,  6, 11, 54,  9,  3, 62, 50, 47, 16, 58,  5, 55, 65,
       13, 40, 53, 51, 31, 43, 44,  8, 32, 25,  7, 42, 38,  1, 21, 15, 57,
       17, 14, 18, 59, 33, 35, 20, 12, 64, 48, 45, 61, 22, 49, 10]), 'cur_cost': 91131.0}]
2025-06-26 11:01:54,863 - ExploitationExpert - INFO - 局部搜索耗时: 1.88秒
2025-06-26 11:01:54,863 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 11, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 11, 'cache_hits': 0, 'similarity_calculations': 55, 'cache_hit_rate': 0.0, 'cache_size': 55}}
2025-06-26 11:01:54,863 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 11:01:54,863 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 11:01:54,864 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 11:01:54,864 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 11:01:54,864 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 107956.0
2025-06-26 11:01:55,368 - ExploitationExpert - INFO - res_population_num: 22
2025-06-26 11:01:55,368 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521, 9521]
2025-06-26 11:01:55,368 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-26 11:01:55,380 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 11:01:55,380 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([29, 41, 19, 49, 22, 15,  8, 26,  1, 17, 31, 32, 10, 43, 13, 55, 35,
       18, 37,  6, 46, 54, 50, 52, 25, 47,  2, 58, 63,  0, 65, 44, 30, 42,
       57, 59, 16, 48, 56, 27,  4, 40, 62, 12, 24,  9, 51, 61, 38, 14, 39,
       21,  3, 23, 64, 34, 11,  7, 33, 53,  5, 45, 60, 28, 36, 20]), 'cur_cost': 120618.0}, {'tour': array([57, 38, 25, 64, 53, 24, 43, 26, 10, 44, 54, 48, 46, 28, 36, 31, 59,
       21, 29, 55,  6, 33, 61,  1,  3, 42,  5, 40, 12, 52, 30, 16, 15, 14,
       13, 19, 35, 39,  9, 20, 51, 34, 62, 17, 32, 41, 60,  8, 11, 63,  2,
       50, 49, 47, 37, 58, 22,  0, 18,  7,  4, 23, 56, 27, 45, 65]), 'cur_cost': 107956.0}, {'tour': array([47, 51, 44, 20, 10,  4, 50, 49, 53, 15, 45, 62, 28, 61, 32, 29,  0,
       57, 58, 35, 22, 54, 30, 38, 25,  3, 33, 40,  9,  7, 37,  8, 41, 19,
       55, 63, 65, 21, 14,  5, 52, 48,  2, 17, 12, 18, 36, 59, 27, 24,  1,
       13, 39, 60, 34, 16, 56, 31, 64, 23, 26, 42,  6, 46, 11, 43]), 'cur_cost': 110701.0}, {'tour': array([ 4,  2, 28, 34, 63, 52, 39, 27, 26, 19, 41, 56, 37, 30, 36, 46, 24,
       23, 29, 60,  0,  6, 11, 54,  9,  3, 62, 50, 47, 16, 58,  5, 55, 65,
       13, 40, 53, 51, 31, 43, 44,  8, 32, 25,  7, 42, 38,  1, 21, 15, 57,
       17, 14, 18, 59, 33, 35, 20, 12, 64, 48, 45, 61, 22, 49, 10]), 'cur_cost': 91131.0}]
2025-06-26 11:01:55,383 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 11:01:55,383 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 12, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 12, 'cache_hits': 0, 'similarity_calculations': 66, 'cache_hit_rate': 0.0, 'cache_size': 66}}
2025-06-26 11:01:55,383 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 11:01:55,383 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 11:01:55,384 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 11:01:55,384 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 11:01:55,384 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 112065.0
2025-06-26 11:01:55,887 - ExploitationExpert - INFO - res_population_num: 23
2025-06-26 11:01:55,887 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521, 9521, 9521]
2025-06-26 11:01:55,887 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-26 11:01:55,899 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 11:01:55,899 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([29, 41, 19, 49, 22, 15,  8, 26,  1, 17, 31, 32, 10, 43, 13, 55, 35,
       18, 37,  6, 46, 54, 50, 52, 25, 47,  2, 58, 63,  0, 65, 44, 30, 42,
       57, 59, 16, 48, 56, 27,  4, 40, 62, 12, 24,  9, 51, 61, 38, 14, 39,
       21,  3, 23, 64, 34, 11,  7, 33, 53,  5, 45, 60, 28, 36, 20]), 'cur_cost': 120618.0}, {'tour': array([57, 38, 25, 64, 53, 24, 43, 26, 10, 44, 54, 48, 46, 28, 36, 31, 59,
       21, 29, 55,  6, 33, 61,  1,  3, 42,  5, 40, 12, 52, 30, 16, 15, 14,
       13, 19, 35, 39,  9, 20, 51, 34, 62, 17, 32, 41, 60,  8, 11, 63,  2,
       50, 49, 47, 37, 58, 22,  0, 18,  7,  4, 23, 56, 27, 45, 65]), 'cur_cost': 107956.0}, {'tour': array([ 6, 17, 34, 39, 26,  3, 38, 64,  0, 11, 56,  2, 31, 35, 10, 42,  4,
       20, 18, 59, 23, 16, 54, 52, 28,  5, 50, 21, 45, 40,  8,  9, 30,  7,
       47,  1, 36, 49, 61, 33, 32, 53, 15, 51, 13, 57, 46, 14, 43, 37, 58,
       60, 63, 24, 29, 22, 44, 12, 41, 25, 55, 19, 48, 62, 27, 65]), 'cur_cost': 112065.0}, {'tour': array([ 4,  2, 28, 34, 63, 52, 39, 27, 26, 19, 41, 56, 37, 30, 36, 46, 24,
       23, 29, 60,  0,  6, 11, 54,  9,  3, 62, 50, 47, 16, 58,  5, 55, 65,
       13, 40, 53, 51, 31, 43, 44,  8, 32, 25,  7, 42, 38,  1, 21, 15, 57,
       17, 14, 18, 59, 33, 35, 20, 12, 64, 48, 45, 61, 22, 49, 10]), 'cur_cost': 91131.0}]
2025-06-26 11:01:55,902 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 11:01:55,902 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 13, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 13, 'cache_hits': 0, 'similarity_calculations': 78, 'cache_hit_rate': 0.0, 'cache_size': 78}}
2025-06-26 11:01:55,903 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 11:01:55,904 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 11:01:55,904 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 11:01:55,904 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 11:01:55,904 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 101510.0
2025-06-26 11:01:56,407 - ExploitationExpert - INFO - res_population_num: 24
2025-06-26 11:01:56,407 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521, 9521, 9521, 9521]
2025-06-26 11:01:56,407 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-06-26 11:01:56,419 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 11:01:56,419 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([29, 41, 19, 49, 22, 15,  8, 26,  1, 17, 31, 32, 10, 43, 13, 55, 35,
       18, 37,  6, 46, 54, 50, 52, 25, 47,  2, 58, 63,  0, 65, 44, 30, 42,
       57, 59, 16, 48, 56, 27,  4, 40, 62, 12, 24,  9, 51, 61, 38, 14, 39,
       21,  3, 23, 64, 34, 11,  7, 33, 53,  5, 45, 60, 28, 36, 20]), 'cur_cost': 120618.0}, {'tour': array([57, 38, 25, 64, 53, 24, 43, 26, 10, 44, 54, 48, 46, 28, 36, 31, 59,
       21, 29, 55,  6, 33, 61,  1,  3, 42,  5, 40, 12, 52, 30, 16, 15, 14,
       13, 19, 35, 39,  9, 20, 51, 34, 62, 17, 32, 41, 60,  8, 11, 63,  2,
       50, 49, 47, 37, 58, 22,  0, 18,  7,  4, 23, 56, 27, 45, 65]), 'cur_cost': 107956.0}, {'tour': array([ 6, 17, 34, 39, 26,  3, 38, 64,  0, 11, 56,  2, 31, 35, 10, 42,  4,
       20, 18, 59, 23, 16, 54, 52, 28,  5, 50, 21, 45, 40,  8,  9, 30,  7,
       47,  1, 36, 49, 61, 33, 32, 53, 15, 51, 13, 57, 46, 14, 43, 37, 58,
       60, 63, 24, 29, 22, 44, 12, 41, 25, 55, 19, 48, 62, 27, 65]), 'cur_cost': 112065.0}, {'tour': array([ 8,  1, 18,  6, 16, 23,  7, 61,  4, 13, 54, 15, 25, 47, 62, 31, 34,
       28, 55, 51, 22, 26, 24, 59, 45,  2, 33, 21, 53, 41,  9, 63, 12, 29,
       65, 11, 37, 60, 10,  3,  5, 49, 38, 46, 32, 50, 14, 27, 36, 64, 17,
       20, 40, 57, 48, 42,  0, 19, 43, 30, 35, 58, 39, 44, 56, 52]), 'cur_cost': 101510.0}]
2025-06-26 11:01:56,422 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 11:01:56,422 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 14, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 14, 'cache_hits': 0, 'similarity_calculations': 91, 'cache_hit_rate': 0.0, 'cache_size': 91}}
2025-06-26 11:01:56,422 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 11:01:56,422 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65], 'cur_cost': 13945.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}]
2025-06-26 11:01:56,423 - __main__ - INFO - 进化阶段完成
2025-06-26 11:01:56,423 - __main__ - INFO - 开始评估阶段
2025-06-26 11:01:56,423 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 11:01:56,425 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 11:01:56,425 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 91131.0 → 13945.0 (Δ 77186.0)
- Mean cost: 101885.8 → 69564.1
- Diversity: 0.566 → 0.557 (Δ -0.0092)

#### Elite metrics
- Elite count: 20 → 24
- Best elite cost: 9521 → 9521 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 100.0% , avg Δ 58065.0
- Exploit: 4 inds, success 0.0% , avg Δ -6293.25

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 3 iterations: costs [17289.0, 91924.0, 91131.0], diversity [0.8969696969696969, 0.7676767676767677, 0.7622895622895624]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 11:01:56,426 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:02:08,438 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "100% success rate with significant average cost reduction (Δ 58065.0)"
    },
    "exploit": {
      "contribution": "low",
      "reason": "0% success rate with negative average cost change (Δ -6293.25)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore, 40% exploit",
    "assessment": "explore is effective but exploit is failing; imbalance exists"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "additional_notes": "Reduce exploit attempts until local search shows improvement potential"
  }
}
```
2025-06-26 11:02:08,467 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 11:02:08,467 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "100% success rate with significant average cost reduction (Δ 58065.0)"
    },
    "exploit": {
      "contribution": "low",
      "reason": "0% success rate with negative average cost change (Δ -6293.25)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore, 40% exploit",
    "assessment": "explore is effective but exploit is failing; imbalance exists"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "additional_notes": "Reduce exploit attempts until local search shows improvement potential"
  }
}
```
2025-06-26 11:02:08,467 - __main__ - INFO - 评估阶段完成
2025-06-26 11:02:08,467 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribution": "high",
      "reason": "100% success rate with significant average cost reduction (Δ 58065.0)"
    },
    "exploit": {
      "contribution": "low",
      "reason": "0% success rate with negative average cost change (Δ -6293.25)"
    }
  },
  "balance_state": {
    "current_ratio": "60% explore, 40% exploit",
    "assessment": "explore is effective but exploit is failing; imbalance exists"
  },
  "recommendations": {
    "explore_ratio": 0.7,
    "diversity_weight": 0.75,
    "additional_notes": "Reduce exploit attempts until local search shows improvement potential"
  }
}
```
2025-06-26 11:02:08,467 - __main__ - INFO - 当前最佳适应度: 13945.0
2025-06-26 11:02:08,467 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_3.pkl
2025-06-26 11:02:08,467 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-06-26 11:02:08,467 - __main__ - INFO - 开始分析阶段
2025-06-26 11:02:08,471 - StatsExpert - INFO - 开始统计分析
2025-06-26 11:02:08,487 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 13945.0, 'max': 120618.0, 'mean': 69564.1, 'std': 45822.78218190161}, 'diversity': 0.8060606060606059, 'clusters': {'clusters': 6, 'cluster_sizes': [4, 2, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-26 11:02:08,488 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 13945.0, 'max': 120618.0, 'mean': 69564.1, 'std': 45822.78218190161}, 'diversity_level': 0.8060606060606059, 'convergence_level': 0.0, 'clustering_info': {'clusters': 6, 'cluster_sizes': [4, 2, 1, 1, 1, 1]}, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-06-26 11:02:08,488 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-26 11:02:08,489 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-26 11:02:08,489 - PathExpert - INFO - 开始路径结构分析
2025-06-26 11:02:08,491 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-26 11:02:08,491 - PathExpert - INFO - 路径结构分析完成
2025-06-26 11:02:08,491 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-26 11:02:08,491 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-26 11:02:08,491 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-26 11:02:08,491 - EliteExpert - INFO - 开始精英解分析
2025-06-26 11:02:08,501 - EliteExpert - INFO - 精英解分析完成
2025-06-26 11:02:08,502 - __main__ - INFO - 精英专家分析报告: {'elite_count': 24, 'elite_common_features': {'common_edges': {'(24, 31)': 0.7083333333333334}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 4424.0, 'avg_gap': 55890.80833333334}, 'structure_gap': {'unique_elite_edges': 238, 'unique_pop_edges': 342, 'common_edges': 36}}, 'elite_diversity': {'diversity_score': 0.2824440052700923}}
2025-06-26 11:02:08,502 - __main__ - INFO - 更新共享数据: elite_features
2025-06-26 11:02:08,502 - LandscapeExpert - INFO - 开始景观分析
2025-06-26 11:02:08,504 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-26 11:02:08,504 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 0

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 13945.0, mean 69564.1, max 120618.0, std 45822.78218190161
- diversity: 0.8060606060606059
- convergence: 0.0
- clustering: No clustering information
- recent_best_costs: Recent costs: [91924.0, 91131.0, 13945.0] (Δ 38989.50 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: []
- common_subpaths_sample: []
- difficult_regions_sample: []
- opportunity_regions_sample: []

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

2025-06-26 11:02:08,505 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:02:30,948 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Moderate to high (wide range in edge lengths from 10.0 to 3032.0, std dev ~45822.78)",
    "modality": "Potentially multimodal (high diversity 0.81, no clustering detected)",
    "deceptiveness": "Possible deceptive regions (long_edge_ratio 0.249 suggests some challenging long connections)"
  },
  "population_state": {
    "coverage": "Good spatial coverage (density_grid shows nodes spread across all quadrants except one empty cell)",
    "convergence": "Early stage (convergence 0.0, recent best costs show large Δ 38989.5 per iteration)",
    "diversity": "High (0.806)"
  },
  "difficult_regions": [
    "Long-edge corridors (edges in q3-max range: 2469.0-3032.0)",
    "Low-density cell at grid[0][1] (0 nodes)"
  ],
  "opportunity_regions": [
    "High-density cells: grid[0][0] (14 nodes), grid[2][0] (14 nodes), grid[2][2] (14 nodes)",
    "Centroid region (~1795,1565) with median edge length 1900.0"
  ],
  "evolution_phase": "Exploration-dominant",
  "evolution_direction": [
    "Operator suggestions:",
    "1. Edge-exchange mutations targeting long edges (>2469.0)",
    "2. Density-guided crossover (prioritize high-density cell connections)",
    "3. Directional bias (sectors 3 & 6 have highest edge counts)",
    "4. NN-chain heuristics (median NN dist 17.455 suggests local clusters)"
  ]
}
```
2025-06-26 11:02:30,950 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-26 11:02:30,950 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 10.0 to 3032.0, std dev ~45822.78)', 'modality': 'Potentially multimodal (high diversity 0.81, no clustering detected)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio 0.249 suggests some challenging long connections)'}, 'population_state': {'coverage': 'Good spatial coverage (density_grid shows nodes spread across all quadrants except one empty cell)', 'convergence': 'Early stage (convergence 0.0, recent best costs show large Δ 38989.5 per iteration)', 'diversity': 'High (0.806)'}, 'difficult_regions': ['Long-edge corridors (edges in q3-max range: 2469.0-3032.0)', 'Low-density cell at grid[0][1] (0 nodes)'], 'opportunity_regions': ['High-density cells: grid[0][0] (14 nodes), grid[2][0] (14 nodes), grid[2][2] (14 nodes)', 'Centroid region (~1795,1565) with median edge length 1900.0'], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Operator suggestions:', '1. Edge-exchange mutations targeting long edges (>2469.0)', '2. Density-guided crossover (prioritize high-density cell connections)', '3. Directional bias (sectors 3 & 6 have highest edge counts)', '4. NN-chain heuristics (median NN dist 17.455 suggests local clusters)']}
2025-06-26 11:02:30,950 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-26 11:02:30,950 - __main__ - INFO - 分析阶段完成
2025-06-26 11:02:30,950 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Moderate to high (wide range in edge lengths from 10.0 to 3032.0, std dev ~45822.78)', 'modality': 'Potentially multimodal (high diversity 0.81, no clustering detected)', 'deceptiveness': 'Possible deceptive regions (long_edge_ratio 0.249 suggests some challenging long connections)'}, 'population_state': {'coverage': 'Good spatial coverage (density_grid shows nodes spread across all quadrants except one empty cell)', 'convergence': 'Early stage (convergence 0.0, recent best costs show large Δ 38989.5 per iteration)', 'diversity': 'High (0.806)'}, 'difficult_regions': ['Long-edge corridors (edges in q3-max range: 2469.0-3032.0)', 'Low-density cell at grid[0][1] (0 nodes)'], 'opportunity_regions': ['High-density cells: grid[0][0] (14 nodes), grid[2][0] (14 nodes), grid[2][2] (14 nodes)', 'Centroid region (~1795,1565) with median edge length 1900.0'], 'evolution_phase': 'Exploration-dominant', 'evolution_direction': ['Operator suggestions:', '1. Edge-exchange mutations targeting long edges (>2469.0)', '2. Density-guided crossover (prioritize high-density cell connections)', '3. Directional bias (sectors 3 & 6 have highest edge counts)', '4. NN-chain heuristics (median NN dist 17.455 suggests local clusters)']}
2025-06-26 11:02:30,952 - __main__ - INFO - 开始策略分配阶段
2025-06-26 11:02:30,952 - StrategyExpert - INFO - 开始策略分配分析
2025-06-26 11:02:30,952 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0
  • best_cost: 13945.0
  • mean_cost: 69564.1
- Search-space: ruggedness Moderate to high (wide range in edge lengths from 10.0 to 3032.0, std dev ~45822.78), modality Potentially multimodal (high diversity 0.81, no clustering detected), deceptiveness Possible deceptive regions (long_edge_ratio 0.249 suggests some challenging long connections)
- Evolution phase: Exploration-dominant
- Landscape focus suggestion: balance
- Spatial density summary: N/A
- Difficult regions (# 2): Long-edge corridors , Low-density cell at 
- Opportunity regions (# 2): High-density cells: , Centroid region (~17
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid-stage optimization",
  "strategy_effectiveness": {
    "explore": {
      "contribut...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

2025-06-26 11:02:30,952 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-26 11:02:30,952 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:02:43,819 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 11:02:43,823 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 11:02:43,823 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 11:02:43,823 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 11:02:43,823 - __main__ - INFO - 策略分配阶段完成
2025-06-26 11:02:43,823 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-26 11:02:43,823 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "Low diversity and exploration-dominant phase suggest prioritizing exploration. Top 60% explore to diversify, bottom 40% exploit to refine."
}
```
2025-06-26 11:02:43,824 - __main__ - INFO - 开始进化阶段
2025-06-26 11:02:43,824 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-26 11:02:43,824 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:02:43,825 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13945.0 (rank 0.0%)
- Population diversity: 0.556923076923077
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid region
- Difficult regions to avoid (sample): Long-edge corri, Low-density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:02:43,825 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:02:43,825 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:02:58,075 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "High-density ce, Centroid region, adjacent low-density",
  "strategy_comment": "Layered spiral through opportunity regions, avoiding long edges"
}
```
2025-06-26 11:02:58,075 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:02:58,078 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 11:02:58,078 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 11:02:58,079 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-26 11:02:58,079 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:02:58,079 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 30.0%)
- Population diversity: 0.556923076923077
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid region
- Difficult regions to avoid (sample): Long-edge corri, Low-density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:02:58,080 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:02:58,080 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:03:12,749 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "High-density ce, Centroid region",
  "strategy_comment": "Leverage high-density regions with structured jumps to balance exploration"
}
```
2025-06-26 11:03:12,749 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:03:12,750 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-06-26 11:03:12,750 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-06-26 11:03:12,750 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-26 11:03:12,751 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:03:12,751 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13945.0 (rank 0.0%)
- Population diversity: 0.58
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid region
- Difficult regions to avoid (sample): Long-edge corri, Low-density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:03:12,751 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:03:12,752 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:03:29,852 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "High-density centroid and adjacent low-density cells",
  "strategy_comment": "Spiral pattern from center outward, skipping long edges"
}
```
2025-06-26 11:03:29,852 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:03:29,852 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-06-26 11:03:29,852 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-06-26 11:03:29,852 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-26 11:03:29,852 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:03:29,860 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13945.0 (rank 0.0%)
- Population diversity: 0.58
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid region
- Difficult regions to avoid (sample): Long-edge corri, Low-density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:03:29,860 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:03:29,860 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:03:44,959 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 66, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1],
  "targeted_regions": "High-density ce, Centroid region, adjacent low-density cells",
  "strategy_comment": "Leverage centroid clustering with radial jumps to sparse cells"
}
```
2025-06-26 11:03:44,960 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:03:44,961 - ExplorationExpert - INFO - 探索路径生成完成，成本: 94921.0, 路径: [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 20, 10, 21, 32, 43, 54, 65, 9, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1]
2025-06-26 11:03:44,961 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 20, 10, 21, 32, 43, 54, 65, 9, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 94921.0}
2025-06-26 11:03:44,961 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-26 11:03:44,961 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:03:44,961 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13945.0 (rank 0.0%)
- Population diversity: 0.6676923076923077
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid region
- Difficult regions to avoid (sample): Long-edge corri, Low-density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:03:44,961 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:03:44,961 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:04:00,090 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 66, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1],
  "targeted_regions": "High-density ce, Centroid region",
  "strategy_comment": "Leverage high-density regions while exploring adjacent sparse cells"
}
```
2025-06-26 11:04:00,091 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:04:00,092 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93259.0, 路径: [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 65, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1]
2025-06-26 11:04:00,092 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 65, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93259.0}
2025-06-26 11:04:00,092 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-26 11:04:00,092 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-26 11:04:00,092 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 20.0%)
- Population diversity: 0.5938461538461538
- Evolution phase: Exploration-dominant
- Landscape recommends focus: balance
- Opportunity regions (sample): High-density ce, Centroid region
- Difficult regions to avoid (sample): Long-edge corri, Low-density cel
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

2025-06-26 11:04:00,092 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-26 11:04:00,092 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:04:15,578 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 66, 10, 21, 32, 43, 54, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1],
  "targeted_regions": "High-density ce, Centroid region, adjacent low-density cells",
  "strategy_comment": "Balanced exploration of high-density and adjacent low-density regions"
}
```
2025-06-26 11:04:15,578 - ExplorationExpert - INFO - 计算路径成本
2025-06-26 11:04:15,583 - ExplorationExpert - INFO - 探索路径生成完成，成本: 93307.0, 路径: [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 54, 10, 21, 32, 43, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1]
2025-06-26 11:04:15,583 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 54, 10, 21, 32, 43, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93307.0}
2025-06-26 11:04:15,584 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-26 11:04:15,584 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 11:04:15,584 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 11:04:15,584 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 118005.0
2025-06-26 11:04:16,086 - ExploitationExpert - INFO - res_population_num: 27
2025-06-26 11:04:16,087 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521, 9521, 9521]
2025-06-26 11:04:16,087 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-26 11:04:16,106 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 11:04:16,108 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 20, 10, 21, 32, 43, 54, 65, 9, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 94921.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 65, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93259.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 54, 10, 21, 32, 43, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93307.0}, {'tour': array([65, 26, 19, 30, 31, 64, 62, 48, 28, 53,  7, 60, 38, 36, 52, 27,  3,
        2, 40,  5, 16, 56, 51, 61, 22, 39, 13,  8, 46, 50, 10, 33, 23, 15,
        6, 42, 34, 25, 18, 14, 11, 29, 47, 37, 20, 57, 45, 49, 55, 24, 54,
       12, 41, 21, 63, 44, 35, 58, 43,  9,  1, 59, 32,  0, 17,  4]), 'cur_cost': 118005.0}, {'tour': array([57, 38, 25, 64, 53, 24, 43, 26, 10, 44, 54, 48, 46, 28, 36, 31, 59,
       21, 29, 55,  6, 33, 61,  1,  3, 42,  5, 40, 12, 52, 30, 16, 15, 14,
       13, 19, 35, 39,  9, 20, 51, 34, 62, 17, 32, 41, 60,  8, 11, 63,  2,
       50, 49, 47, 37, 58, 22,  0, 18,  7,  4, 23, 56, 27, 45, 65]), 'cur_cost': 107956.0}, {'tour': array([ 6, 17, 34, 39, 26,  3, 38, 64,  0, 11, 56,  2, 31, 35, 10, 42,  4,
       20, 18, 59, 23, 16, 54, 52, 28,  5, 50, 21, 45, 40,  8,  9, 30,  7,
       47,  1, 36, 49, 61, 33, 32, 53, 15, 51, 13, 57, 46, 14, 43, 37, 58,
       60, 63, 24, 29, 22, 44, 12, 41, 25, 55, 19, 48, 62, 27, 65]), 'cur_cost': 112065.0}, {'tour': array([ 8,  1, 18,  6, 16, 23,  7, 61,  4, 13, 54, 15, 25, 47, 62, 31, 34,
       28, 55, 51, 22, 26, 24, 59, 45,  2, 33, 21, 53, 41,  9, 63, 12, 29,
       65, 11, 37, 60, 10,  3,  5, 49, 38, 46, 32, 50, 14, 27, 36, 64, 17,
       20, 40, 57, 48, 42,  0, 19, 43, 30, 35, 58, 39, 44, 56, 52]), 'cur_cost': 101510.0}]
2025-06-26 11:04:16,111 - ExploitationExpert - INFO - 局部搜索耗时: 0.53秒
2025-06-26 11:04:16,111 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 15, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 15, 'cache_hits': 0, 'similarity_calculations': 105, 'cache_hit_rate': 0.0, 'cache_size': 105}}
2025-06-26 11:04:16,111 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-26 11:04:16,111 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-26 11:04:16,111 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 11:04:16,111 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 11:04:16,114 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 99943.0
2025-06-26 11:04:16,618 - ExploitationExpert - INFO - res_population_num: 27
2025-06-26 11:04:16,619 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521, 9521, 9521]
2025-06-26 11:04:16,619 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-26 11:04:16,632 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 11:04:16,632 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 20, 10, 21, 32, 43, 54, 65, 9, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 94921.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 65, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93259.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 54, 10, 21, 32, 43, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93307.0}, {'tour': array([65, 26, 19, 30, 31, 64, 62, 48, 28, 53,  7, 60, 38, 36, 52, 27,  3,
        2, 40,  5, 16, 56, 51, 61, 22, 39, 13,  8, 46, 50, 10, 33, 23, 15,
        6, 42, 34, 25, 18, 14, 11, 29, 47, 37, 20, 57, 45, 49, 55, 24, 54,
       12, 41, 21, 63, 44, 35, 58, 43,  9,  1, 59, 32,  0, 17,  4]), 'cur_cost': 118005.0}, {'tour': array([38, 24, 49, 12, 41, 17, 42, 34, 14, 36, 33, 47, 59, 39, 54, 45,  1,
       19,  0, 65, 53,  6, 11, 21, 63, 52,  9, 29,  8, 25, 60, 55, 64, 43,
       26,  7, 58, 23, 46, 48, 10, 37,  5, 51, 61, 62, 15, 20, 18, 57,  3,
       32, 27, 31, 44, 30, 28, 13, 35, 22, 40, 56,  2, 50, 16,  4]), 'cur_cost': 99943.0}, {'tour': array([ 6, 17, 34, 39, 26,  3, 38, 64,  0, 11, 56,  2, 31, 35, 10, 42,  4,
       20, 18, 59, 23, 16, 54, 52, 28,  5, 50, 21, 45, 40,  8,  9, 30,  7,
       47,  1, 36, 49, 61, 33, 32, 53, 15, 51, 13, 57, 46, 14, 43, 37, 58,
       60, 63, 24, 29, 22, 44, 12, 41, 25, 55, 19, 48, 62, 27, 65]), 'cur_cost': 112065.0}, {'tour': array([ 8,  1, 18,  6, 16, 23,  7, 61,  4, 13, 54, 15, 25, 47, 62, 31, 34,
       28, 55, 51, 22, 26, 24, 59, 45,  2, 33, 21, 53, 41,  9, 63, 12, 29,
       65, 11, 37, 60, 10,  3,  5, 49, 38, 46, 32, 50, 14, 27, 36, 64, 17,
       20, 40, 57, 48, 42,  0, 19, 43, 30, 35, 58, 39, 44, 56, 52]), 'cur_cost': 101510.0}]
2025-06-26 11:04:16,635 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 11:04:16,635 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 16, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 16, 'cache_hits': 0, 'similarity_calculations': 120, 'cache_hit_rate': 0.0, 'cache_size': 120}}
2025-06-26 11:04:16,635 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-26 11:04:16,635 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-26 11:04:16,635 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 11:04:16,635 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 11:04:16,637 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 106829.0
2025-06-26 11:04:17,141 - ExploitationExpert - INFO - res_population_num: 27
2025-06-26 11:04:17,141 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521, 9521, 9521]
2025-06-26 11:04:17,142 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-26 11:04:17,154 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 11:04:17,154 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 20, 10, 21, 32, 43, 54, 65, 9, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 94921.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 65, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93259.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 54, 10, 21, 32, 43, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93307.0}, {'tour': array([65, 26, 19, 30, 31, 64, 62, 48, 28, 53,  7, 60, 38, 36, 52, 27,  3,
        2, 40,  5, 16, 56, 51, 61, 22, 39, 13,  8, 46, 50, 10, 33, 23, 15,
        6, 42, 34, 25, 18, 14, 11, 29, 47, 37, 20, 57, 45, 49, 55, 24, 54,
       12, 41, 21, 63, 44, 35, 58, 43,  9,  1, 59, 32,  0, 17,  4]), 'cur_cost': 118005.0}, {'tour': array([38, 24, 49, 12, 41, 17, 42, 34, 14, 36, 33, 47, 59, 39, 54, 45,  1,
       19,  0, 65, 53,  6, 11, 21, 63, 52,  9, 29,  8, 25, 60, 55, 64, 43,
       26,  7, 58, 23, 46, 48, 10, 37,  5, 51, 61, 62, 15, 20, 18, 57,  3,
       32, 27, 31, 44, 30, 28, 13, 35, 22, 40, 56,  2, 50, 16,  4]), 'cur_cost': 99943.0}, {'tour': array([18,  9, 10, 26, 41, 58, 45, 15, 62, 43, 32, 29, 20,  3, 22,  4, 65,
       21, 23, 14, 44, 39, 54, 36, 49, 30, 13,  2,  6, 27, 63, 64, 50, 25,
       38, 19, 59, 35, 46, 51, 57, 37, 31, 52, 47,  1,  8,  0, 28, 17, 16,
        5, 56, 34, 48, 12, 42, 55, 33, 24, 60, 40, 11, 53, 61,  7]), 'cur_cost': 106829.0}, {'tour': array([ 8,  1, 18,  6, 16, 23,  7, 61,  4, 13, 54, 15, 25, 47, 62, 31, 34,
       28, 55, 51, 22, 26, 24, 59, 45,  2, 33, 21, 53, 41,  9, 63, 12, 29,
       65, 11, 37, 60, 10,  3,  5, 49, 38, 46, 32, 50, 14, 27, 36, 64, 17,
       20, 40, 57, 48, 42,  0, 19, 43, 30, 35, 58, 39, 44, 56, 52]), 'cur_cost': 101510.0}]
2025-06-26 11:04:17,157 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 11:04:17,157 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 17, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 17, 'cache_hits': 0, 'similarity_calculations': 136, 'cache_hit_rate': 0.0, 'cache_size': 136}}
2025-06-26 11:04:17,157 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-26 11:04:17,157 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-26 11:04:17,157 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-26 11:04:17,157 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-26 11:04:17,159 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110894.0
2025-06-26 11:04:17,662 - ExploitationExpert - INFO - res_population_num: 27
2025-06-26 11:04:17,662 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9522, 9526, 9527, 9533, 9552, 9572, 109070, 9521, 9521, 9521]
2025-06-26 11:04:17,662 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 41, 51, 38, 45, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 12, 17, 15, 14, 22, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 43, 40, 49, 47,
       46, 48, 42, 50, 51, 38, 41, 45, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 23, 18, 17, 12, 22, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 49, 40, 43, 48, 46, 47, 39, 44, 45, 41, 38,
       51, 50, 42, 34, 35, 30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 34, 30, 28, 35, 26, 25, 33, 32, 29, 24, 31, 37, 36, 27,
       19, 13, 21, 20, 14, 15, 22, 23, 16, 18, 12, 17,  3,  7,  1],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 43,
       40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 36, 26, 35, 34,
       30, 28, 32, 29, 24, 31, 33, 25, 37, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0, 22, 61, 41, 39, 35,  3, 36,  8, 53, 59, 45, 10, 27, 46,  2, 24,
       64, 50, 29, 60, 44, 52, 57, 43, 30, 34, 25, 62,  4, 40, 42, 37, 11,
       18, 58, 32, 28, 33, 19,  6, 54, 38, 16, 26,  9, 49, 51,  5, 48,  7,
       13, 55,  1, 47, 31, 65, 12, 14, 15, 17, 56, 21, 23, 20, 63],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-06-26 11:04:17,676 - ExploitationExpert - INFO - populations_num: 10
2025-06-26 11:04:17,676 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 20, 10, 21, 32, 43, 54, 65, 9, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 94921.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 65, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93259.0}, {'tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 54, 10, 21, 32, 43, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93307.0}, {'tour': array([65, 26, 19, 30, 31, 64, 62, 48, 28, 53,  7, 60, 38, 36, 52, 27,  3,
        2, 40,  5, 16, 56, 51, 61, 22, 39, 13,  8, 46, 50, 10, 33, 23, 15,
        6, 42, 34, 25, 18, 14, 11, 29, 47, 37, 20, 57, 45, 49, 55, 24, 54,
       12, 41, 21, 63, 44, 35, 58, 43,  9,  1, 59, 32,  0, 17,  4]), 'cur_cost': 118005.0}, {'tour': array([38, 24, 49, 12, 41, 17, 42, 34, 14, 36, 33, 47, 59, 39, 54, 45,  1,
       19,  0, 65, 53,  6, 11, 21, 63, 52,  9, 29,  8, 25, 60, 55, 64, 43,
       26,  7, 58, 23, 46, 48, 10, 37,  5, 51, 61, 62, 15, 20, 18, 57,  3,
       32, 27, 31, 44, 30, 28, 13, 35, 22, 40, 56,  2, 50, 16,  4]), 'cur_cost': 99943.0}, {'tour': array([18,  9, 10, 26, 41, 58, 45, 15, 62, 43, 32, 29, 20,  3, 22,  4, 65,
       21, 23, 14, 44, 39, 54, 36, 49, 30, 13,  2,  6, 27, 63, 64, 50, 25,
       38, 19, 59, 35, 46, 51, 57, 37, 31, 52, 47,  1,  8,  0, 28, 17, 16,
        5, 56, 34, 48, 12, 42, 55, 33, 24, 60, 40, 11, 53, 61,  7]), 'cur_cost': 106829.0}, {'tour': array([ 5, 23,  6, 21, 42,  0, 37,  9, 64, 12, 14, 18,  3, 51,  8, 31, 61,
       25, 43, 17, 44, 26, 48, 36, 54, 46, 65, 63, 38, 33, 35, 20, 50, 56,
       11, 62, 52, 53, 34, 47,  7, 55, 49, 58, 16, 40, 60, 32,  2, 10, 15,
       39, 27,  4, 22, 45, 59, 57,  1, 29, 28, 41, 24, 19, 13, 30]), 'cur_cost': 110894.0}]
2025-06-26 11:04:17,679 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-26 11:04:17,679 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 18, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 18, 'cache_hits': 0, 'similarity_calculations': 153, 'cache_hit_rate': 0.0, 'cache_size': 153}}
2025-06-26 11:04:17,680 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-26 11:04:17,680 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 20, 10, 21, 32, 43, 54, 65, 9, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 94921.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 65, 10, 21, 32, 43, 54, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93259.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 11, 22, 33, 44, 55, 54, 10, 21, 32, 43, 65, 9, 20, 31, 42, 53, 64, 8, 19, 30, 41, 52, 63, 7, 18, 29, 40, 51, 62, 6, 17, 28, 39, 50, 61, 5, 16, 27, 38, 49, 60, 4, 15, 26, 37, 48, 59, 3, 14, 25, 36, 47, 58, 2, 13, 24, 35, 46, 57, 1], 'cur_cost': 93307.0}}]
2025-06-26 11:04:17,680 - __main__ - INFO - 进化阶段完成
2025-06-26 11:04:17,680 - __main__ - INFO - 开始评估阶段
2025-06-26 11:04:17,680 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-26 11:04:17,681 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-26 11:04:17,681 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 13945.0 → 93259.0 (Δ -79314.0)
- Mean cost: 69564.1 → 101627.5
- Diversity: 0.557 → 0.588 (Δ 0.0308)

#### Elite metrics
- Elite count: 24 → 27
- Best elite cost: 9521 → 9521 (Δ 0)

#### Strategy performance
- Explore: 6 inds, success 16.7% , avg Δ -54518.67
- Exploit: 4 inds, success 75.0% , avg Δ 1619.5

#### Other indicators
- No-change individuals: 0
- Regression individuals: 6
- Historical trends: Last 3 iterations: costs [91924.0, 91131.0, 13945.0], diversity [0.7676767676767677, 0.7622895622895624, 0.8060606060606059]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

2025-06-26 11:04:17,682 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-06-26 11:04:29,538 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low success rate (16.7%) but high impact (-54518.67 avg Δ)",
    "exploit": "high success rate (75.0%) but low impact (1619.5 avg Δ)"
  },
  "balance_state": "explore strategy is underperforming in success rate but critical for breakthroughs, exploit is stable but incremental",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "consider increasing by 10-15% to escape local optima",
    "elite_preservation": "maintain current level (27 elites)"
  }
}
```
2025-06-26 11:04:29,569 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-26 11:04:29,570 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low success rate (16.7%) but high impact (-54518.67 avg Δ)",
    "exploit": "high success rate (75.0%) but low impact (1619.5 avg Δ)"
  },
  "balance_state": "explore strategy is underperforming in success rate but critical for breakthroughs, exploit is stable but incremental",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "consider increasing by 10-15% to escape local optima",
    "elite_preservation": "maintain current level (27 elites)"
  }
}
```
2025-06-26 11:04:29,570 - __main__ - INFO - 评估阶段完成
2025-06-26 11:04:29,570 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid-stage with signs of stagnation",
  "strategy_effectiveness": {
    "explore": "low success rate (16.7%) but high impact (-54518.67 avg Δ)",
    "exploit": "high success rate (75.0%) but low impact (1619.5 avg Δ)"
  },
  "balance_state": "explore strategy is underperforming in success rate but critical for breakthroughs, exploit is stable but incremental",
  "recommendations": {
    "explore_ratio": 0.6,
    "diversity_weight": 0.8,
    "mutation_rate": "consider increasing by 10-15% to escape local optima",
    "elite_preservation": "maintain current level (27 elites)"
  }
}
```
2025-06-26 11:04:29,570 - __main__ - INFO - 当前最佳适应度: 93259.0
2025-06-26 11:04:29,572 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_4.pkl
2025-06-26 11:04:29,585 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_solution.json
2025-06-26 11:04:29,585 - __main__ - INFO - 实例 composite13_66 处理完成
