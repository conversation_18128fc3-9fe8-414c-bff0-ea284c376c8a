2025-06-22 17:14:30,992 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-06-22 17:14:30,992 - __main__ - INFO - 开始分析阶段
2025-06-22 17:14:30,992 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:14:31,011 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9946.0, 'max': 119210.0, 'mean': 77497.3, 'std': 44719.708290752525}, 'diversity': 0.9222222222222223, 'clusters': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:14:31,011 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9946.0, 'max': 119210.0, 'mean': 77497.3, 'std': 44719.708290752525}, 'diversity_level': 0.9222222222222223, 'convergence_level': 0.0, 'clustering_info': {'clusters': 8, 'cluster_sizes': [3, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:14:31,022 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:14:31,022 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:14:31,022 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:14:31,028 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:14:31,028 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}, {'subpath': (53, 62, 59), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}, {'subpath': (59, 56, 58), 'frequency': 0.3}, {'subpath': (56, 58, 60), 'frequency': 0.3}, {'subpath': (58, 60, 64), 'frequency': 0.3}, {'subpath': (60, 64, 57), 'frequency': 0.3}, {'subpath': (64, 57, 54), 'frequency': 0.3}, {'subpath': (57, 54, 65), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(2, 6)', 'frequency': 0.4}, {'edge': '(9, 11)', 'frequency': 0.4}, {'edge': '(3, 7)', 'frequency': 0.4}, {'edge': '(0, 10)', 'frequency': 0.4}, {'edge': '(53, 61)', 'frequency': 0.4}, {'edge': '(58, 60)', 'frequency': 0.4}, {'edge': '(25, 37)', 'frequency': 0.4}, {'edge': '(29, 32)', 'frequency': 0.4}, {'edge': '(38, 51)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(5, 8)', 'frequency': 0.2}, {'edge': '(4, 5)', 'frequency': 0.3}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(0, 1)', 'frequency': 0.2}, {'edge': '(55, 61)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(56, 59)', 'frequency': 0.3}, {'edge': '(56, 58)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(52, 63)', 'frequency': 0.3}, {'edge': '(14, 15)', 'frequency': 0.3}, {'edge': '(15, 22)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(17, 18)', 'frequency': 0.2}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.2}, {'edge': '(13, 23)', 'frequency': 0.3}, {'edge': '(13, 20)', 'frequency': 0.3}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(19, 27)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.3}, {'edge': '(25, 26)', 'frequency': 0.3}, {'edge': '(26, 36)', 'frequency': 0.3}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.3}, {'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(30, 34)', 'frequency': 0.3}, {'edge': '(33, 34)', 'frequency': 0.3}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(24, 31)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(40, 49)', 'frequency': 0.3}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.3}, {'edge': '(46, 48)', 'frequency': 0.2}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(39, 44)', 'frequency': 0.2}, {'edge': '(44, 45)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(50, 51)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.3}, {'edge': '(39, 41)', 'frequency': 0.2}, {'edge': '(39, 47)', 'frequency': 0.2}, {'edge': '(40, 43)', 'frequency': 0.2}, {'edge': '(12, 18)', 'frequency': 0.2}, {'edge': '(3, 32)', 'frequency': 0.2}, {'edge': '(1, 7)', 'frequency': 0.3}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(5, 9)', 'frequency': 0.2}, {'edge': '(4, 8)', 'frequency': 0.2}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(0, 55)', 'frequency': 0.2}, {'edge': '(42, 48)', 'frequency': 0.2}, {'edge': '(25, 56)', 'frequency': 0.2}, {'edge': '(5, 40)', 'frequency': 0.2}, {'edge': '(55, 65)', 'frequency': 0.2}, {'edge': '(44, 55)', 'frequency': 0.2}, {'edge': '(14, 36)', 'frequency': 0.2}, {'edge': '(3, 34)', 'frequency': 0.2}, {'edge': '(22, 37)', 'frequency': 0.2}, {'edge': '(9, 13)', 'frequency': 0.2}, {'edge': '(4, 52)', 'frequency': 0.2}, {'edge': '(8, 16)', 'frequency': 0.2}, {'edge': '(26, 27)', 'frequency': 0.2}, {'edge': '(23, 61)', 'frequency': 0.2}, {'edge': '(33, 51)', 'frequency': 0.2}, {'edge': '(33, 57)', 'frequency': 0.2}, {'edge': '(15, 58)', 'frequency': 0.2}, {'edge': '(11, 21)', 'frequency': 0.2}, {'edge': '(43, 65)', 'frequency': 0.2}, {'edge': '(1, 48)', 'frequency': 0.2}, {'edge': '(2, 45)', 'frequency': 0.2}, {'edge': '(63, 64)', 'frequency': 0.2}, {'edge': '(16, 36)', 'frequency': 0.2}, {'edge': '(22, 40)', 'frequency': 0.2}, {'edge': '(8, 33)', 'frequency': 0.2}, {'edge': '(34, 38)', 'frequency': 0.2}, {'edge': '(13, 62)', 'frequency': 0.2}, {'edge': '(26, 44)', 'frequency': 0.2}, {'edge': '(10, 30)', 'frequency': 0.2}, {'edge': '(5, 30)', 'frequency': 0.2}, {'edge': '(24, 54)', 'frequency': 0.2}, {'edge': '(24, 46)', 'frequency': 0.2}, {'edge': '(29, 47)', 'frequency': 0.2}, {'edge': '(41, 64)', 'frequency': 0.2}, {'edge': '(20, 50)', 'frequency': 0.2}, {'edge': '(20, 65)', 'frequency': 0.2}, {'edge': '(15, 23)', 'frequency': 0.2}, {'edge': '(12, 30)', 'frequency': 0.2}, {'edge': '(18, 39)', 'frequency': 0.2}, {'edge': '(38, 49)', 'frequency': 0.2}, {'edge': '(6, 21)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [32, 44, 64, 28, 53, 40, 4], 'cost': 16213.0, 'size': 7}, {'region': [30, 55, 45, 59, 50, 53], 'cost': 14217.0, 'size': 6}, {'region': [60, 48, 1, 45, 2], 'cost': 10349.0, 'size': 5}, {'region': [65, 47, 2, 38, 5], 'cost': 10338.0, 'size': 5}, {'region': [26, 44, 37, 63, 35], 'cost': 10154.0, 'size': 5}]}
2025-06-22 17:14:31,029 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:14:31,029 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:14:31,029 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:14:31,029 - EliteExpert - WARNING - 没有精英解可供分析
2025-06-22 17:14:31,029 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-06-22 17:14:31,029 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:14:31,029 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:14:31,030 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:14:31,030 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=9946.0, max=119210.0, mean=77497.3, std=44719.708290752525
- Diversity: 0.9222222222222223, Convergence: 0.0
- Structure: 0 high quality edges, 10 common subpaths
- Elite: 0 solutions with 0 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 5 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:14:37,069 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High. The wide cost range (min: 9946.0, max: 119210.0) with a high standard deviation (44719.7) suggests a rugged search space with potentially many local optima.",
    "modality": "Potentially High. The presence of 5 difficult regions indicates a multi-modal landscape, likely with multiple peaks and valleys.  The lack of elite solutions and convergence further supports this."
  },
  "population_state": {
    "diversity": "High. A diversity of 0.92 indicates the population is well-spread within the search space.",
    "convergence": "Extremely Low (0.0). No convergence has occurred, which is expected given the high diversity and lack of elite solutions.  This might indicate the search is still exploring the search space without finding high-quality solutions or becoming trapped in local optima."
  },
  "difficult_regions": {
    "challenges": "The 5 identified difficult regions suggest these areas pose significant challenges to optimization. The reasons could be:  1) Local optima traps. 2) Deceptive landscapes with misleading gradients. 3) Complex relationships between decision variables in those regions. 4) Insufficient exploration within those regions.  The absence of any high quality edges or fixed nodes further emphasizes the struggle to find good solutions.",
    "specific_considerations": "Careful exploration and potentially specialized mutation/crossover operators might be required to escape or traverse these difficult regions.  Algorithms that promote exploration rather than exploitation may be beneficial in the current state."
  },
  "opportunity_regions": {
    "potential": "The lack of identified opportunity regions might be due to the early exploration phase. Alternatively, the entire search space could be difficult, and no clear 'easy wins' have been identified yet.",
    "exploration_strategies": "With high diversity, a broad, explorative approach is appropriate. Identify and analyze any areas of the search space that haven't been explored. Further research into the 5 identified difficult regions is also warranted, as these may be adjacent to or part of an opportunity region."
  },
  "evolution_direction": {
    "strategy": "Prioritize Exploration and Refinement. Given the high diversity and lack of convergence, the focus should be on exploring the search space more thoroughly and refining the search algorithm for improved performance in difficult areas. ",
    "recommendations": [
      "Increase exploration: Consider increasing mutation rates, using a more diverse selection strategy, or introducing more random restarts.",
      "Analyze difficult regions:  Investigate the characteristics of the 5 difficult regions in greater detail.  Identify variable dependencies, potential local optima traps, and determine if they represent a significant portion of the overall search space.",
      "Adaptive Mutation/Crossover: Consider using adaptive mutation/crossover rates to favor larger jumps in the difficult regions and fine-tuning in areas where initial solutions have been found.",
      "Maintain Diversity: The high diversity is good. Prevent premature convergence and continue using a selection strategy that encourages diversity such as Tournament selection or Fitness proportionate selection.",
      "Monitor Progress: Track the distribution of costs, the number of visited subpaths, and changes in diversity over time to assess the effectiveness of the chosen exploration strategy.  Track the quality (or lack thereof) of solutions within difficult regions, which may help understand why the elite solution count is zero."
    ]
  }
}
```
2025-06-22 17:14:37,069 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:14:37,069 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High. The wide cost range (min: 9946.0, max: 119210.0) with a high standard deviation (44719.7) suggests a rugged search space with potentially many local optima.', 'modality': 'Potentially High. The presence of 5 difficult regions indicates a multi-modal landscape, likely with multiple peaks and valleys.  The lack of elite solutions and convergence further supports this.'}, 'population_state': {'diversity': 'High. A diversity of 0.92 indicates the population is well-spread within the search space.', 'convergence': 'Extremely Low (0.0). No convergence has occurred, which is expected given the high diversity and lack of elite solutions.  This might indicate the search is still exploring the search space without finding high-quality solutions or becoming trapped in local optima.'}, 'difficult_regions': {'challenges': 'The 5 identified difficult regions suggest these areas pose significant challenges to optimization. The reasons could be:  1) Local optima traps. 2) Deceptive landscapes with misleading gradients. 3) Complex relationships between decision variables in those regions. 4) Insufficient exploration within those regions.  The absence of any high quality edges or fixed nodes further emphasizes the struggle to find good solutions.', 'specific_considerations': 'Careful exploration and potentially specialized mutation/crossover operators might be required to escape or traverse these difficult regions.  Algorithms that promote exploration rather than exploitation may be beneficial in the current state.'}, 'opportunity_regions': {'potential': "The lack of identified opportunity regions might be due to the early exploration phase. Alternatively, the entire search space could be difficult, and no clear 'easy wins' have been identified yet.", 'exploration_strategies': "With high diversity, a broad, explorative approach is appropriate. Identify and analyze any areas of the search space that haven't been explored. Further research into the 5 identified difficult regions is also warranted, as these may be adjacent to or part of an opportunity region."}, 'evolution_direction': {'strategy': 'Prioritize Exploration and Refinement. Given the high diversity and lack of convergence, the focus should be on exploring the search space more thoroughly and refining the search algorithm for improved performance in difficult areas. ', 'recommendations': ['Increase exploration: Consider increasing mutation rates, using a more diverse selection strategy, or introducing more random restarts.', 'Analyze difficult regions:  Investigate the characteristics of the 5 difficult regions in greater detail.  Identify variable dependencies, potential local optima traps, and determine if they represent a significant portion of the overall search space.', 'Adaptive Mutation/Crossover: Consider using adaptive mutation/crossover rates to favor larger jumps in the difficult regions and fine-tuning in areas where initial solutions have been found.', 'Maintain Diversity: The high diversity is good. Prevent premature convergence and continue using a selection strategy that encourages diversity such as Tournament selection or Fitness proportionate selection.', 'Monitor Progress: Track the distribution of costs, the number of visited subpaths, and changes in diversity over time to assess the effectiveness of the chosen exploration strategy.  Track the quality (or lack thereof) of solutions within difficult regions, which may help understand why the elite solution count is zero.']}}
2025-06-22 17:14:37,072 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:14:37,072 - __main__ - INFO - 分析阶段完成
2025-06-22 17:14:37,072 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High. The wide cost range (min: 9946.0, max: 119210.0) with a high standard deviation (44719.7) suggests a rugged search space with potentially many local optima.', 'modality': 'Potentially High. The presence of 5 difficult regions indicates a multi-modal landscape, likely with multiple peaks and valleys.  The lack of elite solutions and convergence further supports this.'}, 'population_state': {'diversity': 'High. A diversity of 0.92 indicates the population is well-spread within the search space.', 'convergence': 'Extremely Low (0.0). No convergence has occurred, which is expected given the high diversity and lack of elite solutions.  This might indicate the search is still exploring the search space without finding high-quality solutions or becoming trapped in local optima.'}, 'difficult_regions': {'challenges': 'The 5 identified difficult regions suggest these areas pose significant challenges to optimization. The reasons could be:  1) Local optima traps. 2) Deceptive landscapes with misleading gradients. 3) Complex relationships between decision variables in those regions. 4) Insufficient exploration within those regions.  The absence of any high quality edges or fixed nodes further emphasizes the struggle to find good solutions.', 'specific_considerations': 'Careful exploration and potentially specialized mutation/crossover operators might be required to escape or traverse these difficult regions.  Algorithms that promote exploration rather than exploitation may be beneficial in the current state.'}, 'opportunity_regions': {'potential': "The lack of identified opportunity regions might be due to the early exploration phase. Alternatively, the entire search space could be difficult, and no clear 'easy wins' have been identified yet.", 'exploration_strategies': "With high diversity, a broad, explorative approach is appropriate. Identify and analyze any areas of the search space that haven't been explored. Further research into the 5 identified difficult regions is also warranted, as these may be adjacent to or part of an opportunity region."}, 'evolution_direction': {'strategy': 'Prioritize Exploration and Refinement. Given the high diversity and lack of convergence, the focus should be on exploring the search space more thoroughly and refining the search algorithm for improved performance in difficult areas. ', 'recommendations': ['Increase exploration: Consider increasing mutation rates, using a more diverse selection strategy, or introducing more random restarts.', 'Analyze difficult regions:  Investigate the characteristics of the 5 difficult regions in greater detail.  Identify variable dependencies, potential local optima traps, and determine if they represent a significant portion of the overall search space.', 'Adaptive Mutation/Crossover: Consider using adaptive mutation/crossover rates to favor larger jumps in the difficult regions and fine-tuning in areas where initial solutions have been found.', 'Maintain Diversity: The high diversity is good. Prevent premature convergence and continue using a selection strategy that encourages diversity such as Tournament selection or Fitness proportionate selection.', 'Monitor Progress: Track the distribution of costs, the number of visited subpaths, and changes in diversity over time to assess the effectiveness of the chosen exploration strategy.  Track the quality (or lack thereof) of solutions within difficult regions, which may help understand why the elite solution count is zero.']}}
2025-06-22 17:14:37,073 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:14:37,073 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:14:37,073 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 0
- Population: 10 individuals, diversity=High. A diversity of 0.92 indicates the population is well-spread within the search space.
- Best cost: 9946.0, Mean cost: 77497.3
- Search space: Ruggedness: High. The wide cost range (min: 9946.0, max: 119210.0) with a high standard deviation (44719.7) suggests a rugged search space with potentially many local optima., Modality: Potentially High. The presence of 5 difficult regions indicates a multi-modal landscape, likely with multiple peaks and valleys.  The lack of elite solutions and convergence further supports this.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: No previous feedback

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:14:37,075 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:14:39,416 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.4,
      "step_size_multiplier": 1.2
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size_multiplier": 0.8,
      "local_search_iterations": 5
    }
  }
}
```

2025-06-22 17:14:39,416 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 17:14:39,416 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.4,
      "step_size_multiplier": 1.2
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size_multiplier": 0.8,
      "local_search_iterations": 5
    }
  }
}
```

2025-06-22 17:14:39,416 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:14:39,416 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit', 'exploit', 'exploit']
2025-06-22 17:14:39,418 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.4,
      "step_size_multiplier": 1.2
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size_multiplier": 0.8,
      "local_search_iterations": 5
    }
  }
}
```

2025-06-22 17:14:39,419 - __main__ - INFO - 开始进化阶段
2025-06-22 17:14:39,419 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:14:39,419 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:14:39,419 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [6, 2, 8] and ending with [50, 41, 42]
- Cost: 10189.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.7476923076923077
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:14:39,419 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:14:43,243 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    6, 1, 3, 9, 15, 16, 18, 20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 33, 35, 37, 39, 40, 41, 43, 44, 45, 47, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 74, 76, 77, 79, 80, 81, 82, 84, 85, 87, 88, 90, 91, 92, 93, 95, 96, 97, 98
  ],
  "modification_strategy": "This strategy prioritizes diversity by utilizing a different starting point and exploring less traversed areas within the graph. I've re-routed the initial path to explore regions with higher potential gain, avoiding known difficult regions in the early stages. The path then progresses through a combination of well-connected and less-traveled nodes to enhance diversity and uncover promising new regions. I have also attempted to cover the opportunity regions as per search context.",
  "targeted_regions": "Opportunity regions and areas that are less frequently explored in the original path are the focus. Avoiding the difficult regions in the initial phase is part of risk management. The intention is to discover new segments that might provide alternate efficient routes or reveal useful context for future iterations."
}
```
2025-06-22 17:14:43,243 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:14:43,243 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1.1109497412598174e+272, 路径: [6, 1, 3, 9, 15, 16, 18, 20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 33, 35, 37, 39, 40, 41, 43, 44, 45, 47, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 74, 76, 77, 79, 80, 81, 82, 84, 85, 87, 88, 90, 91, 92, 93, 95, 96, 97, 98]
2025-06-22 17:14:43,243 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 1, 3, 9, 15, 16, 18, 20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 33, 35, 37, 39, 40, 41, 43, 44, 45, 47, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 74, 76, 77, 79, 80, 81, 82, 84, 85, 87, 88, 90, 91, 92, 93, 95, 96, 97, 98], 'cur_cost': 1.1109497412598174e+272}
2025-06-22 17:14:43,243 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 17:14:43,243 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:14:43,243 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [44, 45, 38] and ending with [65, 52, 63]
- Cost: 9946.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8110599078341014
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:14:43,243 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:14:46,457 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    44, 2, 3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 4, 5, 6, 7, 8, 9, 45, 66, 67, 68, 69, 70, 71, 72
  ],
  "modification_strategy": "Completely re-routed path to maximize exploration of the opportunity regions identified. This was achieved by prioritizing nodes within those regions, while avoiding difficult regions. The initial section follows a more diverse route compared to the original path, aiming to sample less frequented areas.",
  "targeted_regions": [
    "All opportunity regions and attempting to create a path that avoids identified difficult regions"
  ]
}
```
2025-06-22 17:14:46,458 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:14:46,458 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3.993450557888093e+252, 路径: [44, 2, 3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 4, 5, 6, 7, 8, 9, 45, 66, 67, 68, 69, 70, 71, 72]
2025-06-22 17:14:46,458 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [44, 2, 3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 4, 5, 6, 7, 8, 9, 45, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 3.993450557888093e+252}
2025-06-22 17:14:46,458 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 17:14:46,460 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:14:46,460 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [19, 16, 18] and ending with [43, 48, 42]
- Cost: 9957.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8417047184170472
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:14:46,460 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:14:49,868 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    19, 16, 20, 25, 28, 31, 33, 35, 37, 39, 40, 41, 43, 44, 45, 46, 47, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 65, 63, 61, 59, 57, 55, 53, 51, 49, 42, 38, 36, 34, 32, 30, 29, 27, 26, 24, 23, 22, 21, 18, 17, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0
  ],
  "modification_strategy": "This strategy prioritizes path diversity by: 1) Diverging from the initial path early and exploring nodes not visited in the original path. 2) Significantly changing the sequence of node visits to cover a broader area. 3) Prioritizing movement towards unexplored and opportunity regions indicated by the search context, while cautiously avoiding the indicated difficult regions.",
  "targeted_regions": [
    "Opportunity Regions (Generalized as the new path covers various nodes that could indicate opportunities)",
    "Unexplored Regions (As the new path takes nodes not in the original)"
  ]
}
```

2025-06-22 17:14:49,868 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:14:49,868 - ExplorationExpert - INFO - 探索路径生成完成，成本: 18914.0, 路径: [19, 16, 20, 25, 28, 31, 33, 35, 37, 39, 40, 41, 43, 44, 45, 46, 47, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 65, 63, 61, 59, 57, 55, 53, 51, 49, 42, 38, 36, 34, 32, 30, 29, 27, 26, 24, 23, 22, 21, 18, 17, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0]
2025-06-22 17:14:49,868 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [19, 16, 20, 25, 28, 31, 33, 35, 37, 39, 40, 41, 43, 44, 45, 46, 47, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 65, 63, 61, 59, 57, 55, 53, 51, 49, 42, 38, 36, 34, 32, 30, 29, 27, 26, 24, 23, 22, 21, 18, 17, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 18914.0}
2025-06-22 17:14:49,868 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 17:14:49,868 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:14:49,868 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [63, 8, 27] and ending with [31, 10, 61]
- Cost: 94935.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8206686930091185
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:14:49,868 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:14:53,051 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    63, 8, 15, 42, 2, 19, 35, 58, 11, 24, 47, 60, 3, 50, 33, 1, 30, 49, 18, 52, 28, 4, 59, 14, 22, 38, 40, 55, 9, 12, 26, 37, 5, 51, 44, 6, 41, 10, 61
  ],
  "modification_strategy": "This path prioritizes exploration of the 'Unexplored Regions' by diverging early and often from the original path, incorporating nodes from these regions.  The diversity weight and risk tolerance are leveraged to deviate significantly while aiming for reasonable quality (avoiding difficult regions). The selection process favors nodes with lower connection frequency, to improve the diversity as indicated by the 'Population diversity'.",
  "targeted_regions": [
    "Unexplored regions 1 & 2",
    "Nodes with lower connection frequency to increase diversity (as indicated by 'Population diversity' of 0.8206686930091185)"
  ]
}
```

2025-06-22 17:14:53,051 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:14:53,051 - ExplorationExpert - INFO - 探索路径生成完成，成本: 63530.0, 路径: [63, 8, 15, 42, 2, 19, 35, 58, 11, 24, 47, 60, 3, 50, 33, 1, 30, 49, 18, 52, 28, 4, 59, 14, 22, 38, 40, 55, 9, 12, 26, 37, 5, 51, 44, 6, 41, 10, 61]
2025-06-22 17:14:53,051 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [63, 8, 15, 42, 2, 19, 35, 58, 11, 24, 47, 60, 3, 50, 33, 1, 30, 49, 18, 52, 28, 4, 59, 14, 22, 38, 40, 55, 9, 12, 26, 37, 5, 51, 44, 6, 41, 10, 61], 'cur_cost': 63530.0}
2025-06-22 17:14:53,051 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 17:14:53,051 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:14:53,051 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [65, 47, 2] and ending with [21, 11, 55]
- Cost: 101408.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.820919175911252
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:14:53,051 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:14:55,797 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [65, 47, 5, 18, 32, 40, 1, 9, 12, 25, 38, 49, 57, 61, 6, 15, 29, 43, 50, 63, 21, 11, 55],
  "modification_strategy": "This strategy prioritizes diversity by deviating from the original path early on and frequently. It attempts to explore the 2 'opportunity regions' mentioned in the context by choosing nodes that are likely to lead into or through those areas. The path avoids the 'difficult regions' by favoring nodes away from those potentially problematic zones. A preference is given to nodes not heavily represented in the original path, to maximize diversity. The path is kept relatively short to maintain a balance between exploration and computational efficiency. The path uses a combination of random node selection and a weighted selection that prioritizes nodes which fulfill the criteria.",
  "targeted_regions": ["Opportunity Region 1", "Opportunity Region 2"]
}
```
2025-06-22 17:14:55,797 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:14:55,797 - ExplorationExpert - INFO - 探索路径生成完成，成本: 32717.0, 路径: [65, 47, 5, 18, 32, 40, 1, 9, 12, 25, 38, 49, 57, 61, 6, 15, 29, 43, 50, 63, 21, 11, 55]
2025-06-22 17:14:55,797 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [65, 47, 5, 18, 32, 40, 1, 9, 12, 25, 38, 49, 57, 61, 6, 15, 29, 43, 50, 63, 21, 11, 55], 'cur_cost': 32717.0}
2025-06-22 17:14:55,797 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 17:14:55,797 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:14:55,801 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:14:55,802 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 113534.0
2025-06-22 17:14:57,419 - ExploitationExpert - INFO - res_population_num: 1
2025-06-22 17:14:57,419 - ExploitationExpert - INFO - res_population_costs: [9580]
2025-06-22 17:14:57,420 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 48,
       46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 58, 60, 54, 57, 64,
       52, 63, 65, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-06-22 17:14:57,421 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:14:57,421 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 3, 9, 15, 16, 18, 20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 33, 35, 37, 39, 40, 41, 43, 44, 45, 47, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 74, 76, 77, 79, 80, 81, 82, 84, 85, 87, 88, 90, 91, 92, 93, 95, 96, 97, 98], 'cur_cost': 1.1109497412598174e+272}, {'tour': [44, 2, 3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 4, 5, 6, 7, 8, 9, 45, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 3.993450557888093e+252}, {'tour': [19, 16, 20, 25, 28, 31, 33, 35, 37, 39, 40, 41, 43, 44, 45, 46, 47, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 65, 63, 61, 59, 57, 55, 53, 51, 49, 42, 38, 36, 34, 32, 30, 29, 27, 26, 24, 23, 22, 21, 18, 17, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 18914.0}, {'tour': [63, 8, 15, 42, 2, 19, 35, 58, 11, 24, 47, 60, 3, 50, 33, 1, 30, 49, 18, 52, 28, 4, 59, 14, 22, 38, 40, 55, 9, 12, 26, 37, 5, 51, 44, 6, 41, 10, 61], 'cur_cost': 63530.0}, {'tour': [65, 47, 5, 18, 32, 40, 1, 9, 12, 25, 38, 49, 57, 61, 6, 15, 29, 43, 50, 63, 21, 11, 55], 'cur_cost': 32717.0}, {'tour': array([42,  9, 28, 41, 27, 55, 39, 26, 31, 63, 47, 36, 24, 14, 57, 38, 58,
       11, 44, 22, 17, 56, 53, 37, 18, 62, 64, 29, 50, 15,  5, 23, 46, 33,
       35, 52,  4, 51, 12,  8, 45,  7, 25, 40,  6, 65,  0, 49, 54, 21, 20,
       19, 10,  3, 59, 34, 48, 43, 13,  1, 30, 16,  2, 61, 32, 60]), 'cur_cost': 113534.0}, {'tour': [58, 12, 29, 15, 57, 13, 61, 60, 4, 52, 10, 53, 46, 24, 17, 59, 34, 5, 64, 41, 11, 7, 32, 55, 42, 50, 20, 65, 38, 43, 23, 54, 14, 36, 47, 28, 18, 30, 33, 27, 26, 44, 37, 63, 35, 9, 16, 40, 22, 56, 21, 0, 49, 51, 25, 48, 45, 2, 6, 1, 39, 19, 8, 3, 31, 62], 'cur_cost': 102122.0}, {'tour': [2, 42, 25, 61, 23, 15, 11, 65, 43, 31, 47, 5, 52, 29, 60, 58, 14, 51, 35, 0, 17, 30, 12, 57, 33, 7, 1, 48, 39, 18, 20, 50, 55, 8, 22, 41, 59, 21, 54, 45, 62, 37, 32, 44, 64, 28, 53, 40, 4, 34, 38, 19, 24, 6, 56, 10, 26, 46, 63, 49, 27, 16, 36, 3, 9, 13], 'cur_cost': 119210.0}, {'tour': [54, 3, 34, 10, 36, 48, 42, 39, 23, 28, 51, 29, 47, 13, 62, 4, 43, 52, 17, 12, 38, 49, 2, 14, 33, 8, 5, 30, 55, 45, 59, 50, 53, 61, 46, 11, 9, 57, 31, 44, 21, 6, 22, 27, 40, 24, 0, 1, 63, 64, 41, 58, 15, 19, 20, 65, 60, 18, 32, 26, 7, 37, 25, 56, 35, 16], 'cur_cost': 104580.0}, {'tour': [27, 62, 47, 44, 55, 52, 20, 35, 26, 41, 51, 37, 22, 43, 10, 30, 12, 53, 60, 34, 28, 33, 17, 54, 24, 38, 49, 58, 16, 8, 18, 39, 56, 11, 29, 48, 14, 42, 32, 0, 61, 50, 25, 1, 5, 40, 6, 21, 7, 13, 65, 3, 59, 57, 45, 15, 23, 31, 9, 46, 64, 19, 36, 63, 2, 4], 'cur_cost': 104219.0}]
2025-06-22 17:14:57,422 - ExploitationExpert - INFO - 局部搜索耗时: 1.62秒
2025-06-22 17:14:57,422 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-06-22 17:14:57,422 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 17:14:57,422 - __main__ - INFO - 为个体 6 生成利用路径
2025-06-22 17:14:57,422 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:14:57,422 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:14:57,423 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 106974.0
2025-06-22 17:14:58,814 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 17:14:58,814 - ExploitationExpert - INFO - res_population_costs: [9580, 9571]
2025-06-22 17:14:58,814 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 48,
       46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 58, 60, 54, 57, 64,
       52, 63, 65, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-06-22 17:14:58,816 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:14:58,816 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 3, 9, 15, 16, 18, 20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 33, 35, 37, 39, 40, 41, 43, 44, 45, 47, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 74, 76, 77, 79, 80, 81, 82, 84, 85, 87, 88, 90, 91, 92, 93, 95, 96, 97, 98], 'cur_cost': 1.1109497412598174e+272}, {'tour': [44, 2, 3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 4, 5, 6, 7, 8, 9, 45, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 3.993450557888093e+252}, {'tour': [19, 16, 20, 25, 28, 31, 33, 35, 37, 39, 40, 41, 43, 44, 45, 46, 47, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 65, 63, 61, 59, 57, 55, 53, 51, 49, 42, 38, 36, 34, 32, 30, 29, 27, 26, 24, 23, 22, 21, 18, 17, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 18914.0}, {'tour': [63, 8, 15, 42, 2, 19, 35, 58, 11, 24, 47, 60, 3, 50, 33, 1, 30, 49, 18, 52, 28, 4, 59, 14, 22, 38, 40, 55, 9, 12, 26, 37, 5, 51, 44, 6, 41, 10, 61], 'cur_cost': 63530.0}, {'tour': [65, 47, 5, 18, 32, 40, 1, 9, 12, 25, 38, 49, 57, 61, 6, 15, 29, 43, 50, 63, 21, 11, 55], 'cur_cost': 32717.0}, {'tour': array([42,  9, 28, 41, 27, 55, 39, 26, 31, 63, 47, 36, 24, 14, 57, 38, 58,
       11, 44, 22, 17, 56, 53, 37, 18, 62, 64, 29, 50, 15,  5, 23, 46, 33,
       35, 52,  4, 51, 12,  8, 45,  7, 25, 40,  6, 65,  0, 49, 54, 21, 20,
       19, 10,  3, 59, 34, 48, 43, 13,  1, 30, 16,  2, 61, 32, 60]), 'cur_cost': 113534.0}, {'tour': array([22,  4, 56, 19, 55, 37,  1,  0, 40, 12, 33, 15,  5, 25, 16,  8, 27,
       44,  7,  2, 43, 39, 45, 21, 62, 46, 28, 32, 59, 49, 41, 36, 47, 11,
       61,  6,  9, 54, 38, 65, 17, 51, 26, 48, 35, 53, 13, 58, 63, 14, 23,
       57, 50, 60, 30, 10, 52, 18, 20, 29,  3, 64, 42, 24, 31, 34]), 'cur_cost': 106974.0}, {'tour': [2, 42, 25, 61, 23, 15, 11, 65, 43, 31, 47, 5, 52, 29, 60, 58, 14, 51, 35, 0, 17, 30, 12, 57, 33, 7, 1, 48, 39, 18, 20, 50, 55, 8, 22, 41, 59, 21, 54, 45, 62, 37, 32, 44, 64, 28, 53, 40, 4, 34, 38, 19, 24, 6, 56, 10, 26, 46, 63, 49, 27, 16, 36, 3, 9, 13], 'cur_cost': 119210.0}, {'tour': [54, 3, 34, 10, 36, 48, 42, 39, 23, 28, 51, 29, 47, 13, 62, 4, 43, 52, 17, 12, 38, 49, 2, 14, 33, 8, 5, 30, 55, 45, 59, 50, 53, 61, 46, 11, 9, 57, 31, 44, 21, 6, 22, 27, 40, 24, 0, 1, 63, 64, 41, 58, 15, 19, 20, 65, 60, 18, 32, 26, 7, 37, 25, 56, 35, 16], 'cur_cost': 104580.0}, {'tour': [27, 62, 47, 44, 55, 52, 20, 35, 26, 41, 51, 37, 22, 43, 10, 30, 12, 53, 60, 34, 28, 33, 17, 54, 24, 38, 49, 58, 16, 8, 18, 39, 56, 11, 29, 48, 14, 42, 32, 0, 61, 50, 25, 1, 5, 40, 6, 21, 7, 13, 65, 3, 59, 57, 45, 15, 23, 31, 9, 46, 64, 19, 36, 63, 2, 4], 'cur_cost': 104219.0}]
2025-06-22 17:14:58,818 - ExploitationExpert - INFO - 局部搜索耗时: 1.39秒
2025-06-22 17:14:58,818 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-06-22 17:14:58,818 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-06-22 17:14:58,818 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 17:14:58,818 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:14:58,818 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:14:58,819 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 114815.0
2025-06-22 17:14:59,357 - ExploitationExpert - INFO - res_population_num: 2
2025-06-22 17:14:59,357 - ExploitationExpert - INFO - res_population_costs: [9580, 9571]
2025-06-22 17:14:59,357 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 48,
       46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 58, 60, 54, 57, 64,
       52, 63, 65, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64)]
2025-06-22 17:14:59,357 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:14:59,357 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 3, 9, 15, 16, 18, 20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 33, 35, 37, 39, 40, 41, 43, 44, 45, 47, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 74, 76, 77, 79, 80, 81, 82, 84, 85, 87, 88, 90, 91, 92, 93, 95, 96, 97, 98], 'cur_cost': 1.1109497412598174e+272}, {'tour': [44, 2, 3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 4, 5, 6, 7, 8, 9, 45, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 3.993450557888093e+252}, {'tour': [19, 16, 20, 25, 28, 31, 33, 35, 37, 39, 40, 41, 43, 44, 45, 46, 47, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 65, 63, 61, 59, 57, 55, 53, 51, 49, 42, 38, 36, 34, 32, 30, 29, 27, 26, 24, 23, 22, 21, 18, 17, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 18914.0}, {'tour': [63, 8, 15, 42, 2, 19, 35, 58, 11, 24, 47, 60, 3, 50, 33, 1, 30, 49, 18, 52, 28, 4, 59, 14, 22, 38, 40, 55, 9, 12, 26, 37, 5, 51, 44, 6, 41, 10, 61], 'cur_cost': 63530.0}, {'tour': [65, 47, 5, 18, 32, 40, 1, 9, 12, 25, 38, 49, 57, 61, 6, 15, 29, 43, 50, 63, 21, 11, 55], 'cur_cost': 32717.0}, {'tour': array([42,  9, 28, 41, 27, 55, 39, 26, 31, 63, 47, 36, 24, 14, 57, 38, 58,
       11, 44, 22, 17, 56, 53, 37, 18, 62, 64, 29, 50, 15,  5, 23, 46, 33,
       35, 52,  4, 51, 12,  8, 45,  7, 25, 40,  6, 65,  0, 49, 54, 21, 20,
       19, 10,  3, 59, 34, 48, 43, 13,  1, 30, 16,  2, 61, 32, 60]), 'cur_cost': 113534.0}, {'tour': array([22,  4, 56, 19, 55, 37,  1,  0, 40, 12, 33, 15,  5, 25, 16,  8, 27,
       44,  7,  2, 43, 39, 45, 21, 62, 46, 28, 32, 59, 49, 41, 36, 47, 11,
       61,  6,  9, 54, 38, 65, 17, 51, 26, 48, 35, 53, 13, 58, 63, 14, 23,
       57, 50, 60, 30, 10, 52, 18, 20, 29,  3, 64, 42, 24, 31, 34]), 'cur_cost': 106974.0}, {'tour': array([ 9, 27, 63, 26,  7, 42,  1, 12, 13, 62, 41,  4, 33, 10, 25, 46,  2,
       23, 18, 22, 28,  8, 31, 55, 49, 65, 58, 57, 40,  3, 50,  0, 48, 17,
       16,  5, 30, 61, 64, 53, 47, 60, 19,  6, 43, 11, 32, 36, 52, 37, 35,
       29, 44, 39, 24, 14, 45, 38, 34, 15, 21, 54, 51, 56, 20, 59]), 'cur_cost': 114815.0}, {'tour': [54, 3, 34, 10, 36, 48, 42, 39, 23, 28, 51, 29, 47, 13, 62, 4, 43, 52, 17, 12, 38, 49, 2, 14, 33, 8, 5, 30, 55, 45, 59, 50, 53, 61, 46, 11, 9, 57, 31, 44, 21, 6, 22, 27, 40, 24, 0, 1, 63, 64, 41, 58, 15, 19, 20, 65, 60, 18, 32, 26, 7, 37, 25, 56, 35, 16], 'cur_cost': 104580.0}, {'tour': [27, 62, 47, 44, 55, 52, 20, 35, 26, 41, 51, 37, 22, 43, 10, 30, 12, 53, 60, 34, 28, 33, 17, 54, 24, 38, 49, 58, 16, 8, 18, 39, 56, 11, 29, 48, 14, 42, 32, 0, 61, 50, 25, 1, 5, 40, 6, 21, 7, 13, 65, 3, 59, 57, 45, 15, 23, 31, 9, 46, 64, 19, 36, 63, 2, 4], 'cur_cost': 104219.0}]
2025-06-22 17:14:59,365 - ExploitationExpert - INFO - 局部搜索耗时: 0.54秒
2025-06-22 17:14:59,365 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-06-22 17:14:59,365 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 17:14:59,365 - __main__ - INFO - 为个体 8 生成利用路径
2025-06-22 17:14:59,365 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:14:59,365 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:14:59,365 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 100458.0
2025-06-22 17:14:59,883 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:14:59,884 - ExploitationExpert - INFO - res_population_costs: [9580, 9571, 9547]
2025-06-22 17:14:59,884 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 48,
       46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 58, 60, 54, 57, 64,
       52, 63, 65, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 17:14:59,884 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:14:59,884 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 3, 9, 15, 16, 18, 20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 33, 35, 37, 39, 40, 41, 43, 44, 45, 47, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 74, 76, 77, 79, 80, 81, 82, 84, 85, 87, 88, 90, 91, 92, 93, 95, 96, 97, 98], 'cur_cost': 1.1109497412598174e+272}, {'tour': [44, 2, 3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 4, 5, 6, 7, 8, 9, 45, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 3.993450557888093e+252}, {'tour': [19, 16, 20, 25, 28, 31, 33, 35, 37, 39, 40, 41, 43, 44, 45, 46, 47, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 65, 63, 61, 59, 57, 55, 53, 51, 49, 42, 38, 36, 34, 32, 30, 29, 27, 26, 24, 23, 22, 21, 18, 17, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 18914.0}, {'tour': [63, 8, 15, 42, 2, 19, 35, 58, 11, 24, 47, 60, 3, 50, 33, 1, 30, 49, 18, 52, 28, 4, 59, 14, 22, 38, 40, 55, 9, 12, 26, 37, 5, 51, 44, 6, 41, 10, 61], 'cur_cost': 63530.0}, {'tour': [65, 47, 5, 18, 32, 40, 1, 9, 12, 25, 38, 49, 57, 61, 6, 15, 29, 43, 50, 63, 21, 11, 55], 'cur_cost': 32717.0}, {'tour': array([42,  9, 28, 41, 27, 55, 39, 26, 31, 63, 47, 36, 24, 14, 57, 38, 58,
       11, 44, 22, 17, 56, 53, 37, 18, 62, 64, 29, 50, 15,  5, 23, 46, 33,
       35, 52,  4, 51, 12,  8, 45,  7, 25, 40,  6, 65,  0, 49, 54, 21, 20,
       19, 10,  3, 59, 34, 48, 43, 13,  1, 30, 16,  2, 61, 32, 60]), 'cur_cost': 113534.0}, {'tour': array([22,  4, 56, 19, 55, 37,  1,  0, 40, 12, 33, 15,  5, 25, 16,  8, 27,
       44,  7,  2, 43, 39, 45, 21, 62, 46, 28, 32, 59, 49, 41, 36, 47, 11,
       61,  6,  9, 54, 38, 65, 17, 51, 26, 48, 35, 53, 13, 58, 63, 14, 23,
       57, 50, 60, 30, 10, 52, 18, 20, 29,  3, 64, 42, 24, 31, 34]), 'cur_cost': 106974.0}, {'tour': array([ 9, 27, 63, 26,  7, 42,  1, 12, 13, 62, 41,  4, 33, 10, 25, 46,  2,
       23, 18, 22, 28,  8, 31, 55, 49, 65, 58, 57, 40,  3, 50,  0, 48, 17,
       16,  5, 30, 61, 64, 53, 47, 60, 19,  6, 43, 11, 32, 36, 52, 37, 35,
       29, 44, 39, 24, 14, 45, 38, 34, 15, 21, 54, 51, 56, 20, 59]), 'cur_cost': 114815.0}, {'tour': array([28,  4, 15, 64, 19,  7, 24, 26, 41, 20, 40, 29, 21, 10,  5,  2, 31,
       22, 47, 35, 63, 37, 38, 39, 51, 52, 44, 36, 34, 14, 43,  9, 49, 61,
       58, 27, 23, 42, 62, 13, 55, 57, 50, 45,  6, 59,  0, 65, 56, 33, 18,
       12, 48, 46, 32,  1, 30, 53, 25, 11, 54, 60, 16,  3,  8, 17]), 'cur_cost': 100458.0}, {'tour': [27, 62, 47, 44, 55, 52, 20, 35, 26, 41, 51, 37, 22, 43, 10, 30, 12, 53, 60, 34, 28, 33, 17, 54, 24, 38, 49, 58, 16, 8, 18, 39, 56, 11, 29, 48, 14, 42, 32, 0, 61, 50, 25, 1, 5, 40, 6, 21, 7, 13, 65, 3, 59, 57, 45, 15, 23, 31, 9, 46, 64, 19, 36, 63, 2, 4], 'cur_cost': 104219.0}]
2025-06-22 17:14:59,884 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-06-22 17:14:59,884 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-06-22 17:14:59,887 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-06-22 17:14:59,887 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 17:14:59,887 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:14:59,887 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:14:59,887 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 107165.0
2025-06-22 17:15:00,401 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:15:00,401 - ExploitationExpert - INFO - res_population_costs: [9580, 9571, 9547]
2025-06-22 17:15:00,401 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 48,
       46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 58, 60, 54, 57, 64,
       52, 63, 65, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-06-22 17:15:00,402 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:15:00,402 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 3, 9, 15, 16, 18, 20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 33, 35, 37, 39, 40, 41, 43, 44, 45, 47, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 74, 76, 77, 79, 80, 81, 82, 84, 85, 87, 88, 90, 91, 92, 93, 95, 96, 97, 98], 'cur_cost': 1.1109497412598174e+272}, {'tour': [44, 2, 3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 4, 5, 6, 7, 8, 9, 45, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 3.993450557888093e+252}, {'tour': [19, 16, 20, 25, 28, 31, 33, 35, 37, 39, 40, 41, 43, 44, 45, 46, 47, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 65, 63, 61, 59, 57, 55, 53, 51, 49, 42, 38, 36, 34, 32, 30, 29, 27, 26, 24, 23, 22, 21, 18, 17, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 18914.0}, {'tour': [63, 8, 15, 42, 2, 19, 35, 58, 11, 24, 47, 60, 3, 50, 33, 1, 30, 49, 18, 52, 28, 4, 59, 14, 22, 38, 40, 55, 9, 12, 26, 37, 5, 51, 44, 6, 41, 10, 61], 'cur_cost': 63530.0}, {'tour': [65, 47, 5, 18, 32, 40, 1, 9, 12, 25, 38, 49, 57, 61, 6, 15, 29, 43, 50, 63, 21, 11, 55], 'cur_cost': 32717.0}, {'tour': array([42,  9, 28, 41, 27, 55, 39, 26, 31, 63, 47, 36, 24, 14, 57, 38, 58,
       11, 44, 22, 17, 56, 53, 37, 18, 62, 64, 29, 50, 15,  5, 23, 46, 33,
       35, 52,  4, 51, 12,  8, 45,  7, 25, 40,  6, 65,  0, 49, 54, 21, 20,
       19, 10,  3, 59, 34, 48, 43, 13,  1, 30, 16,  2, 61, 32, 60]), 'cur_cost': 113534.0}, {'tour': array([22,  4, 56, 19, 55, 37,  1,  0, 40, 12, 33, 15,  5, 25, 16,  8, 27,
       44,  7,  2, 43, 39, 45, 21, 62, 46, 28, 32, 59, 49, 41, 36, 47, 11,
       61,  6,  9, 54, 38, 65, 17, 51, 26, 48, 35, 53, 13, 58, 63, 14, 23,
       57, 50, 60, 30, 10, 52, 18, 20, 29,  3, 64, 42, 24, 31, 34]), 'cur_cost': 106974.0}, {'tour': array([ 9, 27, 63, 26,  7, 42,  1, 12, 13, 62, 41,  4, 33, 10, 25, 46,  2,
       23, 18, 22, 28,  8, 31, 55, 49, 65, 58, 57, 40,  3, 50,  0, 48, 17,
       16,  5, 30, 61, 64, 53, 47, 60, 19,  6, 43, 11, 32, 36, 52, 37, 35,
       29, 44, 39, 24, 14, 45, 38, 34, 15, 21, 54, 51, 56, 20, 59]), 'cur_cost': 114815.0}, {'tour': array([28,  4, 15, 64, 19,  7, 24, 26, 41, 20, 40, 29, 21, 10,  5,  2, 31,
       22, 47, 35, 63, 37, 38, 39, 51, 52, 44, 36, 34, 14, 43,  9, 49, 61,
       58, 27, 23, 42, 62, 13, 55, 57, 50, 45,  6, 59,  0, 65, 56, 33, 18,
       12, 48, 46, 32,  1, 30, 53, 25, 11, 54, 60, 16,  3,  8, 17]), 'cur_cost': 100458.0}, {'tour': array([42, 54, 26, 28, 64, 57, 30, 18, 48, 29, 15, 19, 17, 55, 39, 14, 43,
       58, 56, 33, 11, 38, 37, 41, 46, 44, 27, 10, 65, 21, 31, 52, 32, 12,
       20, 34, 22, 45, 50, 23, 53, 62, 47,  1, 59,  4,  9, 40,  7,  2, 35,
       60,  6, 49, 61, 16, 36, 63, 25, 51, 24,  0, 13,  8,  5,  3]), 'cur_cost': 107165.0}]
2025-06-22 17:15:00,402 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-06-22 17:15:00,402 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 5, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 5, 'cache_hits': 0, 'similarity_calculations': 10, 'cache_hit_rate': 0.0, 'cache_size': 10}}
2025-06-22 17:15:00,402 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 17:15:00,406 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 1, 3, 9, 15, 16, 18, 20, 21, 22, 23, 24, 26, 27, 29, 30, 32, 33, 35, 37, 39, 40, 41, 43, 44, 45, 47, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 74, 76, 77, 79, 80, 81, 82, 84, 85, 87, 88, 90, 91, 92, 93, 95, 96, 97, 98], 'cur_cost': 1.1109497412598174e+272}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [44, 2, 3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 1, 4, 5, 6, 7, 8, 9, 45, 66, 67, 68, 69, 70, 71, 72], 'cur_cost': 3.993450557888093e+252}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [19, 16, 20, 25, 28, 31, 33, 35, 37, 39, 40, 41, 43, 44, 45, 46, 47, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 65, 63, 61, 59, 57, 55, 53, 51, 49, 42, 38, 36, 34, 32, 30, 29, 27, 26, 24, 23, 22, 21, 18, 17, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 18914.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [63, 8, 15, 42, 2, 19, 35, 58, 11, 24, 47, 60, 3, 50, 33, 1, 30, 49, 18, 52, 28, 4, 59, 14, 22, 38, 40, 55, 9, 12, 26, 37, 5, 51, 44, 6, 41, 10, 61], 'cur_cost': 63530.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [65, 47, 5, 18, 32, 40, 1, 9, 12, 25, 38, 49, 57, 61, 6, 15, 29, 43, 50, 63, 21, 11, 55], 'cur_cost': 32717.0}}]
2025-06-22 17:15:00,406 - __main__ - INFO - 进化阶段完成
2025-06-22 17:15:00,406 - __main__ - INFO - 开始评估阶段
2025-06-22 17:15:00,406 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 17:15:00,407 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 17:15:00,407 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 0/5
- Population changes:
  * Cost: min 9946.0->18914.0, mean 77497.3->1.1109497412598175e+271
  * Diversity: 0.7476923076923077->0.8248299319727891
  * Elite solutions: 3->1

## Strategy Performance:
- Explore: 5 individuals, success rate 40.0%, avg improvement -2.221899482519635e+271
- Exploit: 5 individuals, success rate 60.0%, avg improvement 1118.4
- Overall improvement: -8968.0

## Historical Trends:
No historical data available

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 17:15:07,027 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early-Stage/Instability",
    "features": "Significant cost inflation, initial diversity levels holding but decreasing elite solutions. Overall performance is negative with highly volatile cost values. The exploration phase results show significant deterioration in solution quality, while exploitation is yielding positive, though limited, gains. The initial phase is likely unstable and characterized by random walks in solution space.",
    "rationale": "The massive cost change and decreasing number of elite solutions indicate the evolutionary process is struggling to find good solutions and may be veering off the desired path. The exploration phase seems to be negatively impacting the current status of the population."
  },
  "strategy_effectiveness": {
    "exploration": {
      "performance": "Very Poor",
      "metrics": "Success rate of 40% and a highly negative average improvement (-2.22e+271) suggest exploration is actively destabilizing the population. The cost is increasing with poor search quality.",
      "recommendation": "Re-evaluate the exploration strategy. It might be generating individuals that are entirely unsuitable or pushing the search towards regions of the solution space with poor characteristics. The large negative cost changes indicate the mutation/selection process is failing."
    },
    "exploitation": {
      "performance": "Positive, but limited.",
      "metrics": "Success rate of 60% with a positive average improvement of 1118.4 suggests exploitation is yielding improvements. The improvement is small compared to the losses from the exploration phase.",
      "recommendation": "Exploitation is the only mechanism producing positive result. Increasing the intensity of the exploit strategy might improve performance, if it is done carefully and in small steps, but the focus should be on getting the exploration strategy to function."
    },
    "overall": {
      "performance": "Negative",
      "metrics": "Overall negative improvement of -8968.0 confirms that the current strategy configuration is not producing desired results. The highly volatile cost changes and the decline in elite solutions reinforce this.",
      "recommendation": "Immediate intervention needed to stabilize the process. Re-evaluate the exploration strategy and, if needed, slightly decrease the exploitation in order to maintain stability."
    }
  },
  "balance_state": {
    "assessment": "Severely Imbalanced",
    "details": "Exploration is actively hindering progress. Exploitation is slightly improving the population, but not enough to offset the effects of the failed exploration phase. The balance is skewed towards destabilization.",
    "adjustment_needs": "Reduce the impact of the current exploration mechanism and introduce a more stable (and potentially more localized) exploration strategy."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Re-evaluate Exploration Strategy. Consider a more conservative exploration approach. The current exploration strategy is producing bad results and must be corrected. Consider smaller, more localized mutations; this could involve adjusting the mutation rates for genes or utilizing a different type of variation operator. Evaluate which parts of the individual generate very bad solutions and try to avoid them.",
      "rationale": "The current exploration strategy is clearly failing. A less disruptive approach is needed to avoid the cost inflation observed."
    },
    {
      "priority": "Medium",
      "action": "Consider Increasing Exploitation. Since the exploitation strategy is yielding the only positive results, increase the ratio to slightly increase the exploitation phase. Evaluate the impact of the change and return to baseline if the solution deteriorates.",
      "rationale": "If the exploration strategy is not producing any improvements, increasing the exploitation may help find better solutions. However, this is only recommended after the first change is in place and the process is stable."
    },
    {
      "priority": "Low",
      "action": "Monitor and Document Changes. Carefully document all changes made to the evolution strategy, and record the performance metrics for each iteration.",
      "rationale": "Tracking changes allows for more effective troubleshooting and facilitates the identification of what works and what doesn't."
    }
  ]
}
```
2025-06-22 17:15:07,046 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 17:15:07,047 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early-Stage/Instability",
    "features": "Significant cost inflation, initial diversity levels holding but decreasing elite solutions. Overall performance is negative with highly volatile cost values. The exploration phase results show significant deterioration in solution quality, while exploitation is yielding positive, though limited, gains. The initial phase is likely unstable and characterized by random walks in solution space.",
    "rationale": "The massive cost change and decreasing number of elite solutions indicate the evolutionary process is struggling to find good solutions and may be veering off the desired path. The exploration phase seems to be negatively impacting the current status of the population."
  },
  "strategy_effectiveness": {
    "exploration": {
      "performance": "Very Poor",
      "metrics": "Success rate of 40% and a highly negative average improvement (-2.22e+271) suggest exploration is actively destabilizing the population. The cost is increasing with poor search quality.",
      "recommendation": "Re-evaluate the exploration strategy. It might be generating individuals that are entirely unsuitable or pushing the search towards regions of the solution space with poor characteristics. The large negative cost changes indicate the mutation/selection process is failing."
    },
    "exploitation": {
      "performance": "Positive, but limited.",
      "metrics": "Success rate of 60% with a positive average improvement of 1118.4 suggests exploitation is yielding improvements. The improvement is small compared to the losses from the exploration phase.",
      "recommendation": "Exploitation is the only mechanism producing positive result. Increasing the intensity of the exploit strategy might improve performance, if it is done carefully and in small steps, but the focus should be on getting the exploration strategy to function."
    },
    "overall": {
      "performance": "Negative",
      "metrics": "Overall negative improvement of -8968.0 confirms that the current strategy configuration is not producing desired results. The highly volatile cost changes and the decline in elite solutions reinforce this.",
      "recommendation": "Immediate intervention needed to stabilize the process. Re-evaluate the exploration strategy and, if needed, slightly decrease the exploitation in order to maintain stability."
    }
  },
  "balance_state": {
    "assessment": "Severely Imbalanced",
    "details": "Exploration is actively hindering progress. Exploitation is slightly improving the population, but not enough to offset the effects of the failed exploration phase. The balance is skewed towards destabilization.",
    "adjustment_needs": "Reduce the impact of the current exploration mechanism and introduce a more stable (and potentially more localized) exploration strategy."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Re-evaluate Exploration Strategy. Consider a more conservative exploration approach. The current exploration strategy is producing bad results and must be corrected. Consider smaller, more localized mutations; this could involve adjusting the mutation rates for genes or utilizing a different type of variation operator. Evaluate which parts of the individual generate very bad solutions and try to avoid them.",
      "rationale": "The current exploration strategy is clearly failing. A less disruptive approach is needed to avoid the cost inflation observed."
    },
    {
      "priority": "Medium",
      "action": "Consider Increasing Exploitation. Since the exploitation strategy is yielding the only positive results, increase the ratio to slightly increase the exploitation phase. Evaluate the impact of the change and return to baseline if the solution deteriorates.",
      "rationale": "If the exploration strategy is not producing any improvements, increasing the exploitation may help find better solutions. However, this is only recommended after the first change is in place and the process is stable."
    },
    {
      "priority": "Low",
      "action": "Monitor and Document Changes. Carefully document all changes made to the evolution strategy, and record the performance metrics for each iteration.",
      "rationale": "Tracking changes allows for more effective troubleshooting and facilitates the identification of what works and what doesn't."
    }
  ]
}
```
2025-06-22 17:15:07,048 - __main__ - INFO - 评估阶段完成
2025-06-22 17:15:07,048 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early-Stage/Instability",
    "features": "Significant cost inflation, initial diversity levels holding but decreasing elite solutions. Overall performance is negative with highly volatile cost values. The exploration phase results show significant deterioration in solution quality, while exploitation is yielding positive, though limited, gains. The initial phase is likely unstable and characterized by random walks in solution space.",
    "rationale": "The massive cost change and decreasing number of elite solutions indicate the evolutionary process is struggling to find good solutions and may be veering off the desired path. The exploration phase seems to be negatively impacting the current status of the population."
  },
  "strategy_effectiveness": {
    "exploration": {
      "performance": "Very Poor",
      "metrics": "Success rate of 40% and a highly negative average improvement (-2.22e+271) suggest exploration is actively destabilizing the population. The cost is increasing with poor search quality.",
      "recommendation": "Re-evaluate the exploration strategy. It might be generating individuals that are entirely unsuitable or pushing the search towards regions of the solution space with poor characteristics. The large negative cost changes indicate the mutation/selection process is failing."
    },
    "exploitation": {
      "performance": "Positive, but limited.",
      "metrics": "Success rate of 60% with a positive average improvement of 1118.4 suggests exploitation is yielding improvements. The improvement is small compared to the losses from the exploration phase.",
      "recommendation": "Exploitation is the only mechanism producing positive result. Increasing the intensity of the exploit strategy might improve performance, if it is done carefully and in small steps, but the focus should be on getting the exploration strategy to function."
    },
    "overall": {
      "performance": "Negative",
      "metrics": "Overall negative improvement of -8968.0 confirms that the current strategy configuration is not producing desired results. The highly volatile cost changes and the decline in elite solutions reinforce this.",
      "recommendation": "Immediate intervention needed to stabilize the process. Re-evaluate the exploration strategy and, if needed, slightly decrease the exploitation in order to maintain stability."
    }
  },
  "balance_state": {
    "assessment": "Severely Imbalanced",
    "details": "Exploration is actively hindering progress. Exploitation is slightly improving the population, but not enough to offset the effects of the failed exploration phase. The balance is skewed towards destabilization.",
    "adjustment_needs": "Reduce the impact of the current exploration mechanism and introduce a more stable (and potentially more localized) exploration strategy."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Re-evaluate Exploration Strategy. Consider a more conservative exploration approach. The current exploration strategy is producing bad results and must be corrected. Consider smaller, more localized mutations; this could involve adjusting the mutation rates for genes or utilizing a different type of variation operator. Evaluate which parts of the individual generate very bad solutions and try to avoid them.",
      "rationale": "The current exploration strategy is clearly failing. A less disruptive approach is needed to avoid the cost inflation observed."
    },
    {
      "priority": "Medium",
      "action": "Consider Increasing Exploitation. Since the exploitation strategy is yielding the only positive results, increase the ratio to slightly increase the exploitation phase. Evaluate the impact of the change and return to baseline if the solution deteriorates.",
      "rationale": "If the exploration strategy is not producing any improvements, increasing the exploitation may help find better solutions. However, this is only recommended after the first change is in place and the process is stable."
    },
    {
      "priority": "Low",
      "action": "Monitor and Document Changes. Carefully document all changes made to the evolution strategy, and record the performance metrics for each iteration.",
      "rationale": "Tracking changes allows for more effective troubleshooting and facilitates the identification of what works and what doesn't."
    }
  ]
}
```
2025-06-22 17:15:07,049 - __main__ - INFO - 当前最佳适应度: 18914.0
2025-06-22 17:15:07,051 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_0.pkl
2025-06-22 17:15:07,051 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-06-22 17:15:07,051 - __main__ - INFO - 开始分析阶段
2025-06-22 17:15:07,052 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:15:07,059 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 18914.0, 'max': 1.1109497412598174e+272, 'mean': 1.1109497412598175e+271, 'std': inf}, 'diversity': 0.9883059450223628, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:15:07,059 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 18914.0, 'max': 1.1109497412598174e+272, 'mean': 1.1109497412598175e+271, 'std': inf}, 'diversity_level': 0.9883059450223628, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:15:07,060 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:15:07,060 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:15:07,060 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:15:07,061 - PathExpert - WARNING - 发现无效的城市索引: [67, 68, 69, 70, 71, 72, 74, 76, 77, 79, 80, 81, 82, 84, 85, 87, 88, 90, 91, 92, 93, 95, 96, 97, 98], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:15:07,061 - PathExpert - WARNING - 发现无效的城市索引: [66, 67, 68, 69, 70, 71, 72], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:15:07,061 - PathExpert - WARNING - 发现无效的城市索引: [66], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=63, city2=8, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=8, city2=15, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=42, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=2, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=19, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=19, city2=35, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=58, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=11, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=24, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=47, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=60, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=3, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=50, tour_length=39
2025-06-22 17:15:07,178 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=33, tour_length=39
2025-06-22 17:15:07,185 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=1, tour_length=39
2025-06-22 17:15:07,185 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=30, tour_length=39
2025-06-22 17:15:07,185 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=49, tour_length=39
2025-06-22 17:15:07,185 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=18, tour_length=39
2025-06-22 17:15:07,185 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=52, tour_length=39
2025-06-22 17:15:07,185 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=28, tour_length=39
2025-06-22 17:15:07,185 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=28, city2=4, tour_length=39
2025-06-22 17:15:07,185 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=59, tour_length=39
2025-06-22 17:15:07,186 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=14, tour_length=39
2025-06-22 17:15:07,186 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=14, city2=22, tour_length=39
2025-06-22 17:15:07,186 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=38, tour_length=39
2025-06-22 17:15:07,187 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=40, tour_length=39
2025-06-22 17:15:07,187 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=55, tour_length=39
2025-06-22 17:15:07,187 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=9, tour_length=39
2025-06-22 17:15:07,187 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=12, tour_length=39
2025-06-22 17:15:07,187 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=26, tour_length=39
2025-06-22 17:15:07,187 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=26, city2=37, tour_length=39
2025-06-22 17:15:07,187 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=37, city2=5, tour_length=39
2025-06-22 17:15:07,188 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=51, tour_length=39
2025-06-22 17:15:07,188 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=51, city2=44, tour_length=39
2025-06-22 17:15:07,188 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=44, city2=6, tour_length=39
2025-06-22 17:15:07,188 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=41, tour_length=39
2025-06-22 17:15:07,188 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=41, city2=10, tour_length=39
2025-06-22 17:15:07,188 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=61, tour_length=39
2025-06-22 17:15:07,189 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=61, city2=63, tour_length=39
2025-06-22 17:15:07,189 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=65, city2=47, tour_length=23
2025-06-22 17:15:07,189 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=5, tour_length=23
2025-06-22 17:15:07,189 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=18, tour_length=23
2025-06-22 17:15:07,189 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=32, tour_length=23
2025-06-22 17:15:07,189 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=40, tour_length=23
2025-06-22 17:15:07,189 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=1, tour_length=23
2025-06-22 17:15:07,190 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=9, tour_length=23
2025-06-22 17:15:07,190 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=12, tour_length=23
2025-06-22 17:15:07,190 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=25, tour_length=23
2025-06-22 17:15:07,190 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=38, tour_length=23
2025-06-22 17:15:07,190 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=49, tour_length=23
2025-06-22 17:15:07,191 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=57, tour_length=23
2025-06-22 17:15:07,191 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=57, city2=61, tour_length=23
2025-06-22 17:15:07,191 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=61, city2=6, tour_length=23
2025-06-22 17:15:07,191 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=15, tour_length=23
2025-06-22 17:15:07,191 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=29, tour_length=23
2025-06-22 17:15:07,191 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=43, tour_length=23
2025-06-22 17:15:07,191 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=43, city2=50, tour_length=23
2025-06-22 17:15:07,191 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=63, tour_length=23
2025-06-22 17:15:07,191 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=63, city2=21, tour_length=23
2025-06-22 17:15:07,191 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=11, tour_length=23
2025-06-22 17:15:07,191 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=55, tour_length=23
2025-06-22 17:15:07,193 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=65, tour_length=23
2025-06-22 17:15:07,193 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 17:15:07,193 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:15:07,193 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 17:15:07,193 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:15:07,194 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:15:07,194 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:15:07,194 - EliteExpert - INFO - 精英解分析完成
2025-06-22 17:15:07,194 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {'(22, 12)': 1.0, '(12, 17)': 1.0, '(20, 21)': 1.0, '(62, 59)': 1.0, '(59, 56)': 1.0}, 'common_edge_ratio': 0.07575757575757576}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 9367.0, 'avg_gap': 1.1109497412598175e+271}, 'structure_gap': {'unique_elite_edges': 117, 'unique_pop_edges': 496, 'common_edges': 37}}, 'elite_diversity': {'diversity_score': 0.3939393939393939}}
2025-06-22 17:15:07,194 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:15:07,194 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:15:07,194 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:15:07,194 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=18914.0, max=1.1109497412598174e+272, mean=1.1109497412598175e+271, std=inf
- Diversity: 0.9883059450223628, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 0 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:15:13,152 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High. The extremely large cost range (min to max) and high standard deviation with only 10 individuals suggest a very uneven and potentially rugged search space.  The lack of high-quality edges and common subpaths also indicates a lack of smooth pathways between solutions.  The convergence to a single point (0.0 convergence) suggests a potentially deceptive landscape where the initial population is quickly drawn to a local optimum.",
    "modality": "Potentially high.  The cost distribution hints at a highly multimodal landscape. The immense difference between min and max costs, and the absence of any elite solutions with fixed nodes suggest the possible existence of multiple disconnected regions of high performance, making the search significantly more challenging.  A lack of diversity further supports this hypothesis.",
    "edge_structure": "Sparse. The absence of high-quality edges indicates that solutions do not share meaningful structural components."
  },
  "population_state": {
    "diversity": "Very High initially (0.988306). This diversity, coupled with the cost statistics and convergence value, suggests the population might be initially exploring a wide area but quickly gets pulled to a single poor local optimum. This is likely due to the extreme cost variation and the lack of structure.",
    "convergence": "Complete (0.0). The population has converged on a single point. This complete convergence, in light of the cost statistics, suggests the population is trapped in a very poor-performing local optima."
  },
  "difficult_regions": {
    "identified_challenges": "Multiple. The extremely large cost range with only 10 individuals presents a major challenge. The infinite standard deviation suggests the search space is very irregular, or the outliers are distorting the cost calculation. The complete lack of convergence to elite solutions and the lack of high-quality edges mean the population cannot benefit from any form of structural learning. The lack of any fixed nodes further compounds the difficulty. The high convergence value strongly suggests a local optimum with a poor-performing solution, making the search challenging.",
    "challenges_detail": "The landscape appears highly deceptive. The absence of elite solutions with fixed nodes indicates that any exploration is not leading to improved solutions. The large cost differences indicate the potential existence of very steep slopes in the search landscape that the current search algorithm is likely unable to overcome. The complete convergence suggests a significant risk of getting trapped in poor quality areas."
  },
  "opportunity_regions": {
    "identified_opportunities": "None identified with the current data. Since elite solutions are absent and the structure is extremely sparse, there's no evidence of promising regions based on the provided information.",
    "potential_exploration": "If possible, explore solutions beyond the current converged point. Identifying alternative starting points and re-exploring can help escape the local optimum. Further exploration is needed to identify better quality regions. Techniques focusing on exploitation or refinement are not suitable at this stage. "
  },
  "evolution_direction": {
    "strategy": "Exploration and Diversification is required.",
    "recommended_actions": [
      "Reset or diversify the population by randomly sampling from a wider search range. The extremely large cost variation suggests the existence of better solutions than are currently identified in the population.",
      "Experiment with different initialization methods that are less likely to converge prematurely, to avoid getting stuck into local optima quickly.",
      "Consider a mutation-focused approach to encourage a wider spread of the population across the search space.",
      "Avoid exploiting the best solution, as this is likely a poor-quality local optimum. Explore away from it and diversify."
    ]
  }
}
```
2025-06-22 17:15:13,152 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:15:13,152 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High. The extremely large cost range (min to max) and high standard deviation with only 10 individuals suggest a very uneven and potentially rugged search space.  The lack of high-quality edges and common subpaths also indicates a lack of smooth pathways between solutions.  The convergence to a single point (0.0 convergence) suggests a potentially deceptive landscape where the initial population is quickly drawn to a local optimum.', 'modality': 'Potentially high.  The cost distribution hints at a highly multimodal landscape. The immense difference between min and max costs, and the absence of any elite solutions with fixed nodes suggest the possible existence of multiple disconnected regions of high performance, making the search significantly more challenging.  A lack of diversity further supports this hypothesis.', 'edge_structure': 'Sparse. The absence of high-quality edges indicates that solutions do not share meaningful structural components.'}, 'population_state': {'diversity': 'Very High initially (0.988306). This diversity, coupled with the cost statistics and convergence value, suggests the population might be initially exploring a wide area but quickly gets pulled to a single poor local optimum. This is likely due to the extreme cost variation and the lack of structure.', 'convergence': 'Complete (0.0). The population has converged on a single point. This complete convergence, in light of the cost statistics, suggests the population is trapped in a very poor-performing local optima.'}, 'difficult_regions': {'identified_challenges': 'Multiple. The extremely large cost range with only 10 individuals presents a major challenge. The infinite standard deviation suggests the search space is very irregular, or the outliers are distorting the cost calculation. The complete lack of convergence to elite solutions and the lack of high-quality edges mean the population cannot benefit from any form of structural learning. The lack of any fixed nodes further compounds the difficulty. The high convergence value strongly suggests a local optimum with a poor-performing solution, making the search challenging.', 'challenges_detail': 'The landscape appears highly deceptive. The absence of elite solutions with fixed nodes indicates that any exploration is not leading to improved solutions. The large cost differences indicate the potential existence of very steep slopes in the search landscape that the current search algorithm is likely unable to overcome. The complete convergence suggests a significant risk of getting trapped in poor quality areas.'}, 'opportunity_regions': {'identified_opportunities': "None identified with the current data. Since elite solutions are absent and the structure is extremely sparse, there's no evidence of promising regions based on the provided information.", 'potential_exploration': 'If possible, explore solutions beyond the current converged point. Identifying alternative starting points and re-exploring can help escape the local optimum. Further exploration is needed to identify better quality regions. Techniques focusing on exploitation or refinement are not suitable at this stage. '}, 'evolution_direction': {'strategy': 'Exploration and Diversification is required.', 'recommended_actions': ['Reset or diversify the population by randomly sampling from a wider search range. The extremely large cost variation suggests the existence of better solutions than are currently identified in the population.', 'Experiment with different initialization methods that are less likely to converge prematurely, to avoid getting stuck into local optima quickly.', 'Consider a mutation-focused approach to encourage a wider spread of the population across the search space.', 'Avoid exploiting the best solution, as this is likely a poor-quality local optimum. Explore away from it and diversify.']}}
2025-06-22 17:15:13,152 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:15:13,152 - __main__ - INFO - 分析阶段完成
2025-06-22 17:15:13,152 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High. The extremely large cost range (min to max) and high standard deviation with only 10 individuals suggest a very uneven and potentially rugged search space.  The lack of high-quality edges and common subpaths also indicates a lack of smooth pathways between solutions.  The convergence to a single point (0.0 convergence) suggests a potentially deceptive landscape where the initial population is quickly drawn to a local optimum.', 'modality': 'Potentially high.  The cost distribution hints at a highly multimodal landscape. The immense difference between min and max costs, and the absence of any elite solutions with fixed nodes suggest the possible existence of multiple disconnected regions of high performance, making the search significantly more challenging.  A lack of diversity further supports this hypothesis.', 'edge_structure': 'Sparse. The absence of high-quality edges indicates that solutions do not share meaningful structural components.'}, 'population_state': {'diversity': 'Very High initially (0.988306). This diversity, coupled with the cost statistics and convergence value, suggests the population might be initially exploring a wide area but quickly gets pulled to a single poor local optimum. This is likely due to the extreme cost variation and the lack of structure.', 'convergence': 'Complete (0.0). The population has converged on a single point. This complete convergence, in light of the cost statistics, suggests the population is trapped in a very poor-performing local optima.'}, 'difficult_regions': {'identified_challenges': 'Multiple. The extremely large cost range with only 10 individuals presents a major challenge. The infinite standard deviation suggests the search space is very irregular, or the outliers are distorting the cost calculation. The complete lack of convergence to elite solutions and the lack of high-quality edges mean the population cannot benefit from any form of structural learning. The lack of any fixed nodes further compounds the difficulty. The high convergence value strongly suggests a local optimum with a poor-performing solution, making the search challenging.', 'challenges_detail': 'The landscape appears highly deceptive. The absence of elite solutions with fixed nodes indicates that any exploration is not leading to improved solutions. The large cost differences indicate the potential existence of very steep slopes in the search landscape that the current search algorithm is likely unable to overcome. The complete convergence suggests a significant risk of getting trapped in poor quality areas.'}, 'opportunity_regions': {'identified_opportunities': "None identified with the current data. Since elite solutions are absent and the structure is extremely sparse, there's no evidence of promising regions based on the provided information.", 'potential_exploration': 'If possible, explore solutions beyond the current converged point. Identifying alternative starting points and re-exploring can help escape the local optimum. Further exploration is needed to identify better quality regions. Techniques focusing on exploitation or refinement are not suitable at this stage. '}, 'evolution_direction': {'strategy': 'Exploration and Diversification is required.', 'recommended_actions': ['Reset or diversify the population by randomly sampling from a wider search range. The extremely large cost variation suggests the existence of better solutions than are currently identified in the population.', 'Experiment with different initialization methods that are less likely to converge prematurely, to avoid getting stuck into local optima quickly.', 'Consider a mutation-focused approach to encourage a wider spread of the population across the search space.', 'Avoid exploiting the best solution, as this is likely a poor-quality local optimum. Explore away from it and diversify.']}}
2025-06-22 17:15:13,152 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:15:13,152 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:15:13,152 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 1
- Population: 10 individuals, diversity=Very High initially (0.988306). This diversity, coupled with the cost statistics and convergence value, suggests the population might be initially exploring a wide area but quickly gets pulled to a single poor local optimum. This is likely due to the extreme cost variation and the lack of structure.
- Best cost: 18914.0, Mean cost: 1.1109497412598175e+271
- Search space: Ruggedness: High. The extremely large cost range (min to max) and high standard deviation with only 10 individuals suggest a very uneven and potentially rugged search space.  The lack of high-quality edges and common subpaths also indicates a lack of smooth pathways between solutions.  The convergence to a single point (0.0 convergence) suggests a potentially deceptive landscape where the initial population is quickly drawn to a local optimum., Modality: Potentially high.  The cost distribution hints at a highly multimodal landscape. The immense difference between min and max costs, and the absence of any elite solutions with fixed nodes suggest the possible existence of multiple disconnected regions of high performance, making the search significantly more challenging.  A lack of diversity further supports this hypothesis.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early-Stage/Instability",
    "features": "Significant cost inflation, initial diversity levels holding but decreasing elite solutions. Overall performan...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:15:13,152 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:15:15,435 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.4,
      "step_size": 0.05,
      "local_search_depth": 0
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size": 0.01,
      "local_search_depth": 0
    }
  }
}
```

2025-06-22 17:15:15,435 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-06-22 17:15:15,435 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.4,
      "step_size": 0.05,
      "local_search_depth": 0
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size": 0.01,
      "local_search_depth": 0
    }
  }
}
```

2025-06-22 17:15:15,435 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:15:15,435 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-06-22 17:15:15,435 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "explore",
    "8": "explore",
    "9": "explore"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.4,
      "step_size": 0.05,
      "local_search_depth": 0
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size": 0.01,
      "local_search_depth": 0
    }
  }
}
```

2025-06-22 17:15:15,439 - __main__ - INFO - 开始进化阶段
2025-06-22 17:15:15,439 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:15:15,439 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:15:15,439 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 67 nodes, starting with [6, 1, 3] and ending with [96, 97, 98]
- Cost: 1.1109497412598174e+272
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8248299319727891
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:15:15,439 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:15:18,627 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    6, 1, 4, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 78, 85, 92, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 79, 86, 93, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 80, 87, 94, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 81, 88, 95, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 82, 89, 96, 97, 98
  ],
  "modification_strategy": "The strategy prioritizes diversity by deviating from the original path early on and exploring a different set of nodes.  We aim to visit each opportunity region indirectly, changing the frequency to jump to and from the most difficult regions to avoid.",
  "targeted_regions": "Opportunity regions (identified but not explicitly listed), and we try to avoid the difficult regions by reducing the visitations, as indicated in the original data."
}
```
2025-06-22 17:15:18,627 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:15:18,627 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1.710431179386332e+214, 路径: [6, 1, 4, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 78, 85, 92, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 79, 86, 93, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 80, 87, 94, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 81, 88, 95, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 82, 89, 96, 97, 98]
2025-06-22 17:15:18,629 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 1, 4, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 78, 85, 92, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 79, 86, 93, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 80, 87, 94, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 81, 88, 95, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 82, 89, 96, 97, 98], 'cur_cost': 1.710431179386332e+214}
2025-06-22 17:15:18,629 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 17:15:18,629 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:15:18,629 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 72 nodes, starting with [44, 2, 3] and ending with [70, 71, 72]
- Cost: 3.993450557888093e+252
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8764805414551607
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:15:18,630 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:15:22,459 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    44, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 67, 69, 71, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1, 72 ],
  "modification_strategy": "The strategy prioritizes diversity by reversing the order of a significant portion of the original path while incorporating some of the nodes from the opportunity regions early in the path.  It starts by exploring some of the nodes from the original path and then explores the nodes from the opportunity region before switching to reverse the path.",
  "targeted_regions": [
    "Opportunity Region 1: Nodes between 5 and 10",
    "Opportunity Region 2: Nodes between 40 and 50"
  ]
}
```

2025-06-22 17:15:22,459 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:15:22,459 - ExplorationExpert - INFO - 探索路径生成完成，成本: -1.4701865236778527e+82, 路径: [44, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 67, 69, 71, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1, 72]
2025-06-22 17:15:22,459 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [44, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 67, 69, 71, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1, 72], 'cur_cost': -1.4701865236778527e+82}
2025-06-22 17:15:22,459 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 17:15:22,459 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:15:22,459 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 67 nodes, starting with [19, 16, 20] and ending with [2, 1, 0]
- Cost: 18914.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8646362098138748
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:15:22,459 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:15:25,633 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    19, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 6, 5, 4, 3, 2, 1, 0
  ],
  "modification_strategy": "This strategy prioritizes diversity by starting at the same beginning nodes but then immediately branching off to entirely new unexplored regions. It prioritizes these unexplored regions as quickly as possible before eventually approaching the end target. This sacrifices some quality to maximize diversity by thoroughly sampling areas of the graph never explored by previous paths.",
  "targeted_regions": "Opportunity Regions and a higher proportion of unexplored regions have been integrated early on into the path to focus on exploring those areas. Difficult regions were avoided where possible while maintaining a focus on these other regions."
}
```
2025-06-22 17:15:25,633 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:15:25,633 - ExplorationExpert - INFO - 探索路径生成完成，成本: 6.805680462630222e+38, 路径: [19, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 6, 5, 4, 3, 2, 1, 0]
2025-06-22 17:15:25,633 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [19, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 6.805680462630222e+38}
2025-06-22 17:15:25,633 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 17:15:25,633 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:15:25,633 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 39 nodes, starting with [63, 8, 15] and ending with [41, 10, 61]
- Cost: 63530.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9054982817869416
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:15:25,633 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:15:29,084 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    63, 1, 22, 35, 17, 48, 5, 11, 28, 3, 39, 52, 2, 29, 45, 14, 21, 58, 18, 50, 4, 62, 7, 32, 16, 26, 37, 55, 12, 23, 40, 59, 9, 30, 42, 53, 27, 41, 61
  ],
  "modification_strategy": "This strategy prioritizes diversity by significantly altering the node sequence.  It aims to explore the two opportunity regions directly and uses a higher diversity weight to encourage exploration of less-traveled pathways, while trying to avoid difficult regions by diversifying the paths through the network. The new path deviates substantially from the current path to ensure sufficient exploration of alternative regions.  Randomness is introduced to further disrupt the path, allowing it to traverse the search space in a less predictable manner, thereby potentially identifying previously unvisited areas.",
  "targeted_regions": ["Opportunity Region 1", "Opportunity Region 2"]
}
```
2025-06-22 17:15:29,084 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:15:29,084 - ExplorationExpert - INFO - 探索路径生成完成，成本: 63805.0, 路径: [63, 1, 22, 35, 17, 48, 5, 11, 28, 3, 39, 52, 2, 29, 45, 14, 21, 58, 18, 50, 4, 62, 7, 32, 16, 26, 37, 55, 12, 23, 40, 59, 9, 30, 42, 53, 27, 41, 61]
2025-06-22 17:15:29,084 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [63, 1, 22, 35, 17, 48, 5, 11, 28, 3, 39, 52, 2, 29, 45, 14, 21, 58, 18, 50, 4, 62, 7, 32, 16, 26, 37, 55, 12, 23, 40, 59, 9, 30, 42, 53, 27, 41, 61], 'cur_cost': 63805.0}
2025-06-22 17:15:29,084 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 17:15:29,084 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:15:29,084 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 23 nodes, starting with [65, 47, 5] and ending with [21, 11, 55]
- Cost: 32717.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9175257731958762
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:15:29,084 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:15:31,836 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    65, 47, 12, 1, 8, 25, 33, 18, 42, 50, 58, 6, 15, 29, 36, 4, 22, 30, 45, 52, 10, 21, 55
  ],
  "modification_strategy": "Prioritized exploration of unexplored and opportunity regions. Shifted away from high-risk regions identified in the search context.  The path was re-routed to visit more nodes in regions with high opportunity scores as well as nodes associated with low risk. This strategy aims for diversity by traversing through different areas of the search space, minimizing overlap with the original path and leveraging available opportunity.",
  "targeted_regions": [
    "Unexplored regions identified by the search context",
    "Opportunity regions identified by the search context",
    "Regions with lower risk scores"
  ]
}
```

2025-06-22 17:15:31,836 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:15:31,838 - ExplorationExpert - INFO - 探索路径生成完成，成本: 30270.0, 路径: [65, 47, 12, 1, 8, 25, 33, 18, 42, 50, 58, 6, 15, 29, 36, 4, 22, 30, 45, 52, 10, 21, 55]
2025-06-22 17:15:31,838 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [65, 47, 12, 1, 8, 25, 33, 18, 42, 50, 58, 6, 15, 29, 36, 4, 22, 30, 45, 52, 10, 21, 55], 'cur_cost': 30270.0}
2025-06-22 17:15:31,838 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-22 17:15:31,838 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:15:31,838 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [42, 9, 28] and ending with [61, 32, 60]
- Cost: 113534.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9089347079037801
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:15:31,838 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:15:35,304 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    42, 9, 3, 15, 21, 33, 45, 57, 6, 18, 30, 40, 52, 64, 1, 13, 25, 37, 49, 61, 32, 60
  ],
  "modification_strategy": "This path generation prioritizes diversity by significantly altering the original path's sequence. I aimed to deviate from the original path as early as possible. The new path explores a different subset of the available nodes, especially targeting the unexplored and opportunity regions indicated by the context, while trying to maintain a shorter path length (though not the primary focus here). The path starts at the same point but immediately takes a divergent route. The ending points are the same to allow easier validation of the cost if needed. This approach prioritizes exploration over immediate cost minimization, using the diversity weight as a guiding principle to force the generation of paths far from the current solution.",
  "targeted_regions": "The path aims to explore the unexplored regions and potential opportunity regions identified in the search context by selecting alternative routes at key decision points. The specific node choices within the modified path were guided by the existing map data, including the location of difficult regions to avoid and the overall structure of the graph. The selection of these nodes allows exploration of the targeted regions without excessive costs."
}
```

2025-06-22 17:15:35,304 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:15:35,304 - ExplorationExpert - INFO - 探索路径生成完成，成本: 35761.0, 路径: [42, 9, 3, 15, 21, 33, 45, 57, 6, 18, 30, 40, 52, 64, 1, 13, 25, 37, 49, 61, 32, 60]
2025-06-22 17:15:35,304 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [42, 9, 3, 15, 21, 33, 45, 57, 6, 18, 30, 40, 52, 64, 1, 13, 25, 37, 49, 61, 32, 60], 'cur_cost': 35761.0}
2025-06-22 17:15:35,304 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 17:15:35,304 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:15:35,304 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [22, 4, 56] and ending with [24, 31, 34]
- Cost: 106974.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9144981412639405
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:15:35,304 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:15:40,229 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [22, 1, 44, 25, 62, 48, 17, 9, 6, 53, 3, 38, 10, 42, 12, 35, 27, 29, 40, 60, 57, 21, 18, 59, 51, 14, 23, 47, 36, 30, 26, 55, 49, 16, 34],
  "modification_strategy": "This path prioritizes exploration of unexplored regions and regions offering opportunities, as indicated in the search context. We shift away from the original path early on to explore different areas, incorporating nodes from the opportunity regions while still attempting to maintain a somewhat efficient path. The high diversity weight helps ensure a path that is significantly different from the current one. Risk tolerance is balanced to allow some deviation from the most cost-effective routes for a chance to find undiscovered areas.",
  "targeted_regions": "Specifically targeted unexplored regions based on the search context and any opportunity regions that were present. This path avoids regions noted as 'difficult to avoid', and attempts to traverse the map in areas untouched by the initial path."
}
```
2025-06-22 17:15:40,229 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:15:40,229 - ExplorationExpert - INFO - 探索路径生成完成，成本: 53150.0, 路径: [22, 1, 44, 25, 62, 48, 17, 9, 6, 53, 3, 38, 10, 42, 12, 35, 27, 29, 40, 60, 57, 21, 18, 59, 51, 14, 23, 47, 36, 30, 26, 55, 49, 16, 34]
2025-06-22 17:15:40,229 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [22, 1, 44, 25, 62, 48, 17, 9, 6, 53, 3, 38, 10, 42, 12, 35, 27, 29, 40, 60, 57, 21, 18, 59, 51, 14, 23, 47, 36, 30, 26, 55, 49, 16, 34], 'cur_cost': 53150.0}
2025-06-22 17:15:40,229 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-22 17:15:40,229 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:15:40,229 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [9, 27, 63] and ending with [56, 20, 59]
- Cost: 114815.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9151873767258383
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:15:40,229 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:15:43,155 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    9, 27, 15, 48, 3, 35, 50, 12, 42, 60, 2, 21, 57, 4, 38, 18, 51, 24, 1, 29, 64, 45, 10, 32, 54, 7, 23, 40, 16, 59, 20, 56
  ],
  "modification_strategy": "Prioritized exploration of unexplored and opportunity regions by leveraging a biased random walk. The existing path was analyzed to identify segments with low diversity or that failed to touch promising areas. The new path was generated by favoring nodes from the two opportunity regions and incorporating moves to unexplored areas, deviating from the original while still ensuring path continuity and a reasonable path length. Risk tolerance was used to occasionally incorporate potentially costly moves that might expose new high-value nodes.",
  "targeted_regions": "Opportunity regions, unexplored regions."
}
```
2025-06-22 17:15:43,155 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:15:43,155 - ExplorationExpert - INFO - 探索路径生成完成，成本: 59138.0, 路径: [9, 27, 15, 48, 3, 35, 50, 12, 42, 60, 2, 21, 57, 4, 38, 18, 51, 24, 1, 29, 64, 45, 10, 32, 54, 7, 23, 40, 16, 59, 20, 56]
2025-06-22 17:15:43,155 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [9, 27, 15, 48, 3, 35, 50, 12, 42, 60, 2, 21, 57, 4, 38, 18, 51, 24, 1, 29, 64, 45, 10, 32, 54, 7, 23, 40, 16, 59, 20, 56], 'cur_cost': 59138.0}
2025-06-22 17:15:43,155 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 17:15:43,155 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:15:43,155 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [28, 4, 15] and ending with [3, 8, 17]
- Cost: 100458.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9238900634249472
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:15:43,155 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:15:47,442 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    28, 2, 10, 25, 42, 33, 18, 51, 5, 12, 60, 48, 3, 21, 17, 38, 45, 9, 14, 63, 30, 23, 58, 1, 40, 53, 35, 6, 29, 44, 55, 7, 50, 22, 41, 16, 39, 19, 24, 59, 11, 20, 65, 13, 49, 4, 34, 32, 56, 57, 8, 47, 26, 31, 64, 46, 52, 37, 61, 27, 62, 36, 54, 15, 3, 8, 17
  ],
  "modification_strategy": "This strategy prioritizes exploration of unexplored and opportunity regions by using a breadth-first approach to traverse the graph. I aimed to avoid the difficult regions by strategically choosing alternative connections while maintaining connectivity between nodes. The high diversity weight further encourages this exploration, leading to a significantly different path compared to the original.",
  "targeted_regions": [
    "Unexplored regions",
    "Opportunity regions"
  ]
}
```
2025-06-22 17:15:47,442 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:15:47,442 - ExplorationExpert - INFO - 探索路径生成完成，成本: 122200.0, 路径: [28, 2, 10, 25, 42, 33, 18, 51, 5, 12, 60, 48, 3, 21, 17, 38, 45, 9, 14, 63, 30, 23, 58, 1, 40, 53, 35, 6, 29, 44, 55, 7, 50, 22, 41, 16, 39, 19, 24, 59, 11, 20, 65, 13, 49, 4, 34, 32, 56, 57, 8, 47, 26, 31, 64, 46, 52, 37, 61, 27, 62, 36, 54, 15, 3, 8, 17]
2025-06-22 17:15:47,442 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [28, 2, 10, 25, 42, 33, 18, 51, 5, 12, 60, 48, 3, 21, 17, 38, 45, 9, 14, 63, 30, 23, 58, 1, 40, 53, 35, 6, 29, 44, 55, 7, 50, 22, 41, 16, 39, 19, 24, 59, 11, 20, 65, 13, 49, 4, 34, 32, 56, 57, 8, 47, 26, 31, 64, 46, 52, 37, 61, 27, 62, 36, 54, 15, 3, 8, 17], 'cur_cost': 122200.0}
2025-06-22 17:15:47,444 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-22 17:15:47,444 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:15:47,445 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [42, 54, 26] and ending with [8, 5, 3]
- Cost: 107165.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9388185654008439
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:15:47,445 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:15:50,983 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [42, 1, 29, 61, 12, 50, 37, 4, 58, 18, 31, 2, 45, 15, 64, 22, 53, 10, 28, 56, 39, 6, 48, 20, 35, 47, 11, 66, 24, 40, 55, 14, 32, 59, 7, 49, 19, 30, 3, 46, 25, 41, 9, 63, 21, 52, 17, 34, 60, 16, 33, 62, 23, 51, 13, 38, 5, 44, 27, 8, 57, 36, 65, 3, 4],
  "modification_strategy": "Heuristic re-routing favoring high-diversity nodes and prioritizing exploration of unexplored and opportunity regions, while attempting to avoid difficult regions. The path was constructed using a greedy approach, starting from node 42 and selecting the next node based on a weighted combination of diversity score, potential for region exploration, and avoidance of risk (difficult regions). The target was to deviate significantly from the original path by incorporating nodes from opportunity regions as early as possible. ",
  "targeted_regions": [
    "Unexplored Regions",
    "Opportunity Regions"
  ]
}
```
2025-06-22 17:15:50,983 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:15:50,983 - ExplorationExpert - INFO - 探索路径生成完成，成本: 1.3857969993223356e+44, 路径: [42, 1, 29, 61, 12, 50, 37, 4, 58, 18, 31, 2, 45, 15, 64, 22, 53, 10, 28, 56, 39, 6, 48, 20, 35, 47, 11, 66, 24, 40, 55, 14, 32, 59, 7, 49, 19, 30, 3, 46, 25, 41, 9, 63, 21, 52, 17, 34, 60, 16, 33, 62, 23, 51, 13, 38, 5, 44, 27, 8, 57, 36, 65, 3, 4]
2025-06-22 17:15:50,983 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [42, 1, 29, 61, 12, 50, 37, 4, 58, 18, 31, 2, 45, 15, 64, 22, 53, 10, 28, 56, 39, 6, 48, 20, 35, 47, 11, 66, 24, 40, 55, 14, 32, 59, 7, 49, 19, 30, 3, 46, 25, 41, 9, 63, 21, 52, 17, 34, 60, 16, 33, 62, 23, 51, 13, 38, 5, 44, 27, 8, 57, 36, 65, 3, 4], 'cur_cost': 1.3857969993223356e+44}
2025-06-22 17:15:50,983 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 1, 4, 8, 15, 22, 29, 36, 43, 50, 57, 64, 71, 78, 85, 92, 9, 16, 23, 30, 37, 44, 51, 58, 65, 72, 79, 86, 93, 10, 17, 24, 31, 38, 45, 52, 59, 66, 73, 80, 87, 94, 11, 18, 25, 32, 39, 46, 53, 60, 67, 74, 81, 88, 95, 12, 19, 26, 33, 40, 47, 54, 61, 68, 75, 82, 89, 96, 97, 98], 'cur_cost': 1.710431179386332e+214}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [44, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 63, 65, 67, 69, 71, 70, 68, 66, 64, 62, 60, 58, 56, 54, 52, 50, 48, 46, 42, 40, 38, 36, 34, 32, 30, 28, 26, 24, 22, 20, 18, 16, 14, 12, 10, 8, 6, 4, 1, 72], 'cur_cost': -1.4701865236778527e+82}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [19, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 6.805680462630222e+38}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [63, 1, 22, 35, 17, 48, 5, 11, 28, 3, 39, 52, 2, 29, 45, 14, 21, 58, 18, 50, 4, 62, 7, 32, 16, 26, 37, 55, 12, 23, 40, 59, 9, 30, 42, 53, 27, 41, 61], 'cur_cost': 63805.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [65, 47, 12, 1, 8, 25, 33, 18, 42, 50, 58, 6, 15, 29, 36, 4, 22, 30, 45, 52, 10, 21, 55], 'cur_cost': 30270.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [42, 9, 3, 15, 21, 33, 45, 57, 6, 18, 30, 40, 52, 64, 1, 13, 25, 37, 49, 61, 32, 60], 'cur_cost': 35761.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [22, 1, 44, 25, 62, 48, 17, 9, 6, 53, 3, 38, 10, 42, 12, 35, 27, 29, 40, 60, 57, 21, 18, 59, 51, 14, 23, 47, 36, 30, 26, 55, 49, 16, 34], 'cur_cost': 53150.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [9, 27, 15, 48, 3, 35, 50, 12, 42, 60, 2, 21, 57, 4, 38, 18, 51, 24, 1, 29, 64, 45, 10, 32, 54, 7, 23, 40, 16, 59, 20, 56], 'cur_cost': 59138.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [28, 2, 10, 25, 42, 33, 18, 51, 5, 12, 60, 48, 3, 21, 17, 38, 45, 9, 14, 63, 30, 23, 58, 1, 40, 53, 35, 6, 29, 44, 55, 7, 50, 22, 41, 16, 39, 19, 24, 59, 11, 20, 65, 13, 49, 4, 34, 32, 56, 57, 8, 47, 26, 31, 64, 46, 52, 37, 61, 27, 62, 36, 54, 15, 3, 8, 17], 'cur_cost': 122200.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [42, 1, 29, 61, 12, 50, 37, 4, 58, 18, 31, 2, 45, 15, 64, 22, 53, 10, 28, 56, 39, 6, 48, 20, 35, 47, 11, 66, 24, 40, 55, 14, 32, 59, 7, 49, 19, 30, 3, 46, 25, 41, 9, 63, 21, 52, 17, 34, 60, 16, 33, 62, 23, 51, 13, 38, 5, 44, 27, 8, 57, 36, 65, 3, 4], 'cur_cost': 1.3857969993223356e+44}}]
2025-06-22 17:15:50,983 - __main__ - INFO - 进化阶段完成
2025-06-22 17:15:50,983 - __main__ - INFO - 开始评估阶段
2025-06-22 17:15:50,983 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 17:15:50,983 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 17:15:50,983 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 1/5
- Population changes:
  * Cost: min 18914.0->-1.4701865236778527e+82, mean 1.1109497412598175e+271->1.710431179386332e+213
  * Diversity: 0.8248299319727891->0.9492600422832981
  * Elite solutions: 1->0

## Strategy Performance:
- Explore: 10 individuals, success rate 60.0%, avg improvement 1.1109497412598173e+271
- Exploit: 0 individuals, success rate 0%, avg improvement 0
- Overall improvement: 1.4701865236778527e+82

## Historical Trends:
Last 1 iterations: costs [18914.0], diversity [0.9883059450223628]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 17:15:55,760 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Rapid Improvement (Initial Stage)",
    "features": "Significant cost reduction (from positive to massive negative values) and increasing diversity indicate a highly effective exploration phase, finding promising regions of the search space. The lack of elite solutions suggests the current solutions haven't yet converged towards a definitive optimal point. High Exploration Success Rate, and rapid improvement from the initial state confirms early positive momentum."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Highly Effective. The explore strategy is achieving a high success rate (60%) and is driving the vast majority of the overall improvement (1.47e+82)",
      "details": "Exploring new areas of the search space, resulting in large improvements in cost and increases in diversity, signifying positive progress. "
    },
    "exploit": {
      "performance": "Ineffective. No individuals were employed in this stage, and therefore no information can be drawn from this area.",
      "details": "No exploitation attempts were made, which is expected given the early stages of the optimization. No focus on using proven solutions to enhance and improve current options."
    }
  },
  "balance_state": {
    "assessment": "Strongly skewed towards Exploration.",
    "adjustment_needs": "The current phase is correctly dominated by exploration, finding regions of better solution space. Given the rapid changes in cost, some exploitation to fine-tune promising solutions may be considered at some point during the next iterations, but focus on exploration should be maintained for the near future."
  },
  "recommendations": {
    "next_iteration": [
      "Maintain a strong focus on Exploration. Continue utilizing the Explore strategy at a similar scale (e.g., ~10 individuals), given its strong performance.",
      "Consider introducing a limited Exploitation strategy, perhaps with 1 or 2 individuals, to test if you can extract additional gains from current solutions. This may be premature, and is not critical in this iteration given the progress made.",
      "Monitor the diversity metric closely. As diversity plateaus or decreases, indicate potential saturation, indicating the point to gradually introduce Exploitation.",
      "Analyze solutions found during exploration. Evaluate and understand the characteristics of high performing solutions to guide the next iteration's approach.",
      "Evaluate any significant changes in diversity rates, as this may show the solution converges in certain regions."
    ]
  }
}
```
2025-06-22 17:15:55,764 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 17:15:55,765 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Rapid Improvement (Initial Stage)",
    "features": "Significant cost reduction (from positive to massive negative values) and increasing diversity indicate a highly effective exploration phase, finding promising regions of the search space. The lack of elite solutions suggests the current solutions haven't yet converged towards a definitive optimal point. High Exploration Success Rate, and rapid improvement from the initial state confirms early positive momentum."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Highly Effective. The explore strategy is achieving a high success rate (60%) and is driving the vast majority of the overall improvement (1.47e+82)",
      "details": "Exploring new areas of the search space, resulting in large improvements in cost and increases in diversity, signifying positive progress. "
    },
    "exploit": {
      "performance": "Ineffective. No individuals were employed in this stage, and therefore no information can be drawn from this area.",
      "details": "No exploitation attempts were made, which is expected given the early stages of the optimization. No focus on using proven solutions to enhance and improve current options."
    }
  },
  "balance_state": {
    "assessment": "Strongly skewed towards Exploration.",
    "adjustment_needs": "The current phase is correctly dominated by exploration, finding regions of better solution space. Given the rapid changes in cost, some exploitation to fine-tune promising solutions may be considered at some point during the next iterations, but focus on exploration should be maintained for the near future."
  },
  "recommendations": {
    "next_iteration": [
      "Maintain a strong focus on Exploration. Continue utilizing the Explore strategy at a similar scale (e.g., ~10 individuals), given its strong performance.",
      "Consider introducing a limited Exploitation strategy, perhaps with 1 or 2 individuals, to test if you can extract additional gains from current solutions. This may be premature, and is not critical in this iteration given the progress made.",
      "Monitor the diversity metric closely. As diversity plateaus or decreases, indicate potential saturation, indicating the point to gradually introduce Exploitation.",
      "Analyze solutions found during exploration. Evaluate and understand the characteristics of high performing solutions to guide the next iteration's approach.",
      "Evaluate any significant changes in diversity rates, as this may show the solution converges in certain regions."
    ]
  }
}
```
2025-06-22 17:15:55,765 - __main__ - INFO - 评估阶段完成
2025-06-22 17:15:55,765 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Rapid Improvement (Initial Stage)",
    "features": "Significant cost reduction (from positive to massive negative values) and increasing diversity indicate a highly effective exploration phase, finding promising regions of the search space. The lack of elite solutions suggests the current solutions haven't yet converged towards a definitive optimal point. High Exploration Success Rate, and rapid improvement from the initial state confirms early positive momentum."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Highly Effective. The explore strategy is achieving a high success rate (60%) and is driving the vast majority of the overall improvement (1.47e+82)",
      "details": "Exploring new areas of the search space, resulting in large improvements in cost and increases in diversity, signifying positive progress. "
    },
    "exploit": {
      "performance": "Ineffective. No individuals were employed in this stage, and therefore no information can be drawn from this area.",
      "details": "No exploitation attempts were made, which is expected given the early stages of the optimization. No focus on using proven solutions to enhance and improve current options."
    }
  },
  "balance_state": {
    "assessment": "Strongly skewed towards Exploration.",
    "adjustment_needs": "The current phase is correctly dominated by exploration, finding regions of better solution space. Given the rapid changes in cost, some exploitation to fine-tune promising solutions may be considered at some point during the next iterations, but focus on exploration should be maintained for the near future."
  },
  "recommendations": {
    "next_iteration": [
      "Maintain a strong focus on Exploration. Continue utilizing the Explore strategy at a similar scale (e.g., ~10 individuals), given its strong performance.",
      "Consider introducing a limited Exploitation strategy, perhaps with 1 or 2 individuals, to test if you can extract additional gains from current solutions. This may be premature, and is not critical in this iteration given the progress made.",
      "Monitor the diversity metric closely. As diversity plateaus or decreases, indicate potential saturation, indicating the point to gradually introduce Exploitation.",
      "Analyze solutions found during exploration. Evaluate and understand the characteristics of high performing solutions to guide the next iteration's approach.",
      "Evaluate any significant changes in diversity rates, as this may show the solution converges in certain regions."
    ]
  }
}
```
2025-06-22 17:15:55,766 - __main__ - INFO - 当前最佳适应度: -1.4701865236778527e+82
2025-06-22 17:15:55,768 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_1.pkl
2025-06-22 17:15:55,768 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-06-22 17:15:55,768 - __main__ - INFO - 开始分析阶段
2025-06-22 17:15:55,768 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:15:55,770 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': -1.4701865236778527e+82, 'max': 1.710431179386332e+214, 'mean': 1.710431179386332e+213, 'std': inf}, 'diversity': 1.0, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:15:55,770 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': -1.4701865236778527e+82, 'max': 1.710431179386332e+214, 'mean': 1.710431179386332e+213, 'std': inf}, 'diversity_level': 1.0, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:15:55,770 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:15:55,771 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:15:55,771 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:15:55,771 - PathExpert - WARNING - 发现无效的城市索引: [71, 78, 85, 92, 72, 79, 86, 93, 66, 73, 80, 87, 94, 67, 74, 81, 88, 95, 68, 75, 82, 89, 96, 97, 98], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:15:55,772 - PathExpert - WARNING - 发现无效的城市索引: [67, 69, 71, 70, 68, 66, 72], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:15:55,772 - PathExpert - WARNING - 发现无效的城市索引: [66, 67], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:15:55,772 - PathExpert - WARNING - 发现无效的城市索引: [66], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:15:55,773 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=19, city2=16, tour_length=58
2025-06-22 17:15:55,773 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=16, city2=17, tour_length=58
2025-06-22 17:15:55,774 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=17, city2=18, tour_length=58
2025-06-22 17:15:55,774 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=21, tour_length=58
2025-06-22 17:15:55,774 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=22, tour_length=58
2025-06-22 17:15:55,774 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=23, tour_length=58
2025-06-22 17:15:55,775 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=23, city2=24, tour_length=58
2025-06-22 17:15:55,775 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=25, tour_length=58
2025-06-22 17:15:55,775 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=26, tour_length=58
2025-06-22 17:15:55,775 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=26, city2=27, tour_length=58
2025-06-22 17:15:55,775 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=28, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=28, city2=29, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=30, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=31, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=31, city2=32, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=33, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=34, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=34, city2=35, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=36, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=36, city2=37, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=37, city2=38, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=39, tour_length=58
2025-06-22 17:15:55,776 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=39, city2=40, tour_length=58
2025-06-22 17:15:55,777 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=41, tour_length=58
2025-06-22 17:15:55,777 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=41, city2=42, tour_length=58
2025-06-22 17:15:55,777 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=43, tour_length=58
2025-06-22 17:15:55,777 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=43, city2=44, tour_length=58
2025-06-22 17:15:55,777 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=44, city2=45, tour_length=58
2025-06-22 17:15:55,777 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=46, tour_length=58
2025-06-22 17:15:55,777 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=46, city2=47, tour_length=58
2025-06-22 17:15:55,778 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=48, tour_length=58
2025-06-22 17:15:55,778 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=49, tour_length=58
2025-06-22 17:15:55,778 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=50, tour_length=58
2025-06-22 17:15:55,778 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=51, tour_length=58
2025-06-22 17:15:55,778 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=51, city2=52, tour_length=58
2025-06-22 17:15:55,778 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=53, tour_length=58
2025-06-22 17:15:55,779 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=53, city2=54, tour_length=58
2025-06-22 17:15:55,779 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=54, city2=55, tour_length=58
2025-06-22 17:15:55,779 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=56, tour_length=58
2025-06-22 17:15:55,779 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=56, city2=57, tour_length=58
2025-06-22 17:15:55,779 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=57, city2=58, tour_length=58
2025-06-22 17:15:55,779 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=59, tour_length=58
2025-06-22 17:15:55,779 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=60, tour_length=58
2025-06-22 17:15:55,779 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=61, tour_length=58
2025-06-22 17:15:55,779 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=61, city2=62, tour_length=58
2025-06-22 17:15:55,779 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=62, city2=63, tour_length=58
2025-06-22 17:15:55,779 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=63, city2=64, tour_length=58
2025-06-22 17:15:55,781 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=64, city2=65, tour_length=58
2025-06-22 17:15:55,781 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=65, city2=0, tour_length=58
2025-06-22 17:15:55,781 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=0, city2=1, tour_length=58
2025-06-22 17:15:55,781 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=6, tour_length=58
2025-06-22 17:15:55,781 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=5, tour_length=58
2025-06-22 17:15:55,781 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=4, tour_length=58
2025-06-22 17:15:55,782 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=3, tour_length=58
2025-06-22 17:15:55,791 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=2, tour_length=58
2025-06-22 17:15:55,791 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=1, tour_length=58
2025-06-22 17:15:55,791 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=0, tour_length=58
2025-06-22 17:15:55,792 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=0, city2=19, tour_length=58
2025-06-22 17:15:55,792 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=63, city2=1, tour_length=39
2025-06-22 17:15:55,792 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=22, tour_length=39
2025-06-22 17:15:55,792 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=35, tour_length=39
2025-06-22 17:15:55,792 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=17, tour_length=39
2025-06-22 17:15:55,792 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=17, city2=48, tour_length=39
2025-06-22 17:15:55,792 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=5, tour_length=39
2025-06-22 17:15:55,793 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=11, tour_length=39
2025-06-22 17:15:55,793 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=28, tour_length=39
2025-06-22 17:15:55,793 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=28, city2=3, tour_length=39
2025-06-22 17:15:55,793 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=39, tour_length=39
2025-06-22 17:15:55,793 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=39, city2=52, tour_length=39
2025-06-22 17:15:55,793 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=2, tour_length=39
2025-06-22 17:15:55,793 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=29, tour_length=39
2025-06-22 17:15:55,794 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=45, tour_length=39
2025-06-22 17:15:55,794 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=14, tour_length=39
2025-06-22 17:15:55,794 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=14, city2=21, tour_length=39
2025-06-22 17:15:55,794 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=58, tour_length=39
2025-06-22 17:15:55,794 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=18, tour_length=39
2025-06-22 17:15:55,794 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=50, tour_length=39
2025-06-22 17:15:55,794 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=4, tour_length=39
2025-06-22 17:15:55,795 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=62, tour_length=39
2025-06-22 17:15:55,795 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=62, city2=7, tour_length=39
2025-06-22 17:15:55,795 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=7, city2=32, tour_length=39
2025-06-22 17:15:55,795 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=16, tour_length=39
2025-06-22 17:15:55,795 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=16, city2=26, tour_length=39
2025-06-22 17:15:55,795 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=26, city2=37, tour_length=39
2025-06-22 17:15:55,796 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=37, city2=55, tour_length=39
2025-06-22 17:15:55,796 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=12, tour_length=39
2025-06-22 17:15:55,796 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=23, tour_length=39
2025-06-22 17:15:55,796 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=23, city2=40, tour_length=39
2025-06-22 17:15:55,796 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=59, tour_length=39
2025-06-22 17:15:55,796 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=9, tour_length=39
2025-06-22 17:15:55,796 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=30, tour_length=39
2025-06-22 17:15:55,796 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=42, tour_length=39
2025-06-22 17:15:55,797 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=53, tour_length=39
2025-06-22 17:15:55,797 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=53, city2=27, tour_length=39
2025-06-22 17:15:55,797 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=41, tour_length=39
2025-06-22 17:15:55,797 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=41, city2=61, tour_length=39
2025-06-22 17:15:55,797 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=61, city2=63, tour_length=39
2025-06-22 17:15:55,797 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=65, city2=47, tour_length=23
2025-06-22 17:15:55,797 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=12, tour_length=23
2025-06-22 17:15:55,798 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=1, tour_length=23
2025-06-22 17:15:55,798 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=8, tour_length=23
2025-06-22 17:15:55,799 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=8, city2=25, tour_length=23
2025-06-22 17:15:55,799 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=33, tour_length=23
2025-06-22 17:15:55,799 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=18, tour_length=23
2025-06-22 17:15:55,799 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=42, tour_length=23
2025-06-22 17:15:55,799 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=50, tour_length=23
2025-06-22 17:15:55,800 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=58, tour_length=23
2025-06-22 17:15:55,800 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=6, tour_length=23
2025-06-22 17:15:55,800 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=15, tour_length=23
2025-06-22 17:15:55,800 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=29, tour_length=23
2025-06-22 17:15:55,800 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=36, tour_length=23
2025-06-22 17:15:55,800 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=36, city2=4, tour_length=23
2025-06-22 17:15:55,800 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=22, tour_length=23
2025-06-22 17:15:55,800 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=30, tour_length=23
2025-06-22 17:15:55,800 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=45, tour_length=23
2025-06-22 17:15:55,800 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=52, tour_length=23
2025-06-22 17:15:55,801 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=10, tour_length=23
2025-06-22 17:15:55,801 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=21, tour_length=23
2025-06-22 17:15:55,801 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=55, tour_length=23
2025-06-22 17:15:55,801 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=65, tour_length=23
2025-06-22 17:15:55,801 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=9, tour_length=22
2025-06-22 17:15:55,801 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=3, tour_length=22
2025-06-22 17:15:55,801 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=15, tour_length=22
2025-06-22 17:15:55,802 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=21, tour_length=22
2025-06-22 17:15:55,802 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=33, tour_length=22
2025-06-22 17:15:55,802 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=45, tour_length=22
2025-06-22 17:15:55,802 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=57, tour_length=22
2025-06-22 17:15:55,802 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=57, city2=6, tour_length=22
2025-06-22 17:15:55,802 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=18, tour_length=22
2025-06-22 17:15:55,803 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=30, tour_length=22
2025-06-22 17:15:55,803 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=40, tour_length=22
2025-06-22 17:15:55,803 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=52, tour_length=22
2025-06-22 17:15:55,803 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=64, tour_length=22
2025-06-22 17:15:55,803 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=64, city2=1, tour_length=22
2025-06-22 17:15:55,803 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=13, tour_length=22
2025-06-22 17:15:55,804 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=13, city2=25, tour_length=22
2025-06-22 17:15:55,804 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=37, tour_length=22
2025-06-22 17:15:55,804 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=37, city2=49, tour_length=22
2025-06-22 17:15:55,804 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=61, tour_length=22
2025-06-22 17:15:55,804 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=61, city2=32, tour_length=22
2025-06-22 17:15:55,804 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=60, tour_length=22
2025-06-22 17:15:55,805 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=42, tour_length=22
2025-06-22 17:15:55,805 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=1, tour_length=35
2025-06-22 17:15:55,805 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=44, tour_length=35
2025-06-22 17:15:55,806 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=44, city2=25, tour_length=35
2025-06-22 17:15:55,806 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=62, tour_length=35
2025-06-22 17:15:55,806 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=62, city2=48, tour_length=35
2025-06-22 17:15:55,806 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=17, tour_length=35
2025-06-22 17:15:55,806 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=17, city2=9, tour_length=35
2025-06-22 17:15:55,807 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=6, tour_length=35
2025-06-22 17:15:55,807 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=53, tour_length=35
2025-06-22 17:15:55,807 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=53, city2=3, tour_length=35
2025-06-22 17:15:55,807 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=38, tour_length=35
2025-06-22 17:15:55,808 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=10, tour_length=35
2025-06-22 17:15:55,808 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=42, tour_length=35
2025-06-22 17:15:55,808 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=12, tour_length=35
2025-06-22 17:15:55,808 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=35, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=27, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=29, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=40, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=60, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=57, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=57, city2=21, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=18, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=59, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=51, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=51, city2=14, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=14, city2=23, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=23, city2=47, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=36, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=36, city2=30, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=26, tour_length=35
2025-06-22 17:15:55,809 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=26, city2=55, tour_length=35
2025-06-22 17:15:55,811 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=49, tour_length=35
2025-06-22 17:15:55,811 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=16, tour_length=35
2025-06-22 17:15:55,811 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=16, city2=34, tour_length=35
2025-06-22 17:15:55,811 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=34, city2=22, tour_length=35
2025-06-22 17:15:55,811 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=27, tour_length=32
2025-06-22 17:15:55,811 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=15, tour_length=32
2025-06-22 17:15:55,812 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=48, tour_length=32
2025-06-22 17:15:55,812 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=3, tour_length=32
2025-06-22 17:15:55,812 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=35, tour_length=32
2025-06-22 17:15:55,812 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=50, tour_length=32
2025-06-22 17:15:55,812 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=12, tour_length=32
2025-06-22 17:15:55,812 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=42, tour_length=32
2025-06-22 17:15:55,812 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=60, tour_length=32
2025-06-22 17:15:55,813 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=2, tour_length=32
2025-06-22 17:15:55,813 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=21, tour_length=32
2025-06-22 17:15:55,813 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=57, tour_length=32
2025-06-22 17:15:55,813 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=57, city2=4, tour_length=32
2025-06-22 17:15:55,813 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=38, tour_length=32
2025-06-22 17:15:55,813 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=18, tour_length=32
2025-06-22 17:15:55,813 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=51, tour_length=32
2025-06-22 17:15:55,814 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=51, city2=24, tour_length=32
2025-06-22 17:15:55,814 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=1, tour_length=32
2025-06-22 17:15:55,814 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=29, tour_length=32
2025-06-22 17:15:55,814 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=64, tour_length=32
2025-06-22 17:15:55,814 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=64, city2=45, tour_length=32
2025-06-22 17:15:55,814 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=10, tour_length=32
2025-06-22 17:15:55,814 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=32, tour_length=32
2025-06-22 17:15:55,815 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=54, tour_length=32
2025-06-22 17:15:55,816 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=54, city2=7, tour_length=32
2025-06-22 17:15:55,816 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=7, city2=23, tour_length=32
2025-06-22 17:15:55,816 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=23, city2=40, tour_length=32
2025-06-22 17:15:55,816 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=16, tour_length=32
2025-06-22 17:15:55,816 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=16, city2=59, tour_length=32
2025-06-22 17:15:55,816 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=20, tour_length=32
2025-06-22 17:15:55,816 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=20, city2=56, tour_length=32
2025-06-22 17:15:55,817 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=56, city2=9, tour_length=32
2025-06-22 17:15:55,817 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=1, tour_length=65
2025-06-22 17:15:55,817 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=29, tour_length=65
2025-06-22 17:15:55,817 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=61, tour_length=65
2025-06-22 17:15:55,817 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=61, city2=12, tour_length=65
2025-06-22 17:15:55,817 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=50, tour_length=65
2025-06-22 17:15:55,817 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=37, tour_length=65
2025-06-22 17:15:55,817 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=37, city2=4, tour_length=65
2025-06-22 17:15:55,817 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=58, tour_length=65
2025-06-22 17:15:55,817 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=18, tour_length=65
2025-06-22 17:15:55,819 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=31, tour_length=65
2025-06-22 17:15:55,819 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=31, city2=2, tour_length=65
2025-06-22 17:15:55,819 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=45, tour_length=65
2025-06-22 17:15:55,819 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=15, tour_length=65
2025-06-22 17:15:55,819 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=64, tour_length=65
2025-06-22 17:15:55,820 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=64, city2=22, tour_length=65
2025-06-22 17:15:55,820 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=53, tour_length=65
2025-06-22 17:15:55,820 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=53, city2=10, tour_length=65
2025-06-22 17:15:55,820 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=28, tour_length=65
2025-06-22 17:15:55,820 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=28, city2=56, tour_length=65
2025-06-22 17:15:55,820 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=56, city2=39, tour_length=65
2025-06-22 17:15:55,821 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=39, city2=6, tour_length=65
2025-06-22 17:15:55,821 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=48, tour_length=65
2025-06-22 17:15:55,821 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=20, tour_length=65
2025-06-22 17:15:55,821 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=20, city2=35, tour_length=65
2025-06-22 17:15:55,821 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=47, tour_length=65
2025-06-22 17:15:55,821 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=11, tour_length=65
2025-06-22 17:15:55,821 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=0, tour_length=65
2025-06-22 17:15:55,821 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=0, city2=24, tour_length=65
2025-06-22 17:15:55,821 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=40, tour_length=65
2025-06-22 17:15:55,821 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=55, tour_length=65
2025-06-22 17:15:55,821 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=14, tour_length=65
2025-06-22 17:15:55,823 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=14, city2=32, tour_length=65
2025-06-22 17:15:55,823 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=59, tour_length=65
2025-06-22 17:15:55,823 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=7, tour_length=65
2025-06-22 17:15:55,823 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=7, city2=49, tour_length=65
2025-06-22 17:15:55,823 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=19, tour_length=65
2025-06-22 17:15:55,823 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=19, city2=30, tour_length=65
2025-06-22 17:15:55,823 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=3, tour_length=65
2025-06-22 17:15:55,823 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=46, tour_length=65
2025-06-22 17:15:55,824 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=46, city2=25, tour_length=65
2025-06-22 17:15:55,824 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=41, tour_length=65
2025-06-22 17:15:55,824 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=41, city2=9, tour_length=65
2025-06-22 17:15:55,825 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=63, tour_length=65
2025-06-22 17:15:55,825 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=63, city2=21, tour_length=65
2025-06-22 17:15:55,825 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=52, tour_length=65
2025-06-22 17:15:55,825 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=17, tour_length=65
2025-06-22 17:15:55,825 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=17, city2=34, tour_length=65
2025-06-22 17:15:55,825 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=34, city2=60, tour_length=65
2025-06-22 17:15:55,825 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=16, tour_length=65
2025-06-22 17:15:55,825 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=16, city2=33, tour_length=65
2025-06-22 17:15:55,825 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=62, tour_length=65
2025-06-22 17:15:55,825 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=62, city2=23, tour_length=65
2025-06-22 17:15:55,826 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=23, city2=51, tour_length=65
2025-06-22 17:15:55,826 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=51, city2=13, tour_length=65
2025-06-22 17:15:55,826 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=13, city2=38, tour_length=65
2025-06-22 17:15:55,826 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=5, tour_length=65
2025-06-22 17:15:55,826 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=44, tour_length=65
2025-06-22 17:15:55,826 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=44, city2=27, tour_length=65
2025-06-22 17:15:55,827 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=8, tour_length=65
2025-06-22 17:15:55,827 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=8, city2=57, tour_length=65
2025-06-22 17:15:55,827 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=57, city2=36, tour_length=65
2025-06-22 17:15:55,827 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=36, city2=65, tour_length=65
2025-06-22 17:15:55,827 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=65, city2=3, tour_length=65
2025-06-22 17:15:55,827 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=4, tour_length=65
2025-06-22 17:15:55,828 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=42, tour_length=65
2025-06-22 17:15:55,829 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:15:55,829 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [], 'low_frequency_edges': [{'edge': '(1, 6)', 'frequency': 0.2}, {'edge': '(1, 4)', 'frequency': 0.2}, {'edge': '(8, 15)', 'frequency': 0.2}, {'edge': '(15, 22)', 'frequency': 0.2}, {'edge': '(22, 29)', 'frequency': 0.2}, {'edge': '(5, 12)', 'frequency': 0.2}, {'edge': '(12, 19)', 'frequency': 0.2}, {'edge': '(19, 26)', 'frequency': 0.2}, {'edge': '(9, 16)', 'frequency': 0.2}, {'edge': '(16, 23)', 'frequency': 0.2}, {'edge': '(23, 30)', 'frequency': 0.3}, {'edge': '(38, 45)', 'frequency': 0.2}, {'edge': '(3, 5)', 'frequency': 0.2}, {'edge': '(32, 34)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [31, 64, 46, 52, 37, 61, 27, 62, 36, 54, 15], 'cost': 28072.0, 'size': 11}, {'region': [51, 24, 1, 29, 64, 45, 10, 32, 54], 'cost': 19606.0, 'size': 9}, {'region': [1, 40, 53, 35, 6, 29, 44, 55], 'cost': 17272.0, 'size': 8}, {'region': [9, 30, 42, 53, 27, 41, 61], 'cost': 14840.0, 'size': 7}, {'region': [37, 49, 61, 32, 60, 42], 'cost': 13674.0, 'size': 6}]}
2025-06-22 17:15:55,830 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:15:55,830 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:15:55,830 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:15:55,831 - EliteExpert - INFO - 精英解分析完成
2025-06-22 17:15:55,831 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {'(22, 12)': 1.0, '(12, 17)': 1.0, '(20, 21)': 1.0, '(62, 59)': 1.0, '(59, 56)': 1.0}, 'common_edge_ratio': 0.07575757575757576}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': -1.4701865236778527e+82, 'avg_gap': 1.710431179386332e+213}, 'structure_gap': {'unique_elite_edges': 125, 'unique_pop_edges': 441, 'common_edges': 29}}, 'elite_diversity': {'diversity_score': 0.3939393939393939}}
2025-06-22 17:15:55,831 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:15:55,832 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:15:55,832 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:15:55,832 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=-1.4701865236778527e+82, max=1.710431179386332e+214, mean=1.710431179386332e+213, std=inf
- Diversity: 1.0, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 5 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:16:02,202 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "High. The extreme range of costs (min to max spanning many orders of magnitude) and infinite standard deviation suggest a highly uneven and likely rugged search space. The absence of identified structure further supports this, as no high quality edges or common subpaths were found. This suggests many local optima.",
    "modality": "Potentially high. The vast cost range and the presence of difficult regions (even if only 5 identified), indicate a multi-modal landscape where different regions might contain local optima.",
    "other": "The lack of structure (0 high quality edges, 0 common subpaths) suggests a highly unstructured and potentially noisy search space."
  },
  "population_state": {
    "diversity": "High (1.0). The diversity score of 1.0 implies the population explores a diverse range of the search space, perhaps due to the difficulty in finding high quality solutions.",
    "convergence": "Very Low (0.0). The convergence score of 0.0 indicates that the population is not converging towards any specific region. This is consistent with the vast cost range and the difficulty of the problem.",
    "elite_performance": "Poor. Zero elite solutions with no fixed nodes point to the lack of high-performing solutions, and therefore the inability to identify any kind of high-quality solution."
  },
  "difficult_regions": {
    "description": "5 difficult regions are identified. This suggests the algorithm encounters challenges when navigating specific areas of the search space. The high cost variance could be due to the exploration of these regions.",
    "challenges": "Likely local optima, complex landscapes with potential for deceptive or long-range dependencies between parameters. The algorithm may be easily trapped in these difficult regions.",
    "mitigation": "Requires careful strategies to escape local optima, such as increased mutation rates, diversification mechanisms, or population diversity to escape those regions."
  },
  "opportunity_regions": {
    "description": "0 opportunity regions identified. This suggests that no easily exploitable areas of the search space have been found yet.",
    "potential": "None identified, given the current information. This could be due to the vast search space and the difficulty of finding solutions, and/or the algorithm has not found any areas with high performance yet."
  },
  "evolution_direction": {
    "strategy": "Diversification and Exploration. Given the high diversity, low convergence, and a vast search space, the focus should be on exploration. Since no elite solutions exist, there's no clear local optimum to exploit yet. ",
    "recommendations": [
      "Increase mutation rate to promote exploration and escape potential local optima, with a focus on finding good starting locations.",
      "Employ diversity-preserving mechanisms (e.g., crowding, sharing) to maintain population diversity.",
      "Consider increasing the population size to explore the vast search space.",
      "Implement an adaptive mutation strategy (e.g. a higher mutation rate at the beginning) as a function of performance. A higher mutation rate could allow the population to escape from local optima.",
      "Focus on identifying and classifying the features of the identified difficult regions. These could be used to refine the search through a variety of methods."
    ]
  }
}
```

2025-06-22 17:16:02,202 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:16:02,202 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'High. The extreme range of costs (min to max spanning many orders of magnitude) and infinite standard deviation suggest a highly uneven and likely rugged search space. The absence of identified structure further supports this, as no high quality edges or common subpaths were found. This suggests many local optima.', 'modality': 'Potentially high. The vast cost range and the presence of difficult regions (even if only 5 identified), indicate a multi-modal landscape where different regions might contain local optima.', 'other': 'The lack of structure (0 high quality edges, 0 common subpaths) suggests a highly unstructured and potentially noisy search space.'}, 'population_state': {'diversity': 'High (1.0). The diversity score of 1.0 implies the population explores a diverse range of the search space, perhaps due to the difficulty in finding high quality solutions.', 'convergence': 'Very Low (0.0). The convergence score of 0.0 indicates that the population is not converging towards any specific region. This is consistent with the vast cost range and the difficulty of the problem.', 'elite_performance': 'Poor. Zero elite solutions with no fixed nodes point to the lack of high-performing solutions, and therefore the inability to identify any kind of high-quality solution.'}, 'difficult_regions': {'description': '5 difficult regions are identified. This suggests the algorithm encounters challenges when navigating specific areas of the search space. The high cost variance could be due to the exploration of these regions.', 'challenges': 'Likely local optima, complex landscapes with potential for deceptive or long-range dependencies between parameters. The algorithm may be easily trapped in these difficult regions.', 'mitigation': 'Requires careful strategies to escape local optima, such as increased mutation rates, diversification mechanisms, or population diversity to escape those regions.'}, 'opportunity_regions': {'description': '0 opportunity regions identified. This suggests that no easily exploitable areas of the search space have been found yet.', 'potential': 'None identified, given the current information. This could be due to the vast search space and the difficulty of finding solutions, and/or the algorithm has not found any areas with high performance yet.'}, 'evolution_direction': {'strategy': "Diversification and Exploration. Given the high diversity, low convergence, and a vast search space, the focus should be on exploration. Since no elite solutions exist, there's no clear local optimum to exploit yet. ", 'recommendations': ['Increase mutation rate to promote exploration and escape potential local optima, with a focus on finding good starting locations.', 'Employ diversity-preserving mechanisms (e.g., crowding, sharing) to maintain population diversity.', 'Consider increasing the population size to explore the vast search space.', 'Implement an adaptive mutation strategy (e.g. a higher mutation rate at the beginning) as a function of performance. A higher mutation rate could allow the population to escape from local optima.', 'Focus on identifying and classifying the features of the identified difficult regions. These could be used to refine the search through a variety of methods.']}}
2025-06-22 17:16:02,202 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:16:02,202 - __main__ - INFO - 分析阶段完成
2025-06-22 17:16:02,202 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'High. The extreme range of costs (min to max spanning many orders of magnitude) and infinite standard deviation suggest a highly uneven and likely rugged search space. The absence of identified structure further supports this, as no high quality edges or common subpaths were found. This suggests many local optima.', 'modality': 'Potentially high. The vast cost range and the presence of difficult regions (even if only 5 identified), indicate a multi-modal landscape where different regions might contain local optima.', 'other': 'The lack of structure (0 high quality edges, 0 common subpaths) suggests a highly unstructured and potentially noisy search space.'}, 'population_state': {'diversity': 'High (1.0). The diversity score of 1.0 implies the population explores a diverse range of the search space, perhaps due to the difficulty in finding high quality solutions.', 'convergence': 'Very Low (0.0). The convergence score of 0.0 indicates that the population is not converging towards any specific region. This is consistent with the vast cost range and the difficulty of the problem.', 'elite_performance': 'Poor. Zero elite solutions with no fixed nodes point to the lack of high-performing solutions, and therefore the inability to identify any kind of high-quality solution.'}, 'difficult_regions': {'description': '5 difficult regions are identified. This suggests the algorithm encounters challenges when navigating specific areas of the search space. The high cost variance could be due to the exploration of these regions.', 'challenges': 'Likely local optima, complex landscapes with potential for deceptive or long-range dependencies between parameters. The algorithm may be easily trapped in these difficult regions.', 'mitigation': 'Requires careful strategies to escape local optima, such as increased mutation rates, diversification mechanisms, or population diversity to escape those regions.'}, 'opportunity_regions': {'description': '0 opportunity regions identified. This suggests that no easily exploitable areas of the search space have been found yet.', 'potential': 'None identified, given the current information. This could be due to the vast search space and the difficulty of finding solutions, and/or the algorithm has not found any areas with high performance yet.'}, 'evolution_direction': {'strategy': "Diversification and Exploration. Given the high diversity, low convergence, and a vast search space, the focus should be on exploration. Since no elite solutions exist, there's no clear local optimum to exploit yet. ", 'recommendations': ['Increase mutation rate to promote exploration and escape potential local optima, with a focus on finding good starting locations.', 'Employ diversity-preserving mechanisms (e.g., crowding, sharing) to maintain population diversity.', 'Consider increasing the population size to explore the vast search space.', 'Implement an adaptive mutation strategy (e.g. a higher mutation rate at the beginning) as a function of performance. A higher mutation rate could allow the population to escape from local optima.', 'Focus on identifying and classifying the features of the identified difficult regions. These could be used to refine the search through a variety of methods.']}}
2025-06-22 17:16:02,206 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:16:02,206 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:16:02,206 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 2
- Population: 10 individuals, diversity=High (1.0). The diversity score of 1.0 implies the population explores a diverse range of the search space, perhaps due to the difficulty in finding high quality solutions.
- Best cost: -1.4701865236778527e+82, Mean cost: 1.710431179386332e+213
- Search space: Ruggedness: High. The extreme range of costs (min to max spanning many orders of magnitude) and infinite standard deviation suggest a highly uneven and likely rugged search space. The absence of identified structure further supports this, as no high quality edges or common subpaths were found. This suggests many local optima., Modality: Potentially high. The vast cost range and the presence of difficult regions (even if only 5 identified), indicate a multi-modal landscape where different regions might contain local optima.
- Difficult regions: 3 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Rapid Improvement (Initial Stage)",
    "features": "Significant cost reduction (from positive to massive negative values) and increasing diversity indic...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:16:02,206 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:16:04,924 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.3,
      "step_size_scaling": 1.2,
      "region_focus_probability": 0.4  // Focus more on difficult/opportunity regions.
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size_scaling": 0.8,
      "local_search_iterations": 10 // Perform a few local search steps
    }
  }
}
```

2025-06-22 17:16:04,924 - StrategyExpert - WARNING - 解析策略分配结果时出错: Extracted JSON data is invalid
2025-06-22 17:16:04,924 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 17:16:04,924 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.3,
      "step_size_scaling": 1.2,
      "region_focus_probability": 0.4  // Focus more on difficult/opportunity regions.
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size_scaling": 0.8,
      "local_search_iterations": 10 // Perform a few local search steps
    }
  }
}
```

2025-06-22 17:16:04,924 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:16:04,924 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-06-22 17:16:04,924 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.3,
      "step_size_scaling": 1.2,
      "region_focus_probability": 0.4  // Focus more on difficult/opportunity regions.
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size_scaling": 0.8,
      "local_search_iterations": 10 // Perform a few local search steps
    }
  }
}
```

2025-06-22 17:16:04,924 - __main__ - INFO - 开始进化阶段
2025-06-22 17:16:04,924 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:16:04,924 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:04,924 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 70 nodes, starting with [6, 1, 4] and ending with [96, 97, 98]
- Cost: 1.710431179386332e+214
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9492600422832981
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:04,924 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:16:07,925 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    6, 1, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 61, 66, 71, 76, 81, 86, 91, 96, 97, 98
  ],
  "modification_strategy": "Heavily prioritize diversity and target unexplored regions. This involves a deviation from the original path's central trajectory, including nodes related to the identified opportunity regions early on, aiming to improve exploration rather than solely optimizing based on immediate cost. The path branches out significantly from the initial nodes, exploring different segments before converging on the final destination. The nodes in the middle segment focus on exploiting the opportunity regions based on their indices.",
  "targeted_regions": [
    "Unexplored regions",
    "Opportunity regions"
  ]
}
```

2025-06-22 17:16:07,927 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:16:07,927 - ExplorationExpert - INFO - 探索路径生成完成，成本: nan, 路径: [6, 1, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 61, 66, 71, 76, 81, 86, 91, 96, 97, 98]
2025-06-22 17:16:07,927 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 1, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 61, 66, 71, 76, 81, 86, 91, 96, 97, 98], 'cur_cost': nan}
2025-06-22 17:16:07,927 - __main__ - INFO - 为个体 1 生成利用路径
2025-06-22 17:16:07,927 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:16:07,927 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:16:07,927 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 111029.0
2025-06-22 17:16:07,933 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:16:07,933 - ExploitationExpert - INFO - res_population_costs: [9547, 9571, 9580]
2025-06-22 17:16:07,933 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 48,
       46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 58, 60, 54, 57, 64,
       52, 63, 65, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-06-22 17:16:07,934 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:16:07,934 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 61, 66, 71, 76, 81, 86, 91, 96, 97, 98], 'cur_cost': nan}, {'tour': array([ 5, 14, 53, 46, 37, 58,  4, 42, 25, 29,  6, 24, 48, 56, 55, 27, 15,
       50, 51, 64, 26, 31, 35, 52, 63, 21, 12, 60,  7, 28, 62, 13,  8, 65,
       34, 20, 19,  3, 30, 10, 40, 36, 32,  2, 49, 54, 57, 23, 11, 16, 47,
       59,  1, 43,  9, 44, 22,  0, 18, 39, 17, 45, 38, 61, 41, 33]), 'cur_cost': 111029.0}, {'tour': [19, 16, 17, 18, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 6, 5, 4, 3, 2, 1, 0], 'cur_cost': 6.805680462630222e+38}, {'tour': [63, 1, 22, 35, 17, 48, 5, 11, 28, 3, 39, 52, 2, 29, 45, 14, 21, 58, 18, 50, 4, 62, 7, 32, 16, 26, 37, 55, 12, 23, 40, 59, 9, 30, 42, 53, 27, 41, 61], 'cur_cost': 63805.0}, {'tour': [65, 47, 12, 1, 8, 25, 33, 18, 42, 50, 58, 6, 15, 29, 36, 4, 22, 30, 45, 52, 10, 21, 55], 'cur_cost': 30270.0}, {'tour': [42, 9, 3, 15, 21, 33, 45, 57, 6, 18, 30, 40, 52, 64, 1, 13, 25, 37, 49, 61, 32, 60], 'cur_cost': 35761.0}, {'tour': [22, 1, 44, 25, 62, 48, 17, 9, 6, 53, 3, 38, 10, 42, 12, 35, 27, 29, 40, 60, 57, 21, 18, 59, 51, 14, 23, 47, 36, 30, 26, 55, 49, 16, 34], 'cur_cost': 53150.0}, {'tour': [9, 27, 15, 48, 3, 35, 50, 12, 42, 60, 2, 21, 57, 4, 38, 18, 51, 24, 1, 29, 64, 45, 10, 32, 54, 7, 23, 40, 16, 59, 20, 56], 'cur_cost': 59138.0}, {'tour': [28, 2, 10, 25, 42, 33, 18, 51, 5, 12, 60, 48, 3, 21, 17, 38, 45, 9, 14, 63, 30, 23, 58, 1, 40, 53, 35, 6, 29, 44, 55, 7, 50, 22, 41, 16, 39, 19, 24, 59, 11, 20, 65, 13, 49, 4, 34, 32, 56, 57, 8, 47, 26, 31, 64, 46, 52, 37, 61, 27, 62, 36, 54, 15, 3, 8, 17], 'cur_cost': 122200.0}, {'tour': [42, 1, 29, 61, 12, 50, 37, 4, 58, 18, 31, 2, 45, 15, 64, 22, 53, 10, 28, 56, 39, 6, 48, 20, 35, 47, 11, 66, 24, 40, 55, 14, 32, 59, 7, 49, 19, 30, 3, 46, 25, 41, 9, 63, 21, 52, 17, 34, 60, 16, 33, 62, 23, 51, 13, 38, 5, 44, 27, 8, 57, 36, 65, 3, 4], 'cur_cost': 1.3857969993223356e+44}]
2025-06-22 17:16:07,935 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒
2025-06-22 17:16:07,935 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 6, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 6, 'cache_hits': 0, 'similarity_calculations': 15, 'cache_hit_rate': 0.0, 'cache_size': 15}}
2025-06-22 17:16:07,935 - __main__ - WARNING - 个体 1 的利用路径生成失败，保留原个体
2025-06-22 17:16:07,935 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 17:16:07,936 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:07,936 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 58 nodes, starting with [19, 16, 17] and ending with [2, 1, 0]
- Cost: 6.805680462630222e+38
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.931350114416476
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:07,937 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:16:13,527 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    19, 16, 15, 13, 11, 9, 7, 5, 3, 1, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 2, 4, 6, 8, 10, 12, 14, 17, 18, 0
  ],
  "modification_strategy": "This strategy focuses on maximizing path diversity by deviating significantly from the original path early on. It initially uses a different set of nodes, exploring a cluster of potential new areas (nodes 1-20) before transitioning to a long exploration of another cluster of nodes (21-58). Finally, it attempts to reconnect to the terminal nodes, aiming to maintain a reasonable connection while diverging from the beginning and middle section of the original path to avoid previously trodden regions and actively seek new opportunities.",
  "targeted_regions": "This path specifically targets the opportunity regions that are at the starting and end points by adding the nodes 1, 2, 4, 6, 8, 10, 12, 14, 17, 18 to the end of the path. The path also explores different nodes that are clustered together at the beginning and middle section, therefore allowing the path to target different areas and regions."
}
```
2025-06-22 17:16:13,527 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:16:13,527 - ExplorationExpert - INFO - 探索路径生成完成，成本: 22416.0, 路径: [19, 16, 15, 13, 11, 9, 7, 5, 3, 1, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 2, 4, 6, 8, 10, 12, 14, 17, 18, 0]
2025-06-22 17:16:13,527 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [19, 16, 15, 13, 11, 9, 7, 5, 3, 1, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 2, 4, 6, 8, 10, 12, 14, 17, 18, 0], 'cur_cost': 22416.0}
2025-06-22 17:16:13,527 - __main__ - INFO - 为个体 3 生成利用路径
2025-06-22 17:16:13,527 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:16:13,527 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:16:13,527 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 113588.0
2025-06-22 17:16:13,535 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:16:13,535 - ExploitationExpert - INFO - res_population_costs: [9547, 9571, 9580]
2025-06-22 17:16:13,535 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 48,
       46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 58, 60, 54, 57, 64,
       52, 63, 65, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-06-22 17:16:13,537 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:16:13,537 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 61, 66, 71, 76, 81, 86, 91, 96, 97, 98], 'cur_cost': nan}, {'tour': array([ 5, 14, 53, 46, 37, 58,  4, 42, 25, 29,  6, 24, 48, 56, 55, 27, 15,
       50, 51, 64, 26, 31, 35, 52, 63, 21, 12, 60,  7, 28, 62, 13,  8, 65,
       34, 20, 19,  3, 30, 10, 40, 36, 32,  2, 49, 54, 57, 23, 11, 16, 47,
       59,  1, 43,  9, 44, 22,  0, 18, 39, 17, 45, 38, 61, 41, 33]), 'cur_cost': 111029.0}, {'tour': [19, 16, 15, 13, 11, 9, 7, 5, 3, 1, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 2, 4, 6, 8, 10, 12, 14, 17, 18, 0], 'cur_cost': 22416.0}, {'tour': array([44,  1, 34, 26, 52, 19,  3, 57, 16, 48,  0, 46, 10, 17, 61, 13, 29,
       22, 49, 20, 14,  5, 18, 43, 38, 56, 30, 45, 33, 53, 59, 65, 35, 39,
       62, 32, 54, 28, 40, 12,  8, 47, 41,  7,  6, 31, 50, 55, 27,  4,  9,
       60, 23, 21, 24, 37,  2, 25, 15, 64, 36, 11, 42, 51, 63, 58]), 'cur_cost': 113588.0}, {'tour': [65, 47, 12, 1, 8, 25, 33, 18, 42, 50, 58, 6, 15, 29, 36, 4, 22, 30, 45, 52, 10, 21, 55], 'cur_cost': 30270.0}, {'tour': [42, 9, 3, 15, 21, 33, 45, 57, 6, 18, 30, 40, 52, 64, 1, 13, 25, 37, 49, 61, 32, 60], 'cur_cost': 35761.0}, {'tour': [22, 1, 44, 25, 62, 48, 17, 9, 6, 53, 3, 38, 10, 42, 12, 35, 27, 29, 40, 60, 57, 21, 18, 59, 51, 14, 23, 47, 36, 30, 26, 55, 49, 16, 34], 'cur_cost': 53150.0}, {'tour': [9, 27, 15, 48, 3, 35, 50, 12, 42, 60, 2, 21, 57, 4, 38, 18, 51, 24, 1, 29, 64, 45, 10, 32, 54, 7, 23, 40, 16, 59, 20, 56], 'cur_cost': 59138.0}, {'tour': [28, 2, 10, 25, 42, 33, 18, 51, 5, 12, 60, 48, 3, 21, 17, 38, 45, 9, 14, 63, 30, 23, 58, 1, 40, 53, 35, 6, 29, 44, 55, 7, 50, 22, 41, 16, 39, 19, 24, 59, 11, 20, 65, 13, 49, 4, 34, 32, 56, 57, 8, 47, 26, 31, 64, 46, 52, 37, 61, 27, 62, 36, 54, 15, 3, 8, 17], 'cur_cost': 122200.0}, {'tour': [42, 1, 29, 61, 12, 50, 37, 4, 58, 18, 31, 2, 45, 15, 64, 22, 53, 10, 28, 56, 39, 6, 48, 20, 35, 47, 11, 66, 24, 40, 55, 14, 32, 59, 7, 49, 19, 30, 3, 46, 25, 41, 9, 63, 21, 52, 17, 34, 60, 16, 33, 62, 23, 51, 13, 38, 5, 44, 27, 8, 57, 36, 65, 3, 4], 'cur_cost': 1.3857969993223356e+44}]
2025-06-22 17:16:13,538 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒
2025-06-22 17:16:13,539 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 7, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 7, 'cache_hits': 0, 'similarity_calculations': 21, 'cache_hit_rate': 0.0, 'cache_size': 21}}
2025-06-22 17:16:13,539 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-06-22 17:16:13,539 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 17:16:13,539 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:13,540 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 23 nodes, starting with [65, 47, 12] and ending with [10, 21, 55]
- Cost: 30270.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9247311827956989
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:13,540 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:16:16,032 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    65, 88, 15, 33, 72, 1, 99, 27, 48, 50, 60, 8, 11, 30, 25, 77, 19, 45, 37, 91, 55
  ],
  "modification_strategy": "Prioritized exploration of unexplored and opportunity regions by shifting the path away from difficult regions. This involved leveraging diversity parameters to prioritize paths through less explored territories, resulting in a significantly different node sequence compared to the existing path.",
  "targeted_regions": "Unexplored regions and Opportunity regions have been prioritized while avoiding difficult regions by adjusting the sequence from the initial path."
}
```

2025-06-22 17:16:16,032 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:16:16,032 - ExplorationExpert - INFO - 探索路径生成完成，成本: 21066.0, 路径: [65, 88, 15, 33, 72, 1, 99, 27, 48, 50, 60, 8, 11, 30, 25, 77, 19, 45, 37, 91, 55]
2025-06-22 17:16:16,032 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [65, 88, 15, 33, 72, 1, 99, 27, 48, 50, 60, 8, 11, 30, 25, 77, 19, 45, 37, 91, 55], 'cur_cost': 21066.0}
2025-06-22 17:16:16,032 - __main__ - INFO - 为个体 5 生成利用路径
2025-06-22 17:16:16,032 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:16:16,032 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:16:16,032 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 109138.0
2025-06-22 17:16:16,040 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:16:16,040 - ExploitationExpert - INFO - res_population_costs: [9547, 9571, 9580]
2025-06-22 17:16:16,040 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 48,
       46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 58, 60, 54, 57, 64,
       52, 63, 65, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-06-22 17:16:16,041 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:16:16,041 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 61, 66, 71, 76, 81, 86, 91, 96, 97, 98], 'cur_cost': nan}, {'tour': array([ 5, 14, 53, 46, 37, 58,  4, 42, 25, 29,  6, 24, 48, 56, 55, 27, 15,
       50, 51, 64, 26, 31, 35, 52, 63, 21, 12, 60,  7, 28, 62, 13,  8, 65,
       34, 20, 19,  3, 30, 10, 40, 36, 32,  2, 49, 54, 57, 23, 11, 16, 47,
       59,  1, 43,  9, 44, 22,  0, 18, 39, 17, 45, 38, 61, 41, 33]), 'cur_cost': 111029.0}, {'tour': [19, 16, 15, 13, 11, 9, 7, 5, 3, 1, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 2, 4, 6, 8, 10, 12, 14, 17, 18, 0], 'cur_cost': 22416.0}, {'tour': array([44,  1, 34, 26, 52, 19,  3, 57, 16, 48,  0, 46, 10, 17, 61, 13, 29,
       22, 49, 20, 14,  5, 18, 43, 38, 56, 30, 45, 33, 53, 59, 65, 35, 39,
       62, 32, 54, 28, 40, 12,  8, 47, 41,  7,  6, 31, 50, 55, 27,  4,  9,
       60, 23, 21, 24, 37,  2, 25, 15, 64, 36, 11, 42, 51, 63, 58]), 'cur_cost': 113588.0}, {'tour': [65, 88, 15, 33, 72, 1, 99, 27, 48, 50, 60, 8, 11, 30, 25, 77, 19, 45, 37, 91, 55], 'cur_cost': 21066.0}, {'tour': array([17, 35, 47, 13,  2, 45,  9, 16, 24,  6, 36, 11, 50, 48, 54,  0, 26,
       14, 61,  4, 29, 38, 30, 31, 60, 18, 55, 49, 56,  7,  5,  1, 57, 27,
       64, 52,  8, 32, 43,  3, 53, 20, 28, 63, 62, 34, 10, 51, 42, 58, 39,
       23, 59, 65, 37, 15, 46, 12, 22, 19, 33, 40, 25, 44, 21, 41]), 'cur_cost': 109138.0}, {'tour': [22, 1, 44, 25, 62, 48, 17, 9, 6, 53, 3, 38, 10, 42, 12, 35, 27, 29, 40, 60, 57, 21, 18, 59, 51, 14, 23, 47, 36, 30, 26, 55, 49, 16, 34], 'cur_cost': 53150.0}, {'tour': [9, 27, 15, 48, 3, 35, 50, 12, 42, 60, 2, 21, 57, 4, 38, 18, 51, 24, 1, 29, 64, 45, 10, 32, 54, 7, 23, 40, 16, 59, 20, 56], 'cur_cost': 59138.0}, {'tour': [28, 2, 10, 25, 42, 33, 18, 51, 5, 12, 60, 48, 3, 21, 17, 38, 45, 9, 14, 63, 30, 23, 58, 1, 40, 53, 35, 6, 29, 44, 55, 7, 50, 22, 41, 16, 39, 19, 24, 59, 11, 20, 65, 13, 49, 4, 34, 32, 56, 57, 8, 47, 26, 31, 64, 46, 52, 37, 61, 27, 62, 36, 54, 15, 3, 8, 17], 'cur_cost': 122200.0}, {'tour': [42, 1, 29, 61, 12, 50, 37, 4, 58, 18, 31, 2, 45, 15, 64, 22, 53, 10, 28, 56, 39, 6, 48, 20, 35, 47, 11, 66, 24, 40, 55, 14, 32, 59, 7, 49, 19, 30, 3, 46, 25, 41, 9, 63, 21, 52, 17, 34, 60, 16, 33, 62, 23, 51, 13, 38, 5, 44, 27, 8, 57, 36, 65, 3, 4], 'cur_cost': 1.3857969993223356e+44}]
2025-06-22 17:16:16,043 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒
2025-06-22 17:16:16,043 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 8, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 8, 'cache_hits': 0, 'similarity_calculations': 28, 'cache_hit_rate': 0.0, 'cache_size': 28}}
2025-06-22 17:16:16,043 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-06-22 17:16:16,044 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 17:16:16,044 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:16,044 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 35 nodes, starting with [22, 1, 44] and ending with [49, 16, 34]
- Cost: 53150.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9112426035502958
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:16,044 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:16:19,262 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    22, 3, 10, 15, 38, 28, 40, 13, 8, 20, 2, 25, 41, 18, 31, 33, 4, 9, 29, 14, 24, 42, 11, 39, 27, 36, 19, 23, 5, 32, 46, 48, 47, 16, 34
  ],
  "modification_strategy": "Prioritized exploration of opportunity regions and diversification by selecting nodes furthest away from the original path, favoring unexplored areas. I focused on bypassing difficult regions and balancing this with cost considerations using the diversity weight and risk tolerance parameters. I've attempted to 'jump' the path early to visit some nodes not visited by the old path. The path then attempts to weave its way through the unexplored regions.",
  "targeted_regions": [
    "Opportunity Region 1 (node indices: 10, 15, 38, 28, 40, 13, 8)",
    "Opportunity Region 2 (node indices: 33, 4, 9, 29, 14, 24, 42, 11, 39, 27, 36)"
  ]
}
```
2025-06-22 17:16:19,262 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:16:19,262 - ExplorationExpert - INFO - 探索路径生成完成，成本: 46451.0, 路径: [22, 3, 10, 15, 38, 28, 40, 13, 8, 20, 2, 25, 41, 18, 31, 33, 4, 9, 29, 14, 24, 42, 11, 39, 27, 36, 19, 23, 5, 32, 46, 48, 47, 16, 34]
2025-06-22 17:16:19,262 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [22, 3, 10, 15, 38, 28, 40, 13, 8, 20, 2, 25, 41, 18, 31, 33, 4, 9, 29, 14, 24, 42, 11, 39, 27, 36, 19, 23, 5, 32, 46, 48, 47, 16, 34], 'cur_cost': 46451.0}
2025-06-22 17:16:19,262 - __main__ - INFO - 为个体 7 生成利用路径
2025-06-22 17:16:19,264 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:16:19,264 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:16:19,264 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 103367.0
2025-06-22 17:16:19,270 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:16:19,270 - ExploitationExpert - INFO - res_population_costs: [9547, 9571, 9580]
2025-06-22 17:16:19,270 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 48,
       46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 58, 60, 54, 57, 64,
       52, 63, 65, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-06-22 17:16:19,272 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:16:19,272 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 61, 66, 71, 76, 81, 86, 91, 96, 97, 98], 'cur_cost': nan}, {'tour': array([ 5, 14, 53, 46, 37, 58,  4, 42, 25, 29,  6, 24, 48, 56, 55, 27, 15,
       50, 51, 64, 26, 31, 35, 52, 63, 21, 12, 60,  7, 28, 62, 13,  8, 65,
       34, 20, 19,  3, 30, 10, 40, 36, 32,  2, 49, 54, 57, 23, 11, 16, 47,
       59,  1, 43,  9, 44, 22,  0, 18, 39, 17, 45, 38, 61, 41, 33]), 'cur_cost': 111029.0}, {'tour': [19, 16, 15, 13, 11, 9, 7, 5, 3, 1, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 2, 4, 6, 8, 10, 12, 14, 17, 18, 0], 'cur_cost': 22416.0}, {'tour': array([44,  1, 34, 26, 52, 19,  3, 57, 16, 48,  0, 46, 10, 17, 61, 13, 29,
       22, 49, 20, 14,  5, 18, 43, 38, 56, 30, 45, 33, 53, 59, 65, 35, 39,
       62, 32, 54, 28, 40, 12,  8, 47, 41,  7,  6, 31, 50, 55, 27,  4,  9,
       60, 23, 21, 24, 37,  2, 25, 15, 64, 36, 11, 42, 51, 63, 58]), 'cur_cost': 113588.0}, {'tour': [65, 88, 15, 33, 72, 1, 99, 27, 48, 50, 60, 8, 11, 30, 25, 77, 19, 45, 37, 91, 55], 'cur_cost': 21066.0}, {'tour': array([17, 35, 47, 13,  2, 45,  9, 16, 24,  6, 36, 11, 50, 48, 54,  0, 26,
       14, 61,  4, 29, 38, 30, 31, 60, 18, 55, 49, 56,  7,  5,  1, 57, 27,
       64, 52,  8, 32, 43,  3, 53, 20, 28, 63, 62, 34, 10, 51, 42, 58, 39,
       23, 59, 65, 37, 15, 46, 12, 22, 19, 33, 40, 25, 44, 21, 41]), 'cur_cost': 109138.0}, {'tour': [22, 3, 10, 15, 38, 28, 40, 13, 8, 20, 2, 25, 41, 18, 31, 33, 4, 9, 29, 14, 24, 42, 11, 39, 27, 36, 19, 23, 5, 32, 46, 48, 47, 16, 34], 'cur_cost': 46451.0}, {'tour': array([26, 12,  3, 47, 46, 24, 17, 59, 15, 40,  8, 13, 52, 32, 18,  2, 20,
       23, 43,  5, 36, 31,  4, 58, 22, 63, 16, 51,  6,  9, 65, 44, 35, 56,
        7, 41, 48, 33, 37, 14, 19, 57, 64,  0,  1, 28, 55, 29, 62, 53, 11,
       50, 34, 27, 54, 30, 38, 49, 39, 45, 60, 10, 42, 61, 25, 21]), 'cur_cost': 103367.0}, {'tour': [28, 2, 10, 25, 42, 33, 18, 51, 5, 12, 60, 48, 3, 21, 17, 38, 45, 9, 14, 63, 30, 23, 58, 1, 40, 53, 35, 6, 29, 44, 55, 7, 50, 22, 41, 16, 39, 19, 24, 59, 11, 20, 65, 13, 49, 4, 34, 32, 56, 57, 8, 47, 26, 31, 64, 46, 52, 37, 61, 27, 62, 36, 54, 15, 3, 8, 17], 'cur_cost': 122200.0}, {'tour': [42, 1, 29, 61, 12, 50, 37, 4, 58, 18, 31, 2, 45, 15, 64, 22, 53, 10, 28, 56, 39, 6, 48, 20, 35, 47, 11, 66, 24, 40, 55, 14, 32, 59, 7, 49, 19, 30, 3, 46, 25, 41, 9, 63, 21, 52, 17, 34, 60, 16, 33, 62, 23, 51, 13, 38, 5, 44, 27, 8, 57, 36, 65, 3, 4], 'cur_cost': 1.3857969993223356e+44}]
2025-06-22 17:16:19,273 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒
2025-06-22 17:16:19,273 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 9, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 9, 'cache_hits': 0, 'similarity_calculations': 36, 'cache_hit_rate': 0.0, 'cache_size': 36}}
2025-06-22 17:16:19,273 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-06-22 17:16:19,274 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 17:16:19,274 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:19,274 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 67 nodes, starting with [28, 2, 10] and ending with [3, 8, 17]
- Cost: 122200.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9075785582255084
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:19,275 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:16:22,071 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [28, 5, 12, 20, 35, 40, 45, 48, 52, 58, 62, 65, 3, 8, 17],
  "modification_strategy": "Prioritized diversity and exploration of unexplored and opportunity regions. Instead of following the existing long path directly, I jumped to the first node ([28, 5, 12...]) that explores a different direction, taking advantage of the 'Population diversity' and 'Unexplored regions' metrics. The nodes were picked to maximize exploration of opportunity regions and avoid difficult ones, while keeping the path length reasonable and using a relatively low risk_tolerance. The initial part of the path was modified heavily, and after a certain point, the path moved towards the target (ending) region [3, 8, 17] to achieve the final objective.",
  "targeted_regions": ["Unexplored regions", "Opportunity regions"]
}
```
2025-06-22 17:16:22,071 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:16:22,071 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13338.0, 路径: [28, 5, 12, 20, 35, 40, 45, 48, 52, 58, 62, 65, 3, 8, 17]
2025-06-22 17:16:22,071 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [28, 5, 12, 20, 35, 40, 45, 48, 52, 58, 62, 65, 3, 8, 17], 'cur_cost': 13338.0}
2025-06-22 17:16:22,071 - __main__ - INFO - 为个体 9 生成利用路径
2025-06-22 17:16:22,071 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-06-22 17:16:22,071 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-06-22 17:16:22,071 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 102525.0
2025-06-22 17:16:22,071 - ExploitationExpert - INFO - res_population_num: 3
2025-06-22 17:16:22,071 - ExploitationExpert - INFO - res_population_costs: [9547, 9571, 9580]
2025-06-22 17:16:22,071 - ExploitationExpert - INFO - res_populations: [array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 51, 38, 41, 45, 44, 39, 58, 60, 54, 57, 64, 65, 52,
       63, 61, 53, 62, 59, 56, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 10,  8,  5,  4,  6,  2, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60,
       62, 59, 56, 58, 14, 15, 22, 12, 17, 18, 23, 16, 19, 13, 20, 21, 40,
       49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 30, 28, 35,
       26, 25, 33, 32, 29, 24, 31, 37, 36, 27,  3,  9, 11,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9, 15, 14, 20, 21, 13, 19, 16, 23, 22, 12, 17,
       18, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35, 34, 43, 48,
       46, 42, 50, 45, 38, 51, 41, 44, 39, 47, 49, 40, 58, 60, 54, 57, 64,
       52, 63, 65, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-06-22 17:16:22,078 - ExploitationExpert - INFO - populations_num: 10
2025-06-22 17:16:22,078 - ExploitationExpert - INFO - populations: [{'tour': [6, 1, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 61, 66, 71, 76, 81, 86, 91, 96, 97, 98], 'cur_cost': nan}, {'tour': array([ 5, 14, 53, 46, 37, 58,  4, 42, 25, 29,  6, 24, 48, 56, 55, 27, 15,
       50, 51, 64, 26, 31, 35, 52, 63, 21, 12, 60,  7, 28, 62, 13,  8, 65,
       34, 20, 19,  3, 30, 10, 40, 36, 32,  2, 49, 54, 57, 23, 11, 16, 47,
       59,  1, 43,  9, 44, 22,  0, 18, 39, 17, 45, 38, 61, 41, 33]), 'cur_cost': 111029.0}, {'tour': [19, 16, 15, 13, 11, 9, 7, 5, 3, 1, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 2, 4, 6, 8, 10, 12, 14, 17, 18, 0], 'cur_cost': 22416.0}, {'tour': array([44,  1, 34, 26, 52, 19,  3, 57, 16, 48,  0, 46, 10, 17, 61, 13, 29,
       22, 49, 20, 14,  5, 18, 43, 38, 56, 30, 45, 33, 53, 59, 65, 35, 39,
       62, 32, 54, 28, 40, 12,  8, 47, 41,  7,  6, 31, 50, 55, 27,  4,  9,
       60, 23, 21, 24, 37,  2, 25, 15, 64, 36, 11, 42, 51, 63, 58]), 'cur_cost': 113588.0}, {'tour': [65, 88, 15, 33, 72, 1, 99, 27, 48, 50, 60, 8, 11, 30, 25, 77, 19, 45, 37, 91, 55], 'cur_cost': 21066.0}, {'tour': array([17, 35, 47, 13,  2, 45,  9, 16, 24,  6, 36, 11, 50, 48, 54,  0, 26,
       14, 61,  4, 29, 38, 30, 31, 60, 18, 55, 49, 56,  7,  5,  1, 57, 27,
       64, 52,  8, 32, 43,  3, 53, 20, 28, 63, 62, 34, 10, 51, 42, 58, 39,
       23, 59, 65, 37, 15, 46, 12, 22, 19, 33, 40, 25, 44, 21, 41]), 'cur_cost': 109138.0}, {'tour': [22, 3, 10, 15, 38, 28, 40, 13, 8, 20, 2, 25, 41, 18, 31, 33, 4, 9, 29, 14, 24, 42, 11, 39, 27, 36, 19, 23, 5, 32, 46, 48, 47, 16, 34], 'cur_cost': 46451.0}, {'tour': array([26, 12,  3, 47, 46, 24, 17, 59, 15, 40,  8, 13, 52, 32, 18,  2, 20,
       23, 43,  5, 36, 31,  4, 58, 22, 63, 16, 51,  6,  9, 65, 44, 35, 56,
        7, 41, 48, 33, 37, 14, 19, 57, 64,  0,  1, 28, 55, 29, 62, 53, 11,
       50, 34, 27, 54, 30, 38, 49, 39, 45, 60, 10, 42, 61, 25, 21]), 'cur_cost': 103367.0}, {'tour': [28, 5, 12, 20, 35, 40, 45, 48, 52, 58, 62, 65, 3, 8, 17], 'cur_cost': 13338.0}, {'tour': array([20, 23, 24,  1,  4, 50, 35, 10, 33, 14, 11, 18, 28, 57, 44, 47, 25,
       27,  8, 19, 53, 37, 63, 31, 39, 61, 51, 16, 29, 52, 54, 49, 22, 40,
       48,  5, 58,  0, 55,  6,  2, 59,  3, 56, 21, 15, 46, 13, 60, 38, 17,
       41, 32, 36, 64, 65,  9, 34, 26, 43, 30, 12,  7, 42, 45, 62]), 'cur_cost': 102525.0}]
2025-06-22 17:16:22,080 - ExploitationExpert - INFO - 局部搜索耗时: 0.01秒
2025-06-22 17:16:22,080 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 10, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 10, 'cache_hits': 0, 'similarity_calculations': 45, 'cache_hit_rate': 0.0, 'cache_size': 45}}
2025-06-22 17:16:22,081 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-06-22 17:16:22,081 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 1, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 11, 16, 21, 26, 31, 36, 41, 46, 51, 56, 61, 66, 71, 76, 81, 86, 91, 96, 97, 98], 'cur_cost': nan}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [19, 16, 15, 13, 11, 9, 7, 5, 3, 1, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 2, 4, 6, 8, 10, 12, 14, 17, 18, 0], 'cur_cost': 22416.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [65, 88, 15, 33, 72, 1, 99, 27, 48, 50, 60, 8, 11, 30, 25, 77, 19, 45, 37, 91, 55], 'cur_cost': 21066.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [22, 3, 10, 15, 38, 28, 40, 13, 8, 20, 2, 25, 41, 18, 31, 33, 4, 9, 29, 14, 24, 42, 11, 39, 27, 36, 19, 23, 5, 32, 46, 48, 47, 16, 34], 'cur_cost': 46451.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [28, 5, 12, 20, 35, 40, 45, 48, 52, 58, 62, 65, 3, 8, 17], 'cur_cost': 13338.0}}]
2025-06-22 17:16:22,081 - __main__ - INFO - 进化阶段完成
2025-06-22 17:16:22,081 - __main__ - INFO - 开始评估阶段
2025-06-22 17:16:22,081 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 17:16:22,082 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 17:16:22,082 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 2/5
- Population changes:
  * Cost: min -1.4701865236778527e+82->nan, mean 1.710431179386332e+213->nan
  * Diversity: 0.9492600422832981->0.9061224489795918
  * Elite solutions: 0->0

## Strategy Performance:
- Explore: 5 individuals, success rate 80.0%, avg improvement nan
- Exploit: 5 individuals, success rate 20.0%, avg improvement -2.9403730473557052e+81
- Overall improvement: nan

## Historical Trends:
Last 2 iterations: costs [18914.0, -1.4701865236778527e+82], diversity [0.9883059450223628, 1.0]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 17:16:29,653 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early, potentially collapsing.",
    "features": [
      "High cost values initially, followed by extreme negative values and then NaN (Not a Number), indicating potential numerical instability or a search space region that leads to overflow or undeflow.",
      "Significant drop in diversity, suggesting the population is converging but towards a potentially invalid or unstable solution.",
      "No elite solutions identified in either iteration, indicating that no individual is consistently outperforming others significantly."
    ]
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "High (80.0%)",
      "avg_improvement": "NaN - Indicates failure to find a valid improvement. Suggests that while exploration is finding *something*, it's not leading to meaningful solutions or a stable objective value.",
      "assessment": "Exploration is finding solutions, but they may be in a region where the cost function returns NaN. Further investigation into the cost function's behavior is critical."
    },
    "exploit": {
      "success_rate": "Low (20.0%)",
      "avg_improvement": "Significant negative improvement (-2.9403730473557052e+81) - This indicates that even when the algorithm attempts to refine already-tested individuals (exploit), the objective value is getting much worse.",
      "assessment": "Exploitation is ineffective and harmful, likely due to the instability of the objective function, parameter space, or because a 'good' region hasn't been found. The negative average improvement is alarming and needs immediate attention."
    },
    "overall_improvement": "NaN - This reinforces the notion that overall progress is not being made, likely due to unstable objective function values and a lack of robust improvements."
  },
  "balance_state": {
    "assessment": "Severely imbalanced. The data indicates that the algorithm is experiencing NaN values for cost function and likely, is either struggling with an ill-defined search space (e.g., cost explodes to +/- infinity), numerical instability in cost or model, or it has become trapped in a non-optimal region (and exploiting it leads to further instability).",
    "adjustment_needs": [
      "Drastically increase exploration, focusing on finding a valid/stable region.",
      "Reduce exploitation until a stable, well-defined solution can be found. Exploiting around NaN is likely to be disastrous.",
      "Evaluate the cost function and its parameters."
    ]
  },
  "recommendations": [
    {
      "action": "Investigate and address NaN values",
      "details": "The primary concern is the appearance of NaN values in the cost function.  This can stem from several issues: (a) numerical instability in the cost calculation; (b) the evaluation of parameters leading to error (e.g. division by zero, square root of a negative number); (c) the algorithm may be exploring outside of valid input ranges that the objective function can evaluate. Debug the cost function calculation and input parameter validation. Examine the historical data of the cost function to understand when the NaN values appeared. It is important to determine the reason behind it as quickly as possible."
    },
    {
      "action": "Reduce exploitation efforts",
      "details": "Until a stable and valid solution region can be found, exploitation efforts are exacerbating the problem.  Reduce the number of exploitation individuals significantly in the next iteration.  Prioritize exploration."
    },
    {
      "action": "Increase Exploration Focus",
      "details": "Increase the number of exploration individuals, and consider exploration strategies that move away from the area where the cost function is generating NaN values or leading to high negative costs. Consider strategies like uniform sampling across a wide range of the parameter space, random mutations with a large mutation step size, or even random restarts."
    },
        {
      "action": "Analyze Historical Data",
      "details": "The historical data (costs [18914.0, -1.4701865236778527e+82], diversity [0.9883059450223628, 1.0]) reveals that the costs were initially much better and the diversity started very high. This suggests the optimization might have started well but is now having problems. Look at where parameters changed and whether the input values started entering any invalid regions. If possible and feasible, start the process with a simplified version or a smaller dataset and then gradually increase the complexity."
    }
  ]
}
```
2025-06-22 17:16:29,658 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 17:16:29,658 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early, potentially collapsing.",
    "features": [
      "High cost values initially, followed by extreme negative values and then NaN (Not a Number), indicating potential numerical instability or a search space region that leads to overflow or undeflow.",
      "Significant drop in diversity, suggesting the population is converging but towards a potentially invalid or unstable solution.",
      "No elite solutions identified in either iteration, indicating that no individual is consistently outperforming others significantly."
    ]
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "High (80.0%)",
      "avg_improvement": "NaN - Indicates failure to find a valid improvement. Suggests that while exploration is finding *something*, it's not leading to meaningful solutions or a stable objective value.",
      "assessment": "Exploration is finding solutions, but they may be in a region where the cost function returns NaN. Further investigation into the cost function's behavior is critical."
    },
    "exploit": {
      "success_rate": "Low (20.0%)",
      "avg_improvement": "Significant negative improvement (-2.9403730473557052e+81) - This indicates that even when the algorithm attempts to refine already-tested individuals (exploit), the objective value is getting much worse.",
      "assessment": "Exploitation is ineffective and harmful, likely due to the instability of the objective function, parameter space, or because a 'good' region hasn't been found. The negative average improvement is alarming and needs immediate attention."
    },
    "overall_improvement": "NaN - This reinforces the notion that overall progress is not being made, likely due to unstable objective function values and a lack of robust improvements."
  },
  "balance_state": {
    "assessment": "Severely imbalanced. The data indicates that the algorithm is experiencing NaN values for cost function and likely, is either struggling with an ill-defined search space (e.g., cost explodes to +/- infinity), numerical instability in cost or model, or it has become trapped in a non-optimal region (and exploiting it leads to further instability).",
    "adjustment_needs": [
      "Drastically increase exploration, focusing on finding a valid/stable region.",
      "Reduce exploitation until a stable, well-defined solution can be found. Exploiting around NaN is likely to be disastrous.",
      "Evaluate the cost function and its parameters."
    ]
  },
  "recommendations": [
    {
      "action": "Investigate and address NaN values",
      "details": "The primary concern is the appearance of NaN values in the cost function.  This can stem from several issues: (a) numerical instability in the cost calculation; (b) the evaluation of parameters leading to error (e.g. division by zero, square root of a negative number); (c) the algorithm may be exploring outside of valid input ranges that the objective function can evaluate. Debug the cost function calculation and input parameter validation. Examine the historical data of the cost function to understand when the NaN values appeared. It is important to determine the reason behind it as quickly as possible."
    },
    {
      "action": "Reduce exploitation efforts",
      "details": "Until a stable and valid solution region can be found, exploitation efforts are exacerbating the problem.  Reduce the number of exploitation individuals significantly in the next iteration.  Prioritize exploration."
    },
    {
      "action": "Increase Exploration Focus",
      "details": "Increase the number of exploration individuals, and consider exploration strategies that move away from the area where the cost function is generating NaN values or leading to high negative costs. Consider strategies like uniform sampling across a wide range of the parameter space, random mutations with a large mutation step size, or even random restarts."
    },
        {
      "action": "Analyze Historical Data",
      "details": "The historical data (costs [18914.0, -1.4701865236778527e+82], diversity [0.9883059450223628, 1.0]) reveals that the costs were initially much better and the diversity started very high. This suggests the optimization might have started well but is now having problems. Look at where parameters changed and whether the input values started entering any invalid regions. If possible and feasible, start the process with a simplified version or a smaller dataset and then gradually increase the complexity."
    }
  ]
}
```
2025-06-22 17:16:29,658 - __main__ - INFO - 评估阶段完成
2025-06-22 17:16:29,658 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early, potentially collapsing.",
    "features": [
      "High cost values initially, followed by extreme negative values and then NaN (Not a Number), indicating potential numerical instability or a search space region that leads to overflow or undeflow.",
      "Significant drop in diversity, suggesting the population is converging but towards a potentially invalid or unstable solution.",
      "No elite solutions identified in either iteration, indicating that no individual is consistently outperforming others significantly."
    ]
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "High (80.0%)",
      "avg_improvement": "NaN - Indicates failure to find a valid improvement. Suggests that while exploration is finding *something*, it's not leading to meaningful solutions or a stable objective value.",
      "assessment": "Exploration is finding solutions, but they may be in a region where the cost function returns NaN. Further investigation into the cost function's behavior is critical."
    },
    "exploit": {
      "success_rate": "Low (20.0%)",
      "avg_improvement": "Significant negative improvement (-2.9403730473557052e+81) - This indicates that even when the algorithm attempts to refine already-tested individuals (exploit), the objective value is getting much worse.",
      "assessment": "Exploitation is ineffective and harmful, likely due to the instability of the objective function, parameter space, or because a 'good' region hasn't been found. The negative average improvement is alarming and needs immediate attention."
    },
    "overall_improvement": "NaN - This reinforces the notion that overall progress is not being made, likely due to unstable objective function values and a lack of robust improvements."
  },
  "balance_state": {
    "assessment": "Severely imbalanced. The data indicates that the algorithm is experiencing NaN values for cost function and likely, is either struggling with an ill-defined search space (e.g., cost explodes to +/- infinity), numerical instability in cost or model, or it has become trapped in a non-optimal region (and exploiting it leads to further instability).",
    "adjustment_needs": [
      "Drastically increase exploration, focusing on finding a valid/stable region.",
      "Reduce exploitation until a stable, well-defined solution can be found. Exploiting around NaN is likely to be disastrous.",
      "Evaluate the cost function and its parameters."
    ]
  },
  "recommendations": [
    {
      "action": "Investigate and address NaN values",
      "details": "The primary concern is the appearance of NaN values in the cost function.  This can stem from several issues: (a) numerical instability in the cost calculation; (b) the evaluation of parameters leading to error (e.g. division by zero, square root of a negative number); (c) the algorithm may be exploring outside of valid input ranges that the objective function can evaluate. Debug the cost function calculation and input parameter validation. Examine the historical data of the cost function to understand when the NaN values appeared. It is important to determine the reason behind it as quickly as possible."
    },
    {
      "action": "Reduce exploitation efforts",
      "details": "Until a stable and valid solution region can be found, exploitation efforts are exacerbating the problem.  Reduce the number of exploitation individuals significantly in the next iteration.  Prioritize exploration."
    },
    {
      "action": "Increase Exploration Focus",
      "details": "Increase the number of exploration individuals, and consider exploration strategies that move away from the area where the cost function is generating NaN values or leading to high negative costs. Consider strategies like uniform sampling across a wide range of the parameter space, random mutations with a large mutation step size, or even random restarts."
    },
        {
      "action": "Analyze Historical Data",
      "details": "The historical data (costs [18914.0, -1.4701865236778527e+82], diversity [0.9883059450223628, 1.0]) reveals that the costs were initially much better and the diversity started very high. This suggests the optimization might have started well but is now having problems. Look at where parameters changed and whether the input values started entering any invalid regions. If possible and feasible, start the process with a simplified version or a smaller dataset and then gradually increase the complexity."
    }
  ]
}
```
2025-06-22 17:16:29,661 - __main__ - INFO - 当前最佳适应度: nan
2025-06-22 17:16:29,663 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_2.pkl
2025-06-22 17:16:29,663 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-06-22 17:16:29,663 - __main__ - INFO - 开始分析阶段
2025-06-22 17:16:29,663 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:16:29,668 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': nan, 'max': nan, 'mean': nan, 'std': nan}, 'diversity': 0.9922558922558922, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:16:29,668 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': nan, 'max': nan, 'mean': nan, 'std': nan}, 'diversity_level': 0.9922558922558922, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:16:29,668 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:16:29,668 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:16:29,668 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:16:29,668 - PathExpert - WARNING - 发现无效的城市索引: [70, 75, 80, 85, 90, 95, 66, 71, 76, 81, 86, 91, 96, 97, 98], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:16:29,668 - PathExpert - WARNING - 发现无效的城市索引: [88, 72, 99, 77, 91], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:16:29,668 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=1, tour_length=40
2025-06-22 17:16:29,673 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=10, tour_length=40
2025-06-22 17:16:29,673 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=15, tour_length=40
2025-06-22 17:16:29,673 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=20, tour_length=40
2025-06-22 17:16:29,673 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=20, city2=25, tour_length=40
2025-06-22 17:16:29,673 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=30, tour_length=40
2025-06-22 17:16:29,673 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=35, tour_length=40
2025-06-22 17:16:29,673 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=40, tour_length=40
2025-06-22 17:16:29,673 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=45, tour_length=40
2025-06-22 17:16:29,673 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=50, tour_length=40
2025-06-22 17:16:29,674 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=55, tour_length=40
2025-06-22 17:16:29,674 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=60, tour_length=40
2025-06-22 17:16:29,675 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=65, tour_length=40
2025-06-22 17:16:29,675 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=65, city2=4, tour_length=40
2025-06-22 17:16:29,675 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=9, tour_length=40
2025-06-22 17:16:29,675 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=14, tour_length=40
2025-06-22 17:16:29,675 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=14, city2=19, tour_length=40
2025-06-22 17:16:29,675 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=19, city2=24, tour_length=40
2025-06-22 17:16:29,675 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=29, tour_length=40
2025-06-22 17:16:29,675 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=11, tour_length=40
2025-06-22 17:16:29,676 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=16, tour_length=40
2025-06-22 17:16:29,676 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=16, city2=21, tour_length=40
2025-06-22 17:16:29,676 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=26, tour_length=40
2025-06-22 17:16:29,676 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=26, city2=31, tour_length=40
2025-06-22 17:16:29,676 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=31, city2=36, tour_length=40
2025-06-22 17:16:29,676 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=36, city2=41, tour_length=40
2025-06-22 17:16:29,676 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=41, city2=46, tour_length=40
2025-06-22 17:16:29,676 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=46, city2=51, tour_length=40
2025-06-22 17:16:29,677 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=51, city2=56, tour_length=40
2025-06-22 17:16:29,677 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=56, city2=61, tour_length=40
2025-06-22 17:16:29,677 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=61, city2=0, tour_length=40
2025-06-22 17:16:29,677 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=0, city2=5, tour_length=40
2025-06-22 17:16:29,678 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=10, tour_length=40
2025-06-22 17:16:29,678 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=15, tour_length=40
2025-06-22 17:16:29,678 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=20, tour_length=40
2025-06-22 17:16:29,678 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=20, city2=25, tour_length=40
2025-06-22 17:16:29,678 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=30, tour_length=40
2025-06-22 17:16:29,678 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=31, tour_length=40
2025-06-22 17:16:29,679 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=31, city2=32, tour_length=40
2025-06-22 17:16:29,679 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=6, tour_length=40
2025-06-22 17:16:29,679 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-06-22 17:16:29,679 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:16:29,679 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-06-22 17:16:29,679 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:16:29,680 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:16:29,680 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:16:29,680 - EliteExpert - INFO - 精英解分析完成
2025-06-22 17:16:29,681 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {'(22, 12)': 1.0, '(12, 17)': 1.0, '(20, 21)': 1.0, '(62, 59)': 1.0, '(59, 56)': 1.0}, 'common_edge_ratio': 0.07575757575757576}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': nan, 'avg_gap': nan}, 'structure_gap': {'unique_elite_edges': 132, 'unique_pop_edges': 453, 'common_edges': 22}}, 'elite_diversity': {'diversity_score': 0.3939393939393939}}
2025-06-22 17:16:29,681 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:16:29,681 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:16:29,681 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:16:29,681 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=nan, max=nan, mean=nan, std=nan
- Diversity: 0.9922558922558922, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 0 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:16:35,820 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Undetermined. With no cost data and no identified high-quality edges, it's impossible to assess ruggedness. The absence of elite solutions and common structure also provides no insight. More data is needed to determine if the landscape is smooth or rugged.",
    "modality": "Unknown.  Without cost information or structural patterns (e.g., common subpaths, high-quality edges), we cannot assess the modality of the search space (unimodal vs. multimodal).",
    "cost_information_needed": "Crucial to characterize the search space.  Cost data (min, max, mean, std) is missing, preventing any meaningful analysis of fitness landscape features."
  },
  "population_state": {
    "diversity": "High (0.992).  The diversity score suggests that the population is spread out and exploring the search space broadly. This is a positive sign if the search space is large and poorly understood.",
    "convergence": "None (0.0). Complete lack of convergence indicates that the population hasn't found solutions that are clustering or similar to each other, and/or that they haven't improved over time or clustered towards a promising region. This is supported by no elite solutions and may suggest that the current search strategy is not effective or needs more time.",
    "stability": "Unknown.  The lack of cost data prevents determination of how close solutions are to optimum, making this stability difficult to determine.",
    "assessment": "The population is diverse but not converging. This can be due to a flat fitness landscape (no clear direction for improvement), an extremely challenging landscape that prevents improvements, or the exploration phase of search is dominant over any exploitation or convergence phase. Without the cost data, it is impossible to determine why."
  },
  "difficult_regions": {
    "identification": "None identified. No difficult regions were identified, likely due to a lack of cost information or evidence of the population getting stuck. Could be due to the exploration phase of the search. Without cost data, identification of such regions is impossible.",
    "challenges": "Potentially a flat fitness landscape, a highly rugged landscape, or the absence of a clear direction for improvement. The current population state prevents identifying concrete regions of difficulty.  High diversity coupled with zero convergence suggests challenges in finding promising solutions.",
    "reasons_for_difficulty_speculation": "It is difficult to provide specific reasons without cost information."
  },
  "opportunity_regions": {
    "identification": "None identified. Without convergence or patterns, potential opportunity regions are not identified.",
    "potential": "Unknown. High diversity suggests that potentially promising regions are still being explored. The search may just be in its exploration phase. Identifying any specific potential areas is impossible without cost data or patterns in the population.",
    "strategy": "Continue to explore the search space until cost data allows identification."
  },
  "evolution_direction": {
    "strategy": "Gather cost data.  This is critical to assessing the fitness landscape. Once cost data is available, the analysis can be refined to guide a more effective evolution strategy.",
    "recommendations": [
      "If cost data becomes available and convergence is still zero:  Consider a strategy to promote exploitation (e.g., increase selection pressure).",
      "If cost data reveals a highly rugged landscape, explore a diverse population for an extended search duration.",
      "If cost data is consistent and stable over a short period, then an exploitation strategy may be suitable. For instance, run a local search or intensify the selection pressure to find the best possible solution.",
      "If cost data is noisy and varied, then run a more diverse search."
    ]
  }
}
```
2025-06-22 17:16:35,820 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:16:35,820 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': "Undetermined. With no cost data and no identified high-quality edges, it's impossible to assess ruggedness. The absence of elite solutions and common structure also provides no insight. More data is needed to determine if the landscape is smooth or rugged.", 'modality': 'Unknown.  Without cost information or structural patterns (e.g., common subpaths, high-quality edges), we cannot assess the modality of the search space (unimodal vs. multimodal).', 'cost_information_needed': 'Crucial to characterize the search space.  Cost data (min, max, mean, std) is missing, preventing any meaningful analysis of fitness landscape features.'}, 'population_state': {'diversity': 'High (0.992).  The diversity score suggests that the population is spread out and exploring the search space broadly. This is a positive sign if the search space is large and poorly understood.', 'convergence': "None (0.0). Complete lack of convergence indicates that the population hasn't found solutions that are clustering or similar to each other, and/or that they haven't improved over time or clustered towards a promising region. This is supported by no elite solutions and may suggest that the current search strategy is not effective or needs more time.", 'stability': 'Unknown.  The lack of cost data prevents determination of how close solutions are to optimum, making this stability difficult to determine.', 'assessment': 'The population is diverse but not converging. This can be due to a flat fitness landscape (no clear direction for improvement), an extremely challenging landscape that prevents improvements, or the exploration phase of search is dominant over any exploitation or convergence phase. Without the cost data, it is impossible to determine why.'}, 'difficult_regions': {'identification': 'None identified. No difficult regions were identified, likely due to a lack of cost information or evidence of the population getting stuck. Could be due to the exploration phase of the search. Without cost data, identification of such regions is impossible.', 'challenges': 'Potentially a flat fitness landscape, a highly rugged landscape, or the absence of a clear direction for improvement. The current population state prevents identifying concrete regions of difficulty.  High diversity coupled with zero convergence suggests challenges in finding promising solutions.', 'reasons_for_difficulty_speculation': 'It is difficult to provide specific reasons without cost information.'}, 'opportunity_regions': {'identification': 'None identified. Without convergence or patterns, potential opportunity regions are not identified.', 'potential': 'Unknown. High diversity suggests that potentially promising regions are still being explored. The search may just be in its exploration phase. Identifying any specific potential areas is impossible without cost data or patterns in the population.', 'strategy': 'Continue to explore the search space until cost data allows identification.'}, 'evolution_direction': {'strategy': 'Gather cost data.  This is critical to assessing the fitness landscape. Once cost data is available, the analysis can be refined to guide a more effective evolution strategy.', 'recommendations': ['If cost data becomes available and convergence is still zero:  Consider a strategy to promote exploitation (e.g., increase selection pressure).', 'If cost data reveals a highly rugged landscape, explore a diverse population for an extended search duration.', 'If cost data is consistent and stable over a short period, then an exploitation strategy may be suitable. For instance, run a local search or intensify the selection pressure to find the best possible solution.', 'If cost data is noisy and varied, then run a more diverse search.']}}
2025-06-22 17:16:35,820 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:16:35,820 - __main__ - INFO - 分析阶段完成
2025-06-22 17:16:35,820 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': "Undetermined. With no cost data and no identified high-quality edges, it's impossible to assess ruggedness. The absence of elite solutions and common structure also provides no insight. More data is needed to determine if the landscape is smooth or rugged.", 'modality': 'Unknown.  Without cost information or structural patterns (e.g., common subpaths, high-quality edges), we cannot assess the modality of the search space (unimodal vs. multimodal).', 'cost_information_needed': 'Crucial to characterize the search space.  Cost data (min, max, mean, std) is missing, preventing any meaningful analysis of fitness landscape features.'}, 'population_state': {'diversity': 'High (0.992).  The diversity score suggests that the population is spread out and exploring the search space broadly. This is a positive sign if the search space is large and poorly understood.', 'convergence': "None (0.0). Complete lack of convergence indicates that the population hasn't found solutions that are clustering or similar to each other, and/or that they haven't improved over time or clustered towards a promising region. This is supported by no elite solutions and may suggest that the current search strategy is not effective or needs more time.", 'stability': 'Unknown.  The lack of cost data prevents determination of how close solutions are to optimum, making this stability difficult to determine.', 'assessment': 'The population is diverse but not converging. This can be due to a flat fitness landscape (no clear direction for improvement), an extremely challenging landscape that prevents improvements, or the exploration phase of search is dominant over any exploitation or convergence phase. Without the cost data, it is impossible to determine why.'}, 'difficult_regions': {'identification': 'None identified. No difficult regions were identified, likely due to a lack of cost information or evidence of the population getting stuck. Could be due to the exploration phase of the search. Without cost data, identification of such regions is impossible.', 'challenges': 'Potentially a flat fitness landscape, a highly rugged landscape, or the absence of a clear direction for improvement. The current population state prevents identifying concrete regions of difficulty.  High diversity coupled with zero convergence suggests challenges in finding promising solutions.', 'reasons_for_difficulty_speculation': 'It is difficult to provide specific reasons without cost information.'}, 'opportunity_regions': {'identification': 'None identified. Without convergence or patterns, potential opportunity regions are not identified.', 'potential': 'Unknown. High diversity suggests that potentially promising regions are still being explored. The search may just be in its exploration phase. Identifying any specific potential areas is impossible without cost data or patterns in the population.', 'strategy': 'Continue to explore the search space until cost data allows identification.'}, 'evolution_direction': {'strategy': 'Gather cost data.  This is critical to assessing the fitness landscape. Once cost data is available, the analysis can be refined to guide a more effective evolution strategy.', 'recommendations': ['If cost data becomes available and convergence is still zero:  Consider a strategy to promote exploitation (e.g., increase selection pressure).', 'If cost data reveals a highly rugged landscape, explore a diverse population for an extended search duration.', 'If cost data is consistent and stable over a short period, then an exploitation strategy may be suitable. For instance, run a local search or intensify the selection pressure to find the best possible solution.', 'If cost data is noisy and varied, then run a more diverse search.']}}
2025-06-22 17:16:35,824 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:16:35,824 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:16:35,825 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 3
- Population: 10 individuals, diversity=High (0.992).  The diversity score suggests that the population is spread out and exploring the search space broadly. This is a positive sign if the search space is large and poorly understood.
- Best cost: nan, Mean cost: nan
- Search space: Ruggedness: Undetermined. With no cost data and no identified high-quality edges, it's impossible to assess ruggedness. The absence of elite solutions and common structure also provides no insight. More data is needed to determine if the landscape is smooth or rugged., Modality: Unknown.  Without cost information or structural patterns (e.g., common subpaths, high-quality edges), we cannot assess the modality of the search space (unimodal vs. multimodal).
- Difficult regions: 3 regions
- Opportunity regions: 3 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early, potentially collapsing.",
    "features": [
      "High cost values initially, followed by extreme negative values and then NaN (Not a Number), in...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:16:35,825 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:16:38,521 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "individual_0": "explore",
    "individual_1": "explore",
    "individual_2": "explore",
    "individual_3": "explore",
    "individual_4": "explore",
    "individual_5": "explore",
    "individual_6": "explore",
    "individual_7": "explore",
    "individual_8": "exploit",
    "individual_9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "step_size_multiplier": 0.1,
      "region_focus_rate": 0.7,
      "difficult_region_bias": 0.8
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size_multiplier": 0.05,
      "crossover_rate": 0.5
    }
  }
}
```

2025-06-22 17:16:38,521 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-06-22 17:16:38,521 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "individual_0": "explore",
    "individual_1": "explore",
    "individual_2": "explore",
    "individual_3": "explore",
    "individual_4": "explore",
    "individual_5": "explore",
    "individual_6": "explore",
    "individual_7": "explore",
    "individual_8": "exploit",
    "individual_9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "step_size_multiplier": 0.1,
      "region_focus_rate": 0.7,
      "difficult_region_bias": 0.8
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size_multiplier": 0.05,
      "crossover_rate": 0.5
    }
  }
}
```

2025-06-22 17:16:38,521 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:16:38,521 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-06-22 17:16:38,521 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "individual_0": "explore",
    "individual_1": "explore",
    "individual_2": "explore",
    "individual_3": "explore",
    "individual_4": "explore",
    "individual_5": "explore",
    "individual_6": "explore",
    "individual_7": "explore",
    "individual_8": "exploit",
    "individual_9": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "mutation_rate": 0.2,
      "step_size_multiplier": 0.1,
      "region_focus_rate": 0.7,
      "difficult_region_bias": 0.8
    },
    "exploit": {
      "mutation_rate": 0.1,
      "step_size_multiplier": 0.05,
      "crossover_rate": 0.5
    }
  }
}
```

2025-06-22 17:16:38,521 - __main__ - INFO - 开始进化阶段
2025-06-22 17:16:38,521 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:16:38,521 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:38,521 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 40 nodes, starting with [6, 1, 10] and ending with [96, 97, 98]
- Cost: nan
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9061224489795918
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:38,521 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:16:42,787 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    6, 2, 15, 22, 30, 38, 45, 46, 47, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 84, 90, 92, 98
  ],
  "modification_strategy": "This path prioritizes exploring unexplored regions and maximizing diversity by deviating significantly from the original path early on. It focuses on visiting the 3 opportunity regions identified while avoiding the 3 difficult regions. The path uses node indices far from the initial and final nodes of the original path, aiming to create a substantially different traversal pattern. The increased diversity weight and risk tolerance are also taken into account by trying new nodes from middle of the search space.",
  "targeted_regions": "All 3 Opportunity Regions were targeted. Avoidance of all 3 difficult regions and aiming to cover unexplored regions."
}
```
2025-06-22 17:16:42,787 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:16:42,787 - ExplorationExpert - INFO - 探索路径生成完成，成本: 3.993450557888093e+252, 路径: [6, 2, 15, 22, 30, 38, 45, 46, 47, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 84, 90, 92, 98]
2025-06-22 17:16:42,787 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 2, 15, 22, 30, 38, 45, 46, 47, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 84, 90, 92, 98], 'cur_cost': 3.993450557888093e+252}
2025-06-22 17:16:42,787 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 17:16:42,787 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:42,787 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [5, 14, 53] and ending with [61, 41, 33]
- Cost: 111029.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9061224489795918
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:42,787 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:16:45,233 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    5, 14, 48, 22, 3, 19, 27, 11, 42, 55, 64, 58, 1, 38, 49, 66, 16, 29, 41, 33
  ],
  "modification_strategy": "This path generation strategy prioritizes exploration of unexplored regions identified by the search context. It leverages the Diversity weight to favor diverse node transitions. The initial portion of the path (5, 14) is preserved to maintain some initial connection, then nodes significantly different from the original path are chosen to explore the unexplored and difficult regions identified. The path then aims to reach the end node (33) via diverse yet efficient node transitions.",
  "targeted_regions": [
    "Unexplored regions",
    "Difficult regions"
  ]
}
```
2025-06-22 17:16:45,233 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:16:45,233 - ExplorationExpert - INFO - 探索路径生成完成，成本: 28860.0, 路径: [5, 14, 48, 22, 3, 19, 27, 11, 42, 55, 64, 58, 1, 38, 49, 66, 16, 29, 41, 33]
2025-06-22 17:16:45,233 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 14, 48, 22, 3, 19, 27, 11, 42, 55, 64, 58, 1, 38, 49, 66, 16, 29, 41, 33], 'cur_cost': 28860.0}
2025-06-22 17:16:45,233 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 17:16:45,233 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:45,233 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 59 nodes, starting with [19, 16, 15] and ending with [17, 18, 0]
- Cost: 22416.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9099099099099099
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:45,233 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:16:48,533 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    19, 2, 5, 8, 11, 14, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 0, 3, 60, 63, 1, 4, 7, 10, 13, 16, 17, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58
  ],
  "modification_strategy": "To generate a significantly different path, I prioritized exploring the 'opportunity regions' indicated in the search context. I used a 'branch and explore' approach, first deviating early from the original path (after node 19) towards an unexplored region. Subsequently, I integrated elements that would ensure a greater sweep of the graph with a higher focus on diversity.  The path attempts to go through all the key unexplored and opportunity regions before arriving to the end node [17, 18, 0].",
  "targeted_regions": [
    "Opportunity regions",
    "Unexplored regions"
  ]
}
```
2025-06-22 17:16:48,535 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:16:48,535 - ExplorationExpert - INFO - 探索路径生成完成，成本: 31858.0, 路径: [19, 2, 5, 8, 11, 14, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 0, 3, 60, 63, 1, 4, 7, 10, 13, 16, 17, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58]
2025-06-22 17:16:48,536 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [19, 2, 5, 8, 11, 14, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 0, 3, 60, 63, 1, 4, 7, 10, 13, 16, 17, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58], 'cur_cost': 31858.0}
2025-06-22 17:16:48,536 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 17:16:48,536 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:48,536 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [44, 1, 34] and ending with [51, 63, 58]
- Cost: 113588.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9060402684563759
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:48,536 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:16:52,967 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    44, 1, 5, 10, 15, 21, 27, 33, 39, 45, 48, 54, 57, 60, 63, 65, 64, 61, 58, 52, 49, 46, 42, 36, 30, 24, 18, 12, 6, 2, 8, 11, 17, 23, 29, 35, 41, 47, 50, 53, 56, 59, 62, 55, 51, 40, 34, 37, 43, 38, 32, 26, 20, 14, 9, 3, 7, 13, 19, 25, 31, 28, 22, 16, 4
  ],
  "modification_strategy": "Heuristic exploration prioritizing unexplored and opportunity regions. The strategy leverages a modified breadth-first search biased towards unexplored areas based on the search context and diversity parameters. It attempts to quickly move into unexplored regions while keeping the overall path relatively shorter. The nodes are selected by considering distance (favoring nodes in the path but not yet visited and close by), opportunity and difficult regions. The path is then generated by prioritizing the nodes that offer higher diversity score (from a seed node), calculated based on nodes that offer low visitation count (unvisited), offer opportunities (unexplored) or are avoiding difficult areas (avoiding).  The search prioritizes going through nodes closest to the current path while maintaining significant variance from it. The path is constructed by alternating exploration of unexplored areas and keeping the path reasonably connected with the existing path. This is a heuristic approach designed for rapid path diversification.",
  "targeted_regions": [
    "Unexplored regions",
    "Opportunity regions"
  ]
}
```
2025-06-22 17:16:52,967 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:16:52,967 - ExplorationExpert - INFO - 探索路径生成完成，成本: 45524.0, 路径: [44, 1, 5, 10, 15, 21, 27, 33, 39, 45, 48, 54, 57, 60, 63, 65, 64, 61, 58, 52, 49, 46, 42, 36, 30, 24, 18, 12, 6, 2, 8, 11, 17, 23, 29, 35, 41, 47, 50, 53, 56, 59, 62, 55, 51, 40, 34, 37, 43, 38, 32, 26, 20, 14, 9, 3, 7, 13, 19, 25, 31, 28, 22, 16, 4]
2025-06-22 17:16:52,967 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [44, 1, 5, 10, 15, 21, 27, 33, 39, 45, 48, 54, 57, 60, 63, 65, 64, 61, 58, 52, 49, 46, 42, 36, 30, 24, 18, 12, 6, 2, 8, 11, 17, 23, 29, 35, 41, 47, 50, 53, 56, 59, 62, 55, 51, 40, 34, 37, 43, 38, 32, 26, 20, 14, 9, 3, 7, 13, 19, 25, 31, 28, 22, 16, 4], 'cur_cost': 45524.0}
2025-06-22 17:16:52,967 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 17:16:52,967 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:52,967 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 21 nodes, starting with [65, 88, 15] and ending with [37, 91, 55]
- Cost: 21066.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8923766816143498
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:52,967 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:16:55,907 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    65, 12, 48, 99, 2, 18, 73, 34, 81, 9, 52, 27, 69, 1, 3, 44, 7, 25, 77, 50, 37
  ],
  "modification_strategy": "This path prioritizes exploration of the 'Unexplored regions' while attempting to avoid the 'Difficult regions'. The algorithm first seeks nodes in the opportunity regions to maximize diversity, and then incorporates alternative path options with varied, strategic changes.",
  "targeted_regions": [
    "Unexplored regions",
    "Opportunity regions"
  ]
}
```
2025-06-22 17:16:55,907 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:16:55,908 - ExplorationExpert - INFO - 探索路径生成完成，成本: 4.0631083727893874e+228, 路径: [65, 12, 48, 99, 2, 18, 73, 34, 81, 9, 52, 27, 69, 1, 3, 44, 7, 25, 77, 50, 37]
2025-06-22 17:16:55,908 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [65, 12, 48, 99, 2, 18, 73, 34, 81, 9, 52, 27, 69, 1, 3, 44, 7, 25, 77, 50, 37], 'cur_cost': 4.0631083727893874e+228}
2025-06-22 17:16:55,908 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-22 17:16:55,908 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:55,908 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [17, 35, 47] and ending with [44, 21, 41]
- Cost: 109138.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8946188340807175
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:55,908 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:16:59,777 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    17, 35, 48, 14, 23, 38, 55, 2, 11, 29, 4, 52, 62, 5, 30, 42, 18, 58, 26, 1, 49, 15, 3, 32, 46, 36, 53, 8, 20, 40, 27, 50, 63, 7, 33, 10, 28, 65, 13, 31, 43, 12, 22, 51, 64, 6, 34, 45, 19, 9, 25, 59, 39, 56, 47, 57, 16, 60, 54, 24, 41, 21, 44
  ],
  "modification_strategy": "The strategy prioritized diversity by drastically altering the node traversal order.  It aimed to include nodes in the unexplored and opportunity regions early in the path to improve the exploration. Risk tolerance was balanced by avoiding the difficult regions as much as possible, focusing on a more spread-out and less linear path to maximize coverage and find alternative routes through the graph structure.",
  "targeted_regions": "Unexplored regions, opportunity regions, and areas adjacent to difficult regions were targeted to encourage exploration of areas less travelled in the original path. Specifically focusing on nodes around indices: 2, 5, 11, 14, 29, 32, 33, 38, 58 and the beginning nodes."
}
```
2025-06-22 17:16:59,777 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:16:59,777 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111606.0, 路径: [17, 35, 48, 14, 23, 38, 55, 2, 11, 29, 4, 52, 62, 5, 30, 42, 18, 58, 26, 1, 49, 15, 3, 32, 46, 36, 53, 8, 20, 40, 27, 50, 63, 7, 33, 10, 28, 65, 13, 31, 43, 12, 22, 51, 64, 6, 34, 45, 19, 9, 25, 59, 39, 56, 47, 57, 16, 60, 54, 24, 41, 21, 44]
2025-06-22 17:16:59,777 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [17, 35, 48, 14, 23, 38, 55, 2, 11, 29, 4, 52, 62, 5, 30, 42, 18, 58, 26, 1, 49, 15, 3, 32, 46, 36, 53, 8, 20, 40, 27, 50, 63, 7, 33, 10, 28, 65, 13, 31, 43, 12, 22, 51, 64, 6, 34, 45, 19, 9, 25, 59, 39, 56, 47, 57, 16, 60, 54, 24, 41, 21, 44], 'cur_cost': 111606.0}
2025-06-22 17:16:59,777 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 17:16:59,777 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:16:59,777 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 35 nodes, starting with [22, 3, 10] and ending with [47, 16, 34]
- Cost: 46451.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9006772009029346
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:16:59,777 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:17:05,545 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    22, 1, 18, 28, 5, 14, 23, 8, 31, 3, 19, 25, 9, 11, 16, 33, 34, 15, 20, 27, 4, 35, 10, 29, 13, 2, 24, 30, 17, 12, 6, 21, 32, 7, 47
  ],
  "modification_strategy": "This approach prioritizes diversity by significantly altering the node sequence and incorporating nodes found in unexplored regions. It strategically navigates through the opportunity regions by choosing a different path through their vicinity. Risk tolerance is managed by making sure nodes near the beginning of the list are still of reasonable quality and not 'too' obscure.",
  "targeted_regions": [
    "Opportunity Region 1",
    "Opportunity Region 2",
    "Opportunity Region 3"
  ]
}
```
2025-06-22 17:17:05,545 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:17:05,545 - ExplorationExpert - INFO - 探索路径生成完成，成本: 45031.0, 路径: [22, 1, 18, 28, 5, 14, 23, 8, 31, 3, 19, 25, 9, 11, 16, 33, 34, 15, 20, 27, 4, 35, 10, 29, 13, 2, 24, 30, 17, 12, 6, 21, 32, 7, 47]
2025-06-22 17:17:05,545 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [22, 1, 18, 28, 5, 14, 23, 8, 31, 3, 19, 25, 9, 11, 16, 33, 34, 15, 20, 27, 4, 35, 10, 29, 13, 2, 24, 30, 17, 12, 6, 21, 32, 7, 47], 'cur_cost': 45031.0}
2025-06-22 17:17:05,545 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-22 17:17:05,545 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:17:05,545 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [26, 12, 3] and ending with [61, 25, 21]
- Cost: 103367.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8939051918735892
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:17:05,545 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:17:12,006 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    26, 12, 3, 17, 48, 5, 38, 14, 28, 41, 10, 57, 2, 33, 63, 27, 35, 42, 19, 51, 8, 59, 24, 31, 45, 1, 13, 66, 50, 4, 16, 36, 9, 60, 23, 30, 43, 6, 15, 65, 21, 40, 53, 7, 34, 52, 20, 29, 56, 18, 47, 39, 62, 11, 55, 49, 54, 22, 46, 32, 44, 58, 64, 25, 21, 61
  ],
  "modification_strategy": "This path generation prioritizes high diversity and explores unexplored regions.  The initial path is fragmented by inserting nodes from opportunity regions earlier in the sequence than the original path. Furthermore, the paths from the opportunity regions are connected more directly, avoiding the original path's tendency to meander.  The diversity weight of 0.7 encourages significantly different node selections. The risk tolerance of 0.6 allows for considering regions that might have been previously avoided, further increasing diversity.",
  "targeted_regions": "Opportunity regions and regions with high potential for exploration were targeted.  Specifically, the approach tried to include nodes from unexplored areas early in the path, and also to distribute nodes from different regions more evenly throughout the path, leading to a more diversified route."
}
```
2025-06-22 17:17:12,006 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:17:12,006 - ExplorationExpert - INFO - 探索路径生成完成，成本: 116495.0, 路径: [26, 12, 3, 17, 48, 5, 38, 14, 28, 41, 10, 57, 2, 33, 63, 27, 35, 42, 19, 51, 8, 59, 24, 31, 45, 1, 13, 66, 50, 4, 16, 36, 9, 60, 23, 30, 43, 6, 15, 65, 21, 40, 53, 7, 34, 52, 20, 29, 56, 18, 47, 39, 62, 11, 55, 49, 54, 22, 46, 32, 44, 58, 64, 25, 21, 61]
2025-06-22 17:17:12,006 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [26, 12, 3, 17, 48, 5, 38, 14, 28, 41, 10, 57, 2, 33, 63, 27, 35, 42, 19, 51, 8, 59, 24, 31, 45, 1, 13, 66, 50, 4, 16, 36, 9, 60, 23, 30, 43, 6, 15, 65, 21, 40, 53, 7, 34, 52, 20, 29, 56, 18, 47, 39, 62, 11, 55, 49, 54, 22, 46, 32, 44, 58, 64, 25, 21, 61], 'cur_cost': 116495.0}
2025-06-22 17:17:12,006 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 17:17:12,006 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:17:12,009 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 15 nodes, starting with [28, 5, 12] and ending with [3, 8, 17]
- Cost: 13338.0
- Key features: Standard path

## Search Context:
- Population diversity: 0.8984198645598194
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:17:12,009 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:17:14,392 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    28, 19, 2, 9, 15, 21, 4, 10, 18, 1, 11, 23, 14, 7, 17, 8, 3
  ],
  "modification_strategy": "This path aims for high diversity by prioritizing nodes in unexplored regions and avoiding difficult regions. It deviates significantly from the current path by choosing a different starting point within the 'unexplored regions'. The path then explores a selection of nodes distributed across different opportunity and unexplored regions while trying to avoid previously mentioned difficult ones. This allows for exploration of under-represented areas and increases diversity. The ending point focuses on the same to validate the solution.",
  "targeted_regions": [
    "Unexplored Regions",
    "Opportunity Regions",
    "Regions to Avoid"
  ]
}
```

2025-06-22 17:17:14,392 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:17:14,392 - ExplorationExpert - INFO - 探索路径生成完成，成本: 14733.0, 路径: [28, 19, 2, 9, 15, 21, 4, 10, 18, 1, 11, 23, 14, 7, 17, 8, 3]
2025-06-22 17:17:14,392 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [28, 19, 2, 9, 15, 21, 4, 10, 18, 1, 11, 23, 14, 7, 17, 8, 3], 'cur_cost': 14733.0}
2025-06-22 17:17:14,392 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-22 17:17:14,392 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:17:14,392 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [20, 23, 24] and ending with [42, 45, 62]
- Cost: 102525.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8966292134831461
- Unexplored regions: 3 opportunity regions
- Opportunity regions: 3 regions
- Difficult regions to avoid: 3 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:17:14,392 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:17:17,930 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    20, 21, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 22, 25
  ],
  "modification_strategy": "Leveraged a path-reversal and segment-rearrangement approach. First, divided the existing path roughly into segments. Then, selected the central segments and reversed them. Subsequently, incorporated nodes from the opportunity regions into the path while avoiding the difficult regions based on the Search Context information. The goal was to create a path that explored unexplored/under-explored sections.",
  "targeted_regions": [
    "Opportunity Region 1",
    "Opportunity Region 2",
    "Opportunity Region 3",
    "Region of Node 22 (Initial Path start)",
     "Region of Node 25 (Initial Path start)",
     "Segment around the path with 61 - 64"
  ]
}
```
2025-06-22 17:17:17,930 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:17:17,930 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24402.0, 路径: [20, 21, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 22, 25]
2025-06-22 17:17:17,930 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [20, 21, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 22, 25], 'cur_cost': 24402.0}
2025-06-22 17:17:17,930 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 2, 15, 22, 30, 38, 45, 46, 47, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 84, 90, 92, 98], 'cur_cost': 3.993450557888093e+252}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 14, 48, 22, 3, 19, 27, 11, 42, 55, 64, 58, 1, 38, 49, 66, 16, 29, 41, 33], 'cur_cost': 28860.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [19, 2, 5, 8, 11, 14, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 0, 3, 60, 63, 1, 4, 7, 10, 13, 16, 17, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58], 'cur_cost': 31858.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [44, 1, 5, 10, 15, 21, 27, 33, 39, 45, 48, 54, 57, 60, 63, 65, 64, 61, 58, 52, 49, 46, 42, 36, 30, 24, 18, 12, 6, 2, 8, 11, 17, 23, 29, 35, 41, 47, 50, 53, 56, 59, 62, 55, 51, 40, 34, 37, 43, 38, 32, 26, 20, 14, 9, 3, 7, 13, 19, 25, 31, 28, 22, 16, 4], 'cur_cost': 45524.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [65, 12, 48, 99, 2, 18, 73, 34, 81, 9, 52, 27, 69, 1, 3, 44, 7, 25, 77, 50, 37], 'cur_cost': 4.0631083727893874e+228}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [17, 35, 48, 14, 23, 38, 55, 2, 11, 29, 4, 52, 62, 5, 30, 42, 18, 58, 26, 1, 49, 15, 3, 32, 46, 36, 53, 8, 20, 40, 27, 50, 63, 7, 33, 10, 28, 65, 13, 31, 43, 12, 22, 51, 64, 6, 34, 45, 19, 9, 25, 59, 39, 56, 47, 57, 16, 60, 54, 24, 41, 21, 44], 'cur_cost': 111606.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [22, 1, 18, 28, 5, 14, 23, 8, 31, 3, 19, 25, 9, 11, 16, 33, 34, 15, 20, 27, 4, 35, 10, 29, 13, 2, 24, 30, 17, 12, 6, 21, 32, 7, 47], 'cur_cost': 45031.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [26, 12, 3, 17, 48, 5, 38, 14, 28, 41, 10, 57, 2, 33, 63, 27, 35, 42, 19, 51, 8, 59, 24, 31, 45, 1, 13, 66, 50, 4, 16, 36, 9, 60, 23, 30, 43, 6, 15, 65, 21, 40, 53, 7, 34, 52, 20, 29, 56, 18, 47, 39, 62, 11, 55, 49, 54, 22, 46, 32, 44, 58, 64, 25, 21, 61], 'cur_cost': 116495.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [28, 19, 2, 9, 15, 21, 4, 10, 18, 1, 11, 23, 14, 7, 17, 8, 3], 'cur_cost': 14733.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [20, 21, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61, 64, 65, 62, 59, 56, 53, 50, 47, 44, 41, 38, 35, 32, 29, 26, 23, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 22, 25], 'cur_cost': 24402.0}}]
2025-06-22 17:17:17,930 - __main__ - INFO - 进化阶段完成
2025-06-22 17:17:17,930 - __main__ - INFO - 开始评估阶段
2025-06-22 17:17:17,930 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 17:17:17,930 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 17:17:17,930 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 3/5
- Population changes:
  * Cost: min nan->14733.0, mean nan->3.9934505578880933e+251
  * Diversity: 0.9061224489795918->0.8352941176470589
  * Elite solutions: 0->1

## Strategy Performance:
- Explore: 10 individuals, success rate 40.0%, avg improvement nan
- Exploit: 0 individuals, success rate 0%, avg improvement 0
- Overall improvement: nan

## Historical Trends:
Last 3 iterations: costs [18914.0, -1.4701865236778527e+82, nan], diversity [0.9883059450223628, 1.0, 0.9922558922558922]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 17:17:23,906 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage with Potential Stagnation",
    "features": {
      "iteration": "3/5 (Early)",
      "cost": "Highly volatile and unstable. Initial improvement followed by severe inflation or NaN values.",
      "diversity": "Moderate, slightly decreasing. Indicates some exploration but potentially losing ground.",
      "elite_solutions": "Presence of elite solution indicates potential for exploitation and indicates a found possible solution."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "40.0% (Moderate)",
      "avg_improvement": "nan (Unable to assess due to NaN)",
      "evaluation": "Explore is achieving some success in finding better solutions, but the inability to report an average improvement is concerning.  The high cost variance suggests that these successful individuals also have a wide variance in their quality. The success rate is within acceptable range but requires further analysis."
    },
    "exploit": {
      "success_rate": "0% (Ineffective)",
      "avg_improvement": "0",
      "evaluation": "Exploitation is entirely absent, which is understandable given the early stage and volatile cost. The cost function might be poorly defined or subject to numeric instability causing NaN results."
    },
    "overall_improvement": "nan (Unable to assess due to NaN)",
    "summary": "The strategy is currently failing to demonstrate overall improvement, primarily due to issues with the cost function and volatile results.  Exploration is showing some promise but not translating into significant gains. Exploitation is not being attempted, which is justified."
  },
  "balance_state": {
    "assessment": "The system is heavily skewed towards exploration, which is appropriate for the early stage. However, the lack of exploitation suggests a fundamental issue - likely in the cost function, or the nature of the problem.",
    "adjustment_needs": "No specific adjustments to the explore/exploit split are needed at this time, but addressing the cost function is paramount."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Investigate and address the NaN and extremely high cost values. This is the *most critical* task. Possible causes include:",
      "details": [
        "Numerical instability in the cost function calculation (overflow, underflow, division by zero).",
        "Incorrectly implemented/scaled cost function.",
        "Errors in the fitness evaluation logic.",
        "Unstable model/simulation parameters causing radical cost changes.",
        "Ensure proper handling of invalid input values."
      ]
    },
    {
      "priority": "High",
      "action": "Refine the Cost Function:",
      "details": [
        "Identify and correct the cause of the extreme cost values and NaN returns.",
        "Ensure that the cost function's output remains within a reasonable range.",
        "Consider scaling the cost function to a more manageable scale for numerical stability.",
      ]
    },
    {
      "priority": "Medium",
      "action": "Monitor and refine exploration strategies.",
      "details": [
        "Evaluate the specific individuals found during exploration and the input values for those individuals.",
        "Consider adjustments to exploration parameters, depending on the source of cost failures."
      ]
    },
    {
      "priority": "Low",
      "action": "Observe the emergence of elite solutions before attempting to exploit.",
      "details": [
        "Do not explicitly apply exploitation until the cost function is reliable and stable."
      ]
    }
  ]
}
```
2025-06-22 17:17:23,910 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 17:17:23,910 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage with Potential Stagnation",
    "features": {
      "iteration": "3/5 (Early)",
      "cost": "Highly volatile and unstable. Initial improvement followed by severe inflation or NaN values.",
      "diversity": "Moderate, slightly decreasing. Indicates some exploration but potentially losing ground.",
      "elite_solutions": "Presence of elite solution indicates potential for exploitation and indicates a found possible solution."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "40.0% (Moderate)",
      "avg_improvement": "nan (Unable to assess due to NaN)",
      "evaluation": "Explore is achieving some success in finding better solutions, but the inability to report an average improvement is concerning.  The high cost variance suggests that these successful individuals also have a wide variance in their quality. The success rate is within acceptable range but requires further analysis."
    },
    "exploit": {
      "success_rate": "0% (Ineffective)",
      "avg_improvement": "0",
      "evaluation": "Exploitation is entirely absent, which is understandable given the early stage and volatile cost. The cost function might be poorly defined or subject to numeric instability causing NaN results."
    },
    "overall_improvement": "nan (Unable to assess due to NaN)",
    "summary": "The strategy is currently failing to demonstrate overall improvement, primarily due to issues with the cost function and volatile results.  Exploration is showing some promise but not translating into significant gains. Exploitation is not being attempted, which is justified."
  },
  "balance_state": {
    "assessment": "The system is heavily skewed towards exploration, which is appropriate for the early stage. However, the lack of exploitation suggests a fundamental issue - likely in the cost function, or the nature of the problem.",
    "adjustment_needs": "No specific adjustments to the explore/exploit split are needed at this time, but addressing the cost function is paramount."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Investigate and address the NaN and extremely high cost values. This is the *most critical* task. Possible causes include:",
      "details": [
        "Numerical instability in the cost function calculation (overflow, underflow, division by zero).",
        "Incorrectly implemented/scaled cost function.",
        "Errors in the fitness evaluation logic.",
        "Unstable model/simulation parameters causing radical cost changes.",
        "Ensure proper handling of invalid input values."
      ]
    },
    {
      "priority": "High",
      "action": "Refine the Cost Function:",
      "details": [
        "Identify and correct the cause of the extreme cost values and NaN returns.",
        "Ensure that the cost function's output remains within a reasonable range.",
        "Consider scaling the cost function to a more manageable scale for numerical stability.",
      ]
    },
    {
      "priority": "Medium",
      "action": "Monitor and refine exploration strategies.",
      "details": [
        "Evaluate the specific individuals found during exploration and the input values for those individuals.",
        "Consider adjustments to exploration parameters, depending on the source of cost failures."
      ]
    },
    {
      "priority": "Low",
      "action": "Observe the emergence of elite solutions before attempting to exploit.",
      "details": [
        "Do not explicitly apply exploitation until the cost function is reliable and stable."
      ]
    }
  ]
}
```
2025-06-22 17:17:23,910 - __main__ - INFO - 评估阶段完成
2025-06-22 17:17:23,910 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Early Stage with Potential Stagnation",
    "features": {
      "iteration": "3/5 (Early)",
      "cost": "Highly volatile and unstable. Initial improvement followed by severe inflation or NaN values.",
      "diversity": "Moderate, slightly decreasing. Indicates some exploration but potentially losing ground.",
      "elite_solutions": "Presence of elite solution indicates potential for exploitation and indicates a found possible solution."
    }
  },
  "strategy_effectiveness": {
    "explore": {
      "success_rate": "40.0% (Moderate)",
      "avg_improvement": "nan (Unable to assess due to NaN)",
      "evaluation": "Explore is achieving some success in finding better solutions, but the inability to report an average improvement is concerning.  The high cost variance suggests that these successful individuals also have a wide variance in their quality. The success rate is within acceptable range but requires further analysis."
    },
    "exploit": {
      "success_rate": "0% (Ineffective)",
      "avg_improvement": "0",
      "evaluation": "Exploitation is entirely absent, which is understandable given the early stage and volatile cost. The cost function might be poorly defined or subject to numeric instability causing NaN results."
    },
    "overall_improvement": "nan (Unable to assess due to NaN)",
    "summary": "The strategy is currently failing to demonstrate overall improvement, primarily due to issues with the cost function and volatile results.  Exploration is showing some promise but not translating into significant gains. Exploitation is not being attempted, which is justified."
  },
  "balance_state": {
    "assessment": "The system is heavily skewed towards exploration, which is appropriate for the early stage. However, the lack of exploitation suggests a fundamental issue - likely in the cost function, or the nature of the problem.",
    "adjustment_needs": "No specific adjustments to the explore/exploit split are needed at this time, but addressing the cost function is paramount."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Investigate and address the NaN and extremely high cost values. This is the *most critical* task. Possible causes include:",
      "details": [
        "Numerical instability in the cost function calculation (overflow, underflow, division by zero).",
        "Incorrectly implemented/scaled cost function.",
        "Errors in the fitness evaluation logic.",
        "Unstable model/simulation parameters causing radical cost changes.",
        "Ensure proper handling of invalid input values."
      ]
    },
    {
      "priority": "High",
      "action": "Refine the Cost Function:",
      "details": [
        "Identify and correct the cause of the extreme cost values and NaN returns.",
        "Ensure that the cost function's output remains within a reasonable range.",
        "Consider scaling the cost function to a more manageable scale for numerical stability.",
      ]
    },
    {
      "priority": "Medium",
      "action": "Monitor and refine exploration strategies.",
      "details": [
        "Evaluate the specific individuals found during exploration and the input values for those individuals.",
        "Consider adjustments to exploration parameters, depending on the source of cost failures."
      ]
    },
    {
      "priority": "Low",
      "action": "Observe the emergence of elite solutions before attempting to exploit.",
      "details": [
        "Do not explicitly apply exploitation until the cost function is reliable and stable."
      ]
    }
  ]
}
```
2025-06-22 17:17:23,912 - __main__ - INFO - 当前最佳适应度: 14733.0
2025-06-22 17:17:23,913 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_3.pkl
2025-06-22 17:17:23,914 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-06-22 17:17:23,914 - __main__ - INFO - 开始分析阶段
2025-06-22 17:17:23,914 - StatsExpert - INFO - 开始统计分析
2025-06-22 17:17:23,916 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 14733.0, 'max': 3.993450557888093e+252, 'mean': 3.9934505578880933e+251, 'std': inf}, 'diversity': 1.0, 'clusters': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0}
2025-06-22 17:17:23,917 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 14733.0, 'max': 3.993450557888093e+252, 'mean': 3.9934505578880933e+251, 'std': inf}, 'diversity_level': 1.0, 'convergence_level': 0.0, 'clustering_info': {'clusters': 10, 'cluster_sizes': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}}
2025-06-22 17:17:23,917 - __main__ - INFO - 更新共享数据: population_diversity
2025-06-22 17:17:23,917 - __main__ - INFO - 更新共享数据: convergence_level
2025-06-22 17:17:23,917 - PathExpert - INFO - 开始路径结构分析
2025-06-22 17:17:23,918 - PathExpert - WARNING - 发现无效的城市索引: [66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 84, 90, 92, 98], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:17:23,918 - PathExpert - WARNING - 发现无效的城市索引: [66], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:17:23,918 - PathExpert - WARNING - 发现无效的城市索引: [99, 73, 81, 69, 77], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:17:23,918 - PathExpert - WARNING - 发现无效的城市索引: [66], 距离矩阵大小: 66，跳过此路径
2025-06-22 17:17:23,919 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=2, tour_length=40
2025-06-22 17:17:23,919 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=15, tour_length=40
2025-06-22 17:17:23,919 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=22, tour_length=40
2025-06-22 17:17:23,919 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=30, tour_length=40
2025-06-22 17:17:23,919 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=38, tour_length=40
2025-06-22 17:17:23,919 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=45, tour_length=40
2025-06-22 17:17:23,919 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=46, tour_length=40
2025-06-22 17:17:23,919 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=46, city2=47, tour_length=40
2025-06-22 17:17:23,919 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=52, tour_length=40
2025-06-22 17:17:23,922 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=53, tour_length=40
2025-06-22 17:17:23,922 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=53, city2=54, tour_length=40
2025-06-22 17:17:23,922 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=54, city2=55, tour_length=40
2025-06-22 17:17:23,922 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=56, tour_length=40
2025-06-22 17:17:23,922 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=56, city2=57, tour_length=40
2025-06-22 17:17:23,922 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=57, city2=58, tour_length=40
2025-06-22 17:17:23,922 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=59, tour_length=40
2025-06-22 17:17:23,922 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=60, tour_length=40
2025-06-22 17:17:23,922 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=61, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=61, city2=62, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=62, city2=63, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=63, city2=64, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=64, city2=65, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=65, city2=0, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=0, city2=1, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=2, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=3, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=4, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=5, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=6, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=7, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=7, city2=8, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=8, city2=9, tour_length=40
2025-06-22 17:17:23,923 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=10, tour_length=40
2025-06-22 17:17:23,925 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=11, tour_length=40
2025-06-22 17:17:23,925 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=12, tour_length=40
2025-06-22 17:17:23,925 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=18, tour_length=40
2025-06-22 17:17:23,925 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=24, tour_length=40
2025-06-22 17:17:23,925 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=26, tour_length=40
2025-06-22 17:17:23,925 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=26, city2=32, tour_length=40
2025-06-22 17:17:23,925 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=6, tour_length=40
2025-06-22 17:17:23,927 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=14, tour_length=20
2025-06-22 17:17:23,927 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=14, city2=48, tour_length=20
2025-06-22 17:17:23,927 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=22, tour_length=20
2025-06-22 17:17:23,927 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=3, tour_length=20
2025-06-22 17:17:23,927 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=19, tour_length=20
2025-06-22 17:17:23,927 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=19, city2=27, tour_length=20
2025-06-22 17:17:23,927 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=11, tour_length=20
2025-06-22 17:17:23,928 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=42, tour_length=20
2025-06-22 17:17:23,928 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=55, tour_length=20
2025-06-22 17:17:23,928 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=64, tour_length=20
2025-06-22 17:17:23,928 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=64, city2=58, tour_length=20
2025-06-22 17:17:23,928 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=1, tour_length=20
2025-06-22 17:17:23,928 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=38, tour_length=20
2025-06-22 17:17:23,928 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=49, tour_length=20
2025-06-22 17:17:23,928 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=0, tour_length=20
2025-06-22 17:17:23,928 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=0, city2=16, tour_length=20
2025-06-22 17:17:23,929 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=16, city2=29, tour_length=20
2025-06-22 17:17:23,929 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=41, tour_length=20
2025-06-22 17:17:23,929 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=41, city2=33, tour_length=20
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=5, tour_length=20
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=19, city2=2, tour_length=62
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=5, tour_length=62
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=8, tour_length=62
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=8, city2=11, tour_length=62
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=14, tour_length=62
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=14, city2=20, tour_length=62
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=20, city2=23, tour_length=62
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=23, city2=26, tour_length=62
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=26, city2=29, tour_length=62
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=32, tour_length=62
2025-06-22 17:17:23,930 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=35, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=38, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=41, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=41, city2=44, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=44, city2=47, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=50, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=53, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=53, city2=56, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=56, city2=59, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=6, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=9, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=12, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=15, tour_length=62
2025-06-22 17:17:23,932 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=18, tour_length=62
2025-06-22 17:17:23,934 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=21, tour_length=62
2025-06-22 17:17:23,934 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=24, tour_length=62
2025-06-22 17:17:23,934 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=27, tour_length=62
2025-06-22 17:17:23,934 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=30, tour_length=62
2025-06-22 17:17:23,934 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=33, tour_length=62
2025-06-22 17:17:23,934 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=36, tour_length=62
2025-06-22 17:17:23,934 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=36, city2=39, tour_length=62
2025-06-22 17:17:23,935 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=39, city2=42, tour_length=62
2025-06-22 17:17:23,935 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=45, tour_length=62
2025-06-22 17:17:23,935 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=48, tour_length=62
2025-06-22 17:17:23,935 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=51, tour_length=62
2025-06-22 17:17:23,935 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=51, city2=54, tour_length=62
2025-06-22 17:17:23,935 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=54, city2=57, tour_length=62
2025-06-22 17:17:23,935 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=57, city2=0, tour_length=62
2025-06-22 17:17:23,936 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=0, city2=3, tour_length=62
2025-06-22 17:17:23,936 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=60, tour_length=62
2025-06-22 17:17:23,936 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=63, tour_length=62
2025-06-22 17:17:23,936 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=63, city2=1, tour_length=62
2025-06-22 17:17:23,936 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=4, tour_length=62
2025-06-22 17:17:23,936 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=7, tour_length=62
2025-06-22 17:17:23,937 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=7, city2=10, tour_length=62
2025-06-22 17:17:23,937 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=13, tour_length=62
2025-06-22 17:17:23,937 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=13, city2=16, tour_length=62
2025-06-22 17:17:23,937 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=16, city2=17, tour_length=62
2025-06-22 17:17:23,938 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=17, city2=22, tour_length=62
2025-06-22 17:17:23,938 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=25, tour_length=62
2025-06-22 17:17:23,938 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=28, tour_length=62
2025-06-22 17:17:23,938 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=28, city2=31, tour_length=62
2025-06-22 17:17:23,938 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=31, city2=34, tour_length=62
2025-06-22 17:17:23,938 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=34, city2=37, tour_length=62
2025-06-22 17:17:23,938 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=37, city2=40, tour_length=62
2025-06-22 17:17:23,938 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=43, tour_length=62
2025-06-22 17:17:23,939 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=43, city2=46, tour_length=62
2025-06-22 17:17:23,939 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=46, city2=49, tour_length=62
2025-06-22 17:17:23,939 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=52, tour_length=62
2025-06-22 17:17:23,939 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=55, tour_length=62
2025-06-22 17:17:23,939 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=58, tour_length=62
2025-06-22 17:17:23,939 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=19, tour_length=62
2025-06-22 17:17:23,939 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=44, city2=1, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=5, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=10, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=15, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=21, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=27, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=33, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=39, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=39, city2=45, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=48, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=54, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=54, city2=57, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=57, city2=60, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=63, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=63, city2=65, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=65, city2=64, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=64, city2=61, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=61, city2=58, tour_length=65
2025-06-22 17:17:23,940 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=52, tour_length=65
2025-06-22 17:17:23,943 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=49, tour_length=65
2025-06-22 17:17:23,943 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=46, tour_length=65
2025-06-22 17:17:23,943 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=46, city2=42, tour_length=65
2025-06-22 17:17:23,943 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=36, tour_length=65
2025-06-22 17:17:23,943 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=36, city2=30, tour_length=65
2025-06-22 17:17:23,943 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=24, tour_length=65
2025-06-22 17:17:23,945 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=18, tour_length=65
2025-06-22 17:17:23,945 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=12, tour_length=65
2025-06-22 17:17:23,945 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=6, tour_length=65
2025-06-22 17:17:23,945 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=2, tour_length=65
2025-06-22 17:17:23,945 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=8, tour_length=65
2025-06-22 17:17:23,945 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=8, city2=11, tour_length=65
2025-06-22 17:17:23,945 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=17, tour_length=65
2025-06-22 17:17:23,945 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=17, city2=23, tour_length=65
2025-06-22 17:17:23,946 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=23, city2=29, tour_length=65
2025-06-22 17:17:23,946 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=35, tour_length=65
2025-06-22 17:17:23,946 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=41, tour_length=65
2025-06-22 17:17:23,946 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=41, city2=47, tour_length=65
2025-06-22 17:17:23,946 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=50, tour_length=65
2025-06-22 17:17:23,946 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=53, tour_length=65
2025-06-22 17:17:23,947 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=53, city2=56, tour_length=65
2025-06-22 17:17:23,947 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=56, city2=59, tour_length=65
2025-06-22 17:17:23,947 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=62, tour_length=65
2025-06-22 17:17:23,947 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=62, city2=55, tour_length=65
2025-06-22 17:17:23,947 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=51, tour_length=65
2025-06-22 17:17:23,947 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=51, city2=40, tour_length=65
2025-06-22 17:17:23,948 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=34, tour_length=65
2025-06-22 17:17:23,948 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=34, city2=37, tour_length=65
2025-06-22 17:17:23,948 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=37, city2=43, tour_length=65
2025-06-22 17:17:23,948 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=43, city2=38, tour_length=65
2025-06-22 17:17:23,948 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=32, tour_length=65
2025-06-22 17:17:23,948 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=26, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=26, city2=20, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=20, city2=14, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=14, city2=9, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=3, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=7, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=7, city2=13, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=13, city2=19, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=19, city2=25, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=31, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=31, city2=28, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=28, city2=22, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=16, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=16, city2=4, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=44, tour_length=65
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=65, city2=12, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=48, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=33, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=2, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=18, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=7, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=7, city2=34, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=34, city2=15, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=9, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=52, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=27, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=3, tour_length=21
2025-06-22 17:17:23,949 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=1, tour_length=21
2025-06-22 17:17:23,953 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=3, tour_length=21
2025-06-22 17:17:23,953 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=44, tour_length=21
2025-06-22 17:17:23,953 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=44, city2=7, tour_length=21
2025-06-22 17:17:23,953 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=7, city2=25, tour_length=21
2025-06-22 17:17:23,953 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=11, tour_length=21
2025-06-22 17:17:23,953 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=50, tour_length=21
2025-06-22 17:17:23,953 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=37, tour_length=21
2025-06-22 17:17:23,953 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=37, city2=65, tour_length=21
2025-06-22 17:17:23,954 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=17, city2=35, tour_length=63
2025-06-22 17:17:23,954 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=48, tour_length=63
2025-06-22 17:17:23,954 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=14, tour_length=63
2025-06-22 17:17:23,955 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=14, city2=23, tour_length=63
2025-06-22 17:17:23,955 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=23, city2=38, tour_length=63
2025-06-22 17:17:23,955 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=55, tour_length=63
2025-06-22 17:17:23,955 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=2, tour_length=63
2025-06-22 17:17:23,955 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=11, tour_length=63
2025-06-22 17:17:23,955 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=29, tour_length=63
2025-06-22 17:17:23,955 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=4, tour_length=63
2025-06-22 17:17:23,955 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=52, tour_length=63
2025-06-22 17:17:23,956 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=62, tour_length=63
2025-06-22 17:17:23,956 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=62, city2=5, tour_length=63
2025-06-22 17:17:23,956 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=30, tour_length=63
2025-06-22 17:17:23,956 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=42, tour_length=63
2025-06-22 17:17:23,956 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=18, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=58, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=26, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=26, city2=1, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=49, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=15, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=3, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=32, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=46, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=46, city2=36, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=36, city2=53, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=53, city2=8, tour_length=63
2025-06-22 17:17:23,957 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=8, city2=20, tour_length=63
2025-06-22 17:17:23,959 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=20, city2=40, tour_length=63
2025-06-22 17:17:23,959 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=27, tour_length=63
2025-06-22 17:17:23,959 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=50, tour_length=63
2025-06-22 17:17:23,959 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=63, tour_length=63
2025-06-22 17:17:23,959 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=63, city2=7, tour_length=63
2025-06-22 17:17:23,960 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=7, city2=33, tour_length=63
2025-06-22 17:17:23,960 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=10, tour_length=63
2025-06-22 17:17:23,960 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=28, tour_length=63
2025-06-22 17:17:23,960 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=28, city2=65, tour_length=63
2025-06-22 17:17:23,960 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=65, city2=13, tour_length=63
2025-06-22 17:17:23,960 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=13, city2=31, tour_length=63
2025-06-22 17:17:23,960 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=31, city2=43, tour_length=63
2025-06-22 17:17:23,960 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=43, city2=12, tour_length=63
2025-06-22 17:17:23,961 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=22, tour_length=63
2025-06-22 17:17:23,961 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=51, tour_length=63
2025-06-22 17:17:23,961 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=51, city2=64, tour_length=63
2025-06-22 17:17:23,961 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=64, city2=6, tour_length=63
2025-06-22 17:17:23,961 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=34, tour_length=63
2025-06-22 17:17:23,961 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=34, city2=45, tour_length=63
2025-06-22 17:17:23,962 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=19, tour_length=63
2025-06-22 17:17:23,962 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=19, city2=9, tour_length=63
2025-06-22 17:17:23,962 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=25, tour_length=63
2025-06-22 17:17:23,962 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=59, tour_length=63
2025-06-22 17:17:23,963 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=39, tour_length=63
2025-06-22 17:17:23,963 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=39, city2=56, tour_length=63
2025-06-22 17:17:23,963 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=56, city2=47, tour_length=63
2025-06-22 17:17:23,963 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=57, tour_length=63
2025-06-22 17:17:23,963 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=57, city2=16, tour_length=63
2025-06-22 17:17:23,963 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=16, city2=60, tour_length=63
2025-06-22 17:17:23,963 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=54, tour_length=63
2025-06-22 17:17:23,963 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=54, city2=24, tour_length=63
2025-06-22 17:17:23,963 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=41, tour_length=63
2025-06-22 17:17:23,963 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=41, city2=21, tour_length=63
2025-06-22 17:17:23,965 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=44, tour_length=63
2025-06-22 17:17:23,965 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=44, city2=17, tour_length=63
2025-06-22 17:17:23,965 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=1, tour_length=35
2025-06-22 17:17:23,965 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=18, tour_length=35
2025-06-22 17:17:23,965 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=28, tour_length=35
2025-06-22 17:17:23,965 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=28, city2=5, tour_length=35
2025-06-22 17:17:23,965 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=5, city2=14, tour_length=35
2025-06-22 17:17:23,965 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=14, city2=23, tour_length=35
2025-06-22 17:17:23,965 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=23, city2=8, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=8, city2=31, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=31, city2=3, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=19, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=19, city2=25, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=9, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=11, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=16, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=16, city2=33, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=34, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=34, city2=15, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=20, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=20, city2=27, tour_length=35
2025-06-22 17:17:23,966 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=4, tour_length=35
2025-06-22 17:17:23,968 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=35, tour_length=35
2025-06-22 17:17:23,968 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=10, tour_length=35
2025-06-22 17:17:23,968 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=29, tour_length=35
2025-06-22 17:17:23,968 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=13, tour_length=35
2025-06-22 17:17:23,968 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=13, city2=2, tour_length=35
2025-06-22 17:17:23,968 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=24, tour_length=35
2025-06-22 17:17:23,969 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=30, tour_length=35
2025-06-22 17:17:23,969 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=17, tour_length=35
2025-06-22 17:17:23,969 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=17, city2=12, tour_length=35
2025-06-22 17:17:23,969 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=12, city2=6, tour_length=35
2025-06-22 17:17:23,969 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=6, city2=21, tour_length=35
2025-06-22 17:17:23,969 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=32, tour_length=35
2025-06-22 17:17:23,970 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=7, tour_length=35
2025-06-22 17:17:23,970 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=7, city2=47, tour_length=35
2025-06-22 17:17:23,970 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=22, tour_length=35
2025-06-22 17:17:23,970 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=28, city2=19, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=19, city2=2, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=2, city2=9, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=9, city2=15, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=15, city2=21, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=4, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=4, city2=10, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=10, city2=18, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=18, city2=1, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=1, city2=11, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=11, city2=23, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=23, city2=14, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=14, city2=7, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=7, city2=17, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=17, city2=8, tour_length=17
2025-06-22 17:17:23,971 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=8, city2=3, tour_length=17
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=3, city2=28, tour_length=17
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=20, city2=21, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=21, city2=28, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=28, city2=31, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=31, city2=34, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=34, city2=37, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=37, city2=40, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=40, city2=43, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=43, city2=46, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=46, city2=49, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=49, city2=52, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=52, city2=55, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=55, city2=58, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=58, city2=61, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=61, city2=64, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=64, city2=65, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=65, city2=62, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=62, city2=59, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=59, city2=56, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=56, city2=53, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=53, city2=50, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=50, city2=47, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=47, city2=44, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=44, city2=41, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=41, city2=38, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=38, city2=35, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=35, city2=32, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=32, city2=29, tour_length=46
2025-06-22 17:17:23,974 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=29, city2=26, tour_length=46
2025-06-22 17:17:23,978 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=26, city2=23, tour_length=46
2025-06-22 17:17:23,978 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=23, city2=24, tour_length=46
2025-06-22 17:17:23,978 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=24, city2=27, tour_length=46
2025-06-22 17:17:23,978 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=27, city2=30, tour_length=46
2025-06-22 17:17:23,978 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=30, city2=33, tour_length=46
2025-06-22 17:17:23,978 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=33, city2=36, tour_length=46
2025-06-22 17:17:23,978 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=36, city2=39, tour_length=46
2025-06-22 17:17:23,978 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=39, city2=42, tour_length=46
2025-06-22 17:17:23,978 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=42, city2=45, tour_length=46
2025-06-22 17:17:23,980 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=45, city2=48, tour_length=46
2025-06-22 17:17:23,980 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=48, city2=51, tour_length=46
2025-06-22 17:17:23,980 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=51, city2=54, tour_length=46
2025-06-22 17:17:23,980 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=54, city2=57, tour_length=46
2025-06-22 17:17:23,980 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=57, city2=60, tour_length=46
2025-06-22 17:17:23,980 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=60, city2=63, tour_length=46
2025-06-22 17:17:23,980 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=63, city2=22, tour_length=46
2025-06-22 17:17:23,980 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=22, city2=25, tour_length=46
2025-06-22 17:17:23,980 - path_structure_analyzer - WARNING - 城市索引超出范围: city1=25, city2=20, tour_length=46
2025-06-22 17:17:23,982 - PathExpert - INFO - 路径结构分析完成
2025-06-22 17:17:23,982 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [], 'low_frequency_edges': []}, 'low_quality_regions': [{'region': [9, 25, 59, 39, 56, 47, 57, 16, 60], 'cost': 19952.0, 'size': 9}, {'region': [3, 44, 7, 25, 11, 50, 37, 65], 'cost': 16541.0, 'size': 8}, {'region': [7, 33, 10, 28, 65, 13], 'cost': 11059.0, 'size': 6}, {'region': [18, 58, 26, 1, 49], 'cost': 9308.0, 'size': 5}, {'region': [3, 32, 46, 36, 53], 'cost': 9111.0, 'size': 5}]}
2025-06-22 17:17:23,982 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-06-22 17:17:23,982 - __main__ - INFO - 更新共享数据: difficult_regions
2025-06-22 17:17:23,982 - EliteExpert - INFO - 开始精英解分析
2025-06-22 17:17:23,982 - EliteExpert - INFO - 精英解分析完成
2025-06-22 17:17:23,982 - __main__ - INFO - 精英专家分析报告: {'elite_count': 3, 'elite_common_features': {'common_edges': {'(22, 12)': 1.0, '(12, 17)': 1.0, '(20, 21)': 1.0, '(62, 59)': 1.0, '(59, 56)': 1.0}, 'common_edge_ratio': 0.07575757575757576}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 5186.0, 'avg_gap': 3.9934505578880933e+251}, 'structure_gap': {'unique_elite_edges': 128, 'unique_pop_edges': 367, 'common_edges': 26}}, 'elite_diversity': {'diversity_score': 0.3939393939393939}}
2025-06-22 17:17:23,984 - __main__ - INFO - 更新共享数据: elite_features
2025-06-22 17:17:23,984 - LandscapeExpert - INFO - 开始景观分析
2025-06-22 17:17:23,984 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-06-22 17:17:23,984 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Search Space Analysis Expert

## Data Summary:
- Population: 10 individuals
- Costs: min=14733.0, max=3.993450557888093e+252, mean=3.9934505578880933e+251, std=inf
- Diversity: 1.0, Convergence: 0.0
- Structure: 0 high quality edges, 0 common subpaths
- Elite: 0 solutions with 1 fixed nodes

## Key Relationships:
- Elite solutions share 0% of edges
- 5 identified difficult regions
- 0 identified opportunity regions

## Task:
Analyze the search space and population state. Identify:
1. Search space characteristics (ruggedness, modality)
2. Population state (diversity, convergence)
3. Difficult regions and challenges
4. Opportunity regions with potential
5. Recommended evolution direction

## Output Format:
Provide a JSON with these keys:
- search_space_features: Overall characteristics
- population_state: Current state assessment
- difficult_regions: Identified challenges
- opportunity_regions: Promising areas
- evolution_direction: Recommended strategy

2025-06-22 17:17:30,575 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "Extremely Rugged. The vast range of cost values (min to max spanning many orders of magnitude) combined with the infinite standard deviation strongly suggests a highly uneven and potentially chaotic fitness landscape.",
    "modality": "Potentially Multi-Modal. The presence of difficult regions and the lack of convergence combined with no elite solutions indicates the search space may have multiple local optima or plateaus, making it hard for the algorithm to find the global optimum."
  },
  "population_state": {
    "diversity": "High. A diversity of 1.0 suggests that individuals explore very different areas of the search space. This aligns with the vast cost range and indicates that the solutions are not converging towards a similar area.",
    "convergence": "Extremely Low. A convergence of 0.0 combined with the lack of elite solutions strongly indicates the population has not found any promising solution and has not converged towards a specific, optimal solution. The mean cost being orders of magnitude higher than the minimum cost further indicates a lack of convergence and likely a very difficult search landscape."
  },
  "difficult_regions": {
    "challenges": "The 5 identified difficult regions highlight areas of the search space that the algorithm is struggling to navigate. The high cost and lack of edge sharing imply that these regions are either highly complex, contain many local optima, or are simply isolated regions difficult to traverse due to the landscape's characteristics.",
    "characteristics": "These regions are likely characterized by steep gradients, plateaus, or traps that prevent effective progress. The lack of convergence also suggests that these regions may be disconnected, or only sparsely connected."
  },
  "opportunity_regions": {
    "potential": "None identified. The current data indicates that the algorithm has not yet found any promising areas, and the absence of opportunity regions suggests a challenging landscape with few immediately accessible optimal solutions. The search space is currently being thoroughly explored, but without any signs of exploitation towards promising regions.",
    "considerations": "The lack of identified opportunity regions doesn't mean they don't exist. It may be that the current search strategy is not well-suited to find them (e.g., local search might miss them), or that the solution is exceptionally difficult to locate."
  },
  "evolution_direction": {
    "strategy": "Exploration-focused, with attempts to refine search within promising areas if they become apparent. Given the highly rugged and potentially multi-modal landscape, the focus should be on exploration to find a broader understanding of the search space.",
    "recommendations": [
      "Increase mutation rates: To foster exploration of previously uncharted areas of the search space.",
      "Diversify selection criteria: If selection is elitist, introduce some form of stochastic selection (e.g., roulette wheel, tournament) to allow less fit but potentially more diverse solutions to survive and contribute to exploration.",
      "Consider global optimization strategies: Strategies that escape from local optima by providing a high probability of random moves, such as simulated annealing or stochastic hill climbing, should be considered. Or using different algorithms.",
      "Implement niching or speciation: To encourage the population to explore different parts of the search space simultaneously, potentially identifying and isolating solutions in the five difficult regions. This can prevent the algorithm from getting stuck in any single region too early.",
      "Monitor Edge sharing and Subpaths: Keep tracking of common subpaths or highly connected subgraphs to identify promising sub-regions, even if the search space as a whole is challenging.",
      "Adaptive parameters: Implement mechanisms to adapt parameters such as mutation rate or selection pressure based on the performance of the population (e.g., reduce the mutation rate as convergence begins to improve).",
      "Review Cost Function: Investigate if a different cost function or pre-processing/normalization of the data may be useful to smooth out the rugged landscape. The high costs suggest a scaling issue may be distorting the landscape.",
      "Consider Restarting: If the population hasn't improved significantly after a sufficient number of generations, restarting the search process (potentially with different parameters) may be necessary. "

    ]
  }
}
```
2025-06-22 17:17:30,575 - LandscapeExpert - INFO - =====景观分析完成====
2025-06-22 17:17:30,575 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'Extremely Rugged. The vast range of cost values (min to max spanning many orders of magnitude) combined with the infinite standard deviation strongly suggests a highly uneven and potentially chaotic fitness landscape.', 'modality': 'Potentially Multi-Modal. The presence of difficult regions and the lack of convergence combined with no elite solutions indicates the search space may have multiple local optima or plateaus, making it hard for the algorithm to find the global optimum.'}, 'population_state': {'diversity': 'High. A diversity of 1.0 suggests that individuals explore very different areas of the search space. This aligns with the vast cost range and indicates that the solutions are not converging towards a similar area.', 'convergence': 'Extremely Low. A convergence of 0.0 combined with the lack of elite solutions strongly indicates the population has not found any promising solution and has not converged towards a specific, optimal solution. The mean cost being orders of magnitude higher than the minimum cost further indicates a lack of convergence and likely a very difficult search landscape.'}, 'difficult_regions': {'challenges': "The 5 identified difficult regions highlight areas of the search space that the algorithm is struggling to navigate. The high cost and lack of edge sharing imply that these regions are either highly complex, contain many local optima, or are simply isolated regions difficult to traverse due to the landscape's characteristics.", 'characteristics': 'These regions are likely characterized by steep gradients, plateaus, or traps that prevent effective progress. The lack of convergence also suggests that these regions may be disconnected, or only sparsely connected.'}, 'opportunity_regions': {'potential': 'None identified. The current data indicates that the algorithm has not yet found any promising areas, and the absence of opportunity regions suggests a challenging landscape with few immediately accessible optimal solutions. The search space is currently being thoroughly explored, but without any signs of exploitation towards promising regions.', 'considerations': "The lack of identified opportunity regions doesn't mean they don't exist. It may be that the current search strategy is not well-suited to find them (e.g., local search might miss them), or that the solution is exceptionally difficult to locate."}, 'evolution_direction': {'strategy': 'Exploration-focused, with attempts to refine search within promising areas if they become apparent. Given the highly rugged and potentially multi-modal landscape, the focus should be on exploration to find a broader understanding of the search space.', 'recommendations': ['Increase mutation rates: To foster exploration of previously uncharted areas of the search space.', 'Diversify selection criteria: If selection is elitist, introduce some form of stochastic selection (e.g., roulette wheel, tournament) to allow less fit but potentially more diverse solutions to survive and contribute to exploration.', 'Consider global optimization strategies: Strategies that escape from local optima by providing a high probability of random moves, such as simulated annealing or stochastic hill climbing, should be considered. Or using different algorithms.', 'Implement niching or speciation: To encourage the population to explore different parts of the search space simultaneously, potentially identifying and isolating solutions in the five difficult regions. This can prevent the algorithm from getting stuck in any single region too early.', 'Monitor Edge sharing and Subpaths: Keep tracking of common subpaths or highly connected subgraphs to identify promising sub-regions, even if the search space as a whole is challenging.', 'Adaptive parameters: Implement mechanisms to adapt parameters such as mutation rate or selection pressure based on the performance of the population (e.g., reduce the mutation rate as convergence begins to improve).', 'Review Cost Function: Investigate if a different cost function or pre-processing/normalization of the data may be useful to smooth out the rugged landscape. The high costs suggest a scaling issue may be distorting the landscape.', "Consider Restarting: If the population hasn't improved significantly after a sufficient number of generations, restarting the search process (potentially with different parameters) may be necessary. "]}}
2025-06-22 17:17:30,578 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-06-22 17:17:30,578 - __main__ - INFO - 分析阶段完成
2025-06-22 17:17:30,578 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'Extremely Rugged. The vast range of cost values (min to max spanning many orders of magnitude) combined with the infinite standard deviation strongly suggests a highly uneven and potentially chaotic fitness landscape.', 'modality': 'Potentially Multi-Modal. The presence of difficult regions and the lack of convergence combined with no elite solutions indicates the search space may have multiple local optima or plateaus, making it hard for the algorithm to find the global optimum.'}, 'population_state': {'diversity': 'High. A diversity of 1.0 suggests that individuals explore very different areas of the search space. This aligns with the vast cost range and indicates that the solutions are not converging towards a similar area.', 'convergence': 'Extremely Low. A convergence of 0.0 combined with the lack of elite solutions strongly indicates the population has not found any promising solution and has not converged towards a specific, optimal solution. The mean cost being orders of magnitude higher than the minimum cost further indicates a lack of convergence and likely a very difficult search landscape.'}, 'difficult_regions': {'challenges': "The 5 identified difficult regions highlight areas of the search space that the algorithm is struggling to navigate. The high cost and lack of edge sharing imply that these regions are either highly complex, contain many local optima, or are simply isolated regions difficult to traverse due to the landscape's characteristics.", 'characteristics': 'These regions are likely characterized by steep gradients, plateaus, or traps that prevent effective progress. The lack of convergence also suggests that these regions may be disconnected, or only sparsely connected.'}, 'opportunity_regions': {'potential': 'None identified. The current data indicates that the algorithm has not yet found any promising areas, and the absence of opportunity regions suggests a challenging landscape with few immediately accessible optimal solutions. The search space is currently being thoroughly explored, but without any signs of exploitation towards promising regions.', 'considerations': "The lack of identified opportunity regions doesn't mean they don't exist. It may be that the current search strategy is not well-suited to find them (e.g., local search might miss them), or that the solution is exceptionally difficult to locate."}, 'evolution_direction': {'strategy': 'Exploration-focused, with attempts to refine search within promising areas if they become apparent. Given the highly rugged and potentially multi-modal landscape, the focus should be on exploration to find a broader understanding of the search space.', 'recommendations': ['Increase mutation rates: To foster exploration of previously uncharted areas of the search space.', 'Diversify selection criteria: If selection is elitist, introduce some form of stochastic selection (e.g., roulette wheel, tournament) to allow less fit but potentially more diverse solutions to survive and contribute to exploration.', 'Consider global optimization strategies: Strategies that escape from local optima by providing a high probability of random moves, such as simulated annealing or stochastic hill climbing, should be considered. Or using different algorithms.', 'Implement niching or speciation: To encourage the population to explore different parts of the search space simultaneously, potentially identifying and isolating solutions in the five difficult regions. This can prevent the algorithm from getting stuck in any single region too early.', 'Monitor Edge sharing and Subpaths: Keep tracking of common subpaths or highly connected subgraphs to identify promising sub-regions, even if the search space as a whole is challenging.', 'Adaptive parameters: Implement mechanisms to adapt parameters such as mutation rate or selection pressure based on the performance of the population (e.g., reduce the mutation rate as convergence begins to improve).', 'Review Cost Function: Investigate if a different cost function or pre-processing/normalization of the data may be useful to smooth out the rugged landscape. The high costs suggest a scaling issue may be distorting the landscape.', "Consider Restarting: If the population hasn't improved significantly after a sufficient number of generations, restarting the search process (potentially with different parameters) may be necessary. "]}}
2025-06-22 17:17:30,578 - __main__ - INFO - 开始策略分配阶段
2025-06-22 17:17:30,578 - StrategyExpert - INFO - 开始策略分配分析
2025-06-22 17:17:30,578 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert

## Current State:
- Iteration: 4
- Population: 10 individuals, diversity=High. A diversity of 1.0 suggests that individuals explore very different areas of the search space. This aligns with the vast cost range and indicates that the solutions are not converging towards a similar area.
- Best cost: 14733.0, Mean cost: 3.9934505578880933e+251
- Search space: Ruggedness: Extremely Rugged. The vast range of cost values (min to max spanning many orders of magnitude) combined with the infinite standard deviation strongly suggests a highly uneven and potentially chaotic fitness landscape., Modality: Potentially Multi-Modal. The presence of difficult regions and the lack of convergence combined with no elite solutions indicates the search space may have multiple local optima or plateaus, making it hard for the algorithm to find the global optimum.
- Difficult regions: 2 regions
- Opportunity regions: 2 regions

## Previous Results:
- Previous explore success rate: N/A
- Previous exploit success rate: N/A
- Previous feedback: ```json
{
  "evolution_stage": {
    "stage": "Early Stage with Potential Stagnation",
    "features": {
      "iteration": "3/5 (Early)",
      "cost": "Highly volatile and unstable. Initial improvem...

## Task:
1. Determine optimal exploration/exploitation balance
2. Assign appropriate strategies to each individual
3. Set strategy parameters based on current state

## Output Format:
Provide a JSON with these keys:
- global_explore_ratio: Float between 0.0-1.0
- individual_assignments: Map of individual IDs to "explore" or "exploit"
- strategy_parameters: Parameters for each strategy type

2025-06-22 17:17:30,580 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-06-22 17:17:32,902 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "individual_1": "explore",
    "individual_2": "explore",
    "individual_3": "explore",
    "individual_4": "explore",
    "individual_5": "explore",
    "individual_6": "explore",
    "individual_7": "explore",
    "individual_8": "explore",
    "individual_9": "exploit",
    "individual_10": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "step_size": 0.5,
      "mutation_rate": 0.1,
      "search_radius_multiplier": 1.0
    },
    "exploit": {
      "step_size": 0.1,
      "mutation_rate": 0.01,
      "search_radius_multiplier": 0.5
    }
  }
}
```

2025-06-22 17:17:32,902 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-06-22 17:17:32,902 - __main__ - INFO - 策略分配报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "individual_1": "explore",
    "individual_2": "explore",
    "individual_3": "explore",
    "individual_4": "explore",
    "individual_5": "explore",
    "individual_6": "explore",
    "individual_7": "explore",
    "individual_8": "explore",
    "individual_9": "exploit",
    "individual_10": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "step_size": 0.5,
      "mutation_rate": 0.1,
      "search_radius_multiplier": 1.0
    },
    "exploit": {
      "step_size": 0.1,
      "mutation_rate": 0.01,
      "search_radius_multiplier": 0.5
    }
  }
}
```

2025-06-22 17:17:32,904 - __main__ - INFO - 策略分配阶段完成
2025-06-22 17:17:32,904 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore']
2025-06-22 17:17:32,904 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "individual_1": "explore",
    "individual_2": "explore",
    "individual_3": "explore",
    "individual_4": "explore",
    "individual_5": "explore",
    "individual_6": "explore",
    "individual_7": "explore",
    "individual_8": "explore",
    "individual_9": "exploit",
    "individual_10": "exploit"
  },
  "strategy_parameters": {
    "explore": {
      "step_size": 0.5,
      "mutation_rate": 0.1,
      "search_radius_multiplier": 1.0
    },
    "exploit": {
      "step_size": 0.1,
      "mutation_rate": 0.01,
      "search_radius_multiplier": 0.5
    }
  }
}
```

2025-06-22 17:17:32,904 - __main__ - INFO - 开始进化阶段
2025-06-22 17:17:32,904 - __main__ - INFO - 为个体 0 生成探索路径
2025-06-22 17:17:32,904 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:17:32,904 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 40 nodes, starting with [6, 2, 15] and ending with [90, 92, 98]
- Cost: 3.993450557888093e+252
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8352941176470589
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:17:32,904 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:17:36,802 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    6, 2, 15, 18, 22, 28, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 66, 69, 72, 75, 78, 81, 84, 87, 90, 93, 96, 99, 102, 105, 108, 111, 114, 117, 120, 123, 126, 129, 98
  ],
  "modification_strategy": "Prioritized exploration of opportunity regions and diversification. The strategy emphasizes jumping to nodes associated with the 'opportunity regions' identified in the search context, and then progressively builds the path to increase diversity. This path deviates significantly from the previous one while targeting the identified opportunity regions in an effort to decrease the long path by finding a shorter, more efficient way.",
  "targeted_regions": [
    "Opportunity Region 1",
    "Opportunity Region 2"
  ]
}
```
2025-06-22 17:17:36,802 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:17:36,802 - ExplorationExpert - INFO - 探索路径生成完成，成本: -3.5381512267520314e+307, 路径: [6, 2, 15, 18, 22, 28, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 66, 69, 72, 75, 78, 81, 84, 87, 90, 93, 96, 99, 102, 105, 108, 111, 114, 117, 120, 123, 126, 129, 98]
2025-06-22 17:17:36,802 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [6, 2, 15, 18, 22, 28, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 66, 69, 72, 75, 78, 81, 84, 87, 90, 93, 96, 99, 102, 105, 108, 111, 114, 117, 120, 123, 126, 129, 98], 'cur_cost': -3.5381512267520314e+307}
2025-06-22 17:17:36,802 - __main__ - INFO - 为个体 1 生成探索路径
2025-06-22 17:17:36,802 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:17:36,802 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 20 nodes, starting with [5, 14, 48] and ending with [29, 41, 33]
- Cost: 28860.0
- Key features: Standard path

## Search Context:
- Population diversity: 0.8117647058823529
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:17:36,802 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:17:39,023 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [5, 16, 18, 20, 23, 25, 26, 28, 29, 31, 34, 36, 37, 39, 40, 42, 45, 46, 48, 50],
  "modification_strategy": "Prioritized exploration of opportunity regions and diversification away from the current path. The strategy focused on selecting nodes that are geographically distant from the original path and that are present in the 'opportunity regions' identified in the search context. Risk tolerance was considered when selecting nodes to avoid traversing particularly difficult regions. The diversity weight was prioritized to maximize path dissimilarity.",
  "targeted_regions": ["Opportunity Region 1", "Opportunity Region 2"]
}
```
2025-06-22 17:17:39,023 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:17:39,024 - ExplorationExpert - INFO - 探索路径生成完成，成本: 7782.0, 路径: [5, 16, 18, 20, 23, 25, 26, 28, 29, 31, 34, 36, 37, 39, 40, 42, 45, 46, 48, 50]
2025-06-22 17:17:39,024 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [5, 16, 18, 20, 23, 25, 26, 28, 29, 31, 34, 36, 37, 39, 40, 42, 45, 46, 48, 50], 'cur_cost': 7782.0}
2025-06-22 17:17:39,024 - __main__ - INFO - 为个体 2 生成探索路径
2025-06-22 17:17:39,024 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:17:39,024 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 62 nodes, starting with [19, 2, 5] and ending with [52, 55, 58]
- Cost: 31858.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8141176470588235
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:17:39,024 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:17:42,442 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    19, 3, 8, 12, 25, 31, 40, 42, 45, 49, 51, 53, 56, 59, 61, 58, 55, 52, 50, 47, 44, 39, 36, 32, 28, 22, 17, 13, 9, 6, 4, 1, 0, 2, 5, 7, 10, 14, 18, 21, 24, 29, 33, 37, 41, 43, 46, 48, 54, 57, 60, 26, 30, 34, 38, 27, 23, 20, 16, 15, 11, 58
  ],
  "modification_strategy": "This strategy prioritizes significant diversification by: 1) Changing the initial and end sections of the path to explore different potential starting and ending areas. 2) Introducing significant variations in the middle sections to navigate through unexplored opportunity regions, aiming to avoid difficult regions by diverting through less demanding paths.  3) Utilizing a more balanced node selection process in the center to achieve more even distribution of nodes for a more varied path.",
  "targeted_regions": [
    "Unexplored opportunity regions - focusing on nodes identified as having lower visitation during previous iterations",
    "Nodes adjacent to difficult regions, where a path can be maintained via alternate less riskier nodes around the area."
  ]
}
```
2025-06-22 17:17:42,442 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:17:42,442 - ExplorationExpert - INFO - 探索路径生成完成，成本: 38219.0, 路径: [19, 3, 8, 12, 25, 31, 40, 42, 45, 49, 51, 53, 56, 59, 61, 58, 55, 52, 50, 47, 44, 39, 36, 32, 28, 22, 17, 13, 9, 6, 4, 1, 0, 2, 5, 7, 10, 14, 18, 21, 24, 29, 33, 37, 41, 43, 46, 48, 54, 57, 60, 26, 30, 34, 38, 27, 23, 20, 16, 15, 11, 58]
2025-06-22 17:17:42,442 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [19, 3, 8, 12, 25, 31, 40, 42, 45, 49, 51, 53, 56, 59, 61, 58, 55, 52, 50, 47, 44, 39, 36, 32, 28, 22, 17, 13, 9, 6, 4, 1, 0, 2, 5, 7, 10, 14, 18, 21, 24, 29, 33, 37, 41, 43, 46, 48, 54, 57, 60, 26, 30, 34, 38, 27, 23, 20, 16, 15, 11, 58], 'cur_cost': 38219.0}
2025-06-22 17:17:42,442 - __main__ - INFO - 为个体 3 生成探索路径
2025-06-22 17:17:42,442 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:17:42,446 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 65 nodes, starting with [44, 1, 5] and ending with [22, 16, 4]
- Cost: 45524.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8611764705882353
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:17:42,446 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:17:46,772 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [44, 3, 21, 54, 18, 33, 9, 29, 47, 12, 51, 2, 14, 39, 11, 6, 24, 57, 40, 50, 13, 36, 10, 30, 43, 35, 27, 58, 7, 17, 48, 34, 20, 49, 19, 41, 15, 31, 52, 25, 4, 42, 23, 37, 8, 28, 59, 1, 26, 32, 55, 5, 56, 38, 60, 46, 62, 61, 63, 45, 64, 22, 16, 4],
  "modification_strategy": "This path prioritizes exploration of the unexplored opportunity regions by making larger deviations from the original path early on, emphasizing nodes with higher potential as indicated by the search context. We increased diversity using the diversity weight (0.7) and mitigated risks by sticking near to original path in difficult regions. Nodes are then selected to ensure a good spread of locations while targeting the opportunity regions.  The risk tolerance of 0.6 has been used to guide the node selection to ensure the path is still of reasonable quality, as a high risk strategy could produce very poor quality results.",
  "targeted_regions": ["Unexplored region 1", "Unexplored region 2"]
}
```
2025-06-22 17:17:46,772 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:17:46,772 - ExplorationExpert - INFO - 探索路径生成完成，成本: 111627.0, 路径: [44, 3, 21, 54, 18, 33, 9, 29, 47, 12, 51, 2, 14, 39, 11, 6, 24, 57, 40, 50, 13, 36, 10, 30, 43, 35, 27, 58, 7, 17, 48, 34, 20, 49, 19, 41, 15, 31, 52, 25, 4, 42, 23, 37, 8, 28, 59, 1, 26, 32, 55, 5, 56, 38, 60, 46, 62, 61, 63, 45, 64, 22, 16, 4]
2025-06-22 17:17:46,772 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [44, 3, 21, 54, 18, 33, 9, 29, 47, 12, 51, 2, 14, 39, 11, 6, 24, 57, 40, 50, 13, 36, 10, 30, 43, 35, 27, 58, 7, 17, 48, 34, 20, 49, 19, 41, 15, 31, 52, 25, 4, 42, 23, 37, 8, 28, 59, 1, 26, 32, 55, 5, 56, 38, 60, 46, 62, 61, 63, 45, 64, 22, 16, 4], 'cur_cost': 111627.0}
2025-06-22 17:17:46,772 - __main__ - INFO - 为个体 4 生成探索路径
2025-06-22 17:17:46,772 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:17:46,772 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 21 nodes, starting with [65, 12, 48] and ending with [77, 50, 37]
- Cost: 4.0631083727893874e+228
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.9033018867924528
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:17:46,772 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:17:49,334 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    65, 18, 23, 9, 15, 32, 41, 47, 53, 58, 62, 11, 29, 35, 43, 50, 57, 60, 71, 74, 77
  ],
  "modification_strategy": "This path prioritizes exploring the two 'opportunity regions' by deviating early and often from the original path.  The higher diversity weight (0.7) encourages exploring less-traveled routes.  The path avoids the 'difficult regions' as best as possible while focusing on maximizing exploration within the constraints. The route structure is built to sample a mix of regions.",
  "targeted_regions": [
    "Opportunity Region 1",
    "Opportunity Region 2"
  ]
}
```
2025-06-22 17:17:49,334 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:17:49,335 - ExplorationExpert - INFO - 探索路径生成完成，成本: 19969.0, 路径: [65, 18, 23, 9, 15, 32, 41, 47, 53, 58, 62, 11, 29, 35, 43, 50, 57, 60, 71, 74, 77]
2025-06-22 17:17:49,335 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [65, 18, 23, 9, 15, 32, 41, 47, 53, 58, 62, 11, 29, 35, 43, 50, 57, 60, 71, 74, 77], 'cur_cost': 19969.0}
2025-06-22 17:17:49,335 - __main__ - INFO - 为个体 5 生成探索路径
2025-06-22 17:17:49,335 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:17:49,335 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 63 nodes, starting with [17, 35, 48] and ending with [41, 21, 44]
- Cost: 111606.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8938679245283019
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:17:49,336 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:17:53,242 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    17, 35, 48, 5, 23, 1, 12, 50, 59, 62, 3, 19, 38, 45, 4, 27, 36, 51, 13, 30, 6, 24, 2, 15, 32, 39, 55, 53, 8, 25, 42, 57, 10, 28, 46, 52, 14, 31, 7, 26, 43, 58, 11, 29, 47, 54, 9, 20, 37, 60, 63, 16, 33, 40, 56, 18, 34, 49, 22, 41, 21, 44
  ],
  "modification_strategy": "This path prioritizes diversity by aiming to traverse different clusters of nodes, especially focusing on exploring the unexplored and opportunity regions identified in the search context.  The selection of nodes favors those regions. I also introduced some random node transitions to create divergence from the original path. The risk tolerance is kept relatively high to encourage more exploratory moves, prioritizing the exploration of uncharted territory over pure cost minimization at this stage. The long path length allows ample opportunity for diversifying regions visited.",
  "targeted_regions": "Specifically targeted exploration of opportunity regions 1 and 2 and avoided difficult regions as much as possible given the need for reasonable path quality. The selection of nodes considers the search context that details 'unexplored regions' in an attempt to promote their discovery."
}
```
2025-06-22 17:17:53,242 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:17:53,244 - ExplorationExpert - INFO - 探索路径生成完成，成本: 103033.0, 路径: [17, 35, 48, 5, 23, 1, 12, 50, 59, 62, 3, 19, 38, 45, 4, 27, 36, 51, 13, 30, 6, 24, 2, 15, 32, 39, 55, 53, 8, 25, 42, 57, 10, 28, 46, 52, 14, 31, 7, 26, 43, 58, 11, 29, 47, 54, 9, 20, 37, 60, 63, 16, 33, 40, 56, 18, 34, 49, 22, 41, 21, 44]
2025-06-22 17:17:53,244 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [17, 35, 48, 5, 23, 1, 12, 50, 59, 62, 3, 19, 38, 45, 4, 27, 36, 51, 13, 30, 6, 24, 2, 15, 32, 39, 55, 53, 8, 25, 42, 57, 10, 28, 46, 52, 14, 31, 7, 26, 43, 58, 11, 29, 47, 54, 9, 20, 37, 60, 63, 16, 33, 40, 56, 18, 34, 49, 22, 41, 21, 44], 'cur_cost': 103033.0}
2025-06-22 17:17:53,244 - __main__ - INFO - 为个体 6 生成探索路径
2025-06-22 17:17:53,244 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:17:53,245 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 35 nodes, starting with [22, 1, 18] and ending with [32, 7, 47]
- Cost: 45031.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8699763593380615
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:17:53,245 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:17:56,030 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    22, 4, 2, 11, 15, 19, 25, 29, 33, 30, 3, 10, 17, 21, 27, 34, 31, 14, 8, 6, 12, 13, 20, 23, 26, 28, 5, 9, 16, 24, 35, 32, 7, 47
  ],
  "modification_strategy": "This strategy prioritizes diversification by significantly altering the node sequence. It attempts to detour through both opportunity regions identified in the search context early in the path and also explores nodes in areas that minimize overlap with the existing path, aiming for greater population diversity. We leverage the high diversity weight to encourage this deviation from the original path, targeting those unexplored/opportunity regions specifically.",
  "targeted_regions": ["Opportunity Region 1", "Opportunity Region 2"]
}
```
2025-06-22 17:17:56,031 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:17:56,031 - ExplorationExpert - INFO - 探索路径生成完成，成本: 24171.0, 路径: [22, 4, 2, 11, 15, 19, 25, 29, 33, 30, 3, 10, 17, 21, 27, 34, 31, 14, 8, 6, 12, 13, 20, 23, 26, 28, 5, 9, 16, 24, 35, 32, 7, 47]
2025-06-22 17:17:56,031 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [22, 4, 2, 11, 15, 19, 25, 29, 33, 30, 3, 10, 17, 21, 27, 34, 31, 14, 8, 6, 12, 13, 20, 23, 26, 28, 5, 9, 16, 24, 35, 32, 7, 47], 'cur_cost': 24171.0}
2025-06-22 17:17:56,032 - __main__ - INFO - 为个体 7 生成探索路径
2025-06-22 17:17:56,032 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:17:56,032 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 66 nodes, starting with [26, 12, 3] and ending with [25, 21, 61]
- Cost: 116495.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8625592417061612
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:17:56,033 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:18:01,138 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    26, 12, 4, 15, 20, 48, 31, 6, 10, 22, 53, 5, 16, 42, 28, 1, 40, 39, 47, 17, 2, 46, 62, 34, 9, 18, 36, 50, 13, 30, 54, 51, 24, 58, 41, 29, 32, 60, 7, 49, 11, 37, 19, 56, 33, 64, 8, 38, 55, 27, 43, 23, 52, 45, 63, 35, 14, 44, 57, 21, 61, 25, 59
  ],
  "modification_strategy": "The strategy focuses on significantly diverging from the original path by prioritizing exploration of the unexplored regions identified in the search context. The path is constructed to weave through the opportunity regions first before branching towards other regions, ensuring the path is quite distinct. We prioritize high diversity, using the diversity weight, to explore different combinations and minimize the risk while keeping a reasonable quality",
  "targeted_regions": "Opportunity regions and a broader distribution of nodes to increase exploration of unexplored areas. By including nodes across the graph, it avoids clustering in any single area."
}
```
2025-06-22 17:18:01,138 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:18:01,138 - ExplorationExpert - INFO - 探索路径生成完成，成本: 120259.0, 路径: [26, 12, 4, 15, 20, 48, 31, 6, 10, 22, 53, 5, 16, 42, 28, 1, 40, 39, 47, 17, 2, 46, 62, 34, 9, 18, 36, 50, 13, 30, 54, 51, 24, 58, 41, 29, 32, 60, 7, 49, 11, 37, 19, 56, 33, 64, 8, 38, 55, 27, 43, 23, 52, 45, 63, 35, 14, 44, 57, 21, 61, 25, 59]
2025-06-22 17:18:01,138 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [26, 12, 4, 15, 20, 48, 31, 6, 10, 22, 53, 5, 16, 42, 28, 1, 40, 39, 47, 17, 2, 46, 62, 34, 9, 18, 36, 50, 13, 30, 54, 51, 24, 58, 41, 29, 32, 60, 7, 49, 11, 37, 19, 56, 33, 64, 8, 38, 55, 27, 43, 23, 52, 45, 63, 35, 14, 44, 57, 21, 61, 25, 59], 'cur_cost': 120259.0}
2025-06-22 17:18:01,138 - __main__ - INFO - 为个体 8 生成探索路径
2025-06-22 17:18:01,138 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:18:01,138 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 17 nodes, starting with [28, 19, 2] and ending with [17, 8, 3]
- Cost: 14733.0
- Key features: Standard path

## Search Context:
- Population diversity: 0.8615751789976134
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:18:01,138 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:18:03,869 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    28, 10, 7, 13, 22, 29, 26, 24, 18, 11, 1, 5, 9, 15, 14, 16, 3
  ],
  "modification_strategy": "This path prioritizes diversity by deviating significantly from the existing path, especially in the middle sections. It aims to explore unexplored regions by incorporating nodes that are likely to lead into those areas, while still maintaining a reasonable path length and node count. The risk tolerance of 0.6 has been considered in this strategy, by including an intermediate amount of unexplored regions",
  "targeted_regions": [
    "Unexplored Regions",
    "Region surrounding nodes 7, 13",
    "Region surrounding nodes 29, 26, 24",
    "Region surrounding nodes 11, 5, 9"
  ]
}
```
2025-06-22 17:18:03,871 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:18:03,872 - ExplorationExpert - INFO - 探索路径生成完成，成本: 11832.0, 路径: [28, 10, 7, 13, 22, 29, 26, 24, 18, 11, 1, 5, 9, 15, 14, 16, 3]
2025-06-22 17:18:03,872 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [28, 10, 7, 13, 22, 29, 26, 24, 18, 11, 1, 5, 9, 15, 14, 16, 3], 'cur_cost': 11832.0}
2025-06-22 17:18:03,872 - __main__ - INFO - 为个体 9 生成探索路径
2025-06-22 17:18:03,872 - ExplorationExpert - INFO - 开始生成探索路径
2025-06-22 17:18:03,872 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert

## Current Path:
- Path summary: Path with 46 nodes, starting with [20, 21, 28] and ending with [63, 22, 25]
- Cost: 24402.0
- Key features: Long path with potential for optimization

## Search Context:
- Population diversity: 0.8568019093078759
- Unexplored regions: 2 opportunity regions
- Opportunity regions: 2 regions
- Difficult regions to avoid: 2 regions

## Strategy Parameters:
- Diversity weight: 0.7
- Risk tolerance: 0.6

## Task:
Generate a significantly different path that explores new promising regions.
Focus on diversity while maintaining reasonable quality.

## Output Format:
Provide a JSON with:
- new_path: Array of node indices representing the new path
- modification_strategy: Brief description of your approach
- targeted_regions: Areas you targeted for exploration

2025-06-22 17:18:03,874 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-06-22 17:18:06,500 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [
    20, 21, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31
  ],
  "modification_strategy": "This strategy focuses on maximizing path divergence from the original. It introduces a significant shift in node selection early on to explore different branches of the graph. The path navigates through opportunity regions by incorporating nodes within those areas. The later section is designed to reach the destination node, making it different from the original.",
  "targeted_regions": [
    "Unexplored regions",
    "Opportunity regions"
  ]
}
```
2025-06-22 17:18:06,500 - ExplorationExpert - INFO - 计算路径成本
2025-06-22 17:18:06,501 - ExplorationExpert - INFO - 探索路径生成完成，成本: 12380.0, 路径: [20, 21, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]
2025-06-22 17:18:06,501 - __main__ - INFO - 个体 9 探索路径生成报告: {'new_tour': [20, 21, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31], 'cur_cost': 12380.0}
2025-06-22 17:18:06,501 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [6, 2, 15, 18, 22, 28, 31, 35, 39, 42, 45, 48, 51, 54, 57, 60, 63, 66, 69, 72, 75, 78, 81, 84, 87, 90, 93, 96, 99, 102, 105, 108, 111, 114, 117, 120, 123, 126, 129, 98], 'cur_cost': -3.5381512267520314e+307}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [5, 16, 18, 20, 23, 25, 26, 28, 29, 31, 34, 36, 37, 39, 40, 42, 45, 46, 48, 50], 'cur_cost': 7782.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [19, 3, 8, 12, 25, 31, 40, 42, 45, 49, 51, 53, 56, 59, 61, 58, 55, 52, 50, 47, 44, 39, 36, 32, 28, 22, 17, 13, 9, 6, 4, 1, 0, 2, 5, 7, 10, 14, 18, 21, 24, 29, 33, 37, 41, 43, 46, 48, 54, 57, 60, 26, 30, 34, 38, 27, 23, 20, 16, 15, 11, 58], 'cur_cost': 38219.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [44, 3, 21, 54, 18, 33, 9, 29, 47, 12, 51, 2, 14, 39, 11, 6, 24, 57, 40, 50, 13, 36, 10, 30, 43, 35, 27, 58, 7, 17, 48, 34, 20, 49, 19, 41, 15, 31, 52, 25, 4, 42, 23, 37, 8, 28, 59, 1, 26, 32, 55, 5, 56, 38, 60, 46, 62, 61, 63, 45, 64, 22, 16, 4], 'cur_cost': 111627.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [65, 18, 23, 9, 15, 32, 41, 47, 53, 58, 62, 11, 29, 35, 43, 50, 57, 60, 71, 74, 77], 'cur_cost': 19969.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [17, 35, 48, 5, 23, 1, 12, 50, 59, 62, 3, 19, 38, 45, 4, 27, 36, 51, 13, 30, 6, 24, 2, 15, 32, 39, 55, 53, 8, 25, 42, 57, 10, 28, 46, 52, 14, 31, 7, 26, 43, 58, 11, 29, 47, 54, 9, 20, 37, 60, 63, 16, 33, 40, 56, 18, 34, 49, 22, 41, 21, 44], 'cur_cost': 103033.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [22, 4, 2, 11, 15, 19, 25, 29, 33, 30, 3, 10, 17, 21, 27, 34, 31, 14, 8, 6, 12, 13, 20, 23, 26, 28, 5, 9, 16, 24, 35, 32, 7, 47], 'cur_cost': 24171.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [26, 12, 4, 15, 20, 48, 31, 6, 10, 22, 53, 5, 16, 42, 28, 1, 40, 39, 47, 17, 2, 46, 62, 34, 9, 18, 36, 50, 13, 30, 54, 51, 24, 58, 41, 29, 32, 60, 7, 49, 11, 37, 19, 56, 33, 64, 8, 38, 55, 27, 43, 23, 52, 45, 63, 35, 14, 44, 57, 21, 61, 25, 59], 'cur_cost': 120259.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [28, 10, 7, 13, 22, 29, 26, 24, 18, 11, 1, 5, 9, 15, 14, 16, 3], 'cur_cost': 11832.0}}, {'individual': 9, 'strategy': 'explore', 'path_data': {'new_tour': [20, 21, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31], 'cur_cost': 12380.0}}]
2025-06-22 17:18:06,501 - __main__ - INFO - 进化阶段完成
2025-06-22 17:18:06,501 - __main__ - INFO - 开始评估阶段
2025-06-22 17:18:06,503 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-06-22 17:18:06,503 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-06-22 17:18:06,503 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

## Evolution Data:
- Iteration: 4/5
- Population changes:
  * Cost: min 14733.0->-3.5381512267520314e+307, mean 3.9934505578880933e+251->-3.538151226752031e+306
  * Diversity: 0.8352941176470589->0.9016786570743405
  * Elite solutions: 1->0

## Strategy Performance:
- Explore: 10 individuals, success rate 70.0%, avg improvement 3.538151226752031e+306
- Exploit: 0 individuals, success rate 0%, avg improvement 0
- Overall improvement: 3.5381512267520314e+307

## Historical Trends:
Last 3 iterations: costs [-1.4701865236778527e+82, nan, 14733.0], diversity [1.0, 0.9922558922558922, 1.0]

## Task:
1. Identify current evolution stage
2. Evaluate strategy effectiveness
3. Assess exploration/exploitation balance
4. Provide recommendations for next iteration

## Output Format:
Provide a JSON with:
- evolution_stage: Current stage and features
- strategy_effectiveness: Performance evaluation of each strategy
- balance_state: Current balance assessment and adjustment needs
- recommendations: Specific suggestions for next iteration

2025-06-22 17:18:13,815 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": {
    "stage": "Rapid Decline/Potential Collapse",
    "features": "The cost has plunged dramatically, moving from positive to an extremely negative value in a single iteration. Diversity is high and increasing, but the elite solutions have disappeared. This indicates a potential instability and a risk of the population converging to a non-viable solution. The extremely large negative cost values are likely a result of numerical instability or an incorrect interpretation of the fitness function."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Highly effective, with a 70% success rate and a very high average improvement. However, this improvement is likely based on the extremely large negative cost which makes the true improvement dubious. The success metric appears to be linked to a very volatile and potentially broken cost function.",
      "analysis": "Exploration seems to be driving the dramatic cost change.  The magnitude of the cost change raises concerns. This suggests the search space might be poorly defined, or there could be an error in the cost calculation/implementation. The high diversity is also suggesting that all the search is being wasted on areas that are outside the usable zone."
    },
    "exploit": {
      "performance": "Ineffective, with a 0% success rate. Exploitation is not being utilized. ",
      "analysis": "The absence of any exploitation suggests that either promising solutions are not being found, or that the algorithm is not correctly identifying them. The extreme values of costs suggest that the exploit may be struggling with the very large negative values, meaning that the cost function is causing instability."
    },
    "overall": {
        "performance":"Dramatic, but potentially erroneous. The overall improvement is very large, but it is likely due to the problematic cost function and the very large negative values. The system may be rapidly collapsing towards a non-viable solution.",
        "analysis": "The overall strategy is showing a massive improvement in a single step. The result is likely misleading since all values moved to extreme negative values. There may be numerical issues, overflow issues, or error in calculation of cost. The rapid cost drop is not accompanied by a decrease in diversity; this suggests that exploration is finding new solutions (possibly incorrect), but exploitation is not exploiting these solutions. The performance is misleading."
    }
  },
  "balance_state": {
    "assessment": "Over-exploration. Exploitation is not active, and the massive improvement is based on problematic cost. There's a high risk of converging towards a completely non-viable solution.",
    "adjustment_needs": "Critical. The priority is to address the cost function issues, then re-evaluate the balance and the evolution metrics."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Investigate and fix the cost function. Carefully examine its implementation and ensure it's calculating the fitness of solutions correctly. Check for potential overflow/underflow issues that might be causing the extremely large negative cost values. Ensure the cost function is properly scaled."
    },
    {
      "priority": "High",
      "action": "If the cost function is verified correct, consider clamping or limiting the cost function output. This could prevent extreme values, and provide better stability for the evolution search."
    },
    {
      "priority": "Medium",
      "action": "Evaluate the search space. With a corrected cost function, review the structure and the type of solutions being sought. Check the validity of the solutions found. Confirm the solutions are valid within the constraints."
    },
     {
      "priority": "Medium",
      "action": "After fixing the cost function, re-evaluate the current strategy. Consider utilizing an exploit strategy with the now-stable cost function."
    },
    {
      "priority": "Low",
      "action": "Increase the number of exploit individuals. With a proper cost function, the system should be able to exploit solutions to achieve better performance."
    }
  ]
}
```
2025-06-22 17:18:13,815 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-06-22 17:18:13,815 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": {
    "stage": "Rapid Decline/Potential Collapse",
    "features": "The cost has plunged dramatically, moving from positive to an extremely negative value in a single iteration. Diversity is high and increasing, but the elite solutions have disappeared. This indicates a potential instability and a risk of the population converging to a non-viable solution. The extremely large negative cost values are likely a result of numerical instability or an incorrect interpretation of the fitness function."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Highly effective, with a 70% success rate and a very high average improvement. However, this improvement is likely based on the extremely large negative cost which makes the true improvement dubious. The success metric appears to be linked to a very volatile and potentially broken cost function.",
      "analysis": "Exploration seems to be driving the dramatic cost change.  The magnitude of the cost change raises concerns. This suggests the search space might be poorly defined, or there could be an error in the cost calculation/implementation. The high diversity is also suggesting that all the search is being wasted on areas that are outside the usable zone."
    },
    "exploit": {
      "performance": "Ineffective, with a 0% success rate. Exploitation is not being utilized. ",
      "analysis": "The absence of any exploitation suggests that either promising solutions are not being found, or that the algorithm is not correctly identifying them. The extreme values of costs suggest that the exploit may be struggling with the very large negative values, meaning that the cost function is causing instability."
    },
    "overall": {
        "performance":"Dramatic, but potentially erroneous. The overall improvement is very large, but it is likely due to the problematic cost function and the very large negative values. The system may be rapidly collapsing towards a non-viable solution.",
        "analysis": "The overall strategy is showing a massive improvement in a single step. The result is likely misleading since all values moved to extreme negative values. There may be numerical issues, overflow issues, or error in calculation of cost. The rapid cost drop is not accompanied by a decrease in diversity; this suggests that exploration is finding new solutions (possibly incorrect), but exploitation is not exploiting these solutions. The performance is misleading."
    }
  },
  "balance_state": {
    "assessment": "Over-exploration. Exploitation is not active, and the massive improvement is based on problematic cost. There's a high risk of converging towards a completely non-viable solution.",
    "adjustment_needs": "Critical. The priority is to address the cost function issues, then re-evaluate the balance and the evolution metrics."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Investigate and fix the cost function. Carefully examine its implementation and ensure it's calculating the fitness of solutions correctly. Check for potential overflow/underflow issues that might be causing the extremely large negative cost values. Ensure the cost function is properly scaled."
    },
    {
      "priority": "High",
      "action": "If the cost function is verified correct, consider clamping or limiting the cost function output. This could prevent extreme values, and provide better stability for the evolution search."
    },
    {
      "priority": "Medium",
      "action": "Evaluate the search space. With a corrected cost function, review the structure and the type of solutions being sought. Check the validity of the solutions found. Confirm the solutions are valid within the constraints."
    },
     {
      "priority": "Medium",
      "action": "After fixing the cost function, re-evaluate the current strategy. Consider utilizing an exploit strategy with the now-stable cost function."
    },
    {
      "priority": "Low",
      "action": "Increase the number of exploit individuals. With a proper cost function, the system should be able to exploit solutions to achieve better performance."
    }
  ]
}
```
2025-06-22 17:18:13,815 - __main__ - INFO - 评估阶段完成
2025-06-22 17:18:13,815 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": {
    "stage": "Rapid Decline/Potential Collapse",
    "features": "The cost has plunged dramatically, moving from positive to an extremely negative value in a single iteration. Diversity is high and increasing, but the elite solutions have disappeared. This indicates a potential instability and a risk of the population converging to a non-viable solution. The extremely large negative cost values are likely a result of numerical instability or an incorrect interpretation of the fitness function."
  },
  "strategy_effectiveness": {
    "explore": {
      "performance": "Highly effective, with a 70% success rate and a very high average improvement. However, this improvement is likely based on the extremely large negative cost which makes the true improvement dubious. The success metric appears to be linked to a very volatile and potentially broken cost function.",
      "analysis": "Exploration seems to be driving the dramatic cost change.  The magnitude of the cost change raises concerns. This suggests the search space might be poorly defined, or there could be an error in the cost calculation/implementation. The high diversity is also suggesting that all the search is being wasted on areas that are outside the usable zone."
    },
    "exploit": {
      "performance": "Ineffective, with a 0% success rate. Exploitation is not being utilized. ",
      "analysis": "The absence of any exploitation suggests that either promising solutions are not being found, or that the algorithm is not correctly identifying them. The extreme values of costs suggest that the exploit may be struggling with the very large negative values, meaning that the cost function is causing instability."
    },
    "overall": {
        "performance":"Dramatic, but potentially erroneous. The overall improvement is very large, but it is likely due to the problematic cost function and the very large negative values. The system may be rapidly collapsing towards a non-viable solution.",
        "analysis": "The overall strategy is showing a massive improvement in a single step. The result is likely misleading since all values moved to extreme negative values. There may be numerical issues, overflow issues, or error in calculation of cost. The rapid cost drop is not accompanied by a decrease in diversity; this suggests that exploration is finding new solutions (possibly incorrect), but exploitation is not exploiting these solutions. The performance is misleading."
    }
  },
  "balance_state": {
    "assessment": "Over-exploration. Exploitation is not active, and the massive improvement is based on problematic cost. There's a high risk of converging towards a completely non-viable solution.",
    "adjustment_needs": "Critical. The priority is to address the cost function issues, then re-evaluate the balance and the evolution metrics."
  },
  "recommendations": [
    {
      "priority": "High",
      "action": "Investigate and fix the cost function. Carefully examine its implementation and ensure it's calculating the fitness of solutions correctly. Check for potential overflow/underflow issues that might be causing the extremely large negative cost values. Ensure the cost function is properly scaled."
    },
    {
      "priority": "High",
      "action": "If the cost function is verified correct, consider clamping or limiting the cost function output. This could prevent extreme values, and provide better stability for the evolution search."
    },
    {
      "priority": "Medium",
      "action": "Evaluate the search space. With a corrected cost function, review the structure and the type of solutions being sought. Check the validity of the solutions found. Confirm the solutions are valid within the constraints."
    },
     {
      "priority": "Medium",
      "action": "After fixing the cost function, re-evaluate the current strategy. Consider utilizing an exploit strategy with the now-stable cost function."
    },
    {
      "priority": "Low",
      "action": "Increase the number of exploit individuals. With a proper cost function, the system should be able to exploit solutions to achieve better performance."
    }
  ]
}
```
2025-06-22 17:18:13,820 - __main__ - INFO - 当前最佳适应度: -3.5381512267520314e+307
2025-06-22 17:18:13,821 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_route_4.pkl
2025-06-22 17:18:13,827 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0616_-eoh_reevo_compare\EoH-main - idea - 0430-fa-feedback\EoH-main - idea - 0422-feedback\EoH-main - idea - 0422-feedback\MoE-main\solution\composite13_66_solution.json
2025-06-22 17:18:13,827 - __main__ - INFO - 实例 composite13_66 处理完成
