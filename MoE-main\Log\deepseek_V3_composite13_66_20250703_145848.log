2025-07-03 14:58:48,317 - __main__ - INFO - composite13_66 开始进化第 1 代
2025-07-03 14:58:48,318 - __main__ - INFO - 开始分析阶段
2025-07-03 14:58:48,318 - StatsExpert - INFO - 开始统计分析
2025-07-03 14:58:48,348 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 9874.0, 'max': 114304.0, 'mean': 76067.3, 'std': 43543.10929194193}, 'diversity': 0.9228956228956229, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 14:58:48,349 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 9874.0, 'max': 114304.0, 'mean': 76067.3, 'std': 43543.10929194193}, 'diversity_level': 0.9228956228956229, 'convergence_level': 0.0, 'clusters': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [2, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 9, 'cluster_sizes': [2, 1, 1, 1, 1, 1, 1, 1, 1], 'summary': '9 clusters, sizes [2, 1, 1, 1, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 14:58:48,350 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 14:58:48,351 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 14:58:48,351 - PathExpert - INFO - 开始路径结构分析
2025-07-03 14:58:48,362 - PathExpert - INFO - 路径结构分析完成
2025-07-03 14:58:48,363 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [{'subpath': (8, 2, 6), 'frequency': 0.3}, {'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}, {'subpath': (53, 62, 59), 'frequency': 0.3}, {'subpath': (62, 59, 56), 'frequency': 0.3}, {'subpath': (59, 56, 58), 'frequency': 0.3}, {'subpath': (56, 58, 60), 'frequency': 0.3}, {'subpath': (58, 60, 64), 'frequency': 0.3}, {'subpath': (60, 64, 57), 'frequency': 0.3}, {'subpath': (64, 57, 54), 'frequency': 0.3}], 'edge_frequency': {'high_frequency_edges': [], 'medium_frequency_edges': [{'edge': '(25, 26)', 'frequency': 0.4}, {'edge': '(9, 11)', 'frequency': 0.4}, {'edge': '(4, 5)', 'frequency': 0.4}, {'edge': '(55, 61)', 'frequency': 0.4}, {'edge': '(56, 59)', 'frequency': 0.4}, {'edge': '(56, 58)', 'frequency': 0.4}, {'edge': '(52, 63)', 'frequency': 0.4}, {'edge': '(38, 51)', 'frequency': 0.4}, {'edge': '(50, 51)', 'frequency': 0.4}, {'edge': '(40, 49)', 'frequency': 0.4}, {'edge': '(4, 8)', 'frequency': 0.4}, {'edge': '(6, 42)', 'frequency': 0.4}], 'low_frequency_edges': [{'edge': '(28, 30)', 'frequency': 0.3}, {'edge': '(34, 35)', 'frequency': 0.2}, {'edge': '(31, 33)', 'frequency': 0.3}, {'edge': '(29, 32)', 'frequency': 0.3}, {'edge': '(24, 29)', 'frequency': 0.3}, {'edge': '(16, 18)', 'frequency': 0.3}, {'edge': '(16, 23)', 'frequency': 0.3}, {'edge': '(22, 23)', 'frequency': 0.2}, {'edge': '(12, 22)', 'frequency': 0.3}, {'edge': '(12, 17)', 'frequency': 0.2}, {'edge': '(15, 17)', 'frequency': 0.2}, {'edge': '(14, 15)', 'frequency': 0.3}, {'edge': '(14, 20)', 'frequency': 0.2}, {'edge': '(20, 21)', 'frequency': 0.3}, {'edge': '(13, 21)', 'frequency': 0.2}, {'edge': '(13, 19)', 'frequency': 0.2}, {'edge': '(9, 19)', 'frequency': 0.2}, {'edge': '(7, 11)', 'frequency': 0.2}, {'edge': '(3, 7)', 'frequency': 0.3}, {'edge': '(1, 3)', 'frequency': 0.2}, {'edge': '(0, 10)', 'frequency': 0.3}, {'edge': '(2, 8)', 'frequency': 0.3}, {'edge': '(2, 6)', 'frequency': 0.3}, {'edge': '(53, 61)', 'frequency': 0.3}, {'edge': '(53, 62)', 'frequency': 0.3}, {'edge': '(59, 62)', 'frequency': 0.3}, {'edge': '(58, 60)', 'frequency': 0.3}, {'edge': '(60, 64)', 'frequency': 0.3}, {'edge': '(57, 64)', 'frequency': 0.3}, {'edge': '(54, 57)', 'frequency': 0.3}, {'edge': '(54, 65)', 'frequency': 0.3}, {'edge': '(52, 65)', 'frequency': 0.3}, {'edge': '(39, 63)', 'frequency': 0.2}, {'edge': '(39, 44)', 'frequency': 0.2}, {'edge': '(44, 45)', 'frequency': 0.2}, {'edge': '(38, 45)', 'frequency': 0.3}, {'edge': '(41, 50)', 'frequency': 0.2}, {'edge': '(41, 46)', 'frequency': 0.3}, {'edge': '(46, 47)', 'frequency': 0.2}, {'edge': '(47, 49)', 'frequency': 0.3}, {'edge': '(40, 43)', 'frequency': 0.3}, {'edge': '(43, 48)', 'frequency': 0.3}, {'edge': '(42, 48)', 'frequency': 0.2}, {'edge': '(39, 47)', 'frequency': 0.2}, {'edge': '(17, 19)', 'frequency': 0.2}, {'edge': '(19, 27)', 'frequency': 0.2}, {'edge': '(27, 37)', 'frequency': 0.2}, {'edge': '(25, 37)', 'frequency': 0.2}, {'edge': '(26, 36)', 'frequency': 0.2}, {'edge': '(35, 36)', 'frequency': 0.3}, {'edge': '(28, 35)', 'frequency': 0.2}, {'edge': '(30, 34)', 'frequency': 0.2}, {'edge': '(33, 34)', 'frequency': 0.2}, {'edge': '(24, 31)', 'frequency': 0.2}, {'edge': '(3, 32)', 'frequency': 0.2}, {'edge': '(1, 7)', 'frequency': 0.3}, {'edge': '(1, 11)', 'frequency': 0.2}, {'edge': '(5, 9)', 'frequency': 0.2}, {'edge': '(6, 10)', 'frequency': 0.2}, {'edge': '(0, 55)', 'frequency': 0.2}, {'edge': '(20, 58)', 'frequency': 0.2}, {'edge': '(16, 46)', 'frequency': 0.2}, {'edge': '(30, 64)', 'frequency': 0.2}, {'edge': '(0, 59)', 'frequency': 0.2}, {'edge': '(8, 48)', 'frequency': 0.2}, {'edge': '(38, 40)', 'frequency': 0.2}, {'edge': '(9, 32)', 'frequency': 0.2}, {'edge': '(10, 40)', 'frequency': 0.2}, {'edge': '(13, 15)', 'frequency': 0.2}, {'edge': '(28, 39)', 'frequency': 0.2}, {'edge': '(23, 63)', 'frequency': 0.2}, {'edge': '(20, 27)', 'frequency': 0.2}, {'edge': '(14, 34)', 'frequency': 0.2}, {'edge': '(22, 25)', 'frequency': 0.2}, {'edge': '(5, 38)', 'frequency': 0.2}, {'edge': '(9, 62)', 'frequency': 0.2}, {'edge': '(1, 56)', 'frequency': 0.2}, {'edge': '(0, 65)', 'frequency': 0.2}, {'edge': '(15, 32)', 'frequency': 0.2}, {'edge': '(60, 63)', 'frequency': 0.2}, {'edge': '(7, 45)', 'frequency': 0.2}, {'edge': '(16, 33)', 'frequency': 0.2}, {'edge': '(20, 47)', 'frequency': 0.2}, {'edge': '(10, 45)', 'frequency': 0.2}, {'edge': '(8, 46)', 'frequency': 0.2}, {'edge': '(21, 35)', 'frequency': 0.2}, {'edge': '(41, 42)', 'frequency': 0.2}, {'edge': '(23, 65)', 'frequency': 0.2}, {'edge': '(32, 36)', 'frequency': 0.2}, {'edge': '(15, 43)', 'frequency': 0.2}, {'edge': '(50, 60)', 'frequency': 0.2}, {'edge': '(27, 40)', 'frequency': 0.2}, {'edge': '(1, 48)', 'frequency': 0.2}, {'edge': '(16, 62)', 'frequency': 0.2}, {'edge': '(3, 43)', 'frequency': 0.2}, {'edge': '(13, 65)', 'frequency': 0.3}, {'edge': '(10, 54)', 'frequency': 0.2}]}, 'low_quality_regions': [{'region': [59, 49, 5, 43, 55, 29, 54, 46, 64], 'cost': 22100.0, 'size': 9}, {'region': [61, 30, 44, 58, 40], 'cost': 10625.0, 'size': 5}, {'region': [26, 57, 24, 44, 2], 'cost': 10603.0, 'size': 5}, {'region': [26, 39, 61, 41, 3], 'cost': 10242.0, 'size': 5}, {'region': [11, 48, 59, 44, 33], 'cost': 10234.0, 'size': 5}]}
2025-07-03 14:58:48,364 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 14:58:48,364 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 14:58:48,364 - EliteExpert - INFO - 开始精英解分析
2025-07-03 14:58:48,365 - EliteExpert - WARNING - 没有精英解可供分析
2025-07-03 14:58:48,365 - __main__ - INFO - 精英专家分析报告: {'elite_count': 0, 'elite_common_features': {}, 'fixed_nodes': [], 'population_gap': {}, 'elite_diversity': {}}
2025-07-03 14:58:48,365 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 14:58:48,365 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 14:58:49,079 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 14:58:49,081 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 0/5)
- population_size: 10
- cost_stats: min 9874.0, mean 76067.3, max 114304.0, std 43543.10929194193
- diversity: 0.9228956228956229
- convergence: 0.0
- clustering: 9 clusters, sizes [2, 1, 1, 1, 1, 1, 1, 1, 1]
- recent_best_costs: Not available (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: [{'subpath': (8, 2, 6), 'frequency': 0.3}, {'subpath': (55, 61, 53), 'frequency': 0.3}, {'subpath': (61, 53, 62), 'frequency': 0.3}]
- difficult_regions_sample: [{'region': [59, 49, 5, 43, 55, 29, 54, 46, 64], 'cost': 22100.0, 'size': 9}, {'region': [61, 30, 44, 58, 40], 'cost': 10625.0, 'size': 5}, {'region': [26, 57, 24, 44, 2], 'cost': 10603.0, 'size': 5}]
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: []
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 14:58:49,082 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 14:59:15,283 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "moderate"
  },
  "population_state": {
    "diversity": 0.923,
    "convergence": 0.0,
    "clustering": "weak"
  },
  "difficult_regions": [
    {"region": [59, 49, 5, 43, 55, 29, 54, 46, 64], "cost": 22100.0, "size": 9},
    {"region": [61, 30, 44, 58, 40], "cost": 10625.0, "size": 5},
    {"region": [26, 57, 24, 44, 2], "cost": 10603.0, "size": 5}
  ],
  "opportunity_regions": [
    {"region": [8, 2, 6], "frequency": 0.3},
    {"region": [55, 61, 53], "frequency": 0.3},
    {"region": [61, 53, 62], "frequency": 0.3}
  ],
  "evolution_phase": "initial_exploration",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["crossover", "mutation", "local_search"]
  }
}
```
2025-07-03 14:59:15,284 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 14:59:15,284 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.923, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'region': [59, 49, 5, 43, 55, 29, 54, 46, 64], 'cost': 22100.0, 'size': 9}, {'region': [61, 30, 44, 58, 40], 'cost': 10625.0, 'size': 5}, {'region': [26, 57, 24, 44, 2], 'cost': 10603.0, 'size': 5}], 'opportunity_regions': [{'region': [8, 2, 6], 'frequency': 0.3}, {'region': [55, 61, 53], 'frequency': 0.3}, {'region': [61, 53, 62], 'frequency': 0.3}], 'evolution_phase': 'initial_exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation', 'local_search']}}
2025-07-03 14:59:15,285 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 14:59:15,285 - __main__ - INFO - 分析阶段完成
2025-07-03 14:59:15,285 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.923, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'region': [59, 49, 5, 43, 55, 29, 54, 46, 64], 'cost': 22100.0, 'size': 9}, {'region': [61, 30, 44, 58, 40], 'cost': 10625.0, 'size': 5}, {'region': [26, 57, 24, 44, 2], 'cost': 10603.0, 'size': 5}], 'opportunity_regions': [{'region': [8, 2, 6], 'frequency': 0.3}, {'region': [55, 61, 53], 'frequency': 0.3}, {'region': [61, 53, 62], 'frequency': 0.3}], 'evolution_phase': 'initial_exploration', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover', 'mutation', 'local_search']}}
2025-07-03 14:59:15,286 - __main__ - INFO - 开始策略分配阶段
2025-07-03 14:59:15,286 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 14:59:15,286 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 0
- Population: 10 individuals
  • diversity: 0.923
  • best_cost: 9874.0
  • mean_cost: 76067.3
- Search-space: ruggedness high, modality multi, deceptiveness moderate
- Evolution phase: initial_exploration
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 3): {'region': [59, 49, , {'region': [61, 30, ...
- Opportunity regions (# 3): {'region': [8, 2, 6], {'region': [55, 61, ...
- Last-iteration feedback: None

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 14:59:15,287 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 14:59:15,287 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 14:59:25,342 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and initial exploration phase favor exploration; 3 individuals exploit to capitalize on opportunity regions."
}
```
2025-07-03 14:59:25,343 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 14:59:25,343 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 14:59:25,343 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and initial exploration phase favor exploration; 3 individuals exploit to capitalize on opportunity regions."
}
```
2025-07-03 14:59:25,344 - __main__ - INFO - 策略分配阶段完成
2025-07-03 14:59:25,344 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'exploit', 'exploit']
2025-07-03 14:59:25,344 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "exploit",
    "9": "exploit"
  },
  "rationale": "High diversity and initial exploration phase favor exploration; 3 individuals exploit to capitalize on opportunity regions."
}
```
2025-07-03 14:59:25,344 - __main__ - INFO - 开始进化阶段
2025-07-03 14:59:25,344 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 14:59:25,345 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 14:59:25,345 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9874.0 (rank 0.0%)
- Population diversity: 0.7523076923076923
- Evolution phase: initial_exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [8, , {'region': [55,...
- Difficult regions to avoid (sample): {'region': [59,, {'region': [61,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 14:59:25,346 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 14:59:25,346 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 14:59:40,359 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 8, 55, 12, 34, 23, 45, 7, 19, 30, 42, 5, 17, 28, 50, 3, 15, 37, 21, 43, 9, 31, 52, 14, 36, 25, 47, 2, 24, 46, 10, 32, 54, 16, 38, 20, 41, 6, 18, 29, 51, 4, 26, 48, 13, 35, 22, 44, 11, 33, 53, 1, 27, 49, 40, 62, 64, 57, 59, 61, 63, 65, 58, 60, 39, 56],
  "targeted_regions": "region_8, region_55, sparse_adjacent",
  "strategy_comment": "Focus on opportunity regions and adjacent sparse cells, avoid difficult nodes."
}
```
2025-07-03 14:59:40,360 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 14:59:40,360 - ExplorationExpert - INFO - 探索路径生成完成，成本: 102538.0, 路径: [0, 8, 55, 12, 34, 23, 45, 7, 19, 30, 42, 5, 17, 28, 50, 3, 15, 37, 21, 43, 9, 31, 52, 14, 36, 25, 47, 2, 24, 46, 10, 32, 54, 16, 38, 20, 41, 6, 18, 29, 51, 4, 26, 48, 13, 35, 22, 44, 11, 33, 53, 1, 27, 49, 40, 62, 64, 57, 59, 61, 63, 65, 58, 60, 39, 56]
2025-07-03 14:59:40,360 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 8, 55, 12, 34, 23, 45, 7, 19, 30, 42, 5, 17, 28, 50, 3, 15, 37, 21, 43, 9, 31, 52, 14, 36, 25, 47, 2, 24, 46, 10, 32, 54, 16, 38, 20, 41, 6, 18, 29, 51, 4, 26, 48, 13, 35, 22, 44, 11, 33, 53, 1, 27, 49, 40, 62, 64, 57, 59, 61, 63, 65, 58, 60, 39, 56], 'cur_cost': 102538.0}
2025-07-03 14:59:40,361 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 14:59:40,361 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 14:59:40,362 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9962.0 (rank 0.0%)
- Population diversity: 0.8092307692307692
- Evolution phase: initial_exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [8, , {'region': [55,...
- Difficult regions to avoid (sample): {'region': [59,, {'region': [61,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 14:59:40,362 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 14:59:40,362 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 14:59:56,665 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 8, 55, 12, 34, 21, 45, 3, 17, 29, 52, 7, 40, 63, 14, 26, 48, 5, 19, 37, 58, 10, 31, 50, 23, 42, 65, 15, 28, 46, 2, 24, 41, 60, 9, 33, 54, 13, 35, 57, 20, 44, 62, 16, 38, 59, 1, 25, 43, 64, 11, 30, 53, 6, 22, 39, 61, 4, 18, 36, 56, 27, 49, 32, 51, 47],
  "targeted_regions": "region_8_55_sparse_adjacent",
  "strategy_comment": "Targets opportunity regions and adjacent sparse cells."
}
```
2025-07-03 14:59:56,665 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 14:59:56,665 - ExplorationExpert - INFO - 探索路径生成完成，成本: 122878.0, 路径: [0, 8, 55, 12, 34, 21, 45, 3, 17, 29, 52, 7, 40, 63, 14, 26, 48, 5, 19, 37, 58, 10, 31, 50, 23, 42, 65, 15, 28, 46, 2, 24, 41, 60, 9, 33, 54, 13, 35, 57, 20, 44, 62, 16, 38, 59, 1, 25, 43, 64, 11, 30, 53, 6, 22, 39, 61, 4, 18, 36, 56, 27, 49, 32, 51, 47]
2025-07-03 14:59:56,665 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 8, 55, 12, 34, 21, 45, 3, 17, 29, 52, 7, 40, 63, 14, 26, 48, 5, 19, 37, 58, 10, 31, 50, 23, 42, 65, 15, 28, 46, 2, 24, 41, 60, 9, 33, 54, 13, 35, 57, 20, 44, 62, 16, 38, 59, 1, 25, 43, 64, 11, 30, 53, 6, 22, 39, 61, 4, 18, 36, 56, 27, 49, 32, 51, 47], 'cur_cost': 122878.0}
2025-07-03 14:59:56,665 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 14:59:56,666 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 14:59:56,666 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 9964.0 (rank 0.0%)
- Population diversity: 0.8584615384615385
- Evolution phase: initial_exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [8, , {'region': [55,...
- Difficult regions to avoid (sample): {'region': [59,, {'region': [61,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 14:59:56,666 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 14:59:56,667 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:00:09,995 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61],
  "targeted_regions": "region_8, region_55",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes."
}
```
2025-07-03 15:00:09,996 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:00:09,996 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13348.0, 路径: [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61]
2025-07-03 15:00:09,996 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}
2025-07-03 15:00:09,996 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 15:00:09,996 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:00:09,998 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98985.0 (rank 20.0%)
- Population diversity: 0.86
- Evolution phase: initial_exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [8, , {'region': [55,...
- Difficult regions to avoid (sample): {'region': [59,, {'region': [61,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:00:09,998 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:00:09,999 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:00:24,564 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61],
  "targeted_regions": "region_8, region_55",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes."
}
```
2025-07-03 15:00:24,565 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:00:24,565 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13348.0, 路径: [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61]
2025-07-03 15:00:24,565 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}
2025-07-03 15:00:24,565 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 15:00:24,565 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:00:24,566 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 97114.0 (rank 20.0%)
- Population diversity: 0.7830769230769231
- Evolution phase: initial_exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [8, , {'region': [55,...
- Difficult regions to avoid (sample): {'region': [59,, {'region': [61,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:00:24,566 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:00:24,567 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:00:40,360 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61],
  "targeted_regions": "region_8, region_55",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes."
}
```
2025-07-03 15:00:40,361 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:00:40,361 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13348.0, 路径: [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61]
2025-07-03 15:00:40,361 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}
2025-07-03 15:00:40,362 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 15:00:40,362 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:00:40,363 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 107920.0 (rank 70.0%)
- Population diversity: 0.7
- Evolution phase: initial_exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [8, , {'region': [55,...
- Difficult regions to avoid (sample): {'region': [59,, {'region': [61,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:00:40,363 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:00:40,363 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:00:53,734 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61],
  "targeted_regions": "region_8, region_55",
  "strategy_comment": "Focuses on opportunity regions while avoiding difficult nodes."
}
```
2025-07-03 15:00:53,735 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:00:53,735 - ExplorationExpert - INFO - 探索路径生成完成，成本: 13348.0, 路径: [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61]
2025-07-03 15:00:53,735 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}
2025-07-03 15:00:53,736 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-03 15:00:53,736 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:00:53,737 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 107132.0 (rank 70.0%)
- Population diversity: 0.6138461538461538
- Evolution phase: initial_exploration
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': [8, , {'region': [55,...
- Difficult regions to avoid (sample): {'region': [59,, {'region': [61,...
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:00:53,737 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:00:53,737 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:01:07,825 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 8, 55, 4, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 62, 13, 24, 35, 46, 57, 6, 17, 28, 39, 50, 61, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 15, 26, 37, 48, 59, 10, 21, 32, 43, 54, 65, 16, 27, 38, 49, 60, 1, 11, 22, 33, 44, 63, 3, 5, 19, 30, 41, 52],
  "targeted_regions": "region_8, region_55, sparse_adjacent",
  "strategy_comment": "Focuses on opportunity regions and adjacent sparse cells."
}
```
2025-07-03 15:01:07,825 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:01:07,825 - ExplorationExpert - INFO - 探索路径生成完成，成本: 94344.0, 路径: [0, 8, 55, 4, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 62, 13, 24, 35, 46, 57, 6, 17, 28, 39, 50, 61, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 15, 26, 37, 48, 59, 10, 21, 32, 43, 54, 65, 16, 27, 38, 49, 60, 1, 11, 22, 33, 44, 63, 3, 5, 19, 30, 41, 52]
2025-07-03 15:01:07,825 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 8, 55, 4, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 62, 13, 24, 35, 46, 57, 6, 17, 28, 39, 50, 61, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 15, 26, 37, 48, 59, 10, 21, 32, 43, 54, 65, 16, 27, 38, 49, 60, 1, 11, 22, 33, 44, 63, 3, 5, 19, 30, 41, 52], 'cur_cost': 94344.0}
2025-07-03 15:01:07,827 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 15:01:07,827 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:01:07,829 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:01:07,830 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 109061.0
2025-07-03 15:01:10,506 - ExploitationExpert - INFO - res_population_num: 1
2025-07-03 15:01:10,507 - ExploitationExpert - INFO - res_population_costs: [89049]
2025-07-03 15:01:10,507 - ExploitationExpert - INFO - res_populations: [array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64)]
2025-07-03 15:01:10,508 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:01:10,508 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 55, 12, 34, 23, 45, 7, 19, 30, 42, 5, 17, 28, 50, 3, 15, 37, 21, 43, 9, 31, 52, 14, 36, 25, 47, 2, 24, 46, 10, 32, 54, 16, 38, 20, 41, 6, 18, 29, 51, 4, 26, 48, 13, 35, 22, 44, 11, 33, 53, 1, 27, 49, 40, 62, 64, 57, 59, 61, 63, 65, 58, 60, 39, 56], 'cur_cost': 102538.0}, {'tour': [0, 8, 55, 12, 34, 21, 45, 3, 17, 29, 52, 7, 40, 63, 14, 26, 48, 5, 19, 37, 58, 10, 31, 50, 23, 42, 65, 15, 28, 46, 2, 24, 41, 60, 9, 33, 54, 13, 35, 57, 20, 44, 62, 16, 38, 59, 1, 25, 43, 64, 11, 30, 53, 6, 22, 39, 61, 4, 18, 36, 56, 27, 49, 32, 51, 47], 'cur_cost': 122878.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 4, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 62, 13, 24, 35, 46, 57, 6, 17, 28, 39, 50, 61, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 15, 26, 37, 48, 59, 10, 21, 32, 43, 54, 65, 16, 27, 38, 49, 60, 1, 11, 22, 33, 44, 63, 3, 5, 19, 30, 41, 52], 'cur_cost': 94344.0}, {'tour': array([45, 27, 13, 15, 33, 34,  5, 64, 61, 19, 31,  2,  7, 17, 26, 51, 48,
       35,  6, 28, 46, 11, 57,  4, 30,  9, 60, 20, 53, 40, 22, 59, 41, 62,
       12, 37, 16, 38, 10, 47, 14, 56,  0, 43, 39, 52,  1, 24, 29, 49, 54,
       32, 36, 63, 65,  8, 42, 55, 18, 25, 50,  3, 44, 21, 58, 23]), 'cur_cost': 109061.0}, {'tour': [2, 31, 64, 17, 19, 0, 30, 52, 27, 34, 51, 47, 32, 46, 12, 13, 65, 4, 45, 42, 41, 8, 49, 35, 20, 58, 37, 5, 48, 1, 56, 28, 38, 55, 9, 14, 62, 29, 21, 11, 33, 16, 50, 60, 61, 7, 59, 36, 63, 54, 10, 40, 3, 43, 15, 18, 23, 53, 39, 6, 22, 25, 26, 57, 24, 44], 'cur_cost': 114304.0}, {'tour': [17, 6, 42, 23, 52, 64, 14, 51, 36, 29, 39, 50, 47, 34, 13, 65, 25, 35, 21, 45, 30, 37, 1, 7, 11, 48, 59, 44, 33, 19, 63, 60, 46, 24, 8, 40, 27, 20, 26, 2, 54, 10, 41, 28, 49, 4, 61, 18, 53, 12, 43, 58, 3, 57, 62, 16, 22, 55, 15, 0, 32, 9, 31, 56, 5, 38], 'cur_cost': 104890.0}]
2025-07-03 15:01:10,509 - ExploitationExpert - INFO - 局部搜索耗时: 2.68秒
2025-07-03 15:01:10,509 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 1, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 1, 'cache_hits': 0, 'similarity_calculations': 0, 'cache_hit_rate': 0.0, 'cache_size': 0}}
2025-07-03 15:01:10,509 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-03 15:01:10,509 - __main__ - INFO - 为个体 8 生成利用路径
2025-07-03 15:01:10,509 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:01:10,510 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:01:10,510 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 8 处的路径，新成本: 94942.0
2025-07-03 15:01:11,488 - ExploitationExpert - INFO - res_population_num: 2
2025-07-03 15:01:11,488 - ExploitationExpert - INFO - res_population_costs: [89049, 9609]
2025-07-03 15:01:11,488 - ExploitationExpert - INFO - res_populations: [array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64)]
2025-07-03 15:01:11,490 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:01:11,490 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 55, 12, 34, 23, 45, 7, 19, 30, 42, 5, 17, 28, 50, 3, 15, 37, 21, 43, 9, 31, 52, 14, 36, 25, 47, 2, 24, 46, 10, 32, 54, 16, 38, 20, 41, 6, 18, 29, 51, 4, 26, 48, 13, 35, 22, 44, 11, 33, 53, 1, 27, 49, 40, 62, 64, 57, 59, 61, 63, 65, 58, 60, 39, 56], 'cur_cost': 102538.0}, {'tour': [0, 8, 55, 12, 34, 21, 45, 3, 17, 29, 52, 7, 40, 63, 14, 26, 48, 5, 19, 37, 58, 10, 31, 50, 23, 42, 65, 15, 28, 46, 2, 24, 41, 60, 9, 33, 54, 13, 35, 57, 20, 44, 62, 16, 38, 59, 1, 25, 43, 64, 11, 30, 53, 6, 22, 39, 61, 4, 18, 36, 56, 27, 49, 32, 51, 47], 'cur_cost': 122878.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 4, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 62, 13, 24, 35, 46, 57, 6, 17, 28, 39, 50, 61, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 15, 26, 37, 48, 59, 10, 21, 32, 43, 54, 65, 16, 27, 38, 49, 60, 1, 11, 22, 33, 44, 63, 3, 5, 19, 30, 41, 52], 'cur_cost': 94344.0}, {'tour': array([45, 27, 13, 15, 33, 34,  5, 64, 61, 19, 31,  2,  7, 17, 26, 51, 48,
       35,  6, 28, 46, 11, 57,  4, 30,  9, 60, 20, 53, 40, 22, 59, 41, 62,
       12, 37, 16, 38, 10, 47, 14, 56,  0, 43, 39, 52,  1, 24, 29, 49, 54,
       32, 36, 63, 65,  8, 42, 55, 18, 25, 50,  3, 44, 21, 58, 23]), 'cur_cost': 109061.0}, {'tour': array([48, 44, 37, 49, 65, 53, 17,  6,  1, 16,  5, 56, 14, 10,  4, 13, 15,
       39,  7,  0, 29, 31, 43, 25, 42, 24, 27, 19, 20, 50, 62,  9, 38, 23,
       46, 51, 11, 36, 40, 41, 28, 34, 47, 55, 60, 32, 12, 57, 45,  3, 30,
       54, 59, 61, 18,  2,  8, 22, 64, 52, 63, 26, 21, 35, 58, 33]), 'cur_cost': 94942.0}, {'tour': [17, 6, 42, 23, 52, 64, 14, 51, 36, 29, 39, 50, 47, 34, 13, 65, 25, 35, 21, 45, 30, 37, 1, 7, 11, 48, 59, 44, 33, 19, 63, 60, 46, 24, 8, 40, 27, 20, 26, 2, 54, 10, 41, 28, 49, 4, 61, 18, 53, 12, 43, 58, 3, 57, 62, 16, 22, 55, 15, 0, 32, 9, 31, 56, 5, 38], 'cur_cost': 104890.0}]
2025-07-03 15:01:11,492 - ExploitationExpert - INFO - 局部搜索耗时: 0.98秒
2025-07-03 15:01:11,492 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 2, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 2, 'cache_hits': 0, 'similarity_calculations': 1, 'cache_hit_rate': 0.0, 'cache_size': 1}}
2025-07-03 15:01:11,492 - __main__ - WARNING - 个体 8 的利用路径生成失败，保留原个体
2025-07-03 15:01:11,492 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 15:01:11,492 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:01:11,492 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:01:11,493 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 107262.0
2025-07-03 15:01:11,995 - ExploitationExpert - INFO - res_population_num: 8
2025-07-03 15:01:11,995 - ExploitationExpert - INFO - res_population_costs: [89049, 9609, 9558, 9534, 9530, 9527, 9521, 9521]
2025-07-03 15:01:11,995 - ExploitationExpert - INFO - res_populations: [array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 15:01:11,998 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:01:11,998 - ExploitationExpert - INFO - populations: [{'tour': [0, 8, 55, 12, 34, 23, 45, 7, 19, 30, 42, 5, 17, 28, 50, 3, 15, 37, 21, 43, 9, 31, 52, 14, 36, 25, 47, 2, 24, 46, 10, 32, 54, 16, 38, 20, 41, 6, 18, 29, 51, 4, 26, 48, 13, 35, 22, 44, 11, 33, 53, 1, 27, 49, 40, 62, 64, 57, 59, 61, 63, 65, 58, 60, 39, 56], 'cur_cost': 102538.0}, {'tour': [0, 8, 55, 12, 34, 21, 45, 3, 17, 29, 52, 7, 40, 63, 14, 26, 48, 5, 19, 37, 58, 10, 31, 50, 23, 42, 65, 15, 28, 46, 2, 24, 41, 60, 9, 33, 54, 13, 35, 57, 20, 44, 62, 16, 38, 59, 1, 25, 43, 64, 11, 30, 53, 6, 22, 39, 61, 4, 18, 36, 56, 27, 49, 32, 51, 47], 'cur_cost': 122878.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 4, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 62, 13, 24, 35, 46, 57, 6, 17, 28, 39, 50, 61, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 15, 26, 37, 48, 59, 10, 21, 32, 43, 54, 65, 16, 27, 38, 49, 60, 1, 11, 22, 33, 44, 63, 3, 5, 19, 30, 41, 52], 'cur_cost': 94344.0}, {'tour': array([45, 27, 13, 15, 33, 34,  5, 64, 61, 19, 31,  2,  7, 17, 26, 51, 48,
       35,  6, 28, 46, 11, 57,  4, 30,  9, 60, 20, 53, 40, 22, 59, 41, 62,
       12, 37, 16, 38, 10, 47, 14, 56,  0, 43, 39, 52,  1, 24, 29, 49, 54,
       32, 36, 63, 65,  8, 42, 55, 18, 25, 50,  3, 44, 21, 58, 23]), 'cur_cost': 109061.0}, {'tour': array([48, 44, 37, 49, 65, 53, 17,  6,  1, 16,  5, 56, 14, 10,  4, 13, 15,
       39,  7,  0, 29, 31, 43, 25, 42, 24, 27, 19, 20, 50, 62,  9, 38, 23,
       46, 51, 11, 36, 40, 41, 28, 34, 47, 55, 60, 32, 12, 57, 45,  3, 30,
       54, 59, 61, 18,  2,  8, 22, 64, 52, 63, 26, 21, 35, 58, 33]), 'cur_cost': 94942.0}, {'tour': array([24,  3, 54, 46, 58, 13, 63, 48,  7,  0, 37, 18, 25, 61, 59, 16, 65,
       52, 15, 62, 43, 21, 19, 39, 23, 51, 12, 41, 55, 42, 31, 10, 27, 20,
       14, 11, 30, 17, 22, 32, 28, 50, 57, 36,  6,  2,  9, 49, 40, 34, 44,
       47, 29, 53, 60, 56, 33, 26,  1, 38,  8, 35,  4,  5, 45, 64]), 'cur_cost': 107262.0}]
2025-07-03 15:01:12,000 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 15:01:12,000 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 3, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 3, 'cache_hits': 0, 'similarity_calculations': 3, 'cache_hit_rate': 0.0, 'cache_size': 3}}
2025-07-03 15:01:12,000 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 15:01:12,001 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 55, 12, 34, 23, 45, 7, 19, 30, 42, 5, 17, 28, 50, 3, 15, 37, 21, 43, 9, 31, 52, 14, 36, 25, 47, 2, 24, 46, 10, 32, 54, 16, 38, 20, 41, 6, 18, 29, 51, 4, 26, 48, 13, 35, 22, 44, 11, 33, 53, 1, 27, 49, 40, 62, 64, 57, 59, 61, 63, 65, 58, 60, 39, 56], 'cur_cost': 102538.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 55, 12, 34, 21, 45, 3, 17, 29, 52, 7, 40, 63, 14, 26, 48, 5, 19, 37, 58, 10, 31, 50, 23, 42, 65, 15, 28, 46, 2, 24, 41, 60, 9, 33, 54, 13, 35, 57, 20, 44, 62, 16, 38, 59, 1, 25, 43, 64, 11, 30, 53, 6, 22, 39, 61, 4, 18, 36, 56, 27, 49, 32, 51, 47], 'cur_cost': 122878.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 8, 55, 4, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 62, 13, 24, 35, 46, 57, 6, 17, 28, 39, 50, 61, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 15, 26, 37, 48, 59, 10, 21, 32, 43, 54, 65, 16, 27, 38, 49, 60, 1, 11, 22, 33, 44, 63, 3, 5, 19, 30, 41, 52], 'cur_cost': 94344.0}}]
2025-07-03 15:01:12,001 - __main__ - INFO - 进化阶段完成
2025-07-03 15:01:12,001 - __main__ - INFO - 开始评估阶段
2025-07-03 15:01:12,001 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 15:01:12,003 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 15:01:12,003 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 0/5

#### Population metrics
- Min cost: 9874.0 → 13348.0 (Δ -3474.0)
- Mean cost: 76067.3 → 68441.7
- Diversity: 0.752 → 0.626 (Δ -0.1262)

#### Elite metrics
- Elite count: 0 → 8
- Best elite cost: 9874.0 → 9521 (Δ 353.0)
- Elite diversity: 0.000 → 0.342 (Δ 0.3423)

#### Strategy performance
- Explore: 7 inds, success 57.1% , avg Δ 9685.57
- Exploit: 3 inds, success 33.3% , avg Δ 2819.0

#### Other indicators
- No-change individuals: 0
- Regression individuals: 5
- Historical trends: N/A

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 15:01:12,003 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:01:21,736 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.57, "exploit": 0.33},
  "balance_state": "explore_biased",
  "recommendations": ["set_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 15:01:21,767 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 15:01:21,767 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.57, "exploit": 0.33},
  "balance_state": "explore_biased",
  "recommendations": ["set_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 15:01:21,767 - __main__ - INFO - 评估阶段完成
2025-07-03 15:01:21,767 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.57, "exploit": 0.33},
  "balance_state": "explore_biased",
  "recommendations": ["set_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 15:01:21,767 - __main__ - INFO - 当前最佳适应度: 13348.0
2025-07-03 15:01:21,769 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_0.pkl
2025-07-03 15:01:21,769 - __main__ - INFO - composite13_66 开始进化第 2 代
2025-07-03 15:01:21,769 - __main__ - INFO - 开始分析阶段
2025-07-03 15:01:21,769 - StatsExpert - INFO - 开始统计分析
2025-07-03 15:01:21,787 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 13348.0, 'max': 122878.0, 'mean': 68441.7, 'std': 45604.0237480203}, 'diversity': 0.8323232323232322, 'clusters': {'clusters': 7, 'cluster_sizes': [1, 1, 4, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 15:01:21,789 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 13348.0, 'max': 122878.0, 'mean': 68441.7, 'std': 45604.0237480203}, 'diversity_level': 0.8323232323232322, 'convergence_level': 0.0, 'clusters': {'clusters': 7, 'cluster_sizes': [1, 1, 4, 1, 1, 1, 1], 'summary': '7 clusters, sizes [1, 1, 4, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 7, 'cluster_sizes': [1, 1, 4, 1, 1, 1, 1], 'summary': '7 clusters, sizes [1, 1, 4, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 15:01:21,789 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 15:01:21,789 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 15:01:21,790 - PathExpert - INFO - 开始路径结构分析
2025-07-03 15:01:21,793 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 15:01:21,793 - PathExpert - INFO - 路径结构分析完成
2025-07-03 15:01:21,793 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 15:01:21,793 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 15:01:21,793 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 15:01:21,794 - EliteExpert - INFO - 开始精英解分析
2025-07-03 15:01:21,795 - EliteExpert - INFO - 精英解分析完成
2025-07-03 15:01:21,796 - __main__ - INFO - 精英专家分析报告: {'elite_count': 8, 'elite_common_features': {'common_edges': {'(17, 12)': 0.75, '(12, 22)': 0.75, '(20, 21)': 0.75, '(34, 35)': 0.875, '(26, 36)': 0.75}, 'common_edge_ratio': 0.07575757575757576}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 3827.0, 'avg_gap': 48960.575}, 'structure_gap': {'unique_elite_edges': 213, 'unique_pop_edges': 390, 'common_edges': 36}}, 'elite_diversity': {'diversity_score': 0.4632034632034632}}
2025-07-03 15:01:21,796 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 15:01:21,796 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 15:01:21,799 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 15:01:21,800 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 1/5)
- population_size: 10
- cost_stats: min 13348.0, mean 68441.7, max 122878.0, std 45604.0237480203
- diversity: 0.8323232323232322
- convergence: 0.0
- clustering: 7 clusters, sizes [1, 1, 4, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [13348.0] (Δ Not available, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.4632034632034632}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 15:01:21,800 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:01:35,738 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.8323232323232322,
    "convergence": 0.0,
    "clustering": "weak"
  },
  "difficult_regions": [
    {"region": "low-density cells", "nodes": "nodes in cells with 0 density"},
    {"region": "long-edge corridors", "edges": "edges with length in q3-max range (2469.0-3032.0)"}
  ],
  "opportunity_regions": [
    {"region": "high-density cells", "nodes": "nodes in cells with density 14"},
    {"region": "short-edge clusters", "edges": "edges with length in min-q1 range (10.0-1075.0)"}
  ],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": ["mutation to exploit high-density regions", "crossover to combine short-edge clusters", "local search to navigate difficult regions"]
  }
}
```
2025-07-03 15:01:35,738 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 15:01:35,738 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.8323232323232322, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'region': 'low-density cells', 'nodes': 'nodes in cells with 0 density'}, {'region': 'long-edge corridors', 'edges': 'edges with length in q3-max range (2469.0-3032.0)'}], 'opportunity_regions': [{'region': 'high-density cells', 'nodes': 'nodes in cells with density 14'}, {'region': 'short-edge clusters', 'edges': 'edges with length in min-q1 range (10.0-1075.0)'}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation to exploit high-density regions', 'crossover to combine short-edge clusters', 'local search to navigate difficult regions']}}
2025-07-03 15:01:35,739 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 15:01:35,739 - __main__ - INFO - 分析阶段完成
2025-07-03 15:01:35,739 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.8323232323232322, 'convergence': 0.0, 'clustering': 'weak'}, 'difficult_regions': [{'region': 'low-density cells', 'nodes': 'nodes in cells with 0 density'}, {'region': 'long-edge corridors', 'edges': 'edges with length in q3-max range (2469.0-3032.0)'}], 'opportunity_regions': [{'region': 'high-density cells', 'nodes': 'nodes in cells with density 14'}, {'region': 'short-edge clusters', 'edges': 'edges with length in min-q1 range (10.0-1075.0)'}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['mutation to exploit high-density regions', 'crossover to combine short-edge clusters', 'local search to navigate difficult regions']}}
2025-07-03 15:01:35,739 - __main__ - INFO - 开始策略分配阶段
2025-07-03 15:01:35,739 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 15:01:35,741 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 1
- Population: 10 individuals
  • diversity: 0.832
  • best_cost: 13348.0
  • mean_cost: 68441.7
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): {'region': 'low-dens, {'region': 'long-edg
- Opportunity regions (# 2): {'region': 'high-den, {'region': 'short-ed
- Last-iteration feedback: ```json
{
  "evolution_stage": "early",
  "strategy_effectiveness": {"explore": 0.57, "exploit": 0.33},
  "balance_state...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 15:01:35,741 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 15:01:35,741 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:01:44,783 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Early phase, high diversity, and explore focus suggest 70% exploration. Top performers exploit, others explore."
}
```
2025-07-03 15:01:44,784 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 15:01:44,784 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 15:01:44,784 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Early phase, high diversity, and explore focus suggest 70% exploration. Top performers exploit, others explore."
}
```
2025-07-03 15:01:44,784 - __main__ - INFO - 策略分配阶段完成
2025-07-03 15:01:44,784 - __main__ - INFO - 策略分配: ['explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 15:01:44,785 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.7,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "exploit",
    "3": "explore",
    "4": "exploit",
    "5": "explore",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Early phase, high diversity, and explore focus suggest 70% exploration. Top performers exploit, others explore."
}
```
2025-07-03 15:01:44,785 - __main__ - INFO - 开始进化阶段
2025-07-03 15:01:44,785 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 15:01:44,785 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:01:44,786 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 102538.0 (rank 60.0%)
- Population diversity: 0.6261538461538462
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': 'hig, {'region': 'sho
- Difficult regions to avoid (sample): {'region': 'low, {'region': 'lon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:01:44,786 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:01:44,786 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:01:58,268 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high, sho",
  "strategy_comment": "Leverages opportunity regions with diagonal traversal."
}
```
2025-07-03 15:01:58,269 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:01:58,270 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-07-03 15:01:58,270 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-07-03 15:01:58,270 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 15:01:58,271 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:01:58,271 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 122878.0 (rank 90.0%)
- Population diversity: 0.6384615384615384
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': 'hig, {'region': 'sho
- Difficult regions to avoid (sample): {'region': 'low, {'region': 'lon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:01:58,272 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:01:58,272 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:02:12,487 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11],
  "targeted_regions": "high, sho",
  "strategy_comment": "Leverages high and sho regions while avoiding low and lon."
}
```
2025-07-03 15:02:12,488 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:02:12,488 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99370.0, 路径: [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11]
2025-07-03 15:02:12,488 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11], 'cur_cost': 99370.0}
2025-07-03 15:02:12,489 - __main__ - INFO - 为个体 2 生成利用路径
2025-07-03 15:02:12,489 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:02:12,489 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:02:12,490 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 2 处的路径，新成本: 95954.0
2025-07-03 15:02:12,992 - ExploitationExpert - INFO - res_population_num: 18
2025-07-03 15:02:12,993 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9527, 9530, 9534, 9558, 9609, 89049, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 15:02:12,993 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64)]
2025-07-03 15:02:13,000 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:02:13,000 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11], 'cur_cost': 99370.0}, {'tour': array([59, 63,  4, 64,  1,  2, 37, 54, 49, 52, 55, 46, 12, 21, 41, 27, 39,
       45, 15, 19, 30, 31, 29, 36,  8, 26, 32, 43, 10, 18, 62,  7,  5, 53,
        0, 48,  3,  9, 28, 24, 50, 47, 60, 25, 65, 20, 17, 14, 35, 51, 61,
       16, 11, 34, 44, 22, 56, 42, 38,  6, 23, 40, 33, 57, 58, 13]), 'cur_cost': 95954.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 7, 6, 5, 4, 3, 2, 1, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 59, 61], 'cur_cost': 13348.0}, {'tour': [0, 8, 55, 4, 12, 23, 34, 45, 56, 7, 18, 29, 40, 51, 62, 13, 24, 35, 46, 57, 6, 17, 28, 39, 50, 61, 2, 14, 25, 36, 47, 58, 9, 20, 31, 42, 53, 64, 15, 26, 37, 48, 59, 10, 21, 32, 43, 54, 65, 16, 27, 38, 49, 60, 1, 11, 22, 33, 44, 63, 3, 5, 19, 30, 41, 52], 'cur_cost': 94344.0}, {'tour': array([45, 27, 13, 15, 33, 34,  5, 64, 61, 19, 31,  2,  7, 17, 26, 51, 48,
       35,  6, 28, 46, 11, 57,  4, 30,  9, 60, 20, 53, 40, 22, 59, 41, 62,
       12, 37, 16, 38, 10, 47, 14, 56,  0, 43, 39, 52,  1, 24, 29, 49, 54,
       32, 36, 63, 65,  8, 42, 55, 18, 25, 50,  3, 44, 21, 58, 23]), 'cur_cost': 109061.0}, {'tour': array([48, 44, 37, 49, 65, 53, 17,  6,  1, 16,  5, 56, 14, 10,  4, 13, 15,
       39,  7,  0, 29, 31, 43, 25, 42, 24, 27, 19, 20, 50, 62,  9, 38, 23,
       46, 51, 11, 36, 40, 41, 28, 34, 47, 55, 60, 32, 12, 57, 45,  3, 30,
       54, 59, 61, 18,  2,  8, 22, 64, 52, 63, 26, 21, 35, 58, 33]), 'cur_cost': 94942.0}, {'tour': array([24,  3, 54, 46, 58, 13, 63, 48,  7,  0, 37, 18, 25, 61, 59, 16, 65,
       52, 15, 62, 43, 21, 19, 39, 23, 51, 12, 41, 55, 42, 31, 10, 27, 20,
       14, 11, 30, 17, 22, 32, 28, 50, 57, 36,  6,  2,  9, 49, 40, 34, 44,
       47, 29, 53, 60, 56, 33, 26,  1, 38,  8, 35,  4,  5, 45, 64]), 'cur_cost': 107262.0}]
2025-07-03 15:02:13,002 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 15:02:13,002 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 0, 'total_searches': 4, 'skip_rate': 0.0, 'estimated_time_saved': 0, 'path_optimizer': {'path_count': 4, 'cache_hits': 0, 'similarity_calculations': 6, 'cache_hit_rate': 0.0, 'cache_size': 6}}
2025-07-03 15:02:13,002 - __main__ - WARNING - 个体 2 的利用路径生成失败，保留原个体
2025-07-03 15:02:13,003 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 15:02:13,003 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:02:13,004 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13348.0 (rank 0.0%)
- Population diversity: 0.6569230769230769
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': 'hig, {'region': 'sho
- Difficult regions to avoid (sample): {'region': 'low, {'region': 'lon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:02:13,004 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:02:13,004 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:02:26,236 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 23, 34, 45, 56, 65, 54, 43, 32, 21, 10, 1, 11, 22, 33, 44, 55, 66, 2, 13, 24, 35, 46, 57, 64, 53, 42, 31, 20, 3, 14, 25, 36, 47, 58, 63, 52, 41, 30, 4, 15, 26, 37, 48, 59, 62, 51, 40, 5, 16, 27, 38, 49, 60, 61, 50, 39, 28, 17, 6, 7, 8, 9, 18, 19, 29],
  "targeted_regions": "hig, sho",
  "strategy_comment": "Targets opportunity regions with spiral pattern."
}
```
2025-07-03 15:02:26,237 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:02:26,237 - ExplorationExpert - INFO - 探索路径生成完成，成本: 85180.0, 路径: [0, 12, 23, 34, 45, 56, 65, 54, 43, 32, 21, 10, 1, 11, 22, 33, 44, 55, 2, 13, 24, 35, 46, 57, 64, 53, 42, 31, 20, 3, 14, 25, 36, 47, 58, 63, 52, 41, 30, 4, 15, 26, 37, 48, 59, 62, 51, 40, 5, 16, 27, 38, 49, 60, 61, 50, 39, 28, 17, 6, 7, 8, 9, 18, 19, 29]
2025-07-03 15:02:26,237 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 23, 34, 45, 56, 65, 54, 43, 32, 21, 10, 1, 11, 22, 33, 44, 55, 2, 13, 24, 35, 46, 57, 64, 53, 42, 31, 20, 3, 14, 25, 36, 47, 58, 63, 52, 41, 30, 4, 15, 26, 37, 48, 59, 62, 51, 40, 5, 16, 27, 38, 49, 60, 61, 50, 39, 28, 17, 6, 7, 8, 9, 18, 19, 29], 'cur_cost': 85180.0}
2025-07-03 15:02:26,237 - __main__ - INFO - 为个体 4 生成利用路径
2025-07-03 15:02:26,237 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:02:26,239 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:02:26,239 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 4 处的路径，新成本: 100650.0
2025-07-03 15:02:26,239 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：3,跳过局部搜索
2025-07-03 15:02:26,239 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 15:02:26,240 - __main__ - INFO - 个体 4 利用路径生成报告: {'new_tour': array([63,  5, 30, 50, 56, 11, 35, 45, 32, 44, 53,  3,  6, 18, 61, 22,  7,
       40, 36, 49, 42, 12, 55, 16, 47, 13, 19, 52,  0, 20, 21, 48, 54, 41,
       23, 25, 26, 29, 10,  4, 27, 58, 14, 51, 46, 39, 65, 38, 33, 28, 60,
        1, 17,  8, 62, 43, 34, 59, 24, 15,  2, 31, 37,  9, 64, 57]), 'cur_cost': 100650.0}
2025-07-03 15:02:26,240 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 15:02:26,240 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:02:26,241 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 13348.0 (rank 0.0%)
- Population diversity: 0.7630769230769231
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': 'hig, {'region': 'sho
- Difficult regions to avoid (sample): {'region': 'low, {'region': 'lon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:02:26,241 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:02:26,242 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:02:39,375 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "hig, sho",
  "strategy_comment": "Spiral through opportunity regions with modular jumps"
}
```
2025-07-03 15:02:39,375 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:02:39,375 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-07-03 15:02:39,375 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-07-03 15:02:39,375 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-03 15:02:39,377 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:02:39,377 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 94344.0 (rank 10.0%)
- Population diversity: 0.6892307692307692
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': 'hig, {'region': 'sho
- Difficult regions to avoid (sample): {'region': 'low, {'region': 'lon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:02:39,378 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:02:39,378 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:02:51,798 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47],
  "targeted_regions": "high, sho",
  "strategy_comment": "Targets high and sho regions with sparse jumps."
}
```
2025-07-03 15:02:51,799 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:02:51,800 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100095.0, 路径: [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47]
2025-07-03 15:02:51,800 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}
2025-07-03 15:02:51,800 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 15:02:51,800 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:02:51,801 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:02:51,801 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 111919.0
2025-07-03 15:02:52,302 - ExploitationExpert - INFO - res_population_num: 20
2025-07-03 15:02:52,302 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9527, 9530, 9534, 9558, 9609, 89049, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 15:02:52,303 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-03 15:02:52,310 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:02:52,310 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11], 'cur_cost': 99370.0}, {'tour': array([59, 63,  4, 64,  1,  2, 37, 54, 49, 52, 55, 46, 12, 21, 41, 27, 39,
       45, 15, 19, 30, 31, 29, 36,  8, 26, 32, 43, 10, 18, 62,  7,  5, 53,
        0, 48,  3,  9, 28, 24, 50, 47, 60, 25, 65, 20, 17, 14, 35, 51, 61,
       16, 11, 34, 44, 22, 56, 42, 38,  6, 23, 40, 33, 57, 58, 13]), 'cur_cost': 95954.0}, {'tour': [0, 12, 23, 34, 45, 56, 65, 54, 43, 32, 21, 10, 1, 11, 22, 33, 44, 55, 2, 13, 24, 35, 46, 57, 64, 53, 42, 31, 20, 3, 14, 25, 36, 47, 58, 63, 52, 41, 30, 4, 15, 26, 37, 48, 59, 62, 51, 40, 5, 16, 27, 38, 49, 60, 61, 50, 39, 28, 17, 6, 7, 8, 9, 18, 19, 29], 'cur_cost': 85180.0}, {'tour': array([63,  5, 30, 50, 56, 11, 35, 45, 32, 44, 53,  3,  6, 18, 61, 22,  7,
       40, 36, 49, 42, 12, 55, 16, 47, 13, 19, 52,  0, 20, 21, 48, 54, 41,
       23, 25, 26, 29, 10,  4, 27, 58, 14, 51, 46, 39, 65, 38, 33, 28, 60,
        1, 17,  8, 62, 43, 34, 59, 24, 15,  2, 31, 37,  9, 64, 57]), 'cur_cost': 100650.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([29, 28, 58, 21, 12, 10, 45, 26, 34, 50, 30, 60, 20, 14, 27, 41,  4,
       18, 61,  1, 22, 46,  9, 47,  5, 17, 53, 49, 33, 57, 19, 40, 38, 31,
        3, 15, 44, 11,  8, 23, 35, 62, 48, 24, 51, 43, 13, 52, 56, 25, 65,
       37,  6,  2, 63, 16, 36, 39, 55, 64, 32, 42,  7, 59, 54,  0]), 'cur_cost': 111919.0}, {'tour': array([48, 44, 37, 49, 65, 53, 17,  6,  1, 16,  5, 56, 14, 10,  4, 13, 15,
       39,  7,  0, 29, 31, 43, 25, 42, 24, 27, 19, 20, 50, 62,  9, 38, 23,
       46, 51, 11, 36, 40, 41, 28, 34, 47, 55, 60, 32, 12, 57, 45,  3, 30,
       54, 59, 61, 18,  2,  8, 22, 64, 52, 63, 26, 21, 35, 58, 33]), 'cur_cost': 94942.0}, {'tour': array([24,  3, 54, 46, 58, 13, 63, 48,  7,  0, 37, 18, 25, 61, 59, 16, 65,
       52, 15, 62, 43, 21, 19, 39, 23, 51, 12, 41, 55, 42, 31, 10, 27, 20,
       14, 11, 30, 17, 22, 32, 28, 50, 57, 36,  6,  2,  9, 49, 40, 34, 44,
       47, 29, 53, 60, 56, 33, 26,  1, 38,  8, 35,  4,  5, 45, 64]), 'cur_cost': 107262.0}]
2025-07-03 15:02:52,314 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 15:02:52,314 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 6, 'skip_rate': 0.16666666666666666, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 5, 'cache_hits': 3, 'similarity_calculations': 14, 'cache_hit_rate': 0.21428571428571427, 'cache_size': 11}}
2025-07-03 15:02:52,314 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-03 15:02:52,314 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-03 15:02:52,314 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:02:52,315 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 94942.0 (rank 10.0%)
- Population diversity: 0.6584615384615384
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'region': 'hig, {'region': 'sho
- Difficult regions to avoid (sample): {'region': 'low, {'region': 'lon
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:02:52,315 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:02:52,316 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:03:05,371 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high, sho",
  "strategy_comment": "Leverages high and sho regions with spiral pattern."
}
```
2025-07-03 15:03:05,372 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:03:05,372 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-07-03 15:03:05,372 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-07-03 15:03:05,373 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 15:03:05,373 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:03:05,373 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:03:05,373 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 110385.0
2025-07-03 15:03:06,984 - ExploitationExpert - INFO - res_population_num: 21
2025-07-03 15:03:06,984 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9527, 9530, 9534, 9558, 9609, 89049, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521]
2025-07-03 15:03:06,985 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 15:03:06,992 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:03:06,993 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11], 'cur_cost': 99370.0}, {'tour': array([59, 63,  4, 64,  1,  2, 37, 54, 49, 52, 55, 46, 12, 21, 41, 27, 39,
       45, 15, 19, 30, 31, 29, 36,  8, 26, 32, 43, 10, 18, 62,  7,  5, 53,
        0, 48,  3,  9, 28, 24, 50, 47, 60, 25, 65, 20, 17, 14, 35, 51, 61,
       16, 11, 34, 44, 22, 56, 42, 38,  6, 23, 40, 33, 57, 58, 13]), 'cur_cost': 95954.0}, {'tour': [0, 12, 23, 34, 45, 56, 65, 54, 43, 32, 21, 10, 1, 11, 22, 33, 44, 55, 2, 13, 24, 35, 46, 57, 64, 53, 42, 31, 20, 3, 14, 25, 36, 47, 58, 63, 52, 41, 30, 4, 15, 26, 37, 48, 59, 62, 51, 40, 5, 16, 27, 38, 49, 60, 61, 50, 39, 28, 17, 6, 7, 8, 9, 18, 19, 29], 'cur_cost': 85180.0}, {'tour': array([63,  5, 30, 50, 56, 11, 35, 45, 32, 44, 53,  3,  6, 18, 61, 22,  7,
       40, 36, 49, 42, 12, 55, 16, 47, 13, 19, 52,  0, 20, 21, 48, 54, 41,
       23, 25, 26, 29, 10,  4, 27, 58, 14, 51, 46, 39, 65, 38, 33, 28, 60,
        1, 17,  8, 62, 43, 34, 59, 24, 15,  2, 31, 37,  9, 64, 57]), 'cur_cost': 100650.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([29, 28, 58, 21, 12, 10, 45, 26, 34, 50, 30, 60, 20, 14, 27, 41,  4,
       18, 61,  1, 22, 46,  9, 47,  5, 17, 53, 49, 33, 57, 19, 40, 38, 31,
        3, 15, 44, 11,  8, 23, 35, 62, 48, 24, 51, 43, 13, 52, 56, 25, 65,
       37,  6,  2, 63, 16, 36, 39, 55, 64, 32, 42,  7, 59, 54,  0]), 'cur_cost': 111919.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([56,  5, 36, 38, 14, 30,  8, 41, 45, 16, 48, 32, 39,  7, 37, 50, 26,
       65, 20, 10,  1, 55, 21, 25, 17, 27, 51,  6, 43, 44, 40, 18, 62, 15,
       61, 53, 59, 23, 42,  0, 13,  2, 52, 60, 24, 49, 11, 31, 47, 34, 58,
       33,  3, 29, 22,  9, 35, 57, 19, 46, 54, 28,  4, 12, 63, 64]), 'cur_cost': 110385.0}]
2025-07-03 15:03:06,994 - ExploitationExpert - INFO - 局部搜索耗时: 1.62秒
2025-07-03 15:03:06,995 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 7, 'skip_rate': 0.14285714285714285, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 6, 'cache_hits': 3, 'similarity_calculations': 19, 'cache_hit_rate': 0.15789473684210525, 'cache_size': 16}}
2025-07-03 15:03:06,995 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 15:03:06,995 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11], 'cur_cost': 99370.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 23, 34, 45, 56, 65, 54, 43, 32, 21, 10, 1, 11, 22, 33, 44, 55, 2, 13, 24, 35, 46, 57, 64, 53, 42, 31, 20, 3, 14, 25, 36, 47, 58, 63, 52, 41, 30, 4, 15, 26, 37, 48, 59, 62, 51, 40, 5, 16, 27, 38, 49, 60, 61, 50, 39, 28, 17, 6, 7, 8, 9, 18, 19, 29], 'cur_cost': 85180.0}}, {'individual': 4, 'strategy': 'exploit', 'path_data': {'new_tour': array([63,  5, 30, 50, 56, 11, 35, 45, 32, 44, 53,  3,  6, 18, 61, 22,  7,
       40, 36, 49, 42, 12, 55, 16, 47, 13, 19, 52,  0, 20, 21, 48, 54, 41,
       23, 25, 26, 29, 10,  4, 27, 58, 14, 51, 46, 39, 65, 38, 33, 28, 60,
        1, 17,  8, 62, 43, 34, 59, 24, 15,  2, 31, 37,  9, 64, 57]), 'cur_cost': 100650.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}]
2025-07-03 15:03:06,997 - __main__ - INFO - 进化阶段完成
2025-07-03 15:03:06,997 - __main__ - INFO - 开始评估阶段
2025-07-03 15:03:06,997 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 15:03:06,999 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 15:03:06,999 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 1/5

#### Population metrics
- Min cost: 13348.0 → 85180.0 (Δ -71832.0)
- Mean cost: 68441.7 → 100012.1
- Diversity: 0.626 → 0.589 (Δ -0.0369)

#### Elite metrics
- Elite count: 8 → 21
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.342 → 0.133 (Δ -0.209)

#### Strategy performance
- Explore: 6 inds, success 33.3% , avg Δ -23302.5
- Exploit: 4 inds, success 0.0% , avg Δ -43972.25

#### Other indicators
- No-change individuals: 0
- Regression individuals: 8
- Historical trends: Last 1 iterations: costs [13348.0], diversity [0.8323232323232322]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 15:03:06,999 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:03:13,810 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.333, "exploit": 0.0},
  "balance_state": "exploration_needed",
  "recommendations": ["set explore_ratio to 0.6", "increase diversity weight to 0.8"]
}
```
2025-07-03 15:03:13,838 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 15:03:13,839 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.333, "exploit": 0.0},
  "balance_state": "exploration_needed",
  "recommendations": ["set explore_ratio to 0.6", "increase diversity weight to 0.8"]
}
```
2025-07-03 15:03:13,840 - __main__ - INFO - 评估阶段完成
2025-07-03 15:03:13,840 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.333, "exploit": 0.0},
  "balance_state": "exploration_needed",
  "recommendations": ["set explore_ratio to 0.6", "increase diversity weight to 0.8"]
}
```
2025-07-03 15:03:13,840 - __main__ - INFO - 当前最佳适应度: 85180.0
2025-07-03 15:03:13,842 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_1.pkl
2025-07-03 15:03:13,842 - __main__ - INFO - composite13_66 开始进化第 3 代
2025-07-03 15:03:13,842 - __main__ - INFO - 开始分析阶段
2025-07-03 15:03:13,843 - StatsExpert - INFO - 开始统计分析
2025-07-03 15:03:13,860 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 85180.0, 'max': 111919.0, 'mean': 100012.1, 'std': 7004.758888784109}, 'diversity': 0.7932659932659933, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 15:03:13,861 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 85180.0, 'max': 111919.0, 'mean': 100012.1, 'std': 7004.758888784109}, 'diversity_level': 0.7932659932659933, 'convergence_level': 0.0, 'clusters': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1], 'summary': '6 clusters, sizes [5, 1, 1, 1, 1, 1]'}, 'clustering_info': {'clusters': 6, 'cluster_sizes': [5, 1, 1, 1, 1, 1], 'summary': '6 clusters, sizes [5, 1, 1, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 15:03:13,861 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 15:03:13,862 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 15:03:13,862 - PathExpert - INFO - 开始路径结构分析
2025-07-03 15:03:13,865 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 15:03:13,865 - PathExpert - INFO - 路径结构分析完成
2025-07-03 15:03:13,866 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 15:03:13,866 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 15:03:13,866 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 15:03:13,866 - EliteExpert - INFO - 开始精英解分析
2025-07-03 15:03:13,874 - EliteExpert - INFO - 精英解分析完成
2025-07-03 15:03:13,874 - __main__ - INFO - 精英专家分析报告: {'elite_count': 21, 'elite_common_features': {'common_edges': {'(34, 35)': 0.7142857142857143}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 75659.0, 'avg_gap': 86696.76666666668}, 'structure_gap': {'unique_elite_edges': 255, 'unique_pop_edges': 373, 'common_edges': 29}}, 'elite_diversity': {'diversity_score': 0.2997113997113997}}
2025-07-03 15:03:13,875 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 15:03:13,875 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 15:03:13,877 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 15:03:13,877 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 2/5)
- population_size: 10
- cost_stats: min 85180.0, mean 100012.1, max 111919.0, std 7004.758888784109
- diversity: 0.7932659932659933
- convergence: 0.0
- clustering: 6 clusters, sizes [5, 1, 1, 1, 1, 1]
- recent_best_costs: Recent costs: [13348.0, 85180.0] (Δ 0.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.2997113997113997}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 15:03:13,878 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:03:31,520 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "unknown"
  },
  "population_state": {
    "diversity": 0.793,
    "convergence": 0.0,
    "clustering": "strong"
  },
  "difficult_regions": [
    {
      "description": "Low-density cells in the grid (e.g., top-middle and middle-left cells)",
      "reason": "Density grid shows 0 nodes in these cells, indicating sparse regions"
    },
    {
      "description": "Long-edge corridors",
      "reason": "Long-edge ratio of 0.249 suggests significant distances between some nodes"
    }
  ],
  "opportunity_regions": [
    {
      "description": "High-density cells (e.g., top-left, bottom-left, and bottom-right cells)",
      "reason": "Density grid shows 14 nodes in these cells, indicating concentrated regions"
    },
    {
      "description": "Centroid region (1795.652, 1565.212)",
      "reason": "High node density around the centroid suggests potential for optimization"
    }
  ],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "increase mutation rate to enhance diversity",
      "use local search operators in high-density regions",
      "implement edge recombination to exploit common subpaths",
      "apply long-edge reduction heuristics in difficult regions"
    ]
  }
}
```
2025-07-03 15:03:31,521 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 15:03:31,521 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.793, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'description': 'Low-density cells in the grid (e.g., top-middle and middle-left cells)', 'reason': 'Density grid shows 0 nodes in these cells, indicating sparse regions'}, {'description': 'Long-edge corridors', 'reason': 'Long-edge ratio of 0.249 suggests significant distances between some nodes'}], 'opportunity_regions': [{'description': 'High-density cells (e.g., top-left, bottom-left, and bottom-right cells)', 'reason': 'Density grid shows 14 nodes in these cells, indicating concentrated regions'}, {'description': 'Centroid region (1795.652, 1565.212)', 'reason': 'High node density around the centroid suggests potential for optimization'}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['increase mutation rate to enhance diversity', 'use local search operators in high-density regions', 'implement edge recombination to exploit common subpaths', 'apply long-edge reduction heuristics in difficult regions']}}
2025-07-03 15:03:31,522 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 15:03:31,522 - __main__ - INFO - 分析阶段完成
2025-07-03 15:03:31,522 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'unknown'}, 'population_state': {'diversity': 0.793, 'convergence': 0.0, 'clustering': 'strong'}, 'difficult_regions': [{'description': 'Low-density cells in the grid (e.g., top-middle and middle-left cells)', 'reason': 'Density grid shows 0 nodes in these cells, indicating sparse regions'}, {'description': 'Long-edge corridors', 'reason': 'Long-edge ratio of 0.249 suggests significant distances between some nodes'}], 'opportunity_regions': [{'description': 'High-density cells (e.g., top-left, bottom-left, and bottom-right cells)', 'reason': 'Density grid shows 14 nodes in these cells, indicating concentrated regions'}, {'description': 'Centroid region (1795.652, 1565.212)', 'reason': 'High node density around the centroid suggests potential for optimization'}], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['increase mutation rate to enhance diversity', 'use local search operators in high-density regions', 'implement edge recombination to exploit common subpaths', 'apply long-edge reduction heuristics in difficult regions']}}
2025-07-03 15:03:31,522 - __main__ - INFO - 开始策略分配阶段
2025-07-03 15:03:31,522 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 15:03:31,523 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 2
- Population: 10 individuals
  • diversity: 0.793
  • best_cost: 85180.0
  • mean_cost: 100012.1
- Search-space: ruggedness high, modality multi, deceptiveness unknown
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): {'description': 'Low, {'description': 'Lon
- Opportunity regions (# 2): {'description': 'Hig, {'description': 'Cen
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.333, "exploit": 0.0},
  "balance_state":...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 15:03:31,523 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 15:03:31,523 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:03:39,860 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 80% explore to discover opportunity regions."
}
```
2025-07-03 15:03:39,860 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-07-03 15:03:39,860 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-07-03 15:03:39,860 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 80% explore to discover opportunity regions."
}
```
2025-07-03 15:03:39,861 - __main__ - INFO - 策略分配阶段完成
2025-07-03 15:03:39,861 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit', 'explore', 'explore', 'exploit']
2025-07-03 15:03:39,861 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "explore",
    "6": "exploit",
    "7": "explore",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 80% explore to discover opportunity regions."
}
```
2025-07-03 15:03:39,862 - __main__ - INFO - 开始进化阶段
2025-07-03 15:03:39,862 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 15:03:39,862 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:03:39,863 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 20.0%)
- Population diversity: 0.5892307692307692
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':, {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:03:39,863 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:03:39,863 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:03:53,750 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1],
  "targeted_regions": "sparse_cell(5,12), sparse_cell(19,26)",
  "strategy_comment": "Targets low-density cells with diagonal traversal."
}
```
2025-07-03 15:03:53,751 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:03:53,751 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60408.0, 路径: [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1]
2025-07-03 15:03:53,751 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}
2025-07-03 15:03:53,751 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 15:03:53,751 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:03:53,752 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 99370.0 (rank 50.0%)
- Population diversity: 0.6676923076923077
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':, {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:03:53,752 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:03:53,752 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:04:07,513 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47],
  "targeted_regions": "sparse_cell(12,24), dense_cell(5,17)",
  "strategy_comment": "Alternates between sparse and dense regions to balance exploration."
}
```
2025-07-03 15:04:07,514 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:04:07,514 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100095.0, 路径: [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47]
2025-07-03 15:04:07,514 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}
2025-07-03 15:04:07,514 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 15:04:07,515 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:04:07,515 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 95954.0 (rank 20.0%)
- Population diversity: 0.6661538461538462
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':, {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:04:07,515 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:04:07,516 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:04:21,363 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1],
  "targeted_regions": "sparse_cell(5,12), sparse_cell(19,26)",
  "strategy_comment": "Targets low-density cells with diagonal traversal."
}
```
2025-07-03 15:04:21,364 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:04:21,364 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60408.0, 路径: [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1]
2025-07-03 15:04:21,365 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}
2025-07-03 15:04:21,365 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-03 15:04:21,365 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:04:21,366 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:04:21,366 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 105022.0
2025-07-03 15:04:21,869 - ExploitationExpert - INFO - res_population_num: 22
2025-07-03 15:04:21,869 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9530, 9534, 9558, 9609, 89049, 9521]
2025-07-03 15:04:21,869 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-03 15:04:21,877 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:04:21,877 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}, {'tour': array([18, 56,  4, 42, 21, 35,  9, 17, 28, 14, 50, 48, 30, 53, 44, 26, 60,
       27, 40, 34, 64, 49,  3,  8, 23, 63, 55, 52, 58, 37, 62, 43, 33, 59,
       65,  1, 61,  5, 54,  6, 13, 32,  0, 22, 39, 31, 12, 38,  7, 57, 41,
        2, 47, 25, 24, 36, 15, 10, 16, 11, 20, 19, 46, 29, 51, 45]), 'cur_cost': 105022.0}, {'tour': [63, 5, 30, 50, 56, 11, 35, 45, 32, 44, 53, 3, 6, 18, 61, 22, 7, 40, 36, 49, 42, 12, 55, 16, 47, 13, 19, 52, 0, 20, 21, 48, 54, 41, 23, 25, 26, 29, 10, 4, 27, 58, 14, 51, 46, 39, 65, 38, 33, 28, 60, 1, 17, 8, 62, 43, 34, 59, 24, 15, 2, 31, 37, 9, 64, 57], 'cur_cost': 100650.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([29, 28, 58, 21, 12, 10, 45, 26, 34, 50, 30, 60, 20, 14, 27, 41,  4,
       18, 61,  1, 22, 46,  9, 47,  5, 17, 53, 49, 33, 57, 19, 40, 38, 31,
        3, 15, 44, 11,  8, 23, 35, 62, 48, 24, 51, 43, 13, 52, 56, 25, 65,
       37,  6,  2, 63, 16, 36, 39, 55, 64, 32, 42,  7, 59, 54,  0]), 'cur_cost': 111919.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([56,  5, 36, 38, 14, 30,  8, 41, 45, 16, 48, 32, 39,  7, 37, 50, 26,
       65, 20, 10,  1, 55, 21, 25, 17, 27, 51,  6, 43, 44, 40, 18, 62, 15,
       61, 53, 59, 23, 42,  0, 13,  2, 52, 60, 24, 49, 11, 31, 47, 34, 58,
       33,  3, 29, 22,  9, 35, 57, 19, 46, 54, 28,  4, 12, 63, 64]), 'cur_cost': 110385.0}]
2025-07-03 15:04:21,879 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 15:04:21,879 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 8, 'skip_rate': 0.125, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 7, 'cache_hits': 3, 'similarity_calculations': 25, 'cache_hit_rate': 0.12, 'cache_size': 22}}
2025-07-03 15:04:21,879 - __main__ - WARNING - 个体 3 的利用路径生成失败，保留原个体
2025-07-03 15:04:21,880 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 15:04:21,880 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:04:21,881 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100650.0 (rank 60.0%)
- Population diversity: 0.5769230769230769
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':, {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:04:21,881 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:04:21,881 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:04:35,534 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "sparse_cell(12,24,36,48,60)",
  "strategy_comment": "Targets low-density diagonal cells with modular jumps."
}
```
2025-07-03 15:04:35,535 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:04:35,536 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-07-03 15:04:35,536 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-07-03 15:04:35,536 - __main__ - INFO - 为个体 5 生成探索路径
2025-07-03 15:04:35,536 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:04:35,537 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 20.0%)
- Population diversity: 0.49230769230769234
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':, {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:04:35,537 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:04:35,537 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:04:49,544 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47],
  "targeted_regions": "sparse_cell(12,24), dense_cell(5,17)",
  "strategy_comment": "Alternates between sparse and dense regions for diversity."
}
```
2025-07-03 15:04:49,545 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:04:49,545 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100095.0, 路径: [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47]
2025-07-03 15:04:49,545 - __main__ - INFO - 个体 5 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}
2025-07-03 15:04:49,546 - __main__ - INFO - 为个体 6 生成利用路径
2025-07-03 15:04:49,546 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:04:49,546 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:04:49,546 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 6 处的路径，新成本: 114083.0
2025-07-03 15:04:50,049 - ExploitationExpert - INFO - res_population_num: 23
2025-07-03 15:04:50,049 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9530, 9534, 9558, 9609, 89049, 9521, 9521]
2025-07-03 15:04:50,049 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 15:04:50,059 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:04:50,059 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}, {'tour': array([18, 56,  4, 42, 21, 35,  9, 17, 28, 14, 50, 48, 30, 53, 44, 26, 60,
       27, 40, 34, 64, 49,  3,  8, 23, 63, 55, 52, 58, 37, 62, 43, 33, 59,
       65,  1, 61,  5, 54,  6, 13, 32,  0, 22, 39, 31, 12, 38,  7, 57, 41,
        2, 47, 25, 24, 36, 15, 10, 16, 11, 20, 19, 46, 29, 51, 45]), 'cur_cost': 105022.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([53,  4, 47,  9, 34, 31, 11, 48, 45,  0, 58, 39, 41, 42, 44, 25, 12,
       55,  5, 64, 27, 52, 49, 24, 20, 36, 22, 38, 56, 32, 10, 35,  8, 40,
       29, 54,  1, 33, 63, 23, 30, 61, 13, 28, 18, 17,  7,  6, 59, 65, 50,
       62, 19, 37, 51,  3, 21, 14, 60, 16, 57, 46, 26,  2, 43, 15]), 'cur_cost': 114083.0}, {'tour': array([29, 28, 58, 21, 12, 10, 45, 26, 34, 50, 30, 60, 20, 14, 27, 41,  4,
       18, 61,  1, 22, 46,  9, 47,  5, 17, 53, 49, 33, 57, 19, 40, 38, 31,
        3, 15, 44, 11,  8, 23, 35, 62, 48, 24, 51, 43, 13, 52, 56, 25, 65,
       37,  6,  2, 63, 16, 36, 39, 55, 64, 32, 42,  7, 59, 54,  0]), 'cur_cost': 111919.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([56,  5, 36, 38, 14, 30,  8, 41, 45, 16, 48, 32, 39,  7, 37, 50, 26,
       65, 20, 10,  1, 55, 21, 25, 17, 27, 51,  6, 43, 44, 40, 18, 62, 15,
       61, 53, 59, 23, 42,  0, 13,  2, 52, 60, 24, 49, 11, 31, 47, 34, 58,
       33,  3, 29, 22,  9, 35, 57, 19, 46, 54, 28,  4, 12, 63, 64]), 'cur_cost': 110385.0}]
2025-07-03 15:04:50,061 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 15:04:50,061 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 9, 'skip_rate': 0.1111111111111111, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 8, 'cache_hits': 3, 'similarity_calculations': 32, 'cache_hit_rate': 0.09375, 'cache_size': 29}}
2025-07-03 15:04:50,062 - __main__ - WARNING - 个体 6 的利用路径生成失败，保留原个体
2025-07-03 15:04:50,062 - __main__ - INFO - 为个体 7 生成探索路径
2025-07-03 15:04:50,062 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:04:50,063 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 111919.0 (rank 80.0%)
- Population diversity: 0.5784615384615385
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':, {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:04:50,063 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:04:50,064 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:05:03,006 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "sparse_cell(12,24), sparse_cell(36,48)",
  "strategy_comment": "Targets low-density cells with alternating jumps."
}
```
2025-07-03 15:05:03,007 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:05:03,007 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-07-03 15:05:03,007 - __main__ - INFO - 个体 7 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-07-03 15:05:03,008 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-03 15:05:03,008 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:05:03,009 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 20.0%)
- Population diversity: 0.49230769230769234
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): {'description':, {'description':
- Difficult regions to avoid (sample): {'description':, {'description':
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:05:03,009 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:05:03,010 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:05:16,684 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1],
  "targeted_regions": "sparse_cell(5,12), dense_cell(40,47)",
  "strategy_comment": "Targets low-density areas and leverages high-quality edges."
}
```
2025-07-03 15:05:16,685 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:05:16,685 - ExplorationExpert - INFO - 探索路径生成完成，成本: 60408.0, 路径: [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1]
2025-07-03 15:05:16,685 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}
2025-07-03 15:05:16,685 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 15:05:16,686 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:05:16,686 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:05:16,686 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 100167.0
2025-07-03 15:05:17,188 - ExploitationExpert - INFO - res_population_num: 24
2025-07-03 15:05:17,188 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9530, 9534, 9558, 9609, 89049, 9521, 9521, 9521]
2025-07-03 15:05:17,188 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-03 15:05:17,198 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:05:17,198 - ExploitationExpert - INFO - populations: [{'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}, {'tour': array([18, 56,  4, 42, 21, 35,  9, 17, 28, 14, 50, 48, 30, 53, 44, 26, 60,
       27, 40, 34, 64, 49,  3,  8, 23, 63, 55, 52, 58, 37, 62, 43, 33, 59,
       65,  1, 61,  5, 54,  6, 13, 32,  0, 22, 39, 31, 12, 38,  7, 57, 41,
        2, 47, 25, 24, 36, 15, 10, 16, 11, 20, 19, 46, 29, 51, 45]), 'cur_cost': 105022.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([53,  4, 47,  9, 34, 31, 11, 48, 45,  0, 58, 39, 41, 42, 44, 25, 12,
       55,  5, 64, 27, 52, 49, 24, 20, 36, 22, 38, 56, 32, 10, 35,  8, 40,
       29, 54,  1, 33, 63, 23, 30, 61, 13, 28, 18, 17,  7,  6, 59, 65, 50,
       62, 19, 37, 51,  3, 21, 14, 60, 16, 57, 46, 26,  2, 43, 15]), 'cur_cost': 114083.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}, {'tour': array([63, 20, 12,  6, 59, 14, 30, 23, 31, 42,  0, 18, 17, 47,  3, 55, 51,
       15,  9,  2, 10, 19,  5, 58, 38, 24, 36, 25,  7, 53,  8, 32, 50,  4,
       26, 62, 61, 45, 37,  1, 33, 29, 40, 16, 52, 21, 64, 57, 35, 41, 65,
       43, 28, 34, 27, 22, 56, 39, 13, 46, 11, 54, 49, 48, 44, 60]), 'cur_cost': 100167.0}]
2025-07-03 15:05:17,200 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 15:05:17,200 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 1, 'total_searches': 10, 'skip_rate': 0.1, 'estimated_time_saved': 5, 'path_optimizer': {'path_count': 9, 'cache_hits': 3, 'similarity_calculations': 40, 'cache_hit_rate': 0.075, 'cache_size': 37}}
2025-07-03 15:05:17,200 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 15:05:17,200 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 5, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}}, {'individual': 7, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}}]
2025-07-03 15:05:17,201 - __main__ - INFO - 进化阶段完成
2025-07-03 15:05:17,201 - __main__ - INFO - 开始评估阶段
2025-07-03 15:05:17,201 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 15:05:17,203 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 15:05:17,203 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 2/5

#### Population metrics
- Min cost: 85180.0 → 60408.0 (Δ 24772.0)
- Mean cost: 100012.1 → 89839.8
- Diversity: 0.589 → 0.489 (Δ -0.1)

#### Elite metrics
- Elite count: 21 → 24
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.133 → 0.117 (Δ -0.016)

#### Strategy performance
- Explore: 7 inds, success 71.4% , avg Δ 17905.0
- Exploit: 3 inds, success 33.3% , avg Δ -7870.67

#### Other indicators
- No-change individuals: 0
- Regression individuals: 4
- Historical trends: Last 2 iterations: costs [13348.0, 85180.0], diversity [0.8323232323232322, 0.7932659932659933]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 15:05:17,204 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:05:25,257 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.714, "exploit": 0.333},
  "balance_state": "explore_biased",
  "recommendations": ["set_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 15:05:25,286 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 15:05:25,286 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.714, "exploit": 0.333},
  "balance_state": "explore_biased",
  "recommendations": ["set_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 15:05:25,287 - __main__ - INFO - 评估阶段完成
2025-07-03 15:05:25,287 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.714, "exploit": 0.333},
  "balance_state": "explore_biased",
  "recommendations": ["set_explore_ratio_to_0.6", "increase_diversity_weight_to_0.8"]
}
```
2025-07-03 15:05:25,287 - __main__ - INFO - 当前最佳适应度: 60408.0
2025-07-03 15:05:25,289 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_2.pkl
2025-07-03 15:05:25,289 - __main__ - INFO - composite13_66 开始进化第 4 代
2025-07-03 15:05:25,290 - __main__ - INFO - 开始分析阶段
2025-07-03 15:05:25,290 - StatsExpert - INFO - 开始统计分析
2025-07-03 15:05:25,307 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 60408.0, 'max': 114083.0, 'mean': 89839.8, 'std': 19739.547957336817}, 'diversity': 0.7986531986531986, 'clusters': {'clusters': 5, 'cluster_sizes': [3, 4, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 15:05:25,308 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 60408.0, 'max': 114083.0, 'mean': 89839.8, 'std': 19739.547957336817}, 'diversity_level': 0.7986531986531986, 'convergence_level': 0.0, 'clusters': {'clusters': 5, 'cluster_sizes': [3, 4, 1, 1, 1], 'summary': '5 clusters, sizes [3, 4, 1, 1, 1]'}, 'clustering_info': {'clusters': 5, 'cluster_sizes': [3, 4, 1, 1, 1], 'summary': '5 clusters, sizes [3, 4, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 15:05:25,308 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 15:05:25,309 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 15:05:25,309 - PathExpert - INFO - 开始路径结构分析
2025-07-03 15:05:25,311 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 15:05:25,312 - PathExpert - INFO - 路径结构分析完成
2025-07-03 15:05:25,312 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 15:05:25,312 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 15:05:25,312 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 15:05:25,312 - EliteExpert - INFO - 开始精英解分析
2025-07-03 15:05:25,323 - EliteExpert - INFO - 精英解分析完成
2025-07-03 15:05:25,324 - __main__ - INFO - 精英专家分析报告: {'elite_count': 24, 'elite_common_features': {'common_edges': {'(34, 35)': 0.7083333333333334}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 50887.0, 'avg_gap': 76998.75833333333}, 'structure_gap': {'unique_elite_edges': 267, 'unique_pop_edges': 313, 'common_edges': 19}}, 'elite_diversity': {'diversity_score': 0.2901844532279315}}
2025-07-03 15:05:25,324 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 15:05:25,324 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 15:05:25,326 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 15:05:25,326 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 3/5)
- population_size: 10
- cost_stats: min 60408.0, mean 89839.8, max 114083.0, std 19739.547957336817
- diversity: 0.7986531986531986
- convergence: 0.0
- clustering: 5 clusters, sizes [3, 4, 1, 1, 1]
- recent_best_costs: Recent costs: [13348.0, 85180.0, 60408.0] (Δ 12386.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.2901844532279315}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 15:05:25,327 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:05:37,469 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "moderate"
  },
  "population_state": {
    "diversity": 0.7986531986531986,
    "convergence": 0.0,
    "clustering": "moderate"
  },
  "difficult_regions": [
    "low-density cells in the grid",
    "long-edge corridors"
  ],
  "opportunity_regions": [
    "high-density cells in the grid",
    "centroid region around [1795.652, 1565.212]"
  ],
  "evolution_phase": "early",
  "evolution_direction": {
    "recommended_focus": "explore",
    "operators": [
      "crossover to combine promising subpaths",
      "mutation to introduce diversity in low-density regions",
      "local search to exploit high-density regions"
    ]
  }
}
```
2025-07-03 15:05:37,470 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 15:05:37,470 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.7986531986531986, 'convergence': 0.0, 'clustering': 'moderate'}, 'difficult_regions': ['low-density cells in the grid', 'long-edge corridors'], 'opportunity_regions': ['high-density cells in the grid', 'centroid region around [1795.652, 1565.212]'], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover to combine promising subpaths', 'mutation to introduce diversity in low-density regions', 'local search to exploit high-density regions']}}
2025-07-03 15:05:37,470 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 15:05:37,470 - __main__ - INFO - 分析阶段完成
2025-07-03 15:05:37,471 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.7986531986531986, 'convergence': 0.0, 'clustering': 'moderate'}, 'difficult_regions': ['low-density cells in the grid', 'long-edge corridors'], 'opportunity_regions': ['high-density cells in the grid', 'centroid region around [1795.652, 1565.212]'], 'evolution_phase': 'early', 'evolution_direction': {'recommended_focus': 'explore', 'operators': ['crossover to combine promising subpaths', 'mutation to introduce diversity in low-density regions', 'local search to exploit high-density regions']}}
2025-07-03 15:05:37,471 - __main__ - INFO - 开始策略分配阶段
2025-07-03 15:05:37,471 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 15:05:37,471 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 3
- Population: 10 individuals
  • diversity: 0.799
  • best_cost: 60408.0
  • mean_cost: 89839.8
- Search-space: ruggedness high, modality multi, deceptiveness moderate
- Evolution phase: early
- Landscape focus suggestion: explore
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): low-density cells in, long-edge corridors
- Opportunity regions (# 2): high-density cells i, centroid region arou
- Last-iteration feedback: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.714, "exploit": 0.333},
  "balance_state...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 15:05:37,471 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 15:05:37,472 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:05:46,510 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 80% explore to discover opportunity regions."
}
```
2025-07-03 15:05:46,511 - StrategyExpert - INFO - 策略分配完成: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 15:05:46,511 - __main__ - INFO - 策略分配报告: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 15:05:46,511 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 80% explore to discover opportunity regions."
}
```
2025-07-03 15:05:46,512 - __main__ - INFO - 策略分配阶段完成
2025-07-03 15:05:46,512 - __main__ - INFO - 策略分配: ['explore', 'explore', 'explore', 'explore', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 15:05:46,512 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.8,
  "individual_assignments": {
    "0": "explore",
    "1": "explore",
    "2": "explore",
    "3": "explore",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "High diversity and explore focus; 80% explore to discover opportunity regions."
}
```
2025-07-03 15:05:46,512 - __main__ - INFO - 开始进化阶段
2025-07-03 15:05:46,513 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 15:05:46,513 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:05:46,513 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 60408.0 (rank 0.0%)
- Population diversity: 0.48923076923076925
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:05:46,514 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:05:46,514 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:06:00,010 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59],
  "targeted_regions": "high-density ce, centroid region",
  "strategy_comment": "Leverages high-density regions while exploring adjacent sparse cells."
}
```
2025-07-03 15:06:00,010 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:06:00,011 - ExplorationExpert - INFO - 探索路径生成完成，成本: 101405.0, 路径: [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59]
2025-07-03 15:06:00,012 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}
2025-07-03 15:06:00,012 - __main__ - INFO - 为个体 1 生成探索路径
2025-07-03 15:06:00,012 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:06:00,013 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100095.0 (rank 40.0%)
- Population diversity: 0.5046153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:06:00,013 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:06:00,013 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:06:12,531 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47],
  "targeted_regions": "high-density ce, centroid region",
  "strategy_comment": "Leverages high-density cells while avoiding long-edge corridors"
}
```
2025-07-03 15:06:12,532 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:06:12,532 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100095.0, 路径: [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47]
2025-07-03 15:06:12,532 - __main__ - INFO - 个体 1 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}
2025-07-03 15:06:12,532 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 15:06:12,533 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:06:12,533 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 60408.0 (rank 0.0%)
- Population diversity: 0.5046153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:06:12,533 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:06:12,533 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:06:26,144 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47],
  "targeted_regions": "high-density ce, centroid region",
  "strategy_comment": "Focuses on high-density and centroid regions while avoiding long edges."
}
```
2025-07-03 15:06:26,144 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:06:26,144 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100095.0, 路径: [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47]
2025-07-03 15:06:26,144 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}
2025-07-03 15:06:26,144 - __main__ - INFO - 为个体 3 生成探索路径
2025-07-03 15:06:26,146 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:06:26,146 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 105022.0 (rank 80.0%)
- Population diversity: 0.5046153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:06:26,146 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:06:26,146 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:06:39,681 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47],
  "targeted_regions": "high-density ce, centroid region",
  "strategy_comment": "Leverages high-density and centroid regions while avoiding low-density areas."
}
```
2025-07-03 15:06:39,682 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:06:39,682 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100095.0, 路径: [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47]
2025-07-03 15:06:39,682 - __main__ - INFO - 个体 3 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}
2025-07-03 15:06:39,682 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 15:06:39,683 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:06:39,683 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 10.0%)
- Population diversity: 0.4123076923076923
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:06:39,683 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:06:39,684 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:06:53,562 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high-density ce, centroid region",
  "strategy_comment": "Leverages high-density cells with spiral pattern."
}
```
2025-07-03 15:06:53,562 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:06:53,562 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-07-03 15:06:53,562 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-07-03 15:06:53,562 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-03 15:06:53,564 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:06:53,564 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:06:53,564 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 106123.0
2025-07-03 15:06:53,565 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：7,跳过局部搜索
2025-07-03 15:06:53,565 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 15:06:53,565 - __main__ - INFO - 个体 5 利用路径生成报告: {'new_tour': array([45, 42, 12,  1, 33,  8, 54, 22,  0, 37, 50,  2, 36, 19, 56, 10, 44,
       17, 18, 52, 24, 27, 20, 65, 57,  6, 31, 23, 51, 16, 39, 61, 11, 21,
       35, 25, 59, 60, 13, 43,  5, 15,  3, 63, 46, 47, 64, 58, 14, 41, 29,
       55,  4, 38,  9, 26, 53, 32, 40, 62, 30, 28, 34, 49,  7, 48]), 'cur_cost': 106123.0}
2025-07-03 15:06:53,565 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-03 15:06:53,565 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:06:53,566 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 114083.0 (rank 90.0%)
- Population diversity: 0.5015384615384615
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:06:53,567 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:06:53,567 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:07:07,709 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47],
  "targeted_regions": "high-density ce, centroid region",
  "strategy_comment": "Focuses on high-density and centroid regions, skips long edges."
}
```
2025-07-03 15:07:07,710 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:07:07,710 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100095.0, 路径: [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47]
2025-07-03 15:07:07,711 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}
2025-07-03 15:07:07,711 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 15:07:07,711 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:07:07,711 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:07:07,712 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 111869.0
2025-07-03 15:07:08,213 - ExploitationExpert - INFO - res_population_num: 25
2025-07-03 15:07:08,214 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9530, 9534, 9558, 9609, 89049, 9521]
2025-07-03 15:07:08,214 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64)]
2025-07-03 15:07:08,224 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:07:08,225 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([45, 42, 12,  1, 33,  8, 54, 22,  0, 37, 50,  2, 36, 19, 56, 10, 44,
       17, 18, 52, 24, 27, 20, 65, 57,  6, 31, 23, 51, 16, 39, 61, 11, 21,
       35, 25, 59, 60, 13, 43,  5, 15,  3, 63, 46, 47, 64, 58, 14, 41, 29,
       55,  4, 38,  9, 26, 53, 32, 40, 62, 30, 28, 34, 49,  7, 48]), 'cur_cost': 106123.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([34, 59, 26, 38, 35, 22, 13, 53, 39, 52, 64,  9, 48, 14, 51,  6, 29,
       28, 63, 43, 15, 36, 61, 24, 62, 65, 33, 20, 57,  2,  7, 27, 55, 19,
       49, 21, 45,  0,  3, 18,  1,  8,  4, 12, 23, 41,  5, 46, 25, 31, 37,
       11, 47, 17, 56, 44, 40, 16, 50, 58, 32, 54, 10, 42, 60, 30]), 'cur_cost': 111869.0}, {'tour': [0, 5, 12, 19, 26, 33, 40, 47, 54, 61, 6, 13, 20, 27, 34, 41, 48, 55, 62, 7, 14, 21, 28, 35, 42, 49, 56, 63, 8, 15, 22, 29, 36, 43, 50, 57, 64, 9, 16, 23, 30, 37, 44, 51, 58, 65, 10, 17, 24, 31, 38, 45, 52, 59, 11, 18, 25, 32, 39, 46, 53, 60, 4, 3, 2, 1], 'cur_cost': 60408.0}, {'tour': array([63, 20, 12,  6, 59, 14, 30, 23, 31, 42,  0, 18, 17, 47,  3, 55, 51,
       15,  9,  2, 10, 19,  5, 58, 38, 24, 36, 25,  7, 53,  8, 32, 50,  4,
       26, 62, 61, 45, 37,  1, 33, 29, 40, 16, 52, 21, 64, 57, 35, 41, 65,
       43, 28, 34, 27, 22, 56, 39, 13, 46, 11, 54, 49, 48, 44, 60]), 'cur_cost': 100167.0}]
2025-07-03 15:07:08,227 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 15:07:08,228 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 12, 'skip_rate': 0.16666666666666666, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 10, 'cache_hits': 10, 'similarity_calculations': 57, 'cache_hit_rate': 0.17543859649122806, 'cache_size': 47}}
2025-07-03 15:07:08,228 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-03 15:07:08,228 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-03 15:07:08,229 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:07:08,229 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 60408.0 (rank 0.0%)
- Population diversity: 0.5046153846153846
- Evolution phase: early
- Landscape recommends focus: explore
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:07:08,230 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:07:08,230 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:07:22,476 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high-density ce, centroid region",
  "strategy_comment": "Leverages high-density regions while exploring adjacent sparse cells."
}
```
2025-07-03 15:07:22,477 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:07:22,477 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-07-03 15:07:22,477 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-07-03 15:07:22,478 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 15:07:22,478 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:07:22,478 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:07:22,479 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 96734.0
2025-07-03 15:07:22,981 - ExploitationExpert - INFO - res_population_num: 26
2025-07-03 15:07:22,981 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9530, 9534, 9558, 9609, 89049, 9521, 9521]
2025-07-03 15:07:22,981 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64)]
2025-07-03 15:07:22,991 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:07:22,992 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([45, 42, 12,  1, 33,  8, 54, 22,  0, 37, 50,  2, 36, 19, 56, 10, 44,
       17, 18, 52, 24, 27, 20, 65, 57,  6, 31, 23, 51, 16, 39, 61, 11, 21,
       35, 25, 59, 60, 13, 43,  5, 15,  3, 63, 46, 47, 64, 58, 14, 41, 29,
       55,  4, 38,  9, 26, 53, 32, 40, 62, 30, 28, 34, 49,  7, 48]), 'cur_cost': 106123.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([34, 59, 26, 38, 35, 22, 13, 53, 39, 52, 64,  9, 48, 14, 51,  6, 29,
       28, 63, 43, 15, 36, 61, 24, 62, 65, 33, 20, 57,  2,  7, 27, 55, 19,
       49, 21, 45,  0,  3, 18,  1,  8,  4, 12, 23, 41,  5, 46, 25, 31, 37,
       11, 47, 17, 56, 44, 40, 16, 50, 58, 32, 54, 10, 42, 60, 30]), 'cur_cost': 111869.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([40, 36, 23, 34, 20,  1,  3,  8, 45, 50, 15, 65, 58, 56, 41, 39,  0,
       55, 53, 60, 30, 54, 21, 43, 16, 52, 12, 31, 62, 57,  4, 38, 19, 17,
       18, 42,  5, 49,  2, 26, 44, 48, 29, 59, 11, 13, 63, 25, 14, 32,  7,
       51,  9, 10, 61, 47, 35, 33,  6, 28, 22, 27, 24, 37, 64, 46]), 'cur_cost': 96734.0}]
2025-07-03 15:07:22,994 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 15:07:22,994 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 2, 'total_searches': 13, 'skip_rate': 0.15384615384615385, 'estimated_time_saved': 10, 'path_optimizer': {'path_count': 11, 'cache_hits': 10, 'similarity_calculations': 67, 'cache_hit_rate': 0.14925373134328357, 'cache_size': 57}}
2025-07-03 15:07:22,994 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 15:07:22,995 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 1, 13, 25, 37, 49, 61, 2, 14, 26, 38, 50, 62, 3, 15, 27, 39, 51, 63, 4, 16, 28, 40, 52, 64, 5, 17, 29, 41, 53, 65, 6, 18, 30, 42, 54, 7, 19, 31, 43, 55, 8, 20, 32, 44, 56, 9, 21, 33, 45, 57, 10, 22, 34, 46, 58, 11, 23, 35, 47, 59], 'cur_cost': 101405.0}}, {'individual': 1, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}}, {'individual': 3, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 5, 'strategy': 'exploit', 'path_data': {'new_tour': array([45, 42, 12,  1, 33,  8, 54, 22,  0, 37, 50,  2, 36, 19, 56, 10, 44,
       17, 18, 52, 24, 27, 20, 65, 57,  6, 31, 23, 51, 16, 39, 61, 11, 21,
       35, 25, 59, 60, 13, 43,  5, 15,  3, 63, 46, 47, 64, 58, 14, 41, 29,
       55,  4, 38,  9, 26, 53, 32, 40, 62, 30, 28, 34, 49,  7, 48]), 'cur_cost': 106123.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}]
2025-07-03 15:07:22,995 - __main__ - INFO - 进化阶段完成
2025-07-03 15:07:22,995 - __main__ - INFO - 开始评估阶段
2025-07-03 15:07:22,995 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 15:07:22,998 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 15:07:22,998 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 3/5

#### Population metrics
- Min cost: 60408.0 → 96734.0 (Δ -36326.0)
- Mean cost: 89839.8 → 101422.3
- Diversity: 0.489 → 0.423 (Δ -0.0662)

#### Elite metrics
- Elite count: 24 → 26
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.117 → 0.108 (Δ -0.009)

#### Strategy performance
- Explore: 7 inds, success 28.6% , avg Δ -14316.71
- Exploit: 3 inds, success 33.3% , avg Δ -5202.67

#### Other indicators
- No-change individuals: 2
- Regression individuals: 5
- Historical trends: Last 3 iterations: costs [13348.0, 85180.0, 60408.0], diversity [0.8323232323232322, 0.7932659932659933, 0.7986531986531986]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 15:07:22,998 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:07:32,025 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 0.333},
  "balance_state": "exploit-dominated",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.7", "introduce_new_exploration_mechanisms"]
}
```
2025-07-03 15:07:32,051 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 15:07:32,052 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 0.333},
  "balance_state": "exploit-dominated",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.7", "introduce_new_exploration_mechanisms"]
}
```
2025-07-03 15:07:32,052 - __main__ - INFO - 评估阶段完成
2025-07-03 15:07:32,052 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 0.333},
  "balance_state": "exploit-dominated",
  "recommendations": ["increase_explore_ratio_to_0.7", "increase_diversity_weight_to_0.7", "introduce_new_exploration_mechanisms"]
}
```
2025-07-03 15:07:32,052 - __main__ - INFO - 当前最佳适应度: 96734.0
2025-07-03 15:07:32,054 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_3.pkl
2025-07-03 15:07:32,054 - __main__ - INFO - composite13_66 开始进化第 5 代
2025-07-03 15:07:32,055 - __main__ - INFO - 开始分析阶段
2025-07-03 15:07:32,055 - StatsExpert - INFO - 开始统计分析
2025-07-03 15:07:32,071 - StatsExpert - INFO - 统计分析完成: {'population_size': 10, 'cost_stats': {'min': 96734.0, 'max': 111869.0, 'mean': 101422.3, 'std': 4164.484002850773}, 'diversity': 0.5898989898989899, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1]}, 'convergence': 0.0, 'node_count': 66}
2025-07-03 15:07:32,072 - __main__ - INFO - 统计专家分析报告: {'population_size': 10, 'cost_stats': {'min': 96734.0, 'max': 111869.0, 'mean': 101422.3, 'std': 4164.484002850773}, 'diversity_level': 0.5898989898989899, 'convergence_level': 0.0, 'clusters': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1], 'summary': '4 clusters, sizes [7, 1, 1, 1]'}, 'clustering_info': {'clusters': 4, 'cluster_sizes': [7, 1, 1, 1], 'summary': '4 clusters, sizes [7, 1, 1, 1]'}, 'node_count': 66, 'coordinates': [[2779, 1294], [2756, 1295], [2762, 1372], [2726, 1286], [2733, 1357], [2741, 1340], [2739, 1384], [2741, 1289], [2760, 1352], [2725, 1313], [2804, 1343], [2740, 1309], [1528, 1441], [1488, 1474], [1542, 1490], [1549, 1460], [1497, 1441], [1541, 1426], [1504, 1426], [1466, 1445], [1488, 1498], [1477, 1498], [1528, 1454], [1509, 1455], [1056, 344], [1017, 367], [1014, 379], [1050, 401], [987, 348], [1022, 321], [969, 356], [1030, 353], [999, 328], [1009, 349], [972, 388], [988, 370], [1020, 392], [1033, 377], [486, 2453], [520, 2425], [512, 2378], [499, 2463], [436, 2399], [501, 2375], [505, 2445], [491, 2441], [498, 2403], [512, 2401], [499, 2389], [512, 2388], [477, 2442], [476, 2458], [3345, 2190], [3310, 2208], [3332, 2256], [3280, 2176], [3270, 2203], [3325, 2246], [3263, 2227], [3289, 2207], [3288, 2237], [3306, 2178], [3296, 2214], [3344, 2171], [3319, 2224], [3333, 2192]], 'distance_matrix': array([[   0.,   23.,   80., ..., 1043., 1075., 1055.],
       [  23.,    0.,   77., ..., 1055., 1086., 1067.],
       [  80.,   77.,    0., ...,  988., 1018.,  999.],
       ...,
       [1043., 1055.,  988., ...,    0.,   59.,   24.],
       [1075., 1086., 1018., ...,   59.,    0.,   35.],
       [1055., 1067.,  999., ...,   24.,   35.,    0.]])}
2025-07-03 15:07:32,072 - __main__ - INFO - 更新共享数据: population_diversity
2025-07-03 15:07:32,072 - __main__ - INFO - 更新共享数据: convergence_level
2025-07-03 15:07:32,072 - PathExpert - INFO - 开始路径结构分析
2025-07-03 15:07:32,076 - PathExpert - ERROR - 路径结构分析出错: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
2025-07-03 15:07:32,076 - PathExpert - INFO - 路径结构分析完成
2025-07-03 15:07:32,076 - __main__ - INFO - 路径专家分析报告: {'high_quality_edges': [], 'common_subpaths': [], 'edge_frequency': {}, 'low_quality_regions': []}
2025-07-03 15:07:32,077 - __main__ - INFO - 更新共享数据: high_quality_edges
2025-07-03 15:07:32,077 - __main__ - INFO - 更新共享数据: difficult_regions
2025-07-03 15:07:32,077 - EliteExpert - INFO - 开始精英解分析
2025-07-03 15:07:32,089 - EliteExpert - INFO - 精英解分析完成
2025-07-03 15:07:32,090 - __main__ - INFO - 精英专家分析报告: {'elite_count': 26, 'elite_common_features': {'common_edges': {'(34, 35)': 0.7307692307692307}, 'common_edge_ratio': 0.015151515151515152}, 'fixed_nodes': [{'node': 0, 'position': 0}], 'population_gap': {'cost_gap': {'min_gap': 87213.0, 'avg_gap': 88836.64615384616}, 'structure_gap': {'unique_elite_edges': 268, 'unique_pop_edges': 268, 'common_edges': 18}}, 'elite_diversity': {'diversity_score': 0.2836363636363637}}
2025-07-03 15:07:32,090 - __main__ - INFO - 更新共享数据: elite_features
2025-07-03 15:07:32,090 - LandscapeExpert - INFO - 开始景观分析
2025-07-03 15:07:32,092 - LandscapeExpert - INFO - 调用LLM进行景观分析
2025-07-03 15:07:32,093 - LandscapeExpert - INFO - 发送给LLM的提示词: 
## Role: Landscape Analysis Expert (Traveling Salesman Problem)

### Instance
- name: Unknown
- node_count: 66

### Evolution snapshot (iter 4/5)
- population_size: 10
- cost_stats: min 96734.0, mean 101422.3, max 111869.0, std 4164.484002850773
- diversity: 0.5898989898989899
- convergence: 0.0
- clustering: 4 clusters, sizes [7, 1, 1, 1]
- recent_best_costs: Recent costs: [85180.0, 60408.0, 96734.0] (Δ 12386.00 per iteration, stagnation: None detected)

### Structural signals
- high_quality_edges_sample: None
- common_subpaths_sample: None
- difficult_regions_sample: None
- opportunity_regions_sample: None

### Spatial summary
- bounding_box: [436.0, 321.0, 3345.0, 2463.0]
- centroid/spread: [1795.652, 1565.212] | rms 1310.584
- density_grid(3x3): [[14, 0, 0], [0, 12, 12], [14, 0, 14]]
- edge_len_stats(min,q1,med,q3,max): [10.0, 1075.0, 1900.0, 2469.0, 3032.0]
- long_edge_ratio: 0.249
- direction_hist(8 sectors): [9, 6, 8, 11, 5, 7, 11, 8]
- nn_median_dist: 17.455

### Elite summary
- elite_count: 0
- fixed_nodes_sample: [{'node': 0, 'position': 0}]
- elite_common_edge_percentage: 0
- elite_common_features_sample: {}
- elite_diversity: {'diversity_score': 0.2836363636363637}

### Interpretation tasks
1. Characterize search-space ruggedness, modality, and deceptiveness (use edge_len_stats, long_edge_ratio).
2. Evaluate population coverage vs. convergence (refer to diversity & density_grid).
3. From Spatial summary, identify:
   • High-density cells → potential opportunity regions (nodes/edges to exploit)
   • Low-density or long-edge corridors → potential difficult regions.
4. Combine Structural signals with Spatial findings to refine difficult/opportunity region lists.
5. State current evolution phase and provide concrete operator suggestions informed by spatial insights.

### Output
Return ONLY a JSON object with keys:
- search_space_features
- population_state
- difficult_regions
- opportunity_regions
- evolution_phase
- evolution_direction

### Output Example
```json
{
  "search_space_features": {"ruggedness": 0.5, "modality": "multi", "deceptiveness": "unknown"},
  "population_state": {"diversity": 0.5, "convergence": 0.4, "clustering": "weak"},
  "difficult_regions": [],
  "opportunity_regions": [],
  "evolution_phase": "unknown",
  "evolution_direction": {"recommended_focus": "explore", "operators": []}
}
```

2025-07-03 15:07:32,093 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:07:47,098 - LandscapeExpert - INFO - LLM返回的分析结果: ```json
{
  "search_space_features": {
    "ruggedness": "high",
    "modality": "multi",
    "deceptiveness": "moderate"
  },
  "population_state": {
    "diversity": 0.5898989898989899,
    "convergence": 0.0,
    "clustering": "strong (4 clusters, sizes [7, 1, 1, 1])"
  },
  "difficult_regions": [
    "low-density cells (top-right, bottom-left)",
    "long-edge corridors (q3 edge length: 2469.0, max: 3032.0)"
  ],
  "opportunity_regions": [
    "high-density cells (top-left, bottom-right)",
    "centroid region (1795.652, 1565.212)"
  ],
  "evolution_phase": "mid-stage (iter 4/5)",
  "evolution_direction": {
    "recommended_focus": "balanced exploration-exploitation",
    "operators": [
      "targeted 2-opt in high-density regions",
      "path-relinking between clusters",
      "directed mutation towards centroid"
    ]
  }
}
```
2025-07-03 15:07:47,099 - LandscapeExpert - INFO - =====景观分析完成====
2025-07-03 15:07:47,099 - __main__ - INFO - 景观专家分析报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.5898989898989899, 'convergence': 0.0, 'clustering': 'strong (4 clusters, sizes [7, 1, 1, 1])'}, 'difficult_regions': ['low-density cells (top-right, bottom-left)', 'long-edge corridors (q3 edge length: 2469.0, max: 3032.0)'], 'opportunity_regions': ['high-density cells (top-left, bottom-right)', 'centroid region (1795.652, 1565.212)'], 'evolution_phase': 'mid-stage (iter 4/5)', 'evolution_direction': {'recommended_focus': 'balanced exploration-exploitation', 'operators': ['targeted 2-opt in high-density regions', 'path-relinking between clusters', 'directed mutation towards centroid']}}
2025-07-03 15:07:47,099 - __main__ - INFO - 更新共享数据: opportunity_regions
2025-07-03 15:07:47,100 - __main__ - INFO - 分析阶段完成
2025-07-03 15:07:47,100 - __main__ - INFO - 景观分析完整报告: {'search_space_features': {'ruggedness': 'high', 'modality': 'multi', 'deceptiveness': 'moderate'}, 'population_state': {'diversity': 0.5898989898989899, 'convergence': 0.0, 'clustering': 'strong (4 clusters, sizes [7, 1, 1, 1])'}, 'difficult_regions': ['low-density cells (top-right, bottom-left)', 'long-edge corridors (q3 edge length: 2469.0, max: 3032.0)'], 'opportunity_regions': ['high-density cells (top-left, bottom-right)', 'centroid region (1795.652, 1565.212)'], 'evolution_phase': 'mid-stage (iter 4/5)', 'evolution_direction': {'recommended_focus': 'balanced exploration-exploitation', 'operators': ['targeted 2-opt in high-density regions', 'path-relinking between clusters', 'directed mutation towards centroid']}}
2025-07-03 15:07:47,100 - __main__ - INFO - 开始策略分配阶段
2025-07-03 15:07:47,100 - StrategyExpert - INFO - 开始策略分配分析
2025-07-03 15:07:47,101 - StrategyExpert - INFO - 发送给LLM的策略分配提示词: 
## Role: Strategy Selection Expert (TSP Evolution)

### Context
- Iteration 4
- Population: 10 individuals
  • diversity: 0.59
  • best_cost: 96734.0
  • mean_cost: 101422.3
- Search-space: ruggedness high, modality multi, deceptiveness moderate
- Evolution phase: mid-stage (iter 4/5)
- Landscape focus suggestion: balanced exploration-exploitation
- Spatial density summary: N/A
- Elite diversity: N/A
- Difficult regions (# 2): low-density cells (t, long-edge corridors 
- Opportunity regions (# 2): high-density cells (, centroid region (179
- Last-iteration feedback: ```json
{
  "evolution_stage": "late",
  "strategy_effectiveness": {"explore": 0.286, "exploit": 0.333},
  "balance_stat...

### Task
1. Decide global explore/exploit ratio (0 – 1).
    • If recommended_focus == "explore" or diversity is low, bias toward exploration.
    • If recommended_focus == "exploit" or convergence is low, bias toward exploitation.
    • Use spatial density: sparse cells ⇒ explore, dense quality cells ⇒ exploit.
2. Assign each individual *i* to "explore" or "exploit".
    • Consider their relative cost rank and any cluster / fixed-node information.
3. Provide a concise rationale for the assignment (≤ 40 words).

### Output
Return ONLY JSON with keys:
  "global_explore_ratio": float,
  "individual_assignments": {"0": "explore", ...},
  "rationale": "text"

### Output Example
```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {"0": "explore", "1": "exploit"},
  "rationale": "Population diversity low; half explore."
}
```

2025-07-03 15:07:47,101 - StrategyExpert - INFO - 调用LLM进行策略分配
2025-07-03 15:07:47,101 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:07:57,496 - StrategyExpert - INFO - LLM返回的策略分配结果: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Balanced focus; moderate diversity and high ruggedness favor mixed strategy."
}
```
2025-07-03 15:07:57,497 - StrategyExpert - INFO - 策略分配完成: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 15:07:57,497 - __main__ - INFO - 策略分配报告: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 15:07:57,497 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Balanced focus; moderate diversity and high ruggedness favor mixed strategy."
}
```
2025-07-03 15:07:57,498 - __main__ - INFO - 策略分配阶段完成
2025-07-03 15:07:57,498 - __main__ - INFO - 策略分配: ['explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit', 'explore', 'exploit']
2025-07-03 15:07:57,498 - __main__ - INFO - 策略分配完整报告: ```json
{
  "global_explore_ratio": 0.6,
  "individual_assignments": {
    "0": "explore",
    "1": "exploit",
    "2": "explore",
    "3": "exploit",
    "4": "explore",
    "5": "exploit",
    "6": "explore",
    "7": "exploit",
    "8": "explore",
    "9": "exploit"
  },
  "rationale": "Balanced focus; moderate diversity and high ruggedness favor mixed strategy."
}
```
2025-07-03 15:07:57,498 - __main__ - INFO - 开始进化阶段
2025-07-03 15:07:57,499 - __main__ - INFO - 为个体 0 生成探索路径
2025-07-03 15:07:57,499 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:07:57,499 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 101405.0 (rank 70.0%)
- Population diversity: 0.4230769230769231
- Evolution phase: mid-stage (iter 4/5)
- Landscape recommends focus: balanced exploration-exploitation
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:07:57,500 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:07:57,500 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:08:11,523 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11],
  "targeted_regions": "high-density ce, adjacent sparse cells",
  "strategy_comment": "Balanced exploration of dense and sparse regions with minimal long edges."
}
```
2025-07-03 15:08:11,523 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:08:11,523 - ExplorationExpert - INFO - 探索路径生成完成，成本: 99370.0, 路径: [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11]
2025-07-03 15:08:11,523 - __main__ - INFO - 个体 0 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11], 'cur_cost': 99370.0}
2025-07-03 15:08:11,525 - __main__ - INFO - 为个体 1 生成利用路径
2025-07-03 15:08:11,525 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:08:11,525 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:08:11,526 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 1 处的路径，新成本: 91362.0
2025-07-03 15:08:11,526 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：7,跳过局部搜索
2025-07-03 15:08:11,526 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 15:08:11,526 - __main__ - INFO - 个体 1 利用路径生成报告: {'new_tour': array([34, 17, 22,  7, 64, 56, 63, 11, 48, 47, 40, 37,  8,  4, 30, 32, 18,
       57,  3, 65, 27, 19, 26,  1,  9, 33, 13,  6, 14, 59, 55, 43, 52, 51,
       23, 42, 20, 16, 15, 24, 60, 50, 41, 46, 29, 21, 39, 12, 61, 38, 62,
       45, 36, 44, 10, 49, 58, 31, 28,  0,  2,  5, 54, 53, 25, 35]), 'cur_cost': 91362.0}
2025-07-03 15:08:11,526 - __main__ - INFO - 为个体 2 生成探索路径
2025-07-03 15:08:11,527 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:08:11,527 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100095.0 (rank 50.0%)
- Population diversity: 0.49846153846153846
- Evolution phase: mid-stage (iter 4/5)
- Landscape recommends focus: balanced exploration-exploitation
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:08:11,528 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:08:11,528 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:08:26,542 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47],
  "targeted_regions": "high-density ce, centroid region",
  "strategy_comment": "Balanced path through dense cells and sparse adjacents."
}
```
2025-07-03 15:08:26,542 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:08:26,542 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100095.0, 路径: [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47]
2025-07-03 15:08:26,543 - __main__ - INFO - 个体 2 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}
2025-07-03 15:08:26,543 - __main__ - INFO - 为个体 3 生成利用路径
2025-07-03 15:08:26,543 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:08:26,543 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:08:26,544 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 3 处的路径，新成本: 105422.0
2025-07-03 15:08:26,544 - ExploitationExpert - INFO - 发现相似路径，相似度: 1.0000，相似id：7,跳过局部搜索
2025-07-03 15:08:26,544 - ExploitationExpert - INFO - 路径与已搜索路径相似度高，跳过局部搜索，生成新的贪心路径替换
2025-07-03 15:08:26,545 - __main__ - INFO - 个体 3 利用路径生成报告: {'new_tour': array([43, 29, 34,  9, 62,  5, 12,  3, 33, 59,  7, 15, 17, 24,  0, 46, 50,
       53, 54, 42, 16, 39, 56, 36, 37, 21, 10, 23, 65, 44,  4, 32, 28,  8,
       61, 27, 20, 57, 63,  6,  2, 35, 41, 18, 13, 52, 25, 31, 22, 47, 38,
       58, 45, 11, 40, 55, 19, 60, 49, 26, 14, 48, 30,  1, 64, 51]), 'cur_cost': 105422.0}
2025-07-03 15:08:26,545 - __main__ - INFO - 为个体 4 生成探索路径
2025-07-03 15:08:26,546 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:08:26,546 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 20.0%)
- Population diversity: 0.5846153846153846
- Evolution phase: mid-stage (iter 4/5)
- Landscape recommends focus: balanced exploration-exploitation
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:08:26,547 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:08:26,547 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:08:40,422 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65],
  "targeted_regions": "high-density ce, centroid region",
  "strategy_comment": "Balanced exploration of dense cells with spiral pattern."
}
```
2025-07-03 15:08:40,423 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:08:40,423 - ExplorationExpert - INFO - 探索路径生成完成，成本: 98856.0, 路径: [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65]
2025-07-03 15:08:40,423 - __main__ - INFO - 个体 4 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}
2025-07-03 15:08:40,424 - __main__ - INFO - 为个体 5 生成利用路径
2025-07-03 15:08:40,424 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:08:40,424 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:08:40,424 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 5 处的路径，新成本: 97306.0
2025-07-03 15:08:40,927 - ExploitationExpert - INFO - res_population_num: 26
2025-07-03 15:08:40,927 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9530, 9534, 9558, 9609, 89049]
2025-07-03 15:08:40,927 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64)]
2025-07-03 15:08:40,937 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:08:40,937 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11], 'cur_cost': 99370.0}, {'tour': array([34, 17, 22,  7, 64, 56, 63, 11, 48, 47, 40, 37,  8,  4, 30, 32, 18,
       57,  3, 65, 27, 19, 26,  1,  9, 33, 13,  6, 14, 59, 55, 43, 52, 51,
       23, 42, 20, 16, 15, 24, 60, 50, 41, 46, 29, 21, 39, 12, 61, 38, 62,
       45, 36, 44, 10, 49, 58, 31, 28,  0,  2,  5, 54, 53, 25, 35]), 'cur_cost': 91362.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([43, 29, 34,  9, 62,  5, 12,  3, 33, 59,  7, 15, 17, 24,  0, 46, 50,
       53, 54, 42, 16, 39, 56, 36, 37, 21, 10, 23, 65, 44,  4, 32, 28,  8,
       61, 27, 20, 57, 63,  6,  2, 35, 41, 18, 13, 52, 25, 31, 22, 47, 38,
       58, 45, 11, 40, 55, 19, 60, 49, 26, 14, 48, 30,  1, 64, 51]), 'cur_cost': 105422.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([ 8, 54, 18, 27,  7, 29, 59, 10, 12, 41, 39, 48, 22,  9, 25, 37, 36,
       51, 60, 33, 40, 19, 56, 64, 14, 28, 32, 35,  4, 15, 52,  3, 30, 42,
       13,  1, 61, 38, 24, 43, 65, 31, 26,  0, 20, 53,  6, 23, 45, 44, 17,
       21, 62, 47, 16, 46,  2, 57, 63, 34,  5, 58, 49, 50, 55, 11]), 'cur_cost': 97306.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([34, 59, 26, 38, 35, 22, 13, 53, 39, 52, 64,  9, 48, 14, 51,  6, 29,
       28, 63, 43, 15, 36, 61, 24, 62, 65, 33, 20, 57,  2,  7, 27, 55, 19,
       49, 21, 45,  0,  3, 18,  1,  8,  4, 12, 23, 41,  5, 46, 25, 31, 37,
       11, 47, 17, 56, 44, 40, 16, 50, 58, 32, 54, 10, 42, 60, 30]), 'cur_cost': 111869.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([40, 36, 23, 34, 20,  1,  3,  8, 45, 50, 15, 65, 58, 56, 41, 39,  0,
       55, 53, 60, 30, 54, 21, 43, 16, 52, 12, 31, 62, 57,  4, 38, 19, 17,
       18, 42,  5, 49,  2, 26, 44, 48, 29, 59, 11, 13, 63, 25, 14, 32,  7,
       51,  9, 10, 61, 47, 35, 33,  6, 28, 22, 27, 24, 37, 64, 46]), 'cur_cost': 96734.0}]
2025-07-03 15:08:40,940 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 15:08:40,940 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 16, 'skip_rate': 0.25, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 12, 'cache_hits': 26, 'similarity_calculations': 94, 'cache_hit_rate': 0.2765957446808511, 'cache_size': 68}}
2025-07-03 15:08:40,940 - __main__ - WARNING - 个体 5 的利用路径生成失败，保留原个体
2025-07-03 15:08:40,940 - __main__ - INFO - 为个体 6 生成探索路径
2025-07-03 15:08:40,941 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:08:40,941 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 100095.0 (rank 60.0%)
- Population diversity: 0.5753846153846154
- Evolution phase: mid-stage (iter 4/5)
- Landscape recommends focus: balanced exploration-exploitation
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:08:40,942 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:08:40,942 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:08:56,444 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 1, 2, 4, 6, 8, 10],
  "targeted_regions": "high-density ce, centroid region",
  "strategy_comment": "Balanced path through dense cells, avoids long edges."
}
```
2025-07-03 15:08:56,444 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:08:56,444 - ExplorationExpert - INFO - 探索路径生成完成，成本: 17682.0, 路径: [0, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 1, 2, 4, 6, 8, 10]
2025-07-03 15:08:56,444 - __main__ - INFO - 个体 6 探索路径生成报告: {'new_tour': [0, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 1, 2, 4, 6, 8, 10], 'cur_cost': 17682.0}
2025-07-03 15:08:56,445 - __main__ - INFO - 为个体 7 生成利用路径
2025-07-03 15:08:56,445 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:08:56,445 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:08:56,446 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 7 处的路径，新成本: 93795.0
2025-07-03 15:08:56,949 - ExploitationExpert - INFO - res_population_num: 26
2025-07-03 15:08:56,949 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9530, 9534, 9558, 9609, 89049]
2025-07-03 15:08:56,949 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64)]
2025-07-03 15:08:56,959 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:08:56,960 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11], 'cur_cost': 99370.0}, {'tour': array([34, 17, 22,  7, 64, 56, 63, 11, 48, 47, 40, 37,  8,  4, 30, 32, 18,
       57,  3, 65, 27, 19, 26,  1,  9, 33, 13,  6, 14, 59, 55, 43, 52, 51,
       23, 42, 20, 16, 15, 24, 60, 50, 41, 46, 29, 21, 39, 12, 61, 38, 62,
       45, 36, 44, 10, 49, 58, 31, 28,  0,  2,  5, 54, 53, 25, 35]), 'cur_cost': 91362.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([43, 29, 34,  9, 62,  5, 12,  3, 33, 59,  7, 15, 17, 24,  0, 46, 50,
       53, 54, 42, 16, 39, 56, 36, 37, 21, 10, 23, 65, 44,  4, 32, 28,  8,
       61, 27, 20, 57, 63,  6,  2, 35, 41, 18, 13, 52, 25, 31, 22, 47, 38,
       58, 45, 11, 40, 55, 19, 60, 49, 26, 14, 48, 30,  1, 64, 51]), 'cur_cost': 105422.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([ 8, 54, 18, 27,  7, 29, 59, 10, 12, 41, 39, 48, 22,  9, 25, 37, 36,
       51, 60, 33, 40, 19, 56, 64, 14, 28, 32, 35,  4, 15, 52,  3, 30, 42,
       13,  1, 61, 38, 24, 43, 65, 31, 26,  0, 20, 53,  6, 23, 45, 44, 17,
       21, 62, 47, 16, 46,  2, 57, 63, 34,  5, 58, 49, 50, 55, 11]), 'cur_cost': 97306.0}, {'tour': [0, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 1, 2, 4, 6, 8, 10], 'cur_cost': 17682.0}, {'tour': array([60, 24, 43, 65,  9, 56, 12, 32, 37, 25, 17, 58, 50, 45, 64, 42, 51,
       18,  1,  3, 63,  4,  0,  2, 11, 61, 59, 53, 22,  7, 27, 33, 15, 19,
       62, 38, 30, 40, 20, 54, 26, 16, 47, 44, 41,  8, 28, 14, 10, 13, 46,
       52, 34, 57, 35, 31, 49, 21,  5, 48, 39,  6, 55, 29, 36, 23]), 'cur_cost': 93795.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([40, 36, 23, 34, 20,  1,  3,  8, 45, 50, 15, 65, 58, 56, 41, 39,  0,
       55, 53, 60, 30, 54, 21, 43, 16, 52, 12, 31, 62, 57,  4, 38, 19, 17,
       18, 42,  5, 49,  2, 26, 44, 48, 29, 59, 11, 13, 63, 25, 14, 32,  7,
       51,  9, 10, 61, 47, 35, 33,  6, 28, 22, 27, 24, 37, 64, 46]), 'cur_cost': 96734.0}]
2025-07-03 15:08:56,961 - ExploitationExpert - INFO - 局部搜索耗时: 0.51秒
2025-07-03 15:08:56,961 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 17, 'skip_rate': 0.23529411764705882, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 13, 'cache_hits': 26, 'similarity_calculations': 106, 'cache_hit_rate': 0.24528301886792453, 'cache_size': 80}}
2025-07-03 15:08:56,961 - __main__ - WARNING - 个体 7 的利用路径生成失败，保留原个体
2025-07-03 15:08:56,963 - __main__ - INFO - 为个体 8 生成探索路径
2025-07-03 15:08:56,963 - ExplorationExpert - INFO - 开始生成探索路径
2025-07-03 15:08:56,963 - ExplorationExpert - INFO - 发送给LLM的探索路径生成提示词: 
## Role: Path Exploration Expert (TSP)

### Context
- Iteration: 0
- Individual cost: 98856.0 (rank 50.0%)
- Population diversity: 0.6507692307692308
- Evolution phase: mid-stage (iter 4/5)
- Landscape recommends focus: balanced exploration-exploitation
- Opportunity regions (sample): high-density ce, centroid region
- Difficult regions to avoid (sample): low-density cel, long-edge corri
- Sparse cells / unexplored areas: Explore additional low-density cells adjacent to opportunity regions

### Objective
Design a NEW path that
1. Visits or leverages opportunity regions / sparse cells
2. Avoids difficult regions and respects TSP constraints
3. Increases diversity relative to current population but keeps cost reasonable

### Constraints (MUST follow)
• Valid node index ∈ [0, 65]  
• Path length == 66 and contains each node exactly once  
• Return ONLY JSON, no explanations, with keys:
  "new_path" (array[int]),
  "targeted_regions" (brief text),
  "strategy_comment" (≤30 words)

### Output Example
```json
{
  "new_path": [0,3,1,2],
  "targeted_regions": "dense_cell(0,2)",
  "strategy_comment": "Covers sparse quadrant and uses HQ edges."
}
```

2025-07-03 15:08:56,964 - ExplorationExpert - INFO - 调用LLM生成探索路径
2025-07-03 15:08:56,964 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:09:12,802 - ExplorationExpert - INFO - LLM生成的探索路径: ```json
{
  "new_path": [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47],
  "targeted_regions": "high-density ce, centroid region",
  "strategy_comment": "Balanced path through dense cells with sparse adjacency"
}
```
2025-07-03 15:09:12,802 - ExplorationExpert - INFO - 计算路径成本
2025-07-03 15:09:12,802 - ExplorationExpert - INFO - 探索路径生成完成，成本: 100095.0, 路径: [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47]
2025-07-03 15:09:12,803 - __main__ - INFO - 个体 8 探索路径生成报告: {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}
2025-07-03 15:09:12,803 - __main__ - INFO - 为个体 9 生成利用路径
2025-07-03 15:09:12,803 - ExploitationExpert - INFO - 开始生成利用路径 - 使用局部搜索和扰动算法
2025-07-03 15:09:12,803 - ExploitationExpert - INFO - 使用优化版局部搜索加拓扑感知扰动策略优化路径
2025-07-03 15:09:12,803 - ExploitationExpert - INFO - 已用贪心路径替换种群中索引 9 处的路径，新成本: 89167.0
2025-07-03 15:09:13,307 - ExploitationExpert - INFO - res_population_num: 28
2025-07-03 15:09:13,308 - ExploitationExpert - INFO - res_population_costs: [9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9521, 9527, 9530, 9534, 9558, 9609, 89049, 9521, 9521]
2025-07-03 15:09:13,308 - ExploitationExpert - INFO - res_populations: [array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 31, 33, 29, 32, 28, 30, 34, 35, 25, 26, 36, 37,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28, 30, 35,
       34, 42, 50, 51, 38, 41, 45, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 16, 18, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 45, 41, 38, 51, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 39, 44, 45, 41, 38, 51, 50, 42, 46, 47, 49,
       48, 43, 40, 21, 20, 13, 23, 14, 15, 22, 12, 17, 18, 16, 19, 27, 37,
       36, 26, 25, 35, 34, 30, 28, 32, 29, 33, 31, 24,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 36, 26, 25, 33, 31, 24, 29, 32, 28, 30, 35,
       34, 42, 50, 45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 40, 43, 21, 20,
       13, 19, 18, 16, 23, 22, 12, 17, 15, 14, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  8,  2,  6,  4, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0,  1,  7,  3,  9, 11,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 51, 38, 41, 45, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       33, 32, 29, 24, 31, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 41, 51, 38, 45, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  4,  6,  2,  8,  5, 11,  9,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 64, 57,
       54, 60, 62, 59, 56, 58, 14, 15, 17, 12, 22, 23, 18, 16, 19, 13, 20,
       21, 40, 49, 47, 39, 44, 45, 41, 38, 51, 50, 42, 46, 48, 43, 34, 35,
       30, 28, 33, 32, 29, 24, 31, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0, 11,  9,  5,  4,  6,  2,  8, 10, 55, 61, 63, 52, 65, 53, 59, 62,
       64, 57, 54, 60, 58, 56, 14, 15, 17, 12, 22, 23, 16, 18, 19, 13, 20,
       21, 43, 40, 49, 48, 46, 47, 39, 44, 41, 51, 38, 45, 50, 42, 34, 35,
       30, 28, 32, 29, 24, 31, 33, 25, 26, 36, 37, 27,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 24, 29, 32, 28, 30, 34, 35, 26, 25, 33, 31, 37, 36,
       27, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 48, 49,
       47, 46, 42, 50, 45, 38, 51, 41, 44, 39, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7,  3, 27, 37, 31, 24, 29, 32, 28, 30, 34, 35, 33, 25, 26,
       36, 19, 16, 18, 17, 12, 22, 15, 14, 23, 13, 20, 21, 40, 43, 42, 50,
       45, 38, 51, 41, 44, 39, 47, 46, 48, 49, 58, 56, 59, 62, 60, 54, 57,
       64, 53, 65, 52, 63, 61, 55, 10,  8,  2,  6,  4,  5,  9, 11],
      dtype=int64), array([ 0,  1,  7, 11,  9,  3, 27, 37, 25, 33, 31, 24, 29, 32, 28, 30, 34,
       35, 26, 36, 18, 16, 19, 13, 23, 22, 12, 17, 15, 14, 20, 21, 43, 40,
       49, 48, 42, 46, 47, 39, 44, 45, 50, 51, 38, 41, 58, 60, 54, 57, 64,
       65, 52, 63, 61, 53, 62, 59, 56, 55,  2,  6,  4,  5,  8, 10],
      dtype=int64), array([ 0, 56,  7, 34, 18, 14, 40, 58, 44, 30, 61, 55, 52,  6, 42, 29,  4,
       53, 45, 10, 22, 33, 47, 20, 15, 25, 38, 41, 11, 24, 35, 37, 12, 19,
       54, 64, 50, 51, 31, 13, 65, 23,  2, 60, 59, 63,  5, 21, 57, 49, 46,
        8,  1, 48, 16, 62,  9, 36, 32, 27, 17,  3, 43, 39, 28, 26],
      dtype=int64), array([ 0, 10, 55, 61, 63, 52, 65, 53, 64, 57, 54, 60, 62, 59, 56, 58, 39,
       44, 45, 41, 38, 51, 50, 42, 48, 46, 47, 49, 40, 43, 34, 35, 30, 28,
       32, 29, 24, 31, 33, 25, 26, 36, 37, 27, 19, 21, 20, 13, 23, 16, 18,
       17, 12, 22, 15, 14,  6,  2,  8,  4,  5,  9, 11,  3,  7,  1],
      dtype=int64), array([ 0,  1,  7,  3, 11,  9,  5,  4,  8,  2,  6, 14, 15, 22, 12, 17, 18,
       16, 23, 13, 20, 21, 19, 27, 37, 36, 26, 25, 31, 24, 29, 32, 33, 28,
       30, 35, 34, 43, 40, 49, 47, 46, 48, 42, 50, 45, 38, 51, 41, 44, 39,
       58, 56, 59, 62, 60, 54, 57, 64, 53, 65, 52, 63, 61, 55, 10],
      dtype=int64)]
2025-07-03 15:09:13,319 - ExploitationExpert - INFO - populations_num: 10
2025-07-03 15:09:13,319 - ExploitationExpert - INFO - populations: [{'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11], 'cur_cost': 99370.0}, {'tour': array([34, 17, 22,  7, 64, 56, 63, 11, 48, 47, 40, 37,  8,  4, 30, 32, 18,
       57,  3, 65, 27, 19, 26,  1,  9, 33, 13,  6, 14, 59, 55, 43, 52, 51,
       23, 42, 20, 16, 15, 24, 60, 50, 41, 46, 29, 21, 39, 12, 61, 38, 62,
       45, 36, 44, 10, 49, 58, 31, 28,  0,  2,  5, 54, 53, 25, 35]), 'cur_cost': 91362.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([43, 29, 34,  9, 62,  5, 12,  3, 33, 59,  7, 15, 17, 24,  0, 46, 50,
       53, 54, 42, 16, 39, 56, 36, 37, 21, 10, 23, 65, 44,  4, 32, 28,  8,
       61, 27, 20, 57, 63,  6,  2, 35, 41, 18, 13, 52, 25, 31, 22, 47, 38,
       58, 45, 11, 40, 55, 19, 60, 49, 26, 14, 48, 30,  1, 64, 51]), 'cur_cost': 105422.0}, {'tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}, {'tour': array([ 8, 54, 18, 27,  7, 29, 59, 10, 12, 41, 39, 48, 22,  9, 25, 37, 36,
       51, 60, 33, 40, 19, 56, 64, 14, 28, 32, 35,  4, 15, 52,  3, 30, 42,
       13,  1, 61, 38, 24, 43, 65, 31, 26,  0, 20, 53,  6, 23, 45, 44, 17,
       21, 62, 47, 16, 46,  2, 57, 63, 34,  5, 58, 49, 50, 55, 11]), 'cur_cost': 97306.0}, {'tour': [0, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 1, 2, 4, 6, 8, 10], 'cur_cost': 17682.0}, {'tour': array([60, 24, 43, 65,  9, 56, 12, 32, 37, 25, 17, 58, 50, 45, 64, 42, 51,
       18,  1,  3, 63,  4,  0,  2, 11, 61, 59, 53, 22,  7, 27, 33, 15, 19,
       62, 38, 30, 40, 20, 54, 26, 16, 47, 44, 41,  8, 28, 14, 10, 13, 46,
       52, 34, 57, 35, 31, 49, 21,  5, 48, 39,  6, 55, 29, 36, 23]), 'cur_cost': 93795.0}, {'tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}, {'tour': array([39, 26, 22, 15,  9, 62, 50, 51, 44, 11, 45, 33, 41, 42,  1, 54, 65,
       36, 27, 14, 40, 46, 59, 47,  4, 19, 24,  3, 21, 30, 28, 58, 60, 37,
       31,  7, 29, 13, 20, 57, 52,  2,  0, 16, 32, 34, 61,  5,  6, 43, 17,
       25, 53, 35, 63, 49, 12, 18, 38, 55, 64, 56, 10,  8, 23, 48]), 'cur_cost': 89167.0}]
2025-07-03 15:09:13,321 - ExploitationExpert - INFO - 局部搜索耗时: 0.52秒
2025-07-03 15:09:13,322 - ExploitationExpert - INFO - 路径优化器性能统计: {'skipped_searches': 4, 'total_searches': 18, 'skip_rate': 0.2222222222222222, 'estimated_time_saved': 20, 'path_optimizer': {'path_count': 14, 'cache_hits': 26, 'similarity_calculations': 119, 'cache_hit_rate': 0.2184873949579832, 'cache_size': 93}}
2025-07-03 15:09:13,322 - __main__ - WARNING - 个体 9 的利用路径生成失败，保留原个体
2025-07-03 15:09:13,323 - __main__ - INFO - 进化阶段报告汇总: [{'individual': 0, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 47, 35, 23, 11], 'cur_cost': 99370.0}}, {'individual': 1, 'strategy': 'exploit', 'path_data': {'new_tour': array([34, 17, 22,  7, 64, 56, 63, 11, 48, 47, 40, 37,  8,  4, 30, 32, 18,
       57,  3, 65, 27, 19, 26,  1,  9, 33, 13,  6, 14, 59, 55, 43, 52, 51,
       23, 42, 20, 16, 15, 24, 60, 50, 41, 46, 29, 21, 39, 12, 61, 38, 62,
       45, 36, 44, 10, 49, 58, 31, 28,  0,  2,  5, 54, 53, 25, 35]), 'cur_cost': 91362.0}}, {'individual': 2, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}}, {'individual': 3, 'strategy': 'exploit', 'path_data': {'new_tour': array([43, 29, 34,  9, 62,  5, 12,  3, 33, 59,  7, 15, 17, 24,  0, 46, 50,
       53, 54, 42, 16, 39, 56, 36, 37, 21, 10, 23, 65, 44,  4, 32, 28,  8,
       61, 27, 20, 57, 63,  6,  2, 35, 41, 18, 13, 52, 25, 31, 22, 47, 38,
       58, 45, 11, 40, 55, 19, 60, 49, 26, 14, 48, 30,  1, 64, 51]), 'cur_cost': 105422.0}}, {'individual': 4, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 11, 23, 35, 47, 59, 10, 22, 34, 46, 58, 9, 21, 33, 45, 57, 8, 20, 32, 44, 56, 7, 19, 31, 43, 55, 6, 18, 30, 42, 54, 5, 17, 29, 41, 53, 4, 16, 28, 40, 52, 3, 15, 27, 39, 51, 2, 14, 26, 38, 50, 1, 13, 25, 37, 49, 61, 62, 63, 64, 65], 'cur_cost': 98856.0}}, {'individual': 6, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 65, 63, 61, 59, 57, 55, 53, 51, 49, 47, 45, 43, 41, 39, 37, 35, 33, 31, 29, 27, 25, 23, 21, 19, 17, 15, 13, 11, 9, 7, 5, 3, 1, 2, 4, 6, 8, 10], 'cur_cost': 17682.0}}, {'individual': 8, 'strategy': 'explore', 'path_data': {'new_tour': [0, 12, 24, 36, 48, 60, 5, 17, 29, 41, 53, 65, 10, 22, 34, 46, 58, 3, 15, 27, 39, 51, 63, 8, 20, 32, 44, 56, 1, 13, 25, 37, 49, 61, 6, 18, 30, 42, 54, 59, 4, 16, 28, 40, 52, 64, 9, 21, 33, 45, 57, 2, 14, 26, 38, 50, 62, 7, 19, 31, 43, 55, 11, 23, 35, 47], 'cur_cost': 100095.0}}]
2025-07-03 15:09:13,323 - __main__ - INFO - 进化阶段完成
2025-07-03 15:09:13,323 - __main__ - INFO - 开始评估阶段
2025-07-03 15:09:13,324 - EvolutionAssessmentExpert - INFO - 开始评估进化效果
2025-07-03 15:09:13,326 - EvolutionAssessmentExpert - INFO - 调用LLM生成评估报告
2025-07-03 15:09:13,326 - EvolutionAssessmentExpert - INFO - 生成的提示词: 
## Role: Evolution Assessment Expert

### Iteration 4/5

#### Population metrics
- Min cost: 96734.0 → 17682.0 (Δ 79052.0)
- Mean cost: 101422.3 → 89315.0
- Diversity: 0.423 → 0.645 (Δ 0.2215)

#### Elite metrics
- Elite count: 26 → 28
- Best elite cost: 9521 → 9521 (Δ 0)
- Elite diversity: 0.108 → 0.101 (Δ -0.0077)

#### Strategy performance
- Explore: 5 inds, success 40.0% , avg Δ 16641.8
- Exploit: 5 inds, success 80.0% , avg Δ 7572.8

#### Other indicators
- No-change individuals: 2
- Regression individuals: 2
- Historical trends: Last 3 iterations: costs [85180.0, 60408.0, 96734.0], diversity [0.7932659932659933, 0.7986531986531986, 0.5898989898989899]

### Task
1. Determine current evolution stage and whether search stagnates.
2. Analyse which strategy contributes most to elite improvement.
3. Assess if explore/exploit ratio needs adjustment.
4. Give quantitative recommendations (e.g., "set explore_ratio to 0.6 and increase diversity weight to 0.8").

### Output
Return ONLY JSON with keys:
  "evolution_stage", "strategy_effectiveness", "balance_state", "recommendations".

### Output Example
```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {"explore": 0.5, "exploit": 0.5},
  "balance_state": "balanced",
  "recommendations": ["maintain_current_ratio"]
}
```

2025-07-03 15:09:13,327 - InterfaceAPI.deepseek - INFO - 发送请求到DeepSeek API (尝试 1/3)
2025-07-03 15:09:21,970 - EvolutionAssessmentExpert - INFO - LLM生成的评估报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.4,
    "exploit": 0.8
  },
  "balance_state": "exploit_heavy",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8"
  ]
}
```
2025-07-03 15:09:21,998 - EvolutionAssessmentExpert - INFO - 进化评估完成
2025-07-03 15:09:21,998 - __main__ - INFO - 评估阶段完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.4,
    "exploit": 0.8
  },
  "balance_state": "exploit_heavy",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8"
  ]
}
```
2025-07-03 15:09:21,998 - __main__ - INFO - 评估阶段完成
2025-07-03 15:09:21,999 - __main__ - INFO - 评估完整报告: ```json
{
  "evolution_stage": "mid",
  "strategy_effectiveness": {
    "explore": 0.4,
    "exploit": 0.8
  },
  "balance_state": "exploit_heavy",
  "recommendations": [
    "increase_explore_ratio_to_0.6",
    "increase_diversity_weight_to_0.8"
  ]
}
```
2025-07-03 15:09:21,999 - __main__ - INFO - 当前最佳适应度: 17682.0
2025-07-03 15:09:22,000 - __main__ - INFO - 路由数据已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_route_4.pkl
2025-07-03 15:09:22,014 - __main__ - INFO - 最终结果已保存到: c:\Users\<USER>\Desktop\EoH-main - idea - 0701\MoE-main\solution\composite13_66_solution.json
2025-07-03 15:09:22,015 - __main__ - INFO - 实例 composite13_66 处理完成
